import logging
import os, sys
import threading

import django
from django.db import transaction

sys.path.append('./')
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "steambase.settings")
django.setup()

from charge.models import ChargeLevel
from authentication.models import AuthUser
from apscheduler.schedulers.blocking import BlockingScheduler


_logger = logging.getLogger(__name__)

def init_charge_level():
    with transaction.atomic():
        users = AuthUser.objects.all()
        for user in users:
            user.extra.freebox_lv1_limit = 1
            user.extra.freebox_lv2_limit = 1
            user.extra.freebox_lv3_limit = 1
            user.extra.freebox_lv4_limit = 1
            user.extra.freebox_lv5_limit = 1
            user.extra.freebox_lv6_limit = 1
            user.extra.freebox_lv7_limit = 1
            user.extra.freebox_lv8_limit = 1
            user.extra.freebox_lv9_limit = 1
            user.extra.freebox_lv10_limit = 1
        # user = AuthUser.objects.filter(username='18884135190').first()

            user.extra.save()
            user.save()

        user_levels = ChargeLevel.objects.all()
        user_levels.update(level=0, amount=0)
        _logger.info("charge level init success")
        # user_level = ChargeLevel.objects.filter(user=user)
        # user_level.update(level=0, amount=0)

def job():
    scheduler = BlockingScheduler()
    scheduler.add_job(init_charge_level, trigger='cron', day=1, hour=0, minute=0, second=0)
    scheduler.start()


def setup_init_charge_level():
    th = threading.Thread(target=job, args=())
    th.start()
