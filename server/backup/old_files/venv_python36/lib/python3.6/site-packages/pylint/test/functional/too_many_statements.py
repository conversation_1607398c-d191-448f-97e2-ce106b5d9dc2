# pylint: disable=missing-docstring

from __future__ import print_function

def stupid_function(arg): # [too-many-statements]
    if arg == 1:
        print(1)
    elif arg == 2:
        print(1)
    elif arg == 3:
        print(1)
    elif arg == 4:
        print(1)
    elif arg == 5:
        print(1)
    elif arg == 6:
        print(1)
    elif arg == 7:
        print(1)
    elif arg == 8:
        print(1)
    elif arg == 9:
        print(1)
    elif arg == 10:
        print(1)
    elif arg < 1:
        print(1)
        print(1)
        arg = 0
    for _ in range(arg):
        print(1)
        print(1)
        print(1)
        print(1)
        print(1)
        print(1)
        print(1)
        print(1)
        print(1)
        print(1)
        print(1)
        print(1)
        print(1)
        print(1)
        print(1)
        print(1)
        print(1)
        print(1)
        print(1)
        print(1)
        print(1)
        print(1)
        print(1)
        print(1)
        print(1)
        print(1)
        print(1)
        print(1)
        print(1)
        print(1)
