# Generated by Django 3.2.25 on 2025-07-19 03:46

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('tradeup', '0002_auto_20250718_1647'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='tradeupbetitem',
            options={'verbose_name': '合成下注物品', 'verbose_name_plural': 'Tradeup Bet Item'},
        ),
        migrations.AlterModelOptions(
            name='tradeupbot',
            options={'verbose_name': '合成机器人配置', 'verbose_name_plural': 'Tradeup Bot Config'},
        ),
        migrations.AlterModelOptions(
            name='tradeupcoinsbetitem',
            options={'verbose_name': '金币合成下注金额', 'verbose_name_plural': 'Coins Tradeup Bet amount'},
        ),
        migrations.AlterModelOptions(
            name='tradeupgame',
            options={'ordering': ('-create_time',), 'verbose_name': '合成游戏', 'verbose_name_plural': 'Tradeup Game'},
        ),
        migrations.AlterModelOptions(
            name='tradeupinventory',
            options={'verbose_name': '合成库存物品', 'verbose_name_plural': 'Tradeup Inventory Item'},
        ),
        migrations.AlterModelOptions(
            name='tradeuppumpday',
            options={'ordering': ('-create_time',), 'verbose_name': '合成抽水日统计', 'verbose_name_plural': 'Tradeup Pump Day'},
        ),
        migrations.AlterModelOptions(
            name='tradeuppumpmonth',
            options={'ordering': ('-create_time',), 'verbose_name': '合成抽水月统计', 'verbose_name_plural': 'Tradeup Pump Month'},
        ),
        migrations.AlterModelOptions(
            name='tradeupstatisticsday',
            options={'ordering': ('-create_time',), 'verbose_name': '合成日统计', 'verbose_name_plural': 'Tradeup Statistics Day'},
        ),
        migrations.AlterModelOptions(
            name='tradeupstatisticsmonth',
            options={'ordering': ('-create_time',), 'verbose_name': '合成月统计', 'verbose_name_plural': 'Tradeup Statistics Month'},
        ),
        migrations.AlterModelOptions(
            name='tradeuptargetitem',
            options={'verbose_name': '合成目标物品', 'verbose_name_plural': 'Tradeup Target Item'},
        ),
        migrations.AlterModelOptions(
            name='tradeupwindayrank',
            options={'verbose_name': '合成赢取日排行', 'verbose_name_plural': 'Tradeup Win Day Rank'},
        ),
        migrations.AlterModelOptions(
            name='tradeupwinweekrank',
            options={'verbose_name': '合成赢取周排行', 'verbose_name_plural': 'Tradeup Win Week Rank'},
        ),
        migrations.AlterField(
            model_name='tradeupbot',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='tradeup_bot', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
        migrations.AlterField(
            model_name='tradeupgame',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tradeup_bets', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
        migrations.AlterField(
            model_name='tradeupwindayrank',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tradeup_win_day_rank', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
        migrations.AlterField(
            model_name='tradeupwinweekrank',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tradeup_win_week_rank', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
    ]
