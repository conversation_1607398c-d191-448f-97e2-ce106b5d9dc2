# -*- coding: utf-8 -*-
# Generated by Django 1.11.29 on 2025-04-26 03:18
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('sitecfg', '0031_auto_20250426_1116'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='support',
            name='image_en',
        ),
        migrations.RemoveField(
            model_name='support',
            name='image_zh_hans',
        ),
        migrations.AddField(
            model_name='banner',
            name='description_en',
            field=models.TextField(blank=True, default=None, null=True, verbose_name='description'),
        ),
        migrations.AddField(
            model_name='banner',
            name='description_zh_hans',
            field=models.TextField(blank=True, default=None, null=True, verbose_name='description'),
        ),
        migrations.AddField(
            model_name='banner',
            name='primary_button_text_en',
            field=models.CharField(blank=True, default=None, max_length=128, null=True, verbose_name='primary button text'),
        ),
        migrations.AddField(
            model_name='banner',
            name='primary_button_text_zh_hans',
            field=models.CharField(blank=True, default=None, max_length=128, null=True, verbose_name='primary button text'),
        ),
        migrations.AddField(
            model_name='banner',
            name='secondary_button_text_en',
            field=models.CharField(blank=True, default=None, max_length=128, null=True, verbose_name='secondary button text'),
        ),
        migrations.AddField(
            model_name='banner',
            name='secondary_button_text_zh_hans',
            field=models.CharField(blank=True, default=None, max_length=128, null=True, verbose_name='secondary button text'),
        ),
        migrations.AddField(
            model_name='banner',
            name='title_en',
            field=models.CharField(max_length=128, null=True, verbose_name='title'),
        ),
        migrations.AddField(
            model_name='banner',
            name='title_zh_hans',
            field=models.CharField(max_length=128, null=True, verbose_name='title'),
        ),
    ]
