import json
from pprint import pprint
from steambase import settings
from django.core.cache import cache
from django.db.models import Sum, Q, F
from django.utils.translation import gettext as _
from rest_framework import serializers
from rest_framework.fields import SerializerMethodField

# 移除直接导入，改为延迟导入
from package.serializers import ItemInfoSerializer, ItemCategorySerializer, ItemQualitySerializer, ItemRaritySerializer, ItemExteriorSerializer, ItemPriceSerializer
# from package.interfaces import get_item_price_by_name
from steambase.enums import GameState
from steambase.redis_con import get_redis
from steambase.serializers import CustomFieldsSerializer, ItemInfoBaseSerializer
from authentication.serializers import AuthUserSerializer
from box.models import Case, CaseCategory, CaseType, CaseRecord, DropItem, FreeCaseConfig, FestivalCaseRecord, CaseRoom, CaseRoomBet, \
    CaseRoomItem
from box.models import CaseRoomRound, CaseStatisticsDay, DropDayRank, IncomeDayRank, RoomDayRank, LoseWeekRank
from steambase.utils import get_serializer_cache_key
# 导入轮次数据处理混入类
from .round_data_mixin import RoundDataMixin, RoundDataValidator

_STEAM_IMG_BASE = 'https://steamcommunity-a.akamaihd.net/economy/image/{icon_url}/'

class DropItemSerializer(ItemInfoBaseSerializer):
    chance = serializers.SerializerMethodField()
    item_info = serializers.SerializerMethodField()
    


    class Meta:
        model = DropItem
        fields = '__all__'

    def get_chance(self, obj):
        total_chance = obj.case.drops.all().aggregate(Sum('show_chance')).get('show_chance__sum', 0)
        if not total_chance:
            return 0
        return obj.show_chance / total_chance * 100
    def get_item_info(self, obj):
        # 使用延迟导入避免循环依赖
        # from package.serializers import ItemInfoSerializer
        return ItemInfoSerializer(obj.item_info, fields=('id', 'name', 'name_en', 'name_zh_hans', 'image', 'item_price', 'item_category', 'item_quality', 'item_rarity', 'item_exterior')).data
    
    
        
    
    





class CaseSerializer(CustomFieldsSerializer):
    # drops = DropItemSerializer(many=True, read_only=True, exclude=('drop_chance_a', 'drop_chance_b', 'drop_chance_c',
    
    # room_drops = serializers.SerializerMethodField()
    cover = serializers.SerializerMethodField()
    

    class Meta:
        model = Case
        fields = '__all__'

    
    
    def get_cover(self, obj):
        if obj.cover:
            if obj.cover.startswith('http'):
                return obj.cover
            else:
                # OSS拼接 settings.ALIYUN_OSS_ENDPOINT
                return f'https://{settings.ALIYUN_OSS_BUCKET_NAME}.{settings.ALIYUN_OSS_ENDPOINT}/{obj.cover}'
        return None   
        

    # def get_drops(self, obj):
    #     d = obj.drops.all().order_by('-show_chance')
    #     return DropItemSerializer(d, many=True, read_only=True,
    #                               exclude=('drop_chance_a', 'drop_chance_b', 'drop_chance_c', 'drop_chance_d',
    #                                        'drop_chance_e')).data   

    
    # def get_room_drops(self, obj):
    #     drops = DropItemSerializer(obj.drops, many=True, read_only=True, fields=(
    #         'icon_url', 'market_name_cn', 'uid', 'show_chance', 'rarity_color', 'price', 'id')).data
    #     for d in drops:
    #         d['price'] = self._get_drop_price(DropItem.objects.filter(uid=d['uid']).first())
    #     return drops
    
    


class CaseRecordSerializer(CustomFieldsSerializer):
    user_info = serializers.SerializerMethodField()
    case_info = serializers.SerializerMethodField()
    # cost = serializers.SerializerMethodField()
    item_info = serializers.SerializerMethodField()

    class Meta:
        model = CaseRecord
        fields = '__all__'    

    # def get_cost(self, obj):
    #     return obj.case.price
    
    def get_user_info(self, obj):
        return AuthUserSerializer(obj.user, fields=('uid', 'profile')).data

    def get_item_info(self, obj):
        # 使用延迟导入避免循环依赖
        # from package.serializers import ItemInfoSerializer
        return ItemInfoSerializer(obj.item_info, fields=('id', 'name', 'name_en', 'name_zh_hans', 'image', 'item_price', 'item_category', 'item_quality', 'item_rarity', 'item_exterior')).data
    
    def get_case_info(self, obj):
        return CaseSerializer(obj.case, fields=('id', 'case_key', 'name', 'name_en', 'name_zh_hans', 'cover', 'price', 'open_count')).data


class CaseRoomItemSerializer(CustomFieldsSerializer):
    image = serializers.SerializerMethodField()
    name = serializers.SerializerMethodField()
    name_en = serializers.SerializerMethodField()
    name_zh_hans = serializers.SerializerMethodField()
    item_price = serializers.SerializerMethodField()
    item_category = serializers.SerializerMethodField()
    item_quality = serializers.SerializerMethodField()
    item_rarity = serializers.SerializerMethodField()
    item_exterior = serializers.SerializerMethodField()
    item_id = serializers.SerializerMethodField()

    class Meta:
        model = CaseRoomItem
        fields = '__all__'

    def get_item_id(self, obj):
        return obj.item_info.id
    
    def get_name(self, obj):
        return obj.item_info.name
    
    def get_name_en(self, obj):
        return obj.item_info.name_en
    
    def get_name_zh_hans(self, obj):
        return obj.item_info.name_zh_hans
        
    def get_item_price(self, obj):
        # 有些 ItemInfo 可能还没有 item_price 记录，需要兼容处理
        item_price = getattr(obj.item_info, 'item_price', None)
        if item_price:
            return ItemPriceSerializer(item_price, fields=('price', 'update_time')).data

        # 尝试动态计算价格
        from package.service.item import get_item_price_by_id
        price_value = get_item_price_by_id(obj.item_info.id).price
        return {'price': price_value, 'update_time': None}
    
    def get_item_category(self, obj):
        return ItemCategorySerializer(obj.item_info.item_category, fields=('cate_id', 'cate_name', 'cate_name_en', 'cate_name_zh_hans', 'icon')).data
    
    def get_item_quality(self, obj):
        return ItemQualitySerializer(obj.item_info.item_quality, fields=('quality_id', 'quality_name', 'quality_name_en', 'quality_name_zh_hans', 'quality_color')).data
    
    def get_item_rarity(self, obj):
        return ItemRaritySerializer(obj.item_info.item_rarity, fields=('rarity_id', 'rarity_name', 'rarity_name_en', 'rarity_name_zh_hans', 'rarity_color')).data
    
    def get_item_exterior(self, obj):
        return ItemExteriorSerializer(obj.item_info.item_exterior, fields=('exterior_id', 'exterior_name', 'exterior_name_en', 'exterior_name_zh_hans', 'exterior_color')).data

    def get_image(self, obj):
        if obj.item_info.custom_icon:
            return obj.item_info.custom_icon.url
        else:
            icon_url = obj.item_info.icon_url_large
            if icon_url:
                if "/" in icon_url:  # 建议修改这里的判断条件为 icon_url
                    icon_url = icon_url.split("/")[0]
                return _STEAM_IMG_BASE.format(icon_url=icon_url) if icon_url else None  # 添加对 icon_url 是否为空的检查
            

STATUS_MAPPING = {
    1: "初始化",
    2: "可加入",
    3: "加入中",
    4: "满员",
    5: "运行中",
    11: "结束",
    20: "取消",
}

class CaseRoomBetSerializer(CustomFieldsSerializer):
    user = AuthUserSerializer(read_only=True, fields=('uid', 'profile'))
    open_items = CaseRoomItemSerializer(many=True, read_only=True, fields=('uid', 'item_id', 'name', 'name_en', 'name_zh_hans', 'image', 'item_price', 'item_category', 'item_quality', 'item_rarity', 'item_exterior'))
    win_items = CaseRoomItemSerializer(many=True, read_only=True, fields=('uid', 'item_id', 'name', 'name_en', 'name_zh_hans', 'image', 'item_price', 'item_category', 'item_quality', 'item_rarity', 'item_exterior'))
    # state = serializers.ReadOnlyField(source='room.state')
    # short_id = serializers.ReadOnlyField(source='room.short_id')
    # max_joiner = serializers.ReadOnlyField(source='room.max_joiner')
    # create_time = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S')
    # update_time = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S')
    # caseroom_state = serializers.ReadOnlyField(source='room.state')
    victory = serializers.SerializerMethodField()
    open_amount = serializers.SerializerMethodField()
    win_amount = serializers.SerializerMethodField()
    win_items_count = serializers.SerializerMethodField()

    # state_text = serializers.SerializerMethodField()

    class Meta:
        model = CaseRoomBet
        fields = '__all__'

    def get_victory(self, obj):
        if obj.victory is not None:
            return obj.victory
        else:
            return None
    
    def get_open_amount(self, obj):
        if obj.open_amount:
            return obj.open_amount
        else:
            return None
    
    def get_win_amount(self, obj):
        if obj.win_amount:
            return obj.win_amount
        else:
            return None
    
    def get_win_items_count(self, obj):
        if obj.win_items_count:
            return obj.win_items_count
        else:
            return None

    
    def get_state_text(self, obj):
        # 从映射字典中获取对应的中文状态
        state_code = obj.room.state
        #return map_status_to_chinese(state_code)
        return STATUS_MAPPING.get(state_code, "未知状态")


class CaseRoomRoundSerializer(CustomFieldsSerializer):
    case = CaseSerializer(read_only=True, fields=('case_key', 'name', 'name_en', 'name_zh_hans', 'cover', 'item', 'price', 'drops'))

    class Meta:
        model = CaseRoomRound
        fields = '__all__'


class CaseRoomRoundForHistorySerializer(CustomFieldsSerializer):
    case = CaseSerializer(read_only=True, fields=('id', 'case_key', 'key', 'name', 'name_en', 'name_zh_hans', 'cover', 'price', 'discount'))

    class Meta:
        model = CaseRoomRound
        fields = '__all__'


class CaseRoomSerializer(RoundDataMixin, CustomFieldsSerializer):
    user = AuthUserSerializer(read_only=True, fields=('uid', 'profile',))
    rounds = serializers.SerializerMethodField()
    bets = CaseRoomBetSerializer(many=True, read_only=True, fields=('user', 'victory'))
    round_count = serializers.SerializerMethodField()
    round_count_current = serializers.SerializerMethodField()
    # 新增扩展轮次字段
    round_count_completed = serializers.SerializerMethodField()
    round_count_remaining = serializers.SerializerMethodField()
    round_progress_percent = serializers.SerializerMethodField()
    is_first_round = serializers.SerializerMethodField()
    is_last_round = serializers.SerializerMethodField()
    round_state = serializers.SerializerMethodField()
    joiner_count = serializers.SerializerMethodField()

    class Meta:
        model = CaseRoom
        fields = '__all__'

    def get_round_count(self, obj):
        """获取总轮次数（兼容旧字段）"""
        round_data = self.get_round_data_unified(obj)
        return round_data['round_count']

    def get_round_count_current(self, obj):
        """获取当前轮次（修复后：明确语义为正在进行的轮次）"""
        round_data = self.get_round_data_unified(obj)
        # 验证数据合理性
        is_valid, issues = RoundDataValidator.validate_round_data(round_data, obj.uid)
        if not is_valid:
            import logging
            logger = logging.getLogger(__name__)
            logger.warning(f"房间 {obj.uid}: 轮次数据验证失败，使用安全值")
        return round_data['round_count_current']
        
    def get_round_count_completed(self, obj):
        """获取已完成轮次数（新增字段）"""
        round_data = self.get_round_data_unified(obj)
        return round_data['round_count_completed']
        
    def get_round_count_remaining(self, obj):
        """获取剩余轮次数（新增字段）"""
        round_data = self.get_round_data_unified(obj)
        return round_data['round_count_remaining']
        
    def get_round_progress_percent(self, obj):
        """获取轮次进度百分比（新增字段）"""
        round_data = self.get_round_data_unified(obj)
        return round_data['round_progress_percent']
        
    def get_is_first_round(self, obj):
        """是否是第一轮（新增字段）"""
        round_data = self.get_round_data_unified(obj)
        return round_data['is_first_round']
        
    def get_is_last_round(self, obj):
        """是否是最后一轮（新增字段）"""
        round_data = self.get_round_data_unified(obj)
        return round_data['is_last_round']
        
    def get_round_state(self, obj):
        """轮次状态描述（新增字段）"""
        round_data = self.get_round_data_unified(obj)
        return round_data['round_state']

    def get_joiner_count(self, obj):
        # 优化：通过已关联的 bets 直接获取数量
        if hasattr(obj, 'bets'):
            return obj.bets.count()
        return CaseRoomBet.objects.filter(room=obj).count()

    def get_rounds(self, obj):
        # 优化：避免在循环内进行重复的序列化和字典操作
        rounds = CaseRoomRoundForHistorySerializer(obj.rounds, many=True, read_only=True, fields=('case',)).data
        case_unique = {}
        for c in rounds:
            case = c.get('case', {})
            name = case.get('name', '')
            
            if name in case_unique:
                case_unique[name]['count'] += 1
            else:
                case['count'] = 1
                case_unique[name] = case
        return [{'case': v} for v in case_unique.values()]
    
    



class CaseRoomCacheSerializer(RoundDataMixin, CustomFieldsSerializer):
    user = AuthUserSerializer(read_only=True, fields=('uid', 'profile'))
    rounds = serializers.SerializerMethodField()
    bets = CaseRoomBetSerializer(many=True, read_only=True)
    round_count = serializers.SerializerMethodField()
    round_count_current = serializers.SerializerMethodField()
    joiner_count = serializers.SerializerMethodField()

    class Meta:
        model = CaseRoom
        fields = '__all__'

    def get_round_count(self, obj):
        """获取总轮次数（使用统一轮次管理器）"""
        round_data = self.get_round_data_unified(obj)
        return round_data['round_count']

    def get_round_count_current(self, obj):
        """获取当前轮次（修复后：使用统一轮次管理器）"""
        round_data = self.get_round_data_unified(obj)
        return round_data['round_count_current']

    def get_joiner_count(self, obj):
        return CaseRoomBet.objects.filter(room=obj).count()

    def get_rounds(self, obj):
        rounds = CaseRoomRoundForHistorySerializer(obj.rounds, many=True, read_only=True, fields=('case',)).data
        case_unique = {}
        for c in rounds:
            case = c.get('case', {})
            name = case.get('name', '')
            if name in case_unique:
                case_unique[name]['count'] += 1
            else:
                case['count'] = 1
                case_unique[name] = case
        rounds = [{'case': v} for k, v in case_unique.items()]
        return rounds

    def to_representation(self, instance):
        # 生成包含状态信息的缓存键
        params = {
            "app_label": instance._meta.app_label,
            "model_name": instance._meta.object_name,
            "serializer_name": self.__class__.__name__,
            "id": str(instance.pk),
            "state": str(instance.state),  # 包含状态信息
            "update_time": str(int(instance.update_time.timestamp()))  # 包含更新时间
        }
        key = ":".join(params.values())
        
        cached = cache.get(key)
        if cached:
            return cached

        result = super(CaseRoomCacheSerializer, self).to_representation(instance)
        # 缩短缓存时间，避免状态变化时的数据不一致
        cache.set(key, result, 60 * 30)  # 30分钟缓存
        return result


class CaseRoomDetailSerializer(RoundDataMixin, CustomFieldsSerializer):
    user = AuthUserSerializer(read_only=True, fields=('uid', 'profile',))
    rounds = serializers.SerializerMethodField()
    bets = CaseRoomBetSerializer(many=True, read_only=True, fields=('uid', 'user', 'victory', 'open_amount', 'win_amount', 'win_items_count', 'open_items', 'win_items'))
    round_count = serializers.SerializerMethodField()
    round_count_current = serializers.SerializerMethodField()
    joiner_count = serializers.SerializerMethodField()

    class Meta:
        model = CaseRoom
        fields = '__all__'

    def get_round_count(self, obj):
        """获取总轮次数（使用统一轮次管理器）"""
        round_data = self.get_round_data_unified(obj)
        return round_data['round_count']

    def get_round_count_current(self, obj):
        """获取当前轮次（修复后：使用统一轮次管理器）"""
        round_data = self.get_round_data_unified(obj)
        return round_data['round_count_current']

    def get_joiner_count(self, obj):
        return CaseRoomBet.objects.filter(room=obj).count()

    def get_rounds(self, obj):
        rounds = obj.rounds.all()
        rounds_data = []
        for round in rounds:
            # 确保缓存的数据也包含国际化字段
            cache_key = 'room_round_case:{}'.format(round.case.case_key)
            data = cache.get(cache_key)
            if data and all(field in data for field in ['name_en', 'name_zh_hans']):
                rounds_data.append({'case': data})
            else:
                # 重新序列化并缓存，确保包含国际化字段
                case_data = CaseSerializer(round.case, read_only=True, fields=(
                'case_key', 'name', 'name_en', 'name_zh_hans', 'cover', 'item', 'price')).data
                cache.set(cache_key, case_data, timeout=60*30)  # 缓存30分钟
                rounds_data.append({'case': case_data})
        return rounds_data


class CaseRoomRecordSerializer(CustomFieldsSerializer):
    user = AuthUserSerializer(read_only=True, fields=('uid', 'steam', 'profile', 'nick_name'))
    rounds = CaseRoomRoundSerializer(many=True, read_only=True, fields=('case',))
    bets = CaseRoomBetSerializer(many=True, read_only=True, fields=('user', 'victory', 'team'))
    round_count = serializers.SerializerMethodField()
    win_amount = serializers.SerializerMethodField()

    class Meta:
        model = CaseRoom
        fields = '__all__'

    def get_round_count(self, obj):
        return CaseRoomRound.objects.filter(room=obj).count()

    def get_win_amount(self, obj):
        win_amount = obj.bets.all().aggregate(Sum('win_amount')).get('win_amount__sum', 0)
        if not win_amount:
            return 0
        return win_amount


class CaseRoomHistorySerializer(CustomFieldsSerializer):
    user = AuthUserSerializer(read_only=True, fields=('uid', 'steam',))
    rounds = CaseRoomRoundForHistorySerializer(many=True, read_only=True, fields=('case',))
    bets = CaseRoomBetSerializer(many=True, read_only=True, fields=('user', 'victory', 'team'))
    round_count = serializers.SerializerMethodField()
    win_amount = serializers.SerializerMethodField()

    class Meta:
        model = CaseRoom
        fields = '__all__'

    def get_round_count(self, obj):
        return CaseRoomRound.objects.filter(room=obj).count()

    def get_win_amount(self, obj):
        win_amount = obj.bets.all().aggregate(Sum('win_amount')).get('win_amount__sum', 0)
        if not win_amount:
            return 0
        return win_amount


class FreeCaseConfigSerializer(CustomFieldsSerializer):
    case = CaseSerializer(read_only=True, fields=('key', 'name', 'cover', 'item', 'price'))

    class Meta:
        model = FreeCaseConfig
        fields = '__all__'


class FestivalCaseRecordSerializer(CustomFieldsSerializer):
    case = CaseSerializer(read_only=True, fields=('key', 'name', 'cover', 'item', 'price'))

    class Meta:
        model = FestivalCaseRecord
        fields = '__all__'


class CaseStatisticsDaySerializer(CustomFieldsSerializer):
    class Meta:
        model = CaseStatisticsDay
        fields = '__all__'


class DropDayRankSerializer(ItemInfoBaseSerializer):
    user = AuthUserSerializer(read_only=True, fields=('uid', 'steam', 'profile', 'nick_name'))

    class Meta:
        model = DropDayRank
        fields = '__all__'


class IncomeDayRankSerializer(ItemInfoBaseSerializer):
    user = AuthUserSerializer(read_only=True, fields=('uid', 'steam', 'profile', 'nick_name'))
    case = CaseSerializer(read_only=True, fields=('key', 'name', 'cover', 'item', 'price'))

    class Meta:
        model = IncomeDayRank
        fields = '__all__'


class RoomDayRankSerializer(CustomFieldsSerializer):
    user = AuthUserSerializer(read_only=True, fields=('uid', 'steam', 'profile', 'nick_name'))

    class Meta:
        model = RoomDayRank
        fields = '__all__'


class LoseWeekRankSerializer(CustomFieldsSerializer):
    user = AuthUserSerializer(read_only=True, fields=('uid', 'steam', 'profile', 'nick_name'))

    class Meta:
        model = LoseWeekRank
        fields = '__all__'

class CaseCategorySerializer(CustomFieldsSerializer):
    class Meta:
        model = CaseCategory
        fields = '__all__'

class CaseTypeSerializer(CustomFieldsSerializer):
    class Meta:
        model = CaseType
        fields = '__all__'