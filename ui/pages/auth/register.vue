<template>
  <div class="auth-page min-h-screen flex flex-col justify-center items-center py-12 px-4 relative">
    <!-- 背景装饰元素 -->
    <div class="fixed inset-0 -z-10">
      <div class="absolute top-0 -left-32 w-96 h-96 rounded-full blur-3xl bg-blue-500/5 animate-pulse-slow"></div>
      <div class="absolute bottom-0 -right-32 w-96 h-96 rounded-full blur-3xl bg-purple-500/5 animate-pulse-slow"></div>
      <div class="absolute inset-0 bg-grid opacity-10"></div>
    </div>

    <!-- 注册表单 -->
    <div class="auth-form p-8 rounded-xl shadow-2xl w-full max-w-md mx-auto bg-gray-800/40 backdrop-blur-md border border-gray-700/30">
      <h1 class="text-3xl font-bold mb-8 text-center text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-400">
        {{ $t('auth.register.title') }}
      </h1>
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <!-- 邮箱 -->
          <div class="form-group">
            <label for="email" class="block mb-2 text-sm font-medium text-white/90">
              {{ $t('auth.email') }}
            </label>
            <div class="flex items-center gap-3">
              <div class="relative flex-1">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                  </svg>
                </div>
                <input 
                  type="email" 
                  id="email"
                  v-model="formData.email" 
                  :class="[
                    'w-full pl-10 pr-4 py-3 rounded-lg bg-gray-700/50 border transition-all duration-300 text-white placeholder-gray-400',
                    'focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50',
                    emailError ? 'border-red-500 ring-2 ring-red-500/20' : 'border-gray-600/50 hover:border-gray-500/50'
                  ]"
                  :placeholder="$t('auth.email_placeholder')"
                  @blur="validateEmail"
                  @input="clearEmailError"
                  autocomplete="email"
                />
              </div>
              <button 
                type="button"
                @click="sendVerificationCode"
                :disabled="!formData.email || codeCountdown > 0 || isLoading || !!emailError"
                class="px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white text-sm font-medium rounded-lg transition-all duration-300 whitespace-nowrap min-w-[120px] flex items-center justify-center"
              >
                <span v-if="isCodeLoading" class="loading-spinner mr-2"></span>
                <span v-else-if="codeCountdown > 0">{{ $t('auth.code_countdown', { seconds: codeCountdown }) }}</span>
                <span v-else>{{ $t('auth.send_code') }}</span>
              </button>
            </div>
            <transition name="error">
              <p v-if="emailError" class="mt-2 text-sm text-red-400 flex items-center">
                <span class="mr-1">⚠️</span>
                {{ emailError }}
              </p>
            </transition>
          </div>
          
          <!-- 邮箱验证码 -->
          <div class="form-group">
            <label class="block text-sm font-medium text-gray-300 mb-2">
              {{ $t('auth.email_verification_code') }}
            </label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <input 
                type="text" 
                v-model="formData.verifyCode" 
                :class="[
                  'w-full pl-10 pr-4 py-3 bg-gray-700/50 border rounded-lg text-white placeholder-gray-400',
                  'focus:outline-none focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all',
                  codeError ? 'border-red-500' : 'border-gray-600'
                ]"
                :placeholder="$t('auth.email_verification_code_placeholder')"
                maxlength="6"
                @blur="validateCode"
                @input="clearCodeError"
              />
            </div>
            <Transition name="error">
              <p v-if="codeError" class="mt-1 text-sm text-red-400">{{ codeError }}</p>
            </Transition>
          </div>
          
          <!-- 密码 -->
          <div class="form-group">
            <label class="block text-sm font-medium text-gray-300 mb-2">
              {{ $t('auth.password') }}
            </label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
              <input 
                :type="showPassword ? 'text' : 'password'" 
                v-model="formData.password" 
                :class="[
                  'w-full pl-10 pr-12 py-3 bg-gray-700/50 border rounded-lg text-white placeholder-gray-400',
                  'focus:outline-none focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all',
                  passwordError ? 'border-red-500' : 'border-gray-600'
                ]"
                :placeholder="$t('auth.password_placeholder')"
                @input="validatePassword"
                @blur="validatePassword"
              />
              <button
                type="button"
                class="absolute inset-y-0 right-0 pr-3 flex items-center"
                @click="togglePasswordVisibility"
              >
                <svg v-if="showPassword" class="h-5 w-5 text-gray-400 hover:text-gray-300 transition-colors" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L8.464 8.464m1.414 1.414L8.464 8.464m5.658 5.658L15.536 15.536m-1.414-1.414L15.536 15.536" />
                </svg>
                <svg v-else class="h-5 w-5 text-gray-400 hover:text-gray-300 transition-colors" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
              </button>
            </div>
            <Transition name="error">
              <p v-if="passwordError" class="mt-1 text-sm text-red-400">{{ passwordError }}</p>
            </Transition>
            
            <!-- 密码强度指示器 -->
            <div v-if="formData.password && !passwordError" class="mt-2">
              <div class="flex items-center">
                <div class="h-1.5 flex-1 bg-gray-700/70 rounded-full overflow-hidden">
                  <div class="h-full rounded-full transition-all duration-300" :class="{
                    'w-1/3 bg-red-500': passwordStrength === 'weak',
                    'w-2/3 bg-yellow-500': passwordStrength === 'medium',
                    'w-full bg-green-500': passwordStrength === 'strong'
                  }"></div>
                </div>
                <span class="ml-2 text-xs" :class="{
                  'text-red-400': passwordStrength === 'weak',
                  'text-yellow-400': passwordStrength === 'medium',
                  'text-green-400': passwordStrength === 'strong'
                }">
                  {{ passwordStrengthText }}
                </span>
              </div>
            </div>
          </div>
          
          <!-- 确认密码 -->
          <div class="form-group">
            <label class="block text-sm font-medium text-gray-300 mb-2">
              {{ $t('auth.confirm_password') }}
            </label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
              <input 
                :type="showPassword ? 'text' : 'password'" 
                v-model="formData.confirmPassword" 
                :class="[
                  'w-full pl-10 pr-4 py-3 bg-gray-700/50 border rounded-lg text-white placeholder-gray-400',
                  'focus:outline-none focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all',
                  confirmPasswordError ? 'border-red-500' : 'border-gray-600'
                ]"
                :placeholder="$t('auth.confirm_password_placeholder')"
                @blur="validateConfirmPassword"
                @input="clearConfirmPasswordError"
              />
            </div>
            <Transition name="error">
              <p v-if="confirmPasswordError" class="mt-1 text-sm text-red-400">{{ confirmPasswordError }}</p>
            </Transition>
          </div>
          
          <!-- 服务条款 -->
          <div class="form-group">
            <div class="flex items-start">
              <div class="flex items-center h-5 mt-1">
                <input 
                  id="agree" 
                  type="checkbox" 
                  v-model="formData.agreeTerms" 
                  :class="[
                    'w-4 h-4 text-blue-600 bg-gray-700 border rounded focus:ring-blue-500 focus:ring-2',
                    termsError ? 'border-red-500' : 'border-gray-600'
                  ]"
                />
              </div>
              <div class="ml-3 text-sm">
                <label for="agree" class="text-gray-300">
                  {{ $t('auth.agree_to') }}
                  <NuxtLink :to="localePath('/terms')" class="text-blue-400 hover:text-blue-300 transition-colors">
                    {{ $t('auth.terms') }}
                  </NuxtLink>
                  {{ $t('auth.and') }}
                  <NuxtLink :to="localePath('/privacy')" class="text-blue-400 hover:text-blue-300 transition-colors">
                    {{ $t('auth.privacy') }}
                  </NuxtLink>
                </label>
              </div>
            </div>
            <Transition name="error">
              <p v-if="termsError" class="mt-1 text-sm text-red-400">{{ termsError }}</p>
            </Transition>
          </div>
          
          <!-- 提交按钮 -->
          <button 
            type="submit" 
            class="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-500 hover:to-purple-500 text-white font-medium py-3 px-4 rounded-lg transition-all duration-300 flex items-center justify-center transform hover:-translate-y-0.5 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
            :disabled="isLoading || !isFormValid"
          >
            <div v-if="isLoading" class="loading-spinner mr-2"></div>
            {{ isLoading ? $t('auth.registering') : $t('auth.register.title') }}
          </button>
          
          <!-- 错误信息 -->
          <transition name="error">
            <div v-if="formError" class="p-4 bg-red-500/20 border border-red-500/50 rounded-lg">
              <p class="text-sm text-red-300 text-center flex items-center justify-center">
                <span class="mr-2">❌</span>
                {{ formError }}
              </p>
            </div>
          </transition>
        </form>
        
        <!-- 第三方登录 -->
        <div class="mt-8">
          <div class="relative">
            <div class="absolute inset-0 flex items-center">
              <div class="w-full border-t border-gray-600"></div>
            </div>
            <div class="relative flex justify-center text-sm">
              <span class="px-2 bg-gray-800 text-gray-400">{{ $t('auth.or_continue_with') }}</span>
            </div>
          </div>
          
          <div class="mt-6 grid grid-cols-2 gap-3">
            <button
              @click="handleThirdPartyLogin('steam')"
              class="group w-full inline-flex justify-center py-2.5 px-4 border border-gray-600 rounded-lg shadow-sm bg-gray-700/50 text-sm font-medium text-gray-300 hover:bg-gray-600/50 transition-all duration-300 hover:-translate-y-0.5"
            >
              <svg class="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-4.5 15.5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zm8-8c-1.38 0-2.5-1.12-2.5-2.5S14.12 4.5 15.5 4.5 18 5.62 18 7s-1.12 2.5-2.5 2.5z"/>
              </svg>
              <span class="ml-2">Steam</span>
            </button>
            
                           <button
                @click="handleThirdPartyLogin('google')"
                :disabled="true"
                class="group w-full inline-flex justify-center py-2.5 px-4 border border-gray-600 rounded-lg shadow-sm bg-gray-700/50 text-sm font-medium text-gray-500 cursor-not-allowed"
              >
              <svg class="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
              </svg>
              <span class="ml-2">Google</span>
            </button>
          </div>
        </div>
        
        <!-- 登录链接 -->
        <div class="text-center text-sm text-white/70 mt-6">
          {{ $t('auth.already_have_account') }}？
          <NuxtLink 
            :to="localePath('/auth/login')" 
            class="text-blue-400 hover:text-blue-300 transition-colors font-medium hover:underline ml-1"
          >
            {{ $t('auth.login_here') }}
          </NuxtLink>
        </div>
      </div>

    <!-- 图形验证码弹窗 -->
    <SafeCaptchaDialog
      :show="showCaptchaDialog"
      :image="captchaImage"
      :error="captchaError"
      :loading="isCodeLoading"
      @close="showCaptchaDialog = false"
      @confirm="confirmCaptchaAndSend"
      @refresh="refreshCaptchaImage"
    />
  </div>
</template>

<script setup lang="ts">
// 显式导入组件
import SafeCaptchaDialog from '~/components/ui/SafeCaptchaDialog.vue'

// 多语言和路由
const { t } = useI18n()
const localePath = useLocalePath()
const router = useRouter()

// 认证相关
const { isLoading, authError, sendEmailVerificationCode, register } = useAuth()
const userStore = useUserStore()

// 表单数据
const formData = reactive({
  email: '',
  verifyCode: '',
  password: '',
  confirmPassword: '',
  agreeTerms: false
})

// 错误状态
const emailError = ref('')
const codeError = ref('')
const passwordError = ref('')
const confirmPasswordError = ref('')
const termsError = ref('')
const formError = ref('')

// 组件状态
const isCodeLoading = ref(false)
const codeCountdown = ref(0)
const showPassword = ref(false)
const passwordStrength = ref('')
const showCaptchaDialog = ref(false)
const captcha = ref('')
const captchaUuid = ref('')
const captchaImage = ref('')
const captchaToken = ref('')

// 验证码相关错误
const captchaError = ref('')

// 计算属性
const passwordStrengthText = computed(() => {
  switch (passwordStrength.value) {
    case 'weak':
      return t('auth.password_weak')
    case 'medium':
      return t('auth.password_medium')
    case 'strong':
      return t('auth.password_strong')
    default:
      return ''
  }
})

const isFormValid = computed(() => {
  return formData.email && 
         formData.verifyCode && 
         formData.password && 
         formData.confirmPassword && 
         formData.agreeTerms &&
         !emailError.value && 
         !codeError.value && 
         !passwordError.value && 
         !confirmPasswordError.value && 
         !termsError.value
})

// 获取图形验证码
const { getCaptcha: refreshCaptcha, captcha: authCaptcha } = useAuth()

// 刷新图形验证码
const refreshCaptchaImage = async () => {
  try {
    captchaImage.value = ''
    await refreshCaptcha()
    
    // 从useAuth获取验证码数据
    if (authCaptcha.value?.uuid && authCaptcha.value?.captcha) {
      captchaImage.value = authCaptcha.value.captcha
      captchaUuid.value = authCaptcha.value.uuid
    }
  } catch (error) {
    console.error('获取验证码失败:', error)
  }
}

// 验证图形验证码
const validateCaptcha = () => {
  if (captchaToken.value) {
    captchaError.value = ''
    return true
  }

  if (!captcha.value) {
    captchaError.value = t('auth.captcha_required')
    return false
  }
  captchaError.value = ''
  return true
}

// 验证函数
const validateEmail = () => {
  if (!formData.email) {
    emailError.value = t('auth.email_required')
    return false
  }
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(formData.email)) {
    emailError.value = t('auth.email_invalid')
    return false
  }
  
  emailError.value = ''
  return true
}

const validateCode = () => {
  if (!formData.verifyCode) {
    codeError.value = t('auth.code_required')
    return false
  }
  
  codeError.value = ''
  return true
}

const validatePassword = () => {
  if (!formData.password) {
    passwordError.value = t('auth.password_required')
    passwordStrength.value = ''
    return false
  }
  
  if (formData.password.length < 6) {
    passwordError.value = t('auth.password_too_short')
    passwordStrength.value = ''
    return false
  }
  
  // 密码强度检测
  let strength = 0
  if (formData.password.length >= 8) strength++
  if (/[a-z]/.test(formData.password)) strength++
  if (/[A-Z]/.test(formData.password)) strength++
  if (/[0-9]/.test(formData.password)) strength++
  if (/[^A-Za-z0-9]/.test(formData.password)) strength++
  
  if (strength <= 2) {
    passwordStrength.value = 'weak'
  } else if (strength <= 3) {
    passwordStrength.value = 'medium'
  } else {
    passwordStrength.value = 'strong'
  }
  
  passwordError.value = ''
  
  // 如果已经输入了确认密码，则同时验证确认密码
  if (formData.confirmPassword) {
    validateConfirmPassword()
  }
  
  return true
}

const validateConfirmPassword = () => {
  if (!formData.confirmPassword) {
    confirmPasswordError.value = t('auth.confirm_password_required')
    return false
  }
  
  if (formData.confirmPassword !== formData.password) {
    confirmPasswordError.value = t('auth.password_mismatch')
    return false
  }
  
  confirmPasswordError.value = ''
  return true
}

const validateTerms = () => {
  if (!formData.agreeTerms) {
    termsError.value = t('auth.terms_required')
    return false
  }
  
  termsError.value = ''
  return true
}

// 清除错误信息
const clearEmailError = () => { emailError.value = '' }
const clearCodeError = () => { codeError.value = '' }
const clearConfirmPasswordError = () => { confirmPasswordError.value = '' }
const clearCaptchaError = () => { captchaError.value = '' }

// 切换密码可见性
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value
}

// 获取token的方法
const getToken = async (contact: string, type: string, code: string, uuid: string): Promise<any> => {
  try {
    const response = await $fetch<any>('/api/auth/token/', {
      method: 'POST',
      body: { contact, type, code, uuid }
    })
    return response
  } catch (error) {
    console.error('获取token失败:', error)
    throw error
  }
}

// 核心发送验证码逻辑
const sendVerificationCodeCore = async () => {
  captchaError.value = ''
  emailError.value = ''
  isCodeLoading.value = true

  try {
    // 若没有 token 先获取
    if (!captchaToken.value) {
      const tokenRes = await getToken(formData.email, 'email', captcha.value, captchaUuid.value)
      if (!tokenRes || tokenRes.code !== 0) {
        captchaError.value = tokenRes?.message || t('auth.captcha_invalid')
        await refreshCaptchaImage()
        return
      }
      const tokenItem = tokenRes.body?.items
      const token = tokenItem?.token
      if (!token) {
        captchaError.value = t('auth.captcha_invalid')
        await refreshCaptchaImage()
        return
      }
      captchaToken.value = token
    }

    // 发送邮箱验证码
    const response = await $fetch<any>('/api/auth/email/code/', {
      method: 'POST',
      body: {
        email: formData.email,
        token: captchaToken.value,
        type: 1 // 1 注册
      }
    })

    if (response?.code === 0) {
      codeCountdown.value = 60
      captcha.value = ''
      showCaptchaDialog.value = false

      const timer = setInterval(() => {
        codeCountdown.value--
        if (codeCountdown.value <= 0) {
          clearInterval(timer)
          captchaToken.value = ''
        }
      }, 1000)
    } else {
      const msg = response?.message || t('auth.code_send_failed')
      if (showCaptchaDialog.value) {
        captchaError.value = msg
      }
      emailError.value = msg
      captchaToken.value = ''
      await refreshCaptchaImage()
    }
  } catch (e: any) {
    console.error('发送验证码失败:', e)
    captchaToken.value = ''
    const msg = t('auth.code_send_error')
    if (showCaptchaDialog.value) {
      captchaError.value = msg
    }
    emailError.value = msg
  } finally {
    isCodeLoading.value = false
  }
}

// 发送验证码（外部按钮点击）
const sendVerificationCode = () => {
  captchaError.value = ''
  emailError.value = ''

  if (!validateEmail()) return

  if (captchaToken.value) {
    sendVerificationCodeCore()
  } else {
    showCaptchaDialog.value = true
    captchaError.value = ''
    refreshCaptchaImage()
  }
}



// 确认验证码并发送
const confirmCaptchaAndSend = (captchaValue: string) => {
  captcha.value = captchaValue
  if (!validateCaptcha()) return
  sendVerificationCodeCore()
}

// 表单提交
const handleSubmit = async () => {
  // 清除之前的错误
  formError.value = ''
  
  // 验证表单
  const isEmailValid = validateEmail()
  const isCodeValid = validateCode()
  const isPasswordValid = validatePassword()
  const isConfirmPasswordValid = validateConfirmPassword()
  const isTermsValid = validateTerms()
  
  if (!isEmailValid || !isCodeValid || !isPasswordValid || !isConfirmPasswordValid || !isTermsValid) {
    return
  }
  
  try {
    const success = await register({
      email: formData.email,
      password: formData.password,
      name: '', // 后端会处理随机用户名
      verify_code: formData.verifyCode
    })
    
    if (success) {
      // 注册成功，跳转到首页
      await router.push(localePath('/'))
    } else {
      // 注册失败，显示错误信息
      formError.value = authError.value || t('auth.register_failed')
    }
  } catch (error: any) {
    console.error('注册出错:', error)
    formError.value = error.message || t('auth.register_failed')
  }
}

// 第三方登录
const handleThirdPartyLogin = (provider: string) => {
  if (provider === 'steam') {
    // 跳转到Steam登录页面
    window.location.href = '/api/auth/steam/'
  } else if (provider === 'google') {
    // Google登录功能暂未实现
    formError.value = t('auth.google_login_coming_soon') || 'Google登录功能即将推出'
  }
}

// SEO 和元数据设置
useSeoMeta({
  title: computed(() => t('auth.register.title')),
  description: computed(() => t('auth.register.description')),
  keywords: computed(() => t('auth.register.keywords')),
})

useHead({
  title: computed(() => t('auth.register.title')),
  link: [
    {
      rel: 'canonical',
      href: `${useRuntimeConfig().public.siteUrl}${localePath('/auth/register')}`
    }
  ]
})

// 路由保护：已登录用户重定向到首页
definePageMeta({
  middleware: 'guest'
})
</script>

<style lang="scss" scoped>
.auth-page {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.95) 100%);
  backdrop-filter: blur(10px);
}

/* 背景网格 */
.bg-grid {
  background-image: 
    linear-gradient(to right, rgba(255, 255, 255, 0.05) 0.0625rem, transparent 0.0625rem),
    linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 0.0625rem, transparent 0.0625rem);
  background-size: 1.25rem 1.25rem;
}

/* 慢脉冲动画 */
.animate-pulse-slow {
  animation: pulse-slow 8s ease-in-out infinite;
}

@keyframes pulse-slow {
  0%, 100% { opacity: 0.4; }
  50% { opacity: 0.7; }
}

/* 加载动画 */
.loading-spinner {
  display: inline-block;
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 错误信息过渡动画 */
.error-enter-active {
  transition: all 0.3s ease-out;
}

.error-leave-active {
  transition: all 0.2s ease-in;
}

.error-enter-from,
.error-leave-to {
  opacity: 0;
  transform: translateY(-0.5rem);
}

/* 表单组优化 */
.form-group {
  position: relative;
}

.form-group input:focus + .absolute {
  color: rgba(59, 130, 246, 0.8);
}



/* 按钮悬停效果 */
button[type="submit"]:not(:disabled):hover {
  box-shadow: 0 10px 25px -5px rgba(34, 197, 94, 0.3);
}

/* 第三方登录按钮悬停效果 */
.group:hover {
  box-shadow: 0 4px 15px -3px rgba(59, 130, 246, 0.2);
}
</style> 