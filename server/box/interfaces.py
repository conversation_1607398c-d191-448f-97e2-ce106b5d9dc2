import json
import threading
import logging
import random

from django.conf import settings
from django.core.cache import cache
from box.business import check_case_bot, get_case_statistics_day, get_case_number, get_case_records
from box.business_room import check_case_room, check_run_case_room, check_pk_bot, check_pk_join_bot
from box.serializers import CaseSerializer
from box.utils import get_case_item_info_number
from box.models import Case, CaseBot
from steambase.redis_con import get_redis

_logger = logging.getLogger(__name__)


def setup_case_bot_worker():
    th = threading.Thread(target=check_case_bot, args=())
    th.start()

def setup_case_pk_worker():
    th = threading.Thread(target=check_pk_bot, args=())
    th.start()



def setup_case_pk_join_worker():
    th = threading.Thread(target=check_pk_join_bot, args=())
    th.start()


def setup_full_case_room():
    th = threading.Thread(target=check_case_room, args=())
    th.start()


def setup_run_case_room():
    th = threading.Thread(target=check_run_case_room, args=())
    th.start()


def setup_case_room_worker():
    setup_full_case_room()
    setup_run_case_room()


def init_case_cache():
    cases = Case.objects.all()
    for case in cases:
        case_data = CaseSerializer(case, read_only=True,
                                   fields=('key', 'name', 'cover', 'item', 'price', 'room_drops')).data
        cache.set('room_round_case:{}'.format(case.case_key), case_data, None)
        _logger.info('case cache running:{}'.format(case.name))


def init_case_data():
    cache.delete('caselist')  # 确保 key 被转换为字符串
    cache.delete('freecaselist')
    case = Case.objects.all()
    for c in case:
        cache_key = str(c.case_key)  # 将 c.case_key 转换为字符串
        cache.delete(f'case:{cache_key}')


# def setup_send_box_festival_worker():
#     th = threading.Thread(target=send_box_festival, args=())
#     th.start()


def get_case_statistics_day_data(count=20):
    return get_case_statistics_day(count)


def get_case_number_data():
    return get_case_number()


def get_case_records_data(num):
    return get_case_records(num)


def get_case_item_info_number_data():
    return get_case_item_info_number()


# 设置对战机器人缓存
def set_bot_cache_worker():
    cache_key = 'pk_bot'
    num = 20
    # 获取机器人列表
    bot_list = CaseBot.objects.filter(enable=True).order_by('?')[:num]
    # 缓存
    cache.set(cache_key, bot_list, timeout = 60 * 60)


    
    