<template>
  <div
    class="bg-slate-900/60 backdrop-blur-xl border border-white/20 rounded-2xl p-6 shadow-xl"
  >
    <div class="flex items-center justify-between mb-6">
      <div class="flex items-center gap-4">
        <div
          class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center"
        >
          <Icon name="material-symbols:info" class="w-5 h-5 text-white" />
        </div>
        <h2 class="title-i18n text-2xl font-bold text-white">
          {{ $t("battle.battle_info") }}
        </h2>
      </div>
      <div
        class="flex items-center gap-2 px-4 py-2 rounded-xl border"
        :class="getStateClasses(battleState)"
      >
        <div
          class="w-2 h-2 rounded-full animate-pulse"
          :class="getStateDotClass(battleState)"
        ></div>
        <span class="font-i18n text-sm font-medium">{{
          getStateText(battleState)
        }}</span>
      </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
      <!-- 对战ID -->

      <!-- 创建时间 -->
      <div class="bg-slate-800/50 border border-white/10 rounded-xl p-4">
        <div class="flex items-center gap-2 mb-2">
          <Icon
            name="material-symbols:schedule"
            class="w-4 h-4 text-purple-400"
          />
          <span class="font-i18n text-sm text-white/60">{{
            $t("battle.created_at")
          }}</span>
        </div>
        <span class="font-i18n text-lg font-bold text-white">{{
          formatTime(createdAt)
        }}</span>
      </div>

      <!-- 轮次进度 -->
      <div class="bg-slate-800/50 border border-white/10 rounded-xl p-4">
        <div class="flex items-center gap-2 mb-2">
          <Icon
            name="material-symbols:progress-activity"
            class="w-4 h-4 text-cyan-400"
          />
          <span class="font-i18n text-sm text-white/60">{{
            $t("battle.round_progress")
          }}</span>
        </div>
        <div class="flex items-center gap-2">
          <span class="font-i18n text-lg font-bold text-white"
            >{{ currentRound }}/{{ totalRounds }}</span
          >
          <div class="flex-1 bg-slate-700 rounded-full h-2 overflow-hidden">
            <div
              class="h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full transition-all duration-500"
              :style="{ width: `${roundProgress}%` }"
            ></div>
          </div>
        </div>
      </div>

      <!-- 总花费 -->
      <div class="bg-slate-800/50 border border-white/10 rounded-xl p-4">
        <div class="flex items-center gap-2 mb-2">
          <Icon
            name="material-symbols:monetization-on"
            class="w-4 h-4 text-yellow-400"
          />
          <span class="font-i18n text-sm text-white/60">{{
            $t("battle.total_spent")
          }}</span>
        </div>
        <span class="font-i18n text-lg font-bold text-yellow-400"
          >${{ totalValue.toFixed(2) }}</span
        >
      </div>

      <!-- 用户 -->
      <div class="bg-slate-800/50 border border-white/10 rounded-xl p-4">
        <div class="flex items-center gap-2 mb-2">
          <Icon
            name="material-symbols:groups"
            class="w-4 h-4 text-yellow-400"
          />
          <span class="font-i18n text-sm text-white/60">{{
            $t("battle.players_joined")
          }}</span>
        </div>
        <div class="flex items-center gap-2">
          <span class="font-i18n text-lg font-bold text-white"
            >{{ playerCount }}/{{ maxPlayers || 4 }}</span
          >
          <div class="flex-1 bg-slate-700 rounded-full h-2 overflow-hidden">
            <div
              class="h-full bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full transition-all duration-500"
              :style="{ width: `${playerProgress}%` }"
            ></div>
          </div>
        </div>
      </div>

      <!-- 箱子 -->
      <div class="bg-slate-800/50 border border-white/10 rounded-xl p-4">
        <div class="flex items-center gap-2 mb-2">
          <Icon
            name="material-symbols:inventory"
            class="w-4 h-4 text-yellow-400"
          />
          <span class="font-i18n text-sm text-white/60">{{
            $t("battle.cases_selected")
          }}</span>
        </div>
        <span class="font-i18n text-lg font-bold text-yellow-400">{{
          caseCount
        }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
// Props 定义
const props = defineProps({
  battleId: {
    type: [String, Number],
    required: true,
  },
  battleState: {
    type: String,
    default: "waiting",
  },
  createdAt: {
    type: [Date, String, Number],
    default: () => new Date(),
  },
  currentRound: {
    type: Number,
    default: 1,
  },
  totalRounds: {
    type: Number,
    default: 3,
  },
  totalValue: {
    type: Number,
    default: 0,
  },
  playerCount: {
    type: Number,
    default: 0,
  },
  caseCount: {
    type: Number,
    default: 0,
  },
  duration: {
    type: String,
    default: "00:00",
  },
  showExtraInfo: {
    type: Boolean,
    default: true,
  },
  maxPlayers: {
    type: Number,
    default: 4
  }
});

// 事件定义
const emit = defineEmits(["copy-id"]);

// 国际化
const { t } = useI18n();

// 计算属性
const roundProgress = computed(() => {
  if (props.totalRounds === 0) return 0;
  return (props.currentRound / props.totalRounds) * 100;
});

const playerProgress = computed(() => {
  const max = props.maxPlayers || 4;
  if (max === 0) return 0;
  return (props.playerCount / max) * 100;
});

// 方法
const formatTime = (date) => {
  const d = new Date(date);
  return new Intl.DateTimeFormat("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  }).format(d);
};

const getStateText = (state) => {
  const stateMap = {
    waiting: t("battle.status.waiting"),
    starting: t("battle.status.starting"),
    battle: t("battle.status.battling"),
    calculating: t("battle.status.calculating"),
    finished: t("battle.status.finished"),
  };
  return stateMap[state] || t("battle.status.unknown");
};

const getStateClasses = (state) => {
  const classMap = {
    waiting: "border-yellow-400/30 bg-yellow-400/10",
    starting: "border-blue-400/30 bg-blue-400/10",
    battle: "border-purple-400/30 bg-purple-400/10",
    calculating: "border-orange-400/30 bg-orange-400/10",
    finished: "border-green-400/30 bg-green-400/10",
  };
  return classMap[state] || "border-gray-400/30 bg-gray-400/10";
};

const getStateDotClass = (state) => {
  const dotClassMap = {
    waiting: "bg-yellow-400",
    starting: "bg-blue-400",
    battle: "bg-purple-400",
    calculating: "bg-orange-400",
    finished: "bg-green-400",
  };
  return dotClassMap[state] || "bg-gray-400";
};

const copyBattleId = async () => {
  try {
    await navigator.clipboard.writeText(props.battleId.toString());
    // 这里可以显示复制成功的提示
    emit("copy-id", props.battleId);
    console.log("[🎰BATTLE] 对战ID已复制到剪贴板");
  } catch (error) {
    console.error("[🎰BATTLE] 复制失败:", error);
  }
};
</script>
