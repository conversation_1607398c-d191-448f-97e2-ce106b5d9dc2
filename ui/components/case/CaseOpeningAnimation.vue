<template>
  <div class="case-opening-container">
    <!-- 开箱界面 -->
    <div class="opening-interface">
      <!-- 滚动区域 -->
      <div class="scroll-container" ref="scrollContainer">
        <div class="winning-line"></div>
        <div class="items-overflow-container">
          <div class="items-track" ref="itemsTrack" :style="{ transform: `translateX(${scrollOffset}px)` }">
            <div 
              v-for="(item, index) in displayItems" 
              :key="`item-${index}`" 
              class="item-card"
              :class="getRarityClass(item.rarity)"
            >
              <!-- ST标识 -->
              <div v-if="item.isStatTrak" class="st-badge">
                ST™
              </div>
              
              <!-- 稀有度指示条 -->
              <div class="rarity-indicator" :style="{ backgroundColor: item.rarity_color }"></div>
              
              <div class="item-image-container">
                <img 
                  :src="item.image" 
                  :alt="item.displayName"
                  class="item-image"
                  @error="handleItemImageError"
                />
              </div>
              
              <div class="item-info">
                <div class="item-name" :title="item.fullName">{{ item.displayName }}</div>
                <div class="item-condition" v-if="item.condition">{{ item.condition }}</div>
                <div class="item-price">¥{{ formatPrice(item.price) }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 步骤提示器 -->
      <div v-if="showStepIndicator" class="step-indicator">
        <div class="step-content">
          <div class="step-icon">
            <div class="loading-spinner"></div>
          </div>
          <div class="step-message">{{ stepMessages[currentStep] }}</div>
          <div class="step-progress">
            <div class="progress-bar">
              <div 
                class="progress-fill" 
                :style="{ width: `${((currentStep + 1) / stepMessages.length) * 100}%` }"
              ></div>
            </div>
            <div class="step-counter">{{ currentStep + 1 }} / {{ stepMessages.length }}</div>
          </div>
        </div>
      </div>

      <!-- 控制按钮 -->
      <div class="controls">
        <!-- 调试面板（仅开发环境） -->
        <div v-if="$config.public.env === 'development'" class="debug-panel">
          <details class="mb-3">
            <summary class="cursor-pointer text-sm font-medium text-blue-400 hover:text-blue-300">
              🔍 开发调试信息
            </summary>
            <div class="mt-2 text-xs space-y-1 text-gray-400">
              <div>数据: {{ caseItems?.length || 0 }}组 | 显示: {{ displayItems.length }}个</div>
              <div>状态: {{ isAnimating ? '🔄运行中' : '⏸️空闲' }}</div>
              <div v-if="displayItems.length > 32">
                获胜: {{ displayItems[32]?.name?.substring(0, 15) }}...{{ displayItems[32]?.isWinner ? '🏆' : '' }}
              </div>
              <div v-if="!caseItems?.length" class="text-red-400">⚠️ 缺少箱子数据</div>
            </div>
          </details>
        </div>
        
        <button 
          @click="startOpening" 
          :disabled="!selectedCase || isAnimating"
          class="open-button"
          :class="{ 'disabled': !selectedCase || isAnimating }"
        >
          {{ isAnimating ? (showStepIndicator ? stepMessages[currentStep] : '开启中...') : '开启箱子' }}
          <!-- 调试信息（开发环境） -->
          <span v-if="$config.public.env === 'development'" class="debug-info">
            [{{ isAnimating ? 'true' : 'false' }}]
          </span>
        </button>
      </div>
    </div>

    <!-- 结果弹窗 -->
    <div v-if="showResult" class="result-modal" @click="closeResult">
      <div class="result-content" @click.stop>
        <div class="result-header">
          <h3>🎉 恭喜获得饰品！</h3>
          <button @click="closeResult" class="close-btn">×</button>
        </div>
        
        <div class="result-subtitle">
          来自 {{ selectedCase?.name_zh_hans || selectedCase?.name || '未知箱子' }}
        </div>
        
        <div class="winner-card" :class="getRarityClass(winnerItem?.rarity)">
          <!-- ST标识 -->
          <div v-if="winnerItem?.isStatTrak" class="winner-st-badge">
            StatTrak™
          </div>
          
          <div class="winner-image">
            <img 
              :src="winnerItem?.image" 
              :alt="winnerItem?.displayName || winnerItem?.name"
              @error="handleItemImageError"
            />
          </div>
                      <div class="winner-info">
              <div class="winner-name" :title="winnerItem?.fullName">
                {{ winnerItem?.displayName || winnerItem?.name }}
              </div>
              <div class="winner-condition" v-if="winnerItem?.condition">
                {{ winnerItem?.condition }}
              </div>
              <div class="winner-price">¥{{ formatPrice(winnerItem?.price || 0) }}</div>
              <div class="winner-rarity">{{ getRarityName(winnerItem?.rarityInfo || winnerItem?.rarity) }}</div>
            </div>
        </div>

        <div class="result-actions">
          <button @click="sellItem" class="sell-button">出售</button>
          <button @click="keepItem" class="keep-button">保留</button>
          <button @click="openAnother" class="again-button">再次开启</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, watch } from 'vue'
import { caseApi } from '~/services/case-api'

// Props
interface Props {
  selectedCase?: any
  caseItems?: any[]  // 添加箱子皮肤数据
}

const props = withDefaults(defineProps<Props>(), {
  caseItems: () => []
})

// 事件发射
const emit = defineEmits<{
  'opening-start': []
  'case-opened': [result: any]
}>()

// 状态管理
const isAnimating = ref(false)
const showResult = ref(false)
const scrollOffset = ref(0) // 初始位置从物品开始，无空白
const winnerItem = ref<any>(null)
const displayItems = ref<any[]>([])
const apiResult = ref<any>(null)

// 步骤提示状态
const showStepIndicator = ref(false)
const currentStep = ref(0)
const stepMessages = [
  '正在检查用户状态...',
  '正在检查用户余额...',
  '正在检查箱子状态...',
  '正在准备开箱：打乱排序，载入动画...',
  'Ready, GO! 🚀'
]

// 通知系统
const { success, error, warning } = useNotification()

// 监听 caseItems 变化，优化数据传递逻辑
watch(() => props.caseItems, (newItems, oldItems) => {
  if (process.env.NODE_ENV === 'development') {
    console.log('🔄 Props caseItems 发生变化:')
    console.log('  旧数据长度:', oldItems?.length || 0)
    console.log('  新数据长度:', newItems?.length || 0)
  }
  
  // 验证数据完整性
  if (newItems && newItems.length > 0) {
    const isValidData = validateCaseItemsData(newItems)
    if (isValidData) {
      // 重新生成显示物品（非动画状态下）
      if (!isAnimating.value) {
        displayItems.value = generateDisplayItems()
      }
    } else {
      console.warn('⚠️ 检测到无效的箱子物品数据')
    }
  }
}, { immediate: true, deep: true })

// 验证箱子物品数据的完整性
const validateCaseItemsData = (caseItems: any[]): boolean => {
  if (!Array.isArray(caseItems) || caseItems.length === 0) {
    return false
  }
  
  // 检查是否至少有一个有效的稀有度组
  return caseItems.some(rarityGroup => 
    rarityGroup.items && 
    Array.isArray(rarityGroup.items) && 
    rarityGroup.items.length > 0 &&
    rarityGroup.items.some((item: any) => item.item_info)
  )
}

// 监听状态变化 - 调试用（仅开发环境）
if (process.env.NODE_ENV === 'development') {
  watch(isAnimating, (newVal, oldVal) => {
    console.log(`isAnimating 状态变化: ${oldVal} -> ${newVal}`)
  })

  watch(showResult, (newVal, oldVal) => {
    console.log(`showResult 状态变化: ${oldVal} -> ${newVal}`)
  })
}

// 移除模拟数据，强制使用真实箱子数据

// 计算属性和方法 - 直接使用API数据
const getRarityClass = (rarityInfo: any) => {
  // 如果传入的是旧格式的字符串，保持兼容性
  if (typeof rarityInfo === 'string') {
    const rarityMap: Record<string, string> = {
      'consumer': 'rarity-consumer',
      'industrial': 'rarity-industrial', 
      'mil-spec': 'rarity-mil-spec',
      'restricted': 'rarity-restricted',
      'classified': 'rarity-classified',
      'covert': 'rarity-covert',
      'contraband': 'rarity-contraband'
    }
    return rarityMap[rarityInfo] || 'rarity-consumer'
  }
  
  // 使用API提供的稀有度ID
  const rarityId = rarityInfo?.rarity_id || rarityInfo
  const rarityIdMap: Record<number, string> = {
    1: 'rarity-consumer',    // 消费级
    2: 'rarity-industrial',  // 工业级  
    3: 'rarity-mil-spec',    // 军规级
    4: 'rarity-restricted',  // 受限
    5: 'rarity-classified',  // 保密
    6: 'rarity-covert',      // 隐秘
    7: 'rarity-contraband',  // 违禁
    8: 'rarity-contraband'   // 非凡 (映射为contraband)
  }
  return rarityIdMap[rarityId] || 'rarity-consumer'
}

const getRarityName = (rarityInfo: any) => {
  // 如果传入的是旧格式的字符串，保持兼容性
  if (typeof rarityInfo === 'string') {
    const rarityNames: Record<string, string> = {
      'consumer': '消费级',
      'industrial': '工业级',
      'mil-spec': '军规级',
      'restricted': '受限',
      'classified': '保密',
      'covert': '隐秘',
      'contraband': '违禁'
    }
    return rarityNames[rarityInfo] || '未知'
  }
  
  // 优先使用API提供的中文名称
  if (rarityInfo?.rarity_name_zh_hans) {
    return rarityInfo.rarity_name_zh_hans
  }
  
  if (rarityInfo?.rarity_name) {
    return rarityInfo.rarity_name
  }
  
  // 备选方案：使用稀有度ID映射
  const rarityId = rarityInfo?.rarity_id || rarityInfo
  const rarityNames: Record<number, string> = {
    1: '消费级',
    2: '工业级',
    3: '军规级',
    4: '受限',
    5: '保密',
    6: '隐秘',
    7: '违禁',
    8: '非凡'
  }
  return rarityNames[rarityId] || '未知'
}

// 格式化价格显示
const formatPrice = (price: number): string => {
  if (price < 1) {
    return price.toFixed(2)
  } else if (price < 100) {
    return price.toFixed(1)
  } else {
    return Math.round(price).toString()
  }
}

// 分离武器名称和皮肤名称（参考LiveOpenings组件的实现）
const getWeaponAndSkinName = (fullName: string) => {
  if (!fullName) return { weapon: '', skin: '' }
  
  const parts = fullName.split(' | ')
  if (parts.length < 2) {
    return { weapon: fullName, skin: '' }
  }
  
  const weapon = parts[0].trim()
  const skinPart = parts[1].trim()
  const skin = skinPart.split(' (')[0].trim() // 移除外观部分，如 "(Field-Tested)"
  return { weapon, skin }
}

// 获取皮肤名称（与LiveOpenings组件保持一致）
const getSkinName = (record: any): string => {
  if (!record) return '未知饰品'
  
  // 支持不同的数据结构
  const fullName = record.fullName || record.name || record.item_info?.name || ''
  if (!fullName) return '未知饰品'
  
  const { skin } = getWeaponAndSkinName(fullName)
  return skin || fullName
}

// 提取物品的简短名称
const getDisplayName = (fullName: string): string => {
  if (!fullName) return '未知物品'
  
  // 移除StatTrak前缀
  const cleanName = fullName.replace(/^StatTrak™\s*/, '').replace(/^ST™\s*/, '')
  
  const { weapon, skin } = getWeaponAndSkinName(cleanName)
  
  // 如果没有皮肤部分，直接返回武器名称
  if (!skin) {
    return weapon.length > 12 ? weapon.substring(0, 12) + '...' : weapon
  }
  
  // 简化武器名称
  let shortWeapon = weapon
    .replace('AK-47', 'AK')
    .replace('M4A4', 'M4A4')
    .replace('M4A1-S', 'M4A1-S')
    .replace('Desert Eagle', 'Deagle')
    .replace('Glock-18', 'Glock')
    .replace('P2000', 'P2000')
    .replace('USP-S', 'USP-S')
    .replace('Five-SeveN', '5-7')
    .replace('Tec-9', 'Tec-9')
    .replace('CZ75-Auto', 'CZ75')
    .replace('P250', 'P250')
    .replace('Dual Berettas', 'Dual')
    .replace('MAC-10', 'MAC-10')
    .replace('MP9', 'MP9')
    .replace('MP7', 'MP7')
    .replace('UMP-45', 'UMP')
    .replace('P90', 'P90')
    .replace('PP-Bizon', 'Bizon')
    .replace('FAMAS', 'FAMAS')
    .replace('Galil AR', 'Galil')
    .replace('SSG 08', 'Scout')
    .replace('SCAR-20', 'SCAR')
    .replace('G3SG1', 'G3SG1')
  
  // 简化皮肤名称
  let shortSkin = skin.length > 8 ? skin.substring(0, 8) + '...' : skin
  
  const result = `${shortWeapon} | ${shortSkin}`
  
  // 确保总长度不超过16个字符
  if (result.length > 16) {
    const maxSkinLength = 16 - shortWeapon.length - 3 // 3 for " | "
    if (maxSkinLength > 0) {
      shortSkin = skin.substring(0, maxSkinLength) + '...'
      return `${shortWeapon} | ${shortSkin}`
    } else {
      return shortWeapon.substring(0, 13) + '...'
    }
  }
  
  return result
}

// 获取物品品相
const getCondition = (fullName: string): string => {
  const conditions = [
    { key: 'Factory New', short: 'FN' },
    { key: 'Minimal Wear', short: 'MW' },
    { key: 'Field-Tested', short: 'FT' },
    { key: 'Well-Worn', short: 'WW' },
    { key: 'Battle-Scarred', short: 'BS' }
  ]
  
  // 检查括号内的品相信息，如 "(Factory New)"
  const conditionMatch = fullName.match(/\(([^)]+)\)/)
  if (conditionMatch) {
    const conditionText = conditionMatch[1]
    for (const condition of conditions) {
      if (conditionText.includes(condition.key)) {
        return condition.short
      }
    }
  }
  
  // 备选方案：直接在名称中查找
  for (const condition of conditions) {
    if (fullName.includes(condition.key)) {
      return condition.short
    }
  }
  
  return ''
}

// 检测是否为StatTrak物品
const isStatTrakItem = (fullName: string): boolean => {
  return fullName.includes('StatTrak™') || fullName.includes('ST™')
}

// 模拟步骤执行
const simulateStep = async (stepIndex: number, duration: number) => {
  currentStep.value = stepIndex
  await new Promise(resolve => setTimeout(resolve, duration))
}

// 生成显示物品
const generateDisplayItems = () => {
  if (process.env.NODE_ENV === 'development') {
    console.log('=== 开始生成显示物品 ===')
  }
  
  // 验证数据完整性
  if (!validateCaseItemsData(props.caseItems)) {
    console.error('❌ 箱子皮肤数据无效，无法生成动画物品')
    warning('数据异常', '箱子物品数据不完整，请刷新页面重试')
    return []
  }

  // 从稀有度分组中提取所有物品
  const allItems: any[] = []
  
  try {
    props.caseItems.forEach((rarityGroup, groupIndex) => {
      if (!rarityGroup.items || !Array.isArray(rarityGroup.items)) {
        return
      }
      
      rarityGroup.items.forEach((itemWrapper: any, itemIndex: number) => {
        const item = itemWrapper.item_info
        if (!item) return
        
        // 转换为统一的物品格式
        const convertedItem = convertItemFormat(item, groupIndex, itemIndex)
        if (convertedItem) {
          allItems.push(convertedItem)
        }
      })
    })
    
    if (allItems.length === 0) {
      throw new Error('没有有效的物品数据')
    }
    
    // 生成50个随机物品用于动画
    const items = []
    for (let i = 0; i < 50; i++) {
      const randomItem = allItems[Math.floor(Math.random() * allItems.length)]
      items.push({ ...randomItem, id: `${randomItem.id}-${i}` })
    }
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`✅ 生成显示物品完成，共 ${items.length} 个，来源 ${allItems.length} 个箱子物品`)
    }
    
    return items
  } catch (err) {
    console.error('❌ 生成显示物品时出错:', err)
    error('数据处理失败', '无法处理箱子物品数据')
    return []
  }
}

// 转换物品格式的通用函数
const convertItemFormat = (item: any, groupIndex: number, itemIndex: number) => {
  try {
    const fullName = item.name_zh_hans || item.name || '未知物品'
    const displayName = getDisplayName(fullName)
    const condition = getCondition(fullName)
    const isStatTrak = isStatTrakItem(fullName)
    
    return {
      id: item.id || `${groupIndex}-${itemIndex}`,
      name: fullName, // 保留完整名称用于调试
      fullName, // 完整名称用于tooltip
      displayName, // 显示名称（简化版）
      condition, // 品相
      isStatTrak, // 是否为StatTrak
      price: item.item_price?.price || 0,
      rarity: getRarityClass(item.item_rarity), // 直接传入稀有度对象
      rarityInfo: item.item_rarity, // 保存完整的稀有度信息
      image: item.image || '/demo/item-placeholder.png',
      rarity_color: item.item_rarity?.rarity_color || '#b2b2b2'
    }
  } catch (err) {
    console.warn(`转换物品格式失败 (${groupIndex}-${itemIndex}):`, err)
    return null
  }
}



// 生成包含真实获胜物品的显示序列
const generateDisplayItemsWithWinner = (winnerFromApi: any) => {
  console.log('=== 生成包含真实获胜物品的显示序列 ===')
  console.log('API获胜物品:', winnerFromApi)
  
  // 首先获取所有可用的箱子物品
  if (!props.caseItems || props.caseItems.length === 0) {
    console.error('❌ 没有箱子皮肤数据！无法生成动画物品')
    return []
  }

  // 从稀有度分组中提取所有物品
  let allItems: any[] = []
  
  props.caseItems.forEach((rarityGroup, groupIndex) => {
    if (rarityGroup.items && Array.isArray(rarityGroup.items)) {
      rarityGroup.items.forEach((itemWrapper: any, itemIndex: number) => {
        const item = itemWrapper.item_info
        if (!item) return
        
        const fullName = item.name_zh_hans || item.name || '未知物品'
        const displayName = getDisplayName(fullName)
        const condition = getCondition(fullName)
        const isStatTrak = isStatTrakItem(fullName)
        
        const convertedItem = {
          id: item.id || `${groupIndex}-${itemIndex}`,
          name: fullName,
          fullName,
          displayName,
          condition,
          isStatTrak,
          price: item.item_price?.price || 0,
          rarity: getRarityClass(item.item_rarity),
          rarityInfo: item.item_rarity,
          image: item.image || '/demo/item-placeholder.png',
          rarity_color: item.item_rarity?.rarity_color || '#b2b2b2'
        }
        
        allItems.push(convertedItem)
      })
    }
  })
  
  console.log(`✅ 提取到 ${allItems.length} 个箱子物品`)
  
  // 转换API返回的获胜物品格式
  const winnerFullName = winnerFromApi.name_zh_hans || winnerFromApi.name || '获胜物品'
  const winnerDisplayName = getDisplayName(winnerFullName)
  const winnerCondition = getCondition(winnerFullName)
  const winnerIsStatTrak = isStatTrakItem(winnerFullName)
  
  const convertedWinner = {
    id: winnerFromApi.id || winnerFromApi.item_id || 'api-winner',
    name: winnerFullName,
    fullName: winnerFullName,
    displayName: winnerDisplayName,
    condition: winnerCondition,
    isStatTrak: winnerIsStatTrak,
    price: winnerFromApi.item_price?.price || winnerFromApi.price || 0,
    rarity: getRarityClass(winnerFromApi.item_rarity || winnerFromApi.rarity_id),
    rarityInfo: winnerFromApi.item_rarity,
    image: winnerFromApi.image || '/demo/item-placeholder.png',
    rarity_color: winnerFromApi.item_rarity?.rarity_color || winnerFromApi.rarity_color || '#b2b2b2',
    isWinner: true // 标记为获胜物品
  }
  
  console.log('✅ 转换后的获胜物品:', convertedWinner)
  
  // 生成50个物品的动画序列
  const items = []
  const winnerPosition = 32 // 获胜物品固定在第32位（索引31-33之间的中间位置）
  
  for (let i = 0; i < 50; i++) {
    if (i === winnerPosition) {
      // 在获胜位置插入真实的API获胜物品
      items.push({ ...convertedWinner, id: `winner-${i}` })
      console.log(`🏆 在位置 ${i} 插入获胜物品: ${convertedWinner.name}`)
    } else {
      // 其他位置随机选择箱子中的物品
      const randomItem = allItems[Math.floor(Math.random() * allItems.length)]
      items.push({ ...randomItem, id: `${randomItem.id}-${i}` })
    }
  }
  
  console.log('=== 生成包含获胜物品的显示序列完成 ===')
  console.log(`✅ 总共 ${items.length} 个物品，获胜物品在位置 ${winnerPosition}`)
  
  return items
}

// 开始开箱动画
const startOpening = async () => {
  // 前置检查
  if (!props.selectedCase) {
    warning('无法开箱', '请选择一个箱子')
    return
  }
  
  if (isAnimating.value) {
    warning('开箱进行中', '请等待当前开箱完成')
    return
  }
  
  if (!validateCaseItemsData(props.caseItems)) {
    error('数据异常', '箱子物品数据不完整，请刷新页面重试')
    return
  }
  
  // 重置所有状态
  showResult.value = false
  winnerItem.value = null
  apiResult.value = null
  scrollOffset.value = 0
  currentStep.value = 0
  
  // 开始开箱流程
  emit('opening-start')
  isAnimating.value = true
  showStepIndicator.value = true
  
  // 等待DOM更新
  await nextTick()
  
  try {
    // 步骤1: 检查用户状态
    await simulateStep(0, 800)
    
    // 步骤2: 检查用户余额
    await simulateStep(1, 600)
    
    // 步骤3: 检查箱子状态
    await simulateStep(2, 700)
    
    // 步骤4: 准备开箱
    await simulateStep(3, 1000)
    
    // 步骤5: Ready, GO!
    await simulateStep(4, 800)
    
    if (process.env.NODE_ENV === 'development') {
      console.log('🎰 开始开箱流程...')
    }
    
    // 第一步：调用开箱API获取真实的获胜物品
    const realApiResult = await callOpenCaseApi()
    
    if (!realApiResult) {
      throw new Error('开箱API调用失败')
    }
    
    // 处理API结果，提取获胜物品
    const winnerFromApi = extractWinnerFromApiResult(realApiResult)
    
    if (!winnerFromApi) {
      throw new Error('API返回的物品数据无效')
    }
    
    // 第二步：生成包含获胜物品的动画序列
    displayItems.value = generateDisplayItemsWithWinner(winnerFromApi)
    
    if (displayItems.value.length === 0) {
      throw new Error('无法生成动画序列')
    }
    
    // 等待DOM更新后隐藏步骤提示器，开始动画
    await nextTick()
    showStepIndicator.value = false
    
    // 第三步：执行滚动动画
    await startScrollAnimation()
    
    // 第四步：设置最终结果并通知
    winnerItem.value = winnerFromApi
    apiResult.value = realApiResult
    
    const itemName = winnerFromApi.name_zh_hans || winnerFromApi.name || '未知物品'
    success('开箱成功！', `您获得了 ${itemName}`)
    emit('case-opened', winnerFromApi)
    
    // 更新状态
    await nextTick()
    isAnimating.value = false
    showStepIndicator.value = false
    await nextTick() 
    showResult.value = true
    
  } catch (err) {
    console.error('❌ 开箱过程出错:', err)
    showStepIndicator.value = false
    handleOpeningError(err)
  }
}

// 从API结果中提取获胜物品
const extractWinnerFromApiResult = (apiResult: any) => {
  try {
    if (Array.isArray(apiResult) && apiResult.length > 0) {
      return apiResult[0]
    } else if (apiResult && typeof apiResult === 'object') {
      return apiResult
    }
    return null
  } catch (err) {
    console.error('提取获胜物品失败:', err)
    return null
  }
}

// 处理开箱错误
const handleOpeningError = (err: any) => {
  const errorMessage = err instanceof Error ? err.message : '未知错误'
  error('开箱失败', errorMessage)
  
  // 降级处理：使用预生成的物品作为备选
  try {
    const fallbackItems = displayItems.value.length > 0 ? displayItems.value : generateDisplayItems()
    if (fallbackItems.length > 32) {
      winnerItem.value = fallbackItems[32]
      warning('使用备选结果', '网络异常，显示模拟结果')
    } else {
      // 最后的备选方案
      winnerItem.value = createFallbackItem()
    }
    
    isAnimating.value = false
    showResult.value = true
  } catch (fallbackErr) {
    console.error('备选处理也失败了:', fallbackErr)
    isAnimating.value = false
    error('系统异常', '请刷新页面重试')
  }
}

// 创建备选物品
const createFallbackItem = () => ({
  id: 'fallback-item',
  name: '系统异常',
  price: 0,
  rarity: 'consumer',
  image: '/demo/item-placeholder.png',
  rarity_color: '#b2b2b2'
})

// 执行滚动动画
const startScrollAnimation = (): Promise<any> => {
  return new Promise((resolve) => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🎬 开始执行滚动动画...')
    }
    
    // 获胜物品固定在第32位
    const winnerIndex = 32
    const winner = displayItems.value[winnerIndex]
    
    // 获取DOM元素
    const scrollContainer = document.querySelector('.scroll-container')
    const itemCards = document.querySelectorAll('.item-card')
    
    if (!scrollContainer || itemCards.length === 0) {
      console.error('❌ 无法找到必要的DOM元素')
      resolve(winner)
      return
    }
    
    // 计算尺寸
    const containerWidth = scrollContainer.getBoundingClientRect().width
    const containerCenter = containerWidth / 2
    
    // 获取物品实际宽度（包括间距）
    const firstItemRect = itemCards[0].getBoundingClientRect()
    const secondItemRect = itemCards[1].getBoundingClientRect()
    const itemTotalWidth = Math.abs(secondItemRect.left - firstItemRect.left)
    
    // 计算目标位置：让获胜物品中心对齐容器中心
    const winnerItemCenter = winnerIndex * itemTotalWidth + (firstItemRect.width / 2)
    const targetOffset = -(winnerItemCenter - containerCenter)
    
    // 优化起始位置计算：确保有充足且连贯的滚动距离
    const totalScrollDistance = itemTotalWidth * 25 // 滚动25个物品的距离，更充足
    const initialOffset = targetOffset + totalScrollDistance
    scrollOffset.value = initialOffset // 设置起始位置
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`🎯 获胜物品索引: ${winnerIndex}`)
      console.log(`📏 容器宽度: ${containerWidth}px，中心: ${containerCenter}px`)
      console.log(`📏 物品总宽度: ${itemTotalWidth}px`)
      console.log(`📏 获胜物品中心位置: ${winnerItemCenter}px`)
      console.log(`📏 目标偏移: ${targetOffset}px`)
      console.log(`📏 初始偏移: ${initialOffset}px`)
      console.log(`📏 滚动距离: ${totalScrollDistance}px`)
      console.log(`🚀 开始滚动动画: ${initialOffset}px → ${targetOffset}px`)
    }
    
    // 动画参数 - 增加动画时长
    const startTime = Date.now()
    const duration = 8000 // 8秒动画
    const startingOffset = scrollOffset.value
    
    // 重新设计的流畅缓动动画
    const animate = (currentTime?: number) => {
      const elapsed = Date.now() - startTime
      const progress = Math.min(elapsed / duration, 1)
      
      // 使用更真实的物理缓动函数
      let easeValue
      if (progress < 0.1) {
        // 前10%：缓慢启动（二次函数）
        const t = progress / 0.1
        easeValue = 0.01 * t * t
      } else if (progress < 0.3) {
        // 10%-30%：快速加速（三次函数）
        const t = (progress - 0.1) / 0.2
        easeValue = 0.01 + 0.15 * t * t * t
      } else if (progress < 0.7) {
        // 30%-70%：高速匀速滚动
        const t = (progress - 0.3) / 0.4
        easeValue = 0.16 + 0.60 * t
      } else if (progress < 0.9) {
        // 70%-90%：平滑减速（反向三次函数）
        const t = (progress - 0.7) / 0.2
        const deceleration = 1 - Math.pow(1 - t, 3)
        easeValue = 0.76 + 0.18 * deceleration
      } else {
        // 90%-100%：精确定位（五次函数，更平滑）
        const t = (progress - 0.9) / 0.1
        const precision = t * t * t * t * t // 五次方缓出
        easeValue = 0.94 + 0.06 * precision
      }
      
      // 计算当前位置（确保单调递减）
      const currentOffset = startingOffset + (targetOffset - startingOffset) * easeValue
      scrollOffset.value = currentOffset
      
      if (process.env.NODE_ENV === 'development' && elapsed % 1000 < 16) {
        console.log(`⏱️ 动画进度: ${(progress * 100).toFixed(1)}%, 偏移: ${currentOffset.toFixed(1)}px, 缓动: ${easeValue.toFixed(4)}`)
      }
      
      if (progress < 1) {
        requestAnimationFrame(animate)
      } else {
        // 动画完成，精确停在目标位置
        scrollOffset.value = targetOffset
        
        if (process.env.NODE_ENV === 'development') {
          console.log('🎉 动画完成！最终停在目标位置:', targetOffset)
        }
        
        // 延迟显示结果
        setTimeout(() => {
          resolve(winner)
        }, 2000) // 2秒后显示结果
      }
    }
    
    // 开始动画前先等待一帧，确保DOM更新完成
    setTimeout(() => {
      requestAnimationFrame(animate)
    }, 50) // 50ms延迟，确保状态稳定
  })
}

// 调用真实开箱API
const callOpenCaseApi = async (): Promise<any> => {
  try {
    // 使用箱子唯一标识发起开箱请求
    const caseKey = props.selectedCase.case_key || props.selectedCase.key
    if (!caseKey) {
      console.error('箱子信息:', props.selectedCase)
      throw new Error('箱子信息不完整：缺少case_key或key字段')
    }
    
    console.log('开箱请求参数:', { case_key: caseKey, count: 1 })
    
    // 调用开箱API，参数格式参考old项目：{ case_key, count }
    const response = await caseApi.openCase(caseKey, 1)
    
    console.log('开箱API响应:', response)
    
    if (!response.success) {
      throw new Error(response.message || '开箱API调用失败')
    }
    
    const result = response.data
    
    // 检查返回数据格式
    if (!result) {
      throw new Error('API返回数据为空')
    }
    
    // 无论是数组还是单个对象，都直接返回，让调用者处理
    console.log('API返回数据类型:', Array.isArray(result) ? '数组' : '单个对象', '长度/值:', Array.isArray(result) ? result.length : 'N/A')
    
    return result
  } catch (err) {
    console.error('API开箱失败:', err)
    // 提供更详细的错误信息
    if (err instanceof Error) {
      error(`开箱失败: ${err.message}`)
    } else {
      error('开箱失败，请重试')
    }
    return null
  }
}

// 结果处理
const closeResult = () => {
  showResult.value = false
  // 重置到初始位置，无空白
  scrollOffset.value = 0
  apiResult.value = null
}

const sellItem = () => {
  if (winnerItem.value) {
    // 出售获得金币
    const sellPrice = apiResult.value?.price || winnerItem.value.price
    
    console.log(`出售成功，获得 ¥${sellPrice}`)
    emit('case-opened', { action: 'sell', item: winnerItem.value, price: sellPrice })
  }
  closeResult()
}

const keepItem = () => {
  if (winnerItem.value) {
    console.log('物品已保留到背包')
    emit('case-opened', { action: 'keep', item: winnerItem.value })
  }
  closeResult()
}

const openAnother = () => {
  closeResult()
  nextTick(() => {
    startOpening()
  })
}

const handleItemImageError = (e: Event) => {
  const target = e.target as HTMLImageElement
  target.src = '/images/item-placeholder.svg'
}

// 组件挂载初始化
onMounted(() => {
  console.log('=== CaseOpeningAnimation 组件挂载 ===')
  console.log('Props received:')
  console.log('  selectedCase:', props.selectedCase)
  console.log('  caseItems:', props.caseItems)
  console.log('  caseItems 长度:', props.caseItems?.length)
  
  // 如果有 caseItems，输出详细信息（稀有度分组格式）
  if (props.caseItems && props.caseItems.length > 0) {
    console.log('caseItems 数据结构分析（稀有度分组）:')
    console.log('  第一个稀有度组完整数据:', JSON.stringify(props.caseItems[0], null, 2))
    
    // 分析每个稀有度组
    props.caseItems.forEach((rarityGroup, index) => {
      console.log(`  稀有度组 ${index}:`, {
        rarity_name: rarityGroup.rarity_name_zh_hans || rarityGroup.rarity_name,
        rarity_name_en: rarityGroup.rarity_name_en,
        items_count: rarityGroup.items?.length || 0,
        percentage: rarityGroup.count_percentage
      })
      
      // 显示该组的前3个物品
      if (rarityGroup.items && rarityGroup.items.length > 0) {
        console.log(`    前3个物品:`, rarityGroup.items.slice(0, 3).map((item: any) => ({
          name: item.item_info?.name_zh_hans || item.item_info?.name || '未知',
          price: item.item_info?.item_price?.price || 0,
          image: item.item_info?.image || '/demo/item-placeholder.png'
        })))
      }
    })
    
    // 统计总物品数
    const totalItems = props.caseItems.reduce((total, group) => total + (group.items?.length || 0), 0)
    console.log(`  总物品数: ${totalItems}`)
  } else {
    console.log('⚠️ 没有接收到 caseItems 数据')
  }
  
  console.log('开始生成显示物品...')
  displayItems.value = generateDisplayItems()
  console.log('显示物品生成完成，数量:', displayItems.value.length)
  console.log('=== 组件挂载完成 ===')
})
</script>

<style scoped lang="scss">
.case-opening-container {
  position: relative;
  width: 100%;
  margin: 0;
  padding: 1rem; /* 统一内边距 */
  min-height: 280px; /* 大幅减少高度 */
  overflow: visible; /* 允许获胜线条显示 */
  border-radius: var(--radius-xl); /* 恢复圆角 */
  background: transparent; /* 使用父容器的背景 */
  border: none; /* 移除边框，避免重复 */
}

.opening-interface {
  position: relative;
  z-index: 1;
}

.scroll-container {
  position: relative;
  height: 8rem; /* 大幅减少高度 */
  margin: 0; /* 移除边距 */
  background: linear-gradient(135deg, rgba(17, 24, 39, 0.8), rgba(31, 41, 55, 0.8));
  border-radius: var(--radius-lg);
  overflow: visible; /* 允许获胜线条超出边界 */
  backdrop-filter: blur(10px);
  border: 1px solid rgba(75, 85, 99, 0.3);
  box-shadow: 
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    0 4px 20px rgba(0, 0, 0, 0.3);
  
  /* 确保容器有足够空间显示物品边框 */
  &::before {
    content: '';
    position: absolute;
    left: -4px;
    right: -4px;
    top: -2px;
    bottom: -2px;
    pointer-events: none;
    z-index: -1;
  }
}

.winning-line {
  position: absolute;
  top: -25px; /* 调整超出距离 */
  left: 50%;
  transform: translateX(-50%);
  width: 3px;
  height: calc(100% + 50px); /* 调整高度 */
  background: linear-gradient(to bottom, 
    #1AC6FF 0%, 
    #0FA9CC 25%, 
    #ff6b6b 50%, 
    #feca57 75%, 
    #1AC6FF 100%
  );
  box-shadow: 
    0 0 15px rgba(26, 198, 255, 0.8),
    0 0 30px rgba(26, 198, 255, 0.5),
    0 0 45px rgba(26, 198, 255, 0.3),
    inset 0 0 5px rgba(255, 255, 255, 0.5);
  z-index: 15;
  border-radius: 1.5px;
  
  /* 增强的脉冲动画 */
  animation: winningLinePulse 1.5s ease-in-out infinite;
  
  /* 添加顶部和底部的装饰 */
  &::before,
  &::after {
    content: '';
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    width: 10px; /* 减少装饰尺寸 */
    height: 10px;
    background: radial-gradient(circle, #1AC6FF, transparent);
    border-radius: 50%;
    box-shadow: 0 0 8px rgba(26, 198, 255, 0.8);
  }
  
  &::before {
    top: -5px;
  }
  
  &::after {
    bottom: -5px;
  }
}

@keyframes winningLinePulse {
  0%, 100% { 
    opacity: 1; 
    transform: translateX(-50%) scale(1);
  }
  50% { 
    opacity: 0.8; 
    transform: translateX(-50%) scale(1.1);
  }
}

.items-overflow-container {
  position: relative;
  width: 100%;
  height: 100%;
  padding: 0 8px; /* 增加更多padding确保边框完全显示 */
  overflow: hidden; /* 裁切滚动的物品内容 */
}

.items-track {
  display: flex;
  height: 100%;
  will-change: transform;
  padding-right: 12px; /* 在track末端添加更多间距确保边框可见 */
  /* 移除transition，使用纯requestAnimationFrame控制 */
}

.item-card {
  flex-shrink: 0;
  width: 7rem; /* 减少宽度 */
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0.5rem; /* 减少内边距 */
  margin: 0 1px; /* 减少间距 */
  border-radius: var(--radius-md);
  background: rgba(31, 41, 55, 0.6);
  border: 2px solid transparent;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1); /* 更平滑的过渡 */
  backdrop-filter: blur(8px);
  transform: translateZ(0); /* 硬件加速 */
  
  /* 确保最后几个元素的边框都能正确显示 */
  &:last-child,
  &:nth-last-child(2),
  &:nth-last-child(3) {
    margin-right: 6px; /* 为末端几个元素增加额外边距 */
  }
  
  &.rarity-consumer { border-color: var(--rarity-common); }
  &.rarity-industrial { border-color: var(--rarity-uncommon); }
  &.rarity-mil-spec { border-color: var(--rarity-rare); }
  &.rarity-restricted { border-color: var(--rarity-legendary); }
  &.rarity-classified { border-color: var(--rarity-ancient); }
  &.rarity-covert { border-color: var(--rarity-immortal); }
  &.rarity-contraband { border-color: var(--rarity-divine); }
}

.item-image-container {
  width: 3.5rem; /* 减少尺寸 */
  height: 3.5rem;
  margin-bottom: 0.25rem; /* 减少边距 */
}

.item-image {
  width: 100%;
  height: 100%;
  object-fit: contain; /* 改为contain保证完整显示 */
  border-radius: var(--radius-sm);
}

.item-info {
  text-align: center;
  color: var(--color-text);
}

.item-name {
  font-size: 0.7rem; /* 减少字体大小 */
  font-weight: 500;
  margin-bottom: 0.125rem; /* 减少边距 */
  line-height: 1.1;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.item-condition {
  font-size: 0.6rem;
  color: var(--color-text-secondary);
  margin-bottom: 0.125rem;
  font-weight: 400;
}

.item-price {
  font-size: 0.75rem; /* 减少字体大小 */
  font-weight: 600;
  color: #1AC6FF;
}

/* StatTrak标识 */
.st-badge {
  position: absolute;
  top: 2px;
  right: 2px;
  background: linear-gradient(45deg, #ff6b00, #ff8c00);
  color: black;
  font-size: 0.5rem;
  font-weight: bold;
  padding: 1px 3px;
  border-radius: 2px;
  z-index: 10;
  text-shadow: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

/* 稀有度指示条 */
.rarity-indicator {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  border-radius: 0 0 0.75rem 0.75rem;
  z-index: 5;
}

.controls {
  text-align: center;
  margin: 1rem 0 0 0; /* 只保留上边距，与滚动区域的间距 */
  padding: 0; /* 移除内边距 */
}

.open-button {
  position: relative;
  padding: 0.875rem 2.5rem;
  font-size: 1rem;
  font-weight: 600;
  background: linear-gradient(135deg, #1AC6FF 0%, #0FA9CC 100%);
  color: #000;
  border: none;
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 
    0 4px 14px rgba(26, 198, 255, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  overflow: hidden;
  
  /* 动画光效 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s;
  }
  
  &:hover:not(.disabled) {
    transform: translateY(-2px);
    box-shadow: 
      0 8px 25px rgba(26, 198, 255, 0.5),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
    background: linear-gradient(135deg, #0FA9CC 0%, #0D8FB3 100%);
    
    &::before {
      left: 100%;
    }
  }
  
  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    background: linear-gradient(135deg, #6B7280 0%, #4B5563 100%);
    
    &::before {
      display: none;
    }
  }
  
  /* 脉冲动画 */
  &:not(.disabled) {
    animation: pulse-glow 3s ease-in-out infinite;
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 
      0 4px 14px rgba(26, 198, 255, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }
  50% {
    box-shadow: 
      0 4px 20px rgba(26, 198, 255, 0.6),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }
}

/* 步骤提示器样式 */
.step-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: linear-gradient(135deg, rgba(26, 198, 255, 0.95) 0%, rgba(15, 169, 204, 0.95) 100%);
  border-radius: 1rem;
  padding: 2rem 3rem;
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20px);
  z-index: 100;
  animation: stepFadeIn 0.5s ease-out;
  border: 2px solid rgba(255, 255, 255, 0.1);
}

@keyframes stepFadeIn {
  from { 
    opacity: 0; 
    transform: translate(-50%, -50%) scale(0.9); 
  }
  to { 
    opacity: 1; 
    transform: translate(-50%, -50%) scale(1); 
  }
}

.step-content {
  text-align: center;
  color: black;
}

.step-icon {
  margin-bottom: 1rem;
  display: flex;
  justify-content: center;
}

.loading-spinner {
  width: 3rem;
  height: 3rem;
  border: 3px solid rgba(0, 0, 0, 0.2);
  border-top: 3px solid #000;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.step-message {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: black;
  text-shadow: none;
}

.step-progress {
  margin-top: 1rem;
}

.progress-bar {
  width: 100%;
  height: 0.5rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 0.25rem;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #000 0%, #333 100%);
  border-radius: 0.25rem;
  transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  animation: progressPulse 2s ease-in-out infinite;
}

@keyframes progressPulse {
  0%, 100% { box-shadow: 0 0 5px rgba(0, 0, 0, 0.3); }
  50% { box-shadow: 0 0 15px rgba(0, 0, 0, 0.6); }
}

.step-counter {
  font-size: 0.875rem;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.7);
}

/* 调试面板样式 */
.debug-panel {
  margin-bottom: 0.5rem;
  padding: 0.5rem;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 0.5rem;
  border: 1px solid rgba(59, 130, 246, 0.3);
  font-family: 'Courier New', monospace;
  
  details {
    summary {
      list-style: none;
      
      &::-webkit-details-marker {
        display: none;
      }
      
      &::before {
        content: '▶';
        margin-right: 0.5rem;
        transition: transform 0.2s;
      }
    }
    
    &[open] summary::before {
      transform: rotate(90deg);
    }
  }
}

.debug-panel {
  background: rgba(0, 0, 0, 0.8);
  color: #00ff00;
  padding: 0.5rem;
  margin-bottom: 0.5rem;
  border-radius: 0.25rem;
  font-family: monospace;
  font-size: 0.75rem;
  border: 1px solid rgba(0, 255, 0, 0.3);
}

.debug-info {
  font-size: 0.625rem;
  opacity: 0.7;
  margin-left: 0.5rem;
}

.result-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.result-content {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  padding: 2rem;
  border-radius: 20px;
  min-width: 400px;
  max-width: 500px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  animation: modalSlide 0.3s ease-out;
}

@keyframes modalSlide {
  from { transform: translateY(30px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  
  h3 {
    color: white;
    margin: 0;
    font-size: 1.5rem;
  }
  
  .close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: background-color 0.2s;
    
    &:hover {
      background: rgba(255, 255, 255, 0.1);
    }
  }
}

.result-subtitle {
  text-align: center;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  margin-bottom: 1.5rem;
  font-weight: 400;
}

.winner-card {
  text-align: center;
  padding: 1.5rem;
  border-radius: 15px;
  border: 3px solid;
  margin-bottom: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
}

.winner-image {
  width: 120px;
  height: 120px;
  margin: 0 auto 1rem;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 10px;
  }
}

.winner-st-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: linear-gradient(45deg, #ff6b00, #ff8c00);
  color: black;
  font-size: 0.8rem;
  font-weight: bold;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  z-index: 10;
  text-shadow: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.winner-info {
  color: white;
  
  .winner-name {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .winner-condition {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 0.5rem;
    font-weight: 400;
  }
  
  .winner-price {
    font-size: 1.1rem;
    color: #4facfe;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }
  
  .winner-rarity {
    font-size: 0.9rem;
    opacity: 0.8;
  }
}

.result-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  
  button {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &.sell-button {
      background: #ff6b6b;
      color: white;
      
      &:hover {
        background: #ff5252;
        transform: translateY(-2px);
      }
    }
    
    &.keep-button {
      background: #4ecdc4;
      color: white;
      
      &:hover {
        background: #26d0ce;
        transform: translateY(-2px);
      }
    }
    
    &.again-button {
      background: linear-gradient(45deg, #4facfe, #00f2fe);
      color: white;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(79, 172, 254, 0.4);
      }
    }
  }
}

@media (max-width: 768px) {
  .case-opening-container {
    padding: 0.5rem; /* 进一步减少移动端的边距 */
    min-height: 450px; /* 减少移动端的最小高度 */
  }
  
  .scroll-container {
    height: 150px;
    margin: 0.5rem 0; /* 减少移动端滚动容器边距 */
  }
  
  .item-card {
    width: 120px;
    padding: 0.5rem;
  }
  
  .item-image-container {
    width: 60px;
    height: 60px;
  }
  
  .controls {
    margin: 0.5rem 0; /* 减少移动端控制按钮边距 */
  }
  
  .result-content {
    margin: 1rem;
    min-width: auto;
  }
  
  .result-actions {
    flex-direction: column;
    
    button {
      width: 100%;
    }
  }
}
</style>