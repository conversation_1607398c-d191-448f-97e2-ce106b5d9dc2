from __future__ import absolute_import, unicode_literals
import os
from celery import Celery
from django.conf import settings

# 设置默认Django设置模块
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')

app = Celery('steambase')

# 使用Django的配置文件来配置Celery
app.config_from_object('django.conf:settings', namespace='CELERY')

# 自动发现任务模块
app.autodiscover_tasks()

# Celery配置
app.conf.update(
    # 任务序列化
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',

    # 时区设置
    timezone='Asia/Shanghai',
    enable_utc=True,

    # 任务路由
    task_routes={
        'authentication.tasks.*': {'queue': 'auth'},
        'charge.tasks.*': {'queue': 'charge'},
        'box.tasks.*': {'queue': 'box'},
        'package.tasks.*': {'queue': 'package'},
        'sitecfg.tasks.*': {'queue': 'sitecfg'},
    },

    # 任务结果过期时间
    result_expires=3600,

    # 任务重试配置
    task_acks_late=True,
    worker_prefetch_multiplier=1,

    # 定时任务配置
    beat_scheduler='django_celery_beat.schedulers:DatabaseScheduler',
)