#!/usr/bin/env python
"""
清理sitecfg中的Article和Announce模型
注意：运行此脚本前请确保已经完成数据迁移
"""
import os
import sys

def cleanup_sitecfg_models():
    """清理sitecfg中的旧模型"""
    
    print("🧹 开始清理sitecfg中的旧模型...")
    
    # 读取sitecfg/models.py
    models_file = '/www/wwwroot/csgoskins.com.cn/server/sitecfg/models.py'
    
    if not os.path.exists(models_file):
        print("❌ sitecfg/models.py 文件不存在")
        return
    
    with open(models_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 备份原文件
    backup_file = models_file + '.backup'
    with open(backup_file, 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"✅ 已备份原文件到: {backup_file}")
    
    # 移除Article和Announce模型
    lines = content.split('\n')
    new_lines = []
    skip_lines = False
    skip_until_class = False
    
    for line in lines:
        # 检查是否是Article或Announce类的开始
        if line.strip().startswith('class Article(') or line.strip().startswith('class Announce('):
            skip_lines = True
            skip_until_class = True
            print(f"🗑️  移除模型: {line.strip()}")
            continue
        
        # 如果在跳过模式中，检查是否遇到下一个类或文件结束
        if skip_lines:
            # 如果遇到新的类定义或文件结束，停止跳过
            if line.strip().startswith('class ') and not line.strip().startswith('class Article') and not line.strip().startswith('class Announce'):
                skip_lines = False
                skip_until_class = False
                new_lines.append(line)
            elif line.strip() == '' and skip_until_class:
                # 空行，继续跳过
                continue
            elif not line.strip() and not skip_until_class:
                # 如果不在类定义中且遇到空行，可能是类结束
                skip_lines = False
                new_lines.append(line)
            # 否则继续跳过这一行
            continue
        else:
            new_lines.append(line)
    
    # 写入新内容
    new_content = '\n'.join(new_lines)
    
    # 清理多余的空行
    new_content = '\n'.join([line for line in new_content.split('\n') if line.strip() or new_content.split('\n').index(line) == 0 or new_content.split('\n')[new_content.split('\n').index(line)-1].strip()])
    
    with open(models_file, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print("✅ sitecfg/models.py 清理完成")
    
    # 清理admin.py中的相关注册
    cleanup_admin_file()
    
    print("🎉 sitecfg模型清理完成！")
    print("📝 请注意：")
    print("   1. 已备份原文件，如需恢复可使用备份")
    print("   2. 建议运行 python manage.py makemigrations sitecfg 生成迁移")
    print("   3. 然后运行 python manage.py migrate 应用更改")

def cleanup_admin_file():
    """清理admin.py中的相关注册"""
    admin_file = '/www/wwwroot/csgoskins.com.cn/server/sitecfg/admin.py'
    
    if not os.path.exists(admin_file):
        print("⚠️  sitecfg/admin.py 文件不存在，跳过admin清理")
        return
    
    with open(admin_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 备份admin文件
    backup_file = admin_file + '.backup'
    with open(backup_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    lines = content.split('\n')
    new_lines = []
    skip_lines = False
    
    for line in lines:
        # 跳过Article和Announce相关的导入和注册
        if ('Article' in line and ('import' in line or 'from' in line)) or \
           ('Announce' in line and ('import' in line or 'from' in line)) or \
           line.strip().startswith('@admin.register(Article') or \
           line.strip().startswith('@admin.register(Announce') or \
           line.strip().startswith('class ArticleAdmin') or \
           line.strip().startswith('class AnnounceAdmin'):
            skip_lines = True
            print(f"🗑️  移除admin注册: {line.strip()}")
            continue
        
        # 如果在跳过模式中，检查是否遇到下一个类或装饰器
        if skip_lines:
            if line.strip().startswith('@admin.register') or \
               line.strip().startswith('class ') and 'Admin' in line:
                skip_lines = False
                new_lines.append(line)
            elif line.strip() == '':
                # 空行可能表示类结束
                skip_lines = False
                new_lines.append(line)
            # 否则继续跳过
            continue
        else:
            new_lines.append(line)
    
    # 写入新内容
    new_content = '\n'.join(new_lines)
    with open(admin_file, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print("✅ sitecfg/admin.py 清理完成")

if __name__ == '__main__':
    print("⚠️  警告：此操作将修改sitecfg/models.py和admin.py文件")
    print("📋 请确保：")
    print("   1. 已经完成数据迁移到articles app")
    print("   2. 已经备份重要数据")
    print("   3. 在测试环境中验证过此操作")
    
    confirm = input("\n是否继续？(y/N): ")
    if confirm.lower() in ['y', 'yes']:
        cleanup_sitecfg_models()
    else:
        print("❌ 操作已取消")
