# WebSocket系统升级文档

## 概述

本次升级完善了UI端的WebSocket功能，以适配服务端的重大改动。主要改进包括：

1. **统一的Socket房间管理**
2. **增强的消息格式处理**
3. **实时动画同步系统**
4. **改进的错误处理和重连机制**

## 主要变更

### 1. 新增Socket房间管理器

**文件**: `ui/utils/socket-manager.ts`

提供统一的WebSocket房间订阅管理：

```typescript
// 使用示例
const socketRoomManager = useSocketRoomManager()

// 加入房间
await socketRoomManager.joinRoom(socketRooms.monitor)
await socketRoomManager.joinRoom(socketRooms.caseRecords('case_key'))
await socketRoomManager.joinRoom(socketRooms.battle('battle_id'))

// 监听事件
socketRoomManager.addEventListener(socketEvents.monitor.update, handleUpdate)
```

### 2. 更新的Socket Store

**文件**: `ui/stores/socket.ts`

- 增强了消息处理逻辑
- 支持新的消息格式 `[messageType, action, payload, socketId]`
- 添加了 `sendMessage` 方法支持房间管理

### 3. 组件更新

#### 首页LiveOpenings组件
**文件**: `ui/components/home/<USER>

- 集成新的Socket房间管理器
- 自动加入监控房间接收实时开箱数据
- 改进的事件处理机制

#### 首页HomeStats组件
**文件**: `ui/components/home/<USER>

- 使用新的Socket事件系统
- 实时更新统计数据
- 优化的数据刷新逻辑

#### 开箱详情页
**文件**: `ui/pages/cases/[key].vue`

- 加入开箱记录房间
- 实时更新开箱记录和数量
- 针对特定箱子的数据过滤

#### 对战详情页
**文件**: `ui/pages/battle/[id].vue`

- 集成实时对战同步系统
- 支持实时动画同步
- 增强的状态管理

### 4. 新增实时同步组合函数

**文件**: `ui/composables/useBattleRealtimeSync.ts`

专门用于对战页面的实时WebSocket同步：

```typescript
const { state, onRoomUpdate, onBattleStart, onBattleEnd } = useBattleRealtimeSync(battleId)

// 监听对战事件
onRoomUpdate((data) => {
  // 处理房间更新
})

onBattleStart((data) => {
  // 处理对战开始
})
```

## 消息格式

### 服务端到客户端

```javascript
// 新格式
[messageType, action, payload, socketId?]

// 示例
['monitor', 'update', { user_number: 1000, case_number: 5000 }]
['boxroom', 'update', { uid: 'battle_123', state: 'running' }]
['case_records', 'new', { id: 123, item_info: {...}, case_info: {...} }]
```

### 客户端到服务端

```javascript
// 监控房间
socket.emit('monitor', ['join', 'monitor'])
socket.emit('monitor', ['get_stats'])
socket.emit('monitor', ['case_records'])

// 对战房间
socket.emit('join', 'boxroom')
socket.emit('join', 'battle_id')
```

## 事件系统

### 全局事件

```typescript
// 监控事件
'socket:monitor:update'    // 监控数据更新
'socket:monitor:stats'     // 统计数据更新

// 开箱记录事件
'socket:case_records:update'  // 开箱记录更新

// 对战事件
'socket:battle:update'     // 对战状态更新
'socket:battle:start'      // 对战开始
'socket:battle:end'        // 对战结束
'socket:battle:cancel'     // 对战取消
```

## 房间配置

```typescript
// 预定义房间配置
export const socketRooms = {
  // 监控房间 - 用于首页统计
  monitor: {
    room: 'monitor',
    action: 'join'
  },
  
  // 开箱记录房间 - 用于开箱详情页
  caseRecords: (caseKey: string) => ({
    room: 'case_records',
    action: 'join',
    data: { case_key: caseKey }
  }),
  
  // 对战房间 - 用于对战详情页
  battle: (battleId: string) => ({
    room: 'boxroomdetail',
    action: 'join',
    data: { room_id: battleId }
  })
}
```

## 测试

### 运行WebSocket测试

```bash
# 安装依赖
npm install socket.io-client

# 运行测试脚本
node scripts/test_websocket_ui.js
```

### 测试覆盖

- ✅ WebSocket连接
- ✅ 监控房间功能
- ✅ 开箱记录房间
- ✅ 对战房间功能
- ✅ 消息格式兼容性

## 兼容性

### 向后兼容

- 保留了旧的消息格式支持
- 现有的事件监听器继续工作
- 渐进式升级，不影响现有功能

### 浏览器支持

- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 79+

## 性能优化

### 连接管理

- 智能重连机制
- 连接状态监控
- 自动清理无用订阅

### 消息处理

- 消息去重
- 批量处理
- 内存优化

### 动画同步

- 时间同步算法
- 网络延迟补偿
- 平滑动画插值

## 故障排除

### 常见问题

1. **连接失败**
   - 检查WebSocket服务器状态
   - 确认端口和URL配置
   - 查看浏览器控制台错误

2. **消息丢失**
   - 检查房间订阅状态
   - 确认消息格式正确
   - 查看网络连接质量

3. **动画不同步**
   - 检查时间同步状态
   - 确认事件监听器正常
   - 查看动画状态管理

### 调试工具

```typescript
// 启用调试模式
const socketStore = useSocketStore()
socketStore.enableDebug()

// 查看连接状态
console.log('Socket状态:', socketStore.isConnected)
console.log('消息统计:', socketStore.getMessageStats())
```

## 部署注意事项

1. **服务端配置**
   - 确保WebSocket服务器正常运行
   - 检查Redis连接配置
   - 验证消息发布订阅功能

2. **客户端配置**
   - 更新Socket.IO客户端版本
   - 配置正确的服务器地址
   - 启用必要的传输协议

3. **监控**
   - 监控WebSocket连接数
   - 跟踪消息处理性能
   - 记录错误和异常

## 后续计划

- [ ] 添加消息压缩支持
- [ ] 实现离线消息缓存
- [ ] 优化移动端性能
- [ ] 增加更多调试工具
- [ ] 完善错误恢复机制
