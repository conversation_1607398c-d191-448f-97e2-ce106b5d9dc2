# LuckyBox 拉货模块分析文档

## 模块概述

LuckyBox模块是平台的拉货系统，实现了基于概率的物品获取玩法。用户可以选择心仪的物品并设置成功概率，系统根据概率计算费用，用户有机会以低价获得高价值物品。该模块提供了一种创新的物品获取方式，增强了平台的娱乐性和期待感。

## 目录结构

```
luckybox/
├── __init__.py
├── admin.py                 # Django管理后台配置
├── apps.py                  # 应用配置
├── business.py              # 核心业务逻辑
├── models.py                # 数据模型定义
├── serializers.py           # API序列化器
├── tests.py                 # 单元测试
├── urls.py                  # URL路由配置
├── views.py                 # API视图
├── model_signals.py         # 模型信号处理
└── service/                 # 服务层代码
    └── migrations/          # 数据库迁移文件
```

## 数据模型分析

### 1. LuckyBoxGroup - 物品分组
```python
class LuckyBoxGroup(models.Model):
    market_name = models.CharField(max_length=128)           # 市场名称
    market_hash_name = models.CharField(max_length=128)      # 市场哈希名称
    type = models.CharField(max_length=128)                  # 物品类型
    weapon = models.CharField(max_length=128)                # 武器类型
    price = models.FloatField(default=0)                     # 价格
    icon_url = models.CharField(max_length=256)              # 图标URL
    rarity_color = models.CharField(max_length=128)          # 稀有度颜色
```

**特点：**
- 按武器和外观进行物品分组
- 动态价格管理（选择最低价格）
- 支持图标和稀有度展示

### 2. LuckyBoxItem - 具体物品
```python
class LuckyBoxItem(models.Model):
    item_info = models.OneToOneField(ItemInfo)               # 物品信息
    group = models.ForeignKey(LuckyBoxGroup)                 # 所属分组
    price = models.FloatField(default=0)                     # 物品价格
```

### 3. LuckyRecommendGroup - 推荐分组
```python
class LuckyRecommendGroup(models.Model):
    group = models.ForeignKey(LuckyBoxGroup)                 # 推荐的物品分组
```

**用途：** 热门物品推荐展示

### 4. LuckyBoxCategory - 物品分类
```python
class LuckyBoxCategory(models.Model):
    category = models.CharField(max_length=128)              # 分类名称
    image = models.ImageField()                              # 分类图片
    ico = models.CharField(max_length=128)                   # 图标
    name = models.CharField(max_length=128)                  # 显示名称
```

### 5. LuckyBoxRecord - 拉货记录
```python
class LuckyBoxRecord(ModelBase):
    user = models.ForeignKey(UserModel)                     # 用户
    item = models.ForeignKey(ItemInfo)                      # 获得物品
    origin_percent = models.FloatField(default=0)           # 原始概率
    percent = models.FloatField(default=0)                  # 实际概率
    coins = models.FloatField(default=0)                    # 消费金币
    percentage = models.FloatField(default=0)               # 随机百分比
    win = models.BooleanField(default=False)                # 是否成功
    target = models.ForeignKey(ItemInfo)                    # 目标物品
```

## API 接口分析

### 1. 拉货首页
**路径：** `GET /api/luckybox/`

**功能：** 获取拉货首页数据，包含各武器分类信息

**响应格式：**
```json
{
    "CSGO_Type_Rifle": {
        "ico": "icon_name",
        "name": "步枪",
        "icon_url": "image_url"
    },
    "CSGO_Type_Pistol": {
        "ico": "icon_name", 
        "name": "手枪",
        "icon_url": "image_url"
    }
}
```

### 2. 物品库存列表
**路径：** `GET /api/luckybox/inventory/`

**功能：** 获取可拉货的物品列表

**参数：**
- `type`: 物品类型 (支持特殊类型 "CSGO_Type_Hot" - 热门, "CSGO_Type_other" - 其他)
- `weapon`: 武器类型
- `search`: 搜索关键词
- `order`: 排序字段
- `minPrice`/`maxPrice`: 价格区间
- `page`/`pageSize`: 分页参数

### 3. 物品详情
**路径：** `GET /api/luckybox/inventory/item/`

**功能：** 获取指定分组下的所有磨损度物品

**参数：**
- `id`: 分组ID

### 4. 搜索物品
**路径：** `GET /api/luckybox/inventory/search/`

**功能：** 搜索物品并去重显示

**参数：**
- `weapon`: 武器类型
- `category`: 物品分类
- `q`: 搜索关键词

### 5. 随机物品
**路径：** `GET /api/luckybox/inventory/random/`

**功能：** 随机选择一个物品

**参数：**
- `type`: 物品类型
- `weapon`: 武器类型

### 6. 概率计算
**路径：** `GET /api/luckybox/percent/`

**功能：** 计算指定概率和物品的费用

**参数：**
- `percent`: 成功概率(%)
- `name`: 物品市场哈希名称

**公式：** `费用 = 物品价格 × 概率% × 系统费率%`

### 7. 执行拉货
**路径：** `POST /api/luckybox/lucky/`

**功能：** 执行拉货操作

**参数：**
```json
{
    "percent": 33,
    "target": "AK-47 | Redline (Field-Tested)"
}
```

**业务流程：**
1. 验证用户和参数
2. 计算费用并扣除余额
3. 生成随机数并判断是否成功
4. 成功则获得目标物品，失败获得低价值物品
5. 创建包裹物品和拉货记录
6. WebSocket通知成功记录

### 8. 公开记录
**路径：** `GET /api/luckybox/record/`

**功能：** 获取所有用户的拉货记录（公开展示）

### 9. 个人历史
**路径：** `GET /api/luckybox/history/`

**功能：** 获取当前用户的拉货历史

### 10. 规则说明
**路径：** `GET /api/luckybox/rule`

**功能：** 获取拉货规则说明

## 核心算法分析

### 1. 概率计算算法
```python
def get_lucky_percent(percent, name):
    """计算拉货费用"""
    # 获取最低概率限制
    lowest_percent = get_luckybox_lower_percent()
    
    # 验证概率范围
    if percent <= 0 or percent < lowest_percent:
        raise ValueError("Invalid percent")
    
    # 基础费用计算
    item_price = get_item_price(name)
    base_cost = item_price * percent / 100
    
    # 应用系统费率
    rate = get_luckyboxrate()
    final_cost = base_cost * rate / 100
    
    return round(final_cost, 2)
```

**费用公式：**
```
最终费用 = 物品价格 × 用户设置概率% × 系统费率%
```

### 2. 随机数生成和判断
```python
def create_percentage():
    """生成随机百分比"""
    percentage = numpy.random.uniform(0, 100)
    return percentage

def calc_lucky_result(percentage, bet_percent):
    """计算拉货结果"""
    # 当随机数大于(100-概率)时成功
    win = 100 - bet_percent < percentage
    return win
```

**成功判定：**
- 生成0-100的随机浮点数
- 当随机数 > (100 - 用户概率) 时成功
- 例如：30%概率时，随机数>70即成功

### 3. 失败物品匹配算法
```python
def match_item():
    """失败时返回的低价值物品"""
    free_hash_name = [
        'P250 | Boreal Forest (Field-Tested)',
        'Galil AR | Sage Spray (Field-Tested)',
        'Five-SeveN | Forest Night (Well-Worn)',
        # ... 更多低价值物品
    ]
    
    # 随机选择一个低价值物品
    selected_item = random.choice(free_hash_name)
    item = ItemInfo.objects.filter(market_hash_name=selected_item).first()
    return item
```

### 4. 用户等级优惠系统
```python
def apply_user_discount(user, original_percent):
    """应用用户等级优惠"""
    # 获取用户拉货等级折扣
    discount = get_luckybox_rate_type(user.extra.luckybox_rate_type)
    
    # 实际概率 = 原始概率 × 折扣%
    actual_percent = original_percent * discount / 100
    
    return actual_percent
```

**优惠机制：**
- 用户等级越高，实际成功概率越高
- 但显示给用户的仍是原始概率
- 提升了高等级用户的成功率

## 业务特性分析

### 1. 物品分组策略
- **按武器分组**: 同一武器的不同磨损度归为一组
- **价格动态更新**: 自动选择组内最低价格
- **推荐机制**: 热门物品独立推荐展示

### 2. 概率控制机制
- **最低概率限制**: 防止过低概率的恶意使用
- **最高概率限制**: 防止接近100%的套利行为
- **用户等级优惠**: 提升VIP用户体验

### 3. 经济平衡设计
- **费率调节**: 通过系统费率控制整体盈利
- **失败补偿**: 失败时仍给予低价值物品
- **价格跟踪**: 实时跟踪市场价格变化

## WebSocket 实时通信

### 消息格式
```python
def ws_send_box_game(data, action):
    """发送拉货WebSocket消息"""
    rt_msg = ['luckybox', action, data]
    redis_conn.publish('ws_channel', json.dumps(rt_msg))
```

**消息类型：**
- **new**: 新的成功拉货记录

**消息内容：**
```json
{
    "result": true,
    "data": {
        "case": {"cover": "/media/cases/xingyun.png", "name": "拉货"},
        "item": {...},
        "package": {...},
        "user": {...}
    }
}
```

## 存在的问题与风险

### 1. 算法公平性问题

**问题描述：**
- 使用numpy.random.uniform生成随机数，可能存在伪随机性
- 失败物品池固定，缺少动态性
- 缺少防操控机制

**影响：**
- 理论上存在被预测的可能
- 用户可能质疑公平性
- 监管合规风险

### 2. 经济平衡风险

**问题描述：**
- 高概率低费用可能导致套利
- 物品价格波动影响收益平衡
- 用户等级优惠可能过度倾斜

**影响：**
- 平台可能出现亏损
- 经济系统失衡
- 用户体验不公平

### 3. 数据一致性问题

**问题描述：**
- 物品价格更新可能不及时
- 分组价格计算逻辑复杂
- 缺少数据校验机制

**影响：**
- 费用计算错误
- 用户损失或平台损失
- 系统稳定性问题

### 4. 用户体验问题

**问题描述：**
- 概率理解可能存在误区
- 失败补偿物品价值过低
- 缺少详细的规则说明

**影响：**
- 用户满意度下降
- 投诉和纠纷增加
- 用户流失

## 改进建议

### 1. 算法公平性增强

#### 安全随机数生成
```python
import secrets
import hashlib
import time

class SecureLuckyAlgorithm:
    def __init__(self):
        self.entropy_sources = []
    
    def generate_secure_random(self, user_id, target_item, timestamp):
        """生成安全的随机数"""
        # 基础熵源
        server_seed = secrets.randbits(256)
        client_data = f"{user_id}:{target_item}:{timestamp}"
        
        # 外部熵源（区块链哈希、天气数据等）
        external_entropy = self.get_external_entropy()
        
        # 组合所有熵源
        combined_input = f"{server_seed}:{client_data}:{external_entropy}"
        
        # 使用SHA-256生成确定性随机数
        hash_result = hashlib.sha256(combined_input.encode()).hexdigest()
        random_number = int(hash_result[:16], 16) / (2**64)  # 转换为0-1之间的浮点数
        
        # 记录生成过程供审计
        self.log_random_generation(user_id, combined_input, hash_result, random_number)
        
        return random_number * 100  # 转换为0-100的百分比
    
    def get_external_entropy(self):
        """获取外部不可控的熵源"""
        # 可以使用区块链最新区块哈希、股票指数、天气数据等
        # 这里使用系统时间的微秒部分作为示例
        return str(time.time_ns())
    
    def log_random_generation(self, user_id, input_data, hash_result, random_number):
        """记录随机数生成过程供审计"""
        audit_log = {
            'user_id': user_id,
            'input_hash': hashlib.md5(input_data.encode()).hexdigest(),  # 输入数据的哈希
            'output_hash': hash_result,
            'random_result': random_number,
            'timestamp': time.time()
        }
        _logger.info(f"Lucky random generation: {json.dumps(audit_log)}")
```

#### 动态失败物品池
```python
class DynamicFailureItemPool:
    def __init__(self):
        self.price_thresholds = {
            'very_low': (0, 1),      # 非常低价: 0-1元
            'low': (1, 5),           # 低价: 1-5元  
            'medium': (5, 20),       # 中等: 5-20元
        }
    
    def get_failure_item(self, target_price, user_level=1):
        """根据目标物品价格动态选择失败物品"""
        # 根据目标价格选择失败物品价格区间
        if target_price < 50:
            price_range = self.price_thresholds['very_low']
        elif target_price < 200:
            price_range = self.price_thresholds['low']
        else:
            price_range = self.price_thresholds['medium']
        
        # 查询符合价格区间的物品
        failure_items = ItemInfo.objects.filter(
            item_price__price__gte=price_range[0],
            item_price__price__lte=price_range[1]
        ).order_by('?')[:20]  # 随机排序取20个
        
        if not failure_items:
            # 兜底：使用固定的低价物品
            return self.get_default_failure_item()
        
        # 随机选择一个
        selected_item = random.choice(failure_items)
        
        # 记录选择日志
        _logger.info(f"Selected failure item: {selected_item.market_hash_name}, "
                    f"price: {selected_item.item_price.price}, target_price: {target_price}")
        
        return selected_item
```

### 2. 经济平衡优化

#### 动态费率系统
```python
class DynamicRateManager:
    def __init__(self):
        self.base_rate = 120  # 基础费率120%
        self.adjustment_factors = {
            'profit_margin': 0.1,    # 利润率调节因子
            'volume_factor': 0.05,   # 交易量调节因子
            'price_volatility': 0.02 # 价格波动调节因子
        }
    
    def calculate_dynamic_rate(self, item_price, recent_volume, price_volatility):
        """计算动态费率"""
        rate = self.base_rate
        
        # 价格区间调节
        if item_price > 1000:
            rate *= 1.2  # 高价物品费率更高
        elif item_price < 50:
            rate *= 0.9  # 低价物品费率更低
        
        # 交易量调节
        if recent_volume > 100:
            rate *= 1.1  # 热门物品费率提高
        elif recent_volume < 10:
            rate *= 0.95  # 冷门物品费率降低
        
        # 价格波动调节
        if price_volatility > 0.2:
            rate *= 1.15  # 价格不稳定时费率提高
        
        # 限制费率范围
        rate = max(80, min(200, rate))  # 费率限制在80%-200%之间
        
        return round(rate, 2)
    
    def get_recent_statistics(self, item_name):
        """获取物品近期统计数据"""
        # 近7天的拉货记录
        recent_records = LuckyBoxRecord.objects.filter(
            target__market_hash_name=item_name,
            create_time__gte=timezone.now() - timedelta(days=7)
        )
        
        volume = recent_records.count()
        
        # 计算价格波动（可以从市场数据获取）
        price_volatility = self.calculate_price_volatility(item_name)
        
        return volume, price_volatility
```

#### 概率限制优化
```python
class ProbabilityLimiter:
    def __init__(self):
        self.limits = {
            'min_percent': 1,     # 最小1%
            'max_percent': 75,    # 最大75%
            'price_brackets': {
                (0, 50): {'min': 5, 'max': 80},      # 低价物品
                (50, 200): {'min': 3, 'max': 70},    # 中价物品  
                (200, 1000): {'min': 2, 'max': 60}, # 高价物品
                (1000, float('inf')): {'min': 1, 'max': 50}  # 超高价物品
            }
        }
    
    def validate_probability(self, percent, item_price, user_level):
        """验证概率的合理性"""
        # 根据物品价格获取概率限制
        for price_range, limits in self.limits['price_brackets'].items():
            if price_range[0] <= item_price < price_range[1]:
                min_percent = limits['min']
                max_percent = limits['max']
                break
        else:
            min_percent = self.limits['min_percent']
            max_percent = self.limits['max_percent']
        
        # 用户等级调节
        if user_level >= 5:  # VIP用户
            max_percent += 10
        
        # 验证范围
        if percent < min_percent:
            raise ValueError(f"Probability too low, minimum {min_percent}%")
        if percent > max_percent:
            raise ValueError(f"Probability too high, maximum {max_percent}%")
        
        return True
```

### 3. 数据一致性保障

#### 价格同步机制
```python
class PriceSyncManager:
    def __init__(self):
        self.sync_interval = 300  # 5分钟同步一次
        self.price_sources = ['steam', 'buff', 'c5game']  # 多个价格源
    
    def sync_all_prices(self):
        """同步所有物品价格"""
        groups = LuckyBoxGroup.objects.all()
        
        for group in groups:
            try:
                # 获取最新市场价格
                latest_price = self.get_latest_market_price(group.market_hash_name)
                
                # 更新分组价格
                if abs(group.price - latest_price) > group.price * 0.05:  # 价格变化超过5%
                    old_price = group.price
                    group.price = latest_price
                    group.save()
                    
                    _logger.info(f"Price updated: {group.market_name}, "
                               f"old: {old_price}, new: {latest_price}")
                
            except Exception as e:
                _logger.error(f"Failed to sync price for {group.market_name}: {e}")
    
    def get_latest_market_price(self, market_hash_name):
        """从多个源获取最新市场价格"""
        prices = []
        
        for source in self.price_sources:
            try:
                price = self.fetch_price_from_source(source, market_hash_name)
                if price > 0:
                    prices.append(price)
            except Exception as e:
                _logger.warning(f"Failed to fetch price from {source}: {e}")
        
        if not prices:
            raise ValueError("No valid price found")
        
        # 使用中位数作为最终价格
        prices.sort()
        median_price = prices[len(prices) // 2]
        
        return median_price
```

### 4. 用户体验优化

#### 智能推荐系统
```python
class LuckyBoxRecommendationEngine:
    def __init__(self):
        self.user_behavior_weight = 0.4    # 用户行为权重
        self.success_rate_weight = 0.3     # 成功率权重  
        self.popularity_weight = 0.3       # 热门度权重
    
    def get_personalized_recommendations(self, user, limit=10):
        """获取个性化推荐"""
        # 分析用户历史偏好
        user_preferences = self.analyze_user_preferences(user)
        
        # 获取候选物品
        candidates = self.get_candidate_items(user_preferences)
        
        # 计算推荐分数
        scored_items = []
        for item in candidates:
            score = self.calculate_recommendation_score(user, item, user_preferences)
            scored_items.append((item, score))
        
        # 排序并返回top N
        scored_items.sort(key=lambda x: x[1], reverse=True)
        return [item for item, score in scored_items[:limit]]
    
    def analyze_user_preferences(self, user):
        """分析用户偏好"""
        records = LuckyBoxRecord.objects.filter(user=user).order_by('-create_time')[:50]
        
        preferences = {
            'preferred_weapons': {},
            'preferred_price_range': None,
            'risk_tolerance': None
        }
        
        # 分析武器偏好
        for record in records:
            weapon = record.target.weapon
            preferences['preferred_weapons'][weapon] = preferences['preferred_weapons'].get(weapon, 0) + 1
        
        # 分析价格偏好
        target_prices = [get_item_price(record.target.market_hash_name) for record in records]
        if target_prices:
            preferences['preferred_price_range'] = (min(target_prices), max(target_prices))
        
        # 分析风险承受度（基于平均概率）
        avg_percent = sum(record.origin_percent for record in records) / len(records) if records else 50
        preferences['risk_tolerance'] = 'high' if avg_percent < 30 else 'medium' if avg_percent < 60 else 'low'
        
        return preferences
    
    def calculate_recommendation_score(self, user, item, preferences):
        """计算推荐分数"""
        score = 0
        
        # 武器偏好分数
        weapon_preference = preferences['preferred_weapons'].get(item.weapon, 0)
        score += weapon_preference * self.user_behavior_weight
        
        # 成功率分数（基于历史数据）
        success_rate = self.get_item_success_rate(item.market_hash_name)
        score += success_rate * self.success_rate_weight
        
        # 热门度分数
        popularity = self.get_item_popularity(item.market_hash_name)
        score += popularity * self.popularity_weight
        
        return score
```

## 监控和风控

### 异常检测系统
```python
class LuckyBoxMonitor:
    def __init__(self):
        self.alert_thresholds = {
            'high_success_rate': 0.8,      # 高成功率阈值
            'rapid_attempts': 20,          # 快速尝试阈值
            'unusual_profit': 10000,       # 异常盈利阈值
        }
    
    def monitor_user_behavior(self, user):
        """监控用户行为异常"""
        alerts = []
        
        # 检查成功率异常
        recent_records = LuckyBoxRecord.objects.filter(
            user=user,
            create_time__gte=timezone.now() - timedelta(days=7)
        )
        
        if recent_records.count() > 10:
            success_rate = recent_records.filter(win=True).count() / recent_records.count()
            if success_rate > self.alert_thresholds['high_success_rate']:
                alerts.append({
                    'type': 'high_success_rate',
                    'message': f'User has unusually high success rate: {success_rate:.2%}'
                })
        
        # 检查快速尝试
        rapid_attempts = recent_records.filter(
            create_time__gte=timezone.now() - timedelta(hours=1)
        ).count()
        
        if rapid_attempts > self.alert_thresholds['rapid_attempts']:
            alerts.append({
                'type': 'rapid_attempts',
                'message': f'User made {rapid_attempts} attempts in 1 hour'
            })
        
        return alerts
    
    def monitor_economic_health(self):
        """监控经济健康度"""
        # 计算总投入 vs 总产出
        total_input = LuckyBoxRecord.objects.aggregate(
            total=Sum('coins')
        )['total'] or 0
        
        total_output = 0
        for record in LuckyBoxRecord.objects.filter(win=True):
            item_price = get_item_price(record.target.market_hash_name)
            total_output += item_price
        
        # 添加失败补偿成本
        failure_cost = LuckyBoxRecord.objects.filter(win=False).count() * 2  # 假设平均2元
        total_output += failure_cost
        
        # 计算盈利率
        if total_input > 0:
            profit_rate = (total_input - total_output) / total_input
            
            if profit_rate < 0.1:  # 盈利率低于10%
                _logger.warning(f"Low profit rate detected: {profit_rate:.2%}")
            elif profit_rate > 0.5:  # 盈利率高于50%
                _logger.warning(f"High profit rate detected: {profit_rate:.2%}")
```

## 总结

LuckyBox模块实现了一个创新的概率式物品获取系统，为用户提供了以小博大的娱乐体验。当前设计在基本功能上较为完善，但在算法公平性、经济平衡、数据一致性等方面存在改进空间。建议按照上述优化方案逐步改进，重点关注系统的公平性验证、经济模型的稳定性和用户体验的完善。

## 接口清单

| 接口名称 | 方法 | 路径 | 状态 | 功能描述 |
|---------|------|------|------|----------|
| 拉货首页 | GET | `/api/luckybox/` | 正常 | 获取武器分类信息 |
| 物品库存列表 | GET | `/api/luckybox/inventory/` | 正常 | 获取可拉货物品 |
| 物品详情 | GET | `/api/luckybox/inventory/item/` | 正常 | 获取物品磨损度列表 |
| 搜索物品 | GET | `/api/luckybox/inventory/search/` | 正常 | 搜索物品 |
| 随机物品 | GET | `/api/luckybox/inventory/random/` | 正常 | 随机选择物品 |
| 概率计算 | GET | `/api/luckybox/percent/` | 正常 | 计算拉货费用 |
| 执行拉货 | POST | `/api/luckybox/lucky/` | 正常 | 执行拉货操作 |
| 公开记录 | GET | `/api/luckybox/record/` | 正常 | 获取公开记录 |
| 个人历史 | GET | `/api/luckybox/history/` | 正常 | 获取个人历史 |
| 规则说明 | GET | `/api/luckybox/rule` | 正常 | 获取规则说明 |
