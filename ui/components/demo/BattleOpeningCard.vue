<template>
  <div class="battle-card relative overflow-hidden">
    <!-- 战场背景 -->
    <div class="absolute inset-0 bg-gradient-to-br from-red-900/20 via-gray-900/40 to-orange-900/20"></div>
    <div class="absolute inset-0 bg-military-pattern opacity-10"></div>
    
    <!-- 爆炸效果 -->
    <div class="absolute top-2 right-2 w-6 h-6 bg-orange-400 rounded-full animate-explosion"></div>
    
    <div class="relative bg-gray-900/85 backdrop-blur-md rounded-lg border-2 border-orange-500/40 p-4">
      <!-- 军衔标识 -->
      <div class="absolute -top-3 left-4">
        <div class="bg-orange-500 text-black px-3 py-1 rounded-full text-xs font-bold flex items-center space-x-1">
          <span>⚡</span>
          <span>ELITE OPENING</span>
        </div>
      </div>
      
      <!-- 主要战斗区域 -->
      <div class="mt-2">
        <!-- 玩家信息栏 -->
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center space-x-3">
            <div class="relative">
              <div class="w-10 h-10 bg-gradient-to-br from-red-500 to-orange-600 rounded border-2 border-orange-400 flex items-center justify-center text-white font-bold text-sm">
                {{ record.user_name?.charAt(0).toUpperCase() || 'U' }}
              </div>
              <div class="absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-gray-900 animate-pulse"></div>
            </div>
            <div>
              <div class="text-orange-400 font-bold">{{ record.user_name }}</div>
              <div class="text-orange-400/60 text-xs">⭐ Elite Player</div>
            </div>
          </div>
          
          <!-- 战斗统计 -->
          <div class="text-right">
            <div class="text-red-400 text-xs">🎯 HEADSHOT</div>
            <div class="text-orange-400 text-xs">💀 LEGENDARY</div>
          </div>
        </div>
        
        <!-- 武器展示区 -->
        <div class="bg-black/30 rounded-lg p-4 border border-orange-500/30">
          <div class="flex items-center space-x-4">
            <!-- 箱子（弹药箱） -->
            <div class="flex-shrink-0 relative">
              <div class="absolute inset-0 bg-orange-500/20 blur-lg rounded animate-pulse"></div>
                             <img
                 :src="record.case_info?.cover"
                 :alt="record.case_info?.name"
                 class="relative w-16 h-16 object-contain bg-gray-800"
                 @error="(e) => (e.target as HTMLImageElement).src = 'https://placehold.co/64x64/ea580c/fed7aa?text=SUPPLY'"
               />
              <div class="absolute -bottom-1 left-1/2 transform -translate-x-1/2 bg-orange-500 text-black text-xs px-2 rounded-full font-bold">
                SUPPLY
              </div>
            </div>
            
            <!-- 战斗箭头 -->
            <div class="flex-grow flex items-center justify-center">
              <div class="flex space-x-1">
                <div class="text-red-400 text-xl animate-ping">💥</div>
                <div class="text-orange-400 text-xl">→</div>
                <div class="text-yellow-400 text-xl animate-ping">✨</div>
              </div>
            </div>
            
            <!-- 获得武器 -->
            <div class="flex-shrink-0 relative">
              <div class="absolute inset-0 bg-red-500/20 blur-lg rounded animate-pulse"></div>
                             <img
                 :src="record.item_info?.image"
                 :alt="record.item_info?.name"
                 class="relative w-20 h-20 object-contain transform hover:scale-110 transition-transform duration-300 bg-gray-800"
                 @error="(e) => (e.target as HTMLImageElement).src = 'https://placehold.co/80x80/dc2626/fecaca?text=WEAPON'"
               />
              <div class="absolute -bottom-1 left-1/2 transform -translate-x-1/2 bg-red-500 text-white text-xs px-2 rounded-full font-bold">
                WEAPON
              </div>
            </div>
          </div>
          
          <!-- 武器信息 -->
          <div class="mt-3 text-center">
            <div class="text-yellow-400 font-bold">{{ getSkinName(record.item_info?.name) }}</div>
            <div class="text-orange-400/80 text-sm">{{ getExteriorName() }} | {{ getRarityLevel() }}</div>
          </div>
          
          <!-- 弹药条 -->
          <div class="mt-3">
            <div class="flex justify-between text-xs text-orange-400/60 mb-1">
              <span>Ammo</span>
              <span>30/30</span>
            </div>
            <div class="h-2 bg-gray-700 rounded-full overflow-hidden">
              <div class="h-full bg-gradient-to-r from-red-500 to-orange-400 rounded-full animate-ammo-load"></div>
            </div>
          </div>
        </div>
        
        <!-- 战绩统计 -->
        <div class="flex justify-between mt-3 text-xs">
          <div class="flex items-center space-x-1 text-green-400">
            <span>🏆</span>
            <span>Rare Drop</span>
          </div>
          <div class="flex items-center space-x-1 text-orange-400">
            <span>⚡</span>
            <span>Just Now</span>
          </div>
          <div class="flex items-center space-x-1 text-red-400">
            <span>🔥</span>
            <span>Hot Item</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  record: any
}

const props = defineProps<Props>()

const getSkinName = (fullName?: string): string => {
  if (!fullName) return 'Unknown Weapon'
  const parts = fullName.split(' | ')
  if (parts.length < 2) return fullName
  const skinPart = parts[1].trim()
  return skinPart.split(' (')[0].trim()
}

const getExteriorName = (): string => {
  return props.record.item_info?.item_exterior?.exterior_name || 'Factory New'
}

const getRarityLevel = (): string => {
  return 'Covert' // 可以根据实际数据调整
}
</script>

<style lang="scss" scoped>
.battle-card {
  animation: battleDrop 1s ease-out;
}

.bg-military-pattern {
  background-image: 
    repeating-linear-gradient(
      45deg,
      transparent,
      transparent 10px,
      rgba(255, 140, 0, 0.05) 10px,
      rgba(255, 140, 0, 0.05) 20px
    );
}

@keyframes battleDrop {
  0% {
    transform: translateY(-30px) rotate(-5deg) scale(0.9);
    opacity: 0;
  }
  50% {
    transform: translateY(5px) rotate(2deg) scale(1.05);
  }
  100% {
    transform: translateY(0) rotate(0deg) scale(1);
    opacity: 1;
  }
}

@keyframes explosion {
  0%, 100% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.5);
    opacity: 1;
  }
}

@keyframes ammo-load {
  0% { width: 0%; }
  50% { width: 70%; }
  100% { width: 100%; }
}

.animate-explosion {
  animation: explosion 2s ease-in-out infinite;
}

.animate-ammo-load {
  animation: ammo-load 2s ease-out infinite;
}
</style> 