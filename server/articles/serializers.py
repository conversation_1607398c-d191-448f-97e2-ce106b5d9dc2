from rest_framework import serializers
from django.utils import timezone
from .models import ContentCategory, ContentTag, Content, ContentAttachment


class ContentCategorySerializer(serializers.ModelSerializer):
    content_count = serializers.SerializerMethodField()
    
    class Meta:
        model = ContentCategory
        fields = [
            'id', 'name', 'slug', 'description', 'icon', 'color', 
            'sort_order', 'is_active', 'content_count', 'created_at'
        ]
    
    def get_content_count(self, obj):
        return obj.contents.filter(status='published').count()


class ContentTagSerializer(serializers.ModelSerializer):
    content_count = serializers.SerializerMethodField()
    
    class Meta:
        model = ContentTag
        fields = ['id', 'name', 'slug', 'color', 'is_active', 'content_count']
    
    def get_content_count(self, obj):
        return obj.contents.filter(status='published').count()


class ContentAttachmentSerializer(serializers.ModelSerializer):
    file_size_display = serializers.SerializerMethodField()
    
    class Meta:
        model = ContentAttachment
        fields = [
            'id', 'name', 'file_path', 'file_size', 'file_size_display',
            'file_type', 'description', 'download_count'
        ]
    
    def get_file_size_display(self, obj):
        if obj.file_size < 1024:
            return f"{obj.file_size} B"
        elif obj.file_size < 1024 * 1024:
            return f"{obj.file_size / 1024:.1f} KB"
        else:
            return f"{obj.file_size / (1024 * 1024):.1f} MB"


class ContentListSerializer(serializers.ModelSerializer):
    """内容列表序列化器 - 用于列表页面"""
    category_name = serializers.CharField(source='category.name', read_only=True)
    category_color = serializers.CharField(source='category.color', read_only=True)
    author_name = serializers.CharField(source='author.username', read_only=True)
    tags = ContentTagSerializer(many=True, read_only=True)
    is_published = serializers.ReadOnlyField()
    is_expired = serializers.ReadOnlyField()
    
    class Meta:
        model = Content
        fields = [
            'id', 'title', 'slug', 'subtitle', 'excerpt', 'content_type',
            'category_name', 'category_color', 'tags', 'status', 'priority',
            'is_featured', 'is_pinned', 'publish_date', 'expire_date',
            'author_name', 'featured_image', 'thumbnail', 'view_count',
            'is_published', 'is_expired', 'created_at', 'updated_at'
        ]


class ContentDetailSerializer(serializers.ModelSerializer):
    """内容详情序列化器 - 用于详情页面"""
    category = ContentCategorySerializer(read_only=True)
    tags = ContentTagSerializer(many=True, read_only=True)
    attachments = ContentAttachmentSerializer(many=True, read_only=True)
    author_name = serializers.CharField(source='author.username', read_only=True)
    editor_name = serializers.CharField(source='editor.username', read_only=True)
    is_published = serializers.ReadOnlyField()
    is_expired = serializers.ReadOnlyField()
    
    class Meta:
        model = Content
        fields = [
            'id', 'title', 'slug', 'subtitle', 'excerpt', 'content',
            'content_type', 'category', 'tags', 'status', 'priority',
            'is_featured', 'is_pinned', 'publish_date', 'expire_date',
            'author_name', 'editor_name', 'featured_image', 'thumbnail',
            'seo_title', 'seo_description', 'seo_keywords', 'view_count',
            'allow_comments', 'custom_css_class', 'background_color',
            'text_color', 'attachments', 'is_published', 'is_expired',
            'created_at', 'updated_at'
        ]


class ContentCreateUpdateSerializer(serializers.ModelSerializer):
    """内容创建/更新序列化器"""
    
    class Meta:
        model = Content
        fields = [
            'title', 'slug', 'subtitle', 'excerpt', 'content', 'content_type',
            'category', 'tags', 'status', 'priority', 'is_featured', 'is_pinned',
            'publish_date', 'expire_date', 'featured_image', 'thumbnail',
            'seo_title', 'seo_description', 'seo_keywords', 'allow_comments',
            'custom_css_class', 'background_color', 'text_color', 'sort_order'
        ]
    
    def validate_publish_date(self, value):
        if value and value < timezone.now() and self.instance is None:
            # 新建内容时，发布时间不能是过去时间
            raise serializers.ValidationError("发布时间不能是过去时间")
        return value
    
    def validate_expire_date(self, value):
        if value and value <= timezone.now():
            raise serializers.ValidationError("过期时间必须是未来时间")
        return value
    
    def validate(self, data):
        publish_date = data.get('publish_date')
        expire_date = data.get('expire_date')
        
        if publish_date and expire_date and expire_date <= publish_date:
            raise serializers.ValidationError("过期时间必须晚于发布时间")
        
        return data


class AnnouncementSerializer(serializers.ModelSerializer):
    """公告专用序列化器"""
    category_name = serializers.CharField(source='category.name', read_only=True)
    category_color = serializers.CharField(source='category.color', read_only=True)
    
    class Meta:
        model = Content
        fields = [
            'id', 'title', 'slug', 'subtitle', 'excerpt', 'content',
            'category_name', 'category_color', 'priority', 'is_featured',
            'is_pinned', 'publish_date', 'expire_date', 'featured_image',
            'view_count', 'background_color', 'text_color', 'created_at'
        ]
    
    def to_representation(self, instance):
        # 只返回公告类型的内容
        if instance.content_type != 'announcement':
            return None
        return super().to_representation(instance)
