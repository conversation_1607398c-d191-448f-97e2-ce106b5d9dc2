from django.conf.urls import url

from . import views

app_name = 'articles'

urlpatterns = [
    # 分类和标签
    url(r'^categories/$', views.ContentCategoryListView.as_view(), name='category-list'),
    url(r'^tags/$', views.ContentTagListView.as_view(), name='tag-list'),

    # 内容
    url(r'^contents/$', views.ContentListView.as_view(), name='content-list'),
    url(r'^contents/(?P<slug>[\w-]+)/$', views.ContentDetailView.as_view(), name='content-detail'),

    # 公告
    url(r'^announcements/$', views.AnnouncementListView.as_view(), name='announcement-list'),
    url(r'^announcements/homepage/$', views.homepage_announcements, name='homepage-announcements'),

    # 推荐内容
    url(r'^featured/$', views.FeaturedContentView.as_view(), name='featured-content'),

    # 按分类和标签获取内容
    url(r'^category/(?P<category_slug>[\w-]+)/$', views.content_by_category, name='content-by-category'),
    url(r'^tag/(?P<tag_slug>[\w-]+)/$', views.content_by_tag, name='content-by-tag'),
]
