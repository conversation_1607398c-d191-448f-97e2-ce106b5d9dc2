# -*- coding: utf-8 -*-
# Generated by Django 1.11.29 on 2025-04-27 14:14
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('sitecfg', '0035_auto_20250427_2134'),
    ]

    operations = [
        migrations.AddField(
            model_name='seo',
            name='description_en',
            field=models.TextField(blank=True, default=None, max_length=255, null=True, verbose_name='Description'),
        ),
        migrations.AddField(
            model_name='seo',
            name='description_zh_hans',
            field=models.TextField(blank=True, default=None, max_length=255, null=True, verbose_name='Description'),
        ),
        migrations.AddField(
            model_name='seo',
            name='keywords_en',
            field=models.CharField(blank=True, default=None, max_length=255, null=True, verbose_name='Keywords'),
        ),
        migrations.AddField(
            model_name='seo',
            name='keywords_zh_hans',
            field=models.Char<PERSON>ield(blank=True, default=None, max_length=255, null=True, verbose_name='Keywords'),
        ),
        migrations.AddField(
            model_name='seo',
            name='subtitle_en',
            field=models.CharField(blank=True, default=None, max_length=128, null=True, verbose_name='Subtitle'),
        ),
        migrations.AddField(
            model_name='seo',
            name='subtitle_zh_hans',
            field=models.CharField(blank=True, default=None, max_length=128, null=True, verbose_name='Subtitle'),
        ),
        migrations.AddField(
            model_name='seo',
            name='title_en',
            field=models.CharField(blank=True, default=None, max_length=128, null=True, verbose_name='Title'),
        ),
        migrations.AddField(
            model_name='seo',
            name='title_zh_hans',
            field=models.CharField(blank=True, default=None, max_length=128, null=True, verbose_name='Title'),
        ),
    ]
