# automatically generated by the FlatBuffers compiler, do not modify

# namespace: proto

import flatbuffers

class AuthCraRequest(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAsAuthCraRequest(cls, buf, offset):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = AuthCraRequest()
        x.Init(buf, n + offset)
        return x

    # AuthCraRequest
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

def AuthCraRequestStart(builder): builder.StartObject(0)
def AuthCraRequestEnd(builder): return builder.EndObject()
