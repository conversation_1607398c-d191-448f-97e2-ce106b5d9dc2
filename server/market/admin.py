import json
import logging
import os

from datetime import timed<PERSON>ta

from django.contrib import admin, messages
from django.contrib.admin import TabularInline, StackedInline
from django.contrib.admin.views.main import ChangeList
from django.db.models import Sum
from django.utils.translation import gettext_lazy as _

from market.models import MarketItem


@admin.register(MarketItem)
class MarketItemAdmin(admin.ModelAdmin):
    list_display = ('item_info', 'rarity_cn', 'exterior', 'dark_gold')
    list_filter = ('item_info__type', 'dark_gold', 'exterior', 'rarity_cn')