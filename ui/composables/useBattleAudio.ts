// composables/useBattleAudio.ts
import { ref, onUnmounted } from 'vue'

export interface BattleAudioOptions {
  volume?: number
  autoplay?: boolean
  loop?: boolean
}

export const useBattleAudio = () => {
  // 响应式状态
  const isMuted = ref(false)
  const volume = ref(0.7)
  const isLoading = ref(false)
  
  // 音频实例映射
  const audioInstances = new Map<string, HTMLAudioElement>()
  
  // 音效文件映射
  const audioFiles = {
    roundStart: '/audio/last-tick.mp3',        // 轮次开始 - 使用last-tick.mp3
    roundEnd: '/audio/round-end.ogg',          // 轮次结束 - 使用新的round-end.ogg
    rollStart: '/audio/tick.mp3',              // 开箱开始 - 使用tick.mp3（更短促）
    rollEnd: '/audio/rare-special-item.mp3',   // 开箱结束 - 使用rare-special-item.mp3
    rollRun: '/audio/open-case-1.wav',           // 开箱滚轮 - 使用新的round-run.ogg
    winnerAnnounce: '/audio/win.mp3',          // 获胜者宣布 - 使用win.mp3
    itemDrop: '/audio/rare-special-item.mp3',  // 物品掉落 - 使用rare-special-item.mp3
    buttonClick: '/audio/tick.mp3',            // 按钮点击 - 使用tick.mp3
    notification: '/audio/chat_sound.mp3',     // 通知音效 - 使用chat_sound.mp3
    background: '/audio/boost-on.mp3',         // 背景音乐 - 临时使用boost-on.mp3
    // 🚀 新增：动画相关音效
    animationStart: '/audio/tick.mp3',         // 动画开始
    animationProgress: '/audio/open-case-1.wav', // 动画进行中
    animationComplete: '/audio/rare-special-item.mp3', // 动画完成
    calculationStart: '/audio/last-tick.mp3',  // 计算开始
    calculationProgress: '/audio/tick.mp3',    // 计算进行中
    calculationComplete: '/audio/win.mp3',     // 计算完成
    playerWin: '/audio/rare-special-item.mp3', // 玩家获胜
    battleComplete: '/audio/win.mp3',          // 对战完成
    itemRare: '/audio/rare-special-item.mp3',  // 稀有物品
    itemLegendary: '/audio/win.mp3',           // 传说物品
    itemCommon: '/audio/tick.mp3'              // 普通物品
  }
  
  // 创建音频实例
  const createAudioInstance = (key: string, src: string, options: BattleAudioOptions = {}): HTMLAudioElement => {
    const audio = new Audio(src)
    audio.volume = options.volume ?? volume.value
    audio.loop = options.loop ?? false
    audio.preload = 'auto'
    
    // 处理加载错误
    audio.onerror = () => {
      console.warn(`Failed to load audio: ${src}`)
    }
    
    audioInstances.set(key, audio)
    return audio
  }
  
  // 初始化音频系统
  const initAudio = async (): Promise<void> => {
    isLoading.value = true
    
    try {
      // 预加载所有音效
      const loadPromises = Object.entries(audioFiles).map(([key, src]) => {
        return new Promise<void>((resolve, reject) => {
          const audio = createAudioInstance(key, src)
          
          audio.addEventListener('canplaythrough', () => resolve(), { once: true })
          audio.addEventListener('error', reject, { once: true })
          
          // 超时处理
          setTimeout(() => reject(new Error(`Timeout loading ${key}`)), 5000)
        })
      })
      
      await Promise.allSettled(loadPromises)
    } catch (error) {
      console.warn('Some audio files failed to load:', error)
    } finally {
      isLoading.value = false
    }
  }
  
  // 播放音效
  const playSound = (soundName: string, options?: { volume?: number; rarity?: string; loop?: boolean }) => {
    try {
      const audio = audioInstances.get(soundName);
      if (audio) {
        // 重置音频到开始位置
        audio.currentTime = 0;
        
        // 设置音量（默认为 0.3）
        audio.volume = options?.volume ?? 0.3;

        // 若显式传入 loop 值则使用，否则确保关闭循环，防止残留 loop 状态
        if (typeof options?.loop === 'boolean') {
          audio.loop = options.loop;
        } else {
          audio.loop = false;
        }
        
        // 播放音效
        audio.play().catch(() => {
          // 静默处理播放错误
        });
      }
    } catch (error) {
      // 静默处理音效错误
    }
  };
  
  // 停止音效
  const stopSound = (soundKey: keyof typeof audioFiles): void => {
    const audio = audioInstances.get(soundKey)
    if (audio) {
      audio.pause()
      audio.currentTime = 0
    }
  }
  
  // 停止所有音效
  const stopAllSounds = (): void => {
    audioInstances.forEach(audio => {
      audio.pause()
      audio.currentTime = 0
    })
  }
  
  // 设置音量
  const setVolume = (newVolume: number): void => {
    volume.value = Math.max(0, Math.min(1, newVolume))
    audioInstances.forEach(audio => {
      audio.volume = volume.value
    })
  }
  
  // 静音/取消静音
  const toggleMute = (muted?: boolean): void => {
    // 如果传入了muted参数，直接设置；否则切换状态
    if (typeof muted === 'boolean') {
      isMuted.value = muted;
    } else {
      isMuted.value = !isMuted.value;
    }
    
    console.log('[🎰BATTLE-AUDIO] 静音状态已更新', {
      isMuted: isMuted.value,
      mutedParam: muted
    });
    
    // 如果取消静音且有背景音乐在播放，恢复播放
    if (!isMuted.value) {
      const bgAudio = audioInstances.get('background')
      if (bgAudio && bgAudio.paused) {
        playSound('background', { loop: true, volume: 0.3 })
      }
    } else {
      // 静音时停止所有音效
      stopAllSounds()
    }
  }
  
  // 播放背景音乐
  const playBackgroundMusic = (): void => {
    if (!isMuted.value) {
      playSound('background', { loop: true, volume: 0.3 })
    }
  }
  
  // 停止背景音乐
  const stopBackgroundMusic = (): void => {
    stopSound('background')
  }
  
  // 对战音效序列
  const playBattleSequence = {
    // 对战开始
    battleStart: async () => {
      await playSound('roundStart')
      // playBackgroundMusic() // 移除自动背景音乐播放，避免音效循环
    },
    
    // 轮次开始
    roundStart: async () => {
      await playSound('roundStart')
    },
    
    // 开箱开始
    rollStart: async () => {
      await playSound('rollStart')
    },
    
    // 开箱滚轮（循环播放）
    rollRun: async () => {
      await playSound('rollRun', { loop: false, volume: 0.5 })
    },
    
    // 停止滚轮音效
    stopRollRun: () => {
      stopSound('rollRun')
    },
    
    // 开箱结束
    rollEnd: async (isRareItem = false) => {
      // 先停止滚轮音效
      stopSound('rollRun')
      // 播放结束音效
      await playSound('rollEnd')
    },
    
    // 🚀 新增：动画相关音效
    // 动画开始
    animationStart: async () => {
      await playSound('animationStart')
    },
    
    // 动画进行中
    animationProgress: async () => {
      await playSound('animationProgress', { volume: 0.3 })
    },
    
    // 动画完成
    animationComplete: async (itemRarity = 'common') => {
      // 根据物品稀有度播放不同音效
      switch (itemRarity.toLowerCase()) {
        case 'legendary':
        case 'contraband':
          await playSound('itemLegendary')
          break
        case 'rare':
        case 'epic':
          await playSound('itemRare')
          break
        default:
          await playSound('itemCommon')
      }
    },
    
    // 计算开始
    calculationStart: async () => {
      await playSound('calculationStart')
    },
    
    // 计算进行中
    calculationProgress: async () => {
      await playSound('calculationProgress', { volume: 0.4 })
    },
    
    // 计算完成
    calculationComplete: async () => {
      await playSound('calculationComplete')
    },
    
    // 玩家获胜
    playerWin: async () => {
      await playSound('playerWin')
    },
    
    // 对战完成
    battleComplete: async () => {
      await playSound('battleComplete')
    },
    
    // 物品掉落（根据稀有度）
    itemDrop: async (rarity = 'common') => {
      switch (rarity.toLowerCase()) {
        case 'legendary':
        case 'contraband':
          await playSound('itemLegendary')
          break
        case 'rare':
        case 'epic':
          await playSound('itemRare')
          break
        default:
          await playSound('itemCommon')
      }
    }
  }
  
  // 检查浏览器音频支持
  const checkAudioSupport = (): boolean => {
    try {
      const audio = new Audio()
      return !!(audio.canPlayType && audio.canPlayType('audio/mpeg'))
    } catch {
      return false
    }
  }
  
  // 获取音效状态
  const getAudioState = () => ({
    isMuted: isMuted.value,
    volume: volume.value,
    isLoading: isLoading.value,
    isSupported: checkAudioSupport(),
    loadedSounds: Array.from(audioInstances.keys())
  })
  
  // 清理资源
  const cleanup = (): void => {
    stopAllSounds()
    audioInstances.clear()
  }
  
  // 组件卸载时清理
  onUnmounted(() => {
    cleanup()
  })
  
  return {
    // 状态
    isMuted,
    volume,
    isLoading,
    
    // 基础方法
    initAudio,
    playSound,
    stopSound,
    stopAllSounds,
    setVolume,
    toggleMute,
    
    // 音乐方法
    playBackgroundMusic,
    stopBackgroundMusic,
    
    // 对战音效序列
    playBattleSequence,
    
    // 工具方法
    getAudioState,
    checkAudioSupport,
    cleanup
  }
}

// 全局音效管理（可选）
export const useBattleAudioGlobal = () => {
  // 使用单例模式确保全局只有一个音频管理器
  if (typeof window === 'undefined') {
    return useBattleAudio()
  }
  
  if (!(window as any).__battleAudio) {
    (window as any).__battleAudio = useBattleAudio()
  }
  
  return (window as any).__battleAudio
}
