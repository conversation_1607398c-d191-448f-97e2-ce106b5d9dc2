#!/usr/bin/env python3
"""
Redis连接修复验证脚本
"""

import redis
import sys
import time

def test_redis_connection():
    """测试Redis连接"""
    print("🔧 Redis连接修复验证")
    print("=" * 50)
    
    # 测试配置
    configs = [
        {"name": "Docker网络 - redis:6379", "host": "redis", "port": 6379},
        {"name": "本地网络 - localhost:6379", "host": "localhost", "port": 6379},
        {"name": "本地网络 - 127.0.0.1:6379", "host": "127.0.0.1", "port": 6379},
    ]
    
    results = []
    
    for config in configs:
        print(f"\n📡 测试: {config['name']}")
        try:
            # 创建Redis连接
            r = redis.Redis(
                host=config['host'], 
                port=config['port'], 
                db=0,
                socket_timeout=5,
                socket_connect_timeout=5
            )
            
            # 测试连接
            result = r.ping()
            print(f"   ✅ 连接成功: {result}")
            
            # 测试读写
            test_key = f"test_{int(time.time())}"
            r.set(test_key, "test_value")
            value = r.get(test_key)
            r.delete(test_key)
            print(f"   ✅ 读写测试: {value.decode() if value else 'None'}")
            
            results.append({"config": config['name'], "status": "✅ 成功", "error": None})
            
        except Exception as e:
            print(f"   ❌ 连接失败: {str(e)}")
            results.append({"config": config['name'], "status": "❌ 失败", "error": str(e)})
    
    # 输出总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print("=" * 50)
    
    success_count = 0
    for result in results:
        print(f"{result['status']} {result['config']}")
        if "成功" in result['status']:
            success_count += 1
    
    print(f"\n🎯 成功率: {success_count}/{len(results)} ({success_count/len(results)*100:.1f}%)")
    
    if success_count > 0:
        print("\n🎉 Redis连接修复成功！")
        if "redis:6379" in [r['config'] for r in results if "成功" in r['status']]:
            print("✅ Docker网络配置正确，容器间可以正常通信")
        return True
    else:
        print("\n❌ Redis连接仍有问题，需要进一步检查")
        return False

def test_django_cache():
    """测试Django缓存配置"""
    print("\n🔧 Django缓存配置测试")
    print("=" * 30)
    
    try:
        from django.core.cache import cache
        from django.conf import settings
        
        # 测试缓存
        test_key = f"django_test_{int(time.time())}"
        cache.set(test_key, "django_test_value", 60)
        value = cache.get(test_key)
        cache.delete(test_key)
        
        print(f"✅ Django缓存测试成功: {value}")
        print(f"✅ 缓存配置: {settings.CACHES['default']['LOCATION']}")
        return True
        
    except Exception as e:
        print(f"❌ Django缓存测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 开始Redis连接修复验证...")
    
    # 基础Redis连接测试
    redis_ok = test_redis_connection()
    
    # Django缓存测试（如果在Django环境中）
    django_ok = True
    try:
        import django
        django.setup()
        django_ok = test_django_cache()
    except:
        print("\n⚠️  非Django环境，跳过Django缓存测试")
    
    # 最终结果
    print("\n" + "🎯" * 20)
    if redis_ok:
        print("🎉 Redis连接修复验证通过！")
        print("✅ 您的Docker容器现在可以正常连接Redis了")
        print("✅ 建议重启所有服务以应用新配置")
        sys.exit(0)
    else:
        print("❌ Redis连接仍有问题")
        print("💡 建议检查:")
        print("   1. Redis容器是否正常运行")
        print("   2. Docker网络配置是否正确")
        print("   3. 防火墙设置")
        sys.exit(1)
