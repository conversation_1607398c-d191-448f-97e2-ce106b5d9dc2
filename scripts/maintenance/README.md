# Maintenance Scripts 维护脚本

本目录包含用于系统维护和问题修复的脚本。

## 脚本列表

### fix_stuck_battle_rooms.py
- **功能**：修复卡住的对战房间
- **问题背景**：
  - 对战房间因为`time.sleep()`阻塞导致处理卡住
  - 某些箱子没有掉落物品导致"无效开箱掉落"异常
  - 房间状态无法正确更新，导致用户无法正常游戏

- **修复内容**：
  - 检测超过10分钟未更新的运行中房间
  - 对于没有掉落物品的箱子，直接取消房间
  - 强制执行房间逻辑，推进卡住的开箱过程
  - 异常处理优化，确保房间状态正确更新

- **使用方法**：
  ```bash
  # 预览模式 - 查看将要修复的房间
  python scripts/maintenance/fix_stuck_battle_rooms.py --dry-run
  
  # 执行修复
  python scripts/maintenance/fix_stuck_battle_rooms.py
  
  # 详细输出
  python scripts/maintenance/fix_stuck_battle_rooms.py --verbose
  ```

- **修复记录**：
  - 2025-07-01 16:10: 修复了22个卡住的房间
    - 1个房间因箱子无掉落物品被取消
    - 21个房间成功推进了开箱流程
    - 移除了`open_room_case`函数中的阻塞性`time.sleep()`
    - 增强了异常处理机制

## 注意事项

1. **备份数据**：在执行任何修复脚本前，确保有数据库备份
2. **测试环境**：建议先在测试环境中验证脚本效果
3. **监控影响**：执行后监控系统性能和用户反馈
4. **日志记录**：所有修复操作都会记录到系统日志中

## 开发规范

1. **脚本命名**：使用描述性名称，如`fix_[问题描述].py`
2. **参数支持**：
   - `--dry-run`：预览模式，不实际修改数据
   - `--verbose`：详细输出
   - `--help`：帮助信息

3. **错误处理**：
   - 完善的异常捕获和处理
   - 出错时的回滚机制
   - 清晰的错误提示

4. **文档更新**：每次使用脚本后更新此README文档

## 联系方式

如有问题或需要新的维护脚本，请联系开发团队。

最后更新：2025-07-01 