#!/usr/bin/env python
"""
对战API国际化字段修复验证脚本
验证所有关键API接口是否正确返回国际化字段
"""

import os
import sys
import django

# 设置Django环境
sys.path.append('/www/wwwroot/csgoskins.com.cn/server')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')
django.setup()

from box.business import get_battle_case_list
from box.business_room import get_room_detail, get_battle_list
from box.serializers import CaseRoomItemSerializer
from box.models import CaseRoom, CaseRoomItem
from steambase.enums import RespCode

def test_battle_case_list():
    """测试对战箱子列表API"""
    print("=== 测试对战箱子列表API ===")
    fields = ('case_key', 'name', 'name_en', 'name_zh_hans', 'price', 'open_count', 'discount', 'cover')
    code, resp = get_battle_case_list(fields)
    
    if code == RespCode.Succeed.value and resp:
        first_case = resp[0]
        print("✅ 对战箱子列表API - 国际化字段检查:")
        print(f"   name: {first_case.get('name')}")
        print(f"   name_en: {first_case.get('name_en')}")
        print(f"   name_zh_hans: {first_case.get('name_zh_hans')}")
        
        # 验证必要字段存在
        required_fields = ['name', 'name_en', 'name_zh_hans']
        missing_fields = [field for field in required_fields if not first_case.get(field)]
        if missing_fields:
            print(f"❌ 缺少字段: {missing_fields}")
            return False
        else:
            print("✅ 所有国际化字段都存在")
            return True
    else:
        print(f"❌ API调用失败，code: {code}")
        return False

def test_room_detail():
    """测试房间详情API"""
    print("\n=== 测试房间详情API ===")
    
    # 查找一个有rounds的房间
    room = CaseRoom.objects.filter(rounds__isnull=False).first()
    if not room:
        print("❌ 没有找到有rounds的房间")
        return False
        
    fields = (
        'uid', 'short_id', 'create_time', 'update_time', 'max_joiner', 'price', 'state', 'type', 
        'joiner_count', 'private', 'round_count', 'round_count_current', 'rounds', 'bets', 'user'
    )
    
    code, resp = get_room_detail(room.uid, 'test_sid', fields)
    
    if code == RespCode.Succeed.value and resp:
        rounds = resp.get('rounds', [])
        if rounds:
            first_round = rounds[0]
            case = first_round.get('case', {})
            print("✅ 房间详情API - rounds中箱子名称国际化字段:")
            print(f"   case_name: {case.get('name')}")
            print(f"   case_name_en: {case.get('name_en')}")  
            print(f"   case_name_zh_hans: {case.get('name_zh_hans')}")
            
            # 验证国际化字段
            required_fields = ['name', 'name_en', 'name_zh_hans']
            missing_fields = [field for field in required_fields if not case.get(field)]
            if missing_fields:
                print(f"❌ rounds中箱子缺少字段: {missing_fields}")
                return False
            else:
                print("✅ rounds中箱子的所有国际化字段都存在")
                return True
        else:
            print("❌ 房间没有rounds数据")
            return False
    else:
        print(f"❌ 房间详情API调用失败，code: {code}")
        return False

def test_room_list():
    """测试房间列表API"""
    print("\n=== 测试房间列表API ===")
    
    from authentication.models import AuthUser
    user = AuthUser.objects.first()
    if not user:
        print("❌ 没有找到用户")
        return False
        
    state_list = ['waiting', 'full', 'running', 'finished']
    fields = (
        'uid', 'short_id', 'user', 'max_joiner', 'price', 'state', 'type', 'rounds', 'bets', 
        'joiner_count', 'create_time'
    )
    
    code, resp = get_battle_list(user, state_list, fields, 1, 10, None)
    
    if code == RespCode.Succeed.value and resp:
        rooms = resp.get('rooms', [])
        if rooms:
            # 找一个有rounds的房间
            room_with_rounds = None
            for room in rooms:
                if room.get('rounds'):
                    room_with_rounds = room
                    break
                    
            if room_with_rounds:
                rounds = room_with_rounds.get('rounds', [])
                first_round = rounds[0]
                case = first_round.get('case', {})
                print("✅ 房间列表API - rounds中箱子名称国际化字段:")
                print(f"   case_name: {case.get('name')}")
                print(f"   case_name_en: {case.get('name_en')}")
                print(f"   case_name_zh_hans: {case.get('name_zh_hans')}")
                
                # 验证国际化字段
                required_fields = ['name', 'name_en', 'name_zh_hans']
                missing_fields = [field for field in required_fields if not case.get(field)]
                if missing_fields:
                    print(f"❌ 房间列表rounds中箱子缺少字段: {missing_fields}")
                    return False
                else:
                    print("✅ 房间列表rounds中箱子的所有国际化字段都存在")
                    return True
            else:
                print("✅ 房间列表API正常，但当前没有包含rounds的房间")
                return True
        else:
            print("✅ 房间列表API正常，但当前没有房间数据")
            return True
    else:
        print(f"❌ 房间列表API调用失败，code: {code}")
        return False

def test_room_item():
    """测试房间饰品国际化"""
    print("\n=== 测试房间饰品国际化 ===")
    
    room_item = CaseRoomItem.objects.select_related('item_info').first()
    if not room_item or not room_item.item_info:
        print("❌ 没有找到房间饰品数据")
        return False
        
    serializer = CaseRoomItemSerializer(room_item, fields=('name', 'name_en', 'name_zh_hans'))
    data = serializer.data
    
    print("✅ 房间饰品序列化器 - 国际化字段:")
    print(f"   item_name: {data.get('name')}")
    print(f"   item_name_en: {data.get('name_en')}")
    print(f"   item_name_zh_hans: {data.get('name_zh_hans')}")
    
    # 验证国际化字段
    required_fields = ['name', 'name_en', 'name_zh_hans']
    missing_fields = [field for field in required_fields if not data.get(field)]
    if missing_fields:
        print(f"❌ 饰品缺少字段: {missing_fields}")
        return False
    else:
        print("✅ 饰品的所有国际化字段都存在")
        return True

def main():
    """运行所有测试"""
    print("对战API国际化字段修复验证")
    print("=" * 50)
    
    tests = [
        test_battle_case_list,
        test_room_detail, 
        test_room_list,
        test_room_item
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ 测试 {test.__name__} 出现异常: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("测试结果总结:")
    
    passed = sum(results)
    total = len(results)
    
    for i, (test, result) in enumerate(zip(tests, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{i+1}. {test.__name__}: {status}")
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！国际化字段修复成功！")
        return True
    else:
        print("⚠️  部分测试失败，需要进一步检查")
        return False

if __name__ == '__main__':
    main()
