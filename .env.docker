# Docker环境配置文件
# 复制到 .env 文件中使用

# Django配置
DEBUG=False
SECRET_KEY=your-secret-key-here
ALLOWED_HOSTS=localhost,127.0.0.1,csgoskins.com.cn,*.csgoskins.com.cn

# 数据库配置
DATABASE_URL=mysql://root:password@localhost:3306/csgogo

# Redis配置
REDIS_URL=redis://localhost:6379/0

# Celery配置
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# 静态文件和媒体文件
STATIC_URL=/static/
MEDIA_URL=/media/
STATIC_ROOT=/app/static
MEDIA_ROOT=/app/media

# 日志配置
LOG_LEVEL=INFO
LOG_DIR=/app/logs

# WebSocket配置
WEBSOCKET_PORT=4000
WEBSOCKET_HOST=localhost

# 任务调度配置
CELERY_TIMEZONE=Asia/Shanghai
CELERY_BEAT_SCHEDULER=django_celery_beat.schedulers:DatabaseScheduler

# 监控配置
HEALTH_CHECK_ENABLED=True
METRICS_ENABLED=True

# 安全配置
SECURE_SSL_REDIRECT=False
SECURE_HSTS_SECONDS=0
SECURE_CONTENT_TYPE_NOSNIFF=True
SECURE_BROWSER_XSS_FILTER=True
