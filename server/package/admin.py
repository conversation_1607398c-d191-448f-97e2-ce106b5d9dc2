import json
import logging
import os

from datetime import timedelta

from django.contrib import admin, messages
from django.contrib.admin import TabularInline, StackedInline
from django.contrib.admin.views.main import ChangeList
from django.db.models import Sum
from django.utils.translation import gettext_lazy as _

from jet.filters import Date<PERSON>ange<PERSON><PERSON>er

from modeltranslation.admin import TranslationAdmin


from steambase.admin import ReadOnlyAdmin, ChangeOnlyAdmin
from steambase.enums import PackageState, RespCode
from steambase.utils import ParamException
from libs.steamapi import SteamApi
from authentication.models import AuthUser
from package.models import TradeBot, TradeBotInventory, PackageItem, TradeRecord, TradeItem, ItemInfo, ItemPrice
from package.models import PackageStatisticsDay, PackageStatisticsMonth, ShopBot, BlackList, UnlockTimeConfig
from package.models import LockItemsStatistics, PackageItemPriceRate, ExchangeRecord, ItemWhitelist
from package.models import ItemCategory, ItemQuality, ItemRarity, ItemExterior
from package.service.admin_actions import sync_trade_bot_inventory, startup_trade_bot, review_trade, invalidate_package, \
    exchanged_package, packageitem_export_to_excel
from package.service.admin_actions import available_package, import_to_luckybox, import_to_custombox, import_to_market
from package.service.trade import request_deposit
from package.business import cancel_send


_logger = logging.getLogger(__name__)




class ItemPriceInline(StackedInline):
    model = ItemPrice
    can_delete = False
    fields = ['price', 'update_time' ]
    readonly_fields = ['price', 'update_time']


@admin.register(ItemInfo)
class ItemInfoAdmin(TranslationAdmin):
    fields = ('name', 'name_color', 'appid', 'type', 
              'icon_url', 'icon_url_large', 'rarity', 'rarity_color', 'custom_rarity', 'quality', 'quality_color',
              'exterior', 'description', 'content')
    readonly_fields = ('contextid', 'classid', 'item_set', 'slot', 'hero')
    list_display = ('id', 'name', 'name_en') #, 'classid'
    search_fields = ('name', 'name_en') #, 'classid'
    list_filter = ('weapon', 'type')
    list_per_page = 200
    inlines = [ItemPriceInline]
    actions = [import_to_custombox, import_to_market, import_to_luckybox]

    

    # def save_model(self, request, obj, form, change):
    #     obj.save()
    #     if not obj.item_price:
    #         update_item_price(obj.market_hash_name)
    #     obj.item_price.set_price()


@admin.register(PackageItem)
class PackageItemAdmin(admin.ModelAdmin):
    list_display = ('uid', 'user', 'item_info', 'price', 'state')  # 在列表视图中显示'uid'字段
    list_filter = ('state', 'bot_steamid')
    search_fields = ('assetid', 'user__username', 'item_info__market_name_cn')
    ordering = ('-create_time',)
    raw_id_fields = ('item_info', 'user')
    list_per_page = 200
    actions = [invalidate_package, exchanged_package, available_package, packageitem_export_to_excel]

    def price(self, obj):
        return obj.item_info.item_price.price

    price.short_description = _('price')


# @admin.register(PackageItemPriceRate)
# class PackageItemPriceRateAdmin(admin.ModelAdmin):
#     fields = ('level', 'price_rate', 'min_price', 'max_price')
#     list_display = ('level', 'price_rate', 'min_price', 'max_price')
#     list_per_page = 50


class TradeItemInline(TabularInline):
    model = TradeItem
    can_delete = False
    fields = ['item_info', 'price', 'assetid']
    readonly_fields = ['item_info', 'price', 'assetid']

    def has_add_permission(self, request):
        return False


def cancel_send_record(modeladmin, request, queryset):
    try:
        records = queryset.all()
        for record in records:
            cancel_send(record)
        messages.success(request, _('cancel_send'))
    except Exception as e:
        _logger.exception(e)

cancel_send_record.short_description = _('cancel_send')


# @admin.register(TradeRecord)
# class TradeRecordAdmin(ChangeOnlyAdmin):
#     fields = ('uid', 'user', 'tradeurl', 'trade_type', 'amount', 'state', 'trade_no', 'security_code',
#               'bot_steamid', 'bot_msg', 'create_time', 'trade_time', 'review')
#     readonly_fields = ('uid', 'user', 'tradeurl', 'trade_type', 'amount', 'state', 'trade_no', 'security_code',
#                        'bot_steamid', 'bot_msg', 'create_time', 'trade_time')
#     list_display = ('uid', 'user', 'trade_type', 'amount', 'state', 'create_time', 'trade_time', 'review')
#     ordering = ('-create_time',)
#     list_filter = ('trade_type', 'state', ('create_time', DateRangeFilter), ('trade_time', DateRangeFilter))
#     search_fields = ('uid', 'user__username', 'trade_no', 'security_code')
#     list_per_page = 50
#     inlines = [TradeItemInline]
#     actions = [cancel_send_record]
# 
#     def save_model(self, request, obj, form, change):
#         origin = TradeRecord.objects.filter(uid=obj.uid).first()
#         if not origin.review and obj.review:
#             request_deposit(obj)
#         obj.save()
# 
#     def get_changelist(self, request, **kwargs):
#         return TradeRecordAddTotalChangeList


class TradeRecordAddTotalChangeList(ChangeList):

    def get_results(self, request):
        super().get_results(request)
        total = TradeRecord()
        setattr(total, 'uid', '-')
        setattr(total, 'user', AuthUser(username='总和'))
        setattr(total, 'state', '-')
        setattr(total, 'trade_time', '-')
        setattr(total, 'review', None)
        fields_to_total = ['amount']
        for field in fields_to_total:
            setattr(total, field, round(list(self.queryset.aggregate(Sum(field)).items())[0][1] or 0, 2))
        len(self.result_list)
        self.result_list._result_cache.insert(0, total)


# @admin.register(TradeBot)
# class TradeBotAdmin(admin.ModelAdmin):
#     list_display = ('remark', 'steamid', 'enable', 'enable_deposit', 'enable_withdraw')
#     list_editable = ('enable', 'enable_deposit', 'enable_withdraw')
#     list_per_page = 50
#     actions = [startup_trade_bot, sync_trade_bot_inventory]
# 
#     def save_model(self, request, obj, form, change):
#         if not change and TradeBot.objects.count() >= 5:
#             messages.set_level(request, messages.ERROR)
#             messages.error(request, '交易机器人最多只能设置 5 个')
#             return
#         obj.save()
# 
# 
# @admin.register(TradeBotInventory)
# class TradeBotInventoryAdmin(admin.ModelAdmin):
#     fields = ('uid', 'item_info', 'assetid', 'instanceid', 'bot_steamid', 'matching', 'enable')
#     readonly_fields = ('uid', 'item_info', 'assetid', 'instanceid', 'bot_steamid', 'matching')
#     list_display = ('assetid', 'item_info', 'bot_steamid', 'matching', 'enable')
#     list_editable = ('enable',)
#     list_filter = ('bot_steamid', 'matching', 'enable')
#     search_fields = ('item_info__market_name', 'item_info__market_hash_name', 'bot_steamid', 'assetid')
#     list_per_page = 50
# 
#     def has_add_permission(self, request):
#         return False


@admin.register(ExchangeRecord)
class ExchangeRecordAdmin(ReadOnlyAdmin):
    fields = ('uid', 'user', 'item_info', 'price')
    list_display = ('create_time', 'user', 'item_info', 'price')
    ordering = ('-create_time',)
    search_fields = ('uid', 'user__username', 'item_info__market_hash_name', 'item_info__market_name_cn')
    list_per_page = 50

    def get_queryset(self, request):
        return self.model.objects.filter(package__assetid='0')


# @admin.register(BlackList)
# class BlackListAdmin(admin.ModelAdmin):
#     fields = ('user', 'enable', 'ban_deposit', 'ban_withdraw')
#     list_display = ('user', 'enable', 'ban_deposit', 'ban_withdraw')
#     list_editable = ('enable', 'ban_deposit', 'ban_withdraw')
#     list_per_page = 50
# 
# 
# @admin.register(PackageStatisticsDay)
# class PackageStatisticsDayAdmin(ReadOnlyAdmin):
#     fields = ('date', 'amount', 'trade_type')
#     list_display = ('date', 'amount', 'trade_type')
#     list_filter = (('date', DateRangeFilter), 'trade_type')
#     list_per_page = 50
# 
# 
# @admin.register(PackageStatisticsMonth)
# class PackageStatisticsMonthAdmin(ReadOnlyAdmin):
#     fields = ('month', 'amount', 'trade_type')
#     extra_readonly_fields = ('month',)
#     list_display = ('month', 'amount', 'trade_type')
#     list_filter = (('date', DateRangeFilter), 'trade_type')
#     list_per_page = 50


# @admin.register(UnlockTimeConfig)
# class UnlockTimeConfigAdmin(admin.ModelAdmin):
#     fields = ('last_unlock_time', 'interval', 'next_unlock_time')
#     list_display = ('last_unlock_time', 'interval', 'next_unlock_time')
#     readonly_fields = ('next_unlock_time',)
#     list_editable = ('interval',)
#     list_per_page = 50
# 
#     def next_unlock_time(self, obj):
#         if obj.last_unlock_time:
#             return obj.last_unlock_time + timedelta(minutes=obj.interval)
#         return '-'
# 
#     def save_model(self, request, obj, form, change):
#         if UnlockTimeConfig.objects.count() > 0:
#             messages.set_level(request, messages.ERROR)
#             messages.error(request, '物品解锁时间规则最多只能设置 1 个')
#             return
#         obj.save()
# 
# 
# @admin.register(LockItemsStatistics)
# class LockItemsStatisticsAdmin(ReadOnlyAdmin):
#     actions = []
#     extra_readonly_fields = ('market_hash_name',)
#     fields = ('item_info', 'market_hash_name', 'count')
#     list_display = ('item_info', 'market_hash_name', 'count')
#     search_fields = ('item_info__market_name_cn', 'item_info__market_hash_name')
#     list_per_page = 50
# 
#     def has_delete_permission(self, request, obj=None):
#         # Nobody is allowed to delete
#         return True
# 
#     def has_change_permission(self, request, obj=None):
#         return super(ReadOnlyAdmin, self).has_change_permission(request, obj)
# 
#     def market_hash_name(self, obj):
#         return obj.item_info.market_hash_name
# 
# 
# @admin.register(ItemWhitelist)
# class ItemWhitelistAdmin(admin.ModelAdmin):
#     list_display = ('item_info', 'market_hash_name', 'enable')
#     list_editable = ('enable',)
#     raw_id_fields = ['item_info']
#     search_fields = ('item_info__market_name_cn', 'item_info__market_hash_name')
#     list_per_page = 50
# 
#     def market_hash_name(self, obj):
#         if obj.item_info:
#             return obj.item_info.market_hash_name
#         else:
#             return '-'


@admin.register(ItemCategory)
class ItemCategoryAdmin(admin.ModelAdmin):
    list_display = ('cate_id', 'cate_name', 'get_parent_name', 'is_show', 'sort')
    list_per_page = 50
    list_filter = ('is_show',)
    raw_id_fields = ['parent']  # 如果数据量大的话建议使用
    
    def get_parent_name(self, obj):
        if obj.parent:
            return obj.parent.cate_name
        return '-'
    
    get_parent_name.short_description = '父分类'  # 设置列名称



@admin.register(ItemQuality)
class ItemQualityAdmin(admin.ModelAdmin):
    list_display = ('quality_id', 'quality_name', 'is_show', 'sort')
    list_per_page = 50

@admin.register(ItemRarity)
class ItemRarityAdmin(admin.ModelAdmin):
    list_display = ('rarity_id', 'rarity_name', 'is_show', 'sort')
    list_per_page = 50


@admin.register(ItemExterior)
class ItemExteriorAdmin(admin.ModelAdmin):
    list_display = ('exterior_id', 'exterior_name', 'is_show', 'sort')
    list_per_page = 50