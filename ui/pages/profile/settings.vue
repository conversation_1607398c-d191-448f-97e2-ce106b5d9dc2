<template>
  <div class="min-h-screen relative">
    <!-- 背景装饰 -->
    <div class="fixed inset-0 -z-10">
      <div class="absolute top-0 -left-32 w-64 h-64 rounded-full blur-3xl bg-primary/10 animate-pulse-slow"></div>
      <div class="absolute bottom-0 -right-32 w-64 h-64 rounded-full blur-3xl bg-secondary/10 animate-pulse-slow"></div>
      <div class="absolute inset-0 bg-grid-pattern opacity-20"></div>
      <div class="absolute inset-0 bg-noise opacity-40 mix-blend-soft-light"></div>
    </div>

    <!-- PC端内容 -->
    <div class="container mx-auto px-4 py-8 relative z-10" v-if="!isMobile">
      <!-- 页面头部 -->
      <div class="page-header text-center mb-12">
        <h1 class="text-4xl font-bold mb-4">
          <span class="text-gradient">{{ $t('settings.title') }}</span>
        </h1>
        <p class="text-gray-300 max-w-2xl mx-auto">{{ $t('settings.description') }}</p>
      </div>

      <!-- 设置内容 -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- 左侧导航 -->
        <div class="lg:col-span-1">
          <div class="settings-nav backdrop-card">
            <div class="space-y-2">
              <button
                v-for="section in settingsSections"
                :key="section.id"
                @click="currentSection = section.id"
                class="nav-item w-full text-left"
                :class="{ 'active': currentSection === section.id }"
              >
                <i :class="section.icon + ' mr-3'"></i>
                {{ $t(section.label) }}
              </button>
            </div>
          </div>
        </div>

        <!-- 右侧内容 -->
        <div class="lg:col-span-2">
          <!-- 账户设置 -->
          <div v-if="currentSection === 'account'" class="settings-content">
            <div class="backdrop-card mb-6">
              <h2 class="section-title">{{ $t('settings.account.title') }}</h2>
              <div class="space-y-6">
                <!-- 基本信息 -->
                <div class="form-group">
                  <label class="form-label">{{ $t('settings.account.username') }}</label>
                  <input v-model="accountSettings.username" type="text" class="form-input" />
                </div>
                <div class="form-group">
                  <label class="form-label">{{ $t('settings.account.email') }}</label>
                  <input v-model="accountSettings.email" type="email" class="form-input" />
                </div>
                <div class="form-group">
                  <label class="form-label">{{ $t('settings.account.avatar') }}</label>
                  <div class="avatar-upload">
                    <img :src="accountSettings.avatar" alt="头像" class="avatar-preview" />
                    <button class="upload-btn">{{ $t('settings.account.change_avatar') }}</button>
                  </div>
                </div>
                <button class="btn-primary">{{ $t('settings.account.save_changes') }}</button>
              </div>
            </div>

            <!-- 密码设置 -->
            <div class="backdrop-card">
              <h2 class="section-title">{{ $t('settings.account.password') }}</h2>
              <div class="space-y-6">
                <div class="form-group">
                  <label class="form-label">{{ $t('settings.account.current_password') }}</label>
                  <input v-model="passwordSettings.currentPassword" type="password" class="form-input" />
                </div>
                <div class="form-group">
                  <label class="form-label">{{ $t('settings.account.new_password') }}</label>
                  <input v-model="passwordSettings.newPassword" type="password" class="form-input" />
                </div>
                <div class="form-group">
                  <label class="form-label">{{ $t('settings.account.confirm_password') }}</label>
                  <input v-model="passwordSettings.confirmPassword" type="password" class="form-input" />
                </div>
                <button class="btn-primary">{{ $t('settings.account.change_password') }}</button>
              </div>
            </div>
          </div>

          <!-- 隐私设置 -->
          <div v-if="currentSection === 'privacy'" class="settings-content">
            <div class="backdrop-card">
              <h2 class="section-title">{{ $t('settings.privacy.title') }}</h2>
              <div class="space-y-6">
                <div class="privacy-item">
                  <div class="privacy-info">
                    <h3 class="privacy-title">{{ $t('settings.privacy.profile_visibility') }}</h3>
                    <p class="privacy-description">{{ $t('settings.privacy.profile_visibility_desc') }}</p>
                  </div>
                  <div class="privacy-control">
                    <select v-model="privacySettings.profileVisibility" class="form-select">
                      <option value="public">{{ $t('settings.privacy.public') }}</option>
                      <option value="friends">{{ $t('settings.privacy.friends') }}</option>
                      <option value="private">{{ $t('settings.privacy.private') }}</option>
                    </select>
                  </div>
                </div>
                <div class="privacy-item">
                  <div class="privacy-info">
                    <h3 class="privacy-title">{{ $t('settings.privacy.opening_history') }}</h3>
                    <p class="privacy-description">{{ $t('settings.privacy.opening_history_desc') }}</p>
                  </div>
                  <div class="privacy-control">
                    <label class="toggle-switch">
                      <input v-model="privacySettings.showOpeningHistory" type="checkbox" />
                      <span class="toggle-slider"></span>
                    </label>
                  </div>
                </div>
                <div class="privacy-item">
                  <div class="privacy-info">
                    <h3 class="privacy-title">{{ $t('settings.privacy.online_status') }}</h3>
                    <p class="privacy-description">{{ $t('settings.privacy.online_status_desc') }}</p>
                  </div>
                  <div class="privacy-control">
                    <label class="toggle-switch">
                      <input v-model="privacySettings.showOnlineStatus" type="checkbox" />
                      <span class="toggle-slider"></span>
                    </label>
                  </div>
                </div>
                <button class="btn-primary">{{ $t('settings.privacy.save_settings') }}</button>
              </div>
            </div>
          </div>

          <!-- 通知设置 -->
          <div v-if="currentSection === 'notifications'" class="settings-content">
            <div class="backdrop-card">
              <h2 class="section-title">{{ $t('settings.notifications.title') }}</h2>
              <div class="space-y-6">
                <div class="notification-item">
                  <div class="notification-info">
                    <h3 class="notification-title">{{ $t('settings.notifications.email_notifications') }}</h3>
                    <p class="notification-description">{{ $t('settings.notifications.email_notifications_desc') }}</p>
                  </div>
                  <div class="notification-control">
                    <label class="toggle-switch">
                      <input v-model="notificationSettings.emailNotifications" type="checkbox" />
                      <span class="toggle-slider"></span>
                    </label>
                  </div>
                </div>
                <div class="notification-item">
                  <div class="notification-info">
                    <h3 class="notification-title">{{ $t('settings.notifications.push_notifications') }}</h3>
                    <p class="notification-description">{{ $t('settings.notifications.push_notifications_desc') }}</p>
                  </div>
                  <div class="notification-control">
                    <label class="toggle-switch">
                      <input v-model="notificationSettings.pushNotifications" type="checkbox" />
                      <span class="toggle-slider"></span>
                    </label>
                  </div>
                </div>
                <div class="notification-item">
                  <div class="notification-info">
                    <h3 class="notification-title">{{ $t('settings.notifications.activity_updates') }}</h3>
                    <p class="notification-description">{{ $t('settings.notifications.activity_updates_desc') }}</p>
                  </div>
                  <div class="notification-control">
                    <label class="toggle-switch">
                      <input v-model="notificationSettings.activityUpdates" type="checkbox" />
                      <span class="toggle-slider"></span>
                    </label>
                  </div>
                </div>
                <button class="btn-primary">{{ $t('settings.notifications.save_settings') }}</button>
              </div>
            </div>
          </div>

          <!-- 安全设置 -->
          <div v-if="currentSection === 'security'" class="settings-content">
            <div class="backdrop-card mb-6">
              <h2 class="section-title">{{ $t('settings.security.title') }}</h2>
              <div class="space-y-6">
                <div class="security-item">
                  <div class="security-info">
                    <h3 class="security-title">{{ $t('settings.security.two_factor_auth') }}</h3>
                    <p class="security-description">{{ $t('settings.security.two_factor_auth_desc') }}</p>
                  </div>
                  <div class="security-control">
                    <button class="btn-secondary">{{ $t('settings.security.enable_2fa') }}</button>
                  </div>
                </div>
                <div class="security-item">
                  <div class="security-info">
                    <h3 class="security-title">{{ $t('settings.security.login_history') }}</h3>
                    <p class="security-description">{{ $t('settings.security.login_history_desc') }}</p>
                  </div>
                  <div class="security-control">
                    <button class="btn-secondary">{{ $t('settings.security.view_history') }}</button>
                  </div>
                </div>
              </div>
            </div>

            <!-- 设备管理 -->
            <div class="backdrop-card">
              <h2 class="section-title">{{ $t('settings.security.device_management') }}</h2>
              <div class="space-y-4">
                <div
                  v-for="device in securitySettings.devices"
                  :key="device.id"
                  class="device-item"
                >
                  <div class="device-info">
                    <div class="device-name">{{ device.name }}</div>
                    <div class="device-details">{{ device.location }} • {{ device.lastLogin }}</div>
                  </div>
                  <div class="device-controls">
                    <button class="btn-danger">{{ $t('settings.security.remove_device') }}</button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 偏好设置 -->
          <div v-if="currentSection === 'preferences'" class="settings-content">
            <div class="backdrop-card">
              <h2 class="section-title">{{ $t('settings.preferences.title') }}</h2>
              <div class="space-y-6">
                <div class="preference-item">
                  <div class="preference-info">
                    <h3 class="preference-title">{{ $t('settings.preferences.language') }}</h3>
                    <p class="preference-description">{{ $t('settings.preferences.language_desc') }}</p>
                  </div>
                  <div class="preference-control">
                    <select v-model="preferenceSettings.language" class="form-select">
                      <option value="zh-hans">{{ $t('settings.preferences.chinese') }}</option>
                      <option value="en">{{ $t('settings.preferences.english') }}</option>
                    </select>
                  </div>
                </div>
                <div class="preference-item">
                  <div class="preference-info">
                    <h3 class="preference-title">{{ $t('settings.preferences.theme') }}</h3>
                    <p class="preference-description">{{ $t('settings.preferences.theme_desc') }}</p>
                  </div>
                  <div class="preference-control">
                    <select v-model="preferenceSettings.theme" class="form-select">
                      <option value="dark">{{ $t('settings.preferences.dark_theme') }}</option>
                      <option value="light">{{ $t('settings.preferences.light_theme') }}</option>
                      <option value="auto">{{ $t('settings.preferences.auto_theme') }}</option>
                    </select>
                  </div>
                </div>
                <div class="preference-item">
                  <div class="preference-info">
                    <h3 class="preference-title">{{ $t('settings.preferences.sound_effects') }}</h3>
                    <p class="preference-description">{{ $t('settings.preferences.sound_effects_desc') }}</p>
                  </div>
                  <div class="preference-control">
                    <label class="toggle-switch">
                      <input v-model="preferenceSettings.soundEffects" type="checkbox" />
                      <span class="toggle-slider"></span>
                    </label>
                  </div>
                </div>
                <button class="btn-primary">{{ $t('settings.preferences.save_preferences') }}</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 移动端内容 -->
    <div class="container mx-auto px-4 py-4 relative z-10" v-else>
      <!-- 页面头部 -->
      <div class="page-header mb-6">
        <h1 class="text-2xl font-bold mb-2">{{ $t('settings.title') }}</h1>
        <p class="text-gray-300 text-sm">{{ $t('settings.description') }}</p>
      </div>

      <!-- 设置导航 -->
      <div class="settings-nav-mobile mb-6">
        <div class="flex space-x-2 overflow-x-auto pb-2">
          <button
            v-for="section in settingsSections"
            :key="section.id"
            @click="currentSection = section.id"
            class="nav-item-mobile whitespace-nowrap"
            :class="{ 'active': currentSection === section.id }"
          >
            <i :class="section.icon + ' mr-1'"></i>
            {{ $t(section.label) }}
          </button>
        </div>
      </div>

      <!-- 设置内容 -->
      <div class="settings-content-mobile">
        <!-- 账户设置 -->
        <div v-if="currentSection === 'account'" class="backdrop-card">
          <h2 class="section-title-mobile">{{ $t('settings.account.title') }}</h2>
          <div class="space-y-4">
            <div class="form-group">
              <label class="form-label">{{ $t('settings.account.username') }}</label>
              <input v-model="accountSettings.username" type="text" class="form-input" />
            </div>
            <div class="form-group">
              <label class="form-label">{{ $t('settings.account.email') }}</label>
              <input v-model="accountSettings.email" type="email" class="form-input" />
            </div>
            <button class="btn-primary w-full">{{ $t('settings.account.save_changes') }}</button>
          </div>
        </div>

        <!-- 隐私设置 -->
        <div v-if="currentSection === 'privacy'" class="backdrop-card">
          <h2 class="section-title-mobile">{{ $t('settings.privacy.title') }}</h2>
          <div class="space-y-4">
            <div class="privacy-item-mobile">
              <div class="privacy-info">
                <h3 class="privacy-title">{{ $t('settings.privacy.profile_visibility') }}</h3>
              </div>
              <div class="privacy-control">
                <select v-model="privacySettings.profileVisibility" class="form-select">
                  <option value="public">{{ $t('settings.privacy.public') }}</option>
                  <option value="friends">{{ $t('settings.privacy.friends') }}</option>
                  <option value="private">{{ $t('settings.privacy.private') }}</option>
                </select>
              </div>
            </div>
            <div class="privacy-item-mobile">
              <div class="privacy-info">
                <h3 class="privacy-title">{{ $t('settings.privacy.opening_history') }}</h3>
              </div>
              <div class="privacy-control">
                <label class="toggle-switch">
                  <input v-model="privacySettings.showOpeningHistory" type="checkbox" />
                  <span class="toggle-slider"></span>
                </label>
              </div>
            </div>
            <button class="btn-primary w-full">{{ $t('settings.privacy.save_settings') }}</button>
          </div>
        </div>

        <!-- 通知设置 -->
        <div v-if="currentSection === 'notifications'" class="backdrop-card">
          <h2 class="section-title-mobile">{{ $t('settings.notifications.title') }}</h2>
          <div class="space-y-4">
            <div class="notification-item-mobile">
              <div class="notification-info">
                <h3 class="notification-title">{{ $t('settings.notifications.push_notifications') }}</h3>
              </div>
              <div class="notification-control">
                <label class="toggle-switch">
                  <input v-model="notificationSettings.pushNotifications" type="checkbox" />
                  <span class="toggle-slider"></span>
                </label>
              </div>
            </div>
            <div class="notification-item-mobile">
              <div class="notification-info">
                <h3 class="notification-title">{{ $t('settings.notifications.activity_updates') }}</h3>
              </div>
              <div class="notification-control">
                <label class="toggle-switch">
                  <input v-model="notificationSettings.activityUpdates" type="checkbox" />
                  <span class="toggle-slider"></span>
                </label>
              </div>
            </div>
            <button class="btn-primary w-full">{{ $t('settings.notifications.save_settings') }}</button>
          </div>
        </div>

        <!-- 偏好设置 -->
        <div v-if="currentSection === 'preferences'" class="backdrop-card">
          <h2 class="section-title-mobile">{{ $t('settings.preferences.title') }}</h2>
          <div class="space-y-4">
            <div class="preference-item-mobile">
              <div class="preference-info">
                <h3 class="preference-title">{{ $t('settings.preferences.language') }}</h3>
              </div>
              <div class="preference-control">
                <select v-model="preferenceSettings.language" class="form-select">
                  <option value="zh-hans">{{ $t('settings.preferences.chinese') }}</option>
                  <option value="en">{{ $t('settings.preferences.english') }}</option>
                </select>
              </div>
            </div>
            <div class="preference-item-mobile">
              <div class="preference-info">
                <h3 class="preference-title">{{ $t('settings.preferences.sound_effects') }}</h3>
              </div>
              <div class="preference-control">
                <label class="toggle-switch">
                  <input v-model="preferenceSettings.soundEffects" type="checkbox" />
                  <span class="toggle-slider"></span>
                </label>
              </div>
            </div>
            <button class="btn-primary w-full">{{ $t('settings.preferences.save_preferences') }}</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 国际化
const { t } = useI18n()

// 设备检测 - 使用响应式状态
const isMobile = ref(false)

// 在客户端检测设备类型
onMounted(() => {
  const checkMobile = () => {
    isMobile.value = window.innerWidth < 768
  }
  
  checkMobile()
  window.addEventListener('resize', checkMobile)
  
  onUnmounted(() => {
    window.removeEventListener('resize', checkMobile)
  })
})

// SEO配置
useSeoMeta({
  title: '设置 - CSGO开箱网站',
  description: '管理您的账户设置、隐私偏好、通知和安全选项',
  keywords: '用户设置,账户管理,隐私设置,通知设置',
  ogTitle: '设置 - CSGO开箱网站',
  ogDescription: '管理您的账户设置、隐私偏好、通知和安全选项',
  twitterTitle: '设置 - CSGO开箱网站',
  twitterDescription: '管理您的账户设置、隐私偏好、通知和安全选项'
})

// 设置分类
const settingsSections = [
  { id: 'account', label: 'settings.sections.account', icon: 'i-heroicons-user-circle' },
  { id: 'privacy', label: 'settings.sections.privacy', icon: 'i-heroicons-shield-check' },
  { id: 'notifications', label: 'settings.sections.notifications', icon: 'i-heroicons-bell' },
  { id: 'security', label: 'settings.sections.security', icon: 'i-heroicons-lock-closed' },
  { id: 'preferences', label: 'settings.sections.preferences', icon: 'i-heroicons-cog-6-tooth' }
]

// 当前选中的设置分类
const currentSection = ref('account')

// 账户设置
const accountSettings = ref({
  username: '玩家昵称',
  email: '<EMAIL>',
  avatar: '/demo/avatar1.png'
})

// 密码设置
const passwordSettings = ref({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 隐私设置
const privacySettings = ref({
  profileVisibility: 'public',
  showOpeningHistory: true,
  showOnlineStatus: true
})

// 通知设置
const notificationSettings = ref({
  emailNotifications: true,
  pushNotifications: true,
  activityUpdates: true
})

// 安全设置
const securitySettings = ref({
  devices: [
    {
      id: 1,
      name: 'Chrome on Windows',
      location: '北京, 中国',
      lastLogin: '2024-01-20 15:30'
    },
    {
      id: 2,
      name: 'Safari on iPhone',
      location: '上海, 中国',
      lastLogin: '2024-01-19 10:15'
    }
  ]
})

// 偏好设置
const preferenceSettings = ref({
  language: 'zh-hans',
  theme: 'dark',
  soundEffects: true
})
</script>

<style scoped>
/* 页面头部样式 */
.page-header {
  text-align: center;
  position: relative;
}

.page-header::after {
  content: '';
  position: absolute;
  bottom: -1rem;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
  border-radius: 2px;
}

/* 卡片样式 */
.backdrop-card {
  @apply bg-white/5 backdrop-blur-md border border-white/10 rounded-2xl p-6;
  @apply hover:border-white/20 transition-all duration-300;
}

/* 导航样式 */
.settings-nav {
  @apply bg-white/5 backdrop-blur-md border border-white/10 rounded-2xl p-6;
}

.nav-item {
  @apply flex items-center px-4 py-3 rounded-xl text-sm font-medium transition-all duration-300;
  @apply bg-white/5 border border-white/10 text-white/70;
  @apply hover:bg-white/10 hover:border-white/20 hover:text-white;
}

.nav-item.active {
  @apply bg-primary/20 border-primary text-primary;
  @apply shadow-lg shadow-primary/25;
}

.settings-nav-mobile {
  @apply bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-4;
}

.nav-item-mobile {
  @apply flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300;
  @apply bg-white/5 border border-white/10 text-white/70;
  @apply hover:bg-white/10 hover:border-white/20 hover:text-white;
}

.nav-item-mobile.active {
  @apply bg-primary/20 border-primary text-primary;
  @apply shadow-lg shadow-primary/25;
}

/* 标题样式 */
.section-title {
  @apply text-2xl font-semibold text-white mb-6;
}

.section-title-mobile {
  @apply text-xl font-semibold text-white mb-4;
}

/* 表单样式 */
.form-group {
  @apply space-y-2;
}

.form-label {
  @apply block text-white font-medium text-sm;
}

.form-input, .form-select {
  @apply w-full px-4 py-3 rounded-xl bg-white/5 border border-white/10;
  @apply text-white placeholder-white/50;
  @apply focus:outline-none focus:border-primary focus:bg-white/10;
  @apply transition-all duration-300;
}

/* 头像上传样式 */
.avatar-upload {
  @apply flex items-center space-x-4;
}

.avatar-preview {
  @apply w-16 h-16 rounded-full border-2 border-white/20;
}

.upload-btn {
  @apply px-4 py-2 rounded-xl text-sm font-medium;
  @apply bg-white/5 border border-white/10 text-white/70;
  @apply hover:bg-white/10 hover:border-white/20 hover:text-white;
  @apply transition-all duration-300;
}

/* 开关样式 */
.toggle-switch {
  @apply relative inline-flex items-center h-6 rounded-full w-11;
  @apply bg-white/20 cursor-pointer transition-colors duration-300;
}

.toggle-switch input {
  @apply sr-only;
}

.toggle-switch input:checked + .toggle-slider {
  @apply bg-primary;
}

.toggle-switch input:checked + .toggle-slider:before {
  @apply translate-x-5;
}

.toggle-slider {
  @apply absolute inset-0 rounded-full transition-all duration-300;
}

.toggle-slider:before {
  @apply absolute top-0.5 left-0.5 w-5 h-5 rounded-full;
  @apply bg-white shadow-lg transition-transform duration-300;
  content: '';
}

/* 设置项样式 */
.privacy-item, .notification-item, .security-item, .preference-item {
  @apply flex items-center justify-between p-4 rounded-xl border border-white/10;
  @apply hover:border-white/20 transition-all duration-300;
}

.privacy-item-mobile, .notification-item-mobile, .preference-item-mobile {
  @apply flex items-center justify-between p-3 rounded-lg border border-white/10;
  @apply hover:border-white/20 transition-all duration-300;
}

.privacy-info, .notification-info, .security-info, .preference-info {
  @apply flex-1;
}

.privacy-title, .notification-title, .security-title, .preference-title {
  @apply text-white font-medium mb-1;
}

.privacy-description, .notification-description, .security-description, .preference-description {
  @apply text-white/60 text-sm;
}

.privacy-control, .notification-control, .security-control, .preference-control {
  @apply ml-4;
}

/* 设备管理样式 */
.device-item {
  @apply flex items-center justify-between p-4 rounded-xl border border-white/10;
  @apply hover:border-white/20 transition-all duration-300;
}

.device-info {
  @apply flex-1;
}

.device-name {
  @apply text-white font-medium mb-1;
}

.device-details {
  @apply text-white/60 text-sm;
}

.device-controls {
  @apply ml-4;
}

/* 按钮样式 */
.btn-primary {
  @apply bg-primary hover:bg-primary-dark text-white font-semibold py-3 px-6 rounded-xl;
  @apply transition-all duration-300 shadow-lg shadow-primary/25;
}

.btn-secondary {
  @apply px-4 py-2 rounded-xl text-sm font-medium;
  @apply bg-white/5 border border-white/10 text-white/70;
  @apply hover:bg-white/10 hover:border-white/20 hover:text-white;
  @apply transition-all duration-300;
}

.btn-danger {
  @apply px-4 py-2 rounded-xl text-sm font-medium;
  @apply bg-red-500/20 border border-red-500/30 text-red-400;
  @apply hover:bg-red-500/30 hover:border-red-500/40;
  @apply transition-all duration-300;
}

/* 渐变文本 */
.text-gradient {
  @apply text-transparent bg-clip-text bg-gradient-to-r from-primary to-secondary;
}

/* 网格背景 */
.bg-grid-pattern {
  background-image: linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
}

/* 噪声背景 */
.bg-noise {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
}

/* 动画 */
.animate-pulse-slow {
  animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style> 