#!/usr/bin/env node

/**
 * Redis发布测试脚本
 * 用于测试WebSocket服务器的Redis订阅功能
 */

const redis = require('redis');

// Redis配置
const redisOptions = {
  socket: {
    host: '127.0.0.1',
    port: 6379,
    family: 4
  }
};

async function testRedisPublish() {
  let publisher = null;
  
  try {
    console.log('🔄 连接到Redis...');
    publisher = redis.createClient(redisOptions);
    
    publisher.on('error', (err) => {
      console.error('❌ Redis错误:', err.message);
    });
    
    await publisher.connect();
    console.log('✅ Redis连接成功');
    
    // 测试消息
    const testMessages = [
      {
        type: 'test',
        action: 'redis_test',
        data: {
          message: '这是一条Redis测试消息',
          timestamp: Date.now(),
          from: 'test_script'
        }
      },
      {
        type: 'game_update',
        action: 'battle_update',
        data: {
          battle_id: 123,
          status: 'in_progress',
          players: ['player1', 'player2'],
          timestamp: Date.now()
        }
      },
      {
        type: 'chat',
        action: 'new_message',
        data: {
          user: 'TestUser',
          message: 'Hello from Redis!',
          room: 'general',
          timestamp: Date.now()
        }
      }
    ];
    
    // 发布到不同频道
    const channels = ['ws_channel', 'battle_channel', 'chat_channel'];
    
    for (let i = 0; i < testMessages.length; i++) {
      const channel = channels[i];
      const message = JSON.stringify([
        testMessages[i].type,
        testMessages[i].action,
        testMessages[i].data
      ]);
      
      console.log(`📤 发布消息到频道 ${channel}:`, testMessages[i].data.message || testMessages[i].action);
      await publisher.publish(channel, message);
      
      // 等待1秒
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.log('✅ 所有测试消息已发布');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  } finally {
    if (publisher) {
      await publisher.disconnect();
      console.log('🔌 Redis连接已断开');
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  console.log('🚀 开始Redis发布测试...');
  testRedisPublish().then(() => {
    console.log('🎉 测试完成');
    process.exit(0);
  }).catch((error) => {
    console.error('💥 测试失败:', error);
    process.exit(1);
  });
}

module.exports = { testRedisPublish };
