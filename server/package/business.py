import json
import logging
import random
import re
import requests
import threading
import time



from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.core.paginator import Paginator
from django.db import transaction, connection
from django.db.models import F, Count, Q, Sum
from django.db.models import OuterRef, Subquery
from django.db.models import Prefetch



from django.core import serializers

from collections import namedtuple
from datetime import timedelta

from steambase.enums import RespCode, TradeType, PackageState, TradeState, B2CTradeState, ZBTradeState, ZBTradeSource, ChargeState
from steambase.redis_con import get_redis
from steambase.utils import ParamException, is_connection_usable
from libs.steamapi import SteamApi
from sitecfg.interfaces import get_deposit_price_min, get_enable_dynamic_box_chance
from package.service.item import get_item_price, get_item_price_by_package, get_items_price_from_waxpeer
from package.service.trade import request_deposit, request_withdraw, cancel_withdraw_result
from package.models import TradeBot, PackageItem, TradeRecord, ShopBot, BlackList, ExchangeRecord, ShopRecord, PackageItemPriceRate
from package.models import TradeBotInventory, ItemInfo, UnlockTimeConfig, LockItemsStatistics, PackageStatisticsDay, ItemWhitelist
from box.models import DropItem
from box.serializers import DropItemSerializer
from package.serializers import TradeRecordSerializer, PackageItemSerializer, ExchangeRecordSerializer, PackageStatisticsDaySerializer, ItemInfoSerializer, ItemPriceListSerializer, ItemCategorySerializer, ItemQualitySerializer, ItemRaritySerializer, ItemExteriorSerializer
from package.models import ItemPrice, ItemCategory, ItemQuality, ItemRarity, ItemExterior
from b2ctrade.zbtservice.zbtapi import ZbtApi
from authentication.models import UserAsset, AuthUser, UserExtra
from b2ctrade.models import ZBTradeRecord
from charge.models import ChargeRecord

from box.utils import update_user_box_chance_type

# 2025
from box.serializers import CaseSerializer
from sitecfg.interfaces import get_enable_cache

USER = get_user_model()
_logger = logging.getLogger(__name__)
_steam_img_base = 'https://steamcommunity-a.akamaihd.net/economy/image/{icon_url}/'
AttrKeyMap = namedtuple('AttrKeyMap', ['key', 'default'])
ItemPriceMap = {
    'cai_item_price': AttrKeyMap('cai_item_price', 0),
    'item_refer_price_rmb': AttrKeyMap('item_refer_price_rmb', 0),
    'item_refer_price_dollar': AttrKeyMap('item_refer_price_dollar', 0),
    'steam_normal_price_dollar': AttrKeyMap('steam_normal_price_dollar', 0)
}
PackageItemMap = {
    'market_hash_name': AttrKeyMap('market_hash_name', None),
    'assetid': AttrKeyMap('assetid', None),
    'instanceid': AttrKeyMap('instanceid', None)
}


def cache_user_inventory(steamid, appid, inventory):
    if not inventory:
        return
    key = 'inventory:{0}:{1}'.format(steamid, appid)
    cache.set(key, json.dumps(inventory), settings.HOUR_REDIS_TIMEOUT)


def read_inventory_from_cache(steamid, appid):
    key = 'inventory:{0}:{1}'.format(steamid, appid)
    inventory = json.loads(cache.get(key))
    return inventory


def get_user_inventory(steamid, appid='730', complete=False):
    steamapi = SteamApi(api_key=settings.API_KEY, steamid=steamid, inv_filter={
        'quality': ['autographed', 'corrupted', 'frozen', 'lucky']
    })
    inventory = steamapi.inventory(appid=appid, language='english')

    # 同步可能缺失的饰品数据
    # sync_item_info(inventory.get('items', []))

    # 给物品信息加上价格price
    for item in inventory.get('items', []):
        item['origin_price'] = get_item_price(item.get('market_hash_name'))
        item['price'] = get_item_price(item.get('market_hash_name'))
        item['icon_url'] = _steam_img_base.format(icon_url=item.get('icon_url', ''))
        item['icon_url_large'] = _steam_img_base.format(icon_url=item.get('icon_url_large', ''))
        item_info = ItemInfo.objects.filter(market_hash_name=item.get('market_hash_name')).first()
        item['market_name_cn'] = item_info.market_name_cn if item_info else item.get('market_hash_name')

    # if not complete:
    #     inventory['items'] = [item for item in inventory['items']
    #                            if (item['price'] >= get_deposit_price_min() or item['rarity'] == 'immortal') and
    #                                 (ItemWhitelist.objects.filter(
    #                                     item_info__market_hash_name=item.get('market_hash_name'),
    #                                     enable=True
    #                                 ))]
    inventory['items'] = sorted(inventory['items'], key=lambda i: i['price'], reverse=True)
    inventory['total_count'] = len(inventory['items'])
    cache_user_inventory(steamid, appid, inventory)
    return RespCode.Succeed.value, inventory


def get_user_package(user, query, _all, fields, page, page_size, order, name):
    total_price = 0
    if _all == '1':
        queryset = PackageItem.objects.filter(user=user, state__in=[
            PackageState.Available.value,
            PackageState.Blocked.value,
            PackageState.Withdrawing.value,
            PackageState.Withdrawn.value,
            PackageState.Exchanged.value
        ], **query).order_by(order)
    elif _all == '4':
        queryset = PackageItem.objects.filter(user=user, state=PackageState.Withdrawing.value, **query).order_by(order)

    elif _all == '5':
        queryset = PackageItem.objects.filter(user=user, state=PackageState.Withdrawn.value, **query).order_by(order)
    elif _all == '6':
        queryset = PackageItem.objects.filter(user=user, state=PackageState.Exchanged.value, **query).order_by(order)
    else:
        queryset = PackageItem.objects.filter(user=user, state=PackageState.Available.value, **query).order_by(order)
        
    
    if name:
        queryset = queryset.filter(Q(item_info__market_name_cn__icontains=name)|Q(item_info__market_hash_name__icontains=name))
    items = queryset.filter(user=user, state=PackageState.Available.value).order_by(order)
    for item in items:
        price = item.amount
        total_price += price
    paginator = Paginator(queryset, page_size)
    package = paginator.page(page)
    package_data = PackageItemSerializer(package, many=True, fields=fields).data
    # for p in package_data:
    #     p['price'] = get_item_price(p['market_hash_name'])
    resp = {
        'items': package_data,
        'total': paginator.count,
        'total_price': round(total_price, 2),
        'page': page,
        'limit': page_size
    }
    # unlock_time_cfg = UnlockTimeConfig.objects.first()
    # if unlock_time_cfg:
    #     resp['unlock_time'] = unlock_time_cfg.last_unlock_time + timedelta(minutes=unlock_time_cfg.interval)
    return RespCode.Succeed.value, resp


def get_shop_package(query, fields):
    resp = []
    shops = ShopBot.objects.filter(enable=True)
    for shop in shops:
        package = PackageItem.objects.filter(user=shop.user, state=PackageState.Available.value, **query).order_by('-item_info__item_price__price')
        package_data = PackageItemSerializer(package, many=True, fields=fields).data
        for p in package_data:
            p['price'] = get_item_price(p['market_hash_name'])
        resp.append({
            'shop': shop.user.uid,
            'items': package_data
        })
    return RespCode.Succeed.value, resp


def user_deposit_items(user, appid, assetids):
    blacklist = BlackList.objects.filter(user=user).first()
    if user.extra.ban_deposit or (blacklist and blacklist.enable and blacklist.ban_deposit):
        return RespCode.NoPermission.value, _('无权执行此操作')

    steamid = user.steam.steamid
    trade_url = user.asset.tradeurl
    if not trade_url:
        return RespCode.InvalidParams.value, _('请先输入您的交易链接')
    bots = TradeBot.objects.filter(enable=True, enable_deposit=True)
    if len(bots) == 0:
        return RespCode.NoBotOnline.value, _('No bot online')
    bot = random.choice(bots)
    bot_steamid = bot.steamid
    inventory = read_inventory_from_cache(steamid, appid)
    inventory_items_map = {item.get('assetid', ''): item for item in inventory.get('items', [])}
    deposit_items = []
    item_map = set()
    for assetid in assetids:
        if assetid in inventory_items_map.keys() and assetid not in item_map:
            deposit_items.append(inventory_items_map[assetid])
            item_map.add(assetid)
    if len(deposit_items) == 0 or len(deposit_items) != len(assetids):
        return RespCode.InvalidParams.value, _('Invalid params')

    deposit_data = {
        'user': user,
        'tradeurl': trade_url,
        'trade_type': TradeType.Deposit.value,
        'items': deposit_items,
        'bot_steamid': bot_steamid,
        'review': True
    }
    serializer = TradeRecordSerializer(data=deposit_data)
    if not serializer.is_valid():
        return RespCode.InvalidParams.value, serializer.error_messages
    record = serializer.save(**deposit_data)
    if record.review:
        request_deposit(record)
    resp = {
        'uid': record.uid
    }
    return RespCode.Succeed.value, resp


def user_withdraw_items(user, uids):
    blacklist = BlackList.objects.filter(user=user).first()
    if user.extra.ban_withdraw or (blacklist and blacklist.enable and blacklist.ban_withdraw):
        return RespCode.NoPermission.value, _('无权执行此操作')

    try:
        trade_url = user.asset.tradeurl
        if not trade_url or not re.match(r'^https://steamcommunity.com/tradeoffer/new/\?partner=[\d]+&token=[\w\d\-]{8}$', trade_url):
            return RespCode.InvalidParams.value, _('Invalid trade url')

        items = PackageItem.update_state(uids, PackageState.Available.value, PackageState.Withdrawing.value, user)
        items_map = {}
        for item in items:
            if not item.bot_steamid:
                bot_inv = TradeBotInventory.objects.filter(item_info__market_hash_name=item.item_info.market_hash_name,
                                                           matching=False, enable=True).first()
                if not bot_inv:
                    PackageItem.update_state([item.uid], PackageState.Withdrawing.value, PackageState.Blocked.value, user)
                    uids.remove(item.uid)
                    item_info = item.item_info
                    lock_info = LockItemsStatistics.objects.filter(item_info=item_info).first()
                    if lock_info:
                        lock_info.count += 1
                        lock_info.save()
                    else:
                        LockItemsStatistics.objects.create(item_info=item_info, count=1)
                    continue
                bot_inv.matching = True
                bot_inv.save()
                item.assetid = bot_inv.assetid
                item.instanceid = bot_inv.instanceid
                item.bot_steamid = bot_inv.bot_steamid
                item.save()

            if item.bot_steamid not in items_map:
                items_map[item.bot_steamid] = []
            item_data = {
                'item_info': item.item_info,
                'package': item,
                'price': get_item_price(item.item_info.market_hash_name),
                'assetid': item.assetid,
                'instanceid': item.instanceid,
            }
            items_map[item.bot_steamid].append(item_data)
        if not items_map:
            PackageItem.update_state(uids, PackageState.Withdrawing.value, PackageState.Available.value, user)
            return RespCode.InvalidParams.value, _('Out of stock, please content customer service on the right side.')

        data = {
            'uid': []
        }
        for bot_steamid, bot_items in items_map.items():
            withdraw_data = {
                'user': user,
                'tradeurl': trade_url,
                'trade_type': TradeType.Withdraw.value,
                'items': bot_items,
                'bot_steamid': bot_steamid,
                'review': True
            }
            serializer = TradeRecordSerializer(data=withdraw_data)
            if not serializer.is_valid():
                PackageItem.update_state(uids, PackageState.Withdrawing.value, PackageState.Available.value, user)
                return RespCode.InvalidParams.value, serializer.error_messages

            record = serializer.save(**withdraw_data)
            request_withdraw(record=record)
            data['uid'].append(record.uid)

        return RespCode.Succeed.value, data
    except ParamException as e:
        return RespCode.InvalidParams.value, str(e)


def get_trade_record(user, query, fields, page, page_size):
    queryset = TradeRecord.objects.filter(user=user, **query).order_by('-trade_time')
    paginator = Paginator(queryset, page_size)
    records = paginator.page(page)
    records_data = TradeRecordSerializer(records, many=True, fields=fields).data
    resp = {
        'records': records_data,
        'total': paginator.count
    }
    return RespCode.Succeed.value, resp


def get_exchange_record(user, query, fields, page, page_size, assetid=None):
    queryset = ExchangeRecord.objects.filter(user=user, **query).order_by('-create_time')
    if assetid:
        queryset = queryset.filter(package__assetid__in=assetid)
    paginator = Paginator(queryset, page_size)
    records = paginator.page(page)
    records_data = ExchangeRecordSerializer(records, many=True, fields=fields).data
    resp = {
        'records': records_data,
        'total': paginator.count
    }
    return RespCode.Succeed.value, resp


def user_exchange_items(user, uids, rate=1):
    try:
        # 黑名单限制
        blacklist = BlackList.objects.filter(user=user).first()
        if user.extra.ban_exchange or (blacklist and blacklist.enable and blacklist.ban_exchange):
            return RespCode.NoPermission.value, _('无权执行此操作')
        

        with transaction.atomic():
            # 检查待兑换的物品是否属于该用户并且状态为可兑换
            items_to_exchange = PackageItem.objects.filter(uid__in=uids, user=user, state=PackageState.Available.value)
            if not items_to_exchange.exists() or items_to_exchange.count() != len(uids):
                # 如果找到的物品数量与预期不符，说明有些物品不属于用户或不在可兑换状态
                return RespCode.InvalidParams.value, _('一些饰品不存在或已被出售')

            items = PackageItem.update_state(uids, PackageState.Available.value, PackageState.Exchanged.value, user)

            total_price = 0
            for item in items:
                price = get_item_price_by_package(item)
                # price = item.amount
                # if item.source in [0, 1]:
                #     ExchangeRecord.objects.create(user=user, item_info=item.item_info, package=item, price=price, currency=1)
                #     user.update_diamond(price, 'Exchange item')
                # elif item.source == 2:
                ExchangeRecord.objects.create(user=user, item_info=item.item_info, package=item, price=price, currency=0)
                user.update_balance(price, _('出售饰品'))
                total_price += price
                # else:
                #     raise ParamException(_('Invalid item source'))
                # TradeBotInventory.objects.filter(item_info=item.item_info, assetid=item.assetid, instanceid=item.instanceid,
                                                #  bot_steamid=item.bot_steamid).update(matching=False)
            items.update(user=None)

            # 动态更新用户的box_chance_type 先判断是否开启
            
            # update_user_box_chance_type(user)

            if not user.is_superuser and not user.is_staff and not user.extra.locked_box_chance:
                update_user_box_chance_type(user)
            #     print("普通用户更新概率:"+ user.username)
            # else:
            #     print("超级用户不更新概率")

            resp = {
                'uids': uids,
                'count': len(uids),
                'price': round(total_price, 2)
            }
            return RespCode.Succeed.value, resp
    except ParamException as e:
        return RespCode.InvalidParams.value, str(e)





def user_exchange_all_items(user, rate=1):
    try:
        blacklist = BlackList.objects.filter(user=user).first()
        if user.extra.ban_exchange or (blacklist and blacklist.enable and blacklist.ban_exchange):
            return RespCode.NoPermission.value, _('无权执行此操作')

        items = PackageItem.objects.filter(user=user, state=PackageState.Available.value)
        uids = [i.uid for i in items]
        with transaction.atomic():

            # 直接筛选出用户拥有且状态为可兑换的物品
            items = PackageItem.objects.filter(user=user, state=PackageState.Available.value)
            if not items:
                return RespCode.InvalidParams.value, _('没有可出售的饰品')

            items = PackageItem.update_state(uids, PackageState.Available.value, PackageState.Exchanged.value, user)
            total_price = 0
            for item in items:
                # price = get_item_price_by_package(item)
                price = item.amount
                # if item.source in [0, 1]:
                #     ExchangeRecord.objects.create(user=user, item_info=item.item_info, package=item, price=price, currency=1)
                #     user.update_diamond(price, 'Exchange item')
                # elif item.source == 2:
                ExchangeRecord.objects.create(user=user, item_info=item.item_info, package=item, price=price, currency=0)
                user.update_balance(price, '出售饰品')
                total_price += price
                # else:
                #     raise ParamException(_('Invalid item source'))
                # TradeBotInventory.objects.filter(item_info=item.item_info, assetid=item.assetid, instanceid=item.instanceid,
                                                #  bot_steamid=item.bot_steamid).update(matching=False)
            items.update(user=None)
            resp = {
                'uids': uids,
                'count': len(uids),
                'price': round(total_price, 2)
            }
            return RespCode.Succeed.value, resp
    except ParamException as e:
        return RespCode.InvalidParams.value, str(e)


def user_buy_items(user, shopid, uids):
    blacklist = BlackList.objects.filter(user=user).first()
    if user.extra.ban_shop or (blacklist and blacklist.enable and blacklist.ban_shop):
        return RespCode.NoPermission.value, _('无权执行此操作')

    shop = USER.objects.filter(uid=shopid).first()
    if not shop:
        return RespCode.InvalidParams.value, _('Out of stock, please content customer service on the right side.')

    try:
        with transaction.atomic():
            items = PackageItem.update_state(uids, PackageState.Available.value, PackageState.Blocked.value, shop)
            for item in items:
                price = get_item_price(item.item_info.market_hash_name)
                ShopRecord.objects.create(user=user, item_info=item.item_info, package=item, price=price)
                user.update_balance(-price, _('Shop item'))
            items.update(user=user)
            PackageItem.update_state(uids, PackageState.Blocked.value, PackageState.Available.value, user)
            return RespCode.Succeed.value, uids
    except ParamException as e:
        return RespCode.InvalidParams.value, str(e)


def check_auto_exchange_item():
    while True:
        try:
            if not settings.PACKAGE_EXCHANGE_AUTO:
                continue
            if not is_connection_usable():
                connection.close()

            dt_now = timezone.now()
            exchange_delay = settings.PACKAGE_EXCHANGE_DELAY
            package_timeout = PackageItem.objects.filter(update_time__lt=dt_now-timedelta(seconds=exchange_delay),
                                                         state=PackageState.Available.value)

            items_map = {}
            for item in package_timeout:
                if not item.user:
                    continue
                if item.user not in items_map:
                    items_map[item.user] = []
                items_map[item.user].append(item.uid)
            for user, uids in items_map.items():
                user_exchange_items(user, uids, settings.PACKAGE_ITEM_PRICE_RATE)
                _logger.info('Auto exchange items: {}'.format(uids))
        except Exception as e:
            _logger.exception(e)
        finally:
            time.sleep(1)


def check_package_unlock():
    while True:
        try:
            if not is_connection_usable():
                connection.close()

            unlock_config = UnlockTimeConfig.objects.first()
            if unlock_config.interval <= 0:
                continue

            if unlock_config.last_unlock_time + timedelta(minutes=unlock_config.interval) > timezone.now():
                continue

            unlock_config.last_unlock_time = timezone.now()
            unlock_config.save()

            # lock_items_info = PackageItem.objects.filter(state=PackageState.Blocked.value).values(
            #     'item_info__market_hash_name').annotate(Count('item_info__market_hash_name'))
            # for info in lock_items_info:
            #     item_info = ItemInfo.objects.filter(market_hash_name=info['item_info__market_hash_name']).first()
            #     lock_info = LockItemsStatistics.objects.filter(item_info__market_hash_name=info['item_info__market_hash_name']).first()
            #     if lock_info:
            #         lock_info.count += info['item_info__market_hash_name__count']
            #         lock_info.save()
            #     else:
            #         LockItemsStatistics.objects.create(item_info=item_info, count=info['item_info__market_hash_name__count'])

            PackageItem.objects.filter(state=PackageState.Blocked.value).update(state=PackageState.Available.value)
            _logger.info('Unlock items')
        except Exception as e:
            _logger.exception(e)
        finally:
            time.sleep(1)


def update_item_list_price(market_hash_name_list):
    zbtapi = ZbtApi(app_key=settings.ZBT_APP_KEY, appid='730')
    price_data = zbtapi.get_item_list_price_data(keyword_list=market_hash_name_list)
    if not price_data:
        return
    for hash_name, price_dict in price_data.items():
        price = price_dict.get('price', 0)
        if price:
            item = ItemInfo.objects.filter(market_hash_name=hash_name).first()
            if not item:
                continue
            ItemPrice.objects.update_or_create(item_info=item, defaults={'zbt_price': price})
            item_price = ItemPrice.objects.filter(item_info=item).first()
            item_price.set_price()


def sync_items_price():
    """
    同步所有物品的价格，按批量进行更新。
    """
    batch_size = 50  # 每批处理 50 个物品
    zbtapi = ZbtApi(app_key=settings.ZBT_APP_KEY, appid='730')

    try:
        items = ItemInfo.objects.all().order_by('id')
        market_hash_name_list = []

        for item in items:
            market_hash_name_list.append(item.market_hash_name)
            # 达到批量大小时，更新价格
            if len(market_hash_name_list) >= batch_size:
                process_price_update(zbtapi, market_hash_name_list)
                market_hash_name_list = []

        # 处理剩余的物品
        if market_hash_name_list:
            process_price_update(zbtapi, market_hash_name_list)

        _logger.info('Sync items price completed successfully.')
    except Exception as e:
        _logger.error(f"Error during sync_items_price: {e}", exc_info=True)

def process_price_update(zbtapi, market_hash_name_list):
    """
    更新物品价格。
    :param zbtapi: ZbtApi 实例
    :param market_hash_name_list: 批量的 market_hash_name 列表
    """
    try:
        price_data = zbtapi.get_item_list_price_data(keyword_list=market_hash_name_list)
        if not price_data:
            _logger.warning(f"No price data returned for: {market_hash_name_list}")
            return

        for hash_name, price_dict in price_data.items():
            price = price_dict.get('price', 0)
            if price:
                update_item_price(hash_name, price)
    except Exception as e:
        _logger.error(f"Error in process_price_update: {e}", exc_info=True)

def update_item_price(hash_name, price):
    """
    更新单个物品的价格。
    :param hash_name: 物品的 market_hash_name
    :param price: 新价格
    """
    try:
        item = ItemInfo.objects.filter(market_hash_name=hash_name).first()
        if not item:
            _logger.warning(f"ItemInfo not found for hash_name: {hash_name}")
            return

        item_price, created = ItemPrice.objects.update_or_create(
            item_info=item,
            defaults={'zbt_price': price}
        )
        item_price.set_price()  # 更新自定义价格
        action = "Created" if created else "Updated"
        _logger.info(f"{action} price for item: {hash_name}, zbt_price: {price}")
    except Exception as e:
        _logger.error(f"Error updating price for hash_name: {hash_name}: {e}", exc_info=True)


def setup_sync_items_price_worker():
    th = threading.Thread(target=sync_items_price, args=())
    th.start()


def sync_item_info_from_api(appid, contextid, classid):
    url = 'http://api.steampowered.com/ISteamEconomy/GetAssetClassInfo/v0001/'
    params = {
        'key': settings.API_KEY,
        'appid': appid,
        'class_count': '1',
        'classid0': classid
    }
    params_cn = {
        'key': settings.API_KEY,
        'appid': appid,
        'class_count': '1',
        'classid0': classid,
        'language': 'zh'
    }
    resp = requests.get(url, params=params, timeout=10)
    resp_cn = requests.get(url, params=params_cn, timeout=10)
    body = resp.json()
    body_cn = resp_cn.json()
    asset = body['result'].get(classid, {})
    if not asset:
        return RespCode.InvalidParams.value, _('Invalid params')
    asset_cn = body_cn['result'].get(classid, {})
    tags = asset.get('tags', {})
    tags_map = {t['category']: t for t in tags.values()}
    info = {
        'appid': appid,
        'contextid': contextid,
        'classid': classid,
        'name': asset.get('name', None),
        'name_cn': asset_cn.get('name', None),
        'market_name': asset.get('market_name', None),
        'market_name_cn': asset_cn.get('market_name', None),
        'market_hash_name': asset.get('market_hash_name', None),
        'name_color': asset.get('name_color', None),
        'exterior': tags_map.get('Exterior', {}).get('internal_name', None),
        'rarity': tags_map.get('Rarity', {}).get('internal_name', None),
        'type': tags_map.get('Type', {}).get('internal_name', None),
        'quality': tags_map.get('Quality', {}).get('internal_name', None),
        'weapon': tags_map.get('Weapon', {}).get('internal_name', None),
        'item_set': tags_map.get('ItemSet', {}).get('internal_name', None),
        'hero': tags_map.get('Hero', {}).get('internal_name', None),
        'slot': tags_map.get('Slot', {}).get('internal_name', None),
        'icon_url': asset.get('icon_url', None),
        'icon_url_large': asset.get('icon_url_large', None),
        'rarity_color': tags_map.get('Rarity', {}).get('color', None),
        'quality_color': tags_map.get('Quality', {}).get('color', None),
    }
    item_info = ItemInfo.objects.filter(market_hash_name=info['market_hash_name'])
    if item_info:
        item_info.update(**info)
    else:
        ItemInfo.objects.create(**info)
    # update_item_price(info['market_hash_name'])
    return RespCode.Succeed.value, info['market_hash_name']


def cancel_send(record):
    r = get_redis()
    steamer = record.user
    key = 'send_{0}_{1}'.format(record.uid, steamer.steam.steamid)
    val = cache.get(key)
    if val:
        send = json.loads(val)
        if send.get('status', None) == TradeState.Initialed.value:
            send['status'] = TradeState.Cancelled.value
            r.set(key, json.dumps(send))
            _logger.info('Cancel send {}'.format(record.uid))
    else:
        if record.state == TradeState.Initialed.value:
            record.state = TradeState.Cancelled.value
            record.save()
            cancel_withdraw_result(record)
            _logger.info('Cancel send {}'.format(record.uid))


def get_withdraw_statistics_day(count):
    withdraw_statistics_day = PackageStatisticsDay.objects.filter(trade_type=TradeType.Withdraw.value).order_by('-create_time')[:count]
    withdraw_statistics_day_data = PackageStatisticsDaySerializer(withdraw_statistics_day, many=True).data
    return withdraw_statistics_day_data


def get_item_detail(query, fields):
    """
    获取物品详细信息，包括价格、相关箱子等
    
    参数:
    - query: 查询条件，包含物品ID
    - fields: 需要返回的字段列表
    
    返回:
    - 响应代码和物品详细信息
    """
    # 检查查询参数
    item_id = query.get('id')
    if not item_id:
        return RespCode.InvalidParams.value, _('物品ID不能为空')
        
    # 构建缓存键
    cache_key = f'item_detail_{item_id}'
    
    # 尝试从缓存获取数据
    # cached_data = cache.get(cache_key)
    # if cached_data:
    #     return RespCode.Succeed.value, cached_data
    
    # 使用select_related优化查询
    item = ItemInfo.objects.filter(id=item_id).select_related('item_price').first()
    if not item:
        return RespCode.InvalidParams.value, _('没有找到该饰品')

    # 构建响应数据
    respData = {}
    
    # 获取物品基本信息
    item_data = ItemInfoSerializer(item, fields=fields).data
    
    # 获取价格历史
    respData['item_info'] = item_data

    # 获取下一个和上一个物品数据 - 使用优化的查询
    next_prev_fields = ('id', 'name', 'name_zh_hans', 'name_en')
    next_item_obj = next_item(item_id)
    pre_item_obj = pre_item(item_id)
    
    respData['next_item'] = ItemInfoSerializer(next_item_obj, fields=next_prev_fields).data if next_item_obj else {}
    respData['pre_item'] = ItemInfoSerializer(pre_item_obj, fields=next_prev_fields).data if pre_item_obj else {}

    # 获取关联箱子信息
    drop_items = DropItem.objects.filter(item_info_id=item_id).select_related('case')
    respData['related_cases'] = []
    
    for drop_item in drop_items:
        # 确保case存在
        if drop_item.case:  
            respData['related_cases'].append({
                'id': drop_item.case.id,
                'name': drop_item.case.name,
                'name_en': drop_item.case.name_en,
                'name_zh_hans': drop_item.case.name_zh_hans,
                'case_key': drop_item.case.case_key if hasattr(drop_item.case, 'case_key') else '',
                # 拼接oss图片
                'cover': f'https://{settings.ALIYUN_OSS_BUCKET_NAME}.{settings.ALIYUN_OSS_ENDPOINT}{drop_item.case.cover}' if drop_item.case.cover else '',
                'price': drop_item.case.price if hasattr(drop_item.case, 'price') else '',
                'tag': drop_item.case.tag if hasattr(drop_item.case, 'tag') else '',
                'tag_en': drop_item.case.tag_en if hasattr(drop_item.case, 'tag_en') else '',
                'tag_zh_hans': drop_item.case.tag_zh_hans if hasattr(drop_item.case, 'tag_zh_hans') else '',
                'tag_color': drop_item.case.tag_color if hasattr(drop_item.case, 'tag_color') else '',
            })
        


    
    # 获取关联皮肤信息
    related_skin_fields = ('id', 'name', 'name_en', 'name_zh_hans', 'image', 'item_price', 'item_category', 'item_quality', 'item_rarity', 'item_exterior')
    related_skin_code, related_skins = get_items_relate(key=item.name, fields=related_skin_fields)
    
    if related_skin_code == RespCode.Succeed.value:
        respData['related_skins'] = related_skins        

    resp = {
        'items': respData
    }

    # 缓存结果 - 物品详情缓存1小时
    cache.set(cache_key, resp, timeout=60 * 60)

    return RespCode.Succeed.value, resp

def get_items_prices(item_id):
    try:
        item_prices = ItemPrice.objects.filter(item_info_id=item_id).values('price', 'zbt_price', 'update_time')

        return list(item_prices) if item_prices else []
    except ItemPrice.DoesNotExist:
        return []
    
# def get_item_detail(query, fields):
#     if not ItemInfo.objects.filter(**query).exists():
#         return RespCode.InvalidParams.value, _('没有这件饰品')

#     item = ItemInfo.objects.filter(**query).first()
#     resp = {}
#     if item:
#         item_data = ItemInfoSerializer(item, fields=fields).data
#         item_data['price'] = get_item_price(item_data['market_hash_name'])
#         resp['item'] = item_data        
    
#         field = ('id', 'name_cn')
#         next= next_item(item_data['id'])
#         pre= pre_item(item_data['id'])
#         resp['next'] = ItemInfoSerializer(next, fields=field).data
#         resp['pre'] = ItemInfoSerializer(pre, fields=field).data
        
    
#     return RespCode.Succeed.value, resp
    
# 下一篇    
    
def next_item(id):
    return ItemInfo.objects.filter(id__gt=id).order_by('id').first()

# 上一篇

def pre_item(id):
    return ItemInfo.objects.filter(id__lt=id).order_by('-id').first()

def get_items_new(num):
    if num > 36:
        num = 36
    # 定义缓存键
    cache_key = f'items_new_{num}'
    # 尝试从缓存中获取数据
    cached_data = cache.get(cache_key)
    if cached_data is not None:
        return RespCode.Succeed.value, cached_data  
    itemlist = ItemInfo.objects.filter(icon_url_large__isnull=False).order_by("-id")[:num]
    data = ItemInfoSerializer(itemlist, many=True).data
    # 构建字典
    # item_dict = {
    #     index: {
    #         'item_id': value['id'],
    #         'price': value['item_price']['price'] if value['item_price'] else 0,
    #         'zbt_price': value['item_price']['zbt_price'] if value['item_price'] and value['item_price']['zbt_price'] else 0,
    #         'item_name': value['market_name_cn'],
    #         'color': value['rarity_color'] if value['rarity_color'] != "NULL" else "d1273e",
    #         'image': value['icon_url_large']
    #     }
    #     for index, value in enumerate(data)
    # }

    resp = {
        'data':data
    }
    # 将查询结果缓存1小时，以减少对数据库的访问
    cache.set(cache_key, resp, timeout = 60 * 60) 
    return RespCode.Succeed.value, resp
    
def get_items_update(num):
    if num > 36:
        num = 36
    # 定义缓存键
    cache_key = f'items_update_{num}'
    # 尝试从缓存中获取数据
    cached_data = cache.get(cache_key)
    if cached_data is not None:
        return RespCode.Succeed.value, cached_data  

    itemlist = ItemInfo.objects.filter(icon_url_large__isnull=False).order_by("-update_time")[:num]
    data = ItemInfoSerializer(itemlist, many=True).data
    # 构建字典
    # item_dict = {
    #     index: {
    #         'item_id': value['id'],
    #         'price': value['item_price']['price'] if value['item_price'] else 0,
    #         'zbt_price': value['item_price']['zbt_price'] if value['item_price'] and value['item_price']['zbt_price'] else 0,
    #         'item_name': value['market_name_cn'],
    #         'color': value['rarity_color'] if value['rarity_color'] != "NULL" else "d1273e",
    #         'image': value['icon_url_large']
    #     }
    #     for index, value in enumerate(data)
    # }

    resp = {
        'data':data
    }
    # 将查询结果缓存1小时，以减少对数据库的访问
    cache.set(cache_key, resp, timeout = 60 * 60) 
    return RespCode.Succeed.value, resp    

def get_items_expensive(num):
    if num > 36:
        num = 36
    # 定义缓存键
    cache_key = 'items_expensive'
    # 尝试从缓存中获取数据
    cached_data = cache.get(cache_key)
    if cached_data is not None:
        #print('缓存命中')
        return RespCode.Succeed.value, cached_data  
    itemlist = ItemPrice.objects.all().order_by("-zbt_price")[:num]   
    
    data = ItemPriceListSerializer(itemlist, many=True).data
    # 构建字典
    # item_dict = {
    #     index: {
    #         'item_id': value['id'],
    #         'price': value['item_price']['price'] if value['item_price'] else 0,
    #         'zbt_price': value['item_price']['zbt_price'] if value['item_price'] and value['item_price']['zbt_price'] else 0,
    #         'item_name': value['market_name_cn'],
    #         'color': value['rarity_color'] if value['rarity_color'] != "NULL" else "d1273e",
    #         'image': value['icon_url_large']
    #     }
    #     for index, value in enumerate(data)
    # }

    resp = {
        'data':data
    }
    # 将查询结果缓存24小时，以减少对数据库的访问
    cache.set(cache_key, resp, timeout = 60 * 60 * 24)
    return RespCode.Succeed.value, resp

    
def get_items_relate(key, fields=None):
    """
    获取与关键词相关的物品列表
    
    参数:
    - key: 搜索关键词字符串
    - fields: 要返回的字段列表，默认为None（返回所有字段）
    
    返回:
    - 响应码和相关物品数据
    """
    cache_key = 'items_relate_' + '_'.join(key)  # 修改缓存键以适应关键词列表
    # 尝试从缓存中获取数据
    cached_data = cache.get(cache_key)
    # if cached_data:       
    #     return RespCode.Succeed.value, cached_data  

    keywords = key.split('|')

    # 创建查询条件，初始为空
    query = Q()
    # 遍历所有关键词，并为每个关键词构建查询条件
    for keyword in keywords:
        # 使用正则表达式移除不需要的部分
        condition_patterns = [
            r'\(战痕累累\)', r'\(破损不堪\)', r'\(久经沙场\)', r'\（StatTrak™\）',
            r'\(略有磨损\)', r'\(崭新出厂\)', r'（全息）',
            r'（闪耀）', r'（闪亮）', r'（金色）', r'（透镜）'
        ]
        pattern = re.compile('|'.join(condition_patterns))
        cleaned_keyword = pattern.sub('', keyword).strip()
        # 如果关键词非空，添加到查询条件中
        if cleaned_keyword:
            query &= Q(market_name_cn__contains=cleaned_keyword)

    itemlist = ItemInfo.objects.filter(query).order_by("-id")
    
    # 使用提供的fields参数，如果没有提供则使用所有字段
    data = ItemInfoSerializer(itemlist, fields=fields, many=True).data    

    # 缓存查询结果
    cache.set(cache_key, data, timeout=60 * 60 * 2)
    return RespCode.Succeed.value, data





def get_random_items(num, domain, exclude_types, fields):
    if domain is None:
        return RespCode.InvalidParams.value, '参数错误'
    if num > 60:
        num = 60
    
    # 生成缓存键
    cache_key = f"random_items_{num}_{domain}"
    
    # 尝试从缓存中获取数据
    cached_data = cache.get(cache_key)
    # if cached_data:
    #     return RespCode.Succeed.value, cached_data

    # 构建数据库查询条件
    query = ItemInfo.objects.filter(type__isnull=False).exclude(type='')

    # print("Exclude types:", exclude_types)

    if exclude_types:
        exclude_types_list = exclude_types.split(',')
        query = query.exclude(type__in=exclude_types_list)

    # 直接从数据库中获取随机项
    randitems = query.order_by('?')[:num]
    data = ItemInfoSerializer(randitems, fields=fields, many=True).data

    # 构建字典
    # item_dict = {
    #     index: {
    #         'item_id': value['id'],
    #         'price': value['item_price']['price'] if value['item_price'] else 0,
    #         'zbt_price': value['item_price']['zbt_price'] if value['item_price'] and value['item_price']['zbt_price'] else 0,
    #         'item_name': value['market_name_cn'],
    #         'name': value['market_name_cn'],
    #         'name_en': value['market_hash_name'],
    #         'name_zh_hans': value['market_name_cn'],           
    #         'color': value['rarity_color'] if value['rarity_color'] != "NULL" else "d1273e",
    #         'image': value['icon_url_large']
    #     }
    #     for index, value in enumerate(data)
    # }

    resp = {'items': data}
    
    # 将数据存入缓存
    cache.set(cache_key, resp, timeout=60 * 60 * 24)  # 缓存有效期设为 24 小时    

    return RespCode.Succeed.value, resp


    

def get_type_items(type, page, page_size, order):
    # 生成缓存键
    cache_key = f"type_items_{type}_{page}_{page_size}_{order}"
    # 尝试从缓存中获取数据
    cached_data = cache.get(cache_key)
    if cached_data:
        return RespCode.Succeed.value, cached_data
    
    itemlist = ItemInfo.objects.filter(type=type).order_by(order)
    paginator = Paginator(itemlist, page_size)
    package = paginator.page(page)   
    
    package_data = ItemInfoSerializer(package, many=True).data

    # 构建字典
    # item_dict = {
    #     index: {
    #         'item_id': value['id'],
    #         'price': value['item_price']['price'] if value['item_price'] else 0,
    #         'zbt_price': value['item_price']['zbt_price'] if value['item_price'] and value['item_price']['zbt_price'] else 0,
    #         'item_name': value['market_name_cn'],
    #         'color': value['rarity_color'] if value['rarity_color'] != "NULL" else "d1273e",
    #         'image': value['icon_url_large']
    #     }
    #     for index, value in enumerate(package_data)
    # }
    
    resp = {
        'items': package_data,
        'total': paginator.count,
        'page': page,
        'limit': page_size
    }
    
    # 将数据存入缓存
    cache.set(cache_key, resp, timeout=3600 * 24)  # 缓存有效期设为 24 小时
    
    return RespCode.Succeed.value, resp


def get_tag_items(keyword, type, page, page_size, order):
    if keyword is None or type is None:
        return RespCode.InvalidParams.value, '参数错误'
    # 生成缓存键
    cache_key = f"type_items_{type}_{keyword}_{page}_{page_size}_{order}"
    # 尝试从缓存中获取数据
    cached_data = cache.get(cache_key)
    if cached_data:
        return RespCode.Succeed.value, cached_data
    
    itemlist = ItemInfo.objects.filter(market_name_cn__contains=keyword, type=type).select_related('item_price').order_by(order)
    paginator = Paginator(itemlist, page_size)
    package = paginator.page(page)   
    
    package_data = ItemInfoSerializer(package, many=True).data

    # 构建字典
    # item_dict = {
    #     index: {
    #         'item_id': value['id'],
    #         'price': value['item_price']['price'] if value['item_price'] else 0,
    #         'zbt_price': value['item_price']['zbt_price'] if value['item_price'] and value['item_price']['zbt_price'] else 0,
    #         'item_name': value['market_name_cn'],
    #         'color': value['rarity_color'] if value['rarity_color'] != "NULL" else "d1273e",
    #         'image': value['icon_url_large']
    #     }
    #     for index, value in enumerate(package_data)
    # }
    
    
    resp = {
        'items': package_data,
        'total': paginator.count,
        'page': page,
        'limit': page_size
    }
        
    # 将数据存入缓存
    cache.set(cache_key, resp, timeout=3600 * 24)  # 缓存有效期设为 24 小时
    
    return RespCode.Succeed.value, resp

def get_user_package_skin(user, _all, fields, page, page_size, order, name):
    state_mapping = {
        '0': [
            PackageState.Available.value,
            #PackageState.Blocked.value,
            PackageState.Withdrawing.value,
            PackageState.Withdrawn.value,
            PackageState.Exchanged.value
        ],
        '4': [PackageState.Withdrawing.value],
        '5': [PackageState.Withdrawn.value],
        '6': [PackageState.Exchanged.value],
        '1': [PackageState.Available.value]
    }

    states = state_mapping.get(_all, [PackageState.Available.value])

    queryset = PackageItem.objects.filter(user=user, state__in=states).order_by(order)

    if name:
        queryset = queryset.filter(Q(item_info__market_name_cn__icontains=name) | Q(item_info__market_hash_name__icontains=name))


    paginator = Paginator(queryset, page_size)
    package = paginator.page(page)
    package_data = PackageItemSerializer(package, many=True, fields=fields).data

    resp = {
        'items': package_data,
        'total': paginator.count,
        'page': page,
        'limit': page_size
    }

    return RespCode.Succeed.value, resp


# 以下是2025新版


def get_items_category(fields):
    # 生成缓存键
    cache_key = f"items_category"
    # 尝试从缓存中获取数据
    cached_data = cache.get(cache_key)
    if cached_data:
        return RespCode.Succeed.value, cached_data

    itemlist = ItemCategory.objects.filter(is_show=True).order_by('sort')
    data = ItemCategorySerializer(itemlist, fields=fields, many=True).data
    resp = {
        'items':data
    }
    # 将数据存入缓存
    cache.set(cache_key, resp, timeout=60 * 60 * 24)  # 缓存有效期设为 24 小时    

    return RespCode.Succeed.value, resp

def get_items_quality(fields):
    # 生成缓存键    
    cache_key = f"items_quality"
    # 尝试从缓存中获取数据
    cached_data = cache.get(cache_key)
    if cached_data:
        return RespCode.Succeed.value, cached_data

    itemlist = ItemQuality.objects.filter(is_show=True).order_by('sort')
    data = ItemQualitySerializer(itemlist, fields=fields, many=True).data
    resp = {
        'items':data
    }
    # 将数据存入缓存
    cache.set(cache_key, resp, timeout=60 * 60 * 24)  # 缓存有效期设为 24 小时    

    return RespCode.Succeed.value, resp

def get_items_rarity(fields):
    # 生成缓存键    
    cache_key = f"items_rarity"
    # 尝试从缓存中获取数据
    cached_data = cache.get(cache_key)
    if cached_data:
        return RespCode.Succeed.value, cached_data

    itemlist = ItemRarity.objects.filter(is_show=True).order_by('sort')
    data = ItemRaritySerializer(itemlist, fields=fields, many=True).data
    resp = {
        'items':data
    }
    # 将数据存入缓存
    cache.set(cache_key, resp, timeout=60 * 60 * 24)  # 缓存有效期设为 24 小时    

    return RespCode.Succeed.value, resp

def get_items_exterior(fields):
    # 生成缓存键    
    cache_key = f"items_exterior"
    # 尝试从缓存中获取数据
    cached_data = cache.get(cache_key)
    if cached_data:
        return RespCode.Succeed.value, cached_data

    itemlist = ItemExterior.objects.filter(is_show=True).order_by('sort')
    data = ItemExteriorSerializer(itemlist, fields=fields, many=True).data
    resp = {
        'items':data
    }
    # 将数据存入缓存
    cache.set(cache_key, resp, timeout=60 * 60 * 24)  # 缓存有效期设为 24 小时    

    return RespCode.Succeed.value, resp

def search_items(query, fields, page, page_size, sort):
    """
    搜索物品信息，支持分页和缓存
    
    参数:
    - query: 查询参数字典 (包含搜索关键词q和其他过滤条件)
    - fields: 需要返回的字段
    - page: 当前页码
    - page_size: 每页大小
    - order: 排序字段
    
    返回:
    - 响应码和数据
    """
    # 提取搜索关键词和过滤条件
    q = query.pop('q', '').strip() if 'q' in query else ''
    filter_conditions = {k: v for k, v in query.items() if v}
    
    # 生成缓存键
    cache_params = []
    if q:
        cache_params.append(f"q:{q}")
    for k, v in sorted(filter_conditions.items()):
        cache_params.append(f"{k}:{v}")
    
    cache_key = f"items_search_{'_'.join(cache_params) if cache_params else 'all'}_{page}_{page_size}_{sort}"

    
    # 尝试从缓存中获取数据
    cached_data = cache.get(cache_key)
    if get_enable_cache() and cached_data:
        return RespCode.Succeed.value, cached_data

    # 构建查询
    item_query = ItemInfo.objects.all()
    
    # 应用搜索条件 - 使用Q对象实现OR查询
    if q:
        item_query = item_query.filter(
            Q(name_zh_hans__contains=q) | 
            Q(name_en__icontains=q) | 
            Q(market_name_cn__contains=q) |
            Q(market_hash_name__icontains=q)
        )
    
    # 应用过滤条件
    if filter_conditions:
        item_query = item_query.filter(**filter_conditions)
    
    # 应用排序
    item_query = item_query.order_by(sort)
    
    # 分页处理
    paginator = Paginator(item_query, page_size)
    try:
        current_page = paginator.page(page)
    except:
        # 处理页码超出范围的情况
        current_page = paginator.page(1)
    
    # 序列化数据
    package_data = ItemInfoSerializer(current_page, fields=fields, many=True).data
    
    # 构建响应
    resp = {
        'items': package_data,
        'total': paginator.count,
        'page': page,
        'limit': page_size
    }

    # 设置缓存时间 - 对于不同查询类型使用不同的缓存时间
    timeout = 60 * 30 if q else 60 * 60 * 24
    cache.set(cache_key, resp, timeout=timeout)

    return RespCode.Succeed.value, resp