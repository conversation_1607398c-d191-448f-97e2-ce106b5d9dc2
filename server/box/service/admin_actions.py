from datetime import datetime, timezone, timedelta
from openpyxl import Workbook
from django.http import HttpResponse
from django.utils.translation import gettext_lazy as _

from package.service.item import get_item_price


def startup_export_excel(modeladmin, request, queryset):
    name = "BoxRecord-{}".format(datetime.now().strftime("%Y%m%d-%H%M%S"))  # 用于定义文件名, 格式为: app名.模型类名
    field_names = modeladmin.list_display
    field_names_trans = {
        "uid": "uid",
        "user": "用户",
        "case": "箱子",
        "item_info": "饰品",
        "price": "价格",
        "create_time": "创建时间",
    }
    response = HttpResponse(content_type='application/msexcel')  # 定义响应内容类型
    response['Content-Disposition'] = f'attachment; filename={name}.xlsx'  # 定义响应数据格式
    wb = Workbook()  # 新建Workbook
    ws = wb.active  # 使用当前活动的Sheet表
    ws.append([field_names_trans.get(field, "") for field in field_names])  # 将模型字段名作为标题写入第一行

    # 遍历选中queryset
    for obj in queryset:
        # set_attr = {
        #     'balance': obj.asset.balance,
        #     'box_chance_type': obj.extra.box_chance_type,
        #     'charge_amount': obj.asset.total_charge_balance,
        # }
        # # 设置自定义字段
        # [setattr(obj, key, value) for key, value in set_attr.items()]
        data = []
        for field in field_names:
            if field == 'create_time':
                if getattr(obj, field):
                    data.append(getattr(obj, field).astimezone(timezone(timedelta(hours=8))).strftime("%Y-%m-%d %H:%M:%S"))
                else:
                    data.append(None)
        # data = [f'{getattr(obj, field)}' for field in field_names]
        row = ws.append(data)
    wb.save(response)  # 将数据存入响应内容
    return response


startup_export_excel.short_description = _("导出EXCEL")


def case_export_to_excel(modeladmin, request, queryset):
    name = "CaseDrop-{}".format(datetime.now().strftime("%Y%m%d-%H%M%S"))  # 用于定义文件名, 格式为: app名.模型类名
    field_names = modeladmin.list_display
    field_names_trans = {
        "case": '箱子',
        "item_info": "饰品",
        "show_chance": "显示概率",
        "drop_chance_a": "掉落概率A",
        "drop_chance_b": "掉落概率B",
        "drop_chance_c": "掉落概率C",
        "drop_chance_d": "掉落概率D",
        "drop_chance_e": "掉落概率E",
        "custom_enable": "启用自定义价格",
        "custom_price": "自定义价格",
        "price": "价格"
    }
    response = HttpResponse(content_type='application/msexcel')  # 定义响应内容类型
    response['Content-Disposition'] = f'attachment; filename={name}.xlsx'  # 定义响应数据格式
    wb = Workbook()  # 新建Workbook
    ws = wb.active  # 使用当前活动的Sheet表
    ws.append([field_names_trans.get(field, "") for field in field_names_trans])  # 将模型字段名作为标题写入第一行

    # 遍历选中queryset
    for obj in queryset:
        drops = obj.drops.all()
        for drop in drops:
            set_attr = {
                "case": obj.name,
                "item_info": str(drop.item_info),
                "show_chance": drop.show_chance,
                "drop_chance_a": drop.drop_chance_a,
                "drop_chance_b": drop.drop_chance_b,
                "drop_chance_c": drop.drop_chance_c,
                "drop_chance_d": drop.drop_chance_d,
                "drop_chance_e": drop.drop_chance_e,
                "custom_enable": drop.custom_enable,
                "custom_price": drop.custom_price,
                "price": get_item_price(drop.item_info.market_hash_name)
            }
            # 设置自定义字段
            # [ for key, value in set_attr.items()]
            # for key, value in set_attr.items():
            #     setattr(obj, key, value)
            data = [value for field, value in set_attr.items()]
            row = ws.append(data)
    wb.save(response)  # 将数据存入响应内容
    return response


case_export_to_excel.short_description = _("箱子掉落导出EXCEL")
