// Animation synchronization composable – consumes WebSocket messages & schedules animations
import { useTimeSync } from './useTimeSync'
import { ref } from 'vue'

type OpeningStartPayload = {
  animation_id: string
  animation_start_timestamp: number
  animation_duration: number
  server_timestamp: number
  sync_config?: {
    tolerance_ms?: number
    max_delay_compensation?: number
  }
}

export const useAnimationSync = () => {
  const { updateOffset, toLocalTime } = useTimeSync()

  const activeAnimations = new Map<string, {
    startLocal: number
    duration: number
  }>()

  /** Handle opening_start WS payload */
  const handleOpeningStart = (payload: OpeningStartPayload) => {
    const clientRecv = Date.now()
    updateOffset(payload.server_timestamp, clientRecv, clientRecv)

    const localStart = toLocalTime(payload.animation_start_timestamp)
    const delay = localStart - Date.now()
    const { max_delay_compensation = 500 } = payload.sync_config || {}

    const startAnimation = (catchUp = 0) => {
      // emit custom event for actual animation component
      window.dispatchEvent(new CustomEvent('battle-animation-start', {
        detail: {
          id: payload.animation_id,
          catchUp,
          duration: payload.animation_duration,
        }
      }))
    }

    if (delay > 0) {
      setTimeout(() => startAnimation(0), delay)
    } else {
      const missed = Math.abs(delay)
      if (missed < payload.animation_duration + max_delay_compensation) {
        startAnimation(missed) // fast-forward
      }
    }

    activeAnimations.set(payload.animation_id, {
      startLocal: localStart,
      duration: payload.animation_duration,
    })
  }

  const clearAnimation = (id: string) => activeAnimations.delete(id)

  return {
    handleOpeningStart,
    clearAnimation,
    activeAnimations,
  }
} 