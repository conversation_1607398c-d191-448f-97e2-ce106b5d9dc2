#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
开箱对战系统配置文件
集中管理系统关键参数和阈值
"""

import os
from decimal import Decimal

class BattleSystemConfig:
    """对战系统配置"""
    
    # ========== 基础配置 ==========
    # 最大房间参与者数量
    MAX_ROOM_JOINERS = int(os.getenv('BATTLE_MAX_JOINERS', 4))
    
    # 最小房间参与者数量
    MIN_ROOM_JOINERS = int(os.getenv('BATTLE_MIN_JOINERS', 2))
    
    # 用户同时参与的最大房间数
    MAX_USER_ACTIVE_ROOMS = int(os.getenv('BATTLE_MAX_USER_ROOMS', 3))
    
    # 单个房间最大箱子数量
    MAX_CASES_PER_ROOM = int(os.getenv('BATTLE_MAX_CASES', 10))
    
    # ========== 安全配置 ==========
    # 投注金额不能超过用户余额的百分比
    MAX_BET_RATIO = Decimal(os.getenv('BATTLE_MAX_BET_RATIO', '0.5'))  # 50%
    
    # 分布式锁超时时间（秒）
    LOCK_TIMEOUT = int(os.getenv('BATTLE_LOCK_TIMEOUT', 30))
    
    # 最大重试次数
    MAX_RETRY_ATTEMPTS = int(os.getenv('BATTLE_MAX_RETRIES', 3))
    
    # 消息去重超时时间（秒）
    MESSAGE_DEDUP_TIMEOUT = int(os.getenv('BATTLE_DEDUP_TIMEOUT', 300))  # 5分钟
    
    # ========== 性能配置 ==========
    # 轮次缓存超时时间（秒）
    ROUND_CACHE_TIMEOUT = int(os.getenv('BATTLE_ROUND_CACHE_TIMEOUT', 300))  # 5分钟
    
    # 房间状态缓存超时时间（秒）
    ROOM_STATE_CACHE_TIMEOUT = int(os.getenv('BATTLE_ROOM_CACHE_TIMEOUT', 600))  # 10分钟
    
    # 异步任务超时时间（秒）
    ASYNC_TASK_TIMEOUT = int(os.getenv('BATTLE_ASYNC_TIMEOUT', 60))
    
    # 轮次间延迟时间（秒）
    ROUND_DELAY = int(os.getenv('BATTLE_ROUND_DELAY', 2))
    
    # ========== 增强版系统配置 ==========
    # 启用增强版系统
    ENABLE_ENHANCED_SYSTEM = os.getenv('ENABLE_ENHANCED_BATTLE_SYSTEM', 'true').lower() == 'true'
    
    # 房间最大处理时间（秒）
    MAX_ROOM_PROCESSING_TIME = int(os.getenv('BATTLE_MAX_PROCESSING_TIME', 300))  # 5分钟
    
    # 轮次超时时间（秒）
    ROUND_TIMEOUT = int(os.getenv('BATTLE_ROUND_TIMEOUT', 60))  # 1分钟
    
    # 最大并发房间数
    MAX_CONCURRENT_ROOMS = int(os.getenv('BATTLE_MAX_CONCURRENT', 50))
    
    # WebSocket通道名称
    WEBSOCKET_CHANNEL = os.getenv('BATTLE_WS_CHANNEL', 'ws_channel')
    
    # 消息重试次数
    MESSAGE_RETRY_COUNT = int(os.getenv('BATTLE_MSG_RETRY', 3))
    
    # ========== 清理配置 ==========
    # 房间清理间隔（秒）
    ROOM_CLEANUP_INTERVAL = int(os.getenv('BATTLE_CLEANUP_INTERVAL', 3600))  # 1小时
    
    # 卡住房间阈值（秒）
    STALE_ROOM_THRESHOLD = int(os.getenv('BATTLE_STALE_THRESHOLD', 1800))  # 30分钟
    
    # 自动清理启用
    AUTO_CLEANUP_ENABLED = os.getenv('BATTLE_AUTO_CLEANUP', 'true').lower() == 'true'
    
    # ========== 监控配置 ==========
    # 启用指标收集
    METRICS_ENABLED = os.getenv('BATTLE_METRICS_ENABLED', 'true').lower() == 'true'
    
    # 指标保留天数
    METRICS_RETENTION_DAYS = int(os.getenv('BATTLE_METRICS_RETENTION', 7))
    
    # 错误率告警阈值
    ALERT_THRESHOLD_ERROR_RATE = float(os.getenv('BATTLE_ALERT_ERROR_RATE', '0.1'))  # 10%
    
    # ========== 兼容性配置 ==========
    # Python版本检查
    PYTHON_VERSION_CHECK = os.getenv('BATTLE_PYTHON_VERSION_CHECK', 'true').lower() == 'true'
    
    # 异步回退启用
    ASYNC_FALLBACK_ENABLED = os.getenv('BATTLE_ASYNC_FALLBACK', 'true').lower() == 'true'
    
    # 传统支持启用
    LEGACY_SUPPORT_ENABLED = os.getenv('BATTLE_LEGACY_SUPPORT', 'true').lower() == 'true'
    
    # ========== 安全配置 ==========
    # 启用速率限制
    RATE_LIMIT_ENABLED = os.getenv('BATTLE_RATE_LIMIT', 'true').lower() == 'true'
    
    # 反作弊启用
    ANTI_CHEAT_ENABLED = os.getenv('BATTLE_ANTI_CHEAT', 'true').lower() == 'true'
    ROUND_DELAY = float(os.getenv('BATTLE_ROUND_DELAY', 2.0))
    
    # ========== 日志配置 ==========
    # 启用详细日志
    ENABLE_VERBOSE_LOGGING = os.getenv('BATTLE_VERBOSE_LOG', 'false').lower() == 'true'
    
    # 启用性能监控
    ENABLE_PERFORMANCE_MONITORING = os.getenv('BATTLE_PERF_MONITOR', 'false').lower() == 'true'
    
    # ========== 业务逻辑配置 ==========
    # 平局时的处理方式: 'random', 'split', 'first'
    TIE_RESOLUTION_METHOD = os.getenv('BATTLE_TIE_RESOLUTION', 'random')
    
    # 是否启用失败者补偿机制
    ENABLE_LOSER_COMPENSATION = os.getenv('BATTLE_LOSER_COMPENSATION', 'true').lower() == 'true'
    
    # 补偿物品的最小价值
    MIN_COMPENSATION_VALUE = Decimal(os.getenv('BATTLE_MIN_COMPENSATION', '0.5'))
    
    # 补偿物品的最大价值
    MAX_COMPENSATION_VALUE = Decimal(os.getenv('BATTLE_MAX_COMPENSATION', '5.0'))
    
    # ========== Redis配置 ==========
    # WebSocket消息频道
    WEBSOCKET_CHANNEL = os.getenv('BATTLE_WS_CHANNEL', 'ws_channel')
    
    # 锁键前缀
    LOCK_KEY_PREFIX = os.getenv('BATTLE_LOCK_PREFIX', 'battle_lock:')
    
    # 缓存键前缀
    CACHE_KEY_PREFIX = os.getenv('BATTLE_CACHE_PREFIX', 'battle_cache:')
    
    # 消息去重键前缀
    DEDUP_KEY_PREFIX = os.getenv('BATTLE_DEDUP_PREFIX', 'ws_msg_dedup:')
    
    # ========== 兼容性配置 ==========
    # Python版本检查
    PYTHON_VERSION_CHECK = os.getenv('BATTLE_PYTHON_CHECK', 'true').lower() == 'true'
    
    # 异步功能降级阈值
    ASYNC_FALLBACK_THRESHOLD = int(os.getenv('BATTLE_ASYNC_FALLBACK_THRESHOLD', 3))
    
    # 强制同步模式
    FORCE_SYNC_MODE = os.getenv('BATTLE_FORCE_SYNC', 'false').lower() == 'true'
    
    @classmethod
    def get_lock_key(cls, resource_type, resource_id):
        """生成锁键"""
        return f"{cls.LOCK_KEY_PREFIX}{resource_type}:{resource_id}"
    
    @classmethod
    def get_cache_key(cls, resource_type, resource_id):
        """生成缓存键"""
        return f"{cls.CACHE_KEY_PREFIX}{resource_type}:{resource_id}"
    
    @classmethod
    def get_dedup_key(cls, message_hash):
        """生成消息去重键"""
        return f"{cls.DEDUP_KEY_PREFIX}{message_hash}"
    
    @classmethod
    def validate_config(cls):
        """验证配置合理性"""
        errors = []
        
        # 基础参数验证
        if cls.MAX_ROOM_JOINERS < cls.MIN_ROOM_JOINERS:
            errors.append("MAX_ROOM_JOINERS不能小于MIN_ROOM_JOINERS")
        
        if cls.MAX_BET_RATIO <= 0 or cls.MAX_BET_RATIO > 1:
            errors.append("MAX_BET_RATIO必须在0-1之间")
        
        if cls.LOCK_TIMEOUT <= 0:
            errors.append("LOCK_TIMEOUT必须大于0")
        
        if cls.MESSAGE_DEDUP_TIMEOUT <= 0:
            errors.append("MESSAGE_DEDUP_TIMEOUT必须大于0")
        
        # 补偿配置验证
        if cls.MIN_COMPENSATION_VALUE < 0:
            errors.append("MIN_COMPENSATION_VALUE不能为负数")
        
        if cls.MAX_COMPENSATION_VALUE < cls.MIN_COMPENSATION_VALUE:
            errors.append("MAX_COMPENSATION_VALUE不能小于MIN_COMPENSATION_VALUE")
        
        return errors

class BattleSystemMetrics:
    """对战系统指标"""
    
    # 统计键前缀
    METRICS_KEY_PREFIX = 'battle_metrics:'
    
    @classmethod
    def increment_counter(cls, metric_name, value=1):
        """增加计数器"""
        try:
            from django.core.cache import cache
            key = f"{cls.METRICS_KEY_PREFIX}{metric_name}"
            current = cache.get(key, 0)
            cache.set(key, current + value, 86400)  # 24小时
        except Exception:
            pass  # 静默失败，不影响业务逻辑
    
    @classmethod
    def get_counter(cls, metric_name):
        """获取计数器值"""
        try:
            from django.core.cache import cache
            key = f"{cls.METRICS_KEY_PREFIX}{metric_name}"
            return cache.get(key, 0)
        except Exception:
            return 0
    
    @classmethod
    def record_timing(cls, metric_name, duration):
        """记录执行时间"""
        try:
            from django.core.cache import cache
            key = f"{cls.METRICS_KEY_PREFIX}timing:{metric_name}"
            timings = cache.get(key, [])
            timings.append(duration)
            # 只保留最近100条记录
            if len(timings) > 100:
                timings = timings[-100:]
            cache.set(key, timings, 3600)  # 1小时
        except Exception:
            pass
    
    @classmethod
    def get_average_timing(cls, metric_name):
        """获取平均执行时间"""
        try:
            from django.core.cache import cache
            key = f"{cls.METRICS_KEY_PREFIX}timing:{metric_name}"
            timings = cache.get(key, [])
            if timings:
                return sum(timings) / len(timings)
            return 0
        except Exception:
            return 0

# ========== 增强版系统配置管理 ==========

def get_enhanced_battle_config():
    """获取增强版对战系统配置"""
    from django.conf import settings
    
    config = {
        'enable_enhanced_system': BattleSystemConfig.ENABLE_ENHANCED_SYSTEM,
        'max_room_processing_time': BattleSystemConfig.MAX_ROOM_PROCESSING_TIME,
        'round_timeout': BattleSystemConfig.ROUND_TIMEOUT,
        'lock_timeout': BattleSystemConfig.LOCK_TIMEOUT,
        'max_concurrent_rooms': BattleSystemConfig.MAX_CONCURRENT_ROOMS,
        'message_dedup_timeout': BattleSystemConfig.MESSAGE_DEDUP_TIMEOUT,
        'websocket_channel': BattleSystemConfig.WEBSOCKET_CHANNEL,
        'message_retry_count': BattleSystemConfig.MESSAGE_RETRY_COUNT,
        'room_cleanup_interval': BattleSystemConfig.ROOM_CLEANUP_INTERVAL,
        'stale_room_threshold': BattleSystemConfig.STALE_ROOM_THRESHOLD,
        'auto_cleanup_enabled': BattleSystemConfig.AUTO_CLEANUP_ENABLED,
        'metrics_enabled': BattleSystemConfig.METRICS_ENABLED,
        'python_version_check': BattleSystemConfig.PYTHON_VERSION_CHECK,
        'async_fallback_enabled': BattleSystemConfig.ASYNC_FALLBACK_ENABLED,
        'legacy_support_enabled': BattleSystemConfig.LEGACY_SUPPORT_ENABLED,
        'rate_limit_enabled': BattleSystemConfig.RATE_LIMIT_ENABLED,
        'anti_cheat_enabled': BattleSystemConfig.ANTI_CHEAT_ENABLED,
    }
    
    # 根据环境调整配置
    if settings.DEBUG:
        config.update({
            'max_room_processing_time': 60,  # 开发环境更短的超时
            'round_timeout': 30,
            'lock_timeout': 60,
            'max_concurrent_rooms': 10,
            'room_cleanup_interval': 300,  # 5分钟清理
        })
    
    return config


def validate_enhanced_config():
    """验证增强版配置"""
    errors = []
    
    if BattleSystemConfig.MAX_ROOM_PROCESSING_TIME <= 0:
        errors.append("MAX_ROOM_PROCESSING_TIME必须大于0")
    
    if BattleSystemConfig.ROUND_TIMEOUT <= 0:
        errors.append("ROUND_TIMEOUT必须大于0")
    
    if BattleSystemConfig.MAX_CONCURRENT_ROOMS <= 0:
        errors.append("MAX_CONCURRENT_ROOMS必须大于0")
    
    if not (0 <= BattleSystemConfig.ALERT_THRESHOLD_ERROR_RATE <= 1):
        errors.append("ALERT_THRESHOLD_ERROR_RATE必须在0和1之间")
    
    return errors


def is_enhanced_system_enabled():
    """检查是否启用增强版系统"""
    return BattleSystemConfig.ENABLE_ENHANCED_SYSTEM


def get_system_limits():
    """获取系统限制"""
    return {
        'max_concurrent_rooms': BattleSystemConfig.MAX_CONCURRENT_ROOMS,
        'max_rooms_per_user': BattleSystemConfig.MAX_USER_ACTIVE_ROOMS,
        'max_processing_time': BattleSystemConfig.MAX_ROOM_PROCESSING_TIME,
        'round_timeout': BattleSystemConfig.ROUND_TIMEOUT,
        'max_cases_per_room': BattleSystemConfig.MAX_CASES_PER_ROOM,
    }


def get_monitoring_config():
    """获取监控配置"""
    return {
        'metrics_enabled': BattleSystemConfig.METRICS_ENABLED,
        'retention_days': BattleSystemConfig.METRICS_RETENTION_DAYS,
        'alert_error_rate': BattleSystemConfig.ALERT_THRESHOLD_ERROR_RATE,
        'auto_cleanup': BattleSystemConfig.AUTO_CLEANUP_ENABLED,
    }


def get_security_config():
    """获取安全配置"""
    return {
        'max_bet_ratio': float(BattleSystemConfig.MAX_BET_RATIO),
        'rate_limit_enabled': BattleSystemConfig.RATE_LIMIT_ENABLED,
        'anti_cheat_enabled': BattleSystemConfig.ANTI_CHEAT_ENABLED,
        'lock_timeout': BattleSystemConfig.LOCK_TIMEOUT,
        'max_retry_attempts': BattleSystemConfig.MAX_RETRY_ATTEMPTS,
    }


# 配置验证
_config_errors = BattleSystemConfig.validate_config()
if _config_errors:
    import logging
    logger = logging.getLogger(__name__)
    for error in _config_errors:
        logger.error(f"配置错误: {error}")

# 全局配置验证
_config_errors = validate_enhanced_config()
if _config_errors:
    import logging
    logger = logging.getLogger(__name__)
    for error in _config_errors:
        logger.error(f"增强版配置错误: {error}")

# 导出主要配置
__all__ = [
    'BattleSystemConfig',
    'BattleSystemMetrics'
]
