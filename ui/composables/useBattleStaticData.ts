/**
 * 已结束对战的静态数据管理器
 * 用于已结束的对战，避免不必要的WebSocket连接，提升性能
 */
import { ref, readonly } from 'vue'

export interface StaticBattleData {
  uid: string
  state: number
  round_count: number
  bets: any[]
  update_time: string
  create_time: string
  max_joiner: number
  price: number
  // 其他对战相关字段
}

export interface StaticRoundResult {
  round: number
  playerIndex: number
  item: any
  user: any
  isStatic: boolean
}

export const useBattleStaticData = () => {
  const staticBattleData = ref<StaticBattleData | null>(null)
  const isLoading = ref(false)
  const error = ref<Error | null>(null)

  /**
   * 加载已结束对战的静态数据
   */
  const loadStaticBattleData = async (battleId: string): Promise<StaticBattleData> => {
    try {
      isLoading.value = true
      error.value = null
      
      console.log('[🎰STATIC] 加载已结束对战数据:', battleId)
      
      const response = await $fetch<{
        code: number
        message?: string
        body: StaticBattleData
      }>(`/api/box/battle/detail/`, {
        params: { uid: battleId },
        method: 'GET'
      })
      
      if (response.code !== 0) {
        throw new Error(response.message || '获取对战数据失败')
      }
      
      const battleData = response.body
      staticBattleData.value = battleData
      
      // 验证数据完整性
      validateStaticBattleData(battleData)
      
      console.log('[🎰STATIC] 静态数据加载完成:', {
        state: battleData.state,
        rounds: battleData.round_count,
        players: battleData.bets?.length || 0,
        finished: battleData.update_time
      })
      
      return battleData
      
    } catch (err) {
      const errorObj = err instanceof Error ? err : new Error(String(err))
      error.value = errorObj
      console.error('[🎰STATIC] 静态数据加载失败:', errorObj)
      throw errorObj
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 验证静态对战数据的完整性
   */
  const validateStaticBattleData = (data: StaticBattleData) => {
    if (!data) throw new Error('对战数据为空')
    
    // 检查对战状态
    if (![11, 20].includes(data.state)) {
      console.warn('[🎰STATIC] 警告：对战状态不是已结束', data.state)
    }
    
    // 检查参与者数据
    if (!data.bets || data.bets.length === 0) {
      throw new Error('对战参与者数据缺失')
    }
    
    // 检查基本字段
    if (!data.uid) throw new Error('对战ID缺失')
    if (!data.round_count || data.round_count < 1) {
      console.warn('[🎰STATIC] 警告：轮次数量异常', data.round_count)
    }
  }

  /**
   * 处理静态对战的所有轮次结果
   */
  const processStaticResults = (battleData: StaticBattleData): StaticRoundResult[] => {
    console.log('[🎰STATIC] 处理静态结果数据')
    
    const allRoundResults: StaticRoundResult[] = []
    
    battleData.bets?.forEach((bet, playerIndex) => {
      if (bet.open_items && bet.open_items.length > 0) {
        bet.open_items.forEach((item: any, roundIndex: number) => {
          allRoundResults.push({
            round: roundIndex + 1,
            playerIndex,
            item: {
              ...item,
              round: roundIndex + 1,
              item_id: item.item_id || `static_${playerIndex}_${roundIndex}_${Date.now()}`
            },
            user: bet.user,
            isStatic: true // 标记为静态数据
          })
        })
      }
    })
    
    console.log('[🎰STATIC] 处理完成，共', allRoundResults.length, '个结果')
    return allRoundResults
  }

  /**
   * 获取对战最终胜利者信息
   */
  const getFinalWinner = (battleData: StaticBattleData) => {
    if (!battleData.bets) return null
    
    // 查找胜利者
    const winner = battleData.bets.find(bet => bet.victory === true)
    if (!winner) return null
    
    return {
      user: winner.user,
      winAmount: winner.win_amount || 0,
      winItems: winner.win_items || [],
      totalValue: winner.win_items?.reduce((sum: number, item: any) => 
        sum + (item.item_price?.price || 0), 0) || 0
    }
  }

  /**
   * 预加载静态资源（性能优化）
   */
  const preloadStaticAssets = async (battleData: StaticBattleData) => {
    console.log('[🎰STATIC] 开始预加载静态资源')
    
    const imagesToPreload: string[] = []
    
    // 收集需要预加载的图片
    battleData.bets?.forEach(bet => {
      // 用户头像
      if (bet.user?.profile?.avatar) {
        imagesToPreload.push(bet.user.profile.avatar)
      }
      
      // 开箱物品图片
      bet.open_items?.forEach((item: any) => {
        if (item.image) {
          imagesToPreload.push(item.image)
        }
      })
      
      // 胜利物品图片
      bet.win_items?.forEach((item: any) => {
        if (item.image) {
          imagesToPreload.push(item.image)
        }
      })
    })
    
    // 异步预加载图片
    const preloadPromises = imagesToPreload.map(src => {
      return new Promise((resolve) => {
        const img = new Image()
        img.onload = () => resolve(src)
        img.onerror = () => resolve(src) // 即使失败也继续
        img.src = src
      })
    })
    
    try {
      await Promise.all(preloadPromises)
      console.log('[🎰STATIC] 静态资源预加载完成，共', imagesToPreload.length, '个图片')
    } catch (error) {
      console.warn('[🎰STATIC] 部分静态资源预加载失败:', error)
    }
  }

  /**
   * 清理静态数据
   */
  const clearStaticData = () => {
    staticBattleData.value = null
    error.value = null
    console.log('[🎰STATIC] 静态数据已清理')
  }

  return {
    // 状态
    staticBattleData: readonly(staticBattleData),
    isLoading: readonly(isLoading),
    error: readonly(error),
    
    // 方法
    loadStaticBattleData,
    processStaticResults,
    getFinalWinner,
    preloadStaticAssets,
    validateStaticBattleData,
    clearStaticData
  }
}
