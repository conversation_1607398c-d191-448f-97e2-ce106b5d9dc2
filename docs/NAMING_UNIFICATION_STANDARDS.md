# 🏷️ 命名统一规范

## 📋 概述

本文档定义了CS:GO皮肤交易平台的命名统一标准，规范后续开发中的命名使用，确保新代码的一致性和可维护性。

## 🎯 核心问题

### 1. 箱子命名混用
- **现状**: 后端存在 `box` 和 `case` 两种命名
- **前端标准**: 已统一使用 `case`
- **规范**: 新开发统一使用 `case`

### 2. 饰品命名混用
- **现状**: 后端存在 `skin` 和 `item` 两种命名
- **前端标准**: 已统一使用 `skin`
- **规范**: 新开发统一使用 `skin`

## ⚠️ 重要说明

**本规范仅适用于新开发的代码，现有代码暂不修改，避免大规模重构风险。**

## 📊 当前命名使用情况分析

### 箱子相关命名现状

#### ✅ 已统一使用 `case` 的地方
```python
# 模型名称
class Case(ModelBase):  # server/box/models.py
class CaseRoom(ModelBase):  # server/box/models.py
class CaseRecord(ModelBase):  # server/box/models.py

# 字段名称
case_key = models.CharField()  # 箱子标识
case_type = models.ForeignKey()  # 箱子类型
case_category = models.ForeignKey()  # 箱子分类

# API端点
/api/box/search/  # 搜索箱子
/api/box/detail/  # 箱子详情
/api/box/battle/case/  # 对战箱子列表
```

#### ⚠️ 仍使用 `box` 的地方
```python
# 业务函数名
def get_box_skin_list(case_key, fields):  # server/box/business.py
def get_hot_box_list():  # 热门箱子列表
def get_discount_box_list():  # 折扣箱子列表

# API端点
/api/box/skins/  # 获取箱子皮肤列表
/api/box/records/  # 箱子开启记录

# 缓存键名
cache_key = f'box_skin_list_{case_key}'  # 箱子皮肤列表缓存
```

### 饰品相关命名现状

#### ✅ 已统一使用 `skin` 的地方
```typescript
// 前端组件
SkinCard.vue  // ui/components/skin/SkinCard.vue
getSkinName()  // 获取皮肤名称函数
getSkinFullName()  // 获取完整皮肤名称

// 前端类型定义
interface SkinItem {  // ui/types/skin.ts
  id: string
  name: string
  // ...
}
```

#### ⚠️ 仍使用 `item` 的地方
```python
# 模型名称
class ItemInfo(ModelBase):  # package/models.py
class MarketItem(ModelBase):  # market/models.py

# 字段名称
item_info = models.ForeignKey()  # 物品信息
item_price = models.ForeignKey()  # 物品价格
item_category = models.ForeignKey()  # 物品分类

# API字段
"item_info": {...}  # API响应中的物品信息
"item_price": {...}  # 物品价格信息
```

## 🎯 统一目标和原则

### 命名统一目标
1. **箱子**: 统一使用 `case`
2. **饰品**: 统一使用 `skin`
3. **保持向后兼容**: 渐进式重构，避免破坏性变更

### 统一原则
1. **前端优先**: 以前端已统一的命名为准
2. **渐进式**: 分阶段逐步统一，避免大规模重构
3. **向后兼容**: 保留旧接口，新接口使用统一命名
4. **文档先行**: 新开发严格按照统一命名

## 📋 新开发命名规范

### 🎯 统一原则

1. **前端对齐**: 与前端已统一的命名保持一致
2. **新代码规范**: 所有新开发严格按照统一命名
3. **现有代码保持**: 不修改现有代码，避免破坏性变更
4. **文档先行**: 新功能开发前明确命名规范

### ✅ 新代码命名标准

#### 箱子相关 - 统一使用 `case`
```python
# 函数命名
def get_case_list():
def get_case_details(case_key):
def open_case(case_key):
def get_case_skins(case_key):

# 变量命名
case_data = get_case_info()
case_list = fetch_cases()
case_key = "ak47_redline"

# 类命名 (如需新增)
class CaseService:
class CaseManager:
```

#### 饰品相关 - 统一使用 `skin`
```python
# 函数命名
def get_skin_list():
def get_skin_details(skin_id):
def calculate_skin_price(skin_id):
def get_skin_rarity(skin_id):

# 变量命名
skin_data = get_skin_info()
skin_list = fetch_skins()
skin_price = calculate_price()

# 类命名 (如需新增)
class SkinService:
class SkinManager:
```

#### API端点命名
```python
# 箱子相关API
/api/case/list/          # 箱子列表
/api/case/detail/        # 箱子详情
/api/case/skins/         # 箱子内皮肤
/api/case/open/          # 开箱

# 皮肤相关API
/api/skin/list/          # 皮肤列表
/api/skin/detail/        # 皮肤详情
/api/skin/price/         # 皮肤价格
/api/skin/market/        # 皮肤市场
```

## 🔧 开发检查工具

### 命名规范检查脚本
```bash
# 检查新代码是否遵循命名规范
python3 scripts/tools/check_naming_consistency.py
```

### 代码审查检查点
1. **函数命名**: 新函数是否使用 case/skin 命名
2. **变量命名**: 新变量是否避免 box/item 混用
3. **API设计**: 新端点是否遵循 /api/case/ 或 /api/skin/ 规范
4. **注释文档**: 是否使用统一的术语

## 📖 新开发规范指南

### ✅ 正确命名示例
```python
# 箱子相关 - 新开发使用
def get_case_list():
    """获取箱子列表"""
    pass

def open_case(case_key):
    """开启箱子"""
    pass

case_data = get_case_details(case_key)
case_skins = get_case_skin_list(case_key)

# 饰品相关 - 新开发使用
def get_skin_info(skin_id):
    """获取皮肤信息"""
    pass

def calculate_skin_price(skin_id):
    """计算皮肤价格"""
    pass

skin_price = calculate_skin_price(skin_id)
skin_rarity = get_skin_rarity(skin_id)
```

### ❌ 新开发中避免的命名
```python
# 新代码中避免使用这些命名
def get_box_list():      # ❌ 新代码应使用 get_case_list()
def get_item_info():     # ❌ 新代码应使用 get_skin_info()

box_data = get_data()    # ❌ 新代码应使用 case_data
item_price = get_price() # ❌ 新代码应使用 skin_price
```

### 🔄 与现有代码的兼容处理
```python
# 新函数可以内部调用现有函数，保持兼容
def get_case_skin_list(case_key):
    """新的统一命名函数"""
    # 内部调用现有的函数，保持功能不变
    return get_box_skin_list(case_key)

def get_skin_details(skin_id):
    """新的统一命名函数"""
    # 内部调用现有的ItemInfo相关函数
    return get_item_info(skin_id)
```

## 📝 开发流程建议

### 新功能开发流程
1. **设计阶段**: 确认使用 case/skin 命名
2. **编码阶段**: 严格按照命名规范
3. **代码审查**: 检查命名一致性
4. **文档更新**: 使用统一术语

### 团队协作规范
- 新成员培训时强调命名规范
- 代码审查时重点检查命名
- 定期回顾和更新规范文档
- 在开发工具中添加命名检查

---

**📌 核心原则**:
- **新开发严格遵循**: case/skin 统一命名
- **现有代码保持不变**: 避免破坏性修改
- **兼容性优先**: 新旧代码和谐共存
- **渐进式改进**: 随着业务发展自然统一
