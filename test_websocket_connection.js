#!/usr/bin/env node

/**
 * WebSocket连接测试脚本
 * 测试4000端口的WebSocket服务器连接
 */

const { io } = require('socket.io-client');

console.log('🔧 WebSocket连接测试开始...');

// 测试配置
const configs = [
  {
    name: '本地4000端口',
    url: 'http://localhost:4000',
    options: {
      transports: ['polling', 'websocket'],
      forceNew: true,
      reconnection: true,
      reconnectionAttempts: 3,
      timeout: 10000,
      autoConnect: true,
      path: '/socket.io/',
      query: {
        page: 'ws',
        EIO: '4'
      }
    }
  },
  {
    name: '服务器4000端口',
    url: 'http://************:4000',
    options: {
      transports: ['polling', 'websocket'],
      forceNew: true,
      reconnection: true,
      reconnectionAttempts: 3,
      timeout: 10000,
      autoConnect: true,
      path: '/socket.io/',
      query: {
        page: 'ws',
        EIO: '4'
      }
    }
  }
];

async function testConnection(config) {
  return new Promise((resolve) => {
    console.log(`\n🔌 测试连接: ${config.name} (${config.url})`);
    
    const socket = io(config.url, config.options);
    
    const timeout = setTimeout(() => {
      console.log(`❌ ${config.name}: 连接超时`);
      socket.disconnect();
      resolve({ success: false, error: '连接超时' });
    }, 15000);
    
    socket.on('connect', () => {
      clearTimeout(timeout);
      console.log(`✅ ${config.name}: 连接成功`);
      console.log(`   Socket ID: ${socket.id}`);
      console.log(`   传输方式: ${socket.io.engine.transport.name}`);
      
      // 测试加入房间
      socket.emit('join', 'ws_channel');
      console.log(`📤 ${config.name}: 发送join消息`);
      
      // 测试监控消息
      socket.emit('monitor', ['get_stats']);
      console.log(`📤 ${config.name}: 发送监控消息`);
      
      setTimeout(() => {
        socket.disconnect();
        resolve({ success: true });
      }, 3000);
    });
    
    socket.on('connect_error', (error) => {
      clearTimeout(timeout);
      console.log(`❌ ${config.name}: 连接错误 - ${error.message}`);
      resolve({ success: false, error: error.message });
    });
    
    socket.on('disconnect', (reason) => {
      console.log(`🔌 ${config.name}: 连接断开 - ${reason}`);
    });
    
    socket.on('message', (data) => {
      console.log(`📨 ${config.name}: 收到消息 -`, data);
    });
    
    // 监听新的消息格式
    socket.on('monitor', (data) => {
      console.log(`📊 ${config.name}: 收到监控数据 -`, data);
    });
    
    socket.on('case_records', (data) => {
      console.log(`📦 ${config.name}: 收到开箱记录 -`, data);
    });
  });
}

async function runTests() {
  console.log('🚀 开始WebSocket连接测试...\n');
  
  const results = [];
  
  for (const config of configs) {
    const result = await testConnection(config);
    results.push({ name: config.name, ...result });
  }
  
  console.log('\n📊 测试结果总结:');
  console.log('=' * 50);
  
  let successCount = 0;
  for (const result of results) {
    if (result.success) {
      console.log(`✅ ${result.name}: 成功`);
      successCount++;
    } else {
      console.log(`❌ ${result.name}: 失败 - ${result.error}`);
    }
  }
  
  console.log(`\n🎯 成功率: ${successCount}/${results.length} (${(successCount/results.length*100).toFixed(1)}%)`);
  
  if (successCount > 0) {
    console.log('\n🎉 WebSocket服务器可以正常连接！');
    console.log('💡 建议前端使用以下配置:');
    
    const successfulConfig = configs.find((_, index) => results[index].success);
    if (successfulConfig) {
      console.log(`   URL: ${successfulConfig.url}`);
      console.log(`   传输方式: ['polling', 'websocket']`);
      console.log(`   路径: /socket.io/`);
    }
  } else {
    console.log('\n❌ 所有连接测试都失败了');
    console.log('💡 建议检查:');
    console.log('   1. WebSocket服务器是否正在运行');
    console.log('   2. 端口4000是否开放');
    console.log('   3. 防火墙设置');
  }
  
  process.exit(successCount > 0 ? 0 : 1);
}

// 运行测试
runTests().catch(console.error);
