# Generated by Django 3.2.25 on 2025-07-18 08:47

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('package', '0023_auto_20250718_1628'),
        ('lottery', '0009_auto_20250427_2200'),
    ]

    operations = [
        migrations.AlterField(
            model_name='lotteryinfosetting',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='lotteryjoiner',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='lotteryjoiner',
            name='item_info',
            field=models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='lottery_joiner', to='package.iteminfo', verbose_name='item info'),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name='lotterysetting',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
    ]
