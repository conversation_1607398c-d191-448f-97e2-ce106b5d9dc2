# Articles App - 通用文章和公告管理系统

## 概述

Articles App是一个功能强大的内容管理系统，专为CSGO皮肤交易平台设计，用于替代原有sitecfg中的Article和Announce模型。它提供了统一的文章和公告管理功能，支持多种内容类型、分类管理、标签系统等。

## 功能特性

### 🎯 核心功能
- **统一内容管理**: 支持文章、公告、新闻、指南、FAQ等多种内容类型
- **分类系统**: 灵活的分类管理，支持图标和颜色自定义
- **标签系统**: 多标签支持，便于内容分类和检索
- **状态管理**: 草稿、已发布、已归档、定时发布等状态
- **优先级管理**: 低、普通、高、紧急四个优先级
- **SEO优化**: 内置SEO标题、描述、关键词字段

### 📱 显示功能
- **推荐内容**: 支持设置推荐和置顶内容
- **浏览统计**: 自动统计浏览量，防重复计数
- **时间管理**: 支持定时发布和过期时间设置
- **样式自定义**: 支持自定义CSS类和颜色
- **附件管理**: 支持文件附件上传和下载统计

### 🔧 管理功能
- **富文本编辑**: 支持HTML内容编辑
- **批量操作**: 支持批量发布、设为推荐等操作
- **权限管理**: 作者和编辑者分离
- **搜索过滤**: 强大的搜索和过滤功能

## 数据模型

### ContentCategory (内容分类)
- `name`: 分类名称
- `slug`: URL友好的标识符
- `description`: 分类描述
- `icon`: 图标类名
- `color`: 分类颜色
- `sort_order`: 排序权重
- `is_active`: 是否启用

### ContentTag (内容标签)
- `name`: 标签名称
- `slug`: URL友好的标识符
- `color`: 标签颜色
- `is_active`: 是否启用

### Content (内容)
- **基本信息**: 标题、副标题、摘要、内容
- **分类标签**: 内容类型、分类、标签
- **状态管理**: 状态、优先级、推荐、置顶
- **时间管理**: 发布时间、过期时间
- **作者信息**: 作者、编辑者
- **媒体文件**: 特色图片、缩略图
- **SEO设置**: SEO标题、描述、关键词
- **显示设置**: 排序、浏览量、评论开关
- **样式设置**: CSS类、背景色、文字色

### ContentAttachment (内容附件)
- `content`: 关联内容
- `name`: 文件名
- `file_path`: 文件路径
- `file_size`: 文件大小
- `file_type`: 文件类型
- `download_count`: 下载次数

### ContentView (浏览记录)
- `content`: 关联内容
- `user`: 用户（可选）
- `ip_address`: IP地址
- `user_agent`: 用户代理
- `viewed_at`: 浏览时间

## API接口

### 基础接口
- `GET /api/articles/categories/` - 获取分类列表
- `GET /api/articles/tags/` - 获取标签列表
- `GET /api/articles/contents/` - 获取内容列表
- `GET /api/articles/contents/{slug}/` - 获取内容详情

### 公告接口
- `GET /api/articles/announcements/` - 获取公告列表
- `GET /api/articles/announcements/homepage/` - 获取首页公告

### 特殊接口
- `GET /api/articles/featured/` - 获取推荐内容
- `GET /api/articles/category/{slug}/` - 按分类获取内容
- `GET /api/articles/tag/{slug}/` - 按标签获取内容

### 查询参数
- `content_type`: 内容类型过滤
- `category`: 分类过滤
- `tags`: 标签过滤
- `priority`: 优先级过滤
- `is_featured`: 是否推荐
- `is_pinned`: 是否置顶
- `search`: 搜索关键词
- `page`: 页码
- `page_size`: 每页数量

## 安装和配置

### 1. 添加到INSTALLED_APPS
```python
INSTALLED_APPS = [
    # ...
    'articles',
    # ...
]
```

### 2. 配置URL
```python
urlpatterns = [
    # ...
    url(r'^api/articles/', include('articles.urls', namespace='articles')),
    # ...
]
```

### 3. 运行迁移
```bash
python manage.py makemigrations articles
python manage.py migrate articles
```

### 4. 创建超级用户（如果需要）
```bash
python manage.py createsuperuser
```

## 数据迁移

### 从sitecfg迁移数据
```bash
# 迁移现有的Article和Announce数据
python scripts/migrate_sitecfg_to_articles.py

# 创建示例数据
python scripts/create_sample_articles.py
```

### 清理旧模型（可选）
```bash
# 清理sitecfg中的旧模型
python scripts/cleanup_sitecfg_models.py
```

## 使用示例

### 前端调用示例

#### 获取首页公告
```javascript
fetch('/api/articles/announcements/homepage/')
  .then(response => response.json())
  .then(data => {
    console.log('首页公告:', data);
  });
```

#### 获取文章列表
```javascript
fetch('/api/articles/contents/?content_type=article&page=1&page_size=10')
  .then(response => response.json())
  .then(data => {
    console.log('文章列表:', data.results);
  });
```

#### 搜索内容
```javascript
fetch('/api/articles/contents/?search=CSGO&is_featured=true')
  .then(response => response.json())
  .then(data => {
    console.log('搜索结果:', data.results);
  });
```

### 管理后台使用

1. 访问 `/admin/articles/` 进入文章管理
2. 创建分类和标签
3. 添加文章或公告
4. 设置发布状态和优先级
5. 配置SEO信息

## 最佳实践

### 内容创建
1. **标题优化**: 使用清晰、描述性的标题
2. **摘要编写**: 提供简洁的内容摘要
3. **分类选择**: 选择合适的分类和标签
4. **SEO设置**: 填写SEO标题和描述

### 公告管理
1. **优先级设置**: 重要公告设为高优先级
2. **置顶使用**: 谨慎使用置顶功能
3. **过期时间**: 为临时公告设置过期时间
4. **样式统一**: 保持公告样式的一致性

### 性能优化
1. **图片优化**: 使用适当大小的特色图片
2. **内容长度**: 控制单篇内容的长度
3. **标签使用**: 合理使用标签，避免过多
4. **缓存策略**: 在前端实现适当的缓存

## 故障排除

### 常见问题

1. **迁移失败**: 检查数据库连接和权限
2. **URL不工作**: 确认URL配置正确
3. **编码问题**: 确保数据库使用UTF-8编码
4. **权限错误**: 检查用户权限设置

### 日志查看
```bash
# 查看Django日志
tail -f /path/to/django.log

# 查看数据库日志
tail -f /path/to/mysql.log
```

## 版本历史

- **v1.0.0** (2024-01-19): 初始版本，基础功能实现
- 支持文章和公告管理
- 分类和标签系统
- API接口完整实现
- 数据迁移脚本

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 许可证

本项目采用MIT许可证。
