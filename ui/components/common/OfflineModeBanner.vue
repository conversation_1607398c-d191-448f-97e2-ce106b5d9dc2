<!-- components/common/OfflineModeBanner.vue -->
<template>
  <Transition name="banner">
    <div v-if="shouldShowBanner" class="offline-banner">
      <div class="banner-content">
        <div class="banner-icon">
          <i class="fas fa-wifi-slash"></i>
        </div>
        <div class="banner-text">
          <h4 class="banner-title">{{ $t('ui.offlineMode.title') }}</h4>
          <p class="banner-description">{{ $t('ui.offlineMode.description') }}</p>
        </div>
        <div class="banner-actions">
          <button 
            @click="handleReconnect"
            class="reconnect-btn"
            :disabled="isReconnecting"
          >
            <i v-if="isReconnecting" class="fas fa-spinner fa-spin"></i>
            <i v-else class="fas fa-redo"></i>
            {{ isReconnecting ? $t('ui.offlineMode.reconnecting') : $t('ui.offlineMode.reconnect') }}
          </button>
          <button @click="dismissBanner" class="dismiss-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useSocketStore } from '@/stores/socket'
import { useI18n } from 'vue-i18n'

const socketStore = useSocketStore()
const { t } = useI18n()

const isReconnecting = ref(false)
const isDismissed = ref(false)

// 计算是否应该显示横幅
const shouldShowBanner = computed(() => {
  return socketStore.offlineMode && 
         !socketStore.isConnected && 
         !isDismissed.value
})

// 处理重新连接
const handleReconnect = async () => {
  if (isReconnecting.value) return
  
  isReconnecting.value = true
  try {
    // 重置重连计数
    socketStore.resetReconnectAttempts()
    
    // 尝试重新初始化Socket连接
    await socketStore.reconnect()
    
    // 如果还是无法连接，刷新页面
    if (!socketStore.isConnected && process.client) {
      setTimeout(() => {
        window.location.reload()
      }, 1000)
    }
  } catch (error) {
    console.error('Reconnection failed:', error)
    
    // 如果重连失败，刷新页面
    if (process.client) {
      window.location.reload()
    }
  } finally {
    setTimeout(() => {
      isReconnecting.value = false
    }, 2000)
  }
}

// 关闭横幅
const dismissBanner = () => {
  isDismissed.value = true
}
</script>

<style lang="scss" scoped>
.offline-banner {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
}

.banner-content {
  display: flex;
  align-items: center;
  gap: 1rem;
  max-width: 1200px;
  margin: 0 auto;
  color: #1f2937;
}

.banner-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background: rgba(31, 41, 55, 0.1);
  border-radius: 50%;
  flex-shrink: 0;
  
  i {
    font-size: 1.125rem;
    color: #1f2937;
  }
}

.banner-text {
  flex: 1;
  min-width: 0;
}

.banner-title {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  color: #1f2937;
}

.banner-description {
  font-size: 0.875rem;
  margin: 0;
  color: #374151;
  opacity: 0.9;
}

.banner-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-shrink: 0;
}

.reconnect-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #1f2937;
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover:not(:disabled) {
    background: #374151;
    transform: translateY(-1px);
  }
  
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
  
  i {
    font-size: 0.75rem;
  }
}

.dismiss-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background: rgba(31, 41, 55, 0.1);
  color: #1f2937;
  border: none;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(31, 41, 55, 0.2);
  }
  
  i {
    font-size: 0.875rem;
  }
}

// 动画
.banner-enter-active,
.banner-leave-active {
  transition: all 0.4s ease;
}

.banner-enter-from {
  opacity: 0;
  transform: translateY(-100%);
}

.banner-leave-to {
  opacity: 0;
  transform: translateY(-100%);
}

// 响应式设计
@media (max-width: 768px) {
  .offline-banner {
    padding: 0.5rem 0.75rem;
  }
  
  .banner-content {
    gap: 0.75rem;
  }
  
  .banner-icon {
    width: 2rem;
    height: 2rem;
    
    i {
      font-size: 1rem;
    }
  }
  
  .banner-title {
    font-size: 0.875rem;
  }
  
  .banner-description {
    font-size: 0.75rem;
  }
  
  .reconnect-btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
    
    span {
      display: none;
    }
  }
  
  .dismiss-btn {
    width: 1.75rem;
    height: 1.75rem;
    
    i {
      font-size: 0.75rem;
    }
  }
}

@media (max-width: 480px) {
  .banner-text {
    min-width: 0;
  }
  
  .banner-description {
    display: none;
  }
  
  .reconnect-btn {
    span {
      display: none;
    }
  }
}
</style>
