import string
import random
from uuid import uuid1

from django.contrib.auth.models import PermissionsMixin, User
from django.utils import timezone
from django.db import models, transaction
from django.utils.translation import gettext_lazy as _

from box.models import Case
from steambase.models import ModelBase, USER_MODEL


def ref_generator(size=6, chars=string.ascii_uppercase):
    return ''.join(random.choice(chars) for _ in range(size))


class UserPromotion(models.Model):
    user = models.OneToOneField(USER_MODEL, on_delete=models.CASCADE, verbose_name=_('user'), related_name='promotion')
    code = models.CharField(_('refer code'), max_length=32, default=ref_generator)
    count = models.IntegerField(_("refer count"), default=0)
    total_charge = models.FloatField(_('total charge'), default=0.0)
    total_profit = models.FloatField(_('total profit'), default=0.0)
    profit = models.FloatField(_('profit'), default=0.0)
    custom = models.BooleanField(_('enable custom'), default=False)
    level = models.IntegerField(_("refer level"), default=0)
    profit_rate = models.FloatField(_('profit rate(%)'), default=0)

    class Meta:
        verbose_name = _('User Promotion')
        verbose_name_plural = _('User Promotion')

    def __str__(self):
        return self.user.username


class PromotionRecord(ModelBase):
    ref = models.ForeignKey(UserPromotion, on_delete=models.CASCADE, verbose_name=_("refer"), related_name='records')
    user = models.OneToOneField(USER_MODEL, on_delete=models.CASCADE, verbose_name=_("user"), related_name='promotion_records')
    total_charge = models.FloatField(_('total charge'), default=0.0)
    profit = models.FloatField(_('refer profit'), default=0.0)
    active = models.BooleanField(_("active"), default=False)

    class Meta:
        ordering = ('-create_time',)
        verbose_name = _('Promotion Record')
        verbose_name_plural = _('Promotion Record')

    def __str__(self):
        return self.uid


class PromotionLevelConfig(models.Model):
    # PROMOTION_LEVEL_TYPE = (
    #     (1, _('Base by refer count')),
    #     (2, _('Base by refer amount'))
    # )
    # type = models.SmallIntegerField(_('type'), default=1, choices=PROMOTION_LEVEL_TYPE)
    level = models.IntegerField(_('level'), default=0)
    profit_rate = models.FloatField(_('profit rate(%)'), default=0)
    refer_rate = models.FloatField(_('refer rate(%)'), default=0)
    # min_count = models.IntegerField(_('min count'), default=0)
    # max_count = models.IntegerField(_('max count'), default=0)
    min_amount = models.FloatField(_('min amount'), default=0)
    max_amount = models.FloatField(_('max amount'), default=0)

    class Meta:
        ordering = ('level',)
        verbose_name = _('Promotion Level Config')
        verbose_name_plural = _('Promotion Level Config')

    def __str__(self):
        return str(self.level)


class PromotionCaseConfig(models.Model):
    level = models.ForeignKey(PromotionLevelConfig, on_delete=models.CASCADE, related_name="promotion_level")
    case = models.ForeignKey(Case, on_delete=models.CASCADE, verbose_name=_('case'), related_name='promotion_case')

    class Meta:
        ordering = ('level',)
        verbose_name = _('PromotionCaseConfig')
        verbose_name_plural = _('PromotionCaseConfig')

    def __str__(self):
        return str(self.level)