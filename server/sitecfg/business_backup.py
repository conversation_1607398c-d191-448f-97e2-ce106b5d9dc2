import logging
import threading
import random

from django.contrib.auth import get_user_model

from django.db import transaction
from django.core.paginator import Paginator
from django.core.cache import cache

from django.db.models import Q


from steambase.enums import RespCode


from authentication.interfaces import set_user_box_chance
from package.interfaces import set_all_item_price
from sitecfg.models import Support, Footer, FAQ, Banner, SEO, SiteConfig
# Announcee, Article, ArticleCategoryy 已迁移到 articles 应用
from sitecfg.serializers import SupportSerializer, FooterSerializer, BannerSerializer, SupportCacheSerializer
# AnnounceeSerializer, ArticleSerializer 已迁移到 articles 应用
from sitecfg.interfaces import get_roll_join_min_charge_limit
from package.models import ItemInfo

_logger = logging.getLogger(__name__)

USER = get_user_model()


# get_announce 和 get_announce_list 函数已迁移到 articles 应用
# def get_announce(id, fields):
#     anno = Announcee.objects.filter(enable=True, id=id).first()
#     if not anno:
#         return RespCode.InvalidParams.value, ('没有找到公告')

#     anno_data = AnnounceeSerializer(anno, fields=fields).data
#     resp = {
#         'items': anno_data
#     }
#     return RespCode.Succeed.value, resp

# def get_announce_list(page, page_size, order, fields):
#     anno = Announcee.objects.filter(enable=True).order_by(order)
#     paginator = Paginator(anno, page_size)
#     package = paginator.page(page)
#     package_data = AnnounceeSerializer(package, many=True, fields=fields).data
#     resp = {
#         'items': package_data,
#         'total': paginator.count,
#         'page': page,
#         'limit': page_size
#     }
#     return RespCode.Succeed.value, resp


def get_support(uid, fields_list, fields_detail):
    if uid:
        support = Support.objects.filter(uid=uid, enable=True).order_by('sort')
        if not support.exists():            
            return RespCode.InvalidParams.value, ('没有找到相关内容')
        support_data = SupportSerializer(support, many=True, fields=fields_detail).data
    else:
        support = Support.objects.filter(enable=True).order_by('sort')
        support_data = SupportSerializer(support, many=True, fields=fields_list).data
    resp = { 
        'items': support_data
    }
    return RespCode.Succeed.value, resp


def get_footer():
    footer = Footer.objects.filter(enable=True).order_by('no')
    footer_data = FooterSerializer(footer, many=True).data
    return RespCode.Succeed.value, footer_data


def get_faq(uid, fields):
    if uid:
        queryset = FAQ.objects.filter(uid=uid, enable=True).order_by('sort')
    else:
        queryset = FAQ.objects.filter(enable=True).order_by('sort')  
    if not queryset.exists():
        return RespCode.InvalidParams.value, ('没有找到相关内容')    
    faq_data = SupportSerializer(queryset, many=True, fields=fields).data    
    resp = {
        'items': faq_data
    }
    return RespCode.Succeed.value, resp


def get_sitecfg():
    resp = {
        'roll_join_min_charge': get_roll_join_min_charge_limit()
    }
    return RespCode.Succeed.value, resp


def setup_set_item_price_worker():
    th = threading.Thread(target=set_all_item_price, args=())
    th.start()


def setup_set_user_box_chance_worker(value):
    th = threading.Thread(target=set_user_box_chance, args=(value,))
    th.start()

def setup_update_base_count_worker():
    th = threading.Thread(target=update_base_count_worker, args=())
    th.start()

def update_base_count_worker():
     
    #keys_to_update = ['open_base_count', 'users_base_count']
    _logger.info('------------------------ 每小时随机时间更新基础数据 ------------------------')

    keys_to_update = ['users_base_count']


    for key in keys_to_update:
        try:
            # 查找具有指定键的 SiteConfig 对象
            config = SiteConfig.objects.get(key=key)
            # 获取当前值并增加 1 到 10 之间的随机数
            current_value = int(config.value)
            #new_value = current_value + count
            new_value = current_value + random.randint(1, 10)
            # 更新值
            config.value = str(new_value)
            config.save()
        except SiteConfig.DoesNotExist:
            # 处理键不存在的情况
            pass


def get_banner():
    # 缓存
    cache_key = 'banner'
    cached_data = cache.get(cache_key)
    # if cached_data is not None:
    #     return RespCode.Succeed.value, cached_data

    banners = Banner.objects.filter(enable=True).order_by("order")
    fields = ("title", 'title_en', 'title_zh_hans', "link", "image", "type", "is_simple", "description", 'description_en', 'description_zh_hans', "background_class", "glow_class", "primary_button_text", "primary_button_text_en", "primary_button_text_zh_hans", "primary_button_link", "secondary_button_text", "secondary_button_text_en", "secondary_button_text_zh_hans", "secondary_button_link", 'order')      

    respData = BannerSerializer(banners, fields=fields, many=True).data
    resp = {
        'items': respData
    }
    cache.set(cache_key, resp, timeout = 60 * 60)
    
    return RespCode.Succeed.value, resp


# 以下文章相关函数已迁移到 articles 应用
def get_article_list_DEPRECATED(query, fields, condition, default_image_url, page=1, page_size=10):
    # 用于存储分类ID的列表
    category_ids = []

    # 遍历分类别名列表
    for category_name_en in condition:
        # 查询数据库以获取对应分类别名的分类ID
        categories = ArticleCategoryy.objects.filter(name_en=category_name_en)
        if categories.exists():
            category_ids.extend([category.id for category in categories])
        else:
        # 如果分类不存在，可以根据实际需求处理，比如跳过或返回错误信息
          continue


    # 构造文章查询条件
    query_conditions = Q(**query)
    # 应用分类ID筛选条件
    if category_ids:
        query_conditions &= Q(category__in=category_ids)

    # 使用分页器来获取分页数据
    articles = Article.objects.filter(query_conditions, enable=True).order_by('sort_order')
    paginator = Paginator(articles, page_size)
    page_data = paginator.page(page)

    # 创建一个空的文章列表
    articles_data = []


    # 遍历分页数据中的文章
    for article in page_data:
        article_data = {}
        for field in fields:
            if field == 'image':
                # 如果图像字段存在并且不为空，则使用其URL，否则使用默认图像URL
                article_data[field] = article.image.url if hasattr(article, 'image') and article.image else default_image_url
            else:
                article_data[field] = getattr(article, field, None)
        articles_data.append(article_data)

    # 构建响应
    resp = {
        'items_data': articles_data,
        'total': paginator.count,
        'page': page,
        'limit': page_size
    }
    return RespCode.Succeed.value, resp

def article_cate_sorted_queryset(query):
    return ArticleCategoryy.objects.filter(enable=True, **query).order_by('sort_order')
def article_sorted_queryset(query):
    # 使用 filter 和排序来创建排序后的 queryset
    return Article.objects.filter(enable=True, **query).order_by('sort_order')



def get_article_weapon(query, fields, condition):      
    # 定义缓存
    cache_key = 'article_weapon'
    # 获取缓存
    cached_data = cache.get(cache_key)
    if cached_data is not None:
        return RespCode.Succeed.value, cached_data  
    # 获取文章分类并排序
    cates = article_cate_sorted_queryset(query).filter(condition, enable=True).order_by('sort_order')    
    # 创建一个空的文章列表
    articles = []
     # 初始化匹配数量列表
    matching_counts = []
    # 遍历每个分类
    for cate in cates:
        # 获取该分类下的文章并排序
        articles_in_cate = article_sorted_queryset({'category': cate.id, **query})        
        # 将文章信息添加到列表
        articles_data = []
        for article in articles_in_cate:
            tag = article.tag  # 假设 tag 存在于每篇文章的 tag 字段中
            matching_count = ItemInfo.objects.filter(name_cn__icontains=tag).count()
            # 根据需要选择要返回的字段
            article_data = {
                'id': article.id,
                'title': article.title,
                'tag': article.tag,
                'number': matching_count,
                # 添加其他字段
            }
            articles_data.append(article_data)        
        # 将分类及其文章添加到最终的文章列表中
        category_data = {
            'id': cate.id,
            'name': cate.name,
            'name_en': cate.name_en,
            'articles': articles_data,
        }
        articles.append(category_data)
    # 构建响应
    resp = {
        'items_data': articles,
    }
    # 将查询结果缓存24小时，以减少对数据库的访问
    cache.set(cache_key, resp, timeout = 60 * 60 * 24)  
    # 返回成功响应
    return RespCode.Succeed.value, resp

def get_article_detail(query):
    if not Article.objects.filter(**query).exists():
        return RespCode.InvalidParams.value, ('没有这篇文章')

    item = Article.objects.filter(**query).first()
    resp = {}
    if item:
        item_data = ArticleSerializer(item).data
        
        resp['item'] = item_data        
    
        field = ('id', 'title')
        next= next_article(item_data['id'])
        pre= pre_article(item_data['id'])
        resp['next'] = ArticleSerializer(next, fields=field).data
        resp['pre'] = ArticleSerializer(pre, fields=field).data
        
    
    return RespCode.Succeed.value, resp
    
# 下一篇    
    
def next_article(id):
    return Article.objects.filter(id__gt=id).order_by('id').first()

# 上一篇

def pre_article(id):
    return Article.objects.filter(id__lt=id).order_by('-id').first()

DEFAULT_SEO_DATA = {
    'url': "",
    'name': "CSGOSKINS开箱网",
    'subtitle': "CSGO开箱网中国站",
    'title': "CSGO开箱网站,CS2开箱网站",
    'keywords': "CSGO开箱网站,CS2开箱网站,CSGOSKINS开箱网",
    'description': "CSGOSKINS开箱网致力于打造中国良心CSGO开箱网站,主要提供CSGO饰品开箱、CSGO饰品开箱对战,会员首次注册送CSGO饰品开箱金币,还有免费红包、充值红包和免费ROLL房福利。CSGOSKINS开箱网爆率高、秒取回、7X24小时即开即取,备受CSGO游戏主播推荐和CSGO游戏饰品爱好者好评.",
    'icp': "",
    'news_enable': False
}

def get_seo(url):
    # 确保 url 是字符串
    if not isinstance(url, str) or url is None:
        url = "www.csgoskins.com.cn"
    
    # 缓存
    cache_key = 'seo_' + url
    cached_data = cache.get(cache_key)
    if cached_data is not None:
        return RespCode.Succeed.value, cached_data
    
    seo = SEO.objects.filter(url=url).first()
    if seo:
        resp = {
            'url': seo.url,
            'name': seo.name,
            'subtitle': seo.subtitle,
            'title': seo.title,
            'keywords': seo.keywords,
            'description': seo.description,
            'icp': seo.icp,
            'news_enable': seo.news_enable
        }
    else:
        resp = DEFAULT_SEO_DATA
    
    cache.set(cache_key, resp, timeout=60 * 60 * 24)  # 缓存一天
    
    return RespCode.Succeed.value, resp



