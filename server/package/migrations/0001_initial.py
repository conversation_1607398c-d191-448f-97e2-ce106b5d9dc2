# -*- coding: utf-8 -*-
# Generated by Django 1.11.13 on 2021-04-14 02:09
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import steambase.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODELL),
    ]

    operations = [
        migrations.CreateModel(
            name='BlackList',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('enable', models.BooleanField(default=True, verbose_name='enable')),
                ('ban_deposit', models.BooleanField(default=False, verbose_name='ban deposit')),
                ('ban_withdraw', models.BooleanField(default=False, verbose_name='ban withdraw')),
                ('ban_exchange', models.BooleanField(default=False, verbose_name='ban exchange')),
                ('ban_shop', models.BooleanField(default=False, verbose_name='ban shop')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='blacklist', to=settings.AUTH_USER_MODELL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'BlackList',
                'verbose_name_plural': 'BlackList',
            },
        ),
        migrations.CreateModel(
            name='ExchangeRecord',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('price', models.FloatField(default=0, verbose_name='price')),
                ('currency', models.SmallIntegerField(choices=[(0, 'Coins'), (1, 'Diamond')], default=0, verbose_name='currency')),
            ],
            options={
                'verbose_name': 'Exchange Record',
                'verbose_name_plural': 'Exchange Record',
                'ordering': ('-create_time',),
            },
        ),
        migrations.CreateModel(
            name='ItemInfo',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128, verbose_name='name')),
                ('market_name', models.CharField(max_length=128, verbose_name='market name')),
                ('market_hash_name', models.CharField(max_length=128, unique=True, verbose_name='market hash name')),
                ('name_cn', models.CharField(blank=True, default=None, max_length=128, null=True, verbose_name='name(CN)')),
                ('market_name_cn', models.CharField(blank=True, default=None, max_length=128, null=True, verbose_name='market name(CN)')),
                ('name_color', models.CharField(blank=True, default=None, max_length=32, null=True, verbose_name='name color')),
                ('appid', models.CharField(choices=[('570', 'Dota2'), ('730', 'CSGO'), ('578080', 'PUBG'), ('433850', 'H1Z1')], max_length=64, verbose_name='appid')),
                ('contextid', models.CharField(max_length=64, verbose_name='contextid')),
                ('classid', models.CharField(max_length=64, verbose_name='classid')),
                ('icon_url', models.CharField(blank=True, default=None, max_length=512, null=True, verbose_name='icon url')),
                ('icon_url_large', models.CharField(blank=True, default=None, max_length=512, null=True, verbose_name='icon url large')),
                ('rarity', models.CharField(blank=True, default=None, max_length=32, null=True, verbose_name='rarity')),
                ('rarity_color', models.CharField(blank=True, default=None, max_length=32, null=True, verbose_name='rarity color')),
                ('quality', models.CharField(blank=True, default=None, max_length=32, null=True, verbose_name='quality')),
                ('quality_color', models.CharField(blank=True, default=None, max_length=32, null=True, verbose_name='quality color')),
                ('type', models.CharField(blank=True, default=None, max_length=64, null=True, verbose_name='type')),
                ('weapon', models.CharField(blank=True, default=None, max_length=32, null=True, verbose_name='weapon')),
                ('exterior', models.CharField(blank=True, default=None, max_length=32, null=True, verbose_name='exterior')),
                ('item_set', models.CharField(blank=True, default=None, max_length=64, null=True, verbose_name='item set')),
                ('slot', models.CharField(blank=True, default=None, max_length=64, null=True, verbose_name='slot')),
                ('hero', models.CharField(blank=True, default=None, max_length=64, null=True, verbose_name='hero')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
            ],
            options={
                'verbose_name': 'Item Info',
                'verbose_name_plural': 'Item Info',
            },
        ),
        migrations.CreateModel(
            name='ItemPrice',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('price', models.FloatField(default=0.0, verbose_name='price')),
                ('custom', models.BooleanField(default=False, verbose_name='enable custom')),
                ('custom_price', models.FloatField(default=0.0, verbose_name='custom price(USD)')),
                ('custom_discount', models.FloatField(default=100, verbose_name='custom discount(%)')),
                ('wxp_price', models.FloatField(default=0.0, verbose_name='waxpeer market price(USD)-0')),
                ('zbt_price', models.FloatField(default=0.0, verbose_name='zbt market price(USD)-0')),
                ('steam_prices_net_price', models.FloatField(default=0.0, verbose_name='Steam market price(USD)-0')),
                ('steam_normal_price_dollar', models.FloatField(default=0.0, verbose_name='Steam normal price(USD)-10')),
                ('steam_sale_price_dollar', models.FloatField(default=0.0, verbose_name='Steam sale price(USD)-11')),
                ('item_info', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='item_price', to='package.ItemInfo', verbose_name='item info')),
            ],
            options={
                'verbose_name': 'Item Price',
                'verbose_name_plural': 'Item Price',
            },
        ),
        migrations.CreateModel(
            name='ItemWhitelist',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('enable', models.BooleanField(default=True, verbose_name='enable')),
                ('item_info', models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='whitelist', to='package.ItemInfo', verbose_name='item info')),
            ],
            options={
                'verbose_name': 'Item Whitelist',
                'verbose_name_plural': 'Item Whitelist',
                'ordering': ('item_info__market_hash_name',),
            },
        ),
        migrations.CreateModel(
            name='LockItemsStatistics',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('count', models.IntegerField(verbose_name='Count')),
                ('item_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='package.ItemInfo', verbose_name='item info')),
            ],
            options={
                'verbose_name': 'Lock Items Statistics',
                'verbose_name_plural': 'Lock Items Statistics',
                'ordering': ('-create_time',),
            },
        ),
        migrations.CreateModel(
            name='PackageItem',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('assetid', models.CharField(max_length=64, verbose_name='assetid')),
                ('instanceid', models.CharField(max_length=64, verbose_name='instanceid')),
                ('state', models.SmallIntegerField(choices=[(0, 'Initialed'), (1, 'Available'), (2, 'Blocked'), (3, 'Gaming'), (4, 'Withdrawing'), (5, 'Withdrawn'), (6, 'Exchanged'), (7, 'Invalid')], default=0, verbose_name='status')),
                ('bot_steamid', models.CharField(blank=True, default=None, max_length=64, null=True, verbose_name='bot Steamid')),
                ('source', models.SmallIntegerField(choices=[(0, 'Case'), (6, 'LuckyBox'), (4, 'Roll'), (5, 'BattleRoom')], default=0, verbose_name='status')),
                ('part', models.BooleanField(default=False, verbose_name='part of item')),
                ('amount', models.FloatField(default=0.0, verbose_name='amount')),
                ('case_name', models.CharField(blank=True, default=None, max_length=128, null=True, verbose_name='case name')),
                ('case_cover', models.CharField(blank=True, default=None, max_length=128, null=True, verbose_name='case cover')),
                ('item_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='package.ItemInfo', verbose_name='item info')),
                ('user', models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='package_items', to=settings.AUTH_USER_MODELL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'Package Item',
                'verbose_name_plural': 'Package Item',
            },
        ),
        migrations.CreateModel(
            name='PackageItemPriceRate',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('level', models.IntegerField(default=0, verbose_name='level')),
                ('price_rate', models.FloatField(default=0, verbose_name='price rate(%)')),
                ('min_price', models.FloatField(default=0, verbose_name='min price')),
                ('max_price', models.FloatField(default=0, verbose_name='max price')),
            ],
            options={
                'verbose_name': 'Item Price Rate Config',
                'verbose_name_plural': 'Item Price Rate Config',
                'ordering': ('level',),
            },
        ),
        migrations.CreateModel(
            name='PackageStatisticsDay',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('date', models.DateField(default=django.utils.timezone.now, verbose_name='date')),
                ('amount', models.FloatField(default=0.0, verbose_name='amount')),
                ('trade_type', models.SmallIntegerField(choices=[(1, 'Deposit'), (2, 'Withdraw')], verbose_name='trade type')),
            ],
            options={
                'verbose_name': 'Item Statistics Day',
                'verbose_name_plural': 'Item Statistics Day',
                'ordering': ('-create_time',),
            },
        ),
        migrations.CreateModel(
            name='PackageStatisticsMonth',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('date', models.DateField(default=django.utils.timezone.now, verbose_name='date')),
                ('amount', models.FloatField(default=0.0, verbose_name='amount')),
                ('trade_type', models.SmallIntegerField(choices=[(1, 'Deposit'), (2, 'Withdraw')], verbose_name='trade type')),
            ],
            options={
                'verbose_name': 'Item Statistics Month',
                'verbose_name_plural': 'Item Statistics Month',
                'ordering': ('-create_time',),
            },
        ),
        migrations.CreateModel(
            name='ShopBot',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('remark', models.CharField(max_length=128, verbose_name='remark')),
                ('enable', models.BooleanField(default=True, verbose_name='enable')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='shop_bot', to=settings.AUTH_USER_MODELL, verbose_name='account')),
            ],
            options={
                'verbose_name': 'Shop Bot Config',
                'verbose_name_plural': 'Shop Bot Config',
            },
        ),
        migrations.CreateModel(
            name='ShopRecord',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('price', models.FloatField(default=0, verbose_name='price')),
                ('item_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='shop_records', to='package.ItemInfo', verbose_name='item info')),
                ('package', models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='shop_records', to='package.PackageItem', verbose_name='package item')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='shop_records', to=settings.AUTH_USER_MODELL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'Shop record',
                'verbose_name_plural': 'Shop record',
                'ordering': ('-create_time',),
            },
        ),
        migrations.CreateModel(
            name='TradeBot',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('remark', models.CharField(max_length=128, verbose_name='remark')),
                ('steamid', models.CharField(max_length=128, unique=True, verbose_name='Steamid')),
                ('trade_url', models.CharField(max_length=256, verbose_name='trade url')),
                ('enable', models.BooleanField(default=True, verbose_name='enable')),
                ('enable_deposit', models.BooleanField(default=True, verbose_name='enable deposit')),
                ('enable_withdraw', models.BooleanField(default=True, verbose_name='enable withdraw')),
                ('account', models.CharField(max_length=128, verbose_name='account')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('shared_secret', models.CharField(max_length=128, verbose_name='shared secret')),
                ('identity_secret', models.CharField(max_length=128, verbose_name='identity secret')),
            ],
            options={
                'verbose_name': 'Trade Bot Config',
                'verbose_name_plural': 'Trade Bot Config',
            },
        ),
        migrations.CreateModel(
            name='TradeBotInventory',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('assetid', models.CharField(max_length=64, verbose_name='assetid')),
                ('instanceid', models.CharField(max_length=64, verbose_name='instanceid')),
                ('bot_steamid', models.CharField(blank=True, default=None, max_length=64, null=True, verbose_name='bot Steamid')),
                ('matching', models.BooleanField(default=False, verbose_name='matching')),
                ('enable', models.BooleanField(default=True, verbose_name='enable')),
                ('item_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='package.ItemInfo', verbose_name='item info')),
            ],
            options={
                'verbose_name': 'Trade Bot Inventory',
                'verbose_name_plural': 'Trade Bot Inventory',
            },
        ),
        migrations.CreateModel(
            name='TradeItem',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('assetid', models.CharField(max_length=64, verbose_name='assetid')),
                ('instanceid', models.CharField(max_length=64, verbose_name='instanceid')),
                ('price', models.FloatField(default=0.0, verbose_name='price')),
                ('item_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='package.ItemInfo', verbose_name='item info')),
                ('package', models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, to='package.PackageItem', verbose_name='package item')),
            ],
            options={
                'verbose_name': 'Trade Item',
                'verbose_name_plural': 'Trade Item',
            },
        ),
        migrations.CreateModel(
            name='TradeRecord',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('tradeurl', models.CharField(max_length=256, verbose_name='trade url')),
                ('trade_type', models.SmallIntegerField(choices=[(1, 'Deposit'), (2, 'Withdraw')], verbose_name='trade type')),
                ('amount', models.FloatField(default=0.0, verbose_name='amount')),
                ('state', models.SmallIntegerField(choices=[(0, 'Initialed'), (1, 'Accepted'), (2, 'Cancelled'), (11, 'Submitted'), (12, 'TradeNoUpdated'), (13, 'Active')], default=0, verbose_name='trade status')),
                ('trade_no', models.CharField(blank=True, default=None, max_length=32, null=True, verbose_name='trade No.')),
                ('security_code', models.CharField(blank=True, default=None, max_length=32, null=True, verbose_name='security code')),
                ('bot_steamid', models.CharField(blank=True, default=None, max_length=64, null=True, verbose_name='bot Steamid')),
                ('bot_msg', models.CharField(blank=True, default=None, max_length=128, null=True, verbose_name='bot message')),
                ('trade_time', models.DateTimeField(default=django.utils.timezone.now, verbose_name='trade time')),
                ('review', models.BooleanField(default=False, verbose_name='review')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='trade_records', to=settings.AUTH_USER_MODELL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'Trade Record',
                'verbose_name_plural': 'Trade Record',
                'ordering': ('-create_time',),
            },
        ),
        migrations.CreateModel(
            name='UnlockTimeConfig',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('last_unlock_time', models.DateTimeField(verbose_name='last unlock time')),
                ('interval', models.IntegerField(verbose_name='interval(min)')),
            ],
            options={
                'verbose_name': 'Item Unlock Time Config',
                'verbose_name_plural': 'Item Unlock Time Config',
                'ordering': ('-create_time',),
            },
        ),
        migrations.AddField(
            model_name='tradeitem',
            name='record',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='trade_items', to='package.TradeRecord', verbose_name='trade record'),
        ),
        migrations.AddField(
            model_name='exchangerecord',
            name='item_info',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='exchange_records', to='package.ItemInfo', verbose_name='item info'),
        ),
        migrations.AddField(
            model_name='exchangerecord',
            name='package',
            field=models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='exchange_records', to='package.PackageItem', verbose_name='package item'),
        ),
        migrations.AddField(
            model_name='exchangerecord',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='exchange_records', to=settings.AUTH_USER_MODELL, verbose_name='user'),
        ),
    ]
