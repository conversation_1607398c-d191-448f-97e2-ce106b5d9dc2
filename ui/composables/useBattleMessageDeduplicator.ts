/**
 * 对战WebSocket消息去重管理器
 * 
 * 解决问题：
 * 1. 防止WebSocket消息重复处理导致的数据不一致
 * 2. 基于message_sequence实现消息去重
 * 3. 支持消息类型分类处理
 * 4. 提供消息顺序验证
 * 5. 自动清理过期消息记录
 */

interface MessageRecord {
  messageId: string;
  messageType: string;
  round: number;
  sequence: number;
  timestamp: number;
  processed: boolean;
}

interface MessageValidationResult {
  isValid: boolean;
  isDuplicate: boolean;
  isOutOfOrder: boolean;
  reason?: string;
}

export const useBattleMessageDeduplicator = () => {
  // 已接收消息记录
  const receivedMessages = ref<Set<string>>(new Set());
  
  // 消息历史记录
  const messageHistory = ref<Map<string, MessageRecord>>(new Map());
  
  // 最后处理的序列号（按消息类型分别记录）
  const lastSequenceByType = ref<Map<string, number>>(new Map());
  
  // 消息类型计数器
  const messageCounters = ref<Map<string, number>>(new Map());
  
  // 清理定时器
  const cleanupTimer = ref<NodeJS.Timeout | null>(null);
  
  // 统计信息
  const statistics = ref({
    totalMessages: 0,
    duplicateMessages: 0,
    outOfOrderMessages: 0,
    processedMessages: 0
  });

  /**
   * 验证消息是否有效
   */
  const validateMessage = (messageData: any): MessageValidationResult => {
    try {
      const { message_sequence, message_type, round } = messageData;
      
      // 基础字段验证
      if (!message_sequence || !message_type) {
        return {
          isValid: false,
          isDuplicate: false,
          isOutOfOrder: false,
          reason: 'Missing required fields: message_sequence or message_type'
        };
      }
      
      // 生成消息唯一标识
      const messageId = `${message_type}_${round || 0}_${message_sequence}`;
      
      // 检查消息是否重复
      if (receivedMessages.value.has(messageId)) {
        statistics.value.duplicateMessages++;
        console.warn(`[🔒MESSAGE-DEDUP] 重复消息已忽略: ${messageId}`);
        return {
          isValid: false,
          isDuplicate: true,
          isOutOfOrder: false,
          reason: `Duplicate message: ${messageId}`
        };
      }
      
      // 检查序列号顺序（可选，仅警告）
      const lastSequence = lastSequenceByType.value.get(message_type) || 0;
      const isOutOfOrder = message_sequence < lastSequence;
      
      if (isOutOfOrder) {
        statistics.value.outOfOrderMessages++;
        console.warn(`[🔒MESSAGE-DEDUP] 收到过期消息: ${messageId}, 当前序列: ${lastSequence}`);
      }
      
      return {
        isValid: true,
        isDuplicate: false,
        isOutOfOrder,
        reason: undefined
      };
      
         } catch (error: any) {
       console.error('[🔒MESSAGE-DEDUP] 消息验证失败:', error);
       return {
         isValid: false,
         isDuplicate: false,
         isOutOfOrder: false,
         reason: `Validation error: ${error?.message || 'Unknown error'}`
       };
     }
  };

  /**
   * 记录已处理消息
   */
  const recordProcessedMessage = (messageData: any): void => {
    try {
      const { message_sequence, message_type, round } = messageData;
      
      if (!message_sequence || !message_type) {
        return;
      }
      
      // 生成消息唯一标识
      const messageId = `${message_type}_${round || 0}_${message_sequence}`;
      
      // 记录到已接收消息集合
      receivedMessages.value.add(messageId);
      
      // 记录到消息历史
      const record: MessageRecord = {
        messageId,
        messageType: message_type,
        round: round || 0,
        sequence: message_sequence,
        timestamp: Date.now(),
        processed: true
      };
      
      messageHistory.value.set(messageId, record);
      
      // 更新最后处理的序列号
      const currentSequence = lastSequenceByType.value.get(message_type) || 0;
      if (message_sequence > currentSequence) {
        lastSequenceByType.value.set(message_type, message_sequence);
      }
      
      // 更新计数器
      const currentCount = messageCounters.value.get(message_type) || 0;
      messageCounters.value.set(message_type, currentCount + 1);
      
      // 更新统计信息
      statistics.value.totalMessages++;
      statistics.value.processedMessages++;
      
      console.log(`[🔒MESSAGE-DEDUP] 记录已处理消息: ${messageId}`);
      
         } catch (error: any) {
       console.error('[🔒MESSAGE-DEDUP] 记录消息失败:', error);
     }
  };

  /**
   * 处理消息（验证 + 记录）
   */
  const processMessage = (messageData: any): boolean => {
    try {
      // 验证消息
      const validation = validateMessage(messageData);
      
      if (!validation.isValid) {
        return false;
      }
      
      // 记录已处理消息
      recordProcessedMessage(messageData);
      
      return true;
      
         } catch (error: any) {
       console.error('[🔒MESSAGE-DEDUP] 处理消息失败:', error);
       return false;
     }
  };

  /**
   * 检查消息是否已处理
   */
  const isMessageProcessed = (messageData: any): boolean => {
    try {
      const { message_sequence, message_type, round } = messageData;
      
      if (!message_sequence || !message_type) {
        return false;
      }
      
      const messageId = `${message_type}_${round || 0}_${message_sequence}`;
      return receivedMessages.value.has(messageId);
      
         } catch (error: any) {
       console.error('[🔒MESSAGE-DEDUP] 检查消息状态失败:', error);
       return false;
     }
  };

  /**
   * 清理过期消息记录
   */
  const cleanup = (): void => {
    try {
      const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
      let cleanedCount = 0;
      
      // 清理receivedMessages
      for (const messageId of receivedMessages.value) {
        const record = messageHistory.value.get(messageId);
        if (record && record.timestamp < fiveMinutesAgo) {
          receivedMessages.value.delete(messageId);
          messageHistory.value.delete(messageId);
          cleanedCount++;
        }
      }
      
      console.log(`[🔒MESSAGE-DEDUP] 清理过期消息: ${cleanedCount} 条`);
      
         } catch (error: any) {
       console.error('[🔒MESSAGE-DEDUP] 清理过期消息失败:', error);
     }
  };

  /**
   * 开始定期清理
   */
  const startPeriodicCleanup = (): void => {
    if (cleanupTimer.value) {
      clearInterval(cleanupTimer.value);
    }
    
    // 每5分钟清理一次
    cleanupTimer.value = setInterval(cleanup, 5 * 60 * 1000);
    
    console.log('[🔒MESSAGE-DEDUP] 开始定期清理任务');
  };

  /**
   * 停止定期清理
   */
  const stopPeriodicCleanup = (): void => {
    if (cleanupTimer.value) {
      clearInterval(cleanupTimer.value);
      cleanupTimer.value = null;
    }
    
    console.log('[🔒MESSAGE-DEDUP] 停止定期清理任务');
  };

  /**
   * 重置去重器状态
   */
  const reset = (): void => {
    try {
      receivedMessages.value.clear();
      messageHistory.value.clear();
      lastSequenceByType.value.clear();
      messageCounters.value.clear();
      
      statistics.value = {
        totalMessages: 0,
        duplicateMessages: 0,
        outOfOrderMessages: 0,
        processedMessages: 0
      };
      
      console.log('[🔒MESSAGE-DEDUP] 去重器状态已重置');
      
         } catch (error: any) {
       console.error('[🔒MESSAGE-DEDUP] 重置状态失败:', error);
     }
  };

  /**
   * 获取统计信息
   */
  const getStatistics = () => {
    return {
      ...statistics.value,
      messageTypeCount: Object.fromEntries(messageCounters.value),
      lastSequenceByType: Object.fromEntries(lastSequenceByType.value),
      totalUniqueMessages: receivedMessages.value.size,
      historySize: messageHistory.value.size
    };
  };

  /**
   * 获取消息历史记录
   */
  const getMessageHistory = (messageType?: string, limit = 50) => {
    const history = Array.from(messageHistory.value.values())
      .sort((a, b) => b.timestamp - a.timestamp);
    
    if (messageType) {
      return history
        .filter(record => record.messageType === messageType)
        .slice(0, limit);
    }
    
    return history.slice(0, limit);
  };

  // 生命周期管理
  onMounted(() => {
    startPeriodicCleanup();
  });

  onUnmounted(() => {
    stopPeriodicCleanup();
  });

  return {
    // 核心方法
    validateMessage,
    recordProcessedMessage,
    processMessage,
    isMessageProcessed,
    
    // 管理方法
    cleanup,
    startPeriodicCleanup,
    stopPeriodicCleanup,
    reset,
    
    // 统计和调试
    getStatistics,
    getMessageHistory,
    statistics: readonly(statistics),
    
    // 状态查询
    receivedMessagesCount: computed(() => receivedMessages.value.size),
    messageHistorySize: computed(() => messageHistory.value.size),
    messageTypeCount: computed(() => Object.fromEntries(messageCounters.value))
  };
}; 