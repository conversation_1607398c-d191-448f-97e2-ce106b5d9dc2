--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:14:45:30 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1465, 'M': 1, 'L': '0.001465', 'p': '<15>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd0179a8b0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd0179a970>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd01794a60 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '51982', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:14:46:01 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1505, 'M': 1, 'L': '0.001505', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd01a6c340>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd01ad4370>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd016b2460 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '38662', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:14:46:31 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1546, 'M': 1, 'L': '0.001546', 'p': '<14>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd02061bb0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd02061070>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd017684c0 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '41068', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:14:47:01 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1545, 'M': 1, 'L': '0.001545', 'p': '<14>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd02078ca0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd0206ab80>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd01768520 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '47446', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
