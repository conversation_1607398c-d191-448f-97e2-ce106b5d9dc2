#!/usr/bin/env python3
"""
CKEditor 迁移前备份脚本

在迁移到 CKEditor 5 之前备份相关文件
"""

import os
import sys
import shutil
import argparse
import logging
from datetime import datetime
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_backup():
    """创建迁移前备份"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_dir = Path(f'../backup/ckeditor_migration_backup_{timestamp}')
    backup_dir.mkdir(parents=True, exist_ok=True)
    
    # 需要备份的文件和目录
    files_to_backup = [
        'steambase/base_settings.py',
        'steambase/urls.py',
        'sitecfg/models.py',
        'articles/models.py',
        'sitecfg/migrations/',
        'articles/migrations/',
        'static/ckeditor/',
        'static/django_ckeditor_5/',
    ]
    
    logger.info(f"创建备份目录: {backup_dir}")
    
    for item in files_to_backup:
        source = Path(item)
        if source.exists():
            if source.is_file():
                dest = backup_dir / source
                dest.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(source, dest)
                logger.info(f"备份文件: {source} -> {dest}")
            elif source.is_dir():
                dest = backup_dir / source
                try:
                    shutil.copytree(source, dest)
                    logger.info(f"备份目录: {source} -> {dest}")
                except FileExistsError:
                    logger.warning(f"目录已存在，跳过: {dest}")
                except Exception as e:
                    logger.warning(f"备份目录失败: {source} - {e}")
        else:
            logger.warning(f"文件不存在，跳过: {source}")
    
    # 创建备份说明文件
    readme_content = f"""# CKEditor 迁移备份

备份时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
备份原因: CKEditor 4 到 CKEditor 5 迁移

## 备份内容

- steambase/base_settings.py - Django 设置文件
- steambase/urls.py - URL 配置文件
- sitecfg/models.py - sitecfg 模型文件
- articles/models.py - articles 模型文件
- sitecfg/migrations/ - sitecfg 迁移文件
- articles/migrations/ - articles 迁移文件
- static/ckeditor/ - CKEditor 4 静态文件
- static/django_ckeditor_5/ - CKEditor 5 静态文件

## 恢复方法

如果迁移出现问题，可以使用以下命令恢复：

```bash
# 恢复文件
cp -r {backup_dir}/* ./

# 重新运行迁移
python manage.py migrate

# 重启服务
docker-compose restart web
```

## 注意事项

- 迁移前请确保数据库已备份
- 迁移后需要重新收集静态文件
- 建议在测试环境先验证迁移结果
"""
    
    with open(backup_dir / 'README.md', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    logger.info(f"备份完成: {backup_dir}")
    return backup_dir

def main():
    parser = argparse.ArgumentParser(description='CKEditor 迁移前备份工具')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        backup_dir = create_backup()
        logger.info("备份成功完成！")
        logger.info(f"备份位置: {backup_dir}")
        logger.info("现在可以安全地进行 CKEditor 迁移了")
    except Exception as e:
        logger.error(f"备份失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
