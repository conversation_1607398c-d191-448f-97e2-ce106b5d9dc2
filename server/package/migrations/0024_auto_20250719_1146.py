# Generated by Django 3.2.25 on 2025-07-19 03:46

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('package', '0023_auto_20250718_1628'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='blacklist',
            options={'verbose_name': '黑名单', 'verbose_name_plural': 'BlackList'},
        ),
        migrations.AlterModelOptions(
            name='exchangerecord',
            options={'ordering': ('-create_time',), 'verbose_name': '兑换记录', 'verbose_name_plural': 'Exchange Record'},
        ),
        migrations.AlterModelOptions(
            name='itemcategory',
            options={'verbose_name': '物品分类', 'verbose_name_plural': 'Item Category'},
        ),
        migrations.AlterModelOptions(
            name='itemexterior',
            options={'verbose_name': '物品磨损', 'verbose_name_plural': 'Item Exterior'},
        ),
        migrations.AlterModelOptions(
            name='iteminfo',
            options={'verbose_name': '物品信息', 'verbose_name_plural': 'Item Info'},
        ),
        migrations.AlterModelOptions(
            name='itemprice',
            options={'verbose_name': '物品价格', 'verbose_name_plural': 'Item Price'},
        ),
        migrations.AlterModelOptions(
            name='itemquality',
            options={'verbose_name': '物品品质', 'verbose_name_plural': 'Item Quality'},
        ),
        migrations.AlterModelOptions(
            name='itemrarity',
            options={'verbose_name': '物品稀有度', 'verbose_name_plural': 'Item Rarity'},
        ),
        migrations.AlterModelOptions(
            name='itemwhitelist',
            options={'ordering': ('item_info__market_hash_name',), 'verbose_name': '物品白名单', 'verbose_name_plural': 'Item Whitelist'},
        ),
        migrations.AlterModelOptions(
            name='lockitemsstatistics',
            options={'ordering': ('-create_time',), 'verbose_name': '锁定物品统计', 'verbose_name_plural': 'Lock Items Statistics'},
        ),
        migrations.AlterModelOptions(
            name='packageitem',
            options={'verbose_name': '包裹物品', 'verbose_name_plural': 'Package Item'},
        ),
        migrations.AlterModelOptions(
            name='packageitempricerate',
            options={'ordering': ('level',), 'verbose_name': '物品价格比率配置', 'verbose_name_plural': 'Item Price Rate Config'},
        ),
        migrations.AlterModelOptions(
            name='packagestatisticsday',
            options={'ordering': ('-create_time',), 'verbose_name': '物品日统计', 'verbose_name_plural': 'Item Statistics Day'},
        ),
        migrations.AlterModelOptions(
            name='packagestatisticsmonth',
            options={'ordering': ('-create_time',), 'verbose_name': '物品月统计', 'verbose_name_plural': 'Item Statistics Month'},
        ),
        migrations.AlterModelOptions(
            name='shopbot',
            options={'verbose_name': '商店机器人配置', 'verbose_name_plural': 'Shop Bot Config'},
        ),
        migrations.AlterModelOptions(
            name='shoprecord',
            options={'ordering': ('-create_time',), 'verbose_name': '商店记录', 'verbose_name_plural': 'Shop record'},
        ),
        migrations.AlterModelOptions(
            name='tradebot',
            options={'verbose_name': '交易机器人配置', 'verbose_name_plural': 'Trade Bot Config'},
        ),
        migrations.AlterModelOptions(
            name='tradebotinventory',
            options={'verbose_name': '交易机器人库存', 'verbose_name_plural': 'Trade Bot Inventory'},
        ),
        migrations.AlterModelOptions(
            name='tradeitem',
            options={'verbose_name': '交易物品', 'verbose_name_plural': 'Trade Item'},
        ),
        migrations.AlterModelOptions(
            name='traderecord',
            options={'ordering': ('-create_time',), 'verbose_name': '交易记录', 'verbose_name_plural': 'Trade Record'},
        ),
        migrations.AlterModelOptions(
            name='unlocktimeconfig',
            options={'ordering': ('-create_time',), 'verbose_name': '物品解锁时间配置', 'verbose_name_plural': 'Item Unlock Time Config'},
        ),
        migrations.AlterField(
            model_name='blacklist',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='blacklist', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
        migrations.AlterField(
            model_name='exchangerecord',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='exchange_records', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
        migrations.AlterField(
            model_name='iteminfo',
            name='item_category',
            field=models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='item_info', to='package.itemcategory', verbose_name='分类'),
        ),
        migrations.AlterField(
            model_name='packageitem',
            name='user',
            field=models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='package_items', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
        migrations.AlterField(
            model_name='shoprecord',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='shop_records', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
        migrations.AlterField(
            model_name='traderecord',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='trade_records', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
    ]
