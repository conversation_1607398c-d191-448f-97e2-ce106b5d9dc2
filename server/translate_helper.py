#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
翻译助手脚本 - 用于批量翻译 Django .po 文件中的常见英文词汇
"""

import re
import os

# 常见英文词汇到中文的映射
TRANSLATION_MAP = {
    # 基础词汇
    "Name": "名称",
    "Title": "标题", 
    "Description": "描述",
    "Content": "内容",
    "Status": "状态",
    "Type": "类型",
    "Category": "分类",
    "Tag": "标签",
    "Price": "价格",
    "Amount": "金额",
    "Quantity": "数量",
    "Date": "日期",
    "Time": "时间",
    "Created": "创建时间",
    "Updated": "更新时间",
    "Active": "激活",
    "Inactive": "未激活",
    "Enabled": "启用",
    "Disabled": "禁用",
    "Success": "成功",
    "Failed": "失败",
    "Error": "错误",
    "Warning": "警告",
    "Info": "信息",
    
    # 用户相关
    "User": "用户",
    "Users": "用户",
    "Username": "用户名",
    "Password": "密码",
    "Email": "邮箱",
    "Phone": "电话",
    "Profile": "个人资料",
    "Account": "账户",
    "Balance": "余额",
    "Login": "登录",
    "Logout": "退出",
    "Register": "注册",
    
    # 管理相关
    "Admin": "管理员",
    "Administration": "管理",
    "Dashboard": "仪表板",
    "Settings": "设置",
    "Configuration": "配置",
    "Management": "管理",
    "System": "系统",
    "Database": "数据库",
    "Cache": "缓存",
    "Log": "日志",
    "Logs": "日志",
    
    # 操作相关
    "Add": "添加",
    "Edit": "编辑",
    "Delete": "删除",
    "Update": "更新",
    "Create": "创建",
    "Save": "保存",
    "Cancel": "取消",
    "Submit": "提交",
    "Search": "搜索",
    "Filter": "筛选",
    "Sort": "排序",
    "Export": "导出",
    "Import": "导入",
    "Download": "下载",
    "Upload": "上传",
    
    # 业务相关
    "Order": "订单",
    "Orders": "订单",
    "Payment": "支付",
    "Charge": "充值",
    "Withdraw": "提现",
    "Transaction": "交易",
    "Item": "物品",
    "Items": "物品",
    "Product": "产品",
    "Products": "产品",
    "Case": "箱子",
    "Cases": "箱子",
    "Box": "盒子",
    "Boxes": "盒子",
    "Skin": "皮肤",
    "Skins": "皮肤",
    "Weapon": "武器",
    "Weapons": "武器",
    
    # 状态相关
    "Pending": "待处理",
    "Processing": "处理中",
    "Completed": "已完成",
    "Cancelled": "已取消",
    "Approved": "已批准",
    "Rejected": "已拒绝",
    "Published": "已发布",
    "Draft": "草稿",
    "Hidden": "隐藏",
    "Visible": "可见",
    
    # 其他常用词
    "Total": "总计",
    "Count": "数量",
    "Limit": "限制",
    "Rate": "比率",
    "Percentage": "百分比",
    "Level": "等级",
    "Rank": "排名",
    "Score": "分数",
    "Point": "积分",
    "Points": "积分",
    "Coin": "金币",
    "Coins": "金币",
    "Credit": "信用",
    "Credits": "信用",
    "Bonus": "奖励",
    "Reward": "奖励",
    "Gift": "礼品",
    "Prize": "奖品",
    "Winner": "获胜者",
    "Loser": "失败者",
    "Game": "游戏",
    "Games": "游戏",
    "Room": "房间",
    "Rooms": "房间",
    "Battle": "对战",
    "Battles": "对战",
    "Match": "匹配",
    "Matches": "匹配",
    "Round": "回合",
    "Rounds": "回合",
    
    # 技术相关
    "API": "接口",
    "URL": "链接",
    "Link": "链接",
    "Image": "图片",
    "File": "文件",
    "Files": "文件",
    "Folder": "文件夹",
    "Directory": "目录",
    "Path": "路径",
    "Size": "大小",
    "Format": "格式",
    "Version": "版本",
    "Build": "构建",
    "Release": "发布",
    "Debug": "调试",
    "Test": "测试",
    "Production": "生产",
    "Development": "开发",
    
    # 时间相关
    "Today": "今天",
    "Yesterday": "昨天",
    "Tomorrow": "明天",
    "Week": "周",
    "Month": "月",
    "Year": "年",
    "Hour": "小时",
    "Minute": "分钟",
    "Second": "秒",
    "Daily": "每日",
    "Weekly": "每周",
    "Monthly": "每月",
    "Yearly": "每年",
    
    # 权限相关
    "Permission": "权限",
    "Permissions": "权限",
    "Role": "角色",
    "Roles": "角色",
    "Group": "组",
    "Groups": "组",
    "Access": "访问",
    "Denied": "拒绝",
    "Allowed": "允许",
    "Forbidden": "禁止",
    "Authorized": "已授权",
    "Unauthorized": "未授权",
}

def translate_po_file(po_file_path):
    """
    翻译 .po 文件中的空翻译条目
    """
    if not os.path.exists(po_file_path):
        print(f"文件不存在: {po_file_path}")
        return
    
    with open(po_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 统计翻译前的空条目数量
    empty_msgstr_count = len(re.findall(r'msgstr ""', content))
    print(f"翻译前空翻译条目数量: {empty_msgstr_count}")
    
    # 进行翻译替换
    translated_count = 0
    for english, chinese in TRANSLATION_MAP.items():
        # 匹配模式：msgid "English" 后面跟着 msgstr ""
        pattern = rf'(msgid "{re.escape(english)}"\s*msgstr) ""'
        replacement = rf'\1 "{chinese}"'
        
        new_content, count = re.subn(pattern, replacement, content, flags=re.MULTILINE)
        if count > 0:
            content = new_content
            translated_count += count
            print(f"翻译了 '{english}' -> '{chinese}' ({count} 处)")
    
    # 统计翻译后的空条目数量
    empty_msgstr_count_after = len(re.findall(r'msgstr ""', content))
    
    # 写回文件
    with open(po_file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"\n翻译完成!")
    print(f"总共翻译了 {translated_count} 个条目")
    print(f"翻译后剩余空翻译条目数量: {empty_msgstr_count_after}")
    print(f"减少了 {empty_msgstr_count - empty_msgstr_count_after} 个空条目")

if __name__ == "__main__":
    po_file = "locale/zh_hans/LC_MESSAGES/django.po"
    translate_po_file(po_file)
