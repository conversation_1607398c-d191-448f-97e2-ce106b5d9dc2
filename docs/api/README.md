# API 文档

本目录包含平台所有API接口的详细文档。

## 📋 文档列表

- [**API 总览**](overview.md) - API设计原则、认证机制、通用响应格式
- [**认证 API**](authentication-api.md) - 用户注册、登录、Token管理
- [**箱子 API**](case-api.md) - 开箱、物品管理相关接口
- [**对战 API**](battle-api.md) - 对战房间、回合管理接口
- [**WebSocket API**](websocket-api.md) - 实时通信接口规范
- [**饰品 API**](package-api-analysis.md) - 饰品管理系统分析和改进建议
- [**充值 API**](charge-api-analysis.md) - 充值支付系统分析和改进建议
- [**B2C交易 API**](b2ctrade-api-analysis.md) - 饰品交易系统分析和改进建议
- [**红包 API**](envelope-api-analysis.md) - 口令红包系统分析和改进建议
- [**聊天 API**](chat-api-analysis.md) - 消息系统分析和改进建议
- [**抽奖 API**](lottery-api-analysis.md) - 定时抽奖系统分析和改进建议
- [**Roll API**](roll-api-analysis.md) - 竞拍抽奖系统分析和改进建议
- [**站点配置 API**](sitecfg-api-analysis.md) - 网站配置管理系统分析和改进建议
- [**ThWorker 系统**](thworker-analysis.md) - 后台任务系统架构分析和改进建议
- [**Withdraw 提现**](withdraw-api-analysis.md) - 提现交易系统分析和改进建议
- [**Crash 游戏**](crash-api-analysis.md) - 崩盘游戏系统分析和改进建议
- [**BlindBox 盲盒**](blindbox-api-analysis.md) - 多位置选择盲盒系统分析和改进建议
- [**CustomBox 自制盲盒**](custombox-api-analysis.md) - 用户生成内容盲盒系统分析和改进建议
- [**Grab 夺宝**](grab-api-analysis.md) - 基于随机位置的夺宝游戏系统分析和改进建议
- [**LuckyBox 拉货**](luckybox-api-analysis.md) - 基于概率的物品获取系统分析和改进建议
- [**Market 商城**](market-api-analysis.md) - 直接购买商城系统分析和改进建议
- [**Promotion 推广**](promotion-api-analysis.md) - 推荐返利系统分析和改进建议
- [**Tradeup 换肤**](tradeup-api-analysis.md) - 饰品升级交换系统分析和改进建议
- [**Agent 代理商**](agent-api-analysis.md) - 多级代理商管理系统分析和改进建议
- [**Monitor 监控**](monitor-api-analysis.md) - 实时监控系统分析和改进建议

## 🚀 快速开始

1. 先阅读 [API 总览](overview.md) 了解通用规范
2. 查看 [认证 API](authentication-api.md) 获取访问令牌
3. 根据需要查阅具体功能的API文档

## 📝 API 规范

### 基础URL
- 开发环境: `http://localhost:8000/api/`
- 生产环境: `https://api.csgoskins.com.cn/api/`

### 认证方式
所有API都需要在请求头中包含认证信息：
```
Authorization: Bearer <your_token>
```

### 响应格式
```json
{
    "success": true|false,
    "message": "描述信息",
    "data": {} // 具体数据
}
```

### 状态码
- `200` - 成功
- `400` - 请求参数错误
- `401` - 认证失败
- `403` - 权限不足
- `404` - 资源不存在
- `500` - 服务器错误

---

*更新时间: 2025-06-18*
