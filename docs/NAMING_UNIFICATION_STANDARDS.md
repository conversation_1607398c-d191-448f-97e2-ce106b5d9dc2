# 🏷️ 命名统一规范

## 📋 概述

本文档定义了CS:GO皮肤交易平台的命名统一标准，解决后端命名混用问题，确保代码的一致性和可维护性。

## 🎯 核心问题

### 1. 箱子命名混用
- **后端混用**: `box` 和 `case` 两种命名并存
- **前端统一**: 已统一使用 `case`
- **目标**: 后端逐步统一为 `case`

### 2. 饰品命名混用  
- **后端混用**: `skin` 和 `item` 两种命名并存
- **前端统一**: 已统一使用 `skin`
- **目标**: 后端逐步统一为 `skin`

## 📊 当前命名使用情况分析

### 箱子相关命名现状

#### ✅ 已统一使用 `case` 的地方
```python
# 模型名称
class Case(ModelBase):  # server/box/models.py
class CaseRoom(ModelBase):  # server/box/models.py
class CaseRecord(ModelBase):  # server/box/models.py

# 字段名称
case_key = models.CharField()  # 箱子标识
case_type = models.ForeignKey()  # 箱子类型
case_category = models.ForeignKey()  # 箱子分类

# API端点
/api/box/search/  # 搜索箱子
/api/box/detail/  # 箱子详情
/api/box/battle/case/  # 对战箱子列表
```

#### ⚠️ 仍使用 `box` 的地方
```python
# 业务函数名
def get_box_skin_list(case_key, fields):  # server/box/business.py
def get_hot_box_list():  # 热门箱子列表
def get_discount_box_list():  # 折扣箱子列表

# API端点
/api/box/skins/  # 获取箱子皮肤列表
/api/box/records/  # 箱子开启记录

# 缓存键名
cache_key = f'box_skin_list_{case_key}'  # 箱子皮肤列表缓存
```

### 饰品相关命名现状

#### ✅ 已统一使用 `skin` 的地方
```typescript
// 前端组件
SkinCard.vue  // ui/components/skin/SkinCard.vue
getSkinName()  // 获取皮肤名称函数
getSkinFullName()  // 获取完整皮肤名称

// 前端类型定义
interface SkinItem {  // ui/types/skin.ts
  id: string
  name: string
  // ...
}
```

#### ⚠️ 仍使用 `item` 的地方
```python
# 模型名称
class ItemInfo(ModelBase):  # package/models.py
class MarketItem(ModelBase):  # market/models.py

# 字段名称
item_info = models.ForeignKey()  # 物品信息
item_price = models.ForeignKey()  # 物品价格
item_category = models.ForeignKey()  # 物品分类

# API字段
"item_info": {...}  # API响应中的物品信息
"item_price": {...}  # 物品价格信息
```

## 🎯 统一目标和原则

### 命名统一目标
1. **箱子**: 统一使用 `case`
2. **饰品**: 统一使用 `skin`
3. **保持向后兼容**: 渐进式重构，避免破坏性变更

### 统一原则
1. **前端优先**: 以前端已统一的命名为准
2. **渐进式**: 分阶段逐步统一，避免大规模重构
3. **向后兼容**: 保留旧接口，新接口使用统一命名
4. **文档先行**: 新开发严格按照统一命名

## 📋 分阶段统一计划

### 第一阶段：新开发规范化 (立即执行)

#### 新代码命名规范
```python
# ✅ 箱子相关 - 统一使用 case
def get_case_skin_list(case_key):  # 新函数命名
def get_hot_case_list():  # 热门箱子
def get_discount_case_list():  # 折扣箱子

# ✅ 饰品相关 - 统一使用 skin  
def get_skin_details(skin_id):  # 新函数命名
def get_skin_price(skin_id):  # 皮肤价格
class SkinInfo(ModelBase):  # 新模型命名(如果需要)
```

#### API设计规范
```python
# ✅ 新API端点命名
/api/case/skins/  # 替代 /api/box/skins/
/api/case/records/  # 替代 /api/box/records/
/api/skin/details/  # 皮肤详情
/api/skin/price/  # 皮肤价格
```

### 第二阶段：函数和变量重命名 (3个月内)

#### 优先重命名列表
```python
# 高优先级 - 业务函数
get_box_skin_list() → get_case_skin_list()
get_hot_box_list() → get_hot_case_list()
get_discount_box_list() → get_discount_case_list()

# 中优先级 - 缓存键名
'box_skin_list_' → 'case_skin_list_'
'hot_box_list' → 'hot_case_list'

# 低优先级 - 内部变量
box_data → case_data
item_info → skin_info (谨慎处理)
```

### 第三阶段：API端点统一 (6个月内)

#### API迁移计划
```python
# 保留旧端点，添加新端点
# 旧端点标记为deprecated
url(r'^skins/', views.GetBoxSkinView.as_view()),  # 保留
url(r'^case-skins/', views.GetCaseSkinView.as_view()),  # 新增

# 响应中添加deprecation警告
{
    "code": 0,
    "message": "Success",
    "deprecated": "This endpoint is deprecated, use /api/case/case-skins/ instead",
    "body": {...}
}
```

## 🔧 实施工具和检查

### 1. 命名检查脚本
```python
# scripts/tools/check_naming_consistency.py
def check_box_case_naming():
    """检查box/case命名一致性"""
    
def check_skin_item_naming():
    """检查skin/item命名一致性"""
```

### 2. 重构辅助脚本
```python
# scripts/fixes/rename_box_to_case.py
def rename_function_names():
    """批量重命名函数名"""
    
def update_cache_keys():
    """更新缓存键名"""
```

### 3. API兼容性检查
```python
# scripts/tests/test_api_compatibility.py
def test_old_api_endpoints():
    """测试旧API端点兼容性"""
```

## 📖 开发规范

### 新代码开发规范

#### ✅ 正确命名示例
```python
# 箱子相关
def get_case_list():
    """获取箱子列表"""
    pass

def open_case(case_key):
    """开启箱子"""
    pass

case_data = get_case_details(case_key)

# 饰品相关  
def get_skin_info(skin_id):
    """获取皮肤信息"""
    pass

skin_price = calculate_skin_price(skin_id)
```

#### ❌ 避免的命名
```python
# 避免混用
def get_box_list():  # ❌ 应使用 get_case_list()
def get_item_info():  # ❌ 应使用 get_skin_info()

box_data = get_data()  # ❌ 应使用 case_data
item_price = get_price()  # ❌ 应使用 skin_price
```

### 代码审查检查点
1. **函数命名**: 是否使用统一的 case/skin 命名
2. **变量命名**: 是否避免 box/item 混用
3. **API设计**: 新端点是否遵循命名规范
4. **文档更新**: 是否更新相关文档

## 🚨 注意事项

### 重构风险控制
1. **数据库字段**: 暂不修改，影响太大
2. **核心模型**: ItemInfo等核心模型暂不重命名
3. **API兼容**: 保持旧API可用，避免破坏现有集成
4. **测试覆盖**: 重命名前确保有充分测试

### 特殊情况处理
```python
# 保留的命名 (暂不修改)
class ItemInfo(ModelBase):  # 核心模型，暂不重命名
    pass

item_info = models.ForeignKey(ItemInfo)  # 关联字段，暂不修改

# 新增别名方法
def get_skin_info(skin_id):
    """新的统一命名方法"""
    return get_item_info(skin_id)  # 内部调用旧方法
```

## 📊 进度跟踪

### 统一进度指标
- [ ] 新代码命名规范执行率: 100%
- [ ] 函数重命名完成率: 0% (目标: 80%)
- [ ] API端点统一率: 0% (目标: 60%)
- [ ] 文档更新完成率: 0% (目标: 100%)

### 检查命令
```bash
# 检查命名一致性
python3 scripts/tools/check_naming_consistency.py

# 生成重构报告
python3 scripts/tools/generate_refactor_report.py
```

---

**重要提醒**:
- 新开发严格按照统一命名规范
- 重构采用渐进式，确保系统稳定
- 保持API向后兼容性
- 定期检查和更新进度
