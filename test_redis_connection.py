#!/usr/bin/env python3
"""
测试Redis连接的脚本
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')
sys.path.append('/www/wwwroot/csgoskins.com.cn/server')

try:
    django.setup()
    print("✅ Django设置成功")
except Exception as e:
    print(f"❌ Django设置失败: {e}")
    sys.exit(1)

# 测试Redis连接
def test_redis_connection():
    print("\n🔧 测试Redis连接...")
    
    try:
        from django.core.cache import cache
        from django.conf import settings
        
        print(f"📋 Redis配置:")
        print(f"   REDIS_HOST: {getattr(settings, 'REDIS_HOST', 'Not set')}")
        print(f"   REDIS_PORT: {getattr(settings, 'REDIS_PORT', 'Not set')}")
        print(f"   REDIS_URL: {getattr(settings, 'REDIS_URL', 'Not set')}")
        print(f"   CACHES: {settings.CACHES['default']['LOCATION']}")
        
        # 测试缓存连接
        print("\n🔌 测试Django缓存连接...")
        cache.set('test_key', 'test_value', 60)
        value = cache.get('test_key')
        
        if value == 'test_value':
            print("✅ Django缓存连接成功")
            cache.delete('test_key')
        else:
            print(f"❌ Django缓存连接失败，返回值: {value}")
            return False
            
    except Exception as e:
        print(f"❌ Django缓存连接失败: {e}")
        return False
    
    # 测试直接Redis连接
    try:
        import redis
        print("\n🔌 测试直接Redis连接...")
        
        # 使用Django设置中的配置
        r = redis.Redis(
            host=settings.REDIS_HOST,
            port=settings.REDIS_PORT,
            db=settings.REDIS_DB_INDEX,
            password=settings.REDIS_PASSWORD,
            decode_responses=True
        )
        
        result = r.ping()
        print(f"✅ 直接Redis连接成功: {result}")
        
        # 测试读写
        r.set('direct_test', 'success')
        value = r.get('direct_test')
        print(f"✅ Redis读写测试: {value}")
        r.delete('direct_test')
        
        return True
        
    except Exception as e:
        print(f"❌ 直接Redis连接失败: {e}")
        return False

def test_banner_api():
    print("\n🔧 测试Banner API...")
    
    try:
        from sitecfg.business import get_banner
        
        # 定义字段
        fields = ("title", 'title_en', 'title_zh_hans', "link", "image", "type", "is_simple", "description", 'description_en', 'description_zh_hans', "background_class", "glow_class", "primary_button_text", "primary_button_text_en", "primary_button_text_zh_hans", "primary_button_link", "secondary_button_text", "secondary_button_text_en", "secondary_button_text_zh_hans", "secondary_button_link", 'order')
        
        code, resp = get_banner(fields)
        print(f"✅ Banner API调用成功: code={code}")
        print(f"   返回数据: {len(resp.get('items', []))} 个Banner")
        
        return True
        
    except Exception as e:
        print(f"❌ Banner API调用失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始Redis和API测试...")
    
    # 测试Redis连接
    redis_ok = test_redis_connection()
    
    # 测试Banner API
    banner_ok = test_banner_api()
    
    print("\n" + "="*50)
    print("📊 测试结果总结:")
    print("="*50)
    print(f"Redis连接: {'✅ 成功' if redis_ok else '❌ 失败'}")
    print(f"Banner API: {'✅ 成功' if banner_ok else '❌ 失败'}")
    
    if redis_ok and banner_ok:
        print("\n🎉 所有测试通过！Redis连接和API都正常工作。")
        sys.exit(0)
    else:
        print("\n❌ 部分测试失败，请检查配置。")
        sys.exit(1)
