# automatically generated by the FlatBuffers compiler, do not modify

# namespace: proto

import flatbuffers

class AuthTicketWelcome(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAsAuthTicketWelcome(cls, buf, offset):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = AuthTicketWelcome()
        x.Init(buf, n + offset)
        return x

    # AuthTicketWelcome
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

def AuthTicketWelcomeStart(builder): builder.StartObject(0)
def AuthTicketWelcomeEnd(builder): return builder.EndObject()
