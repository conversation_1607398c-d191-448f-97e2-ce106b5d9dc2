import logging
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny

from steambase.views import CustomAPIView
from steambase.enums import RespCode
from sitegroup.business import (
    get_site_by_domain, check_feature_enabled, get_site_config,
    set_site_config, get_agent_sites, create_site_for_agent,
    update_site_features, get_site_analytics, clear_site_cache
)
from sitegroup.models import Site, SiteGroup
from sitegroup.serializers import (
    SiteSerializer, SiteInfoSerializer, SiteCreateSerializer,
    SiteUpdateSerializer, SiteFeaturesSerializer, SiteConfigSerializer
)

_logger = logging.getLogger(__name__)


class GetSiteInfoView(CustomAPIView):
    """获取站点信息API"""
    permission_classes = [AllowAny]
    
    @method_decorator(cache_page(60 * 30))  # 缓存30分钟
    def get(self, request):
        """获取当前域名的站点信息"""
        try:
            # 获取域名
            domain = request.get_host()
            
            # 获取站点信息
            code, data = get_site_by_domain(domain)
            
            if code == RespCode.Succeed.value:
                return self.success_response(data)
            else:
                return self.error_response(code, data.get('message', 'Failed to get site info'))
                
        except Exception as e:
            _logger.error(f"Error in GetSiteInfoView: {e}")
            return self.error_response(RespCode.ServerError.value, 'Server error')


class CheckFeatureView(CustomAPIView):
    """检查功能开关API"""
    permission_classes = [AllowAny]
    
    def get(self, request):
        """检查指定功能是否启用"""
        try:
            domain = request.get_host()
            feature = request.GET.get('feature')
            
            if not feature:
                return self.error_response(RespCode.InvalidParams.value, 'Feature parameter is required')
            
            enabled = check_feature_enabled(domain, feature)
            
            return self.success_response({
                'feature': feature,
                'enabled': enabled
            })
            
        except Exception as e:
            _logger.error(f"Error in CheckFeatureView: {e}")
            return self.error_response(RespCode.ServerError.value, 'Server error')


class SiteConfigView(CustomAPIView):
    """站点配置API"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """获取站点配置"""
        try:
            domain = request.get_host()
            config_key = request.GET.get('key')
            
            if not config_key:
                return self.error_response(RespCode.InvalidParams.value, 'Config key is required')
            
            value = get_site_config(domain, config_key)
            
            return self.success_response({
                'key': config_key,
                'value': value
            })
            
        except Exception as e:
            _logger.error(f"Error in SiteConfigView GET: {e}")
            return self.error_response(RespCode.ServerError.value, 'Server error')
    
    def post(self, request):
        """设置站点配置"""
        try:
            # 检查用户权限
            if not request.user.is_agent and not request.user.is_staff:
                return self.error_response(RespCode.PermissionDenied.value, 'Permission denied')
            
            domain = request.get_host()
            config_key = request.data.get('key')
            config_value = request.data.get('value')
            config_type = request.data.get('type', 'string')
            description = request.data.get('description', '')
            
            if not config_key or config_value is None:
                return self.error_response(RespCode.InvalidParams.value, 'Key and value are required')
            
            success = set_site_config(domain, config_key, config_value, config_type, description)
            
            if success:
                return self.success_response({'message': 'Config updated successfully'})
            else:
                return self.error_response(RespCode.BusinessError.value, 'Failed to update config')
                
        except Exception as e:
            _logger.error(f"Error in SiteConfigView POST: {e}")
            return self.error_response(RespCode.ServerError.value, 'Server error')


class AgentSitesView(CustomAPIView):
    """代理商站点管理API"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """获取代理商的所有站点"""
        try:
            if not request.user.is_agent:
                return self.error_response(RespCode.PermissionDenied.value, 'Only agents can access this endpoint')
            
            code, data = get_agent_sites(request.user)
            
            if code == RespCode.Succeed.value:
                return self.success_response(data)
            else:
                return self.error_response(code, data.get('message', 'Failed to get sites'))
                
        except Exception as e:
            _logger.error(f"Error in AgentSitesView GET: {e}")
            return self.error_response(RespCode.ServerError.value, 'Server error')
    
    def post(self, request):
        """创建新站点"""
        try:
            if not request.user.is_agent:
                return self.error_response(RespCode.PermissionDenied.value, 'Only agents can create sites')
            
            serializer = SiteCreateSerializer(data=request.data)
            if not serializer.is_valid():
                return self.error_response(RespCode.InvalidParams.value, serializer.errors)
            
            code, data = create_site_for_agent(request.user, serializer.validated_data)
            
            if code == RespCode.Succeed.value:
                return self.success_response(data)
            else:
                return self.error_response(code, data.get('message', 'Failed to create site'))
                
        except Exception as e:
            _logger.error(f"Error in AgentSitesView POST: {e}")
            return self.error_response(RespCode.ServerError.value, 'Server error')


class SiteManagementView(CustomAPIView):
    """站点管理API"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request, site_id):
        """获取站点详情"""
        try:
            site = Site.objects.filter(id=site_id).first()
            if not site:
                return self.error_response(RespCode.InvalidParams.value, 'Site not found')
            
            # 检查权限
            if not request.user.is_staff and site.site_group.agent != request.user:
                return self.error_response(RespCode.PermissionDenied.value, 'Permission denied')
            
            serializer = SiteSerializer(site)
            return self.success_response(serializer.data)
            
        except Exception as e:
            _logger.error(f"Error in SiteManagementView GET: {e}")
            return self.error_response(RespCode.ServerError.value, 'Server error')
    
    def put(self, request, site_id):
        """更新站点信息"""
        try:
            site = Site.objects.filter(id=site_id).first()
            if not site:
                return self.error_response(RespCode.InvalidParams.value, 'Site not found')
            
            # 检查权限
            if not request.user.is_staff and site.site_group.agent != request.user:
                return self.error_response(RespCode.PermissionDenied.value, 'Permission denied')
            
            serializer = SiteUpdateSerializer(site, data=request.data, partial=True)
            if not serializer.is_valid():
                return self.error_response(RespCode.InvalidParams.value, serializer.errors)
            
            serializer.save()
            
            # 清除缓存
            clear_site_cache(site.domain)
            
            return self.success_response(SiteSerializer(site).data)
            
        except Exception as e:
            _logger.error(f"Error in SiteManagementView PUT: {e}")
            return self.error_response(RespCode.ServerError.value, 'Server error')
    
    def delete(self, request, site_id):
        """删除站点"""
        try:
            site = Site.objects.filter(id=site_id).first()
            if not site:
                return self.error_response(RespCode.InvalidParams.value, 'Site not found')
            
            # 检查权限
            if not request.user.is_staff and site.site_group.agent != request.user:
                return self.error_response(RespCode.PermissionDenied.value, 'Permission denied')
            
            domain = site.domain
            site.delete()
            
            # 清除缓存
            clear_site_cache(domain)
            
            return self.success_response({'message': 'Site deleted successfully'})
            
        except Exception as e:
            _logger.error(f"Error in SiteManagementView DELETE: {e}")
            return self.error_response(RespCode.ServerError.value, 'Server error')


class SiteFeaturesView(CustomAPIView):
    """站点功能开关API"""
    permission_classes = [IsAuthenticated]
    
    def put(self, request, site_id):
        """更新站点功能开关"""
        try:
            site = Site.objects.filter(id=site_id).first()
            if not site:
                return self.error_response(RespCode.InvalidParams.value, 'Site not found')
            
            # 检查权限
            if not request.user.is_staff and site.site_group.agent != request.user:
                return self.error_response(RespCode.PermissionDenied.value, 'Permission denied')
            
            serializer = SiteFeaturesSerializer(data=request.data)
            if not serializer.is_valid():
                return self.error_response(RespCode.InvalidParams.value, serializer.errors)
            
            code, data = update_site_features(site.domain, serializer.validated_data)
            
            if code == RespCode.Succeed.value:
                return self.success_response(data)
            else:
                return self.error_response(code, data.get('message', 'Failed to update features'))
                
        except Exception as e:
            _logger.error(f"Error in SiteFeaturesView: {e}")
            return self.error_response(RespCode.ServerError.value, 'Server error')


class SiteAnalyticsView(CustomAPIView):
    """站点统计API"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request, site_id):
        """获取站点统计数据"""
        try:
            site = Site.objects.filter(id=site_id).first()
            if not site:
                return self.error_response(RespCode.InvalidParams.value, 'Site not found')
            
            # 检查权限
            if not request.user.is_staff and site.site_group.agent != request.user:
                return self.error_response(RespCode.PermissionDenied.value, 'Permission denied')
            
            start_date = request.GET.get('start_date')
            end_date = request.GET.get('end_date')
            
            code, data = get_site_analytics(site.domain, start_date, end_date)
            
            if code == RespCode.Succeed.value:
                return self.success_response(data)
            else:
                return self.error_response(code, data.get('message', 'Failed to get analytics'))
                
        except Exception as e:
            _logger.error(f"Error in SiteAnalyticsView: {e}")
            return self.error_response(RespCode.ServerError.value, 'Server error')


class ClearSiteCacheView(CustomAPIView):
    """清除站点缓存API"""
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """清除当前站点缓存"""
        try:
            if not request.user.is_staff:
                return self.error_response(RespCode.PermissionDenied.value, 'Only staff can clear cache')
            
            domain = request.get_host()
            success = clear_site_cache(domain)
            
            if success:
                return self.success_response({'message': 'Cache cleared successfully'})
            else:
                return self.error_response(RespCode.BusinessError.value, 'Failed to clear cache')
                
        except Exception as e:
            _logger.error(f"Error in ClearSiteCacheView: {e}")
            return self.error_response(RespCode.ServerError.value, 'Server error')
