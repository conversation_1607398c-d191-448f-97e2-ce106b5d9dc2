from django.conf import settings
from django.core.cache import cache
from django.db import models, transaction
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from steambase.enums import AppType, TradeRecordState, WxpWithdrawStatus
from steambase.models import ModelBase, ItemBase, USER_MODEL
from steambase.utils import ParamException
from package.models import PackageItem, ItemInfo

APP_TYPE = (
        (AppType.Dota2.value, _('Dota2')),
        (AppType.CSGO.value, _('CSGO')),
        (AppType.PUBG.value, _('PUBG')),
        (AppType.H1Z1.value, _('H1Z1')),
    )

TRADE_RECORD_STATUS = (
    (TradeRecordState.Initialed.value, _('Initialed')),
    (TradeRecordState.Active.value, _('Active')),
    (TradeRecordState.Closed.value, _('Closed')),
    (TradeRecordState.PendClose.value, _('PendClose')),
)

WXP_WITHDRAW_STATUS = (
    (WxpWithdrawStatus.WaitForBuy.value, _('waiting for user to buy more items')),
    (WxpWithdrawStatus.Proccessing.value, _('Proccessing the trade')),
    (WxpWithdrawStatus.WaitForSellConfirm.value, _('Waiting for seller to confirm')),
    (WxpWithdrawStatus.TradeSent.value, _('Trade Sent')),
    (WxpWithdrawStatus.Completed.value, _('Completed')),
    (WxpWithdrawStatus.Declined.value, _('Declined and Refunded')),
)


class TradeRecord(ModelBase):
    user = models.ForeignKey(USER_MODEL, verbose_name=_("user"), related_name='wxp_user')
    trade_url = models.CharField(_('trade url'), max_length=256)
    steam_id = models.CharField(_('steam id'), max_length=128)
    amount = models.FloatField(_('amount'), default=0.0)
    appid = models.CharField(_('appid'), max_length=64, choices=APP_TYPE, default=AppType.CSGO.value, null=True, blank=True)
    amount_used = models.FloatField(_('amount used'), default=0.0)
    drop_refund = models.FloatField(_('drop refund'), default=0.0)
    balance_refund = models.FloatField(_('balance refund'), default=0.0)
    state = models.SmallIntegerField(_('trade state'), default=TradeRecordState.Initialed.value, choices=TRADE_RECORD_STATUS)

    class Meta:
        ordering = ('-create_time',)
        verbose_name = _('Trade Record')
        verbose_name_plural = _('Trade Record')

    def __str__(self):
        return self.uid
    

class WithdrawItem(ModelBase):
    trade = models.ForeignKey(TradeRecord, verbose_name=_("trade id"), related_name='withdraw_items')
    package = models.ForeignKey(PackageItem, verbose_name=_('package item'), default=None, null=True, blank=True)
    returned = models.BooleanField(_("returned"), default=False)

    class Meta:
        ordering = ('-create_time',)
        verbose_name = _('Withdraw Item')
        verbose_name_plural = _('Withdraw Item')

    def __str__(self):
        return self.uid


class WxpTradeOffer(ModelBase):
    trade = models.ForeignKey(TradeRecord, verbose_name=_("trade id"), related_name='wxp_trades')
    project_id = models.CharField(_('project id'), max_length=64, default=None, null=True, blank=True)
    wxp_id = models.CharField(_('waxpeer id'), max_length=64, default=None, null=True, blank=True)
    wxp_item_id = models.CharField(_('waxpeer item id'), max_length=64, default=None, null=True, blank=True)
    status = models.SmallIntegerField(_('waxpeer withdraw status'), default=WxpWithdrawStatus.WaitForBuy.value, choices=WXP_WITHDRAW_STATUS)
    price = models.FloatField(_('price'), default=0.0)
    done = models.BooleanField(_("done"), default=False)
    reason = models.CharField(_("reason"), max_length=256, default=None, null=True, blank=True)
    market_name = models.CharField(_('market name'), max_length=128, default=None, null=True, blank=True)
    item_info = models.ForeignKey(ItemInfo, verbose_name=_("item info"))
    steam_trade_id = models.CharField(_('steam trade id'), max_length=64, default=None, null=True, blank=True)

    class Meta:
        ordering = ('-create_time',)
        verbose_name = _('Wxp Trade Offer')
        verbose_name_plural = _('Wxp Trade Offer')

    def __str__(self):
        return self.uid


class TradeStatisticsDay(ModelBase):
    APP_TYPE = (
        (AppType.Dota2.value, _('Dota2')),
        (AppType.CSGO.value, _('CSGO')),
        (AppType.PUBG.value, _('PUBG')),
        (AppType.H1Z1.value, _('H1Z1')),
    )

    date = models.DateField(_("date"), default=timezone.now)
    amount = models.FloatField(_("amount"), default=0.0)
    test_amount = models.FloatField(_("test amount"), default=0.0)
    appid = models.CharField(_('appid'), max_length=64, choices=APP_TYPE, default=AppType.CSGO.value, null=True, blank=True)

    class Meta:
        ordering = ('-create_time',)
        verbose_name = _('Trade Statistics Day')
        verbose_name_plural = _('Trade Statistics Day')

    @classmethod
    def update_amount(cls, amount, appid):
        with transaction.atomic():
            today = timezone.localdate()
            if not cls.objects.filter(date=today, appid=appid):
                cls.objects.create(date=today, appid=appid)
            record = cls.objects.select_for_update().get(date=today, appid=appid)
            amount = round(amount, 2)
            record.amount += amount
            record.save()

    @classmethod
    def update_test_amount(cls, amount, appid):
        with transaction.atomic():
            today = timezone.localdate()
            if not cls.objects.filter(date=today, appid=appid):
                cls.objects.create(date=today, appid=appid)
            record = cls.objects.select_for_update().get(date=today, appid=appid)
            amount = round(amount, 2)
            record.test_amount += amount
            record.save()

    def __str__(self):
        return str(self.date)


class TradeStatisticsMonth(ModelBase):
    APP_TYPE = (
        (AppType.Dota2.value, _('Dota2')),
        (AppType.CSGO.value, _('CSGO')),
        (AppType.PUBG.value, _('PUBG')),
        (AppType.H1Z1.value, _('H1Z1')),
    )
    date = models.DateField(_("date"), default=timezone.now)
    amount = models.FloatField(_("amount"), default=0.0)
    test_amount = models.FloatField(_("test amount"), default=0.0)
    appid = models.CharField(_('appid'), max_length=64, choices=APP_TYPE, default=AppType.CSGO.value, null=True, blank=True)

    class Meta:
        ordering = ('-create_time',)
        verbose_name = _('Trade Statistics Month')
        verbose_name_plural = _('Trade Statistics Month')


    @classmethod
    def update_amount(cls, amount, appid):
        with transaction.atomic():
            today = timezone.localdate()
            month_day_first = today.replace(day=1)
            if not cls.objects.filter(date=month_day_first, appid=appid):
                cls.objects.create(date=month_day_first, appid=appid)
            record = cls.objects.select_for_update().get(date=month_day_first, appid=appid)
            amount = round(amount, 2)
            record.amount += amount
            record.save()

    @classmethod
    def update_test_amount(cls, amount, appid):
        with transaction.atomic():
            today = timezone.localdate()
            month_day_first = today.replace(day=1)
            if not cls.objects.filter(date=month_day_first, appid=appid):
                cls.objects.create(date=month_day_first, appid=appid)
            record = cls.objects.select_for_update().get(date=month_day_first, appid=appid)
            amount = round(amount, 2)
            record.test_amount += amount
            record.save()

    def __str__(self):
        return self.month()

    def month(self):
        if isinstance(self.date, str):
            return self.date
        return self.date.strftime("%Y-%m")
    month.short_description = _('Month')