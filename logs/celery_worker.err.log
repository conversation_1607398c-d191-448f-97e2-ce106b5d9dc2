ERROR:celery.utils.dispatch.signal:Signal handler <bound method DjangoFixup.on_import_modules of <celery.fixups.django.DjangoFixup object at 0x7f8007ecc9d0>> raised: ModuleNotFoundError("No module named 'django_ckeditor_5'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 91, in populate
    app_config = AppConfig.create(entry)
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 224, in create
    import_module(entry)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 973, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'django_ckeditor_5'
ERROR:celery.utils.dispatch.signal:Signal handler <promise@0x7f8007ee8900 --> <bound method Celery._autodiscover_tasks of <Celery steambase at 0x7f8008ad0340>>> raised: AppRegistryNotReady("Apps aren't loaded yet.")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 161, in __call__
    return self.throw()
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 158, in __call__
    retval = fun(*final_args, **final_kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 788, in _autodiscover_tasks
    return self._autodiscover_tasks_from_fixups(related_name)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 797, in _autodiscover_tasks_from_fixups
    return self._autodiscover_tasks_from_names([
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 800, in <listcomp>
    for pkg in fixup.autodiscover_tasks()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 111, in autodiscover_tasks
    return [config.name for config in apps.get_app_configs()]
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 145, in get_app_configs
    self.check_apps_ready()
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 136, in check_apps_ready
    raise AppRegistryNotReady("Apps aren't loaded yet.")
django.core.exceptions.AppRegistryNotReady: Apps aren't loaded yet.
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/runpy.py", line 194, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/local/lib/python3.8/runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 19, in <module>
    main()
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 15, in main
    sys.exit(_main())
  File "/usr/local/lib/python3.8/site-packages/celery/bin/celery.py", line 231, in main
    return celery(auto_envvar_prefix="CELERY")
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1082, in main
    rv = self.invoke(ctx)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 788, in invoke
    return __callback(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/decorators.py", line 33, in new_func
    return f(get_current_context(), *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/base.py", line 135, in caller
    return f(ctx, *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/worker.py", line 348, in worker
    worker = app.Worker(
  File "/usr/local/lib/python3.8/site-packages/celery/worker/worker.py", line 94, in __init__
    self.app.loader.init_worker()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 110, in init_worker
    self.import_default_modules()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 104, in import_default_modules
    raise response
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 91, in populate
    app_config = AppConfig.create(entry)
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 224, in create
    import_module(entry)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 973, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'django_ckeditor_5'
ERROR:celery.utils.dispatch.signal:Signal handler <bound method DjangoFixup.on_import_modules of <celery.fixups.django.DjangoFixup object at 0x7f193f7539d0>> raised: ModuleNotFoundError("No module named 'django_ckeditor_5'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 91, in populate
    app_config = AppConfig.create(entry)
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 224, in create
    import_module(entry)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 973, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'django_ckeditor_5'
ERROR:celery.utils.dispatch.signal:Signal handler <promise@0x7f193f76f900 --> <bound method Celery._autodiscover_tasks of <Celery steambase at 0x7f19402fe340>>> raised: AppRegistryNotReady("Apps aren't loaded yet.")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 161, in __call__
    return self.throw()
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 158, in __call__
    retval = fun(*final_args, **final_kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 788, in _autodiscover_tasks
    return self._autodiscover_tasks_from_fixups(related_name)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 797, in _autodiscover_tasks_from_fixups
    return self._autodiscover_tasks_from_names([
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 800, in <listcomp>
    for pkg in fixup.autodiscover_tasks()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 111, in autodiscover_tasks
    return [config.name for config in apps.get_app_configs()]
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 145, in get_app_configs
    self.check_apps_ready()
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 136, in check_apps_ready
    raise AppRegistryNotReady("Apps aren't loaded yet.")
django.core.exceptions.AppRegistryNotReady: Apps aren't loaded yet.
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/runpy.py", line 194, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/local/lib/python3.8/runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 19, in <module>
    main()
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 15, in main
    sys.exit(_main())
  File "/usr/local/lib/python3.8/site-packages/celery/bin/celery.py", line 231, in main
    return celery(auto_envvar_prefix="CELERY")
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1082, in main
    rv = self.invoke(ctx)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 788, in invoke
    return __callback(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/decorators.py", line 33, in new_func
    return f(get_current_context(), *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/base.py", line 135, in caller
    return f(ctx, *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/worker.py", line 348, in worker
    worker = app.Worker(
  File "/usr/local/lib/python3.8/site-packages/celery/worker/worker.py", line 94, in __init__
    self.app.loader.init_worker()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 110, in init_worker
    self.import_default_modules()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 104, in import_default_modules
    raise response
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 91, in populate
    app_config = AppConfig.create(entry)
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 224, in create
    import_module(entry)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 973, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'django_ckeditor_5'
ERROR:celery.utils.dispatch.signal:Signal handler <bound method DjangoFixup.on_import_modules of <celery.fixups.django.DjangoFixup object at 0x7feecf7539d0>> raised: ModuleNotFoundError("No module named 'django_ckeditor_5'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 91, in populate
    app_config = AppConfig.create(entry)
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 224, in create
    import_module(entry)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 973, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'django_ckeditor_5'
ERROR:celery.utils.dispatch.signal:Signal handler <promise@0x7feecf76f900 --> <bound method Celery._autodiscover_tasks of <Celery steambase at 0x7feed02ec340>>> raised: AppRegistryNotReady("Apps aren't loaded yet.")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 161, in __call__
    return self.throw()
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 158, in __call__
    retval = fun(*final_args, **final_kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 788, in _autodiscover_tasks
    return self._autodiscover_tasks_from_fixups(related_name)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 797, in _autodiscover_tasks_from_fixups
    return self._autodiscover_tasks_from_names([
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 800, in <listcomp>
    for pkg in fixup.autodiscover_tasks()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 111, in autodiscover_tasks
    return [config.name for config in apps.get_app_configs()]
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 145, in get_app_configs
    self.check_apps_ready()
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 136, in check_apps_ready
    raise AppRegistryNotReady("Apps aren't loaded yet.")
django.core.exceptions.AppRegistryNotReady: Apps aren't loaded yet.
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/runpy.py", line 194, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/local/lib/python3.8/runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 19, in <module>
    main()
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 15, in main
    sys.exit(_main())
  File "/usr/local/lib/python3.8/site-packages/celery/bin/celery.py", line 231, in main
    return celery(auto_envvar_prefix="CELERY")
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1082, in main
    rv = self.invoke(ctx)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 788, in invoke
    return __callback(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/decorators.py", line 33, in new_func
    return f(get_current_context(), *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/base.py", line 135, in caller
    return f(ctx, *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/worker.py", line 348, in worker
    worker = app.Worker(
  File "/usr/local/lib/python3.8/site-packages/celery/worker/worker.py", line 94, in __init__
    self.app.loader.init_worker()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 110, in init_worker
    self.import_default_modules()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 104, in import_default_modules
    raise response
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 91, in populate
    app_config = AppConfig.create(entry)
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 224, in create
    import_module(entry)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 973, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'django_ckeditor_5'
ERROR:celery.utils.dispatch.signal:Signal handler <bound method DjangoFixup.on_import_modules of <celery.fixups.django.DjangoFixup object at 0x7f192a5139d0>> raised: ModuleNotFoundError("No module named 'django_ckeditor_5'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 91, in populate
    app_config = AppConfig.create(entry)
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 224, in create
    import_module(entry)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 973, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'django_ckeditor_5'
ERROR:celery.utils.dispatch.signal:Signal handler <promise@0x7f192a52f900 --> <bound method Celery._autodiscover_tasks of <Celery steambase at 0x7f192b099340>>> raised: AppRegistryNotReady("Apps aren't loaded yet.")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 161, in __call__
    return self.throw()
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 158, in __call__
    retval = fun(*final_args, **final_kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 788, in _autodiscover_tasks
    return self._autodiscover_tasks_from_fixups(related_name)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 797, in _autodiscover_tasks_from_fixups
    return self._autodiscover_tasks_from_names([
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 800, in <listcomp>
    for pkg in fixup.autodiscover_tasks()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 111, in autodiscover_tasks
    return [config.name for config in apps.get_app_configs()]
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 145, in get_app_configs
    self.check_apps_ready()
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 136, in check_apps_ready
    raise AppRegistryNotReady("Apps aren't loaded yet.")
django.core.exceptions.AppRegistryNotReady: Apps aren't loaded yet.
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/runpy.py", line 194, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/local/lib/python3.8/runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 19, in <module>
    main()
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 15, in main
    sys.exit(_main())
  File "/usr/local/lib/python3.8/site-packages/celery/bin/celery.py", line 231, in main
    return celery(auto_envvar_prefix="CELERY")
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1082, in main
    rv = self.invoke(ctx)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 788, in invoke
    return __callback(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/decorators.py", line 33, in new_func
    return f(get_current_context(), *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/base.py", line 135, in caller
    return f(ctx, *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/worker.py", line 348, in worker
    worker = app.Worker(
  File "/usr/local/lib/python3.8/site-packages/celery/worker/worker.py", line 94, in __init__
    self.app.loader.init_worker()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 110, in init_worker
    self.import_default_modules()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 104, in import_default_modules
    raise response
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 91, in populate
    app_config = AppConfig.create(entry)
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 224, in create
    import_module(entry)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 973, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'django_ckeditor_5'
DEBUG:asyncio:Using selector: EpollSelector
ERROR:celery.utils.dispatch.signal:Signal handler <bound method DjangoFixup.on_import_modules of <celery.fixups.django.DjangoFixup object at 0x7fa3942609d0>> raised: ModuleNotFoundError("No module named 'aenum'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
ERROR:celery.utils.dispatch.signal:Signal handler <promise@0x7fa39427c900 --> <bound method Celery._autodiscover_tasks of <Celery steambase at 0x7fa3944e1340>>> raised: ModuleNotFoundError("No module named 'aenum'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 161, in __call__
    return self.throw()
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 158, in __call__
    retval = fun(*final_args, **final_kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 788, in _autodiscover_tasks
    return self._autodiscover_tasks_from_fixups(related_name)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 797, in _autodiscover_tasks_from_fixups
    return self._autodiscover_tasks_from_names([
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 792, in _autodiscover_tasks_from_names
    return self.loader.autodiscover_tasks(
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 220, in autodiscover_tasks
    mod.__name__ for mod in autodiscover_tasks(packages or (),
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 246, in autodiscover_tasks
    return [find_related_module(pkg, related_name) for pkg in packages]
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 246, in <listcomp>
    return [find_related_module(pkg, related_name) for pkg in packages]
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 278, in find_related_module
    raise e
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 270, in find_related_module
    return importlib.import_module(module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/tasks.py", line 7, in <module>
    from authentication.models import UserBalanceRecord, UserAsset, AuthUser, UserExtra, UserActivePointRecord
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/runpy.py", line 194, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/local/lib/python3.8/runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 19, in <module>
    main()
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 15, in main
    sys.exit(_main())
  File "/usr/local/lib/python3.8/site-packages/celery/bin/celery.py", line 231, in main
    return celery(auto_envvar_prefix="CELERY")
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1082, in main
    rv = self.invoke(ctx)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 788, in invoke
    return __callback(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/decorators.py", line 33, in new_func
    return f(get_current_context(), *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/base.py", line 135, in caller
    return f(ctx, *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/worker.py", line 348, in worker
    worker = app.Worker(
  File "/usr/local/lib/python3.8/site-packages/celery/worker/worker.py", line 94, in __init__
    self.app.loader.init_worker()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 110, in init_worker
    self.import_default_modules()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 104, in import_default_modules
    raise response
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
DEBUG:asyncio:Using selector: EpollSelector
ERROR:celery.utils.dispatch.signal:Signal handler <bound method DjangoFixup.on_import_modules of <celery.fixups.django.DjangoFixup object at 0x7f8b5b05f9d0>> raised: ModuleNotFoundError("No module named 'aenum'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
ERROR:celery.utils.dispatch.signal:Signal handler <promise@0x7f8b5b07c900 --> <bound method Celery._autodiscover_tasks of <Celery steambase at 0x7f8b5b1e0340>>> raised: ModuleNotFoundError("No module named 'aenum'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 161, in __call__
    return self.throw()
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 158, in __call__
    retval = fun(*final_args, **final_kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 788, in _autodiscover_tasks
    return self._autodiscover_tasks_from_fixups(related_name)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 797, in _autodiscover_tasks_from_fixups
    return self._autodiscover_tasks_from_names([
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 792, in _autodiscover_tasks_from_names
    return self.loader.autodiscover_tasks(
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 220, in autodiscover_tasks
    mod.__name__ for mod in autodiscover_tasks(packages or (),
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 246, in autodiscover_tasks
    return [find_related_module(pkg, related_name) for pkg in packages]
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 246, in <listcomp>
    return [find_related_module(pkg, related_name) for pkg in packages]
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 278, in find_related_module
    raise e
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 270, in find_related_module
    return importlib.import_module(module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/tasks.py", line 7, in <module>
    from authentication.models import UserBalanceRecord, UserAsset, AuthUser, UserExtra, UserActivePointRecord
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/runpy.py", line 194, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/local/lib/python3.8/runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 19, in <module>
    main()
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 15, in main
    sys.exit(_main())
  File "/usr/local/lib/python3.8/site-packages/celery/bin/celery.py", line 231, in main
    return celery(auto_envvar_prefix="CELERY")
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1082, in main
    rv = self.invoke(ctx)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 788, in invoke
    return __callback(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/decorators.py", line 33, in new_func
    return f(get_current_context(), *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/base.py", line 135, in caller
    return f(ctx, *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/worker.py", line 348, in worker
    worker = app.Worker(
  File "/usr/local/lib/python3.8/site-packages/celery/worker/worker.py", line 94, in __init__
    self.app.loader.init_worker()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 110, in init_worker
    self.import_default_modules()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 104, in import_default_modules
    raise response
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
DEBUG:asyncio:Using selector: EpollSelector
ERROR:celery.utils.dispatch.signal:Signal handler <bound method DjangoFixup.on_import_modules of <celery.fixups.django.DjangoFixup object at 0x7f1b4f67e9d0>> raised: ModuleNotFoundError("No module named 'aenum'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
ERROR:celery.utils.dispatch.signal:Signal handler <promise@0x7f1b4f69c900 --> <bound method Celery._autodiscover_tasks of <Celery steambase at 0x7f1b4f800340>>> raised: ModuleNotFoundError("No module named 'aenum'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 161, in __call__
    return self.throw()
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 158, in __call__
    retval = fun(*final_args, **final_kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 788, in _autodiscover_tasks
    return self._autodiscover_tasks_from_fixups(related_name)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 797, in _autodiscover_tasks_from_fixups
    return self._autodiscover_tasks_from_names([
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 792, in _autodiscover_tasks_from_names
    return self.loader.autodiscover_tasks(
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 220, in autodiscover_tasks
    mod.__name__ for mod in autodiscover_tasks(packages or (),
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 246, in autodiscover_tasks
    return [find_related_module(pkg, related_name) for pkg in packages]
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 246, in <listcomp>
    return [find_related_module(pkg, related_name) for pkg in packages]
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 278, in find_related_module
    raise e
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 270, in find_related_module
    return importlib.import_module(module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/tasks.py", line 7, in <module>
    from authentication.models import UserBalanceRecord, UserAsset, AuthUser, UserExtra, UserActivePointRecord
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/runpy.py", line 194, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/local/lib/python3.8/runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 19, in <module>
    main()
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 15, in main
    sys.exit(_main())
  File "/usr/local/lib/python3.8/site-packages/celery/bin/celery.py", line 231, in main
    return celery(auto_envvar_prefix="CELERY")
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1082, in main
    rv = self.invoke(ctx)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 788, in invoke
    return __callback(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/decorators.py", line 33, in new_func
    return f(get_current_context(), *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/base.py", line 135, in caller
    return f(ctx, *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/worker.py", line 348, in worker
    worker = app.Worker(
  File "/usr/local/lib/python3.8/site-packages/celery/worker/worker.py", line 94, in __init__
    self.app.loader.init_worker()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 110, in init_worker
    self.import_default_modules()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 104, in import_default_modules
    raise response
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
DEBUG:asyncio:Using selector: EpollSelector
ERROR:celery.utils.dispatch.signal:Signal handler <bound method DjangoFixup.on_import_modules of <celery.fixups.django.DjangoFixup object at 0x7f2c338589d0>> raised: ModuleNotFoundError("No module named 'aenum'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
ERROR:celery.utils.dispatch.signal:Signal handler <promise@0x7f2c33874900 --> <bound method Celery._autodiscover_tasks of <Celery steambase at 0x7f2c339d9340>>> raised: ModuleNotFoundError("No module named 'aenum'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 161, in __call__
    return self.throw()
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 158, in __call__
    retval = fun(*final_args, **final_kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 788, in _autodiscover_tasks
    return self._autodiscover_tasks_from_fixups(related_name)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 797, in _autodiscover_tasks_from_fixups
    return self._autodiscover_tasks_from_names([
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 792, in _autodiscover_tasks_from_names
    return self.loader.autodiscover_tasks(
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 220, in autodiscover_tasks
    mod.__name__ for mod in autodiscover_tasks(packages or (),
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 246, in autodiscover_tasks
    return [find_related_module(pkg, related_name) for pkg in packages]
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 246, in <listcomp>
    return [find_related_module(pkg, related_name) for pkg in packages]
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 278, in find_related_module
    raise e
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 270, in find_related_module
    return importlib.import_module(module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/tasks.py", line 7, in <module>
    from authentication.models import UserBalanceRecord, UserAsset, AuthUser, UserExtra, UserActivePointRecord
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/runpy.py", line 194, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/local/lib/python3.8/runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 19, in <module>
    main()
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 15, in main
    sys.exit(_main())
  File "/usr/local/lib/python3.8/site-packages/celery/bin/celery.py", line 231, in main
    return celery(auto_envvar_prefix="CELERY")
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1082, in main
    rv = self.invoke(ctx)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 788, in invoke
    return __callback(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/decorators.py", line 33, in new_func
    return f(get_current_context(), *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/base.py", line 135, in caller
    return f(ctx, *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/worker.py", line 348, in worker
    worker = app.Worker(
  File "/usr/local/lib/python3.8/site-packages/celery/worker/worker.py", line 94, in __init__
    self.app.loader.init_worker()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 110, in init_worker
    self.import_default_modules()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 104, in import_default_modules
    raise response
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
DEBUG:asyncio:Using selector: EpollSelector
ERROR:celery.utils.dispatch.signal:Signal handler <bound method DjangoFixup.on_import_modules of <celery.fixups.django.DjangoFixup object at 0x7f6c9f82b9d0>> raised: ModuleNotFoundError("No module named 'aenum'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
ERROR:celery.utils.dispatch.signal:Signal handler <promise@0x7f6c9f848900 --> <bound method Celery._autodiscover_tasks of <Celery steambase at 0x7f6c9f9ac340>>> raised: ModuleNotFoundError("No module named 'aenum'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 161, in __call__
    return self.throw()
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 158, in __call__
    retval = fun(*final_args, **final_kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 788, in _autodiscover_tasks
    return self._autodiscover_tasks_from_fixups(related_name)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 797, in _autodiscover_tasks_from_fixups
    return self._autodiscover_tasks_from_names([
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 792, in _autodiscover_tasks_from_names
    return self.loader.autodiscover_tasks(
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 220, in autodiscover_tasks
    mod.__name__ for mod in autodiscover_tasks(packages or (),
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 246, in autodiscover_tasks
    return [find_related_module(pkg, related_name) for pkg in packages]
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 246, in <listcomp>
    return [find_related_module(pkg, related_name) for pkg in packages]
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 278, in find_related_module
    raise e
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 270, in find_related_module
    return importlib.import_module(module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/tasks.py", line 7, in <module>
    from authentication.models import UserBalanceRecord, UserAsset, AuthUser, UserExtra, UserActivePointRecord
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/runpy.py", line 194, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/local/lib/python3.8/runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 19, in <module>
    main()
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 15, in main
    sys.exit(_main())
  File "/usr/local/lib/python3.8/site-packages/celery/bin/celery.py", line 231, in main
    return celery(auto_envvar_prefix="CELERY")
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1082, in main
    rv = self.invoke(ctx)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 788, in invoke
    return __callback(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/decorators.py", line 33, in new_func
    return f(get_current_context(), *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/base.py", line 135, in caller
    return f(ctx, *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/worker.py", line 348, in worker
    worker = app.Worker(
  File "/usr/local/lib/python3.8/site-packages/celery/worker/worker.py", line 94, in __init__
    self.app.loader.init_worker()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 110, in init_worker
    self.import_default_modules()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 104, in import_default_modules
    raise response
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
DEBUG:asyncio:Using selector: EpollSelector
ERROR:celery.utils.dispatch.signal:Signal handler <bound method DjangoFixup.on_import_modules of <celery.fixups.django.DjangoFixup object at 0x7f05164d39d0>> raised: ModuleNotFoundError("No module named 'aenum'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
ERROR:celery.utils.dispatch.signal:Signal handler <promise@0x7f05164f0900 --> <bound method Celery._autodiscover_tasks of <Celery steambase at 0x7f051707b340>>> raised: ModuleNotFoundError("No module named 'aenum'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 161, in __call__
    return self.throw()
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 158, in __call__
    retval = fun(*final_args, **final_kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 788, in _autodiscover_tasks
    return self._autodiscover_tasks_from_fixups(related_name)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 797, in _autodiscover_tasks_from_fixups
    return self._autodiscover_tasks_from_names([
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 792, in _autodiscover_tasks_from_names
    return self.loader.autodiscover_tasks(
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 220, in autodiscover_tasks
    mod.__name__ for mod in autodiscover_tasks(packages or (),
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 246, in autodiscover_tasks
    return [find_related_module(pkg, related_name) for pkg in packages]
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 246, in <listcomp>
    return [find_related_module(pkg, related_name) for pkg in packages]
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 278, in find_related_module
    raise e
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 270, in find_related_module
    return importlib.import_module(module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/tasks.py", line 7, in <module>
    from authentication.models import UserBalanceRecord, UserAsset, AuthUser, UserExtra, UserActivePointRecord
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/runpy.py", line 194, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/local/lib/python3.8/runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 19, in <module>
    main()
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 15, in main
    sys.exit(_main())
  File "/usr/local/lib/python3.8/site-packages/celery/bin/celery.py", line 231, in main
    return celery(auto_envvar_prefix="CELERY")
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1082, in main
    rv = self.invoke(ctx)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 788, in invoke
    return __callback(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/decorators.py", line 33, in new_func
    return f(get_current_context(), *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/base.py", line 135, in caller
    return f(ctx, *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/worker.py", line 348, in worker
    worker = app.Worker(
  File "/usr/local/lib/python3.8/site-packages/celery/worker/worker.py", line 94, in __init__
    self.app.loader.init_worker()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 110, in init_worker
    self.import_default_modules()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 104, in import_default_modules
    raise response
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
DEBUG:asyncio:Using selector: EpollSelector
ERROR:celery.utils.dispatch.signal:Signal handler <bound method DjangoFixup.on_import_modules of <celery.fixups.django.DjangoFixup object at 0x7f91482ba9d0>> raised: ModuleNotFoundError("No module named 'aenum'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
ERROR:celery.utils.dispatch.signal:Signal handler <promise@0x7f91482d7900 --> <bound method Celery._autodiscover_tasks of <Celery steambase at 0x7f914843b340>>> raised: ModuleNotFoundError("No module named 'aenum'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 161, in __call__
    return self.throw()
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 158, in __call__
    retval = fun(*final_args, **final_kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 788, in _autodiscover_tasks
    return self._autodiscover_tasks_from_fixups(related_name)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 797, in _autodiscover_tasks_from_fixups
    return self._autodiscover_tasks_from_names([
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 792, in _autodiscover_tasks_from_names
    return self.loader.autodiscover_tasks(
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 220, in autodiscover_tasks
    mod.__name__ for mod in autodiscover_tasks(packages or (),
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 246, in autodiscover_tasks
    return [find_related_module(pkg, related_name) for pkg in packages]
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 246, in <listcomp>
    return [find_related_module(pkg, related_name) for pkg in packages]
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 278, in find_related_module
    raise e
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 270, in find_related_module
    return importlib.import_module(module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/tasks.py", line 7, in <module>
    from authentication.models import UserBalanceRecord, UserAsset, AuthUser, UserExtra, UserActivePointRecord
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/runpy.py", line 194, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/local/lib/python3.8/runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 19, in <module>
    main()
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 15, in main
    sys.exit(_main())
  File "/usr/local/lib/python3.8/site-packages/celery/bin/celery.py", line 231, in main
    return celery(auto_envvar_prefix="CELERY")
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1082, in main
    rv = self.invoke(ctx)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 788, in invoke
    return __callback(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/decorators.py", line 33, in new_func
    return f(get_current_context(), *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/base.py", line 135, in caller
    return f(ctx, *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/worker.py", line 348, in worker
    worker = app.Worker(
  File "/usr/local/lib/python3.8/site-packages/celery/worker/worker.py", line 94, in __init__
    self.app.loader.init_worker()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 110, in init_worker
    self.import_default_modules()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 104, in import_default_modules
    raise response
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
