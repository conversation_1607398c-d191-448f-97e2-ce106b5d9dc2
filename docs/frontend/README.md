# 前端开发文档

本目录包含前端开发相关的技术文档和实现指南。

## 📋 文档列表

- [**前端总览**](overview.md) - 技术栈、开发规范、项目结构
- [**开箱动画实现**](case-opening-real-animation.md) - 老虎机滚轮式开箱动画完整方案
- [**对战动画实现**](frontend-battle-animation.md) - 实时对战动画系统设计

## 🎨 核心特性

### 动画系统
- **老虎机滚轮式动画** - 统一的开箱和对战动画风格
- **GSAP动画引擎** - 高性能的动画实现
- **响应式设计** - 适配PC和移动端
- **音效系统** - 沉浸式的音频体验

### 技术栈
- **动画引擎**: GSAP (GreenSock Animation Platform)
- **CSS预处理器**: SCSS
- **单位系统**: rem（根元素相对单位）
- **设计模式**: CSS原子化、组件化

### 开发规范
- **CSS类命名**: BEM命名规范
- **响应式断点**: 768px (移动端)、1024px (平板)、1440px (桌面)
- **动画性能**: 优先使用transform和opacity属性
- **浏览器兼容**: 支持Chrome 70+、Firefox 65+、Safari 12+

## 🚀 快速开始

### 1. 环境准备
```bash
# 安装依赖
npm install gsap

# 或使用CDN
<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
```

### 2. 基础使用
```javascript
// 初始化开箱动画
const container = document.querySelector('.slot-machine-container');
const animation = new SlotMachineAnimation(container);

// 开始开箱
animation.startSpin(caseId);
```

### 3. 自定义配置
```javascript
const options = {
    reelsCount: 3,
    spinDuration: 4000,
    staggerDelay: 500
};
const animation = new SlotMachineAnimation(container, options);
```

## 📱 响应式设计

### 断点设置
```scss
// 移动端
@media (max-width: 768px) {
    .slot-machine { transform: scale(0.8); }
}

// 平板端
@media (min-width: 769px) and (max-width: 1024px) {
    .slot-machine { transform: scale(0.9); }
}

// 桌面端
@media (min-width: 1025px) {
    .slot-machine { transform: scale(1); }
}
```

### rem单位使用
```scss
// 基础字体大小: 16px
html { font-size: 16px; }

// 组件尺寸使用rem
.slot-machine {
    width: 50rem;  // 800px
    height: 30rem; // 480px
}
```

## 🎵 音效集成

### 音效文件
```
/static/audio/
├── slot_spin_start.mp3     # 开始旋转
├── slot_reel_spin.mp3      # 滚轮旋转
├── slot_reel_stop.mp3      # 滚轮停止
├── slot_win_line.mp3       # 中奖线激活
└── slot_win_reveal.mp3     # 结果揭晓
```

### 使用示例
```javascript
const audioManager = new AudioManager();
audioManager.play('slot_spin_start');
```

## 🔧 性能优化

### 1. 动画优化
- 使用`will-change: transform`预告浏览器优化
- 避免同时运行过多动画
- 及时清理动画监听器

### 2. 资源管理
- 预加载音频文件
- 图片懒加载
- 复用DOM元素

### 3. 内存管理
- 动画结束后清理对象
- 控制粒子效果数量
- 避免内存泄漏

---

*更新时间: 2025-06-18*
