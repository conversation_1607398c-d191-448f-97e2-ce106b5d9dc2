from django.db import models, transaction
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from steambase.enums import GameState, RollType, CaseItemType
from steambase.models import ModelBase, USER_MODEL, ItemBase
from package.models import PackageItem,ItemInfo


class RollRoom(ModelBase):
    GAME_STATE = (
        (GameState.Initial.value, _('Initial')),
        (GameState.Joinable.value, _('Joinable')),
        (GameState.Joining.value, _('Joining')),
        (GameState.Full.value, _('Full')),
        (GameState.Running.value, _('Running')),
        (GameState.End.value, _('End')),
        (GameState.Cancelled.value, _('Cancelled'))
    )
    ROLL_TYPE = (
        (RollType.Nocharge.value, _('No charge room')),
        (RollType.Charge.value, _('Charge room'))
    )
    OFFICIAL = (
        (0, _("主播")),
        (1, _("官方"))
    )
    user = models.ForeignKey(USER_MODEL, verbose_name=_("user"), related_name='roll_room')
    type = models.SmallIntegerField(_("type"), default=RollType.Nocharge.value, choices=ROLL_TYPE)
    name = models.CharField(_('name'), max_length=64)
    desc = models.CharField(_('description'), max_length=1024, default=None, null=True, blank=True)
    password = models.CharField(_('password'), max_length=64, default=None, null=True, blank=True)
    fee = models.FloatField(_("entrance fee"), default=0.0)
    min_joiner = models.IntegerField(_('min joiner'), default=0)
    max_joiner = models.IntegerField(_('max joiner'), default=0)
    max_winner = models.IntegerField(_('max winner'), default=0)
    begin_ts = models.DateTimeField(_("begin time"), default=timezone.now)
    due_ts = models.DateTimeField(_("due time"), default=timezone.now)
    win_ts = models.DateTimeField(_("win time"), default=None, null=True, blank=True)
    total_fee = models.FloatField(_("total fee"), default=0)
    pump_amount = models.FloatField(_('pump amount'), default=0)
    total_amount = models.FloatField(_("total amount"), default=0)
    items_count = models.IntegerField(_("total items count"), default=0)
    state = models.SmallIntegerField(_("status"), default=GameState.Initial.value, choices=GAME_STATE)
    deposit_enable = models.BooleanField(_("deposit enable"), default=False)
    enable = models.BooleanField(_("rollroom enable"), default=True)
    charge_limit = models.FloatField(_("charge limit amount"), default=0, null=True, blank=True)
    charge_begin_time = models.DateTimeField(_("charge begin time"), default=None, null=True, blank=True)
    charge_end_time = models.DateTimeField(_("charge end time"), default=None, null=True, blank=True)
    official = models.SmallIntegerField(_("official"), default=0, choices=OFFICIAL)

    # 增加一个字段用于调试
    debug = models.BooleanField(_("debug"), default=False)

    @classmethod
    def update_state(cls, uid, origin_state, target_state):
        with transaction.atomic():
            room = cls.objects.select_for_update().get(uid=uid, state=origin_state)
            if not room:
                raise Exception(_('Invalid room'))
            room.state = target_state
            room.save()
            return room

    def __str__(self):
        return str(self.name)

    class Meta:
        ordering = ('-create_time',)
        verbose_name = _("Roll Room")
        verbose_name_plural = _("Roll Room")


class RollRoomBet(ModelBase):
    user = models.ForeignKey(USER_MODEL, verbose_name=_("user"), related_name='roll_bets')
    room = models.ForeignKey(RollRoom, verbose_name=_("room"), related_name='bets', on_delete=models.CASCADE)
    win_amount = models.FloatField(_("win amount"), default=0)
    win_items_count = models.IntegerField(_("win items count"), default=0)
    victory = models.BooleanField(_("victory"), default=False)

    def __str__(self):
        return str(self.user.username)

    class Meta:
        ordering = ('-create_time',)
        verbose_name = _('Roll Room Bet')
        verbose_name_plural = _('Roll Room Bet')


class RollRoomItem(ModelBase):
    ITEM_TYPE = (
        (CaseItemType.Assets.value, _('Assets')),
        (CaseItemType.Balance.value, _('Balance'))
    )
    room = models.ForeignKey(RollRoom, verbose_name=_("room"), related_name='items', on_delete=models.CASCADE)
    bet = models.ForeignKey(RollRoomBet, verbose_name=_('win roll bet'), related_name='win_items', null=True,
                            default=None, blank=True)
    
    price = models.FloatField(_('price'), default=0)
    balance = models.FloatField(_('balance'), default=0)
    item_type = models.SmallIntegerField(_('item type'), default=1, choices=ITEM_TYPE)

    # 新增饰品ID
    item_id = models.CharField(_('Item ID'), max_length=128, default=None, null=True, blank=True)

    class Meta:
        verbose_name = _('Roll Room Item')
        verbose_name_plural = _('Roll Room Item')

    def __str__(self):
        if self.item_id:
            # 使用 filter 筛选 id 来避免异常，确保不会因没有找到而中断程序
            item_info = ItemInfo.objects.filter(id=self.item_id).first()  # 使用 id 来筛选
            
            if item_info:
                return item_info.market_name_cn or item_info.market_name  # 返回中文名称或英文名称
            else:
                return f"Item {self.item_id} not found"  # 如果没有找到 ItemInfo，返回自定义的提示

        return str(self.id)  # 如果没有 item_id，返回默认的 id


class RollRoomPumpDay(ModelBase):
    date = models.DateField(_("date"), default=timezone.now)
    amount = models.FloatField(_("amount"), default=0)

    @classmethod
    def update_amount(cls, amount):
        with transaction.atomic():
            today = timezone.localdate()
            if not cls.objects.filter(date=today):
                cls.objects.create(date=today)
            record = cls.objects.select_for_update().get(date=today)
            amount = round(amount, 2)
            record.amount += amount
            record.save()

    def __str__(self):
        return str(self.date)

    class Meta:
        ordering = ('-create_time',)
        verbose_name = _('Roll Room Pump Day')
        verbose_name_plural = _('Roll Room Pump Day')


class RollRoomPumpMonth(ModelBase):
    date = models.DateField(_("date"), default=timezone.now)
    amount = models.FloatField(_("amount"), default=0)

    @classmethod
    def update_amount(cls, amount):
        with transaction.atomic():
            today = timezone.localdate()
            month_day_first = today.replace(day=1)
            if not cls.objects.filter(date=month_day_first):
                cls.objects.create(date=month_day_first)
            record = cls.objects.select_for_update().get(date=month_day_first)
            amount = round(amount, 2)
            record.amount += amount
            record.save()

    def month(self):
        return self.date.strftime("%Y-%m")

    month.short_description = _('Month')

    def __str__(self):
        return self.month()

    class Meta:
        ordering = ('-create_time',)
        verbose_name = _('Roll Room Pump Month')
        verbose_name_plural = _('Roll Room Pump Month')


class RollroomBot(models.Model):
    user = models.OneToOneField(USER_MODEL, verbose_name=_("user"), related_name='roll_bot')
    open_idle_min = models.IntegerField(_('join idle min(seconds)'), default=0)
    open_idle_max = models.IntegerField(_('join idle max(seconds)'), default=0)
    remark = models.CharField(_('remark'), max_length=128)
    enable = models.BooleanField(_('enable'), default=True)

    class Meta:
        verbose_name = _('Rollroom Bot Config')
        verbose_name_plural = _('Rollroom Bot Config')

    def __str__(self):
        return self.remark


class RollRoomBetBot(models.Model):
    bot = models.ForeignKey(RollroomBot, related_name='room_bot', verbose_name=_('bot'))
    room = models.ForeignKey(RollRoom, related_name='bot_bet_room', verbose_name=_('room'))

    class Meta:
        verbose_name = _('Roll房机器人')
        verbose_name_plural = _('Roll房机器人')

    def __str__(self):
        return str(self.bot)


class RollroomStatisticsDay(ModelBase):
    date = models.DateField(_("date"), default=timezone.now)
    amount = models.FloatField(_("game amount"), default=0)
    journal = models.FloatField(_("game journal"), default=0)
    test_amount = models.FloatField(_("test game amount"), default=0)
    admin_amount = models.FloatField(_("admin game amount"), default=0)

    class Meta:
        ordering = ('-create_time',)
        verbose_name = _('Rollroom Statistics Day')
        verbose_name_plural = _('Rollroom Statistics Day')

    def __str__(self):
        return str(self.date)

    @classmethod
    def update_amount(cls, amount):
        with transaction.atomic():
            today = timezone.localdate()
            if not cls.objects.filter(date=today):
                cls.objects.create(date=today)
            record = cls.objects.select_for_update().get(date=today)
            amount = round(amount, 2)
            record.amount += amount
            record.save()

    @classmethod
    def update_journal(cls, journal):
        with transaction.atomic():
            today = timezone.localdate()
            if not cls.objects.filter(date=today):
                cls.objects.create(date=today)
            record = cls.objects.select_for_update().get(date=today)
            journal = round(journal, 2)
            record.journal += journal
            record.save()

    @classmethod
    def update_test_amount(cls, amount):
        with transaction.atomic():
            today = timezone.localdate()
            if not cls.objects.filter(date=today):
                cls.objects.create(date=today)
            record = cls.objects.select_for_update().get(date=today)
            amount = round(amount, 2)
            record.test_amount += amount
            record.save()

    @classmethod
    def update_admin_amount(cls, amount):
        with transaction.atomic():
            today = timezone.localdate()
            if not cls.objects.filter(date=today):
                cls.objects.create(date=today)
            record = cls.objects.select_for_update().get(date=today)
            amount = round(amount, 2)
            record.admin_amount += amount
            record.save()


class RollroomStatisticsMonth(ModelBase):
    date = models.DateField(_("date"), default=timezone.now)
    amount = models.FloatField(_("game amount"), default=0)
    journal = models.FloatField(_("game journal"), default=0)
    test_amount = models.FloatField(_("test game amount"), default=0)
    admin_amount = models.FloatField(_("admin game amount"), default=0)

    class Meta:
        ordering = ('-create_time',)
        verbose_name = _('Rollroom Statistics Month')
        verbose_name_plural = _('Rollroom Statistics Month')

    def __str__(self):
        return self.month()

    @classmethod
    def update_amount(cls, amount):
        with transaction.atomic():
            today = timezone.localdate()
            month_day_first = today.replace(day=1)
            if not cls.objects.filter(date=month_day_first):
                cls.objects.create(date=month_day_first)
            record = cls.objects.select_for_update().get(date=month_day_first)
            amount = round(amount, 2)
            record.amount += amount
            record.save()

    @classmethod
    def update_journal(cls, journal):
        with transaction.atomic():
            today = timezone.localdate()
            month_day_first = today.replace(day=1)
            if not cls.objects.filter(date=month_day_first):
                cls.objects.create(date=month_day_first)
            record = cls.objects.select_for_update().get(date=month_day_first)
            journal = round(journal, 2)
            record.journal += journal
            record.save()

    @classmethod
    def update_test_amount(cls, amount):
        with transaction.atomic():
            today = timezone.localdate()
            month_day_first = today.replace(day=1)
            if not cls.objects.filter(date=month_day_first):
                cls.objects.create(date=month_day_first)
            record = cls.objects.select_for_update().get(date=month_day_first)
            amount = round(amount, 2)
            record.test_amount += amount
            record.save()

    @classmethod
    def update_admin_amount(cls, amount):
        with transaction.atomic():
            today = timezone.localdate()
            month_day_first = today.replace(day=1)
            if not cls.objects.filter(date=month_day_first):
                cls.objects.create(date=month_day_first)
            record = cls.objects.select_for_update().get(date=month_day_first)
            amount = round(amount, 2)
            record.admin_amount += amount
            record.save()

    def month(self):
        return self.date.strftime("%Y-%m")

    month.short_description = _('Month')
