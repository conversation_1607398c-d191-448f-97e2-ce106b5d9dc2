from django.db import models
from django.utils.translation import gettext_lazy as _

from ckeditor_uploader.fields import RichTextUploading<PERSON>ield

from steambase.models import ModelBase, USER_MODEL


class SiteConfigCategoryyy(models.Model):
    name = models.CharField(_('name'), max_length=128)

    class Meta:
        verbose_name = "站点配置分类"
        verbose_name_plural = _('Site Config Category')

    def __str__(self):
        return self.name


class SiteConfig(models.Model):
    category = models.ForeignKey(SiteConfigCategoryyy, on_delete=models.SET_NULL, verbose_name = "分类", related_name='site_config',
                                 default=None, null=True, blank=True)
    key = models.CharField(max_length=64, unique=True)
    value = models.CharField(_('value'), max_length=1280, default=None, blank=True)
    enable = models.BooleanField(_("enable"), default=True)
    remark = models.CharField(_("remark"), max_length=128)

    # 加入新字段表排序
    order = models.IntegerField(_('order No.'), default=0)

    class Meta:
        verbose_name = "站点配置"
        verbose_name_plural = _('Site Config')

    def __str__(self):
        return self.remark


class Announcee(ModelBase):
    ANNO_TYPE = (
        (1, _('Homepage')),
        (2, _('Normal')),
    )
    type = models.SmallIntegerField(_('type'), choices=ANNO_TYPE)
    remark = models.CharField(_("remark"), max_length=128)
    title = models.CharField(_("title"), max_length=128, default=None, null=True, blank=True)
    content = RichTextUploadingField(_('content'))
    enable = models.BooleanField(_('enable'), default=True)

    class Meta:
        ordering = ('type',)
        verbose_name = "公告"
        verbose_name_plural = _('Announcee')

    def __str__(self):
        return self.remark


class Footer(ModelBase):
    FOOTER_TYPE = (
        (1, _('Announcee')),
        (2, _('Link')),
    )
    remark = models.CharField(_("remark"), max_length=128)
    type = models.SmallIntegerField(_('type'), choices=FOOTER_TYPE)
    title = models.CharField(_('title'), max_length=255)
    link = models.CharField(_('link'), max_length=255, default=None, null=True, blank=True)
    anno = models.ForeignKey(Announcee, on_delete=models.SET_NULL, verbose_name = "公告", related_name='footer',
                             default=None, null=True, blank=True)
    no = models.IntegerField(_('order No.'), default=0)
    enable = models.BooleanField(_('enable'), default=True)

    class Meta:
        ordering = ('no',)
        verbose_name = "页脚"
        verbose_name_plural = _('Footer')

    def __str__(self):
        return self.remark


class FAQ(ModelBase):
    title = models.CharField(_('title'), max_length=255)
    content = RichTextUploadingField(_('content'))
    enable = models.BooleanField(_('enable'), default=True)
    sort = models.IntegerField(_('order No.'), default=0)

    class Meta:
        verbose_name = "常见问题"
        verbose_name_plural = _('FAQ')

    def __str__(self):
        return str(self.id)


class Support(ModelBase):
    remark = models.CharField(_("remark"), max_length=128)
    title = models.CharField(_('title'), max_length=255)
    content = RichTextUploadingField(_('content'))
    sort = models.IntegerField(_('order No.'), default=0)
    enable = models.BooleanField(_('enable'), default=True)

    class Meta:
        ordering = ('sort',)
        verbose_name = "客服支持"
        verbose_name_plural = _('Support')

    def __str__(self):
        return self.remark


class Banner(ModelBase):
    TYPE = (
        (0, _('All')),
        (1, _('PC')),
        (2, _('Mobile')),
    )
    title = models.CharField(_("title"), max_length=128)
    type = models.SmallIntegerField(_('type'), choices=TYPE, default=0)
    enable = models.BooleanField(_('enable'), default=0)
    is_simple = models.BooleanField(_('is simple'), default=True)
    order = models.IntegerField(_('order No.'), default=0)
    description = models.TextField(_('description'), default=None, null=True, blank=True)
    image = models.CharField(_("image"), max_length=255, default=None, null=True, blank=True)
    link = models.CharField(_("link"), max_length=255, default=None, null=True, blank=True)
    background_class = models.CharField(_("background class"), max_length=128, default=None, null=True, blank=True)
    glow_class = models.CharField(_("glow class"), max_length=128, default=None, null=True, blank=True)
    primary_button_text = models.CharField(_("primary button text"), max_length=128, default=None, null=True, blank=True)
    primary_button_link = models.CharField(_("primary button link"), max_length=128, default=None, null=True, blank=True)
    secondary_button_text = models.CharField(_("secondary button text"), max_length=128, default=None, null=True, blank=True)
    secondary_button_link = models.CharField(_("secondary button link"), max_length=128, default=None, null=True, blank=True)




    class Meta:
        ordering = ('order',)
        verbose_name = "横幅"
        verbose_name_plural = _("Banner")

    def __str__(self):
        return self.title

class ArticleCategoryy(ModelBase):
    name = models.CharField(_("name"), max_length=128)
    enable = models.BooleanField(_('enable'), default=True)
    sort = models.IntegerField(_('order No.'), default=0)

    class Meta:
        verbose_name = "文章分类"
        verbose_name_plural = _('Article Category')

    def __str__(self):
        return self.name
    
class Article(ModelBase):    
    title = models.CharField(_("title"), max_length=128)
    seo_title = models.CharField(_("seo title"), max_length=128, default=None, null=True, blank=True)
    seo_description = models.CharField(_("seo description"), max_length=128, default=None, null=True, blank=True)
    seo_keywords = models.CharField(_("seo keywords"), max_length=128, default=None, null=True, blank=True)
    tag = models.CharField(_("tag"), max_length=128, default='', unique=True)
    content = RichTextUploadingField(_('content'), null=True, blank=True, default=None)
    enable = models.BooleanField(_('enable'), default=True)
    color = models.CharField(_("color"), max_length=128, default=None, null=True, blank=True)
    image = models.CharField(_("image"), max_length=255, default=None, null=True, blank=True)
    sort = models.IntegerField(_('order No.'), default=0)
    category = models.ForeignKey(ArticleCategoryy, on_delete=models.SET_NULL, verbose_name = "分类", related_name='articles', default=None, null=True, blank=True)


    class Meta:
        verbose_name = '文章'
        verbose_name_plural = '文章'

    def __str__(self):
        return self.title
    

    
    
class SEO(ModelBase):
    url = models.CharField(_('Domain'), max_length=255, default=None, null=True, blank=True)
    name = models.CharField(_("Name"), max_length=128, default=None, null=True, blank=True)
    subtitle = models.CharField(_("Subtitle"), max_length=128, default=None, null=True, blank=True)
    title = models.CharField(_("Title"), max_length=128, default=None, null=True, blank=True)
    keywords = models.CharField(_("Keywords"), max_length=255, default=None, null=True, blank=True)
    description = models.TextField(_("Description"), max_length=255, default=None, null=True, blank=True)
    enable = models.BooleanField(_('enable'), default=True)
    icp = models.CharField(_("ICP"), max_length=128, default=None, null=True, blank=True)
    # 新增字段 
    news_enable = models.BooleanField(_('News Enable'), default=False)
    # 加入代理商功能
    agent = models.ForeignKey(USER_MODEL, on_delete=models.SET_NULL, verbose_name = "代理", related_name='agent_site', default=None, null=True, blank=True)

    class Meta:
        verbose_name = "SEO设置"
        verbose_name_plural = _('SEO')


    def __str__(self):
        return str(self.url)
    

class AccessControl(ModelBase):
    ip = models.CharField(_("IP"), max_length=128, default=None, null=True, blank=True)
    message = models.CharField(_("message"), max_length=255, default=None, null=True, blank=True)
    enable = models.BooleanField(_('enable'), default=True)
    remarks = models.CharField(_("remarks"), max_length=255, default=None, null=True, blank=True)

    class Meta:
        verbose_name = 'IP限制'
        verbose_name_plural = 'IP限制'

    def __str__(self):
        return str(self.ip)