import re

from django.contrib import admin
from django.contrib.admin import TabularInline
from django.core.urlresolvers import reverse
from django.db.models import Q
from django.utils.safestring import mark_safe
from django.utils.translation import gettext_lazy as _

from jet.filters import DateRangeFilter
from modeltranslation.admin import TranslationAdmin

from package.service.item import get_item_price
from steambase.admin import ReadOnlyAdmin, ChangeOnlyAdmin

from roll.models import RollRoom, RollRoomBet, RollRoomItem, RollroomBot, RollRoomPumpDay, RollRoomPumpMonth, \
    RollroomStatisticsDay, RollroomStatisticsMonth, RollRoomBetBot
from roll.service.admin_actions import cancel_rollroom, delete_rollroom

from package.models import ItemInfo, ItemPrice
class RollRoomItemInline(TabularInline):
    model = RollRoomItem
    can_delete = False
    fields = ['item_id', 'price', 'balance', 'bet']  # 使用 item_id 代替 package
    readonly_fields = ['item_id', 'price', 'balance']  # readonly_fields 中也要更新为 item_id
    raw_id_fields = ("bet",)

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        model_id = int(re.search(r'\d+', request.path).group(0))  # 获取RollRoom的ID
        if db_field.name == "bet":
            room = RollRoom.objects.get(id=model_id)
            # 查询与 RollRoom 相关联的所有 Bets
            queryset = room.bets.all()
            kwargs["queryset"] = queryset
        return super().formfield_for_foreignkey(db_field, request, **kwargs)

    def has_add_permission(self, request):
        return False

    def price(self, obj):
        # 使用 item_id 获取对应的 ItemInfo 和 ItemPrice
        if obj.item_id:
            item_info = ItemInfo.objects.filter(item_id=obj.item_id).first()  # 获取 ItemInfo
            if item_info:
                # 获取 ItemPrice
                item_price = ItemPrice.objects.filter(item_info=item_info).first()
                if item_price:
                    return item_price.zbt_price  # 假设 ItemPrice 存储了饰品的价格
        return None  # 如果没有找到对应的饰品信息或价格，返回 None

    def item_id(self, obj):
        return obj.item_id  # 显示 item_id


class RollRoomBetInline(TabularInline):
    model = RollRoomBet
    fields = ['roll_room_bet_link', 'user', 'win_amount', 'win_items_count', 'victory', 'create_time']
    readonly_fields = ['roll_room_bet_link', 'user', 'win_amount', 'win_items_count', 'victory', 'create_time']

    def has_add_permission(self, request):
        return False

    def roll_room_bet_link(self, instance):
        url = reverse('admin:{app}_{model}_change'.format(app=instance._meta.app_label,
                                                          model=instance._meta.model_name),
                      args=[instance.pk])
        if instance.pk:
            return mark_safe(u'<a href="{url}">{name}</a>'.format(url=url, name=instance.uid))
        else:
            return '-'

    roll_room_bet_link.short_description = _('Roll Room Bet')


class RollRoomBotInline(TabularInline):
    model = RollRoomBetBot
    extra = 0
    raw_id_fields = ("bot",)

@admin.register(RollRoom)
class RollRoomAdmin(ChangeOnlyAdmin, TranslationAdmin):
    fields = (
        'uid', 'user', 'type', 'name', 'desc', 'password', 'fee', 'max_joiner', 'max_winner', 'official',
        'begin_ts', 'due_ts', 'win_ts', 'total_fee', 'pump_amount', 'total_amount',
        'charge_limit', 'charge_begin_time', 'charge_end_time',
        'state', 'create_time', 'enable'
    )
    list_display = ('name', 'user', 'type', 'total_amount', 'state', 'create_time', 'win_ts', 'enable', 'official')
    list_editable = ('official',)
    list_filter = ('type', 'state', ('create_time', DateRangeFilter))
    search_fields = ('uid', 'name', 'user__username')
    ordering = ('-create_time',)
    readonly_fields = ('uid', 'user', 'type', 'password', 'fee',
                       'begin_ts', 'due_ts', 'win_ts', 'total_fee', 'pump_amount', 'total_amount', 'state',
                       'create_time')

    list_per_page = 20
    #inlines = [RollRoomItemInline, RollRoomBetInline, RollRoomBotInline]
    actions = [cancel_rollroom, delete_rollroom]


@admin.register(RollRoomBet)
class RollRoomBetAdmin(ReadOnlyAdmin):
    fields = ('uid', 'user', 'room', 'win_amount', 'win_items_count', 'victory', 'create_time')
    list_display = ('uid', 'user', 'room', 'room_uid', 'win_amount', 'win_items_count', 'victory', 'create_time')
    list_filter = ('victory', ('create_time', DateRangeFilter))
    search_fields = ('uid', 'user__username', 'room__name')
    ordering = ('-create_time',)
    list_per_page = 50
    inlines = [RollRoomItemInline]

    def room_uid(self, obj):
        return obj.room.uid

    room_uid.short_description = _('room uid')


# @admin.register(RollroomBot)
# class RollroomBotAmind(admin.ModelAdmin):
#     fields = ('user', 'open_idle_min', 'open_idle_max', 'remark', 'enable')
#     list_editable = ('open_idle_min', 'open_idle_max', 'remark', 'enable')
#     list_display = ('user', 'open_idle_min', 'open_idle_max', 'remark', 'enable')
#     list_per_page = 50


# @admin.register(RollRoomPumpDay)
# class RollRoomPumpDayAdmin(ReadOnlyAdmin):
#     fields = ('date', 'amount')
#     list_display = ('date', 'amount')
#     list_filter = (('date', DateRangeFilter),)
#     list_per_page = 50


# @admin.register(RollRoomPumpMonth)
# class RollRoomPumpMonthAdmin(ReadOnlyAdmin):
#     fields = ('month', 'amount')
#     extra_readonly_fields = ('month',)
#     list_display = ('month', 'amount')
#     list_filter = (('date', DateRangeFilter),)
#     list_per_page = 50


# @admin.register(RollroomStatisticsDay)
# class RollRoomStatisticsDayAdmin(ReadOnlyAdmin):
#     fields = ('date', 'journal', 'amount', 'test_amount', 'admin_amount')
#     list_display = ('date', 'journal', 'amount', 'test_amount', 'admin_amount')
#     list_filter = (('date', DateRangeFilter),)
#     list_per_page = 50


# @admin.register(RollroomStatisticsMonth)
# class RollRoomStatisticsMonthAdmin(ReadOnlyAdmin):
#     fields = ('month', 'journal', 'amount', 'test_amount', 'admin_amount')
#     extra_readonly_fields = ('month',)
#     list_display = ('month', 'journal', 'amount', 'test_amount', 'admin_amount')
#     list_filter = (('date', DateRangeFilter),)
#     list_per_page = 50
