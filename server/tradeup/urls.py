from django.urls import re_path as url

from tradeup import views, model_signals



app_name = 'tradeup'
urlpatterns = [
    url(r'^inventory/', views.GetTradeupInventoryView.as_view()),
    url(r'^match/', views.MatchTradeupInventoryView.as_view()),
    url(r'^bet/', views.BetTradeupGameView.as_view()),
    url(r'^betrecord/', views.GetTradeupBetView.as_view()),
    url(r'^history/', views.GetTradeupHistoryView.as_view()),
    # url(r'^dayrank/', views.GetTradeupDayRankView.as_view()),
    # url(r'^weekrank/', views.GetTradeupWeekRankView.as_view()),

    url(r'^percentage/', views.GetTradeupPercentage.as_view()),
    url(r'^verify/', views.VerifyTradeupView.as_view()),
    url(r'^package/', views.GetUserPackageView.as_view()),

    # url(r'^coinsmatch/', views.MatchCoinsTradeupInventoryView.as_view()),
    # url(r'^coinspercentage/', views.GetCoinsTradeupPercentage.as_view()),
    # url(r'^coinsbet/', views.BetCoinsTradeupGameView.as_view()),
]
