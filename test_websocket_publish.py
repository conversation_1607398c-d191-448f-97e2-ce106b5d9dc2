#!/usr/bin/env python3
"""
测试WebSocket消息发布功能
"""

import os
import sys
import django
import json
import time

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')
sys.path.append('/www/wwwroot/csgoskins.com.cn/server')

try:
    django.setup()
    print("✅ Django设置成功")
except Exception as e:
    print(f"❌ Django设置失败: {e}")
    sys.exit(1)

def test_websocket_publish():
    """测试WebSocket消息发布"""
    print("\n🔧 测试WebSocket消息发布...")
    
    try:
        from django_redis import get_redis_connection
        
        # 获取Redis连接
        redis_client = get_redis_connection("default")
        print("✅ Redis连接成功")
        
        # 测试消息列表
        test_messages = [
            {
                'channel': 'ws_channel',
                'type': 'test',
                'action': 'connection_test',
                'data': {
                    'message': '这是一条测试消息',
                    'timestamp': int(time.time() * 1000),
                    'source': 'python_test_script'
                }
            },
            {
                'channel': 'ws_channel', 
                'type': 'stats',
                'action': 'update',
                'data': {
                    'total_users': 12345,
                    'online_users': 567,
                    'total_cases_opened': 98765,
                    'timestamp': int(time.time() * 1000)
                }
            },
            {
                'channel': 'ws_channel',
                'type': 'box',
                'action': 'new_opening',
                'data': {
                    'user': 'TestUser',
                    'case_name': '测试箱子',
                    'item_name': '测试物品',
                    'item_value': 100.50,
                    'timestamp': int(time.time() * 1000)
                }
            }
        ]
        
        # 发布测试消息
        for i, msg in enumerate(test_messages):
            print(f"\n📤 发布消息 {i+1}/{len(test_messages)}:")
            print(f"   频道: {msg['channel']}")
            print(f"   类型: {msg['type']}")
            print(f"   动作: {msg['action']}")
            
            # 构造消息格式（与Django后端一致）
            message_data = [msg['type'], msg['action'], msg['data']]
            message_json = json.dumps(message_data, ensure_ascii=False)
            
            # 发布到Redis
            result = redis_client.publish(msg['channel'], message_json)
            print(f"   订阅者数量: {result}")
            
            if result > 0:
                print(f"   ✅ 消息发布成功，有 {result} 个订阅者")
            else:
                print(f"   ⚠️ 消息发布成功，但没有订阅者")
            
            # 等待1秒
            time.sleep(1)
        
        print(f"\n✅ 所有测试消息发布完成")
        return True
        
    except Exception as e:
        print(f"❌ WebSocket消息发布测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_specific_business_functions():
    """测试具体的业务函数"""
    print("\n🔧 测试具体业务函数...")
    
    try:
        # 测试用户统计消息
        from authentication.model_signals import ws_send_total_users
        print("📤 测试用户统计消息...")
        ws_send_total_users(12345, 'update')
        print("✅ 用户统计消息发送成功")
        
        # 测试聊天消息
        from chat.business import ws_send_msg
        print("📤 测试聊天消息...")
        test_chat_data = ['message', 'new', {
            'user': 'TestUser',
            'message': '这是一条测试聊天消息',
            'timestamp': int(time.time() * 1000)
        }]
        ws_send_msg(test_chat_data)
        print("✅ 聊天消息发送成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 业务函数测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_redis_subscribers():
    """检查Redis订阅者"""
    print("\n🔧 检查Redis订阅者...")
    
    try:
        from django_redis import get_redis_connection
        
        redis_client = get_redis_connection("default")
        
        # 检查各个频道的订阅者数量
        channels = ['ws_channel', 'box_game_channel', 'chat_channel']
        
        for channel in channels:
            # 使用PUBSUB NUMSUB命令检查订阅者数量
            result = redis_client.execute_command('PUBSUB', 'NUMSUB', channel)
            if len(result) >= 2:
                subscriber_count = result[1]
                print(f"📊 频道 {channel}: {subscriber_count} 个订阅者")
            else:
                print(f"📊 频道 {channel}: 0 个订阅者")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查订阅者失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始WebSocket消息发布测试...")
    
    # 检查Redis订阅者
    subscribers_ok = check_redis_subscribers()
    
    # 测试消息发布
    publish_ok = test_websocket_publish()
    
    # 测试业务函数
    business_ok = test_specific_business_functions()
    
    print("\n" + "="*50)
    print("📊 测试结果总结:")
    print("="*50)
    print(f"Redis订阅者检查: {'✅ 成功' if subscribers_ok else '❌ 失败'}")
    print(f"消息发布测试: {'✅ 成功' if publish_ok else '❌ 失败'}")
    print(f"业务函数测试: {'✅ 成功' if business_ok else '❌ 失败'}")
    
    if publish_ok and business_ok:
        print("\n🎉 WebSocket消息发布功能正常！")
        print("💡 如果前端仍未收到消息，请检查:")
        print("   1. 前端是否正确连接到WebSocket服务器")
        print("   2. 前端是否正确监听消息事件")
        print("   3. WebSocket服务器是否正确订阅Redis频道")
        sys.exit(0)
    else:
        print("\n❌ 部分测试失败，请检查配置。")
        sys.exit(1)
