<!-- components/common/Breadcrumb.vue -->
<template>
  <div v-if="shouldShowBreadcrumb" class="breadcrumb-wrapper">
    <!-- PC端面包屑 -->
    <div v-if="!isMobile" class="breadcrumb-container">
      <div class="container mx-auto px-4">
        <nav class="breadcrumb-nav" aria-label="面包屑导航">
          <!-- 首页链接 -->
          <NuxtLink 
            to="/" 
            class="breadcrumb-home"
            :aria-label="$t('nav.home')"
          >
            <Icon name="heroicons:home-solid" class="home-icon" />
            <span class="home-text">{{ $t('nav.home') }}</span>
          </NuxtLink>
          
          <!-- 面包屑项目 -->
          <template v-for="(crumb, index) in breadcrumbs" :key="`${crumb.path}-${index}`">
            <!-- 分隔符 -->
            <div class="breadcrumb-separator">
              <Icon name="heroicons:chevron-right" class="separator-icon" />
            </div>
            
            <!-- 链接项或当前项 -->
            <component 
              :is="index < breadcrumbs.length - 1 ? 'NuxtLink' : 'span'"
              :to="index < breadcrumbs.length - 1 ? crumb.path : undefined"
              :class="[
                'breadcrumb-item',
                {
                  'breadcrumb-link': index < breadcrumbs.length - 1,
                  'breadcrumb-current': index === breadcrumbs.length - 1
                }
              ]"
              :aria-current="index === breadcrumbs.length - 1 ? 'page' : undefined"
            >
              <Icon 
                v-if="crumb.icon" 
                :name="crumb.icon" 
                class="item-icon" 
              />
              <span class="item-text">{{ crumb.name }}</span>
              <span v-if="index === breadcrumbs.length - 1" class="current-indicator"></span>
            </component>
          </template>
        </nav>
      </div>
    </div>

    <!-- 移动端面包屑 -->
    <div v-else class="breadcrumb-mobile">
      <div class="mobile-container">
        <!-- 返回按钮 -->
        <button 
          v-if="breadcrumbs.length > 0"
          @click="handleGoBack"
          class="mobile-back-btn"
          :aria-label="$t('common.goBack')"
        >
          <Icon name="heroicons:arrow-left" class="back-icon" />
        </button>
        
        <!-- 当前页面标题 -->
        <div class="mobile-current">
          <Icon 
            v-if="currentCrumb?.icon" 
            :name="currentCrumb.icon" 
            class="current-icon" 
          />
          <span class="current-title">{{ currentCrumb?.name || $t('nav.home') }}</span>
        </div>
        
        <!-- 路径层级指示器 -->
        <div class="mobile-indicator">
          <div 
            v-for="(_, index) in breadcrumbs" 
            :key="index"
            class="indicator-dot"
            :class="{ 'active': index === breadcrumbs.length - 1 }"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'
import { useBreakpoints } from '@vueuse/core'

// 响应式断点检测
const breakpoints = useBreakpoints({
  mobile: 768,
  tablet: 1024,
  desktop: 1280
})

const isMobile = breakpoints.smaller('tablet')
const { locale, t } = useI18n()
const route = useRoute()
const router = useRouter()

// 面包屑项目接口
interface BreadcrumbItem {
  name: string
  path: string
  icon?: string
  isHome?: boolean
}

// 是否显示面包屑
const shouldShowBreadcrumb = computed((): boolean => {
  // 排除不需要显示面包屑的页面
  const excludePaths = ['/', '/auth/login', '/auth/register', '/404', '/500']
  return !excludePaths.includes(route.path)
})

// 面包屑导航计算
const breadcrumbs = computed((): BreadcrumbItem[] => {
  if (route.path === '/') return []
  
  const pathParts = route.path.split('/').filter(Boolean)
  const breadcrumbItems: BreadcrumbItem[] = []
  let currentPath = ''
  
  pathParts.forEach((part, index) => {
    currentPath += `/${part}`
    
    const breadcrumbInfo = getBreadcrumbInfo(part, pathParts, index, currentPath)
    if (breadcrumbInfo) {
      breadcrumbItems.push(breadcrumbInfo)
    }
  })

  return breadcrumbItems
})

// 当前面包屑（移动端使用）
const currentCrumb = computed((): BreadcrumbItem | undefined => {
  return breadcrumbs.value[breadcrumbs.value.length - 1]
})

// 获取面包屑信息（包含图标）
const getBreadcrumbInfo = (
  part: string, 
  pathParts: string[], 
  index: number, 
  fullPath: string
): BreadcrumbItem | null => {
  // 处理动态路由参数
  const parentPart = pathParts[index - 1]
  
  // 处理箱子详情页面
  if (parentPart === 'cases') {
    if (part.match(/^\d+$/)) {
      // 纯数字ID的箱子
      return {
        name: t('breadcrumb.caseDetail', { id: part }),
        path: fullPath,
        icon: 'game-icons:locked-chest'
      }
    } else {
      // 箱子key（如 medium-case）
      return {
        name: t('breadcrumb.caseDetail', { id: part }),
        path: fullPath,
        icon: 'game-icons:locked-chest'
      }
    }
  }
  
  // 处理对战详情页面
  if (parentPart === 'battle' && part.match(/^\d+$/)) {
    return {
      name: t('breadcrumb.battleDetail', { id: part }),
      path: fullPath,
      icon: 'game-icons:crossed-swords'
    }
  }
  
  // 处理用户资料页面
  if (parentPart === 'profile') {
    return {
      name: t('breadcrumb.userProfile'),
      path: fullPath,
      icon: 'heroicons:user-circle'
    }
  }

  // 静态路由映射（带图标）
  const routeInfoMap: Record<string, { name: string; icon?: string }> = {
    // 主要页面
    'cases': { 
      name: t('nav.cases'), 
      icon: 'game-icons:locked-chest' 
    },
    'battle': { 
      name: t('nav.battle'), 
      icon: 'game-icons:crossed-swords' 
    },
    'battles': { 
      name: t('nav.battle'), 
      icon: 'game-icons:crossed-swords' 
    },
    'activity': { 
      name: t('nav.activity'), 
      icon: 'heroicons:chart-bar' 
    },
    'activities': { 
      name: t('nav.activity'), 
      icon: 'heroicons:chart-bar' 
    },
    'profile': { 
      name: t('nav.profile'), 
      icon: 'heroicons:user-circle' 
    },
    'skins': { 
      name: t('nav.skin'), 
      icon: 'game-icons:knife' 
    },
    'recharge': { 
      name: t('nav.recharge'), 
      icon: 'heroicons:credit-card' 
    },
    
    // 认证相关
    'auth': { 
      name: t('breadcrumb.account'), 
      icon: 'heroicons:user' 
    },
    'login': { 
      name: t('auth.login.title'), 
      icon: 'heroicons:arrow-right-on-rectangle' 
    },
    'register': { 
      name: t('auth.register.title'), 
      icon: 'heroicons:user-plus' 
    },
    'forgot-password': { 
      name: t('auth.forgotPassword'), 
      icon: 'heroicons:key' 
    },
    
    // 用户相关
    'settings': { 
      name: t('breadcrumb.settings'), 
      icon: 'heroicons:cog-6-tooth' 
    },
    'inventory': { 
      name: t('breadcrumb.inventory'), 
      icon: 'heroicons:cube' 
    },
    'transactions': { 
      name: t('breadcrumb.transactions'), 
      icon: 'heroicons:banknotes' 
    },
    'history': { 
      name: t('breadcrumb.history'), 
      icon: 'heroicons:clock' 
    },
    
    // 其他页面
    'about': { 
      name: t('breadcrumb.about'), 
      icon: 'heroicons:information-circle' 
    },
    'terms': { 
      name: t('breadcrumb.terms'), 
      icon: 'heroicons:document-text' 
    },
    'privacy': { 
      name: t('breadcrumb.privacy'), 
      icon: 'heroicons:shield-check' 
    },
    'faq': { 
      name: t('breadcrumb.faq'), 
      icon: 'heroicons:question-mark-circle' 
    },
    'support': { 
      name: t('breadcrumb.support'), 
      icon: 'heroicons:chat-bubble-left-right' 
    },
    'contact': { 
      name: t('breadcrumb.contact'), 
      icon: 'heroicons:envelope' 
    },
    'help': { 
      name: t('breadcrumb.help'), 
      icon: 'heroicons:lifebuoy' 
    },
    
    // Demo页面
    'demo': { 
      name: t('breadcrumb.demo'), 
      icon: 'heroicons:beaker' 
    }
  }

  const routeInfo = routeInfoMap[part]
  if (routeInfo) {
    return {
      name: routeInfo.name,
      path: fullPath,
      icon: routeInfo.icon
    }
  }

  // 默认处理
  return {
    name: part.charAt(0).toUpperCase() + part.slice(1),
    path: fullPath
  }
}

// 移动端返回处理
const handleGoBack = (): void => {
  if (breadcrumbs.value.length > 1) {
    // 返回上一级
    const parentCrumb = breadcrumbs.value[breadcrumbs.value.length - 2]
    router.push(parentCrumb.path)
  } else {
    // 返回首页
    router.push('/')
  }
}
</script>

<style lang="scss" scoped>
// 共用变量
:root {
  --breadcrumb-primary: #00A8FF;
  --breadcrumb-secondary: #FF4D00;
  --breadcrumb-bg: rgba(13, 15, 18, 0.95);
  --breadcrumb-glass: rgba(31, 41, 55, 0.4);
  --breadcrumb-border: rgba(107, 114, 128, 0.3);
  --breadcrumb-text: rgba(255, 255, 255, 0.9);
  --breadcrumb-text-muted: rgba(255, 255, 255, 0.6);
  --breadcrumb-hover: rgba(0, 168, 255, 0.1);
}

// PC端面包屑样式
.breadcrumb-container {
  background: var(--breadcrumb-glass);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--breadcrumb-border);
  padding: 1rem 0;
  position: relative;
  
  // 顶部发光效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, 
      transparent, 
      var(--breadcrumb-primary), 
      transparent
    );
    opacity: 0.5;
  }
}

.breadcrumb-nav {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
  font-size: 0.875rem;
  font-weight: 500;
}

// 首页链接
.breadcrumb-home {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--breadcrumb-text-muted);
  text-decoration: none;
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  
  .home-icon {
    width: 1rem;
    height: 1rem;
    opacity: 0.8;
  }
  
  .home-text {
    font-weight: 500;
  }
  
  // 悬停效果
  &:hover {
    color: var(--breadcrumb-primary);
    background: var(--breadcrumb-hover);
    transform: translateY(-1px);
    
    .home-icon {
      opacity: 1;
      animation: homeIconPulse 0.6s ease-in-out;
    }
  }
  
  // 滑动光效
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
      transparent, 
      rgba(255, 255, 255, 0.1), 
      transparent
    );
    transition: left 0.5s ease;
  }
  
  &:hover::before {
    left: 100%;
  }
}

// 分隔符
.breadcrumb-separator {
  display: flex;
  align-items: center;
  color: var(--breadcrumb-text-muted);
  opacity: 0.6;
  
  .separator-icon {
    width: 0.875rem;
    height: 0.875rem;
    transition: transform 0.2s ease;
  }
}

// 面包屑项目
.breadcrumb-item {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  
  .item-icon {
    width: 1rem;
    height: 1rem;
    opacity: 0.8;
    flex-shrink: 0;
  }
  
  .item-text {
    font-weight: 500;
    white-space: nowrap;
  }
}

// 链接项样式
.breadcrumb-link {
  color: var(--breadcrumb-text-muted);
  text-decoration: none;
  cursor: pointer;
  
  &:hover {
    color: var(--breadcrumb-primary);
    background: var(--breadcrumb-hover);
    transform: translateY(-1px);
    
    .item-icon {
      opacity: 1;
      transform: scale(1.1);
    }
    
    // 分隔符动画
    + .breadcrumb-separator .separator-icon {
      transform: translateX(2px);
    }
  }
  
  // 滑动光效
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
      transparent, 
      rgba(0, 168, 255, 0.2), 
      transparent
    );
    transition: left 0.5s ease;
  }
  
  &:hover::before {
    left: 100%;
  }
}

// 当前项样式
.breadcrumb-current {
  color: var(--breadcrumb-primary);  
  font-weight: 600;
  position: relative;
  
  .item-icon {
    opacity: 1;
    color: var(--breadcrumb-primary);
  }
  
  // 当前项指示器
  .current-indicator {
    position: absolute;
    right: 0.25rem;
    top: 50%;
    transform: translateY(-50%);
    width: 0.375rem;
    height: 0.375rem;
    background: var(--breadcrumb-primary);
    border-radius: 50%;
    animation: currentPulse 2s ease-in-out infinite;
  }
}

// 移动端面包屑样式
.breadcrumb-mobile {
  background: var(--breadcrumb-glass);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--breadcrumb-border);
  padding: 0.75rem 0;
}

.mobile-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 1rem;
  gap: 1rem;
}

// 返回按钮
.mobile-back-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background: var(--breadcrumb-hover);
  border: 1px solid rgba(0, 168, 255, 0.3);
  border-radius: 0.5rem;
  color: var(--breadcrumb-primary);
  cursor: pointer;
  transition: all 0.3s ease;
  
  .back-icon {
    width: 1.25rem;
    height: 1.25rem;
  }
  
  &:hover {
    background: rgba(0, 168, 255, 0.2);
    transform: translateX(-2px);
  }
  
  &:active {
    transform: translateX(-2px) scale(0.95);
  }
}

// 当前页面标题
.mobile-current {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
  min-width: 0;
  
  .current-icon {
    width: 1.25rem;
    height: 1.25rem;
    color: var(--breadcrumb-primary);
    flex-shrink: 0;
  }
  
  .current-title {
    color: var(--breadcrumb-text);
    font-weight: 600;
    font-size: 1rem;
    truncate: true;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

// 层级指示器
.mobile-indicator {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.indicator-dot {
  width: 0.375rem;
  height: 0.375rem;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  
  &.active {
    background: var(--breadcrumb-primary);
    transform: scale(1.2);
    box-shadow: 0 0 0.5rem rgba(0, 168, 255, 0.5);
  }
}

// 动画效果
@keyframes homeIconPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

@keyframes currentPulse {
  0%, 100% { 
    opacity: 1; 
    transform: translateY(-50%) scale(1); 
  }
  50% { 
    opacity: 0.5; 
    transform: translateY(-50%) scale(1.2); 
  }
}

// 响应式适配
@media (max-width: 768px) {
  .breadcrumb-nav {
    font-size: 0.75rem;
    gap: 0.25rem;
  }
  
  .breadcrumb-home,
  .breadcrumb-item {
    padding: 0.375rem 0.5rem;
  }
  
  .item-text,
  .home-text {
    display: none;
  }
  
  .item-icon,
  .home-icon {
    width: 1.125rem;
    height: 1.125rem;
  }
}

// 平板适配
@media (min-width: 769px) and (max-width: 1024px) {
  .breadcrumb-nav {
    font-size: 0.8125rem;
  }
  
  .breadcrumb-home,
  .breadcrumb-item {
    padding: 0.4375rem 0.625rem;
  }
}

// 无障碍设计
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .breadcrumb-container,
  .breadcrumb-mobile {
    border-bottom: 2px solid var(--breadcrumb-primary);
  }
  
  .breadcrumb-current {
    border: 2px solid var(--breadcrumb-primary);
  }
}
</style>
