#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import logging
import time
from threading import Thread

from steambase.enums import TradeState
from steambase.redis_con import get_redis

_logger = logging.getLogger(__name__)


_trade_list_key = 'trade_list'
_send_list_key = 'send_list'


def temp_confirm():
    r = get_redis()
    last_no = 2102296394
    while True:
        time.sleep(0.1)
        try:
            keys = r.lrange(_trade_list_key, 0, -1)
            for key in keys:
                val = r.get(key)
                if val:
                    deposit = json.loads(val)
                    status = deposit.get('status', TradeState.Initialed.value)
                    if status == TradeState.Initialed.value:
                        deposit['trade_no'] = last_no
                        last_no += 1
                        deposit['status'] = TradeState.Active.value
                        status = TradeState.Active.value
                        time.sleep(3)
                        r.set(key, json.dumps(deposit))

                    if status == TradeState.Active.value:
                        time.sleep(5)
                        deposit['status'] = TradeState.Accepted.value
                        r.set(key, json.dumps(deposit))

                        deposit['exchanged'] = True
                        d_items = deposit['theirItems']
                        for i in d_items:
                            i['new_assetid'] = '1' + i['assetid']
                        time.sleep(3)
                        r.set(key, json.dumps(deposit))
                        _logger.info('temp thread confirm trade {0} items to {1} on {2}'.format(
                            len(deposit['theirItems']), deposit['steamer'].get('steamid', ''),
                            deposit['steamer'].get('tradeurl', '')))
        except Exception as e:
            _logger.exception(e)


def setup_test_confirmer():
    th = Thread(target=temp_confirm)
    th.start()




def temp_sender():
    r = get_redis()
    last_no = 4233252111
    while True:
        time.sleep(1)
        try:
            keys = r.lrange(_send_list_key, 0, -1)
            for key in keys:
                val = r.get(key)
                if val:
                    deposit = json.loads(val)
                    status = deposit.get('status', TradeState.Initialed.value)
                    if status == TradeState.Initialed.value:
                        deposit['trade_no'] = last_no
                        last_no += 1
                        deposit['status'] = TradeState.Active.value
                        status = TradeState.Active.value
                        time.sleep(5)
                        r.set(key, json.dumps(deposit))

                    if status == TradeState.Active.value:
                        deposit['status'] = TradeState.Accepted.value
                        d_items = deposit['myItems']
                        for i in d_items:
                            i['sended_assetid'] = '2' + i['assetid']
                        time.sleep(20)
                        r.set(key, json.dumps(deposit))
                        _logger.info('temp thread send trade {0} items to {1} on {2}'.format(
                            len(deposit['myItems']), deposit['steamer'].get('steamid', ''),
                            deposit['steamer'].get('tradeurl', '')))
        except Exception as e:
            _logger.exception(e)


def setup_test_sender():
    th = Thread(target=temp_sender)
    th.start()

