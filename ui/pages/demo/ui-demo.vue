<template>
  <div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold text-white mb-8">原生UI组件演示</h1>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- 按钮组件 -->
      <div class="bg-gray-800/50 rounded-xl p-6 border border-gray-700/30">
        <h2 class="text-xl font-semibold text-white mb-4">按钮组件</h2>
        <div class="space-y-4">
          <div class="flex flex-wrap gap-3">
            <button class="btn-primary">主要按钮</button>
            <button class="btn-secondary">次要按钮</button>
            <button class="btn-ghost">幽灵按钮</button>
          </div>
        </div>
      </div>

      <!-- 输入框组件 -->
      <div class="bg-gray-800/50 rounded-xl p-6 border border-gray-700/30">
        <h2 class="text-xl font-semibold text-white mb-4">输入框组件</h2>
        <div class="space-y-4">
          <input 
            v-model="inputValue" 
            type="text" 
            placeholder="请输入内容" 
            class="w-full px-4 py-2 bg-gray-700/50 border border-gray-600/50 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none transition-colors"
          />
          <input 
            v-model="inputValue" 
            type="password" 
            placeholder="密码输入框" 
            class="w-full px-4 py-2 bg-gray-700/50 border border-gray-600/50 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none transition-colors"
          />
        </div>
      </div>

      <!-- 消息提示 -->
      <div class="bg-gray-800/50 rounded-xl p-6 border border-gray-700/30">
        <h2 class="text-xl font-semibold text-white mb-4">消息提示</h2>
        <div class="space-y-3">
          <button @click="showSuccessMessage" class="btn-primary">成功消息</button>
          <button @click="showErrorMessage" class="btn-secondary">错误消息</button>
          <button @click="showWarningMessage" class="btn-ghost">警告消息</button>
        </div>
      </div>

      <!-- 对话框 -->
      <div class="bg-gray-800/50 rounded-xl p-6 border border-gray-700/30">
        <h2 class="text-xl font-semibold text-white mb-4">对话框</h2>
        <div class="space-y-3">
          <button @click="showConfirmDialog" class="btn-secondary">确认对话框</button>
          <button @click="showAlertDialog" class="btn-primary">警告对话框</button>
        </div>
      </div>

      <!-- 标签组件 -->
      <div class="bg-gray-800/50 rounded-xl p-6 border border-gray-700/30">
        <h2 class="text-xl font-semibold text-white mb-4">标签组件</h2>
        <div class="flex flex-wrap gap-2">
          <span class="px-3 py-1 bg-green-500/20 border border-green-500/30 text-green-400 rounded-full text-sm">成功</span>
          <span class="px-3 py-1 bg-red-500/20 border border-red-500/30 text-red-400 rounded-full text-sm">错误</span>
          <span class="px-3 py-1 bg-yellow-500/20 border border-yellow-500/30 text-yellow-400 rounded-full text-sm">警告</span>
          <span class="px-3 py-1 bg-blue-500/20 border border-blue-500/30 text-blue-400 rounded-full text-sm">信息</span>
        </div>
      </div>

      <!-- 卡片组件 -->
      <div class="bg-gray-800/50 rounded-xl p-6 border border-gray-700/30">
        <h2 class="text-xl font-semibold text-white mb-4">卡片组件</h2>
        <div class="bg-gray-700/40 rounded-lg p-4 border border-gray-600/30">
          <h3 class="text-lg font-medium text-white mb-2">卡片标题</h3>
          <p class="text-gray-300 text-sm">这是一个原生卡片组件的示例，使用Tailwind CSS + UnoCSS样式。</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const { showSuccess, showError, showWarning, showInfo, showConfirm, showAlert } = useUI()

const inputValue = ref('')

const showSuccessMessage = () => {
  showSuccess('这是一条成功消息！')
}

const showErrorMessage = () => {
  showError('这是一条错误消息！')
}

const showWarningMessage = () => {
  showWarning('这是一条警告消息！')
}

const showConfirmDialog = async () => {
  try {
    await showConfirm('确定要执行此操作吗？', '确认操作')
    showSuccess('操作已确认！')
  } catch {
    showInfo('操作已取消')
  }
}

const showAlertDialog = async () => {
  await showAlert('这是一个警告对话框！', '警告')
}
</script>

<style scoped>
/* 自定义样式可以在这里添加 */
</style> 