unidiomatic-typecheck:5:simple_positives:Using type() instead of isinstance() for a typecheck.
unidiomatic-typecheck:6:simple_positives:Using type() instead of isinstance() for a typecheck.
unidiomatic-typecheck:7:simple_positives:Using type() instead of isinstance() for a typecheck.
unidiomatic-typecheck:8:simple_positives:Using type() instead of isinstance() for a typecheck.
unidiomatic-typecheck:9:simple_positives:Using type() instead of isinstance() for a typecheck.
unidiomatic-typecheck:10:simple_positives:Using type() instead of isinstance() for a typecheck.
unidiomatic-typecheck:14:simple_inference_positives:Using type() instead of isinstance() for a typecheck.
unidiomatic-typecheck:15:simple_inference_positives:Using type() instead of isinstance() for a typecheck.
unidiomatic-typecheck:16:simple_inference_positives:Using type() instead of isinstance() for a typecheck.
unidiomatic-typecheck:17:simple_inference_positives:Using type() instead of isinstance() for a typecheck.
unidiomatic-typecheck:18:simple_inference_positives:Using type() instead of isinstance() for a typecheck.
unidiomatic-typecheck:19:simple_inference_positives:Using type() instead of isinstance() for a typecheck.
unidiomatic-typecheck:71:type_of_literals_positives:Using type() instead of isinstance() for a typecheck.
unidiomatic-typecheck:72:type_of_literals_positives:Using type() instead of isinstance() for a typecheck.
unidiomatic-typecheck:73:type_of_literals_positives:Using type() instead of isinstance() for a typecheck.
unidiomatic-typecheck:74:type_of_literals_positives:Using type() instead of isinstance() for a typecheck.
unidiomatic-typecheck:75:type_of_literals_positives:Using type() instead of isinstance() for a typecheck.
unidiomatic-typecheck:76:type_of_literals_positives:Using type() instead of isinstance() for a typecheck.