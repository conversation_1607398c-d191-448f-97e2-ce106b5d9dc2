/**
 * server.js
 * Socket.IO 2.x 服务端 - 优化版
 * 处理WebSocket连接、Redis通信和客户端消息
 */

const http = require('http');
const axios = require('axios');
const server = http.createServer();
const { Server } = require('socket.io');
const io = new Server(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});
const cookie = require('cookie');
const logger = require('./logger');
const redis = require('redis');

// 维护Socket连接信息
const socketMap = {};

// 映射page到频道
const pageChannelMap = {
  'ws': 'ws_channel'
};

// 配置项
const CONFIG = {
  skipAPIVerification: false,  // 生产环境必须设为false
  debug: process.env.NODE_ENV !== 'production',  // 非生产环境下启用调试
  apiBaseUrl: process.env.API_URL || 'http://localhost:9000',
  redisOptions: {
    // Redis v4配置
    socket: {
      host: process.env.REDIS_HOST || 'localhost',
      port: process.env.REDIS_PORT || 6379,
      reconnectStrategy: (retries) => {
        if (retries > 10) {
          logger.error('[Redis] 重试次数过多，停止重试');
          return false;
        }
        return Math.min(retries * 100, 3000);
      }
    },
    password: process.env.REDIS_PASSWORD
  }
};

/**
 * 创建Redis客户端
 * @returns {Object} Redis客户端
 */
async function createRedisClient() {
  const client = redis.createClient(CONFIG.redisOptions);

  client.on('error', function(err) {
    logger.error('[Redis错误] ' + err.message);
  });

  client.on('connect', function() {
    logger.info('[Redis] 连接成功');
  });

  client.on('reconnecting', function() {
    logger.info('[Redis] 正在尝试重连');
  });

  await client.connect();
  return client;
}

/**
 * 订阅Redis消息，并设置Socket事件监听
 * @param {SocketIO.Socket} sock
 * @param {string} page
 */
async function connectSub(sock, page) {
  try {
    // 读取Cookie，用于后端API请求、验证等
    const subCookies = sock.handshake.headers.cookie;
    const subHeaderCookie = cookie.parse(subCookies || '');

    if (CONFIG.debug) {
      logger.info('[connectSub] 开始为socket配置: ' + sock.id);
      logger.info('[connectSub] page=' + page);
    }

    // 为当前连接创建Redis客户端
    let sub;
    try {
      sub = await createRedisClient();

      // 将当前连接保存到socketMap
      socketMap[sock.id] = { conn: sub, socket: sock };

      // 根据page匹配频道
      const pageChannel = pageChannelMap[page];
      if (pageChannel) {
        await sub.subscribe(pageChannel, (message, channel) => {
          if (CONFIG.debug) {
            logger.info(`[Redis] 收到频道 ${channel} 消息: ${message.substring(0, 100)}...`);
          }

          try {
            const msg = JSON.parse(message);

            // 如果是boxroomdetail类型，定向发给特定Socket
            if (msg[0] === 'boxroomdetail') {
              const sid = msg[3];
              if (sid in socketMap) {
                socketMap[sid].socket.emit('message', JSON.stringify(msg));
                if (CONFIG.debug) logger.info(`[Redis] 消息转发至Socket: ${sid}`);
              }
            } else {
              // 否则直接向当前socket发送
              sock.emit('message', message);
              if (CONFIG.debug) logger.info(`[Redis] 消息发送至当前Socket: ${sock.id}`);
            }
          } catch (parseErr) {
            logger.error('[Redis消息解析错误] ' + parseErr.message);
          }
        });
        logger.info(`[Redis] 订阅频道: ${pageChannel}`);
      } else {
        logger.warn(`[Redis] 未找到页面对应的频道: ${page}`);
      }
    } catch (redisErr) {
      logger.error('[Redis连接失败] ' + redisErr.message);
      throw redisErr; // Redis连接失败，无法继续
    }

    /**
     * 监听自定义"monitor"事件，解析数组负载
     * - 客户端形如： socket.emit('monitor', ['join','monitor']);
     * - 或： socket.emit('monitor', ['get_stats']);
     */
    sock.on('monitor', function (payload) {
      try {
        // 如果payload是字符串，尝试解析
        if (typeof payload === 'string') {
          try {
            payload = JSON.parse(payload);
            logger.info(`[monitor事件] 解析字符串payload: ${payload}`);
          } catch (e) {
            logger.error(`[monitor事件] 解析字符串payload失败: ${e.message}`);
            return;
          }
        }
        
        // payload 应该是数组，例如 ['join','monitor'], ['get_stats'], ['case_records'] 等
        if (!Array.isArray(payload)) {
          logger.warn('[monitor事件] 收到无效payload类型: ' + typeof payload);
          logger.warn('[monitor事件] payload内容: ' + JSON.stringify(payload));
          return;
        }
        
        const [action, param] = payload;
        logger.info(`[monitor事件] 收到 action=${action}, param=${param}`);

        switch (action) {
          case 'join':
            // 房间管理
            if (param) {
              sock.join(param);
              logger.info(`[monitor] 用户加入房间: ${param}`);
              // 确认加入成功
              sock.send(JSON.stringify(['monitor', 'join_success', param]));
              
              // 如果加入monitor房间，发起后端请求获取真实数据
              if (param === 'monitor') {
                // 通过HTTP请求获取初始统计数据
                fetchDataFromAPI(sock, page, 'stats', subCookies, subHeaderCookie.csrftoken);
                // 通过HTTP请求获取初始开箱记录
                fetchDataFromAPI(sock, page, 'case_records', subCookies, subHeaderCookie.csrftoken);
              }
            }
            break;

          case 'get_stats':
            logger.info('[monitor] 收到 get_stats 请求');
            // 通过HTTP请求获取统计数据
            fetchDataFromAPI(sock, page, 'stats', subCookies, subHeaderCookie.csrftoken);
            break;

          case 'case_records':
            logger.info('[monitor] 收到 case_records 请求');
            // 通过HTTP请求获取开箱记录
            fetchDataFromAPI(sock, page, 'case_records', subCookies, subHeaderCookie.csrftoken);
            break;

          default:
            logger.warn('[monitor] 未识别的 action: ' + action);
            // 可以考虑将请求转发给后端API
            break;
        }
      } catch (err) {
        logger.error('[monitor事件处理错误] ' + err.message);
        logger.error('[monitor事件处理错误] 堆栈: ' + err.stack);
      }
    });

    /**
     * 保留原有 sock.on('text', ...) 逻辑
     * 如果您的前端已不再使用 'text' 事件，可删除此段
     */
    sock.on('text', function (message) {
      try {
        const msgData = JSON.parse(message);
        const values = {
          page: page,
          comment: msgData.msg
        };
        logger.info('[text事件] ' + JSON.stringify(values));

        // 请求后端API
        const options = {
          url: `${CONFIG.apiBaseUrl}/api/chat/message/`,
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Cookie': subCookies,
            'X-CSRFTOKEN': subHeaderCookie.csrftoken
          },
          data: values,
          timeout: 5000
        };

        axios(options).then(response => {
          logger.info('[text事件API响应] 状态码: ' + response.status);
        }).catch(err => {
          logger.error('[text事件API请求失败] ' + err.message);
        });
      } catch (err) {
        logger.error('[text事件处理错误] ' + err.message);
      }
    });

    /**
     * 监听消息事件(兼容老版客户端)
     */
    sock.on('message', function (data) {
      try {
        logger.info('[message事件] 收到消息: ' + (typeof data === 'string' ? data : JSON.stringify(data)));
        // 可以根据需要处理老版本客户端发来的消息
      } catch (err) {
        logger.error('[message事件处理错误] ' + err.message);
      }
    });

    /**
     * 监听断开事件
     */
    sock.on('disconnecting', function (reason) {
      logger.info('[ws] disconnect, reason: ' + reason);

      try {
        // 发送后端API请求，通知断开
        const connOptions = {
          url: `${CONFIG.apiBaseUrl}/api/${page}/disconnect/`,
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Cookie': subCookies,
            'X-CSRFTOKEN': subHeaderCookie.csrftoken
          },
          timeout: 5000
        };
        request.post(connOptions, function (err, resp, body) {
          if (err) {
            logger.error('[断开API请求失败] ' + err.message);
          } else {
            logger.info('[断开API响应] 状态码: ' + resp.statusCode);
          }
        });

        // 从socketMap中删除
        if (socketMap[sock.id]) {
          delete socketMap[sock.id];
          logger.info(`[ws] 从socketMap中删除: ${sock.id}`);
        }

        // Redis取消订阅并退出
        if (sub) {
          sub.unsubscribe();
          sub.quit();
          logger.info('[ws] Redis取消订阅并退出');
        }
      } catch (err) {
        logger.error('[断开事件处理错误] ' + err.message);
      }
    });

    // 主动发送服务状态消息
    sock.send(JSON.stringify(['monitor', 'connected', { status: 'ok', time: Date.now() }]));
    
    return true;
  } catch (err) {
    logger.error('[connectSub全局错误] ' + err.message);
    logger.error('[connectSub全局错误] 堆栈: ' + err.stack);
    return false;
  }
}

/**
 * 从API获取数据并发送给客户端
 * @param {SocketIO.Socket} sock Socket连接
 * @param {string} page 页面标识
 * @param {string} dataType 数据类型 (stats|case_records)
 * @param {string} cookies Cookie字符串
 * @param {string} csrfToken CSRF令牌
 */
function fetchDataFromAPI(sock, page, dataType, cookies, csrfToken) {
  let endpoint;
  let responseType;
  
  // 根据数据类型确定API端点和响应类型
  switch (dataType) {
    case 'stats':
      endpoint = `${CONFIG.apiBaseUrl}/api/${page}/stats/`;
      responseType = 'stats';
      break;
    case 'case_records':
      endpoint = `${CONFIG.apiBaseUrl}/api/${page}/case_records/`;
      responseType = 'records';
      break;
    default:
      logger.error(`[API] 未知的数据类型: ${dataType}`);
      return;
  }
  
  const options = {
    url: endpoint,
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Cookie': cookies,
      'X-CSRFTOKEN': csrfToken
    },
    json: true,
    timeout: 5000
  };
  
  logger.info(`[API] 请求数据: ${endpoint}`);
  
  request(options, function(err, resp, body) {
    if (err) {
      logger.error(`[API] ${dataType}请求失败: ${err.message}`);
      // 通知客户端请求失败
      sock.send(JSON.stringify(['monitor', 'error', { 
        type: dataType,
        message: '获取数据失败',
        error: err.message
      }]));
      return;
    }
    
    if (resp.statusCode !== 200) {
      logger.error(`[API] ${dataType}请求返回非200状态码: ${resp.statusCode}`);
      // 通知客户端请求失败
      sock.send(JSON.stringify(['monitor', 'error', { 
        type: dataType,
        message: '获取数据失败',
        statusCode: resp.statusCode
      }]));
      return;
    }
    
    try {
      // 发送获取的数据给客户端
      if (body && (body.data || body.code === 0)) {
        const responseData = body.data || (body.body || []);
        logger.info(`[API] ${dataType}请求成功，发送数据给客户端`);
        sock.send(JSON.stringify(['monitor', responseType, responseData]));
      } else {
        logger.warn(`[API] ${dataType}响应格式错误`);
        sock.send(JSON.stringify(['monitor', 'error', { 
          type: dataType,
          message: '数据格式错误'
        }]));
      }
    } catch (err) {
      logger.error(`[API] 处理${dataType}响应失败: ${err.message}`);
    }
  });
}

/**
 * 健康检查路由
 */
server.on('request', function(req, res) {
  if (req.url === '/health') {
    res.writeHead(200, {'Content-Type': 'application/json'});
    res.end(JSON.stringify({
      status: 'ok',
      uptime: process.uptime(),
      connections: Object.keys(socketMap).length,
      timestamp: Date.now()
    }));
  }
});

/**
 * Socket.IO主监听
 */
io.on('connection', function (socket) {
  logger.info('[ws] 新连接: ' + socket.id);
  
  try {
    const cookies = socket.handshake.headers.cookie;
    const query = socket.handshake.query;
    
    logger.info('[ws] handshake cookies: ' + (cookies || '无'));
    logger.info('[ws] handshake query: ' + JSON.stringify(query || {}));

    // 如果cookie不存在且不是跳过验证模式，则断开
    if (cookies === undefined && !CONFIG.skipAPIVerification) {
      logger.info('[ws] undefined cookie, force disconnect');
      socket.disconnect();
      return;
    }

    // 解析Cookie
    const headerCookie = cookies ? cookie.parse(cookies) : {};
    const page = socket.handshake.query.page || 'ws';

    if (CONFIG.skipAPIVerification) {
      logger.info('[ws] 跳过API验证，直接进入connectSub');
      if (connectSub(socket, page)) {
        logger.info('[ws] connectSub配置成功');
      } else {
        logger.error('[ws] connectSub配置失败，断开连接');
        socket.disconnect();
      }
      return;
    }

    // 向后端发起connect请求，判断session/权限
    const connOptions = {
      url: `${CONFIG.apiBaseUrl}/api/${page}/connect/`,
      method: 'POST',
      json: true,
      headers: {
        'Content-Type': 'application/json',
        'Cookie': cookies,
        'X-CSRFTOKEN': headerCookie.csrftoken
      },
      timeout: 5000
    };

    logger.info('[ws] 发起API验证请求: ' + connOptions.url);

    // 请求后端API验证
    request.post(connOptions, function (err, resp, body) {
      if (err) {
        logger.error('[ws] connect: request error: ' + err.message);
        // 发生错误，也断开
        socket.disconnect();
      } else {
        logger.info('[ws] connect: API响应状态码: ' + resp.statusCode);
        
        if (resp.statusCode === 200 && body && body.code === 0) {
          // 验证成功：向客户端发送body.body（历史消息或初始数据）
          if (body.body && Array.isArray(body.body)) {
            body.body.forEach(function (block) {
              socket.send(JSON.stringify(block));
            });
            logger.info('[ws] 发送初始数据: ' + body.body.length + '条');
          } else {
            logger.warn('[ws] API未返回初始数据或格式错误');
          }
          
          // 进入redis订阅及事件监听
          if (connectSub(socket, page)) {
            logger.info('[ws] connected, session valid');
          } else {
            logger.error('[ws] connectSub配置失败，断开连接');
            socket.disconnect();
          }
        } else {
          logger.info('[ws] connected, but session invalid. code=' + (body ? body.code : 'undefined'));
          // 验证失败断开
          socket.disconnect();
        }
      }
    });
  } catch (err) {
    logger.error('[ws connection全局错误] ' + err.message);
    logger.error('[ws connection全局错误] 堆栈: ' + err.stack);
    socket.disconnect();
  }
});

// 处理进程异常
process.on('uncaughtException', function(err) {
  logger.error('[UNCAUGHT EXCEPTION] ' + err.message);
  logger.error(err.stack);
});

process.on('unhandledRejection', function(reason, promise) {
  logger.error('[UNHANDLED REJECTION] ' + reason);
});

// 启动信息
logger.info('[ws] 服务器启动，监听端口: 4000');
logger.info('[ws] API地址: ' + CONFIG.apiBaseUrl);
logger.info('[ws] 当前环境: ' + (process.env.NODE_ENV || 'development')); 