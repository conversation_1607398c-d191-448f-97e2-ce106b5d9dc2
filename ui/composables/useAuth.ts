import { ref, computed, nextTick } from 'vue'
import { authApi } from '~/services/auth-api'
import type { LoginCredentials, RegisterData, ResetPasswordData, ChangePasswordData } from '~/services/auth-api'
import { useUserStore } from '~/stores/user'

export const useAuth = () => {
  const userStore = useUserStore()
  
  // 状态管理
  const captcha = ref<{ uuid: string; captcha: string } | null>(null)
  const isLoadingCaptcha = ref(false)
  const codeCountdown = ref(0)
  const isLoading = ref(false)
  const isCodeLoading = ref(false)
  const codeSent = ref(false)
  const authError = ref<string | null>(null)
  
  // 计算属性
  const isLoadingCaptchaComputed = computed(() => isLoadingCaptcha.value)
  const codeCountdownComputed = computed(() => codeCountdown.value)
  
  // 获取验证码 - 简化实现
  const getCaptcha = async (): Promise<void> => {
    try {
      isLoadingCaptcha.value = true
      authError.value = null
      
      console.log('[🔐AUTH] 正在获取验证码...')
      const captchaData = await authApi.getCaptcha()
      console.log('[🔐AUTH] 验证码获取成功')
      
      captcha.value = captchaData
    } catch (error: any) {
      authError.value = error.message || '获取验证码失败'
      console.error('[🔐AUTH] 获取验证码失败:', error)
    } finally {
      isLoadingCaptcha.value = false
    }
  }

  // 获取token (验证图形验证码) - 简化实现
  const getToken = async (email: string, captchaCode: string, captchaUuid: string): Promise<string | null> => {
    try {
      const response = await authApi.getToken(email, captchaCode, captchaUuid)
      
      console.log('[🔐AUTH] getToken API响应:', response)
      
      if (response) {
        console.log('[🔐AUTH] Token获取成功')
        return response
      } else {
        throw new Error('获取token失败')
      }
    } catch (error: any) {
      console.error('[🔐AUTH] getToken失败:', error)
      authError.value = error.message || '验证图形验证码失败'
      throw error
    }
  }

  // 使用token发送邮箱验证码
  const sendEmailVerificationWithToken = async (email: string, token: string, type: number = 1): Promise<void> => {
    try {
      isLoading.value = true
      authError.value = null

      const response = await authApi.sendEmailCode(email, type, token)
      
      if (response.success) {
        // 启动倒计时
        startCountdown()
      } else {
        throw new Error(response.message || '发送验证码失败')
      }
    } catch (error: any) {
      authError.value = error.message || '发送验证码失败'
      throw error
    } finally {
      isLoading.value = false
    }
  }
  
  // 刷新验证码
  const refreshCaptcha = async (): Promise<void> => {
    await getCaptcha()
  }
  
  // 发送邮箱验证码 - 简化实现（需要先获取token）
  const sendEmailVerificationCode = async (email: string, type: number = 1): Promise<void> => {
    try {
      isLoading.value = true
      authError.value = null

      // 这个方法需要先获取token，建议使用sendEmailVerificationWithToken
      throw new Error('请使用sendEmailVerificationWithToken方法，需要先获取token')
    } catch (error: any) {
      authError.value = error.message || '发送验证码失败'
      throw error
    } finally {
      isLoading.value = false
    }
  }
  
  // 发送邮箱验证码 - 兼容方法
  const sendEmailCode = async (email: string, type: number = 1): Promise<void> => {
    try {
      isCodeLoading.value = true
      authError.value = null

      // 这个方法需要先获取token，建议使用sendEmailVerificationWithToken
      throw new Error('请使用sendEmailVerificationWithToken方法，需要先获取token')
    } catch (error: any) {
      authError.value = error.message || '发送验证码失败'
      throw error
    } finally {
      isCodeLoading.value = false
    }
  }
  
  // 倒计时
  const startCountdown = (seconds: number = 60): void => {
    codeCountdown.value = seconds
    const timer = setInterval(() => {
      codeCountdown.value--
      if (codeCountdown.value <= 0) {
        clearInterval(timer)
      }
    }, 1000)
  }
  
  // 登录 - 简化实现
  const login = async (email: string, password: string, captchaCode: string): Promise<boolean> => {
    try {
      isLoading.value = true
      authError.value = null
      
      if (!captcha.value?.uuid) {
        throw new Error('验证码UUID缺失，请刷新验证码')
      }
      
      console.log('[🔐AUTH] 开始登录流程...')
      
      const response = await authApi.login({
        email,
        password,
        captcha: captchaCode,
        uuid: captcha.value.uuid
      })
      
      if (response && response.success) {
        console.log('[🔐AUTH] 登录成功，开始获取用户信息')
        
        // 设置cookie认证模式
        userStore.authMode = 'cookie'
        if (process.client) {
          localStorage.setItem('authMode', 'cookie')
        }
        
        try {
          // 获取用户信息
          await userStore.fetchCurrentUser()
          console.log('[🔐AUTH] 用户信息获取成功')
          return true
        } catch (userError: any) {
          console.error('[🔐AUTH] 获取用户信息失败:', userError)
          throw new Error('登录成功但获取用户信息失败')
        }
      } else {
        throw new Error(response?.message || '登录失败')
      }
    } catch (error: any) {
      console.error('[🔐AUTH] 登录失败:', error)
      authError.value = error.message || '登录失败'
      
      // 刷新验证码
      await refreshCaptcha()
      return false
    } finally {
      isLoading.value = false
    }
  }
  
  // 注册 - 简化实现
  const register = async (userData: RegisterData): Promise<boolean> => {
    try {
      isLoading.value = true
      authError.value = null
      
      console.log('[🔐AUTH] 开始注册流程...')
      
      const response = await authApi.register(userData)
      
      if (response && response.success) {
        console.log('[🔐AUTH] 注册成功')
        return true
      } else {
        throw new Error(response?.message || '注册失败')
      }
    } catch (error: any) {
      console.error('[🔐AUTH] 注册失败:', error)
      authError.value = error.message || '注册失败'
      return false
    } finally {
      isLoading.value = false
    }
  }
  
  // 重置密码
  const resetPassword = async (resetData: ResetPasswordData): Promise<{ success: boolean; error?: string }> => {
    try {
      isLoading.value = true
      authError.value = null
      
      const response = await authApi.resetPassword(resetData)
      
      if (response.success) {
        return { success: true }
      } else {
        return { success: false, error: response.message || '重置密码失败' }
      }
    } catch (error: any) {
      console.error('[🔐AUTH] 重置密码失败:', error)
      return { success: false, error: error.message || '重置密码失败' }
    } finally {
      isLoading.value = false
    }
  }
  
  // 修改密码
  const changePassword = async (passwordData: ChangePasswordData): Promise<{ success: boolean; error?: string }> => {
    try {
      isLoading.value = true
      authError.value = null
      
      const response = await authApi.changePassword(passwordData)
      
      if (response.code === 0) {
        return { success: true }
      } else {
        return { success: false, error: response.message || '修改密码失败' }
      }
    } catch (error: any) {
      console.error('[🔐AUTH] 修改密码失败:', error)
      return { success: false, error: error.message || '修改密码失败' }
    } finally {
      isLoading.value = false
    }
  }
  
  // 登出
  const logout = async (): Promise<void> => {
    try {
      await authApi.logout()
    } catch (error) {
      console.warn('[🔐AUTH] 登出API调用失败，但继续清除本地状态')
    } finally {
      userStore.clearAuth()
      if (process.client) {
        await navigateTo('/')
      }
    }
  }
  
  // 检查认证状态
  const checkAuthStatus = async (): Promise<boolean> => {
    try {
      const response = await authApi.checkLogin()
      return response.code === 0 && response.data === true
    } catch (error) {
      return false
    }
  }
  
  // 验证token
  const verifyToken = async (): Promise<boolean> => {
    try {
      return await checkAuthStatus()
    } catch (error) {
      return false
    }
  }
  
  // 获取当前用户
  const getCurrentUser = async (): Promise<any> => {
    try {
      const response = await authApi.getCurrentUser()
      if (response.success && response.data) {
        return response.data
      }
      return null
    } catch (error) {
      return null
    }
  }
  
  // 需要认证的路由守卫
  const requireAuth = (): void => {
    if (!userStore.isAuthenticated) {
      throw createError({
        statusCode: 401,
        statusMessage: '需要登录'
      })
    }
  }
  
  // 需要游客状态的路由守卫
  const requireGuest = (): void => {
    if (userStore.isAuthenticated) {
      throw createError({
        statusCode: 403,
        statusMessage: '已登录用户无法访问'
      })
    }
  }
  
  // 初始化认证状态
  const initAuth = async (): Promise<void> => {
    if (process.client) {
      try {
        // 检查是否已登录
        const isLoggedIn = await checkAuthStatus()
        if (isLoggedIn) {
          // 获取用户信息
          await userStore.fetchCurrentUser()
          console.log('[🔐AUTH] 认证状态初始化成功')
        } else {
          console.log('[🔐AUTH] 用户未登录')
        }
      } catch (error) {
        console.warn('[🔐AUTH] 认证状态初始化失败:', error)
        userStore.clearAuth()
      }
    }
  }

  return {
    // 状态
    captcha: readonly(captcha),
    isLoadingCaptcha: isLoadingCaptchaComputed,
    codeCountdown: codeCountdownComputed,
    isLoading: readonly(isLoading),
    isCodeLoading: readonly(isCodeLoading),
    codeSent: readonly(codeSent),
    authError: readonly(authError),
    
    // 方法
    getCaptcha,
    refreshCaptcha,
    getToken,
    sendEmailVerificationCode,
    sendEmailVerificationWithToken,
    sendEmailCode,
    login,
    register,
    resetPassword,
    changePassword,
    logout,
    checkAuthStatus,
    verifyToken,
    getCurrentUser,
    requireAuth,
    requireGuest,
    initAuth
  }
}

// 认证守卫组合函数
export const useAuthGuard = () => {
  const userStore = useUserStore()
  
  const requireAuth = () => {
    if (!userStore.isAuthenticated) {
      throw createError({ statusCode: 401, statusMessage: '需要登录' })
    }
  }
  
  const requireGuest = () => {
    if (userStore.isAuthenticated) {
      throw createError({ statusCode: 403, statusMessage: '已登录用户无法访问' })
    }
  }
  
  return { requireAuth, requireGuest }
} 