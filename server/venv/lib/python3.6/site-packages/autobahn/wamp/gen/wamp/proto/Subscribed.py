# automatically generated by the FlatBuffers compiler, do not modify

# namespace: proto

import flatbuffers

class Subscribed(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAsSubscribed(cls, buf, offset):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = Subscribed()
        x.Init(buf, n + offset)
        return x

    # Subscribed
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # Subscribed
    def Request(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint64Flags, o + self._tab.Pos)
        return 0

    # Subscribed
    def Subscription(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint64Flags, o + self._tab.Pos)
        return 0

def SubscribedStart(builder): builder.StartObject(2)
def SubscribedAddRequest(builder, request): builder.PrependUint64Slot(0, request, 0)
def SubscribedAddSubscription(builder, subscription): builder.PrependUint64Slot(1, subscription, 0)
def SubscribedEnd(builder): return builder.EndObject()
