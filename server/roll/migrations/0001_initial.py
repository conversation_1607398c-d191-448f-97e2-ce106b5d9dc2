# -*- coding: utf-8 -*-
# Generated by Django 1.11.13 on 2021-04-14 02:09
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import steambase.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODELL),
        ('package', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='RollRoom',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('type', models.SmallIntegerField(choices=[(1, 'No charge room'), (2, 'Charge room')], default=1, verbose_name='type')),
                ('name', models.CharField(max_length=64, verbose_name='name')),
                ('desc', models.CharField(blank=True, default=None, max_length=1024, null=True, verbose_name='description')),
                ('password', models.CharField(blank=True, default=None, max_length=64, null=True, verbose_name='password')),
                ('fee', models.FloatField(default=0.0, verbose_name='entrance fee')),
                ('min_joiner', models.IntegerField(default=0, verbose_name='min joiner')),
                ('max_joiner', models.IntegerField(default=0, verbose_name='max joiner')),
                ('max_winner', models.IntegerField(default=0, verbose_name='max winner')),
                ('begin_ts', models.DateTimeField(default=django.utils.timezone.now, verbose_name='begin time')),
                ('due_ts', models.DateTimeField(default=django.utils.timezone.now, verbose_name='due time')),
                ('win_ts', models.DateTimeField(blank=True, default=None, null=True, verbose_name='win time')),
                ('total_fee', models.FloatField(default=0, verbose_name='total fee')),
                ('pump_amount', models.FloatField(default=0, verbose_name='pump amount')),
                ('total_amount', models.FloatField(default=0, verbose_name='total amount')),
                ('items_count', models.IntegerField(default=0, verbose_name='total items count')),
                ('state', models.SmallIntegerField(choices=[(1, 'Initial'), (2, 'Joinable'), (3, 'Joining'), (4, 'Full'), (5, 'Running'), (11, 'End'), (20, 'Cancelled')], default=1, verbose_name='status')),
                ('deposit_enable', models.BooleanField(default=False, verbose_name='deposit enable')),
                ('enable', models.BooleanField(default=True, verbose_name='rollroom enable')),
                ('charge_limit', models.FloatField(blank=True, default=0, null=True, verbose_name='charge limit amount')),
                ('charge_begin_time', models.DateTimeField(blank=True, default=None, null=True, verbose_name='charge begin time')),
                ('charge_end_time', models.DateTimeField(blank=True, default=None, null=True, verbose_name='charge end time')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='roll_room', to=settings.AUTH_USER_MODELL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'Roll Room',
                'verbose_name_plural': 'Roll Room',
                'ordering': ('-create_time',),
            },
        ),
        migrations.CreateModel(
            name='RollRoomBet',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('win_amount', models.FloatField(default=0, verbose_name='win amount')),
                ('win_items_count', models.IntegerField(default=0, verbose_name='win items count')),
                ('victory', models.BooleanField(default=False, verbose_name='victory')),
                ('room', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bets', to='roll.RollRoom', verbose_name='room')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='roll_bets', to=settings.AUTH_USER_MODELL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'Roll Room Bet',
                'verbose_name_plural': 'Roll Room Bet',
                'ordering': ('-create_time',),
            },
        ),
        migrations.CreateModel(
            name='RollroomBot',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('open_idle_min', models.IntegerField(default=0, verbose_name='join idle min(seconds)')),
                ('open_idle_max', models.IntegerField(default=0, verbose_name='join idle max(seconds)')),
                ('remark', models.CharField(max_length=128, verbose_name='remark')),
                ('enable', models.BooleanField(default=True, verbose_name='enable')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='roll_bot', to=settings.AUTH_USER_MODELL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'Rollroom Bot Config',
                'verbose_name_plural': 'Rollroom Bot Config',
            },
        ),
        migrations.CreateModel(
            name='RollRoomItem',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('price', models.FloatField(default=0, verbose_name='price')),
                ('balance', models.FloatField(default=0, verbose_name='balance')),
                ('item_type', models.SmallIntegerField(choices=[(1, 'Assets'), (2, 'Balance')], default=1, verbose_name='item type')),
                ('bet', models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='win_items', to='roll.RollRoomBet', verbose_name='win roll bet')),
                ('package', models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='roll_items', to='package.PackageItem', verbose_name='package item')),
                ('room', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='roll.RollRoom', verbose_name='room')),
            ],
            options={
                'verbose_name': 'Roll Room Item',
                'verbose_name_plural': 'Roll Room Item',
            },
        ),
        migrations.CreateModel(
            name='RollRoomPumpDay',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('date', models.DateField(default=django.utils.timezone.now, verbose_name='date')),
                ('amount', models.FloatField(default=0, verbose_name='amount')),
            ],
            options={
                'verbose_name': 'Roll Room Pump Day',
                'verbose_name_plural': 'Roll Room Pump Day',
                'ordering': ('-create_time',),
            },
        ),
        migrations.CreateModel(
            name='RollRoomPumpMonth',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('date', models.DateField(default=django.utils.timezone.now, verbose_name='date')),
                ('amount', models.FloatField(default=0, verbose_name='amount')),
            ],
            options={
                'verbose_name': 'Roll Room Pump Month',
                'verbose_name_plural': 'Roll Room Pump Month',
                'ordering': ('-create_time',),
            },
        ),
        migrations.CreateModel(
            name='RollroomStatisticsDay',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('date', models.DateField(default=django.utils.timezone.now, verbose_name='date')),
                ('amount', models.FloatField(default=0, verbose_name='game amount')),
                ('journal', models.FloatField(default=0, verbose_name='game journal')),
                ('test_amount', models.FloatField(default=0, verbose_name='test game amount')),
                ('admin_amount', models.FloatField(default=0, verbose_name='admin game amount')),
            ],
            options={
                'verbose_name': 'Rollroom Statistics Day',
                'verbose_name_plural': 'Rollroom Statistics Day',
                'ordering': ('-create_time',),
            },
        ),
        migrations.CreateModel(
            name='RollroomStatisticsMonth',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('date', models.DateField(default=django.utils.timezone.now, verbose_name='date')),
                ('amount', models.FloatField(default=0, verbose_name='game amount')),
                ('journal', models.FloatField(default=0, verbose_name='game journal')),
                ('test_amount', models.FloatField(default=0, verbose_name='test game amount')),
                ('admin_amount', models.FloatField(default=0, verbose_name='admin game amount')),
            ],
            options={
                'verbose_name': 'Rollroom Statistics Month',
                'verbose_name_plural': 'Rollroom Statistics Month',
                'ordering': ('-create_time',),
            },
        ),
    ]
