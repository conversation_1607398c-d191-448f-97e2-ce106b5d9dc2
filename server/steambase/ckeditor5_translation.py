"""
CKEditor 5 字段的 modeltranslation 支持

由于 modeltranslation 不原生支持 CKEditor5Field，
我们需要创建自定义的翻译字段支持。
"""

from django.db import models
from modeltranslation.fields import TranslationField
from django_ckeditor_5.fields import CKEditor5Field


class CKEditor5TranslationField(TranslationField, models.TextField):
    """CKEditor5Field 的翻译字段"""
    
    def __init__(self, translated_field, language, *args, **kwargs):
        # 从原字段复制配置
        if hasattr(translated_field, 'config_name'):
            self.config_name = translated_field.config_name
        else:
            self.config_name = 'default'
            
        # 调用父类初始化
        super().__init__(translated_field, language, *args, **kwargs)
    
    def contribute_to_class(self, cls, name, **kwargs):
        # 确保字段正确添加到模型
        super().contribute_to_class(cls, name, **kwargs)


# 注册 CKEditor5Field 的翻译字段映射
def register_ckeditor5_translation():
    """注册 CKEditor5Field 的翻译支持"""
    from modeltranslation.fields import TRANSLATION_FIELD_MAPPING
    
    # 将 CKEditor5Field 映射到我们的自定义翻译字段
    TRANSLATION_FIELD_MAPPING[CKEditor5Field] = CKEditor5TranslationField


# 自动注册
register_ckeditor5_translation()
