#!/usr/bin/env python
"""
测试articles API接口
"""
import os
import sys
import django
import json

# 添加项目路径
sys.path.append('/www/wwwroot/csgoskins.com.cn/server')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')

# 初始化Django
django.setup()

from django.test import Client
from django.urls import reverse

def test_articles_api():
    """测试articles API接口"""
    
    print("🧪 开始测试Articles API接口...")
    
    client = Client()
    
    # 测试分类列表
    print("\n📁 测试分类列表API...")
    try:
        response = client.get('/api/articles/categories/')
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"分类数量: {len(data)}")
            if data:
                print(f"第一个分类: {data[0]['name']}")
        else:
            print(f"错误: {response.content}")
    except Exception as e:
        print(f"错误: {e}")
    
    # 测试标签列表
    print("\n🏷️  测试标签列表API...")
    try:
        response = client.get('/api/articles/tags/')
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"标签数量: {len(data)}")
            if data:
                print(f"第一个标签: {data[0]['name']}")
        else:
            print(f"错误: {response.content}")
    except Exception as e:
        print(f"错误: {e}")
    
    # 测试内容列表
    print("\n📄 测试内容列表API...")
    try:
        response = client.get('/api/articles/contents/')
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"内容数量: {data.get('count', 0)}")
            results = data.get('results', [])
            if results:
                print(f"第一篇内容: {results[0]['title']}")
                print(f"内容类型: {results[0]['content_type']}")
        else:
            print(f"错误: {response.content}")
    except Exception as e:
        print(f"错误: {e}")
    
    # 测试公告列表
    print("\n📢 测试公告列表API...")
    try:
        response = client.get('/api/articles/announcements/')
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"公告数量: {data.get('count', len(data))}")
            results = data.get('results', data) if isinstance(data, dict) else data
            if results:
                print(f"第一条公告: {results[0]['title']}")
        else:
            print(f"错误: {response.content}")
    except Exception as e:
        print(f"错误: {e}")
    
    # 测试首页公告
    print("\n🏠 测试首页公告API...")
    try:
        response = client.get('/api/articles/announcements/homepage/')
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"首页公告数量: {len(data)}")
            if data:
                print(f"第一条首页公告: {data[0]['title']}")
        else:
            print(f"错误: {response.content}")
    except Exception as e:
        print(f"错误: {e}")
    
    # 测试推荐内容
    print("\n⭐ 测试推荐内容API...")
    try:
        response = client.get('/api/articles/featured/')
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"推荐内容数量: {data.get('count', len(data))}")
            results = data.get('results', data) if isinstance(data, dict) else data
            if results:
                print(f"第一篇推荐内容: {results[0]['title']}")
        else:
            print(f"错误: {response.content}")
    except Exception as e:
        print(f"错误: {e}")
    
    # 测试搜索功能
    print("\n🔍 测试搜索功能...")
    try:
        response = client.get('/api/articles/contents/?search=CSGO')
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"搜索结果数量: {data.get('count', 0)}")
            results = data.get('results', [])
            if results:
                print(f"第一个搜索结果: {results[0]['title']}")
        else:
            print(f"错误: {response.content}")
    except Exception as e:
        print(f"错误: {e}")
    
    # 测试按内容类型过滤
    print("\n📝 测试按内容类型过滤...")
    try:
        response = client.get('/api/articles/contents/?content_type=article')
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"文章数量: {data.get('count', 0)}")
        else:
            print(f"错误: {response.content}")
    except Exception as e:
        print(f"错误: {e}")
    
    print("\n✅ API测试完成！")

def test_database_data():
    """测试数据库中的数据"""
    print("\n🗄️  测试数据库数据...")
    
    from articles.models import ContentCategory, ContentTag, Content
    
    # 检查分类
    categories = ContentCategory.objects.all()
    print(f"数据库中的分类数量: {categories.count()}")
    for cat in categories:
        print(f"  - {cat.name} ({cat.slug})")
    
    # 检查标签
    tags = ContentTag.objects.all()
    print(f"数据库中的标签数量: {tags.count()}")
    for tag in tags[:5]:  # 只显示前5个
        print(f"  - {tag.name} ({tag.slug})")
    
    # 检查内容
    contents = Content.objects.all()
    print(f"数据库中的内容数量: {contents.count()}")
    
    # 按类型统计
    articles = Content.objects.filter(content_type='article')
    announcements = Content.objects.filter(content_type='announcement')
    print(f"  - 文章: {articles.count()}")
    print(f"  - 公告: {announcements.count()}")
    
    # 显示最新的几篇内容
    latest_contents = Content.objects.order_by('-created_at')[:3]
    print("最新内容:")
    for content in latest_contents:
        print(f"  - {content.title} ({content.content_type})")

if __name__ == '__main__':
    test_database_data()
    test_articles_api()
