# Generated by Django 3.2.25 on 2025-07-19 03:46

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('crash', '0002_auto_20250718_1647'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='crashbet',
            options={'ordering': ('-create_time',), 'verbose_name': '崩盘下注', 'verbose_name_plural': 'Crash Bet'},
        ),
        migrations.AlterModelOptions(
            name='crashgame',
            options={'ordering': ('-create_time',), 'verbose_name': '崩盘游戏', 'verbose_name_plural': 'Crash Game'},
        ),
        migrations.AlterModelOptions(
            name='crashpumpday',
            options={'ordering': ('-create_time',), 'verbose_name': '崩盘抽水日统计', 'verbose_name_plural': 'Crash Pump Day'},
        ),
        migrations.AlterModelOptions(
            name='crashpumpmonth',
            options={'ordering': ('-create_time',), 'verbose_name': '崩盘抽水月统计', 'verbose_name_plural': 'Crash Pump Month'},
        ),
        migrations.AlterModelOptions(
            name='crashstatisticsday',
            options={'ordering': ('-create_time',), 'verbose_name': '崩盘日统计', 'verbose_name_plural': 'Crash Statistics Day'},
        ),
        migrations.AlterModelOptions(
            name='crashstatisticsmonth',
            options={'ordering': ('-create_time',), 'verbose_name': '崩盘月统计', 'verbose_name_plural': 'Crash Statistics Month'},
        ),
        migrations.AlterModelOptions(
            name='crashwindayrank',
            options={'verbose_name': '崩盘赢取日排行', 'verbose_name_plural': 'Crash Win Day Rank'},
        ),
        migrations.AlterModelOptions(
            name='crashwinweekrank',
            options={'verbose_name': '崩盘赢取周排行', 'verbose_name_plural': 'Crash Win Week Rank'},
        ),
        migrations.AlterField(
            model_name='crashbet',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='crash_bets', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
        migrations.AlterField(
            model_name='crashwindayrank',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='crash_win_day_rank', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
        migrations.AlterField(
            model_name='crashwinweekrank',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='crash_win_week_rank', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
    ]
