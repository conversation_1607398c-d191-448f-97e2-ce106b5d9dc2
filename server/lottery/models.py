from django.db import models, transaction
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from package.models import ItemInfo
from steambase.enums import ChargeState, PayType
from steambase.models import ModelBase, USER_MODEL, ItemBase
from django_ckeditor_5.fields import CKEditor5Field



class LotterySetting(models.Model):
    LOTTERY_TYPE = (
        (0, _('Hourly')),
        (1, _('Daily')),
        (2, _('Weekly'))
    )
    lottery_type = models.SmallIntegerField(_('lottery type'), default=0, choices=LOTTERY_TYPE)
    item_info = models.ForeignKey(ItemInfo, on_delete=models.CASCADE, verbose_name=_('item info'), related_name='lottery_award')
    min_charge = models.FloatField(_('min charge'), default=0)
    max_joiner = models.IntegerField(_('max joiner'), default=0)
    enable = models.BooleanField(_('enable'), default=True)

    class Meta:
        verbose_name = "抽奖设置"
        verbose_name_plural = _('Lottery Setting')

    def __str__(self):
        return str(self.id)


class LotteryJoiner(ModelBase):
    LOTTERY_TYPE = (
        (0, _('Hourly')),
        (1, _('Daily')),
        (2, _('Weekly'))
    )
    LOTTERY_JOINER_STATE = (
        (0, _('Not enough charge')),
        (1, _('Joined')),
        (2, _('Full')),
        (3, _('End'))
    )
    user = models.ForeignKey(USER_MODEL, on_delete=models.CASCADE, verbose_name = "用户", related_name='lottery_joiner')
    lottery_type = models.SmallIntegerField(_('lottery type'), default=0, choices=LOTTERY_TYPE)
    charge = models.FloatField(_('charge amount'), default=0)
    state = models.SmallIntegerField(_('state'), default=0, choices=LOTTERY_JOINER_STATE)
    win = models.BooleanField(_('win'), default=False)
    item_info = models.ForeignKey(ItemInfo, on_delete=models.SET_NULL, verbose_name=_('item info'), related_name='lottery_joiner',
                                  default=None, null=True, blank=True)
    set_winner = models.BooleanField(_('set winner'), default=False)

    class Meta:
        verbose_name = "抽奖参与者"
        verbose_name_plural = _('Lottery Joiner')

    def __str__(self):
        return self.uid


class LotteryInfoSetting(models.Model):
    LOTTERY_TYPE = (
        (0, _('Hourly')),
        (1, _('Daily')),
        (2, _('Weekly'))
    )
    title = models.CharField(_("title"), max_length=50, default=None, blank=True, null=True)
    comment = models.CharField(_("comment"), max_length=50, default=None, blank=True, null=True)
    content = CKEditor5Field(_('content'), config_name='default', default=None, null=True, blank=True)
    lottery_type = models.SmallIntegerField(_('lottery type'), default=0, choices=LOTTERY_TYPE)
    sort = models.IntegerField(_('sort'), default=0)
    enable = models.BooleanField(_('enable'), default=True)

    class Meta:
        verbose_name = "抽奖信息设置"
        verbose_name_plural = _('Lottery Info Setting')

    def __str__(self):
        return str(self.id)