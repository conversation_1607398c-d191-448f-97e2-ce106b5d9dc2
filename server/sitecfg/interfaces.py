from django.conf import settings
from dateutil.parser import parse as parseDate
from sitecfg.models import SiteConfig


def get_enable_from_site_config(key, default=False):
    config = SiteConfig.objects.filter(key=key).first()
    if config:
        return config.enable
    return default


def get_string_from_site_config(key, default=None):
    config = SiteConfig.objects.filter(key=key).first()
    if config and config.enable:
        return config.value
    return default


def get_int_from_site_config(key, default=0):
    config = SiteConfig.objects.filter(key=key).first()
    if config and config.enable:
        try:
            return int(config.value)
        except:
            return default
    return default


def get_float_from_site_config(key, default=0.0, digit=2):
    config = SiteConfig.objects.filter(key=key).first()
    value = default
    if config and config.enable:
        try:
            value = float(config.value)
        except:
            pass
    return round(value, digit)


def get_box_room_expire():
    conf = get_int_from_site_config(key='box_room_expire', default=900)
    return conf


def get_box_free_interval():
    conf = get_int_from_site_config(key='box_free_interval', default=1)
    return conf


def get_box_free_zero_interval():
    conf = get_int_from_site_config(key='box_free_zero_interval', default=1)
    return conf


def get_box_free_zero_last():
    conf = get_string_from_site_config(key='box_free_zero_last', default='2000-01-01')
    return parseDate(conf).date()


def set_box_free_zero_last(value):
    conf = SiteConfig.objects.filter(key='box_free_zero_last').first()
    if conf:
        conf.value = value
        conf.save()
    else:
        SiteConfig.objects.create(key='box_free_zero_last', value=value, remark='免费箱子（Lv0）上次发放时间')


def get_box_free_limit_game_count():
    conf = get_int_from_site_config(key='box_free_limit_game_count', default=0)
    return conf


def get_box_free_limit_dota2_playtime():
    conf = get_int_from_site_config(key='box_free_limit_dota2_playtime', default=0)
    return conf


def get_withdraw_count_limit():
    return get_int_from_site_config(key='withdraw_count_limit', default=5)


def get_zbt_buy_max_rate():
    return get_float_from_site_config(key='zbt_buy_max_rate', default=0)


def get_withdraw_price_limit():
    return get_float_from_site_config(key='withdraw_price_limit', default=0)


def get_withdraw_total_charge_limit():
    return get_float_from_site_config(key='withdraw_total_charge_limit', default=0)
    

def get_box_festival_expired():
    conf = get_int_from_site_config(key='box_festival_expired', default=1)
    return conf


def get_box_festival_sms_content():
    return get_string_from_site_config('box_festival_sms_content', default=None)


def get_box_chance_chosen():
    conf = get_string_from_site_config(key='box_chance_chosen', default='a')
    return conf


def get_promotion_new_user_reward():
    conf = get_float_from_site_config(key='promotion_new_user_reward', default=settings.PROMOTION_NEW_USER_REWARD)
    return conf


def get_user_point_max():
    conf = get_float_from_site_config(key='user_point_max', default=settings.USER_POINT_MAX)
    return conf


def get_jackpot_pump_percentage():
    conf = get_float_from_site_config(key='jackpot_pump_percentage', default=0)
    return conf / 100


def get_double_pump_percentage():
    conf = get_float_from_site_config(key='double_pump_percentage', default=0)
    return conf / 100


def get_roll_pump_percentage():
    conf = get_float_from_site_config(key='roll_pump_percentage', default=0)
    return conf / 100

def get_roll_join_min_charge_limit():
    return get_int_from_site_config(key='roll_join_min_charge_limit', default=1)

def get_roll_due_day_max():
    return get_int_from_site_config(key='roll_due_day_max', default=90)


def get_roll_create_count_max():
    return get_int_from_site_config(key='roll_create_count_max', default=3)


def get_exchange_rate(currency, default=1):
    key = '{currency}_exchange_rate'.format(currency=currency)
    return get_float_from_site_config(key=key, default=default, digit=4)


def get_online_users_base_count():
    return get_int_from_site_config(key='online_users_base_count', default=0)

def get_users_base_count():
    return get_int_from_site_config(key='users_base_count', default=0)




def get_price_api_chosen():
    return get_int_from_site_config(key='price_api_chosen', default=1)


def get_deposit_price_min():
    return get_float_from_site_config(key='deposit_price_min', default=0.01)


def get_diamond_to_coins_exchange_rate():
    return get_float_from_site_config(key='diamond_to_coins_exchange_rate', default=1)


def get_admin_sms_msg():
    return get_string_from_site_config('admin_sms_msg')


def get_maintenance():
    return get_enable_from_site_config('maintenance')

def get_maintenance_box_room():
    return get_enable_from_site_config('maintenance_box_room')


def get_custombox_maintenance():
    return get_enable_from_site_config('custombox_maintenance')


def get_maintenance_deposit():
    return get_enable_from_site_config('maintenance_deposit')


def get_maintenance_msg_deposit():
    return get_string_from_site_config('maintenance_deposit')


def get_maintenance_withdraw():
    return get_enable_from_site_config('maintenance_withdraw')


def get_maintenance_msg_withdraw():
    return get_string_from_site_config('maintenance_withdraw')


def get_maintenance_exchange():
    return get_enable_from_site_config('maintenance_exchange')


def get_maintenance_shop():
    return get_enable_from_site_config('maintenance_shop')


def get_maintenance_charge():
    return get_enable_from_site_config('maintenance_charge')


def get_maintenance_jackpot():
    return get_enable_from_site_config('maintenance_jackpot')


def get_maintenance_double():
    return get_enable_from_site_config('maintenance_double')


def get_maintenance_roll():
    return get_enable_from_site_config('maintenance_roll')


def get_maintenance_box():
    return get_enable_from_site_config('maintenance_box')


def get_maintenance_tradeup():
    return get_enable_from_site_config('maintenance_tradeup')


def get_maintenance_luckybox():
    return get_enable_from_site_config('maintenance_luckybox')


def get_tradeup_bet_percentage_max():
    return get_float_from_site_config(key='tradeup_bet_percentage_max', default=85)


def get_tradeup_bet_rate():
    return get_float_from_site_config(key='tradeup_bet_percentage', default=1)


def get_tradeup_bet_amount_min():
    return get_float_from_site_config(key='roulette_bet_amount_min', default=1)


def get_tradeup_bet_amount_max():
    return get_float_from_site_config(key='tradeup_bet_amount_max', default=999999999)


def get_tradeup_targets_max_limit():
    return get_int_from_site_config(key='tradeup_targets_max_limit', default=6)


def get_maintenance_crash():
    return get_enable_from_site_config('maintenance_crash')


def get_maintenance_envelop():
    return get_enable_from_site_config('maintenance_envelop')


def get_crash_percentage_discount():
    return get_float_from_site_config(key='crash_percentage_discount', default=100)


def get_crash_run_countdown():
    return get_int_from_site_config(key='crash_run_countdown', default=20)


def get_crash_end_countdown():
    return get_int_from_site_config(key='crash_end_countdown', default=4)


def get_crash_bet_amount_max():
    return get_float_from_site_config(key='crash_bet_amount_max', default=999999999)


def get_charge_rate():
    return get_float_from_site_config(key='charge_rate', default=6.7)


def get_withdraw_delivery():
    return get_int_from_site_config(key='withdraw_delivery', default=0)


def get_luckybox_rate_type(type):
    rate = get_float_from_site_config(key='luckybox_rate_type_{}'.format(type))
    if rate:
        return rate
    return get_float_from_site_config(key='luckybox_rate_type_a')


def get_luckyboxrate():
    rate = get_float_from_site_config(key='luckybox_rate', default=100)
    return rate


def get_custombox_rate():
    rate = get_float_from_site_config(key='custombox_rate', default=10)
    return round((100 + rate) / 100, 2)


def get_box_chance_previous_times():
    return get_int_from_site_config(key="chance_previous_times", default=3)


def get_box_chance_previous_type():
    return get_string_from_site_config(key="chance_previous_type", default='a')


def get_box_previous_enable():
    return get_enable_from_site_config(key="chance_previous")


def get_first_charge_handsel():
    return get_float_from_site_config(key='first_charge_handsel', default=3)


def get_register_coins():
    return get_float_from_site_config(key="register_coins", default=0.88)


def get_freegive_box_count():
    return get_int_from_site_config(key="freegive_box_count", default=3)

def get_cases_max():
    return get_int_from_site_config(key="box_room_create_cases_max_count")

def get_luckybox_lower_percent():
    return get_float_from_site_config(key="luckybox_lower_percent", default=5.0)

def get_first_charge_handsel():
    return get_float_from_site_config(key="first_charge_handsel", default=1)

def get_register_box_chance_type():
    return get_string_from_site_config(key="register_box_chance_type", default='c')

def get_recharge_box_chance_type():
    return get_string_from_site_config(key="recharge_box_chance_type", default='c')

def get_domain_verify():
    return get_string_from_site_config(key="domain_verify")

def get_admin_msg_mobile():
    return get_string_from_site_config(key="msg_mobile", default='18113051550')

def get_roll_bet_join():
    return get_string_from_site_config(key="roll_bet_join", default=0)


def get_pk_bot_interval_time():
    return get_string_from_site_config(key="pk_bot_interval_time", default=120)

def get_pk_bot_join_interval_time():
    return get_string_from_site_config(key="pk_bot_join_interval_time", default=60)

def get_open_base_count():
    return get_int_from_site_config(key='open_base_count', default=0)

def get_pk_base_count():
    return get_int_from_site_config(key='pk_base_count', default=0)

# 开箱机器人最大
def get_box_bot_num_max():
    return get_int_from_site_config(key='box_bot_num_max', default=20)

# 对战机器人最大
def get_pk_bot_num_max():
    return get_int_from_site_config(key='pk_bot_num_max', default=20)

# 动态开箱概率
def get_enable_dynamic_box_chance():
    return get_float_from_site_config(key='enable_dynamic_box_chance', default=0)

# 启用充值机器人
def get_enable_recharge_bot():

    return get_enable_from_site_config(key='enable_recharge_bot', default=1)

# 启用每日充值限制
def get_enable_recharge_limit():
    return get_enable_from_site_config(key='enable_recharge_limit', default=1)

# 每日充值限制金额
def get_daily_recharge_limit():
    return get_float_from_site_config(key='daily_recharge_limit', default=100)

# 新用户充值限制
def get_new_user_recharge_limit():
    return get_float_from_site_config(key='new_user_recharge_limit', default=100)

    
# 是否启动规则考试
def get_enable_rule_exam():
    return get_enable_from_site_config(key='enable_rule_exam', default=False)

# 启用缓存
def get_enable_cache():
    return get_enable_from_site_config(key='enable_cache', default=False)

# 启用邮箱后缀限制
def get_access_control_email_suffix():
    return get_enable_from_site_config(key='access_control_email_suffix', default=False)

# 启用IP限制
def get_access_control_ip():
    return get_enable_from_site_config(key='access_control_ip', default=False)

# 启用steam限制
def get_access_control_steam():
    return get_enable_from_site_config(key='access_control_steam', default=False)

# 启用 token 限制
def get_access_control_token():
    return get_enable_from_site_config(key='access_control_token', default=False)

# token 限制数量 access_control_token的 value
def get_access_control_token_limit():
    return get_string_from_site_config(key='access_control_token', default=10)

