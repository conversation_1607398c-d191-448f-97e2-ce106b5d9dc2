# Generated by Django 3.2.25 on 2025-07-19 14:08

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import steambase.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Site',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('domain', models.CharField(max_length=255, unique=True, verbose_name='Domain')),
                ('name', models.Char<PERSON>ield(max_length=128, verbose_name='Site Name')),
                ('subtitle', models.CharField(blank=True, max_length=128, null=True, verbose_name='Subtitle')),
                ('logo_url', models.URLField(blank=True, null=True, verbose_name='Logo URL')),
                ('favicon_url', models.URLField(blank=True, null=True, verbose_name='Favicon URL')),
                ('seo_title', models.CharField(blank=True, max_length=128, null=True, verbose_name='SEO Title')),
                ('seo_keywords', models.CharField(blank=True, max_length=255, null=True, verbose_name='SEO Keywords')),
                ('seo_description', models.TextField(blank=True, max_length=500, null=True, verbose_name='SEO Description')),
                ('icp_number', models.CharField(blank=True, max_length=128, null=True, verbose_name='ICP Number')),
                ('articles_enabled', models.BooleanField(default=True, verbose_name='Articles Enabled')),
                ('announcements_enabled', models.BooleanField(default=True, verbose_name='Announcements Enabled')),
                ('case_battle_enabled', models.BooleanField(default=True, verbose_name='Case Battle Enabled')),
                ('case_opening_enabled', models.BooleanField(default=True, verbose_name='Case Opening Enabled')),
                ('market_enabled', models.BooleanField(default=True, verbose_name='Market Enabled')),
                ('lottery_enabled', models.BooleanField(default=True, verbose_name='Lottery Enabled')),
                ('roll_enabled', models.BooleanField(default=True, verbose_name='Roll Enabled')),
                ('crash_enabled', models.BooleanField(default=True, verbose_name='Crash Enabled')),
                ('primary_color', models.CharField(default='#007bff', max_length=7, verbose_name='Primary Color')),
                ('secondary_color', models.CharField(default='#6c757d', max_length=7, verbose_name='Secondary Color')),
                ('custom_css', models.TextField(blank=True, null=True, verbose_name='Custom CSS')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('is_default', models.BooleanField(default=False, verbose_name='Is Default')),
                ('maintenance_mode', models.BooleanField(default=False, verbose_name='Maintenance Mode')),
                ('maintenance_message', models.TextField(blank=True, null=True, verbose_name='Maintenance Message')),
            ],
            options={
                'verbose_name': 'Site',
                'verbose_name_plural': 'Sites',
                'ordering': ['domain'],
            },
        ),
        migrations.CreateModel(
            name='SiteTheme',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('theme_name', models.CharField(default='default', max_length=64, verbose_name='Theme Name')),
                ('header_background', models.CharField(default='#ffffff', max_length=7, verbose_name='Header Background')),
                ('header_text_color', models.CharField(default='#000000', max_length=7, verbose_name='Header Text Color')),
                ('footer_background', models.CharField(default='#f8f9fa', max_length=7, verbose_name='Footer Background')),
                ('footer_text_color', models.CharField(default='#6c757d', max_length=7, verbose_name='Footer Text Color')),
                ('font_family', models.CharField(default='Arial, sans-serif', max_length=128, verbose_name='Font Family')),
                ('font_size_base', models.IntegerField(default=14, verbose_name='Base Font Size')),
                ('container_width', models.CharField(default='1200px', max_length=20, verbose_name='Container Width')),
                ('border_radius', models.IntegerField(default=4, verbose_name='Border Radius')),
                ('site', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='theme', to='sitegroup.site', verbose_name='Site')),
            ],
            options={
                'verbose_name': 'Site Theme',
                'verbose_name_plural': 'Site Themes',
            },
        ),
        migrations.CreateModel(
            name='SiteGroup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('name', models.CharField(max_length=128, verbose_name='Group Name')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('agent', models.ForeignKey(limit_choices_to={'is_active': True, 'is_agent': True}, on_delete=django.db.models.deletion.CASCADE, related_name='site_groups', to=settings.AUTH_USER_MODEL, verbose_name='Agent')),
            ],
            options={
                'verbose_name': 'Site Group',
                'verbose_name_plural': 'Site Groups',
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='site',
            name='site_group',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sites', to='sitegroup.sitegroup', verbose_name='Site Group'),
        ),
        migrations.CreateModel(
            name='SiteConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('config_key', models.CharField(max_length=64, verbose_name='Config Key')),
                ('config_value', models.TextField(blank=True, null=True, verbose_name='Config Value')),
                ('config_type', models.CharField(choices=[('string', 'String'), ('integer', 'Integer'), ('float', 'Float'), ('boolean', 'Boolean'), ('json', 'JSON'), ('url', 'URL'), ('email', 'Email')], default='string', max_length=20, verbose_name='Config Type')),
                ('category', models.CharField(default='general', max_length=64, verbose_name='Category')),
                ('description', models.CharField(blank=True, max_length=255, null=True, verbose_name='Description')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('is_sensitive', models.BooleanField(default=False, verbose_name='Is Sensitive')),
                ('site', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='configs', to='sitegroup.site', verbose_name='Site')),
            ],
            options={
                'verbose_name': 'Site Config',
                'verbose_name_plural': 'Site Configs',
                'ordering': ['category', 'config_key'],
                'unique_together': {('site', 'config_key')},
            },
        ),
        migrations.CreateModel(
            name='SiteAnalytics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('date', models.DateField(verbose_name='Date')),
                ('page_views', models.IntegerField(default=0, verbose_name='Page Views')),
                ('unique_visitors', models.IntegerField(default=0, verbose_name='Unique Visitors')),
                ('new_registrations', models.IntegerField(default=0, verbose_name='New Registrations')),
                ('active_users', models.IntegerField(default=0, verbose_name='Active Users')),
                ('case_openings', models.IntegerField(default=0, verbose_name='Case Openings')),
                ('battles_created', models.IntegerField(default=0, verbose_name='Battles Created')),
                ('market_transactions', models.IntegerField(default=0, verbose_name='Market Transactions')),
                ('site', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='analytics', to='sitegroup.site', verbose_name='Site')),
            ],
            options={
                'verbose_name': 'Site Analytics',
                'verbose_name_plural': 'Site Analytics',
                'ordering': ['-date'],
                'unique_together': {('site', 'date')},
            },
        ),
        migrations.AddIndex(
            model_name='site',
            index=models.Index(fields=['domain'], name='sitegroup_s_domain_5cf253_idx'),
        ),
        migrations.AddIndex(
            model_name='site',
            index=models.Index(fields=['is_active'], name='sitegroup_s_is_acti_ed9bd5_idx'),
        ),
        migrations.AddIndex(
            model_name='site',
            index=models.Index(fields=['is_default'], name='sitegroup_s_is_defa_ec5209_idx'),
        ),
    ]
