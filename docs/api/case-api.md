# 箱子系统 API 文档

## 概述

本文档描述了箱子系统的所有API接口，包括箱子列表查询、开箱操作、箱子详情、记录查询等功能。

## 基础信息

- **基础URL**: `/api/box/`
- **Content-Type**: `application/json`
- **认证方式**: Session认证
- **响应格式**: JSON

## 响应状态码

| 状态码 | 含义 | 说明 |
|--------|------|------|
| 0 | Succeed | 请求成功 |
| 100 | NotLogin | 未登录 |
| 101 | BusinessError | 业务错误 |
| 102 | InvalidParams | 参数无效 |
| 103 | NoBalance | 余额不足 |
| 401 | Maintenance | 系统维护 |
| 500 | Exception | 服务器内部错误 |

## 通用响应格式

```json
{
    "code": 0,
    "body": {},
    "message": "Succeed"
}
```

---

## 1. 箱子列表相关接口

### 1.1 搜索箱子

**接口**: `GET /api/box/search/`

**描述**: 根据关键词搜索箱子

**请求参数**:
- `q`: 搜索关键词（可选）
- `type`: 箱子类型ID（可选）
- `category`: 箱子分类ID（可选）
- `enable_room`: 是否支持对战房间，1=支持，0=不支持（可选）

**响应示例**:
```json
{
    "code": 0,
    "body": {
        "items": [
            {
                "cate_id": 1,
                "cate_name": "武器箱",
                "cate_name_en": "Weapon Case",
                "cate_name_zh_hans": "武器箱",
                "icon": "https://example.com/icon.jpg",
                "cases": [
                    {
                        "case_key": "ak47_redline_case",
                        "name": "AK-47 红线箱子",
                        "name_en": "AK-47 Redline Case",
                        "name_zh_hans": "AK-47 红线箱子",
                        "price": 10.50,
                        "open_count": 12345,
                        "discount": 0.9,
                        "cover": "https://example.com/cover.jpg",
                        "tag": "HOT",
                        "tag_zh_hans": "热门",
                        "tag_en": "Hot",
                        "tag_color": "#ff0000",
                        "enable_room": true,
                        "type_id": 1,
                        "type_name": "普通箱子",
                        "type_name_en": "Normal Case",
                        "type_name_zh_hans": "普通箱子"
                    }
                ]
            }
        ]
    },
    "message": "成功"
}
```

### 1.2 获取箱子分类

**接口**: `GET /api/box/category/`

**描述**: 获取所有箱子分类

**请求参数**: 无

**响应示例**:
```json
{
    "code": 0,
    "body": {
        "items": [
            {
                "cate_id": 1,
                "cate_name": "武器箱",
                "cate_name_en": "Weapon Case",
                "cate_name_zh_hans": "武器箱",
                "icon": "https://example.com/icon.jpg"
            }
        ]
    },
    "message": "Succeed"
}
```

### 1.3 获取箱子类型

**接口**: `GET /api/box/type/`

**描述**: 获取所有箱子类型

**请求参数**: 无

**响应示例**:
```json
{
    "code": 0,
    "body": {
        "items": [
            {
                "type_id": 1,
                "type_name": "普通箱子",
                "type_name_en": "Normal Case",
                "type_name_zh_hans": "普通箱子"
            }
        ]
    },
    "message": "Succeed"
}
```

### 1.4 按标签获取箱子

**接口**: `GET /api/box/tag/`

**描述**: 根据标签获取箱子列表

**请求参数**:
- `q`: 标签名称（必需）
- `num`: 返回数量，默认5

**响应示例**:
```json
{
    "code": 0,
    "body": {
        "items": [
            {
                "id": 1,
                "uid": "case123",
                "case_key": "ak47_redline_case",
                "name": "AK-47 红线箱子",
                "name_en": "AK-47 Redline Case",
                "name_zh_hans": "AK-47 红线箱子",
                "price": 10.50,
                "open_count": 12345,
                "discount": 0.9,
                "cover": "https://example.com/cover.jpg",
                "tag": "HOT",
                "tag_en": "Hot",
                "tag_zh_hans": "热门",
                "tag_color": "#ff0000",
                "cate_id": 1,
                "cate_name": "武器箱",
                "cate_name_en": "Weapon Case",
                "cate_name_zh_hans": "武器箱",
                "type_name": "普通箱子",
                "type_name_en": "Normal Case",
                "type_name_zh_hans": "普通箱子",
                "create_time": "2023-12-01T10:30:00Z"
            }
        ]
    },
    "message": "Succeed"
}
```

---

## 2. 箱子详情相关接口

### 2.1 获取箱子详情

**接口**: `GET /api/box/detail/`

**描述**: 获取指定箱子的详细信息

**请求参数**:
- `key`: 箱子key（必需）

**响应示例**:
```json
{
    "code": 0,
    "body": {
        "id": 1,
        "uid": "case123",
        "case_key": "ak47_redline_case",
        "name": "AK-47 红线箱子",
        "name_en": "AK-47 Redline Case",
        "name_zh_hans": "AK-47 红线箱子",
        "price": 10.50,
        "open_count": 12345,
        "discount": 0.9,
        "cover": "https://example.com/cover.jpg",
        "cate_id": 1,
        "cate_name": "武器箱",
        "cate_name_en": "Weapon Case",
        "cate_name_zh_hans": "武器箱",
        "tag": "HOT",
        "tag_en": "Hot",
        "tag_zh_hans": "热门",
        "tag_color": "#ff0000",
        "type_name": "普通箱子",
        "type_name_en": "Normal Case",
        "type_name_zh_hans": "普通箱子"
    },
    "message": "Succeed"
}
```

### 2.2 获取箱子内物品

**接口**: `GET /api/box/skins/`

**描述**: 获取指定箱子内的所有物品及其爆率

**请求参数**:
- `key`: 箱子key（必需）

**响应示例**:
```json
{
    "code": 0,
    "body": {
        "items": [
            {
                "rarity_id": 5,
                "rarity_name": "隐秘",
                "rarity_name_en": "Covert",
                "rarity_name_zh_hans": "隐秘",
                "rarity_color": "#eb4b4b",
                "count": 2,
                "count_percentage": 15.38,
                "items": [
                    {
                        "item_info": {
                            "id": 123,
                            "name": "AK-47 | 红线 (崭新出厂)",
                            "name_en": "AK-47 | Redline (Factory New)",
                            "name_zh_hans": "AK-47 | 红线 (崭新出厂)",
                            "image": "https://steamcommunity-a.akamaihd.net/economy/image/...",
                            "item_price": {
                                "price": 125.50,
                                "update_time": "2023-12-01T10:30:00Z"
                            },
                            "item_category": {
                                "cate_id": 1,
                                "cate_name": "步枪",
                                "cate_name_en": "Rifle",
                                "cate_name_zh_hans": "步枪",
                                "icon": "rifle.png"
                            },
                            "item_quality": {
                                "quality_id": 1,
                                "quality_name": "崭新出厂",
                                "quality_name_en": "Factory New",
                                "quality_name_zh_hans": "崭新出厂",
                                "quality_color": "#4b69ff"
                            },
                            "item_rarity": {
                                "rarity_id": 5,
                                "rarity_name": "隐秘",
                                "rarity_name_en": "Covert",
                                "rarity_name_zh_hans": "隐秘",
                                "rarity_color": "#eb4b4b"
                            },
                            "item_exterior": {
                                "exterior_id": 1,
                                "exterior_name": "崭新出厂",
                                "exterior_name_en": "Factory New",
                                "exterior_name_zh_hans": "崭新出厂",
                                "exterior_color": "#4b69ff"
                            }
                        },
                        "chance": 0.0256
                    }
                ]
            }
        ],
        "total": 13
    },
    "message": "Succeed"
}
```

### 2.3 获取箱子开启记录

**接口**: `GET /api/box/records/`

**描述**: 获取指定箱子的最近开启记录（全站用户）

**请求参数**:
- `key`: 箱子key（必需）
- `num`: 返回记录数量，默认30

**响应示例**:
```json
{
    "code": 0,
    "body": {
        "items": [
            {
                "user_info": {
                    "uid": "user123",
                    "profile": {
                        "nick_name": "玩家昵称",
                        "avatar": "https://example.com/avatar.jpg"
                    }
                },
                "item_info": {
                    "id": 123,
                    "name": "AK-47 | 红线 (崭新出厂)",
                    "name_en": "AK-47 | Redline (Factory New)",
                    "name_zh_hans": "AK-47 | 红线 (崭新出厂)",
                    "image": "https://steamcommunity-a.akamaihd.net/economy/image/...",
                    "item_price": {
                        "price": 125.50,
                        "update_time": "2023-12-01T10:30:00Z"
                    },
                    "item_category": {
                        "cate_id": 1,
                        "cate_name": "步枪",
                        "cate_name_en": "Rifle",
                        "cate_name_zh_hans": "步枪",
                        "icon": "rifle.png"
                    },
                    "item_quality": {
                        "quality_id": 1,
                        "quality_name": "崭新出厂",
                        "quality_name_en": "Factory New",
                        "quality_name_zh_hans": "崭新出厂",
                        "quality_color": "#4b69ff"
                    },
                    "item_rarity": {
                        "rarity_id": 5,
                        "rarity_name": "隐秘",
                        "rarity_name_en": "Covert",
                        "rarity_name_zh_hans": "隐秘",
                        "rarity_color": "#eb4b4b"
                    },
                    "item_exterior": {
                        "exterior_id": 1,
                        "exterior_name": "崭新出厂",
                        "exterior_name_en": "Factory New",
                        "exterior_name_zh_hans": "崭新出厂",
                        "exterior_color": "#4b69ff"
                    }
                },
                "case_info": {
                    "id": 1,
                    "case_key": "ak47_redline_case",
                    "name": "AK-47 红线箱子",
                    "name_en": "AK-47 Redline Case",
                    "name_zh_hans": "AK-47 红线箱子",
                    "cover": "https://example.com/cover.jpg",
                    "price": 10.50,
                    "open_count": 12345
                },
                "price": 125.50,
                "create_time": "2023-12-01T10:30:00Z"
            }
        ]
    },
    "message": "Succeed"
}
```

---

## 3. 开箱相关接口

### 3.1 开箱

**接口**: `POST /api/box/open/`

**描述**: 开启指定箱子

**权限**: 需要登录

**请求参数**:
```json
{
    "case_key": "ak47_redline_case",
    "count": 1,
    "simulate": false
}
```

**参数说明**:
- `case_key`: 箱子key（必需）
- `count`: 开箱数量，默认1
- `simulate`: 是否模拟开箱，true=模拟，false=真实开箱，默认false

**响应示例**:
```json
{
    "code": 0,
    "body": {
        "items": [
            {
                "uid": "item123",
                "item_type": 1,
                "id": 123,
                "name": "AK-47 | 红线 (崭新出厂)",
                "name_en": "AK-47 | Redline (Factory New)",
                "name_zh_hans": "AK-47 | 红线 (崭新出厂)",
                "image": "https://steamcommunity-a.akamaihd.net/economy/image/...",
                "item_price": {
                    "price": 125.50,
                    "update_time": "2023-12-01T10:30:00Z"
                },
                "item_category": {
                    "cate_id": 1,
                    "cate_name": "步枪",
                    "cate_name_en": "Rifle",
                    "cate_name_zh_hans": "步枪",
                    "icon": "rifle.png"
                },
                "item_quality": {
                    "quality_id": 1,
                    "quality_name": "崭新出厂",
                    "quality_name_en": "Factory New",
                    "quality_name_zh_hans": "崭新出厂",
                    "quality_color": "#4b69ff"
                },
                "item_rarity": {
                    "rarity_id": 5,
                    "rarity_name": "隐秘",
                    "rarity_name_en": "Covert",
                    "rarity_name_zh_hans": "隐秘",
                    "rarity_color": "#eb4b4b"
                },
                "item_exterior": {
                    "exterior_id": 1,
                    "exterior_name": "崭新出厂",
                    "exterior_name_en": "Factory New",
                    "exterior_name_zh_hans": "崭新出厂",
                    "exterior_color": "#4b69ff"
                },
                "pid": "package123"
            }
        ]
    },
    "message": "Succeed"
}
```

**错误响应**:
```json
{
    "code": 103,
    "body": {},
    "message": "余额不足"
}
```

---

## 4. 使用示例

### 完整开箱流程

```javascript
// 1. 搜索想要的箱子
const searchResponse = await fetch('/api/box/search/?q=AK47');
const searchData = await searchResponse.json();

// 2. 获取箱子详情
const detailResponse = await fetch('/api/box/detail/?key=ak47_redline_case');
const detailData = await detailResponse.json();

// 3. 查看箱子内物品
const skinsResponse = await fetch('/api/box/skins/?key=ak47_redline_case');
const skinsData = await skinsResponse.json();

// 4. 开箱
const openResponse = await fetch('/api/box/open/', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        case_key: 'ak47_redline_case',
        count: 1,
        simulate: false
    })
});
const openResult = await openResponse.json();

// 5. 查看开箱记录
const recordsResponse = await fetch('/api/box/records/?key=ak47_redline_case&num=10');
const recordsData = await recordsResponse.json();
```

### 获取分类和搜索

```javascript
// 获取所有分类
const categoryResponse = await fetch('/api/box/category/');
const categories = await categoryResponse.json();

// 获取所有类型
const typeResponse = await fetch('/api/box/type/');
const types = await typeResponse.json();

// 按分类和类型搜索
const searchResponse = await fetch('/api/box/search/?category=1&type=1&enable_room=1');
const searchResults = await searchResponse.json();
```

---

## 5. 字段说明

### 5.1 物品信息字段 (item_info)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `id` | Integer | 物品ID |
| `name` | String | 物品名称（默认） |
| `name_en` | String | 物品英文名称 |
| `name_zh_hans` | String | 物品简体中文名称 |
| `image` | String | 物品图片URL |
| `item_price` | Object | 价格信息 |
| `item_category` | Object | 分类信息 |
| `item_quality` | Object | 品质信息 |
| `item_rarity` | Object | 稀有度信息 |
| `item_exterior` | Object | 外观信息 |

### 5.2 价格信息字段 (item_price)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `price` | Float | 物品价格 |
| `update_time` | String | 价格更新时间 |

### 5.3 分类信息字段 (item_category)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `cate_id` | Integer | 分类ID |
| `cate_name` | String | 分类名称（默认） |
| `cate_name_en` | String | 分类英文名称 |
| `cate_name_zh_hans` | String | 分类简体中文名称 |
| `icon` | String | 分类图标 |

### 5.4 品质信息字段 (item_quality)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `quality_id` | Integer | 品质ID |
| `quality_name` | String | 品质名称（默认） |
| `quality_name_en` | String | 品质英文名称 |
| `quality_name_zh_hans` | String | 品质简体中文名称 |
| `quality_color` | String | 品质颜色 |

### 5.5 稀有度信息字段 (item_rarity)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `rarity_id` | Integer | 稀有度ID |
| `rarity_name` | String | 稀有度名称（默认） |
| `rarity_name_en` | String | 稀有度英文名称 |
| `rarity_name_zh_hans` | String | 稀有度简体中文名称 |
| `rarity_color` | String | 稀有度颜色 |

### 5.6 外观信息字段 (item_exterior)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `exterior_id` | Integer | 外观ID |
| `exterior_name` | String | 外观名称（默认） |
| `exterior_name_en` | String | 外观英文名称 |
| `exterior_name_zh_hans` | String | 外观简体中文名称 |
| `exterior_color` | String | 外观颜色 |

---

## 6. 错误处理

### 常见错误码及处理

1. **未登录 (100)**
   ```json
   {
       "code": 100,
       "body": {},
       "message": "用户未登录"
   }
   ```

2. **参数错误 (102)**
   ```json
   {
       "code": 102,
       "body": {},
       "message": "箱子key不能为空"
   }
   ```

3. **余额不足 (103)**
   ```json
   {
       "code": 103,
       "body": {},
       "message": "余额不足，无法开箱"
   }
   ```

4. **系统维护 (401)**
   ```json
   {
       "code": 401,
       "body": {},
       "message": "系统维护中，请稍后再试"
   }
   ```

---

## 7. 注意事项

1. **开箱限制**: 单次开箱数量有限制，通常最多10个
2. **余额检查**: 开箱前会检查用户余额是否足够
3. **维护状态**: 系统维护期间开箱功能会被禁用
4. **模拟开箱**: 可以使用模拟模式预览开箱结果，不消耗余额
5. **记录保存**: 所有开箱记录都会被保存，可供用户查询
6. **国际化支持**: 所有名称字段都提供多语言版本（name、name_en、name_zh_hans）
7. **价格信息**: 物品价格以对象形式返回，包含价格和更新时间
8. **分类信息**: 物品包含完整的分类、品质、稀有度、外观信息

---

## 8. 更新日志

- **2024-12-29**: ✅ **文档格式全面修正** - 修正所有与实际代码不一致的地方：
  - **响应格式修正**: ✅ 修正所有API响应示例，使其与实际代码实现完全一致
  - **字段名称修正**: ✅ 修正字段命名（market_name → name，market_name_cn → name_zh_hans，icon_url → image）
  - **数据结构修正**: ✅ 修正价格字段结构（price → item_price对象）
  - **分类信息补充**: ✅ 补充完整的item_category、item_quality、item_rarity、item_exterior信息
  - **国际化字段**: ✅ 确保所有名称字段包含name、name_en、name_zh_hans三个版本
  - **字段说明完善**: ✅ 新增详细的字段说明表格，便于前端开发参考
  - **注意事项更新**: ✅ 补充国际化支持、价格信息、分类信息等重要说明
- **2025-06-16**: 初始文档版本

最后更新时间: 2024-12-29
