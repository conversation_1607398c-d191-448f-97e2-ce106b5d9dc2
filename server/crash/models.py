from django.db import models, transaction
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from steambase.models import ModelBase, USER_MODEL
from steambase.enums import GameState, WinResult
from steambase.utils import current_week, current_year


class CrashGame(ModelBase):
    GAME_STATE = (
        (GameState.Initial.value, _("Initial")),
        (GameState.Joinable.value, _("Joinable")),
        (GameState.Joining.value, _("Joining")),
        (GameState.Full.value, _("Full")),
        (GameState.Running.value, _("Running")),
        (GameState.End.value, _("End")),
        (GameState.Cancelled.value, _("Cancelled")),
    )
    hash = models.CharField(_("hash"), max_length=255)
    secret = models.CharField(_("secret"), max_length=32)
    percentage = models.FloatField(_("percentage"), default=0)
    crash_point = models.FloatField(_("crash point"), default=0)
    total_amount = models.FloatField(_("total amount"), default=0)
    pump_amount = models.FloatField(_('pump amount'), default=0)
    win_amount = models.FloatField(_("win amount"), default=0)
    joinable_time = models.DateTimeField(_("joinable time"), default=None, null=True, blank=True)
    run_time = models.DateTimeField(_("run time"), default=None, null=True, blank=True)
    end_time = models.DateTimeField(_("end time"), default=None, null=True, blank=True)
    state = models.SmallIntegerField(_("status"), default=GameState.Initial.value, choices=GAME_STATE)

    class Meta:
        ordering = ('-create_time',)
        verbose_name = _('Crash Game')
        verbose_name_plural = _('Crash Game')

    def __str__(self):
        return str(self.id)


class CrashBet(ModelBase):
    WIN_RESULT_TYPE = (
        (WinResult.NotEnd.value, _('Not End')),
        (WinResult.Win.value, _('Win')),
        (WinResult.Lose.value, _('Lose'))
    )
    user = models.ForeignKey(USER_MODEL, on_delete=models.CASCADE, verbose_name=_("user"), related_name='crash_bets')
    game = models.ForeignKey(CrashGame, on_delete=models.CASCADE, verbose_name=_("game"), related_name='bets')
    amount = models.FloatField(_("amount"), default=0)
    out_point = models.FloatField(_("out point"), default=0)
    # auto_out_point = models.FloatField(_("auto out point"), default=0)
    win_amount = models.FloatField(_("win amount"), default=0)
    win_result = models.SmallIntegerField(_("win result"), default=WinResult.NotEnd.value, choices=WIN_RESULT_TYPE)

    class Meta:
        ordering = ('-create_time',)
        verbose_name = _('Crash Bet')
        verbose_name_plural = _('Crash Bet')

    def __str__(self):
        return self.uid


class CrashPumpDay(ModelBase):
    date = models.DateField(_("date"), default=timezone.now)
    amount = models.FloatField(_("amount"), default=0)

    class Meta:
        ordering = ('-create_time',)
        verbose_name = _('Crash Pump Day')
        verbose_name_plural = _('Crash Pump Day')

    def __str__(self):
        return str(self.date)

    @classmethod
    def update_amount(cls, amount):
        with transaction.atomic():
            today = timezone.localdate()
            if not cls.objects.filter(date=today):
                cls.objects.create(date=today)
            record = cls.objects.select_for_update().get(date=today)
            amount = round(amount, 2)
            record.amount += amount
            record.save()


class CrashPumpMonth(ModelBase):
    date = models.DateField(_("date"), default=timezone.now)
    amount = models.FloatField(_("amount"), default=0)

    class Meta:
        ordering = ('-create_time',)
        verbose_name = _('Crash Pump Month')
        verbose_name_plural = _('Crash Pump Month')

    def __str__(self):
        return self.month()

    @classmethod
    def update_amount(cls, amount):
        with transaction.atomic():
            today = timezone.localdate()
            month_day_first = today.replace(day=1)
            if not cls.objects.filter(date=month_day_first):
                cls.objects.create(date=month_day_first)
            record = cls.objects.select_for_update().get(date=month_day_first)
            amount = round(amount, 2)
            record.amount += amount
            record.save()

    def month(self):
        return self.date.strftime("%Y-%m")
    month.short_description = _('Month')


class CrashStatisticsDay(ModelBase):
    date = models.DateField(_("date"), default=timezone.now)
    amount = models.FloatField(_("game amount"), default=0)
    journal = models.FloatField(_("game journal"), default=0)
    win_amount = models.FloatField(_("win amount journal"), default=0)
    lose_amount = models.FloatField(_("lose amount journal"), default=0)
    test_amount = models.FloatField(_("test game amount"), default=0)
    admin_amount = models.FloatField(_("admin game amount"), default=0)

    class Meta:
        ordering = ('-create_time',)
        verbose_name = _('Crash Statistics Day')
        verbose_name_plural = _('Crash Statistics Day')

    def __str__(self):
        return str(self.date)

    @classmethod
    def update_amount(cls, amount):
        with transaction.atomic():
            today = timezone.localdate()
            if not cls.objects.filter(date=today):
                cls.objects.create(date=today)
            record = cls.objects.select_for_update().get(date=today)
            amount = round(amount, 2)
            record.amount += amount
            record.save()
                   
    @classmethod
    def update_journal(cls, journal):
        with transaction.atomic():
            today = timezone.localdate()
            if not cls.objects.filter(date=today):
                cls.objects.create(date=today)
            record = cls.objects.select_for_update().get(date=today)
            journal = round(journal, 2)
            record.journal += journal
            record.save()

    @classmethod
    def update_win_amount(cls, win_amount):
        with transaction.atomic():
            today = timezone.localdate()
            if not cls.objects.filter(date=today):
                cls.objects.create(date=today)
            record = cls.objects.select_for_update().get(date=today)
            win_amount = round(win_amount, 2)
            record.win_amount += win_amount
            record.save()
    
    @classmethod
    def update_lose_amount(cls, lose_amount):
        with transaction.atomic():
            today = timezone.localdate()
            if not cls.objects.filter(date=today):
                cls.objects.create(date=today)
            record = cls.objects.select_for_update().get(date=today)
            lose_amount = round(lose_amount, 2)
            record.lose_amount += lose_amount
            record.save()

    @classmethod
    def update_test_amount(cls, amount):
        with transaction.atomic():
            today = timezone.localdate()
            if not cls.objects.filter(date=today):
                cls.objects.create(date=today)
            record = cls.objects.select_for_update().get(date=today)
            amount = round(amount, 2)
            record.test_amount += amount
            record.save()

    @classmethod
    def update_admin_amount(cls, amount):
        with transaction.atomic():
            today = timezone.localdate()
            if not cls.objects.filter(date=today):
                cls.objects.create(date=today)
            record = cls.objects.select_for_update().get(date=today)
            amount = round(amount, 2)
            record.admin_amount += amount
            record.save()


class CrashStatisticsMonth(ModelBase):
    date = models.DateField(_("date"), default=timezone.now)
    amount = models.FloatField(_("game amount"), default=0)
    journal = models.FloatField(_("game journal"), default=0)
    win_amount = models.FloatField(_("win amount journal"), default=0)
    lose_amount = models.FloatField(_("lose amount journal"), default=0)
    test_amount = models.FloatField(_("test game amount"), default=0)
    admin_amount = models.FloatField(_("admin game amount"), default=0)

    class Meta:
        ordering = ('-create_time',)
        verbose_name = _('Crash Statistics Month')
        verbose_name_plural = _('Crash Statistics Month')

    def __str__(self):
        return self.month()

    @classmethod
    def update_amount(cls, amount):
        with transaction.atomic():
            today = timezone.localdate()
            month_day_first = today.replace(day=1)
            if not cls.objects.filter(date=month_day_first):
                cls.objects.create(date=month_day_first)
            record = cls.objects.select_for_update().get(date=month_day_first)
            amount = round(amount, 2)
            record.amount += amount
            record.save()
       
    @classmethod
    def update_journal(cls, journal):
        with transaction.atomic():
            today = timezone.localdate()
            month_day_first = today.replace(day=1)
            if not cls.objects.filter(date=month_day_first):
                cls.objects.create(date=month_day_first)
            record = cls.objects.select_for_update().get(date=month_day_first)
            journal = round(journal, 2)
            record.journal += journal
            record.save()

    @classmethod
    def update_win_amount(cls, win_amount):
        with transaction.atomic():
            today = timezone.localdate()
            month_day_first = today.replace(day=1)
            if not cls.objects.filter(date=month_day_first):
                cls.objects.create(date=month_day_first)
            record = cls.objects.select_for_update().get(date=month_day_first)
            win_amount = round(win_amount, 2)
            record.win_amount += win_amount
            record.save()
    
    @classmethod
    def update_lose_amount(cls, lose_amount):
        with transaction.atomic():
            today = timezone.localdate()
            month_day_first = today.replace(day=1)
            if not cls.objects.filter(date=month_day_first):
                cls.objects.create(date=month_day_first)
            record = cls.objects.select_for_update().get(date=month_day_first)
            lose_amount = round(lose_amount, 2)
            record.lose_amount += lose_amount
            record.save()

    @classmethod
    def update_test_amount(cls, amount):
        with transaction.atomic():
            today = timezone.localdate()
            month_day_first = today.replace(day=1)
            if not cls.objects.filter(date=month_day_first):
                cls.objects.create(date=month_day_first)
            record = cls.objects.select_for_update().get(date=month_day_first)
            amount = round(amount, 2)
            record.test_amount += amount
            record.save()

    @classmethod
    def update_admin_amount(cls, amount):
        with transaction.atomic():
            today = timezone.localdate()
            month_day_first = today.replace(day=1)
            if not cls.objects.filter(date=month_day_first):
                cls.objects.create(date=month_day_first)
            record = cls.objects.select_for_update().get(date=month_day_first)
            amount = round(amount, 2)
            record.admin_amount += amount
            record.save()

    def month(self):
        return self.date.strftime("%Y-%m")
    month.short_description = _('Month')


class CrashWinDayRank(ModelBase):
    user = models.ForeignKey(USER_MODEL, on_delete=models.CASCADE, verbose_name=_('user'), related_name='crash_win_day_rank')
    date = models.DateField(_('date'), default=timezone.now)
    amount = models.FloatField(_('amount'), default=0)

    class Meta:
        verbose_name = _('Crash Win Day Rank')
        verbose_name_plural = _('Crash Win Day Rank')

    def __str__(self):
        return self.uid

    @classmethod
    def update_amount(cls, user, amount):
        with transaction.atomic():
            today = timezone.localdate()
            if not cls.objects.filter(user=user, date=today):
                cls.objects.create(user=user, date=today)
            record = cls.objects.select_for_update().get(user=user, date=today)
            amount = round(amount, 2)
            record.amount += amount
            record.save()


class CrashWinWeekRank(ModelBase):
    user = models.ForeignKey(USER_MODEL, on_delete=models.CASCADE, verbose_name=_('user'), related_name='crash_win_week_rank')
    year = models.SmallIntegerField(_('year'), default=0)
    week = models.SmallIntegerField(_('week'), default=0)
    amount = models.FloatField(_('amount'), default=0)

    class Meta:
        verbose_name = _('Crash Win Week Rank')
        verbose_name_plural = _('Crash Win Week Rank')

    def __str__(self):
        return self.uid

    @classmethod
    def update_amount(cls, user, amount):
        with transaction.atomic():
            year = current_year()
            week = current_week()
            if not cls.objects.filter(user=user, year=year, week=week):
                cls.objects.create(user=user, year=year, week=week)
            record = cls.objects.select_for_update().get(user=user, year=year, week=week)
            amount = round(amount, 2)
            record.amount += amount
            record.save()

    def weeks(self):
        return '{}-{}'.format(self.year, self.week)
    weeks.short_description = _('Weeks')
