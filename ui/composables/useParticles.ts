import { onMounted } from 'vue'

export const useParticles = () => {
  const initParticles = () => {
    if (!process.client) return
    
    const container = document.querySelector('.particle-container')
    if (!container) return

    const particleCount = 100 // 粒子数量

    for (let i = 0; i < particleCount; i++) {
      createParticle(container as HTMLElement)
    }
  }

  const createParticle = (container: HTMLElement) => {
    const particle = document.createElement('div')

    // 随机属性
    const size = Math.random() * 3 + 1 // 粒子大小 1-4px
    const posX = Math.random() * 100 // X坐标位置 (%)
    const posY = Math.random() * 100 // Y坐标位置 (%)
    const opacity = Math.random() * 0.3 + 0.1 // 透明度 0.1-0.4
    const duration = Math.random() * 40 + 10 // 动画持续时间 10-50s
    const delay = Math.random() * 5 // 动画延迟 0-5s

    // 设置样式
    particle.style.position = 'absolute'
    particle.style.width = `${size}px`
    particle.style.height = `${size}px`
    particle.style.borderRadius = '50%'

    // 使用主题色
    const colorRandom = Math.random()
    if (colorRandom > 0.6) {
      particle.style.backgroundColor = '#ff6b00' // 主题橙色
    } else if (colorRandom > 0.3) {
      particle.style.backgroundColor = '#6c5ce7' // 主题蓝紫色
    } else {
      particle.style.backgroundColor = '#FFFFFF' // 白色
    }

    particle.style.opacity = opacity.toString()
    particle.style.left = `${posX}%`
    particle.style.top = `${posY}%`
    particle.style.animation = `float ${duration}s ease-in-out ${delay}s infinite`
    particle.style.boxShadow = `0 0 ${size}px ${size / 2}px currentColor`

    // 添加到容器
    container.appendChild(particle)
  }

  return {
    initParticles
  }
} 