import logging

from django.contrib import messages
from django.http.response import HttpResponse
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from rest_framework.views import APIView
from rest_framework.permissions import AllowAny
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON>, JSONParser
from rest_framework.response import Response

from steambase.enums import RespCode
from steambase.utils import reformat_resp, current_user, ParamException
from sitecfg.interfaces import get_maintenance, get_maintenance_withdraw

from withdraw.business import withdraw_items_from_package, get_wxp_sell_list, get_wxp_items, close_tradeoffer
from withdraw.business import buy_wxp_items, get_withdraw_history, get_trade_detail, check_wxp_offer, get_wxp_match


_logger = logging.getLogger(__name__)


class WithdrawItemsView(APIView):

    def post(self, request):
        try:

            if get_maintenance_withdraw():
                return reformat_resp(RespCode.Maintenance.value, {},
                                     _('Withdraw is under maintenance, please wait for a while.'))
            query = {
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            user = current_user(request)
            uids =  request.data.get('uids', [])
            code, resp = withdraw_items_from_package(user, uids)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetWaxpeerSellView(APIView):

    def get(self, request):
        try:

            if get_maintenance_withdraw():
                return reformat_resp(RespCode.Maintenance.value, {},
                                     _('Withdraw is under maintenance, please wait for a while.'))
            query = {
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            user = current_user(request)
            min_price = request.query_params.get('min_price', None)
            max_price = request.query_params.get('max_price', None)
            search = request.query_params.get('search', None)
            code, resp = get_wxp_sell_list(user, min_price, max_price, search)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')



class GetWaxpeerItemsView(APIView):

    def get(self, request):
        try:

            user = current_user(request)
            search = request.query_params.get('search', None)
            skip = request.query_params.get('skip', 0)
            limit = request.query_params.get('limit', 50)
            order_by = request.query_params.get('order_by', 'asc')
            code, resp = get_wxp_items(user, search, skip, limit, order_by)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except ParamException as pe:
            return reformat_resp(RespCode.InvalidParams.value, {}, str(pe))
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetWaxpeerMatchView(APIView):

    def get(self, request):
        try:

            user = current_user(request)
            skip = 0
            limit =  50
            order_by = 'asc'
            code, resp = get_wxp_match(user, skip, limit, order_by)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except ParamException as pe:
            return reformat_resp(RespCode.InvalidParams.value, {}, str(pe))
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class BuyWaxpeerItemView(APIView):

    def post(self, request):
        try:

            user = current_user(request)
            items = request.data.get('items', [])
            code, resp = buy_wxp_items(user, items)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except ParamException as pe:
            return reformat_resp(RespCode.InvalidParams.value, {}, str(pe))
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetTradeHistoryView(APIView):

    def get(self, request):
        try:

            user = current_user(request)
            code, resp = get_withdraw_history(user)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except ParamException as pe:
            return reformat_resp(RespCode.InvalidParams.value, {}, str(pe))
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetTradeDetailView(APIView):

    def get(self, request):
        try:

            user = current_user(request)
            _id = request.query_params.get('id', 0)
            code, resp = get_trade_detail(user, _id)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except ParamException as pe:
            return reformat_resp(RespCode.InvalidParams.value, {}, str(pe))
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class CheckWaxpeerOfferView(APIView):

    def get(self, request):
        try:

            user = current_user(request)
            _id = request.query_params.get('id', '')
            code, resp = check_wxp_offer(user, _id)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except ParamException as pe:
            return reformat_resp(RespCode.InvalidParams.value, {}, str(pe))
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')
    

class CloseTradeView(APIView):

    def get(self, request):
        try:

            user = current_user(request)
            _id = request.query_params.get('id', '')
            code, resp = close_tradeoffer(user, _id)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except ParamException as pe:
            return reformat_resp(RespCode.InvalidParams.value, {}, str(pe))
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')