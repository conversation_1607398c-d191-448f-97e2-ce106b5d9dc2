/**
 * 日期格式化可组合函数
 * 遵循Vue3+Nuxt3技术规范，使用组合式API封装日期处理逻辑
 */

import { computed } from 'vue'

// 在可组合函数中提供dayjs功能，避免ESM导入问题

/**
 * 日期格式化可组合函数
 * @returns 日期处理相关的方法和计算属性
 */
export const useDateFormat = () => {
  /**
   * 格式化日期 - 遵循项目技术规范的同步实现
   * @param date 日期对象、时间戳或日期字符串
   * @param format 格式字符串，默认为 'YYYY-MM-DD HH:mm:ss'
   * @returns 格式化后的日期字符串
   */
  const formatDate = (
    date: Date | number | string | null | undefined, 
    format: string = 'YYYY-MM-DD HH:mm:ss'
  ): string => {
    if (!date) return ''
    
    const dateObj = new Date(date)
    if (isNaN(dateObj.getTime())) {
      return 'Invalid Date'
    }
    
    const year = dateObj.getFullYear()
    const month = String(dateObj.getMonth() + 1).padStart(2, '0')
    const day = String(dateObj.getDate()).padStart(2, '0')
    const hours = String(dateObj.getHours()).padStart(2, '0')
    const minutes = String(dateObj.getMinutes()).padStart(2, '0')
    const seconds = String(dateObj.getSeconds()).padStart(2, '0')
    
    // 支持多种格式，与dayjs兼容
    switch (format) {
      case 'YYYY-MM-DD':
        return `${year}-${month}-${day}`
      case 'YYYY-MM-DD HH:mm':
        return `${year}-${month}-${day} ${hours}:${minutes}`
      case 'YYYY-MM-DD HH:mm:ss':
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
      case 'MM-DD HH:mm':
        return `${month}-${day} ${hours}:${minutes}`
      case 'HH:mm:ss':
        return `${hours}:${minutes}:${seconds}`
      case 'HH:mm':
        return `${hours}:${minutes}`
      case 'MM/DD/YYYY':
        return `${month}/${day}/${year}`
      case 'YYYY/MM/DD':
        return `${year}/${month}/${day}`
      case 'DD-MM-YYYY':
        return `${day}-${month}-${year}`
      default:
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    }
  }

  /**
   * 获取相对时间（如：2小时前）
   * @param date 日期对象、时间戳或日期字符串
   * @returns 相对时间字符串
   */
  const getRelativeTime = (date: Date | number | string): string => {
    if (!date) return ''
    
    const now = new Date()
    const targetDate = new Date(date)
    
    if (isNaN(targetDate.getTime())) {
      return 'Invalid Date'
    }
    
    const diffMs = now.getTime() - targetDate.getTime()
    
    if (diffMs < 0) {
      return '刚刚'
    }
    
    const diffSeconds = Math.floor(diffMs / 1000)
    const diffMinutes = Math.floor(diffSeconds / 60)
    const diffHours = Math.floor(diffMinutes / 60)
    const diffDays = Math.floor(diffHours / 24)
    const diffMonths = Math.floor(diffDays / 30)
    const diffYears = Math.floor(diffDays / 365)
    
    if (diffSeconds < 60) {
      return '刚刚'
    } else if (diffMinutes < 60) {
      return `${diffMinutes}分钟前`
    } else if (diffHours < 24) {
      return `${diffHours}小时前`
    } else if (diffDays < 30) {
      return `${diffDays}天前`
    } else if (diffMonths < 12) {
      return `${diffMonths}个月前`
    } else {
      return `${diffYears}年前`
    }
  }

  /**
   * 判断是否为有效日期
   * @param date 日期对象、时间戳或日期字符串
   * @returns 是否为有效日期
   */
  const isValidDate = (date: any): boolean => {
    if (!date) return false
    const dateObj = new Date(date)
    return !isNaN(dateObj.getTime())
  }

  /**
   * 获取当前时间戳
   * @returns 当前时间戳（毫秒）
   */
  const getCurrentTimestamp = (): number => {
    return Date.now()
  }

  /**
   * 获取今天开始时间
   * @returns 今天00:00:00的Date对象
   */
  const getStartOfToday = (): Date => {
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    return today
  }

  /**
   * 获取今天结束时间
   * @returns 今天23:59:59的Date对象
   */
  const getEndOfToday = (): Date => {
    const today = new Date()
    today.setHours(23, 59, 59, 999)
    return today
  }

  /**
   * 判断两个日期是否为同一天
   * @param date1 第一个日期
   * @param date2 第二个日期
   * @returns 是否为同一天
   */
  const isSameDay = (date1: Date | string | number, date2: Date | string | number): boolean => {
    const d1 = new Date(date1)
    const d2 = new Date(date2)
    
    if (isNaN(d1.getTime()) || isNaN(d2.getTime())) {
      return false
    }
    
    return d1.getFullYear() === d2.getFullYear() &&
           d1.getMonth() === d2.getMonth() &&
           d1.getDate() === d2.getDate()
  }

  /**
   * 计算两个日期之间的天数差
   * @param startDate 开始日期
   * @param endDate 结束日期
   * @returns 天数差
   */
  const daysBetween = (startDate: Date | string | number, endDate: Date | string | number): number => {
    const start = new Date(startDate)
    const end = new Date(endDate)
    
    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      return 0
    }
    
    const diffTime = Math.abs(end.getTime() - start.getTime())
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  }

  /**
   * 获取格式化的时间范围文本
   * @param startDate 开始日期
   * @param endDate 结束日期
   * @returns 格式化的时间范围
   */
  const getTimeRange = (startDate: Date | string | number, endDate: Date | string | number): string => {
    const start = formatDate(startDate, 'YYYY-MM-DD')
    const end = formatDate(endDate, 'YYYY-MM-DD')
    
    if (start === end) {
      return start
    }
    
    return `${start} 至 ${end}`
  }

  // 返回所有方法和计算属性，遵循组合式API规范
  return {
    formatDate,
    getRelativeTime,
    isValidDate,
    getCurrentTimestamp,
    getStartOfToday,
    getEndOfToday,
    isSameDay,
    daysBetween,
    getTimeRange
  }
} 