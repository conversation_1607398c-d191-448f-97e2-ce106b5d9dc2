<template>
  <div class="timeline-item relative">
    <!-- 时间线连接线 -->
    <div class="absolute left-6 top-12 bottom-0 w-px bg-gradient-to-b from-primary/50 to-transparent"></div>
    
    <!-- 时间线节点 -->
    <div class="absolute left-4 top-8 w-4 h-4 rounded-full border-2 border-primary bg-gray-900 z-10">
      <div class="absolute inset-1 rounded-full bg-primary animate-pulse"></div>
    </div>

    <!-- 内容卡片 -->
    <div class="ml-12 bg-gray-800/60 backdrop-blur-sm border border-gray-700/50 rounded-lg p-4 hover:border-primary/40 transition-all duration-300 group">
      <!-- 时间戳 -->
      <div class="text-primary text-xs font-medium mb-3">{{ formatTime() }}</div>
      
      <div class="flex items-center space-x-4">
        <!-- 用户信息 -->
        <div class="flex items-center space-x-2 flex-shrink-0">
          <img
            :src="record.user_avatar || 'https://placehold.co/32x32/374151/9ca3af?text=U'"
            :alt="record.user_name"
            class="w-8 h-8 rounded-full border border-gray-600"
            @error="(e) => (e.target as HTMLImageElement).src = 'https://placehold.co/32x32/374151/9ca3af?text=U'"
          />
          <div class="text-blue-400 font-medium text-sm">{{ record.user_name }}</div>
        </div>

        <!-- 动作描述 -->
        <div class="text-white/60 text-sm">opened</div>

        <!-- 箱子信息 -->
        <div class="flex items-center space-x-2 flex-shrink-0">
          <img
            :src="record.case_info?.cover"
            :alt="record.case_info?.name"
            class="w-6 h-6 object-contain rounded"
            @error="(e) => (e.target as HTMLImageElement).src = 'https://placehold.co/24x24/374151/9ca3af?text=C'"
          />
          <span class="text-white text-sm font-medium">{{ record.case_info?.name }}</span>
        </div>

        <!-- 获得描述 -->
        <div class="text-white/60 text-sm">and got</div>

        <!-- 物品信息 -->
        <div class="flex items-center space-x-3 flex-grow min-w-0">
          <div class="relative">
            <!-- 稀有度光效 -->
            <div 
              class="absolute inset-0 blur-md opacity-40 rounded"
              :style="{ backgroundColor: rarityColor }"
            ></div>
            <img
              :src="record.item_info?.image"
              :alt="record.item_info?.name"
              class="relative w-12 h-12 object-contain drop-shadow-lg group-hover:scale-110 transition-transform duration-300"
              @error="(e) => (e.target as HTMLImageElement).src = 'https://placehold.co/48x48/dc2626/fecaca?text=I'"
            />
          </div>
          
          <div class="min-w-0 flex-1">
            <div class="text-white font-bold text-sm truncate">{{ getSkinName(record.item_info?.name) }}</div>
            <div class="flex items-center space-x-2">
              <span class="text-white/70 text-xs">{{ getWeaponName(record.item_info?.name) }}</span>
              <span class="text-white/50">•</span>
              <span 
                class="text-xs font-medium"
                :style="{ color: rarityColor }"
              >
                {{ getRarityName() }}
              </span>
              <span class="text-white/50">•</span>
              <span class="text-white/60 text-xs">{{ getExteriorName() }}</span>
            </div>
          </div>
        </div>

        <!-- StatTrak 标签 -->
        <div 
          v-if="isStatTrak"
          class="flex-shrink-0 bg-orange-500/90 rounded px-2 py-1 text-xs font-bold text-white border border-orange-500/50"
        >
          <i class="i-ph-lightning mr-1"></i>ST
        </div>
      </div>

      <!-- 悬停效果 -->
      <div class="absolute inset-0 bg-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg pointer-events-none"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  record: any
}

const props = defineProps<Props>()

const getSkinName = (fullName?: string): string => {
  if (!fullName) return 'Unknown Item'
  const parts = fullName.split(' | ')
  if (parts.length < 2) return fullName
  const skinPart = parts[1].trim()
  return skinPart.split(' (')[0].trim()
}

const getWeaponName = (fullName?: string): string => {
  if (!fullName) return 'Unknown Weapon'
  const parts = fullName.split(' | ')
  return parts[0].trim()
}

const getExteriorName = (): string => {
  return props.record.item_info?.item_exterior?.exterior_name || 'Factory New'
}

const getRarityName = (): string => {
  return 'Covert'
}

const isStatTrak = computed(() => {
  return props.record.item_info?.name?.includes('StatTrak™') || false
})

const rarityColor = computed(() => {
  const rarities: Record<string, string> = {
    'Covert': '#eb4b4b',
    'Classified': '#d32ce6', 
    'Restricted': '#8847ff',
    'Mil-Spec': '#4b69ff',
    'Industrial': '#5e98d9',
    'Consumer': '#b0c3d9'
  }
  return rarities[getRarityName()] || '#eb4b4b'
})

const formatTime = (): string => {
  const now = new Date()
  return now.toLocaleTimeString('en-US', { 
    hour: '2-digit', 
    minute: '2-digit',
    second: '2-digit',
    hour12: false 
  })
}
</script>

<style lang="scss" scoped>
.timeline-item {
  animation: slideInFromRight 0.6s ease-out;
}

@keyframes slideInFromRight {
  from {
    transform: translateX(20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
</style> 