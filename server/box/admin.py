from django.conf import settings
from django.contrib import admin
from django.contrib.admin import TabularInline, StackedInline
from django.contrib.admin.views.main import ChangeList
from django.core.cache import cache
from django.db.models import Sum
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
# # from jet.filters import DateRangeFilter  # 暂时禁用，等待Django 4.x兼容版本  # 暂时禁用，等待Django 4.x兼容版本
from modeltranslation.admin import TranslationAdmin

from box.service.admin_actions import case_export_to_excel, startup_export_excel
from steambase.admin import ReadOnlyAdmin
from box.models import Case, CaseType, CaseRecord, DropItem, CaseBot, CaseStatisticsDay, CaseStatisticsMonth, \
    CaseKeyConfig
from box.models import FreeCaseConfig, FestivalCaseConfig, FestivalCaseDate, FestivalCaseRecord
from box.models import CaseRoom, CaseRoomRound, CaseRoomBet, CaseRoomItem, DropDayRank, IncomeDayRank, RoomDayRank, \
    LoseWeekRank
from box.business import get_drop_price

# 2025
from package.service.item import get_item_price_by_id


@admin.register(CaseType)
class CaseTypeAdmin(TranslationAdmin):
    list_display = ('type_name', 'sort')
    search_fields = ('type_name',)
    list_per_page = 50


class DropItemInline(TabularInline):
    model = DropItem
    fields = ['item_info', 'show_chance', 'price']
    readonly_fields = ['price', 'custom', 'custom_price']
    raw_id_fields = ['item_info']
    ordering = ['id']
    extra = 0
   
    def _get_price_attr(self, obj, attr_name):
        if not obj.item_info:
            return None
        price_obj = get_item_price_by_id(obj.item_info.id)
        return getattr(price_obj, attr_name, None) if price_obj else None

    def price(self, obj):
        return self._get_price_attr(obj, 'price')

    def custom(self, obj):
        return self._get_price_attr(obj, 'custom')
        
    def custom_price(self, obj):
        return self._get_price_attr(obj, 'custom_price')


@admin.register(Case)
class CaseAdmin(TranslationAdmin):
    fields = ('name', 'case_key', 'price', 'price_expectation_a', 'discount', 'present', 'cover', 'case_category', 'sort', 'is_show', 'enable', 'enable_room', 'enable_admin', 'tag', 'tag_color', 'open_count', 'algorithm_enable', 'content','seo_title','seo_keywords','seo_description')
    list_display = (
    'name', 'case_key', 'price', 'discount', 'case_category', 'sort', 'is_show', 'enable', 'enable_room',
    'enable_admin')
    readonly_fields = ('price_expectation_a', )
    list_editable = ('is_show', 'enable', 'enable_room', 'enable_admin', 'case_key')
    list_filter = ('case_type', 'is_show', 'enable', 'enable_admin')
    search_fields = ('name',)
    raw_id_fields = ['present']
    inlines = [DropItemInline]
    list_per_page = 100
    actions = [case_export_to_excel]
    ordering = ['price']

    

    def price_expectation_a(self, obj):
        drops = obj.drops.all()
        total_chance = sum([d.drop_chance_a for d in drops])
        if total_chance:
            expectation = sum([get_drop_price(d) * d.drop_chance_a / total_chance for d in drops])
        else:
            expectation = 0
        return round(expectation, 3)

    price_expectation_a.short_description = _('期望价格')

    def price_expectation_b(self, obj):
        drops = obj.drops.all()
        total_chance = sum([d.drop_chance_b for d in drops])
        if total_chance:
            expectation = sum([get_drop_price(d) * d.drop_chance_b / total_chance for d in drops])
        else:
            expectation = 0
        return round(expectation, 3)

    price_expectation_b.short_description = _('price_expectation_b')

    def price_expectation_c(self, obj):
        drops = obj.drops.all()
        total_chance = sum([d.drop_chance_c for d in drops])
        if total_chance:
            expectation = sum([get_drop_price(d) * d.drop_chance_c / total_chance for d in drops])
        else:
            expectation = 0
        return round(expectation, 3)

    price_expectation_c.short_description = _('price_expectation_c')

    def price_expectation_d(self, obj):
        drops = obj.drops.all()
        total_chance = sum([d.drop_chance_d for d in drops])
        if total_chance:
            expectation = sum([get_drop_price(d) * d.drop_chance_d / total_chance for d in drops])
        else:
            expectation = 0
        return round(expectation, 3)

    price_expectation_d.short_description = _('price_expectation_d')

    def price_expectation_e(self, obj):
        drops = obj.drops.all()
        total_chance = sum([d.drop_chance_e for d in drops])
        if total_chance:
            expectation = sum([get_drop_price(d) * d.drop_chance_e / total_chance for d in drops])
        else:
            expectation = 0
        return round(expectation, 3)

    price_expectation_e.short_description = _('price_expectation_e')

    def save_model(self, request, obj, form, change):
        cache.delete('case:{}'.format(obj.key))
        cache.delete('caselist')
        cache.delete('freecaselist')
        obj.save()

    def delete_model(self, request, obj):
        cache.delete('case:{}'.format(obj.key))
        cache.delete('caselist')
        cache.delete('freecaselist')
        obj.delete()


@admin.register(CaseRecord)
class CaseRecordAdmin(ReadOnlyAdmin):
    fields = ('uid', 'user', 'case', 'item_info', 'price', 'create_time')
    list_display = ('uid', 'user', 'case', 'item_info', 'price', 'create_time')
    list_filter = ('case',)  # DateRangeFilter暂时禁用
    search_fields = ('user__username', 'uid', 'item_info__market_name')
    list_per_page = 50
    actions = [startup_export_excel]


class CaseRoomRoundInline(TabularInline):
    model = CaseRoomRound
    can_delete = False
    fields = ['case', 'opened']
    readonly_fields = ['case', 'opened']

    def has_add_permission(self, request):
        return False


class CaseRoomBetInline(TabularInline):
    model = CaseRoomBet
    can_delete = False
    fields = ['user', 'open_items', 'open_amount', 'win_items', 'win_amount', 'victory']
    readonly_fields = ['user', 'open_items', 'open_amount', 'win_items', 'win_amount', 'victory']

    def has_add_permission(self, request):
        return False

    def open_items(self, obj):
        return [i.__str__() for i in CaseRoomItem.objects.filter(bet=obj)]

    open_items.short_description = 'open_items'

    def win_items(self, obj):
        return [i.__str__() for i in CaseRoomItem.objects.filter(winner=obj)]

    win_items.short_description = 'win_items'


@admin.register(CaseRoom)
class CaseRoomAdmin(ReadOnlyAdmin):
    fields = ('uid', 'user', 'max_joiner', 'price', 'state', 'create_time')
    list_display = ('uid', 'user', 'max_joiner', 'price', 'state', 'create_time')
    list_filter = ()  # DateRangeFilter暂时禁用,)
    search_fields = ('user__username', 'uid')
    list_per_page = 50
    inlines = [CaseRoomRoundInline, CaseRoomBetInline]


# @admin.register(CaseBot)
# class CaseBotAdmin(admin.ModelAdmin):
#     list_display = ('remark', 'user', 'open_idle_min', 'open_idle_max', 'enable')
#     list_editable = ('open_idle_min', 'open_idle_max', 'enable')
#     list_filter = ('enable',)
#     search_fields = ('remark', 'user__personaname')
#     list_per_page = 50


@admin.register(CaseStatisticsDay)
class CaseStatisticsDayAdmin(ReadOnlyAdmin):
    fields = ('date', 'amount')
    list_display = ('date', 'amount')
    list_filter = ()  # DateRangeFilter暂时禁用,)
    list_per_page = 50

    def get_changelist(self, request, **kwargs):
        return CaseStatisticsDayAddTotalChangeList


class CaseStatisticsDayAddTotalChangeList(ChangeList):

    def get_results(self, request):
        super().get_results(request)
        total = CaseStatisticsDay()
        setattr(total, 'date', 'total')
        fields_to_total = ['amount']
        for field in fields_to_total:
            setattr(total, field, round(list(self.queryset.aggregate(Sum(field)).items())[0][1] or 0, 2))
        len(self.result_list)
        self.result_list._result_cache.insert(0, total)


@admin.register(CaseStatisticsMonth)
class CaseStatisticsMonthAdmin(ReadOnlyAdmin):
    fields = ('month', 'amount')
    extra_readonly_fields = ('month',)
    list_display = ('month', 'amount')
    list_filter = ()  # DateRangeFilter暂时禁用,)
    list_per_page = 50

    def get_changelist(self, request, **kwargs):
        return CaseStatisticsMonthAddTotalChangeList


class CaseStatisticsMonthAddTotalChangeList(ChangeList):

    def get_results(self, request):
        super().get_results(request)
        total = CaseStatisticsMonth()
        setattr(total, 'date', 'total')
        fields_to_total = ['amount']
        for field in fields_to_total:
            setattr(total, field, round(list(self.queryset.aggregate(Sum(field)).items())[0][1] or 0, 2))
        len(self.result_list)
        self.result_list._result_cache.insert(0, total)


@admin.register(FreeCaseConfig)
class FreeCaseConfigAdmin(admin.ModelAdmin):
    fields = ('level', 'min_amount', 'max_amount', 'case')
    list_display = ('level', 'min_amount', 'max_amount', 'case')
    list_per_page = 50

    def save_model(self, request, obj, form, change):
        cache.delete('freecaselist')
        obj.save()

    def delete_model(self, request, obj):
        cache.delete('freecaselist')
        obj.delete()


@admin.register(CaseKeyConfig)
class CaseKeyConfig(admin.ModelAdmin):
    fields = ('case', 'key',)
    raw_id_fields = ('case', 'key')

# @admin.register(FestivalCaseConfig)
# class FestivalCaseConfigAdmin(admin.ModelAdmin):
#     fields = ('level', 'min_amount', 'max_amount', 'case')
#     list_display = ('level', 'min_amount', 'max_amount', 'case')
#     list_per_page = 50


# @admin.register(FestivalCaseDate)
# class FestivalCaseDateAdmin(admin.ModelAdmin):
#     fields = ('month', 'day', 'enable')
#     list_display = ('month_date', 'enable')
#     readonly_fields = ('month_date',)
#     list_editable = ('enable',)
#     list_per_page = 50

#     def month_date(self, obj):
#         return '{}月{}日'.format(obj.month, obj.day)
#     month_date.short_description = '发放日期'


# @admin.register(FestivalCaseRecord)
# class FestivalCaseRecordAdmin(admin.ModelAdmin):
#     fields = ('user', 'case', 'opened', 'expired')
#     list_display = ('user', 'case', 'opened', 'expired')
#     readonly_fields = ('user', 'case', 'opened', 'expired')
#     list_per_page = 50

#     def has_add_permission(self, request):
#         return False

#     def get_queryset(self, request):
#         return self.model.objects.filter(expired__gte=timezone.now())


# @admin.register(DropDayRank)
# class DropDayRankAdmin(ReadOnlyAdmin):
#     fields = ('user', 'amount', 'item_info', 'date')
#     list_display = ('user', 'amount', 'item_info', 'date')
#     list_filter = ()  # DateRangeFilter暂时禁用,)
#     search_fields = ('user__username', 'uid', 'item_info__market_name')
#     list_per_page = 50


# @admin.register(IncomeDayRank)
# class IncomeDayRankAdmin(ReadOnlyAdmin):
#     fields = ('user', 'amount', 'item_info', 'case', 'date')
#     list_display = ('user', 'amount', 'item_info', 'case', 'date')
#     list_filter = ()  # DateRangeFilter暂时禁用,)
#     search_fields = ('user__username', 'uid', 'item_info__market_name')
#     list_per_page = 50


# @admin.register(RoomDayRank)
# class RoomDayRankAdmin(ReadOnlyAdmin):
#     fields = ('user', 'amount', 'room', 'date')
#     list_display = ('user', 'amount', 'room', 'date')
#     list_filter = ()  # DateRangeFilter暂时禁用,)
#     search_fields = ('user__username', 'uid', 'room__uid')
#     list_per_page = 50


# @admin.register(LoseWeekRank)
# class LoseWeekRankAdmin(ReadOnlyAdmin):
#     fields = ('user', 'amount', 'week', 'year')
#     list_display = ('user', 'amount', 'week', 'year')
#     list_filter = ('week', 'year')
#     search_fields = ('user__username', 'uid')
#     list_per_page = 50
