{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block extrastyle %}{{ block.super }}<link rel="stylesheet" type="text/css" href="{% static "admin/css/login.css" %}" />
{{ form.media }}
{% endblock %}

{% block bodyclass %}{{ block.super }} login{% endblock %}

{% block usertools %}{% endblock %}

{% block nav-global %}{% endblock %}

{% block content_title %}{% endblock %}

{% block breadcrumbs %}{% endblock %}

{% block content %}
{% if form.errors and not form.non_field_errors %}
<p class="errornote">
{% if form.errors.items|length == 1 %}{% trans "Please correct the error below." %}{% else %}{% trans "Please correct the errors below." %}{% endif %}
</p>
{% endif %}

{% if form.non_field_errors %}
{% for error in form.non_field_errors %}
<p class="errornote">
    {{ error }}
</p>
{% endfor %}
{% endif %}

<div id="content-main">

{% if user.is_authenticated %}
<p class="errornote">
{% blocktrans trimmed %}
    You are authenticated as {{ username }}, but are not authorized to
    access this page. Would you like to login to a different account?
{% endblocktrans %}
</p>
{% endif %}

{#<form action="{{ app_path }}" method="post" id="login-form">{% csrf_token %}#}
{#  <div class="form-row">#}
{#    {{ form.username.errors }}#}
{#    {{ form.username.label_tag }} {{ form.username }}#}
{#  </div>#}
{#  <div class="form-row">#}
{#    {{ form.password.errors }}#}
{#    {{ form.password.label_tag }} {{ form.password }}#}
{#    <input type="hidden" name="next" value="{{ next }}" />#}
{#  </div>#}
{#  {% url 'admin_password_reset' as password_reset_url %}#}
{#  {% if password_reset_url %}#}
{#  <div class="password-reset-link">#}
{#    <a href="{{ password_reset_url }}">{% trans 'Forgotten your password or username?' %}</a>#}
{#  </div>#}
{#  {% endif %}#}
{#  <div class="submit-row">#}
{#    <label>&nbsp;</label><input name="submit" type="submit" value="{% trans 'Log in' %}" />#}
{#  </div>#}
{#</form>#}
<p style="text-align: center;">
    {% if request.user.is_anonymous %}
    <form action="{{ app_path }}" method="post" id="login-form">{% csrf_token %}
      <div class="form-row">
        {{ form.username.errors }}
        {{ form.username.label_tag }} {{ form.username }}
      </div>
      <div class="form-row">
        {{ form.password.errors }}
        {{ form.password.label_tag }} {{ form.password }}
        <input type="hidden" name="next" value="{{ next }}" />
      </div>
      {% url 'admin_password_reset' as password_reset_url %}
      {% if password_reset_url %}
      <div class="password-reset-link">
        <a href="{{ password_reset_url }}">{% trans 'Forgotten your password or username?' %}</a>
      </div>
      {% endif %}
      <div class="submit-row">
        <label>&nbsp;</label><input name="submit" type="submit" value="{% trans 'Log in' %}" />
      </div>
    </form>
    <a href="{% url 'social:begin' 'steam' %}">
      <div class="avatar" style="text-align: center;">
        <img src="https://steamcommunity-a.akamaihd.net/public/images/signinthroughsteam/sits_01.png" alt="Login with Steam">
      </div>
    </a>
    {% else %}
    <a href="{% url 'auth:admin_logout' %}">
      <button>logout</button>
    </a>
    {% endif %}
</p>
</div>
<style>
    button {
        display: inline-block;
        background-color: #47bac1;
        color: #fff;
        border: 0;
        border-radius: 4px;
        border-radius: 0.28571rem;
        height: 32px;
        height: 2.28571rem;
        line-height: 32px;
        line-height: 2.28571rem;
        outline: 0;
        font-size: 12px;
        font-size: 0.85714rem;
        font-weight: lighter;
        text-align: center;
        padding: 0 20px;
        padding: 0 1.42857rem;
        text-transform: uppercase;
        margin: 0 8px 5px 0;
        margin: 0 0.57143rem 0.35714rem 0;
        transition: background .3s;
    }
</style>
{% endblock %}
