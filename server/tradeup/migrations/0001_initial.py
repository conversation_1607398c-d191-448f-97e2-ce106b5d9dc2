# -*- coding: utf-8 -*-
# Generated by Django 1.11.13 on 2021-04-14 02:09
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import steambase.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODELL),
        ('package', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='TradeupBetItem',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('price', models.FloatField(default=0, verbose_name='price')),
                ('type', models.SmallIntegerField(choices=[(1, 'Item'), (2, 'Amount')], default=1, verbose_name='bet item type')),
            ],
            options={
                'verbose_name': 'Tradeup Bet Item',
                'verbose_name_plural': 'Tradeup Bet Item',
            },
        ),
        migrations.CreateModel(
            name='TradeupBot',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('bet_idle_min', models.IntegerField(default=0, verbose_name='bet idle min(seconds)')),
                ('bet_idle_max', models.IntegerField(default=0, verbose_name='bet idle max(seconds)')),
                ('chance_min', models.FloatField(default=0, verbose_name='bot tradeup min chance')),
                ('chance_max', models.FloatField(default=100, verbose_name='bot tradeup max chance')),
                ('price_min', models.FloatField(default=0, verbose_name='bot item price min')),
                ('price_max', models.FloatField(default=9999, verbose_name='bot item price max')),
                ('remark', models.CharField(max_length=128, verbose_name='remark')),
                ('enable', models.BooleanField(default=True, verbose_name='enable')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='tradeup_bot', to=settings.AUTH_USER_MODELL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'Tradeup Bot Config',
                'verbose_name_plural': 'Tradeup Bot Config',
            },
        ),
        migrations.CreateModel(
            name='TradeupCoinsBetItem',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('amount', models.FloatField(default=0, verbose_name='amount')),
                ('appid', models.CharField(choices=[('570', 'Dota2'), ('730', 'CSGO'), ('578080', 'PUBG'), ('433850', 'H1Z1')], default='730', max_length=64, verbose_name='appid')),
            ],
            options={
                'verbose_name': 'Coins Tradeup Bet amount',
                'verbose_name_plural': 'Coins Tradeup Bet amount',
            },
        ),
        migrations.CreateModel(
            name='TradeupGame',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('hash', models.CharField(max_length=255, verbose_name='hash')),
                ('secret', models.CharField(max_length=32, verbose_name='secret')),
                ('percentage', models.FloatField(default=0, verbose_name='percentage')),
                ('bet_percentage', models.FloatField(default=0, verbose_name='bet percentage')),
                ('upper', models.BooleanField(default=False, verbose_name='upper')),
                ('bet_amount', models.FloatField(default=0, verbose_name='origin amount')),
                ('target_amount', models.FloatField(default=0, verbose_name='target amount')),
                ('win_amount', models.FloatField(default=0, verbose_name='win amount')),
                ('win_result', models.SmallIntegerField(choices=[(4, 'Not End'), (2, 'Win'), (3, 'Lose')], default=4, verbose_name='win result')),
                ('appid', models.CharField(choices=[('570', 'Dota2'), ('730', 'CSGO'), ('578080', 'PUBG'), ('433850', 'H1Z1')], default='730', max_length=64, verbose_name='appid')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tradeup_bets', to=settings.AUTH_USER_MODELL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'Tradeup Game',
                'verbose_name_plural': 'Tradeup Game',
                'ordering': ('-create_time',),
            },
        ),
        migrations.CreateModel(
            name='TradeupInventory',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('price', models.FloatField(default=0, verbose_name='price')),
                ('count', models.IntegerField(default=0, verbose_name='count')),
                ('unlimited', models.BooleanField(default=True, verbose_name='unlimited')),
                ('enable', models.BooleanField(default=True, verbose_name='enable')),
                ('item_info', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='tradeup_inventory', to='package.ItemInfo', verbose_name='item info')),
            ],
            options={
                'verbose_name': 'Tradeup Inventory Item',
                'verbose_name_plural': 'Tradeup Inventory Item',
            },
        ),
        migrations.CreateModel(
            name='TradeupPumpDay',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('date', models.DateField(default=django.utils.timezone.now, verbose_name='date')),
                ('amount', models.FloatField(default=0, verbose_name='amount')),
            ],
            options={
                'verbose_name': 'Tradeup Pump Day',
                'verbose_name_plural': 'Tradeup Pump Day',
                'ordering': ('-create_time',),
            },
        ),
        migrations.CreateModel(
            name='TradeupPumpMonth',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('date', models.DateField(default=django.utils.timezone.now, verbose_name='date')),
                ('amount', models.FloatField(default=0, verbose_name='amount')),
            ],
            options={
                'verbose_name': 'Tradeup Pump Month',
                'verbose_name_plural': 'Tradeup Pump Month',
                'ordering': ('-create_time',),
            },
        ),
        migrations.CreateModel(
            name='TradeupStatisticsDay',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('date', models.DateField(default=django.utils.timezone.now, verbose_name='date')),
                ('amount', models.FloatField(default=0, verbose_name='game amount')),
                ('journal', models.FloatField(default=0, verbose_name='game journal')),
                ('win_amount', models.FloatField(default=0, verbose_name='win amount journal')),
                ('lose_amount', models.FloatField(default=0, verbose_name='lose amount journal')),
                ('test_amount', models.FloatField(default=0, verbose_name='test game amount')),
                ('admin_amount', models.FloatField(default=0, verbose_name='admin game amount')),
            ],
            options={
                'verbose_name': 'Tradeup Statistics Day',
                'verbose_name_plural': 'Tradeup Statistics Day',
                'ordering': ('-create_time',),
            },
        ),
        migrations.CreateModel(
            name='TradeupStatisticsMonth',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('date', models.DateField(default=django.utils.timezone.now, verbose_name='date')),
                ('amount', models.FloatField(default=0, verbose_name='game amount')),
                ('journal', models.FloatField(default=0, verbose_name='game journal')),
                ('win_amount', models.FloatField(default=0, verbose_name='win amount journal')),
                ('lose_amount', models.FloatField(default=0, verbose_name='lose amount journal')),
                ('test_amount', models.FloatField(default=0, verbose_name='test game amount')),
                ('admin_amount', models.FloatField(default=0, verbose_name='admin game amount')),
            ],
            options={
                'verbose_name': 'Tradeup Statistics Month',
                'verbose_name_plural': 'Tradeup Statistics Month',
                'ordering': ('-create_time',),
            },
        ),
        migrations.CreateModel(
            name='TradeupTargetItem',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('price', models.FloatField(default=0, verbose_name='price')),
                ('game', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='target_items', to='tradeup.TradeupGame', verbose_name='game')),
                ('item_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tradeup_target_items', to='package.ItemInfo', verbose_name='item info')),
            ],
            options={
                'verbose_name': 'Tradeup Target Item',
                'verbose_name_plural': 'Tradeup Target Item',
            },
        ),
        migrations.CreateModel(
            name='TradeupWinDayRank',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('date', models.DateField(default=django.utils.timezone.now, verbose_name='date')),
                ('amount', models.FloatField(default=0, verbose_name='amount')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tradeup_win_day_rank', to=settings.AUTH_USER_MODELL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'Tradeup Win Day Rank',
                'verbose_name_plural': 'Tradeup Win Day Rank',
            },
        ),
        migrations.CreateModel(
            name='TradeupWinWeekRank',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('year', models.SmallIntegerField(default=0, verbose_name='year')),
                ('week', models.SmallIntegerField(default=0, verbose_name='week')),
                ('amount', models.FloatField(default=0, verbose_name='amount')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tradeup_win_week_rank', to=settings.AUTH_USER_MODELL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'Tradeup Win Week Rank',
                'verbose_name_plural': 'Tradeup Win Week Rank',
            },
        ),
        migrations.AddField(
            model_name='tradeupcoinsbetitem',
            name='game',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bet_amounts', to='tradeup.TradeupGame', verbose_name='game'),
        ),
        migrations.AddField(
            model_name='tradeupbetitem',
            name='game',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bet_items', to='tradeup.TradeupGame', verbose_name='game'),
        ),
        migrations.AddField(
            model_name='tradeupbetitem',
            name='package',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='tradeup_bet_items', to='package.PackageItem', verbose_name='package item'),
        ),
    ]
