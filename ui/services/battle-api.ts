import type { ApiResponse } from './common-api'

// 对战状态枚举 - 根据后端提供的GameState
export enum GameState {
  Initial = 1,      // 初始状态
  Joinable = 2,     // 可加入
  Joining = 3,      // 加入中  
  Full = 4,         // 已满员
  Running = 5,      // 运行中
  End = 11,         // 已结束
  Cancelled = 20    // 已取消
}

// 前端状态枚举 - 与后端GameState对应
export enum BattleStatus {
  INITIAL = 1,        // 初始状态 (对应Initial)
  WAITING = 2,        // 等待中 (对应Joinable)
  IN_PROGRESS = 5,    // 进行中 (对应Running)
  COMPLETED = 11,     // 已完成 (对应End)
  CANCELLED = 20      // 已取消 (对应Cancelled)
}

// 对战状态映射 - 后端数字状态到前端状态
function mapStateToStatus(state: number): GameState {
  switch (state) {
    case 1: // Initial
    case 2: // Joinable
    case 3: // Joining
    case 4: // Full
      return GameState.Joinable
    case 5: // Running
      return GameState.Running
    case 11: // End
      return GameState.End
    case 20: // Cancelled
      return GameState.Cancelled
    default:
      return GameState.Joinable
  }
}

// GameState转换为BattleStatus
export function gameStateToBattleStatus(state: GameState): BattleStatus {
  switch (state) {
    case GameState.Initial:
    case GameState.Joinable:
    case GameState.Joining:
    case GameState.Full:
      return BattleStatus.WAITING
    case GameState.Running:
      return BattleStatus.IN_PROGRESS
    case GameState.End:
      return BattleStatus.COMPLETED
    case GameState.Cancelled:
      return BattleStatus.CANCELLED
    default:
      return BattleStatus.WAITING
  }
}

// 数字状态直接转换为BattleStatus
export function numberToBattleStatus(state: number): BattleStatus {
  switch (state) {
    case 1: // Initial
    case 2: // Joinable
    case 3: // Joining
    case 4: // Full
      return BattleStatus.WAITING
    case 5: // Running
      return BattleStatus.IN_PROGRESS
    case 11: // End
      return BattleStatus.COMPLETED
    case 20: // Cancelled
      return BattleStatus.CANCELLED
    default:
      return BattleStatus.WAITING
  }
}

// 对战参与者接口
export interface BattlePlayer {
  uid: string
  nickname: string
  avatar: string
  is_owner: boolean
  is_ready: boolean
  total_value: number
  won_items: any[]
  status: 'joined' | 'ready' | 'playing' | 'finished'
}

// 对战箱子接口
export interface BattleCase {
  cover: string
  name: string
  name_en: string
  name_zh_hans: string
  price: number
  discount: number
  count: number
}

// 对战轮次 - 根据API文档更新
export interface BattleRound {
  case: {
    case_key: string
    name: string
    name_en: string
    name_zh_hans: string
    cover: string
    item: string
    price: number
  }
}

// 用户信息
export interface BattleUser {
  profile: {
    avatar: string
    nickname: string
  }
  uid: string
}

// 下注信息 - 根据API文档更新
export interface BattleBet {
  uid: string
  user: BattleUser
  victory: boolean | null
  open_amount: number
  win_amount: number
  win_items_count: number
  open_items: BattleItem[]
  win_items: BattleItem[]
}

// 对战物品 - 根据API文档更新
export interface BattleItem {
  uid: string
  item_id: number
  name: string
  name_en: string
  name_zh_hans: string
  image: string
  item_price: {
    price: number
    update_time: string
  }
  item_category: {
    cate_id: number
    cate_name: string
    cate_name_en: string
    cate_name_zh_hans: string
    icon: string
  }
  item_quality: {
    quality_id: number
    quality_name: string
    quality_name_en: string
    quality_name_zh_hans: string
    quality_color: string
  }
  item_rarity: {
    rarity_id: number
    rarity_name: string
    rarity_name_en: string
    rarity_name_zh_hans: string
    rarity_color: string
  }
  item_exterior: {
    exterior_id: number
    exterior_name: string
    exterior_name_en: string
    exterior_name_zh_hans: string
    exterior_color: string
  }
}

// 对战房间信息 - 根据API文档更新
export interface BattleRoom {
  uid: string
  short_id: string
  create_time: string
  update_time: string
  max_joiner: number
  price: number
  state: number  // 使用数字状态，与后端保持一致
  type: number
  joiner_count: number
  private: boolean  // 修复：应该是boolean而不是number
  round_count: number
  round_count_current: number
  rounds: BattleRound[]
  bets: BattleBet[]
  user: BattleUser
}

// Battle别名，用于向后兼容
export type Battle = BattleRoom

// 可用箱子接口
export interface AvailableCase {
  id: number
  case_key: string
  name: string
  name_en?: string
  name_zh_hans?: string
  cover: string
  price: number
  discount?: number
  tag?: string
  tag_en?: string
  tag_zh_hans?: string
}

// API参数接口
export interface BattleListParams {
  state_list?: string[]
  page?: number
  page_size?: number
}

export interface JoinRoomParams {
  uid: string
}

export interface CreateBattleParams {
  cases_key: string[]
  max_joiner: number  // 房间最大参与人数，支持2-4人
  private?: number
}

// API服务类
export class BattleApi {
  private apiBase = '/api/box/battle'
  private csrfToken: string | null = null

  /**
   * 获取CSRF Token
   */
  private async getCsrfToken(): Promise<string> {
    try {
      const response = await $fetch<{code: number, message: string, body: {csrf_token: string}}>('/api/auth/csrf/', {
        credentials: 'include'
      })
      
      if (response.code === 0 && response.body) {
        this.csrfToken = response.body.csrf_token
        return this.csrfToken
      } else {
        throw new Error(response.message || '获取CSRF token失败')
      }
    } catch (error: any) {
      // console.error('[Battle API] 获取CSRF token失败:', error)
      throw error
    }
  }

  /**
   * 确保有CSRF Token
   */
  private async ensureCsrfToken(): Promise<string> {
    if (!this.csrfToken) {
      return await this.getCsrfToken()
    }
    return this.csrfToken
  }

  /**
   * 获取特定状态的房间列表
   */
  async getRoomList(params: BattleListParams = {}): Promise<{success: boolean, data: BattleRoom[], total?: number, message?: string}> {
    try {
      const queryParams: any = {
        page: params.page || 1,
        pageSize: params.page_size || 20
      }
      
      // 根据文档，状态参数应该是逗号分隔的字符串
      if (params.state_list && params.state_list.length > 0) {
        queryParams.state = params.state_list.join(',')
      } else {
        queryParams.state = '2,4,5' // 默认获取可加入、已满员、运行中的房间
      }

      // console.log('[Battle API] 获取房间列表，参数:', queryParams)

      const response = await $fetch<ApiResponse<{rooms: any[], total: number}>>(`${this.apiBase}/list/`, {
        method: 'GET',
        query: queryParams,
        credentials: 'include' // 包含认证信息
      })
      
      // console.log('[Battle API] 房间列表响应:', { code: response.code, roomsLength: response.body?.rooms?.length, total: response.body?.total })

      if (response.code === 0 && response.body?.rooms) {
        const transformedRooms = response.body.rooms.map(item => this.transformRoomData(item))
        return {
          success: true,
          data: transformedRooms,
          total: response.body.total || 0,
          message: response.message
        }
      } else {
        return {
          success: false,
          data: [],
          total: 0,
          message: response.message || '获取房间列表失败'
        }
      }
    } catch (error: any) {
      // console.error('[Battle API] 获取房间列表失败:', error)
      return {
        success: false,
        data: [],
        total: 0,
        message: error.message || '网络请求失败'
      }
    }
  }

  /**
   * 加入房间
   */
  async joinRoom(roomUid: string, team = 1): Promise<{success: boolean, data?: any, message?: string}> {
    try {
      // console.log('[Battle API] 加入房间:', { roomUid, team })

      // 获取CSRF token
      let headers: Record<string, string> = {
        'Content-Type': 'application/json'
      }

      try {
        const csrfToken = await this.ensureCsrfToken()
        headers['X-CSRFToken'] = csrfToken
        // console.log('[Battle API] 使用CSRF token加入房间')
      } catch (csrfError) {
        console.warn('[Battle API] 无法获取CSRF token，尝试不使用CSRF:', csrfError)
      }

      const response = await $fetch<ApiResponse<any>>(`${this.apiBase}/join/`, {
        method: 'POST',
        headers,
        body: { uid: roomUid, team },
        credentials: 'include' // 包含认证信息
      })
      
      // console.log('[Battle API] 加入房间响应:', response)

      if (response.code === 0) {
        return { success: true, data: response.body }
      } else {
        return { success: false, message: response.message || '加入房间失败' }
      }
    } catch (error: any) {
      // console.error('[Battle API] 加入房间失败:', error)
      return { success: false, message: error.message || '网络请求失败' }
    }
  }

  /**
   * 创建对战房间
   */
  async createBattle(params: CreateBattleParams): Promise<{success: boolean, data?: any, message?: string}> {
    try {
      // console.log('[Battle API] 创建对战房间:', params)

      // 尝试获取CSRF token（如果需要的话）
      let headers: Record<string, string> = {
        'Content-Type': 'application/json'
      }

      try {
        const csrfToken = await this.ensureCsrfToken()
        headers['X-CSRFToken'] = csrfToken
        // console.log('[Battle API] 使用CSRF token')
      } catch (csrfError) {
        console.warn('[Battle API] 无法获取CSRF token，尝试不使用CSRF:', csrfError)
      }

      const response = await $fetch<ApiResponse<any>>(`${this.apiBase}/create/`, {
        method: 'POST',
        headers,
        body: {
          cases_key: params.cases_key,
          max_joiner: params.max_joiner,
          private: params.private || 0
        },
        credentials: 'include' // 包含认证信息
      })
      
      // console.log('[Battle API] 创建房间响应:', response)

      if (response.code === 0) {
        return { success: true, data: response.body }
      } else {
        return { success: false, message: response.message || '创建房间失败' }
      }
    } catch (error: any) {
      // console.error('[Battle API] 创建房间失败:', error)
      return { success: false, message: error.message || '网络请求失败' }
    }
  }

  /**
   * 获取房间详情
   */
  async getRoomDetail(roomUid: string): Promise<{success: boolean, data?: BattleRoom, message?: string}> {
    try {
      // console.log('[Battle API] 获取房间详情:', roomUid)

      const response = await $fetch<ApiResponse<any>>(`${this.apiBase}/detail/`, {
        method: 'GET',
        query: { uid: roomUid },
        credentials: 'include' // 包含认证信息
      })
      
      // console.log('[Battle API] 房间详情响应:', response)

      if (response.code === 0 && response.body) {
        const room = this.transformRoomData(response.body)
        return { success: true, data: room }
      } else {
        return { success: false, message: response.message || '获取房间详情失败' }
      }
    } catch (error: any) {
      // console.error('[Battle API] 获取房间详情失败:', error)
      return { success: false, message: error.message || '网络请求失败' }
    }
  }

  /**
   * 获取可用于对战的箱子列表
   */
  async getBattleCases(): Promise<{success: boolean, data: AvailableCase[], message?: string}> {
    try {
      // console.log('[Battle API] 获取对战箱子列表')

      const response = await $fetch<ApiResponse<AvailableCase[]>>(`${this.apiBase}/case/`, {
        method: 'GET',
        credentials: 'include' // 包含认证信息
      })
      
      // console.log('[Battle API] 对战箱子响应:', { code: response.code, casesLength: response.body?.length })

      if (response.code === 0 && response.body) {
        return { success: true, data: response.body }
      } else {
        // return { success: false, data: [], message: response.message || '获取对战箱子失败' }
      }
    } catch (error: any) {
      // console.error('[Battle API] 获取对战箱子失败:', error)
      return { success: false, data: [], message: error.message || '网络请求失败' }
    }
  }

  /**
   * 离开房间
   */
  async leaveRoom(roomUid: string): Promise<{success: boolean, data?: any, message?: string}> {
    try {
      // console.log('[Battle API] 离开房间:', roomUid)

      // 获取CSRF token
      let headers: Record<string, string> = {
        'Content-Type': 'application/json'
      }

      try {
        const csrfToken = await this.ensureCsrfToken()
        headers['X-CSRFToken'] = csrfToken
        // console.log('[Battle API] 使用CSRF token离开房间')
      } catch (csrfError) {
        console.warn('[Battle API] 无法获取CSRF token，尝试不使用CSRF:', csrfError)
      }

      const response = await $fetch<ApiResponse<any>>(`${this.apiBase}/quit/`, {
        method: 'POST',
        headers,
        body: { uid: roomUid },
        credentials: 'include' // 包含认证信息
      })
      
      // console.log('[Battle API] 离开房间响应:', response)

      if (response.code === 0) {
        return { success: true, data: response.body }
      } else {
        return { success: false, message: response.message || '离开房间失败' }
      }
    } catch (error: any) {
      // console.error('[Battle API] 离开房间失败:', error)
      return { success: false, message: error.message || '网络请求失败' }
    }
  }

  /**
   * 解散房间（创建者功能）
   */
  async dismissRoom(roomUid: string): Promise<{success: boolean, data?: any, message?: string}> {
    try {
      // console.log('[Battle API] 解散房间:', roomUid)

      // 获取CSRF token
      let headers: Record<string, string> = {
        'Content-Type': 'application/json'
      }

      try {
        const csrfToken = await this.ensureCsrfToken()
        headers['X-CSRFToken'] = csrfToken
        // console.log('[Battle API] 使用CSRF token解散房间')
      } catch (csrfError) {
        console.warn('[Battle API] 无法获取CSRF token，尝试不使用CSRF:', csrfError)
      }

      const response = await $fetch<ApiResponse<any>>(`${this.apiBase}/dismiss/`, {
        method: 'POST',
        headers,
        body: { uid: roomUid },
        credentials: 'include' // 包含认证信息
      })
      
      // console.log('[Battle API] 解散房间响应:', response)

      if (response.code === 0) {
        return { success: true, data: response.body }
      } else {
        return { success: false, message: response.message || '解散房间失败' }
      }
    } catch (error: any) {
      // console.error('[Battle API] 解散房间失败:', error)
      return { success: false, message: error.message || '网络请求失败' }
    }
  }

  /**
   * 获取我创建的对战房间（使用participated接口的type=created参数）
   */
  async getMyCreatedRooms(params?: {
    page?: number
    pageSize?: number
    state?: string
  }): Promise<{success: boolean, data: BattleRoom[], total?: number, message?: string}> {
    return this.getMyParticipatedRooms({
      ...params,
      type: 'created'
    })
  }

  /**
   * 获取我参与的对战房间（支持type参数）
   */
  async getMyParticipatedRooms(params?: {
    page?: number
    pageSize?: number
    state?: string
    type?: 'all' | 'created' | 'joined'
  }): Promise<{success: boolean, data: BattleRoom[], total?: number, message?: string}> {
    try {
      // console.log('[Battle API] 获取我参与的对战:', params)

      const queryParams = {
        page: params?.page || 1,
        pageSize: params?.pageSize || 10,
        ...(params?.state && { state: params.state }),
        ...(params?.type && { type: params.type })
      }

      const response = await $fetch<ApiResponse<{rooms: any[], total: number}>>(`${this.apiBase}/participated/`, {
        method: 'GET',
        query: queryParams,
        credentials: 'include' // 包含认证信息
      })
      
      // console.log('[Battle API] 我参与的对战响应:', { 
      //   code: response.code, 
      //   roomsLength: response.body?.rooms?.length, 
      //   total: response.body?.total,
      //   type: params?.type || 'all'
      // })

      if ((response.code === 0 || response.code === 200) && response.body?.rooms) {
        const transformedRooms = response.body.rooms.map(item => this.transformRoomData(item))
        return {
          success: true,
          data: transformedRooms,
          total: response.body.total || 0,
          message: response.message
        }
      } else {
        return {
          success: false,
          data: [],
          total: 0,
          message: response.message || '获取我参与的对战失败'
        }
      }
    } catch (error: any) {
      // console.error('[Battle API] 获取我参与的对战失败:', error)
      return {
        success: false,
        data: [],
        total: 0,
        message: error.message || '网络请求失败'
      }
    }
  }

  /**
   * 获取我的对战（兼容旧版本）
   */
  async getMyRooms(params?: {
    page?: number
    pageSize?: number
  }): Promise<{success: boolean, data: BattleRoom[], total?: number, message?: string}> {
    try {
      // console.log('[Battle API] 获取我的对战:', params)

      const queryParams = {
        page: params?.page || 1,
        pageSize: params?.pageSize || 10
      }

      const response = await $fetch<ApiResponse<{rooms: any[], total: number}>>(`${this.apiBase}/self/`, {
        method: 'GET',
        query: queryParams,
        credentials: 'include' // 包含认证信息
      })
      
      // console.log('[Battle API] 我的对战响应:', { code: response.code, roomsLength: response.body?.rooms?.length, total: response.body?.total })

      if ((response.code === 0 || response.code === 200) && response.body?.rooms) {
        const transformedRooms = response.body.rooms.map(item => this.transformRoomData(item))
        return {
          success: true,
          data: transformedRooms,
          total: response.body.total || 0,
          message: response.message
        }
      } else {
        return {
          success: false,
          data: [],
          total: 0,
          message: response.message || '获取我的对战失败'
        }
      }
    } catch (error: any) {
      // console.error('[Battle API] 获取我的对战失败:', error)
      return {
        success: false,
        data: [],
        total: 0,
        message: error.message || '网络请求失败'
      }
    }
  }

  /**
   * 转换房间数据格式
   */
  private transformRoomData(rawData: any): BattleRoom {
    return {
      uid: rawData.uid,
      short_id: rawData.short_id,
      create_time: rawData.create_time,
      update_time: rawData.update_time,
      max_joiner: rawData.max_joiner,
      price: rawData.price,
      state: rawData.state,
      type: rawData.type,
      joiner_count: rawData.joiner_count,
      private: rawData.private,
      round_count: rawData.round_count,
      round_count_current: rawData.round_count_current,
      rounds: rawData.rounds || [],
      bets: rawData.bets || [],
      user: rawData.user
    }
  }

  /**
   * 获取动画状态以实现断线重连
   */
  async getAnimationState(roomUid: string): Promise<{success: boolean, data?: any, message?: string}> {
    try {
      // console.log('[Battle API] 获取动画状态:', roomUid);

      const response = await $fetch<ApiResponse<any>>(`${this.apiBase}/animation-state/`, {
        method: 'GET',
        query: { uid: roomUid },
        credentials: 'include'
      });

      // console.log('[Battle API] 动画状态响应:', response);

      if (response.code === 0 && response.body) {
        return { success: true, data: response.body };
      } else {
        return { success: false, message: response.message || '获取动画状态失败' };
      }
    } catch (error: any) {
      // console.error('[Battle API] 获取动画状态失败:', error);
      return { success: false, message: error.message || '网络请求失败' };
    }
  }

  /**
   * 获取状态显示文本
   */
  getStateText(state: GameState, locale: 'zh' | 'en' = 'zh'): string {
    const stateTexts = {
      zh: {
        [GameState.Initial]: '初始状态',
        [GameState.Joinable]: '可加入',
        [GameState.Joining]: '加入中',
        [GameState.Full]: '已满员',
        [GameState.Running]: '进行中',
        [GameState.End]: '已结束',
        [GameState.Cancelled]: '已取消'
      },
      en: {
        [GameState.Initial]: 'Initial',
        [GameState.Joinable]: 'Joinable',
        [GameState.Joining]: 'Joining',
        [GameState.Full]: 'Full',
        [GameState.Running]: 'Running',
        [GameState.End]: 'Ended',
        [GameState.Cancelled]: 'Cancelled'
      }
    }
    
    return stateTexts[locale][state] || 'Unknown'
  }

  /**
   * 获取状态颜色
   */
  getStateColor(state: GameState): string {
    const stateColors = {
      [GameState.Initial]: 'gray',
      [GameState.Joinable]: 'green',
      [GameState.Joining]: 'blue',
      [GameState.Full]: 'orange',
      [GameState.Running]: 'purple',
      [GameState.End]: 'gray',
      [GameState.Cancelled]: 'red'
    }
    
    return stateColors[state] || 'gray'
  }
}

// 导出API实例
export const battleApi = new BattleApi()
export default battleApi 