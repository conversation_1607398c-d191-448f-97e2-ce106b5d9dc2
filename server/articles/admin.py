from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone

from .models import ContentCategory, ContentTag, Content, ContentAttachment, ContentView


@admin.register(ContentCategory)
class ContentCategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'slug', 'content_count', 'sort_order', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'description']
    prepopulated_fields = {'slug': ('name',)}
    ordering = ['sort_order', 'name']
    
    def content_count(self, obj):
        return obj.contents.count()
    content_count.short_description = _('Content Count')


@admin.register(ContentTag)
class ContentTagAdmin(admin.ModelAdmin):
    list_display = ['name', 'slug', 'colored_tag', 'content_count', 'is_active']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name']
    prepopulated_fields = {'slug': ('name',)}
    
    def colored_tag(self, obj):
        return format_html(
            '<span style="background-color: {}; color: white; padding: 2px 6px; border-radius: 3px;">{}</span>',
            obj.color, obj.name
        )
    colored_tag.short_description = _('Tag Preview')
    
    def content_count(self, obj):
        return obj.contents.count()
    content_count.short_description = _('Content Count')


class ContentAttachmentInline(admin.TabularInline):
    model = ContentAttachment
    extra = 0
    fields = ['name', 'file_path', 'file_type', 'file_size', 'description']


@admin.register(Content)
class ContentAdmin(admin.ModelAdmin):
    list_display = [
        'title', 'content_type', 'status_badge', 'priority_badge', 
        'category', 'author', 'publish_date', 'view_count', 'is_featured', 'is_pinned'
    ]
    list_filter = [
        'content_type', 'status', 'priority', 'is_featured', 'is_pinned',
        'category', 'tags', 'publish_date', 'created_at'
    ]
    search_fields = ['title', 'subtitle', 'excerpt', 'content']
    prepopulated_fields = {'slug': ('title',)}
    filter_horizontal = ['tags']
    inlines = [ContentAttachmentInline]
    
    fieldsets = (
        (_('Basic Information'), {
            'fields': ('title', 'slug', 'subtitle', 'excerpt', 'content')
        }),
        (_('Classification'), {
            'fields': ('content_type', 'category', 'tags')
        }),
        (_('Publishing'), {
            'fields': ('status', 'priority', 'is_featured', 'is_pinned', 'publish_date', 'expire_date')
        }),
        (_('Author & Editor'), {
            'fields': ('author', 'editor')
        }),
        (_('Media'), {
            'fields': ('featured_image', 'thumbnail'),
            'classes': ('collapse',)
        }),
        (_('SEO'), {
            'fields': ('seo_title', 'seo_description', 'seo_keywords'),
            'classes': ('collapse',)
        }),
        (_('Display Settings'), {
            'fields': ('sort_order', 'allow_comments'),
            'classes': ('collapse',)
        }),
        (_('Style Settings'), {
            'fields': ('custom_css_class', 'background_color', 'text_color'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ['view_count', 'created_at', 'updated_at']
    
    def status_badge(self, obj):
        colors = {
            'draft': '#6c757d',
            'published': '#28a745',
            'archived': '#ffc107',
            'scheduled': '#17a2b8'
        }
        color = colors.get(obj.status, '#6c757d')
        return format_html(
            '<span style="background-color: {}; color: white; padding: 2px 6px; border-radius: 3px;">{}</span>',
            color, obj.get_status_display()
        )
    status_badge.short_description = _('Status')
    
    def priority_badge(self, obj):
        colors = {
            'low': '#6c757d',
            'normal': '#17a2b8',
            'high': '#ffc107',
            'urgent': '#dc3545'
        }
        color = colors.get(obj.priority, '#6c757d')
        return format_html(
            '<span style="background-color: {}; color: white; padding: 2px 6px; border-radius: 3px;">{}</span>',
            color, obj.get_priority_display()
        )
    priority_badge.short_description = _('Priority')
    
    def save_model(self, request, obj, form, change):
        if not change:  # 新建时设置作者
            obj.author = request.user
        obj.editor = request.user  # 每次保存都更新编辑者
        super().save_model(request, obj, form, change)
    
    actions = ['make_published', 'make_draft', 'make_featured', 'remove_featured']
    
    def make_published(self, request, queryset):
        updated = queryset.update(status='published')
        self.message_user(request, f'{updated} 篇内容已发布')
    make_published.short_description = _('Mark selected contents as published')
    
    def make_draft(self, request, queryset):
        updated = queryset.update(status='draft')
        self.message_user(request, f'{updated} 篇内容已设为草稿')
    make_draft.short_description = _('Mark selected contents as draft')
    
    def make_featured(self, request, queryset):
        updated = queryset.update(is_featured=True)
        self.message_user(request, f'{updated} 篇内容已设为推荐')
    make_featured.short_description = _('Mark selected contents as featured')
    
    def remove_featured(self, request, queryset):
        updated = queryset.update(is_featured=False)
        self.message_user(request, f'{updated} 篇内容已取消推荐')
    remove_featured.short_description = _('Remove featured from selected contents')


@admin.register(ContentAttachment)
class ContentAttachmentAdmin(admin.ModelAdmin):
    list_display = ['name', 'content', 'file_type', 'file_size_display', 'download_count', 'created_at']
    list_filter = ['file_type', 'created_at']
    search_fields = ['name', 'content__title']
    
    def file_size_display(self, obj):
        if obj.file_size < 1024:
            return f"{obj.file_size} B"
        elif obj.file_size < 1024 * 1024:
            return f"{obj.file_size / 1024:.1f} KB"
        else:
            return f"{obj.file_size / (1024 * 1024):.1f} MB"
    file_size_display.short_description = _('File Size')


@admin.register(ContentView)
class ContentViewAdmin(admin.ModelAdmin):
    list_display = ['content', 'user', 'ip_address', 'viewed_at']
    list_filter = ['viewed_at', 'content__content_type']
    search_fields = ['content__title', 'user__username', 'ip_address']
    readonly_fields = ['content', 'user', 'ip_address', 'user_agent', 'viewed_at']
    
    def has_add_permission(self, request):
        return False  # 不允许手动添加浏览记录
