# 常见问题与故障排除

## 目录
- [安装和设置问题](#安装和设置问题)
- [认证和登录问题](#认证和登录问题)
- [数据库问题](#数据库问题)
- [WebSocket连接问题](#websocket连接问题)
- [性能问题](#性能问题)
- [部署问题](#部署问题)
- [前端开发问题](#前端开发问题)
- [API问题](#api问题)
- [监控和日志](#监控和日志)
- [安全问题](#安全问题)

## 安装和设置问题

### Q: Docker容器启动失败
**问题描述**: 运行 `docker-compose up` 时容器无法启动

**可能原因**:
- 端口被占用
- 环境文件缺失
- 权限问题
- 资源不足

**解决方案**:
```bash
# 1. 检查端口占用
sudo netstat -tulpn | grep :8000
sudo netstat -tulpn | grep :3001
sudo netstat -tulpn | grep :3306

# 2. 检查环境文件
ls -la server/.env
cp server/example.env server/.env

# 3. 检查Docker权限
sudo usermod -aG docker $USER
newgrp docker

# 4. 检查系统资源
free -h
df -h

# 5. 清理Docker资源
docker system prune -a
docker-compose down -v
docker-compose up --build
```

### Q: Python依赖安装失败
**问题描述**: `pip install -r requirements.txt` 报错

**解决方案**:
```bash
# 1. 升级pip
python -m pip install --upgrade pip

# 2. 使用国内镜像源
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 3. 安装系统依赖
sudo apt-get update
sudo apt-get install python3-dev libmysqlclient-dev

# 4. 使用虚拟环境
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

### Q: Node.js模块安装问题
**问题描述**: `npm install` 失败或速度慢

**解决方案**:
```bash
# 1. 清理npm缓存
npm cache clean --force

# 2. 使用淘宝镜像
npm config set registry https://registry.npm.taobao.org

# 3. 删除node_modules重新安装
rm -rf node_modules package-lock.json
npm install

# 4. 使用yarn
npm install -g yarn
yarn install

# 5. 设置npm配置
npm config set unsafe-perm true
npm config set user 0
```

## 认证和登录问题

### Q: Steam登录重定向错误
**问题描述**: Steam登录后无法正确重定向

**检查项目**:
```bash
# 1. 检查Steam配置
grep -r "STEAM_API_KEY" server/
grep -r "SOCIAL_AUTH_STEAM_API_KEY" server/

# 2. 检查回调URL
grep -r "SOCIAL_AUTH_STEAM_REDIRECT_URI" server/
```

**解决方案**:
```python
# server/steambase/settings.py
SOCIAL_AUTH_STEAM_API_KEY = 'your_steam_api_key'
SOCIAL_AUTH_STEAM_REDIRECT_URI = 'http://localhost:8000/auth/steam/complete/'

# 检查Steam应用设置
# 1. 登录Steam开发者控制台
# 2. 确认Web API Key正确
# 3. 检查域名白名单
```

### Q: JWT Token验证失败
**问题描述**: API请求返回401未授权错误

**调试步骤**:
```bash
# 1. 检查token
curl -H "Authorization: Bearer YOUR_TOKEN" http://localhost:8000/api/auth/profile/

# 2. 检查token格式
python -c "
import jwt
token = 'YOUR_TOKEN'
print(jwt.decode(token, options={'verify_signature': False}))
"

# 3. 检查token过期时间
grep -r "JWT_EXPIRATION_DELTA" server/
```

**解决方案**:
```python
# 检查JWT配置
JWT_AUTH = {
    'JWT_EXPIRATION_DELTA': datetime.timedelta(days=7),
    'JWT_ALLOW_REFRESH': True,
    'JWT_REFRESH_EXPIRATION_DELTA': datetime.timedelta(days=30),
}
```

## 数据库问题

### Q: 数据库连接失败
**问题描述**: Django无法连接到MySQL数据库

**检查步骤**:
```bash
# 1. 检查MySQL服务状态
sudo systemctl status mysql
# 或
docker ps | grep mysql

# 2. 测试数据库连接
mysql -h localhost -u root -p -e "SHOW DATABASES;"

# 3. 检查Django数据库配置
python manage.py dbshell
```

**解决方案**:
```python
# server/steambase/settings.py
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'steambase',
        'USER': 'root',
        'PASSWORD': 'your_password',
        'HOST': 'localhost',  # 或容器名称
        'PORT': '3306',
        'OPTIONS': {
            'charset': 'utf8mb4',
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
        }
    }
}
```

### Q: 数据库迁移失败
**问题描述**: `python manage.py migrate` 报错

**解决方案**:
```bash
# 1. 检查迁移状态
python manage.py showmigrations

# 2. 假迁移已执行的文件
python manage.py migrate --fake-initial

# 3. 重新生成迁移文件
python manage.py makemigrations
python manage.py migrate

# 4. 手动修复迁移冲突
python manage.py migrate --merge

# 5. 重置特定应用的迁移
python manage.py migrate authentication zero
python manage.py migrate authentication
```

### Q: Redis连接问题
**问题描述**: 缓存或会话存储失败

**检查步骤**:
```bash
# 1. 检查Redis服务
redis-cli ping
# 或
docker exec -it redis_container redis-cli ping

# 2. 检查Redis配置
grep -r "REDIS" server/steambase/settings.py

# 3. 测试连接
python -c "
import redis
r = redis.Redis(host='localhost', port=6379, db=0)
print(r.ping())
"
```

## WebSocket连接问题

### Q: WebSocket连接失败
**问题描述**: 前端无法连接到WebSocket服务器

**调试步骤**:
```bash
# 1. 检查WebSocket服务器状态
ps aux | grep node
netstat -tulpn | grep 3001

# 2. 检查防火墙设置
sudo ufw status
sudo iptables -L

# 3. 测试WebSocket连接
node -e "
const io = require('socket.io-client');
const socket = io('http://localhost:3001');
socket.on('connect', () => console.log('Connected'));
socket.on('connect_error', (err) => console.log('Error:', err));
"
```

**解决方案**:
```javascript
// deployment/node/nodejs/ws_server.js
const io = require('socket.io')(server, {
  cors: {
    origin: ["http://localhost:3000", "https://yourdomain.com"],
    methods: ["GET", "POST"],
    credentials: true
  },
  transports: ['websocket', 'polling']
});

// 检查端口监听
server.listen(3001, '0.0.0.0', () => {
  console.log('WebSocket server running on port 3001');
});
```

### Q: WebSocket认证失败
**问题描述**: WebSocket连接后认证验证失败

**解决方案**:
```javascript
// 检查认证中间件
io.use((socket, next) => {
  const token = socket.handshake.auth.token;
  if (!token) {
    return next(new Error('Authentication token required'));
  }
  
  // 验证JWT token
  jwt.verify(token, JWT_SECRET, (err, decoded) => {
    if (err) {
      return next(new Error('Invalid token'));
    }
    socket.userId = decoded.user_id;
    next();
  });
});
```

## 性能问题

### Q: 数据库查询慢
**问题描述**: API响应时间过长

**优化步骤**:
```bash
# 1. 启用慢查询日志
# MySQL配置文件 /etc/mysql/mysql.conf.d/mysqld.cnf
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2

# 2. 分析慢查询
mysqldumpslow /var/log/mysql/slow.log

# 3. 使用Django Debug Toolbar
pip install django-debug-toolbar
```

**数据库优化**:
```sql
-- 检查表结构和索引
SHOW INDEX FROM authentication_user;
EXPLAIN SELECT * FROM box_boxitem WHERE box_id = 1;

-- 添加必要索引
CREATE INDEX idx_box_boxitem_box_id ON box_boxitem(box_id);
CREATE INDEX idx_authentication_user_steam_id ON authentication_user(steam_id);
```

### Q: 内存使用过高
**问题描述**: 服务器内存占用过高

**监控和分析**:
```bash
# 1. 检查内存使用
free -h
top -o %MEM

# 2. 检查Python进程内存
pip install memory_profiler
python -m memory_profiler manage.py runserver

# 3. 检查MySQL内存配置
SHOW VARIABLES LIKE 'innodb_buffer_pool_size';
```

**优化方案**:
```python
# server/steambase/settings.py
# 数据库连接池配置
DATABASES['default']['CONN_MAX_AGE'] = 60

# 缓存配置
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
            'CONNECTION_POOL_KWARGS': {'max_connections': 20}
        }
    }
}
```

### Q: 静态文件加载慢
**问题描述**: 前端资源加载时间过长

**解决方案**:
```nginx
# nginx配置优化
location /static/ {
    alias /path/to/static/;
    expires 1y;
    add_header Cache-Control "public, immutable";
    gzip on;
    gzip_types text/css application/javascript image/svg+xml;
}

location /media/ {
    alias /path/to/media/;
    expires 30d;
    add_header Cache-Control "public";
}
```

## 部署问题

### Q: SSL证书配置问题
**问题描述**: HTTPS无法正常工作

**检查步骤**:
```bash
# 1. 检查证书文件
ls -la deployment/nginx/cert/
openssl x509 -in cert.pem -text -noout

# 2. 检查Nginx配置
nginx -t
systemctl status nginx

# 3. 检查端口监听
netstat -tulpn | grep :443
```

**Nginx SSL配置**:
```nginx
server {
    listen 443 ssl http2;
    server_name yourdomain.com;
    
    ssl_certificate /etc/nginx/cert/cert.pem;
    ssl_certificate_key /etc/nginx/cert/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    
    # SSL安全配置
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # HSTS
    add_header Strict-Transport-Security "max-age=31536000" always;
}
```

### Q: Docker容器重启问题
**问题描述**: 容器频繁重启或无法启动

**调试步骤**:
```bash
# 1. 查看容器日志
docker logs container_name
docker-compose logs service_name

# 2. 检查容器资源限制
docker stats
docker inspect container_name

# 3. 检查健康检查
docker-compose ps
```

**解决方案**:
```yaml
# docker-compose.yml
version: '3.8'
services:
  web:
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health/"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
```

## 前端开发问题

### Q: CORS跨域问题
**问题描述**: 前端请求API时出现跨域错误

**后端解决方案**:
```python
# server/steambase/settings.py
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
    "https://yourdomain.com"
]

CORS_ALLOW_CREDENTIALS = True
CORS_ALLOW_ALL_ORIGINS = False  # 生产环境设为False
```

**Nginx解决方案**:
```nginx
location /api/ {
    proxy_pass http://backend:8000;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    
    # CORS headers
    add_header Access-Control-Allow-Origin $http_origin;
    add_header Access-Control-Allow-Credentials true;
    add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
    add_header Access-Control-Allow-Headers "Content-Type, Authorization";
    
    if ($request_method = 'OPTIONS') {
        return 204;
    }
}
```

### Q: 前端构建失败
**问题描述**: `npm run build` 构建失败

**解决方案**:
```bash
# 1. 清理缓存
rm -rf node_modules package-lock.json
npm cache clean --force
npm install

# 2. 检查Node.js版本
node --version
npm --version

# 3. 增加内存限制
NODE_OPTIONS="--max_old_space_size=4096" npm run build

# 4. 检查代码语法错误
npm run lint
npm run type-check
```

### Q: 生产环境前端路由404
**问题描述**: 刷新页面时出现404错误

**Nginx配置**:
```nginx
location / {
    root /usr/share/nginx/html;
    try_files $uri $uri/ /index.html;
    
    # 缓存静态资源
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

## API问题

### Q: API响应格式不一致
**问题描述**: 不同接口返回格式不统一

**标准化方案**:
```python
# server/libs/response.py
from rest_framework.response import Response

class StandardResponse:
    @staticmethod
    def success(data=None, message="Success"):
        return Response({
            "code": 200,
            "message": message,
            "data": data,
            "timestamp": timezone.now().isoformat()
        })
    
    @staticmethod
    def error(message="Error", code=400, data=None):
        return Response({
            "code": code,
            "message": message,
            "data": data,
            "timestamp": timezone.now().isoformat()
        }, status=code)
```

### Q: API版本管理问题
**问题描述**: API升级后兼容性问题

**版本管理方案**:
```python
# server/steambase/urls.py
urlpatterns = [
    path('api/v1/', include('v1.urls')),
    path('api/v2/', include('v2.urls')),
    path('api/', include('v2.urls')),  # 默认最新版本
]

# 视图中处理版本
class UserViewSet(viewsets.ModelViewSet):
    def get_serializer_class(self):
        if self.request.version == 'v1':
            return UserSerializerV1
        return UserSerializerV2
```

## 监控和日志

### Q: 日志文件过大
**问题描述**: 日志文件占用大量磁盘空间

**日志轮转配置**:
```bash
# /etc/logrotate.d/steambase
/www/wwwroot/csgoskins.com.cn/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload nginx
    endscript
}
```

**Django日志配置**:
```python
# server/steambase/settings.py
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': 'logs/django.log',
            'maxBytes': 1024*1024*15,  # 15MB
            'backupCount': 10,
            'formatter': 'verbose',
        },
    },
    'root': {
        'handlers': ['file'],
        'level': 'INFO',
    },
}
```

### Q: 性能监控设置
**问题描述**: 缺乏系统性能监控

**监控方案**:
```bash
# 1. 安装系统监控工具
sudo apt-get install htop iotop nethogs

# 2. 设置MySQL慢查询监控
# 在MySQL配置中启用
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2

# 3. 设置Nginx访问日志分析
tail -f /var/log/nginx/access.log | grep "POST\|PUT\|DELETE"

# 4. 使用Django性能分析
pip install django-silk
# 添加到INSTALLED_APPS和URLs
```

## 安全问题

### Q: SQL注入防护
**问题描述**: 担心SQL注入攻击

**防护措施**:
```python
# 使用Django ORM（自动防护）
User.objects.filter(username=username)

# 避免原生SQL，如必须使用则参数化
cursor.execute("SELECT * FROM users WHERE id = %s", [user_id])

# 输入验证
from django.core.validators import validate_email
from rest_framework import serializers

class UserSerializer(serializers.ModelSerializer):
    email = serializers.EmailField(validators=[validate_email])
```

### Q: XSS防护
**问题描述**: 防止跨站脚本攻击

**防护配置**:
```python
# server/steambase/settings.py
# 安全中间件
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    # ... 其他中间件
]

# 安全设置
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
X_FRAME_OPTIONS = 'DENY'

# CSP配置
SECURE_REFERRER_POLICY = 'strict-origin-when-cross-origin'
```

### Q: CSRF防护
**问题描述**: CSRF token验证问题

**解决方案**:
```python
# API视图中禁用CSRF（使用其他认证方式）
from django.views.decorators.csrf import csrf_exempt
from rest_framework.decorators import authentication_classes, permission_classes

@csrf_exempt
@api_view(['POST'])
@authentication_classes([JWTAuthentication])
@permission_classes([IsAuthenticated])
def api_view(request):
    # 处理逻辑
    pass
```

## 快速诊断脚本

创建快速诊断脚本来检查常见问题：

```bash
#!/bin/bash
# diagnosis.sh - 快速诊断脚本

echo "=== CSGO皮肤交易平台 - 系统诊断 ==="

# 检查服务状态
echo "1. 检查服务状态..."
docker-compose ps
systemctl status nginx mysql redis-server

# 检查端口监听
echo "2. 检查端口监听..."
netstat -tulpn | grep -E ":8000|:3001|:3306|:6379|:80|:443"

# 检查磁盘空间
echo "3. 检查磁盘空间..."
df -h

# 检查内存使用
echo "4. 检查内存使用..."
free -h

# 检查数据库连接
echo "5. 检查数据库连接..."
python server/manage.py dbshell --command="SELECT 1;"

# 检查Redis连接
echo "6. 检查Redis连接..."
redis-cli ping

# 检查日志错误
echo "7. 检查最近错误日志..."
tail -20 logs/steambase.log | grep -i error
tail -20 /var/log/nginx/error.log

echo "=== 诊断完成 ==="
```

使用方法：
```bash
chmod +x diagnosis.sh
./diagnosis.sh
```

这个FAQ文档涵盖了开发和部署过程中最常遇到的问题，提供了详细的诊断步骤和解决方案，帮助开发者快速定位和解决问题。
