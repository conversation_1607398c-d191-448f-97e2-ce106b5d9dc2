/**
 * 战斗详情页主要组合函数
 * 整合所有子功能模块，提供统一的接口
 */
import { onMounted, onUnmounted, nextTick } from 'vue'
import { useBattleDetailState } from './useBattleDetailState'
import { useBattleDetailCaseItems } from './useBattleDetailCaseItems'
import { useBattleDetailActions } from './useBattleDetailActions'
import { useBattleDetailDebug } from './useBattleDetailDebug'

export interface BattleDetailPageComposable {
  // 状态管理
  state: ReturnType<typeof useBattleDetailState>
  // 案例物品管理
  caseItems: ReturnType<typeof useBattleDetailCaseItems>
  // 事件处理
  actions: ReturnType<typeof useBattleDetailActions>
  // 调试功能
  debug: ReturnType<typeof useBattleDetailDebug>
  // 初始化函数
  initializePage: () => Promise<void>
}

export function useBattleDetailPage(): BattleDetailPageComposable {
  // 获取核心管理器
  const battleCore = useBattleCore()
  const roundManager = useBattleRoundManager()
  const memoryManager = useBattleMemoryManager()
  const route = useRoute()

  // 初始化各个功能模块
  const state = useBattleDetailState()
  const caseItems = useBattleDetailCaseItems()
  const actions = useBattleDetailActions()
  const debug = useBattleDetailDebug()

  // 页面初始化函数
  const initializePage = async () => {
    console.log("[🎰BATTLE-PAGE] 开始初始化页面")

    // 设置调试监控器
    debug.setupDebugWatchers()

    // 设置动画事件监听器（防重复）
    actions.setupAnimationEventListeners()

    try {
      // 🎯 等待路由参数和battleCore准备就绪
      await nextTick()
      
      // 🎯 检查初始化状态，使用更智能的检测逻辑
      const checkInitialization = () => {
        const hasData = !!battleCore.battleState.battleData.value
        const isLoading = battleCore.battleState.isLoading.value
        const hasError = !!battleCore.battleState.error.value
        
        if (hasData || hasError) {
          console.log("[🎰BATTLE-PAGE] 初始化已完成", { hasData, hasError })
          actions.handleInitializationCompleteWithCheck()
          return true
        } else if (!isLoading) {
          console.log("[🎰BATTLE-PAGE] 需要执行备选初始化")
          actions.handleFallbackInitialization()
          return true
        }
        
        return false
      }

      // 立即检查一次
      if (!checkInitialization()) {
        // 如果初始化未完成，创建定时器等待
        console.log("[🎰BATTLE-PAGE] 等待初始化完成...")
        
        let attempts = 0
        const maxAttempts = 50 // 最多等待5秒
        
        const waitForInitialization = () => {
          attempts++
          
          if (checkInitialization()) {
            console.log("[🎰BATTLE-PAGE] 初始化完成", { attempts })
            return
          }
          
          if (attempts >= maxAttempts) {
            console.warn("[🎰BATTLE-PAGE] 初始化等待超时，执行备选方案")
            actions.handleFallbackInitialization()
            return
          }
          
          // 继续等待
          memoryManager.createTimer(waitForInitialization, 100)
        }
        
        memoryManager.createTimer(waitForInitialization, 100)
      }
    } catch (error) {
      console.error("[🎰BATTLE-PAGE] 页面初始化异常:", error)
    }

    console.log("[🎰BATTLE-PAGE] 页面初始化完成")
  }

  // 页面挂载时初始化
  onMounted(async () => {
    console.log("[🎰BATTLE-PAGE] 页面挂载，开始初始化")
    
    // 获取路由参数
    const battleId = route.params.id as string
    console.log("[🎰BATTLE-PAGE] 当前对战ID:", battleId)

    // 初始化battleCore（简化处理）
    console.log("[🎰BATTLE-PAGE] 初始化battleCore for battle:", battleId)
    
    // 初始化页面
    await initializePage()
  })

  // 页面卸载时清理
  onUnmounted(() => {
    console.log("[🎰BATTLE-PAGE] 页面卸载，开始清理")
    
    // 清理内存管理器
    memoryManager.clearAllTimers()
    
    // 清理battleCore相关资源（简化处理）
    console.log("[🎰BATTLE-PAGE] 清理battleCore资源")
    
    console.log("[🎰BATTLE-PAGE] 页面清理完成")
  })

  return {
    state,
    caseItems,
    actions,
    debug,
    initializePage,
  }
}
