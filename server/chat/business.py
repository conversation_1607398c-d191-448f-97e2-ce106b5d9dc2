import numpy
import random
import schedule
import time
import threading
import json
import logging

from django.conf import settings
from django.core.cache import cache
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger

from django.contrib.auth import get_user_model
from django.db import connection
from django.db.models import Q
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from datetime import timedelta, timezone as dt_timezone

from django_redis import get_redis_connection
from dateutil.parser import parse as parseDate

from steambase.enums import RespCode, ChargeState, PayType
from steambase.redis_con import get_redis
from steambase.utils import is_connection_usable, DecimalEncoder
from sitecfg.interfaces import get_exchange_rate
from libs.steamapi import SteamApi
from chat.models import Message, ChatBot, ChatBotMessage
from chat.serializers import MessageSerializer


_logger = logging.getLogger(__name__)
_box_redis_channel_key = 'box_game_channel'
_ws_channel_key = 'ws_channel'

CST_TZ = dt_timezone(timedelta(hours=8))  # 东八区

def ws_send_msg(data):
    r = get_redis_connection('default')
    r.publish(_ws_channel_key, json.dumps(data, cls=DecimalEncoder))


def send_chat_message(user, message):
    if user.asset.total_charge_balance < 0.5:
        return RespCode.InvalidParams.value, _('At least 50coins to chat')
    if user.extra.ban_chat:
        return RespCode.InvalidParams.value, _('You have been banned chat')
    if message:
        # message = html.escape(message)
        m_serializer = MessageSerializer(data={'user': user, 'message': message})
        if m_serializer.is_valid():
            m_serializer.save(user=user)
            msg = ['message', [m_serializer.data]]
            ws_send_msg(msg)
    return RespCode.Succeed.value, {}


def get_last_chat_msg():
    max_count = settings.CHAT_MESSAGE_LAST_COUNT
    messages = Message.objects.all().order_by('-timestamp')[:max_count]
    msgs = []
    for m in messages:
        msgs.append(MessageSerializer(m).data)
    msgs = msgs[::-1]
    return msgs


def check_chat_bot():
    while True:
        try:
            if not is_connection_usable():
                connection.close()

            bots = ChatBot.objects.filter(enable=True)
            for bot in bots:
                last_msg = Message.objects.filter(user=bot.user).order_by('-id').first()
                last_msg_past = (timezone.now() - last_msg.timestamp).seconds
                if last_msg_past < bot.chat_idle_min:
                    continue
                elif last_msg_past > bot.chat_idle_max:
                    chat = True
                else:
                    chat_probability = 1 / (bot.chat_idle_max - last_msg_past + 1)
                    percentage = numpy.random.uniform(0, 1)
                    chat = chat_probability > percentage
                if chat:
                    msg_list = bot.message.filter(enable=True)
                    if msg_list:
                        msg = random.choice(msg_list)
                        send_chat_message(bot.user, msg.message)
                        break
        except Exception as e:
            _logger.exception(e)
        finally:
            time.sleep(1)


def get_message(user, fields, page, page_size):
    # 查询该用户接收到的消息
    query_set = Message.objects.filter(recipient=user).order_by('-sent_at')  # 按照发送时间降序排列
    paginator = Paginator(query_set, page_size)

    try:
        messages = paginator.page(page)
    except PageNotAnInteger:
        messages = paginator.page(1)
    except EmptyPage:
        messages = paginator.page(paginator.num_pages)

    # 序列化消息数据
    serializer = MessageSerializer(messages, many=True, fields=fields)
    
    resp = {
        'items': serializer.data,
        'total': paginator.count,
        'page': page,
        'page_size': page_size
    }
    return RespCode.Succeed.value, resp

def mark_message_as_read(user, message_id):
    """
    标记指定消息为已读。
    """
    # 查询消息并确保消息属于当前用户
    message = Message.objects.filter(id=message_id, recipient=user).first()
    if not message:
        return RespCode.NoPermission.value, {}, _('Message not found or permission denied')
    
    # 标记为已读
    message.mark_as_read()

    # 返回成功响应以及消息数据
    fields = ('id', 'subject', 'body', 'sender', 'read_at', 'sent_at')
    data = {field: getattr(message, field) for field in fields}  # 提取指定字段数据
    return RespCode.Succeed.value, data, _('Message marked as read')

def get_message_detail(user, message_id):
    # 查询消息并检查是否属于当前用户
    message = Message.objects.filter(id=message_id, recipient=user).first()
    if not message:
        return RespCode.NoPermission.value, {}, 'Message not found or permission denied'
    
    # 自动标记为已读
    if not message.read_at:
        message.read_at = timezone.now().astimezone(CST_TZ)  # 设置阅读时间为东八区的当前时间
        message.save(update_fields=['read_at'])
    
    # 序列化消息数据
    serializer = MessageSerializer(message)
    return RespCode.Succeed.value, serializer.data



    

