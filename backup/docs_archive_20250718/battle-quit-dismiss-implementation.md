# 对战房间退出和解散功能分离实现

## 概述

本文档描述了对战房间退出和解散功能的分离实现，将原来混合在一个接口中的功能拆分为两个独立的接口。

## 功能分离

### 原有设计问题
- 房主和普通参与者都使用同一个 `POST /api/box/battle/quit/` 接口
- 房主退出会导致整个房间解散，所有参与者退款
- 普通参与者退出只退出自己
- 功能混合在一个接口中，逻辑不够清晰

### 新设计方案
- **退出接口** (`POST /api/box/battle/quit/`)：仅供普通参与者使用
- **解散接口** (`POST /api/box/battle/dismiss/`)：仅供房主使用

## 实现详情

### 1. 后端实现

#### 新增解散函数
```python
def dismiss_case_room(user, uid):
    """
    房主解散对战房间
    只有房主可以解散房间，解散后所有参与者退款
    """
    # 权限检查：只有房主可以解散
    # 参与者检查：必须有其他参与者才能解散
    # 退款逻辑：所有参与者退款
    # 状态更新：房间状态变为已取消
```

#### 修改退出函数
```python
def quit_case_room(user, uid):
    """
    普通参与者退出对战房间
    注意：房主不能使用此接口退出，需要使用dismiss接口解散房间
    """
    # 权限检查：房主不能使用此接口
    # 退出逻辑：只退出当前用户
    # 状态维护：房间继续存在
```

#### 新增视图类
```python
class DismissCaseRoomView(APIView):
    """房主解散对战房间接口"""
    def post(self, request):
        # 调用dismiss_case_room函数
```

#### URL配置
```python
url(r'^battle/dismiss/', views.DismissCaseRoomView.as_view()),
```

### 2. 权限控制

#### 退出接口权限
- ✅ 普通参与者可以退出
- ❌ 房主不能退出（返回业务错误）

#### 解散接口权限  
- ✅ 房主可以解散
- ❌ 普通参与者不能解散（返回业务错误）
- ❌ 非参与者不能解散（返回参数错误）

### 3. 业务逻辑

#### 退出逻辑
1. 检查用户是否为房主
2. 如果是房主，返回错误提示使用解散功能
3. 如果是普通参与者，执行退出：
   - 退还房间费用
   - 删除投注记录
   - 发送房间更新WebSocket消息

#### 解散逻辑
1. 检查用户是否为房主
2. 检查房间是否有其他参与者
3. 如果满足条件，执行解散：
   - 所有参与者退款
   - 删除所有投注记录
   - 房间状态变为已取消
   - 发送房间取消WebSocket消息

### 4. 错误处理

#### 退出接口错误码
- `101` - 房主不能退出房间，请使用解散功能
- `102` - 无效房间或无效投注记录

#### 解散接口错误码
- `101` - 只有房主可以解散房间
- `101` - 房间没有其他参与者，无需解散
- `102` - 无效房间

## 测试验证

### 测试用例
1. ✅ 普通参与者成功退出
2. ✅ 房主使用quit接口被阻止
3. ✅ 房主成功解散房间
4. ✅ 非房主使用dismiss接口被阻止
5. ✅ 空房间解散处理
6. ✅ 无效房间处理

### 测试结果
- 总计：6项测试
- 通过：6项
- 失败：0项
- 成功率：100%

## API文档更新

### 退出接口
- **接口**: `POST /api/box/battle/quit/`
- **功能**: 普通参与者退出对战房间
- **限制**: 房主不能使用此接口

### 解散接口
- **接口**: `POST /api/box/battle/dismiss/`
- **功能**: 房主解散对战房间
- **限制**: 只有房主可以使用此接口

## 前端适配建议

### UI交互
1. **房主界面**：
   - 显示"解散房间"按钮而不是"退出房间"
   - 点击时弹出确认对话框，说明会影响所有参与者

2. **参与者界面**：
   - 显示"退出房间"按钮
   - 点击时弹出确认对话框，说明只退出自己

### 错误处理
1. 如果房主误用退出接口，显示提示："房主不能退出房间，请使用解散功能"
2. 如果参与者误用解散接口，显示提示："只有房主可以解散房间"

## 兼容性说明

### 向后兼容
- 原有的退出接口 `POST /api/box/battle/quit/` 继续存在
- 对于普通参与者，行为保持不变
- 对于房主，现在会返回错误而不是直接解散

### 迁移建议
1. 前端需要根据用户身份（房主/参与者）显示不同的按钮
2. 房主操作需要调用新的解散接口
3. 错误处理需要适配新的错误消息

## 总结

通过功能分离，我们实现了：
1. **职责明确**：退出和解散功能分离，逻辑更清晰
2. **权限控制**：严格的权限检查，防止误操作
3. **用户体验**：房主和参与者有不同的操作界面
4. **代码维护**：功能独立，便于维护和扩展

这个实现符合CSGO饰品开箱项目的开发规范，提供了更好的用户体验和更清晰的业务逻辑。 