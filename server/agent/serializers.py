from rest_framework import serializers

import pytz
from datetime import timed<PERSON>ta

from authentication.models import <PERSON>th<PERSON><PERSON>, User<PERSON><PERSON>, UserSteam
from sitecfg.models import SEO
from charge.models import ChargeRecord
from b2ctrade.models import ZBTradeRecord
from package.models import PackageItem, ItemInfo

from steambase.serializers import CustomFieldsSerializer
from steambase.enums import B2CTradeState
from agent.models import ArticleCategoryy, AgentBalanceRecord, Agent, AgentWithdrawalOrder


class AgentSerializer(serializers.ModelSerializer):
    class Meta:
        model = Agent
        fields = '__all__'

class AgentSiteSerializer(CustomFieldsSerializer):

   class Meta:
        model = SEO
        fields = '__all__'

class AgentWithdrawalOrderSerializer(CustomFieldsSerializer):
    create_time = serializers.SerializerMethodField()
    update_time = serializers.SerializerMethodField()
    status = serializers.SerializerMethodField()
    #agent = AgentSerializer()
    amount = serializers.SerializerMethodField()
    pay_type = serializers.SerializerMethodField()

    class Meta:
        model = AgentWithdrawalOrder
        fields = '__all__'
    
    def get_create_time(self, obj):
        beijing_tz = pytz.timezone('Asia/Shanghai')
        create_time = obj.create_time.astimezone(beijing_tz)
        return create_time.strftime('%Y-%m-%d %H:%M:%S')
    
    def get_update_time(self, obj):
        beijing_tz = pytz.timezone('Asia/Shanghai')
        update_time = obj.update_time.astimezone(beijing_tz)
        return update_time.strftime('%Y-%m-%d %H:%M:%S')
    
    def get_status(self, obj):
        # 0 提交申请，1 成功，2 失败
        if obj.status == 0:
            return '正在处理'
        elif obj.status == 1:
            return '成功'
        else:
            return '失败'
        
    def get_amount(self, obj):
        return obj.amount
    
    def get_pay_type(self, obj):
        # 0 支付宝，1 微信，2 银行卡
        if obj.pay_type == 0:
            return '支付宝'
        elif obj.pay_type == 1:
            return '微信'
        else:
            return '银行卡'
        

class UserAssetSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserAsset  
        fields = ('balance', 'total_charge_balance')

class UserSteamSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserSteam  
        fields = ('steamid', 'personaname', 'profileurl', 'avatar', 'avatarmedium', 'avatarfull')

class AuthUserSerializer(CustomFieldsSerializer):
   asset = UserAssetSerializer()
   steam = UserSteamSerializer()
   class Meta:
        model = AuthUser
        fields = ('id', 'username', 'email', 'phone', 'domain', 'is_active','reg_ip', 'login_ip', 'date_joined', 'asset','steam')

class ItemInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = ItemInfo
        fields = ('id', 'market_name_cn', 'icon_url_large')

class ArticleCategoryySerializer(serializers.ModelSerializer):
    class Meta:
        model = ArticleCategoryy
        fields = ['id', 'name', 'site']


class AgentSiteUserSerializer(CustomFieldsSerializer):
    email = serializers.SerializerMethodField()
    phone = serializers.SerializerMethodField()
    domain = serializers.SerializerMethodField()
    is_active = serializers.SerializerMethodField()
    reg_ip = serializers.SerializerMethodField()
    login_ip = serializers.SerializerMethodField()
    create_time = serializers.SerializerMethodField()
    balance = serializers.SerializerMethodField()
    total_charge_balance = serializers.SerializerMethodField()
    personaname = serializers.SerializerMethodField()
    

    asset = UserAssetSerializer()
    steam = UserSteamSerializer()
    
    class Meta:
        model = AuthUser
        fields = '__all__'

    def get_username(self, obj):
        return obj.username  

    def get_email(self, obj):
        return obj.email  
    
    def get_phone(self, obj):
        return obj.phone  

    def get_domain(self, obj):
        return obj.domain  
    
    def get_is_active(self, obj):
        #转换为中文
        return '激活' if obj.is_active else '锁定'
         #return obj.is_active
    
    def get_reg_ip(self, obj):
         return obj.reg_ip
    
    def get_login_ip(self, obj):
         return obj.login_ip
    
    def get_create_time(self, obj):

      bj_timezone = pytz.timezone('Asia/Shanghai')
      date_joined_bj = obj.date_joined.astimezone(bj_timezone)
      return date_joined_bj.strftime('%Y-%m-%d %H:%M:%S')
    
    def get_balance(self, obj):
        return obj.asset.balance if hasattr(obj, 'asset') else None
    
    def get_total_charge_balance(self, obj):
         return obj.asset.total_charge_balance if hasattr(obj, 'asset') else None
    
    def get_personaname(self, obj):
         return obj.steam.personaname if hasattr(obj, 'steam') else None
    
   
    
   
class AgentSiteUserChargeRecordSerializer(CustomFieldsSerializer):
    
   create_time = serializers.SerializerMethodField()
   amount = serializers.SerializerMethodField()
   out_trade_no = serializers.SerializerMethodField()
   pay_type = serializers.SerializerMethodField()
   state = serializers.SerializerMethodField()
   pay_time = serializers.SerializerMethodField()   
   personaname = serializers.SerializerMethodField()
   domain = serializers.SerializerMethodField()
   user = AuthUserSerializer() 
    
   class Meta:
        model = ChargeRecord
        fields = '__all__'
   
   def get_amount(self, obj):
        return obj.amount
   
   def get_out_trade_no(self, obj):
         return obj.out_trade_no
   
   def get_personaname(self, obj):
         return obj.user.steam.personaname if hasattr(obj, 'user') and hasattr(obj.user, 'steam') else None
   def get_create_time(self, obj):
      bj_timezone = pytz.timezone('Asia/Shanghai')
      create_time_bj = obj.create_time.astimezone(bj_timezone)
      return create_time_bj.strftime('%Y-%m-%d %H:%M:%S')
   # pay_time 可能为空
   def get_pay_time(self, obj):
      if obj.pay_time is None:
         return None
      bj_timezone = pytz.timezone('Asia/Shanghai')
      pay_time_bj = obj.pay_time.astimezone(bj_timezone)
      return pay_time_bj.strftime('%Y-%m-%d %H:%M:%S')
   def get_domain(self, obj):
       return obj.user.domain if hasattr(obj, 'user') else None
   

  
   def get_pay_type(self, obj):
        # 1 微信， 2 支付宝
         return '微信' if obj.pay_type == 1 else '支付宝'        
   
   def get_state(self, obj):
         # 2 成功支付，其它为失败
         return '成功' if obj.state == 2 else '失败'
   
class AgentSiteUserWithdrawSerializer(CustomFieldsSerializer):
   create_time = serializers.SerializerMethodField()
   amount = serializers.SerializerMethodField()
   out_trade_no = serializers.SerializerMethodField()
   state = serializers.SerializerMethodField()
   skin_name = serializers.SerializerMethodField()
   personaname = serializers.SerializerMethodField()
   domain = serializers.SerializerMethodField()
 
   user = AuthUserSerializer()
   item_info = ItemInfoSerializer()


   class Meta:
        model = ZBTradeRecord
        fields = '__all__'
    
   
   def get_amount(self, obj):
        return obj.amount
   
   def get_out_trade_no(self, obj):
         return obj.out_trade_no

   def get_create_time(self, obj):
      bj_timezone = pytz.timezone('Asia/Shanghai')
      create_time_bj = obj.create_time.astimezone(bj_timezone)
      return create_time_bj.strftime('%Y-%m-%d %H:%M:%S')
   
   def get_state(self, obj):
   
      if obj.state == B2CTradeState.Accepted:
         return '成功'
      elif obj.state == B2CTradeState.Cancelled:
         return '取消'
      elif obj.state == B2CTradeState.Trading:
         return '取回中'
        

   
   def get_skin_name(self, obj):
         return obj.item_info.market_name_cn if hasattr(obj, 'item_info') else None
   
   def get_personaname(self, obj):
         return obj.user.steam.personaname if hasattr(obj, 'user') and hasattr(obj.user, 'steam') else None
   def get_domain(self, obj):
         return obj.user.domain if hasattr(obj, 'user') else None
   
    
   
class AgentSiteUserSkinRecordSerializer(CustomFieldsSerializer):
   create_time = serializers.SerializerMethodField()
   source = serializers.SerializerMethodField()
   skin_name = serializers.SerializerMethodField()
   state = serializers.SerializerMethodField()
   amount = serializers.SerializerMethodField()
   personaname = serializers.SerializerMethodField()
   domain = serializers.SerializerMethodField()
   user = AuthUserSerializer()
   item_info = ItemInfoSerializer()
   class Meta:
        model = PackageItem
        fields = '__all__'
    
   def get_source(self, obj):
     return obj.source
   
   def get_create_time(self, obj):
      bj_timezone = pytz.timezone('Asia/Shanghai')
      create_time_bj = obj.create_time.astimezone(bj_timezone)
      return create_time_bj.strftime('%Y-%m-%d %H:%M:%S')
   
   def get_skin_name(self, obj):
     return obj.item_info.market_name_cn if hasattr(obj, 'item_info') else None
   
   def get_state(self, obj):
     # 1 为可用，其它为不可用

     return '可用' if obj.state == 1 else '不可用'
   
   def get_amount(self, obj):
     return obj.amount
   
   def get_personaname(self, obj):
     return obj.user.steam.personaname if hasattr(obj, 'user') and hasattr(obj.user, 'steam') else None
   def get_domain(self, obj):
     return obj.user.domain if hasattr(obj, 'user') else None   
   
  
class AgentBalanceRecordSerializer(CustomFieldsSerializer):
    create_time = serializers.SerializerMethodField()
    update_time = serializers.SerializerMethodField()
    type = serializers.SerializerMethodField()

    class Meta:
        model = AgentBalanceRecord
        fields = '__all__'

    def get_create_time(self, obj):
        beijing_tz = pytz.timezone('Asia/Shanghai')
        create_time = obj.create_time.astimezone(beijing_tz)
        return create_time.strftime('%Y-%m-%d %H:%M:%S')
    
    def get_update_time(self, obj):
        beijing_tz = pytz.timezone('Asia/Shanghai')
        update_time = obj.update_time.astimezone(beijing_tz)
        return update_time.strftime('%Y-%m-%d %H:%M:%S')
    
    # type 1充值，2提取，3结算
    def get_type(self, obj):
        if obj.type == 0:
            return '用户充值'
        elif obj.type == 1:
            return '用户提货'
        elif obj.type == 2:
            return '代理商结算'
        elif obj.type == 3:
            return '其它'
        

