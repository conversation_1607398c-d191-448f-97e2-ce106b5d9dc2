#!/usr/bin/env python3
"""
修复所有重复的on_delete参数
"""
import os
import re

def fix_all_duplicates(file_path):
    """修复单个文件中所有重复的on_delete参数"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 修复重复的on_delete参数 - 更强的正则表达式
        # 匹配从on_delete开始到下一个on_delete之间的所有内容
        pattern = r'(on_delete=models\.[A-Z_]+)([^,\)]*,\s*[^,\)]*)*,\s*(on_delete=models\.[A-Z_]+)'
        
        # 多次执行替换，直到没有重复为止
        max_iterations = 10
        for i in range(max_iterations):
            new_content = re.sub(pattern, r'\1', content)
            if new_content == content:
                break
            content = new_content
        
        # 额外的清理：移除明显的重复模式
        # 处理跨行的重复
        lines = content.split('\n')
        cleaned_lines = []
        
        for line in lines:
            # 如果这行只包含on_delete参数，并且前面已经有了on_delete，则跳过
            if re.match(r'^\s*on_delete=models\.[A-Z_]+,?\s*$', line):
                # 检查前面几行是否已经有on_delete
                found_previous = False
                for prev_line in cleaned_lines[-5:]:  # 检查前5行
                    if 'on_delete=' in prev_line:
                        found_previous = True
                        break
                if found_previous:
                    continue  # 跳过这行
            
            cleaned_lines.append(line)
        
        final_content = '\n'.join(cleaned_lines)
        
        if final_content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(final_content)
            print(f"✅ 修复了 {file_path}")
            return True
        else:
            print(f"⏭️  跳过 {file_path} (无需修复)")
            return False
            
    except Exception as e:
        print(f"❌ 修复 {file_path} 时出错: {e}")
        return False

def main():
    """主函数"""
    print("🔧 开始修复所有重复的on_delete参数...")
    
    # 查找所有Python文件
    model_files = []
    for root, dirs, files in os.walk('.'):
        # 跳过虚拟环境
        if 'venv' in root:
            continue
            
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if 'on_delete=' in content:
                            model_files.append(file_path)
                except:
                    continue
    
    print(f"📁 找到 {len(model_files)} 个包含on_delete的文件")
    
    fixed_count = 0
    for file_path in model_files:
        if fix_all_duplicates(file_path):
            fixed_count += 1
    
    print(f"\n🎉 修复完成！共修复了 {fixed_count} 个文件")

if __name__ == '__main__':
    main()
