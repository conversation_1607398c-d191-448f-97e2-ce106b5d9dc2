from uuid import uuid1
from django.conf import settings
from django.shortcuts import render, redirect
from django.utils.translation import gettext_lazy as _

from libs.steamapi import SteamApi
from social_core.exceptions import AuthAlreadyAssociated

from authentication.models import AuthUser, UserSteam
from authentication.business import calc_user_box_free_count


def get_username(strategy, uid, user=None, *args, **kwargs):
    """Removes unnecessary slugification and cleaning of the username since the uid is unique and well formed"""
    if not user:
        backend = kwargs.get('backend', None)
        if backend and backend.name == 'steam':
            user_steam = UserSteam.objects.filter(steamid=uid).first()
            if user_steam:
                username = strategy.storage.user.get_username(user_steam.user)
            else:
                username = uid
        else:
            username = uid
    else:
        username = strategy.storage.user.get_username(user)
    return {'username': username}


def user_details(user, details, strategy, *args, **kwargs):
    """Update user details using data from provider."""
    backend = kwargs.get('backend', None)
    if user and backend and backend.name == 'steam':
        changed = False  # flag to track changes
        protected = ('steamid', 'id', 'pk') + tuple(strategy.setting('PROTECTED_USER_FIELDS', []))

        # Update user model attributes with the new data sent by the current
        # provider. Update on some attributes is disabled by default, for
        # example username and id fields. It's also possible to disable update
        # on fields defined in SOCIAL_AUTH_PROTECTED_FIELDS.
        user_steam = user.steam
        if details['player']:
            for name, value in details['player'].items():
                if value is not None and hasattr(user_steam, name):
                    current_value = getattr(user_steam, name, None)
                    if not current_value or name not in protected:
                        changed |= current_value != value
                        setattr(user_steam, name, value)

        if changed:
            user_steam.save()


def associate_existing_user(uid, *args, **kwargs):
    """If there already is an user with the given steamid, hand it over to the pipeline"""
    backend = kwargs.get('backend', None)
    if backend and backend.name == 'steam':
        if (backend.data.get('openid.claimed_id', '')[:36] == 'https://steamcommunity.com/openid/id' and
            backend.data.get('openid.op_endpoint', '') == 'https://steamcommunity.com/openid/login' and
            backend.data.get('openid.identity', '')[:36] == 'https://steamcommunity.com/openid/id'):
            if UserSteam.objects.filter(steamid=uid).exists():
                user_steam = UserSteam.objects.get(steamid=uid)
                steamapi = SteamApi(api_key=settings.API_KEY, steamid=uid)
                level = steamapi.level()
                own_games = steamapi.own_games()
                dota2_playtime = steamapi.playtime('570')
                user_steam.level = level
                user_steam.own_games_count = len(own_games)
                user_steam.dota2_playtime = dota2_playtime
                user_steam.save(update_fields=['level', 'own_games_count', 'dota2_playtime'])
                user_steam.user.username = user_steam.steamid
                user_steam.user.save(update_fields=['username'])
                calc_user_box_free_count(user_steam.user)
                return {
                    'user': user_steam.user
                }


def social_user(backend, uid, user=None, *args, **kwargs):
    provider = backend.name
    social = backend.strategy.storage.user.get_social_auth(provider, uid)
    if (backend.data.get('openid.claimed_id', '')[:36] != 'https://steamcommunity.com/openid/id' or
        backend.data.get('openid.op_endpoint', '') != 'https://steamcommunity.com/openid/login' or
        backend.data.get('openid.identity', '')[:36] != 'https://steamcommunity.com/openid/id'):
        msg = _('This Steam account is already in bind.')
        return redirect('/?msg={}'.format(msg))
    if user and user.steam.steamid and user.steam.steamid != uid:
        msg = _('This Steam account is already in bind.')
        return redirect('/?msg={}'.format(msg))
        # raise AuthAlreadyAssociated(backend, msg)
    if social:
        if user and social.user != user:
            msg = _('This Steam account is already in bind.')
            return redirect('/?msg={}'.format(msg))
            # raise AuthAlreadyAssociated(backend, msg)
        elif not user:
            user = social.user
    return {'social': social,
            'user': user,
            'is_new': user is None,
            'new_association': social is None}