import logging
import re
from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.core.paginator import <PERSON><PERSON>ator, EmptyPage, PageNotAnInteger
from django.db import transaction
from django.utils.translation import gettext_lazy as _

from box.business import check_user_promotion_level
from libs.steamapi import SteamApi

from steambase.enums import RespCode
from sitecfg.interfaces import get_promotion_new_user_reward
from promotion.models import UserPromotion, PromotionRecord, PromotionLevelConfig, PromotionCaseConfig
from promotion.serializers import PromotionRecordSerializer, PromotionLevelConfigSerializer, PromotionCaseConfigSerializers

_logger = logging.getLogger(__name__)

USER = get_user_model()


def create_promotion(user, ref_code):
    with transaction.atomic():
        refer = UserPromotion.objects.select_for_update().filter(code=ref_code).first()
        if not refer or refer.user == user:
            return RespCode.InvalidParams.value, _('Invalid promotion code')

        last_ref = PromotionRecord.objects.filter(user=user).first()
        if last_ref:
            return RespCode.InvalidParams.value, _('You have been bound another account')
        PromotionRecord.objects.create(ref=refer, user=user)

        refer.count += 1
        refer.save()
        # reward = get_promotion_new_user_reward()
        # if reward:
            # user.update_balance(reward, 'New user reward')

        # steamapi = SteamApi(api_key=settings.API_KEY, steamid=user.steam.steamid)
        # check_playtime = steamapi.playtime('570') >= 1000 * 60
        # if check_playtime:
        #     user.update_box_free_count(1)
        return RespCode.Succeed.value, {}


def set_promotion_code(user, code):
    with transaction.atomic():
        code = code.upper()
        code_length = len(code)
        if code_length < 4 or code_length > 8:
            return RespCode.InvalidParams.value, _('Code Length Must 4 to 8 digits')
        if not re.match(r'^[\w\d]{4,8}$', code):
            return RespCode.InvalidParams.value, _('Invalid params')
        refer = UserPromotion.objects.select_for_update().filter(user=user).first()
        if not refer:
            return RespCode.InvalidParams.value, _('Invalid params')
        if UserPromotion.objects.filter(code=code).exists():
            return RespCode.InvalidParams.value, _('Code is already exists')
        refer.code = code
        refer.save(update_fields=['code'])
        return RespCode.Succeed.value, {}


def user_pick_profit(user):
    user_pro = UserPromotion.objects.filter(user=user).first()
    record_count = PromotionRecord.objects.filter(ref=user_pro, total_charge__gt=0).count()
    if record_count < 5:
        return RespCode.BusinessError.value, _('邀请人数不足')

    with transaction.atomic():
        refer = UserPromotion.objects.select_for_update().filter(user=user).first()
        active_profit = refer.profit - refer.profit % settings.PROMOTION_PROFIT_PICK_UNIT
        if settings.PROMOTION_PROFIT_PICK_UNIT <= 0.01:
            active_profit = refer.profit
        if active_profit == 0:
            return RespCode.BusinessError.value, _('您的收益不足5B')
        refer.profit -= active_profit
        refer.user.update_balance(active_profit, '推广收益')

        refer.save()
        return RespCode.Succeed.value, {}


def get_promotion_record(user, query, fields, page, page_size):
    refer = UserPromotion.objects.filter(user=user).first()
    queryset = PromotionRecord.objects.filter(ref=refer, active=True, **query).order_by('-create_time')
    paginator = Paginator(queryset, page_size)
    records = paginator.page(page)
    records_data = PromotionRecordSerializer(records, many=True, fields=fields).data
    resp = {
        'records': records_data,
        'total': paginator.count
    }
    return RespCode.Succeed.value, resp


def get_promotion_config(query, fields):
    config_data = cache.get('promocfg')
    if not config_data:
        config_data = []
        config = PromotionLevelConfig.objects.filter(**query).order_by('level')
        if config:
            config_data = PromotionLevelConfigSerializer(config, many=True, fields=fields).data
        cache.set('promocfg', config_data, settings.DAY_REDIS_TIMEOUT)

    resp = {
        'config': config_data
    }
    return RespCode.Succeed.value, resp


def get_promotion_box(user):
    promotion_case = PromotionCaseConfig.objects.all().order_by('level__level')
    case_list = []
    if promotion_case:
        case_list = PromotionCaseConfigSerializers(promotion_case, many=True, fields=('level', "case")).data
    if user:
        for case in case_list:
            case["status"] = not check_user_promotion_level(user, case['case']['key'])
    return RespCode.Succeed.value, case_list