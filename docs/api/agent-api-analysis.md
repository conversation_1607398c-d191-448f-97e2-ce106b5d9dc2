# Agent 代理商模块分析文档

## 模块概述

Agent模块是平台的代理商管理系统，实现了多级代理商运营体系。代理商可以创建和管理自己的子站点，获取用户数据统计，管理站点内容，并根据下级用户的充值和消费获得分成收益。该模块是平台扩展业务、增加收入来源的重要商业工具。

## 目录结构

```
agent/
├── __init__.py
├── admin.py                 # Django管理后台配置
├── apps.py                  # 应用配置
├── business.py              # 核心业务逻辑
├── models.py                # 数据模型定义
├── serializers.py           # API序列化器
├── views.py                 # API视图
├── urls.py                  # URL路由配置
├── tests.py                 # 单元测试
└── migrations/              # 数据库迁移文件
```

## 数据模型分析

### Agent - 代理商信息
```python
class Agent(models.Model):
    user = models.OneToOneField(USER_MODEL)                 # 关联用户
    agent_name = models.CharField(max_length=255)           # 代理商名称
    agent_phone = models.CharField(max_length=255)          # 联系电话
    agent_email = models.EmailField(max_length=255)         # 联系邮箱
    agent_wechat = models.CharField(max_length=255)         # 微信号
    agent_qq = models.CharField(max_length=255)             # QQ号
    agent_alipay = models.CharField(max_length=255)         # 支付宝账号
    agent_wechatpay = models.CharField(max_length=255)      # 微信支付账号
    agent_bankcard = models.CharField(max_length=255)       # 银行卡号
    agent_bank = models.CharField(max_length=255)           # 开户银行
    agent_bankname = models.CharField(max_length=255)       # 开户姓名
    balance = models.DecimalField(max_digits=10, decimal_places=2)  # 账户余额
    enable = models.BooleanField(default=True)              # 启用状态
    remark = models.TextField()                             # 备注信息
```

**特点：**
- 详细的代理商联系信息和收款方式
- 独立的余额管理系统
- 支持多种提现方式（支付宝、微信、银行卡）
- 可以启用/禁用代理商

### AgentWithdrawalOrder - 代理商提现订单
```python
class AgentWithdrawalOrder(models.Model):
    agent = models.ForeignKey(Agent)                        # 关联代理商
    amount = models.DecimalField(max_digits=10, decimal_places=2)  # 提现金额
    status = models.IntegerField(choices=(                  # 状态
        (0, '待处理'),
        (1, '成功'),
        (2, '失败'),
    ))
    pay_type = models.IntegerField(choices=(                # 支付方式
        (0, '支付宝'),
        (1, '微信支付'),
        (2, '银行卡'),
    ))
    remark = models.TextField()                             # 备注
```

**特点：**
- 完整的提现流程管理
- 支持多种提现方式
- 状态跟踪和备注记录

### AgentBalanceRecord - 代理商余额变动记录
```python
class AgentBalanceRecord(models.Model):
    agent = models.ForeignKey(Agent)                        # 关联代理商
    balance_changed = models.DecimalField()                 # 变动金额
    balance_before = models.DecimalField()                  # 变动前余额
    balance_after = models.DecimalField()                   # 变动后余额
    type = models.IntegerField(choices=(                    # 变动类型
        (0, '用户充值'),
        (1, '用户提现'),
        (2, '代理商提现订单'),
        (3, '其他'),
    ))
    out_trade_no = models.CharField(max_length=64)          # 外部交易号
    remark = models.TextField()                             # 备注
```

**特点：**
- 详细的余额变动日志
- 记录变动前后余额状态
- 支持多种变动类型分类
- 关联外部交易号便于对账

### Article - 文章管理
```python
class Article(models.Model):
    title = models.CharField(max_length=255)                # 文章标题
    content = models.TextField()                            # 文章内容
    enable = models.BooleanField(default=True)              # 启用状态
    category = models.ForeignKey('ArticleCategory')         # 文章分类
    user = models.ForeignKey(USER_MODEL)                    # 创建用户
```

### ArticleCategory - 文章分类
```python
class ArticleCategory(models.Model):
    name = models.CharField(max_length=255)                 # 分类名称
    enable = models.BooleanField(default=True)              # 启用状态
    site = models.ForeignKey(SEO)                           # 关联站点
    user = models.ForeignKey(USER_MODEL)                    # 创建用户
```

**特点：**
- 支持代理商自定义站点内容
- 分类管理便于内容组织
- 与SEO站点关联，支持多站点

## API 接口分析

### 1. 检查代理商身份
**路径：** `GET /api/agent/checkagent/`

**功能：** 检查当前用户是否为代理商

**响应格式：**
```json
{
    "success": true,
    "data": true,  // 是否为代理商
    "message": "Succeed"
}
```

### 2. 获取代理商站点列表
**路径：** `GET /api/agent/site/`

**功能：** 获取代理商管理的站点列表

**响应格式：**
```json
{
    "items": [
        {
            "id": 1,
            "name": "我的站点",
            "url": "https://demo.site.com",
            "title": "站点标题",
            "subtitle": "站点副标题",
            "keywords": "关键词",
            "description": "站点描述",
            "icp": "备案号",
            "enable": true
        }
    ]
}
```

### 3. 获取站点用户数据
**路径：** `GET /api/agent/site/user/`

**功能：** 获取代理商站点下的用户列表和统计

**参数：**
- `page`: 页码
- `pageSize`: 每页数量

**响应格式：**
```json
{
    "items": [
        {
            "create_time": "2025-06-19T10:00:00Z",
            "email": "<EMAIL>",
            "phone": "***********",
            "domain": "demo.site.com",
            "is_active": true,
            "reg_ip": "***********",
            "login_ip": "***********",
            "balance": 100.0,
            "total_charge_balance": 500.0,
            "personaname": "用户昵称"
        }
    ],
    "total": 100
}
```

### 4. 获取用户充值记录
**路径：** `GET /api/agent/site/user/charge/`

**功能：** 获取站点用户的充值记录

**参数：**
- `page`: 页码
- `pageSize`: 每页数量

**响应格式：**
```json
{
    "items": [
        {
            "create_time": "2025-06-19T10:00:00Z",
            "personaname": "用户昵称",
            "amount": 100.0,
            "out_trade_no": "202506191000001",
            "pay_type": 1,
            "state": 1,
            "pay_time": "2025-06-19T10:05:00Z",
            "domain": "demo.site.com"
        }
    ],
    "total": 50
}
```

### 5. 获取用户提现记录
**路径：** `GET /api/agent/site/user/withdraw/`

**功能：** 获取站点用户的提现记录

### 6. 获取用户饰品记录
**路径：** `GET /api/agent/site/user/skin/`

**功能：** 获取站点用户的饰品获取记录

### 7. 文章分类管理
**路径：** `POST /api/agent/manage/articlecategory/`

**功能：** 创建或更新文章分类

**参数：**
```json
{
    "site": 1,
    "category": "公告",
    "id": null  // 更新时传入分类ID
}
```

### 8. 文章管理
**路径：** `POST /api/agent/manage/article/`

**功能：** 创建或更新文章

**参数：**
```json
{
    "title": "文章标题",
    "content": "文章内容",
    "category": 1,
    "id": null  // 更新时传入文章ID
}
```

### 9. 获取代理商信息
**路径：** `GET /api/agent/info/`

**功能：** 获取当前代理商的详细信息

### 10. 编辑代理商信息
**路径：** `POST /api/agent/info/edit/`

**功能：** 更新代理商信息

**参数：**
```json
{
    "agent_name": "代理商名称",
    "agent_phone": "***********",
    "agent_email": "<EMAIL>",
    "agent_wechat": "wechat_id",
    "agent_qq": "*********",
    "agent_alipay": "<EMAIL>",
    "agent_bankcard": "****************",
    "agent_bank": "中国银行",
    "agent_bankname": "张三"
}
```

### 11. 代理商提现
**路径：** `POST /api/agent/withdrawal/`

**功能：** 申请代理商收益提现

**参数：**
```json
{
    "amount": 100.0,
    "pay_type": 0  // 0-支付宝, 1-微信, 2-银行卡
}
```

### 12. 获取提现记录
**路径：** `GET /api/agent/withdrawal/records/`

**功能：** 获取代理商提现记录

### 13. 获取余额变动记录
**路径：** `GET /api/agent/balance/records/`

**功能：** 获取代理商余额变动明细

## 核心业务逻辑分析

### 1. 多站点管理机制
```python
def get_agent_site_user(agent, fields, page, page_size):
    """获取代理商站点用户"""
    # 获取代理商管理的所有站点
    sites = SEO.objects.filter(agent=agent).order_by('-id')
    
    items = []
    for site in sites:
        # 获取每个站点的用户
        auth_users = AuthUser.objects.filter(domain=site.url)
        for auth_user in auth_users:
            serialized_data = AgentSiteUserSerializer(auth_user, fields=fields).data
            items.append((auth_user.date_joined, serialized_data))
    
    # 按时间排序和分页
    items.sort(key=lambda x: x[0], reverse=True)
    sorted_items = [item[1] for item in items]
    
    paginator = Paginator(sorted_items, page_size)
    paginated_items = paginator.page(page)
    
    return paginated_items
```

### 2. 分成收益计算（需补充）
```python
# 当前代码中缺少分成计算逻辑，需要实现
class AgentCommissionCalculator:
    def __init__(self):
        self.commission_rates = {
            'charge': 0.05,      # 充值分成5%
            'consumption': 0.02,  # 消费分成2%
            'withdrawal': 0.01   # 提现分成1%
        }
    
    def calculate_commission(self, agent, user_action, amount):
        """计算代理商分成"""
        # 验证用户属于代理商站点
        if not self.is_agent_user(agent, user_action.user):
            return 0
        
        # 获取分成比例
        rate = self.commission_rates.get(user_action.type, 0)
        
        # 计算分成金额
        commission = amount * rate
        
        # 更新代理商余额
        agent.update_balance(
            commission, 
            0,  # 用户充值类型
            user_action.out_trade_no,
            f"用户{user_action.user.username}充值分成"
        )
        
        return commission
    
    def is_agent_user(self, agent, user):
        """检查用户是否属于代理商"""
        agent_sites = SEO.objects.filter(agent=agent.user)
        return AuthUser.objects.filter(
            id=user.id,
            domain__in=[site.url for site in agent_sites]
        ).exists()
```

### 3. 余额管理系统
```python
def update_balance(self, amount, change_type, out_trade_no, remark=''):
    """更新代理商余额"""
    with transaction.atomic():
        amount = Decimal(amount)
        balance_before = self.balance
        self.balance += amount
        self.save()

        # 记录余额变动
        AgentBalanceRecord.objects.create(
            agent=self,
            balance_changed=amount,
            balance_before=balance_before,
            balance_after=self.balance,
            type=change_type,
            out_trade_no=out_trade_no,
            remark=remark                
        )
```

### 4. 提现处理系统
```python
def agent_withdrawal(user, amount, pay_type):
    """代理商提现处理"""
    try:
        agent = Agent.objects.get(user=user)
        amount = Decimal(amount).quantize(Decimal('0.00'), rounding=ROUND_HALF_UP)
        
        # 余额检查
        if agent.balance < amount or amount <= 0:
            return RespCode.NoBalance.value, 'Insufficient balance'
        
        with transaction.atomic():
            # 扣除余额
            agent.update_balance(-amount, 2, f"代理商提现 {amount}")
            
            # 创建提现订单
            AgentWithdrawalOrder.objects.create(
                agent=agent, 
                amount=amount, 
                pay_type=pay_type
            )
            
            return RespCode.Succeed.value, 'Withdrawal success'
            
    except Agent.DoesNotExist:
        return RespCode.NotFound.value, 'Agent not found'
```

### 5. 数据统计与缓存
```python
def get_agent_site_user_charge_records(agent, fields, page, page_size):
    """获取充值记录（带缓存）"""
    # 缓存策略
    cache_key = f'agent_site_user_charge_records_{agent.id}_{page}_{page_size}'
    resp = cache.get(cache_key)
    if resp:
        return RespCode.Succeed.value, resp
    
    # 获取所有站点用户的充值记录
    sites = SEO.objects.filter(agent=agent).order_by('-id')
    charge_records = []
    
    for site in sites:
        auth_users = AuthUser.objects.filter(domain=site.url)
        for auth_user in auth_users:
            records = ChargeRecord.objects.filter(user=auth_user)
            for record in records:
                serialized_data = AgentSiteUserChargeRecordSerializer(
                    record, fields=fields
                ).data
                charge_records.append((record.create_time, serialized_data))
    
    # 排序和分页
    charge_records.sort(key=lambda x: x[0], reverse=True)
    sorted_records = [record[1] for record in charge_records]
    
    paginator = Paginator(sorted_records, page_size)
    paginated_records = paginator.page(page)
    
    resp = {
        'items': list(paginated_records),
        'total': paginator.count
    }
    
    # 缓存30分钟
    cache.set(cache_key, resp, timeout=60*30)
    
    return RespCode.Succeed.value, resp
```

## 存在的问题与风险

### 1. 分成机制不完整

**问题描述：**
- 缺少自动分成计算逻辑
- 没有分成比例配置系统
- 缺少分成触发机制
- 没有分成统计报表

**影响：**
- 代理商无法获得应有收益
- 手动处理效率低下
- 容易出现计算错误
- 代理商满意度低

### 2. 权限控制缺失

**问题描述：**
- 缺少代理商等级管理
- 没有功能权限控制
- 站点管理权限模糊
- 数据访问权限不清晰

**影响：**
- 可能存在越权操作
- 数据安全风险
- 管理混乱

### 3. 数据统计性能问题

**问题描述：**
- 大量嵌套查询
- 缓存策略简单
- 实时统计压力大
- 缺少数据预处理

**影响：**
- 页面响应慢
- 数据库压力大
- 用户体验差

### 4. 财务管理风险

**问题描述：**
- 余额变动缺少审核
- 提现处理不够严格
- 没有风控机制
- 缺少财务对账

**影响：**
- 财务风险
- 可能被恶意利用
- 对账困难

## 改进建议

### 1. 完善分成系统

#### 分成配置管理
```python
class AgentCommissionConfig(models.Model):
    """代理商分成配置"""
    agent_level = models.IntegerField(default=1)            # 代理商等级
    commission_type = models.CharField(max_length=50)       # 分成类型
    commission_rate = models.FloatField(default=0)          # 分成比例
    min_amount = models.FloatField(default=0)               # 最小金额
    max_amount = models.FloatField(default=0)               # 最大金额
    is_active = models.BooleanField(default=True)           # 是否启用
    
    class Meta:
        verbose_name = '代理商分成配置'

class AgentCommissionRecord(ModelBase):
    """代理商分成记录"""
    agent = models.ForeignKey(Agent, on_delete=models.CASCADE)
    user = models.ForeignKey(USER_MODEL, on_delete=models.CASCADE)  # 产生分成的用户
    commission_type = models.CharField(max_length=50)       # 分成类型
    source_amount = models.FloatField()                     # 源金额
    commission_rate = models.FloatField()                   # 分成比例
    commission_amount = models.FloatField()                 # 分成金额
    source_order_no = models.CharField(max_length=64)       # 源订单号
    status = models.CharField(max_length=20, default='completed')  # 状态
    
    class Meta:
        verbose_name = '代理商分成记录'
```

#### 自动分成处理
```python
class AgentCommissionProcessor:
    def __init__(self):
        self.signal_handlers = {
            'charge_completed': self.handle_charge_commission,
            'withdrawal_completed': self.handle_withdrawal_commission,
            'game_completed': self.handle_game_commission,
        }
    
    def process_commission(self, signal_type, **kwargs):
        """处理分成"""
        handler = self.signal_handlers.get(signal_type)
        if handler:
            return handler(**kwargs)
    
    def handle_charge_commission(self, charge_record):
        """处理充值分成"""
        # 查找用户对应的代理商
        agent = self.find_user_agent(charge_record.user)
        if not agent:
            return
        
        # 获取分成配置
        config = AgentCommissionConfig.objects.filter(
            agent_level=agent.level,
            commission_type='charge',
            is_active=True,
            min_amount__lte=charge_record.amount,
            max_amount__gte=charge_record.amount
        ).first()
        
        if not config:
            return
        
        # 计算分成
        commission_amount = charge_record.amount * config.commission_rate
        
        # 更新代理商余额
        with transaction.atomic():
            agent.update_balance(
                commission_amount,
                0,  # 用户充值分成
                charge_record.out_trade_no,
                f"用户{charge_record.user.username}充值分成"
            )
            
            # 记录分成
            AgentCommissionRecord.objects.create(
                agent=agent,
                user=charge_record.user,
                commission_type='charge',
                source_amount=charge_record.amount,
                commission_rate=config.commission_rate,
                commission_amount=commission_amount,
                source_order_no=charge_record.out_trade_no
            )
    
    def find_user_agent(self, user):
        """查找用户对应的代理商"""
        # 根据用户域名查找代理商
        if hasattr(user, 'domain'):
            site = SEO.objects.filter(url=user.domain).first()
            if site and site.agent:
                return Agent.objects.filter(user=site.agent).first()
        return None
```

### 2. 权限管理系统

#### 代理商等级管理
```python
class AgentLevel(models.Model):
    """代理商等级"""
    level = models.IntegerField(unique=True)                # 等级
    name = models.CharField(max_length=50)                  # 等级名称
    min_sites = models.IntegerField(default=0)              # 最小站点数
    max_sites = models.IntegerField(default=0)              # 最大站点数
    permissions = models.JSONField(default=list)           # 权限列表
    commission_bonus = models.FloatField(default=0)        # 分成加成
    
    class Meta:
        verbose_name = '代理商等级'

class AgentPermission(models.Model):
    """代理商权限"""
    PERMISSION_CHOICES = [
        ('site_manage', '站点管理'),
        ('user_view', '用户查看'),
        ('finance_view', '财务查看'),
        ('content_manage', '内容管理'),
        ('data_export', '数据导出'),
        ('withdrawal_apply', '提现申请'),
    ]
    
    agent = models.ForeignKey(Agent, on_delete=models.CASCADE)
    permission_code = models.CharField(max_length=50, choices=PERMISSION_CHOICES)
    is_granted = models.BooleanField(default=True)
    granted_by = models.ForeignKey(USER_MODEL, on_delete=models.SET_NULL, null=True)
    granted_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ['agent', 'permission_code']
        verbose_name = '代理商权限'
```

#### 权限装饰器
```python
def require_agent_permission(permission_code):
    """代理商权限装饰器"""
    def decorator(view_func):
        def wrapper(request, *args, **kwargs):
            user = request.user
            
            # 检查是否为代理商
            if not hasattr(user, 'is_agent') or not user.is_agent:
                return reformat_resp(RespCode.Forbidden.value, {}, '无代理商权限')
            
            # 检查具体权限
            agent = Agent.objects.filter(user=user).first()
            if not agent:
                return reformat_resp(RespCode.Forbidden.value, {}, '代理商不存在')
            
            has_permission = AgentPermission.objects.filter(
                agent=agent,
                permission_code=permission_code,
                is_granted=True
            ).exists()
            
            if not has_permission:
                return reformat_resp(RespCode.Forbidden.value, {}, '权限不足')
            
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator
```

### 3. 数据统计优化

#### 预计算统计数据
```python
class AgentStatistics(models.Model):
    """代理商统计数据（预计算）"""
    agent = models.ForeignKey(Agent, on_delete=models.CASCADE)
    stat_date = models.DateField()                          # 统计日期
    
    # 用户统计
    total_users = models.IntegerField(default=0)            # 总用户数
    active_users = models.IntegerField(default=0)           # 活跃用户数
    new_users = models.IntegerField(default=0)              # 新增用户数
    
    # 财务统计
    total_charge = models.FloatField(default=0)             # 总充值金额
    total_withdrawal = models.FloatField(default=0)         # 总提现金额
    total_commission = models.FloatField(default=0)         # 总分成金额
    
    # 业务统计
    total_games = models.IntegerField(default=0)            # 总游戏次数
    total_orders = models.IntegerField(default=0)           # 总订单数
    
    class Meta:
        unique_together = ['agent', 'stat_date']
        verbose_name = '代理商统计'

class AgentStatisticsCalculator:
    """代理商统计计算器"""
    
    def calculate_daily_statistics(self, agent, target_date):
        """计算日统计数据"""
        # 获取代理商站点
        sites = SEO.objects.filter(agent=agent.user)
        site_urls = [site.url for site in sites]
        
        # 用户统计
        users_qs = AuthUser.objects.filter(domain__in=site_urls)
        total_users = users_qs.count()
        new_users = users_qs.filter(date_joined__date=target_date).count()
        active_users = users_qs.filter(last_login__date=target_date).count()
        
        # 财务统计
        charges = ChargeRecord.objects.filter(
            user__domain__in=site_urls,
            create_time__date=target_date,
            state=1  # 成功状态
        )
        total_charge = charges.aggregate(total=Sum('amount'))['total'] or 0
        
        withdrawals = ZBTradeRecord.objects.filter(
            user__domain__in=site_urls,
            create_time__date=target_date
        )
        total_withdrawal = withdrawals.aggregate(total=Sum('amount'))['total'] or 0
        
        # 分成统计
        commissions = AgentCommissionRecord.objects.filter(
            agent=agent,
            create_time__date=target_date
        )
        total_commission = commissions.aggregate(total=Sum('commission_amount'))['total'] or 0
        
        # 保存或更新统计数据
        stats, created = AgentStatistics.objects.update_or_create(
            agent=agent,
            stat_date=target_date,
            defaults={
                'total_users': total_users,
                'active_users': active_users,
                'new_users': new_users,
                'total_charge': total_charge,
                'total_withdrawal': total_withdrawal,
                'total_commission': total_commission,
            }
        )
        
        return stats
    
    def get_statistics_summary(self, agent, start_date, end_date):
        """获取统计汇总"""
        stats = AgentStatistics.objects.filter(
            agent=agent,
            stat_date__range=[start_date, end_date]
        )
        
        return {
            'total_charge': stats.aggregate(total=Sum('total_charge'))['total'] or 0,
            'total_commission': stats.aggregate(total=Sum('total_commission'))['total'] or 0,
            'avg_daily_users': stats.aggregate(avg=Avg('active_users'))['avg'] or 0,
            'total_new_users': stats.aggregate(total=Sum('new_users'))['total'] or 0,
        }
```

### 4. 财务风控系统

#### 提现审核机制
```python
class AgentWithdrawalApproval(models.Model):
    """代理商提现审核"""
    withdrawal_order = models.OneToOneField(AgentWithdrawalOrder, on_delete=models.CASCADE)
    approver = models.ForeignKey(USER_MODEL, on_delete=models.SET_NULL, null=True)
    approval_status = models.CharField(max_length=20, choices=[
        ('pending', '待审核'),
        ('approved', '已通过'),
        ('rejected', '已拒绝'),
    ], default='pending')
    approval_note = models.TextField(blank=True)
    approval_time = models.DateTimeField(null=True)
    
    class Meta:
        verbose_name = '代理商提现审核'

class AgentRiskControl:
    """代理商风控系统"""
    
    def __init__(self):
        self.daily_withdrawal_limit = 10000  # 日提现限额
        self.monthly_withdrawal_limit = 100000  # 月提现限额
        self.risk_score_threshold = 80  # 风险分数阈值
    
    def check_withdrawal_risk(self, agent, amount):
        """检查提现风险"""
        risks = []
        risk_score = 0
        
        # 检查日提现限额
        today = timezone.now().date()
        daily_total = AgentWithdrawalOrder.objects.filter(
            agent=agent,
            create_time__date=today,
            status__in=[0, 1]  # 待处理和成功
        ).aggregate(total=Sum('amount'))['total'] or 0
        
        if daily_total + amount > self.daily_withdrawal_limit:
            risks.append('超出日提现限额')
            risk_score += 30
        
        # 检查月提现限额
        month_start = today.replace(day=1)
        monthly_total = AgentWithdrawalOrder.objects.filter(
            agent=agent,
            create_time__date__gte=month_start,
            status__in=[0, 1]
        ).aggregate(total=Sum('amount'))['total'] or 0
        
        if monthly_total + amount > self.monthly_withdrawal_limit:
            risks.append('超出月提现限额')
            risk_score += 50
        
        # 检查余额异常增长
        recent_balance_increase = self.check_balance_growth(agent)
        if recent_balance_increase > 50000:  # 近期余额增长超过5万
            risks.append('余额异常增长')
            risk_score += 40
        
        # 检查分成合理性
        commission_ratio = self.check_commission_ratio(agent)
        if commission_ratio > 0.2:  # 分成比例超过20%
            risks.append('分成比例异常')
            risk_score += 30
        
        return {
            'risk_score': risk_score,
            'risks': risks,
            'requires_approval': risk_score >= self.risk_score_threshold
        }
    
    def check_balance_growth(self, agent):
        """检查余额增长情况"""
        week_ago = timezone.now() - timedelta(days=7)
        recent_records = AgentBalanceRecord.objects.filter(
            agent=agent,
            create_time__gte=week_ago,
            balance_changed__gt=0
        )
        return recent_records.aggregate(total=Sum('balance_changed'))['total'] or 0
```

## 监控和运营

### 代理商效果分析
```python
class AgentPerformanceAnalyzer:
    """代理商效果分析"""
    
    def generate_performance_report(self, agent, period_days=30):
        """生成代理商表现报告"""
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=period_days)
        
        # 基础数据
        sites = SEO.objects.filter(agent=agent.user)
        users = AuthUser.objects.filter(domain__in=[s.url for s in sites])
        
        # 用户增长
        user_growth = self.analyze_user_growth(users, start_date, end_date)
        
        # 收入分析
        revenue_analysis = self.analyze_revenue(agent, start_date, end_date)
        
        # 活跃度分析
        activity_analysis = self.analyze_user_activity(users, start_date, end_date)
        
        # 排名信息
        ranking = self.get_agent_ranking(agent)
        
        return {
            'period': {'start': start_date, 'end': end_date},
            'user_growth': user_growth,
            'revenue_analysis': revenue_analysis,
            'activity_analysis': activity_analysis,
            'ranking': ranking,
            'performance_score': self.calculate_performance_score(
                user_growth, revenue_analysis, activity_analysis
            )
        }
    
    def calculate_performance_score(self, user_growth, revenue, activity):
        """计算综合表现分数"""
        # 用户增长权重30%
        growth_score = min(user_growth['growth_rate'] * 10, 30)
        
        # 收入权重40%
        revenue_score = min(revenue['total_commission'] / 1000 * 4, 40)
        
        # 活跃度权重30%
        activity_score = activity['avg_daily_active_rate'] * 30
        
        return growth_score + revenue_score + activity_score
```

## 总结

Agent模块是一个功能相对完整但仍需重大改进的代理商管理系统。当前版本实现了基础的站点管理、用户统计、内容管理和提现功能，但在自动分成、权限管理、数据统计优化和风险控制方面存在明显不足。

建议重点改进：
1. **完善自动分成系统** - 实现基于用户行为的自动分成计算和发放
2. **强化权限管理** - 建立分级权限体系，确保数据安全
3. **优化数据统计** - 通过预计算和缓存提升查询性能
4. **加强风险控制** - 建立完整的财务风控和审核机制

通过这些改进，可以将Agent模块打造成一个安全、高效、功能完善的代理商管理平台。

## 接口清单

| 接口名称 | 方法 | 路径 | 状态 | 功能描述 |
|---------|------|------|------|----------|
| 检查代理商身份 | GET | `/api/agent/checkagent/` | 正常 | 验证代理商身份 |
| 获取站点列表 | GET | `/api/agent/site/` | 正常 | 获取管理站点 |
| 获取站点用户 | GET | `/api/agent/site/user/` | 正常 | 获取用户列表 |
| 获取充值记录 | GET | `/api/agent/site/user/charge/` | 正常 | 获取用户充值 |
| 获取提现记录 | GET | `/api/agent/site/user/withdraw/` | 正常 | 获取用户提现 |
| 获取饰品记录 | GET | `/api/agent/site/user/skin/` | 正常 | 获取饰品记录 |
| 文章分类管理 | POST | `/api/agent/manage/articlecategory/` | 正常 | 管理文章分类 |
| 文章管理 | POST | `/api/agent/manage/article/` | 正常 | 管理文章内容 |
| 获取代理商信息 | GET | `/api/agent/info/` | 正常 | 获取代理商信息 |
| 编辑代理商信息 | POST | `/api/agent/info/edit/` | 正常 | 更新代理商信息 |
| 申请提现 | POST | `/api/agent/withdrawal/` | 正常 | 申请收益提现 |
| 获取提现记录 | GET | `/api/agent/withdrawal/records/` | 正常 | 查看提现历史 |
| 获取余额记录 | GET | `/api/agent/balance/records/` | 正常 | 查看余额变动 |

**注：** 建议增加自动分成、数据统计、风险监控等管理接口。
