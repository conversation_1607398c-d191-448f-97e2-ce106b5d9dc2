/**
 * 统一轮次管理器
 * 
 * 解决问题：
 * 1. 消除getRoundIndex和roundIndex计算的不一致性
 * 2. 建立单一轮次索引计算标准
 * 3. 提供轮次验证和状态管理
 * 4. 统一轮次相关的计算逻辑
 * 5. 支持轮次范围检查和边界处理
 */

interface RoundInfo {
  currentRound: number;       // 当前轮次（1-based）
  totalRounds: number;        // 总轮次数
  roundIndex: number;         // 轮次索引（0-based）
  isFirstRound: boolean;      // 是否为第一轮
  isLastRound: boolean;       // 是否为最后一轮
  progress: number;           // 进度百分比 (0-100)
  remaining: number;          // 剩余轮次
  completed: number;          // 已完成轮次
}

interface RoundValidationResult {
  isValid: boolean;
  reason?: string;
  correctedRound?: number;
}

export const useBattleRoundManager = () => {
  // 当前轮次（1-based）
  const currentRound = ref<number>(1);
  
  // 总轮次数
  const totalRounds = ref<number>(1);
  
  // 轮次状态
  const roundState = ref<'initial' | 'waiting' | 'ready' | 'running' | 'completed'>('initial');
  
  // 轮次历史记录
  const roundHistory = ref<Array<{ round: number; timestamp: number; action: string }>>([]);

  /**
   * 统一的轮次索引计算方法
   * 将1-based轮次转换为0-based索引
   */
  const getRoundIndex = (round?: number): number => {
    const targetRound = round ?? currentRound.value;
    const total = totalRounds.value;
    
    // 基础验证
    if (!targetRound || !total || targetRound < 1) {
      console.warn('[🔄ROUND-MANAGER] 无效轮次，返回默认索引0:', { targetRound, total });
      return 0;
    }
    
    // 计算0-based索引
    const index = (targetRound - 1) % total;
    
    // 边界检查
    if (index < 0 || index >= total) {
      console.warn('[🔄ROUND-MANAGER] 轮次索引超出范围，返回安全索引:', { 
        targetRound, 
        total, 
        calculatedIndex: index,
        safeIndex: Math.max(0, Math.min(index, total - 1))
      });
      return Math.max(0, Math.min(index, total - 1));
    }
    
    return index;
  };

  /**
   * 获取轮次详细信息
   */
  const getRoundInfo = (round?: number): RoundInfo => {
    const targetRound = round ?? currentRound.value;
    const total = totalRounds.value;
    const index = getRoundIndex(targetRound);
    
    return {
      currentRound: targetRound,
      totalRounds: total,
      roundIndex: index,
      isFirstRound: targetRound === 1,
      isLastRound: targetRound === total,
      progress: total > 0 ? (targetRound / total) * 100 : 0,
      remaining: Math.max(0, total - targetRound),
      completed: Math.max(0, targetRound - 1)
    };
  };

  /**
   * 验证轮次是否有效
   */
  const validateRound = (round: number): RoundValidationResult => {
    const total = totalRounds.value;
    
    // 🎯 统一验证逻辑：与 isRoundInRange 保持一致的类型检查
    if (!round || typeof round !== 'number') {
      return {
        isValid: false,
        reason: '轮次必须是有效数字',
        correctedRound: 1
      };
    }
    
    if (round < 1) {
      return {
        isValid: false,
        reason: '轮次不能小于1',
        correctedRound: 1
      };
    }
    
    if (round > total) {
      return {
        isValid: false,
        reason: `轮次不能大于总轮次数(${total})`,
        correctedRound: total
      };
    }
    
    return {
      isValid: true
    };
  };

  /**
   * 安全的轮次设置方法
   */
  const setCurrentRound = (round: number, force = false): boolean => {
    try {
      const validation = validateRound(round);
      
      if (!validation.isValid && !force) {
        console.warn('[🔄ROUND-MANAGER] 轮次验证失败:', validation.reason);
        
        // 使用修正后的轮次
        if (validation.correctedRound) {
          currentRound.value = validation.correctedRound;
          recordRoundAction(validation.correctedRound, 'corrected');
          console.log('[🔄ROUND-MANAGER] 使用修正轮次:', validation.correctedRound);
          return true;
        }
        
        return false;
      }
      
      // 记录轮次变更
      if (currentRound.value !== round) {
        recordRoundAction(round, 'changed');
        currentRound.value = round;
        console.log('[🔄ROUND-MANAGER] 轮次已更新:', round);
      }
      
      return true;
      
    } catch (error: any) {
      console.error('[🔄ROUND-MANAGER] 设置轮次失败:', error);
      return false;
    }
  };

  /**
   * 安全的总轮次设置方法
   */
  const setTotalRounds = (total: number): boolean => {
    try {
      if (!total || typeof total !== 'number' || total < 1) {
        console.warn('[🔄ROUND-MANAGER] 无效的总轮次数:', total);
        return false;
      }
      
      totalRounds.value = total;
      
      // 如果当前轮次超出范围，自动调整
      if (currentRound.value > total) {
        setCurrentRound(total, true);
      }
      
      recordRoundAction(total, 'total_updated');
      console.log('[🔄ROUND-MANAGER] 总轮次已更新:', total);
      
      return true;
      
    } catch (error: any) {
      console.error('[🔄ROUND-MANAGER] 设置总轮次失败:', error);
      return false;
    }
  };

  /**
   * 进入下一轮
   */
  const nextRound = (): boolean => {
    const current = currentRound.value;
    const total = totalRounds.value;
    
    if (current >= total) {
      console.warn('[🔄ROUND-MANAGER] 已经是最后一轮，无法进入下一轮');
      return false;
    }
    
    return setCurrentRound(current + 1);
  };

  /**
   * 进入上一轮
   */
  const previousRound = (): boolean => {
    const current = currentRound.value;
    
    if (current <= 1) {
      console.warn('[🔄ROUND-MANAGER] 已经是第一轮，无法进入上一轮');
      return false;
    }
    
    return setCurrentRound(current - 1);
  };

  /**
   * 重置轮次状态
   */
  const resetRounds = (): void => {
    try {
      currentRound.value = 1;
      totalRounds.value = 1;
      roundState.value = 'initial';
      roundHistory.value = [];
      
      console.log('[🔄ROUND-MANAGER] 轮次状态已重置');
      
    } catch (error: any) {
      console.error('[🔄ROUND-MANAGER] 重置轮次状态失败:', error);
    }
  };

  /**
   * 记录轮次操作历史
   */
  const recordRoundAction = (round: number, action: string): void => {
    try {
      const record = {
        round,
        timestamp: Date.now(),
        action
      };
      
      roundHistory.value.push(record);
      
      // 保持历史记录在合理范围内
      if (roundHistory.value.length > 100) {
        roundHistory.value = roundHistory.value.slice(-50);
      }
      
    } catch (error: any) {
      console.error('[🔄ROUND-MANAGER] 记录轮次操作失败:', error);
    }
  };

  /**
   * 获取轮次历史记录
   */
  const getRoundHistory = (limit = 20) => {
    return roundHistory.value
      .slice(-limit)
      .reverse();
  };

  /**
   * 检查轮次是否在有效范围内
   */
  const isRoundInRange = (round: number): boolean => {
    // 🎯 统一验证逻辑：确保类型安全和范围检查一致
    if (!round || typeof round !== 'number') {
      return false;
    }
    return round >= 1 && round <= totalRounds.value;
  };

  /**
   * 获取轮次范围信息
   */
  const getRoundRange = () => {
    return {
      min: 1,
      max: totalRounds.value,
      current: currentRound.value,
      isValid: isRoundInRange(currentRound.value)
    };
  };

  /**
   * 从API数据同步轮次信息
   */
  const syncFromApiData = (apiData: any): void => {
    try {
      if (!apiData) {
        console.warn('[🔄ROUND-MANAGER] API数据为空，跳过同步');
        return;
      }
      
      // 同步总轮次
      if (apiData.round_count && typeof apiData.round_count === 'number') {
        setTotalRounds(apiData.round_count);
      }
      
      // 同步当前轮次
      if (apiData.round_count_current && typeof apiData.round_count_current === 'number') {
        setCurrentRound(apiData.round_count_current);
      }
      
      // 同步轮次状态
      if (apiData.round_state && typeof apiData.round_state === 'string') {
        roundState.value = apiData.round_state as any;
      }
      
      recordRoundAction(currentRound.value, 'api_sync');
      console.log('[🔄ROUND-MANAGER] 轮次信息已从API同步:', {
        current: currentRound.value,
        total: totalRounds.value,
        state: roundState.value
      });
      
    } catch (error: any) {
      console.error('[🔄ROUND-MANAGER] 从API同步轮次信息失败:', error);
    }
  };

  // 计算属性
  const roundInfo = computed(() => getRoundInfo());
  const isFirstRound = computed(() => currentRound.value === 1);
  const isLastRound = computed(() => currentRound.value === totalRounds.value);
  const roundProgress = computed(() => totalRounds.value > 0 ? (currentRound.value / totalRounds.value) * 100 : 0);
  const remainingRounds = computed(() => Math.max(0, totalRounds.value - currentRound.value));
  const completedRounds = computed(() => Math.max(0, currentRound.value - 1));

  // 响应式状态
  const state = {
    currentRound: readonly(currentRound),
    totalRounds: readonly(totalRounds),
    roundState: readonly(roundState),
    roundHistory: readonly(roundHistory)
  };

  return {
    // 核心方法
    getRoundIndex,
    getRoundInfo,
    validateRound,
    setCurrentRound,
    setTotalRounds,
    nextRound,
    previousRound,
    resetRounds,
    syncFromApiData,
    
    // 查询方法
    isRoundInRange,
    getRoundRange,
    getRoundHistory,
    
    // 计算属性
    roundInfo,
    isFirstRound,
    isLastRound,
    roundProgress,
    remainingRounds,
    completedRounds,
    
    // 状态
    ...state
  };
}; 