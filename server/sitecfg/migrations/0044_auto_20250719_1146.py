# Generated by Django 3.2.25 on 2025-07-19 03:46

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('sitecfg', '0043_auto_20250718_1459'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='announcee',
            options={'ordering': ('type',), 'verbose_name': '公告', 'verbose_name_plural': 'Announcee'},
        ),
        migrations.AlterModelOptions(
            name='articlecategoryy',
            options={'verbose_name': '文章分类', 'verbose_name_plural': 'Article Category'},
        ),
        migrations.AlterModelOptions(
            name='banner',
            options={'ordering': ('order',), 'verbose_name': '横幅', 'verbose_name_plural': 'Banner'},
        ),
        migrations.AlterModelOptions(
            name='faq',
            options={'verbose_name': '常见问题', 'verbose_name_plural': 'FAQ'},
        ),
        migrations.AlterModelOptions(
            name='footer',
            options={'ordering': ('no',), 'verbose_name': '页脚', 'verbose_name_plural': 'Footer'},
        ),
        migrations.AlterModelOptions(
            name='seo',
            options={'verbose_name': 'SEO设置', 'verbose_name_plural': 'SEO'},
        ),
        migrations.AlterModelOptions(
            name='siteconfig',
            options={'verbose_name': '站点配置', 'verbose_name_plural': 'Site Config'},
        ),
        migrations.AlterModelOptions(
            name='siteconfigcategoryyy',
            options={'verbose_name': '站点配置分类', 'verbose_name_plural': 'Site Config Category'},
        ),
        migrations.AlterModelOptions(
            name='support',
            options={'ordering': ('sort',), 'verbose_name': '客服支持', 'verbose_name_plural': 'Support'},
        ),
        migrations.AlterField(
            model_name='article',
            name='category',
            field=models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='articles', to='sitecfg.articlecategoryy', verbose_name='分类'),
        ),
        migrations.AlterField(
            model_name='footer',
            name='anno',
            field=models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='footer', to='sitecfg.announcee', verbose_name='公告'),
        ),
        migrations.AlterField(
            model_name='seo',
            name='agent',
            field=models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='agent_site', to=settings.AUTH_USER_MODEL, verbose_name='代理'),
        ),
        migrations.AlterField(
            model_name='siteconfig',
            name='category',
            field=models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='site_config', to='sitecfg.siteconfigcategoryyy', verbose_name='分类'),
        ),
    ]
