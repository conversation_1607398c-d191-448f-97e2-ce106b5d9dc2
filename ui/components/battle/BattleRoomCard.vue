<template>
  <div 
    :class="[
      'battle-room-card group relative backdrop-blur-xl border rounded-2xl p-6 transition-all duration-300 hover:scale-[1.02] cursor-pointer',
      cardTheme.background,
      cardTheme.border,
      cardTheme.hover,
      {
        'ring-2 ring-yellow-500/30': hasWinner && (type === 'my' || type === 'history'),
        'ring-2 ring-green-500/30': type === 'my' && isRoomOwner(),
        'ring-2 ring-blue-500/30': type === 'my' && isParticipant() && !isRoomOwner()
      }
    ]"
    @click="$emit('view-room', room)"
  >
    <!-- 新用户加入动画提示 -->
    <div 
      v-if="(room as any)._hasNewJoiner"
      class="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50 pointer-events-none"
    >
      <div class="user-joined-notification bg-gradient-to-r from-green-500 to-green-600 text-white px-4 py-2 rounded-full shadow-lg border border-green-400/30">
        <div class="flex items-center gap-2">
          <!-- 使用用户头像昵称 -->
          
          <span class="animate-bounce flex items-center text-sm font-monos text-blond">
            <img 
            :src="room.user?.profile?.avatar || '/images/default-avatar.png'" 
            :alt="room.user?.profile?.nickname || 'New User'"
            class="w-6 h-6 rounded-full object-cover border-2 border-white/35 shadow-lg text-sm animate-bounce"
          />
          {{ room.user?.profile?.nickname }}</span>
          <span class="text-sm font-medium">
            
            {{ t('battle.user_joined_notification', { count: (room as any)._joinerDiff || 1 }) }}
          </span>
        </div>
      </div>
    </div>
    <!-- 状态标识 -->
    <div class="absolute top-4 right-4 flex gap-2">
      <div :class="['px-3 py-1 rounded-full text-xs font-medium text-white', getStatusBadgeClass()]">
        {{ getStatusText() }}
      </div>
      
      <!-- 角色标识 -->
      <div v-if="showRole" :class="['px-3 py-1 rounded-full text-xs font-medium text-white', getRoleBadgeClass()]">
        {{ getRoleText() }}
      </div>
      
      <!-- 获胜者标识 -->
      <!-- <div v-if="hasWinner && (type === 'my' || type === 'history')" class="px-3 py-1 rounded-full text-xs font-medium text-white bg-gradient-to-r from-yellow-500 to-yellow-600">
        <i class="fas fa-crown text-xs mr-1"></i>
        {{ t('battle.winner') }}
      </div> -->
    </div>

    <!-- 房间基本信息 -->
    <div class="mb-4">
      <div class="flex items-center gap-3 mb-2">
        <div :class="['px-3 py-1 border rounded-lg', cardTheme.price]">
          <span class="font-bold">${{ (room.price || 0).toFixed(2) }}</span>
        </div>
      </div>
    </div>

    <!-- 箱子展示区域 -->
    <div class="cases-grid mb-4">
      <div class="grid grid-cols-4 gap-3">
        <div 
          v-for="(round, index) in room.rounds?.slice(0, 4)" 
          :key="index"
          class="case-item bg-gray-700/30 rounded-xl p-3 border border-gray-600/30 hover:border-gray-500/50 transition-colors"
        >
          <div class="relative h-20 mb-2 rounded-lg overflow-hidden">
            <img 
              :src="round.case?.cover || '/images/default-case.png'" 
              :alt="getLocalizedCaseName(round.case)"
              class="w-full h-full object-cover"
              loading="lazy"
            >
            <!-- 箱子使用次数 - 左上角 -->
            <div v-if="(round.case as any)?.count > 1" class="absolute top-2 left-2 px-2 py-1 bg-orange-500/90 rounded text-xs text-white font-bold flex items-center gap-1">
              <span>X{{ (round.case as any).count }}</span>
            </div>
            <!-- 价格标签 - 右下角 -->
            <div class="absolute top-2 right-2 px-2 py-1 bg-blue-600/80 rounded text-xs text-white font-medium">
              ${{ (round.case?.price || 0).toFixed(2) }}
            </div>
          </div>
          <div class="text-center text-xs text-white/70 truncate">
            {{ getLocalizedCaseName(round.case) }}
          </div>
        </div>
      </div>
    </div>

    <!-- 参与者预览 -->
    <div class="participants-preview mb-4">
      <div class="flex items-center justify-between">
        <span class="text-white/60 text-sm">{{ t('battle.participants') }}（{{ getCurrentPlayerCount() }}/{{ room.max_joiner }}）</span>
      </div>
      
      <!-- 参与者头像紧凑布局 -->
      <div class="mt-2 flex items-center justify-between">
        <!-- 左侧：参与者头像组 -->
        <div class="flex items-center -space-x-2">
          <!-- 房主头像（创建者） -->
          <div class="relative group">
            <div class="relative">
              <img 
                :src="room.user?.profile?.avatar || '/images/default-avatar.png'" 
                :alt="room.user?.profile?.nickname || 'Creator'"
                class="w-9 h-9 rounded-full text-secondary-light object-cover border-2 border-yellow-500/100 shadow-lg z-10 relative bg-gray-800"
              >
              <!-- 胜利者皇冠角标（仅在已结束的我的对战和历史对战中显示） -->
              <div 
                v-if="isWinner(room.user) && (type === 'my' || type === 'history') && room.state === GameState.End"
                class="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-full flex items-center justify-center shadow-lg border border-yellow-300/50 z-20"
              >
                <i class="fas fa-crown text-[10px] text-gray-900"></i>
              </div>

              <!-- 创建者角标（活跃对战和我的对战中未结束房间） -->
              <div 
                v-else-if="shouldShowCreatorBadge()"
                :class="[
                  'absolute -top-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center shadow-lg border z-20',
                  type === 'active' 
                    ? 'bg-gradient-to-br from-blue-500 to-blue-600 border-blue-300/50'
                    : 'bg-gradient-to-br from-yellow-500 to-yellow-600 border-yellow-300/50'
                ]"
              >
                <i :class="[
                  'text-[8px] text-white',
                  type === 'active' ? 'fas fa-star' : 'fas fa-star'
                ]"></i>
              </div>
              <!-- 胜利者光环（仅在已结束的我的对战和历史对战中显示） -->
              <div 
                v-if="isWinner(room.user) && (type === 'my' || type === 'history') && room.state === GameState.End"
                class="absolute inset-0 rounded-full bg-gradient-to-r from-yellow-400/30 to-orange-400/30 animate-pulse"
              ></div>
            </div>
            <!-- 悬停提示（活跃对战和我的对战中未结束的房间显示名称） -->
            <div 
              v-if="shouldShowTooltip()"
              class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 px-2 py-1 bg-black/90 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-30"
            >
              {{ room.user?.profile?.nickname || 'Creator' }}
              <span v-if="type === 'active'" class="text-blue-400 ml-1">⭐</span>
              <span v-else-if="isRoomOwner()" class="text-yellow-400 ml-1">👑</span>
            </div>
          </div>
          
          <!-- 参与者头像 -->
          <div 
            v-for="(participant, index) in getDisplayParticipants()" 
            :key="participant.user?.uid || index"
            class="relative group"
          >
            <div class="relative">
              <img 
                :src="participant.user?.profile?.avatar || '/images/default-avatar.png'" 
                :alt="participant.user?.profile?.nickname || 'Player'"
                :class="[
                  'w-9 h-9 rounded-full object-cover border-2 shadow-lg transition-all duration-300 relative bg-gray-800',
                  isWinner(participant.user) && (type === 'my' || type === 'history') && room.state === GameState.End ? 'border-yellow-500/80 z-10' : 'border-gray-500/60 z-5'
                ]"
              >
              <!-- 胜利者皇冠角标（仅在已结束的我的对战和历史对战中显示） -->
              <div 
                v-if="isWinner(participant.user) && (type === 'my' || type === 'history') && room.state === GameState.End"
                class="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-full flex items-center justify-center shadow-lg border border-yellow-300/50 z-20"
              >
                <i class="fas fa-crown text-[10px] text-gray-900"></i>
              </div>
              <!-- 胜利者光环（仅在已结束的我的对战和历史对战中显示） -->
              <div 
                v-if="isWinner(participant.user) && (type === 'my' || type === 'history') && room.state === GameState.End"
                class="absolute inset-0 rounded-full bg-gradient-to-r from-yellow-400/30 to-orange-400/30 animate-pulse"
              ></div>
            </div>
            <!-- 悬停提示（活跃对战和我的对战中未结束的房间显示名称） -->
            <div 
              v-if="shouldShowTooltip()"
              class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 px-2 py-1 bg-black/90 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-30"
            >
              {{ participant.user?.profile?.nickname || 'Player' }}
              <span v-if="isWinner(participant.user) && (type === 'my' || type === 'history') && room.state === GameState.End" class="text-yellow-400 ml-1">👑</span>
            </div>
          </div>
          
          <!-- 空位占位符（仅在活跃对战中显示） -->
          <div 
            v-if="shouldShowEmptySlots()"
            v-for="n in getEmptySlots()" 
            :key="`empty-${n}`"
            class="w-9 h-9 rounded-full border-2 border-dashed border-gray-600/40 bg-gray-700/20 flex items-center justify-center"
          >
            <i class="fas fa-plus text-gray-500/50 text-xs"></i>
          </div>
        </div>
        
        <!-- 右侧区域保留为空，不显示任何用户信息 -->
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="action-buttons flex gap-2">
      <!-- 活跃对战：显示加入按钮和查看详情按钮 -->
      <template v-if="type === 'active'">
        <!-- 加入对战按钮（突出显示） -->
        <button
          v-if="canJoin"
          @click.stop="$emit('join-room', room)"
          :class="['flex-1 px-4 py-3 rounded-lg font-bold text-white transition-all duration-300 shadow-lg hover:shadow-xl', cardTheme.joinButton]"
        >
          <i class="fas fa-play text-sm mr-2"></i>
          {{ t('battle.join_battle') }}
        </button>
        
        <!-- 观战按钮（进行中的房间） -->
        <button
          v-else-if="canSpectate"
          @click.stop="$emit('spectate-room', room)"
          class="flex-1 px-4 py-3 bg-purple-600/60 hover:bg-purple-600 rounded-lg font-bold text-white transition-all duration-300 shadow-lg hover:shadow-xl"
        >
          <i class="fas fa-eye text-sm mr-2"></i>
          {{ t('battle.spectate') }}
        </button>
        
        <!-- 满员或其他状态时显示主要按钮 -->
        <button
          v-else
          @click.stop="$emit('view-room', room)"
          :class="['flex-1 px-4 py-3 rounded-lg font-bold text-white transition-all duration-300 shadow-lg hover:shadow-xl', cardTheme.joinButton]"
        >
          <i class="fas fa-eye text-sm mr-2"></i>
          {{ room.state === 11 ? t('battle.view_result') : t('battle.view_details') }}
        </button>
        
        <!-- 查看详情按钮（弱化显示） -->
        <button
          @click.stop="$emit('view-room', room)"
          class="px-3 py-3 bg-gray-600/30 hover:bg-gray-600/50 border border-gray-500/30 hover:border-gray-400/50 rounded-lg font-medium text-gray-300 hover:text-white transition-all duration-300 text-sm"
        >
          <i class="fas fa-info-circle text-xs"></i>
        </button>
      </template>
      
      <!-- 我的对战和历史对战：保持原有逻辑 -->
      <template v-else>
        <button
          v-if="canJoin"
          @click.stop="$emit('join-room', room)"
          :class="['flex-1 px-4 py-2 rounded-lg font-medium text-white transition-all duration-300', cardTheme.joinButton]"
        >
          <i class="fas fa-play text-sm mr-2"></i>
          {{ t('battle.join_battle') }}
        </button>
        
        <button
          v-else-if="canSpectate"
          @click.stop="$emit('spectate-room', room)"
          class="flex-1 px-4 py-2 bg-purple-600/50 hover:bg-purple-600 rounded-lg font-medium text-white transition-all duration-300"
        >
          <i class="fas fa-eye text-sm mr-2"></i>
          {{ t('battle.spectate') }}
        </button>
        
        <button
          v-else
          @click.stop="$emit('view-room', room)"
          :class="['flex-1 px-4 py-2 rounded-lg font-medium transition-all duration-300', cardTheme.viewButton]"
        >
          <i class="fas fa-eye text-sm mr-2"></i>
          {{ room.state === 11 ? t('battle.view_result') : t('battle.view_details') }}
        </button>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { BattleRoom } from '~/services/battle-api'
import { GameState } from '~/services/battle-api'

const { t, locale } = useI18n()
const userStore = useUserStore()

const props = defineProps<{
  room: BattleRoom
  type: 'active' | 'my' | 'history'
  showRole?: boolean
}>()

defineEmits<{
  'view-room': [room: BattleRoom]
  'join-room': [room: BattleRoom]
  'spectate-room': [room: BattleRoom]
}>()

// 计算属性
const cardTheme = computed(() => {
  switch (props.type) {
    case 'active':
      return {
        background: 'bg-gradient-to-br from-blue-800/30 to-blue-700/30',
        border: 'border-blue-600/30 hover:border-blue-500/50',
        hover: 'hover:shadow-lg hover:shadow-blue-500/20',
        price: 'bg-blue-500/20 border-blue-500/30 text-blue-400',
        joinButton: 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 hover:scale-105 transform',
        viewButton: 'bg-gradient-to-r from-blue-600/50 to-blue-700/50 hover:from-blue-600 hover:to-blue-700 text-white/80 hover:text-white'
      }
    case 'my':
      return {
        background: 'bg-gradient-to-br from-green-800/30 to-green-700/30',
        border: 'border-green-600/30 hover:border-green-500/50',
        hover: 'hover:shadow-lg hover:shadow-green-500/20',
        price: 'bg-green-500/20 border-green-500/30 text-green-400',
        joinButton: 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700',
        viewButton: 'bg-gradient-to-r from-green-600/50 to-green-700/50 hover:from-green-600 hover:to-green-700 text-white/80 hover:text-white'
      }
    case 'history':
      return {
        background: 'bg-gradient-to-br from-gray-800/30 to-gray-700/30',
        border: 'border-gray-600/30 hover:border-gray-500/50',
        hover: 'hover:shadow-lg hover:shadow-gray-500/20',
        price: 'bg-gray-600/20 border-gray-500/30 text-gray-400',
        joinButton: 'bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700',
        viewButton: 'bg-gradient-to-r from-gray-600/50 to-gray-700/50 hover:from-gray-600 hover:to-gray-700 text-white/80 hover:text-white'
      }
    default:
      return {
        background: 'bg-gradient-to-br from-gray-800/30 to-gray-700/30',
        border: 'border-gray-600/30',
        hover: '',
        price: 'bg-gray-600/20 border-gray-500/30 text-gray-400',
        joinButton: 'bg-gray-600',
        viewButton: 'bg-gray-600 text-white'
      }
  }
})

const canJoin = computed(() => {
  const isJoinableState = props.room.state === GameState.Joinable || props.room.state === GameState.Initial
  const currentPlayers = getCurrentPlayerCount()
  return isJoinableState &&
         currentPlayers < props.room.max_joiner &&
         !isRoomOwner() &&
         !isParticipant()
})

const canSpectate = computed(() => {
  return props.room.state === 5 && !isParticipant() // Running
})

const hasWinner = computed(() => {
  return props.room.bets?.some((bet: any) => bet.victory === 1) || false
})

// 计算实际房间状态（考虑满员情况）
const getRoomActualState = () => {
  // 优先根据后端返回的状态判断（使用数字状态）
  // 如果是进行中或已结束，直接返回该状态
  if (props.room.state === 5 || props.room.state === 11 || props.room.state === 20) { // Running, End, or Cancelled
    return props.room.state
  }
  
  // 如果后端直接返回满员状态，显示满员
  if (props.room.state === 4) { // Full
    return 4 // Full
  }
  
  // 如果是等待状态但人数已满，显示满员状态
  if ((props.room.state === 1 || props.room.state === 2) && getCurrentPlayerCount() >= props.room.max_joiner) { // Initial or Joinable
    return 4 // Full
  }
  
  // 其他情况返回原始状态
  return props.room.state
}

// 方法
const getStatusBadgeClass = () => {
  const actualState = getRoomActualState()
  
  switch (actualState) {
    case 1: // Initial - 新创建的房间，可加入
    case 2: // Joinable - 可加入
      return 'bg-green-600/80'
    case 3: // Joining - 加入中
      return 'bg-yellow-600/80'
    case 4: // Full - 满员
      return 'bg-orange-600/80'
    case 5: // Running - 进行中
      return 'bg-blue-600/80'
    case 11: // End - 已结束
      return 'bg-gray-600/80'
    case 20: // Cancelled - 已取消
      return 'bg-red-600/80'
    default:
      return 'bg-gray-600/80'
  }
}

const getStatusText = () => {
  const actualState = getRoomActualState()
  
  switch (actualState) {
    case 1: // Initial - 新创建的房间，显示为可加入
    case 2: // Joinable - 可加入
      return t('battle.status.joinable')
    case 3: // Joining - 加入中
      return t('battle.status.joining')
    case 4: // Full - 满员
      return t('battle.status.full')
    case 5: // Running - 进行中
      return t('battle.status.ongoing')
    case 11: // End - 已结束
      return t('battle.status.ended')
    case 20: // Cancelled - 已取消
      return t('battle.status.cancelled')
    default:
      return t('battle.status.unknown')
  }
}

const getRoleBadgeClass = () => {
  return isRoomOwner() ? 'bg-yellow-600/80' : 'bg-green-600/80'
}

const getRoleText = () => {
  return isRoomOwner() ? t('battle.created_by_me') : t('battle.participated_by_me')
}

const isRoomOwner = () => {
  return props.room.user?.uid === userStore.user?.uid
}

const isParticipant = () => {
  return props.room.bets?.some((bet: any) => bet.user?.uid === userStore.user?.uid)
}

const isWinner = (user: any) => {
  if (!user?.uid || !props.room.bets) return false
  
  // 查找胜利的bet（victory为1表示胜利，根据API文档）
  const winnerBet = props.room.bets?.find((bet: any) => bet.victory === 1)
  
  // 检查用户是否在胜利的bet中
  const isBetWinner = winnerBet && winnerBet.user?.uid === user.uid
  
  // 检查房主是否也在bets数组中作为胜利者
  const roomOwnerInBets = props.room.bets?.find((bet: any) => 
    bet.user?.uid === props.room.user?.uid && bet.victory === 1
  )
  
  // 如果是房主且房主在bets中获胜，则为胜利者
  const isRoomOwnerWinner = user.uid === props.room.user?.uid && !!roomOwnerInBets
  
  const result = isBetWinner || isRoomOwnerWinner
  
  return result
}

const getWinnerUser = () => {
  const winnerBet = props.room.bets?.find((bet: any) => bet.victory === true)
  return winnerBet?.user || null
}

const getUniqueParticipants = () => {
  if (!props.room.bets) return []
  
  const seen = new Set()
  return props.room.bets.filter((bet: any) => {
    if (seen.has(bet.user?.uid) || bet.user?.uid === props.room.user?.uid) {
      return false
    }
    seen.add(bet.user?.uid)
    return true
  })
}

const getCurrentPlayerCount = () => {
  const uniqueJoiners = getUniqueParticipants()
  return uniqueJoiners.length + 1 // +1 for room owner
}

const getDisplayParticipants = () => {
  const participants = getUniqueParticipants()
  // 最多显示3个参与者（除了房主）
  return participants.slice(0, 3)
}

const getEmptySlots = () => {
  const currentCount = getCurrentPlayerCount()
  const maxSlots = Math.min(props.room.max_joiner, 4) // 最多显示4个位置
  const emptyCount = maxSlots - currentCount
  return emptyCount > 0 ? emptyCount : 0
}

const shouldShowEmptySlots = () => {
  // 活跃对战和我的对战中未结束的房间显示空位
  return props.type === 'active' || 
         (props.type === 'my' && props.room.state !== 11) // End
}

const shouldShowTooltip = () => {
  // 活跃对战和我的对战中未结束的房间显示悬停提示
  return props.type === 'active' || 
         (props.type === 'my' && props.room.state !== 11) // End
}

const shouldShowCreatorBadge = () => {
  // 活跃对战和我的对战中未结束的房间显示创建者角标
  return props.type === 'active' || 
         (props.type === 'my' && props.room.state !== 11) // End
}

const getLocalizedCaseName = (caseItem: any) => {
  if (!caseItem) return t('battle.unknown_case')
  
  return locale.value.startsWith('zh') ? 
    (caseItem.name_zh_hans || caseItem.name) : 
    (caseItem.name_en || caseItem.name)
}
</script>

<style scoped>
/* 用户加入提示动画 */
.user-joined-notification {
  animation: userJoinedSlideIn 1.5s ease-out;
}

@keyframes userJoinedSlideIn {
  0% {
    opacity: 0;
    transform: translateY(-20px) scale(0.8);
  }
  20% {
    opacity: 1;
    transform: translateY(-10px) scale(1.1);
  }
  40% {
    transform: translateY(-8px) scale(1);
  }
  60% {
    transform: translateY(-10px) scale(1.05);
  }
  80% {
    transform: translateY(-8px) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateY(-20px) scale(0.9);
  }
}
</style> 