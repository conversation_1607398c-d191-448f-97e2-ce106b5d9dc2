[supervisord]
nodaemon=true
logfile=/app/logs/supervisord.log
logfile_maxbytes=50MB
logfile_backups=10
loglevel=info
pidfile=/app/logs/supervisord.pid
user=root

[program:steambase_server]
command=/usr/local/bin/python3 /usr/local/bin/gunicorn --config=/app/server.conf --log-config=/app/log_config.ini steambase.wsgi:application
stdout_logfile=/app/logs/gunicorn.log
stderr_logfile=/app/logs/gunicorn_error.log
autostart=true
autorestart=true
startsecs=5
priority=1
stopasgroup=true
killasgroup=true

[program:steambase_worker]
command=/usr/local/bin/python3 /app/manage.py setupworker
stdout_logfile=/app/logs/worker.log
stderr_logfile=/app/logs/worker_error.log
autostart=true
autorestart=true
startsecs=5
priority=1
stopasgroup=true
killasgroup=true

[program:celery_worker]
command=/usr/local/bin/python3 -m celery -A steambase worker --loglevel=info
stdout_logfile=/app/logs/celery_worker.log
stderr_logfile=/app/logs/celery_worker.err.log
autostart=true
autorestart=true
startsecs=5
priority=10
stopasgroup=true
killasgroup=true

[program:celery_beat]
command=/usr/local/bin/python3 -m celery -A steambase beat --loglevel=info
stdout_logfile=/app/logs/celery_beat.log
stderr_logfile=/app/logs/celery_beat.err.log
autostart=true
autorestart=true
startsecs=5
priority=9
stopasgroup=true
killasgroup=true