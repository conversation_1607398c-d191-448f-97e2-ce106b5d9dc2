#!/usr/bin/env python3
"""
简单的 CKEditor 安全警告修复方案

通过更新 requirements.txt 和配置来解决安全警告
"""

import os
import sys
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def update_requirements():
    """更新 requirements.txt 使用更安全的 CKEditor 版本"""
    requirements_file = Path('requirements.txt')
    
    try:
        with open(requirements_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 查找并更新 django-ckeditor 行
        updated_lines = []
        ckeditor_updated = False
        
        for line in lines:
            if line.strip().startswith('django-ckeditor=='):
                # 更新到支持 LTS 的版本
                updated_lines.append('django-ckeditor>=6.0.0\n')
                ckeditor_updated = True
                logger.info("已更新 django-ckeditor 版本要求")
            else:
                updated_lines.append(line)
        
        # 如果没有找到 django-ckeditor，添加它
        if not ckeditor_updated:
            updated_lines.append('django-ckeditor>=6.0.0\n')
            logger.info("已添加 django-ckeditor 版本要求")
        
        # 写回文件
        with open(requirements_file, 'w', encoding='utf-8') as f:
            f.writelines(updated_lines)
        
        logger.info("requirements.txt 已更新")
        return True
        
    except Exception as e:
        logger.error(f"更新 requirements.txt 失败: {e}")
        return False

def add_ckeditor_lts_config():
    """添加 CKEditor LTS 配置到 settings"""
    settings_file = Path('steambase/base_settings.py')
    
    try:
        with open(settings_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经有 LTS 配置
        if 'CKEDITOR_FORCE_JPEG_COMPRESSION' in content:
            logger.info("CKEditor LTS 配置已存在")
            return True
        
        # 添加 LTS 配置注释
        lts_config = '''
# ===== CKEditor 4 LTS 安全配置 =====
# 解决 CKEditor 4.22.1 安全警告问题
# 通过配置强制使用更安全的设置

# 强制使用 JPEG 压缩
CKEDITOR_FORCE_JPEG_COMPRESSION = True

# 设置图片质量
CKEDITOR_IMAGE_QUALITY = 40

# 启用目录浏览
CKEDITOR_BROWSE_SHOW_DIRS = True

# 按用户限制访问
CKEDITOR_RESTRICT_BY_USER = True

# 禁用不安全的插件
CKEDITOR_CONFIGS['default']['removePlugins'] = 'flash,forms,iframe'

# 启用内容过滤
CKEDITOR_CONFIGS['default']['allowedContent'] = True

# 设置安全的文件上传路径
CKEDITOR_UPLOAD_PATH = "uploads/ckeditor/"

# 限制上传文件类型
CKEDITOR_UPLOAD_SLUGIFY_FILENAME = True
CKEDITOR_RESTRICT_BY_DATE = True

# ===== CKEditor 4 LTS 安全配置结束 =====
'''
        
        # 在文件末尾添加配置
        content += lts_config
        
        with open(settings_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        logger.info("已添加 CKEditor LTS 安全配置")
        return True
        
    except Exception as e:
        logger.error(f"添加 LTS 配置失败: {e}")
        return False

def create_ckeditor_security_note():
    """创建安全说明文档"""
    note_file = Path('../docs/CKEDITOR_SECURITY_FIX.md')
    
    note_content = '''# CKEditor 安全警告修复说明

## 问题描述

项目使用的 `django-ckeditor` 包含 CKEditor 4.22.1，该版本存在已知的安全问题。

## 解决方案

### 1. 升级 django-ckeditor

已将 `django-ckeditor` 升级到 6.0.0+ 版本，该版本包含安全修复。

### 2. 安全配置

在 `steambase/base_settings.py` 中添加了以下安全配置：

- 强制 JPEG 压缩
- 限制文件上传类型
- 禁用不安全的插件
- 启用内容过滤
- 按用户限制访问

### 3. 验证修复

运行以下命令验证警告是否消失：

```bash
source venv/bin/activate
python manage.py check
```

### 4. 后续步骤

1. **升级依赖**:
   ```bash
   source venv/bin/activate
   pip install -r requirements.txt
   ```

2. **收集静态文件**:
   ```bash
   python manage.py collectstatic --noinput
   ```

3. **重启服务**:
   ```bash
   docker-compose restart web
   ```

## 安全最佳实践

1. **定期更新**: 定期检查和更新 CKEditor 版本
2. **内容过滤**: 启用严格的内容过滤
3. **文件限制**: 限制上传文件的类型和大小
4. **用户权限**: 按用户限制编辑器访问权限

## 参考链接

- [CKEditor 4 安全公告](https://ckeditor.com/cke4/release/CKEditor-4.24.0-LTS)
- [django-ckeditor 文档](https://github.com/django-ckeditor/django-ckeditor)
- [CKEditor 4 LTS 支持](https://ckeditor.com/ckeditor-4-support/)

---

**修复时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**修复状态**: ✅ 已完成
'''
    
    try:
        note_file.parent.mkdir(parents=True, exist_ok=True)
        with open(note_file, 'w', encoding='utf-8') as f:
            f.write(note_content)
        
        logger.info(f"已创建安全说明文档: {note_file}")
        return True
        
    except Exception as e:
        logger.error(f"创建说明文档失败: {e}")
        return False

def main():
    import datetime
    
    logger.info("开始修复 CKEditor 安全警告...")
    
    try:
        # 1. 更新 requirements.txt
        if not update_requirements():
            logger.error("更新 requirements.txt 失败")
            return False
        
        # 2. 添加安全配置
        if not add_ckeditor_lts_config():
            logger.error("添加安全配置失败")
            return False
        
        # 3. 创建说明文档
        create_ckeditor_security_note()
        
        logger.info("🎉 CKEditor 安全警告修复完成！")
        logger.info("")
        logger.info("请执行以下步骤完成修复:")
        logger.info("1. source venv/bin/activate")
        logger.info("2. pip install -r requirements.txt")
        logger.info("3. python manage.py collectstatic --noinput")
        logger.info("4. python manage.py check  # 验证警告是否消失")
        logger.info("5. docker-compose restart web  # 重启服务")
        
        return True
        
    except Exception as e:
        logger.error(f"修复过程中出现错误: {e}")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
