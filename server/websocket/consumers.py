from channels.generic.websocket import AsyncJsonWebsocketConsumer

class BattleConsumer(AsyncJsonWebsocketConsumer):
    async def connect(self):
        room_id = self.scope['url_route']['kwargs'].get('room_id')
        self.group_name = f'battle_{room_id}'
        # 加入频道组
        await self.channel_layer.group_add(self.group_name, self.channel_name)
        await self.accept()

    async def disconnect(self, code):
        # 离开频道组
        await self.channel_layer.group_discard(self.group_name, self.channel_name)

    async def battle_message(self, event):
        # 接收 group_send 发送的消息
        message = event.get('message')
        if message:
            await self.send_json(message)
