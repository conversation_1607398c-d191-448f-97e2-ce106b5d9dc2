# 🤖 智能体操作规则文档

本文档为AI智能体提供系统理解、bug分析、文件创建和代码优化的全面指导规则。

## 📋 目录
- [系统架构理解](#系统架构理解)
- [文件组织规范](#文件组织规范)
- [开发环境和工具](#开发环境和工具)
- [代码规范和最佳实践](#代码规范和最佳实践)
- [Bug分析和修复流程](#bug分析和修复流程)
- [API和数据库操作](#api和数据库操作)
- [前端开发规范](#前端开发规范)
- [部署和运维](#部署和运维)

## 🏗️ 系统架构理解

### 核心技术栈
```
后端: Django 1.11 + DRF + MySQL + Redis + Celery
前端: Vue3 + Nuxt3 + TypeScript + Pinia + Socket.IO
部署: Docker + Docker Compose + Nginx + Supervisor
```

### 主要应用模块
```
server/
├── steambase/          # 核心配置和工具 (settings, urls, middleware)
├── authentication/     # 用户认证系统 (Steam OAuth, 权限管理)
├── articles/          # 内容管理系统 (CKEditor集成)
├── box/              # 开箱和对战系统 (WebSocket实时通信)
├── package/          # 物品包裹管理 (Steam物品集成)
├── market/           # 交易市场 (买卖系统)
├── charge/           # 充值系统 (多支付方式)
├── websocket/        # WebSocket服务 (实时通信)
├── thworker/         # 自定义任务队列框架
├── sitecfg/          # 网站配置管理
└── [游戏模块]/       # crash, roll, grab, lottery等
```

### 数据流架构
```
客户端 → Nginx → Django(Gunicorn) → MySQL/Redis
                    ↓
              WebSocket(Channels) → Redis Pub/Sub
                    ↓
              Celery Workers → 异步任务处理
```

## 📁 文件组织规范

### 严格禁止的文件位置
❌ **server/根目录不允许**:
- 临时脚本文件 (fix_*.py, temp_*.py)
- 工具脚本 (应放在scripts/tools/)
- 备份文件 (应放在backup/)
- 配置文件重复 (supervisord.conf已统一)

### 正确的文件放置位置
✅ **脚本文件分类**:
```
scripts/
├── tools/           # 工具脚本 (数据库工具, 修复脚本)
├── ckeditor/        # CKEditor相关脚本
├── fixes/           # 系统修复脚本
├── maintenance/     # 维护脚本
├── monitoring/      # 监控脚本
├── tests/          # 测试脚本
└── debug/          # 调试脚本
```

✅ **配置文件**:
- 主配置: 根目录 (supervisord.conf, docker-compose.yml)
- 应用配置: server/ (settings.py, gunicorn_conf.py)
- 环境配置: .env文件

✅ **文档文件**:
- 项目文档: docs/
- 应用文档: server/docs/ 或对应应用目录
- API文档: docs/api/

## 🛠️ 开发环境和工具

### 包管理器使用规则
**必须使用包管理器，禁止手动编辑配置文件**:
- Python: `pip install`, `pip uninstall`
- Node.js: `npm install`, `npm uninstall`
- 不要直接编辑 requirements.txt 或 package.json

### 虚拟环境
- Python虚拟环境: `server/venv/`
- Node.js依赖: `ui/node_modules/`
- 激活脚本: `server/activate_env.sh`

### 数据库操作
```bash
# Django迁移
cd server && python manage.py makemigrations
cd server && python manage.py migrate

# 数据库备份
cd server && python manage.py dumpdata > backup.json
```

## 📝 代码规范和最佳实践

### Django应用结构规范
```python
# 每个Django应用必须包含:
app_name/
├── __init__.py
├── admin.py           # 管理后台配置
├── apps.py           # 应用配置
├── models.py         # 数据模型
├── views.py          # 视图逻辑
├── urls.py           # URL路由
├── serializers.py    # DRF序列化器
├── business.py       # 业务逻辑层
├── interfaces.py     # 接口定义
├── tasks.py          # Celery任务
├── migrations/       # 数据库迁移
└── service/         # 服务层
```

### 时间处理规范
```python
# 时区配置 (settings.py)
TIME_ZONE = 'Asia/Shanghai'  # 北京时间
USE_TZ = True
CELERY_TIMEZONE = 'Asia/Shanghai'

# 时间工具函数使用
from steambase.utils import aware_datetime_to_timestamp

# 序列化器时间字段
class MySerializer(CustomFieldsSerializer):
    created_at = serializers.DateTimeField(format="%Y-%m-%d %H:%M:%S")
    created_at_ts = serializers.SerializerMethodField()

    def get_created_at_ts(self, obj):
        return aware_datetime_to_timestamp(obj.created_at)
```

### 国际化规范
```python
# 必须使用Django翻译函数
from django.utils.translation import gettext_lazy as _

# 模型字段 - 正确方式
verbose_name = _('Articles')
verbose_name_plural = _('Articles')

# 模型字段 - 错误方式
verbose_name = '文章'  # ❌ 禁止直接使用中文

# 翻译文件位置
server/locale/zh_hans/LC_MESSAGES/django.po

# API多语言字段规范
{
    "name": "默认名称",
    "name_en": "English Name",
    "name_zh_hans": "中文名称"
}
```

### API设计规范
```python
# DRF视图类命名
class GetArticleListView(APIView):  # 获取列表
class CreateArticleView(APIView):   # 创建
class UpdateArticleView(APIView):   # 更新
class DeleteArticleView(APIView):   # 删除

# URL命名规范
url(r'^api/articles/$', views.GetArticleListView.as_view()),
url(r'^api/articles/(?P<id>\d+)/$', views.GetArticleDetailView.as_view()),
```

## 🐛 Bug分析和修复流程

### 1. 系统分析步骤
```
1. 使用 codebase-retrieval 获取相关代码信息
2. 使用 git-commit-retrieval 查看历史修改
3. 检查日志文件: logs/ 目录
4. 分析错误类型: 前端/后端/数据库/网络
```

### 2. 常见问题定位
**数据库问题**:
- 检查 server/steambase/settings.py 数据库配置
- 查看迁移文件: app/migrations/
- 检查模型定义: app/models.py

**缓存问题**:
- Redis连接: server/steambase/redis_con.py
- 缓存配置: settings.py CACHES

**WebSocket问题**:
- 检查 server/websocket/ 和 server/steambase/routing.py
- 前端Socket连接: ui/plugins/socket.client.ts

### 3. 修复文件创建位置
- 修复脚本: `scripts/fixes/fix_[问题描述].py`
- 临时工具: `scripts/tools/temp_[功能].py`
- 测试脚本: `scripts/tests/test_[功能].py`

## 🔌 API和数据库操作

### 数据库查询优化
```python
# 使用select_related和prefetch_related
articles = Article.objects.select_related('author').prefetch_related('tags')

# 避免N+1查询
# ❌ 错误
for article in articles:
    print(article.author.name)  # 每次都查询数据库

# ✅ 正确  
articles = Article.objects.select_related('author')
for article in articles:
    print(article.author.name)  # 一次查询
```

### Celery任务使用
```python
# 任务定义位置: app/tasks.py
from celery import shared_task

@shared_task
def process_article_content(article_id):
    # 任务逻辑
    pass

# 任务调用
from app.tasks import process_article_content
process_article_content.delay(article_id)
```

### Redis缓存使用
```python
from django.core.cache import cache

# 缓存设置
cache.set('key', value, timeout=3600)
# 缓存获取
value = cache.get('key', default_value)
```

## 🎨 前端开发规范

### Vue3 + Nuxt3 结构
```
ui/
├── components/        # 组件库
├── composables/      # 组合式函数
├── pages/           # 页面路由
├── stores/          # Pinia状态管理
├── services/        # API服务
├── plugins/         # 插件
├── middleware/      # 中间件
└── types/          # TypeScript类型定义
```

### API调用规范
```typescript
// services/api.ts
export const articleApi = {
  getList: () => $fetch('/api/articles/'),
  getDetail: (id: number) => $fetch(`/api/articles/${id}/`),
  create: (data: ArticleData) => $fetch('/api/articles/', {
    method: 'POST',
    body: data
  })
}
```

### 状态管理
```typescript
// stores/article.ts
export const useArticleStore = defineStore('article', () => {
  const articles = ref<Article[]>([])
  
  const fetchArticles = async () => {
    articles.value = await articleApi.getList()
  }
  
  return { articles, fetchArticles }
})
```

## 🚀 部署和运维

### Docker命令
```bash
# 构建和启动
docker-compose up -d

# 查看日志
docker-compose logs -f [service_name]

# 重启服务
docker-compose restart [service_name]
```

### 进程管理
```bash
# Supervisor配置文件: supervisord.conf
# 重启服务
supervisorctl restart steambase_server
supervisorctl restart celery_worker
```

### 日志查看
```bash
# 应用日志
tail -f logs/gunicorn.log
tail -f logs/celery_worker.log

# 错误日志
tail -f logs/gunicorn_error.log
tail -f logs/worker_error.log
```

## ⚡ 性能优化建议

### 数据库优化
- 使用索引优化查询
- 避免N+1查询问题
- 使用数据库连接池

### 缓存策略
- Redis缓存热点数据
- 页面缓存和片段缓存
- CDN静态资源缓存

### 前端优化
- 组件懒加载
- 图片懒加载和压缩
- 代码分割和Tree Shaking

## 🎯 智能体操作流程

### 问题分析流程
```
1. 理解问题 → 使用 codebase-retrieval 获取相关代码
2. 历史分析 → 使用 git-commit-retrieval 查看类似修改
3. 环境检查 → 检查当前工作目录和文件结构
4. 制定方案 → 确定修改范围和影响
5. 执行修改 → 按规范创建/修改文件
6. 验证结果 → 运行测试确保功能正常
```

### 代码修改优先级
```
1. 🔴 高优先级: 安全漏洞、数据丢失风险
2. 🟡 中优先级: 功能bug、性能问题
3. 🟢 低优先级: 代码优化、文档更新
```

## 🔍 常见问题快速定位

### CKEditor相关问题
- 配置文件: `server/steambase/settings.py` (CKEDITOR_CONFIGS)
- 静态文件: `server/static/ckeditor/`
- 安全修复脚本: `scripts/ckeditor/`

### 国际化问题
- 翻译文件: `server/locale/zh_hans/LC_MESSAGES/django.po`
- 模型翻译: 各应用的 `translation.py`
- 前端翻译: `ui/locales/zh-hans.json`

### WebSocket连接问题
- 后端路由: `server/steambase/routing.py`
- 消费者: `server/websocket/consumers.py`
- 前端连接: `ui/plugins/socket.client.ts`

### 数据库迁移问题
```bash
# 检查迁移状态
cd server && python manage.py showmigrations

# 创建迁移
cd server && python manage.py makemigrations [app_name]

# 应用迁移
cd server && python manage.py migrate
```

## 📊 监控和调试

### 日志文件位置
```
logs/
├── gunicorn.log          # Web服务日志
├── gunicorn_error.log    # Web服务错误日志
├── celery_worker.log     # Celery任务日志
├── celery_beat.log       # 定时任务日志
├── supervisord.log       # 进程管理日志
└── [app_name]/          # 应用特定日志
```

### 性能监控
- Redis监控: `redis-cli monitor`
- 数据库监控: MySQL慢查询日志
- 应用监控: `server/monitor/` 模块

### 调试工具
- Django Debug Toolbar (开发环境)
- Python pdb调试器
- Vue DevTools (前端调试)

## 🛡️ 安全规范

### 敏感信息处理
- 环境变量: `.env` 文件 (不提交到版本控制)
- 密钥管理: Django SECRET_KEY
- 数据库密码: 环境变量配置

### 权限控制
- Django权限系统: `authentication/` 模块
- API权限: DRF权限类
- 前端路由守卫: `ui/middleware/auth.ts`

### 输入验证
- 后端验证: DRF序列化器
- 前端验证: `ui/utils/validation.ts`
- SQL注入防护: Django ORM

## 🔧 工具和命令速查

### Django管理命令
```bash
# 进入Django shell
cd server && python manage.py shell

# 创建超级用户
cd server && python manage.py createsuperuser

# 收集静态文件
cd server && python manage.py collectstatic

# 清理会话
cd server && python manage.py clearsessions
```

### 前端开发命令
```bash
# 开发模式
cd ui && npm run dev

# 构建生产版本
cd ui && npm run build

# 类型检查
cd ui && npm run type-check
```

### Docker操作
```bash
# 查看容器状态
docker-compose ps

# 进入容器
docker-compose exec [service_name] bash

# 查看容器资源使用
docker stats
```

---

**重要提醒**:
1. 始终先使用 `codebase-retrieval` 了解现有代码结构
2. 修改前使用 `git-commit-retrieval` 查看历史变更
3. 严格遵循文件组织规范
4. 优先使用包管理器而非手动编辑配置文件
5. 创建修复脚本而非直接修改核心文件
6. 所有修改都要考虑向后兼容性
7. 重要修改前先创建备份
