import logging
import threading
import random

from django.contrib.auth import get_user_model
from django.db import transaction
from django.core.paginator import Paginator
from django.core.cache import cache
from django.db.models import Q

from steambase.enums import RespCode

from authentication.interfaces import set_user_box_chance
from package.interfaces import set_all_item_price
from sitecfg.models import Support, Footer, FAQ, Banner, SEO, SiteConfig
from sitecfg.serializers import SupportSerializer, FooterSerializer, BannerSerializer, SupportCacheSerializer
from sitecfg.interfaces import get_roll_join_min_charge_limit
from package.models import ItemInfo

_logger = logging.getLogger(__name__)

USER = get_user_model()

# 文章相关函数已迁移到 articles 应用
# get_announce, get_announce_list, get_article_list, get_article_detail 等函数
# 请使用 articles 应用中的对应功能

def get_support(uid, fields_list, fields_detail):
    if uid:
        support = Support.objects.filter(enable=True, id=uid).first()
        if not support:
            return RespCode.InvalidParams.value, ('没有找到客服支持')
        support_data = SupportSerializer(support, fields=fields_detail).data
        resp = {
            'items': support_data
        }
        return RespCode.Succeed.value, resp
    else:
        support = Support.objects.filter(enable=True).order_by('sort')
        support_data = SupportSerializer(support, many=True, fields=fields_list).data
        resp = {
            'items': support_data
        }
        return RespCode.Succeed.value, resp


def get_support_cache(fields):
    cache_key = 'support_cache'
    cached_data = cache.get(cache_key)
    if cached_data is not None:
        return RespCode.Succeed.value, cached_data

    support = Support.objects.filter(enable=True).order_by('sort')
    support_data = SupportCacheSerializer(support, many=True, fields=fields).data
    resp = {
        'items': support_data
    }
    cache.set(cache_key, resp, 60 * 60 * 24)
    return RespCode.Succeed.value, resp


def get_footer(fields):
    footer = Footer.objects.filter(enable=True).order_by('no')
    footer_data = FooterSerializer(footer, many=True, fields=fields).data
    resp = {
        'items': footer_data
    }
    return RespCode.Succeed.value, resp


def get_faq(fields):
    faq = FAQ.objects.filter(enable=True).order_by('sort')
    faq_data = []
    for item in faq:
        faq_item = {}
        for field in fields:
            faq_item[field] = getattr(item, field, None)
        faq_data.append(faq_item)
    resp = {
        'items': faq_data
    }
    return RespCode.Succeed.value, resp


def get_banner(fields):
    banner = Banner.objects.filter(enable=True).order_by('order')
    banner_data = BannerSerializer(banner, many=True, fields=fields).data
    resp = {
        'items': banner_data
    }
    return RespCode.Succeed.value, resp


DEFAULT_SEO_DATA = {
    'url': "",
    'name': "CSGOSKINS开箱网",
    'subtitle': "全球最大的CSGO开箱网站",
    'title': "CSGOSKINS - 专业的CSGO皮肤开箱平台",
    'keywords': "CSGO,开箱,皮肤,交易,游戏",
    'description': "CSGOSKINS是专业的CSGO皮肤开箱平台，提供安全可靠的开箱服务",
    'enable': True,
    'icp': "",
    'news_enable': False,
    'agent': None
}


def get_seo_by_domain(domain):
    try:
        seo = SEO.objects.filter(url=domain, enable=True).first()
        if seo:
            return {
                'url': seo.url,
                'name': seo.name or DEFAULT_SEO_DATA['name'],
                'subtitle': seo.subtitle or DEFAULT_SEO_DATA['subtitle'],
                'title': seo.title or DEFAULT_SEO_DATA['title'],
                'keywords': seo.keywords or DEFAULT_SEO_DATA['keywords'],
                'description': seo.description or DEFAULT_SEO_DATA['description'],
                'enable': seo.enable,
                'icp': seo.icp or DEFAULT_SEO_DATA['icp'],
                'news_enable': seo.news_enable,
                'agent': seo.agent
            }
        else:
            return DEFAULT_SEO_DATA
    except Exception as e:
        _logger.error(f"Error getting SEO data for domain {domain}: {e}")
        return DEFAULT_SEO_DATA


def setup_set_item_price_worker():
    """设置物品价格工作器"""
    try:
        config = SiteConfig.objects.filter(key='price_api_chosen').first()
        if config and config.enable:
            set_all_item_price()
            _logger.info("Item price worker setup completed")
    except Exception as e:
        _logger.error(f"Error setting up item price worker: {e}")


def setup_set_user_box_chance_worker(chance_type):
    """设置用户开箱概率工作器"""
    try:
        if chance_type in ['a', 'b', 'c', 'd', 'e']:
            set_user_box_chance(chance_type)
            _logger.info(f"User box chance worker setup completed with type: {chance_type}")
    except Exception as e:
        _logger.error(f"Error setting up user box chance worker: {e}")


def get_site_config(key, default_value=None):
    """获取站点配置"""
    try:
        config = SiteConfig.objects.filter(key=key, enable=True).first()
        if config:
            return config.value
        return default_value
    except Exception as e:
        _logger.error(f"Error getting site config for key {key}: {e}")
        return default_value


def set_site_config(key, value, remark=""):
    """设置站点配置"""
    try:
        config, created = SiteConfig.objects.get_or_create(
            key=key,
            defaults={'value': value, 'remark': remark, 'enable': True}
        )
        if not created:
            config.value = value
            config.save()
        return True
    except Exception as e:
        _logger.error(f"Error setting site config for key {key}: {e}")
        return False


def get_maintenance_status():
    """获取维护状态"""
    return get_site_config('maintenance_mode', 'false') == 'true'


def get_online_users_count():
    """获取在线用户数"""
    base_count = int(get_site_config('online_users_base_count', '100'))
    random_add = random.randint(0, 50)
    return base_count + random_add


def get_roll_join_status():
    """获取roll房间加入状态"""
    return get_site_config('roll_bet_join', 'true') == 'true'


def get_charge_limit():
    """获取充值限制"""
    return float(get_site_config('charge_limit', '10000.0'))


def get_withdraw_limit():
    """获取提现限制"""
    return float(get_site_config('withdraw_limit', '1000.0'))


# 缓存相关函数
def clear_support_cache():
    """清除客服支持缓存"""
    cache.delete('support_cache')


def clear_all_cache():
    """清除所有缓存"""
    cache.clear()
    _logger.info("All cache cleared")


def setup_update_base_count_worker():
    """设置更新基础计数工作器"""
    th = threading.Thread(target=update_base_count_worker, args=())
    th.daemon = True
    th.start()
    _logger.info("Base count update worker started")


def update_base_count_worker():
    """更新基础计数工作器"""
    _logger.info('------------------------ 每小时随机时间更新基础数据 ------------------------')

    keys_to_update = ['users_base_count']

    for key in keys_to_update:
        try:
            # 查找具有指定键的 SiteConfig 对象
            config = SiteConfig.objects.get(key=key)
            # 获取当前值并增加 1 到 10 之间的随机数
            current_value = int(config.value)
            new_value = current_value + random.randint(1, 10)
            # 更新值
            config.value = str(new_value)
            config.save()
            _logger.info(f"Updated {key} from {current_value} to {new_value}")
        except SiteConfig.DoesNotExist:
            # 处理键不存在的情况
            _logger.warning(f"SiteConfig key '{key}' does not exist")
        except Exception as e:
            _logger.error(f"Error updating base count for key {key}: {e}")


# 统计相关函数
def get_site_statistics():
    """获取站点统计信息"""
    try:
        stats = {
            'total_users': USER.objects.count(),
            'active_users': USER.objects.filter(is_active=True).count(),
            'online_users': get_online_users_count(),
            'maintenance_mode': get_maintenance_status(),
        }
        return RespCode.Succeed.value, stats
    except Exception as e:
        _logger.error(f"Error getting site statistics: {e}")
        return RespCode.ServerError.value, "Failed to get statistics"


# 工具函数
def validate_domain(domain):
    """验证域名格式"""
    if not domain:
        return False
    # 简单的域名验证
    import re
    pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
    return bool(re.match(pattern, domain))


def get_client_ip(request):
    """获取客户端IP地址"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


def is_mobile_request(request):
    """判断是否为移动端请求"""
    user_agent = request.META.get('HTTP_USER_AGENT', '').lower()
    mobile_keywords = ['mobile', 'android', 'iphone', 'ipad', 'phone']
    return any(keyword in user_agent for keyword in mobile_keywords)
