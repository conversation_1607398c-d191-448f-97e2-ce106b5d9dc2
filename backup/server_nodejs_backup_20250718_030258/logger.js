/**
 * Created by Administrator on 2017/7/21.
 */

const argv = require('yargs').option('debug', {boolean: false}).argv;

var logger = null;
const tracer = require('tracer');
const logLevel = argv.log || 'info';
tracer.setLevel(logLevel);
if (argv.debug) {
    logger = tracer.console();
} else {
  logger = tracer.dailyfile({root:'./logs/', maxLogFiles: 10, allLogsFileName: 'websocket'});
}

module.exports = logger;
