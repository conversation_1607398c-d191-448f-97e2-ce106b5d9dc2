useless-super-delegation:191:UselessSuper.equivalent_params:Useless super delegation in method 'equivalent_params'
useless-super-delegation:194:UselessSuper.equivalent_params_1:Useless super delegation in method 'equivalent_params_1'
useless-super-delegation:197:UselessSuper.equivalent_params_2:Useless super delegation in method 'equivalent_params_2'
useless-super-delegation:200:UselessSuper.equivalent_params_3:Useless super delegation in method 'equivalent_params_3'
useless-super-delegation:203:UselessSuper.equivalent_params_4:Useless super delegation in method 'equivalent_params_4'
useless-super-delegation:206:UselessSuper.equivalent_params_5:Useless super delegation in method 'equivalent_params_5'
useless-super-delegation:209:UselessSuper.equivalent_params_6:Useless super delegation in method 'equivalent_params_6'
useless-super-delegation:212:UselessSuper.with_default_argument:Useless super delegation in method 'with_default_argument'
useless-super-delegation:216:UselessSuper.without_default_argument:Useless super delegation in method 'without_default_argument'
useless-super-delegation:219:UselessSuper.with_default_argument_none:Useless super delegation in method 'with_default_argument_none'
useless-super-delegation:223:UselessSuper.with_default_argument_int:Useless super delegation in method 'with_default_argument_int'
useless-super-delegation:226:UselessSuper.with_default_argument_tuple:Useless super delegation in method 'with_default_argument_tuple'
useless-super-delegation:229:UselessSuper.__init__:Useless super delegation in method '__init__'
useless-super-delegation:232:UselessSuper.with_default_arg:Useless super delegation in method 'with_default_arg'
useless-super-delegation:235:UselessSuper.with_default_arg_bis:Useless super delegation in method 'with_default_arg_bis'
useless-super-delegation:238:UselessSuper.with_default_arg_ter:Useless super delegation in method 'with_default_arg_ter'
useless-super-delegation:241:UselessSuper.with_default_arg_quad:Useless super delegation in method 'with_default_arg_quad'
