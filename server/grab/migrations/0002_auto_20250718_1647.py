# Generated by Django 3.2.25 on 2025-07-18 08:47

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('package', '0023_auto_20250718_1628'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('grab', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='grabbet',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='grabbet',
            name='room',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='grab.grabroom'),
        ),
        migrations.AlterField(
            model_name='grabbet',
            name='user',
            field=models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='grabcard',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='grabhistory',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='grabhistory',
            name='room',
            field=models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, to='grab.grabroom'),
        ),
        migrations.AlterField(
            model_name='grabhistory',
            name='user',
            field=models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='grabroom',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='grabroom',
            name='item_info',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='package.iteminfo'),
        ),
    ]
