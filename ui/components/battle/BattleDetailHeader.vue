<!-- components/battle/BattleDetailHeader.vue -->
<template>
  <div class="battle-detail-header">
    <!-- 背景装饰 -->
    <div class="header-background">
      <div class="bg-gradient-primary absolute inset-0 opacity-10"></div>
      <div class="absolute top-4 right-4 w-32 h-32 bg-primary/5 rounded-full blur-3xl"></div>
      <div class="absolute bottom-4 left-4 w-24 h-24 bg-secondary/5 rounded-full blur-2xl"></div>
    </div>

    <div class="container mx-auto px-4 relative z-10">
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <!-- 左侧：房间基本信息 -->
        <div class="flex items-center gap-4">
          <!-- 返回按钮 -->
          <button 
            @click="$router.back()" 
            class="btn-icon-secondary"
            :title="t('common.back')"
          >
            <Icon name="heroicons:arrow-left" class="w-5 h-5" />
          </button>

          <!-- 房间信息 -->
          <div class="room-info">
            <div class="flex items-center gap-3 mb-2">
              <h1 class="text-2xl font-bold text-white">
                {{ t('battle.detail.room_title') }} #{{ battleData?.short_id }}
              </h1>
              <div class="status-badge" :class="getStatusClass(battleData?.state)">
                {{ getStatusText(battleData?.state) }}
              </div>
            </div>
            
            <div class="flex items-center gap-4 text-sm text-gray-300">
              <span class="flex items-center gap-1">
                <Icon name="heroicons:users" class="w-4 h-4" />
                {{ battleData?.joiner_count || 0 }}/{{ battleData?.max_joiner || 4 }}
              </span>
              <span class="flex items-center gap-1">
                <Icon name="heroicons:currency-dollar" class="w-4 h-4" />
                ${{ (battleData?.price || 0).toFixed(2) }}
              </span>
              <span class="flex items-center gap-1">
                <Icon name="heroicons:clock" class="w-4 h-4" />
                {{ formatTime(battleData?.create_time) }}
              </span>
            </div>
          </div>
        </div>

        <!-- 右侧：回合进度和操作 -->
        <div class="flex items-center gap-4">
          <!-- 回合进度 -->
          <div class="round-progress">
            <div class="flex items-center gap-2 mb-1">
              <span class="text-sm text-gray-400">{{ t('battle.detail.round_progress') }}</span>
              <span class="text-lg font-bold text-primary">
                {{ currentRound + 1 }}/{{ totalRounds }}
              </span>
            </div>
            <div class="progress-bar">
              <div 
                class="progress-fill" 
                :style="{ width: `${((currentRound + 1) / totalRounds) * 100}%` }"
              ></div>
            </div>
          </div>

          <!-- 房间操作 -->
          <div class="room-actions flex items-center gap-2">
            <!-- 复制房间ID -->
            <button 
              @click="copyRoomId" 
              class="btn-icon-secondary"
              :title="t('battle.detail.copy_room_id')"
            >
              <Icon name="heroicons:clipboard-document" class="w-4 h-4" />
            </button>
            
            <!-- 分享按钮 -->
            <button 
              @click="shareRoom" 
              class="btn-icon-secondary"
              :title="t('battle.detail.share_room')"
            >
              <Icon name="heroicons:share" class="w-4 h-4" />
            </button>

            <!-- 音效控制 -->
            <button 
              @click="toggleSound" 
              class="btn-icon-secondary"
              :title="soundEnabled ? t('common.sound_off') : t('common.sound_on')"
            >
              <Icon :name="soundEnabled ? 'heroicons:speaker-wave' : 'heroicons:speaker-x-mark'" class="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      <!-- 箱子信息展示 -->
      <div class="cases-info mt-6">
        <div class="flex items-center gap-2 mb-3">
          <Icon name="heroicons:cube" class="w-5 h-5 text-primary" />
          <h3 class="text-lg font-semibold text-white">{{ t('battle.detail.battle_cases') }}</h3>
        </div>
        
        <div class="cases-grid grid grid-cols-2 md:grid-cols-4 gap-3">
          <div 
            v-for="(round, index) in battleData?.rounds || []" 
            :key="index"
            class="case-card"
            :class="{ 'active': index === currentRound }"
          >
            <div class="case-image-container">
              <img 
                :src="round.case?.cover || '/images/case-placeholder.svg'" 
                :alt="round.case?.name"
                class="case-image"
                @error="handleImageError"
              >
              <div class="case-overlay">
                <div class="round-number">#{{ index + 1 }}</div>
              </div>
            </div>
            <div class="case-info">
              <div class="case-name">{{ getLocalizedName(round.case) }}</div>
              <div class="case-price">${{ (round.case?.price || 0).toFixed(2) }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { BattleDetail } from '~/types/battle'

interface Props {
  battleData: BattleDetail | null
  currentRound: number
  totalRounds: number
  soundEnabled: boolean
}

interface Emits {
  (e: 'toggle-sound'): void
  (e: 'copy-room-id'): void
  (e: 'share-room'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const { t } = useI18n()

// 状态相关方法
const getStatusClass = (state: number | undefined) => {
  switch (state) {
    case 2: return 'status-waiting'
    case 4: return 'status-full' 
    case 5: return 'status-running'
    case 11: return 'status-finished'
    default: return 'status-unknown'
  }
}

const getStatusText = (state: number | undefined) => {
  switch (state) {
    case 2: return t('battle.status.waiting')
    case 4: return t('battle.status.full')
    case 5: return t('battle.status.running')
    case 11: return t('battle.status.finished')
    default: return t('battle.status.unknown')
  }
}

// 工具方法
const formatTime = (timeString: string | undefined) => {
  if (!timeString) return ''
  return new Date(timeString).toLocaleString()
}

const getLocalizedName = (item: any) => {
  if (!item) return ''
  const { locale } = useI18n()
  return locale.value === 'zh-hans' 
    ? (item.name_zh_hans || item.name_zh || item.name || 'Unknown')
    : (item.name_en || item.name || 'Unknown')
}

const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement
  target.src = '/images/case-placeholder.svg'
}

// 事件处理
const toggleSound = () => emit('toggle-sound')
const copyRoomId = () => emit('copy-room-id')
const shareRoom = () => emit('share-room')
</script>

<style lang="scss" scoped>
.battle-detail-header {
  @apply relative bg-gray-900/80 border-b border-gray-800/50 backdrop-blur-sm;
  padding: 1.5rem 0;
}

.header-background {
  @apply absolute inset-0 overflow-hidden;
}

.room-info {
  .status-badge {
    @apply px-3 py-1 rounded-full text-xs font-medium;
    
    &.status-waiting {
      @apply bg-yellow-500/20 text-yellow-400 border border-yellow-500/30;
    }
    
    &.status-full {
      @apply bg-green-500/20 text-green-400 border border-green-500/30;
    }
    
    &.status-running {
      @apply bg-blue-500/20 text-blue-400 border border-blue-500/30;
      animation: pulse 2s infinite;
    }
    
    &.status-finished {
      @apply bg-gray-500/20 text-gray-400 border border-gray-500/30;
    }
  }
}

.round-progress {
  @apply text-center;
  
  .progress-bar {
    @apply w-24 h-2 bg-gray-700/50 rounded-full overflow-hidden;
    
    .progress-fill {
      @apply h-full bg-gradient-to-r from-primary to-secondary transition-all duration-500;
    }
  }
}

.btn-icon-secondary {
  @apply w-10 h-10 bg-gray-800/50 hover:bg-gray-700/50 border border-gray-700/30 
         rounded-lg flex items-center justify-center text-gray-300 hover:text-white
         transition-all duration-200;
}

.cases-grid {
  .case-card {
    @apply bg-gray-800/30 border border-gray-700/30 rounded-lg p-3 transition-all duration-200;
    
    &:hover {
      @apply border-gray-600/50 transform scale-105;
    }
    
    &.active {
      @apply border-primary/50 bg-primary/5;
      box-shadow: 0 0 20px rgba(var(--color-primary-rgb), 0.2);
    }
    
    .case-image-container {
      @apply relative mb-2;
      
      .case-image {
        @apply w-full h-16 object-cover rounded border border-gray-600/30;
      }
      
      .case-overlay {
        @apply absolute inset-0 bg-black/20 rounded flex items-center justify-center
               opacity-0 hover:opacity-100 transition-opacity duration-200;
        
        .round-number {
          @apply text-white font-bold text-sm;
        }
      }
    }
    
    .case-info {
      .case-name {
        @apply text-white text-sm font-medium truncate mb-1;
      }
      
      .case-price {
        @apply text-primary text-xs font-bold;
      }
    }
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}
</style> 