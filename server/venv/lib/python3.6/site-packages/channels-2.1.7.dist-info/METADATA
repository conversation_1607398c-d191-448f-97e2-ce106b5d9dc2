Metadata-Version: 2.1
Name: channels
Version: 2.1.7
Summary: Brings async, event-driven capabilities to Django. Django 1.11 and up only.
Home-page: http://github.com/django/channels
Author: Django Software Foundation
Author-email: <EMAIL>
License: BSD
Platform: UNKNOWN
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Web Environment
Classifier: Framework :: Django
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Topic :: Internet :: WWW/HTTP
Requires-Python: >=3.5
Requires-Dist: Django (>=1.11)
Requires-Dist: asgiref (~=2.3)
Requires-Dist: daphne (~=2.2)
Provides-Extra: tests
Requires-Dist: pytest (~=3.6.0) ; extra == 'tests'
Requires-Dist: pytest-django (~=3.1) ; extra == 'tests'
Requires-Dist: pytest-asyncio (~=0.8) ; extra == 'tests'
Requires-Dist: async-generator (~=1.8) ; extra == 'tests'
Requires-Dist: async-timeout (~=2.0) ; extra == 'tests'
Requires-Dist: coverage (~=4.4) ; extra == 'tests'

UNKNOWN


