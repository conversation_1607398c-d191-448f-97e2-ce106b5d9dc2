<!-- components/battle/BattlePlayer.vue -->
<template>
  <!-- 空玩家位置 -->
  <div v-if="player.isEmpty" class="battle-player bg-gray-900/80 rounded-xl border border-gray-800/30 backdrop-blur-md shadow-xl p-6 relative overflow-hidden transition-all duration-300 border-dashed">
    <div class="text-center py-12">
      <div class="w-16 h-16 bg-gray-800/50 rounded-full mx-auto mb-4 flex items-center justify-center">
        <i class="fa fa-user-plus text-gray-500 text-xl"></i>
      </div>
      <p class="text-gray-500 mb-4">{{ t('battle.player.waiting_for_player') }}</p>
      <button
        v-if="canJoin"
        @click="handleJoin"
        class="battle-btn-primary px-6 py-3"
      >
        <i class="fa fa-user-plus mr-2"></i>
        {{ t('battle.player.join_as_player') }}
      </button>
    </div>
  </div>

  <!-- 正常玩家 -->
  <div v-else
    class="battle-player bg-gray-900/80 rounded-xl border backdrop-blur-md shadow-xl p-6 relative overflow-hidden transition-all duration-300"
    :class="[
      {
        'border-yellow-400/50 shadow-yellow-400/20': player.isWinner,
        'border-primary/50 animate-glow-pulse': isRolling,
        'border-gray-800/30': !player.isWinner && !isRolling
      }
    ]"
  >
    <!-- ...existing code... -->
    <!-- 装饰性光效 -->
    <div class="absolute top-0 left-0 w-16 h-16 rounded-full blur-2xl bg-primary/5 animate-pulse-slow"></div>
    <div class="absolute bottom-0 right-0 w-16 h-16 rounded-full blur-2xl bg-secondary/5 animate-pulse-slow"></div>
    
    <!-- 获胜者皇冠 -->
    <div v-if="player.isWinner" class="absolute -top-3 -left-3 z-20">
      <i class="fa fa-crown text-yellow-400 text-2xl animate-pulse"></i>
    </div>

    <!-- 玩家信息 -->
    <div class="flex items-center justify-between mb-6 relative z-10">
      <div class="flex items-center gap-3">
        <div class="relative">
          <img
            :src="player.avatar || '/images/default-avatar.png'"
            :alt="player.name"
            class="w-12 h-12 rounded-full border-2 object-cover transition-all duration-300"
            :class="player.isWinner ? 'border-yellow-400' : 'border-gray-600'"
            @error="handleAvatarError"
          >
          <!-- 房主标识 -->
          <div
            v-if="player.isOwner"
            class="absolute -top-1 -right-1 w-4 h-4 bg-primary rounded-full flex items-center justify-center"
          >
            <i class="fa fa-crown text-black text-xs"></i>
          </div>
          <!-- 在线状态 -->
          <div
            v-if="player.isOnline"
            class="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-gray-900"
          ></div>
        </div>
        
        <div>
          <h4 class="text-white font-semibold">{{ player.name || t('battle.unknown_player') }}</h4>
          <div class="flex items-center gap-2 text-sm">
            <span v-if="player.isOwner" class="text-primary">{{ t('battle.owner') }}</span>
            <span v-else class="text-white/60">{{ t('battle.player') }}</span>
            <span v-if="player.isWinner" class="text-yellow-400">• {{ t('battle.winner') }}</span>
          </div>
        </div>
      </div>

      <!-- 玩家统计 -->
      <div class="text-right">
        <div class="text-emerald-400 font-bold text-lg">${{ player.totalValue?.toFixed(2) || '0.00' }}</div>
        <div class="text-white/60 text-sm">{{ t('battle.total_value') }}</div>
      </div>
    </div>

    <!-- 开箱动画区域 -->
    <div v-if="isRolling || player.items?.length === 0" class="mb-4">
      <div v-if="isRolling" class="relative">
        <BattleRollAnimation
          ref="rollAnimationRef"
          :items="availableItems"
          :auto-start="false"
          :duration="3000"
          :winner-item="pendingWinnerItem"
          @animation-start="handleAnimationStart"
          @animation-complete="handleAnimationComplete"
        />
      </div>
      
      <!-- 等待状态 -->
      <div v-else-if="player.items?.length === 0" class="text-center py-8">
        <div class="bg-gray-800/40 rounded-lg border border-dashed border-gray-700 p-6">
          <i class="fa fa-package text-gray-500 text-2xl mb-2"></i>
          <p class="text-gray-500 text-sm">{{ t('battle.waiting_for_items') }}</p>
        </div>
      </div>
    </div>

    <!-- 玩家物品展示 -->
    <div v-if="player.items && player.items.length > 0" class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3 mb-4">
      <div
        v-for="(item, index) in player.items"
        :key="index"
        class="item-card bg-gray-800/50 rounded-lg p-3 border border-gray-700/30 hover:border-primary/30 transition-all duration-300 group"
        :class="{ 'animate-pulse border-yellow-400/50': item.isHighlighted }"
      >
        <!-- 稀有度指示条 -->
        <div
          class="w-full h-1 rounded-full mb-2"
          :style="{ backgroundColor: item.rarity_color || '#6b7280' }"
        ></div>
        
        <!-- 物品图片 -->
        <div class="h-16 bg-gradient-to-br from-gray-700/30 to-gray-800/30 rounded-lg flex items-center justify-center mb-2 group-hover:scale-105 transition-transform">
          <img
            :src="item.image"
            :alt="item.name"
            class="w-12 h-12 object-contain"
            @error="handleImageError"
          >
        </div>
        
        <!-- 物品信息 -->
        <div class="space-y-1">
          <p class="text-xs text-white font-medium truncate">{{ getItemDisplayName(item) }}</p>
          <p class="text-xs text-white/60">{{ getItemCondition(item) }}</p>
          <p class="text-xs text-emerald-400 font-bold">${{ item.price?.toFixed(2) || '0.00' }}</p>
        </div>
      </div>
    </div>

    <!-- 空座位状态 -->
    <div v-if="canJoin && !player.items?.length" class="text-center py-6">
      <button
        @click="handleJoin"
        class="battle-btn-primary px-6 py-3"
      >
        <i class="fa fa-user-plus mr-2"></i>
        {{ t('battle.join_as_player') }}
      </button>
    </div>

    <!-- 玩家操作按钮 -->
    <div v-if="showActions" class="mt-4 flex gap-2 justify-center">
      <button
        v-if="canKick && !player.isOwner"
        @click="handleKick"
        class="battle-btn-danger text-xs px-3 py-1"
      >
        <i class="fa fa-user-times mr-1"></i>
        {{ t('battle.kick') }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import BattleRollAnimation from './BattleRollAnimation.vue'

// Props & Emits
interface BattlePlayer {
  uid: string
  name: string
  avatar: string
  isOwner: boolean
  isWinner: boolean
  items: any[]
  totalValue: number
  isOnline?: boolean
}

interface CaseItem {
  id: string
  name: string
  image: string
  price: number
  rarity_color?: string
  isHighlighted?: boolean
}

interface Props {
  player: BattlePlayer
  currentRound?: number
  totalRounds?: number
  isRolling?: boolean
  availableItems?: CaseItem[]
  pendingWinnerItem?: CaseItem | null
  canJoin?: boolean
  canKick?: boolean
  showActions?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  currentRound: 0,
  totalRounds: 0,
  isRolling: false,
  availableItems: () => [],
  pendingWinnerItem: null,
  canJoin: false,
  canKick: false,
  showActions: false
})

const emit = defineEmits<{
  join: []
  kick: [playerId: string]
  rollComplete: [playerId: string, item: CaseItem]
  rollStart: [playerId: string]
}>()

// 组合函数
const { t } = useI18n()

// 响应式状态
const rollAnimationRef = ref<InstanceType<typeof BattleRollAnimation>>()

// 方法
const handleJoin = () => {
  emit('join')
}

const handleKick = () => {
  emit('kick', props.player.uid)
}

const handleAnimationStart = () => {
  emit('rollStart', props.player.uid)
}

const handleAnimationComplete = (item: CaseItem) => {
  emit('rollComplete', props.player.uid, item)
}

const handleAvatarError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/images/default-avatar.png'
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/images/default-item.png'
}

const getItemDisplayName = (item: CaseItem): string => {
  if (!item?.name) return 'Unknown Item'
  
  let name = item.name.replace('StatTrak™ ', '')
  
  if (name.length > 15) {
    name = name.substring(0, 12) + '...'
  }
  
  return name
}

const getItemCondition = (item: CaseItem): string => {
  if (!item?.name) return ''
  
  const name = item.name
  
  // 提取品相信息
  if (name.includes('Factory New') || name.includes('崭新出厂')) return 'FN'
  if (name.includes('Minimal Wear') || name.includes('略有磨损')) return 'MW'
  if (name.includes('Field-Tested') || name.includes('久经沙场')) return 'FT'
  if (name.includes('Well-Worn') || name.includes('破损不堪')) return 'WW'
  if (name.includes('Battle-Scarred') || name.includes('战痕累累')) return 'BS'
  
  return ''
}

// 暴露滚轮动画方法
const startRoll = () => {
  if (rollAnimationRef.value && rollAnimationRef.value.startRoll) {
    rollAnimationRef.value.startRoll()
  }
}

const resetRoll = () => {
  if (rollAnimationRef.value && rollAnimationRef.value.reset) {
    rollAnimationRef.value.reset()
  }
}

defineExpose({
  startRoll,
  resetRoll
})
</script>

<style lang="scss" scoped>
.battle-player {
  .item-card {
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
    }
    
    &.animate-pulse {
      animation: item-highlight 2s ease-in-out infinite;
    }
  }
  
  .empty-slot {
    aspect-ratio: 1;
    min-height: 80px;
  }
}

// 高亮动画
@keyframes item-highlight {
  0%, 100% {
    border-color: rgba(var(--color-primary-rgb), 0.3);
    box-shadow: 0 0 10px rgba(var(--color-primary-rgb), 0.2);
  }
  50% {
    border-color: rgba(var(--color-primary-rgb), 0.6);
    box-shadow: 0 0 20px rgba(var(--color-primary-rgb), 0.4);
  }
}

// 脉冲光环动画
.animate-glow-pulse {
  animation: glow-pulse 2s ease-in-out infinite;
}

@keyframes glow-pulse {
  0%, 100% {
    box-shadow: 0 0 10px rgba(var(--color-primary-rgb), 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(var(--color-primary-rgb), 0.6);
  }
}

// 按钮样式
.battle-btn-primary {
  @apply inline-flex items-center justify-center gap-2 px-4 py-2 rounded-lg font-semibold text-black;
  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(var(--color-primary-rgb), 0.3);
  }
}

// 按钮样式
.battle-btn-danger {
  @apply inline-flex items-center justify-center gap-1 px-3 py-1 rounded text-red-400 border border-red-400/30 bg-red-900/20;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.5);
  }
}
</style>
