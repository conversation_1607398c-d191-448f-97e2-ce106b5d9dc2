<template>
  <div
    v-if="modelValue"
    class="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
    @click.self="closeDialog"
  >
    <div class="bg-gray-900/95 rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto border border-gray-800/50 shadow-2xl">
      <!-- 头部 -->
      <div class="p-6 border-b border-gray-800/50 flex items-center justify-between">
        <h3 class="text-xl font-bold text-white">
          {{ $t('battle.create_room', '创建房间') }}
        </h3>
        <button
          @click="closeDialog"
          class="text-white/60 hover:text-white transition-colors"
        >
          <i class="fas fa-times text-xl"></i>
        </button>
      </div>

      <div class="p-6 space-y-6">
        <!-- 玩家数量选择 -->
        <div>
          <label class="block text-white/80 mb-3 text-sm font-medium">
            {{ $t('battle.player_count', '玩家数量') }}
          </label>
          <div class="grid grid-cols-3 gap-3">
            <button
              v-for="option in [2, 3, 4]"
              :key="option"
              @click="maxJoiner = option"
              :class="[
                'py-3 px-3 rounded-lg text-center transition-all duration-300 font-medium text-sm',
                maxJoiner === option
                  ? 'bg-blue-600 text-white shadow-lg'
                  : 'bg-gray-800/80 text-white/80 hover:bg-gray-700/80 border border-gray-700/50'
              ]"
            >
              {{ $t('battle.x_players', { count: option }) }}
            </button>
          </div>
        </div>

        <!-- 筛选和排序 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <!-- 搜索框 -->
          <div class="relative">
            <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-white/40"></i>
            <input
              v-model="searchQuery"
              type="text"
              :placeholder="$t('battle.search_cases', '搜索箱子...')"
              class="w-full bg-gray-800/50 border border-gray-700/50 rounded-lg py-2.5 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-blue-500/50 text-sm transition-all text-white"
            />
          </div>

          <!-- 分类筛选 -->
          <select
            v-model="categoryFilter"
            class="w-full bg-gray-800/50 border border-gray-700/50 rounded-lg py-2.5 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500/50 text-sm transition-all text-white"
          >
            <option value="all">{{ $t('battle.filters.all', '全部') }}</option>
            <option value="hot">{{ $t('battle.filters.hot', '热门') }}</option>
            <option value="new">{{ $t('battle.filters.new', '最新') }}</option>
            <option value="discount">{{ $t('battle.filters.discount', '折扣') }}</option>
          </select>

          <!-- 排序方式 -->
          <select
            v-model="sortBy"
            class="w-full bg-gray-800/50 border border-gray-700/50 rounded-lg py-2.5 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500/50 text-sm transition-all text-white"
          >
            <option value="name">{{ $t('battle.sort.name', '名称') }}</option>
            <option value="price_asc">{{ $t('battle.sort.price_asc', '价格升序') }}</option>
            <option value="price_desc">{{ $t('battle.sort.price_desc', '价格降序') }}</option>
          </select>
        </div>

        <!-- 已选择的箱子总览 -->
        <div v-if="selectedCases.length > 0" class="bg-gray-800/30 rounded-lg p-4 border border-gray-700/30">
          <div class="flex items-center justify-between mb-3">
                         <h4 class="text-white font-medium">
               {{ $t('battle.selected_cases_count', { count: selectedCases.length }) }}
             </h4>
            <button
              @click="clearSelection"
              class="text-white/60 hover:text-white text-sm transition-colors"
            >
              <i class="fas fa-trash mr-1"></i>
              {{ $t('common.clear', '清空') }}
            </button>
          </div>
          
          <div class="flex flex-wrap gap-2 mb-3">
            <div
              v-for="caseKey in uniqueSelectedCases"
              :key="caseKey"
              class="flex items-center gap-2 bg-gray-700/50 rounded-lg px-3 py-2 text-sm"
            >
              <img
                :src="getCaseByKey(caseKey)?.cover"
                :alt="getLocalizedCaseName(getCaseByKey(caseKey))"
                class="w-6 h-6 object-contain"
                @error="handleImageError"
              >
              <span class="text-white truncate max-w-24">
                {{ getLocalizedCaseName(getCaseByKey(caseKey)) }}
              </span>
              <!-- 显示数量 -->
              <span v-if="getCaseCount(caseKey) > 1" class="text-blue-400 font-bold text-xs bg-blue-500/20 px-2 py-1 rounded">
                x{{ getCaseCount(caseKey) }}
              </span>
              <span class="text-emerald-400 font-bold">
                ${{ ((getCaseByKey(caseKey)?.price || 0) * getCaseCount(caseKey)).toFixed(2) }}
              </span>
              <!-- 减少数量按钮 -->
              <button
                @click="removeCase(caseKey)"
                class="text-white/60 hover:text-yellow-400 transition-colors"
                :title="$t('battle.remove_one', '移除一个')"
              >
                <i class="fas fa-minus"></i>
              </button>
              <!-- 移除全部按钮 -->
              <button
                @click="removeAllCases(caseKey)"
                class="text-white/60 hover:text-red-400 transition-colors"
                :title="$t('battle.remove_all', '移除全部')"
              >
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>
          
          <div class="flex items-center justify-between text-sm">
            <span class="text-white/80">{{ $t('battle.total_price', '总价格') }}:</span>
            <span class="text-xl font-bold text-emerald-400">${{ totalPrice.toFixed(2) }}</span>
          </div>
        </div>

        <!-- 箱子选择 -->
        <div>
          <div class="flex items-center justify-between mb-4">
            <label class="text-white/80 text-sm font-medium">
              {{ $t('battle.select_cases', '选择箱子') }}
            </label>
            <div class="text-xs text-white/60">
              {{ selectedCases.length }} / 4
            </div>
          </div>

          <!-- 箱子列表 -->
          <div class="max-h-80 overflow-y-auto pr-2 scrollbar-thin">
            <div v-if="battleStore.loading.cases" class="grid grid-cols-2 md:grid-cols-4 gap-3">
              <div
                v-for="i in 8"
                :key="i"
                class="h-24 bg-gray-700 rounded-lg animate-pulse"
              ></div>
            </div>
            
            <div v-else-if="filteredCases.length === 0" class="text-center py-8">
              <p class="text-gray-400">{{ $t('battle.no_cases_found', '未找到符合条件的箱子') }}</p>
            </div>
            
            <div v-else class="grid grid-cols-2 md:grid-cols-4 gap-3">
              <div
                v-for="caseItem in filteredCases"
                :key="caseItem.case_key"
                class="relative bg-gray-800/50 border border-gray-700/30 rounded-lg overflow-hidden cursor-pointer transition-all duration-200 hover:border-blue-500/30"
                :class="{ 
                  'border-blue-500/50 bg-blue-500/10': getCaseCount(caseItem.case_key) > 0,
                  'opacity-50 cursor-not-allowed': selectedCases.length >= 4 && getCaseCount(caseItem.case_key) === 0
                }"
                @click="toggleCaseSelection(caseItem)"
              >
                <!-- 折扣标签 -->
                <div v-if="caseItem.discount && caseItem.discount < 100" class="absolute top-2 left-2 bg-red-500 text-white text-xs px-2 py-1 rounded z-10">
                  -{{ Math.round(100 - caseItem.discount) }}%
                </div>

                <!-- 图片 -->
                <div class="h-20 bg-gradient-to-br from-gray-700/30 to-gray-800/30 relative flex items-center justify-center">
                  <img
                    :src="caseItem.cover"
                    :alt="getLocalizedCaseName(caseItem)"
                    class="w-12 h-12 object-contain"
                    @error="handleImageError"
                  >
                  <!-- 选中标识和数量 -->
                  <div
                    v-if="getCaseCount(caseItem.case_key) > 0"
                    class="absolute top-2 right-2 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center"
                  >
                    <span class="text-white text-xs font-bold">{{ getCaseCount(caseItem.case_key) }}</span>
                  </div>
                </div>

                <!-- 信息 -->
                <div class="p-3">
                  <p class="text-white text-xs font-medium truncate mb-1">
                    {{ getLocalizedCaseName(caseItem) }}
                  </p>
                  <div class="flex items-center justify-between">
                    <div class="flex flex-col">
                      <span
                        v-if="caseItem.discount && caseItem.discount < 100"
                        class="text-gray-400 text-xs line-through"
                      >
                        ${{ caseItem.price.toFixed(2) }}
                      </span>
                      <span class="text-emerald-400 text-sm font-bold">
                        ${{ getDiscountPrice(caseItem).toFixed(2) }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 错误信息显示 -->
        <div 
          v-if="showError" 
          class="mb-4 p-4 bg-red-500/10 border border-red-500/30 rounded-lg flex items-center gap-3 animate-in slide-in-from-top-2 duration-300"
        >
          <i class="fas fa-exclamation-triangle text-red-400 text-lg"></i>
          <div class="flex-1">
            <p class="text-red-400 font-medium">{{ errorMessage }}</p>
          </div>
          <button 
            @click="hideError"
            class="text-red-400/60 hover:text-red-400 transition-colors"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>

        <!-- 操作按钮 -->
        <div class="flex gap-3 pt-4 border-t border-gray-800/30">
          <button 
            @click="closeDialog"
            class="flex-1 px-6 py-3 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors duration-200"
          >
            {{ $t('common.cancel', '取消') }}
          </button>
          <button 
            @click="createRoom"
            :disabled="!canCreateRoom || isCreating"
            class="flex-1 px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <i v-if="!isCreating" class="fas fa-plus mr-2"></i>
            <i v-else class="fas fa-spinner fa-spin mr-2"></i>
            {{ isCreating ? $t('battle.creating', '创建中...') : $t('battle.create_room', '创建房间') }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useBattleStore } from '~/stores/battle'
import type { AvailableCase } from '~/services/battle-api'

// Props和Emits
interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'created', data: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Store
const battleStore = useBattleStore()
const { t, locale } = useI18n()

// 响应式数据
const maxJoiner = ref(2)
const selectedCases = ref<string[]>([])
const searchQuery = ref('')
const categoryFilter = ref('all')
const sortBy = ref('name')
const isCreating = ref(false)
const errorMessage = ref('')
const showError = ref(false)

// 计算属性
const filteredCases = computed(() => {
  let filtered = [...battleStore.availableCases]
  
  // 搜索筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(caseItem => 
      caseItem.name.toLowerCase().includes(query) ||
      (caseItem.name_en && caseItem.name_en.toLowerCase().includes(query)) ||
      (caseItem.name_zh_hans && caseItem.name_zh_hans.toLowerCase().includes(query))
    )
  }
  
  // 分类筛选
  if (categoryFilter.value !== 'all') {
    switch (categoryFilter.value) {
      case 'hot':
        // 假设热门箱子有特定标签或者按价格排序
        filtered = filtered.slice().sort((a, b) => b.price - a.price).slice(0, 20)
        break
      case 'new':
        // 假设新箱子价格较高
        filtered = filtered.filter(item => item.price > 5)
        break
      case 'discount':
        // 只显示有折扣的箱子
        filtered = filtered.filter(item => item.discount && item.discount < 100)
        break
    }
  }
  
  // 排序
  switch (sortBy.value) {
    case 'price_asc':
      filtered.sort((a, b) => getDiscountPrice(a) - getDiscountPrice(b))
      break
    case 'price_desc':
      filtered.sort((a, b) => getDiscountPrice(b) - getDiscountPrice(a))
      break
    case 'name':
    default:
      filtered.sort((a, b) => {
        const nameA = getLocalizedCaseName(a)
        const nameB = getLocalizedCaseName(b)
        return nameA.localeCompare(nameB)
      })
      break
  }
  
  return filtered
})

const totalPrice = computed(() => {
  return selectedCases.value.reduce((total, caseKey) => {
    const caseItem = getCaseByKey(caseKey)
    return total + (caseItem ? getDiscountPrice(caseItem) : 0)
  }, 0)
})

const canCreateRoom = computed(() => {
  return selectedCases.value.length > 0 && selectedCases.value.length <= 4
})

// 获取去重的选中箱子列表
const uniqueSelectedCases = computed(() => {
  return [...new Set(selectedCases.value)]
})

// 方法
const getLocalizedCaseName = (caseItem: AvailableCase | null): string => {
  if (!caseItem) return 'Unknown Case'
  
  if (locale.value === 'zh-hans') {
    return caseItem.name_zh_hans || caseItem.name || '未知箱子'
  } else {
    return caseItem.name_en || caseItem.name || 'Unknown Case'
  }
}

const getDiscountPrice = (caseItem: AvailableCase): number => {
  if (caseItem.discount && caseItem.discount < 100) {
    return caseItem.price * (caseItem.discount / 100)
  }
  return caseItem.price
}

const getCaseByKey = (caseKey: string): AvailableCase | null => {
  return battleStore.availableCases.find(c => c.case_key === caseKey) || null
}

const toggleCaseSelection = (caseItem: AvailableCase) => {
  // 检查是否已经选择了这个箱子
  const existingCount = selectedCases.value.filter(key => key === caseItem.case_key).length
  
  if (existingCount > 0) {
    // 如果已经选择了，再次点击会添加一个（最多4个总数）
    if (selectedCases.value.length < 4) {
      selectedCases.value.push(caseItem.case_key)
      console.log(`[CreateRoomDialog] 添加箱子: ${caseItem.case_key}, 当前数量: ${existingCount + 1}`)
    } else {
      console.log(`[CreateRoomDialog] 已达到最大选择数量(4个)`)
    }
  } else {
    // 如果没有选择过，直接添加（最多4个总数）
    if (selectedCases.value.length < 4) {
      selectedCases.value.push(caseItem.case_key)
      console.log(`[CreateRoomDialog] 首次选择箱子: ${caseItem.case_key}`)
    } else {
      console.log(`[CreateRoomDialog] 已达到最大选择数量(4个)`)
    }
  }
}

const removeCase = (caseKey: string) => {
  const index = selectedCases.value.indexOf(caseKey)
  if (index > -1) {
    selectedCases.value.splice(index, 1)
    console.log(`[CreateRoomDialog] 移除箱子: ${caseKey}`)
  }
}

// 添加移除所有相同箱子的方法
const removeAllCases = (caseKey: string) => {
  selectedCases.value = selectedCases.value.filter(key => key !== caseKey)
  console.log(`[CreateRoomDialog] 移除所有 ${caseKey} 箱子`)
}

// 获取某个箱子的选择数量
const getCaseCount = (caseKey: string): number => {
  return selectedCases.value.filter(key => key === caseKey).length
}

const clearSelection = () => {
  selectedCases.value = []
}

// 错误处理方法
const showErrorMessage = (message: string) => {
  errorMessage.value = message
  showError.value = true
  // 3秒后自动隐藏错误信息
  setTimeout(() => {
    showError.value = false
  }, 3000)
}

const hideError = () => {
  showError.value = false
  errorMessage.value = ''
}

const createRoom = async () => {
  if (!canCreateRoom.value) return
  
  // 隐藏之前的错误信息
  hideError()
  
  // 检查用户登录状态
  const userStore = useUserStore()
  if (!userStore.isLoggedIn) {
    showErrorMessage('请先登录后再创建对战房间')
    console.error('用户未登录，无法创建房间')
    return
  }
  
  // 检查用户余额
  const userBalance = typeof userStore.userBalance === 'number' ? userStore.userBalance : 0
  if (userBalance < totalPrice.value) {
    showErrorMessage(`余额不足！需要 $${totalPrice.value.toFixed(2)}，当前余额 $${userBalance.toFixed(2)}`)
    console.error('用户余额不足，无法创建房间', {
      userBalance,
      totalPrice: totalPrice.value
    })
    return
  }
  
  try {
    isCreating.value = true
    
    console.log('[CreateRoomDialog] 用户已登录，开始创建房间')
    console.log('[CreateRoomDialog] 用户信息:', {
      isLoggedIn: userStore.isLoggedIn,
      userId: userStore.userId,
      nickname: userStore.userNickname
    })
    
    const result = await battleStore.createRoom(
      selectedCases.value,
      maxJoiner.value, // 使用用户选择的最大玩家数量
      false // 不是私人房间
    )
    
    if (result.success) {
      emit('created', result.data)
      resetForm()
      closeDialog() // 创建成功后关闭弹出框
    } else {
      // 处理API返回的具体错误
      const errorMsg = result.message || '创建房间失败'
      showErrorMessage(getErrorMessage(errorMsg))
      console.error('创建房间失败:', result.message)
    }
  } catch (error: any) {
    // 处理网络错误或其他异常
    const errorMsg = error?.message || '网络连接失败，请稍后重试'
    showErrorMessage(getErrorMessage(errorMsg))
    console.error('创建房间失败:', error)
  } finally {
    isCreating.value = false
  }
}

// 错误信息映射
const getErrorMessage = (apiError: string): string => {
  const errorMap: Record<string, string> = {
    'NoBalance': '余额不足，请先充值',
    'UserNotLogin': '用户未登录，请先登录',
    'CaseNotFound': '选择的箱子不存在，请重新选择',
    'CaseNotAvailable': '选择的箱子暂时不可用',
    'TooManyCases': '选择的箱子数量过多，最多选择4个',
    'TooFewCases': '请至少选择1个箱子',
    'InvalidTeam': '队伍参数无效',
    'RoomCreateFailed': '房间创建失败，请稍后重试',
    'ServerError': '服务器错误，请稍后重试',
    'NetworkError': '网络连接失败，请检查网络连接',
    'Forbidden': '权限不足，无法创建房间',
    'RateLimited': '操作过于频繁，请稍后再试',
    'MaintenanceMode': '系统维护中，暂时无法创建房间'
  }
  
  // 检查是否有匹配的错误码
  for (const [code, message] of Object.entries(errorMap)) {
    if (apiError.includes(code)) {
      return message
    }
  }
  
  // 检查常见的HTTP错误
  if (apiError.includes('403')) {
    return '权限不足，请检查登录状态'
  } else if (apiError.includes('429')) {
    return '操作过于频繁，请稍后再试'
  } else if (apiError.includes('500')) {
    return '服务器内部错误，请稍后重试'
  } else if (apiError.includes('502') || apiError.includes('503')) {
    return '服务暂时不可用，请稍后重试'
  } else if (apiError.includes('timeout')) {
    return '请求超时，请检查网络连接'
  }
  
  // 返回原始错误信息或默认信息
  return apiError || '创建房间失败，请稍后重试'
}

const closeDialog = () => {
  emit('update:modelValue', false)
}

const resetForm = () => {
  maxJoiner.value = 2
  selectedCases.value = []
  searchQuery.value = ''
  categoryFilter.value = 'all'
  sortBy.value = 'name'
  isCreating.value = false
  hideError() // 清除错误状态
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/images/default-case.png'
}

// 监听对话框打开，预加载数据
watch(() => props.modelValue, async (newValue) => {
  if (newValue) {
    // 重置表单
    resetForm()
    // 获取箱子数据
    await battleStore.fetchCases()
  }
})

// 初始化
onMounted(async () => {
  if (props.modelValue) {
    await battleStore.fetchCases()
  }
})
</script>

<style scoped>
/* 滚动条样式 */
.scrollbar-thin::-webkit-scrollbar {
  width: 4px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}
</style> 