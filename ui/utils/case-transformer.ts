import type { CaseDetailItem } from '~/services/case-api'
import type { CaseItem } from '~/types/case'
import { formatDate, formatDateOnly } from '~/utils/date'

/**
 * 将API返回的CaseDetailItem转换为CaseItem
 * 这是一个纯函数，不依赖Vue composables，可以在任何地方调用
 */
export const transformCaseItem = (apiCase: CaseDetailItem, locale: string = 'zh_hans'): CaseItem => {
  // 增强的本地化字段获取逻辑
  const getLocalizedField = (item: any, field: string): string | undefined => {
    if (!item || !field) return undefined
    
    // 标准化语言代码
    const normalizedLocale = locale === 'zh' ? 'zh_hans' : locale
    
    // 按优先级尝试获取本地化字段
    if (normalizedLocale === 'zh_hans') {
      // 中文环境：优先标准zh_hans，然后zh，最后基础字段
      const zhHansField = `${field}_zh_hans`
      if (item[zhHansField] !== undefined && item[zhHansField] !== null) {
        return item[zhHansField]
      }
      
      const zhField = `${field}_zh`
      if (item[zhField] !== undefined && item[zhField] !== null) {
        return item[zhField]
      }
    } else {
      // 其他语言环境
      const localizedKey = `${field}_${normalizedLocale}`
      if (item[localizedKey] !== undefined && item[localizedKey] !== null) {
        return item[localizedKey]
      }
    }
    
    // 回退到基础字段
    if (item[field] !== undefined && item[field] !== null) {
      return item[field]
    }
    
    // 最后回退到英文字段（如果当前不是英文环境）
    if (normalizedLocale !== 'en') {
      const enField = `${field}_en`
      if (item[enField] !== undefined && item[enField] !== null) {
        return item[enField]
      }
    }
    
    return undefined
  }

  // 计算原价和折扣
  let originalPrice: number | undefined
  let discountPercentage: number | undefined
  
  if (apiCase.discount && apiCase.discount > 0 && apiCase.discount < 100) {
    // API中的discount是折扣后的价格百分比，例如80表示打8折
    originalPrice = apiCase.price / (apiCase.discount / 100)
    discountPercentage = Math.round(100 - apiCase.discount)
  }

  return {
    id: apiCase.id,
    name: getLocalizedField(apiCase, 'name') || apiCase.name,
    image: apiCase.cover,
    price: apiCase.price,
    original_price: originalPrice,
    discount: discountPercentage,
    is_hot: apiCase.tag === 'HOT',
    is_new: apiCase.tag === 'new' || apiCase.tag === 'NEW',
    description: getLocalizedField(apiCase, 'description'),
    category: undefined,
    rarity: 'Base Grade',
    created_at: formatDateOnly(apiCase.created_at || new Date().toISOString(), undefined, locale),
    updated_at: formatDateOnly(apiCase.updated_at || new Date().toISOString(), undefined, locale),
    open_count: apiCase.open_count,
    // 支持 case_key 或 key 字段
    ...((apiCase.case_key || (apiCase as any).key) && { case_key: apiCase.case_key || (apiCase as any).key, key: apiCase.case_key || (apiCase as any).key })
  } as CaseItem & { case_key?: string; key?: string }
}

/**
 * 批量转换case数据
 */
export const transformCaseItems = (apiCases: CaseDetailItem[], locale: string = 'zh_hans'): CaseItem[] => {
  return apiCases.map(item => transformCaseItem(item, locale))
}