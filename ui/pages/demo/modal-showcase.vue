<template>
  <div
    class="min-h-screen bg-gradient-to-br from-slate-950 via-slate-900 to-slate-950 relative overflow-hidden"
  >
    <!-- 整站风格背景装饰 -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      <!-- 动态光球效果 -->
      <div
        class="absolute top-20 left-20 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse"
      ></div>
      <div
        class="absolute top-40 right-32 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl animate-pulse"
        style="animation-delay: 1s"
      ></div>
      <div
        class="absolute bottom-40 left-1/3 w-72 h-72 bg-cyan-500/10 rounded-full blur-3xl animate-pulse"
        style="animation-delay: 2s"
      ></div>

      <!-- 网格背景 -->
      <div
        class="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:50px_50px]"
      ></div>

      <!-- 渐变叠加 -->
      <div
        class="absolute inset-0 bg-gradient-to-b from-transparent via-slate-900/50 to-slate-950/80"
      ></div>
    </div>

    <div class="relative z-10 container mx-auto px-4 py-8">
      <!-- 页面标题 -->
      <div class="text-center mb-12">
        <div class="inline-flex items-center gap-4 mb-6">
          <div
            class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center"
          >
            <Icon name="material-symbols:view-in-ar" class="w-6 h-6 text-white" />
          </div>
          <h1
            class="title-i18n text-5xl font-bold bg-gradient-to-r from-blue-400 via-white to-purple-400 bg-clip-text text-transparent"
          >
            弹出框样式展示
          </h1>
          <div
            class="w-12 h-12 bg-gradient-to-r from-purple-500 to-blue-500 rounded-xl flex items-center justify-center"
          >
            <Icon
              name="material-symbols:design-services"
              class="w-6 h-6 text-white"
            />
          </div>
        </div>
        <p class="text-xl text-white/60 max-w-2xl mx-auto">
          展示项目中所有弹出框组件的样式和交互效果，包含对战加入反馈、验证码对话框等多种类型
        </p>
      </div>

      <!-- 弹出框分类展示 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
        <!-- 对战加入反馈模态框 -->
        <div class="bg-slate-800/60 backdrop-blur-xl border border-white/20 rounded-2xl p-6 shadow-xl">
          <div class="flex items-center gap-3 mb-6">
            <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
              <Icon name="material-symbols:group-add" class="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 class="text-xl font-bold text-white">对战加入反馈模态框</h3>
              <p class="text-sm text-white/60">BattleJoinModal.vue</p>
            </div>
          </div>
          
          <div class="space-y-3">
            <button
              @click="showBattleModal('success')"
              class="w-full px-4 py-3 bg-green-600 hover:bg-green-500 text-white font-medium rounded-xl transition-all duration-300 hover:scale-105"
            >
              成功状态
            </button>
            <button
              @click="showBattleModal('error')"
              class="w-full px-4 py-3 bg-red-600 hover:bg-red-500 text-white font-medium rounded-xl transition-all duration-300 hover:scale-105"
            >
              错误状态
            </button>
            <button
              @click="showBattleModal('warning')"
              class="w-full px-4 py-3 bg-yellow-600 hover:bg-yellow-500 text-white font-medium rounded-xl transition-all duration-300 hover:scale-105"
            >
              警告状态
            </button>
            <button
              @click="showBattleModal('info')"
              class="w-full px-4 py-3 bg-blue-600 hover:bg-blue-500 text-white font-medium rounded-xl transition-all duration-300 hover:scale-105"
            >
              信息状态
            </button>
            <button
              @click="showBattleModal('loading')"
              class="w-full px-4 py-3 bg-purple-600 hover:bg-purple-500 text-white font-medium rounded-xl transition-all duration-300 hover:scale-105"
            >
              加载状态
            </button>
          </div>
        </div>

        <!-- 安全验证码对话框 -->
        <div class="bg-slate-800/60 backdrop-blur-xl border border-white/20 rounded-2xl p-6 shadow-xl">
          <div class="flex items-center gap-3 mb-6">
            <div class="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl flex items-center justify-center">
              <Icon name="material-symbols:security" class="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 class="text-xl font-bold text-white">安全验证码对话框</h3>
              <p class="text-sm text-white/60">SafeCaptchaDialog.vue</p>
            </div>
          </div>
          
          <div class="space-y-3">
            <button
              @click="showSafeCaptcha"
              class="w-full px-4 py-3 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white font-medium rounded-xl transition-all duration-300 hover:scale-105"
            >
              显示安全验证码
            </button>
            <button
              @click="showSafeCaptchaWithError"
              class="w-full px-4 py-3 bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 text-white font-medium rounded-xl transition-all duration-300 hover:scale-105"
            >
              显示错误状态
            </button>
            <button
              @click="showSafeCaptchaWithLoading"
              class="w-full px-4 py-3 bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600 text-white font-medium rounded-xl transition-all duration-300 hover:scale-105"
            >
              显示加载状态
            </button>
          </div>
        </div>

        <!-- 标准验证码对话框 -->
        <div class="bg-slate-800/60 backdrop-blur-xl border border-white/20 rounded-2xl p-6 shadow-xl">
          <div class="flex items-center gap-3 mb-6">
            <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-cyan-600 rounded-xl flex items-center justify-center">
              <Icon name="material-symbols:verified" class="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 class="text-xl font-bold text-white">标准验证码对话框</h3>
              <p class="text-sm text-white/60">CaptchaDialog.vue</p>
            </div>
          </div>
          
          <div class="space-y-3">
            <button
              @click="showCaptcha"
              class="w-full px-4 py-3 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white font-medium rounded-xl transition-all duration-300 hover:scale-105"
            >
              显示标准验证码
            </button>
            <button
              @click="showCaptchaWithError"
              class="w-full px-4 py-3 bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-medium rounded-xl transition-all duration-300 hover:scale-105"
            >
              显示错误状态
            </button>
          </div>
        </div>

        <!-- 简单验证码对话框 -->
        <div class="bg-slate-800/60 backdrop-blur-xl border border-white/20 rounded-2xl p-6 shadow-xl">
          <div class="flex items-center gap-3 mb-6">
            <div class="w-10 h-10 bg-gradient-to-r from-gray-500 to-slate-600 rounded-xl flex items-center justify-center">
              <Icon name="material-symbols:simple-mode" class="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 class="text-xl font-bold text-white">简单验证码对话框</h3>
              <p class="text-sm text-white/60">SimpleCaptchaDialog.vue</p>
            </div>
          </div>
          
          <div class="space-y-3">
            <button
              @click="showSimpleCaptcha"
              class="w-full px-4 py-3 bg-gradient-to-r from-gray-500 to-slate-500 hover:from-gray-600 hover:to-slate-600 text-white font-medium rounded-xl transition-all duration-300 hover:scale-105"
            >
              显示简单验证码
            </button>
            <button
              @click="showSimpleCaptchaWithError"
              class="w-full px-4 py-3 bg-gradient-to-r from-red-500 to-rose-500 hover:from-red-600 hover:to-rose-600 text-white font-medium rounded-xl transition-all duration-300 hover:scale-105"
            >
              显示错误状态
            </button>
          </div>
        </div>
      </div>

      <!-- 新增的复杂模态框展示区域 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
        <!-- 通知模态框 -->
        <div class="bg-slate-800/60 backdrop-blur-xl border border-white/20 rounded-2xl p-6 shadow-xl">
          <div class="flex items-center gap-3 mb-6">
            <div class="w-10 h-10 bg-gradient-to-r from-orange-500 to-red-600 rounded-xl flex items-center justify-center">
              <Icon name="material-symbols:notifications" class="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 class="text-xl font-bold text-white">通知模态框</h3>
              <p class="text-sm text-white/60">NotificationModal.vue</p>
            </div>
          </div>
          
          <div class="space-y-3">
            <button
              @click="showNotificationModal('success', '操作成功', '您的操作已成功完成！', '这是一条成功消息的详细内容。')"
              class="w-full px-4 py-3 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-medium rounded-xl transition-all duration-300 hover:scale-105"
            >
              成功通知
            </button>
            <button
              @click="showNotificationModal('error', '操作失败', '您的操作未能完成。', '请检查网络连接或稍后重试。')"
              class="w-full px-4 py-3 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-medium rounded-xl transition-all duration-300 hover:scale-105"
            >
              错误通知
            </button>
            <button
              @click="showNotificationModal('warning', '警告提示', '请注意以下事项。', '这是一个重要的警告信息。')"
              class="w-full px-4 py-3 bg-gradient-to-r from-yellow-500 to-orange-600 hover:from-yellow-600 hover:to-orange-700 text-white font-medium rounded-xl transition-all duration-300 hover:scale-105"
            >
              警告通知
            </button>
            <button
              @click="showNotificationModal('info', '信息提示', '这是一条信息。', '包含详细的信息内容。')"
              class="w-full px-4 py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-medium rounded-xl transition-all duration-300 hover:scale-105"
            >
              信息通知
            </button>
            <button
              @click="showNotificationModal('question', '确认操作', '您确定要执行此操作吗？', '此操作不可撤销，请谨慎考虑。')"
              class="w-full px-4 py-3 bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white font-medium rounded-xl transition-all duration-300 hover:scale-105"
            >
              确认对话框
            </button>
          </div>
        </div>

        <!-- 表单模态框 -->
        <div class="bg-slate-800/60 backdrop-blur-xl border border-white/20 rounded-2xl p-6 shadow-xl">
          <div class="flex items-center gap-3 mb-6">
            <div class="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center">
              <Icon name="material-symbols:edit-document" class="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 class="text-xl font-bold text-white">表单模态框</h3>
              <p class="text-sm text-white/60">FormModal.vue</p>
            </div>
          </div>
          
          <div class="space-y-3">
            <button
              @click="showUserFormModal"
              class="w-full px-4 py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-medium rounded-xl transition-all duration-300 hover:scale-105"
            >
              用户注册表单
            </button>
            <button
              @click="showContactFormModal"
              class="w-full px-4 py-3 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-medium rounded-xl transition-all duration-300 hover:scale-105"
            >
              联系表单
            </button>
            <button
              @click="showSettingsFormModal"
              class="w-full px-4 py-3 bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white font-medium rounded-xl transition-all duration-300 hover:scale-105"
            >
              设置表单
            </button>
          </div>
        </div>

        <!-- 详情模态框 -->
        <div class="bg-slate-800/60 backdrop-blur-xl border border-white/20 rounded-2xl p-6 shadow-xl">
          <div class="flex items-center gap-3 mb-6">
            <div class="w-10 h-10 bg-gradient-to-r from-cyan-500 to-blue-600 rounded-xl flex items-center justify-center">
              <Icon name="material-symbols:info" class="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 class="text-xl font-bold text-white">详情模态框</h3>
              <p class="text-sm text-white/60">DetailModal.vue</p>
            </div>
          </div>
          
          <div class="space-y-3">
            <button
              @click="showProductDetailModal"
              class="w-full px-4 py-3 bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700 text-white font-medium rounded-xl transition-all duration-300 hover:scale-105"
            >
              产品详情
            </button>
            <button
              @click="showUserProfileModal"
              class="w-full px-4 py-3 bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700 text-white font-medium rounded-xl transition-all duration-300 hover:scale-105"
            >
              用户资料
            </button>
            <button
              @click="showStatisticsModal"
              class="w-full px-4 py-3 bg-gradient-to-r from-cyan-500 to-cyan-600 hover:from-cyan-600 hover:to-cyan-700 text-white font-medium rounded-xl transition-all duration-300 hover:scale-105"
            >
              统计数据
            </button>
          </div>
        </div>
      </div>

      <!-- 功能说明 -->
      <div class="bg-slate-800/60 backdrop-blur-xl border border-white/20 rounded-2xl p-6 shadow-xl mb-8">
        <h3 class="text-xl font-bold text-white mb-4 flex items-center gap-2">
          <Icon name="material-symbols:info" class="w-5 h-5 text-blue-400" />
          功能说明
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div>
            <h4 class="font-semibold text-white mb-2">对战加入反馈模态框</h4>
            <ul class="text-sm text-white/70 space-y-1">
              <li>• 支持5种状态：成功、错误、警告、信息、加载</li>
              <li>• 自动关闭功能</li>
              <li>• 重试和登录跳转按钮</li>
              <li>• 详细信息展示</li>
              <li>• 毛玻璃背景效果</li>
            </ul>
          </div>
          <div>
            <h4 class="font-semibold text-white mb-2">验证码对话框</h4>
            <ul class="text-sm text-white/70 space-y-1">
              <li>• 三种风格：安全版、标准版、简单版</li>
              <li>• 验证码图片刷新</li>
              <li>• 错误信息显示</li>
              <li>• 加载状态处理</li>
              <li>• 键盘回车确认</li>
            </ul>
          </div>
          <div>
            <h4 class="font-semibold text-white mb-2">复杂模态框</h4>
            <ul class="text-sm text-white/70 space-y-1">
              <li>• 通知模态框：多种类型和配置</li>
              <li>• 表单模态框：多种字段类型</li>
              <li>• 详情模态框：多媒体展示</li>
              <li>• 响应式设计</li>
              <li>• 丰富的交互效果</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- 控制按钮 -->
      <div class="flex flex-wrap items-center justify-center gap-4">
        <button
          @click="resetAllModals"
          class="group relative flex items-center gap-3 px-6 py-3 bg-gradient-to-r from-gray-600 to-slate-600 hover:from-gray-700 hover:to-slate-700 text-white font-medium rounded-xl transition-all duration-300 hover:scale-105 border border-white/20 hover:border-white/40 overflow-hidden"
        >
          <Icon name="material-symbols:refresh" class="w-5 h-5" />
          <span class="font-i18n">重置所有弹窗</span>
        </button>

        <button
          @click="toggleAutoDemo"
          class="group relative flex items-center gap-3 px-6 py-3 text-white font-medium rounded-xl transition-all duration-300 hover:scale-105 border border-white/20 hover:border-white/40 overflow-hidden"
          :class="autoDemoEnabled ? 'bg-green-600 hover:bg-green-500' : 'bg-blue-600 hover:bg-blue-500'"
        >
          <Icon
            :name="autoDemoEnabled ? 'material-symbols:stop' : 'material-symbols:play-arrow'"
            class="w-5 h-5"
          />
          <span class="font-i18n">{{ autoDemoEnabled ? '停止自动演示' : '开始自动演示' }}</span>
        </button>
      </div>
    </div>

    <!-- 弹出框组件 -->
    
    <!-- 对战加入反馈模态框 -->
    <BattleJoinModal
      :visible="battleModalVisible"
      :type="battleModalType"
      :title="battleModalTitle"
      :description="battleModalDescription"
      :details="battleModalDetails"
      :show-retry="battleModalShowRetry"
      :show-go-to-login="battleModalShowGoToLogin"
      :loading-text="battleModalLoadingText"
      :auto-close="battleModalAutoClose"
      @close="closeBattleModal"
      @retry="handleBattleModalRetry"
      @go-to-login="handleBattleModalGoToLogin"
    />

    <!-- 安全验证码对话框 -->
    <SafeCaptchaDialog
      :show="safeCaptchaVisible"
      :image="captchaImage"
      :error="captchaError"
      :loading="captchaLoading"
      @close="closeSafeCaptcha"
      @confirm="handleSafeCaptchaConfirm"
      @refresh="handleCaptchaRefresh"
    />

    <!-- 标准验证码对话框 -->
    <CaptchaDialog
      :show="captchaVisible"
      :image="captchaImage"
      :error="captchaError"
      :loading="captchaLoading"
      @close="closeCaptcha"
      @confirm="handleCaptchaConfirm"
      @refresh="handleCaptchaRefresh"
    />

    <!-- 简单验证码对话框 -->
    <SimpleCaptchaDialog
      :show="simpleCaptchaVisible"
      @close="closeSimpleCaptcha"
      @refresh="handleCaptchaRefresh"
    />

    <!-- 通知模态框 -->
    <NotificationModal
      :visible="notificationModalVisible"
      :type="notificationType"
      :title="notificationTitle"
      :subtitle="notificationSubtitle"
      :content="notificationContent"
      :image="notificationImage"
      :details="notificationDetails"
      :show-cancel="notificationShowCancel"
      :show-confirm="notificationShowConfirm"
      :cancel-text="notificationCancelText"
      :confirm-text="notificationConfirmText"
      :loading="notificationLoading"
      :auto-close="notificationAutoClose"
      @close="closeNotificationModal"
      @cancel="handleNotificationCancel"
      @confirm="handleNotificationConfirm"
    />

    <!-- 表单模态框 -->
    <FormModal
      :visible="formModalVisible"
      :title="formModalTitle"
      :subtitle="formModalSubtitle"
      :icon="formModalIcon"
      :fields="formModalFields"
      :initial-data="formModalInitialData"
      :submit-text="formModalSubmitText"
      :submit-icon="formModalSubmitIcon"
      :cancel-text="formModalCancelText"
      :loading="formModalLoading"
      :loading-text="formModalLoadingText"
      @close="closeFormModal"
      @cancel="handleFormCancel"
      @submit="handleFormSubmit"
    />

    <!-- 详情模态框 -->
    <DetailModal
      :visible="detailModalVisible"
      :title="detailModalTitle"
      :subtitle="detailModalSubtitle"
      :icon="detailModalIcon"
      :badge="detailModalBadge"
      :badge-type="detailModalBadgeType"
      :image="detailModalImage"
      :image-alt="detailModalImageAlt"
      :image-overlay="detailModalImageOverlay"
      :gallery="detailModalGallery"
      :basic-info="detailModalBasicInfo"
      :description="detailModalDescription"
      :stats="detailModalStats"
      :tags="detailModalTags"
      :actions="detailModalActions"
      :loading="detailModalLoading"
      @close="closeDetailModal"
      @action="handleDetailAction"
    />
  </div>
</template>

<script setup>
// 导入必要的组件
import BattleJoinModal from '~/components/ui/BattleJoinModal.vue'
import SafeCaptchaDialog from '~/components/ui/SafeCaptchaDialog.vue'
import CaptchaDialog from '~/components/ui/CaptchaDialog.vue'
import SimpleCaptchaDialog from '~/components/ui/SimpleCaptchaDialog.vue'
import NotificationModal from '~/components/ui/NotificationModal.vue'
import FormModal from '~/components/ui/FormModal.vue'
import DetailModal from '~/components/ui/DetailModal.vue'

// SEO 元数据
useSeoMeta({
  title: "弹出框样式展示 - CSGO开箱网站",
  description: "展示项目中所有弹出框组件的样式和交互效果，包含对战加入反馈、验证码对话框等多种类型",
  keywords: "弹出框展示,模态框,验证码对话框,UI组件演示",
});

// 国际化
const { t } = useI18n();

// 对战加入反馈模态框状态
const battleModalVisible = ref(false);
const battleModalType = ref('success');
const battleModalTitle = ref('');
const battleModalDescription = ref('');
const battleModalDetails = ref({});
const battleModalShowRetry = ref(false);
const battleModalShowGoToLogin = ref(false);
const battleModalLoadingText = ref('');
const battleModalAutoClose = ref(false);

// 验证码对话框状态
const safeCaptchaVisible = ref(false);
const captchaVisible = ref(false);
const simpleCaptchaVisible = ref(false);
const captchaImage = ref('/demo/captcha-demo.png');
const captchaError = ref('');
const captchaLoading = ref(false);

// 自动演示状态
const autoDemoEnabled = ref(false);
const autoDemoInterval = ref(null);

// 通知模态框状态
const notificationModalVisible = ref(false);
const notificationType = ref('info');
const notificationTitle = ref('');
const notificationSubtitle = ref('');
const notificationContent = ref('');
const notificationImage = ref('');
const notificationDetails = ref({});
const notificationShowCancel = ref(true);
const notificationShowConfirm = ref(true);
const notificationCancelText = ref('取消');
const notificationConfirmText = ref('确认');
const notificationLoading = ref(false);
const notificationAutoClose = ref(false);

// 表单模态框状态
const formModalVisible = ref(false);
const formModalTitle = ref('');
const formModalSubtitle = ref('');
const formModalIcon = ref('material-symbols:edit');
const formModalFields = ref([]);
const formModalInitialData = ref({});
const formModalSubmitText = ref('提交');
const formModalSubmitIcon = ref('material-symbols:check');
const formModalCancelText = ref('取消');
const formModalLoading = ref(false);
const formModalLoadingText = ref('提交中...');

// 详情模态框状态
const detailModalVisible = ref(false);
const detailModalTitle = ref('');
const detailModalSubtitle = ref('');
const detailModalIcon = ref('material-symbols:info');
const detailModalBadge = ref('');
const detailModalBadgeType = ref('info');
const detailModalImage = ref('');
const detailModalImageAlt = ref('');
const detailModalImageOverlay = ref('');
const detailModalGallery = ref([]);
const detailModalBasicInfo = ref({});
const detailModalDescription = ref('');
const detailModalStats = ref([]);
const detailModalTags = ref([]);
const detailModalActions = ref([]);
const detailModalLoading = ref(false);

// 显示对战加入反馈模态框
const showBattleModal = (type) => {
  console.log('[🎰MODAL] 显示对战模态框:', type);
  battleModalType.value = type;
  
  switch (type) {
    case 'success':
      battleModalTitle.value = '加入成功！';
      battleModalDescription.value = '您已成功加入对战房间，请等待房主开始游戏。';
      battleModalDetails.value = {
        roomId: 'A1B2C3',
        price: 15.50,
        balance: 100.00
      };
      battleModalShowRetry.value = false;
      battleModalShowGoToLogin.value = false;
      battleModalAutoClose.value = true;
      break;
      
    case 'error':
      battleModalTitle.value = '加入失败';
      battleModalDescription.value = '无法加入对战房间，请检查房间状态或稍后重试。';
      battleModalDetails.value = {
        roomId: 'A1B2C3',
        message: '房间可能已满或已开始游戏'
      };
      battleModalShowRetry.value = true;
      battleModalShowGoToLogin.value = false;
      battleModalAutoClose.value = false;
      break;
      
    case 'warning':
      battleModalTitle.value = '余额不足';
      battleModalDescription.value = '您的账户余额不足以参与此对战，请充值后重试。';
      battleModalDetails.value = {
        roomId: 'A1B2C3',
        price: 15.50,
        balance: 5.00
      };
      battleModalShowRetry.value = false;
      battleModalShowGoToLogin.value = true;
      battleModalAutoClose.value = false;
      break;
      
    case 'info':
      battleModalTitle.value = '对战信息';
      battleModalDescription.value = '这是一个3轮对战，每轮所有玩家同时开箱，总价值最高的玩家获胜。';
      battleModalDetails.value = {
        roomId: 'A1B2C3',
        price: 15.50
      };
      battleModalShowRetry.value = false;
      battleModalShowGoToLogin.value = false;
      battleModalAutoClose.value = false;
      break;
      
    case 'loading':
      battleModalTitle.value = '正在加入...';
      battleModalDescription.value = '正在为您加入对战房间，请稍候。';
      battleModalDetails.value = {
        roomId: 'A1B2C3',
        price: 15.50
      };
      battleModalShowRetry.value = false;
      battleModalShowGoToLogin.value = false;
      battleModalLoadingText.value = '正在连接服务器...';
      battleModalAutoClose.value = false;
      break;
  }
  
  battleModalVisible.value = true;
  console.log('[🎰MODAL] 模态框状态已设置，visible:', battleModalVisible.value);
};

// 关闭对战加入反馈模态框
const closeBattleModal = () => {
  console.log('[🎰MODAL] 关闭对战模态框');
  battleModalVisible.value = false;
};

// 处理重试
const handleBattleModalRetry = () => {
  console.log('[🎰MODAL] 重试加入对战');
  closeBattleModal();
  // 模拟重试
  setTimeout(() => {
    showBattleModal('success');
  }, 1000);
};

// 处理跳转登录
const handleBattleModalGoToLogin = () => {
  console.log('[🎰MODAL] 跳转到登录页面');
  closeBattleModal();
};

// 显示安全验证码对话框
const showSafeCaptcha = () => {
  console.log('[🎰MODAL] 显示安全验证码对话框');
  safeCaptchaVisible.value = true;
  captchaError.value = '';
  captchaLoading.value = false;
};

// 显示安全验证码对话框（错误状态）
const showSafeCaptchaWithError = () => {
  console.log('[🎰MODAL] 显示安全验证码对话框（错误状态）');
  safeCaptchaVisible.value = true;
  captchaError.value = '验证码错误，请重新输入';
  captchaLoading.value = false;
};

// 显示安全验证码对话框（加载状态）
const showSafeCaptchaWithLoading = () => {
  console.log('[🎰MODAL] 显示安全验证码对话框（加载状态）');
  safeCaptchaVisible.value = true;
  captchaError.value = '';
  captchaLoading.value = true;
};

// 关闭安全验证码对话框
const closeSafeCaptcha = () => {
  console.log('[🎰MODAL] 关闭安全验证码对话框');
  safeCaptchaVisible.value = false;
};

// 处理安全验证码确认
const handleSafeCaptchaConfirm = (value) => {
  console.log('[🎰MODAL] 安全验证码确认:', value);
  captchaLoading.value = true;
  
  // 模拟验证
  setTimeout(() => {
    captchaLoading.value = false;
    if (value === '1234') {
      closeSafeCaptcha();
      showBattleModal('success');
    } else {
      captchaError.value = '验证码错误，请重新输入';
    }
  }, 1500);
};

// 显示标准验证码对话框
const showCaptcha = () => {
  console.log('[🎰MODAL] 显示标准验证码对话框');
  captchaVisible.value = true;
  captchaError.value = '';
  captchaLoading.value = false;
};

// 显示标准验证码对话框（错误状态）
const showCaptchaWithError = () => {
  console.log('[🎰MODAL] 显示标准验证码对话框（错误状态）');
  captchaVisible.value = true;
  captchaError.value = '验证码不正确，请检查后重新输入';
  captchaLoading.value = false;
};

// 关闭标准验证码对话框
const closeCaptcha = () => {
  console.log('[🎰MODAL] 关闭标准验证码对话框');
  captchaVisible.value = false;
};

// 处理标准验证码确认
const handleCaptchaConfirm = (value) => {
  console.log('[🎰MODAL] 标准验证码确认:', value);
  captchaLoading.value = true;
  
  // 模拟验证
  setTimeout(() => {
    captchaLoading.value = false;
    if (value === '5678') {
      closeCaptcha();
      showBattleModal('info');
    } else {
      captchaError.value = '验证码不正确，请检查后重新输入';
    }
  }, 1500);
};

// 显示简单验证码对话框
const showSimpleCaptcha = () => {
  console.log('[🎰MODAL] 显示简单验证码对话框');
  simpleCaptchaVisible.value = true;
  captchaError.value = '';
  captchaLoading.value = false;
};

// 显示简单验证码对话框（错误状态）
const showSimpleCaptchaWithError = () => {
  console.log('[🎰MODAL] 显示简单验证码对话框（错误状态）');
  simpleCaptchaVisible.value = true;
  captchaError.value = '验证码错误';
  captchaLoading.value = false;
};

// 关闭简单验证码对话框
const closeSimpleCaptcha = () => {
  console.log('[🎰MODAL] 关闭简单验证码对话框');
  simpleCaptchaVisible.value = false;
};

// 处理简单验证码确认
const handleSimpleCaptchaConfirm = (value) => {
  console.log('[🎰MODAL] 简单验证码确认:', value);
  captchaLoading.value = true;
  
  // 模拟验证
  setTimeout(() => {
    captchaLoading.value = false;
    if (value === '9999') {
      closeSimpleCaptcha();
      showBattleModal('success');
    } else {
      captchaError.value = '验证码错误';
    }
  }, 1500);
};

// 处理验证码刷新
const handleCaptchaRefresh = () => {
  console.log('[🎰MODAL] 刷新验证码');
  // 模拟刷新验证码
  setTimeout(() => {
    console.log('[🎰MODAL] 验证码已刷新');
  }, 500);
};

// 重置所有弹窗
const resetAllModals = () => {
  console.log('[🎰MODAL] 重置所有弹窗');
  battleModalVisible.value = false;
  safeCaptchaVisible.value = false;
  captchaVisible.value = false;
  simpleCaptchaVisible.value = false;
  captchaError.value = '';
  captchaLoading.value = false;
  console.log('[🎰MODAL] 所有弹窗已重置');
};

// 切换自动演示
const toggleAutoDemo = () => {
  if (autoDemoEnabled.value) {
    // 停止自动演示
    if (autoDemoInterval.value) {
      clearInterval(autoDemoInterval.value);
      autoDemoInterval.value = null;
    }
    autoDemoEnabled.value = false;
    console.log('[🎰MODAL] 自动演示已停止');
  } else {
    // 开始自动演示
    autoDemoEnabled.value = true;
    const modals = [
      () => showBattleModal('success'),
      () => showBattleModal('error'),
      () => showBattleModal('warning'),
      () => showBattleModal('info'),
      () => showSafeCaptcha(),
      () => showCaptcha(),
      () => showSimpleCaptcha()
    ];
    
    let currentIndex = 0;
    autoDemoInterval.value = setInterval(() => {
      // 先关闭所有弹窗
      resetAllModals();
      
      // 延迟后显示下一个弹窗
      setTimeout(() => {
        modals[currentIndex]();
        currentIndex = (currentIndex + 1) % modals.length;
      }, 500);
    }, 4000); // 每4秒切换一次
    
    console.log('[🎰MODAL] 自动演示已开始');
  }
};

// 通知模态框方法
const showNotificationModal = (type, title, subtitle, content, image = '', details = {}, showCancel = true, showConfirm = true, autoClose = false) => {
  console.log('[🎰MODAL] 显示通知模态框:', type, title);
  notificationType.value = type;
  notificationTitle.value = title;
  notificationSubtitle.value = subtitle;
  notificationContent.value = content;
  notificationImage.value = image;
  notificationDetails.value = details;
  notificationShowCancel.value = showCancel;
  notificationShowConfirm.value = showConfirm;
  notificationAutoClose.value = autoClose;
  notificationModalVisible.value = true;
};

const closeNotificationModal = () => {
  console.log('[🎰MODAL] 关闭通知模态框');
  notificationModalVisible.value = false;
};

const handleNotificationCancel = () => {
  console.log('[🎰MODAL] 通知模态框取消');
  closeNotificationModal();
};

const handleNotificationConfirm = () => {
  console.log('[🎰MODAL] 通知模态框确认');
  notificationLoading.value = true;
  
  // 模拟处理
  setTimeout(() => {
    notificationLoading.value = false;
    closeNotificationModal();
    showNotificationModal('success', '操作成功', '您的操作已完成', '感谢您的使用！', '', {}, false, true, true);
  }, 1500);
};

// 表单模态框方法
const showUserFormModal = () => {
  console.log('[🎰MODAL] 显示用户注册表单');
  formModalTitle.value = '用户注册';
  formModalSubtitle.value = '请填写以下信息完成注册';
  formModalIcon.value = 'material-symbols:person-add';
  formModalFields.value = [
    { name: 'username', label: '用户名', type: 'text', placeholder: '请输入用户名', required: true },
    { name: 'email', label: '邮箱', type: 'email', placeholder: '请输入邮箱地址', required: true },
    { name: 'password', label: '密码', type: 'password', placeholder: '请输入密码', required: true },
    { name: 'confirmPassword', label: '确认密码', type: 'password', placeholder: '请再次输入密码', required: true },
    { name: 'gender', label: '性别', type: 'radio', required: true, options: [
      { value: 'male', label: '男' },
      { value: 'female', label: '女' },
      { value: 'other', label: '其他' }
    ]},
    { name: 'agree', label: '同意用户协议', type: 'checkbox', required: true }
  ];
  formModalSubmitText.value = '注册';
  formModalSubmitIcon.value = 'material-symbols:person-add';
  formModalVisible.value = true;
};

const showContactFormModal = () => {
  console.log('[🎰MODAL] 显示联系表单');
  formModalTitle.value = '联系我们';
  formModalSubtitle.value = '请填写以下信息，我们会尽快回复您';
  formModalIcon.value = 'material-symbols:contact-support';
  formModalFields.value = [
    { name: 'name', label: '姓名', type: 'text', placeholder: '请输入您的姓名', required: true },
    { name: 'email', label: '邮箱', type: 'email', placeholder: '请输入您的邮箱', required: true },
    { name: 'subject', label: '主题', type: 'select', required: true, options: [
      { value: 'general', label: '一般咨询' },
      { value: 'technical', label: '技术支持' },
      { value: 'billing', label: '账单问题' },
      { value: 'other', label: '其他' }
    ]},
    { name: 'message', label: '消息内容', type: 'textarea', placeholder: '请详细描述您的问题', required: true, rows: 5 }
  ];
  formModalSubmitText.value = '发送';
  formModalSubmitIcon.value = 'material-symbols:send';
  formModalVisible.value = true;
};

const showSettingsFormModal = () => {
  console.log('[🎰MODAL] 显示设置表单');
  formModalTitle.value = '个人设置';
  formModalSubtitle.value = '自定义您的账户设置';
  formModalIcon.value = 'material-symbols:settings';
  formModalFields.value = [
    { name: 'nickname', label: '昵称', type: 'text', placeholder: '请输入昵称' },
    { name: 'language', label: '语言', type: 'select', required: true, options: [
      { value: 'zh-hans', label: '简体中文' },
      { value: 'en', label: 'English' }
    ]},
    { name: 'notifications', label: '接收通知', type: 'checkbox' },
    { name: 'theme', label: '主题', type: 'radio', options: [
      { value: 'dark', label: '深色主题' },
      { value: 'light', label: '浅色主题' },
      { value: 'auto', label: '跟随系统' }
    ]}
  ];
  formModalInitialData.value = {
    nickname: '测试用户',
    language: 'zh-hans',
    notifications: true,
    theme: 'dark'
  };
  formModalSubmitText.value = '保存';
  formModalSubmitIcon.value = 'material-symbols:save';
  formModalVisible.value = true;
};

const closeFormModal = () => {
  console.log('[🎰MODAL] 关闭表单模态框');
  formModalVisible.value = false;
};

const handleFormCancel = () => {
  console.log('[🎰MODAL] 表单模态框取消');
  closeFormModal();
};

const handleFormSubmit = (data) => {
  console.log('[🎰MODAL] 表单提交:', data);
  formModalLoading.value = true;
  
  // 模拟提交
  setTimeout(() => {
    formModalLoading.value = false;
    closeFormModal();
    showNotificationModal('success', '提交成功', '您的信息已成功提交', '我们会尽快处理您的请求。', '', {}, false, true, true);
  }, 2000);
};

// 详情模态框方法
const showProductDetailModal = () => {
  console.log('[🎰MODAL] 显示产品详情');
  detailModalTitle.value = 'AK-47 | 血腥运动';
  detailModalSubtitle.value = 'StatTrak™ | 崭新出厂';
  detailModalIcon.value = 'material-symbols:inventory';
  detailModalBadge.value = '稀有';
  detailModalBadgeType.value = 'success';
  detailModalImage.value = '/demo/item1.png';
  detailModalImageAlt.value = 'AK-47 血腥运动';
  detailModalImageOverlay.value = 'StatTrak™';
  detailModalGallery.value = [
    { src: '/demo/item1.png', alt: '主视图' },
    { src: '/demo/item2.png', alt: '侧视图' },
    { src: '/demo/item3.png', alt: '细节图' },
    { src: '/demo/item4.png', alt: '背面图' }
  ];
  detailModalBasicInfo.value = {
    '武器类型': 'AK-47',
    '皮肤名称': '血腥运动',
    '品质': '崭新出厂',
    '稀有度': '受限',
    'StatTrak™': '是',
    '磨损值': '0.001'
  };
  detailModalDescription.value = '这是一款经典的AK-47皮肤，采用血腥运动主题设计，具有独特的视觉效果和优秀的游戏体验。';
  detailModalStats.value = [
    { label: '市场价值', value: '$125.50' },
    { label: '开箱概率', value: '0.15%' },
    { label: '收藏数量', value: '1,234' },
    { label: '评分', value: '4.8/5' }
  ];
  detailModalTags.value = ['AK-47', '血腥运动', 'StatTrak™', '崭新出厂', '受限'];
  detailModalActions.value = [
    { key: 'buy', label: '立即购买', icon: 'material-symbols:shopping-cart', class: 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700' },
    { key: 'trade', label: '交易', icon: 'material-symbols:swap-horiz', class: 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700' },
    { key: 'favorite', label: '收藏', icon: 'material-symbols:favorite', class: 'bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700' }
  ];
  detailModalVisible.value = true;
};

const showUserProfileModal = () => {
  console.log('[🎰MODAL] 显示用户资料');
  detailModalTitle.value = '用户资料';
  detailModalSubtitle.value = '查看用户详细信息';
  detailModalIcon.value = 'material-symbols:person';
  detailModalBadge.value = 'VIP';
  detailModalBadgeType.value = 'warning';
  detailModalImage.value = '/demo/avatar1.png';
  detailModalImageAlt.value = '用户头像';
  detailModalBasicInfo.value = {
    '用户名': 'CSGO_Player_123',
    '注册时间': '2023-01-15',
    '会员等级': 'VIP',
    '账户余额': '$1,250.00',
    '开箱次数': '1,234',
    '胜率': '68.5%'
  };
  detailModalDescription.value = '这是一位活跃的CSGO玩家，拥有丰富的开箱经验和优秀的游戏战绩。';
  detailModalStats.value = [
    { label: '总开箱', value: '1,234' },
    { label: '成功次数', value: '845' },
    { label: '胜率', value: '68.5%' },
    { label: '总价值', value: '$12,450' }
  ];
  detailModalTags.value = ['VIP会员', '活跃用户', '高胜率', '经验丰富'];
  detailModalActions.value = [
    { key: 'message', label: '发送消息', icon: 'material-symbols:message', class: 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700' },
    { key: 'follow', label: '关注', icon: 'material-symbols:person-add', class: 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700' },
    { key: 'block', label: '屏蔽', icon: 'material-symbols:block', class: 'bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700' }
  ];
  detailModalVisible.value = true;
};

const showStatisticsModal = () => {
  console.log('[🎰MODAL] 显示统计数据');
  detailModalTitle.value = '平台统计';
  detailModalSubtitle.value = '实时数据统计';
  detailModalIcon.value = 'material-symbols:analytics';
  detailModalBadge.value = '实时';
  detailModalBadgeType.value = 'info';
  detailModalImage.value = '/demo/stats-chart.png';
  detailModalImageAlt.value = '统计图表';
  detailModalBasicInfo.value = {
    '在线用户': '12,345',
    '今日开箱': '45,678',
    '总注册用户': '1,234,567',
    '服务器状态': '正常',
    '更新时间': '2024-01-15 14:30:00'
  };
  detailModalDescription.value = '这是平台的实时统计数据，包含用户活跃度、开箱数量、服务器状态等关键指标。';
  detailModalStats.value = [
    { label: '在线用户', value: '12,345' },
    { label: '今日开箱', value: '45,678' },
    { label: '成功率', value: '15.2%' },
    { label: '平均价值', value: '$25.50' }
  ];
  detailModalTags.value = ['实时数据', '用户统计', '开箱统计', '平台监控'];
  detailModalActions.value = [
    { key: 'refresh', label: '刷新数据', icon: 'material-symbols:refresh', class: 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700' },
    { key: 'export', label: '导出报告', icon: 'material-symbols:download', class: 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700' }
  ];
  detailModalVisible.value = true;
};

const closeDetailModal = () => {
  console.log('[🎰MODAL] 关闭详情模态框');
  detailModalVisible.value = false;
};

const handleDetailAction = (key) => {
  console.log('[🎰MODAL] 详情模态框操作:', key);
  detailModalLoading.value = true;
  
  // 模拟操作
  setTimeout(() => {
    detailModalLoading.value = false;
    closeDetailModal();
    showNotificationModal('success', '操作成功', `已成功执行 ${key} 操作`, '操作已完成，请查看结果。', '', {}, false, true, true);
  }, 1500);
};

// 组件生命周期
onMounted(() => {
  console.log('[🎰MODAL] 弹出框展示页面已加载');
});

onBeforeUnmount(() => {
  // 清理自动演示定时器
  if (autoDemoInterval.value) {
    clearInterval(autoDemoInterval.value);
    autoDemoInterval.value = null;
  }
});
</script>

<style scoped>
/* 自定义样式 */
.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: opacity 0.3s ease;
}

.modal-fade-enter-from,
.modal-fade-leave-to {
  opacity: 0;
}

.modal-backdrop-enter-active,
.modal-backdrop-leave-active {
  transition: opacity 0.3s ease;
}

.modal-backdrop-enter-from,
.modal-backdrop-leave-to {
  opacity: 0;
}

.modal-content-enter-active,
.modal-content-leave-active {
  transition: all 0.3s ease;
}

.modal-content-enter-from,
.modal-content-leave-to {
  opacity: 0;
  transform: scale(0.9) translateY(-20px);
}

.error-message-enter-active,
.error-message-leave-active {
  transition: all 0.3s ease;
}

.error-message-enter-from,
.error-message-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
</style> 