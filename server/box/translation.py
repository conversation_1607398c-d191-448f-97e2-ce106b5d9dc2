from modeltranslation.translator import register, TranslationOptions

from box.models import Case, CaseType, CaseCategory


@register(Case)
class CaseTranslationOptions(TranslationOptions):
    fields = ('name', 'seo_title', 'seo_keywords', 'seo_description', 'tag')  # 暂时移除 content 字段，等待 CKEditor5 翻译支持


@register(CaseType)
class CaseTypeTranslationOptions(TranslationOptions):
    fields = ('type_name', )

@register(CaseCategory)
class CaseCategoryTranslationOptions(TranslationOptions):
    fields = ('cate_name', )