<template>
  <div class="bg-slate-900/60 backdrop-blur-xl border border-white/20 rounded-2xl p-6 shadow-xl">
    <div class="flex items-center gap-4 mb-6">
      <div class="w-10 h-10 bg-gradient-to-r from-red-500 to-pink-500 rounded-xl flex items-center justify-center">
        <Icon name="material-symbols:casino" class="w-5 h-5 text-white" />
      </div>
      <h2 class="title-i18n text-2xl font-bold text-white">{{ $t('battle.battle_animation') }}</h2>
      <div class="flex items-center gap-2 px-3 py-1 bg-red-500/20 border border-red-400/30 rounded-lg">
        <div class="w-2 h-2 bg-red-400 rounded-full animate-pulse"></div>
        <span class="font-i18n text-sm font-medium text-red-400">{{ $t('battle.live') }}</span>
      </div>
    </div>

    <!-- 动画区域 - 只在动画进行时显示 -->
    <div v-if="isAnimating" class="space-y-4">
      <div 
        v-for="(player, playerIndex) in players" 
        :key="player.id"
        class="relative"
      >
        <!-- 使用整合后的BattleCaseAnimation组件 -->
        <BattleCaseAnimation
          v-if="player.animationData"
          :ref="(el) => setCaseAnimationRef(el, playerIndex)"
          :selected-case="player.animationData.selectedCase"
          :case-items="player.animationData.caseItems"
          :player-index="playerIndex"
          :round-index="currentRound - 1"
          :player="player"
          @animation-complete="handleAnimationComplete(playerIndex, $event)"
          @opening-start="handleAnimationStart(playerIndex)"
        />
        
        <!-- 等待动画的骨架图 -->
        <div v-else class="w-full h-48 flex items-center justify-center bg-slate-800/50 rounded-xl border border-white/10">
          <div class="flex items-center gap-4">
            <div class="w-6 h-6 border-2 border-blue-400 border-t-transparent rounded-full animate-spin"></div>
            <span class="font-i18n text-sm text-white/60">{{ $t('battle.preparing_animation') }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 等待开始状态 -->
    <div v-else-if="!hasStarted" class="text-center py-12">
      <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-cyan-400 to-blue-400 rounded-full flex items-center justify-center">
        <Icon name="material-symbols:casino" class="w-8 h-8 text-white" />
      </div>
      <h3 class="text-xl font-bold text-white mb-2">{{ $t('battle.waiting_to_start') }}</h3>
      <p class="text-gray-400">{{ $t('battle.ready_to_begin') }}</p>
    </div>

    <!-- 轮次信息 -->
    <div v-if="showRoundInfo && hasStarted" class="mt-6 pt-6 border-t border-white/10">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-2">
          <Icon name="material-symbols:progress-activity" class="w-5 h-5 text-cyan-400" />
          <span class="font-i18n text-lg font-medium text-white">{{ $t('battle.round_progress') }}</span>
        </div>
        <div class="flex items-center gap-2">
          <span class="font-i18n text-lg font-bold text-white">{{ currentRound }}/{{ totalRounds }}</span>
          <div class="w-32 bg-slate-700 rounded-full h-2 overflow-hidden">
            <div 
              class="h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full transition-all duration-500"
              :style="{ width: `${(currentRound / totalRounds) * 100}%` }"
            ></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// Vue 导入
import { ref, computed, watch, nextTick, onMounted } from 'vue'

// Props 定义
const props = defineProps({
  players: {
    type: Array,
    default: () => []
  },
  currentRound: {
    type: Number,
    default: 1
  },
  totalRounds: {
    type: Number,
    default: 3
  },
  isAnimating: {
    type: Boolean,
    default: false
  },
  showRoundInfo: {
    type: Boolean,
    default: true
  },
  animationData: {
    type: Object,
    default: () => ({})
  }
})

// 事件定义
const emit = defineEmits([
  'animation-complete',
  'animation-start',
  'round-complete',
  'opening-state-change'
])

// 国际化
const { t } = useI18n()

// 状态管理
const shouldShowAnimation = ref(false)
const hasStarted = ref(false)
const animationCompletedPlayers = ref(new Set())
const caseAnimationRefs = ref([])

// 🔧 新增：开箱状态管理
const currentOpeningPlayerIndex = ref(null)
const currentOpeningCaseId = ref(null)
const currentOpeningPlayerName = ref('')

// 计算属性
const allAnimationsComplete = computed(() => {
  return animationCompletedPlayers.value.size === props.players.length
})

// 🔧 修复：正确设置子组件引用
const setCaseAnimationRef = (el, playerIndex) => {
  if (el) {
    // 确保数组有足够的长度
    while (caseAnimationRefs.value.length <= playerIndex) {
      caseAnimationRefs.value.push(null)
    }
    caseAnimationRefs.value[playerIndex] = el
    console.log(`[🎰BATTLE] 🔧 设置玩家${playerIndex + 1}组件引用:`, !!el)
  }
}

// 方法
const handleAnimationComplete = (playerIndex, result) => {
  console.log(`[🎰BATTLE] 玩家${playerIndex + 1}动画完成:`, result)
  
  // 记录完成的玩家
  animationCompletedPlayers.value.add(playerIndex)
  console.log(`[🎰BATTLE] 已完成玩家:`, Array.from(animationCompletedPlayers.value), `总玩家数: ${props.players.length}`)
  
  // 更新玩家结果
  if (props.players[playerIndex]) {
    const player = props.players[playerIndex]
    
    // 设置当前轮次结果
    player.currentRoundResult = result
    
    // 🔧 修复：移除开箱记录添加逻辑，避免重复添加
    // 开箱记录由页面级别的handleAnimationComplete函数处理
    console.log(`[🎰BATTLE] 玩家 ${player.nickname} 当前轮次结果已设置`)
  }
  
  // 发送动画完成事件
  emit('animation-complete', { playerIndex, result })
  
  // 检查是否所有玩家都完成了
  if (allAnimationsComplete.value) {
    console.log(`[🎰BATTLE] 所有玩家动画完成，清除开箱状态`)
    
    // 清除当前轮次的开箱状态
    currentOpeningPlayerIndex.value = null
    currentOpeningCaseId.value = null
    currentOpeningPlayerName.value = ''
    
    // 发送开箱状态清除事件
    emit('opening-state-change', {
      caseId: null,
      playerName: '',
      playerIndex: null
    })
    
    // 延迟发送轮次完成事件，让用户看到结果
    setTimeout(() => {
      const roundResults = Array.from(animationCompletedPlayers.value).map(index => ({
        playerIndex: index,
        result: props.players[index]?.currentRoundResult
      }))
      
      emit('round-complete', {
        round: props.currentRound,
        results: roundResults
      })
      
      console.log(`[🎰BATTLE] 轮次完成事件已发送，结果数量: ${roundResults.length}`)
    }, 1000)
  }
}

const handleAnimationStart = (playerIndex) => {
  console.log(`[🎰BATTLE-TEST] [COMP] 玩家${playerIndex + 1}动画开始，emit animation-start`)
  emit('animation-start', { playerIndex })
}

const startAnimation = async () => {
  console.log('[🎰BATTLE] 🚀 开始所有玩家动画')
  animationCompletedPlayers.value.clear()
  shouldShowAnimation.value = true
  hasStarted.value = true
  
  // 重置开箱状态
  currentOpeningPlayerIndex.value = null
  currentOpeningCaseId.value = null
  currentOpeningPlayerName.value = ''
  
  // 等待DOM更新后触发子组件自动开始
  await nextTick()
  
  // 🔧 修复：延迟更长时间确保组件完全渲染和引用设置完成
  setTimeout(async () => {
    console.log('[🎰BATTLE] 🎮 开始所有玩家同时滚动同一个箱子')
    console.log('[🎰BATTLE] 🔍 当前子组件引用数量:', caseAnimationRefs.value.length)
    
    // 🔧 修复：所有玩家同时开始动画，滚动同一个箱子
    // 获取第一个玩家的箱子作为当前轮次的箱子
    const firstPlayer = props.players[0]
    if (firstPlayer?.animationData?.selectedCase) {
      // 设置当前开箱状态（所有玩家都在开这个箱子）
      currentOpeningPlayerIndex.value = null
      currentOpeningCaseId.value = firstPlayer.animationData.selectedCase.id
      currentOpeningPlayerName.value = '正在开箱'
      console.log(`[🎰BATTLE] 🎯 设置当前轮次箱子: ${firstPlayer.animationData.selectedCase.name_zh_hans}，所有玩家同时开箱`)
      
      // 发送开箱状态变化事件
      emit('opening-state-change', {
        caseId: firstPlayer.animationData.selectedCase.id,
        playerName: '正在开箱',
        playerIndex: null
      })
      
      // 同时触发所有玩家的动画
      for (let index = 0; index < props.players.length; index++) {
        const ref = caseAnimationRefs.value[index]
        if (ref && typeof ref.triggerAutoStart === 'function') {
          console.log(`[🎰BATTLE] 🎮 触发玩家${index + 1}动画自动开始`)
          ref.triggerAutoStart()
        } else {
          console.warn(`[🎰BATTLE] ⚠️ 玩家${index + 1}组件引用未找到或方法不存在:`, !!ref, typeof ref?.triggerAutoStart)
        }
      }
    } else {
      console.error('[🎰BATTLE] ❌ 第一个玩家数据不完整，无法开始动画')
    }
  }, 800) // 增加延迟时间确保组件完全准备好
}

const resetAnimation = () => {
  console.log('[🎰BATTLE] 🔄 重置多轮动画状态 - 修复反向滚动')
  shouldShowAnimation.value = false
  animationCompletedPlayers.value.clear()
  
  // 清除玩家当前轮次结果
  props.players.forEach(player => {
    if (player.currentRoundResult) {
      player.currentRoundResult = null
    }
  })
  
  // 🔧 修复：重置时清理组件引用，为下一轮准备
  caseAnimationRefs.value = []
  
  // 🎯 确保所有SmoothCaseAnimation组件都完全重置
  nextTick(() => {
    // 给SmoothCaseAnimation组件时间重置状态
    console.log('[🎰BATTLE] ✅ 轮次重置完成，准备下一轮')
  })
}

// 🔧 新增：轮次间重置方法，保持组件引用
const resetForNextRound = () => {
  console.log('[🎰BATTLE] 🔄 轮次间重置，保持组件引用')
  animationCompletedPlayers.value.clear()
  
  // 重置开箱状态
  currentOpeningPlayerIndex.value = null
  currentOpeningCaseId.value = null
  currentOpeningPlayerName.value = ''
  
  // 发送开箱状态重置事件
  emit('opening-state-change', {
    caseId: null,
    playerName: '',
    playerIndex: null
  })
  
  // 清除玩家当前轮次结果
  props.players.forEach(player => {
    if (player.currentRoundResult) {
      player.currentRoundResult = null
    }
  })
  
  // 🔧 修复：确保动画状态正确
  shouldShowAnimation.value = true
  
  // 🎯 不清理组件引用，保持现有引用
  console.log('[🎰BATTLE] ✅ 轮次间重置完成，保持组件引用数量:', caseAnimationRefs.value.length)
}

// 暴露方法供父组件调用
defineExpose({
  startAnimation,
  resetAnimation,
  resetForNextRound
})

// 监听动画状态变化
watch(() => props.isAnimating, (newValue) => {
  if (newValue) {
    startAnimation()
  } else {
    resetAnimation()
  }
})

// 监听玩家数据变化，自动开始动画
watch(() => props.players, (newPlayers) => {
  if (newPlayers && newPlayers.length > 0) {
    // 检查是否所有玩家都有动画数据
    const allPlayersReady = newPlayers.every(player => 
      player.animationData && 
      player.animationData.selectedCase && 
      player.animationData.caseItems
    )
    
    // 🔧 修复：在计算阶段禁用自动开始动画
    if (allPlayersReady && !shouldShowAnimation.value && props.isAnimating) {
      console.log('[🎰BATTLE] 所有玩家数据准备完成，自动开始动画')
      nextTick(() => {
        startAnimation()
      })
    }
  }
}, { deep: true, immediate: true })

// 组件挂载时检查是否可以开始动画
onMounted(() => {
  if (props.players && props.players.length > 0) {
    const allPlayersReady = props.players.every(player => 
      player.animationData && 
      player.animationData.selectedCase && 
      player.animationData.caseItems
    )
    
    // 🔧 修复：在计算阶段禁用自动开始动画
    if (allPlayersReady && props.isAnimating) {
      console.log('[🎰BATTLE] 组件挂载时自动开始动画')
      nextTick(() => {
        startAnimation()
      })
    }
  }
})
</script> 