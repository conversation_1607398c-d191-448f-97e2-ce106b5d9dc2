<template>
  <Teleport to="body">
    <Transition name="modal-fade" appear>
      <div
        v-if="visible"
        class="fixed inset-0 z-[9999] flex items-center justify-center p-4"
        @click.self="handleClose"
      >
        <!-- 背景遮罩 -->
        <div class="absolute inset-0 bg-black/80 backdrop-blur-sm"></div>
        
        <!-- 模态框内容 -->
        <div class="relative w-full max-w-2xl transform transition-all duration-300">
          <div class="relative bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur-xl border border-gray-600/30 rounded-2xl shadow-2xl overflow-hidden">
            <!-- 装饰性背景 -->
            <div class="absolute inset-0 overflow-hidden pointer-events-none">
              <div class="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-blue-500/5 to-purple-500/5"></div>
              <div class="absolute -top-20 -right-20 w-40 h-40 bg-gradient-to-br from-blue-500/10 to-transparent rounded-full blur-xl"></div>
              <div class="absolute -bottom-20 -left-20 w-40 h-40 bg-gradient-to-br from-purple-500/10 to-transparent rounded-full blur-xl"></div>
            </div>

            <!-- 关闭按钮 -->
            <button
              @click="handleClose"
              class="absolute top-4 right-4 w-8 h-8 flex items-center justify-center rounded-full bg-gray-700/50 hover:bg-gray-600/50 text-gray-400 hover:text-white transition-all duration-200 z-20"
            >
              <Icon name="material-symbols:close" class="w-5 h-5" />
            </button>

            <!-- 内容区域 -->
            <div class="relative z-10 p-8">
              <!-- 标题区域 -->
              <div class="text-center mb-8">
                <div class="flex justify-center mb-4">
                  <div class="w-16 h-16 rounded-full bg-gradient-to-br from-blue-500/20 to-purple-500/20 flex items-center justify-center text-blue-400 border border-blue-500/30">
                    <Icon :name="icon" class="w-8 h-8" />
                  </div>
                </div>
                <h3 class="text-2xl font-bold text-white mb-2">{{ title }}</h3>
                <p v-if="subtitle" class="text-gray-400 text-sm">{{ subtitle }}</p>
              </div>

              <!-- 表单区域 -->
              <form @submit.prevent="handleSubmit" class="space-y-6">
                <!-- 动态表单字段 -->
                <div v-for="field in fields" :key="field.name" class="space-y-2">
                  <label :for="field.name" class="block text-sm font-medium text-gray-300">
                    {{ field.label }}
                    <span v-if="field.required" class="text-red-400 ml-1">*</span>
                  </label>
                  
                  <!-- 文本输入 -->
                  <input
                    v-if="field.type === 'text' || field.type === 'email' || field.type === 'password' || field.type === 'number'"
                    :id="field.name"
                    v-model="formData[field.name]"
                    :type="field.type"
                    :placeholder="field.placeholder"
                    :required="field.required"
                    :disabled="loading"
                    class="w-full px-4 py-3 bg-gray-800/50 border border-gray-600/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    :class="{ 'border-red-500/50': errors[field.name] }"
                  />
                  
                  <!-- 文本域 -->
                  <textarea
                    v-else-if="field.type === 'textarea'"
                    :id="field.name"
                    v-model="formData[field.name]"
                    :placeholder="field.placeholder"
                    :required="field.required"
                    :disabled="loading"
                    :rows="field.rows || 4"
                    class="w-full px-4 py-3 bg-gray-800/50 border border-gray-600/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed resize-none"
                    :class="{ 'border-red-500/50': errors[field.name] }"
                  ></textarea>
                  
                  <!-- 选择框 -->
                  <select
                    v-else-if="field.type === 'select'"
                    :id="field.name"
                    v-model="formData[field.name]"
                    :required="field.required"
                    :disabled="loading"
                    class="w-full px-4 py-3 bg-gray-800/50 border border-gray-600/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    :class="{ 'border-red-500/50': errors[field.name] }"
                  >
                    <option value="" disabled>{{ field.placeholder || '请选择...' }}</option>
                    <option v-for="option in field.options" :key="option.value" :value="option.value">
                      {{ option.label }}
                    </option>
                  </select>
                  
                  <!-- 复选框 -->
                  <div v-else-if="field.type === 'checkbox'" class="flex items-center space-x-3">
                    <input
                      :id="field.name"
                      v-model="formData[field.name]"
                      type="checkbox"
                      :required="field.required"
                      :disabled="loading"
                      class="w-4 h-4 text-blue-500 bg-gray-800 border-gray-600 rounded focus:ring-blue-500/50 focus:ring-2 disabled:opacity-50"
                    />
                    <label :for="field.name" class="text-sm text-gray-300">
                      {{ field.label }}
                    </label>
                  </div>
                  
                  <!-- 单选按钮组 -->
                  <div v-else-if="field.type === 'radio'" class="space-y-2">
                    <div v-for="option in field.options" :key="option.value" class="flex items-center space-x-3">
                      <input
                        :id="`${field.name}-${option.value}`"
                        v-model="formData[field.name]"
                        :name="field.name"
                        :value="option.value"
                        type="radio"
                        :required="field.required"
                        :disabled="loading"
                        class="w-4 h-4 text-blue-500 bg-gray-800 border-gray-600 focus:ring-blue-500/50 focus:ring-2 disabled:opacity-50"
                      />
                      <label :for="`${field.name}-${option.value}`" class="text-sm text-gray-300">
                        {{ option.label }}
                      </label>
                    </div>
                  </div>
                  
                  <!-- 错误信息 -->
                  <p v-if="errors[field.name]" class="text-red-400 text-sm mt-1">
                    {{ errors[field.name] }}
                  </p>
                </div>

                <!-- 表单验证错误 -->
                <div v-if="formError" class="bg-red-500/10 border border-red-500/30 rounded-lg p-4">
                  <div class="flex items-center space-x-2">
                    <Icon name="material-symbols:error" class="w-5 h-5 text-red-400" />
                    <span class="text-red-400 text-sm">{{ formError }}</span>
                  </div>
                </div>

                <!-- 按钮区域 -->
                <div class="flex flex-col sm:flex-row gap-3 justify-end pt-6 border-t border-gray-600/30">
                  <button
                    type="button"
                    @click="handleCancel"
                    :disabled="loading"
                    class="px-6 py-3 bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 rounded-lg font-medium text-white transition-all duration-300 flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <Icon name="material-symbols:close" class="w-4 h-4" />
                    {{ cancelText }}
                  </button>
                  
                  <button
                    type="submit"
                    :disabled="loading"
                    class="px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 rounded-lg font-medium text-white transition-all duration-300 flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <Icon v-if="loading" name="material-symbols:sync" class="w-4 h-4 animate-spin" />
                    <Icon v-else :name="submitIcon" class="w-4 h-4" />
                    {{ loading ? loadingText : submitText }}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
interface FormField {
  name: string
  label: string
  type: 'text' | 'email' | 'password' | 'number' | 'textarea' | 'select' | 'checkbox' | 'radio'
  placeholder?: string
  required?: boolean
  options?: Array<{ value: string | number; label: string }>
  rows?: number
  validation?: (value: any) => string | null
}

interface FormModalProps {
  visible: boolean
  title: string
  subtitle?: string
  icon?: string
  fields: FormField[]
  initialData?: Record<string, any>
  submitText?: string
  submitIcon?: string
  cancelText?: string
  loading?: boolean
  loadingText?: string
}

interface FormModalEmits {
  (e: 'close'): void
  (e: 'cancel'): void
  (e: 'submit', data: Record<string, any>): void
}

const props = withDefaults(defineProps<FormModalProps>(), {
  icon: 'material-symbols:edit',
  submitText: '提交',
  submitIcon: 'material-symbols:check',
  cancelText: '取消',
  loading: false,
  loadingText: '提交中...'
})

const emit = defineEmits<FormModalEmits>()

// 响应式数据
const formData = ref<Record<string, any>>({})
const errors = ref<Record<string, any>>({})
const formError = ref<string>('')

// 初始化表单数据
watch(() => props.visible, (newValue) => {
  if (newValue) {
    // 重置表单数据
    formData.value = props.initialData ? { ...props.initialData } : {}
    errors.value = {}
    formError.value = ''
    
    // 初始化默认值
    props.fields.forEach(field => {
      if (formData.value[field.name] === undefined) {
        if (field.type === 'checkbox') {
          formData.value[field.name] = false
        } else if (field.type === 'select' || field.type === 'radio') {
          formData.value[field.name] = ''
        } else {
          formData.value[field.name] = ''
        }
      }
    })
  }
})

// 方法
const handleClose = () => {
  emit('close')
}

const handleCancel = () => {
  emit('cancel')
}

const validateField = (field: FormField, value: any): string | null => {
  // 必填验证
  if (field.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
    return `${field.label}是必填项`
  }
  
  // 自定义验证
  if (field.validation) {
    return field.validation(value)
  }
  
  return null
}

const validateForm = (): boolean => {
  errors.value = {}
  let isValid = true
  
  props.fields.forEach(field => {
    const error = validateField(field, formData.value[field.name])
    if (error) {
      errors.value[field.name] = error
      isValid = false
    }
  })
  
  return isValid
}

const handleSubmit = () => {
  if (validateForm()) {
    emit('submit', { ...formData.value })
  }
}
</script>

<style scoped>
.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: opacity 0.3s ease;
}

.modal-fade-enter-from,
.modal-fade-leave-to {
  opacity: 0;
}
</style> 