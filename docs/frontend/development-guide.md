# 🎨 前端开发指南

## 📋 目录

- [开发环境搭建](#开发环境搭建)
- [项目结构详解](#项目结构详解)
- [开发规范](#开发规范)
- [组件开发](#组件开发)
- [状态管理](#状态管理)
- [API集成](#api集成)
- [样式开发](#样式开发)
- [国际化](#国际化)
- [性能优化](#性能优化)
- [调试技巧](#调试技巧)

## 🛠️ 开发环境搭建

### 环境要求
- Node.js 16+
- npm/pnpm/yarn
- VS Code (推荐)
- Vue DevTools 浏览器扩展

### 推荐的VS Code扩展
```json
{
  "recommendations": [
    "vue.volar",
    "vue.typescript-vue-plugin",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-typescript-next"
  ]
}
```

### 环境变量配置
```bash
# .env.local
NUXT_PUBLIC_API_BASE_URL=http://localhost:8000
NUXT_PUBLIC_WS_URL=http://localhost:4000
NUXT_PUBLIC_STEAM_API_KEY=your_steam_api_key
```

## 📁 项目结构详解

### 组件分类
```
components/
├── auth/          # 认证相关组件
├── battle/        # 对战系统组件
├── case/          # 开箱系统组件
├── common/        # 通用业务组件
├── demo/          # 演示组件
├── home/          # 首页组件
├── skin/          # 皮肤相关组件
└── ui/            # 基础UI组件
```

### 页面路由
```
pages/
├── index.vue                    # 首页
├── auth/
│   ├── login.vue               # 登录页
│   └── register.vue            # 注册页
├── battle/
│   ├── index.vue               # 对战大厅
│   └── [id].vue                # 对战房间
├── cases/
│   ├── index.vue               # 箱子列表
│   └── [id].vue                # 箱子详情
└── profile/
    ├── index.vue               # 个人中心
    ├── inventory.vue           # 库存管理
    └── settings.vue            # 设置页面
```

## 📝 开发规范

### 命名规范
```typescript
// 组件命名 - PascalCase
export default defineComponent({
  name: 'CaseOpeningAnimation'
})

// 文件命名 - kebab-case
case-opening-animation.vue
use-battle-state.ts

// 变量命名 - camelCase
const userBalance = ref(0)
const isLoading = ref(false)

// 常量命名 - UPPER_SNAKE_CASE
const API_ENDPOINTS = {
  CASES: '/api/cases/',
  BATTLE: '/api/battle/'
}
```

### 组件结构
```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup lang="ts">
// 1. 导入
import { ref, computed, onMounted } from 'vue'
import type { CaseItem } from '~/types/case'

// 2. 类型定义
interface Props {
  caseId: number
  autoOpen?: boolean
}

// 3. Props和Emits
const props = withDefaults(defineProps<Props>(), {
  autoOpen: false
})

const emit = defineEmits<{
  opened: [result: CaseItem]
  error: [message: string]
}>()

// 4. 响应式数据
const isOpening = ref(false)
const result = ref<CaseItem | null>(null)

// 5. 计算属性
const canOpen = computed(() => !isOpening.value && props.caseId > 0)

// 6. 方法
const openCase = async () => {
  // 实现逻辑
}

// 7. 生命周期
onMounted(() => {
  if (props.autoOpen) {
    openCase()
  }
})
</script>

<style scoped>
/* 组件样式 */
</style>
```

## 🧩 组件开发

### 基础组件示例
```vue
<!-- components/ui/BaseButton.vue -->
<template>
  <button
    :class="buttonClasses"
    :disabled="disabled || loading"
    @click="handleClick"
  >
    <Icon v-if="loading" name="loading" class="animate-spin" />
    <Icon v-else-if="icon" :name="icon" />
    <slot />
  </button>
</template>

<script setup lang="ts">
interface Props {
  variant?: 'primary' | 'secondary' | 'danger'
  size?: 'sm' | 'md' | 'lg'
  icon?: string
  loading?: boolean
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'primary',
  size: 'md'
})

const emit = defineEmits<{
  click: [event: MouseEvent]
}>()

const buttonClasses = computed(() => [
  'btn',
  `btn-${props.variant}`,
  `btn-${props.size}`,
  {
    'btn-loading': props.loading,
    'btn-disabled': props.disabled
  }
])

const handleClick = (event: MouseEvent) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}
</script>
```

### 业务组件示例
```vue
<!-- components/case/CaseCard.vue -->
<template>
  <div class="case-card" @click="openCase">
    <div class="case-image">
      <NuxtImg
        :src="caseData.image"
        :alt="caseData.name"
        loading="lazy"
      />
    </div>
    <div class="case-info">
      <h3>{{ caseData.name }}</h3>
      <p class="price">{{ formatPrice(caseData.price) }}</p>
    </div>
    <BaseButton
      variant="primary"
      :loading="isOpening"
      @click.stop="openCase"
    >
      {{ $t('case.open') }}
    </BaseButton>
  </div>
</template>

<script setup lang="ts">
import type { Case } from '~/types/case'

interface Props {
  caseData: Case
}

const props = defineProps<Props>()
const { $t } = useI18n()
const { formatPrice } = useUtils()

const isOpening = ref(false)

const openCase = async () => {
  isOpening.value = true
  try {
    // 开箱逻辑
    await $fetch(`/api/cases/${props.caseData.id}/open/`, {
      method: 'POST'
    })
  } finally {
    isOpening.value = false
  }
}
</script>
```

## 🗃️ 状态管理

### Pinia Store示例
```typescript
// stores/user.ts
export const useUserStore = defineStore('user', () => {
  // State
  const user = ref<User | null>(null)
  const isAuthenticated = ref(false)
  const balance = ref(0)

  // Getters
  const userLevel = computed(() => {
    if (!user.value) return 0
    return Math.floor(user.value.experience / 1000)
  })

  // Actions
  const login = async (credentials: LoginCredentials) => {
    try {
      const response = await $fetch('/api/auth/login/', {
        method: 'POST',
        body: credentials
      })
      
      user.value = response.user
      isAuthenticated.value = true
      
      // 保存token
      const token = useCookie('auth-token')
      token.value = response.token
      
      return response
    } catch (error) {
      throw error
    }
  }

  const logout = async () => {
    try {
      await $fetch('/api/auth/logout/', { method: 'POST' })
    } finally {
      user.value = null
      isAuthenticated.value = false
      
      // 清除token
      const token = useCookie('auth-token')
      token.value = null
      
      await navigateTo('/auth/login')
    }
  }

  const updateBalance = (amount: number) => {
    balance.value = amount
  }

  return {
    // State
    user: readonly(user),
    isAuthenticated: readonly(isAuthenticated),
    balance: readonly(balance),
    
    // Getters
    userLevel,
    
    // Actions
    login,
    logout,
    updateBalance
  }
})
```

### 组合式函数示例
```typescript
// composables/useBattleState.ts
export const useBattleState = () => {
  const battleStore = useBattleStore()
  const { $socket } = useNuxtApp()

  const joinBattle = async (roomId: string) => {
    try {
      const response = await $fetch(`/api/battle/join/`, {
        method: 'POST',
        body: { room_id: roomId }
      })
      
      battleStore.setCurrentRoom(response.room)
      
      // 加入WebSocket房间
      $socket.emit('join_battle', { room_id: roomId })
      
      return response
    } catch (error) {
      throw error
    }
  }

  const leaveBattle = () => {
    const currentRoom = battleStore.currentRoom
    if (currentRoom) {
      $socket.emit('leave_battle', { room_id: currentRoom.id })
      battleStore.clearCurrentRoom()
    }
  }

  return {
    joinBattle,
    leaveBattle,
    currentRoom: computed(() => battleStore.currentRoom),
    isInBattle: computed(() => !!battleStore.currentRoom)
  }
}
```

## 🔌 API集成

### API客户端配置
```typescript
// utils/api-client.ts
export const apiClient = $fetch.create({
  baseURL: useRuntimeConfig().public.apiBaseUrl,
  
  onRequest({ request, options }) {
    // 添加认证token
    const token = useCookie('auth-token')
    if (token.value) {
      options.headers = {
        ...options.headers,
        Authorization: `Bearer ${token.value}`
      }
    }
  },
  
  onResponseError({ response }) {
    // 处理认证错误
    if (response.status === 401) {
      // 清除token并跳转到登录页
      const token = useCookie('auth-token')
      token.value = null
      navigateTo('/auth/login')
    }
  }
})
```

### API服务示例
```typescript
// services/case-api.ts
export class CaseAPI {
  static async getCases(params?: CaseListParams) {
    return await apiClient<CaseListResponse>('/api/cases/', {
      query: params
    })
  }

  static async getCaseDetail(id: number) {
    return await apiClient<Case>(`/api/cases/${id}/`)
  }

  static async openCase(id: number) {
    return await apiClient<CaseOpenResult>(`/api/cases/${id}/open/`, {
      method: 'POST'
    })
  }

  static async getCaseHistory(params?: HistoryParams) {
    return await apiClient<CaseHistoryResponse>('/api/cases/history/', {
      query: params
    })
  }
}
```

## 🎨 样式开发

### Tailwind CSS使用
```vue
<template>
  <!-- 响应式设计 -->
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <!-- 网格布局 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <!-- 卡片组件 -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <!-- 渐变背景 -->
        <div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4 rounded">
          <!-- 动画效果 -->
          <button class="transform hover:scale-105 transition-transform duration-200">
            点击我
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
```

### 自定义样式
```scss
// assets/css/components.scss
.case-card {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden;
  @apply transform hover:scale-105 transition-all duration-300;
  
  &:hover {
    @apply shadow-xl;
  }
  
  .case-image {
    @apply relative overflow-hidden;
    
    img {
      @apply w-full h-48 object-cover;
    }
  }
  
  .case-info {
    @apply p-4;
    
    h3 {
      @apply text-lg font-semibold text-gray-900 dark:text-white;
    }
    
    .price {
      @apply text-blue-600 dark:text-blue-400 font-bold;
    }
  }
}
```

## 🌍 国际化

### 语言文件
```json
// locales/zh-hans.json
{
  "common": {
    "loading": "加载中...",
    "error": "错误",
    "success": "成功",
    "confirm": "确认",
    "cancel": "取消"
  },
  "case": {
    "open": "开启",
    "opening": "开启中...",
    "result": "开启结果",
    "history": "开启历史"
  },
  "battle": {
    "join": "加入对战",
    "create": "创建房间",
    "waiting": "等待玩家...",
    "started": "对战开始"
  }
}
```

### 使用示例
```vue
<template>
  <div>
    <!-- 基础翻译 -->
    <h1>{{ $t('case.title') }}</h1>
    
    <!-- 带参数的翻译 -->
    <p>{{ $t('case.price', { amount: formatPrice(100) }) }}</p>
    
    <!-- 复数形式 -->
    <p>{{ $t('case.count', caseCount) }}</p>
  </div>
</template>

<script setup lang="ts">
const { t } = useI18n()
const { formatPrice } = useUtils()

// 在JavaScript中使用
const message = t('common.success')
</script>
```

## ⚡ 性能优化

### 代码分割
```typescript
// 路由级别的懒加载
const BattlePage = defineAsyncComponent(() => import('~/pages/battle/index.vue'))

// 组件级别的懒加载
const HeavyComponent = defineAsyncComponent({
  loader: () => import('~/components/HeavyComponent.vue'),
  loadingComponent: LoadingSpinner,
  errorComponent: ErrorComponent,
  delay: 200,
  timeout: 3000
})
```

### 图片优化
```vue
<template>
  <!-- 使用 NuxtImg 自动优化 -->
  <NuxtImg
    src="/images/case-bg.jpg"
    alt="Case Background"
    width="400"
    height="300"
    loading="lazy"
    placeholder
    format="webp"
  />
  
  <!-- 响应式图片 -->
  <NuxtPicture
    src="/images/hero.jpg"
    :img-attrs="{
      class: 'w-full h-auto',
      alt: 'Hero Image'
    }"
  />
</template>
```

### 缓存策略
```typescript
// composables/useCache.ts
export const useCache = () => {
  const cache = new Map()
  
  const get = <T>(key: string): T | undefined => {
    const item = cache.get(key)
    if (item && item.expires > Date.now()) {
      return item.data
    }
    cache.delete(key)
    return undefined
  }
  
  const set = <T>(key: string, data: T, ttl = 5 * 60 * 1000) => {
    cache.set(key, {
      data,
      expires: Date.now() + ttl
    })
  }
  
  return { get, set }
}
```

## 🐛 调试技巧

### Vue DevTools
```typescript
// 在组件中添加调试信息
export default defineComponent({
  name: 'CaseCard',
  setup() {
    // 开发环境下暴露到全局
    if (process.dev) {
      (window as any).caseCardDebug = {
        props,
        state: reactive({ isOpening, result })
      }
    }
  }
})
```

### 错误处理
```vue
<script setup lang="ts">
// 全局错误处理
const handleError = (error: Error, instance: any, info: string) => {
  console.error('Component Error:', error)
  console.error('Instance:', instance)
  console.error('Info:', info)
  
  // 发送错误到监控服务
  if (process.client) {
    // 错误上报逻辑
  }
}

// 异步错误处理
const asyncOperation = async () => {
  try {
    await someAsyncFunction()
  } catch (error) {
    console.error('Async Error:', error)
    // 显示用户友好的错误信息
    showNotification({
      type: 'error',
      message: t('common.error')
    })
  }
}
</script>
```

### 性能监控
```typescript
// composables/usePerformance.ts
export const usePerformance = () => {
  const measureTime = (name: string, fn: () => void | Promise<void>) => {
    const start = performance.now()
    
    const result = fn()
    
    if (result instanceof Promise) {
      return result.finally(() => {
        const end = performance.now()
        console.log(`${name} took ${end - start} milliseconds`)
      })
    } else {
      const end = performance.now()
      console.log(`${name} took ${end - start} milliseconds`)
      return result
    }
  }
  
  return { measureTime }
}
```

## 📚 相关资源

- [Vue 3 官方文档](https://vuejs.org/)
- [Nuxt 3 官方文档](https://nuxt.com/)
- [Tailwind CSS 文档](https://tailwindcss.com/)
- [Pinia 状态管理](https://pinia.vuejs.org/)
- [GSAP 动画库](https://greensock.com/gsap/)
