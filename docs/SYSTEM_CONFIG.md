# 🔧 系统配置信息

## 📍 关键路径配置

### 工作目录
```
根目录: /www/wwwroot/csgoskins.com.cn/
服务器目录: /www/wwwroot/csgoskins.com.cn/server/
前端目录: /www/wwwroot/csgoskins.com.cn/ui/
```

### 重要配置文件
```
主配置: server/steambase/settings.py
URL路由: server/steambase/urls.py
数据库: server/steambase/base_settings.py
Redis: server/steambase/redis_con.py
Celery: server/steambase/celery.py
进程管理: supervisord.conf
Docker: docker-compose.yml
```

### 日志文件位置
```
应用日志: logs/gunicorn.log
错误日志: logs/gunicorn_error.log
Celery日志: logs/celery_worker.log
定时任务: logs/celery_beat.log
进程管理: logs/supervisord.log
```

## 🗄️ 数据库配置

### 主数据库
```
类型: MySQL
配置位置: server/steambase/settings.py
连接: env.db(default='mysql://root:@127.0.0.1:3306/csgogo')
```

### 缓存系统
```
类型: Redis
配置位置: server/steambase/settings.py
连接: env.cache('REDIS_URL', default='rediscache://127.0.0.1:6379/0')
管理: server/steambase/redis_con.py
```

## 🌐 网络配置

### 端口配置
```
Django: 8000 (开发) / 80,443 (生产)
Redis: 6379
MySQL: 3306
WebSocket: 通过Django Channels
```

### 域名和CORS
```
ALLOWED_HOSTS: 在settings.py中配置
CORS设置: 支持跨域请求
静态文件: /static/ 路径
媒体文件: /media/ 路径
```

## 📦 依赖管理

### Python依赖
```
文件: server/requirements.txt
虚拟环境: server/venv/
激活脚本: server/activate_env.sh
包管理: pip install/uninstall
```

### Node.js依赖
```
文件: ui/package.json
依赖目录: ui/node_modules/
包管理: npm install/uninstall
构建: npm run build
```

## 🔐 安全配置

### 认证系统
```
Django Auth: authentication/models.py
Steam OAuth: authentication/pipeline.py
权限控制: Django权限系统
会话管理: Redis存储
```

### 敏感信息
```
环境变量: .env文件
SECRET_KEY: Django密钥
数据库密码: 环境变量
API密钥: 阿里云OSS等
```

## 🎮 应用模块配置

### 已安装应用
```python
INSTALLED_APPS = [
    'modeltranslation',      # 模型翻译
    'django.contrib.admin',  # 管理后台
    'rest_framework',        # API框架
    'social_django',         # 社交认证
    'authentication',        # 用户认证
    'articles',             # 内容管理
    'package',              # 物品管理
    'box',                  # 开箱系统
    'websocket',            # WebSocket
    'ckeditor',             # 富文本编辑器
    'django_celery_beat',   # 定时任务
    # ... 其他应用
]
```

### 中间件配置
```python
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'social_django.middleware.SocialAuthExceptionMiddleware',
]
```

## 🔄 任务队列配置

### Celery配置
```python
# 任务路由
task_routes = {
    'authentication.tasks.*': {'queue': 'auth'},
    'charge.tasks.*': {'queue': 'charge'},
    'box.tasks.*': {'queue': 'box'},
    'package.tasks.*': {'queue': 'package'},
}

# 定时任务
CELERY_BEAT_SCHEDULE = {
    'update-daily-recharge-limit': {
        'task': 'sitecfg.tasks.update_daily_recharge_limit',
        'schedule': crontab(hour=9, minute=0),
    },
}
```

### thworker框架
```
自定义任务框架: server/thworker/
任务定义: server/thworker/tasks.py
管理命令: python manage.py setupworker
```

## 🌍 国际化配置

### 语言设置
```python
LANGUAGE_CODE = 'zh-hans'
TIME_ZONE = 'Asia/Shanghai'
USE_I18N = True
USE_L10N = True
USE_TZ = True

LOCALE_PATHS = (
    project_root('locale'),
)
```

### 翻译文件
```
后端翻译: server/locale/zh_hans/LC_MESSAGES/django.po
前端翻译: ui/locales/zh-hans.json
模型翻译: 各应用的translation.py
```

## 📁 静态文件配置

### Django静态文件
```python
STATIC_URL = '/static/'
STATIC_ROOT = project_root('static')
MEDIA_URL = '/media/'
MEDIA_ROOT = project_root('media')
```

### 前端资源
```
构建输出: ui/.output/
静态资源: ui/public/
组件: ui/components/
样式: ui/assets/css/
```

## 🔌 WebSocket配置

### Django Channels
```python
ASGI_APPLICATION = 'steambase.routing.application'
CHANNEL_LAYERS = {
    'default': {
        'BACKEND': 'channels_redis.core.RedisChannelLayer',
        'CONFIG': {'hosts': [('127.0.0.1', 6379)]},
    },
}
```

### 路由配置
```
WebSocket路由: server/steambase/routing.py
消费者: server/websocket/consumers.py
前端连接: ui/plugins/socket.client.ts
```

## 🐳 Docker配置

### 服务配置
```yaml
# docker-compose.yml
services:
  web:        # Django应用
  redis:      # Redis缓存
  mysql:      # MySQL数据库
  nginx:      # 反向代理 (可选)
```

### 进程管理
```ini
# supervisord.conf
[program:steambase_server]    # Web服务
[program:celery_worker]       # Celery工作进程
[program:celery_beat]         # 定时任务
```

## 📊 监控配置

### 健康检查
```
端点: /api/health/
监控模块: server/monitor/
日志监控: logs/ 目录
性能监控: Redis + 自定义指标
```

### 错误追踪
```
Django日志: settings.py LOGGING配置
应用错误: 各模块的异常处理
系统错误: supervisord和Docker日志
```

---

**📝 配置更新提醒**:
- 修改配置后需要重启相关服务
- 数据库配置变更需要重新迁移
- 静态文件变更需要重新收集
- 环境变量变更需要重启容器
