# 前端开发指南

## 目录
- [技术栈概览](#技术栈概览)
- [项目结构](#项目结构)
- [开发环境设置](#开发环境设置)
- [API集成](#api集成)
- [WebSocket集成](#websocket集成)
- [组件开发规范](#组件开发规范)
- [状态管理](#状态管理)
- [样式指南](#样式指南)
- [国际化](#国际化)
- [性能优化](#性能优化)
- [测试策略](#测试策略)

## 技术栈概览

### 推荐技术栈
```json
{
  "framework": "React 18+ / Vue 3+",
  "language": "TypeScript",
  "bundler": "Vite / Webpack 5",
  "css": "Tailwind CSS / SCSS",
  "state": "Redux Toolkit / Zustand / Pinia",
  "router": "React Router / Vue Router",
  "http": "Axios",
  "websocket": "Socket.IO Client",
  "ui": "Ant Design / Element Plus",
  "animation": "Framer Motion / CSS Animations"
}
```

### 浏览器支持
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 项目结构

### 推荐目录结构
```
src/
├── components/          # 通用组件
│   ├── ui/             # 基础UI组件
│   ├── layout/         # 布局组件
│   └── common/         # 通用业务组件
├── pages/              # 页面组件
│   ├── auth/           # 认证相关页面
│   ├── trading/        # 交易相关页面
│   ├── games/          # 游戏相关页面
│   └── profile/        # 用户相关页面
├── hooks/              # 自定义Hooks
├── services/           # API服务
├── store/              # 状态管理
├── utils/              # 工具函数
├── types/              # TypeScript类型定义
├── constants/          # 常量定义
├── assets/             # 静态资源
└── styles/             # 样式文件
```

## 开发环境设置

### 1. 创建React项目
```bash
# 使用Vite创建React项目
npm create vite@latest csgo-frontend -- --template react-ts
cd csgo-frontend
npm install

# 安装依赖
npm install axios socket.io-client @reduxjs/toolkit react-redux
npm install -D @types/socket.io-client tailwindcss autoprefixer postcss
```

### 2. 配置环境变量
```bash
# .env.development
VITE_API_BASE_URL=http://localhost:9000/api
VITE_WS_URL=ws://localhost:4000
VITE_STEAM_RETURN_URL=http://localhost:3000/auth/steam/return

# .env.production
VITE_API_BASE_URL=https://api.cs2.net.cn/api
VITE_WS_URL=wss://socket.cs2.net.cn
VITE_STEAM_RETURN_URL=https://cs2.net.cn/auth/steam/return
```

### 3. 配置Tailwind CSS
```javascript
// tailwind.config.js
module.exports = {
  content: ['./src/**/*.{js,jsx,ts,tsx}'],
  theme: {
    extend: {
      colors: {
        primary: '#1890ff',
        secondary: '#722ed1',
        success: '#52c41a',
        warning: '#faad14',
        error: '#f5222d',
      },
      animation: {
        'spin-slow': 'spin 3s linear infinite',
        'pulse-fast': 'pulse 1s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      }
    },
  },
  plugins: [],
}
```

## API集成

### 1. API服务配置
```typescript
// src/services/api.ts
import axios from 'axios';

const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 10000,
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// 响应拦截器
api.interceptors.response.use(
  (response) => response.data,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export default api;
```

### 2. API服务封装
```typescript
// src/services/authService.ts
import api from './api';

export interface LoginData {
  username: string;
  password: string;
}

export interface UserProfile {
  id: number;
  username: string;
  steam_id: string;
  avatar: string;
  balance: number;
}

export const authService = {
  // Steam登录
  steamLogin: () => {
    window.location.href = `${import.meta.env.VITE_API_BASE_URL}/auth/steam/`;
  },

  // 获取用户信息
  getProfile: (): Promise<UserProfile> => 
    api.get('/auth/profile/'),

  // 登出
  logout: () => 
    api.post('/auth/logout/'),
};

// src/services/tradingService.ts
export const tradingService = {
  // 获取库存
  getInventory: (page = 1) => 
    api.get(`/trading/inventory/?page=${page}`),

  // 创建交易订单
  createTrade: (data: any) => 
    api.post('/trading/orders/', data),

  // 获取交易历史
  getTradeHistory: (page = 1) => 
    api.get(`/trading/history/?page=${page}`),
};
```

### 3. React Query集成
```typescript
// src/hooks/useAuth.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { authService } from '../services/authService';

export const useAuth = () => {
  const queryClient = useQueryClient();

  const profileQuery = useQuery({
    queryKey: ['profile'],
    queryFn: authService.getProfile,
    retry: false,
  });

  const logoutMutation = useMutation({
    mutationFn: authService.logout,
    onSuccess: () => {
      localStorage.removeItem('token');
      queryClient.clear();
      window.location.href = '/login';
    },
  });

  return {
    user: profileQuery.data,
    isLoading: profileQuery.isLoading,
    isError: profileQuery.isError,
    logout: logoutMutation.mutate,
  };
};
```

## WebSocket集成

### 1. WebSocket服务
```typescript
// src/services/websocket.ts
import { io, Socket } from 'socket.io-client';

class WebSocketService {
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  connect(token: string) {
    if (this.socket?.connected) return;

    this.socket = io(import.meta.env.VITE_WS_URL, {
      auth: { token },
      transports: ['websocket'],
    });

    this.setupEventListeners();
  }

  private setupEventListeners() {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('WebSocket connected');
      this.reconnectAttempts = 0;
    });

    this.socket.on('disconnect', () => {
      console.log('WebSocket disconnected');
      this.handleReconnect();
    });

    this.socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error);
      this.handleReconnect();
    });
  }

  private handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      setTimeout(() => {
        this.socket?.connect();
      }, 1000 * this.reconnectAttempts);
    }
  }

  // 游戏相关事件
  joinGameRoom(gameType: string, roomId: string) {
    this.socket?.emit('join_room', { game_type: gameType, room_id: roomId });
  }

  onGameUpdate(callback: (data: any) => void) {
    this.socket?.on('game_update', callback);
  }

  onBalanceUpdate(callback: (balance: number) => void) {
    this.socket?.on('balance_update', callback);
  }

  disconnect() {
    this.socket?.disconnect();
    this.socket = null;
  }
}

export const wsService = new WebSocketService();
```

### 2. WebSocket Hook
```typescript
// src/hooks/useWebSocket.ts
import { useEffect, useRef } from 'react';
import { wsService } from '../services/websocket';

export const useWebSocket = (token?: string) => {
  const isConnected = useRef(false);

  useEffect(() => {
    if (token && !isConnected.current) {
      wsService.connect(token);
      isConnected.current = true;
    }

    return () => {
      if (isConnected.current) {
        wsService.disconnect();
        isConnected.current = false;
      }
    };
  }, [token]);

  return wsService;
};
```

## 组件开发规范

### 1. 组件结构
```typescript
// src/components/GameCard/GameCard.tsx
import React from 'react';
import { GameCardProps } from './GameCard.types';
import styles from './GameCard.module.scss';

const GameCard: React.FC<GameCardProps> = ({
  title,
  image,
  players,
  minBet,
  onClick,
  className,
}) => {
  return (
    <div 
      className={`${styles.gameCard} ${className}`}
      onClick={onClick}
      role="button"
      tabIndex={0}
    >
      <div className={styles.imageContainer}>
        <img src={image} alt={title} />
        <div className={styles.overlay}>
          <span className={styles.players}>{players} 在线</span>
        </div>
      </div>
      <div className={styles.content}>
        <h3 className={styles.title}>{title}</h3>
        <p className={styles.minBet}>最低下注: ¥{minBet}</p>
      </div>
    </div>
  );
};

export default GameCard;
```

### 2. 类型定义
```typescript
// src/components/GameCard/GameCard.types.ts
export interface GameCardProps {
  title: string;
  image: string;
  players: number;
  minBet: number;
  onClick: () => void;
  className?: string;
}
```

### 3. 样式文件
```scss
// src/components/GameCard/GameCard.module.scss
.gameCard {
  @apply bg-white rounded-lg shadow-md overflow-hidden cursor-pointer transition-transform duration-200 hover:scale-105;

  .imageContainer {
    @apply relative;

    img {
      @apply w-full h-48 object-cover;
    }

    .overlay {
      @apply absolute top-2 right-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded;
    }
  }

  .content {
    @apply p-4;

    .title {
      @apply text-lg font-semibold mb-2;
    }

    .minBet {
      @apply text-gray-600;
    }
  }
}
```

## 状态管理

### 1. Redux Toolkit配置
```typescript
// src/store/index.ts
import { configureStore } from '@reduxjs/toolkit';
import authSlice from './slices/authSlice';
import gameSlice from './slices/gameSlice';

export const store = configureStore({
  reducer: {
    auth: authSlice,
    game: gameSlice,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
```

### 2. Auth Slice
```typescript
// src/store/slices/authSlice.ts
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { authService } from '../../services/authService';

export const fetchProfile = createAsyncThunk(
  'auth/fetchProfile',
  async () => {
    const response = await authService.getProfile();
    return response;
  }
);

const authSlice = createSlice({
  name: 'auth',
  initialState: {
    user: null,
    isLoading: false,
    error: null,
  },
  reducers: {
    logout: (state) => {
      state.user = null;
    },
    updateBalance: (state, action) => {
      if (state.user) {
        state.user.balance = action.payload;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchProfile.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(fetchProfile.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload;
      })
      .addCase(fetchProfile.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message;
      });
  },
});

export const { logout, updateBalance } = authSlice.actions;
export default authSlice.reducer;
```

## 样式指南

### 1. 设计系统
```scss
// src/styles/variables.scss
:root {
  // 颜色
  --color-primary: #1890ff;
  --color-secondary: #722ed1;
  --color-success: #52c41a;
  --color-warning: #faad14;
  --color-error: #f5222d;
  
  // 间距
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  // 字体大小
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 24px;
  
  // 圆角
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  
  // 阴影
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.2);
}
```

### 2. 响应式设计
```scss
// src/styles/mixins.scss
@mixin mobile {
  @media (max-width: 768px) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: 769px) and (max-width: 1024px) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: 1025px) {
    @content;
  }
}
```

## 性能优化

### 1. 代码分割
```typescript
// src/pages/index.ts
import { lazy } from 'react';

export const HomePage = lazy(() => import('./Home/HomePage'));
export const TradingPage = lazy(() => import('./Trading/TradingPage'));
export const GamePage = lazy(() => import('./Games/GamePage'));
```

### 2. 图片优化
```typescript
// src/components/LazyImage/LazyImage.tsx
import React, { useState, useCallback } from 'react';
import { useIntersectionObserver } from '../../hooks/useIntersectionObserver';

interface LazyImageProps {
  src: string;
  alt: string;
  placeholder?: string;
  className?: string;
}

const LazyImage: React.FC<LazyImageProps> = ({
  src,
  alt,
  placeholder = '/images/placeholder.jpg',
  className,
}) => {
  const [imageSrc, setImageSrc] = useState(placeholder);
  const [imageRef, isIntersecting] = useIntersectionObserver({
    threshold: 0.1,
  });

  const handleLoad = useCallback(() => {
    if (isIntersecting) {
      setImageSrc(src);
    }
  }, [isIntersecting, src]);

  return (
    <img
      ref={imageRef}
      src={imageSrc}
      alt={alt}
      className={className}
      onLoad={handleLoad}
    />
  );
};

export default LazyImage;
```

### 3. 缓存策略
```typescript
// src/utils/cache.ts
class Cache {
  private cache = new Map();
  private ttl = new Map();

  set(key: string, value: any, ttlMs = 5 * 60 * 1000) {
    this.cache.set(key, value);
    this.ttl.set(key, Date.now() + ttlMs);
  }

  get(key: string) {
    if (this.ttl.get(key) < Date.now()) {
      this.cache.delete(key);
      this.ttl.delete(key);
      return null;
    }
    return this.cache.get(key);
  }

  clear() {
    this.cache.clear();
    this.ttl.clear();
  }
}

export const cache = new Cache();
```

## 测试策略

### 1. 单元测试
```typescript
// src/components/__tests__/GameCard.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import GameCard from '../GameCard/GameCard';

describe('GameCard', () => {
  const mockProps = {
    title: 'Crash Game',
    image: '/images/crash.jpg',
    players: 123,
    minBet: 10,
    onClick: jest.fn(),
  };

  it('renders game card correctly', () => {
    render(<GameCard {...mockProps} />);
    
    expect(screen.getByText('Crash Game')).toBeInTheDocument();
    expect(screen.getByText('123 在线')).toBeInTheDocument();
    expect(screen.getByText('最低下注: ¥10')).toBeInTheDocument();
  });

  it('calls onClick when clicked', () => {
    render(<GameCard {...mockProps} />);
    
    fireEvent.click(screen.getByRole('button'));
    expect(mockProps.onClick).toHaveBeenCalledTimes(1);
  });
});
```

### 2. 集成测试
```typescript
// src/pages/__tests__/TradingPage.test.tsx
import { render, screen, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import TradingPage from '../Trading/TradingPage';
import { store } from '../../store';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <Provider store={store}>
      <QueryClientProvider client={queryClient}>
        {component}
      </QueryClientProvider>
    </Provider>
  );
};

describe('TradingPage', () => {
  it('loads and displays trading data', async () => {
    renderWithProviders(<TradingPage />);
    
    await waitFor(() => {
      expect(screen.getByText('我的库存')).toBeInTheDocument();
    });
  });
});
```

## 部署配置

### 1. 构建配置
```javascript
// vite.config.ts
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  build: {
    outDir: 'dist',
    sourcemap: false,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          router: ['react-router-dom'],
          ui: ['antd'],
        },
      },
    },
  },
});
```

### 2. Docker配置
```dockerfile
# Dockerfile
FROM node:18-alpine as builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### 3. Nginx配置
```nginx
# nginx.conf
server {
    listen 80;
    server_name localhost;
    root /usr/share/nginx/html;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }

    location /api {
        proxy_pass http://backend:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    location /ws {
        proxy_pass http://websocket:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
```

这个前端开发指南提供了完整的开发框架和最佳实践，帮助前端开发者快速理解项目架构并开始开发工作。
