<template>
  <div class="simple-home-content">
    <div v-if="loading" class="text-center py-8">
      <div class="animate-spin w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
      <p class="text-gray-400">加载中...</p>
    </div>
    
    <div v-else>
      <!-- 统计数据显示 -->
      <div class="stats-section mb-6">
        <h3 class="text-lg font-semibold text-white mb-3">实时统计</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div class="stat-card bg-blue-600/20 border border-blue-500/30 rounded p-3">
            <div class="text-blue-400 text-2xl font-bold">{{ formatNumber(stats.user_number) }}</div>
            <div class="text-gray-300 text-sm">注册用户</div>
          </div>
          
          <div class="stat-card bg-green-600/20 border border-green-500/30 rounded p-3">
            <div class="text-green-400 text-2xl font-bold">{{ formatNumber(stats.online_number) }}</div>
            <div class="text-gray-300 text-sm">在线用户</div>
          </div>
          
          <div class="stat-card bg-purple-600/20 border border-purple-500/30 rounded p-3">
            <div class="text-purple-400 text-2xl font-bold">{{ formatNumber(stats.case_number) }}</div>
            <div class="text-gray-300 text-sm">开箱次数</div>
          </div>
          
          <div class="stat-card bg-orange-600/20 border border-orange-500/30 rounded p-3">
            <div class="text-orange-400 text-2xl font-bold">{{ formatNumber(stats.battle_number) }}</div>
            <div class="text-gray-300 text-sm">对战次数</div>
          </div>
        </div>
      </div>

      <!-- 最近开箱记录 -->
      <div class="records-section">
        <h3 class="text-lg font-semibold text-white mb-3">最近开箱</h3>
        <div v-if="caseRecords.length === 0" class="text-gray-400 text-center py-4">
          暂无开箱记录
        </div>
        <div v-else class="space-y-2">
          <div 
            v-for="record in caseRecords.slice(0, 5)" 
            :key="record.id"
            class="record-item bg-gray-700/50 border border-gray-600/30 rounded p-3 flex items-center justify-between hover:bg-gray-700/70 transition-colors"
          >
            <div class="flex items-center space-x-3">
              <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                <span class="text-white text-xs font-bold">
                  {{ (record.user_info?.profile?.nickname || 'User')[0].toUpperCase() }}
                </span>
              </div>
              <div>
                <div class="text-white text-sm font-medium">
                  {{ record.user_info?.profile?.nickname || 'Anonymous' }}
                </div>
                <div class="text-gray-400 text-xs">
                  开出了 {{ record.item_info?.name || '神秘物品' }}
                </div>
              </div>
            </div>
            
            <div class="text-right">
              <div class="text-green-400 font-medium text-sm">
                ${{ (record.price || 0).toFixed(2) }}
              </div>
              <div class="text-gray-500 text-xs">
                {{ formatTime(record.create_time) }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { StatsData, CaseRecord } from '~/services/monitor-api'

interface Props {
  stats: StatsData
  caseRecords: CaseRecord[]
  loading: boolean
}

defineProps<Props>()

// 格式化数字显示
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

// 格式化时间显示
const formatTime = (timestamp?: string): string => {
  if (!timestamp) return '刚刚'
  
  const now = new Date()
  const date = new Date(timestamp)
  const diff = Math.floor((now.getTime() - date.getTime()) / 1000)
  
  if (diff < 60) return '刚刚'
  if (diff < 3600) return `${Math.floor(diff / 60)}分钟前`
  if (diff < 86400) return `${Math.floor(diff / 3600)}小时前`
  
  return date.toLocaleDateString()
}
</script>

<style scoped>
.stat-card {
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.record-item {
  transition: all 0.2s ease;
}

.record-item:hover {
  transform: translateX(2px);
}
</style> 