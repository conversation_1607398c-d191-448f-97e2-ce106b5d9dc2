#!/bin/bash

# Docker部署脚本
# 用于启动完整的CSGOSkins.com.cn Docker环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker和Docker Compose
check_requirements() {
    log_info "检查系统要求..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    log_success "系统要求检查通过"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p logs/redis
    mkdir -p logs/django
    mkdir -p logs/celery
    mkdir -p logs/websocket
    mkdir -p server/static
    mkdir -p server/media
    
    # 设置权限
    chmod -R 755 logs/
    chmod -R 755 server/static
    chmod -R 755 server/media
    
    log_success "目录创建完成"
}

# 环境配置检查
check_environment() {
    log_info "检查环境配置..."
    
    if [ ! -f ".env" ]; then
        if [ -f ".env.docker" ]; then
            log_warning ".env文件不存在，使用.env.docker作为模板"
            cp .env.docker .env
            log_warning "请编辑.env文件，配置正确的数据库连接等信息"
        else
            log_error ".env文件不存在，请创建环境配置文件"
            exit 1
        fi
    fi
    
    log_success "环境配置检查完成"
}

# 构建镜像
build_images() {
    log_info "构建Docker镜像..."
    
    docker-compose build --no-cache
    
    log_success "镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动Docker服务..."
    
    # 首先启动Redis
    log_info "启动Redis服务..."
    docker-compose up -d redis
    
    # 等待Redis启动
    log_info "等待Redis服务启动..."
    sleep 10
    
    # 启动Web服务
    log_info "启动Web服务..."
    docker-compose up -d web
    
    # 等待Web服务启动
    log_info "等待Web服务启动..."
    sleep 20
    
    # 启动任务调度服务
    log_info "启动任务调度服务..."
    docker-compose up -d celery-worker celery-beat thworker
    
    # 启动WebSocket服务
    log_info "启动WebSocket服务..."
    docker-compose up -d websocket
    
    log_success "所有服务启动完成"
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."
    
    # 检查容器状态
    docker-compose ps
    
    # 检查健康状态
    log_info "检查服务健康状态..."
    
    # 检查Django健康状态
    if curl -f http://localhost:8000/api/health/ > /dev/null 2>&1; then
        log_success "Django服务健康"
    else
        log_warning "Django服务可能未就绪"
    fi
    
    # 检查WebSocket健康状态
    if curl -f http://localhost:4000/health > /dev/null 2>&1; then
        log_success "WebSocket服务健康"
    else
        log_warning "WebSocket服务可能未就绪"
    fi
    
    # 检查Redis连接
    if docker exec csgoskins-redis redis-cli ping > /dev/null 2>&1; then
        log_success "Redis服务健康"
    else
        log_warning "Redis服务可能未就绪"
    fi
}

# 显示日志
show_logs() {
    log_info "显示服务日志..."
    docker-compose logs --tail=50 -f
}

# 主函数
main() {
    log_info "开始部署CSGOSkins.com.cn Docker环境"
    
    check_requirements
    create_directories
    check_environment
    
    case "${1:-start}" in
        "build")
            build_images
            ;;
        "start")
            build_images
            start_services
            check_services
            ;;
        "stop")
            log_info "停止所有服务..."
            docker-compose down
            log_success "服务已停止"
            ;;
        "restart")
            log_info "重启所有服务..."
            docker-compose down
            start_services
            check_services
            ;;
        "logs")
            show_logs
            ;;
        "status")
            check_services
            ;;
        *)
            echo "用法: $0 {build|start|stop|restart|logs|status}"
            echo "  build   - 仅构建镜像"
            echo "  start   - 构建并启动所有服务（默认）"
            echo "  stop    - 停止所有服务"
            echo "  restart - 重启所有服务"
            echo "  logs    - 显示服务日志"
            echo "  status  - 检查服务状态"
            exit 1
            ;;
    esac
    
    log_success "操作完成"
}

# 执行主函数
main "$@"
