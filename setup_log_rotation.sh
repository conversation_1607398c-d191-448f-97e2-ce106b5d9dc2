#!/bin/bash
# Docker日志轮转设置脚本

echo "🔧 设置Docker日志轮转..."

# 1. 创建logrotate配置文件
cat > /etc/logrotate.d/docker-logs << 'EOF'
/var/lib/docker/containers/*/*.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 0644 root root
    postrotate
        /bin/kill -USR1 $(cat /var/run/docker.pid 2>/dev/null) 2>/dev/null || true
    endscript
}
EOF

echo "✅ 已创建logrotate配置文件"

# 2. 创建每日清理脚本
cat > /usr/local/bin/docker-logs-cleanup << 'EOF'
#!/bin/bash
# Docker日志每日清理脚本

LOG_DIR="/var/lib/docker/containers"
MAX_SIZE_MB=50

for container_dir in "$LOG_DIR"/*; do
    if [ -d "$container_dir" ]; then
        container_id=$(basename "$container_dir")
        log_file="$container_dir/$container_id-json.log"
        
        if [ -f "$log_file" ]; then
            size_mb=$(du -m "$log_file" | cut -f1)
            
            if [ "$size_mb" -gt "$MAX_SIZE_MB" ]; then
                echo "$(date): 清理容器 $container_id 日志 (${size_mb}MB)"
                
                # 备份最后1000行
                tail -n 1000 "$log_file" > "/tmp/${container_id}_backup.log"
                
                # 清空日志文件
                > "$log_file"
                
                # 恢复最后1000行
                cat "/tmp/${container_id}_backup.log" > "$log_file"
                rm "/tmp/${container_id}_backup.log"
                
                new_size_mb=$(du -m "$log_file" | cut -f1)
                echo "$(date): 清理完成 ${size_mb}MB -> ${new_size_mb}MB"
            fi
        fi
    fi
done
EOF

chmod +x /usr/local/bin/docker-logs-cleanup
echo "✅ 已创建每日清理脚本"

# 3. 添加到crontab
(crontab -l 2>/dev/null; echo "0 2 * * * /usr/local/bin/docker-logs-cleanup >> /var/log/docker-logs-cleanup.log 2>&1") | crontab -
echo "✅ 已添加定时任务 (每天凌晨2点执行)"

# 4. 创建全局Docker daemon配置
mkdir -p /etc/docker
cat > /etc/docker/daemon.json << 'EOF'
{
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "50m",
    "max-file": "3"
  }
}
EOF

echo "✅ 已创建Docker daemon日志配置"

# 5. 重启Docker服务以应用配置
echo "⚠️  需要重启Docker服务以应用全局日志配置"
echo "   运行: systemctl restart docker"
echo "   然后: docker-compose up -d"

echo ""
echo "🎉 Docker日志轮转设置完成！"
echo ""
echo "📋 配置摘要:"
echo "  - logrotate: 每日轮转，保留7天"
echo "  - 自动清理: 每天凌晨2点清理>50MB的日志"
echo "  - Docker配置: 单文件最大50MB，保留3个文件"
echo "  - docker-compose: 各服务已配置日志限制"
echo ""
echo "💡 使用方法:"
echo "  - 手动清理: /www/wwwroot/csgoskins.com.cn/docker_logs_cleanup.sh"
echo "  - 查看清理日志: tail -f /var/log/docker-logs-cleanup.log"
echo "  - 测试logrotate: logrotate -d /etc/logrotate.d/docker-logs"
