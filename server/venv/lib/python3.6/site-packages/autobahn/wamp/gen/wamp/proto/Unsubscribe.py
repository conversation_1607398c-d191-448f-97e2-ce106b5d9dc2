# automatically generated by the FlatBuffers compiler, do not modify

# namespace: proto

import flatbuffers

class Unsubscribe(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAsUnsubscribe(cls, buf, offset):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = Unsubscribe()
        x.Init(buf, n + offset)
        return x

    # Unsubscribe
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # Unsubscribe
    def Request(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint64Flags, o + self._tab.Pos)
        return 0

    # Unsubscribe
    def Subscription(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint64Flags, o + self._tab.Pos)
        return 0

def UnsubscribeStart(builder): builder.StartObject(2)
def UnsubscribeAddRequest(builder, request): builder.PrependUint64Slot(0, request, 0)
def UnsubscribeAddSubscription(builder, subscription): builder.PrependUint64Slot(1, subscription, 0)
def UnsubscribeEnd(builder): return builder.EndObject()
