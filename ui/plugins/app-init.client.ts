/**
 * 应用初始化插件
 * 在客户端初始化时恢复用户的语言设置
 */
export default defineNuxtPlugin(() => {
  // 只在客户端执行
  if (!process.client) return

  // 🎯 日志过滤已关闭，便于调试
  console.log('[🎰APP-INIT] 日志过滤已关闭，所有日志正常显示')

  // 延迟执行，确保i18n已经初始化
  nextTick(() => {
    const LANGUAGE_STORAGE_KEY = 'user-locale'
    
    try {
      // 从localStorage读取保存的语言
      const savedLocale = localStorage.getItem(LANGUAGE_STORAGE_KEY)
      if (savedLocale && (savedLocale === 'en' || savedLocale === 'zh-hans')) {
        // 获取当前i18n实例
        const { $i18n } = useNuxtApp()
        
        if ($i18n?.locale && $i18n.locale.value !== savedLocale) {
          $i18n.locale.value = savedLocale
          document.documentElement.setAttribute('lang', savedLocale)
        }
      }
    } catch (error) {
      console.error('应用初始化语言恢复失败:', error)
    }
  })
}) 