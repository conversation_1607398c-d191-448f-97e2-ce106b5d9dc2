from django.conf.urls import url
from agent import views
from .views import upload_to_oss


app_name = 'agent'
urlpatterns = [
    url(r'^checkagent/', views.CheckAgentView.as_view()),
    url(r'^site/$', views.AgentSiteListView.as_view()),
    url(r'^site/user/$', views.AgentSiteUserView.as_view()),
    url(r'^site/user/charge/$', views.AgentSiteUserChargeRecordView.as_view()),
    url(r'^site/user/withdraw/$', views.AgentSiteUserWithdrawRecordView.as_view()),
    url(r'^site/user/skin/$', views.AgentSiteUserSkinRecordView.as_view()),
    url(r'^manage/articlecategory/$', views.ManageAgentArticleCategoryyView.as_view()),
    url(r'^article/categorylist/', views.AgentArticleCategoryyListView.as_view()),
    url(r'^article/categorydetail/(?P<id>\d+)/$', views.GetAgentArticleCategoryyView.as_view()),
    url(r'^manage/article/$', views.ManageAgentArticleView.as_view()),
    url(r'^article/list/', views.GetAgentArticleListView.as_view()),
    url(r'^article/detail/(?P<id>\d+)/$', views.GetAgentArticleView.as_view()),
    url(r'^article/delete/', views.DeleteAgentArticleView.as_view()),
    url(r'^site/edit/', views.EditSiteView.as_view()),
    # 页面
    url(r'^site/detail/(?P<id>\d+)/$', views.GetSiteDetailView.as_view()),
    url(r'^site/articlelist/', views.GetAgentSiteArticleListView.as_view()),
    url(r'^balance/records/$', views.GetAgentBalanceRecordsView.as_view()),

    url(r'^info/$', views.GetAgentInfoView.as_view()),
    url(r'^info/edit/$', views.EditAgentInfoView.as_view()),
    url(r'^withdrawal/$', views.AgentWithdrawalView.as_view()),
    url(r'^withdrawal/records/$', views.GetAgentWithdrawalRecordsView.as_view()),
    
    url('upload/', upload_to_oss, name='upload_to_oss'),

]