import logging

from django.contrib import admin
from django.shortcuts import redirect
from django.views.generic import TemplateView
from django.utils.translation import gettext_lazy as _

from rest_framework.views import APIView
from rest_framework.permissions import AllowAny, IsAdminUser

from steambase.enums import RespCode
from steambase.utils import reformat_resp, current_user
from sitecfg.interfaces import get_maintenance, get_maintenance_charge
from monitor.business import get_monitor_data

_logger = logging.getLogger(__name__)


class MonitorView(TemplateView):
    template_name = 'admin/monitor.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update(admin.site.each_context(self.request))
        context['title'] = _("Monitor")
        return context

    def get(self, request, *args, **kwargs):
        if not request.user.is_superuser or not request.user.is_staff:
            return redirect('/system/')
        return super().get(request, *args, **kwargs)


class GetMonitorDataView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            num = request.query_params.get('num', 30)
            num = int(num)
            code, resp = get_monitor_data(num)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')
