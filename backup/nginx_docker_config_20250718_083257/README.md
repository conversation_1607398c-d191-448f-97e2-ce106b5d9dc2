# Nginx Docker配置备份说明

**备份时间**: 2025-07-18 08:32:57
**原路径**: `deployment/nginx/`
**备份原因**: 当前项目不使用Docker部署nginx，使用宝塔面板管理

## 备份内容

### 1. Docker配置
- `Dockerfile` - Nginx Docker镜像构建文件
- `steambase.conf` - 完整的nginx虚拟主机配置

### 2. SSL证书
- `cert/fullchain.pem` - SSL证书链
- `cert/privkey.pem` - SSL私钥

## 配置特点

### 重要的路由配置
- `/api/` → Django后端 (127.0.0.1:8000)
- `/auth/` → Django认证 (127.0.0.1:8000)
- `/social/` → 社交登录 (127.0.0.1:8000)
- `/socket.io/` → WebSocket服务 (127.0.0.1:4000)
- `/super/` → Django管理后台 (127.0.0.1:8000)
- `/static/` → 静态文件服务
- `/media/` → 媒体文件服务

### SSL配置
- 支持HTTP/2
- TLS 1.1/1.2/1.3
- 现代加密套件
- HSTS安全头

## 当前状态

### 实际使用的nginx
- **管理方式**: 宝塔面板
- **配置路径**: `/www/server/nginx/conf/`
- **运行状态**: 正常运行
- **SSL证书**: 通过宝塔面板管理

### 为什么移除Docker nginx
1. **简化部署**: 当前使用宝塔面板，无需Docker化nginx
2. **管理便利**: 宝塔面板提供图形化nginx管理
3. **证书管理**: 宝塔自动管理SSL证书续期
4. **性能优化**: 直接运行nginx性能更好

## 何时需要恢复

### 场景1: 完全Docker化部署
如果将来需要完全Docker化部署，可以恢复此配置：
```bash
cp -r backup/nginx_docker_config_20250718_083257 deployment/nginx
```

### 场景2: 配置参考
当前宝塔nginx配置可参考此文件的路由规则：
- WebSocket代理配置
- API路由规则
- 静态文件服务配置

### 场景3: SSL配置迁移
如需迁移到其他服务器，可参考SSL配置部分

## 注意事项

1. **证书有效性**: 备份的SSL证书可能已过期，使用前需检查
2. **路径调整**: 恢复时需根据实际部署路径调整配置
3. **端口冲突**: 确保Docker nginx不与宝塔nginx冲突
4. **域名配置**: 需要根据实际域名调整server_name

## 相关文件

- 当前项目结构: `deployment/node/` (仅保留Node.js WebSocket服务)
- 宝塔nginx配置: 通过宝塔面板管理
- Docker compose: 项目根目录的docker-compose.yml
