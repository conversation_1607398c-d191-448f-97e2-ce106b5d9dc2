import os,sys
import django
sys.path.append('./')
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "steambase.settings")
django.setup()

from datetime import datetime

from charge.models import ChargeLevel
from steambase.enums import CaseCategoryType
from box.models import CaseRecord

def check_user_free_case(user):
    user_level = 0
    charge_level = user.charge_level.all()
    if len(charge_level) > 0:
        user_level = charge_level[0].level
    if user_level < 1:
        return 
    now = datetime.now()
    early = datetime(now.year, now.month, now.day)
    records = CaseRecord.objects.filter(user=user, case__case_type=CaseCategoryType.Free.value,
                                        create_time__gte=early, create_time__lte=now).count()
    if records != 0:
        return 
    else:
        level = user_level
        if level == 1:
            user.extra.freebox_lv1_count = 1
        if level == 2:
            user.extra.freebox_lv2_count = 1
        if level == 3:
            user.extra.freebox_lv3_count = 1
        if level == 4:
            user.extra.freebox_lv4_count = 1
        if level == 5:
            user.extra.freebox_lv5_count = 1
        user.extra.save()


def main():
    cl = ChargeLevel.objects.filter(level__gte=1)
    for item in cl:
        check_user_free_case(item.user)

if __name__ == "__main__":
    main()