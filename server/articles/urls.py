from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer

from . import views

app_name = 'articles'

urlpatterns = [
    # 分类和标签
    path('categories/', views.ContentCategoryListView.as_view(), name='category-list'),
    path('tags/', views.ContentTagListView.as_view(), name='tag-list'),
    
    # 内容
    path('contents/', views.ContentListView.as_view(), name='content-list'),
    path('contents/<slug:slug>/', views.ContentDetailView.as_view(), name='content-detail'),
    
    # 公告
    path('announcements/', views.AnnouncementListView.as_view(), name='announcement-list'),
    path('announcements/homepage/', views.homepage_announcements, name='homepage-announcements'),
    
    # 推荐内容
    path('featured/', views.FeaturedContentView.as_view(), name='featured-content'),
    
    # 按分类和标签获取内容
    path('category/<slug:category_slug>/', views.content_by_category, name='content-by-category'),
    path('tag/<slug:tag_slug>/', views.content_by_tag, name='content-by-tag'),
]
