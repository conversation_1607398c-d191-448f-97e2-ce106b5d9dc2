# 📁 文件组织规范

本文档定义了项目的文件组织规范，确保代码库的整洁和可维护性。

## 🗂️ 目录结构规范

### 根目录结构
```
/
├── server/                 # Django后端应用
├── ui/                     # Nuxt.js前端应用
├── scripts/                # 所有脚本文件的统一存放位置
├── docs/                   # 项目文档
├── backup/                 # 备份文件
├── logs/                   # 日志文件
├── deployment/             # 部署相关文件
├── supervisord.conf        # 主要的supervisord配置
└── docker-compose.yml      # Docker编排文件
```

### Scripts目录组织
```
scripts/
├── tools/                  # 工具脚本
├── fixes/                  # 修复脚本
├── maintenance/            # 维护脚本
├── monitoring/             # 监控脚本
├── debug/                  # 调试脚本
├── tests/                  # 测试脚本
├── ckeditor/              # CKEditor相关脚本
└── README.md              # 脚本使用说明
```

### Server目录规范
```
server/
├── [app_name]/            # Django应用目录
├── steambase/             # 主要设置和配置
├── locale/                # 国际化文件
├── static/                # 静态文件
├── templates/             # 模板文件
├── media/                 # 媒体文件
├── data/                  # 初始化数据
├── docs/                  # 应用特定文档
├── manage.py              # Django管理脚本
├── requirements.txt       # Python依赖
└── gunicorn_conf.py       # Gunicorn配置
```

## 🚫 禁止的文件位置

### 不应该在server/根目录的文件类型：
- ❌ 临时修复脚本 (如 `fix_*.py`)
- ❌ 工具脚本 (应放在 `scripts/tools/`)
- ❌ 备份文件 (应放在 `backup/`)
- ❌ 日志文件 (应放在 `logs/`)
- ❌ 重复的配置文件 (如多个supervisord.conf)

### 不应该在根目录的文件类型：
- ❌ 应用特定的脚本 (应放在 `scripts/` 子目录)
- ❌ 临时文件和测试文件
- ❌ IDE生成的文件

## ✅ 文件放置规则

### 1. 脚本文件
- **工具脚本**: `scripts/tools/`
- **修复脚本**: `scripts/fixes/`
- **维护脚本**: `scripts/maintenance/`
- **监控脚本**: `scripts/monitoring/`
- **测试脚本**: `scripts/tests/`
- **特定功能脚本**: `scripts/[功能名]/`

### 2. 配置文件
- **主配置**: 根目录
- **应用配置**: 对应应用目录内
- **环境配置**: `deployment/` 或根目录

### 3. 文档文件
- **项目文档**: `docs/`
- **应用文档**: `server/docs/` 或对应应用目录
- **API文档**: `docs/api/`

### 4. 数据文件
- **初始化数据**: `server/data/`
- **备份数据**: `backup/`
- **媒体文件**: `server/media/`

## 🔧 维护建议

1. **定期检查**: 每月检查一次文件组织是否符合规范
2. **自动化**: 使用脚本自动检测不规范的文件位置
3. **文档更新**: 新增目录或规则时及时更新此文档
4. **团队培训**: 确保所有开发者了解文件组织规范

## 📝 最近整理记录

- **2025-07-19**: 
  - 移动 `server/fix_po_*.py` 到 `scripts/tools/`
  - 移动 `server/messages.mo` 到 `server/locale/zh_hans/LC_MESSAGES/`
  - 统一supervisord.conf配置文件
  - 整理scripts目录结构
  - 移动server/tools内容到scripts/tools
  - 创建CKEditor脚本专用目录
