# pylint: disable=missing-docstring, too-few-public-methods

class Aaaa(object): # [too-many-instance-attributes]

    def __init__(self):
        self.aaaa = 1
        self.bbbb = 2
        self.cccc = 3
        self.dddd = 4
        self.eeee = 5
        self.ffff = 6
        self.gggg = 7
        self.hhhh = 8
        self.iiii = 9
        self.jjjj = 10
        self._aaaa = 1
        self._bbbb = 2
        self._cccc = 3
        self._dddd = 4
        self._eeee = 5
        self._ffff = 6
        self._gggg = 7
        self._hhhh = 8
        self._iiii = 9
        self._jjjj = 10
        self.tomuch = None
