# Tools 工具脚本

本目录包含各种数据导入、初始化、同步等工具脚本。

## 脚本列表

### 数据导入和同步

#### import_csgoinfo.py
- **功能**：导入CSGO物品信息到数据库
- **用途**：初始化或更新游戏物品数据
- **运行方式**：
  ```bash
  cd /www/wwwroot/csgoskins.com.cn/server
  python ../scripts/tools/import_csgoinfo.py
  ```
- **注意事项**：运行前确保数据库连接正常，建议在维护时间窗口执行

#### sync_csgoinfo.py
- **功能**：同步CSGO物品信息
- **用途**：定期更新游戏物品的最新信息
- **运行方式**：
  ```bash
  cd /www/wwwroot/csgoskins.com.cn/server
  python ../scripts/tools/sync_csgoinfo.py
  ```
- **注意事项**：可以设置为定时任务，建议每日执行

#### sync_user_level.py
- **功能**：同步用户等级信息
- **用途**：更新用户等级数据，确保等级信息一致性
- **运行方式**：
  ```bash
  cd /www/wwwroot/csgoskins.com.cn/server
  python ../scripts/tools/sync_user_level.py
  ```
- **注意事项**：涉及用户数据，运行前建议备份

### 系统初始化

#### initChargeLevel.py
- **功能**：初始化充值等级配置
- **用途**：设置系统的充值等级和相关参数
- **运行方式**：
  ```bash
  cd /www/wwwroot/csgoskins.com.cn/server
  python ../scripts/tools/initChargeLevel.py
  ```
- **注意事项**：通常只在系统初始化时运行一次

#### initItemPrice.py
- **功能**：初始化物品价格
- **用途**：设置游戏物品的初始价格
- **运行方式**：
  ```bash
  cd /www/wwwroot/csgoskins.com.cn/server
  python ../scripts/tools/initItemPrice.py
  ```
- **注意事项**：影响系统经济，运行前需要确认价格策略

### 数据处理

#### temp_data.py
- **功能**：临时数据处理工具
- **用途**：处理临时数据任务，支持多线程处理
- **运行方式**：
  ```bash
  cd /www/wwwroot/csgoskins.com.cn/server
  python ../scripts/tools/temp_data.py
  ```
- **注意事项**：根据具体需求修改脚本参数，注意线程安全

## 运行前检查清单

### 环境检查
- [ ] 确认虚拟环境已激活
- [ ] 确认数据库连接正常
- [ ] 确认Redis连接正常（如果脚本需要）
- [ ] 确认相关配置文件存在

### 数据备份
- [ ] 备份相关数据表（特别是用户数据和配置数据）
- [ ] 记录当前系统状态
- [ ] 确认回滚方案

### 权限确认
- [ ] 确认数据库读写权限
- [ ] 确认文件系统访问权限
- [ ] 确认网络访问权限（如果需要外部API）

## 故障排查

### 常见问题

1. **Django设置错误**
   ```
   django.core.exceptions.ImproperlyConfigured: ...
   ```
   - 解决方案：确认DJANGO_SETTINGS_MODULE环境变量设置正确

2. **数据库连接失败**
   ```
   django.db.utils.OperationalError: ...
   ```
   - 解决方案：检查数据库配置和连接状态

3. **模块导入错误**
   ```
   ModuleNotFoundError: No module named '...'
   ```
   - 解决方案：确认在server目录下运行，路径设置正确

### 调试技巧

1. 增加详细日志输出
2. 使用Python调试器（pdb）
3. 分批处理大量数据
4. 添加进度显示

## 开发规范

### 新增工具脚本

1. **文件命名**：使用描述性名称，下划线分隔
2. **文件头部**：包含功能说明、作者、创建日期
3. **错误处理**：添加适当的异常捕获和处理
4. **日志记录**：使用标准的logging模块
5. **参数处理**：支持命令行参数（argparse）
6. **确认机制**：对于危险操作，添加用户确认

### 示例模板

```python
#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
工具脚本：[脚本功能描述]

作者：[作者名]
创建时间：[创建日期]
最后修改：[修改日期]

用法：
    python script_name.py [options]

示例：
    python script_name.py --dry-run
    python script_name.py --help
"""

import os
import sys
import django
import logging
import argparse

# Django环境设置
sys.path.append('./')
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "steambase.settings")
django.setup()

# 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    parser = argparse.ArgumentParser(description='脚本功能描述')
    parser.add_argument('--dry-run', action='store_true', help='模拟运行，不实际执行')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        # 主要逻辑
        pass
    except Exception as e:
        logger.error(f"脚本执行失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
```

最后更新：2025-06-28
