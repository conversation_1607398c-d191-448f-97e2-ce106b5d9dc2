import json
import os
import logging

from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.db import transaction

from steambase.enums import GameState, RollType, PackageState
from roll.models import RollRoom
from package.models import PackageItem

_logger = logging.getLogger(__name__)


def cancel_rollroom(modeladmin, request, queryset):
    try:
        with transaction.atomic():
            for room in queryset:
                if room.state not in [GameState.Cancelled.value, GameState.End.value, GameState.Full.value]:
                    room.state = GameState.Cancelled.value
                    room.save()
    except Exception as e:
        _logger.exception(e)

    messages.success(request, _('Cancel rollroom state complete'))

cancel_rollroom.short_description = _('Cancel rollroom state')


def delete_rollroom(modeladmin, request, queryset):
    try:
        with transaction.atomic():
            for room in queryset:
                if room.state in [GameState.Cancelled.value, GameState.End.value, GameState.Full.value]:
                    room.delete()
    except Exception as e:
        _logger.exception(e)

    messages.success(request, _('delete rollroom complete'))

delete_rollroom.short_description = _('Delete rollroom')