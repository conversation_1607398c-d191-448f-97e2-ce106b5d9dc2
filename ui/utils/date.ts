/**
 * 日期处理工具函数
 * 使用原生Date API，避免与Element Plus的dayjs冲突
 */

/**
 * 格式化日期 - 使用原生Date API
 * @param date 日期对象、时间戳或日期字符串
 * @param format 格式字符串，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的日期字符串
 */
export const formatDate = (
  date: Date | number | string | null | undefined, 
  format: string = 'YYYY-MM-DD HH:mm:ss'
): string => {
  if (!date) return ''
  
  const dateObj = new Date(date)
  if (isNaN(dateObj.getTime())) {
    return 'Invalid Date'
  }
  
  const year = dateObj.getFullYear()
  const month = String(dateObj.getMonth() + 1).padStart(2, '0')
  const day = String(dateObj.getDate()).padStart(2, '0')
  const hours = String(dateObj.getHours()).padStart(2, '0')
  const minutes = String(dateObj.getMinutes()).padStart(2, '0')
  const seconds = String(dateObj.getSeconds()).padStart(2, '0')
  
  switch (format) {
    case 'YYYY-MM-DD':
      return `${year}-${month}-${day}`
    case 'YYYY-MM-DD HH:mm':
      return `${year}-${month}-${day} ${hours}:${minutes}`
    case 'YYYY-MM-DD HH:mm:ss':
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    case 'MM-DD HH:mm':
      return `${month}-${day} ${hours}:${minutes}`
    case 'HH:mm:ss':
      return `${hours}:${minutes}:${seconds}`
    case 'HH:mm':
      return `${hours}:${minutes}`
    case 'MM/DD/YYYY':
      return `${month}/${day}/${year}`
    case 'YYYY/MM/DD':
      return `${year}/${month}/${day}`
    case 'DD-MM-YYYY':
      return `${day}-${month}-${year}`
    default:
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }
}

/**
 * 获取相对时间（如：2小时前）
 * @param date 日期对象、时间戳或日期字符串
 * @returns 相对时间字符串
 */
export const getRelativeTime = (date: Date | number | string): string => {
  if (!date) return ''
  
  const now = new Date()
  const targetDate = new Date(date)
  
  if (isNaN(targetDate.getTime())) {
    return 'Invalid Date'
  }
  
  const diffMs = now.getTime() - targetDate.getTime()
  
  if (diffMs < 0) {
    return '刚刚'
  }
  
  const diffSeconds = Math.floor(diffMs / 1000)
  const diffMinutes = Math.floor(diffSeconds / 60)
  const diffHours = Math.floor(diffMinutes / 60)
  const diffDays = Math.floor(diffHours / 24)
  const diffMonths = Math.floor(diffDays / 30)
  const diffYears = Math.floor(diffDays / 365)
  
  if (diffSeconds < 60) {
    return '刚刚'
  } else if (diffMinutes < 60) {
    return `${diffMinutes}分钟前`
  } else if (diffHours < 24) {
    return `${diffHours}小时前`
  } else if (diffDays < 30) {
    return `${diffDays}天前`
  } else if (diffMonths < 12) {
    return `${diffMonths}个月前`
  } else {
    return `${diffYears}年前`
  }
}

/**
 * 判断是否为有效日期
 * @param date 日期对象、时间戳或日期字符串
 * @returns 是否为有效日期
 */
export const isValidDate = (date: any): boolean => {
  if (!date) return false
  const dateObj = new Date(date)
  return !isNaN(dateObj.getTime())
}

/**
 * 获取当前时间戳
 * @returns 当前时间戳（毫秒）
 */
export const getCurrentTimestamp = (): number => {
  return Date.now()
}

/**
 * 同步版本的格式化日期（与formatDate相同，保持向后兼容）
 * @param date 日期对象、时间戳或日期字符串
 * @param format 格式字符串
 * @returns 格式化后的日期字符串
 */
export const formatDateSync = (
  date: Date | number | string, 
  format: string = 'YYYY-MM-DD HH:mm:ss'
): string => {
  return formatDate(date, format)
}

/**
 * 专门用于格式化日期的函数（只显示年月日）
 * 支持中英文不同的显示习惯
 * @param date 日期输入
 * @param separator 分隔符，默认根据locale自动选择
 * @param locale 本地化设置，默认为中文
 */
export const formatDateOnly = (
  date: Date | string | number | null,
  separator?: string,
  locale: string = 'zh-CN'
): string => {
  if (!date) return locale.startsWith('zh') ? '未设置日期' : 'Date not set'
  
  const d = new Date(date)
  
  if (isNaN(d.getTime())) {
    return locale.startsWith('zh') ? '无效日期' : 'Invalid date'
  }
  
  // 根据locale决定日期格式
  if (locale.startsWith('zh')) {
    // 中文格式：2024年1月15日 或 2024-01-15
    if (separator === undefined) {
      // 默认使用中文日期格式
      return d.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    } else {
      // 使用指定分隔符的数字格式
      const year = d.getFullYear()
      const month = String(d.getMonth() + 1).padStart(2, '0')
      const day = String(d.getDate()).padStart(2, '0')
      return `${year}${separator}${month}${separator}${day}`
    }
  } else if (locale.startsWith('en')) {
    // 英文格式：January 15, 2024 或 01/15/2024
    if (separator === undefined) {
      // 默认使用英文长日期格式
      return d.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    } else {
      // 使用指定分隔符的数字格式
      const year = d.getFullYear()
      const month = String(d.getMonth() + 1).padStart(2, '0')
      const day = String(d.getDate()).padStart(2, '0')
      
      // 根据常见的英文格式习惯
      if (separator === '/') {
        return `${month}/${day}/${year}` // 美式：MM/DD/YYYY
      } else if (separator === '-') {
        return `${year}-${month}-${day}` // ISO格式：YYYY-MM-DD
      } else {
        return `${month}${separator}${day}${separator}${year}`
      }
    }
  } else {
    // 其他locale，使用系统默认格式
    return d.toLocaleDateString(locale, {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  }
}

/**
 * 专门用于格式化时间的函数（只显示时分秒）
 * @param date 日期输入
 * @param locale 本地化设置，默认为中文
 */
export const formatTime = (date: Date | string | number | null, locale: string = 'zh-CN'): string => {
  if (!date) return '未知时间'
  
  const d = new Date(date)
  
  if (isNaN(d.getTime())) {
    return '无效时间'
  }
  
  return d.toLocaleTimeString(locale, {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  })
}

/**
 * 专门用于格式化短日期格式（紧凑显示）
 * @param date 日期输入
 * @param locale 本地化设置，默认为中文
 */
export const formatDateShort = (
  date: Date | string | number | null,
  locale: string = 'zh-CN'
): string => {
  if (!date) return locale.startsWith('zh') ? '未设置' : 'Not set'
  
  const d = new Date(date)
  
  if (isNaN(d.getTime())) {
    return locale.startsWith('zh') ? '无效' : 'Invalid'
  }
  
  if (locale.startsWith('zh')) {
    // 中文短格式：1月15日 或 2024/1/15
    const today = new Date()
    const isCurrentYear = d.getFullYear() === today.getFullYear()
    
    if (isCurrentYear) {
      return d.toLocaleDateString('zh-CN', {
        month: 'long',
        day: 'numeric'
      })
    } else {
      return d.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'numeric',
        day: 'numeric'
      })
    }
  } else if (locale.startsWith('en')) {
    // 英文短格式：Jan 15 或 1/15/24
    const today = new Date()
    const isCurrentYear = d.getFullYear() === today.getFullYear()
    
    if (isCurrentYear) {
      return d.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric'
      })
    } else {
      return d.toLocaleDateString('en-US', {
        year: '2-digit',
        month: 'numeric',
        day: 'numeric'
      })
    }
  } else {
    return d.toLocaleDateString(locale, {
      month: 'short',
      day: 'numeric'
    })
  }
}

/**
 * 格式化相对时间（如：刚刚、5分钟前、1小时前等）
 * @param date 日期输入
 * @param locale 本地化设置，默认为中文
 */
export const formatRelativeTime = (
  date: Date | string | number | null,
  locale: string = 'zh-CN'
): string => {
  if (!date) return '未知时间'
  
  const d = new Date(date)
  if (isNaN(d.getTime())) return '无效时间'
  
  const now = new Date()
  const diffMs = now.getTime() - d.getTime()
  const diffSeconds = Math.floor(diffMs / 1000)
  const diffMinutes = Math.floor(diffSeconds / 60)
  const diffHours = Math.floor(diffMinutes / 60)
  const diffDays = Math.floor(diffHours / 24)
  
  if (locale.startsWith('zh')) {
    if (diffSeconds < 60) return '刚刚'
    if (diffMinutes < 60) return `${diffMinutes}分钟前`
    if (diffHours < 24) return `${diffHours}小时前`
    if (diffDays < 7) return `${diffDays}天前`
    return formatDateOnly(date)
  } else {
    if (diffSeconds < 60) return 'just now'
    if (diffMinutes < 60) return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`
    if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`
    if (diffDays < 7) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`
    return formatDateOnly(date)
  }
}

// ========== DayJS 兼容层 ==========
// 提供dayjs兼容的API，供Element Plus等组件使用

/**
 * DayJS兼容的日期包装类
 */
class DateWrapper {
  private dateInstance: Date

  constructor(input?: any) {
    if (input instanceof Date) {
      this.dateInstance = new Date(input)
    } else if (typeof input === 'string' || typeof input === 'number') {
      this.dateInstance = new Date(input)
    } else {
      this.dateInstance = new Date()
    }
  }

  format(template?: string): string {
    return formatDate(this.dateInstance, template || 'YYYY-MM-DD HH:mm:ss')
  }

  fromNow(): string {
    return getRelativeTime(this.dateInstance)
  }

  subtract(value: number, unit: string): DateWrapper {
    const newDate = new Date(this.dateInstance)
    switch (unit) {
      case 'day':
      case 'days':
        newDate.setDate(newDate.getDate() - value)
        break
      case 'month':
      case 'months':
        newDate.setMonth(newDate.getMonth() - value)
        break
      case 'year':
      case 'years':
        newDate.setFullYear(newDate.getFullYear() - value)
        break
      case 'hour':
      case 'hours':
        newDate.setHours(newDate.getHours() - value)
        break
      case 'minute':
      case 'minutes':
        newDate.setMinutes(newDate.getMinutes() - value)
        break
      default:
        break
    }
    return new DateWrapper(newDate)
  }

  add(value: number, unit: string): DateWrapper {
    const newDate = new Date(this.dateInstance)
    switch (unit) {
      case 'day':
      case 'days':
        newDate.setDate(newDate.getDate() + value)
        break
      case 'month':
      case 'months':
        newDate.setMonth(newDate.getMonth() + value)
        break
      case 'year':
      case 'years':
        newDate.setFullYear(newDate.getFullYear() + value)
        break
      case 'hour':
      case 'hours':
        newDate.setHours(newDate.getHours() + value)
        break
      case 'minute':
      case 'minutes':
        newDate.setMinutes(newDate.getMinutes() + value)
        break
      default:
        break
    }
    return new DateWrapper(newDate)
  }

  endOf(unit: string): DateWrapper {
    const newDate = new Date(this.dateInstance)
    switch (unit) {
      case 'day':
        newDate.setHours(23, 59, 59, 999)
        break
      case 'month':
        newDate.setMonth(newDate.getMonth() + 1, 0)
        newDate.setHours(23, 59, 59, 999)
        break
      case 'year':
        newDate.setMonth(11, 31)
        newDate.setHours(23, 59, 59, 999)
        break
      default:
        break
    }
    return new DateWrapper(newDate)
  }

  startOf(unit: string): DateWrapper {
    const newDate = new Date(this.dateInstance)
    switch (unit) {
      case 'day':
        newDate.setHours(0, 0, 0, 0)
        break
      case 'month':
        newDate.setDate(1)
        newDate.setHours(0, 0, 0, 0)
        break
      case 'year':
        newDate.setMonth(0, 1)
        newDate.setHours(0, 0, 0, 0)
        break
      default:
        break
    }
    return new DateWrapper(newDate)
  }

  date(): number {
    return this.dateInstance.getDate()
  }

  day(): number {
    return this.dateInstance.getDay()
  }

  month(): number {
    return this.dateInstance.getMonth()
  }

  year(): number {
    return this.dateInstance.getFullYear()
  }

  hour(): number {
    return this.dateInstance.getHours()
  }

  minute(): number {
    return this.dateInstance.getMinutes()
  }

  second(): number {
    return this.dateInstance.getSeconds()
  }

  millisecond(): number {
    return this.dateInstance.getMilliseconds()
  }

  daysInMonth(): number {
    const year = this.dateInstance.getFullYear()
    const month = this.dateInstance.getMonth()
    return new Date(year, month + 1, 0).getDate()
  }

  toDate(): Date {
    return new Date(this.dateInstance)
  }

  valueOf(): number {
    return this.dateInstance.getTime()
  }

  isSame(other: any, unit?: string): boolean {
    const otherDate = new Date(other)
    if (unit === 'day') {
      return this.dateInstance.toDateString() === otherDate.toDateString()
    }
    return this.dateInstance.getTime() === otherDate.getTime()
  }

  isBefore(other: any): boolean {
    const otherDate = new Date(other)
    return this.dateInstance.getTime() < otherDate.getTime()
  }

  isAfter(other: any): boolean {
    const otherDate = new Date(other)
    return this.dateInstance.getTime() > otherDate.getTime()
  }

  isValid(): boolean {
    return !isNaN(this.dateInstance.getTime())
  }
}

/**
 * DayJS兼容的主函数
 */
const dayjs = (input?: any) => {
  return new DateWrapper(input)
}

// 添加静态方法
dayjs.extend = () => dayjs // 忽略插件扩展
dayjs.locale = () => dayjs // 忽略本地化设置
dayjs.isDayjs = () => false // 表明这不是真的dayjs
dayjs.unix = (timestamp: number) => new DateWrapper(timestamp * 1000)
dayjs.utc = (input?: any) => new DateWrapper(input) // 简化处理
dayjs.now = () => new DateWrapper(Date.now())

// 默认导出（供Element Plus使用）
export default dayjs

// 导出插件兼容函数
export const customParseFormat = () => {}
export const utc = () => {}
export const timezone = () => {}
export const weekOfYear = () => {}
export const isoWeek = () => {}
export const weekday = () => {}
export const localeData = () => {}
export const updateLocale = () => {}
export const getSet = () => {}
export const minMax = () => {}
export const calendar = () => {}
export const relativeTime = () => {}
export const duration = () => {}
export const isMoment = () => false
export const isDate = () => false