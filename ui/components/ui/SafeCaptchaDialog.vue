<template>
  <Teleport to="body">
    <!-- 背景遮罩 -->
    <Transition name="modal-backdrop">
      <div 
        v-if="show" 
        class="fixed inset-0 bg-gradient-to-br from-slate-900/80 via-blue-900/60 to-purple-900/80 backdrop-blur-sm flex items-center justify-center"
        style="z-index: 10000;"
        @click.self="closeDialog"
      >
        <!-- 弹窗容器 -->
        <Transition name="modal-content">
          <div 
            v-if="show"
            class="captcha-dialog-container bg-white/95 backdrop-blur-md rounded-2xl shadow-2xl w-full max-w-md mx-4 border border-white/20"
          >
            <!-- 装饰性渐变背景 -->
            <div class="absolute inset-0 bg-gradient-to-br from-blue-50/50 via-purple-50/30 to-pink-50/50 rounded-2xl"></div>
            
            <!-- 内容区域 -->
            <div class="relative p-8">
              <!-- 标题栏 -->
              <div class="flex justify-between items-center mb-6">
                <div class="flex items-center gap-3">
                  <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                    </svg>
                  </div>
                  <div>
                    <h3 class="text-xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">
                      {{ $t('ui.captcha_dialog.title') }}
                    </h3>
                    <p class="text-sm text-gray-500 mt-0.5">{{ $t('ui.captcha_dialog.subtitle') }}</p>
                  </div>
                </div>
                <button 
                  @click="closeDialog"
                  class="close-button w-8 h-8 bg-gray-100/80 hover:bg-red-100 rounded-lg flex items-center justify-center transition-all duration-200 group"
                  type="button"
                >
                  <svg class="w-4 h-4 text-gray-500 group-hover:text-red-500 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                  </svg>
                </button>
              </div>
              
              <!-- 验证码输入区域 -->
              <div class="space-y-5">
                <div class="input-group relative">
                  <div class="flex gap-4 items-center">
                    <!-- 输入框 -->
                    <div class="flex-1 relative">
                      <div class="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-xl blur-sm"></div>
                                              <input 
                          ref="inputRef"
                          v-model="inputValue"
                          type="text" 
                          class="captcha-input relative w-full px-4 py-3 bg-white/80 backdrop-blur-sm border border-gray-200/50 rounded-xl text-gray-800 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200"
                          :placeholder="$t('ui.captcha_dialog.placeholder')"
                          @keyup.enter="confirmDialog"
                        />
                      <div class="absolute right-3 top-1/2 transform -translate-y-1/2">
                        <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                      </div>
                    </div>
                    
                    <!-- 验证码图片 -->
                    <div class="captcha-image-container relative">
                      <div class="absolute inset-0 bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl blur-sm"></div>
                      <div 
                        class="relative w-24 h-12 bg-white/90 backdrop-blur-sm border border-gray-200/50 rounded-xl overflow-hidden cursor-pointer hover:scale-105 transition-all duration-200 group shadow-lg"
                        @click="refreshDialog"
                      >
                        <img 
                          v-if="image" 
                          :src="image" 
                          alt="验证码" 
                          class="w-full h-full object-cover"
                        />
                        <div v-else class="w-full h-full flex items-center justify-center">
                          <svg class="w-5 h-5 text-gray-400 group-hover:text-blue-500 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                          </svg>
                        </div>
                                                 <!-- 刷新提示 -->
                         <div class="absolute inset-0 bg-blue-500/0 group-hover:bg-blue-500/10 transition-all duration-200 flex items-center justify-center">
                           <span class="text-xs text-white/0 group-hover:text-blue-600 transition-all duration-200 font-medium">{{ $t('captcha_refresh_hint') }}</span>
                         </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- 错误信息 -->
                <Transition name="error-message">
                  <div v-if="error" class="error-message flex items-center gap-2 px-4 py-3 bg-red-50/80 backdrop-blur-sm border border-red-200/50 rounded-xl">
                    <svg class="w-4 h-4 text-red-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span class="text-sm text-red-700">{{ error }}</span>
                  </div>
                </Transition>
                
                                 <!-- 按钮区域 -->
                 <div class="flex gap-3 pt-4">
                   <button 
                     @click="closeDialog"
                     type="button"
                     class="cancel-button flex-1 px-6 py-3 bg-gray-200/90 hover:bg-gray-300/90 text-gray-700 rounded-xl font-medium transition-all duration-200 backdrop-blur-sm border border-gray-300/60 hover:border-gray-400/60 hover:scale-105 shadow-md hover:shadow-lg"
                   >
                     {{ $t('ui.captcha_dialog.cancel') }}
                   </button>
                   <button 
                     @click="confirmDialog"
                     type="button"
                     :disabled="loading"
                     class="confirm-button flex-1 px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white rounded-xl font-medium transition-all duration-200 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed shadow-lg hover:scale-105 disabled:hover:scale-100"
                   >
                     <span v-if="loading" class="flex items-center justify-center gap-2">
                       <svg class="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                         <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 2v4m0 12v4m9-9h-4M7 12H3m15.364 6.364l-2.828-2.828M6.464 6.464L3.636 3.636m12.728 0l-2.828 2.828M6.464 17.536L3.636 20.364"></path>
                       </svg>
                       {{ $t('ui.captcha_dialog.processing') }}
                     </span>
                     <span v-else>{{ $t('ui.captcha_dialog.confirm') }}</span>
                   </button>
                 </div>
              </div>
            </div>
          </div>
        </Transition>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
// 多语言支持
const { t } = useI18n()

interface Props {
  show: boolean
  image?: string
  error?: string
  loading?: boolean
}

interface Emits {
  (e: 'close'): void
  (e: 'confirm', value: string): void
  (e: 'refresh'): void
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  image: '',
  error: '',
  loading: false
})

const emit = defineEmits<Emits>()

const inputValue = ref('')
const inputRef = ref<HTMLInputElement>()

const closeDialog = () => {
  inputValue.value = ''
  emit('close')
}

const confirmDialog = () => {
  if (!inputValue.value.trim()) {
    return
  }
  emit('confirm', inputValue.value)
}

const refreshDialog = () => {
  emit('refresh')
}

// 当弹窗显示时聚焦输入框
watch(() => props.show, (newValue) => {
  if (newValue) {
    nextTick(() => {
      inputRef.value?.focus()
    })
  } else {
    inputValue.value = ''
  }
})
</script>

<style scoped>
/* 弹窗容器样式 */
.captcha-dialog-container {
  position: relative;
  overflow: hidden;
}

/* 背景遮罩动画 */
.modal-backdrop-enter-active {
  transition: all 0.3s ease-out;
}

.modal-backdrop-leave-active {
  transition: all 0.2s ease-in;
}

.modal-backdrop-enter-from,
.modal-backdrop-leave-to {
  opacity: 0;
}

/* 弹窗内容动画 */
.modal-content-enter-active {
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.modal-content-leave-active {
  transition: all 0.2s ease-in;
}

.modal-content-enter-from,
.modal-content-leave-to {
  opacity: 0;
  transform: scale(0.9) translateY(-20px);
}

/* 错误信息动画 */
.error-message-enter-active {
  transition: all 0.3s ease-out;
}

.error-message-leave-active {
  transition: all 0.2s ease-in;
}

.error-message-enter-from,
.error-message-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* 输入框聚焦效果 */
.captcha-input:focus {
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
}

/* 验证码图片容器悬停效果 */
.captcha-image-container:hover .captcha-image {
  transform: translateY(-1px);
}

/* 按钮悬停效果 */
.confirm-button:not(:disabled):hover {
  box-shadow: 0 8px 25px -8px rgba(59, 130, 246, 0.4);
}

.cancel-button:hover {
  box-shadow: 0 6px 20px -6px rgba(0, 0, 0, 0.15);
}

/* 关闭按钮悬停效果 */
.close-button:hover {
  box-shadow: 0 2px 8px -2px rgba(0, 0, 0, 0.1);
}

/* 微妙的动画效果 */
@keyframes subtle-float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-2px); }
}

.captcha-dialog-container {
  animation: subtle-float 6s ease-in-out infinite;
}
</style> 