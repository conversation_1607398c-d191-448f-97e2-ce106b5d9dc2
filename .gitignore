# 字节编译 / 优化 / DLL 文件
__pycache__/
*.py[cod]
*$py.class

# C 扩展
*.so

# 分发 / 打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
# 通常这些文件是由 python 脚本从模板写入的
# 在 PyInstaller 构建 exe 之前，以便注入日期/其他信息到其中
*.manifest
*.spec

# 安装器日志
pip-log.txt
pip-delete-this-directory.txt

# 单元测试 / 覆盖率报告
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# 翻译
*.mo
*.pot

# Django 相关:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask 相关:
instance/
.webassets-cache

# Scrapy 相关:
.scrapy

# Sphinx 文档
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
# 根据 pypa/pipenv#598，建议在版本控制中包含 Pipfile.lock
# 但是，在协作的情况下，如果有平台特定的依赖项或依赖项
# 没有跨平台支持，pipenv 可能会安装不工作的依赖项，或者不
# 安装所有需要的依赖项
#Pipfile.lock

# PEP 582; 由 github.com/David-OConnor/pyflow 等使用
__pypackages__/

# Celery 相关
celerybeat-schedule
celerybeat.pid

# SageMath 解析文件
*.sage.py

# 环境变量
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder 项目设置
.spyderproject
.spyproject

# Rope 项目设置
.ropeproject

# mkdocs 文档
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre 类型检查器
.pyre/

# Django 特定
*.log
*.pot
*.pyc
__pycache__/
local_settings.py
db.sqlite3
media/
staticfiles/
static/

# Django 迁移 (可选 - 如果你想跟踪迁移，请注释掉)
# */migrations/*.py
# */migrations/*.pyc

# 虚拟环境
venv/
env/
.venv/
.env/

# IDE 文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Node.js (如果你有任何前端资源)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Redis 转储文件
dump.rdb

# Celery beat 调度文件
celerybeat-schedule

# dotenv - 重要: 永远不要提交这些文件!
.env
.env.*
server/.env
server/.env.*
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 运行时数据
pids/
*.pid
*.seed
*.pid.lock

# istanbul 等工具使用的覆盖率目录
coverage/

# nyc 测试覆盖率
.nyc_output

# Grunt 中间存储 (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower 依赖目录 (https://bower.io/)
bower_components

# node-waf 配置
.lock-wscript

# 编译的二进制插件 (https://nodejs.org/api/addons.html)
build/Release

# 依赖目录
node_modules/
jspm_packages/

# TypeScript v1 声明文件
typings/

# 可选的 npm 缓存目录
.npm

# 可选的 eslint 缓存
.eslintcache

# 可选的 REPL 历史
.node_repl_history

# 'npm pack' 的输出
*.tgz

# Yarn 完整性文件
.yarn-integrity

# parcel-bundler 缓存 (https://parceljs.org/)
.cache
.parcel-cache

# next.js 构建输出
.next

# nuxt.js 构建输出
.nuxt

# vuepress 构建输出
.vuepress/dist

# Serverless 目录
.serverless

# FuseBox 缓存
.fusebox/

# DynamoDB Local 文件
.dynamodb/

# TernJS 端口文件
.tern-port

# 项目特定
# 在此处添加任何项目特定的文件或目录以忽略
server/media/
server/staticfiles/
server/static/
server/logs/
server/__pycache__/
server/venv/
*.sqlite3
*.db

# 日志目录
logs/
*.log
*.err.log
*.pid

# 备份目录
backup/

# 虚拟环境
server/venv/

# Supervisor 日志
supervisord.log
supervisord.pid

# Gunicorn 日志
gunicorn.log
gunicorn_error.log

# 应用程序日志
steambase.log
steambase_access.log
steambase_service.log
worker.log
worker_error.log

# Celery 日志
celery_beat.log
celery_beat.err.log
celery_worker.log
celery_worker.err.log

# 备份文件
*.bak
*.backup
*.old

# 临时文件
*.tmp
*.temp
temp/
tmp/

# 包含敏感数据的配置文件
config/production.py
config/local.py
secrets.json
.secrets

# SSL 证书
*.pem
*.key
*.crt
*.csr

# 数据库转储
*.sql
*.dump

# 压缩文件
*.zip
*.tar.gz
*.rar
*.7z

# ===== UI 目录特定忽略规则 =====

# Nuxt.js 构建输出
ui/.nuxt/
ui/.output/
ui/dist/

# Node.js 依赖
ui/node_modules/

# 包管理器锁文件 (保留 package-lock.json，忽略其他)
ui/yarn.lock
ui/pnpm-lock.yaml

# 环境变量文件
ui/.env
ui/.env.*
ui/.env.local
ui/.env.development.local
ui/.env.test.local
ui/.env.production.local

# 日志文件
ui/logs/
ui/*.log
ui/npm-debug.log*
ui/yarn-debug.log*
ui/yarn-error.log*

# 缓存目录
ui/.cache/
ui/.parcel-cache/
ui/.vite/

# 测试覆盖率
ui/coverage/
ui/.nyc_output/

# TypeScript 构建信息
ui/*.tsbuildinfo

# IDE 和编辑器文件
ui/.vscode/
ui/.idea/
ui/*.swp
ui/*.swo
ui/*~

# 操作系统文件
ui/.DS_Store
ui/.DS_Store?
ui/._*
ui/Thumbs.db

# 临时文件
ui/tmp/
ui/temp/
ui/*.tmp
ui/*.temp

# 构建工具缓存
ui/.eslintcache
ui/.stylelintcache

# 运行时文件
ui/*.pid
ui/*.seed
ui/*.pid.lock
