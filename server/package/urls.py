from django.urls import re_path as url

from package import views, model_signals



app_name = 'package'
urlpatterns = [
    # url(r'^inventory/', views.GetUserInventoryView.as_view()),
    # 用户的饰品
    url(r'^package/', views.GetUserPackageView.as_view()),
    # url(r'^shop/', views.GetShopView.as_view()),
    # url(r'^deposit/', views.UserDepositItemsView.as_view()),
    # url(r'^withdraw/', views.UserWithdrawItemsView.as_view()),
    url(r'^exchange/', views.UserExchangeItemsView.as_view()),
    #url(r'^exchangeall/', views.UserExchangeALLItemsView.as_view()),
    # url(r'^buy/', views.UserBuyItemsView.as_view()),
    url(r'^tradeoffer/', views.GetTradeOfferView.as_view()),
    url(r'^exchangerecord/', views.GetExchangeRecordView.as_view()),

    url(r'^syncitemprice/', views.SyncItemPriceView.as_view()),
    # url(r'^synciteminfo/', views.SyncItemInfoView.as_view()),
    url(r'^itemdetail/', views.GetItemDetailView.as_view()),
    url(r'^newitems/', views.GetNewItemsView.as_view()),
    
    
    # 新的接口
    url(r'^items/update/', views.GetUpdateItemsView.as_view()), 
    url(r'^items/new/', views.GetNewItemsView.as_view()),
    url(r'^items/expensive/', views.GetExpensiveItemsView.as_view()),
    url(r'^items/relate/', views.GetRelateItemsView.as_view()),
    url(r'^items/random/', views.GetRandomItemsView.as_view()),
    url(r'^items/type/', views.GetTypeItemsView.as_view()),
    url(r'^items/tag/', views.GetTagItemsView.as_view()),
    url(r'^items/detail/', views.GetItemDetailView.as_view()),

    


    #废弃
    url(r'^expensiveitems/', views.GetExpensiveItemsView.as_view()),
    url(r'^newitemsupdate/', views.GetUpdateItemsView.as_view()), 
    url(r'^relateitems/', views.GetRelateItemsView.as_view()),


    url(r'^deleteoldpackageitems/', views.DeleteUserPackageSkinView.as_view()),
    

    #url(r'^randomitems/', views.GetRandomItemsView.as_view()),
    #url(r'^typeitems/', views.GetTypeItemsView.as_view()),

    # 2025
    url(r'^skins/search/', views.SearchItemsView.as_view()),
    url(r'^skins/category/', views.GetItemsCategoryView.as_view()),
    url(r'^skins/quality/', views.GetItemsQualityView.as_view()),
    url(r'^skins/rarity/', views.GetItemsRarityView.as_view()),
    url(r'^skins/exterior/', views.GetItemsExteriorView.as_view()),
    url(r'^skins/detail/', views.GetItemDetailView.as_view()),
    url(r'^skins/random/', views.GetRandomItemsView.as_view()),
    url(r'^userpackage/', views.GetUserPackageSkinView.as_view()),

]
