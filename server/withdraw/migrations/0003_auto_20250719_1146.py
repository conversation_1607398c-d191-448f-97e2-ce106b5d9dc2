# Generated by Django 3.2.25 on 2025-07-19 03:46

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('withdraw', '0002_auto_20250718_1647'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='traderecord',
            options={'ordering': ('-create_time',), 'verbose_name': '交易记录', 'verbose_name_plural': 'Trade Record'},
        ),
        migrations.AlterModelOptions(
            name='tradestatisticsday',
            options={'ordering': ('-create_time',), 'verbose_name': '交易日统计', 'verbose_name_plural': 'Trade Statistics Day'},
        ),
        migrations.AlterModelOptions(
            name='tradestatisticsmonth',
            options={'ordering': ('-create_time',), 'verbose_name': '交易月统计', 'verbose_name_plural': 'Trade Statistics Month'},
        ),
        migrations.AlterModelOptions(
            name='withdrawitem',
            options={'ordering': ('-create_time',), 'verbose_name': '提现物品', 'verbose_name_plural': 'Withdraw Item'},
        ),
        migrations.AlterModelOptions(
            name='wxptradeoffer',
            options={'ordering': ('-create_time',), 'verbose_name': 'Waxpeer交易报价', 'verbose_name_plural': 'Wxp Trade Offer'},
        ),
        migrations.AlterField(
            model_name='traderecord',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='wxp_user', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
    ]
