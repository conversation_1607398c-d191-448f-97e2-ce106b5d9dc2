#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量修复模型翻译脚本
将模型中的直接中文字符串替换为翻译函数调用
"""

import os
import re
import sys
from pathlib import Path

def backup_file(file_path):
    """备份文件"""
    backup_path = f"{file_path}.backup"
    with open(file_path, 'r', encoding='utf-8') as src:
        with open(backup_path, 'w', encoding='utf-8') as dst:
            dst.write(src.read())
    print(f"📁 已备份: {backup_path}")

def fix_model_file(file_path):
    """修复单个模型文件"""
    print(f"\n🔧 处理文件: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    changes_made = []
    
    # 检查是否已经导入翻译函数
    has_translation_import = 'from django.utils.translation import' in content
    
    # 如果没有导入翻译函数，添加导入
    if not has_translation_import:
        # 查找合适的位置插入导入语句
        import_lines = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines):
            if line.startswith('from django.') or line.startswith('import django.'):
                import_lines.append(i)
        
        if import_lines:
            # 在最后一个Django导入后插入
            insert_pos = max(import_lines) + 1
            lines.insert(insert_pos, 'from django.utils.translation import gettext_lazy as _')
            content = '\n'.join(lines)
            changes_made.append("添加翻译函数导入")
    
    # 修复verbose_name中的直接中文
    verbose_name_pattern = r'verbose_name\s*=\s*["\']([^"\']*[\u4e00-\u9fff][^"\']*)["\']'
    
    def replace_verbose_name(match):
        chinese_text = match.group(1)
        return f'verbose_name = _(\'{chinese_text}\')'
    
    new_content = re.sub(verbose_name_pattern, replace_verbose_name, content)
    
    if new_content != content:
        changes_made.append("修复verbose_name翻译")
        content = new_content
    
    # 修复verbose_name_plural中的直接中文
    verbose_name_plural_pattern = r'verbose_name_plural\s*=\s*["\']([^"\']*[\u4e00-\u9fff][^"\']*)["\']'
    
    def replace_verbose_name_plural(match):
        chinese_text = match.group(1)
        return f'verbose_name_plural = _(\'{chinese_text}\')'
    
    new_content = re.sub(verbose_name_plural_pattern, replace_verbose_name_plural, content)
    
    if new_content != content:
        changes_made.append("修复verbose_name_plural翻译")
        content = new_content
    
    # 修复字段的verbose_name
    field_verbose_name_pattern = r'(\w+\s*=\s*models\.\w+\([^)]*verbose_name\s*=\s*)["\']([^"\']*[\u4e00-\u9fff][^"\']*)["\']'
    
    def replace_field_verbose_name(match):
        prefix = match.group(1)
        chinese_text = match.group(2)
        return f'{prefix}_(\'{chinese_text}\')'
    
    new_content = re.sub(field_verbose_name_pattern, replace_field_verbose_name, content)
    
    if new_content != content:
        changes_made.append("修复字段verbose_name翻译")
        content = new_content
    
    # 如果有修改，写入文件
    if content != original_content:
        backup_file(file_path)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 修复完成，变更内容:")
        for change in changes_made:
            print(f"   - {change}")
        
        return True
    else:
        print("ℹ️ 无需修改")
        return False

def fix_specific_models():
    """修复特定的模型文件"""
    print("🎯 修复特定模型文件...")
    
    # 根据验证脚本的结果，这些文件需要修复
    problem_apps = [
        'b2ctrade', 'promotion', 'envelope', 'roll', 'sitecfg', 
        'chat', 'blindbox', 'articles', 'authentication', 'market',
        'package', 'withdraw', 'box', 'custombox', 'luckybox',
        'lottery', 'crash', 'grab'
    ]
    
    server_dir = Path(__file__).parent.parent.parent / "server"
    fixed_count = 0
    
    for app_name in problem_apps:
        models_file = server_dir / app_name / "models.py"
        
        if models_file.exists():
            if fix_model_file(models_file):
                fixed_count += 1
        else:
            print(f"⚠️ 文件不存在: {models_file}")
    
    return fixed_count

def fix_apps_py_files():
    """修复apps.py文件中的verbose_name"""
    print("\n📱 修复apps.py文件...")
    
    server_dir = Path(__file__).parent.parent.parent / "server"
    fixed_count = 0
    
    for app_dir in server_dir.iterdir():
        if app_dir.is_dir():
            apps_file = app_dir / "apps.py"
            
            if apps_file.exists():
                with open(apps_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查是否有直接的中文verbose_name
                if re.search(r'verbose_name\s*=\s*["\'][^"\']*[\u4e00-\u9fff]', content):
                    if fix_model_file(apps_file):
                        fixed_count += 1
    
    return fixed_count

def validate_fixes():
    """验证修复结果"""
    print("\n✅ 验证修复结果...")
    
    # 重新运行验证脚本的模型翻译检查部分
    server_dir = Path(__file__).parent.parent.parent / "server"
    issues_found = []
    
    for app_dir in server_dir.iterdir():
        if app_dir.is_dir() and (app_dir / "models.py").exists():
            models_file = app_dir / "models.py"
            
            with open(models_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否还有直接的中文字符串
            if re.search(r'verbose_name\s*=\s*["\'][^"\']*[\u4e00-\u9fff]', content):
                if 'from django.utils.translation import' not in content:
                    issues_found.append(f"{app_dir.name}/models.py")
    
    if issues_found:
        print("⚠️ 仍有问题的文件:")
        for issue in issues_found:
            print(f"   - {issue}")
        return False
    else:
        print("🎉 所有模型文件已修复完成！")
        return True

def create_translation_template():
    """创建翻译模板文件"""
    print("\n📝 创建翻译模板...")
    
    template_content = '''# 模型翻译示例

## 正确的翻译使用方式

```python
from django.utils.translation import gettext_lazy as _

class MyModel(models.Model):
    name = models.CharField(max_length=100, verbose_name=_('Name'))
    description = models.TextField(verbose_name=_('Description'))
    
    class Meta:
        verbose_name = _('My Model')
        verbose_name_plural = _('My Models')

class MyAppConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'myapp'
    verbose_name = _('My Application')
```

## 翻译文件更新

修改模型后，需要更新翻译文件：

```bash
cd server
python manage.py makemessages -l zh_hans
python manage.py makemessages -l en
python manage.py compilemessages
```
'''
    
    template_file = Path(__file__).parent.parent / "docs" / "model_translation_guide.md"
    template_file.parent.mkdir(exist_ok=True)
    
    with open(template_file, 'w', encoding='utf-8') as f:
        f.write(template_content)
    
    print(f"📄 翻译指南已创建: {template_file}")

def main():
    """主函数"""
    print("🔧 模型翻译批量修复工具")
    print("=" * 50)
    
    # 询问用户确认
    response = input("⚠️ 此操作将修改模型文件，是否继续？(y/N): ")
    if response.lower() != 'y':
        print("❌ 操作已取消")
        return 1
    
    try:
        # 修复models.py文件
        models_fixed = fix_specific_models()
        
        # 修复apps.py文件
        apps_fixed = fix_apps_py_files()
        
        # 验证修复结果
        validation_passed = validate_fixes()
        
        # 创建翻译指南
        create_translation_template()
        
        # 输出总结
        print("\n" + "=" * 50)
        print("📊 修复总结:")
        print(f"  修复的models.py文件: {models_fixed}")
        print(f"  修复的apps.py文件: {apps_fixed}")
        print(f"  验证结果: {'✅ 通过' if validation_passed else '❌ 仍有问题'}")
        
        if validation_passed:
            print("\n🎉 所有模型翻译问题已修复！")
            print("\n📝 下一步操作:")
            print("1. 运行 python manage.py makemessages -l zh_hans")
            print("2. 运行 python manage.py makemessages -l en") 
            print("3. 运行 python manage.py compilemessages")
            print("4. 重新运行验证脚本确认修复效果")
            return 0
        else:
            print("\n⚠️ 部分问题仍需手动修复")
            return 1
            
    except Exception as e:
        print(f"❌ 修复过程中出现错误: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
