#!/bin/bash

# 设置项目根目录
PROJECT_ROOT="/www/wwwroot/csgoskins.com.cn"
NGINX_CERT_DIR="/www/server/nginx/conf/cert"

# 创建必要的目录
mkdir -p ${NGINX_CERT_DIR}
mkdir -p ${PROJECT_ROOT}/deployment/node

# 复制部署文件
cp -r ${PROJECT_ROOT}/deployment /www/
cp ${PROJECT_ROOT}/docker-compose.yml /www/

# 复制证书并设置权限
cp -r ${PROJECT_ROOT}/deployment/nginx/cert/* ${NGINX_CERT_DIR}/
chown -R www:www ${NGINX_CERT_DIR}
chmod -R 600 ${NGINX_CERT_DIR}

# 复制nginx配置
\cp -f ${PROJECT_ROOT}/deployment/nginx/steambase.conf /www/server/panel/vhost/nginx/

# 创建静态文件和媒体文件目录
mkdir -p ${PROJECT_ROOT}/server/static
mkdir -p ${PROJECT_ROOT}/server/media
chown -R www:www ${PROJECT_ROOT}/server/static
chown -R www:www ${PROJECT_ROOT}/server/media

# 创建日志目录
mkdir -p ${PROJECT_ROOT}/logs
mkdir -p /www/wwwlogs/python/csgoskins
chown -R www:www ${PROJECT_ROOT}/logs
chown -R www:www /www/wwwlogs/python/csgoskins

# 重新加载nginx配置
nginx -t && nginx -s reload

# 重启docker服务
docker-compose --file ${PROJECT_ROOT}/docker-compose.yml restart

echo "Deployment completed successfully!"