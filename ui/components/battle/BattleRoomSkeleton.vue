<template>
  <div class="battle-room-skeleton">
    <div :class="['backdrop-blur-xl border rounded-2xl p-6 animate-pulse', skeletonTheme.background, skeletonTheme.border]">
      <!-- 房间头部骨架 - 模拟状态指示器和价格 -->
      <div class="flex items-center justify-between mb-4">
        <div class="flex items-center gap-3">
          <!-- 状态指示器骨架 -->
          <div :class="['flex items-center gap-2 px-3 py-1.5 border rounded-lg', skeletonTheme.status]">
            <div :class="['w-2.5 h-2.5 rounded-full', skeletonTheme.dot]"></div>
            <div :class="['h-4 w-16 rounded', skeletonTheme.text]"></div>
          </div>
        </div>
        <!-- 价格标签骨架 -->
        <div :class="['px-3 py-1 border rounded-lg', skeletonTheme.price]">
          <div :class="['h-4 w-12 rounded', skeletonTheme.priceText]"></div>
        </div>
      </div>
      
      <!-- 箱子展示区域骨架 - 模拟真实布局（一行4个） -->
      <div class="mb-4">
        <div class="grid grid-cols-4 gap-3">
          <div v-for="i in 4" :key="i" class="bg-gray-700/30 rounded-xl p-3 border border-gray-600/30">
            <!-- 箱子图片容器 -->
            <div class="relative h-20 mb-2 rounded-lg overflow-hidden bg-gray-600/50">
              <!-- 价格标签 -->
              <div class="absolute top-2 right-2 px-2 py-1 bg-blue-500/50 rounded">
                <div class="h-2 w-6 bg-white/50 rounded"></div>
              </div>
            </div>
            <!-- 箱子名称 -->
            <div class="h-3 bg-gray-600/50 rounded w-3/4 mx-auto"></div>
          </div>
        </div>
      </div>
      
      <!-- 玩家头像区域骨架 - 模拟真实头像尺寸和布局 -->
      <div class="flex items-center gap-2 mb-4">
        <!-- 房主头像（32px，带皇冠） -->
        <div class="relative">
          <div :class="['w-8 h-8 rounded-full border-2', skeletonTheme.avatar]"></div>
          <div :class="['absolute -top-1 -right-1 w-3 h-3 rounded-full', skeletonTheme.crown]"></div>
        </div>
        <!-- 参与者头像（28px） -->
        <div :class="['w-7 h-7 rounded-full border', skeletonTheme.participant]"></div>
        <div :class="['w-7 h-7 rounded-full border', skeletonTheme.participant]"></div>
        <!-- 空位（24px，虚线边框） -->
        <div class="w-6 h-6 bg-gray-700/20 rounded-full border-2 border-gray-600/40 border-dashed flex items-center justify-center">
          <div class="w-1.5 h-1.5 bg-gray-500/60 rounded-full"></div>
        </div>
        <!-- 玩家计数 -->
        <div :class="['ml-auto h-4 w-12 rounded', skeletonTheme.count]"></div>
      </div>
      
      <!-- 操作按钮骨架 - 模拟真实按钮布局 -->
      <div class="flex gap-2">
        <div :class="['flex-1 h-10 rounded-lg', skeletonTheme.button]"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  type: 'active' | 'my' | 'history'
}>()

const skeletonTheme = computed(() => {
  switch (props.type) {
    case 'active':
      return {
        background: 'bg-gradient-to-br from-blue-800/20 to-blue-700/20',
        border: 'border-blue-600/20',
        status: 'bg-blue-700/30 border-blue-600/30',
        dot: 'bg-blue-500/50',
        text: 'bg-blue-600/40',
        price: 'bg-blue-500/20 border-blue-500/30',
        priceText: 'bg-blue-400/50',
        avatar: 'bg-blue-700/40 border-blue-400/50',
        crown: 'bg-blue-500/50',
        participant: 'bg-blue-700/30 border-blue-500/40',
        count: 'bg-blue-700/40',
        button: 'bg-blue-500/30'
      }
    case 'my':
      return {
        background: 'bg-gradient-to-br from-green-800/20 to-green-700/20',
        border: 'border-green-600/20',
        status: 'bg-green-700/30 border-green-600/30',
        dot: 'bg-green-500/50',
        text: 'bg-green-600/40',
        price: 'bg-green-500/20 border-green-500/30',
        priceText: 'bg-green-400/50',
        avatar: 'bg-green-700/40 border-green-400/50',
        crown: 'bg-green-500/50',
        participant: 'bg-green-700/30 border-green-500/40',
        count: 'bg-green-700/40',
        button: 'bg-green-500/30'
      }
    case 'history':
      return {
        background: 'bg-gradient-to-br from-gray-800/20 to-gray-700/20',
        border: 'border-gray-600/20',
        status: 'bg-gray-700/30 border-gray-600/30',
        dot: 'bg-gray-500/50',
        text: 'bg-gray-600/40',
        price: 'bg-gray-600/20 border-gray-500/30',
        priceText: 'bg-gray-400/50',
        avatar: 'bg-gray-700/40 border-gray-400/50',
        crown: 'bg-gray-500/50',
        participant: 'bg-gray-700/30 border-gray-500/40',
        count: 'bg-gray-700/40',
        button: 'bg-gray-500/30'
      }
    default:
      return {
        background: 'bg-gradient-to-br from-gray-800/20 to-gray-700/20',
        border: 'border-gray-600/20',
        status: 'bg-gray-700/30 border-gray-600/30',
        dot: 'bg-gray-500/50',
        text: 'bg-gray-600/40',
        price: 'bg-gray-600/20 border-gray-500/30',
        priceText: 'bg-gray-400/50',
        avatar: 'bg-gray-700/40 border-gray-400/50',
        crown: 'bg-gray-500/50',
        participant: 'bg-gray-700/30 border-gray-500/40',
        count: 'bg-gray-700/40',
        button: 'bg-gray-500/30'
      }
  }
})
</script> 