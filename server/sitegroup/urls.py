from django.conf.urls import url
from sitegroup import views

app_name = 'sitegroup'

urlpatterns = [
    # 公开接口
    url(r'^site-info/$', views.GetSiteInfoView.as_view(), name='site-info'),
    url(r'^check-feature/$', views.CheckFeatureView.as_view(), name='check-feature'),
    
    # 站点配置
    url(r'^config/$', views.SiteConfigView.as_view(), name='site-config'),
    
    # 代理商站点管理
    url(r'^agent/sites/$', views.AgentSitesView.as_view(), name='agent-sites'),
    
    # 站点管理
    url(r'^sites/(?P<site_id>\d+)/$', views.SiteManagementView.as_view(), name='site-management'),
    url(r'^sites/(?P<site_id>\d+)/features/$', views.SiteFeaturesView.as_view(), name='site-features'),
    url(r'^sites/(?P<site_id>\d+)/analytics/$', views.SiteAnalyticsView.as_view(), name='site-analytics'),
    
    # 缓存管理
    url(r'^clear-cache/$', views.ClearSiteCacheView.as_view(), name='clear-cache'),
]
