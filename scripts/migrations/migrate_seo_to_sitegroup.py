#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SEO数据迁移到站群系统脚本
将sitecfg.SEO模型的数据迁移到新的sitegroup应用
"""

import os
import sys
import django
from django.db import transaction

# 设置Django环境
sys.path.append('/www/wwwroot/csgoskins.com.cn/server')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')
django.setup()

from sitecfg.models import SEO
from sitegroup.models import SiteGroup, Site, SiteTheme
from authentication.models import AuthUser


def create_default_site_group():
    """创建默认站群"""
    try:
        # 查找超级管理员作为默认站群的所有者
        admin_user = AuthUser.objects.filter(is_superuser=True).first()
        if not admin_user:
            print("❌ 未找到超级管理员用户，无法创建默认站群")
            return None
        
        site_group, created = SiteGroup.objects.get_or_create(
            agent=admin_user,
            name='默认站群',
            defaults={
                'description': '系统默认站群，用于管理主站点',
                'is_active': True
            }
        )
        
        if created:
            print(f"✅ 创建默认站群: {site_group.name}")
        else:
            print(f"ℹ️ 默认站群已存在: {site_group.name}")
        
        return site_group
        
    except Exception as e:
        print(f"❌ 创建默认站群失败: {e}")
        return None


def migrate_seo_to_sites():
    """将SEO数据迁移到Site模型"""
    print("🔄 开始迁移SEO数据到站群系统...")
    
    migrated_count = 0
    error_count = 0
    
    # 获取所有SEO记录
    seo_records = SEO.objects.all()
    total_count = seo_records.count()
    
    print(f"📊 找到 {total_count} 条SEO记录需要迁移")
    
    if total_count == 0:
        print("ℹ️ 没有SEO数据需要迁移")
        return True
    
    # 创建默认站群
    default_site_group = create_default_site_group()
    if not default_site_group:
        return False
    
    with transaction.atomic():
        for seo in seo_records:
            try:
                print(f"\n🔄 处理SEO记录: {seo.url}")
                
                # 确定站群
                if seo.agent and seo.agent.is_agent:
                    # 为代理商创建或获取站群
                    site_group, created = SiteGroup.objects.get_or_create(
                        agent=seo.agent,
                        defaults={
                            'name': f'{seo.agent.username} 的站群',
                            'description': f'代理商 {seo.agent.username} 的站点集合',
                            'is_active': True
                        }
                    )
                    if created:
                        print(f"  ✅ 为代理商 {seo.agent.username} 创建站群")
                else:
                    # 使用默认站群
                    site_group = default_site_group
                    print(f"  ℹ️ 使用默认站群")
                
                # 检查域名是否已存在
                if Site.objects.filter(domain=seo.url).exists():
                    print(f"  ⚠️ 域名 {seo.url} 已存在，跳过")
                    continue
                
                # 创建站点
                site = Site.objects.create(
                    site_group=site_group,
                    domain=seo.url or 'default.local',
                    name=seo.name or '未命名站点',
                    subtitle=seo.subtitle or '',
                    seo_title=seo.title or '',
                    seo_keywords=seo.keywords or '',
                    seo_description=seo.description or '',
                    icp_number=seo.icp or '',
                    articles_enabled=seo.news_enable,
                    is_active=seo.enable,
                    is_default=(seo.url == 'www.csgoskins.com.cn')  # 设置主域名为默认
                )
                
                # 创建默认主题
                SiteTheme.objects.create(
                    site=site,
                    theme_name='default',
                    header_background='#ffffff',
                    header_text_color='#000000',
                    footer_background='#f8f9fa',
                    footer_text_color='#6c757d'
                )
                
                print(f"  ✅ 创建站点: {site.domain} - {site.name}")
                migrated_count += 1
                
            except Exception as e:
                print(f"  ❌ 迁移SEO记录失败 {seo.url}: {e}")
                error_count += 1
                continue
    
    print(f"\n📊 迁移完成:")
    print(f"  ✅ 成功迁移: {migrated_count} 条")
    print(f"  ❌ 迁移失败: {error_count} 条")
    print(f"  📈 成功率: {migrated_count/(migrated_count+error_count)*100:.1f}%")
    
    return error_count == 0


def associate_users_with_sites():
    """关联现有用户到对应站点"""
    print("\n🔗 开始关联用户到站点...")
    
    associated_count = 0
    error_count = 0
    
    # 获取有域名信息的用户
    users_with_domain = AuthUser.objects.filter(domain__isnull=False).exclude(domain='')
    total_users = users_with_domain.count()
    
    print(f"📊 找到 {total_users} 个用户有域名信息")
    
    if total_users == 0:
        print("ℹ️ 没有用户需要关联站点")
        return True
    
    with transaction.atomic():
        for user in users_with_domain:
            try:
                # 查找对应的站点
                site = Site.objects.filter(domain=user.domain, is_active=True).first()
                
                if site:
                    user.registered_site = site
                    user.save(update_fields=['registered_site'])
                    associated_count += 1
                    
                    if associated_count % 100 == 0:
                        print(f"  🔄 已关联 {associated_count} 个用户...")
                else:
                    print(f"  ⚠️ 用户 {user.username} 的域名 {user.domain} 未找到对应站点")
                    error_count += 1
                    
            except Exception as e:
                print(f"  ❌ 关联用户失败 {user.username}: {e}")
                error_count += 1
                continue
    
    print(f"\n📊 用户关联完成:")
    print(f"  ✅ 成功关联: {associated_count} 个用户")
    print(f"  ❌ 关联失败: {error_count} 个用户")
    
    return error_count == 0


def verify_migration():
    """验证迁移结果"""
    print("\n🔍 验证迁移结果...")
    
    # 统计数据
    seo_count = SEO.objects.count()
    site_count = Site.objects.count()
    site_group_count = SiteGroup.objects.count()
    users_with_site = AuthUser.objects.filter(registered_site__isnull=False).count()
    
    print(f"📊 迁移结果统计:")
    print(f"  原SEO记录数: {seo_count}")
    print(f"  新站点数: {site_count}")
    print(f"  站群数: {site_group_count}")
    print(f"  已关联站点的用户数: {users_with_site}")
    
    # 检查数据完整性
    issues = []
    
    # 检查是否有站点没有主题
    sites_without_theme = Site.objects.filter(theme__isnull=True).count()
    if sites_without_theme > 0:
        issues.append(f"有 {sites_without_theme} 个站点没有主题配置")
    
    # 检查是否有重复域名
    from django.db.models import Count
    duplicate_domains = Site.objects.values('domain').annotate(
        count=Count('domain')
    ).filter(count__gt=1)
    
    if duplicate_domains.exists():
        issues.append(f"发现 {duplicate_domains.count()} 个重复域名")
    
    if issues:
        print("⚠️ 发现问题:")
        for issue in issues:
            print(f"  - {issue}")
        return False
    else:
        print("✅ 迁移验证通过，数据完整性良好")
        return True


def main():
    """主函数"""
    print("🌐 SEO数据迁移到站群系统")
    print("=" * 50)
    
    # 询问用户确认
    response = input("⚠️ 此操作将迁移SEO数据到新的站群系统，是否继续？(y/N): ")
    if response.lower() != 'y':
        print("❌ 操作已取消")
        return 1
    
    try:
        # 步骤1: 迁移SEO数据到站点
        if not migrate_seo_to_sites():
            print("❌ SEO数据迁移失败")
            return 1
        
        # 步骤2: 关联用户到站点
        if not associate_users_with_sites():
            print("❌ 用户关联失败")
            return 1
        
        # 步骤3: 验证迁移结果
        if not verify_migration():
            print("❌ 迁移验证失败")
            return 1
        
        print("\n🎉 SEO数据迁移到站群系统完成！")
        print("\n📝 下一步操作:")
        print("1. 在Django settings中添加 'sitegroup' 到 INSTALLED_APPS")
        print("2. 运行 python manage.py makemigrations sitegroup")
        print("3. 运行 python manage.py migrate")
        print("4. 在主URL配置中添加站群应用的URL")
        print("5. 测试站群功能是否正常工作")
        
        return 0
        
    except Exception as e:
        print(f"❌ 迁移过程中出现错误: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
