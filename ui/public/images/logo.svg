<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200">
  <!-- Creative Target O Design -->
  <g transform="translate(100, 100)">
    <!-- Outer circle with gradient -->
    <defs>
      <linearGradient id="blueGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#00A8FF;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#00A8FF;stop-opacity:1" />
      </linearGradient>
    </defs>
    
    <!-- Base circle -->
    <circle cx="0" cy="0" r="70" fill="url(#blueGradient)" />
    
    <!-- Scope elements -->
    <circle cx="0" cy="0" r="56" fill="none" stroke="#FFFFFF" stroke-width="3" />
    <circle cx="0" cy="0" r="40" fill="none" stroke="#FFFFFF" stroke-width="3" />
    
    <!-- Center dot -->
    <circle cx="0" cy="0" r="12" fill="#FFFFFF" />
    
    <!-- Diagonal crosshair lines -->
    <line x1="-50" y1="-50" x2="-30" y2="-30" stroke="#FFFFFF" stroke-width="4" />
    <line x1="30" y1="30" x2="50" y2="50" stroke="#FFFFFF" stroke-width="4" />
    <line x1="-50" y1="50" x2="-30" y2="30" stroke="#FFFFFF" stroke-width="4" />
    <line x1="30" y1="-30" x2="50" y2="-50" stroke="#FFFFFF" stroke-width="4" />
    
    <!-- Horizontal and vertical lines -->
    <line x1="-90" y1="0" x2="-24" y2="0" stroke="#FFFFFF" stroke-width="4" />
    <line x1="24" y1="0" x2="90" y2="0" stroke="#FFFFFF" stroke-width="4" />
    <line x1="0" y1="-90" x2="0" y2="-24" stroke="#FFFFFF" stroke-width="4" />
    <line x1="0" y1="24" x2="0" y2="90" stroke="#FFFFFF" stroke-width="4" />
    
    <!-- Decorative tick marks around the edge -->
    <line x1="70" y1="20" x2="84" y2="24" stroke="#00A8FF" stroke-width="4" />
    <line x1="70" y1="-20" x2="84" y2="-24" stroke="#00A8FF" stroke-width="4" />
    <line x1="-70" y1="20" x2="-84" y2="24" stroke="#00A8FF" stroke-width="4" />
    <line x1="-70" y1="-20" x2="-84" y2="-24" stroke="#00A8FF" stroke-width="4" />
  </g>
</svg>