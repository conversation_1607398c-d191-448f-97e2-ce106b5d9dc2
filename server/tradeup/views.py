import logging

from django.utils.translation import gettext_lazy as _

from rest_framework.permissions import AllowAny
from rest_framework.views import APIView

from steambase.enums import RespCode
from steambase.utils import reformat_resp, current_user
from sitecfg.interfaces import get_maintenance, get_maintenance_tradeup
from tradeup.business import bet_tradeup_game, match_tradeup_inventory, get_tradeup_game_inventory, get_tradeup_bet
from tradeup.business import get_tradeup_game_history, get_tradeup_day_rank, get_tradeup_week_rank
from tradeup.business import bet_tradeup_percentage, verify_tradeup
from tradeup.business import bet_coins_tradeup_game, bet_coins_tradeup_percentage, match_coins_tradeup_inventory
from package.business import get_user_package


_logger = logging.getLogger(__name__)


class GetTradeupInventoryView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:

            query = {
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            fields = ('uid', 'market_name', 'market_hash_name', 'icon_url', 'price', 'rarity', 'rarity_color')
            # appid = request.query_params.get('appid', 730)
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            name = request.query_params.get('name', '')
            max_price = request.query_params.get('max_price', '')
            min_price = request.query_params.get('min_price', 0)
            order = request.query_params.get('order', '-price')
            rarity = request.query_params.get('rarity', '')
            code, resp = get_tradeup_game_inventory(query, fields, page, page_size, name, rarity, max_price, min_price, order)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class MatchTradeupInventoryView(APIView):

    def post(self, request):
        try:

            if get_maintenance_tradeup():
                return reformat_resp(RespCode.Maintenance.value, {},
                                     _('Current game is under maintenance, please wait for a while.'))
            user = current_user(request)
            bet_items = request.data.get('bet_items', [])
            bet_amount = request.data.get('bet_amount', 0)
            times = request.data.get('times', 1)
            code, resp = match_tradeup_inventory(user, bet_items, bet_amount, times)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, _('Succeed'))
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, _('Exception'))


class MatchCoinsTradeupInventoryView(APIView):

    def post(self, request):
        try:

            if get_maintenance_tradeup():
                return reformat_resp(RespCode.Maintenance.value, {},
                                     _('Current game is under maintenance, please wait for a while.'))
            user = current_user(request)
            bet_amount = request.data.get('bet_amount', 0)
            times = request.data.get('times', 1)
            appid = request.data.get('appid', 570)
            code, resp = match_coins_tradeup_inventory(user, bet_amount, appid, times)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, _('Succeed'))
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, _('Exception'))   


class BetTradeupGameView(APIView):

    def post(self, request):
        try:

            if get_maintenance_tradeup():
                return reformat_resp(RespCode.Maintenance.value, {},
                                     _('Current game is under maintenance, please wait for a while.'))
            user = current_user(request)
            bet_items = request.data.get('bet_items', [])
            bet_amount = request.data.get('bet_amount', 0)
            target_items = request.data.get('target_items', [])
            upper = request.data.get('upper', False)
            code, resp = bet_tradeup_game(user, bet_items, bet_amount, target_items, upper)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, _('Succeed'))
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, _('Exception'))


class GetTradeupBetView(APIView):

    def get(self, request):
        try:

            user = current_user(request)
            query = {
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            fields = ('uid', 'hash', 'secret', 'percentage', 'bet_percentage', 'upper', 'bet_items', 'target_items',
                      'bet_amount', 'target_amount', 'win_result', 'appid')
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            code, resp = get_tradeup_bet(user, query, fields, page, page_size)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class BetCoinsTradeupGameView(APIView):

    def post(self, request):
        try:

            if get_maintenance_tradeup():
                return reformat_resp(RespCode.Maintenance.value, {},
                                     _('Current game is under maintenance, please wait for a while.'))
            user = current_user(request)
            bet_amount = request.data.get('bet_amount', 1)
            target_items = request.data.get('target_items', None)
            upper = request.data.get('upper', False)
            code, resp = bet_coins_tradeup_game(user, bet_amount, target_items, upper)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, _('Succeed'))
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, _('Exception'))


class GetTradeupHistoryView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:

            query = {
                'uid': request.query_params.get('uid', None),
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            fields = ('uid', 'user', 'hash', 'secret', 'percentage', 'bet_percentage', 'upper', 'bet_items',
                      'target_items', 'bet_amount', 'target_amount', 'win_result', 'appid')
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            code, resp = get_tradeup_game_history(query, fields, page, page_size)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, _('Succeed'))
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, _('Exception'))


class GetTradeupDayRankView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:

            query = {
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            fields = ('user', 'amount')
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            count = int(request.query_params.get('count', 10))
            code, resp = get_tradeup_day_rank(query, fields, page, page_size, count)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, _('Succeed'))
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, _('Exception'))


class GetTradeupWeekRankView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:

            query = {
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            fields = ('user', 'amount')
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            count = int(request.query_params.get('count', 10))
            code, resp = get_tradeup_week_rank(query, fields, page, page_size, count)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, _('Succeed'))
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, _('Exception'))


class GetTradeupPercentage(APIView):

    def post(self, request):
        try:

            if get_maintenance_tradeup():
                return reformat_resp(RespCode.Maintenance.value, {},
                                     _('Current game is under maintenance, please wait for a while.'))
            user = current_user(request)
            bet_items = request.data.get('bet_items', [])
            bet_amount = request.data.get('bet_amount', 0)
            target_items = request.data.get('target_items', None)
            upper = request.data.get('upper', False)
            code, resp = bet_tradeup_percentage(user, bet_items, bet_amount, target_items, upper)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, _('Succeed'))
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, _('Exception'))


class GetCoinsTradeupPercentage(APIView):

    def post(self, request):
        try:

            if get_maintenance_tradeup():
                return reformat_resp(RespCode.Maintenance.value, {},
                                     _('Current game is under maintenance, please wait for a while.'))
            user = current_user(request)
            bet_amount = request.data.get('bet_amount', 0)
            target_items = request.data.get('target_items', [])
            upper = request.data.get('upper', False)
            code, resp = bet_coins_tradeup_percentage(user, bet_amount, target_items, upper)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, _('Succeed'))
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, _('Exception'))


class VerifyTradeupView(APIView):

       def post(self, request):
        try:

            if get_maintenance_tradeup():
                return reformat_resp(RespCode.Maintenance.value, {},
                                     _('Current game is under maintenance, please wait for a while.'))
            user = current_user(request)
            percentage = request.data.get('percentage', 0)
            bet_percentage = request.data.get('bet_percentage', 0)
            secret = request.data.get('secret', '')
            upper = request.data.get('upper', '')
            code, resp = verify_tradeup(percentage, bet_percentage, upper, secret)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, _('Succeed'))
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, _('Exception'))


class GetUserPackageView(APIView):

    def get(self, request):
        try:

            user = current_user(request)
            query = { 
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            # stocks = int(request.query_params.get('stocks', 0))
            fields = ('uid', 'market_name', 'market_hash_name', 'icon_url', 'price', 'part', 'amount', 'state',
                      'source')
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            order = request.query_params.get('order', '-amount')
            _all = request.query_params.get('all', 0)
            code, resp = get_user_package(user, query, _all, fields, page, page_size, order)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')