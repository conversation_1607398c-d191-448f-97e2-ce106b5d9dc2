# Generated by Django 3.2.25 on 2025-07-19 03:46

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('charge', '0022_auto_20250718_1647'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='cdkey',
            options={'verbose_name': 'CDKey信息', 'verbose_name_plural': 'CDKey信息'},
        ),
        migrations.AlterModelOptions(
            name='cdkeyrecord',
            options={'verbose_name': 'CDKey兑换记录', 'verbose_name_plural': 'CDKey兑换记录'},
        ),
        migrations.AlterModelOptions(
            name='chargeamountconfig',
            options={'verbose_name': '充值金额配置', 'verbose_name_plural': 'ChargeAmountConfig'},
        ),
        migrations.AlterModelOptions(
            name='chargelevel',
            options={'ordering': ('-create_time',), 'verbose_name': '充值等级', 'verbose_name_plural': 'Charge Level'},
        ),
        migrations.AlterModelOptions(
            name='chargelevelconfig',
            options={'ordering': ('-create_time',), 'verbose_name': '充值等级配置', 'verbose_name_plural': 'Charge Level Config'},
        ),
        migrations.AlterModelOptions(
            name='chargerecord',
            options={'ordering': ('-create_time',), 'verbose_name': '充值记录', 'verbose_name_plural': '充值记录'},
        ),
        migrations.AlterModelOptions(
            name='chargestatisticsday',
            options={'ordering': ('-create_time',), 'verbose_name': '充值日统计', 'verbose_name_plural': 'Charge Statistics Day'},
        ),
        migrations.AlterModelOptions(
            name='chargestatisticsmonth',
            options={'ordering': ('-create_time',), 'verbose_name': '充值月统计', 'verbose_name_plural': 'Charge Statistics Month'},
        ),
        migrations.AlterModelOptions(
            name='chargeweekrank',
            options={'verbose_name': '充值周排行', 'verbose_name_plural': 'Charge Week Rank'},
        ),
        migrations.AlterModelOptions(
            name='cxkagenerate',
            options={'verbose_name': 'Cxka生成', 'verbose_name_plural': 'Cxka Generate'},
        ),
        migrations.AlterModelOptions(
            name='cxkagoods',
            options={'ordering': ('good_id',), 'verbose_name': 'Cxka商品', 'verbose_name_plural': 'Cxka goods'},
        ),
        migrations.AlterModelOptions(
            name='generatecdkey',
            options={'verbose_name': '生成CDKey', 'verbose_name_plural': '生成CDKey'},
        ),
        migrations.AlterModelOptions(
            name='paymethod',
            options={'verbose_name': '支付方式', 'verbose_name_plural': '支付方式'},
        ),
        migrations.AlterField(
            model_name='cdkey',
            name='amount',
            field=models.FloatField(blank=True, default=0, null=True, verbose_name='金额'),
        ),
        migrations.AlterField(
            model_name='cdkey',
            name='state',
            field=models.SmallIntegerField(choices=[(0, 'used'), (1, 'unused')], default=1, verbose_name='CDKey状态'),
        ),
        migrations.AlterField(
            model_name='cdkeyrecord',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
        migrations.AlterField(
            model_name='chargelevel',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='charge_level', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
        migrations.AlterField(
            model_name='chargerecord',
            name='amount',
            field=models.FloatField(default=0.0, verbose_name='金额'),
        ),
        migrations.AlterField(
            model_name='chargerecord',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='charges', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
        migrations.AlterField(
            model_name='chargeweekrank',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='charge_week_rank', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
    ]
