unsupported-binary-operation:9::"unsupported operand type(s) for +: 'int' and 'str'"
unsupported-binary-operation:10::"unsupported operand type(s) for -: 'int' and 'list'"
unsupported-binary-operation:11::"unsupported operand type(s) for *: 'int' and 'dict'"
unsupported-binary-operation:12::"unsupported operand type(s) for /: 'int' and 'module'"
unsupported-binary-operation:13::"unsupported operand type(s) for **: 'int' and 'function'"
unsupported-binary-operation:14::"unsupported operand type(s) for *: 'dict' and 'dict'"
unsupported-binary-operation:15::"unsupported operand type(s) for -: 'dict' and 'dict'"
unsupported-binary-operation:16::"unsupported operand type(s) for |: 'dict' and 'dict'"
unsupported-binary-operation:17::"unsupported operand type(s) for >>: 'dict' and 'dict'"
unsupported-binary-operation:18::"unsupported operand type(s) for +: 'list' and 'tuple'"
unsupported-binary-operation:19::"unsupported operand type(s) for +: 'tuple' and 'list'"
unsupported-binary-operation:20::"unsupported operand type(s) for *: 'list' and 'float'"
unsupported-binary-operation:21::"unsupported operand type(s) for *: 'tuple' and 'float'"
unsupported-binary-operation:22::"unsupported operand type(s) for >>: 'float' and 'float'"
unsupported-binary-operation:27::"unsupported operand type(s) for +: 'A' and 'B'"
unsupported-binary-operation:32::"unsupported operand type(s) for +: 'A1' and 'A1'"
unsupported-binary-operation:40::"unsupported operand type(s) for +: 'A2' and 'B2'"
unsupported-binary-operation:47::"unsupported operand type(s) for +: 'Child' and 'Parent'"
unsupported-binary-operation:54::"unsupported operand type(s) for +: 'A3' and 'B3'"
unsupported-binary-operation:57::"unsupported operand type(s) for +=: 'int' and 'A'"
unsupported-binary-operation:59::"unsupported operand type(s) for +=: 'int' and 'list'"