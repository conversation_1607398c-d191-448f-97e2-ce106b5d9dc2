// Time synchronization composable – lightweight NTP-like client
// Provides clock offset / RTT estimation and helpers to convert server timestamps to local times.
import { ref } from 'vue'

interface SyncSample {
  offset: number
  rtt: number
  ts: number
}

export const useTimeSync = () => {
  // 正向：server = client + offset
  const clockOffset = ref(0)  // 毫秒
  const rtt = ref(0)

  const history: SyncSample[] = []
  const MAX_HISTORY = 10

  /**
   * Update clock offset via single RTT sample.
   * @param serverTs   Remote server timestamp (ms)
   * @param clientSend   Local time when request sent (ms)
   * @param clientReceive Local time when response received (ms)
   */
  const updateOffset = (serverTs: number, clientSend: number, clientReceive: number) => {
    const newRtt = clientReceive - clientSend
    const newOffset = serverTs + newRtt / 2 - clientReceive

    history.push({ offset: newOffset, rtt: newRtt, ts: Date.now() })
    if (history.length > MAX_HISTORY) history.shift()

    // 简单移动平均
    const avg = history.reduce((sum, s) => sum + s.offset, 0) / history.length
    const avgRtt = history.reduce((sum, s) => sum + s.rtt, 0) / history.length

    clockOffset.value = avg
    rtt.value = avgRtt
  }

  const getServerTime = () => Date.now() + clockOffset.value
  const toLocalTime = (serverTimestamp: number) => serverTimestamp - clockOffset.value

  return {
    clockOffset,
    rtt,
    updateOffset,
    getServerTime,
    toLocalTime,
  }
} 