<template>
  <div class="min-h-screen relative">
    <!-- 背景层 -->
    <div class="absolute inset-0 overflow-hidden">
      <div class="absolute inset-0 bg-gradient-to-b from-background-darker via-background to-background-dark z-0" />
      <div class="particle-container absolute inset-0 z-0" />
      <div class="absolute top-0 left-0 w-full h-full overflow-hidden opacity-15 z-0">
        <div class="light-beam light-beam-1" />
        <div class="light-beam light-beam-3" />
        <div class="light-beam light-beam-2" />
      </div>
      <div class="absolute inset-0 bg-grid-pattern opacity-[0.04] z-0" />
    </div>
    <div class="container mx-auto px-4 py-6 relative z-10" v-if="!isMobile">
      <div class="max-w-6xl mx-auto">
        <div class="page-header mb-12">
          <h1 class="text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-primary to-secondary mb-4">
            {{ $t('contact.title') }}
          </h1>
          <p class="text-white/60 text-lg">{{ $t('contact.subtitle') }}</p>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <!-- 联系信息 -->
          <div class="backdrop-blur-sm border border-gray-700/30 shadow-lg rounded-xl p-8">
            <h2 class="section-title text-transparent bg-clip-text bg-gradient-to-r from-primary to-primary-600 mb-4">
              {{ $t('contact.info_title') }}
            </h2>
            <ul class="space-y-4">
              <li>
                <span class="font-bold">{{ $t('contact.email') }}：</span>
                <a href="mailto:<EMAIL>" class="text-primary"><EMAIL></a>
              </li>
              <li>
                <span class="font-bold">{{ $t('contact.support') }}：</span>
                <a href="mailto:<EMAIL>" class="text-primary"><EMAIL></a>
              </li>
              <li>
                <span class="font-bold">{{ $t('contact.business') }}：</span>
                <a href="mailto:<EMAIL>" class="text-primary"><EMAIL></a>
              </li>
              <li>
                <span class="font-bold">{{ $t('contact.phone') }}：</span>
                <span>+86 21 5888 XXXX</span>
              </li>
              <li>
                <span class="font-bold">{{ $t('contact.address') }}：</span>
                <span>{{ $t('contact.address_value') }}</span>
              </li>
            </ul>
          </div>
          <!-- 联系表单 -->
          <div class="backdrop-blur-sm border border-gray-700/30 shadow-lg rounded-xl p-8">
            <h2 class="section-title text-transparent bg-clip-text bg-gradient-to-r from-primary to-primary-600 mb-4">
              {{ $t('contact.form_title') }}
            </h2>
            <form @submit.prevent="submitForm">
              <div class="mb-4">
                <label class="block mb-1 text-white/80">{{ $t('contact.form.name') }}</label>
                <input v-model="form.name" type="text" class="form-input w-full" required />
              </div>
              <div class="mb-4">
                <label class="block mb-1 text-white/80">{{ $t('contact.form.email') }}</label>
                <input v-model="form.email" type="email" class="form-input w-full" required />
              </div>
              <div class="mb-4">
                <label class="block mb-1 text-white/80">{{ $t('contact.form.subject') }}</label>
                <input v-model="form.subject" type="text" class="form-input w-full" required />
              </div>
              <div class="mb-4">
                <label class="block mb-1 text-white/80">{{ $t('contact.form.type') }}</label>
                <select v-model="form.type" class="form-select w-full" required>
                  <option value="" disabled>{{ $t('contact.form.type_placeholder') }}</option>
                  <option v-for="type in contactTypes" :key="type.value" :value="type.value">{{ $t(type.label) }}</option>
                </select>
              </div>
              <div class="mb-4">
                <label class="block mb-1 text-white/80">{{ $t('contact.form.message') }}</label>
                <textarea v-model="form.message" class="form-textarea w-full" rows="5" required></textarea>
              </div>
              <button type="submit" class="primary-button w-full" :disabled="isSubmitting">
                <span v-if="isSubmitting">{{ $t('contact.form.sending') }}</span>
                <span v-else>{{ $t('contact.form.submit') }}</span>
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
    <!-- 移动端内容（结构同PC，样式更紧凑） -->
    <div class="container mx-auto px-3 py-4 relative z-10" v-else>
      <div class="max-w-2xl mx-auto">
        <div class="page-header mb-8">
          <h1 class="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-primary to-secondary mb-3">
            {{ $t('contact.title') }}
          </h1>
          <p class="text-white/60 text-sm">{{ $t('contact.subtitle') }}</p>
        </div>
        <div class="space-y-6">
          <div class="backdrop-blur-sm border border-gray-700/30 shadow-lg rounded-xl p-4">
            <h2 class="section-title text-transparent bg-clip-text bg-gradient-to-r from-primary to-primary-600 mb-2">
              {{ $t('contact.info_title') }}
            </h2>
            <ul class="space-y-2 text-sm">
              <li><span class="font-bold">{{ $t('contact.email') }}：</span> <a href="mailto:<EMAIL>" class="text-primary"><EMAIL></a></li>
              <li><span class="font-bold">{{ $t('contact.support') }}：</span> <a href="mailto:<EMAIL>" class="text-primary"><EMAIL></a></li>
              <li><span class="font-bold">{{ $t('contact.business') }}：</span> <a href="mailto:<EMAIL>" class="text-primary"><EMAIL></a></li>
              <li><span class="font-bold">{{ $t('contact.phone') }}：</span> +86 21 5888 XXXX</li>
              <li><span class="font-bold">{{ $t('contact.address') }}：</span> {{ $t('contact.address_value') }}</li>
            </ul>
          </div>
          <div class="backdrop-blur-sm border border-gray-700/30 shadow-lg rounded-xl p-4">
            <h2 class="section-title text-transparent bg-clip-text bg-gradient-to-r from-primary to-primary-600 mb-2">
              {{ $t('contact.form_title') }}
            </h2>
            <form @submit.prevent="submitForm">
              <div class="mb-3">
                <label class="block mb-1 text-white/80">{{ $t('contact.form.name') }}</label>
                <input v-model="form.name" type="text" class="form-input w-full" required />
              </div>
              <div class="mb-3">
                <label class="block mb-1 text-white/80">{{ $t('contact.form.email') }}</label>
                <input v-model="form.email" type="email" class="form-input w-full" required />
              </div>
              <div class="mb-3">
                <label class="block mb-1 text-white/80">{{ $t('contact.form.subject') }}</label>
                <input v-model="form.subject" type="text" class="form-input w-full" required />
              </div>
              <div class="mb-3">
                <label class="block mb-1 text-white/80">{{ $t('contact.form.type') }}</label>
                <select v-model="form.type" class="form-select w-full" required>
                  <option value="" disabled>{{ $t('contact.form.type_placeholder') }}</option>
                  <option v-for="type in contactTypes" :key="type.value" :value="type.value">{{ $t(type.label) }}</option>
                </select>
              </div>
              <div class="mb-3">
                <label class="block mb-1 text-white/80">{{ $t('contact.form.message') }}</label>
                <textarea v-model="form.message" class="form-textarea w-full" rows="4" required></textarea>
              </div>
              <button type="submit" class="primary-button w-full" :disabled="isSubmitting">
                <span v-if="isSubmitting">{{ $t('contact.form.sending') }}</span>
                <span v-else>{{ $t('contact.form.submit') }}</span>
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, computed } from 'vue'
import { useAppStore } from '~/stores/app'
const store = useAppStore()
const isMobile = computed(() => store.isMobile)
const form = ref({ name: '', email: '', subject: '', type: '', message: '' })
const isSubmitting = ref(false)
const contactTypes = [
  { value: 'general', label: 'contact.form.type_general' },
  { value: 'support', label: 'contact.form.type_support' },
  { value: 'feedback', label: 'contact.form.type_feedback' },
  { value: 'business', label: 'contact.form.type_business' },
  { value: 'other', label: 'contact.form.type_other' }
]
const submitForm = async () => {
  isSubmitting.value = true
  await new Promise(resolve => setTimeout(resolve, 1200))
  form.value = { name: '', email: '', subject: '', type: '', message: '' }
  isSubmitting.value = false
}
const { $i18n } = useNuxtApp()
useHead({
  title: () => $i18n.t('contact.title'),
  meta: [
    { name: 'description', content: () => $i18n.t('contact.description') },
    { name: 'keywords', content: 'CSGO, contact, support, business, 联系我们, 客服, 合作' },
    { property: 'og:title', content: () => $i18n.t('contact.title') },
    { property: 'og:description', content: () => $i18n.t('contact.description') }
  ]
})
</script>
<style scoped>
.bg-grid-pattern {
  background-image: linear-gradient(to right, rgba(255,255,255,0.05) 0.0625rem, transparent 0.0625rem), linear-gradient(to bottom, rgba(255,255,255,0.05) 0.0625rem, transparent 0.0625rem);
  background-size: 1.25rem 1.25rem;
}
.particle-container {
  background-image: radial-gradient(circle at 25% 25%, rgba(59,130,246,0.1) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(245,158,11,0.1) 0%, transparent 50%);
}
.light-beam { position: absolute; width: 1px; height: 100%; background: linear-gradient(to bottom, transparent, rgba(59,130,246,0.3), transparent); animation: light-beam-animation 8s ease-in-out infinite; }
.light-beam-1 { left: 20%; animation-delay: 0s; }
.light-beam-2 { left: 50%; animation-delay: 2s; }
.light-beam-3 { left: 80%; animation-delay: 4s; }
@keyframes light-beam-animation { 0%,100%{opacity:0;transform:scaleY(0);} 50%{opacity:1;transform:scaleY(1);} }
.page-header { @apply py-8 bg-gradient-to-r from-gray-900/80 to-gray-800/80 rounded-xl; }
.section-title { @apply text-2xl font-bold mb-6; }
.primary-button { @apply inline-flex items-center bg-gradient-to-r from-primary to-secondary hover:from-primary-light hover:to-secondary-light text-white font-medium py-2 px-6 rounded-lg transition-all duration-300 transform hover:-translate-y-0.5 shadow-md; }
</style> 