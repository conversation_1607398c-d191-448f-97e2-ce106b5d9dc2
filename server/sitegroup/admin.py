from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe

from sitegroup.models import SiteGroup, Site, SiteConfig, SiteTheme, SiteAnalytics


@admin.register(SiteGroup)
class SiteGroupAdmin(admin.ModelAdmin):
    list_display = ['name', 'agent', 'active_sites_count', 'is_active', 'create_time']
    list_filter = ['is_active', 'create_time']
    search_fields = ['name', 'agent__username']
    readonly_fields = ['create_time', 'update_time']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'agent', 'description', 'is_active')
        }),
        ('时间信息', {
            'fields': ('create_time', 'update_time'),
            'classes': ('collapse',)
        }),
    )
    
    def active_sites_count(self, obj):
        count = obj.get_active_sites_count()
        return format_html('<span style="color: green;">{}</span>', count)
    active_sites_count.short_description = '活跃站点数'


class SiteConfigInline(admin.TabularInline):
    model = SiteConfig
    extra = 0
    fields = ['config_key', 'config_value', 'config_type', 'category', 'is_active']
    readonly_fields = []


class SiteThemeInline(admin.StackedInline):
    model = SiteTheme
    extra = 0
    fields = [
        'theme_name', 
        ('header_background', 'header_text_color'),
        ('footer_background', 'footer_text_color'),
        ('font_family', 'font_size_base'),
        ('container_width', 'border_radius')
    ]


@admin.register(Site)
class SiteAdmin(admin.ModelAdmin):
    list_display = [
        'domain', 'name', 'site_group', 'agent_name', 'is_active',
        'is_default', 'maintenance_mode', 'users_count', 'create_time'
    ]
    list_filter = [
        'is_active', 'is_default', 'maintenance_mode',
        'articles_enabled', 'case_battle_enabled', 'market_enabled',
        'site_group__agent', 'create_time'
    ]
    search_fields = ['domain', 'name', 'site_group__name', 'site_group__agent__username']
    readonly_fields = ['create_time', 'update_time', 'users_count']
    inlines = [SiteThemeInline, SiteConfigInline]
    
    fieldsets = (
        ('基本信息', {
            'fields': ('site_group', 'domain', 'name', 'subtitle', 'logo_url', 'favicon_url')
        }),
        ('SEO设置', {
            'fields': ('seo_title', 'seo_keywords', 'seo_description', 'icp_number'),
            'classes': ('collapse',)
        }),
        ('功能开关', {
            'fields': (
                ('articles_enabled', 'announcements_enabled'),
                ('case_battle_enabled', 'case_opening_enabled'),
                ('market_enabled', 'lottery_enabled'),
                ('roll_enabled', 'crash_enabled')
            ),
            'classes': ('collapse',)
        }),
        ('样式配置', {
            'fields': ('primary_color', 'secondary_color', 'custom_css'),
            'classes': ('collapse',)
        }),
        ('状态设置', {
            'fields': ('is_active', 'is_default', 'maintenance_mode', 'maintenance_message')
        }),
        ('时间信息', {
            'fields': ('create_time', 'update_time', 'users_count'),
            'classes': ('collapse',)
        }),
    )
    
    def agent_name(self, obj):
        return obj.site_group.agent.username if obj.site_group.agent else '-'
    agent_name.short_description = '代理商'
    
    def users_count(self, obj):
        count = obj.get_registered_users_count()
        if count > 0:
            url = reverse('admin:authentication_authuser_changelist')
            return format_html(
                '<a href="{}?registered_site__id__exact={}" style="color: blue;">{} 用户</a>',
                url, obj.id, count
            )
        return '0 用户'
    users_count.short_description = '注册用户'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('site_group', 'site_group__agent')
    
    actions = ['enable_sites', 'disable_sites', 'enable_maintenance', 'disable_maintenance']
    
    def enable_sites(self, request, queryset):
        updated = queryset.update(is_active=True)
        self.message_user(request, f'已启用 {updated} 个站点')
    enable_sites.short_description = '启用选中的站点'
    
    def disable_sites(self, request, queryset):
        updated = queryset.update(is_active=False)
        self.message_user(request, f'已禁用 {updated} 个站点')
    disable_sites.short_description = '禁用选中的站点'
    
    def enable_maintenance(self, request, queryset):
        updated = queryset.update(maintenance_mode=True)
        self.message_user(request, f'已开启 {updated} 个站点的维护模式')
    enable_maintenance.short_description = '开启维护模式'
    
    def disable_maintenance(self, request, queryset):
        updated = queryset.update(maintenance_mode=False)
        self.message_user(request, f'已关闭 {updated} 个站点的维护模式')
    disable_maintenance.short_description = '关闭维护模式'


@admin.register(SiteConfig)
class SiteConfigAdmin(admin.ModelAdmin):
    list_display = [
        'site', 'config_key', 'config_type', 'category', 
        'is_active', 'is_sensitive', 'config_value_preview'
    ]
    list_filter = ['config_type', 'category', 'is_active', 'is_sensitive', 'site']
    search_fields = ['config_key', 'description', 'site__domain']
    readonly_fields = ['create_time', 'update_time']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('site', 'config_key', 'config_value', 'config_type')
        }),
        ('分类和描述', {
            'fields': ('category', 'description')
        }),
        ('状态设置', {
            'fields': ('is_active', 'is_sensitive')
        }),
        ('时间信息', {
            'fields': ('create_time', 'update_time'),
            'classes': ('collapse',)
        }),
    )
    
    def config_value_preview(self, obj):
        if obj.is_sensitive:
            return '***敏感信息***'
        value = obj.config_value or ''
        if len(value) > 50:
            return value[:50] + '...'
        return value
    config_value_preview.short_description = '配置值预览'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('site')


@admin.register(SiteTheme)
class SiteThemeAdmin(admin.ModelAdmin):
    list_display = ['site', 'theme_name', 'color_preview', 'font_family', 'create_time']
    list_filter = ['theme_name', 'font_family']
    search_fields = ['site__domain', 'theme_name']
    readonly_fields = ['create_time', 'update_time']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('site', 'theme_name')
        }),
        ('颜色配置', {
            'fields': (
                ('header_background', 'header_text_color'),
                ('footer_background', 'footer_text_color')
            )
        }),
        ('字体配置', {
            'fields': ('font_family', 'font_size_base')
        }),
        ('布局配置', {
            'fields': ('container_width', 'border_radius')
        }),
        ('时间信息', {
            'fields': ('create_time', 'update_time'),
            'classes': ('collapse',)
        }),
    )
    
    def color_preview(self, obj):
        return format_html(
            '<div style="display: flex; gap: 5px;">'
            '<div style="width: 20px; height: 20px; background-color: {}; border: 1px solid #ccc;" title="Header"></div>'
            '<div style="width: 20px; height: 20px; background-color: {}; border: 1px solid #ccc;" title="Footer"></div>'
            '</div>',
            obj.header_background, obj.footer_background
        )
    color_preview.short_description = '颜色预览'


@admin.register(SiteAnalytics)
class SiteAnalyticsAdmin(admin.ModelAdmin):
    list_display = [
        'site', 'date', 'page_views', 'unique_visitors',
        'new_registrations', 'case_openings', 'create_time'
    ]
    list_filter = ['date', 'site']
    search_fields = ['site__domain']
    readonly_fields = ['create_time', 'update_time']
    date_hierarchy = 'date'
    
    fieldsets = (
        ('基本信息', {
            'fields': ('site', 'date')
        }),
        ('访问统计', {
            'fields': ('page_views', 'unique_visitors', 'active_users')
        }),
        ('业务统计', {
            'fields': ('new_registrations', 'case_openings', 'battles_created', 'market_transactions')
        }),
        ('时间信息', {
            'fields': ('create_time', 'update_time'),
            'classes': ('collapse',)
        }),
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('site')


# 自定义管理后台标题
admin.site.site_header = 'CSGOSKINS 站群管理系统'
admin.site.site_title = '站群管理'
admin.site.index_title = '站群管理后台'
