# Battle Animation Config 时间参数更新

## 更新日期
2025-06-29

## 更新内容

### 调整的时间参数

1. **开箱动画时长** (`case_animation_duration`)
   - 旧值: 5000ms (5秒)
   - 新值: 8000ms (8秒)
   - 说明: 增加开箱滚动时间，提供更好的悬念体验

2. **结果揭晓延迟** (`result_reveal_delay`)
   - 旧值: 800ms (0.8秒)
   - 新值: 2000ms (2秒)
   - 说明: 增加结果揭晓前的停顿时间，增强戏剧效果

### 更新后的完整配置

```json
{
  "global_config": {
    "start_countdown_duration": 3000,
    "round_preparation_time": 2000,
    "case_animation_duration": 8000,    // ← 更新: 5秒 → 8秒
    "result_reveal_delay": 2000,        // ← 更新: 0.8秒 → 2秒
    "result_display_duration": 3000,
    "battle_end_celebration": 5000
  }
}
```

## 影响分析

### 用户体验影响
- ✅ 更长的开箱时间增加悬念感
- ✅ 更长的揭晓延迟提升戏剧效果
- ✅ 整体对战时间略有增加，但体验更佳

### 技术影响
- ✅ 前端动画需要适配新的时间参数
- ✅ WebSocket同步逻辑需要考虑新的时间窗口
- ✅ 总对战时长增加约 3.2 秒

### 前端适配说明

前端需要根据新的配置调整：

```javascript
// 获取最新配置
const config = await fetch('/api/box/battle/animation-config/').then(r => r.json());

// 开箱动画时长现在是8秒
const caseAnimationDuration = config.body.global_config.case_animation_duration; // 8000ms

// 结果揭晓延迟现在是2秒
const resultRevealDelay = config.body.global_config.result_reveal_delay; // 2000ms
```

## 文件变更

- `/server/box/views.py` - 更新 `GetBattleAnimationConfigView` 中的时间配置

## 测试验证

- ✅ 后端API返回新的时间参数
- ✅ 接口响应正常，无错误
- ✅ 配置结构完整性验证通过

## 上线状态

- ✅ 已部署到生产环境
- ✅ 接口立即生效
- ✅ 前端可直接使用新配置

---

**更新完成时间**: 2025-06-29 09:21  
**状态**: ✅ 已上线生效
