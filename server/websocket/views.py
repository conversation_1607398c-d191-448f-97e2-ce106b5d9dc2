import logging

from django.utils.translation import gettext_lazy as _

from rest_framework.views import APIView
from rest_framework.permissions import AllowAny

from steambase.enums import RespCode
from steambase.utils import reformat_resp
from sitecfg.interfaces import get_maintenance
from websocket.business import socket_connect, socket_disconnect

_logger = logging.getLogger(__name__)


class SocketConnectView(APIView):
    permission_classes = [AllowAny]

    def socket_connect(self, request, params):
        try:

            code, resp = socket_connect(request, params)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, _('Exception'))

    def post(self, request, format=None):
        return self.socket_connect(request, request.data)


class SocketDisconnectView(APIView):
    permission_classes = [AllowAny]

    def socket_disconnect(self, request, params):
        try:

            code, resp = socket_disconnect(request, params)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, 'Exception')

    def post(self, request, format=None):
        return self.socket_disconnect(request, request.data)
