#!/usr/bin/env python3
"""
修复所有admin文件中的DateRangeFilter使用
"""
import os
import re

def fix_daterange_filter(file_path):
    """修复单个文件中的DateRangeFilter使用"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 注释掉DateRangeFilter导入
        content = re.sub(
            r'from jet\.filters import DateRangeFilter',
            '# from jet.filters import DateRangeFilter  # 暂时禁用，等待Django 4.x兼容版本',
            content
        )
        
        # 修复list_filter中的DateRangeFilter使用
        # 模式1: ('field', DateRangeFilter)
        content = re.sub(
            r"\('([^']+)', DateRangeFilter\)",
            r"# ('\1', DateRangeFilter) 暂时禁用",
            content
        )
        
        # 模式2: list_filter = (..., ('field', DateRangeFilter), ...)
        # 这个比较复杂，需要特殊处理
        lines = content.split('\n')
        new_lines = []
        
        for line in lines:
            # 如果行包含DateRangeFilter，尝试修复
            if 'DateRangeFilter' in line and 'list_filter' in line:
                # 简单的替换策略：移除包含DateRangeFilter的部分
                if line.strip().endswith(','):
                    # 如果是多行list_filter的一部分，注释掉这行
                    new_lines.append('    # ' + line.strip() + '  # DateRangeFilter暂时禁用')
                else:
                    # 如果是单行，尝试移除DateRangeFilter部分
                    modified_line = re.sub(r",\s*\([^)]*DateRangeFilter[^)]*\)", '', line)
                    modified_line = re.sub(r"\([^)]*DateRangeFilter[^)]*\),?\s*", '', modified_line)
                    new_lines.append(modified_line)
            else:
                new_lines.append(line)
        
        final_content = '\n'.join(new_lines)
        
        if final_content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(final_content)
            print(f"✅ 修复了 {file_path}")
            return True
        else:
            print(f"⏭️  跳过 {file_path} (无需修复)")
            return False
            
    except Exception as e:
        print(f"❌ 修复 {file_path} 时出错: {e}")
        return False

def main():
    """主函数"""
    print("🔧 开始修复DateRangeFilter使用...")
    
    # 查找所有admin.py文件
    admin_files = []
    for root, dirs, files in os.walk('.'):
        # 跳过虚拟环境
        if 'venv' in root:
            continue
            
        for file in files:
            if file == 'admin.py':
                file_path = os.path.join(root, file)
                admin_files.append(file_path)
    
    print(f"📁 找到 {len(admin_files)} 个admin.py文件")
    
    fixed_count = 0
    for file_path in admin_files:
        if fix_daterange_filter(file_path):
            fixed_count += 1
    
    print(f"\n🎉 修复完成！共修复了 {fixed_count} 个文件")

if __name__ == '__main__':
    main()
