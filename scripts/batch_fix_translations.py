#!/usr/bin/env python
"""
批量修复Django模型翻译问题
"""

import os
import sys
import re
import django
from pathlib import Path

# 设置Django环境
sys.path.append('/www/wwwroot/csgoskins.com.cn/server')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')
django.setup()

# 翻译映射表
TRANSLATION_MAP = {
    # 通用字段
    'user': '用户',
    'amount': '金额',
    'price': '价格',
    'name': '名称',
    'title': '标题',
    'content': '内容',
    'description': '描述',
    'status': '状态',
    'state': '状态',
    'type': '类型',
    'category': '分类',
    'tag': '标签',
    'image': '图片',
    'url': '链接',
    'email': '邮箱',
    'phone': '电话',
    'address': '地址',
    'count': '数量',
    'level': '等级',
    'score': '分数',
    'rank': '排名',
    'order': '订单',
    'date': '日期',
    'time': '时间',
    'create_time': '创建时间',
    'update_time': '更新时间',
    'start_time': '开始时间',
    'end_time': '结束时间',
    
    # 业务相关
    'CDKey': 'CDKey',
    'CDKey info': 'CDKey信息',
    'CDKey record': 'CDKey兑换记录',
    'generate CDKey': '生成CDKey',
    'Charge Record': '充值记录',
    'PayMethod': '支付方式',
    'Trade Record': '交易记录',
    'Market Item': '市场物品',
    'User Profile': '用户资料',
    'Chat Message': '聊天消息',
    'Box Item': '箱子物品',
    'Lucky Box': '幸运箱',
    'Lottery': '抽奖',
    'Promotion': '促销活动',
    'Agent': '代理',
    'Withdraw': '提现',
    'Package': '包裹',
    'Roll Game': '滚轮游戏',
    'Crash Game': '崩盘游戏',
    'Grab Game': '抢夺游戏',
    'Blind Box': '盲盒',
    'Custom Box': '自定义箱子',
    'Envelope': '红包',
    'Trade Up': '合成',
    'Site Config': '站点配置',
    'Article': '文章',
    'Content': '内容',
    'Category': '分类',
    'Tag': '标签',
    
    # 状态相关
    'active': '激活',
    'inactive': '未激活',
    'pending': '待处理',
    'approved': '已批准',
    'rejected': '已拒绝',
    'completed': '已完成',
    'cancelled': '已取消',
    'processing': '处理中',
    'success': '成功',
    'failed': '失败',
    'enabled': '启用',
    'disabled': '禁用',
    'published': '已发布',
    'draft': '草稿',
    'archived': '已归档',
    'used': '已使用',
    'unused': '未使用',
    
    # 其他常用
    'yes': '是',
    'no': '否',
    'true': '真',
    'false': '假',
    'male': '男',
    'female': '女',
    'admin': '管理员',
    'moderator': '版主',
    'member': '会员',
    'guest': '游客',
    'vip': 'VIP',
    'premium': '高级',
    'basic': '基础',
    'standard': '标准',
    'advanced': '高级',
}

def find_translation_issues():
    """查找所有需要翻译的模型文件"""
    model_files = []
    server_path = Path('/www/wwwroot/csgoskins.com.cn/server')
    
    # 排除虚拟环境和第三方包
    exclude_dirs = ['venv', '__pycache__', '.git', 'migrations']
    
    for models_file in server_path.rglob('models.py'):
        # 跳过虚拟环境中的文件
        if any(exclude_dir in str(models_file) for exclude_dir in exclude_dirs):
            continue
            
        # 检查文件是否包含需要翻译的内容
        try:
            with open(models_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'verbose_name.*_(' in content or 'verbose_name = _(' in content:
                    model_files.append(models_file)
        except Exception as e:
            print(f"❌ 读取文件失败 {models_file}: {e}")
    
    return model_files

def analyze_file(file_path):
    """分析文件中的翻译问题"""
    issues = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        for i, line in enumerate(lines, 1):
            # 查找 verbose_name = _("...") 模式
            match = re.search(r'verbose_name\s*=\s*_\(["\']([^"\']+)["\']\)', line)
            if match:
                original_text = match.group(1)
                if original_text in TRANSLATION_MAP:
                    issues.append({
                        'line_num': i,
                        'line': line.strip(),
                        'original': original_text,
                        'translation': TRANSLATION_MAP[original_text],
                        'type': 'verbose_name'
                    })
            
            # 查找字段的 verbose_name
            field_match = re.search(r'(\w+)\s*=\s*models\.\w+Field\([^)]*verbose_name\s*=\s*_\(["\']([^"\']+)["\']\)', line)
            if field_match:
                field_name = field_match.group(1)
                original_text = field_match.group(2)
                if original_text in TRANSLATION_MAP:
                    issues.append({
                        'line_num': i,
                        'line': line.strip(),
                        'original': original_text,
                        'translation': TRANSLATION_MAP[original_text],
                        'type': 'field_verbose_name',
                        'field_name': field_name
                    })
                    
    except Exception as e:
        print(f"❌ 分析文件失败 {file_path}: {e}")
        
    return issues

def main():
    print("🔍 扫描项目中的翻译问题...")
    
    model_files = find_translation_issues()
    print(f"📁 找到 {len(model_files)} 个包含翻译的模型文件")
    
    all_issues = {}
    total_issues = 0
    
    for file_path in model_files:
        issues = analyze_file(file_path)
        if issues:
            all_issues[file_path] = issues
            total_issues += len(issues)
            print(f"📄 {file_path.relative_to(Path('/www/wwwroot/csgoskins.com.cn/server'))}: {len(issues)} 个问题")
    
    print(f"\n📊 总计发现 {total_issues} 个翻译问题")
    
    # 显示详细问题
    for file_path, issues in all_issues.items():
        print(f"\n📁 {file_path.relative_to(Path('/www/wwwroot/csgoskins.com.cn/server'))}:")
        for issue in issues:
            print(f"   第{issue['line_num']}行: '{issue['original']}' → '{issue['translation']}'")
    
    return all_issues

if __name__ == "__main__":
    main()
