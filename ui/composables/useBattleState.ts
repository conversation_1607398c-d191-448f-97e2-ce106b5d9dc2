// 🚀 Battle状态管理 Composable
// 分离复杂的状态逻辑，提高可维护性

import { ref, computed, watch } from 'vue'
import { useUserStore } from '~/stores/user'
import type { BattleDetail } from '~/types/battle'
import { BattleStatus, numberToBattleStatus } from '~/services/battle-api'

interface BattleData {
  uid: string
  short_id: string
  create_time: string
  update_time: string
  max_joiner: number
  price: number
  state: number
  type: number
  joiner_count: number
  private: boolean
  
  // 🎯 扩展轮次字段 - 后端统一轮次管理器提供
  round_count: number              // 总轮次数
  round_count_current: number      // 当前轮次 (1-based)
  round_count_completed: number    // 已完成轮次数 (0-based)
  round_count_remaining: number    // 剩余轮次数
  round_progress_percent: number   // 进度百分比 (0-100)
  is_first_round: boolean         // 是否第一轮
  is_last_round: boolean          // 是否最后一轮
  round_state: string             // 轮次状态 ('initial', 'waiting', 'running', 'completed')
  
  rounds: Array<{
    case: {
      case_key: string
      name: string
      name_en: string
      name_zh_hans: string
      cover: string
      item: string
      price: number
    }
  }>
  bets: Array<{
    uid: string
    user: {
      uid: string
      profile: {
        nickname: string
        avatar: string
      }
    }
    victory: boolean
    open_amount: number
    win_amount: number
    win_items_count: number
    open_items: any[]
    win_items: any[]
  }>
  user: {
    uid: string
    nickname: string
    avatar: string
  }
}

// 🎯 统一日志前缀，便于过滤调试信息
const LOG_PREFIX = '[🎰BATTLE-STATE]'

export const useBattleState = () => {
  // 🎯 基础状态
  const isLoading = ref(true)
  const error = ref<string | null>(null)
  const battleData = ref<BattleData | null>(null)
  
  // 🎯 对战状态
  const battleState = ref<BattleStatus>(BattleStatus.WAITING)
  const currentRound = ref(1)
  const totalRounds = ref(1)
  const calculationProgress = ref(0)
  
  // 🎯 开箱状态
  const openingCaseId = ref<string | null>(null)
  const openingPlayerName = ref('')
  const openingPlayerIndex = ref<number | null>(null)
  const openingRoundIndex = ref<number | null>(null)
  const currentPlayerId = ref('')
  
  // 🎯 结果状态
  const finalWinner = ref<any>(null)
  const showResultModal = ref(false)
  const completedAnimations = ref<Set<number>>(new Set())
  const roundCompleted = ref(false)
  const showCalculationAnimation = ref(false)
  
  // 🎯 轮次结果
  const currentRoundResults = ref<RoundResult[]>([])
  
  // 🎯 数据结构定义
  interface RoundResult {
    playerId: string
    playerIndex: number
    result?: any
    value?: number
  }
  
  interface PlayerRoundResult {
    item: any
    value: number
  }
  
  // 🎯 计算属性
  const allPlayers = computed(() => {
    if (!battleData.value?.bets) return []
    
    return battleData.value.bets.map(bet => ({
      id: bet.user.uid,
      nickname: bet.user.profile.nickname,
      avatar: bet.user.profile.avatar,
      uid: bet.user.uid,
      isHost: bet.user.uid === battleData.value?.user?.uid,
      isWinner: bet.victory || false,
      openingHistory: bet.open_items || [],
      rewardItems: bet.win_items || [],
      openAmount: bet.open_amount || 0,
      winAmount: bet.win_amount || 0,
      totalValue: (bet.win_items || []).reduce((sum: number, item: any) => sum + (item.item_price?.price || 0), 0),
      currentRoundResult: null as PlayerRoundResult | null,
      isPlaceholder: false
    }))
  })
  
  const displayCases = computed(() => {
    if (!battleData.value?.rounds) {
      return [];
    }

    // 后端在开始阶段可能把连续相同箱子合并为 count>1
    // 这里按 count 展开，确保 rounds 长度 === 总轮次，UI 不被折叠
    const expanded: any[] = []
    let seq = 0
    battleData.value.rounds.forEach((round: any) => {
      const caseObj = round?.case || round // 兼容旧格式：直接包含case字段或已展开
      if (!caseObj) return

      const repeat = Math.max(1, caseObj?.count || round?.count || 1)

      // 兼容不同字段名
      const caseKey = caseObj.case_key || caseObj.key || caseObj.id

      for (let i = 0; i < repeat; i++) {
        expanded.push({
          id: caseKey,
          key: caseKey,
          case_key: caseKey,
          name: caseObj.name,
          name_en: caseObj.name_en,
          name_zh_hans: caseObj.name_zh_hans,
          price: caseObj.price,
          cover: caseObj.cover,
          roundIndex: seq,
          instanceId: `${caseKey || 'unknown'}-round-${seq}`,
          isOpening: openingRoundIndex.value === seq
        })
        seq += 1
      }
    })
    
    console.log('[🎰BATTLE-STATE] displayCases计算结果:', {
      expandedLength: expanded.length,
      expanded: expanded.map(c => ({ id: c.id, name: c.name, roundIndex: c.roundIndex }))
    });
    
    return expanded
  })
  
  const totalValue = computed(() => {
    if (!battleData.value?.rounds?.length) return 0
    return battleData.value.rounds.reduce((sum, round) => sum + (round.case.price || 0), 0)
  })
  
  const allPlayersWithPlaceholders = computed(() => {
    const bets = battleData.value?.bets || []
    const players = bets.map((bet, idx) => {
      const user = bet.user || { profile: { nickname: '', avatar: '' }, uid: '' }
      return {
        id: user.uid || `player-${idx}`,
        uid: user.uid,
        nickname: user.profile.nickname,
        avatar: user.profile.avatar,
        isHost: battleData.value?.user?.uid === user.uid,
        isWinner: bet.victory === true,
        openingHistory: bet.open_items || [],
        rewardItems: bet.win_items || [],
        openAmount: bet.open_amount || 0,
        winAmount: bet.win_amount || 0,
        totalValue: bet.win_items?.reduce((sum, item) => sum + (item.item_price?.price || 0), 0) || 0,
        currentRoundResult: null,
        isPlaceholder: false
      }
    })
    
    // 修复：max_joiner是最大参与人数，bets已经包含了所有参与者（包括创建者）
    // 所以空位数量应该是 max_joiner - 实际参与者数量
    const maxPlayers = battleData.value?.max_joiner || 4
    const actualPlayerCount = players.length
    const emptySlots = Math.max(0, maxPlayers - actualPlayerCount)
    
    console.log('[🎰BATTLE-STATE] 玩家数量计算:', {
      maxPlayers,
      actualPlayerCount,
      emptySlots,
      betsLength: bets.length
    })
    
    // 填充空位
    for (let i = 0; i < emptySlots; i++) {
      players.push({
        id: `empty-${i}`,
        uid: '',
        nickname: '',
        avatar: '',
        isHost: false,
        isWinner: false,
        openingHistory: [],
        rewardItems: [],
        openAmount: 0,
        winAmount: 0,
        totalValue: 0,
        currentRoundResult: null,
        isPlaceholder: true
      } as any)
    }
    return players
  })
  
  const isUserJoined = computed(() => {
    const userStore = useUserStore()
    if (!userStore.user) return false
    
    // bets数组包含所有参与者（包括房主）
    return allPlayers.value.some(p => p.uid === userStore.user?.uid)
  })
  
  const isUserCreator = computed(() => {
    const userStore = useUserStore()
    if (!userStore.user || !battleData.value) return false
    
    // 使用uid字段比较，既然后端修复了顶层user字段的uid问题
    return battleData.value.user?.uid === userStore.user?.uid
  })
  
  const isAnimationInProgress = computed(() => {
    // 🎯 关键修复：对战结束后不应该显示开箱状态
    if (battleState.value === BattleStatus.COMPLETED || battleState.value === BattleStatus.CANCELLED) {
      return false;
    }
    
    return openingCaseId.value !== null || 
           openingPlayerName.value !== '' || 
           openingPlayerIndex.value !== null
  })
  
  // 🎯 新增：基于后端扩展轮次字段的计算属性
  const roundProgress = computed(() => {
    return battleData.value?.round_progress_percent || 0
  })
  
  const isFirstRound = computed(() => {
    return battleData.value?.is_first_round || false
  })
  
  const isLastRound = computed(() => {
    return battleData.value?.is_last_round || false
  })
  
  const roundState = computed(() => {
    return battleData.value?.round_state || 'initial'
  })
  
  const completedRounds = computed(() => {
    return battleData.value?.round_count_completed || 0
  })
  
  const remainingRounds = computed(() => {
    return battleData.value?.round_count_remaining || totalRounds.value
  })
  
  // 🎯 状态计算属性
  const isBattleWaiting = computed(() => battleState.value === BattleStatus.WAITING)
  const isBattleInProgress = computed(() => battleState.value === BattleStatus.IN_PROGRESS)
  const isBattleCompleted = computed(() => battleState.value === BattleStatus.COMPLETED)
  const isBattleCancelled = computed(() => battleState.value === BattleStatus.CANCELLED)
  const isBattleFinished = computed(() => 
    battleState.value === BattleStatus.COMPLETED || 
    battleState.value === BattleStatus.CANCELLED
  )
  
  // 🎯 状态更新方法
  const updateBattleData = (data: any) => {
    if (!data) return



    // 创建新对象以确保变更引用，触发 Vue 响应式更新
    const previous = battleData.value || {}
    const merged: any = { ...previous, ...data }

    // ⚠️ 若 bets 或 rounds 为数组，始终创建新引用
    if (Array.isArray(data?.bets)) {
      // 运行时快速校验 bets 结构，兼容 participants/users -> bets 简化对象
      merged.bets = data.bets.map((b: any) => {
        // 若缺少 user 字段而存在 uid/profile，自动封装
        if (!b.user && (b.uid || b.profile)) {
          return { user: { uid: b.uid, profile: b.profile }, ...b }
        }
        return b
      })
    }

    // 仅当服务端明确返回非空 rounds 时才覆盖，避免 start 阶段 rounds 被折叠为空或简写导致 UI 箱子数量异常
    if (Array.isArray(data?.rounds) && data.rounds.length > 0) {
      const prevLen = Array.isArray((previous as any).rounds) ? (previous as any).rounds.length : 0

      // 👉 仅当round对象包含有效case_key/key/id时才认为数据完整
      const hasValidCaseKey = (round: any): boolean => {
        const c = round?.case || round
        return !!(c?.case_key || c?.key || c?.id)
      }

      const allValid = data.rounds.every(hasValidCaseKey)

      if (!allValid) {
        // 若仍有缺失字段则忽略，避免破坏前端数据
        console.warn('[BATTLE-STATE] 收到不完整的 rounds 数据，已忽略此次更新')
      } else {
        // 后端已保证字段完整，直接替换即可
        merged.rounds = [...data.rounds]
      }
    }

    // 替换引用
    battleData.value = merged

    console.log(`${LOG_PREFIX} 合并完成，当前state=${merged.state}, bets.length=${merged.bets?.length || 0}`)

    // 🎯 更新轮次信息 - 后端统一轮次管理器确保数据一致性
    if (data.round_count_current !== undefined) {
      // 后端已通过数据验证器确保数据合理性，前端可信任使用
      currentRound.value = Number(data.round_count_current) || 1;
    }
    if (data.round_count !== undefined) {
      totalRounds.value = Number(data.round_count) || 1;
    }
    
    // 🎯 记录扩展轮次信息用于调试
    if (process.dev && data.round_count_current !== undefined) {
      console.log(`${LOG_PREFIX} 轮次数据同步:`, {
        当前轮次: data.round_count_current,
        总轮次: data.round_count,
        已完成: data.round_count_completed,
        剩余轮次: data.round_count_remaining,
        进度百分比: data.round_progress_percent,
        是否第一轮: data.is_first_round,
        是否最后一轮: data.is_last_round,
        轮次状态: data.round_state
      });
    }
  }
  
  const updateBattleState = (newState: BattleStatus) => {
    console.log('[🎯BATTLE] 更新对战状态:', newState)
    battleState.value = newState
  }
  
  const resetState = () => {
    console.log('[🎯BATTLE] 重置对战状态')
    battleState.value = BattleStatus.WAITING
    isLoading.value = true
    error.value = null
  }
  
  const updatePlayerRoundResult = (playerUid: string, result: any) => {
    const player = allPlayers.value.find(p => p.uid === playerUid)
    if (player && result) {
      player.currentRoundResult = {
        item: result,
        value: result.item_price?.price || result.price || 0
      }
      
      // 添加到开箱历史
      if (!player.openingHistory) {
        player.openingHistory = []
      }
      
      const openingRecord = {
        ...result,
        round: currentRound.value,
        timestamp: Date.now(),
        caseId: displayCases.value[currentRound.value - 1]?.id || 'unknown'
      }
      
      player.openingHistory.push(openingRecord)
      player.totalValue += (result.item_price?.price || result.price || 0)
      
      console.log('[🎰BATTLE-STATE] 更新玩家轮次结果:', {
        player: player.nickname,
        result: result.name,
        value: result.item_price?.price || result.price || 0
      })
    }
  }
  
  const resetOpeningState = () => {
    openingCaseId.value = null
    openingPlayerName.value = ''
    openingPlayerIndex.value = null
    openingRoundIndex.value = null
    currentPlayerId.value = ''
  }
  
  const setOpeningState = (caseId: string, playerName: string, playerIndex: number, roundIndex: number) => {
    console.log('[🎰BATTLE-STATE] setOpeningState被调用:', {
      caseId,
      playerName,
      playerIndex,
      roundIndex,
      oldOpeningCaseId: openingCaseId.value
    });
    openingCaseId.value = caseId
    openingPlayerName.value = playerName
    openingPlayerIndex.value = playerIndex
    openingRoundIndex.value = roundIndex
    console.log('[🎰BATTLE-STATE] setOpeningState完成，新的openingCaseId:', openingCaseId.value);
  }
  
  const nextRound = () => {
    if (currentRound.value < totalRounds.value) {
      currentRound.value++
      // 🎯 修复：不要在轮次推进时清除开箱状态，让它在下一个opening_start事件时自动更新
      // resetOpeningState()
      roundCompleted.value = false
      console.log('[🎰BATTLE-STATE] 进入下一轮:', currentRound.value)
    } else {
      battleState.value = BattleStatus.COMPLETED
      console.log('[🎰BATTLE-STATE] 对战结束')
    }
  }
  
  const showFinalCalculation = () => {
    console.log('[🎰BATTLE-STATE] 显示最终统计动画')
    showCalculationAnimation.value = true
  }
  
  const closeCalculationAnimation = () => {
    showCalculationAnimation.value = false
  }
  
  const processFinishedBattle = () => {
    console.log('[🎰BATTLE-STATE] 处理对战结束')
    
    // 🎯 关键修复：不要立即清除开箱状态，让计算动画先完成
    // resetOpeningState()
    
    // 确定获胜者
    const winner = allPlayers.value.find(p => p.isWinner)
    if (winner) {
      finalWinner.value = winner
      // 🎯 延迟显示结果模态框，给计算动画时间
      setTimeout(() => {
        showResultModal.value = true
      }, 3000)
    }
    
    battleState.value = BattleStatus.COMPLETED
  }
  
  const closeResultModal = () => {
    showResultModal.value = false
  }
  
  // 🎯 处理对战状态变化
  const handleBattleStateChange = (newState: BattleStatus) => {
    console.log('[🎯BATTLE] 处理对战状态变化:', newState)
    
    if (newState === BattleStatus.COMPLETED) {
      // 对战完成，停止动画
      console.log('[🎯BATTLE] 对战完成，停止动画')
    } else if (newState === BattleStatus.IN_PROGRESS) {
      // 对战开始，启动动画
      console.log('[🎯BATTLE] 对战开始，启动动画')
    } else if (newState === BattleStatus.WAITING) {
      // 对战等待中
      console.log('[🎯BATTLE] 对战等待中')
    }
    
    updateBattleState(newState)
  }
  
  // 🎯 模拟状态变化（用于测试）
  const simulateStateChange = (targetState: BattleStatus) => {
    console.log('[🎯BATTLE] 模拟状态变化到:', targetState)
    handleBattleStateChange(targetState)
  }
  
  // 🎯 监听battleData变化，自动同步状态
  watch(
    () => battleData.value?.state,
    async (apiState, oldApiState) => {
      console.log('[🎰BATTLE-STATE] API状态变化:', { oldApiState, apiState });
      
      // 🎯 页面加载时的处理：如果状态已经是结束状态，静默设置状态，不触发变化
      if (typeof apiState === 'number' && oldApiState === undefined && apiState === 11) {
        console.log('[🎰BATTLE-STATE] 页面加载时检测到已结束的对战，静默设置状态');
        // 直接设置状态值，不通过updateBattleState，避免触发状态变化事件
        battleState.value = BattleStatus.COMPLETED;
        return;
      }
      
      if (typeof apiState === 'number') {
        await updateBattleState(numberToBattleStatus(apiState))
      }
    },
    { immediate: false } // 🎯 移除immediate，避免页面加载时立即触发
  )
  
  watch(
    () => battleData.value,
    async (newBattleData) => {
      if (newBattleData) {
        // 🎯 后端统一轮次管理器确保数据一致性，直接使用
        currentRound.value = Number(newBattleData.round_count_current) || 1;
        totalRounds.value = Number(newBattleData.round_count) || 1;
      }
    },
    { immediate: true }
  )

  // 🎯 开发模式下：监控 bets 长度变化，方便排查未触发更新的问题
  if (import.meta.dev) {
    watch(
      () => battleData.value?.bets?.length,
      (newLen, oldLen) => {
        console.log(`${LOG_PREFIX} 😀 bets.length 变化: ${oldLen} -> ${newLen}`)
      }
    )
  }
  
  return {
    // 状态
    isLoading,
    error,
    battleData,
    battleState,
    currentRound,
    totalRounds,
    calculationProgress,
    openingCaseId,
    openingPlayerName,
    openingPlayerIndex,
    openingRoundIndex,
    currentPlayerId,
    finalWinner,
    showResultModal,
    completedAnimations,
    roundCompleted,
    showCalculationAnimation,
    currentRoundResults,
    
    // 计算属性
    allPlayers,
    displayCases,
    totalValue,
    allPlayersWithPlaceholders,
    isUserJoined,
    isUserCreator,
    isAnimationInProgress,
    isBattleWaiting,
    isBattleInProgress,
    isBattleCompleted,
    isBattleCancelled,
    isBattleFinished,
    
    // 🎯 新增：扩展轮次字段相关计算属性
    roundProgress,
    isFirstRound,
    isLastRound,
    roundState,
    completedRounds,
    remainingRounds,
    
    // 方法
    updateBattleData,
    updateBattleState,
    updatePlayerRoundResult,
    resetOpeningState,
    setOpeningState,
    nextRound,
    showFinalCalculation,
    closeCalculationAnimation,
    processFinishedBattle,
    closeResultModal,
    handleBattleStateChange,
    simulateStateChange
  }
} 