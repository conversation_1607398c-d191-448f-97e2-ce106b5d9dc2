<template>
  <div class="min-h-screen relative">
    <!-- 整站统一的背景效果 -->
    <div class="absolute inset-0 overflow-hidden">
      <!-- 动态渐变背景 -->
      <div
        class="absolute inset-0 bg-gradient-to-b from-background-darker via-background to-background-dark z-0"
      />
      <!-- 粒子效果层 -->
      <div class="particle-container absolute inset-0 z-0" />
      <!-- 动态光线效果 -->
      <div
        class="absolute top-0 left-0 w-full h-full overflow-hidden opacity-15 z-0"
      >
        <div class="light-beam light-beam-1" />
        <div class="light-beam light-beam-3" />
        <div class="light-beam light-beam-2" />
      </div>
      <!-- 网格线效果 -->
      <div class="absolute inset-0 bg-grid-pattern opacity-[0.04] z-0" />
    </div>

    <!-- 主要内容区域 -->
    <div class="container mx-auto px-4 py-6 relative z-10">
      <!-- 英雄区域 -->
      <BattleHeroSection @create-battle="openCreateDialog" />

      <!-- Tab导航和内容区域 -->
      <div class="tabs-section">
        <!-- Tab导航栏 -->
        <BattleTabNavigation
          v-model:activeTab="activeTab"
          :activeCount="activeRooms.length"
        />

        <!-- Tab内容区域 -->
        <div class="tab-content">
          <!-- 活跃对战 Tab -->
          <div v-show="activeTab === 'active'" class="active-battles-tab">
            <!-- 骨架屏 -->
            <div
              v-if="shouldShowActiveSkeleton"
              class="grid grid-cols-1 lg:grid-cols-2 gap-6"
            >
              <BattleRoomSkeleton v-for="n in 6" :key="n" type="active" />
            </div>

            <!-- 活跃对战列表 -->
            <TransitionGroup
              v-else
              name="battle-list"
              tag="div"
              class="grid grid-cols-1 lg:grid-cols-2 gap-6"
              appear
            >
              <BattleRoomCard
                v-for="room in activeRooms"
                :key="room.uid"
                :room="room"
                type="active"
                :class="getRoomAnimationClass(room)"
                @view-room="viewRoom"
                @join-room="joinRoom"
                @spectate-room="spectateRoom"
              />
            </TransitionGroup>

            <!-- 空状态 -->
            <div
              v-if="!shouldShowActiveSkeleton && activeRooms.length === 0"
              class="empty-active text-center py-16"
            >
              <div
                class="relative bg-gradient-to-br from-blue-800/20 to-blue-700/20 backdrop-blur-xl border border-blue-600/20 rounded-2xl p-12 max-w-md mx-auto"
              >
                <div class="relative z-10">
                  <i class="fas fa-play-circle text-6xl text-blue-500 mb-6"></i>
                  <h3 class="text-xl font-semibold text-white/70 mb-3">
                    {{ t("battle.no_active") }}
                  </h3>
                  <p class="text-white/50 mb-6">
                    {{ t("battle.no_active_desc") }}
                  </p>
                  <button
                    @click="openCreateDialog"
                    class="px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 rounded-lg font-medium text-white transition-all duration-300"
                  >
                    {{ t("battle.create_first_battle") }}
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 我的对战 Tab -->
          <div v-show="activeTab === 'my'" class="my-battles-tab">
            <!-- 未登录状态 -->
            <div
              v-if="!userStore.isLoggedIn"
              class="not-logged-in text-center py-16"
            >
              <div
                class="relative bg-gradient-to-br from-blue-800/20 to-purple-800/20 backdrop-blur-xl border border-blue-600/20 rounded-2xl p-12 max-w-md mx-auto"
              >
                <div class="relative z-10">
                  <i class="fas fa-sign-in-alt text-6xl text-blue-400 mb-6"></i>
                  <h3 class="text-xl font-semibold text-white/70 mb-3">
                    {{ t("battle.login_required_title") }}
                  </h3>
                  <p class="text-white/50 mb-6">
                    {{ t("battle.modal.login_required_desc") }}
                  </p>
                  <div class="flex flex-col sm:flex-row gap-3 justify-center">
                    <button
                      @click="navigateTo('/auth/login')"
                      class="px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 rounded-lg font-medium text-white transition-all duration-300"
                    >
                      <i class="fas fa-sign-in-alt mr-2"></i>
                      {{ t("auth.login.title") }}
                    </button>
                    <button
                      @click="navigateTo('/auth/register')"
                      class="px-6 py-3 bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 rounded-lg font-medium text-white transition-all duration-300"
                    >
                      <i class="fas fa-user-plus mr-2"></i>
                      {{ t("auth.register.title") }}
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- 已登录状态 -->
            <template v-else>
              <!-- 工具栏 -->
              <div class="my-battles-toolbar mb-4">
                <div
                  class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-3"
                >
                  <div class="flex-1">
                    <div
                      class="flex flex-col sm:flex-row sm:items-end sm:gap-4"
                    >
                      <h3 class="text-lg font-semibold text-white">
                        {{ t("battle.my_battles_desc") }}
                      </h3>
                      <p class="text-white/60 text-sm">
                        {{
                          t("battle.total_my_battles_count", {
                            count: myBattlePagination.total,
                          })
                        }}
                        <span
                          v-if="myBattleFilter !== 'all'"
                          class="ml-2 text-green-400"
                        >
                          ({{ t("battle.filtered_results") }}:
                          {{ filteredMyRooms.length }})
                        </span>
                      </p>
                    </div>
                  </div>
                  <div class="flex items-center gap-3 flex-shrink-0">
                    <!-- 筛选器 -->
                    <div class="relative">
                      <select
                        v-model="myBattleFilter"
                        @change="applyMyBattleFilter"
                        class="px-3 py-2 bg-gray-800/60 border border-gray-600/50 rounded-lg text-white text-sm focus:outline-none focus:border-green-500/50 focus:ring-2 focus:ring-green-500/20 transition-all duration-300 appearance-none cursor-pointer min-w-[120px]"
                      >
                        <option value="all">
                          {{ t("battle.all_battles") }}
                        </option>
                        <option value="ongoing">
                          {{ t("battle.ongoing_battles") }}
                        </option>
                        <option value="created">
                          {{ t("battle.created_by_me") }}
                        </option>
                        <option value="participated">
                          {{ t("battle.participated_by_me") }}
                        </option>
                      </select>
                      <i
                        class="fas fa-chevron-down absolute right-2 top-1/2 transform -translate-y-1/2 text-white/40 pointer-events-none text-xs"
                      ></i>
                    </div>

                    <!-- 刷新按钮 -->
                    <button
                      @click="fetchAllMyRooms(1)"
                      :disabled="myBattleLoading"
                      class="px-3 py-2 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 rounded-lg font-medium text-white transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 text-sm"
                    >
                      <i
                        class="fas fa-refresh text-sm"
                        :class="{ 'animate-spin': myBattleLoading }"
                      ></i>
                      <span class="hidden sm:inline">{{
                        t("battle.refresh")
                      }}</span>
                    </button>
                  </div>
                </div>
              </div>

              <!-- 骨架屏 -->
              <div
                v-if="shouldShowMyBattleSkeleton"
                class="grid grid-cols-1 lg:grid-cols-2 gap-6"
              >
                <BattleRoomSkeleton v-for="n in 6" :key="n" type="my" />
              </div>

              <!-- 我的对战列表 -->
              <TransitionGroup
                v-if="!shouldShowMyBattleSkeleton && filteredMyRooms.length > 0"
                name="battle-list"
                tag="div"
                class="grid grid-cols-1 lg:grid-cols-2 gap-6"
                appear
              >
                <BattleRoomCard
                  v-for="room in filteredMyRooms"
                  :key="room.uid"
                  :room="room"
                  type="my"
                  :showRole="true"
                  @view-room="viewRoom"
                  @join-room="joinRoom"
                  @spectate-room="spectateRoom"
                />
              </TransitionGroup>

              <!-- 空状态 -->
              <div
                v-if="
                  !shouldShowMyBattleSkeleton && filteredMyRooms.length === 0
                "
                class="empty-my text-center py-16"
              >
                <div
                  class="relative bg-gradient-to-br from-green-800/20 to-green-700/20 backdrop-blur-xl border border-green-600/20 rounded-2xl p-12 max-w-md mx-auto"
                >
                  <div class="relative z-10">
                    <i class="fas fa-user text-6xl text-green-500 mb-6"></i>
                    <h3 class="text-xl font-semibold text-white/70 mb-3">
                      {{ t("battle.no_my_battles") }}
                    </h3>
                    <p class="text-white/50 mb-6">
                      {{ t("battle.no_my_battles_desc") }}
                    </p>
                    <button
                      @click="openCreateDialog"
                      class="px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 rounded-lg font-medium text-white transition-all duration-300"
                    >
                      {{ t("battle.create_first_battle") }}
                    </button>
                  </div>
                </div>
              </div>

              <!-- 分页控制 -->
              <div
                v-if="filteredMyRooms.length > 0"
                class="pagination-section flex items-center justify-center gap-4 mt-8"
              >
                <button
                  @click="fetchAllMyRooms(myBattlePagination.page - 1)"
                  :disabled="myBattlePagination.page <= 1 || myBattleLoading"
                  class="px-4 py-2 bg-gray-700/50 hover:bg-gray-600/50 border border-gray-600/50 hover:border-gray-500/50 rounded-lg text-white/70 hover:text-white transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <i class="fas fa-chevron-left text-sm mr-2"></i>
                  {{ t("battle.prev_page") }}
                </button>

                <button
                  @click="fetchAllMyRooms(myBattlePagination.page + 1)"
                  :disabled="
                    myBattlePagination.page >=
                      Math.ceil(
                        myBattlePagination.total / myBattlePagination.pageSize
                      ) || myBattleLoading
                  "
                  class="px-4 py-2 bg-gray-700/50 hover:bg-gray-600/50 border border-gray-600/50 hover:border-gray-500/50 rounded-lg text-white/70 hover:text-white transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {{ t("battle.next_page") }}
                  <i class="fas fa-chevron-right text-sm ml-2"></i>
                </button>
              </div>
            </template>
          </div>

          <!-- 历史对战 Tab -->
          <div v-show="activeTab === 'history'" class="history-battles-tab">
            <!-- 工具栏 -->
            <div class="history-battles-toolbar mb-4">
              <div
                class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-3"
              >
                <div class="flex-1">
                  <div class="flex flex-col sm:flex-row sm:items-end sm:gap-4">
                    <h3 class="text-lg font-semibold text-white">
                      {{ t("battle.history_battles_desc") }}
                    </h3>
                    <p class="text-white/60 text-sm">
                      {{
                        t("battle.total_history_count", {
                          count: historyPagination.total,
                        })
                      }}
                    </p>
                  </div>
                </div>
                <div class="flex items-center gap-3 flex-shrink-0">
                  <button
                    @click="fetchHistoryRooms(1)"
                    :disabled="historyLoading"
                    class="px-3 py-2 bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 rounded-lg font-medium text-white transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 text-sm"
                  >
                    <i
                      class="fas fa-refresh text-sm"
                      :class="{ 'animate-spin': historyLoading }"
                    ></i>
                    <span class="hidden sm:inline">{{
                      t("battle.refresh")
                    }}</span>
                  </button>
                </div>
              </div>
            </div>

            <!-- 骨架屏 -->
            <div
              v-if="shouldShowHistorySkeleton"
              class="grid grid-cols-1 lg:grid-cols-2 gap-6"
            >
              <BattleRoomSkeleton v-for="n in 6" :key="n" type="history" />
            </div>

            <!-- 历史对战列表 -->
            <TransitionGroup
              v-if="!shouldShowHistorySkeleton && historyRooms.length > 0"
              name="battle-list"
              tag="div"
              class="grid grid-cols-1 lg:grid-cols-2 gap-6"
              appear
            >
              <BattleRoomCard
                v-for="room in historyRooms"
                :key="room.uid"
                :room="room"
                type="history"
                @view-room="viewRoom"
              />
            </TransitionGroup>

            <!-- 空状态 -->
            <div
              v-if="!shouldShowHistorySkeleton && historyRooms.length === 0"
              class="empty-history text-center py-16"
            >
              <div
                class="relative bg-gradient-to-br from-gray-800/20 to-gray-700/20 backdrop-blur-xl border border-gray-600/20 rounded-2xl p-12 max-w-md mx-auto"
              >
                <div class="relative z-10">
                  <i class="fas fa-history text-6xl text-gray-500 mb-6"></i>
                  <h3 class="text-xl font-semibold text-white/70 mb-3">
                    {{ t("battle.no_history") }}
                  </h3>
                  <p class="text-white/50">{{ t("battle.no_history_desc") }}</p>
                </div>
              </div>
            </div>

            <!-- 分页控制 -->
            <div
              v-if="historyRooms.length > 0"
              class="pagination-section flex items-center justify-center gap-4 mt-8"
            >
              <button
                @click="fetchHistoryRooms(historyPagination.page - 1)"
                :disabled="historyPagination.page <= 1 || historyLoading"
                class="px-4 py-2 bg-gray-700/50 hover:bg-gray-600/50 border border-gray-600/50 hover:border-gray-500/50 rounded-lg text-white/70 hover:text-white transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <i class="fas fa-chevron-left text-sm mr-2"></i>
                {{ t("battle.prev_page") }}
              </button>

              <button
                @click="fetchHistoryRooms(historyPagination.page + 1)"
                :disabled="
                  historyPagination.page >=
                    Math.ceil(
                      historyPagination.total / historyPagination.pageSize
                    ) || historyLoading
                "
                class="px-4 py-2 bg-gray-700/50 hover:bg-gray-600/50 border border-gray-600/50 hover:border-gray-500/50 rounded-lg text-white/70 hover:text-white transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {{ t("battle.next_page") }}
                <i class="fas fa-chevron-right text-sm ml-2"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建对战模态框 -->
    <BattleCreateRoomDialog
      v-model="showCreateDialog"
      @created="handleBattleCreated"
    />

    <!-- 加入对战反馈模态框 -->
    <BattleJoinModal
      :visible="joinModal.visible"
      :type="joinModal.type"
      :title="joinModal.title"
      :description="joinModal.description"
      :details="joinModal.details"
      :show-retry="joinModal.showRetry"
      :show-go-to-login="joinModal.showGoToLogin"
      :loading-text="joinModal.loadingText"
      :auto-close="joinModal.autoClose"
      @close="closeJoinModal"
      @view-details="viewDetails"
      @retry="retryJoinRoom"
      @go-to-login="goToLogin"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from "vue";
import { useBattleStore } from "~/stores/battle";
import { GameState, battleApi } from "~/services/battle-api";
import type { BattleRoom } from "~/services/battle-api";
import BattleJoinModal from "~/components/ui/BattleJoinModal.vue";

// 国际化和响应式设计
const { t } = useI18n();

// 页面配置和SEO
definePageMeta({
  title: "Battle Arena",
  key: "battle",
});

useSeoMeta({
  title: () => `${t("battle.title")} | CSGO开箱网站`,
  description: () => t("battle.subtitle"),
  ogTitle: () => `${t("battle.title")} | CSGO开箱网站`,
  ogDescription: () => t("battle.subtitle"),
  twitterCard: "summary_large_image",
});

// 响应式数据
const battleStore = useBattleStore();
const userStore = useUserStore();
const notification = useNotification();
const showCreateDialog = ref(false);
const activeTab = ref("active");

// 历史对战相关状态
const historyRooms = ref<BattleRoom[]>([]);
const historyLoading = ref(false);
const historyPagination = ref({
  page: 1,
  pageSize: 10,
  total: 0,
});

// 我的对战相关状态
const allMyRooms = ref<BattleRoom[]>([]);
const myBattleLoading = ref(false);
const myBattleFilter = ref<"all" | "ongoing" | "created" | "participated">(
  "all"
);
const myBattlePagination = ref({
  page: 1,
  pageSize: 10,
  total: 0,
});

// 加入对战模态框状态
const joinModal = ref({
  visible: false,
  type: "info" as "success" | "error" | "warning" | "info" | "loading",
  title: "",
  description: "",
  details: null as any,
  showRetry: false,
  showGoToLogin: false,
  loadingText: "",
  autoClose: false,
});

// 当前加入的房间（用于重试）
const currentJoinRoom = ref<BattleRoom | null>(null);

// 计算属性
const activeRooms = computed(() => {
  // 显示活跃的房间：初始化、可加入、加入中、满员、进行中，只排除已结束和已取消的房间
  const filtered = battleStore.rooms.filter(
    (room) =>
      room.state === 1 || // Initial (初始化) - 新创建的房间
      room.state === 2 || // Joinable (可加入)
      room.state === 3 || // Joining (加入中) - 有人正在加入
      room.state === 4 || // Full (满员)
      room.state === 5 // Running (进行中)
    // 排除: 11 (End/已结束) 和 20 (Cancelled/已取消)
  );

  return filtered.sort((a, b) => {
    // 1. 新房间优先级最高（带动画效果）
    const aIsNew = (a as any)._isNewRoom;
    const bIsNew = (b as any)._isNewRoom;
    if (aIsNew && !bIsNew) return -1;
    if (!aIsNew && bIsNew) return 1;

    // 2. 可加入房间优先级最高
    const aCanJoin = isRoomJoinable(a);
    const bCanJoin = isRoomJoinable(b);
    if (aCanJoin && !bCanJoin) return -1;
    if (!aCanJoin && bCanJoin) return 1;

    // 3. 满员房间排到中间，进行中房间排到最后
    const aIsFull = isRoomFull(a) && a.state !== 5; // Running
    const bIsFull = isRoomFull(b) && b.state !== 5; // Running
    const aIsRunning = a.state === 5; // Running
    const bIsRunning = b.state === 5; // Running

    // 排序优先级：可加入 > 满员 > 进行中
    if (aIsRunning && !bIsRunning) return 1;
    if (!aIsRunning && bIsRunning) return -1;
    if (aIsFull && !bIsFull && !bIsRunning) return 1;
    if (!aIsFull && bIsFull && !aIsRunning) return -1;

    // 4. 按创建时间排序（新的在前）
    const aTime = new Date(a.create_time).getTime();
    const bTime = new Date(b.create_time).getTime();
    return bTime - aTime;
  });
});

const filteredMyRooms = computed(() => {
  if (myBattleFilter.value === "all") {
    return allMyRooms.value;
  }

  return allMyRooms.value.filter((room) => {
    switch (myBattleFilter.value) {
      case "ongoing":
        return room.state !== 11; // End
      case "created":
        return room.user?.uid === userStore.user?.uid;
      case "participated":
        return room.user?.uid !== userStore.user?.uid;
      default:
        return true;
    }
  });
});

const shouldShowActiveSkeleton = computed(() => {
  return (
    battleStore.loading.rooms ||
    battleStore.errors.rooms ||
    activeRooms.value.length === 0
  );
});

const shouldShowMyBattleSkeleton = computed(() => {
  return myBattleLoading.value;
});

const shouldShowHistorySkeleton = computed(() => {
  return historyLoading.value;
});

// 方法
const openCreateDialog = async () => {
  showCreateDialog.value = true;
};

const handleBattleCreated = (newRoom: BattleRoom) => {
  console.log("[🎰BATTLE] 新对战创建成功:", newRoom);
  // 不需要重新获取全部数据，Socket会自动推送新房间
  // battleStore.fetchRooms(true) // 移除全量刷新
  activeTab.value = "active";
};

const viewRoom = (room: BattleRoom) => {
  navigateTo(`/battle/${room.uid}`);
};

const joinRoom = async (room: BattleRoom) => {
  currentJoinRoom.value = room;

  // 1. 检查登录状态
  if (!userStore.isLoggedIn) {
    showJoinModal(
      "warning",
      t("battle.modal.login_required_title"),
      t("battle.modal.login_required_desc"),
      {
        showGoToLogin: true,
      }
    );
    return;
  }

  // 2. 检查余额是否充足
  const userBalance = parseFloat(userStore.formattedBalance);
  const roomPrice = room.price || 0;

  if (userBalance < roomPrice) {
    showJoinModal(
      "error",
      t("battle.modal.insufficient_balance_title"),
      t("battle.modal.insufficient_balance_desc", {
        required: roomPrice.toFixed(2),
        current: userBalance.toFixed(2),
      }),
      {
        showRetry: false,
        details: {
          price: roomPrice,
          balance: userBalance,
        },
      }
    );
    return;
  }

  try {
    // 显示加入中状态
    showJoinModal(
      "loading",
      t("battle.modal.joining_title"),
      t("battle.modal.joining_desc"),
      {
        loadingText: t("battle.modal.joining_loading"),
      }
    );

    // 调用API加入房间
    const response = await battleApi.joinRoom(room.uid, 1);

    if (response.success) {
      // 显示成功提示 - 包含详细信息
      showJoinModal(
        "success",
        t("battle.modal.join_success_title"),
        t("battle.modal.join_success_desc"),
        {
          autoClose: true,
          details: {
            roomId: room.uid,
            price: roomPrice,
            balance: userBalance,
            message: t("battle.modal.join_success_message"),
          },
        }
      );

      // 不跳转到详情页，留在当前页面
      // 房间状态会通过Socket自动更新
    } else {
      // 显示错误提示 - 只显示错误信息
      showJoinModal(
        "error",
        t("battle.modal.join_failed_title"),
        response.message || t("battle.modal.join_failed_default"),
        {
          showRetry: true,
        }
      );
    }
  } catch (error: any) {
    // 显示网络错误 - 只显示错误信息
    showJoinModal(
      "error",
      t("battle.modal.network_error_title"),
      error.message || t("battle.modal.network_error_default"),
      {
        showRetry: true,
      }
    );
  }
};

const spectateRoom = (room: BattleRoom) => {
  navigateTo(`/battle/${room.uid}?spectate=true`);
};

// 模态框相关方法
const closeJoinModal = () => {
  joinModal.value.visible = false;
  currentJoinRoom.value = null;
};

// 查看详情
const viewDetails = () => {
  navigateTo(`/battle/${currentJoinRoom.value?.uid}`);
};

const retryJoinRoom = () => {
  if (currentJoinRoom.value) {
    closeJoinModal();
    joinRoom(currentJoinRoom.value);
  }
};

const goToLogin = () => {
  closeJoinModal();
  navigateTo("/auth/login");
};

const showJoinModal = (
  type: typeof joinModal.value.type,
  title: string,
  description: string,
  options: Partial<typeof joinModal.value> = {}
) => {
  joinModal.value = {
    visible: true,
    type,
    title,
    description,
    details: options.details || null,
    showRetry: options.showRetry || false,
    showGoToLogin: options.showGoToLogin || false,
    loadingText: options.loadingText || "",
    autoClose: options.autoClose || false,
  };
};

const fetchAllMyRooms = async (page = 1) => {
  if (!userStore.isLoggedIn) return;

  myBattleLoading.value = true;

  try {
    const response = await battleApi.getMyParticipatedRooms({
      page,
      pageSize: myBattlePagination.value.pageSize,
    });

    if (response.success && response.data) {
      allMyRooms.value = response.data;
      myBattlePagination.value.page = page;
      myBattlePagination.value.total = response.total || 0;
    }
  } catch (error) {
    console.error("[🎰BATTLE] 获取我的对战失败:", error);
  } finally {
    myBattleLoading.value = false;
  }
};

const fetchHistoryRooms = async (page = 1) => {
  historyLoading.value = true;

  try {
    const response = await battleApi.getRoomList({
      state_list: ["11"],
      page,
      page_size: historyPagination.value.pageSize,
    });

    if (response.success && response.data) {
      historyRooms.value = response.data;
      historyPagination.value.page = page;
      historyPagination.value.total = response.total || 0;
    }
  } catch (error) {
    console.error("[🎰BATTLE] 获取历史对战失败:", error);
  } finally {
    historyLoading.value = false;
  }
};

const applyMyBattleFilter = () => {
  // 筛选逻辑已在计算属性中处理
};

// 判断房间是否满员的辅助函数
const isRoomFull = (room: BattleRoom) => {
  return room.joiner_count >= room.max_joiner || room.state === 4; // Full
};

// 判断房间是否可加入
const isRoomJoinable = (room: BattleRoom) => {
  return (
    (room.state === 1 || room.state === 2) &&
    room.joiner_count < room.max_joiner
  ); // Initial or Joinable
};

// 获取房间动画类
const getRoomAnimationClass = (room: BattleRoom) => {
  const classes = [];

  // 新房间动画
  if ((room as any)._isNewRoom) {
    classes.push("new-room");
  }

  // 被挤压的房间动画
  if ((room as any)._isPushed) {
    classes.push("pushed-room");
  }

  // 房间移除闪烁动画
  if ((room as any)._isRemoving) {
    classes.push("room-removing");
  }

  // 房间更新动画
  if ((room as any)._isUpdated) {
    const updateType = (room as any)._updateType;
    const hasNewJoiner = (room as any)._hasNewJoiner;
    const hasUserLeft = (room as any)._hasUserLeft;

    switch (updateType) {
      case "joined":
        if (hasNewJoiner) {
          classes.push("room-user-joined"); // 新用户加入 - 增强绿色脉冲
        } else {
          classes.push("room-joined"); // 有人加入 - 普通绿色脉冲
        }
        break;
      case "left":
        classes.push("room-user-left"); // 用户离开 - 红色脉冲动画
        break;
      case "full":
        classes.push("room-full"); // 房间满员 - 橙色脉冲
        break;
      case "start":
        classes.push("room-started"); // 对战开始 - 紫色脉冲
        break;
      case "state":
        classes.push("room-state-changed"); // 状态变化 - 蓝色脉冲
        break;
      default:
        classes.push("room-updated"); // 通用更新 - 白色脉冲
    }
  }

  // 满员房间样式
  if (isRoomFull(room)) {
    classes.push("full-room");
  }

  // 进行中房间特效
  if (room.state === 5) {
    // Running
    classes.push("running-room");
  }

  return classes.join(" ");
};

// 监听标签切换
watch(activeTab, async (newTab, oldTab) => {
  console.log("[🎰BATTLE] 标签切换:", oldTab, "->", newTab);

  if (newTab === "my") {
    if (userStore.isLoggedIn) {
      console.log("[🎰BATTLE] 切换到我的对战，用户已登录，重新获取数据");
      await fetchAllMyRooms(1);
    } else {
      console.log("[🎰BATTLE] 切换到我的对战，用户未登录，显示登录提示");
    }
  } else if (newTab === "history") {
    console.log("[🎰BATTLE] 切换到历史对战，重新获取数据");
    await fetchHistoryRooms(1);
  }
});

// 监听用户登录状态变化
watch(
  () => userStore.isLoggedIn,
  async (isLoggedIn, wasLoggedIn) => {
    console.log("[🎰BATTLE] 用户登录状态变化:", wasLoggedIn, "->", isLoggedIn);

    // 用户登录后，如果当前在"我的对战"tab，需要获取数据
    if (isLoggedIn && !wasLoggedIn && activeTab.value === "my") {
      console.log("[🎰BATTLE] 用户登录，当前在我的对战tab，获取数据");
      await fetchAllMyRooms(1);
    }

    // 用户登出后，清空我的对战数据
    if (!isLoggedIn && wasLoggedIn) {
      console.log("[🎰BATTLE] 用户登出，清空我的对战数据");
      allMyRooms.value = [];
      myBattlePagination.value = {
        page: 1,
        pageSize: 10,
        total: 0,
      };
    }
  }
);

// 生命周期
onMounted(async () => {
  // 设置Socket监听器
  battleStore.setupSocketListeners();

  // 获取初始数据
  await battleStore.fetchRooms();

  if (userStore.isLoggedIn) {
    await fetchAllMyRooms();
  }

  await fetchHistoryRooms();
});

onUnmounted(() => {
  // Socket监听器由battleStore内部管理，这里只做清理标记
});
</script>

<style scoped>
/* 背景效果样式 */
.particle-container {
  background: radial-gradient(
      circle at 20% 50%,
      rgba(59, 130, 246, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 20%,
      rgba(147, 51, 234, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 40% 80%,
      rgba(16, 185, 129, 0.1) 0%,
      transparent 50%
    );
}

.light-beam {
  position: absolute;
  width: 1px;
  height: 100%;
  background: linear-gradient(
    to bottom,
    transparent,
    rgba(59, 130, 246, 0.5),
    transparent
  );
  animation: moveBeam 8s linear infinite;
}

.light-beam-1 {
  left: 20%;
  animation-delay: 0s;
}

.light-beam-2 {
  left: 50%;
  animation-delay: 2s;
}

.light-beam-3 {
  left: 80%;
  animation-delay: 4s;
}

@keyframes moveBeam {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateY(100vh);
    opacity: 0;
  }
}

.bg-grid-pattern {
  background-image: linear-gradient(
      rgba(255, 255, 255, 0.1) 1px,
      transparent 1px
    ),
    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
}

/* 对战列表动画效果 */
.battle-list-enter-active {
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  z-index: 10;
}

.battle-list-leave-active {
  transition: all 0.6s cubic-bezier(0.55, 0.085, 0.68, 0.53);
  z-index: 1;
}

.battle-list-enter-from {
  opacity: 0;
  transform: translateY(-100%) scale(0.8);
  box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
}

.battle-list-enter-to {
  opacity: 1;
  transform: translateY(0) scale(1);
  box-shadow: 0 0 20px 5px rgba(59, 130, 246, 0.3);
}

.battle-list-leave-to {
  opacity: 0;
  transform: translateY(-30px) scale(0.85);
}

/* 移动动画（排序时） */
.battle-list-move {
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 新房间特殊动画效果 */
:deep(.new-room) {
  animation: newRoomInsert 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  z-index: 20;
}

/* 房间更新动画效果 */
:deep(.room-joined) {
  animation: roomJoinedPulse 1s ease-out;
}

:deep(.room-user-joined) {
  animation: roomUserJoinedPulse 1.5s ease-out;
}

:deep(.room-full) {
  animation: roomFullPulse 1s ease-out;
}

:deep(.room-state-changed) {
  animation: roomStatePulse 1s ease-out;
}

:deep(.room-updated) {
  animation: roomUpdatedPulse 1s ease-out;
}

/* 用户离开房间动画 - 红色脉冲效果 */
:deep(.room-user-left) {
  animation: roomUserLeftPulse 1.5s ease-out;
}

/* 对战开始动画 - 紫色脉冲效果 */
:deep(.room-started) {
  animation: roomStartedPulse 1.2s ease-out;
}

/* 房间移除闪烁动画 */
:deep(.room-removing) {
  animation: roomRemovingBlink 2s ease-in-out;
}

@keyframes roomJoinedPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.8);
    border-color: rgba(16, 185, 129, 0.5);
  }
  50% {
    box-shadow: 0 0 20px 5px rgba(16, 185, 129, 0.4);
    border-color: rgba(16, 185, 129, 1);
    transform: scale(1.02);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
    border-color: rgba(107, 114, 128, 0.3);
    transform: scale(1);
  }
}

@keyframes roomUserJoinedPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 1);
    border-color: rgba(16, 185, 129, 0.8);
    transform: scale(1);
  }
  15% {
    box-shadow: 0 0 30px 10px rgba(16, 185, 129, 0.6);
    border-color: rgba(16, 185, 129, 1);
    transform: scale(1.05);
  }
  30% {
    box-shadow: 0 0 40px 15px rgba(16, 185, 129, 0.4);
    border-color: rgba(16, 185, 129, 1);
    transform: scale(1.03);
  }
  50% {
    box-shadow: 0 0 35px 12px rgba(16, 185, 129, 0.5);
    border-color: rgba(16, 185, 129, 0.9);
    transform: scale(1.04);
  }
  70% {
    box-shadow: 0 0 25px 8px rgba(16, 185, 129, 0.3);
    border-color: rgba(16, 185, 129, 0.7);
    transform: scale(1.02);
  }
  85% {
    box-shadow: 0 0 15px 5px rgba(16, 185, 129, 0.2);
    border-color: rgba(16, 185, 129, 0.5);
    transform: scale(1.01);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
    border-color: rgba(107, 114, 128, 0.3);
    transform: scale(1);
  }
}

@keyframes roomFullPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(249, 115, 22, 0.8);
    border-color: rgba(249, 115, 22, 0.5);
  }
  50% {
    box-shadow: 0 0 20px 5px rgba(249, 115, 22, 0.4);
    border-color: rgba(249, 115, 22, 1);
    transform: scale(1.02);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(249, 115, 22, 0);
    border-color: rgba(107, 114, 128, 0.3);
    transform: scale(1);
  }
}

@keyframes roomStatePulse {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.8);
    border-color: rgba(59, 130, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 20px 5px rgba(59, 130, 246, 0.4);
    border-color: rgba(59, 130, 246, 1);
    transform: scale(1.02);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
    border-color: rgba(107, 114, 128, 0.3);
    transform: scale(1);
  }
}

@keyframes roomUpdatedPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.6);
    border-color: rgba(255, 255, 255, 0.3);
  }
  50% {
    box-shadow: 0 0 15px 3px rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.8);
    transform: scale(1.01);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    border-color: rgba(107, 114, 128, 0.3);
    transform: scale(1);
  }
}

/* 用户离开房间脉冲动画 - 红色警告效果 */
@keyframes roomUserLeftPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.9);
    border-color: rgba(239, 68, 68, 0.6);
    transform: scale(1);
  }
  20% {
    box-shadow: 0 0 25px 8px rgba(239, 68, 68, 0.6);
    border-color: rgba(239, 68, 68, 1);
    transform: scale(1.03);
  }
  40% {
    box-shadow: 0 0 35px 12px rgba(239, 68, 68, 0.4);
    border-color: rgba(239, 68, 68, 0.8);
    transform: scale(0.98);
  }
  60% {
    box-shadow: 0 0 30px 10px rgba(239, 68, 68, 0.5);
    border-color: rgba(239, 68, 68, 0.9);
    transform: scale(1.02);
  }
  80% {
    box-shadow: 0 0 20px 6px rgba(239, 68, 68, 0.3);
    border-color: rgba(239, 68, 68, 0.6);
    transform: scale(1.01);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
    border-color: rgba(107, 114, 128, 0.3);
    transform: scale(1);
  }
}

/* 对战开始脉冲动画 - 紫色激活效果 */
@keyframes roomStartedPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(147, 51, 234, 0.8);
    border-color: rgba(147, 51, 234, 0.5);
    transform: scale(1);
  }
  25% {
    box-shadow: 0 0 30px 10px rgba(147, 51, 234, 0.6);
    border-color: rgba(147, 51, 234, 1);
    transform: scale(1.05);
  }
  50% {
    box-shadow: 0 0 40px 15px rgba(147, 51, 234, 0.4);
    border-color: rgba(147, 51, 234, 0.9);
    transform: scale(1.02);
  }
  75% {
    box-shadow: 0 0 25px 8px rgba(147, 51, 234, 0.3);
    border-color: rgba(147, 51, 234, 0.7);
    transform: scale(1.03);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(147, 51, 234, 0);
    border-color: rgba(107, 114, 128, 0.3);
    transform: scale(1);
  }
}

@keyframes newRoomInsert {
  0% {
    transform: translateY(-120%) scale(0.7);
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.8);
    opacity: 0;
  }
  30% {
    transform: translateY(-20%) scale(0.95);
    box-shadow: 0 0 30px 10px rgba(59, 130, 246, 0.6);
    opacity: 0.8;
  }
  60% {
    transform: translateY(5%) scale(1.05);
    box-shadow: 0 0 40px 15px rgba(59, 130, 246, 0.4);
    opacity: 1;
  }
  80% {
    transform: translateY(-2%) scale(1.02);
    box-shadow: 0 0 25px 8px rgba(59, 130, 246, 0.2);
  }
  100% {
    transform: translateY(0) scale(1);
    box-shadow: 0 0 15px 3px rgba(59, 130, 246, 0.1);
    opacity: 1;
  }
}

/* 被挤压的房间动画效果 */
:deep(.pushed-room) {
  animation: pushDownEffect 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes pushDownEffect {
  0% {
    transform: translateY(0) scale(1);
  }
  20% {
    transform: translateY(8px) scale(0.98);
  }
  40% {
    transform: translateY(15px) scale(0.96);
  }
  60% {
    transform: translateY(20px) scale(0.95);
  }
  80% {
    transform: translateY(10px) scale(0.97);
  }
  100% {
    transform: translateY(0) scale(1);
  }
}

/* 满员房间样式 - 橙色背景持续显示 */
:deep(.full-room) {
  position: relative;
  order: 999; /* 确保满员房间排到后面 */
  background: linear-gradient(
    135deg,
    rgba(249, 115, 22, 0.12) 0%,
    rgba(251, 146, 60, 0.08) 50%,
    rgba(249, 115, 22, 0.05) 100%
  ) !important;
  border: 1px solid rgba(249, 115, 22, 0.3) !important;
}

:deep(.full-room:hover) {
  background: linear-gradient(
    135deg,
    rgba(249, 115, 22, 0.18) 0%,
    rgba(251, 146, 60, 0.12) 50%,
    rgba(249, 115, 22, 0.08) 100%
  ) !important;
  border-color: rgba(249, 115, 22, 0.5) !important;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(249, 115, 22, 0.15);
}

/* 进行中房间样式 - 蓝色背景持续显示 + 动画效果 */
:deep(.running-room) {
  position: relative;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.15) 0%,
    rgba(139, 92, 246, 0.12) 30%,
    rgba(16, 185, 129, 0.08) 70%,
    rgba(59, 130, 246, 0.05) 100%
  ) !important;
  border: 1px solid rgba(59, 130, 246, 0.4) !important;
  animation: runningRoomGlow 3s ease-in-out infinite;
}

:deep(.running-room:hover) {
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.22) 0%,
    rgba(139, 92, 246, 0.18) 30%,
    rgba(16, 185, 129, 0.12) 70%,
    rgba(59, 130, 246, 0.08) 100%
  ) !important;
  border-color: rgba(59, 130, 246, 0.6) !important;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
}

/* 进行中房间的流动边框效果 */
:deep(.running-room::before) {
  content: "";
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  background: linear-gradient(
    90deg,
    #3b82f6,
    #8b5cf6,
    #10b981,
    #3b82f6,
    #8b5cf6
  );
  background-size: 300% 100%;
  border-radius: inherit;
  z-index: -1;
  animation: runningBorderFlow 4s linear infinite;
  opacity: 0.6;
}

@keyframes runningRoomGlow {
  0% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.1);
  }
  50% {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.2);
  }
  100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.1);
  }
}

@keyframes runningBorderFlow {
  0% {
    background-position: 0% 0;
  }
  100% {
    background-position: 300% 0;
  }
}

/* 房间移除闪烁动画 */
@keyframes roomRemovingBlink {
  0% {
    opacity: 1;
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
    border-color: rgba(107, 114, 128, 0.3);
    transform: scale(1);
  }
  10% {
    opacity: 0.3;
    box-shadow: 0 0 20px 5px rgba(239, 68, 68, 0.6);
    border-color: rgba(239, 68, 68, 0.8);
    transform: scale(0.98);
  }
  20% {
    opacity: 1;
    box-shadow: 0 0 30px 10px rgba(239, 68, 68, 0.4);
    border-color: rgba(239, 68, 68, 1);
    transform: scale(1.02);
  }
  30% {
    opacity: 0.2;
    box-shadow: 0 0 25px 8px rgba(239, 68, 68, 0.7);
    border-color: rgba(239, 68, 68, 0.6);
    transform: scale(0.97);
  }
  40% {
    opacity: 1;
    box-shadow: 0 0 35px 12px rgba(239, 68, 68, 0.5);
    border-color: rgba(239, 68, 68, 1);
    transform: scale(1.03);
  }
  50% {
    opacity: 0.1;
    box-shadow: 0 0 20px 6px rgba(239, 68, 68, 0.8);
    border-color: rgba(239, 68, 68, 0.4);
    transform: scale(0.95);
  }
  60% {
    opacity: 1;
    box-shadow: 0 0 40px 15px rgba(239, 68, 68, 0.3);
    border-color: rgba(239, 68, 68, 1);
    transform: scale(1.04);
  }
  70% {
    opacity: 0.3;
    box-shadow: 0 0 15px 4px rgba(239, 68, 68, 0.9);
    border-color: rgba(239, 68, 68, 0.5);
    transform: scale(0.96);
  }
  80% {
    opacity: 1;
    box-shadow: 0 0 25px 8px rgba(239, 68, 68, 0.4);
    border-color: rgba(239, 68, 68, 0.8);
    transform: scale(1.02);
  }
  90% {
    opacity: 0.2;
    box-shadow: 0 0 10px 3px rgba(239, 68, 68, 0.6);
    border-color: rgba(239, 68, 68, 0.3);
    transform: scale(0.98);
  }
  100% {
    opacity: 0;
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
    border-color: rgba(239, 68, 68, 0);
    transform: scale(0.9);
  }
}
</style>
