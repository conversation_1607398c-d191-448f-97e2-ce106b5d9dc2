<!-- layouts/default.vue -->
<template>
  <div class="app-layout min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white">
    <!-- 响应式布局 -->
    <template v-if="!isMobile">
      <!-- PC端布局 -->
      <AppHeader />
      <main class="main-content">
        <Breadcrumb />
        <div class="container mx-auto px-4 py-6">
          <slot />
        </div>
      </main>
      <AppFooter />
    </template>

    <template v-else>
      <!-- 移动端布局 -->
      <MobileHeader />
      <main class="mobile-main-content">
        <div class="container mx-auto px-4 py-6">
          <slot />
        </div>
      </main>
      <MobileBottomNav />
    </template>

    <!-- 通知容器 -->
    <NotificationContainer />

    
  </div>
</template>

<script setup lang="ts">
import AppHeader from '~/components/common/AppHeader.vue'
import MobileHeader from '~/components/common/MobileHeader.vue'
import MobileBottomNav from '~/components/common/MobileBottomNav.vue'
import Breadcrumb from '~/components/common/Breadcrumb.vue'
import AppFooter from '~/components/common/AppFooter.vue'
import NotificationContainer from '~/components/ui/NotificationContainer.vue'
import { useBattleAudioGlobal } from '~/composables/useBattleAudio'

// Nuxt 3 组合式 API
const appStore = useAppStore()
const socketStore = useSocketStore()

// 响应式计算属性
const isMobile = computed(() => appStore.isMobile)

const audio = useBattleAudioGlobal()

// 全局初始化逻辑
onMounted(async () => {
  // 初始化应用状态
  appStore.init()
  
  // 初始化Socket连接状态检查
  if (!socketStore.isConnected && socketStore.statsData.user_number === 0) {
    // Socket连接逻辑将在组件内部处理
    console.log('[DefaultLayout] 等待Socket连接初始化')
  }
})
</script>

<style lang="scss" scoped>
.app-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.main-content {
  flex: 1;
  min-height: calc(100vh - 140px); // 为header和footer预留空间
  padding-top: 148px; // 为固定header预留空间（48px通知栏 + 4px分割线 + 96px主导航）
}

.mobile-main-content {
  flex: 1;
  min-height: calc(100vh - 120px); // 为mobile header和bottom nav预留空间
  padding-top: 80px; // 为移动端header预留空间
  padding-bottom: 80px; // 为底部导航留出额外空间
}

.container {
  /* 容器样式在这里定义 */
  // max-width: 1200px;
}
</style>