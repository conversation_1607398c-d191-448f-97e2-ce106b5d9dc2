<template>
  <Teleport to="body">
    <Transition name="modal-fade" appear>
      <div
        v-if="visible"
        class="fixed inset-0 z-[9999] flex items-center justify-center p-4"
        @click.self="handleClose"
      >
        <!-- 背景遮罩 -->
        <div class="absolute inset-0 bg-black/80 backdrop-blur-sm"></div>
        
        <!-- 模态框内容 -->
        <div class="relative w-full max-w-lg transform transition-all duration-300">
          <div class="relative bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur-xl border border-gray-600/30 rounded-2xl shadow-2xl overflow-hidden">
            <!-- 装饰性背景 -->
            <div class="absolute inset-0 overflow-hidden pointer-events-none">
              <div class="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-blue-500/5 to-purple-500/5"></div>
              <div class="absolute -top-20 -right-20 w-40 h-40 bg-gradient-to-br from-blue-500/10 to-transparent rounded-full blur-xl"></div>
              <div class="absolute -bottom-20 -left-20 w-40 h-40 bg-gradient-to-br from-purple-500/10 to-transparent rounded-full blur-xl"></div>
            </div>

            <!-- 关闭按钮 -->
            <button
              @click="handleClose"
              class="absolute top-4 right-4 w-8 h-8 flex items-center justify-center rounded-full bg-gray-700/50 hover:bg-gray-600/50 text-gray-400 hover:text-white transition-all duration-200 z-20"
            >
              <Icon name="material-symbols:close" class="w-5 h-5" />
            </button>

            <!-- 内容区域 -->
            <div class="relative z-10 p-8">
              <!-- 图标区域 -->
              <div class="flex justify-center mb-6">
                <div 
                  class="w-20 h-20 rounded-full flex items-center justify-center text-3xl shadow-lg"
                  :class="iconClasses"
                >
                  <Icon :name="iconName" class="w-10 h-10" />
                </div>
              </div>

              <!-- 标题 -->
              <h3 
                class="text-2xl font-bold text-center mb-4"
                :class="titleClasses"
              >
                {{ title }}
              </h3>

              <!-- 副标题 -->
              <p v-if="subtitle" class="text-gray-400 text-center mb-6 text-sm">
                {{ subtitle }}
              </p>

              <!-- 内容 -->
              <div v-if="content" class="bg-gray-800/50 rounded-lg p-4 mb-6 border border-gray-600/30">
                <div class="prose prose-invert max-w-none">
                  <div v-html="content"></div>
                </div>
              </div>

              <!-- 图片 -->
              <div v-if="image" class="flex justify-center mb-6">
                <img 
                  :src="image" 
                  :alt="imageAlt || title"
                  class="max-w-full h-auto rounded-lg shadow-lg border border-gray-600/30"
                  @error="handleImageError"
                />
              </div>

              <!-- 详细信息 -->
              <div v-if="details && Object.keys(details).length > 0" class="bg-gray-800/50 rounded-lg p-4 mb-6 border border-gray-600/30">
                <div class="space-y-3 text-sm">
                  <div v-for="(value, key) in details" :key="key" class="flex justify-between">
                    <span class="text-gray-400">{{ key }}:</span>
                    <span class="text-white font-medium">{{ value }}</span>
                  </div>
                </div>
              </div>

              <!-- 按钮区域 -->
              <div class="flex flex-col sm:flex-row gap-3 justify-center">
                <button
                  v-if="showCancel"
                  @click="handleCancel"
                  class="px-6 py-3 bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 rounded-lg font-medium text-white transition-all duration-300 flex items-center justify-center gap-2"
                >
                  <Icon name="material-symbols:close" class="w-4 h-4" />
                  {{ cancelText }}
                </button>
                
                <button
                  v-if="showConfirm"
                  @click="handleConfirm"
                  :disabled="loading"
                  class="px-6 py-3 rounded-lg font-medium text-white transition-all duration-300 flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                  :class="confirmButtonClasses"
                >
                  <Icon v-if="loading" name="material-symbols:sync" class="w-4 h-4 animate-spin" />
                  <Icon v-else :name="confirmIcon" class="w-4 h-4" />
                  {{ loading ? loadingText : confirmText }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
interface NotificationModalProps {
  visible: boolean
  type?: 'success' | 'error' | 'warning' | 'info' | 'question'
  title: string
  subtitle?: string
  content?: string
  image?: string
  imageAlt?: string
  details?: Record<string, any>
  showCancel?: boolean
  showConfirm?: boolean
  cancelText?: string
  confirmText?: string
  confirmIcon?: string
  loading?: boolean
  loadingText?: string
  autoClose?: boolean
  autoCloseDelay?: number
}

interface NotificationModalEmits {
  (e: 'close'): void
  (e: 'cancel'): void
  (e: 'confirm'): void
}

const props = withDefaults(defineProps<NotificationModalProps>(), {
  type: 'info',
  showCancel: true,
  showConfirm: true,
  cancelText: '取消',
  confirmText: '确认',
  confirmIcon: 'material-symbols:check',
  loading: false,
  loadingText: '处理中...',
  autoClose: false,
  autoCloseDelay: 3000
})

const emit = defineEmits<NotificationModalEmits>()

// 计算属性
const iconName = computed(() => {
  switch (props.type) {
    case 'success':
      return 'material-symbols:check-circle'
    case 'error':
      return 'material-symbols:error'
    case 'warning':
      return 'material-symbols:warning'
    case 'info':
      return 'material-symbols:info'
    case 'question':
      return 'material-symbols:help'
    default:
      return 'material-symbols:info'
  }
})

const iconClasses = computed(() => {
  switch (props.type) {
    case 'success':
      return 'bg-gradient-to-br from-green-500/20 to-green-600/20 text-green-400 border border-green-500/30'
    case 'error':
      return 'bg-gradient-to-br from-red-500/20 to-red-600/20 text-red-400 border border-red-500/30'
    case 'warning':
      return 'bg-gradient-to-br from-yellow-500/20 to-orange-600/20 text-yellow-400 border border-yellow-500/30'
    case 'info':
      return 'bg-gradient-to-br from-blue-500/20 to-blue-600/20 text-blue-400 border border-blue-500/30'
    case 'question':
      return 'bg-gradient-to-br from-purple-500/20 to-purple-600/20 text-purple-400 border border-purple-500/30'
    default:
      return 'bg-gradient-to-br from-gray-500/20 to-gray-600/20 text-gray-400 border border-gray-500/30'
  }
})

const titleClasses = computed(() => {
  switch (props.type) {
    case 'success':
      return 'text-green-400'
    case 'error':
      return 'text-red-400'
    case 'warning':
      return 'text-yellow-400'
    case 'info':
      return 'text-blue-400'
    case 'question':
      return 'text-purple-400'
    default:
      return 'text-white'
  }
})

const confirmButtonClasses = computed(() => {
  switch (props.type) {
    case 'success':
      return 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700'
    case 'error':
      return 'bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700'
    case 'warning':
      return 'bg-gradient-to-r from-yellow-500 to-orange-600 hover:from-yellow-600 hover:to-orange-700'
    case 'info':
      return 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700'
    case 'question':
      return 'bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700'
    default:
      return 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700'
  }
})

// 方法
const handleClose = () => {
  emit('close')
}

const handleCancel = () => {
  emit('cancel')
}

const handleConfirm = () => {
  emit('confirm')
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/images/placeholder.svg'
}

// 自动关闭
watch(() => props.visible, (newValue) => {
  if (newValue && props.autoClose) {
    setTimeout(() => {
      handleClose()
    }, props.autoCloseDelay)
  }
})
</script>

<style scoped>
.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: opacity 0.3s ease;
}

.modal-fade-enter-from,
.modal-fade-leave-to {
  opacity: 0;
}
</style> 