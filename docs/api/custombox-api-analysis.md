# CustomBox 自制盲盒模块分析文档

## 模块概述

CustomBox模块是平台的自制盲盒系统，允许用户创建自定义的开箱配置。用户可以选择物品、设置概率、定制外观，创建属于自己的专属盲盒。该模块提供了完整的用户生成内容(UGC)解决方案，增强了平台的互动性和个性化体验。

## 目录结构

```
custombox/
├── __init__.py
├── admin.py                 # Django管理后台配置
├── apps.py                  # 应用配置
├── business.py              # 核心业务逻辑
├── models.py                # 数据模型定义
├── serializers.py           # API序列化器
├── tests.py                 # 单元测试
├── urls.py                  # URL路由配置
├── views.py                 # API视图
└── migrations/              # 数据库迁移文件
```

## 数据模型分析

### 1. CustomBox - 自制盲盒主表
```python
class CustomBox(ModelBase):
    user = models.ForeignKey(USER_MODEL)                 # 创建者
    name = models.CharField(max_length=128)              # 盲盒名称
    key = models.CharField(max_length=128, unique=True)  # 唯一标识符
    price = models.FloatField(default=0)                 # 开箱价格
    cover = models.ImageField()                          # 封面图片
    item = models.ImageField()                           # 物品图片
```

**特点：**
- 用户自创建的盲盒配置
- 通过算法自动计算价格
- 支持自定义外观

### 2. CustomDropItem - 自制掉落物品
```python
class CustomDropItem(ModelBase):
    item_info = models.ForeignKey(ItemInfo)              # 物品信息
    case = models.ForeignKey(CustomBox)                  # 关联自制盲盒
    show_chance = models.FloatField(default=0)           # 显示概率
    drop_chance_a = models.FloatField(default=0)         # 掉落概率A
    drop_chance_b = models.FloatField(default=0)         # 掉落概率B
    drop_chance_c = models.FloatField(default=0)         # 掉落概率C
    drop_chance_d = models.FloatField(default=0)         # 掉落概率D
    drop_chance_e = models.FloatField(default=0)         # 掉落概率E
```

**多概率系统：**
- 支持A-E五个概率档位
- 用户设置初始概率，系统根据用户等级应用对应概率
- 灵活的差异化概率控制

### 3. 资源管理模型

#### CustomBoxCover - 封面资源
```python
class CustomBoxCover(ModelBase):
    cover = models.ImageField()                          # 封面图片
```

#### CustomBoxItem - 物品图标
```python
class CustomBoxItem(ModelBase):
    item = models.ImageField()                           # 物品图片
```

#### CustomBoxItemInfo - 自定义物品信息
```python
class CustomBoxItemInfo(ModelBase):
    item_info = models.OneToOneField(ItemInfo)           # 关联物品信息
    market_name = models.CharField(max_length=128)       # 自定义市场名称
```

### 4. CustomBoxRecord - 开箱记录
```python
class CustomBoxRecord(ModelBase):
    user = models.ForeignKey(USER_MODEL)                 # 开箱用户
    case = models.ForeignKey(CustomBox)                  # 自制盲盒
    item_info = models.ForeignKey(ItemInfo)              # 获得物品
    price = models.FloatField(default=0)                 # 物品价格
```

## API 接口分析

### 1. 创建自制盲盒
**路径：** `POST /api/custombox/create/`

**功能：** 创建或更新自制盲盒配置

**参数：**
```json
{
    "name": "我的专属盲盒",
    "skin": {
        "cover": "cover_uid_123",
        "item": "item_uid_456"
    },
    "items": [
        {
            "uid": "item_uid_789",
            "percent": 30.0
        },
        {
            "uid": "item_uid_012",
            "percent": 70.0
        }
    ]
}
```

**业务流程：**
1. 验证物品数量（最少2个）
2. 计算盲盒价格（物品价格 × 概率 × 系统费率）
3. 创建盲盒记录和掉落配置
4. 返回盲盒标识符

### 2. 开启自制盲盒
**路径：** `POST /api/custombox/open/`

**功能：** 开启指定的自制盲盒

**参数：**
```json
{
    "key": "custombox_key_123",
    "count": 1
}
```

**权限验证：** 仅创建者可以开启自己的盲盒

### 3. 自制盲盒列表
**路径：** `GET /api/custombox/list/`

**功能：** 获取用户创建的自制盲盒列表

### 4. 自制盲盒详情
**路径：** `GET /api/custombox/detail/`

**功能：** 获取指定自制盲盒的详细信息

**参数：**
- `key`: 盲盒标识符

### 5. 可用物品查询
**路径：** `GET /api/custombox/items/`

**功能：** 查询可用于自制盲盒的物品

**参数：**
- `q`: 搜索关键词
- `ext`: 磨损度
- `min`/`max`: 价格区间
- `type`: 物品类型
- `sort`: 排序方式
- `page`/`pageSize`: 分页参数

### 6. 外观资源获取
**路径：** `GET /api/custombox/skin/`

**功能：** 获取可用的封面和图标资源

**响应：**
```json
{
    "covers": [...],
    "items": [...]
}
```

### 7. 物品分类信息
**路径：** `GET /api/custombox/term/`

**功能：** 获取武器分类和类型信息

### 8. 开箱记录
**路径：** `GET /api/custombox/record/`

**功能：** 获取用户的自制盲盒开启记录

## 核心算法分析

### 1. 价格计算算法
```python
def calculate_custombox_price(items, rate):
    """自制盲盒价格计算"""
    total_price = 0
    
    for item in items:
        # 获取物品当前市场价格
        item_info = ItemInfo.objects.filter(
            custom_item__uid=item.get('uid')
        ).first()
        item_price = get_item_price(item_info.market_hash_name)
        
        # 价格 × 概率百分比
        weighted_price = item_price * item.get('percent') / 100
        total_price += weighted_price
    
    # 应用系统费率
    final_price = round(total_price * rate, 2)
    return final_price
```

**特点：**
- 基于物品市场价格和用户设置概率计算
- 应用系统费率调节
- 确保价格的合理性和平台收益

### 2. 概率选择算法
```python
def select_reward_item(box, user):
    """基于概率选择奖励物品"""
    available_drops = box.custombox_drops.all()
    
    # 根据用户等级选择概率类型
    chance_type = f'drop_chance_{user.extra.box_chance_type}'
    
    # 构建权重数组
    weights = []
    for drop in available_drops:
        weight = getattr(drop, chance_type, 0)
        weights.append(weight)
    
    # 验证权重有效性
    if sum(weights) <= 0:
        raise ValueError("Invalid weight configuration")
    
    # 使用加权随机选择
    if len(available_drops) > 1:
        chosen = random.choices(available_drops, weights=weights, k=1)[0]
    else:
        chosen = available_drops[0]
    
    return chosen
```

**特点：**
- 支持用户等级差异化概率
- 使用 `random.choices` 进行加权随机选择
- 异常情况的容错处理

### 3. 物品搜索算法
```python
def search_custombox_items(filters):
    """可用物品搜索"""
    query_params = {}
    
    # 磨损度过滤
    if filters.get('ext'):
        query_params['item_info__exterior'] = f"WearCategory{filters['ext']}"
    
    # 类型过滤
    if filters.get('category'):
        query_params['item_info__type'] = filters['category']
    
    # 价格区间过滤
    if filters.get('min_price'):
        query_params['item_info__item_price__price__gt'] = filters['min_price']
    if filters.get('max_price'):
        query_params['item_info__item_price__price__lt'] = filters['max_price']
    
    # 基础查询
    items = CustomBoxItemInfo.objects.filter(**query_params)
    
    # 关键词搜索
    if filters.get('query'):
        items = items.filter(
            Q(item_info__market_hash_name__contains=filters['query']) |
            Q(item_info__market_name_cn__contains=filters['query'])
        ).distinct()
    
    # 排序
    if filters.get('order'):
        order_map = {'up': '', 'down': '-'}
        order_field = f"{order_map[filters['order']]}item_info__item_price__price"
        items = items.order_by(order_field)
    
    return items
```

## 业务特性分析

### 1. 用户生成内容(UGC)
- **创作自由度**: 用户可以自由选择物品和设置概率
- **个性化定制**: 支持自定义名称、外观
- **创作门槛**: 最少需要2个物品，降低创作门槛

### 2. 经济模型
- **动态定价**: 基于物品市场价格实时计算
- **费率调节**: 通过系统费率控制平台收益
- **概率公平**: 用户设置的概率会被真实应用

### 3. 权限控制
- **创建权限**: 所有注册用户都可以创建
- **开启权限**: 仅创建者可以开启自己的盲盒
- **查看权限**: 详情信息对所有用户开放

## WebSocket 实时通信

### 消息格式
```python
def ws_send_box_game(data, action):
    """发送自制盲盒WebSocket消息"""
    rt_msg = ['box', action, data]
    redis_conn.publish('ws_channel', json.dumps(rt_msg))
```

**消息类型：**
- **new**: 新的开箱记录

**消息内容：**
```json
{
    "uid": "record_uuid",
    "user": {...},
    "case": {...},
    "item_info": {...},
    "price": 150.0,
    "create_time": "2024-01-01T10:00:00Z"
}
```

## 存在的问题与风险

### 1. 经济平衡问题

**问题描述：**
- 价格计算可能存在套利空间
- 用户可能创建不合理的概率配置
- 缺少对极端配置的限制

**影响：**
- 平台经济失衡
- 用户体验不一致
- 潜在的财务风险

### 2. 权限设计局限

**问题描述：**
- 只有创建者可以开启，限制了分享和社交
- 缺少公开展示和推荐机制
- 无法实现盲盒的二次流通

**影响：**
- UGC价值未充分发挥
- 用户参与度有限
- 缺少社区互动

### 3. 概率验证不足

**问题描述：**
- 用户设置的概率缺少合理性验证
- 可能出现概率和为0的情况
- 没有最小概率限制

**影响：**
- 可能导致无法开箱
- 用户体验不佳
- 系统稳定性风险

### 4. 资源管理问题

**问题描述：**
- 自定义图片资源缺少审核机制
- 没有资源使用统计和清理
- 可能存储大量无用资源

**影响：**
- 存储成本增加
- 内容质量难以保证
- 系统性能下降

## 改进建议

### 1. 经济模型优化

#### 价格合理性检查
```python
class CustomBoxPriceValidator:
    def __init__(self):
        self.min_profit_rate = 0.1  # 最小利润率
        self.max_loss_rate = 0.2    # 最大亏损率
    
    def validate_price_configuration(self, items, rate):
        """验证价格配置的合理性"""
        expected_value = sum(
            get_item_price(item['market_hash_name']) * item['percent'] / 100
            for item in items
        )
        
        final_price = expected_value * rate
        
        # 检查利润率
        profit_rate = (final_price - expected_value) / expected_value
        
        if profit_rate < self.min_profit_rate:
            raise ValueError("Price too low, may cause financial risk")
        
        if profit_rate > self.max_loss_rate:
            raise ValueError("Price too high, poor user experience")
        
        return final_price
    
    def suggest_optimal_rate(self, items):
        """建议最优费率"""
        expected_value = sum(
            get_item_price(item['market_hash_name']) * item['percent'] / 100
            for item in items
        )
        
        # 基于物品价值和数量计算建议费率
        item_count = len(items)
        value_tier = self.get_value_tier(expected_value)
        
        base_rate = 1.15  # 基础费率15%
        count_adjustment = max(0.95, 1.0 - (item_count - 2) * 0.02)  # 物品越多费率越低
        value_adjustment = self.get_value_adjustment(value_tier)
        
        suggested_rate = base_rate * count_adjustment * value_adjustment
        return round(suggested_rate, 3)
```

#### 概率配置验证
```python
class ProbabilityValidator:
    def __init__(self):
        self.min_item_probability = 1.0  # 每个物品最小1%概率
        self.max_item_probability = 80.0  # 每个物品最大80%概率
    
    def validate_probability_distribution(self, items):
        """验证概率分布的合理性"""
        total_probability = sum(item['percent'] for item in items)
        
        # 检查总概率
        if abs(total_probability - 100.0) > 0.1:
            raise ValueError("Total probability must equal 100%")
        
        # 检查单个物品概率
        for item in items:
            prob = item['percent']
            if prob < self.min_item_probability:
                raise ValueError(f"Item probability too low: {prob}%")
            if prob > self.max_item_probability:
                raise ValueError(f"Item probability too high: {prob}%")
        
        # 检查概率分散度
        self.check_probability_distribution(items)
        
        return True
    
    def check_probability_distribution(self, items):
        """检查概率分布的合理性"""
        probabilities = [item['percent'] for item in items]
        
        # 计算基尼系数（衡量分布不均匀程度）
        gini = self.calculate_gini_coefficient(probabilities)
        
        if gini > 0.8:  # 基尼系数过高说明分布过于不均
            raise ValueError("Probability distribution too uneven")
    
    def calculate_gini_coefficient(self, values):
        """计算基尼系数"""
        sorted_values = sorted(values)
        n = len(values)
        cumsum = [0]
        
        for val in sorted_values:
            cumsum.append(cumsum[-1] + val)
        
        return (n + 1 - 2 * sum(cumsum) / cumsum[-1]) / n
```

### 2. 社交功能增强

#### 盲盒分享系统
```python
class CustomBoxSharingSystem:
    def share_custombox(self, box, owner, target_users=None, public=False):
        """分享自制盲盒"""
        share_record = CustomBoxShare.objects.create(
            box=box,
            owner=owner,
            is_public=public,
            share_code=self.generate_share_code(),
            expire_time=timezone.now() + timedelta(days=7)
        )
        
        if target_users:
            for user in target_users:
                CustomBoxShareTarget.objects.create(
                    share=share_record,
                    target_user=user,
                    can_open=True
                )
        
        return share_record.share_code
    
    def open_shared_box(self, user, share_code):
        """开启分享的盲盒"""
        share = CustomBoxShare.objects.filter(
            share_code=share_code,
            expire_time__gt=timezone.now()
        ).first()
        
        if not share:
            raise ValueError("Invalid or expired share code")
        
        # 检查权限
        if not share.is_public:
            target = CustomBoxShareTarget.objects.filter(
                share=share,
                target_user=user,
                can_open=True
            ).first()
            
            if not target:
                raise ValueError("No permission to open this box")
        
        # 执行开箱逻辑
        return self.open_box_logic(user, share.box)
    
    def get_public_customboxes(self, filters=None):
        """获取公开的自制盲盒"""
        queryset = CustomBoxShare.objects.filter(
            is_public=True,
            expire_time__gt=timezone.now()
        )
        
        if filters:
            if filters.get('min_price'):
                queryset = queryset.filter(box__price__gte=filters['min_price'])
            if filters.get('max_price'):
                queryset = queryset.filter(box__price__lte=filters['max_price'])
        
        return queryset.order_by('-create_time')
```

### 3. 内容审核系统

#### 自动审核机制
```python
class CustomBoxContentModerator:
    def __init__(self):
        self.forbidden_words = self.load_forbidden_words()
        self.max_name_length = 50
        self.min_items = 2
        self.max_items = 20
    
    def moderate_custombox(self, name, items, images):
        """自制盲盒内容审核"""
        issues = []
        
        # 名称审核
        name_issues = self.check_name(name)
        issues.extend(name_issues)
        
        # 物品配置审核
        item_issues = self.check_items(items)
        issues.extend(item_issues)
        
        # 图片审核
        image_issues = self.check_images(images)
        issues.extend(image_issues)
        
        # 返回审核结果
        return {
            'passed': len(issues) == 0,
            'issues': issues,
            'severity': max([issue['severity'] for issue in issues], default=0)
        }
    
    def check_name(self, name):
        """检查盲盒名称"""
        issues = []
        
        if len(name) > self.max_name_length:
            issues.append({
                'type': 'name_too_long',
                'message': f'Name too long (max {self.max_name_length} characters)',
                'severity': 2
            })
        
        for word in self.forbidden_words:
            if word.lower() in name.lower():
                issues.append({
                    'type': 'forbidden_word',
                    'message': f'Contains forbidden word: {word}',
                    'severity': 3
                })
        
        return issues
    
    def check_items(self, items):
        """检查物品配置"""
        issues = []
        
        if len(items) < self.min_items:
            issues.append({
                'type': 'too_few_items',
                'message': f'At least {self.min_items} items required',
                'severity': 3
            })
        
        if len(items) > self.max_items:
            issues.append({
                'type': 'too_many_items',
                'message': f'At most {self.max_items} items allowed',
                'severity': 2
            })
        
        # 检查重复物品
        item_uids = [item['uid'] for item in items]
        if len(item_uids) != len(set(item_uids)):
            issues.append({
                'type': 'duplicate_items',
                'message': 'Duplicate items found',
                'severity': 2
            })
        
        return issues
```

### 4. 性能优化

#### 缓存策略优化
```python
class CustomBoxCacheManager:
    def __init__(self):
        self.redis_client = redis.Redis()
        self.cache_timeout = {
            'item_list': 300,      # 物品列表5分钟
            'box_detail': 600,     # 盲盒详情10分钟
            'user_boxes': 60,      # 用户盲盒列表1分钟
            'skin_resources': 3600  # 外观资源1小时
        }
    
    def get_cached_items(self, filters):
        """获取缓存的物品列表"""
        cache_key = self.build_items_cache_key(filters)
        cached = self.redis_client.get(cache_key)
        
        if cached:
            return json.loads(cached)
        
        # 查询数据库
        items = self.query_items_from_db(filters)
        
        # 缓存结果
        self.redis_client.setex(
            cache_key,
            self.cache_timeout['item_list'],
            json.dumps(items)
        )
        
        return items
    
    def invalidate_box_cache(self, box_key):
        """清除盲盒相关缓存"""
        patterns = [
            f"custombox:detail:{box_key}",
            f"custombox:user_boxes:*",
            "custombox:public_boxes"
        ]
        
        for pattern in patterns:
            keys = self.redis_client.keys(pattern)
            if keys:
                self.redis_client.delete(*keys)
```

## 监控和风控

### 异常行为检测
```python
class CustomBoxMonitor:
    def __init__(self):
        self.alert_thresholds = {
            'creation_rate': 10,     # 1小时内创建数量
            'opening_rate': 50,      # 1小时内开启数量
            'price_deviation': 0.5,  # 价格偏离度
        }
    
    def monitor_user_behavior(self, user):
        """监控用户行为"""
        alerts = []
        
        # 检查创建频率
        recent_creations = CustomBox.objects.filter(
            user=user,
            create_time__gte=timezone.now() - timedelta(hours=1)
        ).count()
        
        if recent_creations > self.alert_thresholds['creation_rate']:
            alerts.append({
                'type': 'high_creation_rate',
                'message': f'User created {recent_creations} boxes in 1 hour'
            })
        
        # 检查开启频率
        recent_openings = CustomBoxRecord.objects.filter(
            user=user,
            create_time__gte=timezone.now() - timedelta(hours=1)
        ).count()
        
        if recent_openings > self.alert_thresholds['opening_rate']:
            alerts.append({
                'type': 'high_opening_rate',
                'message': f'User opened {recent_openings} boxes in 1 hour'
            })
        
        return alerts
    
    def monitor_economic_health(self):
        """监控经济健康度"""
        # 统计总创建价值 vs 总开启价值
        total_created_value = CustomBox.objects.aggregate(
            total=Sum('price')
        )['total'] or 0
        
        total_opened_value = CustomBoxRecord.objects.aggregate(
            total=Sum('price')
        )['total'] or 0
        
        # 计算价值比率
        if total_created_value > 0:
            value_ratio = total_opened_value / total_created_value
            
            if value_ratio > 1.2:  # 开启价值远超创建价值
                _logger.warning(f"Economic imbalance detected: ratio {value_ratio}")
```

## 总结

CustomBox模块实现了一个创新的用户生成内容系统，允许用户创建个性化的开箱体验。当前设计在基本功能上较为完善，但在经济平衡、社交功能、内容审核等方面存在改进空间。建议按照上述优化方案逐步完善，重点关注经济模型的稳定性、用户体验的提升和平台内容的质量控制。

## 接口清单

| 接口名称 | 方法 | 路径 | 状态 | 功能描述 |
|---------|------|------|------|----------|
| 创建自制盲盒 | POST | `/api/custombox/create/` | 正常 | 创建/更新盲盒配置 |
| 开启自制盲盒 | POST | `/api/custombox/open/` | 正常 | 开启指定盲盒 |
| 自制盲盒列表 | GET | `/api/custombox/list/` | 正常 | 获取用户盲盒列表 |
| 自制盲盒详情 | GET | `/api/custombox/detail/` | 正常 | 获取盲盒详情 |
| 可用物品查询 | GET | `/api/custombox/items/` | 正常 | 查询可用物品 |
| 外观资源获取 | GET | `/api/custombox/skin/` | 正常 | 获取外观资源 |
| 物品分类信息 | GET | `/api/custombox/term/` | 正常 | 获取分类信息 |
| 开箱记录 | GET | `/api/custombox/record/` | 正常 | 获取开箱记录 |
