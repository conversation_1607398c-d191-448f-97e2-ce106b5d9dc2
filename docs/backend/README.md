# 后端架构文档

本目录包含后端系统架构、数据库设计、核心机制等技术文档。

## 📋 文档列表

- [**系统架构**](architecture.md) - 整体架构设计和技术选型
- [**数据库设计**](database.md) - 数据模型和关系设计
- [**对战房间状态**](caseroom-states.md) - 对战房间状态机制详解
- [**WebSocket 实现**](websocket.md) - 实时通信后端实现
- [**thworker 框架**](thworker.md) - 自定义异步任务处理框架

## 🏗️ 架构概览

### 技术栈
- **Web框架**: Django 1.11 + Django REST Framework
- **数据库**: MySQL 5.7+
- **缓存**: Redis 5.0+
- **异步任务**: thworker (自定义线程池+定时任务框架)
- **WebSocket**: Django Channels
- **容器化**: Docker + Docker Compose

### 核心模块
```
server/
├── authentication/     # 用户认证系统
├── box/               # 开箱和对战系统
├── b2ctrade/          # 交易系统
├── charge/            # 充值系统
├── websocket/         # WebSocket通信
└── steambase/         # 核心配置和工具
```

## 🎯 核心功能

### 异步任务系统
- 定时任务调度 (基于schedule库)
- 多线程并发处理  
- Redis数据存储和锁机制
- 业务模块任务集成

### 认证系统
- 定时任务调度 (基于schedule库)
- 多线程并发处理
- Redis数据存储和锁机制
- 业务模块任务集成

### 开箱系统
- 多种箱子类型支持
- 概率算法实现
- 对战房间机制
- 实时状态推进

### 实时通信
- WebSocket消息推送
- 房间状态同步
- 用户行为通知
- 异常处理机制

## 🔄 状态管理

### 对战房间状态
```python
class GameState(Enum):
    Waiting = 1      # 等待玩家
    Full = 2         # 已满员
    Running = 3      # 进行中
    Finished = 4     # 已结束
    Cancelled = 5    # 已取消
```

### 状态转换
- `Waiting` → `Full` (玩家加入满员)
- `Full` → `Running` (开始对战)
- `Running` → `Finished` (对战结束)
- `Waiting/Full` → `Cancelled` (取消对战)

## 📊 数据库设计

### 核心表结构
- **用户表** (`authentication_user`) - 用户基础信息
- **箱子表** (`box_case`) - 箱子配置信息
- **物品表** (`package_item`) - 游戏物品信息
- **房间表** (`box_caseroom`) - 对战房间信息
- **回合表** (`box_caseroomround`) - 对战回合记录

### 关键关系
```sql
-- 箱子包含物品
box_case → box_caseitem → package_item

-- 房间包含回合
box_caseroom → box_caseroomround

-- 用户参与房间
authentication_user → box_caseroomjoiner ← box_caseroom
```

## 🚀 性能优化

### 缓存策略
- **Redis缓存**: 房间列表、用户状态、热点数据
- **查询优化**: `select_related`, `prefetch_related`
- **分页查询**: 大数据量分页处理

### 并发处理
- **锁机制**: Redis锁防止并发问题
- **异步任务**: thworker处理定时和后台任务
- **连接池**: 数据库连接池优化

### 监控告警
- **日志系统**: 结构化日志记录
- **性能监控**: 接口响应时间监控
- **异常告警**: 关键错误实时通知

## 🔧 开发调试

### 常用命令
```bash
# 数据库迁移
python manage.py migrate

# 创建管理员
python manage.py createsuperuser

# 收集静态文件
python manage.py collectstatic

# 启动开发服务器
python manage.py runserver

# 启动thworker
python manage.py setupworker
```

### 调试工具
- **Django Debug Toolbar** - SQL查询分析
- **Django Extensions** - 增强管理命令
- **ipdb** - 断点调试
- **Django Logging** - 日志输出

---

*更新时间: 2025-06-18*
