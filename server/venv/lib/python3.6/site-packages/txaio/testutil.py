###############################################################################
#
# The MIT License (MIT)
#
# Copyright (c) Crossbar.io Technologies GmbH
#
# Permission is hereby granted, free of charge, to any person obtaining a copy
# of this software and associated documentation files (the "Software"), to deal
# in the Software without restriction, including without limitation the rights
# to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
# copies of the Software, and to permit persons to whom the Software is
# furnished to do so, subject to the following conditions:
#
# The above copyright notice and this permission notice shall be included in
# all copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
# IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
# AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
# LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
# OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
# THE SOFTWARE.
#
###############################################################################

import txaio
from contextlib import contextmanager


@contextmanager
def replace_loop(new_loop):
    """
    This is a context-manager that sets the txaio event-loop to the
    one supplied temporarily. It's up to you to ensure you pass an
    event_loop or a reactor instance depending upon asyncio/Twisted.

    Use like so:

    .. sourcecode:: python

        from twisted.internet import task
        with replace_loop(task.Clock()) as fake_reactor:
            f = txaio.call_later(5, foo)
            fake_reactor.advance(10)
            # ...etc
    """

    # setup
    orig = txaio.config.loop
    txaio.config.loop = new_loop

    yield new_loop

    # cleanup
    txaio.config.loop = orig
