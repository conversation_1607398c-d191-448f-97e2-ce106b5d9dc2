import json
import logging
import string
import uuid
import re
import threading
import base64
import oss2
import secrets
import time
import ipaddress
from typing import Optional

from datetime import timedelta, datetime

from django.db import transaction
from django.db.models import F
from django.conf import settings
from django.core.paginator import Paginator
from django.contrib.auth import authenticate, get_user_model, login
from django.contrib.auth.hashers import make_password, check_password
from django.core.cache import cache
from django.core.exceptions import ObjectDoesNotExist
from functools import lru_cache

from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django_redis import get_redis_connection
from captcha.image import ImageCaptcha
from django.template.defaultfilters import escape

# from libs.aliyun_sms import send_sms as ali_send_sms
from rest_framework import status

from authentication.service.email_service import MailServer
from authentication.service.sms_service import SMService
from libs.aliyun_sms import send_sms
from libs.steamapi import <PERSON><PERSON>pi
from sitecfg.models import Support, SiteConfig
from sitecfg.models import AccessControl
from sitecfg.interfaces import get_box_free_interval, get_box_free_limit_game_count, get_box_free_limit_dota2_playtime, \
    get_register_coins, get_register_box_chance_type
from sitecfg.interfaces import get_box_free_zero_interval, get_box_free_zero_last, set_box_free_zero_last, get_user_point_max, get_enable_rule_exam
from sitecfg.interfaces import get_diamond_to_coins_exchange_rate
from authentication.models import UserExtra, UserAsset, UserPointsRecord, UserStatisticsDay, AuthUser, UserProfile,  UserBalanceRecord, PhoneCodeRecord, SteamBlackList
from authentication.serializers import AuthUserSerializer, UserStatisticsDaySerializer, UserBalanceRecordSerializer
from steambase.sendmsg import sendmsg_by_qybor
from promotion.interfaces import bind_promotion
from steambase.enums import RespCode
from steambase.utils import string_generator, id_generator, get_uuid
from sitecfg.interfaces import get_domain_verify, get_access_control_email_suffix, get_access_control_ip, get_access_control_steam, get_access_control_token, get_access_control_token_limit
from sitecfg.models import AccessControl
from sitecfg.models import SEO

from steambase.utils import parse_tradeurl, steam3id_to_steamid64

logging.getLogger("oss2").setLevel(logging.WARNING)
logging.getLogger("oss2").setLevel(logging.WARNING)

_logger = logging.getLogger(__name__)
USER = get_user_model()


def get_user_info(user, fields):
    user_data = AuthUserSerializer(user, fields=fields).data
    return RespCode.Succeed.value, user_data

def get_user_balance_record(user, fields, page, page_size):
    queryset = UserBalanceRecord.objects.filter(user=user).order_by('-create_time')
    paginator = Paginator(queryset, page_size)
    records = paginator.page(page)
    records_data = UserBalanceRecordSerializer(records, many=True, fields=fields).data
    resp = {
        'items': records_data,
        'total': paginator.count
    }
    return RespCode.Succeed.value, resp




def set_user_tradeurl(user, tradeurl):
    """
    设置用户的 Steam 交易 URL
    :param user: 当前用户 
    :param tradeurl: 新的 Steam 交易 URL
    :return: 响应码和信息
    """

    def is_valid_tradeurl(tradeurl):
        if not isinstance(tradeurl, str):  # 确保 tradeurl 为字符串
            return False, _('Invalid trade url')
        patterns = [
            r'^https://steamcommunity.com/tradeoffer/new/\?partner=[\d]+&token=[\w\d\-]{8}$',
            r'^https://steamcommunity.com/tradeoffer/new/\?partner=[\d]+&token=[\w\d]{8}$'
        ]
        for pattern in patterns:
            if re.match(pattern, tradeurl):
                return True, ''
        return False, _('Invalid trade url')

    def check_existing_tradeurl(user, tradeurl):
        # 不在这里执行事务, 由外部的事务管理
        user_asset = UserAsset.objects.select_for_update().filter(user=user).first()
        if not user_asset:
            return None, RespCode.InvalidParams.value, _('User asset not found')
        if tradeurl == user_asset.tradeurl:
            return user_asset, RespCode.InvalidParams.value, _('No changes were detected in the URL you provided.')
        return user_asset, None, ''

    def is_tradeurl_bound_to_other_user(partner, user):
        partner_str = str(partner)
        return UserAsset.objects.filter(tradeurl__contains=f'partner={partner_str}').exclude(user=user).exists()

    def is_tradeurl_in_blacklist(old_partner, new_partner):
        old_partner_str = str(old_partner) if old_partner else None
        new_partner_str = str(new_partner) if new_partner else None
        return SteamBlackList.objects.filter(steam_id__in=[old_partner_str, new_partner_str], enable=True).exists()

    def update_user_steam_info(user, steamid):
        steamapi = SteamApi(api_key=settings.API_KEY, steamid=steamid)
        res = steamapi.get_player_summary()
        if res:
            steam_profile_fields = ['profileurl', 'avatar', 'avatarmedium', 'avatarfull']
            for field in steam_profile_fields:
                setattr(user.steam, field, res.get(field, ''))
            user.steam.save()

    # 验证 tradeurl 是否有效
    valid, message = is_valid_tradeurl(tradeurl)
    if not valid:
        return RespCode.InvalidParams.value, message

    # 解析 tradeurl 获取 partner
    try:
        partner, token = parse_tradeurl(tradeurl)
        partner = int(partner)
        steamid = steam3id_to_steamid64(partner)
    except (ValueError, TypeError):
        return RespCode.InvalidParams.value, _('Invalid trade URL format')

    # 将所有数据库相关操作放入事务中
    with transaction.atomic():
        # 检查用户资产和 tradeurl 是否已存在
        user_asset, code, message = check_existing_tradeurl(user, tradeurl)
        if code:
            return code, message

        # 检查 partner 是否已绑定到其他用户
        if is_tradeurl_bound_to_other_user(partner, user):
            return RespCode.InvalidParams.value, _('This Steam link is already in use. Each link can bind only one account.')

        # 检查黑名单
        old_partner = parse_tradeurl(user_asset.tradeurl)[0] if user_asset.tradeurl else None
        if is_tradeurl_in_blacklist(old_partner, partner):
            return RespCode.InvalidParams.value, _('Failed to update. An unknown error occurred.')
        
        # 确保新旧 partner 相同，否则返回安全问题错误
        if old_partner and int(old_partner) != partner:
            return RespCode.InvalidParams.value, _('Update failed. The system detected a security issue with this operation. Please ensure your actions comply with security policies.')

        # 更新用户的 tradeurl
        user_asset.tradeurl = tradeurl
        user_asset.save()

    # 更新 Steam 信息
    if user.phone:
        update_user_steam_info(user, steamid)

    _logger.info(f'User {user} updated tradeurl to: {tradeurl}')
    return RespCode.Succeed.value, user_asset.tradeurl


   

def set_user_phone(user, phone, verify_code):
    """
    设置用户手机
    :param user: 当前用户
    :param phone: 新手机
    :param verify_code: 验证码
    :return: 响应码和信息
    """
    def is_valid_phone_and_code(phone, code):
        if not phone or not code:
            return False, _('Oops! Wrong CAPTCHA. Try again.')
        return True, ''

    def verify_code_in_cache(phone, code):
        #key = f'verify_code:{phone}'
        key = 'verify_code:{}'.format(phone)
        cache_code = cache.get(key)
        
        if cache_code:
            try:
                cached_code = json.loads(cache_code)
                if cached_code == code:
                    cache.delete(key)
                    return True
            except json.JSONDecodeError:
                _logger.error('Invalid JSON in cache for key: {}'.format(key))
                cache.delete(key)
        return False

    def is_phone_already_used(phone):
        return USER.objects.filter(phone=phone).exists()

    # 验证 phone 和 verify_code 是否有效
    valid, message = is_valid_phone_and_code(phone, verify_code)
    if not valid:
        return RespCode.InvalidParams.value, message

    # 验证验证码是否正确
    if not verify_code_in_cache(phone, verify_code.upper()):
        return RespCode.BadRequest.value, _('Oops! Wrong CAPTCHA. Try again.')

    # 检查手机是否已被其他用户使用
    if is_phone_already_used(phone):
        return RespCode.BadRequest.value, _('This phone number has already been bound to a different user. Please check and retry.')

    # 更新用户手机
    if not user.steam.steamid:
        user.username = phone
    user.phone = phone
    user.save(update_fields=['username', 'phone'])
    _logger.info('User {} update phone: {}'.format(user.uid, phone))
    return RespCode.Succeed.value, {}


def set_user_email(user, email, verify_code):
    """
    设置用户邮箱
    :param user: 当前用户
    :param email: 新邮箱
    :param verify_code: 验证码
    :return: 响应码和信息
    """
    # print(email, verify_code)
    
    def is_valid_email_and_code(email, code):
        if not email or not code:
            return False, _('Oops! Wrong CAPTCHA. Try again.')
        return True, ''

    def verify_code_in_cache(email, code):
        #key = f'verify_code:{email}'
        key = 'verify_code:{}'.format(email)
        cache_code = cache.get(key)
        # print(cache_code)
        if cache_code == code:
            cache.delete(key)
            return True
        return False
        # if cache_code:
        #     try:
        #         cached_code = json.loads(cache_code)
        #         if cached_code == code:
        #             cache.delete(key)
        #             return True
        #     except json.JSONDecodeError:
        #         _logger.error('Invalid JSON in cache for key: {}'.format(key))
        #         cache.delete(key)
        # return False

    def is_email_already_used(email):
        return USER.objects.filter(email=email).exists()

    # 验证 email 和 verify_code 是否有效
    valid, message = is_valid_email_and_code(email, verify_code)
    if not valid:
        return RespCode.InvalidParams.value, message

    # 验证验证码是否正确
    if not verify_code_in_cache(email, verify_code.upper()):
        return RespCode.BadRequest.value, _('Oops! Wrong CAPTCHA. Try again.')

    # 检查邮箱是否已被其他用户使用
    if is_email_already_used(email):
        return RespCode.BadRequest.value, _('Oops! This email is already taken.')

    # 更新用户邮箱
    user.email = email
    user.save(update_fields=['email'])
    _logger.info('User {} update email: {}'.format(user.uid, email))
    return RespCode.Succeed.value, {}


def send_verify_code_by_qrbor(phone):
    # cache_captch = cache.get(uuid)
    # if captcha.upper() != cache_captch:
    #     return RespCode.InvalidParams.value, _('Verification failed: Incorrect code.')
    key = 'verify_code:{}'.format(phone)
    cache_code = cache.get(key)
    if cache_code:
        return RespCode.BadRequest.value, _('Exceeded verification code sending limit. Please try again later.')
    verify_code = id_generator(4)
    if settings.DEBUG == True:
        # print(verify_code)
        resp = {'respcode': '0'}
    else:
        resp = sendmsg_by_qybor(phone, verify_code)
    _logger.info('Send verify code to {}, result: {}'.format(phone, resp))
    if resp and resp.get('respcode') == '0':
        key = 'verify_code:{}'.format(phone)
        cache.set(key, json.dumps(verify_code), 60)
        return RespCode.Succeed.value, {}
    return RespCode.BusinessError.value, _('Failed to send verification code. Please ensure your network connection is stable and try again.')


def get_captcha():
    # 避免使用 0 o O 1 l L i I等容易混淆的字符
    while True:
        captchatext = string_generator(4)
        if '0' not in captchatext and 'o' not in captchatext and 'O' not in captchatext and '1' not in captchatext and 'l' not in captchatext and 'L' not in captchatext and 'i' not in captchatext and 'I' not in captchatext:
            break
    img = ImageCaptcha()
    # print(captchatext)
    captcha = base64.b64encode(img.generate(captchatext).getvalue())
    captcha = ','.join(['data:image/png;base64', captcha.decode()])
    uuid = get_uuid()
    # 返回数据结构
    resp = {
        'uuid': uuid,
        'captcha': captcha        
    }
    cache.set(uuid, captchatext, 120)
    return RespCode.Succeed.value, resp


def register_by_phone(request, phone, name, verify_code, password, ref_code, token):
    passed = not get_enable_rule_exam()
    reg_ip = get_client_ip(request)
    domain = request.META.get('HTTP_X_HOST', '')

    # if domain == 'csgobox.com.cn':
    #     passed = False
        
    if len(name) > 10 or len(name) < 2:
        return RespCode.InvalidParams.value, _('Username must be 2–10 characters. Please adjust and try again."')        
        
    if phone == name:
        return RespCode.InvalidParams.value, _('Error: Username cannot contain a phone number. Please try another username.')

    if not phone or not verify_code or not password or not name or not token:
        return RespCode.InvalidParams.value, _('API request failed due to missing parameters.')
    if len(password) > 16 or len(password) < 8:
        return RespCode.InvalidParams.value, _('Error: Password must be 8 to 16 characters.')

    # 检查IP频率限制
    ip_key = f'reg_ip:{reg_ip}'
    reg_count = cache.get(ip_key, 0)
    if reg_count >= 10:
        return RespCode.BadRequest.value, _('Security alert: High registration activity detected from this IP. Please try again later.')
    cache.set(ip_key, reg_count + 1, timeout=3600)

    # 验证码尝试次数限制
    attempt_key = f'verify_code_attempts:{phone}'
    attempts = cache.get(attempt_key, 0)
    if attempts >= 5:
        return RespCode.BadRequest.value, _('Too many verification code attempts. Please try again later.')
    cache.set(attempt_key, attempts + 1, timeout=600)

    # 获取缓存中的验证码和相关信息
    key = f'verify_code:{phone}'
    cached_data = cache.get(key)
    if not cached_data:
        return RespCode.InvalidParams.value, _('Verification failed: Code expired.')

    # 验证验证码
    if str(verify_code) != cached_data['verify_code']:
        return RespCode.BadRequest.value, _('Verification failed: Incorrect code.')

    # 校验token与IP、UA、有效期
    if cached_data['token'] != token:
        return RespCode.BadRequest.value, _('The token does not match. Please try again.')
    if cached_data['ip'] != reg_ip or cached_data['user_agent'] != request.META.get('HTTP_USER_AGENT', ''):
        return RespCode.BadRequest.value, _('Token or verification code validation failed. Please ensure you have entered the correct information and try again.')
    if time.time() - cached_data['created_at'] > 900:
        return RespCode.BadRequest.value, _('Verification failed: Token or code has expired.')

    # 验证通过后删除验证码与尝试次数限制缓存
    cache.delete(key)
    cache.delete(attempt_key)

    # 检查手机号是否已注册
    user = USER.objects.filter(phone=phone).first()
    if user:
        return RespCode.BadRequest.value, _('Error: Phone number already in use. Please try another one.')

    # 创建用户
    user = USER.objects.create_user(
        username=phone,
        phone=phone,
        password=password,
        domain=domain,
        reg_ip=reg_ip
    )
    user.set_password(password)
    user.steam.avatar = '/media/user/default_avatar.jpg'
    user.steam.avatarmedium = '/media/user/default_avatar.jpg'
    user.steam.avatarfull = '/media/user/default_avatar.jpg'
    user.steam.personaname = name
    user.steam.save(update_fields=['avatar', 'avatarmedium', 'avatarfull', 'personaname'])
    user.save()

    bind_promotion(user, ref_code)

    register_coins = get_register_coins()
    box_chance_type = get_register_box_chance_type()
    user.update_balance(register_coins, "注册赠送")
    user.update_box_chance_type(box_chance_type)
    user.update_exams_passed(passed)
    user.ban_users_with_same_ip()

    _logger.info(f"User {name} registered by phone: {phone}")
    login(request, user, backend='django.contrib.auth.backends.ModelBackend')

    update_base_count('users_base_count', 1)

    return RespCode.Succeed.value, {}


def login_by_phone(request, phone, password):
    try:
        login_ip = get_client_ip(request)
        domain = request.META.get('HTTP_X_HOST', '')    

        if not phone or not validate_phone_number(phone):
            return RespCode.InvalidParams.value, {}, _('Invalid phone number.')
        if not password:
            return RespCode.InvalidParams.value, {}, _('You must enter a password to proceed.')

        if get_domain_verify():
            site = SEO.objects.filter(url=domain, enable=True)
            if not site.exists():
                return RespCode.BadRequest.value, {}, _('System interception: We have detected bulk registration attempts or malicious activity. Please comply with our terms of service and avoid such behavior.')           

        # check ip in blacklist
        blacklisted_ips = AccessControl.objects.filter(enable=True).values_list('ip', flat=True)  
        if ip_in_blacklist(login_ip, blacklisted_ips):
            blacklisted_entry = AccessControl.objects.filter(enable=True, ip__in=[login_ip]).first()
            message = blacklisted_entry.message if blacklisted_entry else 'System Maintenance'
            return RespCode.BadRequest.value, {}, message
        
        # 通过电话号码获取用户，如果用户不存在将抛出异常
        user = USER.objects.get(phone=phone)

        # 检查账号是否被禁用，并返回禁用原因
        if not user.is_active:
            return RespCode.InvalidParams.value, user.ban_active_reasons

        # 验证密码
        if check_password(password, user.password):
            # 密码验证通过，处理用户登录逻辑
            login(request, user, backend='django.contrib.auth.backends.ModelBackend')
            user.login_ip = login_ip
            user.save()
            return RespCode.Succeed.value, _('Login successful')
        else:
            # 密码验证失败
            return RespCode.BadRequest.value, _('Invalid username or password. Please try again.')
    except ObjectDoesNotExist:
        # 用户不存在
        return RespCode.InvalidParams.value, _('This account does not exist.')

# def login_by_phone(request, phone, password, login_ip):
#     user = authenticate(request, username=phone, password=password)

#     if user is not None:
#         # 验证成功，更新login_ip字段
#         user.login_ip = login_ip
#         user.save()

#         # 登录用户
#         login(request, user)

#         return RespCode.Succeed.value,  _('登录成功')
#     else:
#         return RespCode.BadRequest.value, _('账号或密码错误')


def reset_pwd_by_phone(phone, password, verify_code):
    """
    重置密码通过手机号和验证码
    :param phone: 手机号
    :param password: 新密码
    :param verify_code: 用户输入的验证码
    :return: 状态码和响应信息
    """
    # 校验必需参数
    if not phone or not verify_code or not password:
        return RespCode.InvalidParams.value, _('API request failed due to missing parameters.')

    # 验证密码长度
    if not (8 <= len(password) <= 16):
        return RespCode.InvalidParams.value, _('Error: Password must be 8 to 16 characters.')

    # 获取验证码缓存
    cache_key = f'verify_code:{phone}'
    cache_val = cache.get(cache_key)

    if not cache_val:
        return RespCode.InvalidParams.value, _('Verification code is expired. Please try again with a new one.')

    # 兼容 send_email_code 中存储的字典格式和字符串格式
    if isinstance(cache_val, dict):
        cache_code = cache_val.get('verify_code')
    else:
        cache_code = cache_val

    if str(cache_code).strip().upper() != str(verify_code).strip().upper():
        return RespCode.BadRequest.value, _('Verification failed: Incorrect code.')

    # 不删除验证码，让后续重置密码接口继续校验

    # 查询用户是否存在
    user = USER.objects.filter(phone=phone).first()
    if not user:
        return RespCode.InvalidParams.value, _('该账号不存在')

    # 更新用户密码
    user.password = make_password(password)
    user.save()

    return RespCode.Succeed.value, _('Password reset successful')
    





def reset_box_free_count():
    try:
        _logger.info('Reset free case count')
        interval = get_box_free_zero_interval()
        last_day = get_box_free_zero_last()
        today = timezone.localdate()
        past_day = (today - last_day).days
        reset_box_free_zero = True if past_day >= interval else False
        if reset_box_free_zero:
            set_box_free_zero_last(today)
        users = USER.objects.filter(is_active=True)
        for user in users:
            with transaction.atomic():
                extra = UserExtra.objects.select_for_update().filter(user=user).first()
                if extra:
                    if extra.box_free_count <= 0:
                        extra.box_free_count = 1
                    if reset_box_free_zero and extra.box_free_zero_count <= 0:
                        extra.box_free_zero_count = 1
                    extra.save(update_fields=['box_free_count', 'box_free_zero_count'])
    except Exception as e:
        _logger.exception(e)


def calc_user_box_free_count(user):
    own_games_count = user.steam.own_games_count
    limit_game_count = get_box_free_limit_game_count()
    if own_games_count < limit_game_count:
        return
    dota2_playtime = user.steam.dota2_playtime
    limit_dota2_playtime = get_box_free_limit_dota2_playtime()
    if dota2_playtime < limit_dota2_playtime:
        return

    with transaction.atomic():
        extra = UserExtra.objects.select_for_update().filter(user=user).first()
        if extra:
            today = timezone.localdate()
            box_free_last_day = timezone.localtime(extra.box_free_last).date()
            past_day = (today - box_free_last_day).days
            interval = get_box_free_interval()
            if past_day >= interval and extra.box_free_count <= 0:
                extra.box_free_count = 1
                extra.save(update_fields=['box_free_count'])


def reduce_user_point():
    for user in USER.objects.all():
        user_update_points(user, -50, 'Reduce point per day')


@transaction.atomic
def user_update_points(user, amount, remark=None):
    asset = UserAsset.objects.select_for_update().get(user=user)
    if asset.points + amount < 0:
        amount = -asset.points
    elif asset.points + amount > get_user_point_max():
        amount = get_user_point_max() - asset.points
    if amount == 0:
        return
    points_before = asset.points
    asset.points += amount
    asset.save()
    points_after = asset.points
    UserPointsRecord.objects.create(
        user=user, points_changed=amount, points_before=points_before,
        points_after=points_after, reason=remark)


def get_user_statistics_day(count):
    user_statistics_day = UserStatisticsDay.objects.all().order_by('-create_time')[:count]
    user_statistics_day_data = UserStatisticsDaySerializer(user_statistics_day, many=True).data
    return user_statistics_day_data


def get_users_number():
    count = UserAsset.objects.all().count()
    return count

def generate_random_username(length=8):
    import string, secrets
    # 定义允许的字符（大/小写字母和数字）
    allowed_chars = string.ascii_letters + string.digits
    # 生成随机用户名
    username = ''.join(secrets.choice(allowed_chars) for _ in range(length))
    return username


def register_by_email(request, email, name, verify_code, password, ref_code):
    passed = not get_enable_rule_exam()
    domain = request.META.get('HTTP_X_HOST', '')
    reg_ip = get_client_ip(request)    

    if not email or not verify_code or not password:
        return RespCode.InvalidParams.value, _('API request failed due to missing parameters.')
    if len(password) > 16 or len(password) < 8:
        return RespCode.InvalidParams.value, _('Error: Password must be 8 to 16 characters.')       

    # 用户名长度校验（最大 10 个字符）
    if name and len(name) > 10:
        return RespCode.InvalidParams.value, _('Username length cannot exceed 10 characters.')
    
    if not name:
        name = generate_random_username()

    # 检查IP频率限制
    ip_key = f'reg_ip:{reg_ip}'
    reg_count = cache.get(ip_key, 0)
    if reg_count >= 10:
        return RespCode.BadRequest.value, _('High registration activity detected from this IP. Please try again later.')
    cache.set(ip_key, reg_count + 1, timeout=3600)

    # 验证码尝试次数限制
    attempt_key = f'verify_code_attempts:{email}'
    attempts = cache.get(attempt_key, 0)
    if attempts >= 5:
        return RespCode.BadRequest.value, _('Too many verification code attempts. Please try again later.')
    cache.set(attempt_key, attempts + 1, timeout=600)

    code = verify_code.upper()
    key = f'verify_code:{email}'
    cached_data = cache.get(key)
    if not cached_data:
        return RespCode.InvalidParams.value, _('Verification code is expired. Please try again with a new one.')

    # 验证码字段名称已改为verify_code
    if cached_data['verify_code'].upper() != code:
        return RespCode.BadRequest.value, _('Verification failed: Incorrect code.')

    # 1.1 Token 在发送验证码时已校验并标记，无需再次验证

    # 4. 检查邮箱是否已注册
    user = USER.objects.filter(email=email).first()
    if user:
        return RespCode.BadRequest.value, _('Email already registered')

    # 创建用户
    user = USER.objects.create_user(
        username=email,
        email=email,
        password=password,
        domain=domain,
        reg_ip=reg_ip
    )
    user.set_password(password)
    user.profile.nickname = name
    user.profile.avatar = '/media/user/default_avatar.jpg'
    user.profile.save(update_fields=['nickname', 'avatar'])
    # user.steam.avatar = '/media/user/default_avatar.jpg'
    # user.steam.avatarmedium = '/media/user/default_avatar.jpg'
    # user.steam.avatarfull = '/media/user/default_avatar.jpg'
    # user.steam.personaname = name
    # user.steam.save(update_fields=['avatar', 'avatarmedium', 'avatarfull', 'personaname'])
    user.save()

    bind_promotion(user, ref_code)
    register_coins = get_register_coins()
    box_chance_type = get_register_box_chance_type()
    user.update_balance(register_coins, "注册赠送")
    user.update_box_chance_type(box_chance_type)
    user.update_exams_passed(passed)
    user.ban_users_with_same_ip()

    _logger.info(f"User {name} registered by email: {email}")
    login(request, user, backend='django.contrib.auth.backends.ModelBackend')

    update_base_count('users_base_count', 1)

    return RespCode.Succeed.value, {}

def is_valid_email(email):
    # Split the email into local and domain parts
    try:
        local_part, domain_part = email.split('@')
    except ValueError:
        # If there is no '@' or more than one '@', it's invalid
        return False

    # Check if the local part contains a dot
    if '.' in local_part:
        return False

    # Basic regex for validating the domain part
    domain_regex = r'^[a-zA-Z0-9-]+\.[a-zA-Z]{2,}$'
    if not re.match(domain_regex, domain_part):
        return False

    return True

# 注意：为保持向后兼容，新增参数都放在末尾并提供默认值
# code_len        : 验证码长度，默认 6 位
# subject         : 邮件标题，默认固定标题
# content_tpl     : 邮件内容模板，其中 {code} 会被替换为验证码
# cache_expire    : 验证码缓存有效期（秒），默认 5 分钟
# extra_cache     : 额外需要写入缓存的键值对(dict)，可为空

def send_email_code(
    request,
    email,
    type,
    token,
    code_len: int = 6,
    subject: str = 'CSGOSKINS邮箱验证码',
    content_tpl: str = '您的验证码是：{code}',
    cache_expire: int = 5 * 60,
    extra_cache: Optional[dict] = None
):
    """
    发送邮箱验证码，并将verify_code与token、ip、user_agent等信息一起缓存
    :param email: 邮箱地址
    :param type: 验证码类型（1-注册，2-找回密码）
    :param ip: 用户 IP 地址 (用于记录)
    :param token: 前端传入的token，用于后续注册校验
    :param user_agent: 用户浏览器标识
    :return: (code, resp)
    """
    email = _normalize_email_addr(email)
    ip = get_client_ip(request)
    user_agent = request.META.get('HTTP_USER_AGENT', '')
    domain = request.META.get('HTTP_X_HOST', '')
    # 1. 验证类型参数 1 注册 2 找回密码
    if not isinstance(type, int) or type not in [1, 2]:
        return RespCode.InvalidParams.value, _("Invalid verification type")    
    
    # 1.1 Token 在发送验证码时已校验并标记，无需再次验证

    # 4. 检查邮件服务配置
    email_server = MailServer._check_config()
    if not email_server:
        return RespCode.BusinessError.value, _("Email service not configured")

    # 5. 根据类型验证用户状态
    user_exists = USER.objects.filter(email=email).exists()
    if type == 1 and user_exists:  # 注册
        return RespCode.InvalidParams.value, _("Email already registered")
    elif type == 2 and not user_exists:  # 找回密码
        return RespCode.BusinessError.value, _("User not found")

    # 6. 生成验证码（支持自定义长度）
    if code_len < 4:
        code_len = 4  # 最小 4 位，避免过短
    verify_code = ''.join(secrets.choice(string.digits) for _ in range(code_len))

    # 7. 发送邮件
    try:
        mail_title = subject
        mail_content = content_tpl.format(code=verify_code)
        mail_server_response = MailServer.send_email(mail_title, mail_content, email)

        if mail_server_response == status.HTTP_200_OK:
            # 记录发送历史
            PhoneCodeRecord.objects.create(
                phone=email,  # 使用email作为phone字段的值
                code=verify_code,
                ip=ip,
                domain=domain
            )
            # 将验证码和关联信息（token、ip、user_agent、时间戳）一起缓存
            cache_key = f"verify_code:{email}"
            if cache.get(cache_key):
                return RespCode.BadRequest.value, _('Please wait 5 minutes before requesting another code.')

            cache_data = {
                'verify_code': verify_code,
                'token': token,
                'ip': ip,
                'user_agent': user_agent,
                'created_at': time.time()
            }
            if extra_cache and isinstance(extra_cache, dict):
                cache_data.update(extra_cache)

            cache.set(cache_key, cache_data, timeout=cache_expire)
            return RespCode.Succeed.value, {}
        else:
            error_message = _("Failed to send verification code")
            if mail_server_response == status.HTTP_404_NOT_FOUND:
                error_message = _("Email service not available")
            _logger.error(f"Email send failed for {email}: {mail_server_response}")
            return RespCode.BusinessError.value, error_message
    except Exception as e:
        _logger.exception(f"Error sending email to {email}: {str(e)}")
        return RespCode.BusinessError.value, _('System error while sending verification code.')
    
        



def login_by_email(request, email, password, captcha, uuid):
    """
    处理邮箱+密码方式登录，并校验图形验证码

    :param request: HttpRequest 对象
    :param email: 邮箱
    :param password: 密码
    :param login_ip: 登录 IP
    :param captcha: 用户输入的验证码
    :param uuid: 获取验证码时返回的 uuid，用于从缓存中获取正确验证码
    """
    try:
        login_ip = get_client_ip(request)
        domain = request.META.get('HTTP_X_HOST', '')    

        # 1. 基本参数检查
        if not captcha:
            return RespCode.InvalidParams.value, _('CAPTCHA cannot be empty')
        if not uuid:
            return RespCode.InvalidParams.value, _('Missing CAPTCHA identifier')
        if not email or not validate_email(email):
            return RespCode.InvalidParams.value, _('Invalid email address')
        if not password:
            return RespCode.InvalidParams.value, _('Password cannot be empty')

        # 2. 校验验证码
        captcha_code, message = verify_captcha(captcha, uuid)
        if captcha_code != RespCode.Succeed.value:
            return captcha_code, message

        # 3. IP 黑名单检查
        blacklisted_ips = AccessControl.objects.filter(enable=True).values_list('ip', flat=True)
        if ip_in_blacklist(login_ip, blacklisted_ips):
            blacklisted_entry = AccessControl.objects.filter(enable=True, ip__in=[login_ip]).first()
            message = blacklisted_entry.message if blacklisted_entry else 'System Maintenance'
            return RespCode.BadRequest.value, message

        # 4. 用户验证
        user = USER.objects.filter(email=email).first()
        if not user:
            return RespCode.InvalidParams.value, _('This account does not exist.')
        if not user.is_active:
            return RespCode.InvalidParams.value, user.ban_active_reasons

        # 5. 密码验证
        if check_password(password, user.password):
            login(request, user, backend='django.contrib.auth.backends.ModelBackend')
            user.login_ip = login_ip
            user.login_domain = domain
            user.login_time = timezone.now()
            user.save()
            return RespCode.Succeed.value, _('Login successful')
        else:
            return RespCode.BadRequest.value, _('Invalid username or password. Please try again.')
    except Exception as e:
        _logger.exception(e)
        return RespCode.Exception.value, _('Exception')


def reset_pwd_by_email(email, password, verify_code):
    email = _normalize_email_addr(email)
    """
    重置密码通过邮箱和验证码
    :param email: 用户邮箱
    :param password: 新密码
    :param verify_code: 用户输入的验证码
    :return: 状态码和响应信息
    """
    print('email:'+str(email), 'password:'+str(password), 'verify_code:'+str(verify_code))
    # 参数校验
    if not email or not password or not verify_code:
        return RespCode.InvalidParams.value, _('API request failed due to missing parameters.')

    if not validate_email(email):
        return RespCode.InvalidParams.value, _('Invalid email format')

    # 校验密码长度
    if not (8 <= len(password) <= 16):
        return RespCode.InvalidParams.value, _('Error: Password must be 8 to 16 characters.')

    # 查询用户是否存在
    user = USER.objects.filter(email=email).first()
    if not user:
        return RespCode.InvalidParams.value, _('This account does not exist.')

    # 验证码校验
    cache_key = f'verify_code:{email}'
    cache_val = cache.get(cache_key)
    print('cache_key:'+str(cache_key))
    print('cache_val:'+str(cache_val))
    if not cache_val:
        return RespCode.InvalidParams.value, _('Verification code is expired. Please try again with a new one.')

    # 兼容 send_email_code 中存储的字典格式和字符串格式
    if isinstance(cache_val, dict):
        cache_code = cache_val.get('verify_code')
    else:
        cache_code = cache_val

    if str(cache_code).strip().upper() != str(verify_code).strip().upper():
        return RespCode.BadRequest.value, _('Verification failed: Incorrect code.')

    # 不删除验证码，让后续重置密码接口继续校验

    # 更新用户密码
    user.password = make_password(password)
    user.save()

    return RespCode.Succeed.value, _('Password reset successful')


def validate_email(email):
    """
    验证邮箱格式
    :param email: 待验证的邮箱地址
    :return: bool 是否有效
    """
    email_regex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(email_regex, email))

def check_file(image):
    name = image.name
    suffix_list = ['jpeg', 'jpg', 'png', 'gif']
    if name.split('.')[-1] not in suffix_list:
        return False
    return True

def set_personaname(user, name):
    user = USER.objects.filter(id=user.id).first()
    if not user:
        return RespCode.InvalidParams.value, _("This account does not exist.")
    if user.extra.ban_rename:
        return RespCode.InvalidParams.value, _("You are not allowed to change your nickname")
    if len(name) > 16 or len(name) < 1:
        return RespCode.InvalidParams.value, _("Username must be 2–16 characters. Please adjust and try again.")
    
    name = escape(name)
    user.steam.personaname = name
    user.steam.save()
    return RespCode.Succeed.value, {'personaname': name}


def set_avatar(user, image):
    if not user:
        return RespCode.BadRequest.value, _("Please log in first")
    if user.extra.ban_avatar:
        return RespCode.BadRequest.value, _("You are not allowed to change your avatar")
    if not image:
        return RespCode.BadRequest.value, _("Image data is empty")
    if not check_file(image):
        return RespCode.BadRequest.value, _("Only jpeg, jpg, and png files are supported")
    
    try:
        avatar_url = upload_to_oss(image, 'avatars')
        userprofile = UserProfile.objects.filter(user=user).first()
        if not userprofile:
            return RespCode.BadRequest.value, _("User profile does not exist")
        
        userprofile.avatar = avatar_url
        userprofile.save()
        
        user.steam.avatar = avatar_url
        user.steam.avatarmedium = avatar_url
        user.steam.avatarfull = avatar_url
        user.steam.save()
        
        return RespCode.Succeed.value, {'avatar_url': avatar_url}

    except Exception as e:
        _logger.exception("Error uploading avatar to OSS: %s", e)
        return RespCode.Exception.value, _("Error occurred while uploading avatar")


def upload_to_oss(file_obj, directory):
    # 获取当前年月
    now = datetime.now()
    year_month = now.strftime('%Y%m')
    # 生成唯一文件名
    file_extension = file_obj.name.split('.')[-1]
    unique_filename = f"{uuid.uuid4()}.{file_extension}"

    # 生成带有年月目录的文件路径
    file_name = f"{directory}/{year_month}/{unique_filename}"
    bucket = oss2.Bucket(
        oss2.Auth(settings.ALIYUN_OSS_ACCESS_KEY_ID, settings.ALIYUN_OSS_ACCESS_KEY_SECRET),
        settings.ALIYUN_OSS_ENDPOINT,
        settings.ALIYUN_OSS_BUCKET_NAME
    )
    bucket.put_object(file_name, file_obj)
    # 使用相对路径
    return file_name
    #return f"https://{settings.ALIYUN_OSS_BUCKET_NAME}.{settings.ALIYUN_OSS_ENDPOINT}/{file_name}"



def send_sms_code(request, phone, type, token):
    """
    发送手机验证码
    :param phone: 手机号
    :param type: 验证码类型（1-注册，2-找回密码）
    :param ip: 用户 IP 地址 (仅用于记录)
    :param token: 前端传入的token，用于后续注册校验
    :param user_agent: 用户浏览器标识
    :return: 验证码发送结果
    """
    reg_ip = get_client_ip(request)
    user_agent = request.META.get('HTTP_USER_AGENT', '')
    domain = request.META.get('HTTP_X_HOST', '')
    
    # 1. 验证类型参数
    if not isinstance(type, int) or type not in [1, 2]:
        return RespCode.InvalidParams.value, _("Invalid verification type")

    # 1.1 Token 校验（必须由 GetTokenView 获取的 token）
    if not token:
        return RespCode.BadRequest.value, _('Token is required')

    token_key = f"sms_token:{token}"
    token_data = cache.get(token_key)
    if not token_data:
        return RespCode.BadRequest.value, _('Invalid or expired token')
    if token_data.get('used'):
        return RespCode.BadRequest.value, _('Token already used')

    # 校验 token 绑定的环境信息
    if (token_data.get('ip') != ip or
            token_data.get('user_agent') != user_agent or
            time.time() - token_data.get('created_at', 0) > 900):
        return RespCode.BadRequest.value, _('Token validation failed')

    # 标记 token 为已使用
    token_data['used'] = True
    cache.set(token_key, token_data, timeout=900)

    # 2. 检查短信服务配置
    sms_server = SMService.check_config()
    if not sms_server:
        return RespCode.BusinessError.value, _("SMS service not configured")

    # 3. 检查验证码发送频率（5 分钟一次）
    cache_key = f'verify_code:{phone}'
    if cache.get(cache_key):
        return RespCode.BadRequest.value, _('Please wait 5 minutes before requesting another code.')

    # 4. 根据类型验证用户状态
    user_exists = USER.objects.filter(phone=phone).exists()
    if type == 1 and user_exists:  # 注册
        return RespCode.InvalidParams.value, _("Phone number already registered")
    elif type == 2 and not user_exists:  # 找回密码
        return RespCode.BusinessError.value, _("User not found")

    # 5. 生成验证码
    verify_code = ''.join(secrets.choice(string.digits) for _ in range(4))

    # 6. 发送短信
    try:
        response = sms_server('', phone, verify_code)
        if response.get('status'):
            # 记录发送历史
            PhoneCodeRecord.objects.create(
                phone=phone,
                code=verify_code,
                ip=reg_ip,
                domain=domain
            )
            # 将验证码与token、ip、user_agent、created_at一起缓存
            cache.set(cache_key, {
                'verify_code': verify_code,
                'token': token,
                'ip': reg_ip,
                'user_agent': user_agent,
                'created_at': time.time()
            }, timeout=5 * 60)
            return RespCode.Succeed.value, {}
        else:
            _logger.error(f"SMS send failed for phone {phone}: {response}")
            return RespCode.BusinessError.value, _('Failed to send verification code.')
    except Exception as e:
        _logger.exception(f"Error sending SMS to {phone}: {str(e)}")
        return RespCode.BusinessError.value, _('System error while sending verification code.')


    
    


def update_base_count(key, count):
    # 尝试更新指定 key 的 SiteConfig 对象的 value 字段
    try:
        # 使用 F() 表达式直接在数据库层面上增加 count 值，避免了先查询再保存的额外操作
        SiteConfig.objects.filter(key=key).update(value=F('value') + count)
    except SiteConfig.DoesNotExist:
        # 处理键不存在的情况
        pass


# def get_client_ip(request):
#     x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
#     if x_forwarded_for:
#         ip = x_forwarded_for.split(',')[0]
#     else:
#         ip = request.META.get('REMOTE_ADDR')
#     return ip

def get_client_ip(request):
    """
    获取客户端真实IP地址
    优先从 X-Forwarded-For 获取，如果不存在则从 X-Real-IP 获取
    最后才使用 REMOTE_ADDR
    """
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        # 获取列表中的第一个IP，即真实客户端IP
        ip = x_forwarded_for.split(',')[0].strip()
    else:
        # 尝试从X-Real-IP获取
        ip = request.META.get('HTTP_X_REAL_IP')
        if not ip:
            # 最后才使用REMOTE_ADDR
            ip = request.META.get('REMOTE_ADDR')
    return ip

def check_user_exam(user):
    extra = UserExtra.objects.filter(user=user).first()
    if extra:
        items = {
            'exam_passed': extra.exam_passed,
            'exam_time': extra.exam_time

        }
        resp = {
            'items': items
        }
        return RespCode.Succeed.value, resp
    return RespCode.BadRequest.value, _("User does not exist")

def set_user_exam(user, exam_passed):
    try:
        # 确定返回的信息
        msg = _("Congratulations on passing the exam") if exam_passed else _("Unfortunately, you did not pass the exam. Please retake it.")

        # 使用事务确保数据一致性
        with transaction.atomic():
            # 获取或创建 UserExtra 对象
            extra, created = UserExtra.objects.get_or_create(user=user)
            extra.exam_passed = exam_passed
            extra.exam_time = timezone.now()
            extra.save()

        return RespCode.Succeed.value, msg
    except UserExtra.DoesNotExist:
        return RespCode.BadRequest.value, _("User does not exist")
    except Exception as e:
        _logger.exception("Error setting user exam: %s", e)
        return RespCode.Exception.value, _("An error occurred")
    
def request_reset_email(email, code):
    email = _normalize_email_addr(email)
    try:
        # 检查邮箱是否存在
        user = USER.objects.filter(email=email).first()
        if not user:
            return RespCode.InvalidParams.value, _("This email does not exist")
        # 检查验证码是否正确
        cache_key = f'verify_code:{email}'
        cache_val = cache.get(cache_key)
        print('cache_val:'+str(cache_val))
        print('cache_key:'+str(cache_key))
        if not cache_val:
            return RespCode.InvalidParams.value, _("Verification code is expired. Please try again with a new one.")

        # 兼容 send_email_code 中存储的字典格式和字符串格式
        if isinstance(cache_val, dict):
            cache_code = cache_val.get('verify_code')
        else:
            cache_code = cache_val

        if str(cache_code).strip().upper() != str(code).strip().upper():
            return RespCode.BadRequest.value, _("Verification failed: Incorrect code.")
        
        # 不删除验证码，让后续重置密码接口继续校验
        
        # 生成token
        token = secrets.token_urlsafe(32)
        token_key = f'reset_email_token:{token}'
        cache.set(token_key, {
            'email': email,
            'created_at': time.time()
        }, timeout=900)  # 15分钟有效期
        
        
        return RespCode.Succeed.value, {'token': token}
        
    except Exception as e:
        _logger.exception("Error requesting reset email: %s", e)
        return RespCode.Exception.value, _("An error occurred")
        
        
        
        
        

def generate_token(request, type, contact, captcha, uuid):
    """
    生成用于验证的令牌，并进行相关验证
    
    :param type: 验证类型 ('sms' 或 'email')
    :param contact: 联系方式 (手机号或邮箱)
    :param code: 用户输入的验证码
    :param uuid: 验证码的标识符
    :return: (code, resp, message) - 状态码、响应数据和消息
    """
    try:
        reg_ip = get_client_ip(request)
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        domain = request.META.get('HTTP_X_HOST', '')

       
        # 1. 基本参数验证
        if not type or type not in ['sms', 'email']:
            return RespCode.InvalidParams.value, {}, _("Invalid verification type")
        if not contact:
            return RespCode.InvalidParams.value, {}, _("Contact information cannot be empty")

        # 2. 验证格式
        if type == 'sms' and not validate_phone_number(contact):
            return RespCode.InvalidParams.value, {}, _("Invalid phone number format")
        elif type == 'email' and not validate_email(contact):
            return RespCode.InvalidParams.value, {}, _("Invalid email format")
            
        # 3. 验证码校验 (如果提供了code和uuid)
        if captcha is not None and uuid is not None:
            captcha_code, message = verify_captcha(captcha, uuid)
            if captcha_code != RespCode.Succeed.value:
                return captcha_code, {}, message

        # 4. User-Agent 检查
        if not user_agent or len(user_agent) < 20:
            return RespCode.BadRequest.value, {}, _('Invalid client')

        # 5. 域名验证
        if get_domain_verify():
            site = SEO.objects.filter(url=domain, enable=True)
            if not site.exists():
                return RespCode.BadRequest.value, {}, _('Website not allowed')

        # 6. IP 黑名单检查  
        if get_access_control_ip():
            blacklisted_ips = AccessControl.objects.filter(enable=True).values_list('ip', flat=True)
            if ip_in_blacklist(reg_ip, blacklisted_ips):
                blacklisted_entry = AccessControl.objects.filter(enable=True, ip__in=[reg_ip]).first()
                message = blacklisted_entry.message if blacklisted_entry else _('Under maintenance')
                return RespCode.BadRequest.value, {}, message

        # 7. IP 每日令牌限制
        if get_access_control_token():
            ip_tokens_key = f"ip_daily_tokens:{reg_ip}"
            ip_tokens_count = cache.get(ip_tokens_key, 0)
            if ip_tokens_count >= int(get_access_control_token_limit()):
                return RespCode.TooManyRequests.value, {}, _("Exceeded daily token limit")

            # 记录新的请求次数
            cache.set(ip_tokens_key, ip_tokens_count + 1, timeout=86400)

        # 8. IP 短期内请求不同联系方式限制
        if get_access_control_ip():
            ip_contacts_key = f"ip_contacts:{reg_ip}"
            ip_contacts = cache.get(ip_contacts_key, set())
            if contact not in ip_contacts:
                ip_contacts.add(contact)
                if len(ip_contacts) > 5:  # 同一IP 10分钟内最多请求5个不同联系方式
                    return RespCode.TooManyRequests.value, {}, _("Too many requests from the same IP for different contact methods")
            cache.set(ip_contacts_key, ip_contacts, timeout=600)

        # 9. 生成并存储令牌
        token = secrets.token_urlsafe(32)
        email = _normalize_email_addr(contact)
        token_key = f"{type}_token:{token}"
        token_data = {
            'ip': reg_ip,
            'user_agent': user_agent,
            'created_at': time.time(),
            'used': False
        }
        cache.set(token_key, token_data, timeout=900)  # 15分钟有效期

        resp ={
            'items':{
                'token': token               
            }
        }

        return RespCode.Succeed.value, resp, _('Token generated successfully')

    except Exception as e:
        _logger.exception(f'Error generating token: {str(e)}')
        return RespCode.Exception.value, {}, _('An error occurred')
        
def validate_phone_number(phone):
    """
    验证电话号码是否符合中国大陆格式。
    :param phone: 要验证的电话号码
    :return: 如果电话号码有效，返回True；否则返回False。
    """
    phone_regex = r'^1[3-9]\d{9}$'
    return bool(re.match(phone_regex, phone))

def verify_captcha(captcha, uuid):
    """
    验证图形验证码
    :param captcha: 用户输入的验证码
    :param uuid: 验证码的标识符
    :return: (code, message) - 验证结果的状态码和消息
    """
    if not uuid or not captcha:
        return RespCode.InvalidParams.value, _('Verification code parameters are incomplete')

    # 从缓存获取验证码
    cache_code = cache.get(uuid)
    if not cache_code:
        return RespCode.InvalidParams.value, _('Verification code expired. Please refresh.')

    # 验证码比较 (大小写不敏感)
    if str(cache_code).strip().upper() != str(captcha).strip().upper():
        return RespCode.BadRequest.value, _('Verification failed: Incorrect code.')

    # 验证通过后删除缓存，避免重复使用
    cache.delete(uuid)
    return RespCode.Succeed.value, _('Verification successful')

def ip_in_blacklist(ip, blacklist):
    """
    Check if the IP address matches any of the blacklisted IP ranges or single IPs.
    """
    try:
        ip_addr = ipaddress.ip_address(ip)
    except ValueError:
        return False

    # Handle IPv4-mapped IPv6 addresses
    if ip_addr.version == 6 and ip_addr.ipv4_mapped:
        ip_addr = ip_addr.ipv4_mapped

    for blacklisted_ip in blacklist:
        net = parse_blacklisted_ip(blacklisted_ip)
        if net and ip_addr in net:
            return True

    return False

@lru_cache(maxsize=128)
def parse_blacklisted_ip(blacklisted_ip):
    if '*' in blacklisted_ip:
        # Convert wildcard to network range
        if ':' in blacklisted_ip:  # IPv6
            blacklisted_ip = blacklisted_ip.replace('*', '0')
            return ipaddress.ip_network(blacklisted_ip + '/64', strict=False)
        else:  # IPv4
            blacklisted_ip = blacklisted_ip.replace('*', '0')
            return ipaddress.ip_network(blacklisted_ip + '/24', strict=False)
    else:
        try:
            return ipaddress.ip_network(blacklisted_ip, strict=False)
        except ValueError:
            return None

def _normalize_email_addr(email: str) -> str:
    """统一邮箱地址用于缓存键：去除首尾空格并转小写"""
    return email.strip().lower() if email else email