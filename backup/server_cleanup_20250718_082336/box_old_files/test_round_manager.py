#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
轮次管理器单元测试
作者: Backend Engineering Team
日期: 2025-07-05
目的: 测试轮次管理器的功能，确保修复轮次硬编码问题
"""

import unittest
import time
from unittest.mock import patch, MagicMock
from django.test import TestCase
from django.core.cache import cache

# 导入测试目标
from box.round_manager import BattleRoundManager, BattleRoundValidator


class TestBattleRoundManager(TestCase):
    """轮次管理器单元测试"""
    
    def setUp(self):
        """测试前准备"""
        self.room_uid = "test_room_123"
        self.manager = BattleRoundManager(self.room_uid)
        # 清理缓存
        cache.clear()
    
    def tearDown(self):
        """测试后清理"""
        cache.clear()
    
    @patch('box.round_manager.CaseRoom.objects')
    @patch('box.round_manager.CaseRoomRound.objects')
    def test_get_current_round_default(self, mock_rounds, mock_rooms):
        """测试默认轮次为1"""
        # 模拟房间存在
        mock_room = MagicMock()
        mock_rooms.filter.return_value.first.return_value = mock_room
        
        # 模拟没有已开启的轮次
        mock_rounds.filter.return_value.count.return_value = 0
        
        round_num = self.manager.get_current_round()
        self.assertEqual(round_num, 1)
    
    @patch('box.round_manager.CaseRoom.objects')  
    @patch('box.round_manager.CaseRoomRound.objects')
    def test_get_current_round_with_opened_rounds(self, mock_rounds, mock_rooms):
        """测试有已开启轮次的情况"""
        # 模拟房间存在
        mock_room = MagicMock()
        mock_rooms.filter.return_value.first.return_value = mock_room
        
        # 模拟有2个已开启的轮次
        mock_rounds.filter.return_value.count.return_value = 2
        
        round_num = self.manager.get_current_round()
        self.assertEqual(round_num, 3)  # 2 + 1 = 3
    
    @patch('box.round_manager.CaseRoom.objects')
    def test_get_total_rounds(self, mock_rooms):
        """测试获取总轮次数"""
        # 模拟房间有round_count字段
        mock_room = MagicMock()
        mock_room.round_count = 5
        mock_room.max_joiner = 4
        mock_rooms.filter.return_value.first.return_value = mock_room
        
        total = self.manager.get_total_rounds()
        self.assertEqual(total, 5)
    
    @patch('box.round_manager.CaseRoom.objects')
    def test_get_total_rounds_fallback(self, mock_rooms):
        """测试总轮次数回退到max_joiner"""
        # 模拟房间没有round_count字段
        mock_room = MagicMock()
        mock_room.max_joiner = 4
        del mock_room.round_count  # 删除round_count属性
        mock_rooms.filter.return_value.first.return_value = mock_room
        
        total = self.manager.get_total_rounds()
        self.assertEqual(total, 4)
    
    def test_advance_round_success(self):
        """测试成功推进轮次"""
        # 设置初始轮次
        cache.set(self.manager.redis_key, 1, 3600)
        
        with patch.object(self.manager, 'get_total_rounds', return_value=3):
            next_round = self.manager.advance_round()
            self.assertEqual(next_round, 2)
            
            # 验证缓存更新
            cached_round = cache.get(self.manager.redis_key)
            self.assertEqual(cached_round, 2)
    
    def test_advance_round_exceeds_total(self):
        """测试轮次超出总数时抛出异常"""
        # 设置轮次为总数
        cache.set(self.manager.redis_key, 3, 3600)
        
        with patch.object(self.manager, 'get_total_rounds', return_value=3):
            with self.assertRaises(ValueError) as context:
                self.manager.advance_round()
            
            self.assertIn("轮次超出限制", str(context.exception))
    
    def test_validate_round_valid(self):
        """测试有效轮次验证"""
        with patch.object(self.manager, 'get_total_rounds', return_value=3):
            # 测试有效轮次
            self.assertTrue(self.manager.validate_round(1))
            self.assertTrue(self.manager.validate_round(2))
            self.assertTrue(self.manager.validate_round(3))
    
    def test_validate_round_invalid_type(self):
        """测试无效类型轮次验证"""
        with patch.object(self.manager, 'get_total_rounds', return_value=3):
            with self.assertRaises(ValueError) as context:
                self.manager.validate_round("1")  # 字符串
            
            self.assertIn("轮次必须是整数", str(context.exception))
    
    def test_validate_round_out_of_range(self):
        """测试超出范围的轮次验证"""
        with patch.object(self.manager, 'get_total_rounds', return_value=3):
            # 测试小于1的轮次
            with self.assertRaises(ValueError) as context:
                self.manager.validate_round(0)
            self.assertIn("轮次不能小于1", str(context.exception))
            
            # 测试大于总数的轮次
            with self.assertRaises(ValueError) as context:
                self.manager.validate_round(4)
            self.assertIn("轮次超出总数", str(context.exception))
    
    def test_reset_rounds(self):
        """测试重置轮次"""
        # 设置非1的轮次
        cache.set(self.manager.redis_key, 3, 3600)
        
        self.manager.reset_rounds()
        
        # 验证重置为1
        cached_round = cache.get(self.manager.redis_key)
        self.assertEqual(cached_round, 1)
    
    def test_get_round_status(self):
        """测试获取轮次状态"""
        with patch.object(self.manager, 'get_current_round', return_value=2):
            with patch.object(self.manager, 'get_total_rounds', return_value=3):
                status = self.manager.get_round_status()
                
                expected = {
                    'current_round': 2,
                    'total_rounds': 3,
                    'is_first_round': False,
                    'is_last_round': False,
                    'progress_percent': 66.67,  # (2/3)*100 ≈ 66.67
                    'remaining_rounds': 1
                }
                
                self.assertEqual(status['current_round'], expected['current_round'])
                self.assertEqual(status['total_rounds'], expected['total_rounds'])
                self.assertEqual(status['is_first_round'], expected['is_first_round'])
                self.assertEqual(status['is_last_round'], expected['is_last_round'])
                self.assertAlmostEqual(status['progress_percent'], expected['progress_percent'], places=1)
                self.assertEqual(status['remaining_rounds'], expected['remaining_rounds'])
    
    def test_cleanup_cache(self):
        """测试清理缓存"""
        # 设置缓存值
        cache.set(self.manager.redis_key, 2, 3600)
        
        self.manager.cleanup_cache()
        
        # 验证缓存被清理
        cached_round = cache.get(self.manager.redis_key)
        self.assertIsNone(cached_round)


class TestBattleRoundValidator(TestCase):
    """轮次验证器单元测试"""
    
    def setUp(self):
        """测试前准备"""
        self.room_uid = "test_room_456"
        cache.clear()
    
    def tearDown(self):
        """测试后清理"""
        cache.clear()
    
    @patch('box.round_manager.BattleRoundManager')
    def test_validate_message_round_success(self, mock_manager_class):
        """测试消息轮次验证成功"""
        # 模拟管理器返回当前轮次为2
        mock_manager = MagicMock()
        mock_manager.get_current_round.return_value = 2
        mock_manager_class.return_value = mock_manager
        
        result = BattleRoundValidator.validate_message_round(
            self.room_uid, 'round_start', 2
        )
        
        self.assertTrue(result)
    
    @patch('box.round_manager.BattleRoundManager')
    def test_validate_message_round_mismatch(self, mock_manager_class):
        """测试消息轮次验证不匹配"""
        # 模拟管理器返回当前轮次为2
        mock_manager = MagicMock()
        mock_manager.get_current_round.return_value = 2
        mock_manager_class.return_value = mock_manager
        
        result = BattleRoundValidator.validate_message_round(
            self.room_uid, 'round_start', 1  # 期望轮次与实际不匹配
        )
        
        self.assertFalse(result)
    
    @patch('box.round_manager.BattleRoundManager')
    def test_get_safe_round_success(self, mock_manager_class):
        """测试安全获取轮次成功"""
        # 模拟管理器正常返回轮次
        mock_manager = MagicMock()
        mock_manager.get_current_round.return_value = 3
        mock_manager_class.return_value = mock_manager
        
        result = BattleRoundValidator.get_safe_round(self.room_uid)
        
        self.assertEqual(result, 3)
    
    @patch('box.round_manager.BattleRoundManager')
    def test_get_safe_round_fallback(self, mock_manager_class):
        """测试安全获取轮次失败时返回fallback"""
        # 模拟管理器抛出异常
        mock_manager = MagicMock()
        mock_manager.get_current_round.side_effect = Exception("测试异常")
        mock_manager_class.return_value = mock_manager
        
        result = BattleRoundValidator.get_safe_round(self.room_uid, fallback_round=5)
        
        self.assertEqual(result, 5)


class TestRoundManagerIntegration(TestCase):
    """轮次管理器集成测试"""
    
    def test_full_round_lifecycle(self):
        """测试完整的轮次生命周期"""
        room_uid = "integration_test_room"
        manager = BattleRoundManager(room_uid)
        
        try:
            # 1. 重置轮次
            manager.reset_rounds()
            current = manager.get_current_round()
            self.assertEqual(current, 1)
            
            # 2. 验证初始状态
            status = manager.get_round_status()
            self.assertTrue(status['is_first_round'])
            self.assertFalse(status['is_last_round'])
            
            # 3. 验证当前轮次
            self.assertTrue(manager.validate_round(current))
            
            # 4. 推进轮次（模拟总轮次为3）
            with patch.object(manager, 'get_total_rounds', return_value=3):
                next_round = manager.advance_round()
                self.assertEqual(next_round, 2)
                
                # 再次推进
                next_round = manager.advance_round()
                self.assertEqual(next_round, 3)
                
                # 检查是否为最后一轮
                status = manager.get_round_status()
                self.assertTrue(status['is_last_round'])
                
                # 尝试超出总轮次
                with self.assertRaises(ValueError):
                    manager.advance_round()
            
            # 5. 清理缓存
            manager.cleanup_cache()
            
        except Exception as e:
            self.fail(f"集成测试失败: {e}")


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
