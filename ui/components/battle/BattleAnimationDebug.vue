<template>
  <div class="battle-animation-debug">
    <!-- 组件内容占位 -->
    <p>BattleAnimationDebug 组件占位</p>
  </div>
</template>

<script setup lang="ts">
// 定义 props 和 emits
defineProps<{
  // 根据页面中的传递参数定义
}>()

// 定义事件
defineEmits<{
  // 根据页面中的事件定义
}>()
</script>

<style lang="scss" scoped>
.battle-animation-debug {
  background: rgba(0, 0, 0, 0.8);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  border: 1px solid var(--color-gray-600);
}

.debug-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-white);
  margin: 0 0 1rem 0;
}

.debug-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.debug-section {
  h4 {
    font-size: 1rem;
    font-weight: 500;
    color: var(--color-gray-300);
    margin: 0 0 0.5rem 0;
  }
}

.debug-data {
  background: var(--color-gray-900);
  border-radius: var(--radius-md);
  padding: 1rem;
  font-family: 'Monaco', '<PERSON><PERSON>', 'Ubuntu Mono', monospace;
  font-size: 0.75rem;
  color: var(--color-green-400);
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-all;
}

.debug-actions {
  display: flex;
  gap: 0.5rem;
}

.debug-button {
  padding: 0.5rem 1rem;
  background: var(--color-blue-600);
  color: var(--color-white);
  border: none;
  border-radius: var(--radius-md);
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: var(--color-blue-700);
    transform: translateY(-1px);
  }
}
</style> 