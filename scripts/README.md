# Scripts 目录说明

本目录包含了项目中使用的各种脚本工具，按功能分类组织，便于维护和使用。

## 目录结构

```
scripts/
├── debug/          # 调试脚本
├── fixes/          # 修复脚本
├── monitoring/     # 监控脚本
├── tests/          # 测试脚本
├── tools/          # 工具脚本
└── diagnosis.sh    # 诊断脚本
```

## 各目录说明

### debug/ - 调试脚本
包含用于调试和问题排查的脚本。

- `debug_user_rooms.py` - 调试用户房间相关问题

### fixes/ - 修复脚本
包含用于修复数据库数据和系统状态的脚本。

*注意：大部分一次性修复脚本已在系统升级后清理，如需要可从版本控制历史中恢复。*

### monitoring/ - 监控脚本
包含用于系统监控和状态检查的脚本。

- `monitor_rooms.py` - 监控房间状态
- `battle_system_monitor.py` - **[新增]** 对战系统监控和诊断脚本

### tests/ - 测试脚本
包含各种测试和验证脚本。

- `test_battle_api_i18n.py` - 对战API国际化字段测试

*注意：大部分临时测试脚本已在系统升级后清理，保留核心功能测试脚本。*

### tools/ - 工具脚本
包含数据导入、初始化、同步等工具脚本。

- `import_csgoinfo.py` - 导入CSGO物品信息
- `initChargeLevel.py` - 初始化充值等级配置
- `initItemPrice.py` - 初始化物品价格
- `sync_csgoinfo.py` - 同步CSGO物品信息
- `sync_user_level.py` - 同步用户等级
- `temp_data.py` - 临时数据处理脚本

### 核心脚本
- `battle_system_monitor.py` - 对战系统监控和诊断
- `verify_enhanced_system.py` - 增强系统验证
- `translate_helper.py` - 翻译辅助脚本
- `deploy_battle_optimization.sh` - 对战系统优化部署和验证脚本

## 运行脚本的注意事项

### 1. 环境准备
所有Python脚本都需要在项目的虚拟环境中运行：

```bash
# 激活虚拟环境
cd /www/wwwroot/csgoskins.com.cn/server
source venv/bin/activate

# 或者使用conda环境（如果有的话）
conda activate steambase
```

### 2. Django环境
大部分脚本需要Django环境，运行前确保：

```bash
# 进入server目录
cd /www/wwwroot/csgoskins.com.cn/server

# 运行脚本
python ../scripts/tools/sync_user_level.py
```

### 3. 权限和备份
- **修复脚本**：运行前建议备份相关数据
- **数据导入脚本**：确保有足够的权限访问数据库
- **监控脚本**：可以设置为定时任务运行

### 4. 日志输出
运行脚本时建议重定向输出到日志文件：

```bash
python ../scripts/fixes/fix_stuck_rooms.py > /tmp/fix_stuck_rooms.log 2>&1
```

## 脚本维护规范

### 添加新脚本
1. 根据功能选择合适的子目录
2. 脚本文件名要有意义，使用下划线分隔
3. 在脚本开头添加注释说明用途、作者、创建时间
4. 更新对应子目录的README文档

### 脚本编写规范
1. 使用Python 3.6+语法
2. 包含必要的错误处理
3. 添加适当的日志输出
4. 对于数据修改操作，添加确认提示
5. 包含usage说明或help信息

### 删除废弃脚本
1. 确认脚本不再使用
2. 移动到 `deprecated/` 目录（如需要）
3. 更新相关文档

## 常用操作示例

### 运行测试脚本
```bash
cd /www/wwwroot/csgoskins.com.cn/server
python ../scripts/tests/test_battle_api_i18n.py
```

### 运行修复脚本
```bash
cd /www/wwwroot/csgoskins.com.cn/server
python ../scripts/fixes/fix_stuck_rooms.py
```

### 运行数据同步
```bash
cd /www/wwwroot/csgoskins.com.cn/server
python ../scripts/tools/sync_user_level.py
```

### 运行监控脚本
```bash
cd /www/wwwroot/csgoskins.com.cn/server
python ../scripts/monitoring/monitor_rooms.py
```

## ⚡ 对战系统优化脚本 (2025-07-15新增)

### 核心脚本

#### `battle_system_monitor.py` - 对战系统监控诊断
**位置**: `scripts/battle_system_monitor.py`

专门用于对战系统的监控和诊断：

```bash
# 运行系统诊断
python scripts/battle_system_monitor.py --mode diagnosis

# 运行系统监控
python scripts/battle_system_monitor.py --mode monitor
```

**主要功能**:
- 检查Python版本和异步支持
- 验证数据库和Redis连接
- 分析房间状态和数据一致性
- 提供详细的健康报告

#### `deploy_battle_optimization.sh` - 优化部署验证
**位置**: `scripts/deploy_battle_optimization.sh`

完整的对战系统优化部署和验证脚本：

```bash
# 运行完整部署验证
./scripts/deploy_battle_optimization.sh
```

**验证内容**:
- ✅ Python环境检查
- ✅ 优化文件完整性验证
- ✅ Django环境配置检查
- ✅ 系统健康状态检查
- ✅ 功能模块测试

#### `verify_enhanced_system.py` - 增强系统验证
**位置**: `scripts/verify_enhanced_system.py`

验证增强版对战系统的各个组件：

```bash
# 验证增强系统
python scripts/verify_enhanced_system.py
```

### 优化模块文件

以下文件已添加到 `server/box/` 目录：

- `compat_async.py` - Python 3.6异步兼容性处理
- `message_utils.py` - WebSocket消息处理和去重
- `system_fixes.py` - 系统修复工具集
- `enhanced_battle_system.py` - 增强版对战系统管理器
- `battle_config.py` - 配置管理
- `battle_system_integration.py` - 系统集成模块

### 使用建议

1. **首次部署**: 运行 `deploy_battle_optimization.sh` 进行完整验证
2. **日常监控**: 定期运行 `battle_system_monitor.py --mode monitor`
3. **问题诊断**: 出现问题时运行 `battle_system_monitor.py --mode diagnosis`
4. **系统验证**: 更新后运行 `verify_enhanced_system.py` 验证功能

### 3. 权限和备份
- 确保对相关文件和目录有足够的访问权限
- 运行脚本前备份重要数据
- 定期检查和清理日志文件
