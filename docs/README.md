# 📚 CSGO皮肤交易平台 - 文档中心

> 欢迎来到CSGO皮肤交易平台的完整文档中心！这里包含了项目的全面技术文档，涵盖前端、后端、API、部署等各个方面。

[![文档状态](https://img.shields.io/badge/docs-up--to--date-brightgreen.svg)](https://github.com/your-repo/docs)
[![最后更新](https://img.shields.io/badge/last%20update-2025--07--19-blue.svg)](https://github.com/your-repo/commits)
[![Vue.js](https://img.shields.io/badge/Vue.js-3.5+-brightgreen.svg)](https://vuejs.org)
[![Django](https://img.shields.io/badge/Django-4.2+-green.svg)](https://djangoproject.com)

## 🎯 文档概览

本文档中心为不同角色的用户提供了详细的指导：

- 🆕 **新用户** - 快速上手和基础概念
- 👨‍💻 **开发者** - 技术实现和开发指南
- 🔧 **运维人员** - 部署配置和系统维护
- 📊 **产品经理** - 功能说明和业务逻辑

## 📚 文档目录

### 🚀 新手入门
| 文档 | 描述 | 预计阅读时间 |
|------|------|-------------|
| [🚀 快速开始](guides/quickstart.md) | 5分钟快速体验平台功能 | 5分钟 |
| [🛠️ 环境搭建](deployment/setup.md) | 本地开发环境配置 | 15分钟 |
| [🐳 Docker部署](DOCKER_DEPLOYMENT.md) | 完整的Docker部署指南 | 20分钟 |
| [❓ 常见问题](guides/faq.md) | 新手常见问题解答 | 10分钟 |

### 🔌 API 文档
- [API 总览](api/overview.md) - API设计原则和通用规范
- [API字段命名规范](api/api-field-naming-standards.md) - 统一的字段命名标准
- [认证 API](api/authentication-api.md) - 用户注册、登录、Token管理
- [箱子 API](api/case-api.md) - 开箱、物品管理相关接口
- [对战 API](api/battle-api.md) - 对战房间、回合管理接口
- [对战 API v2](api/battle-api-v2.md) - 对战API最新版本
- [WebSocket API](api/websocket-api.md) - 实时通信接口文档

#### 模块API文档
- [代理系统 API](api/agent-api-analysis.md) - 代理商管理接口
- [B2C交易 API](api/b2ctrade-api-analysis.md) - B2C交易相关接口
- [盲盒系统 API](api/blindbox-api-analysis.md) - 盲盒功能接口
- [充值系统 API](api/charge-api-analysis.md) - 充值相关接口
- [聊天系统 API](api/chat-api-analysis.md) - 聊天功能接口
- [Crash游戏 API](api/crash-api-analysis.md) - Crash游戏接口
- [自定义箱子 API](api/custombox-api-analysis.md) - 自定义箱子接口
- [红包系统 API](api/envelope-api-analysis.md) - 红包功能接口
- [抢夺游戏 API](api/grab-api-analysis.md) - 抢夺游戏接口
- [彩票系统 API](api/lottery-api-analysis.md) - 彩票功能接口
- [幸运箱子 API](api/luckybox-api-analysis.md) - 幸运箱子接口
- [市场交易 API](api/market-api-analysis.md) - 市场交易接口
- [监控系统 API](api/monitor-api-analysis.md) - 系统监控接口
- [礼包系统 API](api/package-api-analysis.md) - 礼包功能接口
- [推广系统 API](api/promotion-api-analysis.md) - 推广活动接口
- [Roll游戏 API](api/roll-api-analysis.md) - Roll游戏接口
- [站点配置 API](api/sitecfg-api-analysis.md) - 站点配置接口
- [任务系统分析](api/thworker-analysis.md) - ThWorker任务系统
- [合成系统 API](api/tradeup-api-analysis.md) - 物品合成接口
- [提现系统 API](api/withdraw-api-analysis.md) - 提现相关接口

### 👨‍💻 前端开发者
| 文档 | 描述 | 技术栈 |
|------|------|---------|
| [🎨 前端开发指南](frontend/development-guide.md) | Vue3/Nuxt3完整开发指南 | Vue3 + Nuxt3 + TS |
| [🏗️ 前端架构](frontend/overview.md) | 前端架构设计和最佳实践 | Pinia + Tailwind |
| [🎭 开箱动画](frontend/case-opening-real-animation.md) | 老虎机滚轮式开箱动画 | GSAP + CSS3 |
| [⚔️ 对战动画](frontend/frontend-battle-animation.md) | 实时对战动画系统 | WebSocket + GSAP |
| [🔄 WebSocket重连](frontend/websocket-recovery-advanced.md) | 高级容错重连机制 | Socket.IO |

### ⚙️ 后端开发者
| 文档 | 描述 | 技术栈 |
|------|------|---------|
| [🔧 后端架构](backend/architecture.md) | Django系统架构设计 | Django + DRF |
| [🗄️ 数据库设计](backend/database.md) | 数据模型和关系设计 | MySQL + Redis |
| [🌐 WebSocket服务](backend/websocket.md) | 实时通信服务实现 | Django Channels |
| [⚔️ 对战房间状态](backend/caseroom-states.md) | 对战房间状态机制 | 状态机设计 |
| [🔄 ThWorker框架](backend/thworker.md) | 异步任务处理框架 | Celery + Custom |

### 🔧 运维工程师
| 文档 | 描述 | 技术栈 |
|------|------|---------|
| [🐳 Docker部署](DOCKER_DEPLOYMENT.md) | 容器化部署完整指南 | Docker + Compose |
| [🛠️ 环境搭建](deployment/setup.md) | 开发和生产环境配置 | Linux + Python |
| [🚀 部署指南](deployment/deployment.md) | CI/CD流程和自动化 | 自动化部署 |

### 📊 监控与诊断
- 系统健康检查: `python3 scripts/battle_system_monitor.py --mode diagnosis`
- 部署验证: `./scripts/deploy_battle_optimization.sh`
- 系统监控: `python3 scripts/battle_system_monitor.py --mode monitor`

## 🔍 快速导航

### 开发者入门
1. 阅读 [快速开始指南](guides/quickstart.md)
2. 查看 [系统架构](backend/architecture.md)
3. 配置 [开发环境](deployment/setup.md)
4. 了解 [API 规范](api/overview.md)

### API 集成
1. 查看 [认证流程](api/authentication-api.md)
2. 集成 [核心功能 API](api/case-api.md)
3. 实现 [实时通信](api/websocket-api.md)

### 前端开发
1. 了解 [前端架构](frontend/overview.md)
2. 实现 [开箱动画](frontend/case-opening-real-animation.md)
3. 集成 [对战系统](frontend/frontend-battle-animation.md)

### 运维部署
1. 配置 [基础环境](deployment/setup.md)
2. 执行 [部署流程](deployment/deployment.md)
3. 查看 [Docker部署指南](DOCKER_DEPLOYMENT.md)

## 📝 文档贡献

如果你发现文档中的错误或有改进建议，欢迎提交Issue或Pull Request。

### 文档编写规范
- 使用Markdown格式
- 代码示例要完整可运行
- API文档包含请求/响应示例
- 截图和图表使用相对路径

### 目录结构说明
```
docs/
├── README.md                 # 文档中心首页（当前文件）
├── DOCKER_DEPLOYMENT.md     # Docker部署指南
├── api/                      # API接口文档
│   ├── overview.md          # API总览和规范
│   ├── authentication-api.md # 认证相关API
│   ├── case-api.md          # 箱子相关API
│   ├── battle-api.md        # 对战相关API
│   ├── websocket-api.md     # WebSocket接口
│   └── ...                  # 其他模块API文档
├── frontend/                 # 前端开发文档
│   ├── overview.md          # 前端总览
│   ├── case-opening-real-animation.md  # 开箱动画
│   ├── frontend-battle-animation.md    # 对战动画
│   └── websocket-recovery-advanced.md  # WebSocket重连
├── backend/                  # 后端架构文档
│   ├── architecture.md      # 系统架构
│   ├── database.md          # 数据库设计
│   ├── caseroom-states.md   # 房间状态机制
│   ├── websocket.md         # WebSocket实现
│   └── thworker.md          # 任务处理框架
├── deployment/               # 部署运维文档
│   ├── setup.md            # 环境搭建
│   └── deployment.md       # 部署指南
└── guides/                   # 使用指南
    ├── quickstart.md        # 快速开始
    └── faq.md               # 常见问题
```

## 🏷️ 标签说明

- `🔥 热门` - 经常查阅的文档
- `🆕 新增` - 最近更新的内容
- `⚡ 重要` - 关键配置或注意事项
- `🛠️ 开发` - 开发相关文档
- `🚀 部署` - 部署相关文档
- `📱 前端` - 前端开发文档
- `⚙️ 后端` - 后端开发文档

## 📞 联系方式

如有技术问题或建议，可通过以下方式联系：
- 技术支持: <EMAIL>
- 问题反馈: 提交 GitHub Issue
- 文档问题: 查看相关文档或提交Issue

## 📋 归档说明

系统升级过程中的变更记录、修复记录和临时实现方案已归档至 `backup/docs_archive_20250718/`，包括：
- changelog/ - 变更日志
- fixes/ - 修复记录
- 过时的升级指南和部署报告

当前文档反映系统的最新状态，历史记录仅作参考。

---

*最后更新时间: 2025-07-18*
*文档版本: v3.0 (Docker化版本)*
