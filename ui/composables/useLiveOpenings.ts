import { ref, computed, onMounted, onUnmounted } from 'vue'
import { monitorApi, type CaseRecord } from '~/services/monitor-api'

export interface Opening { 
  id: string
  user: string
  skin: string
  time: string
  isNew?: boolean
  caseInfo?: {
    name: string
    image: string
  }
  itemInfo?: {
    name: string
    image: string
    exterior: string
    rarity: string
    price: number
  }
}

export interface LiveOpeningsOptions {
  maxRecords?: number
  autoRefreshInterval?: number
}

/**
 * Composable for managing live openings via WebSocket messages.
 * Handles real-time case opening records with connection state management.
 */
export function useLiveOpenings(options: LiveOpeningsOptions = {}) {
  const { t } = useI18n()
  const { maxRecords = 20, autoRefreshInterval = 30000 } = options
  
  // console.log('🚀 [useLiveOpenings] 初始化组件，配置:', { maxRecords, autoRefreshInterval })
  
  const openings = ref<Opening[]>([])
  const isLoading = ref(true)
  const socketError = ref(false)
  const noDataAvailable = ref(false)
  const isRefreshing = ref(false)
  
  let refreshTimer: NodeJS.Timeout | null = null
  let newRecordTimers: Record<string, NodeJS.Timeout> = {}
  
  // 计算属性：过滤有效记录
  const validOpenings = computed(() => 
    openings.value.filter(o => o.id && o.user && o.skin)
  )
  
  // 性能优化：限制显示的记录数量
  const displayOpenings = computed(() => 
    validOpenings.value.slice(0, Math.min(maxRecords, 50))
  )
  
  // 防抖刷新函数
  let refreshDebounceTimer: NodeJS.Timeout | null = null
  const debouncedRefresh = () => {
    if (refreshDebounceTimer) {
      clearTimeout(refreshDebounceTimer)
    }
    refreshDebounceTimer = setTimeout(() => {
      refresh()
    }, 300)
  }
  
  function formatTime(timestamp: string): string {
    if (!timestamp) return t('live_openings.just_now')
    
    const now = new Date()
    const date = new Date(timestamp)
    const diff = Math.floor((now.getTime() - date.getTime()) / 1000)
    
    if (diff < 5) return t('live_openings.just_now')
    if (diff < 60) return t('live_openings.seconds_ago', { n: diff })
    if (diff < 3600) return t('live_openings.minutes_ago', { n: Math.floor(diff / 60) })
    if (diff < 86400) return t('live_openings.hours_ago', { n: Math.floor(diff / 3600) })
    
    // 对于超过一天的，直接显示日期格式
    const month = date.getMonth() + 1
    const day = date.getDate()
    return `${month}/${day}`
  }
  
  // 更新数据映射函数以匹配新的API结构
  function mapRecordData(r: CaseRecord): Opening {
    return {
      id: r.id?.toString() || Math.random().toString(36),
      user: r.user_info?.profile?.nickname || 'Anonymous',
      skin: r.item_info?.name || 'Unknown Item',
      time: formatTime(r.create_time || r.update_time),
      isNew: false,
      caseInfo: r.case_info ? {
        name: r.case_info.name || 'Unknown Case',
        image: r.case_info.cover || '/images/case-placeholder.png'
      } : undefined,
      itemInfo: r.item_info ? {
        name: r.item_info.name || 'Unknown Item',
        image: r.item_info.image || '/images/item-placeholder.svg',
        exterior: r.item_info.item_exterior?.exterior_name || 'Unknown Condition',
        rarity: r.item_info.item_rarity?.rarity_name || 'Unknown Rarity',
        price: r.item_info.item_price?.price || r.price || 0
      } : undefined
    }
  }
  
  function addNewRecord(record: Opening) {
    console.log('[useLiveOpenings] 添加新记录到列表:', record)
    
    const newRecord = { ...record, isNew: true }
    openings.value.unshift(newRecord)
    
    console.log('[useLiveOpenings] 当前记录总数:', openings.value.length)
    
    // 限制记录数量
    if (openings.value.length > maxRecords) {
      const removedRecords = openings.value.splice(maxRecords)
      console.log('[useLiveOpenings] 移除旧记录:', removedRecords.length, '条')
      // 清理被移除记录的计时器
      removedRecords.forEach(r => {
        if (newRecordTimers[r.id]) {
          clearTimeout(newRecordTimers[r.id])
          delete newRecordTimers[r.id]
        }
      })
    }
    
    // 设置新记录标记清除计时器
    if (newRecordTimers[newRecord.id]) {
      clearTimeout(newRecordTimers[newRecord.id])
    }
    
    newRecordTimers[newRecord.id] = setTimeout(() => {
      const index = openings.value.findIndex(o => o.id === newRecord.id)
      if (index !== -1) {
        openings.value[index].isNew = false
        console.log('[useLiveOpenings] 移除新记录标记:', newRecord.id)
      }
      delete newRecordTimers[newRecord.id]
    }, 6000)
    
    console.log('[useLiveOpenings] 新记录添加完成，当前显示记录数:', openings.value.length)
  }
  
  /**
   * 处理monitor消息（来自Socket.IO的开箱数据）
   */
  function handleMonitorMessage(data: any) {
    console.log('📨 [useLiveOpenings] 处理monitor消息:', data)
    
    try {
      // 如果是数组，处理全量数据
      if (Array.isArray(data)) {
        console.log('[useLiveOpenings] 初始化开箱记录:', data.length, '条记录')
        openings.value = data.slice(0, maxRecords).map(mapRecordData)
        isLoading.value = false
        noDataAvailable.value = openings.value.length === 0
        
        // 触发记录更新事件
        window.dispatchEvent(new CustomEvent('case-records-updated', {
          detail: { count: openings.value.length }
        }))
      }
      // 如果是单个对象，作为新记录添加
      else if (data && typeof data === 'object') {
        console.log('[useLiveOpenings] 添加新开箱记录:', data)
        const newRecord = mapRecordData(data)
        addNewRecord(newRecord)
        isLoading.value = false
        noDataAvailable.value = false
      }
    } catch (error) {
      console.error('[useLiveOpenings] 处理monitor消息失败:', error, data)
    }
  }

  function handleMessage(e: Event) {
    console.log('[useLiveOpenings] 收到消息事件:', e.type, (e as CustomEvent)?.detail)
    
    const msg = (e as CustomEvent).detail
    
    // 处理连接状态事件
    if (e.type === 'socket-connected' || e.type === 'socket-reconnected') {
      console.log('[useLiveOpenings] Socket.IO 连接成功')
      socketError.value = false
      return
    }
    
    if (e.type === 'socket-disconnected') {
      console.log('[useLiveOpenings] Socket.IO 连接断开')
      socketError.value = true
      return
    }
    
    // 处理 socket-monitor 事件（新的Socket.IO插件分发的事件）
    if (e.type === 'socket-monitor' && msg?.data) {
      console.log('[useLiveOpenings] 处理Socket.IO monitor数据:', msg.data)
      handleMonitorMessage(msg.data)
      return
    }
    
    // 处理 socket-case-records 事件（开箱记录数据）
    if (e.type === 'socket-case-records' && msg?.data) {
      console.log('[useLiveOpenings] 处理Socket.IO开箱记录数据:', msg.data)
      handleMonitorMessage(msg.data)
      return
    }
    
    // 处理 socket-box 事件（新开箱数据）
    if (e.type === 'socket-box' && msg?.data) {
      console.log('[useLiveOpenings] 处理Socket.IO box数据:', msg.data)
      // Box事件通常是新的开箱记录
      if (msg.data && typeof msg.data === 'object') {
        const newRecord = mapRecordData(msg.data)
        addNewRecord(newRecord)
        isLoading.value = false
        noDataAvailable.value = false
      }
      return
    }
    
    // 处理 socket-case-opened 事件（开箱完成事件）
    if (e.type === 'socket-case-opened' && msg?.data) {
      console.log('[useLiveOpenings] 处理Socket.IO开箱完成事件:', msg.data)
      if (msg.data && typeof msg.data === 'object') {
        const newRecord = mapRecordData(msg.data)
        addNewRecord(newRecord)
        isLoading.value = false
        noDataAvailable.value = false
      }
      return
    }
    
    // 处理旧格式的消息（兼容性保留）
    if (e.type === 'socket:message') {
      const { event, data } = msg || {}
      console.log('[useLiveOpenings] 处理旧格式Socket.IO消息:', { event, data })
      
      if (event === 'monitor' && data) {
        if (Array.isArray(data) && data.length > 0) {
          handleMonitorMessage(data[0])
        } else {
          handleMonitorMessage(data)
        }
      }
      return
    }
    
    // 处理旧格式的数组消息（兼容性保留）
    if (e.type === 'socket:message' && Array.isArray(msg) && msg.length >= 1) {
      try {
        const [type, action, payload] = msg
        console.log('[useLiveOpenings] 处理旧格式socket数组消息:', { type, action, payload })
        
        // 全量数据初始化
        if (type === 'monitor' && action === 'case_records' && Array.isArray(payload)) {
          console.log('[useLiveOpenings] 初始化开箱记录:', payload.length, '条记录')
          openings.value = payload.slice(0, maxRecords).map(mapRecordData)
          isLoading.value = false
          noDataAvailable.value = openings.value.length === 0
          
          // 触发记录更新事件
          window.dispatchEvent(new CustomEvent('case-records-updated', {
            detail: { count: openings.value.length }
          }))
        }
        // 新增单条记录
        else if (type === 'box' && action === 'new' && payload && typeof payload === 'object') {
          console.log('[useLiveOpenings] 添加新开箱记录:', payload)
          const newRecord = mapRecordData(payload)
          addNewRecord(newRecord)
          isLoading.value = false
          noDataAvailable.value = false
        }
        // 处理 case_opened 事件
        else if (type === 'case_opened' && payload) {
          console.log('[useLiveOpenings] 处理开箱事件:', payload)
          const newRecord = mapRecordData(payload)
          addNewRecord(newRecord)
          isLoading.value = false
          noDataAvailable.value = false
        }
        else {
          console.warn('[useLiveOpenings] 未处理的消息类型:', { type, action })
        }
      } catch (error) {
        console.error('[useLiveOpenings] 处理旧格式消息失败:', error, msg)
      }
    }
  }
  
  async function refresh() {
    console.log('🔄 [useLiveOpenings] 刷新请求 - 当前状态:', {
      isRefreshing: isRefreshing.value,
      recordCount: openings.value.length,
      socketError: socketError.value
    })
    
    if (isRefreshing.value) {
      console.log('⚠️ [useLiveOpenings] 刷新已在进行中，跳过此次请求')
      return
    }
    
    isRefreshing.value = true
    console.log('📤 [useLiveOpenings] 开始从API获取开箱记录')
    
    try {
      // 使用新的API获取开箱记录
      const result = await monitorApi.getCaseRecords()
      
      if (result.success && result.data?.case_records) {
        console.log('✅ [useLiveOpenings] API数据获取成功，记录数:', result.data.case_records.length)
        
        // 映射数据并更新状态
        openings.value = result.data.case_records.slice(0, maxRecords).map(mapRecordData)
        isLoading.value = false
        noDataAvailable.value = openings.value.length === 0
        socketError.value = false
        
        // 触发记录更新事件
        window.dispatchEvent(new CustomEvent('case-records-updated', {
          detail: { count: openings.value.length, source: 'api-refresh' }
        }))
      } else {
        throw new Error(result.message || '获取开箱记录失败')
      }
    } catch (error) {
      console.error('❌ [useLiveOpenings] API刷新失败:', error)
      socketError.value = true
      isLoading.value = false
    } finally {
      isRefreshing.value = false
      console.log('✅ [useLiveOpenings] 刷新完成')
    }
  }
  
  function clearAllTimers() {
    Object.values(newRecordTimers).forEach(clearTimeout)
    newRecordTimers = {}
    
    if (refreshTimer) {
      clearTimeout(refreshTimer)
      refreshTimer = null
    }
  }
  
  onMounted(async () => {
    console.log('🔧 [useLiveOpenings] 组件挂载，开始设置事件监听器')
    
    // 立即获取初始数据
    console.log('📤 [useLiveOpenings] 获取初始开箱记录数据')
    await refresh()
    
    // 监听新的Socket.IO插件分发的事件
    console.log('📡 [useLiveOpenings] 注册新的Socket.IO事件监听器')
    window.addEventListener('socket-monitor', handleMessage)
    window.addEventListener('socket-case-records', handleMessage)
    window.addEventListener('socket-box', handleMessage)
    window.addEventListener('socket-case-opened', handleMessage)
    
    // 监听旧的Socket.IO消息（兼容性保留）
    console.log('📡 [useLiveOpenings] 注册兼容性Socket.IO事件监听器')
    window.addEventListener('socket:message', handleMessage)
    window.addEventListener('socket:monitor', (e) => {
      console.log('📨 [useLiveOpenings] 收到socket:monitor事件:', (e as CustomEvent).detail)
      handleMonitorMessage((e as CustomEvent).detail)
    })
    
    // 监听连接状态事件
    console.log('📡 [useLiveOpenings] 注册Socket.IO连接状态监听器')
    window.addEventListener('socket-connected', handleMessage)
    window.addEventListener('socket-disconnected', handleMessage)
    window.addEventListener('socket-reconnected', handleMessage)
    window.addEventListener('socket-reconnecting', (e) => {
      console.log('🔄 [useLiveOpenings] Socket.IO重连中:', (e as CustomEvent).detail)
      socketError.value = true
    })
    window.addEventListener('socket-error', (e) => {
      console.error('❌ [useLiveOpenings] Socket.IO错误:', (e as CustomEvent).detail)
      socketError.value = true
      isLoading.value = false
    })
    
    console.log('✅ [useLiveOpenings] 所有事件监听器注册完成')
    
    // 设置加载超时
    const loadingTimeout = setTimeout(() => {
      console.log('⏰ [useLiveOpenings] 加载超时检查 - 当前状态:', {
        isLoading: isLoading.value,
        recordCount: openings.value.length,
        socketError: socketError.value
      })
      
      if (isLoading.value && openings.value.length === 0) {
        console.log('❌ [useLiveOpenings] 加载超时，切换到无数据状态')
        isLoading.value = false
        noDataAvailable.value = true
      }
    }, 5000)
    
    // 设置自动刷新
    if (autoRefreshInterval > 0) {
      console.log(`🔄 [useLiveOpenings] 设置自动刷新间隔: ${autoRefreshInterval}ms`)
      refreshTimer = setInterval(() => {
        console.log('🔄 [useLiveOpenings] 自动刷新触发')
        refresh()
      }, autoRefreshInterval)
    } else {
      console.log('⚠️ [useLiveOpenings] 自动刷新已禁用')
    }
    
    // 打印初始状态
    console.log('📊 [useLiveOpenings] 初始状态:', {
      openings: openings.value.length,
      isLoading: isLoading.value,
      socketError: socketError.value,
      noDataAvailable: noDataAvailable.value
    })
    
    // 清理超时计时器
    onUnmounted(() => {
      clearTimeout(loadingTimeout)
    })
    
    // 启动直接Socket.IO连接测试
    console.log('🚀 [useLiveOpenings] 启动直接Socket.IO连接测试')
    setTimeout(() => {
      testDirectSocketIO()
    }, 2000) // 延迟2秒执行，确保页面完全加载
  })
  
  onUnmounted(() => {
    console.log('🧹 [useLiveOpenings] 组件卸载，开始清理资源')
    
    // 清理新的Socket.IO事件监听器
    console.log('🔇 [useLiveOpenings] 移除新的Socket.IO事件监听器')
    window.removeEventListener('socket-monitor', handleMessage)
    window.removeEventListener('socket-case-records', handleMessage)
    window.removeEventListener('socket-box', handleMessage)
    window.removeEventListener('socket-case-opened', handleMessage)
    
    // 清理旧的事件监听器（兼容性）
    console.log('🔇 [useLiveOpenings] 移除兼容性事件监听器')
    window.removeEventListener('socket:message', handleMessage)
    window.removeEventListener('socket-connected', handleMessage)
    window.removeEventListener('socket-disconnected', handleMessage)
    window.removeEventListener('socket-reconnected', handleMessage)
    
    // 清理所有计时器
    console.log('⏹️ [useLiveOpenings] 清理计时器')
    clearAllTimers()
    
    console.log('✅ [useLiveOpenings] 资源清理完成')
  })
  
  // 直接Socket.IO连接测试
  async function testDirectSocketIO() {
    console.log('🔍 [useLiveOpenings] 开始直接Socket.IO连接测试')

    try {
      const config = useRuntimeConfig()

      // 在开发环境使用本地代理，生产环境使用配置的URL
      const isDev = process.env.NODE_ENV === 'development'
      const socketUrl = isDev
        ? window.location.origin  // 开发环境使用本地代理 http://localhost:3000
        : (config.public.socketUrl as string)

      console.log('🔍 [useLiveOpenings] Socket.IO连接地址:', socketUrl)
      console.log('🔍 [useLiveOpenings] 开发环境:', isDev)

      // 动态导入Socket.IO客户端
      const io = (await import('socket.io-client')).default
      console.log('🔍 [useLiveOpenings] Socket.IO客户端导入成功')

      // 创建连接
      const testSocket = io(socketUrl, {
        transports: ['polling', 'websocket'],
        forceNew: true,
        reconnection: true,
        reconnectionAttempts: 3,
        timeout: 10000,
        autoConnect: true,
        path: '/socket.io/',
        query: {
          page: 'ws',
          EIO: '4'  // 使用正确的 EIO 版本
        }
      })
      
      console.log('🔍 [useLiveOpenings] Socket.IO实例创建完成，开始连接测试...')
      
      // 设置事件监听器
      testSocket.on('connect', () => {
        console.log('🎉 [useLiveOpenings] 直接Socket.IO连接成功! ID:', testSocket.id)
        console.log('🎉 [useLiveOpenings] 连接详情:', {
          id: testSocket.id,
          connected: testSocket.connected
        })
        
        // 测试发送消息
        testSocket.emit('join', 'monitor')
        console.log('📤 [useLiveOpenings] 已请求加入monitor频道')
        
        setTimeout(() => {
          testSocket.emit('monitor', 'get_stats')
          console.log('📤 [useLiveOpenings] 已请求统计数据')
        }, 1000)
      })
      
      testSocket.on('connect_error', (error: any) => {
        console.error('❌ [useLiveOpenings] 直接Socket.IO连接错误:', error.message)
      })
      
      testSocket.on('disconnect', (reason: any) => {
        console.warn('🔴 [useLiveOpenings] 直接Socket.IO连接断开:', reason)
      })
      
      testSocket.on('monitor', (data: any) => {
        console.log('📨 [useLiveOpenings] 收到monitor数据:', data)
        handleMonitorMessage(data)
      })
      
      // 监听所有事件
      const socket = testSocket as any
      if (socket.onAny) {
        socket.onAny((event: any, ...args: any[]) => {
          console.log('📨 [useLiveOpenings] 收到Socket.IO事件:', event, args)
        })
      }
      
      return testSocket
      
    } catch (error) {
      console.error('❌ [useLiveOpenings] 直接Socket.IO连接测试失败:', error)
      return null
    }
  }

  return {
    openings: displayOpenings,
    validOpenings,
    isLoading,
    socketError,
    noDataAvailable,
    isRefreshing,
    refresh,
    debouncedRefresh,
    formatTime,
    testDirectSocketIO  // 导出测试函数
  }
}
