<template>
  <Transition name="modal" appear>
    <div v-if="show" class="battle-result-modal">
      <!-- 模态框背景遮罩 -->
      <div class="modal-backdrop" @click="closeModal"></div>
      
      <!-- 模态框内容 -->
      <div class="modal-content">
        <!-- 关闭按钮 -->
        <button 
          class="modal-close" 
          @click="closeModal"
          :aria-label="t('common.close')"
        >
          <Icon name="heroicons:x-mark" class="w-6 h-6" />
        </button>
        
        <!-- 获胜者庆祝区域 -->
        <div class="winner-celebration">
          <div class="confetti-container">
            <div 
              v-for="i in 50" 
              :key="i" 
              class="confetti"
              :style="getConfettiStyle(i)"
            ></div>
          </div>
          
          <div class="winner-info">
            <div class="crown-animation">
              <Icon name="heroicons:trophy" class="w-16 h-16 text-yellow-400" />
            </div>
            
            <img 
              :src="winner?.user?.profile?.avatar || '/demo/avatar1.png'" 
              :alt="winner?.user?.profile?.nickname || 'Winner'"
              class="winner-avatar"
              @error="handleImageError"
            />
            
            <h2 class="winner-name">
              {{ winner?.user?.profile?.nickname || t('battle.result.unknown_winner') }}
            </h2>
            
            <div class="winner-value">
              ${{ formatPrice(winner?.totalValue || 0) }}
            </div>
            
            <div class="winner-declaration">
              {{ t('battle.result.winner_declaration') }}
            </div>
          </div>
        </div>
        
        <!-- 结果统计区域 -->
        <div class="result-statistics">
          <!-- 对战摘要 -->
          <div class="battle-summary">
            <h3 class="summary-title">{{ t('battle.result.summary') }}</h3>
            <div class="summary-grid">
              <div class="summary-item">
                <span class="summary-label">{{ t('battle.result.players') }}</span>
                <span class="summary-value">{{ players.length }}</span>
              </div>
              <div class="summary-item">
                <span class="summary-label">{{ t('battle.result.rounds') }}</span>
                <span class="summary-value">{{ battleData?.rounds?.length || 0 }}</span>
              </div>
              <div class="summary-item">
                <span class="summary-label">{{ t('battle.result.total_value') }}</span>
                <span class="summary-value">${{ formatPrice(totalBattleValue) }}</span>
              </div>
              <div class="summary-item">
                <span class="summary-label">{{ t('battle.result.duration') }}</span>
                <span class="summary-value">{{ formatDuration(battleData?.duration || 0) }}</span>
              </div>
            </div>
          </div>
          
          <!-- 玩家排行榜 -->
          <div class="players-ranking">
            <h3 class="ranking-title">{{ t('battle.result.ranking') }}</h3>
            
            <div class="ranking-list">
              <div 
                v-for="(player, index) in sortedPlayers" 
                :key="player.user?.uid || index"
                :class="['ranking-item', {
                  'first-place': index === 0,
                  'winner': player.user?.uid === winner?.user?.uid
                }]"
              >
                <!-- 排名徽章 -->
                <div class="rank-badge">
                  <Icon 
                    v-if="index === 0" 
                    name="heroicons:trophy" 
                    class="w-5 h-5 text-yellow-400" 
                  />
                  <span v-else>{{ index + 1 }}</span>
                </div>
                
                <!-- 玩家信息 -->
                <div class="player-info">
                  <img 
                    :src="player.user?.profile?.avatar || '/demo/avatar1.png'" 
                    class="player-avatar"
                    @error="handleImageError"
                  />
                  <span class="player-name">
                    {{ player.user?.profile?.nickname || t('battle.result.unknown_player') }}
                  </span>
                </div>
                
                <!-- 统计数据 -->
                <div class="player-stats">
                  <div class="total-value">${{ formatPrice(player.totalValue || 0) }}</div>
                  <div class="best-item">
                    {{ getBestItemName(player) }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 操作按钮区域 -->
        <div class="result-actions">
          <button 
            class="action-btn share-btn"
            @click="shareResult"
            :aria-label="t('battle.result.share')"
          >
            <Icon name="heroicons:share" class="w-5 h-5" />
            {{ t('battle.result.share') }}
          </button>
          
          <button 
            class="action-btn new-battle-btn"
            @click="startNewBattle"
          >
            <Icon name="heroicons:plus" class="w-5 h-5" />
            {{ t('battle.result.new_battle') }}
          </button>
          
          <button 
            class="action-btn back-btn"
            @click="goBack"
          >
            <Icon name="heroicons:arrow-left" class="w-5 h-5" />
            {{ t('battle.result.back_to_list') }}
          </button>
        </div>
      </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'

// Props 接口定义
interface BattleResultModalProps {
  show: boolean
  battleData: any
  winner: any
  players: any[]
}

// Emits 接口定义
interface BattleResultModalEmits {
  close: () => void
}

// Props
const props = defineProps<BattleResultModalProps>()

// Emits
const emit = defineEmits<BattleResultModalEmits>()

// 国际化
const { t } = useI18n()

// 路由
const router = useRouter()

// 计算属性
const sortedPlayers = computed(() => {
  return [...props.players].sort((a, b) => (b.totalValue || 0) - (a.totalValue || 0))
})

const totalBattleValue = computed(() => {
  return props.players.reduce((total, player) => total + (player.totalValue || 0), 0)
})

// 方法
const closeModal = () => {
  emit('close')
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/demo/avatar1.png'
}

const formatPrice = (price: number) => {
  return price.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

const formatDuration = (duration: number) => {
  const minutes = Math.floor(duration / 60)
  const seconds = duration % 60
  return `${minutes}:${seconds.toString().padStart(2, '0')}`
}

const getBestItemName = (player: any) => {
  if (!player.items || player.items.length === 0) {
    return t('battle.result.no_items')
  }
  
  const bestItem = player.items.reduce((best: any, current: any) => {
    return (current.price || 0) > (best.price || 0) ? current : best
  })
  
  return bestItem.name || t('battle.result.unknown_item')
}

const getConfettiStyle = (index: number) => {
  const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3']
  const color = colors[index % colors.length]
  const left = Math.random() * 100
  const animationDelay = Math.random() * 3
  const animationDuration = 2 + Math.random() * 2
  
  return {
    left: `${left}%`,
    backgroundColor: color,
    animationDelay: `${animationDelay}s`,
    animationDuration: `${animationDuration}s`
  }
}

const shareResult = async () => {
  try {
    const shareData = {
      title: t('battle.result.share_title'),
      text: t('battle.result.share_text', {
        winner: props.winner?.user?.profile?.nickname || t('battle.result.unknown_winner'),
        value: formatPrice(props.winner?.totalValue || 0)
      }),
      url: window.location.href
    }
    
    if (navigator.share) {
      await navigator.share(shareData)
    } else {
      // 降级到复制链接
      await navigator.clipboard.writeText(window.location.href)
    }
  } catch (error) {
    console.error('分享失败:', error)
  }
}

const startNewBattle = () => {
  closeModal()
  router.push('/battle')
}

const goBack = () => {
  closeModal()
  router.push('/battle')
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    closeModal()
  }
}

// 生命周期
onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
  document.body.style.overflow = 'hidden'
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
  document.body.style.overflow = ''
})
</script>

<style scoped lang="scss">
.battle-result-modal {
  @apply fixed inset-0 z-50 flex items-center justify-center p-4;
  
  .modal-backdrop {
    @apply absolute inset-0 bg-black/80 backdrop-blur-sm;
  }
  
  .modal-content {
    @apply relative bg-gray-900/95 backdrop-blur-md border border-gray-700/50 rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto;
    
    .modal-close {
      @apply absolute top-4 right-4 z-10 p-2 rounded-lg hover:bg-gray-700/50 transition-colors;
    }
  }
}

// 获胜者庆祝区域
.winner-celebration {
  @apply relative text-center py-8 px-6 border-b border-gray-700/50;
  
  .confetti-container {
    @apply absolute inset-0 overflow-hidden pointer-events-none;
    
    .confetti {
      @apply absolute w-2 h-2 rounded-sm;
      animation: confetti-fall linear infinite;
    }
  }
  
  .winner-info {
    @apply relative z-10;
    
    .crown-animation {
      @apply mb-4;
      animation: crown-bounce 2s ease-in-out infinite;
    }
    
    .winner-avatar {
      @apply w-24 h-24 rounded-full border-4 border-yellow-400/50 mx-auto mb-4;
      animation: avatar-glow 2s ease-in-out infinite;
    }
    
    .winner-name {
      @apply text-2xl font-bold text-white mb-2;
    }
    
    .winner-value {
      @apply text-3xl font-bold text-yellow-400 mb-2;
    }
    
    .winner-declaration {
      @apply text-lg text-gray-300;
    }
  }
}

// 结果统计区域
.result-statistics {
  @apply p-6 space-y-6;
  
  .battle-summary {
    .summary-title {
      @apply text-xl font-semibold text-white mb-4;
    }
    
    .summary-grid {
      @apply grid grid-cols-2 md:grid-cols-4 gap-4;
      
      .summary-item {
        @apply text-center p-4 rounded-lg bg-gray-800/50 border border-gray-700/30;
        
        .summary-label {
          @apply block text-sm text-gray-400 mb-1;
        }
        
        .summary-value {
          @apply block text-lg font-semibold text-white;
        }
      }
    }
  }
  
  .players-ranking {
    .ranking-title {
      @apply text-xl font-semibold text-white mb-4;
    }
    
    .ranking-list {
      @apply space-y-3;
      
      .ranking-item {
        @apply flex items-center gap-4 p-4 rounded-lg bg-gray-800/50 border border-gray-700/30 transition-all duration-300;
        
        &.first-place {
          @apply border-yellow-400/50 bg-yellow-400/10;
        }
        
        &.winner {
          @apply border-green-400/50 bg-green-400/10;
        }
        
        .rank-badge {
          @apply w-8 h-8 rounded-full bg-gray-700 flex items-center justify-center text-white font-bold text-sm;
        }
        
        .player-info {
          @apply flex items-center gap-3 flex-1;
          
          .player-avatar {
            @apply w-10 h-10 rounded-full;
          }
          
          .player-name {
            @apply text-white font-medium;
          }
        }
        
        .player-stats {
          @apply text-right;
          
          .total-value {
            @apply text-lg font-bold text-white;
          }
          
          .best-item {
            @apply text-sm text-gray-400;
          }
        }
      }
    }
  }
}

// 操作按钮区域
.result-actions {
  @apply flex flex-wrap gap-3 p-6 border-t border-gray-700/50;
  
  .action-btn {
    @apply flex items-center gap-2 px-6 py-3 rounded-lg font-medium transition-all duration-300;
    
    &.share-btn {
      @apply bg-blue-600 hover:bg-blue-700 text-white;
    }
    
    &.new-battle-btn {
      @apply bg-green-600 hover:bg-green-700 text-white;
    }
    
    &.back-btn {
      @apply bg-gray-600 hover:bg-gray-700 text-white;
    }
  }
}

// 动画定义
@keyframes confetti-fall {
  0% {
    transform: translateY(-100vh) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(100vh) rotate(360deg);
    opacity: 0;
  }
}

@keyframes crown-bounce {
  0%, 100% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(-10px) scale(1.1);
  }
}

@keyframes avatar-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(251, 191, 36, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(251, 191, 36, 0.6);
  }
}

// 模态框过渡动画
.modal-enter-active,
.modal-leave-active {
  transition: all 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
  transform: scale(0.9);
}

// 响应式设计
@media (max-width: 768px) {
  .battle-result-modal {
    @apply p-2;
    
    .modal-content {
      @apply max-h-[95vh];
    }
  }
  
  .winner-celebration {
    @apply py-6 px-4;
    
    .winner-info {
      .winner-avatar {
        @apply w-20 h-20;
      }
      
      .winner-name {
        @apply text-xl;
      }
      
      .winner-value {
        @apply text-2xl;
      }
    }
  }
  
  .result-statistics {
    @apply p-4 space-y-4;
    
    .battle-summary .summary-grid {
      @apply grid-cols-2;
    }
  }
  
  .result-actions {
    @apply p-4 flex-col;
    
    .action-btn {
      @apply w-full justify-center;
    }
  }
}
</style> 