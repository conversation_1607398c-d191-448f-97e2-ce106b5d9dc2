/**
 * i18n客户端插件 - 处理语言持久化
 * 确保页面刷新时正确恢复用户的语言设置
 */
export default defineNuxtPlugin(() => {
    // 只在客户端执行
    if (!process.client) return

    const LANGUAGE_STORAGE_KEY = 'user-locale'

    // 恢复语言设置
    const restoreLanguage = () => {
      try {
        const savedLocale = localStorage.getItem(LANGUAGE_STORAGE_KEY)
        if (savedLocale && (savedLocale === 'en' || savedLocale === 'zh-hans')) {
          const { $i18n } = useNuxtApp()
          if ($i18n?.locale && $i18n.locale.value !== savedLocale) {
            console.log(`恢复语言设置: ${$i18n.locale.value} -> ${savedLocale}`)
            $i18n.locale.value = savedLocale
            document.documentElement.setAttribute('lang', savedLocale)
          }
        }
      } catch (error) {
        console.error('恢复语言设置失败:', error)
      }
    }

    // 页面加载时恢复语言
    nextTick(() => {
      restoreLanguage()
    })

    // 监听页面可见性变化
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden) {
        setTimeout(() => {
          restoreLanguage()
        }, 100)
      }
    })

    // 监听localStorage变化（多标签页同步）
    window.addEventListener('storage', (event) => {
      if (event.key === LANGUAGE_STORAGE_KEY && event.newValue) {
        const newLocale = event.newValue
        if (newLocale && (newLocale === 'en' || newLocale === 'zh-hans')) {
          const { $i18n } = useNuxtApp()
          if ($i18n?.locale && $i18n.locale.value !== newLocale) {
            console.log(`从其他标签页同步语言: ${newLocale}`)
            $i18n.locale.value = newLocale
            document.documentElement.setAttribute('lang', newLocale)
          }
        }
      }
    })
}) 