<template>
  <div class="table-style-card bg-gray-800/50 border-b border-gray-700/50 hover:bg-gray-800/80 transition-all duration-300 group">
    <div class="grid grid-cols-12 gap-4 p-3 items-center">
      <!-- 时间戳 -->
      <div class="col-span-1 text-center">
        <div class="text-white/60 text-xs">{{ formatTime() }}</div>
      </div>

      <!-- 用户信息 -->
      <div class="col-span-2 flex items-center space-x-2">
        <img
          :src="record.user_avatar || 'https://placehold.co/24x24/374151/9ca3af?text=U'"
          :alt="record.user_name"
          class="w-6 h-6 rounded-full border border-gray-600"
          @error="(e) => (e.target as HTMLImageElement).src = 'https://placehold.co/24x24/374151/9ca3af?text=U'"
        />
        <div class="min-w-0 flex-1">
          <div class="text-blue-400 font-medium text-sm truncate">{{ record.user_name }}</div>
        </div>
      </div>

      <!-- 箱子信息 -->
      <div class="col-span-3 flex items-center space-x-2">
        <img
          :src="record.case_info?.cover"
          :alt="record.case_info?.name"
          class="w-8 h-8 object-contain rounded bg-gray-700"
          @error="(e) => (e.target as HTMLImageElement).src = 'https://placehold.co/32x32/374151/9ca3af?text=C'"
        />
        <div class="min-w-0 flex-1">
          <div class="text-white text-sm truncate">{{ record.case_info?.name }}</div>
        </div>
      </div>

      <!-- 物品信息 -->
      <div class="col-span-4 flex items-center space-x-2">
        <img
          :src="record.item_info?.image"
          :alt="record.item_info?.name"
          class="w-10 h-10 object-contain"
          @error="(e) => (e.target as HTMLImageElement).src = 'https://placehold.co/40x40/dc2626/fecaca?text=I'"
        />
        <div class="min-w-0 flex-1">
          <div class="text-white font-medium text-sm truncate">{{ getSkinName(record.item_info?.name) }}</div>
          <div class="text-white/60 text-xs truncate">{{ getWeaponName(record.item_info?.name) }}</div>
        </div>
      </div>

      <!-- 外观品质 -->
      <div class="col-span-1 text-center">
        <div class="text-white/80 text-xs">{{ getExteriorShort() }}</div>
      </div>

      <!-- 稀有度 -->
      <div class="col-span-1 text-center">
        <div 
          class="inline-block w-3 h-3 rounded-full"
          :style="{ backgroundColor: rarityColor }"
          :title="getRarityName()"
        ></div>
      </div>
    </div>

    <!-- 表格分隔线 -->
    <div class="h-px bg-gradient-to-r from-transparent via-gray-600/50 to-transparent group-hover:via-primary/30 transition-colors duration-300"></div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  record: any
}

const props = defineProps<Props>()

const getSkinName = (fullName?: string): string => {
  if (!fullName) return 'Unknown Item'
  const parts = fullName.split(' | ')
  if (parts.length < 2) return fullName
  const skinPart = parts[1].trim()
  return skinPart.split(' (')[0].trim()
}

const getWeaponName = (fullName?: string): string => {
  if (!fullName) return 'Unknown Weapon'
  const parts = fullName.split(' | ')
  return parts[0].trim()
}

const getExteriorShort = (): string => {
  const exterior = props.record.item_info?.item_exterior?.exterior_name || 'FN'
  const shortMap: Record<string, string> = {
    'Factory New': 'FN',
    'Minimal Wear': 'MW', 
    'Field-Tested': 'FT',
    'Well-Worn': 'WW',
    'Battle-Scarred': 'BS'
  }
  return shortMap[exterior] || exterior.substring(0, 2).toUpperCase()
}

const getRarityName = (): string => {
  return 'Covert'
}

const rarityColor = computed(() => {
  const rarities: Record<string, string> = {
    'Covert': '#eb4b4b',
    'Classified': '#d32ce6', 
    'Restricted': '#8847ff',
    'Mil-Spec': '#4b69ff',
    'Industrial': '#5e98d9',
    'Consumer': '#b0c3d9'
  }
  return rarities[getRarityName()] || '#eb4b4b'
})

const formatTime = (): string => {
  const now = new Date()
  return now.toLocaleTimeString('en-US', { 
    hour: '2-digit', 
    minute: '2-digit',
    hour12: false 
  })
}
</script>

<style lang="scss" scoped>
.table-style-card {
  animation: fadeInTableRow 0.4s ease-out;
}

@keyframes fadeInTableRow {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style> 