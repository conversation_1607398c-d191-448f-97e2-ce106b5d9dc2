<template>
  <div class="slot-machine-demo">
    <!-- 页面标题 -->
    <div class="demo-header">
      <h1 class="demo-title">🎰 老虎机开箱演示</h1>
      <p class="demo-subtitle">真实的老虎机滚轮式开箱体验</p>
    </div>

    <!-- 老虎机容器 -->
    <div class="slot-machine-container">
      <!-- 箱子信息区 -->
      <div class="case-info">
        <img :src="selectedCase?.cover || '/demo/case1.png'" class="case-image" :alt="selectedCase?.name || '选择箱子'" />
        <div class="case-selector">
          <select v-model="selectedCaseKey" @change="onCaseChange" class="case-select">
            <option value="">请选择箱子</option>
            <option v-for="case_ in slotCases" :key="case_.case_key" :value="case_.case_key">
              {{ case_.name }} - ¥{{ case_.price }}
            </option>
          </select>
        </div>
        <button 
          class="open-case-btn"
          :disabled="!selectedCase || isSpinning"
          @click="startSlotMachine"
        >
          {{ isSpinning ? '滚轮旋转中...' : '开启老虎机' }}
        </button>
        <div class="balance">余额: ¥{{ userBalance.toFixed(2) }}</div>
      </div>

      <!-- 老虎机主体 -->
      <div class="slot-machine">
        <!-- 机器外框 -->
        <div class="machine-frame"></div>
        
        <!-- 滚轮组 -->
        <div class="reels-container">
          <div 
            v-for="(reel, index) in reels" 
            :key="index"
            class="reel"
            :data-reel="index"
          >
            <div 
              class="reel-strip" 
              :ref="el => reelRefs[index] = el"
              :style="{ transform: `translateY(${reel.position}px)` }"
            >
              <div
                v-for="(item, itemIndex) in reel.items"
                :key="itemIndex"
                class="reel-item"
                :class="getRarityClass(item.rarity_color)"
              >
                <img :src="item.image" :alt="item.name" class="item-image" />
                <div class="item-name">{{ item.name }}</div>
              </div>
            </div>
            <div class="reel-window"></div>
          </div>
        </div>
        
        <!-- 中奖线 -->
        <div class="win-line" :class="{ 'active': showWinLine }"></div>
        
        <!-- 结果展示区 -->
        <div class="result-display" v-if="winningItem">
          <div class="winning-item" :class="getRarityClass(winningItem.rarity_color)">
            <img :src="winningItem.image" :alt="winningItem.name" class="item-image">
            <div class="item-info">
              <span class="item-name">{{ winningItem.name }}</span>
              <span class="item-value">¥{{ winningItem.price }}</span>
              <span class="item-rarity">{{ getRarityName(winningItem.rarity_color) }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 音效控制 -->
      <div class="audio-controls">
        <button class="mute-toggle" @click="toggleMute">
          {{ isMuted ? '🔇' : '🔊' }}
        </button>
      </div>
    </div>

    <!-- 开箱历史 -->
    <div class="slot-history">
      <h3 class="history-title">🎰 老虎机开箱记录</h3>
      <div class="history-list">
        <div 
          v-for="record in slotHistory" 
          :key="record.id"
          class="history-item"
          :class="record.status"
        >
          <div class="record-time">{{ formatTime(record.timestamp) }}</div>
          <div class="record-case">{{ record.caseName }}</div>
          <div class="record-result" v-if="record.result">
            <img :src="record.result.image" :alt="record.result.name" />
            <span class="result-name">{{ record.result.name }}</span>
            <span class="result-value" :style="{ color: record.result.rarity_color }">
              ¥{{ record.result.price }}
            </span>
          </div>
          <div class="record-status">
            <span v-if="record.status === 'spinning'" class="status-badge spinning">
              <div class="spinner"></div>
              旋转中
            </span>
            <span v-else class="status-badge completed">✅ 完成</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// @ts-nocheck
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'

// 页面元信息
definePageMeta({
  layout: 'default'
})

useHead({
  title: '老虎机开箱演示 - CSGO开发演示中心',
  meta: [
    { name: 'description', content: '真实的老虎机滚轮式开箱演示，使用GSAP动画引擎实现流畅的滚轮效果' }
  ]
})

// 状态管理
const selectedCaseKey = ref('')
const isSpinning = ref(false)
const userBalance = ref(1888.88)
const isMuted = ref(false)
const showWinLine = ref(false)
const winningItem = ref(null)
const reelRefs = ref([])
const slotHistory = ref([])

// 老虎机滚轮数据
const reels = reactive([
  { position: 0, items: [] },
  { position: 0, items: [] },
  { position: 0, items: [] }
])

// 箱子数据
const slotCases = ref([
  {
    case_key: 'ak47_slot',
    name: 'AK-47 老虎机箱',
    cover: '/demo/case1.png',
    price: 28.8,
    items: [
      { name: 'AK-47 | 红线', price: 185.5, rarity_color: '#D32CE6', image: '/demo/item1.png' },
      { name: 'AK-47 | 点射', price: 95.2, rarity_color: '#8847FF', image: '/demo/item2.png' },
      { name: 'AK-47 | 红色层压板', price: 55.8, rarity_color: '#4B69FF', image: '/demo/item3.png' },
      { name: 'AK-47 | 蓝色层压板', price: 25.5, rarity_color: '#5E98D9', image: '/demo/item4.png' },
      { name: 'AK-47 | 翡翠拼花', price: 15.8, rarity_color: '#B0C3D9', image: '/demo/item5.png' }
    ]
  },
  {
    case_key: 'awp_slot',
    name: 'AWP 狙击老虎机',
    cover: '/demo/case2.png',
    price: 45.9,
    items: [
      { name: 'AWP | 龙狙', price: 3580.0, rarity_color: '#E4AE33', image: '/demo/item6.png' },
      { name: 'AWP | 闪电风暴', price: 285.5, rarity_color: '#EB4B4B', image: '/demo/item7.png' },
      { name: 'AWP | 石墨', price: 135.3, rarity_color: '#D32CE6', image: '/demo/item8.png' },
      { name: 'AWP | 骨蛇', price: 85.8, rarity_color: '#8847FF', image: '/demo/item9.png' },
      { name: 'AWP | 蛇咬', price: 35.5, rarity_color: '#4B69FF', image: '/demo/item10.png' }
    ]
  },
  {
    case_key: 'knife_slot',
    name: '刀具老虎机箱',
    cover: '/demo/case3.png',
    price: 88.6,
    items: [
      { name: '★ 蝴蝶刀 | 渐变之色', price: 4280.0, rarity_color: '#E4AE33', image: '/demo/item11.png' },
      { name: '★ 爪子刀 | 深红之网', price: 1580.0, rarity_color: '#E4AE33', image: '/demo/item12.png' },
      { name: '★ 猎杀者匕首 | 翡翠之网', price: 980.8, rarity_color: '#E4AE33', image: '/demo/item13.png' },
      { name: 'M4A4 | 龙王', price: 385.5, rarity_color: '#EB4B4B', image: '/demo/item14.png' },
      { name: 'USP-S | 击杀确认', price: 185.8, rarity_color: '#D32CE6', image: '/demo/item15.png' }
    ]
  }
])

// 计算属性
const selectedCase = computed(() => {
  return slotCases.value.find(c => c.case_key === selectedCaseKey.value)
})

// 选择箱子
const onCaseChange = () => {
  if (selectedCase.value) {
    console.log('选择箱子:', selectedCase.value)
    generateReelItems()
  }
}

// 生成滚轮物品
const generateReelItems = () => {
  if (!selectedCase.value) return
  
  const caseItems = selectedCase.value.items
  
  reels.forEach((reel, reelIndex) => {
    reel.items = []
    // 每个滚轮生成20个物品（重复使用箱子物品）
    for (let i = 0; i < 20; i++) {
      const randomItem = caseItems[Math.floor(Math.random() * caseItems.length)]
      reel.items.push({
        ...randomItem,
        id: `${reelIndex}-${i}`
      })
    }
    reel.position = 0
  })
  
  console.log('滚轮物品生成完成:', reels)
}

// 开始老虎机
const startSlotMachine = async () => {
  if (!selectedCase.value || isSpinning.value) return
  
  console.log('🎰 开始老虎机开箱')
  isSpinning.value = true
  winningItem.value = null
  showWinLine.value = false
  
  // 减少余额
  userBalance.value -= selectedCase.value.price
  
  // 添加开箱记录
  const recordId = Date.now()
  slotHistory.value.unshift({
    id: recordId,
    timestamp: new Date(),
    caseName: selectedCase.value.name,
    result: null,
    status: 'spinning'
  })
  
  // 确保有GSAP
  if (typeof window.gsap === 'undefined') {
    console.error('GSAP未加载')
    isSpinning.value = false
    return
  }
  
  const gsap = window.gsap
  
  // 选择获胜物品
  const winItem = selectedCase.value.items[Math.floor(Math.random() * selectedCase.value.items.length)]
  console.log('🎯 选择的获胜物品:', winItem)
  
  try {
    // 播放滚轮动画
    await playSlotAnimation(gsap, winItem)
    
    // 显示结果
    winningItem.value = winItem
    showWinLine.value = true
    
    // 更新记录
    const record = slotHistory.value.find(r => r.id === recordId)
    if (record) {
      record.result = winItem
      record.status = 'completed'
    }
    
    // 增加余额（获得物品价值）
    userBalance.value += winItem.price
    
    console.log('🎉 老虎机开箱完成!')
    
  } catch (error) {
    console.error('老虎机动画错误:', error)
  } finally {
    isSpinning.value = false
  }
}

// 播放滚轮动画
const playSlotAnimation = (gsap, winItem) => {
  return new Promise((resolve) => {
    console.log('🎬 开始播放滚轮动画')
    
    const timeline = gsap.timeline({
      onComplete: () => {
        console.log('✅ 滚轮动画完成')
        resolve()
      }
    })
    
    // 计算每个滚轮的停止位置
    const itemHeight = 80 // 每个物品的高度
    const reelHeight = 12 * itemHeight // 滚轮总高度
    
    reels.forEach((reel, index) => {
      // 计算滚动距离
      const spins = 3 + index * 0.5 // 不同滚轮不同圈数
      const finalPosition = -(spins * reelHeight + (Math.floor(Math.random() * 5) * itemHeight))
      
      // 滚轮动画（递延开始，递延停止）
      timeline.to(reel, {
        position: finalPosition,
        duration: 2 + index * 0.5, // 递延停止
        ease: "power2.out",
        onStart: () => {
          console.log(`🎯 滚轮${index + 1}开始旋转`)
        },
        onComplete: () => {
          console.log(`⏹️ 滚轮${index + 1}停止`)
        }
      }, index * 0.3) // 递延开始
    })
  })
}

// 工具函数
const getRarityClass = (color) => {
  const rarityMap = {
    '#B0C3D9': 'rarity-common',
    '#5E98D9': 'rarity-uncommon', 
    '#4B69FF': 'rarity-rare',
    '#8847FF': 'rarity-mythical',
    '#D32CE6': 'rarity-legendary',
    '#EB4B4B': 'rarity-ancient',
    '#E4AE33': 'rarity-immortal'
  }
  return rarityMap[color] || 'rarity-common'
}

const getRarityName = (color) => {
  const rarityMap = {
    '#B0C3D9': '消费级',
    '#5E98D9': '工业级',
    '#4B69FF': '军规级',
    '#8847FF': '受限',
    '#D32CE6': '保密',
    '#EB4B4B': '隐秘',
    '#E4AE33': '★极为罕见★'
  }
  return rarityMap[color] || '未知'
}

const formatTime = (date) => {
  return date.toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit',
    second: '2-digit'
  })
}

const toggleMute = () => {
  isMuted.value = !isMuted.value
  console.log('音效', isMuted.value ? '关闭' : '开启')
}

// 生命周期
onMounted(() => {
  console.log('🎰 老虎机演示已加载')
  
  // 默认选择第一个箱子
  selectedCaseKey.value = slotCases.value[0].case_key
  onCaseChange()
})
</script>

<style scoped lang="scss">
.slot-machine-demo {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f0f23, #1a1a2e, #16213e);
  padding: var(--spacing-xl);
  color: var(--color-text);

  .demo-header {
    text-align: center;
    margin-bottom: var(--spacing-3xl);

    .demo-title {
      font-size: 3.5rem;
      font-weight: bold;
      background: linear-gradient(45deg, #ffd700, #ffed4e, #ffd700);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-bottom: var(--spacing-md);
      text-shadow: 0 0 30px rgba(255, 215, 0, 0.3);
    }

    .demo-subtitle {
      font-size: 1.3rem;
      color: var(--color-text-secondary);
    }
  }

  .slot-machine-container {
    width: 100%;
    max-width: 50rem;
    margin: 0 auto;
    padding: 2rem;
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    border-radius: 1rem;
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.5);
    border: 2px solid #ffd700;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: -4px;
      left: -4px;
      right: -4px;
      bottom: -4px;
      border: 2px solid rgba(255, 215, 0, 0.3);
      border-radius: 1.2rem;
      pointer-events: none;
    }
  }

  .case-info {
    text-align: center;
    margin-bottom: 2rem;

    .case-image {
      width: 8rem;
      height: 8rem;
      object-fit: cover;
      border-radius: 0.5rem;
      margin-bottom: 1rem;
      transition: transform 0.3s ease;
      border: 2px solid var(--color-border);

      &:hover {
        transform: scale(1.05);
      }
    }

    .case-selector {
      margin-bottom: 1rem;

      .case-select {
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        border: 1px solid var(--color-border);
        background: rgba(255, 255, 255, 0.1);
        color: var(--color-text);
        font-size: 1rem;
        min-width: 300px;

        option {
          background: var(--color-background);
          color: var(--color-text);
        }
      }
    }

    .open-case-btn {
      padding: 0.75rem 2rem;
      font-size: 1.2rem;
      font-weight: bold;
      background: linear-gradient(45deg, #ff6b6b, #feca57);
      border: none;
      border-radius: 2rem;
      color: white;
      cursor: pointer;
      transition: all 0.3s ease;
      margin-bottom: 0.5rem;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);

      &:hover:not(:disabled) {
        transform: translateY(-0.2rem);
        box-shadow: 0 0.5rem 1rem rgba(255, 107, 107, 0.4);
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
      }
    }

    .balance {
      color: #ffd700;
      font-size: 1.1rem;
      font-weight: bold;
      text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
    }
  }

  .slot-machine {
    position: relative;
    background: linear-gradient(145deg, #2c3e50, #34495e);
    border-radius: 1rem;
    padding: 2rem 1rem;
    box-shadow: inset 0 0.5rem 1rem rgba(0, 0, 0, 0.3);

    .machine-frame {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border: 0.3rem solid #ffd700;
      border-radius: 1rem;
      pointer-events: none;

      &::before {
        content: '';
        position: absolute;
        top: -0.5rem;
        left: -0.5rem;
        right: -0.5rem;
        bottom: -0.5rem;
        border: 0.2rem solid rgba(255, 215, 0, 0.3);
        border-radius: 1.2rem;
      }
    }

    .reels-container {
      display: flex;
      justify-content: center;
      gap: 1rem;
      margin-bottom: 1rem;
      position: relative;
      z-index: 2;

      .reel {
        position: relative;
        width: 8rem;
        height: 15rem;
        background: #000;
        border: 0.2rem solid #333;
        border-radius: 0.5rem;
        overflow: hidden;

        .reel-strip {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          display: flex;
          flex-direction: column;

          .reel-item {
            width: 100%;
            height: 5rem;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border-bottom: 0.1rem solid #333;
            padding: 0.5rem;
            transition: all 0.3s ease;

            .item-image {
              width: 3rem;
              height: 3rem;
              object-fit: cover;
              border-radius: 0.25rem;
              margin-bottom: 0.25rem;
            }

            .item-name {
              font-size: 0.7rem;
              color: white;
              text-align: center;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              width: 100%;
            }

            // 稀有度边框颜色
            &.rarity-common { border-left: 0.2rem solid #B0C3D9; }
            &.rarity-uncommon { border-left: 0.2rem solid #5E98D9; }
            &.rarity-rare { border-left: 0.2rem solid #4B69FF; }
            &.rarity-mythical { border-left: 0.2rem solid #8847FF; }
            &.rarity-legendary { border-left: 0.2rem solid #D32CE6; }
            &.rarity-ancient { border-left: 0.2rem solid #EB4B4B; }
            &.rarity-immortal { border-left: 0.2rem solid #E4AE33; }
          }
        }

        .reel-window {
          position: absolute;
          top: 50%;
          left: 0;
          right: 0;
          height: 5rem;
          transform: translateY(-50%);
          border: 0.2rem solid #ffd700;
          border-radius: 0.25rem;
          pointer-events: none;
          box-shadow: 0 0 20px rgba(255, 215, 0, 0.6);

          &::before, &::after {
            content: '';
            position: absolute;
            left: -0.5rem;
            right: -0.5rem;
            height: 0.1rem;
            background: linear-gradient(90deg, transparent, #ffd700, transparent);
          }

          &::before { top: -0.1rem; }
          &::after { bottom: -0.1rem; }
        }
      }
    }

    .win-line {
      position: absolute;
      top: 50%;
      left: 1rem;
      right: 1rem;
      height: 0.2rem;
      background: linear-gradient(90deg, transparent, #ff0000, #ff0000, transparent);
      transform: translateY(-50%);
      opacity: 0;
      transition: opacity 0.5s ease;
      pointer-events: none;
      z-index: 3;

      &.active {
        opacity: 1;
        animation: win-line-glow 1s ease-in-out infinite alternate;
      }
    }

    .result-display {
      margin-top: 2rem;
      text-align: center;

      .winning-item {
        display: inline-flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem;
        border-radius: 0.5rem;
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid #ffd700;
        animation: result-appear 0.8s ease-out;

        .item-image {
          width: 4rem;
          height: 4rem;
          object-fit: cover;
          border-radius: 0.25rem;
        }

        .item-info {
          display: flex;
          flex-direction: column;
          align-items: flex-start;

          .item-name {
            font-size: 1.1rem;
            font-weight: bold;
            color: var(--color-text);
            margin-bottom: 0.25rem;
          }

          .item-value {
            font-size: 1.3rem;
            font-weight: bold;
            color: #ffd700;
            margin-bottom: 0.25rem;
          }

          .item-rarity {
            font-size: 0.9rem;
            opacity: 0.8;
          }
        }

        // 稀有度背景
        &.rarity-immortal {
          background: linear-gradient(135deg, rgba(228, 174, 51, 0.2), rgba(228, 174, 51, 0.05));
          box-shadow: 0 0 30px rgba(228, 174, 51, 0.3);
        }
      }
    }
  }

  .audio-controls {
    position: absolute;
    top: 1rem;
    right: 1rem;

    .mute-toggle {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid var(--color-border);
      border-radius: 50%;
      width: 3rem;
      height: 3rem;
      cursor: pointer;
      font-size: 1.2rem;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
      }
    }
  }

  .slot-history {
    max-width: 50rem;
    margin: 3rem auto 0;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 1rem;
    padding: 2rem;
    border: 1px solid var(--color-border);

    .history-title {
      font-size: 1.5rem;
      font-weight: bold;
      margin-bottom: 1.5rem;
      text-align: center;
      color: #ffd700;
    }

    .history-list {
      display: flex;
      flex-direction: column;
      gap: 1rem;
      max-height: 400px;
      overflow-y: auto;

      .history-item {
        display: grid;
        grid-template-columns: 100px 200px 1fr 120px;
        gap: 1rem;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 0.5rem;
        align-items: center;
        border: 1px solid var(--color-border);

        &.spinning {
          border-color: #feca57;
          background: rgba(254, 202, 87, 0.1);
        }

        &.completed {
          border-color: #4CAF50;
          background: rgba(76, 175, 80, 0.1);
        }

        .record-time {
          font-size: 0.9rem;
          color: var(--color-text-secondary);
        }

        .record-case {
          font-weight: 500;
        }

        .record-result {
          display: flex;
          align-items: center;
          gap: 0.5rem;

          img {
            width: 32px;
            height: 32px;
            border-radius: 4px;
          }

          .result-name {
            font-size: 0.9rem;
          }

          .result-value {
            font-weight: bold;
            font-size: 1rem;
          }
        }

        .status-badge {
          display: flex;
          align-items: center;
          gap: 0.25rem;
          padding: 0.25rem 0.5rem;
          border-radius: 0.25rem;
          font-size: 0.8rem;

          &.spinning {
            background: #feca57;
            color: #000;
          }

          &.completed {
            background: #4CAF50;
            color: white;
          }

          .spinner {
            width: 12px;
            height: 12px;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
          }
        }
      }
    }
  }
}

// 动画效果
@keyframes win-line-glow {
  0% {
    box-shadow: 0 0 5px #ff0000;
  }
  100% {
    box-shadow: 0 0 20px #ff0000, 0 0 30px #ff0000;
  }
}

@keyframes result-appear {
  0% {
    transform: scale(0.5) rotateY(-90deg);
    opacity: 0;
  }
  50% {
    transform: scale(1.1) rotateY(-45deg);
  }
  100% {
    transform: scale(1) rotateY(0deg);
    opacity: 1;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .slot-machine-demo {
    padding: 1rem;

    .demo-header .demo-title {
      font-size: 2.5rem;
    }

    .slot-machine-container {
      padding: 1rem;

      .case-info {
        .case-image {
          width: 6rem;
          height: 6rem;
        }

        .case-selector .case-select {
          min-width: 250px;
          font-size: 0.9rem;
        }
      }

      .slot-machine .reels-container {
        gap: 0.5rem;

        .reel {
          width: 6rem;
          height: 12rem;

          .reel-strip .reel-item {
            height: 4rem;

            .item-image {
              width: 2.5rem;
              height: 2.5rem;
            }

            .item-name {
              font-size: 0.6rem;
            }
          }

          .reel-window {
            height: 4rem;
          }
        }
      }
    }

    .slot-history .history-list .history-item {
      grid-template-columns: 1fr;
      gap: 0.5rem;
      text-align: center;
    }
  }
}
</style>
