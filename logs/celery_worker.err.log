ERROR:celery.utils.dispatch.signal:Signal handler <bound method DjangoFixup.on_import_modules of <celery.fixups.django.DjangoFixup object at 0x7f8007ecc9d0>> raised: ModuleNotFoundError("No module named 'django_ckeditor_5'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 91, in populate
    app_config = AppConfig.create(entry)
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 224, in create
    import_module(entry)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 973, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'django_ckeditor_5'
ERROR:celery.utils.dispatch.signal:Signal handler <promise@0x7f8007ee8900 --> <bound method Celery._autodiscover_tasks of <Celery steambase at 0x7f8008ad0340>>> raised: AppRegistryNotReady("Apps aren't loaded yet.")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 161, in __call__
    return self.throw()
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 158, in __call__
    retval = fun(*final_args, **final_kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 788, in _autodiscover_tasks
    return self._autodiscover_tasks_from_fixups(related_name)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 797, in _autodiscover_tasks_from_fixups
    return self._autodiscover_tasks_from_names([
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 800, in <listcomp>
    for pkg in fixup.autodiscover_tasks()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 111, in autodiscover_tasks
    return [config.name for config in apps.get_app_configs()]
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 145, in get_app_configs
    self.check_apps_ready()
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 136, in check_apps_ready
    raise AppRegistryNotReady("Apps aren't loaded yet.")
django.core.exceptions.AppRegistryNotReady: Apps aren't loaded yet.
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/runpy.py", line 194, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/local/lib/python3.8/runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 19, in <module>
    main()
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 15, in main
    sys.exit(_main())
  File "/usr/local/lib/python3.8/site-packages/celery/bin/celery.py", line 231, in main
    return celery(auto_envvar_prefix="CELERY")
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1082, in main
    rv = self.invoke(ctx)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 788, in invoke
    return __callback(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/decorators.py", line 33, in new_func
    return f(get_current_context(), *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/base.py", line 135, in caller
    return f(ctx, *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/worker.py", line 348, in worker
    worker = app.Worker(
  File "/usr/local/lib/python3.8/site-packages/celery/worker/worker.py", line 94, in __init__
    self.app.loader.init_worker()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 110, in init_worker
    self.import_default_modules()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 104, in import_default_modules
    raise response
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 91, in populate
    app_config = AppConfig.create(entry)
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 224, in create
    import_module(entry)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 973, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'django_ckeditor_5'
ERROR:celery.utils.dispatch.signal:Signal handler <bound method DjangoFixup.on_import_modules of <celery.fixups.django.DjangoFixup object at 0x7f193f7539d0>> raised: ModuleNotFoundError("No module named 'django_ckeditor_5'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 91, in populate
    app_config = AppConfig.create(entry)
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 224, in create
    import_module(entry)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 973, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'django_ckeditor_5'
ERROR:celery.utils.dispatch.signal:Signal handler <promise@0x7f193f76f900 --> <bound method Celery._autodiscover_tasks of <Celery steambase at 0x7f19402fe340>>> raised: AppRegistryNotReady("Apps aren't loaded yet.")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 161, in __call__
    return self.throw()
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 158, in __call__
    retval = fun(*final_args, **final_kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 788, in _autodiscover_tasks
    return self._autodiscover_tasks_from_fixups(related_name)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 797, in _autodiscover_tasks_from_fixups
    return self._autodiscover_tasks_from_names([
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 800, in <listcomp>
    for pkg in fixup.autodiscover_tasks()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 111, in autodiscover_tasks
    return [config.name for config in apps.get_app_configs()]
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 145, in get_app_configs
    self.check_apps_ready()
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 136, in check_apps_ready
    raise AppRegistryNotReady("Apps aren't loaded yet.")
django.core.exceptions.AppRegistryNotReady: Apps aren't loaded yet.
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/runpy.py", line 194, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/local/lib/python3.8/runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 19, in <module>
    main()
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 15, in main
    sys.exit(_main())
  File "/usr/local/lib/python3.8/site-packages/celery/bin/celery.py", line 231, in main
    return celery(auto_envvar_prefix="CELERY")
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1082, in main
    rv = self.invoke(ctx)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 788, in invoke
    return __callback(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/decorators.py", line 33, in new_func
    return f(get_current_context(), *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/base.py", line 135, in caller
    return f(ctx, *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/worker.py", line 348, in worker
    worker = app.Worker(
  File "/usr/local/lib/python3.8/site-packages/celery/worker/worker.py", line 94, in __init__
    self.app.loader.init_worker()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 110, in init_worker
    self.import_default_modules()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 104, in import_default_modules
    raise response
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 91, in populate
    app_config = AppConfig.create(entry)
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 224, in create
    import_module(entry)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 973, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'django_ckeditor_5'
ERROR:celery.utils.dispatch.signal:Signal handler <bound method DjangoFixup.on_import_modules of <celery.fixups.django.DjangoFixup object at 0x7feecf7539d0>> raised: ModuleNotFoundError("No module named 'django_ckeditor_5'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 91, in populate
    app_config = AppConfig.create(entry)
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 224, in create
    import_module(entry)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 973, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'django_ckeditor_5'
ERROR:celery.utils.dispatch.signal:Signal handler <promise@0x7feecf76f900 --> <bound method Celery._autodiscover_tasks of <Celery steambase at 0x7feed02ec340>>> raised: AppRegistryNotReady("Apps aren't loaded yet.")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 161, in __call__
    return self.throw()
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 158, in __call__
    retval = fun(*final_args, **final_kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 788, in _autodiscover_tasks
    return self._autodiscover_tasks_from_fixups(related_name)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 797, in _autodiscover_tasks_from_fixups
    return self._autodiscover_tasks_from_names([
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 800, in <listcomp>
    for pkg in fixup.autodiscover_tasks()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 111, in autodiscover_tasks
    return [config.name for config in apps.get_app_configs()]
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 145, in get_app_configs
    self.check_apps_ready()
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 136, in check_apps_ready
    raise AppRegistryNotReady("Apps aren't loaded yet.")
django.core.exceptions.AppRegistryNotReady: Apps aren't loaded yet.
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/runpy.py", line 194, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/local/lib/python3.8/runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 19, in <module>
    main()
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 15, in main
    sys.exit(_main())
  File "/usr/local/lib/python3.8/site-packages/celery/bin/celery.py", line 231, in main
    return celery(auto_envvar_prefix="CELERY")
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1082, in main
    rv = self.invoke(ctx)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 788, in invoke
    return __callback(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/decorators.py", line 33, in new_func
    return f(get_current_context(), *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/base.py", line 135, in caller
    return f(ctx, *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/worker.py", line 348, in worker
    worker = app.Worker(
  File "/usr/local/lib/python3.8/site-packages/celery/worker/worker.py", line 94, in __init__
    self.app.loader.init_worker()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 110, in init_worker
    self.import_default_modules()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 104, in import_default_modules
    raise response
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 91, in populate
    app_config = AppConfig.create(entry)
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 224, in create
    import_module(entry)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 973, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'django_ckeditor_5'
ERROR:celery.utils.dispatch.signal:Signal handler <bound method DjangoFixup.on_import_modules of <celery.fixups.django.DjangoFixup object at 0x7f192a5139d0>> raised: ModuleNotFoundError("No module named 'django_ckeditor_5'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 91, in populate
    app_config = AppConfig.create(entry)
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 224, in create
    import_module(entry)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 973, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'django_ckeditor_5'
ERROR:celery.utils.dispatch.signal:Signal handler <promise@0x7f192a52f900 --> <bound method Celery._autodiscover_tasks of <Celery steambase at 0x7f192b099340>>> raised: AppRegistryNotReady("Apps aren't loaded yet.")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 161, in __call__
    return self.throw()
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 158, in __call__
    retval = fun(*final_args, **final_kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 788, in _autodiscover_tasks
    return self._autodiscover_tasks_from_fixups(related_name)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 797, in _autodiscover_tasks_from_fixups
    return self._autodiscover_tasks_from_names([
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 800, in <listcomp>
    for pkg in fixup.autodiscover_tasks()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 111, in autodiscover_tasks
    return [config.name for config in apps.get_app_configs()]
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 145, in get_app_configs
    self.check_apps_ready()
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 136, in check_apps_ready
    raise AppRegistryNotReady("Apps aren't loaded yet.")
django.core.exceptions.AppRegistryNotReady: Apps aren't loaded yet.
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/runpy.py", line 194, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/local/lib/python3.8/runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 19, in <module>
    main()
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 15, in main
    sys.exit(_main())
  File "/usr/local/lib/python3.8/site-packages/celery/bin/celery.py", line 231, in main
    return celery(auto_envvar_prefix="CELERY")
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1082, in main
    rv = self.invoke(ctx)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 788, in invoke
    return __callback(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/decorators.py", line 33, in new_func
    return f(get_current_context(), *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/base.py", line 135, in caller
    return f(ctx, *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/worker.py", line 348, in worker
    worker = app.Worker(
  File "/usr/local/lib/python3.8/site-packages/celery/worker/worker.py", line 94, in __init__
    self.app.loader.init_worker()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 110, in init_worker
    self.import_default_modules()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 104, in import_default_modules
    raise response
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 91, in populate
    app_config = AppConfig.create(entry)
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 224, in create
    import_module(entry)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 973, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'django_ckeditor_5'
DEBUG:asyncio:Using selector: EpollSelector
ERROR:celery.utils.dispatch.signal:Signal handler <bound method DjangoFixup.on_import_modules of <celery.fixups.django.DjangoFixup object at 0x7fa3942609d0>> raised: ModuleNotFoundError("No module named 'aenum'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
ERROR:celery.utils.dispatch.signal:Signal handler <promise@0x7fa39427c900 --> <bound method Celery._autodiscover_tasks of <Celery steambase at 0x7fa3944e1340>>> raised: ModuleNotFoundError("No module named 'aenum'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 161, in __call__
    return self.throw()
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 158, in __call__
    retval = fun(*final_args, **final_kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 788, in _autodiscover_tasks
    return self._autodiscover_tasks_from_fixups(related_name)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 797, in _autodiscover_tasks_from_fixups
    return self._autodiscover_tasks_from_names([
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 792, in _autodiscover_tasks_from_names
    return self.loader.autodiscover_tasks(
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 220, in autodiscover_tasks
    mod.__name__ for mod in autodiscover_tasks(packages or (),
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 246, in autodiscover_tasks
    return [find_related_module(pkg, related_name) for pkg in packages]
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 246, in <listcomp>
    return [find_related_module(pkg, related_name) for pkg in packages]
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 278, in find_related_module
    raise e
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 270, in find_related_module
    return importlib.import_module(module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/tasks.py", line 7, in <module>
    from authentication.models import UserBalanceRecord, UserAsset, AuthUser, UserExtra, UserActivePointRecord
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/runpy.py", line 194, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/local/lib/python3.8/runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 19, in <module>
    main()
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 15, in main
    sys.exit(_main())
  File "/usr/local/lib/python3.8/site-packages/celery/bin/celery.py", line 231, in main
    return celery(auto_envvar_prefix="CELERY")
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1082, in main
    rv = self.invoke(ctx)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 788, in invoke
    return __callback(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/decorators.py", line 33, in new_func
    return f(get_current_context(), *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/base.py", line 135, in caller
    return f(ctx, *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/worker.py", line 348, in worker
    worker = app.Worker(
  File "/usr/local/lib/python3.8/site-packages/celery/worker/worker.py", line 94, in __init__
    self.app.loader.init_worker()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 110, in init_worker
    self.import_default_modules()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 104, in import_default_modules
    raise response
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
DEBUG:asyncio:Using selector: EpollSelector
ERROR:celery.utils.dispatch.signal:Signal handler <bound method DjangoFixup.on_import_modules of <celery.fixups.django.DjangoFixup object at 0x7f8b5b05f9d0>> raised: ModuleNotFoundError("No module named 'aenum'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
ERROR:celery.utils.dispatch.signal:Signal handler <promise@0x7f8b5b07c900 --> <bound method Celery._autodiscover_tasks of <Celery steambase at 0x7f8b5b1e0340>>> raised: ModuleNotFoundError("No module named 'aenum'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 161, in __call__
    return self.throw()
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 158, in __call__
    retval = fun(*final_args, **final_kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 788, in _autodiscover_tasks
    return self._autodiscover_tasks_from_fixups(related_name)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 797, in _autodiscover_tasks_from_fixups
    return self._autodiscover_tasks_from_names([
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 792, in _autodiscover_tasks_from_names
    return self.loader.autodiscover_tasks(
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 220, in autodiscover_tasks
    mod.__name__ for mod in autodiscover_tasks(packages or (),
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 246, in autodiscover_tasks
    return [find_related_module(pkg, related_name) for pkg in packages]
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 246, in <listcomp>
    return [find_related_module(pkg, related_name) for pkg in packages]
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 278, in find_related_module
    raise e
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 270, in find_related_module
    return importlib.import_module(module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/tasks.py", line 7, in <module>
    from authentication.models import UserBalanceRecord, UserAsset, AuthUser, UserExtra, UserActivePointRecord
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/runpy.py", line 194, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/local/lib/python3.8/runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 19, in <module>
    main()
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 15, in main
    sys.exit(_main())
  File "/usr/local/lib/python3.8/site-packages/celery/bin/celery.py", line 231, in main
    return celery(auto_envvar_prefix="CELERY")
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1082, in main
    rv = self.invoke(ctx)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 788, in invoke
    return __callback(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/decorators.py", line 33, in new_func
    return f(get_current_context(), *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/base.py", line 135, in caller
    return f(ctx, *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/worker.py", line 348, in worker
    worker = app.Worker(
  File "/usr/local/lib/python3.8/site-packages/celery/worker/worker.py", line 94, in __init__
    self.app.loader.init_worker()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 110, in init_worker
    self.import_default_modules()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 104, in import_default_modules
    raise response
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
DEBUG:asyncio:Using selector: EpollSelector
ERROR:celery.utils.dispatch.signal:Signal handler <bound method DjangoFixup.on_import_modules of <celery.fixups.django.DjangoFixup object at 0x7f1b4f67e9d0>> raised: ModuleNotFoundError("No module named 'aenum'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
ERROR:celery.utils.dispatch.signal:Signal handler <promise@0x7f1b4f69c900 --> <bound method Celery._autodiscover_tasks of <Celery steambase at 0x7f1b4f800340>>> raised: ModuleNotFoundError("No module named 'aenum'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 161, in __call__
    return self.throw()
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 158, in __call__
    retval = fun(*final_args, **final_kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 788, in _autodiscover_tasks
    return self._autodiscover_tasks_from_fixups(related_name)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 797, in _autodiscover_tasks_from_fixups
    return self._autodiscover_tasks_from_names([
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 792, in _autodiscover_tasks_from_names
    return self.loader.autodiscover_tasks(
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 220, in autodiscover_tasks
    mod.__name__ for mod in autodiscover_tasks(packages or (),
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 246, in autodiscover_tasks
    return [find_related_module(pkg, related_name) for pkg in packages]
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 246, in <listcomp>
    return [find_related_module(pkg, related_name) for pkg in packages]
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 278, in find_related_module
    raise e
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 270, in find_related_module
    return importlib.import_module(module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/tasks.py", line 7, in <module>
    from authentication.models import UserBalanceRecord, UserAsset, AuthUser, UserExtra, UserActivePointRecord
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/runpy.py", line 194, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/local/lib/python3.8/runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 19, in <module>
    main()
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 15, in main
    sys.exit(_main())
  File "/usr/local/lib/python3.8/site-packages/celery/bin/celery.py", line 231, in main
    return celery(auto_envvar_prefix="CELERY")
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1082, in main
    rv = self.invoke(ctx)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 788, in invoke
    return __callback(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/decorators.py", line 33, in new_func
    return f(get_current_context(), *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/base.py", line 135, in caller
    return f(ctx, *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/worker.py", line 348, in worker
    worker = app.Worker(
  File "/usr/local/lib/python3.8/site-packages/celery/worker/worker.py", line 94, in __init__
    self.app.loader.init_worker()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 110, in init_worker
    self.import_default_modules()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 104, in import_default_modules
    raise response
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
DEBUG:asyncio:Using selector: EpollSelector
ERROR:celery.utils.dispatch.signal:Signal handler <bound method DjangoFixup.on_import_modules of <celery.fixups.django.DjangoFixup object at 0x7f2c338589d0>> raised: ModuleNotFoundError("No module named 'aenum'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
ERROR:celery.utils.dispatch.signal:Signal handler <promise@0x7f2c33874900 --> <bound method Celery._autodiscover_tasks of <Celery steambase at 0x7f2c339d9340>>> raised: ModuleNotFoundError("No module named 'aenum'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 161, in __call__
    return self.throw()
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 158, in __call__
    retval = fun(*final_args, **final_kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 788, in _autodiscover_tasks
    return self._autodiscover_tasks_from_fixups(related_name)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 797, in _autodiscover_tasks_from_fixups
    return self._autodiscover_tasks_from_names([
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 792, in _autodiscover_tasks_from_names
    return self.loader.autodiscover_tasks(
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 220, in autodiscover_tasks
    mod.__name__ for mod in autodiscover_tasks(packages or (),
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 246, in autodiscover_tasks
    return [find_related_module(pkg, related_name) for pkg in packages]
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 246, in <listcomp>
    return [find_related_module(pkg, related_name) for pkg in packages]
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 278, in find_related_module
    raise e
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 270, in find_related_module
    return importlib.import_module(module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/tasks.py", line 7, in <module>
    from authentication.models import UserBalanceRecord, UserAsset, AuthUser, UserExtra, UserActivePointRecord
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/runpy.py", line 194, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/local/lib/python3.8/runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 19, in <module>
    main()
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 15, in main
    sys.exit(_main())
  File "/usr/local/lib/python3.8/site-packages/celery/bin/celery.py", line 231, in main
    return celery(auto_envvar_prefix="CELERY")
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1082, in main
    rv = self.invoke(ctx)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 788, in invoke
    return __callback(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/decorators.py", line 33, in new_func
    return f(get_current_context(), *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/base.py", line 135, in caller
    return f(ctx, *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/worker.py", line 348, in worker
    worker = app.Worker(
  File "/usr/local/lib/python3.8/site-packages/celery/worker/worker.py", line 94, in __init__
    self.app.loader.init_worker()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 110, in init_worker
    self.import_default_modules()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 104, in import_default_modules
    raise response
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
DEBUG:asyncio:Using selector: EpollSelector
ERROR:celery.utils.dispatch.signal:Signal handler <bound method DjangoFixup.on_import_modules of <celery.fixups.django.DjangoFixup object at 0x7f6c9f82b9d0>> raised: ModuleNotFoundError("No module named 'aenum'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
ERROR:celery.utils.dispatch.signal:Signal handler <promise@0x7f6c9f848900 --> <bound method Celery._autodiscover_tasks of <Celery steambase at 0x7f6c9f9ac340>>> raised: ModuleNotFoundError("No module named 'aenum'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 161, in __call__
    return self.throw()
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 158, in __call__
    retval = fun(*final_args, **final_kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 788, in _autodiscover_tasks
    return self._autodiscover_tasks_from_fixups(related_name)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 797, in _autodiscover_tasks_from_fixups
    return self._autodiscover_tasks_from_names([
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 792, in _autodiscover_tasks_from_names
    return self.loader.autodiscover_tasks(
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 220, in autodiscover_tasks
    mod.__name__ for mod in autodiscover_tasks(packages or (),
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 246, in autodiscover_tasks
    return [find_related_module(pkg, related_name) for pkg in packages]
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 246, in <listcomp>
    return [find_related_module(pkg, related_name) for pkg in packages]
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 278, in find_related_module
    raise e
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 270, in find_related_module
    return importlib.import_module(module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/tasks.py", line 7, in <module>
    from authentication.models import UserBalanceRecord, UserAsset, AuthUser, UserExtra, UserActivePointRecord
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/runpy.py", line 194, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/local/lib/python3.8/runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 19, in <module>
    main()
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 15, in main
    sys.exit(_main())
  File "/usr/local/lib/python3.8/site-packages/celery/bin/celery.py", line 231, in main
    return celery(auto_envvar_prefix="CELERY")
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1082, in main
    rv = self.invoke(ctx)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 788, in invoke
    return __callback(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/decorators.py", line 33, in new_func
    return f(get_current_context(), *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/base.py", line 135, in caller
    return f(ctx, *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/worker.py", line 348, in worker
    worker = app.Worker(
  File "/usr/local/lib/python3.8/site-packages/celery/worker/worker.py", line 94, in __init__
    self.app.loader.init_worker()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 110, in init_worker
    self.import_default_modules()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 104, in import_default_modules
    raise response
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
DEBUG:asyncio:Using selector: EpollSelector
ERROR:celery.utils.dispatch.signal:Signal handler <bound method DjangoFixup.on_import_modules of <celery.fixups.django.DjangoFixup object at 0x7f05164d39d0>> raised: ModuleNotFoundError("No module named 'aenum'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
ERROR:celery.utils.dispatch.signal:Signal handler <promise@0x7f05164f0900 --> <bound method Celery._autodiscover_tasks of <Celery steambase at 0x7f051707b340>>> raised: ModuleNotFoundError("No module named 'aenum'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 161, in __call__
    return self.throw()
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 158, in __call__
    retval = fun(*final_args, **final_kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 788, in _autodiscover_tasks
    return self._autodiscover_tasks_from_fixups(related_name)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 797, in _autodiscover_tasks_from_fixups
    return self._autodiscover_tasks_from_names([
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 792, in _autodiscover_tasks_from_names
    return self.loader.autodiscover_tasks(
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 220, in autodiscover_tasks
    mod.__name__ for mod in autodiscover_tasks(packages or (),
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 246, in autodiscover_tasks
    return [find_related_module(pkg, related_name) for pkg in packages]
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 246, in <listcomp>
    return [find_related_module(pkg, related_name) for pkg in packages]
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 278, in find_related_module
    raise e
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 270, in find_related_module
    return importlib.import_module(module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/tasks.py", line 7, in <module>
    from authentication.models import UserBalanceRecord, UserAsset, AuthUser, UserExtra, UserActivePointRecord
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/runpy.py", line 194, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/local/lib/python3.8/runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 19, in <module>
    main()
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 15, in main
    sys.exit(_main())
  File "/usr/local/lib/python3.8/site-packages/celery/bin/celery.py", line 231, in main
    return celery(auto_envvar_prefix="CELERY")
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1082, in main
    rv = self.invoke(ctx)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 788, in invoke
    return __callback(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/decorators.py", line 33, in new_func
    return f(get_current_context(), *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/base.py", line 135, in caller
    return f(ctx, *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/worker.py", line 348, in worker
    worker = app.Worker(
  File "/usr/local/lib/python3.8/site-packages/celery/worker/worker.py", line 94, in __init__
    self.app.loader.init_worker()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 110, in init_worker
    self.import_default_modules()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 104, in import_default_modules
    raise response
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
DEBUG:asyncio:Using selector: EpollSelector
ERROR:celery.utils.dispatch.signal:Signal handler <bound method DjangoFixup.on_import_modules of <celery.fixups.django.DjangoFixup object at 0x7f91482ba9d0>> raised: ModuleNotFoundError("No module named 'aenum'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
ERROR:celery.utils.dispatch.signal:Signal handler <promise@0x7f91482d7900 --> <bound method Celery._autodiscover_tasks of <Celery steambase at 0x7f914843b340>>> raised: ModuleNotFoundError("No module named 'aenum'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 161, in __call__
    return self.throw()
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 158, in __call__
    retval = fun(*final_args, **final_kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 788, in _autodiscover_tasks
    return self._autodiscover_tasks_from_fixups(related_name)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 797, in _autodiscover_tasks_from_fixups
    return self._autodiscover_tasks_from_names([
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 792, in _autodiscover_tasks_from_names
    return self.loader.autodiscover_tasks(
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 220, in autodiscover_tasks
    mod.__name__ for mod in autodiscover_tasks(packages or (),
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 246, in autodiscover_tasks
    return [find_related_module(pkg, related_name) for pkg in packages]
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 246, in <listcomp>
    return [find_related_module(pkg, related_name) for pkg in packages]
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 278, in find_related_module
    raise e
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 270, in find_related_module
    return importlib.import_module(module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/tasks.py", line 7, in <module>
    from authentication.models import UserBalanceRecord, UserAsset, AuthUser, UserExtra, UserActivePointRecord
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/runpy.py", line 194, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/local/lib/python3.8/runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 19, in <module>
    main()
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 15, in main
    sys.exit(_main())
  File "/usr/local/lib/python3.8/site-packages/celery/bin/celery.py", line 231, in main
    return celery(auto_envvar_prefix="CELERY")
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1082, in main
    rv = self.invoke(ctx)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 788, in invoke
    return __callback(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/decorators.py", line 33, in new_func
    return f(get_current_context(), *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/base.py", line 135, in caller
    return f(ctx, *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/worker.py", line 348, in worker
    worker = app.Worker(
  File "/usr/local/lib/python3.8/site-packages/celery/worker/worker.py", line 94, in __init__
    self.app.loader.init_worker()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 110, in init_worker
    self.import_default_modules()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 104, in import_default_modules
    raise response
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
DEBUG:asyncio:Using selector: EpollSelector
DEBUG:celery.utils.functional:
def update_new_user_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def hourly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def case_pk_join_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def accumulate(self, *args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def cleanup_duplicate_roll_room_bets():
    return 1

DEBUG:celery.utils.functional:
def update_domain_verify_switch(value):
    return 1

DEBUG:celery.utils.functional:
def roll_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def unlock_chord(self, group_id, callback, interval=0, max_retries=1, result=2, Result=3, GroupResult=4, result_from_tuple=5, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def check_envelop_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_daily_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def xmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def delete_old_balance_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def daily_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def xstarmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def update_enable_based_on_coins(min_coins, max_coins):
    return 1

DEBUG:celery.utils.functional:
def check_room_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def chunks(task, it, n):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache(count, batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache_task(self, count=0):
    return 1

DEBUG:celery.utils.functional:
def check_pendclose_trade_task(self):
    return 1

DEBUG:celery.utils.functional:
def group(self, tasks, result, group_id, partial_args, add_to_parent=0):
    return 1

DEBUG:celery.utils.functional:
def update_enable_rule_exam(value):
    return 1

DEBUG:celery.utils.functional:
def delete_old_log_entries(batch_size=0, days=1):
    return 1

DEBUG:celery.utils.functional:
def weekly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def chain(*args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_enable_pay_method(enable_list):
    return 1

DEBUG:celery.utils.functional:
def zbt_buy_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def chord(self, header, body, partial_args=0, interval=1, countdown=2, max_retries=3, eager=4, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def delete_old_case_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def check_b2c_trade_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_monitor_data_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_register_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def case_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_staff_balance():
    return 1

DEBUG:celery.utils.functional:
def update_today_amount():
    return 1

DEBUG:celery.utils.functional:
def case_pk_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def init_system_task(self):
    return 1

DEBUG:celery.utils.functional:
def backend_cleanup():
    return 1

DEBUG:celery.utils.functional:
def delete_old_package_items(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def sync_items_price_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_recharge_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def case_bot_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_user_active_point_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_month_amount():
    return 1

/usr/local/lib/python3.8/site-packages/celery/platforms.py:841: SecurityWarning: You're running the worker with superuser privileges: this is
absolutely not recommended!

Please specify a different user using the --uid option.

User information: uid=0 euid=0 gid=0 egid=0

  warnings.warn(SecurityWarning(ROOT_DISCOURAGED.format(
[2025-07-18 10:20:38,697: INFO/MainProcess] Connected to redis://localhost:6379/0
[2025-07-18 10:20:38,712: INFO/MainProcess] mingle: searching for neighbors
[2025-07-18 10:20:39,782: INFO/MainProcess] mingle: all alone
[2025-07-18 10:20:39,804: INFO/MainProcess] celery@iZj6c3ddfpik1c7yalqdt4Z ready.
DEBUG:asyncio:Using selector: EpollSelector
DEBUG:celery.utils.functional:
def update_new_user_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def hourly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def accumulate(self, *args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def cleanup_duplicate_roll_room_bets():
    return 1

DEBUG:celery.utils.functional:
def update_domain_verify_switch(value):
    return 1

DEBUG:celery.utils.functional:
def roll_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def unlock_chord(self, group_id, callback, interval=0, max_retries=1, result=2, Result=3, GroupResult=4, result_from_tuple=5, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def check_envelop_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_daily_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def xmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def delete_old_balance_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def daily_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def xstarmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def update_enable_based_on_coins(min_coins, max_coins):
    return 1

DEBUG:celery.utils.functional:
def check_room_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def case_pk_join_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def chunks(task, it, n):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache(count, batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache_task(self, count=0):
    return 1

DEBUG:celery.utils.functional:
def check_pendclose_trade_task(self):
    return 1

DEBUG:celery.utils.functional:
def group(self, tasks, result, group_id, partial_args, add_to_parent=0):
    return 1

DEBUG:celery.utils.functional:
def update_enable_rule_exam(value):
    return 1

DEBUG:celery.utils.functional:
def delete_old_log_entries(batch_size=0, days=1):
    return 1

DEBUG:celery.utils.functional:
def weekly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def chain(*args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_enable_pay_method(enable_list):
    return 1

DEBUG:celery.utils.functional:
def zbt_buy_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def chord(self, header, body, partial_args=0, interval=1, countdown=2, max_retries=3, eager=4, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def delete_old_case_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def check_b2c_trade_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_monitor_data_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_register_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def case_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_staff_balance():
    return 1

DEBUG:celery.utils.functional:
def update_today_amount():
    return 1

DEBUG:celery.utils.functional:
def case_pk_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def init_system_task(self):
    return 1

DEBUG:celery.utils.functional:
def backend_cleanup():
    return 1

DEBUG:celery.utils.functional:
def delete_old_package_items(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def sync_items_price_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_recharge_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def case_bot_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_user_active_point_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_month_amount():
    return 1

/usr/local/lib/python3.8/site-packages/celery/platforms.py:841: SecurityWarning: You're running the worker with superuser privileges: this is
absolutely not recommended!

Please specify a different user using the --uid option.

User information: uid=0 euid=0 gid=0 egid=0

  warnings.warn(SecurityWarning(ROOT_DISCOURAGED.format(
[2025-07-18 10:24:31,831: INFO/MainProcess] Connected to redis://localhost:6379/0
[2025-07-18 10:24:31,835: INFO/MainProcess] mingle: searching for neighbors
[2025-07-18 10:24:32,917: INFO/MainProcess] mingle: all alone
[2025-07-18 10:24:33,023: INFO/MainProcess] celery@iZj6c3ddfpik1c7yalqdt4Z ready.
[2025-07-18 10:29:32,056: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[8dc8d722-9b56-4d0e-8820-f5fab0c3aee4] received
[2025-07-18 10:29:32,069: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 10:29:32,085: INFO/MainProcess] Task thworker.update_case_records_cache[1629f1bd-62ea-4716-bd01-0e50cd096ee1] received
[2025-07-18 10:29:32,095: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 10:29:32,626: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 10:29:32,630: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[8dc8d722-9b56-4d0e-8820-f5fab0c3aee4] succeeded in 0.5614328600058798s: None
[2025-07-18 10:29:32,634: INFO/MainProcess] Task thworker.check_b2c_trade_state[3a517447-4db1-4c6a-9f84-9f2f57fe565f] received
[2025-07-18 10:29:32,635: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 10:29:32,636: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 10:29:32,648: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[3a517447-4db1-4c6a-9f84-9f2f57fe565f] succeeded in 0.013497011997969821s: 'Success: B2C trade state checked'
[2025-07-18 10:30:11,202: ERROR/MainProcess] Process 'ForkPoolWorker-2' pid:39 exited with 'signal 15 (SIGTERM)'
[2025-07-18 10:30:11,723: ERROR/MainProcess] Process 'ForkPoolWorker-1' pid:38 exited with 'signal 15 (SIGTERM)'
[2025-07-18 10:30:11,743: ERROR/MainProcess] Task handler raised error: WorkerLostError('Worker exited prematurely: signal 15 (SIGTERM) Job: 1.')
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/billiard/pool.py", line 1265, in mark_as_worker_lost
    raise WorkerLostError(
billiard.einfo.ExceptionWithTraceback: 
"""
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/worker/worker.py", line 203, in start
    self.blueprint.start(self)
  File "/usr/local/lib/python3.8/site-packages/celery/bootsteps.py", line 116, in start
    step.start(parent)
  File "/usr/local/lib/python3.8/site-packages/celery/bootsteps.py", line 365, in start
    return self.obj.start()
  File "/usr/local/lib/python3.8/site-packages/celery/worker/consumer/consumer.py", line 341, in start
    blueprint.start(self)
  File "/usr/local/lib/python3.8/site-packages/celery/bootsteps.py", line 116, in start
    step.start(parent)
  File "/usr/local/lib/python3.8/site-packages/celery/worker/consumer/consumer.py", line 772, in start
    c.loop(*c.loop_args())
  File "/usr/local/lib/python3.8/site-packages/celery/worker/loops.py", line 86, in asynloop
    state.maybe_shutdown()
  File "/usr/local/lib/python3.8/site-packages/celery/worker/state.py", line 93, in maybe_shutdown
    raise WorkerShutdown(should_stop)
celery.exceptions.WorkerShutdown: 0

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/billiard/pool.py", line 1265, in mark_as_worker_lost
    raise WorkerLostError(
billiard.exceptions.WorkerLostError: Worker exited prematurely: signal 15 (SIGTERM) Job: 1.
"""
DEBUG:asyncio:Using selector: EpollSelector
DEBUG:celery.utils.functional:
def update_month_amount():
    return 1

DEBUG:celery.utils.functional:
def case_pk_join_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def accumulate(self, *args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_new_user_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def hourly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def case_bot_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def unlock_chord(self, group_id, callback, interval=0, max_retries=1, result=2, Result=3, GroupResult=4, result_from_tuple=5, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def cleanup_duplicate_roll_room_bets():
    return 1

DEBUG:celery.utils.functional:
def update_domain_verify_switch(value):
    return 1

DEBUG:celery.utils.functional:
def roll_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def xmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def check_envelop_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_balance_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def xstarmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def update_daily_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def daily_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def chunks(task, it, n):
    return 1

DEBUG:celery.utils.functional:
def update_enable_based_on_coins(min_coins, max_coins):
    return 1

DEBUG:celery.utils.functional:
def check_room_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache(count, batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def group(self, tasks, result, group_id, partial_args, add_to_parent=0):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache_task(self, count=0):
    return 1

DEBUG:celery.utils.functional:
def check_pendclose_trade_task(self):
    return 1

DEBUG:celery.utils.functional:
def chain(*args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_enable_rule_exam(value):
    return 1

DEBUG:celery.utils.functional:
def delete_old_log_entries(batch_size=0, days=1):
    return 1

DEBUG:celery.utils.functional:
def weekly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def chord(self, header, body, partial_args=0, interval=1, countdown=2, max_retries=3, eager=4, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_enable_pay_method(enable_list):
    return 1

DEBUG:celery.utils.functional:
def zbt_buy_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_case_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_monitor_data_task(self):
    return 1

DEBUG:celery.utils.functional:
def check_b2c_trade_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_register_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def case_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_staff_balance():
    return 1

DEBUG:celery.utils.functional:
def backend_cleanup():
    return 1

DEBUG:celery.utils.functional:
def update_today_amount():
    return 1

DEBUG:celery.utils.functional:
def case_pk_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def init_system_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_package_items(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def sync_items_price_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_recharge_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def delete_old_user_active_point_records(batch_size=0):
    return 1

/usr/local/lib/python3.8/site-packages/celery/platforms.py:841: SecurityWarning: You're running the worker with superuser privileges: this is
absolutely not recommended!

Please specify a different user using the --uid option.

User information: uid=0 euid=0 gid=0 egid=0

  warnings.warn(SecurityWarning(ROOT_DISCOURAGED.format(
[2025-07-18 10:30:30,577: INFO/MainProcess] Connected to redis://localhost:6379/0
[2025-07-18 10:30:30,582: INFO/MainProcess] mingle: searching for neighbors
[2025-07-18 10:30:31,600: INFO/MainProcess] mingle: all alone
[2025-07-18 10:30:31,623: INFO/MainProcess] celery@iZj6c3ddfpik1c7yalqdt4Z ready.
DEBUG:asyncio:Using selector: EpollSelector
DEBUG:celery.utils.functional:
def update_month_amount():
    return 1

DEBUG:celery.utils.functional:
def case_pk_join_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def accumulate(self, *args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_new_user_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def hourly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def unlock_chord(self, group_id, callback, interval=0, max_retries=1, result=2, Result=3, GroupResult=4, result_from_tuple=5, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def cleanup_duplicate_roll_room_bets():
    return 1

DEBUG:celery.utils.functional:
def update_domain_verify_switch(value):
    return 1

DEBUG:celery.utils.functional:
def roll_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def xmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def check_envelop_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_balance_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def xstarmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def update_daily_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def daily_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def chunks(task, it, n):
    return 1

DEBUG:celery.utils.functional:
def update_enable_based_on_coins(min_coins, max_coins):
    return 1

DEBUG:celery.utils.functional:
def check_room_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache(count, batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def group(self, tasks, result, group_id, partial_args, add_to_parent=0):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache_task(self, count=0):
    return 1

DEBUG:celery.utils.functional:
def check_pendclose_trade_task(self):
    return 1

DEBUG:celery.utils.functional:
def chain(*args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_enable_rule_exam(value):
    return 1

DEBUG:celery.utils.functional:
def delete_old_log_entries(batch_size=0, days=1):
    return 1

DEBUG:celery.utils.functional:
def weekly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def chord(self, header, body, partial_args=0, interval=1, countdown=2, max_retries=3, eager=4, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_enable_pay_method(enable_list):
    return 1

DEBUG:celery.utils.functional:
def zbt_buy_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_case_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_monitor_data_task(self):
    return 1

DEBUG:celery.utils.functional:
def check_b2c_trade_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_register_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def case_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_staff_balance():
    return 1

DEBUG:celery.utils.functional:
def backend_cleanup():
    return 1

DEBUG:celery.utils.functional:
def update_today_amount():
    return 1

DEBUG:celery.utils.functional:
def case_pk_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def init_system_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_package_items(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def sync_items_price_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_recharge_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def delete_old_user_active_point_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def case_bot_worker_task(self):
    return 1

/usr/local/lib/python3.8/site-packages/celery/platforms.py:841: SecurityWarning: You're running the worker with superuser privileges: this is
absolutely not recommended!

Please specify a different user using the --uid option.

User information: uid=0 euid=0 gid=0 egid=0

  warnings.warn(SecurityWarning(ROOT_DISCOURAGED.format(
[2025-07-18 10:33:20,691: INFO/MainProcess] Connected to redis://localhost:6379/0
[2025-07-18 10:33:20,694: INFO/MainProcess] mingle: searching for neighbors
[2025-07-18 10:33:21,704: INFO/MainProcess] mingle: all alone
[2025-07-18 10:33:21,727: INFO/MainProcess] celery@iZj6c3ddfpik1c7yalqdt4Z ready.
[2025-07-18 10:34:31,914: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[51809944-fa62-4325-a988-901e60822dbc] received
[2025-07-18 10:34:31,915: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 10:34:31,917: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 10:34:31,920: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[51809944-fa62-4325-a988-901e60822dbc] succeeded in 0.0049108169914688915s: None
[2025-07-18 10:34:32,073: INFO/MainProcess] Task thworker.update_case_records_cache[2a7f6abe-b7b9-46c7-8577-df3b3ab8bacb] received
[2025-07-18 10:34:32,074: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 10:34:32,075: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 10:34:32,076: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 10:34:32,077: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[2a7f6abe-b7b9-46c7-8577-df3b3ab8bacb] succeeded in 0.0030044169980101287s: 'Success: Updated 100 case records cache'
[2025-07-18 10:34:32,155: INFO/MainProcess] Task thworker.check_b2c_trade_state[f80fde76-761c-467e-a7a7-d7cee5ea8230] received
[2025-07-18 10:34:32,156: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 10:34:32,156: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 10:34:32,158: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 10:34:32,159: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[f80fde76-761c-467e-a7a7-d7cee5ea8230] succeeded in 0.003014758985955268s: 'Success: B2C trade state checked'
[2025-07-18 10:35:09,874: INFO/MainProcess] Task thworker.update_case_records_cache[9c5e3ef8-9885-4dfe-a4a9-a4f124253b39] received
[2025-07-18 10:35:09,875: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 10:35:49,952: INFO/ForkPoolWorker-2] 更新 CaseRecord 缓存成功。
[2025-07-18 10:35:49,952: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 10:35:49,953: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[9c5e3ef8-9885-4dfe-a4a9-a4f124253b39] succeeded in 40.07838828401873s: 'Success: Updated 100 case records cache'
[2025-07-18 10:39:31,892: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[9827879f-306d-486b-8c8e-99ce77eb4407] received
[2025-07-18 10:39:31,894: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 10:39:31,896: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 10:39:31,898: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[9827879f-306d-486b-8c8e-99ce77eb4407] succeeded in 0.005068740021670237s: None
[2025-07-18 10:39:32,170: INFO/MainProcess] Task thworker.check_b2c_trade_state[cb715dc9-343d-4f2c-99e0-85dcb34a1719] received
[2025-07-18 10:39:32,171: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 10:39:32,172: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 10:39:32,173: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 10:39:32,174: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[cb715dc9-343d-4f2c-99e0-85dcb34a1719] succeeded in 0.0032732590043451637s: 'Success: B2C trade state checked'
DEBUG:asyncio:Using selector: EpollSelector
DEBUG:celery.utils.functional:
def cleanup_duplicate_roll_room_bets():
    return 1

DEBUG:celery.utils.functional:
def update_domain_verify_switch(value):
    return 1

DEBUG:celery.utils.functional:
def roll_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def accumulate(self, *args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def check_envelop_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def unlock_chord(self, group_id, callback, interval=0, max_retries=1, result=2, Result=3, GroupResult=4, result_from_tuple=5, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_daily_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def delete_old_balance_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def daily_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def xmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def update_enable_based_on_coins(min_coins, max_coins):
    return 1

DEBUG:celery.utils.functional:
def check_room_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def xstarmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache(count, batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache_task(self, count=0):
    return 1

DEBUG:celery.utils.functional:
def check_pendclose_trade_task(self):
    return 1

DEBUG:celery.utils.functional:
def chunks(task, it, n):
    return 1

DEBUG:celery.utils.functional:
def update_enable_rule_exam(value):
    return 1

DEBUG:celery.utils.functional:
def delete_old_log_entries(batch_size=0, days=1):
    return 1

DEBUG:celery.utils.functional:
def weekly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def group(self, tasks, result, group_id, partial_args, add_to_parent=0):
    return 1

DEBUG:celery.utils.functional:
def update_enable_pay_method(enable_list):
    return 1

DEBUG:celery.utils.functional:
def zbt_buy_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def chain(*args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def delete_old_case_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def check_b2c_trade_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_monitor_data_task(self):
    return 1

DEBUG:celery.utils.functional:
def chord(self, header, body, partial_args=0, interval=1, countdown=2, max_retries=3, eager=4, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_register_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def update_staff_balance():
    return 1

DEBUG:celery.utils.functional:
def case_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_today_amount():
    return 1

DEBUG:celery.utils.functional:
def case_pk_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def init_system_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_package_items(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def sync_items_price_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_recharge_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def case_bot_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def backend_cleanup():
    return 1

DEBUG:celery.utils.functional:
def delete_old_user_active_point_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_month_amount():
    return 1

DEBUG:celery.utils.functional:
def hourly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def case_pk_join_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_new_user_recharge_limit(value):
    return 1

/usr/local/lib/python3.8/site-packages/celery/platforms.py:841: SecurityWarning: You're running the worker with superuser privileges: this is
absolutely not recommended!

Please specify a different user using the --uid option.

User information: uid=0 euid=0 gid=0 egid=0

  warnings.warn(SecurityWarning(ROOT_DISCOURAGED.format(
[2025-07-18 10:40:04,384: INFO/MainProcess] Connected to redis://localhost:6379/0
[2025-07-18 10:40:04,389: INFO/MainProcess] mingle: searching for neighbors
[2025-07-18 10:40:05,414: INFO/MainProcess] mingle: all alone
[2025-07-18 10:40:05,437: INFO/MainProcess] celery@iZj6c3ddfpik1c7yalqdt4Z ready.
[2025-07-18 10:44:31,923: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[565d99e0-c4e3-44ac-bf82-e366fd0c8a9c] received
[2025-07-18 10:44:31,925: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 10:44:31,926: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 10:44:31,929: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[565d99e0-c4e3-44ac-bf82-e366fd0c8a9c] succeeded in 0.004207460006000474s: None
[2025-07-18 10:44:32,111: INFO/MainProcess] Task thworker.update_case_records_cache[afaefeb4-205a-4d25-b5e2-9afecdcbb6c9] received
[2025-07-18 10:44:32,113: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 10:44:32,115: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 10:44:32,115: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 10:44:32,118: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[afaefeb4-205a-4d25-b5e2-9afecdcbb6c9] succeeded in 0.004901190986856818s: 'Success: Updated 100 case records cache'
[2025-07-18 10:44:32,170: INFO/MainProcess] Task thworker.check_b2c_trade_state[72a06d2e-d35b-4c6d-9947-35dda4cb0609] received
[2025-07-18 10:44:32,171: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 10:44:32,172: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 10:44:32,183: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[72a06d2e-d35b-4c6d-9947-35dda4cb0609] succeeded in 0.01235854800324887s: 'Success: B2C trade state checked'
[2025-07-18 10:49:31,909: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[6abfd3af-e204-48bb-9a88-6632d3dd9dfa] received
[2025-07-18 10:49:31,910: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 10:49:31,916: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 10:49:31,917: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[6abfd3af-e204-48bb-9a88-6632d3dd9dfa] succeeded in 0.007038721989374608s: None
[2025-07-18 10:49:32,116: INFO/MainProcess] Task thworker.update_case_records_cache[aba0fe8f-5f58-4cf3-8097-15d93aadaf55] received
[2025-07-18 10:49:32,117: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 10:49:32,118: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 10:49:32,118: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 10:49:32,119: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[aba0fe8f-5f58-4cf3-8097-15d93aadaf55] succeeded in 0.002074713003821671s: 'Success: Updated 100 case records cache'
[2025-07-18 10:49:32,174: INFO/MainProcess] Task thworker.check_b2c_trade_state[b90adf85-7232-43a1-bb28-bb9f4e7f8e9f] received
[2025-07-18 10:49:32,176: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 10:49:32,176: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 10:49:32,179: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[b90adf85-7232-43a1-bb28-bb9f4e7f8e9f] succeeded in 0.004130791989155114s: 'Success: B2C trade state checked'
[2025-07-18 10:49:32,181: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 10:54:31,902: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[6877998b-b8c3-41d6-ac9a-765b8c0022ce] received
[2025-07-18 10:54:31,903: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 10:54:32,113: INFO/MainProcess] Task thworker.update_case_records_cache[84bf2317-92be-4a19-8f80-cc84b213e07a] received
[2025-07-18 10:54:32,114: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 10:54:32,381: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 10:54:32,383: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[6877998b-b8c3-41d6-ac9a-765b8c0022ce] succeeded in 0.4805260150169488s: None
[2025-07-18 10:55:12,929: INFO/ForkPoolWorker-1] 更新 CaseRecord 缓存成功。
[2025-07-18 10:55:12,930: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 10:55:12,932: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[84bf2317-92be-4a19-8f80-cc84b213e07a] succeeded in 40.81834783102386s: 'Success: Updated 100 case records cache'
[2025-07-18 10:59:31,902: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[9c2ade14-d3d5-4ead-b3fe-d0801fd9a1dc] received
[2025-07-18 10:59:31,904: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 10:59:32,117: INFO/MainProcess] Task thworker.update_case_records_cache[56cfc90c-9874-4f6b-8d02-1568897870ab] received
[2025-07-18 10:59:32,119: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 10:59:32,120: INFO/ForkPoolWorker-1] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 10:59:32,120: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 10:59:32,122: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[56cfc90c-9874-4f6b-8d02-1568897870ab] succeeded in 0.002908968977862969s: 'Success: Updated 100 case records cache'
[2025-07-18 10:59:32,180: INFO/MainProcess] Task thworker.check_b2c_trade_state[e6daebbf-9679-41e5-b671-d58b528ae99b] received
[2025-07-18 10:59:32,181: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 10:59:32,182: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 10:59:32,183: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[e6daebbf-9679-41e5-b671-d58b528ae99b] succeeded in 0.0029407110123429447s: 'Success: B2C trade state checked'
[2025-07-18 10:59:32,454: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 10:59:32,462: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[9c2ade14-d3d5-4ead-b3fe-d0801fd9a1dc] succeeded in 0.5586069519922603s: None
[2025-07-18 11:00:00,005: INFO/MainProcess] Task thworker.hourly_lottery[ecfc58c2-fdf9-4c55-9b67-f79c3066ef30] received
[2025-07-18 11:00:00,007: INFO/ForkPoolWorker-2] ------------------------ 整点抽奖 ------------------------
[2025-07-18 11:00:00,007: INFO/ForkPoolWorker-2] [Celery] 每小时抽奖任务完成
[2025-07-18 11:00:00,011: INFO/ForkPoolWorker-2] Task thworker.hourly_lottery[ecfc58c2-fdf9-4c55-9b67-f79c3066ef30] succeeded in 0.005735257989726961s: 'Success: Hourly lottery completed'
[2025-07-18 11:04:31,905: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[e29d0d1b-b875-47c4-b122-d6efbe26cfdd] received
[2025-07-18 11:04:31,906: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 11:04:32,118: INFO/MainProcess] Task thworker.update_case_records_cache[eee8350a-b996-425b-9fe2-aa747b41f2e1] received
[2025-07-18 11:04:32,120: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 11:04:32,450: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 11:04:32,452: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[e29d0d1b-b875-47c4-b122-d6efbe26cfdd] succeeded in 0.5462271080177743s: None
[2025-07-18 11:05:11,552: INFO/ForkPoolWorker-1] 更新 CaseRecord 缓存成功。
[2025-07-18 11:05:11,553: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 11:05:11,555: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[eee8350a-b996-425b-9fe2-aa747b41f2e1] succeeded in 39.43522869399749s: 'Success: Updated 100 case records cache'
[2025-07-18 11:09:31,909: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[5c5ce991-8c35-4248-b2bc-cb425bf6f19b] received
[2025-07-18 11:09:31,910: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 11:09:32,135: INFO/MainProcess] Task thworker.update_case_records_cache[2e499ef5-d347-4b15-a025-e3702edec6e8] received
[2025-07-18 11:09:32,136: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 11:09:32,137: INFO/ForkPoolWorker-1] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 11:09:32,137: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 11:09:32,138: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[2e499ef5-d347-4b15-a025-e3702edec6e8] succeeded in 0.001678452012129128s: 'Success: Updated 100 case records cache'
[2025-07-18 11:09:32,180: INFO/MainProcess] Task thworker.check_b2c_trade_state[df0e7d60-63a7-4789-ab49-acd640f69fbf] received
[2025-07-18 11:09:32,181: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 11:09:32,182: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 11:09:32,183: INFO/ForkPoolWorker-1] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 11:09:32,184: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[df0e7d60-63a7-4789-ab49-acd640f69fbf] succeeded in 0.0032298789883498102s: 'Success: B2C trade state checked'
[2025-07-18 11:09:32,535: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 11:09:32,537: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[5c5ce991-8c35-4248-b2bc-cb425bf6f19b] succeeded in 0.626814901974285s: None
[2025-07-18 11:14:31,910: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[e0fcb343-9f64-440f-9b8a-d052ee7b5368] received
[2025-07-18 11:14:31,911: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 11:14:32,129: INFO/MainProcess] Task thworker.update_case_records_cache[98040d83-521f-4280-b63b-22ffc0eb7655] received
[2025-07-18 11:14:32,130: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 11:14:32,431: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 11:14:32,433: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[e0fcb343-9f64-440f-9b8a-d052ee7b5368] succeeded in 0.5224630640004762s: None
[2025-07-18 11:15:12,219: INFO/ForkPoolWorker-1] 更新 CaseRecord 缓存成功。
[2025-07-18 11:15:12,219: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 11:15:12,222: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[98040d83-521f-4280-b63b-22ffc0eb7655] succeeded in 40.09248202299932s: 'Success: Updated 100 case records cache'
[2025-07-18 11:19:31,916: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[ea94dae4-235a-4f56-9ce3-ba9981bb9614] received
[2025-07-18 11:19:31,917: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 11:19:31,918: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 11:19:31,920: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[ea94dae4-235a-4f56-9ce3-ba9981bb9614] succeeded in 0.002212622988736257s: None
[2025-07-18 11:19:32,139: INFO/MainProcess] Task thworker.update_case_records_cache[0aa6fac9-60c1-4899-9dc9-1e9848b29169] received
[2025-07-18 11:19:32,140: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 11:19:32,141: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 11:19:32,141: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 11:19:32,142: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[0aa6fac9-60c1-4899-9dc9-1e9848b29169] succeeded in 0.0018586910155136138s: 'Success: Updated 100 case records cache'
[2025-07-18 11:19:32,190: INFO/MainProcess] Task thworker.check_b2c_trade_state[6a42816c-a1d9-406a-bf26-08f9e8b6cfcb] received
[2025-07-18 11:19:32,191: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 11:19:32,191: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 11:19:32,194: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[6a42816c-a1d9-406a-bf26-08f9e8b6cfcb] succeeded in 0.0031276490190066397s: 'Success: B2C trade state checked'
[2025-07-18 11:24:31,968: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[f3a246df-2b2e-4de8-84dc-16e264cea2de] received
[2025-07-18 11:24:31,969: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 11:24:32,135: INFO/MainProcess] Task thworker.update_case_records_cache[90331c89-e038-4b49-a5c2-4efedd79b9d0] received
[2025-07-18 11:24:32,136: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 11:24:32,417: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 11:24:32,419: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[f3a246df-2b2e-4de8-84dc-16e264cea2de] succeeded in 0.45033483300358057s: None
[2025-07-18 11:25:12,479: INFO/ForkPoolWorker-1] 更新 CaseRecord 缓存成功。
[2025-07-18 11:25:12,479: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 11:25:12,480: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[90331c89-e038-4b49-a5c2-4efedd79b9d0] succeeded in 40.34448819802492s: 'Success: Updated 100 case records cache'
DEBUG:asyncio:Using selector: EpollSelector
DEBUG:celery.utils.functional:
def update_month_amount():
    return 1

DEBUG:celery.utils.functional:
def hourly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def accumulate(self, *args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_new_user_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def roll_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def unlock_chord(self, group_id, callback, interval=0, max_retries=1, result=2, Result=3, GroupResult=4, result_from_tuple=5, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def check_envelop_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def cleanup_duplicate_roll_room_bets():
    return 1

DEBUG:celery.utils.functional:
def update_domain_verify_switch(value):
    return 1

DEBUG:celery.utils.functional:
def xmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def daily_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_balance_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def xstarmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def update_daily_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def check_room_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def chunks(task, it, n):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache(count, batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_enable_based_on_coins(min_coins, max_coins):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache_task(self, count=0):
    return 1

DEBUG:celery.utils.functional:
def check_pendclose_trade_task(self):
    return 1

DEBUG:celery.utils.functional:
def group(self, tasks, result, group_id, partial_args, add_to_parent=0):
    return 1

DEBUG:celery.utils.functional:
def weekly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_log_entries(batch_size=0, days=1):
    return 1

DEBUG:celery.utils.functional:
def chain(*args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_enable_rule_exam(value):
    return 1

DEBUG:celery.utils.functional:
def zbt_buy_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def chord(self, header, body, partial_args=0, interval=1, countdown=2, max_retries=3, eager=4, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def delete_old_case_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_enable_pay_method(enable_list):
    return 1

DEBUG:celery.utils.functional:
def check_b2c_trade_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_monitor_data_task(self):
    return 1

DEBUG:celery.utils.functional:
def case_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_staff_balance():
    return 1

DEBUG:celery.utils.functional:
def update_register_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def case_pk_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def init_system_task(self):
    return 1

DEBUG:celery.utils.functional:
def backend_cleanup():
    return 1

DEBUG:celery.utils.functional:
def delete_old_package_items(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_today_amount():
    return 1

DEBUG:celery.utils.functional:
def sync_items_price_task(self):
    return 1

DEBUG:celery.utils.functional:
def case_bot_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_user_active_point_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_recharge_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def case_pk_join_worker_task(self):
    return 1

/usr/local/lib/python3.8/site-packages/celery/platforms.py:841: SecurityWarning: You're running the worker with superuser privileges: this is
absolutely not recommended!

Please specify a different user using the --uid option.

User information: uid=0 euid=0 gid=0 egid=0

  warnings.warn(SecurityWarning(ROOT_DISCOURAGED.format(
[2025-07-18 11:28:06,040: INFO/MainProcess] Connected to redis://localhost:6379/0
[2025-07-18 11:28:06,055: INFO/MainProcess] mingle: searching for neighbors
[2025-07-18 11:28:07,073: INFO/MainProcess] mingle: all alone
[2025-07-18 11:28:07,093: INFO/MainProcess] celery@iZj6c3ddfpik1c7yalqdt4Z ready.
[2025-07-18 11:29:31,993: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[5f3d7d60-616a-4f09-a7dd-a1064daabc67] received
[2025-07-18 11:29:31,995: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 11:29:32,140: INFO/MainProcess] Task thworker.update_case_records_cache[d090e6c9-0f2c-416d-b46f-9e064d8a0d86] received
[2025-07-18 11:29:32,142: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 11:29:32,144: INFO/ForkPoolWorker-1] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 11:29:32,144: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 11:29:32,147: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[d090e6c9-0f2c-416d-b46f-9e064d8a0d86] succeeded in 0.005732024990720674s: 'Success: Updated 100 case records cache'
[2025-07-18 11:29:32,192: INFO/MainProcess] Task thworker.check_b2c_trade_state[41e1e3e0-c0d1-4613-8948-2612d8440386] received
[2025-07-18 11:29:32,195: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 11:29:32,196: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 11:29:32,198: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[41e1e3e0-c0d1-4613-8948-2612d8440386] succeeded in 0.003126094990875572s: 'Success: B2C trade state checked'
[2025-07-18 11:29:32,550: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 11:29:32,553: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[5f3d7d60-616a-4f09-a7dd-a1064daabc67] succeeded in 0.5587897249788512s: None
DEBUG:asyncio:Using selector: EpollSelector
DEBUG:celery.utils.functional:
def update_new_user_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def hourly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def accumulate(self, *args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def cleanup_duplicate_roll_room_bets():
    return 1

DEBUG:celery.utils.functional:
def update_domain_verify_switch(value):
    return 1

DEBUG:celery.utils.functional:
def roll_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def unlock_chord(self, group_id, callback, interval=0, max_retries=1, result=2, Result=3, GroupResult=4, result_from_tuple=5, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def check_envelop_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_daily_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def xmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def delete_old_balance_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def daily_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def xstarmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def update_enable_based_on_coins(min_coins, max_coins):
    return 1

DEBUG:celery.utils.functional:
def check_room_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def chunks(task, it, n):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache(count, batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache_task(self, count=0):
    return 1

DEBUG:celery.utils.functional:
def check_pendclose_trade_task(self):
    return 1

DEBUG:celery.utils.functional:
def group(self, tasks, result, group_id, partial_args, add_to_parent=0):
    return 1

DEBUG:celery.utils.functional:
def update_enable_rule_exam(value):
    return 1

DEBUG:celery.utils.functional:
def delete_old_log_entries(batch_size=0, days=1):
    return 1

DEBUG:celery.utils.functional:
def weekly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def chain(*args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def case_pk_join_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_enable_pay_method(enable_list):
    return 1

DEBUG:celery.utils.functional:
def zbt_buy_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def chord(self, header, body, partial_args=0, interval=1, countdown=2, max_retries=3, eager=4, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def delete_old_case_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def check_b2c_trade_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_monitor_data_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_register_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def case_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_staff_balance():
    return 1

DEBUG:celery.utils.functional:
def update_today_amount():
    return 1

DEBUG:celery.utils.functional:
def case_pk_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def init_system_task(self):
    return 1

DEBUG:celery.utils.functional:
def backend_cleanup():
    return 1

DEBUG:celery.utils.functional:
def delete_old_package_items(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def sync_items_price_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_recharge_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def case_bot_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_user_active_point_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_month_amount():
    return 1

/usr/local/lib/python3.8/site-packages/celery/platforms.py:841: SecurityWarning: You're running the worker with superuser privileges: this is
absolutely not recommended!

Please specify a different user using the --uid option.

User information: uid=0 euid=0 gid=0 egid=0

  warnings.warn(SecurityWarning(ROOT_DISCOURAGED.format(
[2025-07-18 11:32:50,033: INFO/MainProcess] Connected to redis://localhost:6379/0
[2025-07-18 11:32:50,040: INFO/MainProcess] mingle: searching for neighbors
[2025-07-18 11:32:51,107: INFO/MainProcess] mingle: all alone
[2025-07-18 11:32:51,128: INFO/MainProcess] celery@iZj6c3ddfpik1c7yalqdt4Z ready.
[2025-07-18 11:34:31,990: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[ef1e0cd8-d737-4767-9087-1e4b54bd64aa] received
[2025-07-18 11:34:31,991: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 11:34:31,993: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 11:34:31,995: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[ef1e0cd8-d737-4767-9087-1e4b54bd64aa] succeeded in 0.004347455018432811s: None
[2025-07-18 11:34:32,142: INFO/MainProcess] Task thworker.update_case_records_cache[8ef3f79a-23d6-48f4-832e-cedb2e63811a] received
[2025-07-18 11:34:32,143: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 11:34:32,192: INFO/MainProcess] Task thworker.check_b2c_trade_state[1647c935-89d5-42ee-860e-36beca66392a] received
[2025-07-18 11:34:32,195: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 11:34:32,197: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 11:34:32,199: INFO/ForkPoolWorker-1] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 11:34:32,200: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[1647c935-89d5-42ee-860e-36beca66392a] succeeded in 0.005927411024458706s: 'Success: B2C trade state checked'
[2025-07-18 11:35:04,624: INFO/ForkPoolWorker-2] 更新 CaseRecord 缓存成功。
[2025-07-18 11:35:04,624: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 11:35:04,626: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[8ef3f79a-23d6-48f4-832e-cedb2e63811a] succeeded in 32.48258806898957s: 'Success: Updated 100 case records cache'
[2025-07-18 11:39:31,963: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[625dcb5e-76dd-483d-a520-787652e5f723] received
[2025-07-18 11:39:31,968: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 11:39:32,152: INFO/MainProcess] Task thworker.update_case_records_cache[8ef0b435-3f7a-4a45-a740-80baac35e2cc] received
[2025-07-18 11:39:32,156: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 11:39:32,458: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 11:39:32,460: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[625dcb5e-76dd-483d-a520-787652e5f723] succeeded in 0.49259431500104256s: None
[2025-07-18 11:39:53,948: INFO/ForkPoolWorker-1] 更新 CaseRecord 缓存成功。
[2025-07-18 11:39:53,948: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 11:39:53,949: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[8ef0b435-3f7a-4a45-a740-80baac35e2cc] succeeded in 21.793672948988387s: 'Success: Updated 100 case records cache'
[2025-07-18 11:44:31,993: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[b4b4dcee-5306-4698-87d6-a53dfc9b889a] received
[2025-07-18 11:44:31,994: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 11:44:31,995: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 11:44:31,998: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[b4b4dcee-5306-4698-87d6-a53dfc9b889a] succeeded in 0.003973857994424179s: None
[2025-07-18 11:44:32,155: INFO/MainProcess] Task thworker.update_case_records_cache[b2bba91b-2b69-4577-8cf7-0962225b257c] received
[2025-07-18 11:44:32,156: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 11:44:32,157: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 11:44:32,157: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 11:44:32,158: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[b2bba91b-2b69-4577-8cf7-0962225b257c] succeeded in 0.002856529987184331s: 'Success: Updated 100 case records cache'
[2025-07-18 11:44:32,238: INFO/MainProcess] Task thworker.check_b2c_trade_state[e51794a0-b154-4bd2-9d5c-b488f647e85b] received
[2025-07-18 11:44:32,240: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 11:44:32,241: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 11:44:32,242: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 11:44:32,243: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[e51794a0-b154-4bd2-9d5c-b488f647e85b] succeeded in 0.00438169197877869s: 'Success: B2C trade state checked'
[2025-07-18 11:49:31,984: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[77a17311-b3d5-49c4-a029-133d0f218668] received
[2025-07-18 11:49:31,985: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 11:49:32,158: INFO/MainProcess] Task thworker.update_case_records_cache[5f734b67-abbf-40df-be2c-7ae6f6a411dc] received
[2025-07-18 11:49:32,160: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 11:49:32,162: INFO/ForkPoolWorker-1] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 11:49:32,162: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 11:49:32,166: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[5f734b67-abbf-40df-be2c-7ae6f6a411dc] succeeded in 0.0070734879991505295s: 'Success: Updated 100 case records cache'
[2025-07-18 11:49:32,245: INFO/MainProcess] Task thworker.check_b2c_trade_state[9ee16bbe-f944-46e6-a3b8-7fc05f39642d] received
[2025-07-18 11:49:32,249: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 11:49:32,250: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 11:49:32,251: INFO/ForkPoolWorker-1] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 11:49:32,251: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[9ee16bbe-f944-46e6-a3b8-7fc05f39642d] succeeded in 0.0054115550010465086s: 'Success: B2C trade state checked'
[2025-07-18 11:49:32,440: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 11:49:32,443: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[77a17311-b3d5-49c4-a029-133d0f218668] succeeded in 0.45777829800499603s: None
[2025-07-18 11:54:31,985: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[d4cd6ed2-7e7c-4cb5-96c8-07f3e7f50356] received
[2025-07-18 11:54:31,986: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 11:54:32,193: INFO/MainProcess] Task thworker.update_case_records_cache[f0077e83-5703-40bd-90e7-3ab1d90a115f] received
[2025-07-18 11:54:32,194: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 11:54:32,201: INFO/ForkPoolWorker-1] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 11:54:32,201: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 11:54:32,204: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[f0077e83-5703-40bd-90e7-3ab1d90a115f] succeeded in 0.009624686004826799s: 'Success: Updated 100 case records cache'
[2025-07-18 11:54:32,252: INFO/MainProcess] Task thworker.check_b2c_trade_state[70650803-68c0-45ae-81f2-893662ac8d91] received
[2025-07-18 11:54:32,262: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 11:54:32,263: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 11:54:32,264: INFO/ForkPoolWorker-1] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 11:54:32,273: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[70650803-68c0-45ae-81f2-893662ac8d91] succeeded in 0.02051904599647969s: 'Success: B2C trade state checked'
[2025-07-18 11:54:32,374: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 11:54:32,377: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[d4cd6ed2-7e7c-4cb5-96c8-07f3e7f50356] succeeded in 0.3903589279798325s: None
DEBUG:asyncio:Using selector: EpollSelector
DEBUG:celery.utils.functional:
def update_staff_balance():
    return 1

DEBUG:celery.utils.functional:
def update_enable_pay_method(enable_list):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache_task(self, count=0):
    return 1

DEBUG:celery.utils.functional:
def check_pendclose_trade_task(self):
    return 1

DEBUG:celery.utils.functional:
def accumulate(self, *args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def weekly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_case_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def unlock_chord(self, group_id, callback, interval=0, max_retries=1, result=2, Result=3, GroupResult=4, result_from_tuple=5, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def delete_old_package_items(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_register_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def zbt_buy_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_user_active_point_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def xmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def update_today_amount():
    return 1

DEBUG:celery.utils.functional:
def check_b2c_trade_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_monitor_data_task(self):
    return 1

DEBUG:celery.utils.functional:
def xstarmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def case_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def chunks(task, it, n):
    return 1

DEBUG:celery.utils.functional:
def update_recharge_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def case_pk_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def group(self, tasks, result, group_id, partial_args, add_to_parent=0):
    return 1

DEBUG:celery.utils.functional:
def update_month_amount():
    return 1

DEBUG:celery.utils.functional:
def cleanup_duplicate_roll_room_bets():
    return 1

DEBUG:celery.utils.functional:
def sync_items_price_task(self):
    return 1

DEBUG:celery.utils.functional:
def init_system_task(self):
    return 1

DEBUG:celery.utils.functional:
def chain(*args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_new_user_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def delete_old_balance_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def case_bot_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def chord(self, header, body, partial_args=0, interval=1, countdown=2, max_retries=3, eager=4, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_domain_verify_switch(value):
    return 1

DEBUG:celery.utils.functional:
def case_pk_join_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def hourly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_daily_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def roll_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def backend_cleanup():
    return 1

DEBUG:celery.utils.functional:
def delete_old_log_entries(batch_size=0, days=1):
    return 1

DEBUG:celery.utils.functional:
def update_enable_based_on_coins(min_coins, max_coins):
    return 1

DEBUG:celery.utils.functional:
def check_envelop_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache(count, batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def daily_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_enable_rule_exam(value):
    return 1

DEBUG:celery.utils.functional:
def check_room_state_task(self):
    return 1

/usr/local/lib/python3.8/site-packages/celery/platforms.py:841: SecurityWarning: You're running the worker with superuser privileges: this is
absolutely not recommended!

Please specify a different user using the --uid option.

User information: uid=0 euid=0 gid=0 egid=0

  warnings.warn(SecurityWarning(ROOT_DISCOURAGED.format(
[2025-07-18 11:59:08,946: INFO/MainProcess] Connected to redis://localhost:6379/0
[2025-07-18 11:59:08,952: INFO/MainProcess] mingle: searching for neighbors
[2025-07-18 11:59:10,023: INFO/MainProcess] mingle: all alone
[2025-07-18 11:59:10,421: INFO/MainProcess] celery@iZj6c3ddfpik1c7yalqdt4Z ready.
[2025-07-18 11:59:32,036: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[a0cf7ab0-ddfd-4f34-9514-0e185c18b48f] received
[2025-07-18 11:59:32,038: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 11:59:32,043: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 11:59:32,047: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[a0cf7ab0-ddfd-4f34-9514-0e185c18b48f] succeeded in 0.009572322014719248s: None
[2025-07-18 11:59:32,184: INFO/MainProcess] Task thworker.update_case_records_cache[8a52c056-4d4b-4b01-93bf-559748a1ffee] received
[2025-07-18 11:59:32,188: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 11:59:32,190: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 11:59:32,190: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 11:59:32,194: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[8a52c056-4d4b-4b01-93bf-559748a1ffee] succeeded in 0.006307033996563405s: 'Success: Updated 100 case records cache'
[2025-07-18 11:59:32,250: INFO/MainProcess] Task thworker.check_b2c_trade_state[7b6a0d84-9321-4efe-8969-9dddaa471e6f] received
[2025-07-18 11:59:32,269: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 11:59:32,270: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 11:59:32,272: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 11:59:32,272: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[7b6a0d84-9321-4efe-8969-9dddaa471e6f] succeeded in 0.007377909001661465s: 'Success: B2C trade state checked'
[2025-07-18 12:00:00,030: INFO/MainProcess] Task thworker.hourly_lottery[d9e26828-0356-4e40-9d9a-7451ec97b0b5] received
[2025-07-18 12:00:00,037: INFO/ForkPoolWorker-2] ------------------------ 整点抽奖 ------------------------
[2025-07-18 12:00:00,039: INFO/ForkPoolWorker-2] [Celery] 每小时抽奖任务完成
[2025-07-18 12:00:00,052: INFO/ForkPoolWorker-2] Task thworker.hourly_lottery[d9e26828-0356-4e40-9d9a-7451ec97b0b5] succeeded in 0.017337939993012697s: 'Success: Hourly lottery completed'
[2025-07-18 12:01:59,302: WARNING/MainProcess] consumer: Connection to broker lost. Trying to re-establish the connection...
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/worker/consumer/consumer.py", line 341, in start
    blueprint.start(self)
  File "/usr/local/lib/python3.8/site-packages/celery/bootsteps.py", line 116, in start
    step.start(parent)
  File "/usr/local/lib/python3.8/site-packages/celery/worker/consumer/consumer.py", line 772, in start
    c.loop(*c.loop_args())
  File "/usr/local/lib/python3.8/site-packages/celery/worker/loops.py", line 97, in asynloop
    next(loop)
  File "/usr/local/lib/python3.8/site-packages/kombu/asynchronous/hub.py", line 373, in create_loop
    cb(*cbargs)
  File "/usr/local/lib/python3.8/site-packages/kombu/transport/redis.py", line 1359, in on_readable
    self.cycle.on_readable(fileno)
  File "/usr/local/lib/python3.8/site-packages/kombu/transport/redis.py", line 576, in on_readable
    chan.handlers[type]()
  File "/usr/local/lib/python3.8/site-packages/kombu/transport/redis.py", line 925, in _receive
    ret.append(self._receive_one(c))
  File "/usr/local/lib/python3.8/site-packages/kombu/transport/redis.py", line 935, in _receive_one
    response = c.parse_response()
  File "/usr/local/lib/python3.8/site-packages/redis/client.py", line 1542, in parse_response
    response = self._execute(conn, try_read)
  File "/usr/local/lib/python3.8/site-packages/redis/client.py", line 1518, in _execute
    return conn.retry.call_with_retry(
  File "/usr/local/lib/python3.8/site-packages/redis/retry.py", line 49, in call_with_retry
    fail(error)
  File "/usr/local/lib/python3.8/site-packages/redis/client.py", line 1520, in <lambda>
    lambda error: self._disconnect_raise_connect(conn, error),
  File "/usr/local/lib/python3.8/site-packages/redis/client.py", line 1507, in _disconnect_raise_connect
    raise error
  File "/usr/local/lib/python3.8/site-packages/redis/retry.py", line 46, in call_with_retry
    return do()
  File "/usr/local/lib/python3.8/site-packages/redis/client.py", line 1519, in <lambda>
    lambda: command(*args, **kwargs),
  File "/usr/local/lib/python3.8/site-packages/redis/client.py", line 1540, in try_read
    return conn.read_response(disconnect_on_error=False)
  File "/usr/local/lib/python3.8/site-packages/redis/connection.py", line 882, in read_response
    response = self._parser.read_response(disable_decoding=disable_decoding)
  File "/usr/local/lib/python3.8/site-packages/redis/connection.py", line 349, in read_response
    result = self._read_response(disable_decoding=disable_decoding)
  File "/usr/local/lib/python3.8/site-packages/redis/connection.py", line 359, in _read_response
    raw = self._buffer.readline()
  File "/usr/local/lib/python3.8/site-packages/redis/connection.py", line 262, in readline
    self._read_from_socket()
  File "/usr/local/lib/python3.8/site-packages/redis/connection.py", line 215, in _read_from_socket
    raise ConnectionError(SERVER_CLOSED_CONNECTION_ERROR)
redis.exceptions.ConnectionError: Connection closed by server.
[2025-07-18 12:01:59,327: WARNING/MainProcess] /usr/local/lib/python3.8/site-packages/celery/worker/consumer/consumer.py:392: CPendingDeprecationWarning: 
In Celery 5.1 we introduced an optional breaking change which
on connection loss cancels all currently executed tasks with late acknowledgement enabled.
These tasks cannot be acknowledged as the connection is gone, and the tasks are automatically redelivered
back to the queue. You can enable this behavior using the worker_cancel_long_running_tasks_on_connection_loss
setting. In Celery 5.1 it is set to False by default. The setting will be set to True by default in Celery 6.0.

  warnings.warn(CANCEL_TASKS_BY_DEFAULT, CPendingDeprecationWarning)

[2025-07-18 12:01:59,336: ERROR/MainProcess] consumer: Cannot connect to redis://localhost:6379/0: Error while reading from localhost:6379 : (104, 'Connection reset by peer').
Trying again in 2.00 seconds... (1/100)

[2025-07-18 12:02:01,343: INFO/MainProcess] Connected to redis://localhost:6379/0
[2025-07-18 12:02:01,366: INFO/MainProcess] mingle: searching for neighbors
[2025-07-18 12:02:02,379: INFO/MainProcess] mingle: all alone
[2025-07-18 12:04:32,021: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[5972884f-b284-4d58-841f-3b4e6c17675b] received
[2025-07-18 12:04:32,022: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 12:04:32,032: ERROR/ForkPoolWorker-2] 清理 RollRoomBet 记录时发生错误: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
[2025-07-18 12:04:32,039: ERROR/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[5972884f-b284-4d58-841f-3b4e6c17675b] raised unexpected: OperationalError(1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
  File "/app/roll/tasks.py", line 37, in cleanup_duplicate_roll_room_bets
    latest_room = RollRoom.objects.filter(enable=True).order_by('-create_time').first()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 674, in first
    for obj in (self if self.ordered else self.order_by('pk'))[:1]:
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 280, in __iter__
    self._fetch_all()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 1324, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 51, in __iter__
    results = compiler.execute_sql(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/compiler.py", line 1175, in execute_sql
    cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.OperationalError: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
[2025-07-18 12:04:32,189: INFO/MainProcess] Task thworker.update_case_records_cache[0bcf3feb-a986-4765-bbb1-6bd564246301] received
[2025-07-18 12:04:32,191: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 12:04:32,199: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 12:04:32,199: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 12:04:32,202: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[0bcf3feb-a986-4765-bbb1-6bd564246301] succeeded in 0.011888270993949845s: 'Success: Updated 100 case records cache'
[2025-07-18 12:04:32,266: INFO/MainProcess] Task thworker.check_b2c_trade_state[52b531dc-6f49-4da9-8a4a-58df1248d1d7] received
[2025-07-18 12:04:32,268: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 12:04:32,269: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 12:04:32,291: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[52b531dc-6f49-4da9-8a4a-58df1248d1d7] succeeded in 0.023122841987060383s: 'Success: B2C trade state checked'
DEBUG:asyncio:Using selector: EpollSelector
DEBUG:celery.utils.functional:
def update_daily_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def check_room_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def accumulate(self, *args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def delete_old_log_entries(batch_size=0, days=1):
    return 1

DEBUG:celery.utils.functional:
def update_enable_based_on_coins(min_coins, max_coins):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache_task(self, count=0):
    return 1

DEBUG:celery.utils.functional:
def check_pendclose_trade_task(self):
    return 1

DEBUG:celery.utils.functional:
def unlock_chord(self, group_id, callback, interval=0, max_retries=1, result=2, Result=3, GroupResult=4, result_from_tuple=5, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def weekly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache(count, batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def xmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def update_enable_rule_exam(value):
    return 1

DEBUG:celery.utils.functional:
def zbt_buy_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def xstarmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def update_staff_balance():
    return 1

DEBUG:celery.utils.functional:
def update_enable_pay_method(enable_list):
    return 1

DEBUG:celery.utils.functional:
def check_b2c_trade_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_monitor_data_task(self):
    return 1

DEBUG:celery.utils.functional:
def chunks(task, it, n):
    return 1

DEBUG:celery.utils.functional:
def case_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_case_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def delete_old_package_items(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def group(self, tasks, result, group_id, partial_args, add_to_parent=0):
    return 1

DEBUG:celery.utils.functional:
def update_register_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def case_pk_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def chain(*args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def delete_old_user_active_point_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_today_amount():
    return 1

DEBUG:celery.utils.functional:
def sync_items_price_task(self):
    return 1

DEBUG:celery.utils.functional:
def init_system_task(self):
    return 1

DEBUG:celery.utils.functional:
def chord(self, header, body, partial_args=0, interval=1, countdown=2, max_retries=3, eager=4, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def case_bot_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_recharge_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def case_pk_join_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_month_amount():
    return 1

DEBUG:celery.utils.functional:
def cleanup_duplicate_roll_room_bets():
    return 1

DEBUG:celery.utils.functional:
def hourly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_new_user_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def roll_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def backend_cleanup():
    return 1

DEBUG:celery.utils.functional:
def delete_old_balance_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_domain_verify_switch(value):
    return 1

DEBUG:celery.utils.functional:
def check_envelop_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def daily_lottery_task(self):
    return 1

/usr/local/lib/python3.8/site-packages/celery/platforms.py:841: SecurityWarning: You're running the worker with superuser privileges: this is
absolutely not recommended!

Please specify a different user using the --uid option.

User information: uid=0 euid=0 gid=0 egid=0

  warnings.warn(SecurityWarning(ROOT_DISCOURAGED.format(
[2025-07-18 12:09:34,997: INFO/MainProcess] Connected to redis://localhost:6379/0
[2025-07-18 12:09:35,008: INFO/MainProcess] mingle: searching for neighbors
[2025-07-18 12:09:36,158: INFO/MainProcess] mingle: all alone
[2025-07-18 12:09:36,408: INFO/MainProcess] celery@iZj6c3ddfpik1c7yalqdt4Z ready.
[2025-07-18 12:09:37,126: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[b3712dda-008d-47b4-a3ca-57b23d46aab0] received
[2025-07-18 12:09:37,128: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 12:09:37,184: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 12:09:37,253: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[b3712dda-008d-47b4-a3ca-57b23d46aab0] succeeded in 0.12583539899787866s: None
[2025-07-18 12:09:37,466: INFO/MainProcess] Task thworker.check_b2c_trade_state[948f2434-7256-46f6-80c0-c0b5829fe4e7] received
[2025-07-18 12:09:37,468: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 12:09:37,469: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 12:09:37,475: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 12:09:37,477: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[948f2434-7256-46f6-80c0-c0b5829fe4e7] succeeded in 0.00953262997791171s: 'Success: B2C trade state checked'
[2025-07-18 12:14:32,213: INFO/MainProcess] Task thworker.update_case_records_cache[fb7178dd-c335-4cd2-ad72-ad517302bdae] received
[2025-07-18 12:14:32,215: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 12:14:32,217: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 12:14:32,217: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 12:14:32,219: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[fb7178dd-c335-4cd2-ad72-ad517302bdae] succeeded in 0.003701231995364651s: 'Success: Updated 100 case records cache'
[2025-07-18 12:14:36,937: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[34f2e0f7-cfbf-4717-a737-7e26dde1f8de] received
[2025-07-18 12:14:36,945: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 12:14:36,962: ERROR/ForkPoolWorker-2] 清理 RollRoomBet 记录时发生错误: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
[2025-07-18 12:14:36,983: ERROR/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[34f2e0f7-cfbf-4717-a737-7e26dde1f8de] raised unexpected: OperationalError(1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
  File "/app/roll/tasks.py", line 37, in cleanup_duplicate_roll_room_bets
    latest_room = RollRoom.objects.filter(enable=True).order_by('-create_time').first()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 674, in first
    for obj in (self if self.ordered else self.order_by('pk'))[:1]:
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 280, in __iter__
    self._fetch_all()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 1324, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 51, in __iter__
    results = compiler.execute_sql(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/compiler.py", line 1175, in execute_sql
    cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.OperationalError: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
[2025-07-18 12:14:37,468: INFO/MainProcess] Task thworker.check_b2c_trade_state[49a52e14-c55c-4f71-a9c1-6efa5d86b8a2] received
[2025-07-18 12:14:37,469: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 12:14:37,470: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 12:14:37,488: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[49a52e14-c55c-4f71-a9c1-6efa5d86b8a2] succeeded in 0.018767161993309855s: 'Success: B2C trade state checked'
DEBUG:asyncio:Using selector: EpollSelector
DEBUG:celery.utils.functional:
def update_enable_rule_exam(value):
    return 1

DEBUG:celery.utils.functional:
def check_room_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def accumulate(self, *args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_staff_balance():
    return 1

DEBUG:celery.utils.functional:
def update_enable_pay_method(enable_list):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache_task(self, count=0):
    return 1

DEBUG:celery.utils.functional:
def check_pendclose_trade_task(self):
    return 1

DEBUG:celery.utils.functional:
def unlock_chord(self, group_id, callback, interval=0, max_retries=1, result=2, Result=3, GroupResult=4, result_from_tuple=5, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def weekly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_case_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def delete_old_package_items(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def xmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def update_register_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def zbt_buy_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def xstarmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def delete_old_user_active_point_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_today_amount():
    return 1

DEBUG:celery.utils.functional:
def check_b2c_trade_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_monitor_data_task(self):
    return 1

DEBUG:celery.utils.functional:
def chunks(task, it, n):
    return 1

DEBUG:celery.utils.functional:
def case_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def group(self, tasks, result, group_id, partial_args, add_to_parent=0):
    return 1

DEBUG:celery.utils.functional:
def update_recharge_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def case_pk_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def chain(*args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_month_amount():
    return 1

DEBUG:celery.utils.functional:
def cleanup_duplicate_roll_room_bets():
    return 1

DEBUG:celery.utils.functional:
def sync_items_price_task(self):
    return 1

DEBUG:celery.utils.functional:
def init_system_task(self):
    return 1

DEBUG:celery.utils.functional:
def chord(self, header, body, partial_args=0, interval=1, countdown=2, max_retries=3, eager=4, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_new_user_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def delete_old_balance_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def case_bot_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_domain_verify_switch(value):
    return 1

DEBUG:celery.utils.functional:
def case_pk_join_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def hourly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_daily_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def roll_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def backend_cleanup():
    return 1

DEBUG:celery.utils.functional:
def delete_old_log_entries(batch_size=0, days=1):
    return 1

DEBUG:celery.utils.functional:
def update_enable_based_on_coins(min_coins, max_coins):
    return 1

DEBUG:celery.utils.functional:
def check_envelop_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache(count, batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def daily_lottery_task(self):
    return 1

/usr/local/lib/python3.8/site-packages/celery/platforms.py:841: SecurityWarning: You're running the worker with superuser privileges: this is
absolutely not recommended!

Please specify a different user using the --uid option.

User information: uid=0 euid=0 gid=0 egid=0

  warnings.warn(SecurityWarning(ROOT_DISCOURAGED.format(
[2025-07-18 12:18:12,992: INFO/MainProcess] Connected to redis://localhost:6379/0
[2025-07-18 12:18:12,996: INFO/MainProcess] mingle: searching for neighbors
[2025-07-18 12:18:14,065: INFO/MainProcess] mingle: all alone
[2025-07-18 12:18:14,271: INFO/MainProcess] celery@iZj6c3ddfpik1c7yalqdt4Z ready.
[2025-07-18 12:19:36,996: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[e2597bbf-ef9c-4893-a2ac-6befa820ef9e] received
[2025-07-18 12:19:37,001: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 12:19:37,038: ERROR/ForkPoolWorker-2] 清理 RollRoomBet 记录时发生错误: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
[2025-07-18 12:19:37,061: ERROR/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[e2597bbf-ef9c-4893-a2ac-6befa820ef9e] raised unexpected: OperationalError(1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
  File "/app/roll/tasks.py", line 37, in cleanup_duplicate_roll_room_bets
    latest_room = RollRoom.objects.filter(enable=True).order_by('-create_time').first()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 674, in first
    for obj in (self if self.ordered else self.order_by('pk'))[:1]:
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 280, in __iter__
    self._fetch_all()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 1324, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 51, in __iter__
    results = compiler.execute_sql(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/compiler.py", line 1175, in execute_sql
    cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.OperationalError: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
[2025-07-18 12:19:37,349: INFO/MainProcess] Task thworker.update_case_records_cache[c13e64fe-f744-47e2-a250-636c885b7530] received
[2025-07-18 12:19:37,351: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 12:19:37,496: INFO/MainProcess] Task thworker.check_b2c_trade_state[a855018e-3248-4a36-a2d6-24cb4f818482] received
[2025-07-18 12:19:37,498: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 12:19:37,500: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 12:19:37,510: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[a855018e-3248-4a36-a2d6-24cb4f818482] succeeded in 0.013059938995866105s: 'Success: B2C trade state checked'
[2025-07-18 12:19:47,466: ERROR/ForkPoolWorker-2] 更新 CaseRecord 缓存时发生错误: (1054, "Unknown column 'box_case.name_zh' in 'field list'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 173, in __get__
    rel_obj = self.field.get_cached_value(instance)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/mixins.py", line 15, in get_cached_value
    return instance._state.fields_cache[cache_name]
KeyError: 'case'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'box_case.name_zh' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/app/box/tasks.py", line 58, in update_case_records_cache
    batch_data = CaseRecordSerializer(batch_records, many=True).data
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 795, in data
    ret = super().data
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 249, in data
    self._data = self.to_representation(self.instance)
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 713, in to_representation
    return [
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 714, in <listcomp>
    self.child.to_representation(item) for item in iterable
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 538, in to_representation
    ret[field.field_name] = field.to_representation(attribute)
  File "/usr/local/lib/python3.8/site-packages/rest_framework/fields.py", line 1872, in to_representation
    return method(value)
  File "/app/box/serializers.py", line 117, in get_case_info
    return CaseSerializer(obj.case, fields=('id', 'case_key', 'name', 'name_en', 'name_zh_hans', 'cover', 'price', 'open_count')).data
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 187, in __get__
    rel_obj = self.get_object(instance)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 154, in get_object
    return qs.get(self.field.get_reverse_related_filter(instance))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 431, in get
    num = len(clone)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 262, in __len__
    self._fetch_all()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 1324, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 51, in __iter__
    results = compiler.execute_sql(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/compiler.py", line 1175, in execute_sql
    cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.OperationalError: (1054, "Unknown column 'box_case.name_zh' in 'field list'")
[2025-07-18 12:19:47,512: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 12:19:47,539: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[c13e64fe-f744-47e2-a250-636c885b7530] succeeded in 10.188354437996168s: 'Success: Updated 100 case records cache'
DEBUG:asyncio:Using selector: EpollSelector
DEBUG:celery.utils.functional:
def daily_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def accumulate(self, *args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_daily_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def check_room_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def unlock_chord(self, group_id, callback, interval=0, max_retries=1, result=2, Result=3, GroupResult=4, result_from_tuple=5, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def delete_old_log_entries(batch_size=0, days=1):
    return 1

DEBUG:celery.utils.functional:
def update_enable_based_on_coins(min_coins, max_coins):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache_task(self, count=0):
    return 1

DEBUG:celery.utils.functional:
def check_pendclose_trade_task(self):
    return 1

DEBUG:celery.utils.functional:
def xmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def weekly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache(count, batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def xstarmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def update_enable_rule_exam(value):
    return 1

DEBUG:celery.utils.functional:
def zbt_buy_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def chunks(task, it, n):
    return 1

DEBUG:celery.utils.functional:
def update_staff_balance():
    return 1

DEBUG:celery.utils.functional:
def update_enable_pay_method(enable_list):
    return 1

DEBUG:celery.utils.functional:
def check_b2c_trade_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_monitor_data_task(self):
    return 1

DEBUG:celery.utils.functional:
def group(self, tasks, result, group_id, partial_args, add_to_parent=0):
    return 1

DEBUG:celery.utils.functional:
def case_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_case_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def chain(*args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def delete_old_package_items(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_register_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def case_pk_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def chord(self, header, body, partial_args=0, interval=1, countdown=2, max_retries=3, eager=4, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def delete_old_user_active_point_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_today_amount():
    return 1

DEBUG:celery.utils.functional:
def sync_items_price_task(self):
    return 1

DEBUG:celery.utils.functional:
def init_system_task(self):
    return 1

DEBUG:celery.utils.functional:
def case_bot_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_recharge_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def case_pk_join_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_month_amount():
    return 1

DEBUG:celery.utils.functional:
def backend_cleanup():
    return 1

DEBUG:celery.utils.functional:
def cleanup_duplicate_roll_room_bets():
    return 1

DEBUG:celery.utils.functional:
def hourly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_new_user_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def roll_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_balance_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def check_envelop_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_domain_verify_switch(value):
    return 1

/usr/local/lib/python3.8/site-packages/celery/platforms.py:841: SecurityWarning: You're running the worker with superuser privileges: this is
absolutely not recommended!

Please specify a different user using the --uid option.

User information: uid=0 euid=0 gid=0 egid=0

  warnings.warn(SecurityWarning(ROOT_DISCOURAGED.format(
[2025-07-18 12:23:17,986: INFO/MainProcess] Connected to redis://localhost:6379/0
[2025-07-18 12:23:18,023: INFO/MainProcess] mingle: searching for neighbors
[2025-07-18 12:23:19,147: INFO/MainProcess] mingle: all alone
[2025-07-18 12:23:19,273: INFO/MainProcess] celery@iZj6c3ddfpik1c7yalqdt4Z ready.
[2025-07-18 12:24:36,981: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[6d2c41bb-ab3b-4608-baac-8b7851311482] received
[2025-07-18 12:24:36,984: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 12:24:36,986: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 12:24:36,989: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[6d2c41bb-ab3b-4608-baac-8b7851311482] succeeded in 0.005813705996843055s: None
[2025-07-18 12:24:37,347: INFO/MainProcess] Task thworker.update_case_records_cache[8ef357a0-9dc0-4e39-b40e-dbca90928ceb] received
[2025-07-18 12:24:37,353: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 12:24:37,466: INFO/MainProcess] Task thworker.check_b2c_trade_state[a1fe676d-1c12-4f17-ade1-586132a00bf1] received
[2025-07-18 12:24:37,468: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 12:24:37,470: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 12:24:37,479: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[a1fe676d-1c12-4f17-ade1-586132a00bf1] succeeded in 0.011660824005957693s: 'Success: B2C trade state checked'
[2025-07-18 12:24:37,482: INFO/ForkPoolWorker-1] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 12:24:46,867: ERROR/ForkPoolWorker-2] 更新 CaseRecord 缓存时发生错误: (1054, "Unknown column 'box_case.name_zh' in 'field list'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 173, in __get__
    rel_obj = self.field.get_cached_value(instance)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/mixins.py", line 15, in get_cached_value
    return instance._state.fields_cache[cache_name]
KeyError: 'case'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'box_case.name_zh' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/app/box/tasks.py", line 58, in update_case_records_cache
    batch_data = CaseRecordSerializer(batch_records, many=True).data
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 795, in data
    ret = super().data
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 249, in data
    self._data = self.to_representation(self.instance)
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 713, in to_representation
    return [
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 714, in <listcomp>
    self.child.to_representation(item) for item in iterable
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 538, in to_representation
    ret[field.field_name] = field.to_representation(attribute)
  File "/usr/local/lib/python3.8/site-packages/rest_framework/fields.py", line 1872, in to_representation
    return method(value)
  File "/app/box/serializers.py", line 117, in get_case_info
    return CaseSerializer(obj.case, fields=('id', 'case_key', 'name', 'name_en', 'name_zh_hans', 'cover', 'price', 'open_count')).data
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 187, in __get__
    rel_obj = self.get_object(instance)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 154, in get_object
    return qs.get(self.field.get_reverse_related_filter(instance))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 431, in get
    num = len(clone)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 262, in __len__
    self._fetch_all()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 1324, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 51, in __iter__
    results = compiler.execute_sql(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/compiler.py", line 1175, in execute_sql
    cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.OperationalError: (1054, "Unknown column 'box_case.name_zh' in 'field list'")
[2025-07-18 12:24:46,909: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 12:24:46,933: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[8ef357a0-9dc0-4e39-b40e-dbca90928ceb] succeeded in 9.580242308991728s: 'Success: Updated 100 case records cache'
DEBUG:asyncio:Using selector: EpollSelector
DEBUG:celery.utils.functional:
def check_pendclose_trade_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache(count, batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache_task(self, count=0):
    return 1

DEBUG:celery.utils.functional:
def accumulate(self, *args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def weekly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_enable_rule_exam(value):
    return 1

DEBUG:celery.utils.functional:
def unlock_chord(self, group_id, callback, interval=0, max_retries=1, result=2, Result=3, GroupResult=4, result_from_tuple=5, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_staff_balance():
    return 1

DEBUG:celery.utils.functional:
def update_enable_pay_method(enable_list):
    return 1

DEBUG:celery.utils.functional:
def zbt_buy_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def xmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def update_monitor_data_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_case_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def check_b2c_trade_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def xstarmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def delete_old_package_items(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_register_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def case_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def chunks(task, it, n):
    return 1

DEBUG:celery.utils.functional:
def delete_old_user_active_point_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_today_amount():
    return 1

DEBUG:celery.utils.functional:
def case_pk_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def group(self, tasks, result, group_id, partial_args, add_to_parent=0):
    return 1

DEBUG:celery.utils.functional:
def init_system_task(self):
    return 1

DEBUG:celery.utils.functional:
def sync_items_price_task(self):
    return 1

DEBUG:celery.utils.functional:
def chain(*args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def case_bot_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_recharge_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def chord(self, header, body, partial_args=0, interval=1, countdown=2, max_retries=3, eager=4, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_month_amount():
    return 1

DEBUG:celery.utils.functional:
def cleanup_duplicate_roll_room_bets():
    return 1

DEBUG:celery.utils.functional:
def case_pk_join_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_new_user_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def delete_old_balance_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def hourly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def roll_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_domain_verify_switch(value):
    return 1

DEBUG:celery.utils.functional:
def backend_cleanup():
    return 1

DEBUG:celery.utils.functional:
def check_envelop_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_daily_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def daily_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_enable_based_on_coins(min_coins, max_coins):
    return 1

DEBUG:celery.utils.functional:
def check_room_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_log_entries(batch_size=0, days=1):
    return 1

/usr/local/lib/python3.8/site-packages/celery/platforms.py:841: SecurityWarning: You're running the worker with superuser privileges: this is
absolutely not recommended!

Please specify a different user using the --uid option.

User information: uid=0 euid=0 gid=0 egid=0

  warnings.warn(SecurityWarning(ROOT_DISCOURAGED.format(
[2025-07-18 12:29:01,543: INFO/MainProcess] Connected to redis://localhost:6379/0
[2025-07-18 12:29:01,634: INFO/MainProcess] mingle: searching for neighbors
[2025-07-18 12:29:02,785: INFO/MainProcess] mingle: all alone
[2025-07-18 12:29:03,039: INFO/MainProcess] celery@iZj6c3ddfpik1c7yalqdt4Z ready.
[2025-07-18 12:29:37,014: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[2c860ed8-9bbf-4f06-84e2-675ab4e279ad] received
[2025-07-18 12:29:37,017: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 12:29:37,019: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 12:29:37,022: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[2c860ed8-9bbf-4f06-84e2-675ab4e279ad] succeeded in 0.006082236999645829s: None
[2025-07-18 12:29:37,357: INFO/MainProcess] Task thworker.update_case_records_cache[25a609f8-d2f8-4d24-aa03-2ea0b34ae155] received
[2025-07-18 12:29:37,360: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 12:29:37,474: INFO/MainProcess] Task thworker.check_b2c_trade_state[92bf6096-483c-417f-9ef3-0834294c4d18] received
[2025-07-18 12:29:37,477: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 12:29:37,482: INFO/ForkPoolWorker-1] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 12:29:37,484: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 12:29:37,487: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[92bf6096-483c-417f-9ef3-0834294c4d18] succeeded in 0.01060658399364911s: 'Success: B2C trade state checked'
[2025-07-18 12:29:46,878: ERROR/ForkPoolWorker-2] 更新 CaseRecord 缓存时发生错误: (1054, "Unknown column 'box_case.name_zh' in 'field list'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 173, in __get__
    rel_obj = self.field.get_cached_value(instance)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/mixins.py", line 15, in get_cached_value
    return instance._state.fields_cache[cache_name]
KeyError: 'case'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'box_case.name_zh' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/app/box/tasks.py", line 58, in update_case_records_cache
    batch_data = CaseRecordSerializer(batch_records, many=True).data
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 795, in data
    ret = super().data
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 249, in data
    self._data = self.to_representation(self.instance)
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 713, in to_representation
    return [
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 714, in <listcomp>
    self.child.to_representation(item) for item in iterable
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 538, in to_representation
    ret[field.field_name] = field.to_representation(attribute)
  File "/usr/local/lib/python3.8/site-packages/rest_framework/fields.py", line 1872, in to_representation
    return method(value)
  File "/app/box/serializers.py", line 117, in get_case_info
    return CaseSerializer(obj.case, fields=('id', 'case_key', 'name', 'name_en', 'name_zh_hans', 'cover', 'price', 'open_count')).data
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 187, in __get__
    rel_obj = self.get_object(instance)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 154, in get_object
    return qs.get(self.field.get_reverse_related_filter(instance))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 431, in get
    num = len(clone)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 262, in __len__
    self._fetch_all()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 1324, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 51, in __iter__
    results = compiler.execute_sql(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/compiler.py", line 1175, in execute_sql
    cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.OperationalError: (1054, "Unknown column 'box_case.name_zh' in 'field list'")
[2025-07-18 12:29:46,924: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 12:29:46,982: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[25a609f8-d2f8-4d24-aa03-2ea0b34ae155] succeeded in 9.621509372023866s: 'Success: Updated 100 case records cache'
[2025-07-18 12:34:36,974: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[30b6fb3b-41f4-433d-8f4f-f66c824f1acc] received
[2025-07-18 12:34:36,975: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 12:34:36,977: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 12:34:36,985: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[30b6fb3b-41f4-433d-8f4f-f66c824f1acc] succeeded in 0.010523400007514283s: None
[2025-07-18 12:34:37,355: INFO/MainProcess] Task thworker.update_case_records_cache[f463d932-123b-49a8-90fa-481a15c66ee9] received
[2025-07-18 12:34:37,356: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 12:34:37,358: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 12:34:37,358: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 12:34:37,362: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[f463d932-123b-49a8-90fa-481a15c66ee9] succeeded in 0.0061095739947631955s: 'Success: Updated 100 case records cache'
[2025-07-18 12:34:37,484: INFO/MainProcess] Task thworker.check_b2c_trade_state[cdb45258-2545-4413-ac3d-a38be783df0b] received
[2025-07-18 12:34:37,486: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 12:34:37,487: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 12:34:37,500: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 12:34:37,501: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[cdb45258-2545-4413-ac3d-a38be783df0b] succeeded in 0.015651230001822114s: 'Success: B2C trade state checked'
[2025-07-18 12:39:36,979: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[9f38ad6f-a6f3-423b-bd6b-17cb9884b50f] received
[2025-07-18 12:39:36,980: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 12:39:36,981: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 12:39:36,993: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[9f38ad6f-a6f3-423b-bd6b-17cb9884b50f] succeeded in 0.013023011008044705s: None
[2025-07-18 12:39:37,354: INFO/MainProcess] Task thworker.update_case_records_cache[0cf7ad8a-318e-4a32-ad7a-c07a9be71bf6] received
[2025-07-18 12:39:37,361: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 12:39:37,369: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 12:39:37,369: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 12:39:37,372: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[0cf7ad8a-318e-4a32-ad7a-c07a9be71bf6] succeeded in 0.010767994011985138s: 'Success: Updated 100 case records cache'
[2025-07-18 12:39:37,506: INFO/MainProcess] Task thworker.check_b2c_trade_state[bcb4590d-0eb9-4449-844f-234db565a6d5] received
[2025-07-18 12:39:37,508: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 12:39:37,509: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 12:39:37,511: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 12:39:37,513: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[bcb4590d-0eb9-4449-844f-234db565a6d5] succeeded in 0.005317820003256202s: 'Success: B2C trade state checked'
[2025-07-18 12:44:36,982: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[ca0252d9-53e5-440b-aecb-1a71e162473e] received
[2025-07-18 12:44:36,984: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 12:44:36,991: ERROR/ForkPoolWorker-2] 清理 RollRoomBet 记录时发生错误: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
[2025-07-18 12:44:36,999: ERROR/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[ca0252d9-53e5-440b-aecb-1a71e162473e] raised unexpected: OperationalError(1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
  File "/app/roll/tasks.py", line 37, in cleanup_duplicate_roll_room_bets
    latest_room = RollRoom.objects.filter(enable=True).order_by('-create_time').first()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 674, in first
    for obj in (self if self.ordered else self.order_by('pk'))[:1]:
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 280, in __iter__
    self._fetch_all()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 1324, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 51, in __iter__
    results = compiler.execute_sql(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/compiler.py", line 1175, in execute_sql
    cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.OperationalError: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
[2025-07-18 12:44:37,351: INFO/MainProcess] Task thworker.update_case_records_cache[c117fa9f-669e-497c-8fb2-931f660c2939] received
[2025-07-18 12:44:37,353: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 12:44:37,357: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 12:44:37,357: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 12:44:37,362: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[c117fa9f-669e-497c-8fb2-931f660c2939] succeeded in 0.00916391602368094s: 'Success: Updated 100 case records cache'
[2025-07-18 12:44:37,514: INFO/MainProcess] Task thworker.check_b2c_trade_state[ed9721d8-5fae-4f8f-979d-61f08f3caba0] received
[2025-07-18 12:44:37,519: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 12:44:37,519: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 12:44:37,521: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 12:44:37,524: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[ed9721d8-5fae-4f8f-979d-61f08f3caba0] succeeded in 0.006201852986123413s: 'Success: B2C trade state checked'
[2025-07-18 12:49:36,992: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[e3c646d0-6bbe-423e-aea6-1c3708f5ae9d] received
[2025-07-18 12:49:36,994: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 12:49:36,995: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 12:49:37,001: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[e3c646d0-6bbe-423e-aea6-1c3708f5ae9d] succeeded in 0.007915167981991544s: None
[2025-07-18 12:49:37,390: INFO/MainProcess] Task thworker.update_case_records_cache[844d2346-5bc3-4d14-93b6-f1b9922b381e] received
[2025-07-18 12:49:37,391: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 12:49:37,400: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 12:49:37,401: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 12:49:37,403: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[844d2346-5bc3-4d14-93b6-f1b9922b381e] succeeded in 0.0119143940100912s: 'Success: Updated 100 case records cache'
[2025-07-18 12:49:37,591: INFO/MainProcess] Task thworker.check_b2c_trade_state[794e40c8-1a23-4360-a6a5-ba027c46811a] received
[2025-07-18 12:49:37,593: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 12:49:37,594: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 12:49:37,595: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 12:49:37,596: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[794e40c8-1a23-4360-a6a5-ba027c46811a] succeeded in 0.003832362999673933s: 'Success: B2C trade state checked'
[2025-07-18 12:54:36,998: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[62f9de9e-0b5c-43a4-a44c-308231a19218] received
[2025-07-18 12:54:36,999: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 12:54:37,007: ERROR/ForkPoolWorker-2] 清理 RollRoomBet 记录时发生错误: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
[2025-07-18 12:54:37,015: ERROR/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[62f9de9e-0b5c-43a4-a44c-308231a19218] raised unexpected: OperationalError(1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
  File "/app/roll/tasks.py", line 37, in cleanup_duplicate_roll_room_bets
    latest_room = RollRoom.objects.filter(enable=True).order_by('-create_time').first()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 674, in first
    for obj in (self if self.ordered else self.order_by('pk'))[:1]:
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 280, in __iter__
    self._fetch_all()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 1324, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 51, in __iter__
    results = compiler.execute_sql(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/compiler.py", line 1175, in execute_sql
    cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.OperationalError: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
[2025-07-18 12:54:37,380: INFO/MainProcess] Task thworker.update_case_records_cache[e31df7dc-753d-4784-ba5a-245f50970cd4] received
[2025-07-18 12:54:37,381: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 12:54:37,384: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 12:54:37,384: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 12:54:37,388: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[e31df7dc-753d-4784-ba5a-245f50970cd4] succeeded in 0.006861755013233051s: 'Success: Updated 100 case records cache'
[2025-07-18 12:54:37,595: INFO/MainProcess] Task thworker.check_b2c_trade_state[3b4c0de0-b756-4a15-8bec-0d8abc759284] received
[2025-07-18 12:54:37,597: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 12:54:37,598: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 12:54:37,601: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 12:54:37,602: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[3b4c0de0-b756-4a15-8bec-0d8abc759284] succeeded in 0.005858655989868566s: 'Success: B2C trade state checked'
[2025-07-18 12:59:37,002: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[fd0e7bac-7074-462a-a2a1-6e122ed69b47] received
[2025-07-18 12:59:37,003: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 12:59:37,013: ERROR/ForkPoolWorker-2] 清理 RollRoomBet 记录时发生错误: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
[2025-07-18 12:59:37,019: ERROR/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[fd0e7bac-7074-462a-a2a1-6e122ed69b47] raised unexpected: OperationalError(1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
  File "/app/roll/tasks.py", line 37, in cleanup_duplicate_roll_room_bets
    latest_room = RollRoom.objects.filter(enable=True).order_by('-create_time').first()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 674, in first
    for obj in (self if self.ordered else self.order_by('pk'))[:1]:
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 280, in __iter__
    self._fetch_all()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 1324, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 51, in __iter__
    results = compiler.execute_sql(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/compiler.py", line 1175, in execute_sql
    cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.OperationalError: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
[2025-07-18 12:59:37,387: INFO/MainProcess] Task thworker.update_case_records_cache[27891b99-3928-4d43-a3b6-20aa1c63f438] received
[2025-07-18 12:59:37,396: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 12:59:37,399: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 12:59:37,399: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 12:59:37,400: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[27891b99-3928-4d43-a3b6-20aa1c63f438] succeeded in 0.004332511976826936s: 'Success: Updated 100 case records cache'
[2025-07-18 12:59:37,606: INFO/MainProcess] Task thworker.check_b2c_trade_state[51965aa0-fcd8-4484-b27f-551d0d5eb8d0] received
[2025-07-18 12:59:37,610: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 12:59:37,611: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 12:59:37,612: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 12:59:37,613: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[51965aa0-fcd8-4484-b27f-551d0d5eb8d0] succeeded in 0.0030396619986277074s: 'Success: B2C trade state checked'
[2025-07-18 13:00:00,013: INFO/MainProcess] Task thworker.hourly_lottery[1c7d749e-4b63-424e-b939-e6b76b1bf586] received
[2025-07-18 13:00:00,022: INFO/ForkPoolWorker-2] ------------------------ 整点抽奖 ------------------------
[2025-07-18 13:00:00,022: INFO/ForkPoolWorker-2] [Celery] 每小时抽奖任务完成
[2025-07-18 13:00:00,039: INFO/ForkPoolWorker-2] Task thworker.hourly_lottery[1c7d749e-4b63-424e-b939-e6b76b1bf586] succeeded in 0.0177238569885958s: 'Success: Hourly lottery completed'
[2025-07-18 13:04:36,999: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[f564ac29-f17c-444e-b859-e24694e017bb] received
[2025-07-18 13:04:37,003: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 13:04:37,012: ERROR/ForkPoolWorker-2] 清理 RollRoomBet 记录时发生错误: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
[2025-07-18 13:04:37,014: ERROR/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[f564ac29-f17c-444e-b859-e24694e017bb] raised unexpected: OperationalError(1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
  File "/app/roll/tasks.py", line 37, in cleanup_duplicate_roll_room_bets
    latest_room = RollRoom.objects.filter(enable=True).order_by('-create_time').first()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 674, in first
    for obj in (self if self.ordered else self.order_by('pk'))[:1]:
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 280, in __iter__
    self._fetch_all()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 1324, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 51, in __iter__
    results = compiler.execute_sql(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/compiler.py", line 1175, in execute_sql
    cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.OperationalError: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
[2025-07-18 13:04:37,413: INFO/MainProcess] Task thworker.update_case_records_cache[3edd4954-f1b3-42f3-902b-6407a7b46448] received
[2025-07-18 13:04:37,414: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 13:04:37,428: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 13:04:37,428: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 13:04:37,435: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[3edd4954-f1b3-42f3-902b-6407a7b46448] succeeded in 0.02154738499666564s: 'Success: Updated 100 case records cache'
[2025-07-18 13:04:37,592: INFO/MainProcess] Task thworker.check_b2c_trade_state[b3141d97-d036-41da-9204-a9d597a97add] received
[2025-07-18 13:04:37,594: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 13:04:37,595: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 13:04:37,597: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 13:04:37,601: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[b3141d97-d036-41da-9204-a9d597a97add] succeeded in 0.007808296999428421s: 'Success: B2C trade state checked'
[2025-07-18 13:09:37,012: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[0b769604-612e-4f5e-86cb-3355a54918da] received
[2025-07-18 13:09:37,013: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 13:09:37,020: ERROR/ForkPoolWorker-2] 清理 RollRoomBet 记录时发生错误: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
[2025-07-18 13:09:37,023: ERROR/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[0b769604-612e-4f5e-86cb-3355a54918da] raised unexpected: OperationalError(1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
  File "/app/roll/tasks.py", line 37, in cleanup_duplicate_roll_room_bets
    latest_room = RollRoom.objects.filter(enable=True).order_by('-create_time').first()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 674, in first
    for obj in (self if self.ordered else self.order_by('pk'))[:1]:
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 280, in __iter__
    self._fetch_all()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 1324, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 51, in __iter__
    results = compiler.execute_sql(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/compiler.py", line 1175, in execute_sql
    cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.OperationalError: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
[2025-07-18 13:09:37,422: INFO/MainProcess] Task thworker.update_case_records_cache[f32b44b2-14d0-43de-91bf-75253dc1d822] received
[2025-07-18 13:09:37,423: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 13:09:37,425: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 13:09:37,425: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 13:09:37,427: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[f32b44b2-14d0-43de-91bf-75253dc1d822] succeeded in 0.003935006010578945s: 'Success: Updated 100 case records cache'
[2025-07-18 13:09:37,608: INFO/MainProcess] Task thworker.check_b2c_trade_state[71f91bb4-bfbb-46f8-bfbc-38cfd3ed2a1c] received
[2025-07-18 13:09:37,616: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 13:09:37,617: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 13:09:37,619: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 13:09:37,620: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[71f91bb4-bfbb-46f8-bfbc-38cfd3ed2a1c] succeeded in 0.01100793801015243s: 'Success: B2C trade state checked'
[2025-07-18 13:14:37,043: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[e94295f9-b13b-422b-851b-8f3a9cd36e64] received
[2025-07-18 13:14:37,045: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 13:14:37,045: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 13:14:37,054: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[e94295f9-b13b-422b-851b-8f3a9cd36e64] succeeded in 0.009220285981427878s: None
[2025-07-18 13:14:37,420: INFO/MainProcess] Task thworker.update_case_records_cache[4baea82b-0d81-4db7-9d1b-5e14e3922571] received
[2025-07-18 13:14:37,421: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 13:14:37,423: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 13:14:37,424: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 13:14:37,427: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[4baea82b-0d81-4db7-9d1b-5e14e3922571] succeeded in 0.00672392500564456s: 'Success: Updated 100 case records cache'
[2025-07-18 13:14:37,613: INFO/MainProcess] Task thworker.check_b2c_trade_state[e68b9500-b888-43f7-a51a-4931f9412aa9] received
[2025-07-18 13:14:37,615: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 13:14:37,616: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 13:14:37,627: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[e68b9500-b888-43f7-a51a-4931f9412aa9] succeeded in 0.0123947259853594s: 'Success: B2C trade state checked'
[2025-07-18 13:14:37,635: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 13:19:37,043: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[615115a8-019e-4545-8481-1fc21e724a48] received
[2025-07-18 13:19:37,047: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 13:19:37,048: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 13:19:37,049: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[615115a8-019e-4545-8481-1fc21e724a48] succeeded in 0.0023870989971328527s: None
[2025-07-18 13:19:37,419: INFO/MainProcess] Task thworker.update_case_records_cache[30399c2a-8225-4667-a7cf-22ef6de47efe] received
[2025-07-18 13:19:37,420: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 13:19:37,624: INFO/MainProcess] Task thworker.check_b2c_trade_state[e2d28610-6a7d-4325-ad31-ddc37d4439bb] received
[2025-07-18 13:19:37,625: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 13:19:37,626: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 13:19:37,628: INFO/ForkPoolWorker-1] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 13:19:37,630: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[e2d28610-6a7d-4325-ad31-ddc37d4439bb] succeeded in 0.005056392983533442s: 'Success: B2C trade state checked'
[2025-07-18 13:19:46,966: ERROR/ForkPoolWorker-2] 更新 CaseRecord 缓存时发生错误: (1054, "Unknown column 'box_case.name_zh' in 'field list'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 173, in __get__
    rel_obj = self.field.get_cached_value(instance)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/mixins.py", line 15, in get_cached_value
    return instance._state.fields_cache[cache_name]
KeyError: 'case'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'box_case.name_zh' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/app/box/tasks.py", line 58, in update_case_records_cache
    batch_data = CaseRecordSerializer(batch_records, many=True).data
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 795, in data
    ret = super().data
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 249, in data
    self._data = self.to_representation(self.instance)
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 713, in to_representation
    return [
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 714, in <listcomp>
    self.child.to_representation(item) for item in iterable
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 538, in to_representation
    ret[field.field_name] = field.to_representation(attribute)
  File "/usr/local/lib/python3.8/site-packages/rest_framework/fields.py", line 1872, in to_representation
    return method(value)
  File "/app/box/serializers.py", line 117, in get_case_info
    return CaseSerializer(obj.case, fields=('id', 'case_key', 'name', 'name_en', 'name_zh_hans', 'cover', 'price', 'open_count')).data
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 187, in __get__
    rel_obj = self.get_object(instance)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 154, in get_object
    return qs.get(self.field.get_reverse_related_filter(instance))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 431, in get
    num = len(clone)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 262, in __len__
    self._fetch_all()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 1324, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 51, in __iter__
    results = compiler.execute_sql(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/compiler.py", line 1175, in execute_sql
    cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.OperationalError: (1054, "Unknown column 'box_case.name_zh' in 'field list'")
[2025-07-18 13:19:46,967: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 13:19:46,970: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[30399c2a-8225-4667-a7cf-22ef6de47efe] succeeded in 9.549891474016476s: 'Success: Updated 100 case records cache'
[2025-07-18 13:24:37,078: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[4d1fe300-e079-4dc5-aefb-9b66e2fecbe9] received
[2025-07-18 13:24:37,079: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 13:24:37,080: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 13:24:37,082: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[4d1fe300-e079-4dc5-aefb-9b66e2fecbe9] succeeded in 0.0029048220021650195s: None
[2025-07-18 13:24:37,421: INFO/MainProcess] Task thworker.update_case_records_cache[cfc0a06e-0c55-470e-9a23-7a14de8eafdd] received
[2025-07-18 13:24:37,422: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 13:24:37,630: INFO/MainProcess] Task thworker.check_b2c_trade_state[8655abac-24ba-4268-b6e3-dbf84d10ee54] received
[2025-07-18 13:24:37,639: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 13:24:37,639: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 13:24:37,640: INFO/ForkPoolWorker-1] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 13:24:37,644: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[8655abac-24ba-4268-b6e3-dbf84d10ee54] succeeded in 0.005684262025170028s: 'Success: B2C trade state checked'
[2025-07-18 13:24:49,220: ERROR/ForkPoolWorker-2] 更新 CaseRecord 缓存时发生错误: (1054, "Unknown column 'box_case.name_zh' in 'field list'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 173, in __get__
    rel_obj = self.field.get_cached_value(instance)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/mixins.py", line 15, in get_cached_value
    return instance._state.fields_cache[cache_name]
KeyError: 'case'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'box_case.name_zh' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/app/box/tasks.py", line 58, in update_case_records_cache
    batch_data = CaseRecordSerializer(batch_records, many=True).data
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 795, in data
    ret = super().data
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 249, in data
    self._data = self.to_representation(self.instance)
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 713, in to_representation
    return [
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 714, in <listcomp>
    self.child.to_representation(item) for item in iterable
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 538, in to_representation
    ret[field.field_name] = field.to_representation(attribute)
  File "/usr/local/lib/python3.8/site-packages/rest_framework/fields.py", line 1872, in to_representation
    return method(value)
  File "/app/box/serializers.py", line 117, in get_case_info
    return CaseSerializer(obj.case, fields=('id', 'case_key', 'name', 'name_en', 'name_zh_hans', 'cover', 'price', 'open_count')).data
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 187, in __get__
    rel_obj = self.get_object(instance)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 154, in get_object
    return qs.get(self.field.get_reverse_related_filter(instance))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 431, in get
    num = len(clone)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 262, in __len__
    self._fetch_all()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 1324, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 51, in __iter__
    results = compiler.execute_sql(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/compiler.py", line 1175, in execute_sql
    cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.OperationalError: (1054, "Unknown column 'box_case.name_zh' in 'field list'")
[2025-07-18 13:24:49,220: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 13:24:49,236: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[cfc0a06e-0c55-470e-9a23-7a14de8eafdd] succeeded in 11.814095476991497s: 'Success: Updated 100 case records cache'
[2025-07-18 13:29:37,044: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[d800c46e-c1fb-4d5a-89e3-12b2a99dfabb] received
[2025-07-18 13:29:37,046: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 13:29:37,047: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 13:29:37,048: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[d800c46e-c1fb-4d5a-89e3-12b2a99dfabb] succeeded in 0.0024373430060222745s: None
[2025-07-18 13:29:37,438: INFO/MainProcess] Task thworker.update_case_records_cache[4fa280cb-1311-49e5-b77b-e3f1e20101d7] received
[2025-07-18 13:29:37,440: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 13:29:37,633: INFO/MainProcess] Task thworker.check_b2c_trade_state[46a929fc-3661-479a-b83b-abece3c3bb9e] received
[2025-07-18 13:29:37,635: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 13:29:37,636: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 13:29:37,639: INFO/ForkPoolWorker-1] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 13:29:37,643: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[46a929fc-3661-479a-b83b-abece3c3bb9e] succeeded in 0.008178151998436078s: 'Success: B2C trade state checked'
[2025-07-18 13:29:49,623: ERROR/ForkPoolWorker-2] 更新 CaseRecord 缓存时发生错误: (1054, "Unknown column 'box_case.name_zh' in 'field list'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 173, in __get__
    rel_obj = self.field.get_cached_value(instance)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/mixins.py", line 15, in get_cached_value
    return instance._state.fields_cache[cache_name]
KeyError: 'case'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'box_case.name_zh' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/app/box/tasks.py", line 58, in update_case_records_cache
    batch_data = CaseRecordSerializer(batch_records, many=True).data
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 795, in data
    ret = super().data
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 249, in data
    self._data = self.to_representation(self.instance)
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 713, in to_representation
    return [
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 714, in <listcomp>
    self.child.to_representation(item) for item in iterable
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 538, in to_representation
    ret[field.field_name] = field.to_representation(attribute)
  File "/usr/local/lib/python3.8/site-packages/rest_framework/fields.py", line 1872, in to_representation
    return method(value)
  File "/app/box/serializers.py", line 117, in get_case_info
    return CaseSerializer(obj.case, fields=('id', 'case_key', 'name', 'name_en', 'name_zh_hans', 'cover', 'price', 'open_count')).data
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 187, in __get__
    rel_obj = self.get_object(instance)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 154, in get_object
    return qs.get(self.field.get_reverse_related_filter(instance))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 431, in get
    num = len(clone)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 262, in __len__
    self._fetch_all()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 1324, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 51, in __iter__
    results = compiler.execute_sql(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/compiler.py", line 1175, in execute_sql
    cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.OperationalError: (1054, "Unknown column 'box_case.name_zh' in 'field list'")
[2025-07-18 13:29:49,624: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 13:29:49,677: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[4fa280cb-1311-49e5-b77b-e3f1e20101d7] succeeded in 12.237386496010004s: 'Success: Updated 100 case records cache'
[2025-07-18 13:34:37,143: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[5769a3f0-22a9-4762-b4fe-634fddb7aaf7] received
[2025-07-18 13:34:37,144: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 13:34:37,149: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 13:34:37,156: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[5769a3f0-22a9-4762-b4fe-634fddb7aaf7] succeeded in 0.011314126022625715s: None
[2025-07-18 13:34:37,437: INFO/MainProcess] Task thworker.update_case_records_cache[16d32cb8-62cd-47b7-bb0a-1afe04d1735e] received
[2025-07-18 13:34:37,438: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 13:34:37,632: INFO/MainProcess] Task thworker.check_b2c_trade_state[32632b42-dc90-4952-b481-d2b42450e219] received
[2025-07-18 13:34:37,639: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 13:34:37,640: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 13:34:37,641: INFO/ForkPoolWorker-1] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 13:34:37,641: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[32632b42-dc90-4952-b481-d2b42450e219] succeeded in 0.008197697025025263s: 'Success: B2C trade state checked'
[2025-07-18 13:34:47,055: ERROR/ForkPoolWorker-2] 更新 CaseRecord 缓存时发生错误: (1054, "Unknown column 'box_case.name_zh' in 'field list'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 173, in __get__
    rel_obj = self.field.get_cached_value(instance)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/mixins.py", line 15, in get_cached_value
    return instance._state.fields_cache[cache_name]
KeyError: 'case'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'box_case.name_zh' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/app/box/tasks.py", line 58, in update_case_records_cache
    batch_data = CaseRecordSerializer(batch_records, many=True).data
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 795, in data
    ret = super().data
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 249, in data
    self._data = self.to_representation(self.instance)
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 713, in to_representation
    return [
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 714, in <listcomp>
    self.child.to_representation(item) for item in iterable
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 538, in to_representation
    ret[field.field_name] = field.to_representation(attribute)
  File "/usr/local/lib/python3.8/site-packages/rest_framework/fields.py", line 1872, in to_representation
    return method(value)
  File "/app/box/serializers.py", line 117, in get_case_info
    return CaseSerializer(obj.case, fields=('id', 'case_key', 'name', 'name_en', 'name_zh_hans', 'cover', 'price', 'open_count')).data
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 187, in __get__
    rel_obj = self.get_object(instance)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 154, in get_object
    return qs.get(self.field.get_reverse_related_filter(instance))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 431, in get
    num = len(clone)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 262, in __len__
    self._fetch_all()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 1324, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 51, in __iter__
    results = compiler.execute_sql(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/compiler.py", line 1175, in execute_sql
    cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.OperationalError: (1054, "Unknown column 'box_case.name_zh' in 'field list'")
[2025-07-18 13:34:47,056: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 13:34:47,057: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[16d32cb8-62cd-47b7-bb0a-1afe04d1735e] succeeded in 9.619139196001925s: 'Success: Updated 100 case records cache'
[2025-07-18 13:39:37,167: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[50f97a61-f9f1-4ecf-aca0-3d2f7e45de18] received
[2025-07-18 13:39:37,175: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 13:39:37,181: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 13:39:37,182: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[50f97a61-f9f1-4ecf-aca0-3d2f7e45de18] succeeded in 0.007321326993405819s: None
[2025-07-18 13:39:37,432: INFO/MainProcess] Task thworker.update_case_records_cache[fbcc99de-3908-4c77-92f4-f53255371663] received
[2025-07-18 13:39:37,434: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 13:39:37,436: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 13:39:37,436: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 13:39:37,439: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[fbcc99de-3908-4c77-92f4-f53255371663] succeeded in 0.005717699008528143s: 'Success: Updated 100 case records cache'
[2025-07-18 13:39:37,635: INFO/MainProcess] Task thworker.check_b2c_trade_state[76c84930-bea7-465a-8f44-9365fb38e5f8] received
[2025-07-18 13:39:37,638: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 13:39:37,639: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 13:39:37,644: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 13:39:37,648: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[76c84930-bea7-465a-8f44-9365fb38e5f8] succeeded in 0.010640202002832666s: 'Success: B2C trade state checked'
[2025-07-18 13:44:37,151: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[47a5ea09-077e-4f8c-af9f-d0e95cf4763c] received
[2025-07-18 13:44:37,153: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 13:44:37,161: ERROR/ForkPoolWorker-2] 清理 RollRoomBet 记录时发生错误: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
[2025-07-18 13:44:37,165: ERROR/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[47a5ea09-077e-4f8c-af9f-d0e95cf4763c] raised unexpected: OperationalError(1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
  File "/app/roll/tasks.py", line 37, in cleanup_duplicate_roll_room_bets
    latest_room = RollRoom.objects.filter(enable=True).order_by('-create_time').first()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 674, in first
    for obj in (self if self.ordered else self.order_by('pk'))[:1]:
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 280, in __iter__
    self._fetch_all()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 1324, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 51, in __iter__
    results = compiler.execute_sql(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/compiler.py", line 1175, in execute_sql
    cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.OperationalError: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
[2025-07-18 13:44:37,430: INFO/MainProcess] Task thworker.update_case_records_cache[255aeae8-8332-41f3-94ab-65c1cfdfadcc] received
[2025-07-18 13:44:37,431: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 13:44:37,434: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 13:44:37,434: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 13:44:37,436: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[255aeae8-8332-41f3-94ab-65c1cfdfadcc] succeeded in 0.005381814000429586s: 'Success: Updated 100 case records cache'
[2025-07-18 13:44:37,636: INFO/MainProcess] Task thworker.check_b2c_trade_state[54c756c8-ef63-440b-acd3-4e5e7b87ec5c] received
[2025-07-18 13:44:37,638: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 13:44:37,639: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 13:44:37,641: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[54c756c8-ef63-440b-acd3-4e5e7b87ec5c] succeeded in 0.0035029480059165508s: 'Success: B2C trade state checked'
[2025-07-18 13:44:37,644: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 13:49:37,205: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[3e5c8d10-40ef-477b-b45f-7c6f3489f2c9] received
[2025-07-18 13:49:37,208: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 13:49:37,234: ERROR/ForkPoolWorker-2] 清理 RollRoomBet 记录时发生错误: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
[2025-07-18 13:49:37,237: ERROR/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[3e5c8d10-40ef-477b-b45f-7c6f3489f2c9] raised unexpected: OperationalError(1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
  File "/app/roll/tasks.py", line 37, in cleanup_duplicate_roll_room_bets
    latest_room = RollRoom.objects.filter(enable=True).order_by('-create_time').first()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 674, in first
    for obj in (self if self.ordered else self.order_by('pk'))[:1]:
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 280, in __iter__
    self._fetch_all()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 1324, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 51, in __iter__
    results = compiler.execute_sql(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/compiler.py", line 1175, in execute_sql
    cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.OperationalError: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
[2025-07-18 13:49:37,437: INFO/MainProcess] Task thworker.update_case_records_cache[a0a280f5-a6e1-471c-807a-75c17e5d6f54] received
[2025-07-18 13:49:37,439: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 13:49:37,444: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 13:49:37,444: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 13:49:37,451: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[a0a280f5-a6e1-471c-807a-75c17e5d6f54] succeeded in 0.012659488013014197s: 'Success: Updated 100 case records cache'
[2025-07-18 13:49:37,639: INFO/MainProcess] Task thworker.check_b2c_trade_state[f10c7890-6f4b-4ed1-85f0-2353c03eaacf] received
[2025-07-18 13:49:37,643: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 13:49:37,644: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 13:49:37,645: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 13:49:37,648: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[f10c7890-6f4b-4ed1-85f0-2353c03eaacf] succeeded in 0.005535582982702181s: 'Success: B2C trade state checked'
[2025-07-18 13:54:37,274: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[b8b93441-911b-40e5-855c-92a9024d2883] received
[2025-07-18 13:54:37,275: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 13:54:37,292: ERROR/ForkPoolWorker-2] 清理 RollRoomBet 记录时发生错误: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
[2025-07-18 13:54:37,298: ERROR/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[b8b93441-911b-40e5-855c-92a9024d2883] raised unexpected: OperationalError(1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
  File "/app/roll/tasks.py", line 37, in cleanup_duplicate_roll_room_bets
    latest_room = RollRoom.objects.filter(enable=True).order_by('-create_time').first()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 674, in first
    for obj in (self if self.ordered else self.order_by('pk'))[:1]:
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 280, in __iter__
    self._fetch_all()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 1324, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 51, in __iter__
    results = compiler.execute_sql(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/compiler.py", line 1175, in execute_sql
    cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.OperationalError: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
[2025-07-18 13:54:37,436: INFO/MainProcess] Task thworker.update_case_records_cache[84f15f0f-2e52-4724-854d-3900a80887be] received
[2025-07-18 13:54:37,437: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 13:54:37,441: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 13:54:37,441: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 13:54:37,444: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[84f15f0f-2e52-4724-854d-3900a80887be] succeeded in 0.007470104988897219s: 'Success: Updated 100 case records cache'
[2025-07-18 13:54:37,645: INFO/MainProcess] Task thworker.check_b2c_trade_state[cb6faf22-8c4d-4e3b-9dba-b9f4a3c45039] received
[2025-07-18 13:54:37,646: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 13:54:37,647: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 13:54:37,648: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 13:54:37,649: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[cb6faf22-8c4d-4e3b-9dba-b9f4a3c45039] succeeded in 0.0033942539885174483s: 'Success: B2C trade state checked'
[2025-07-18 13:59:37,333: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[2c2565d3-d087-4ec4-a275-724478d3e4b3] received
[2025-07-18 13:59:37,335: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 13:59:37,336: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 13:59:37,338: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[2c2565d3-d087-4ec4-a275-724478d3e4b3] succeeded in 0.0030662519857287407s: None
[2025-07-18 13:59:37,440: INFO/MainProcess] Task thworker.update_case_records_cache[5d7ad0d8-d547-4ca1-9ed0-d09f4684cd83] received
[2025-07-18 13:59:37,442: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 13:59:37,444: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 13:59:37,444: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 13:59:37,445: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[5d7ad0d8-d547-4ca1-9ed0-d09f4684cd83] succeeded in 0.0037429950025398284s: 'Success: Updated 100 case records cache'
[2025-07-18 13:59:37,643: INFO/MainProcess] Task thworker.check_b2c_trade_state[8d800ed0-c1fd-49d7-8f6c-5767881a8078] received
[2025-07-18 13:59:37,645: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 13:59:37,646: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 13:59:37,652: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 13:59:37,654: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[8d800ed0-c1fd-49d7-8f6c-5767881a8078] succeeded in 0.010021408990724012s: 'Success: B2C trade state checked'
[2025-07-18 14:00:00,015: INFO/MainProcess] Task thworker.hourly_lottery[4d8da24d-7f8b-48f2-983a-8c6818301bb9] received
[2025-07-18 14:00:00,019: INFO/ForkPoolWorker-2] ------------------------ 整点抽奖 ------------------------
[2025-07-18 14:00:00,019: INFO/ForkPoolWorker-2] [Celery] 每小时抽奖任务完成
[2025-07-18 14:00:00,023: INFO/ForkPoolWorker-2] Task thworker.hourly_lottery[4d8da24d-7f8b-48f2-983a-8c6818301bb9] succeeded in 0.004579838976496831s: 'Success: Hourly lottery completed'
[2025-07-18 14:04:37,336: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[0f12a972-b4bc-46df-995d-e9d67419a50e] received
[2025-07-18 14:04:37,337: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 14:04:37,358: ERROR/ForkPoolWorker-2] 清理 RollRoomBet 记录时发生错误: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
[2025-07-18 14:04:37,369: ERROR/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[0f12a972-b4bc-46df-995d-e9d67419a50e] raised unexpected: OperationalError(1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
  File "/app/roll/tasks.py", line 37, in cleanup_duplicate_roll_room_bets
    latest_room = RollRoom.objects.filter(enable=True).order_by('-create_time').first()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 674, in first
    for obj in (self if self.ordered else self.order_by('pk'))[:1]:
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 280, in __iter__
    self._fetch_all()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 1324, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 51, in __iter__
    results = compiler.execute_sql(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/compiler.py", line 1175, in execute_sql
    cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.OperationalError: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
[2025-07-18 14:04:37,448: INFO/MainProcess] Task thworker.update_case_records_cache[33f0519f-c66e-4065-95e4-a2247b378645] received
[2025-07-18 14:04:37,454: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 14:04:37,455: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 14:04:37,455: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 14:04:37,457: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[33f0519f-c66e-4065-95e4-a2247b378645] succeeded in 0.0025222850090358406s: 'Success: Updated 100 case records cache'
[2025-07-18 14:04:37,648: INFO/MainProcess] Task thworker.check_b2c_trade_state[5d10efab-4e17-4ddc-b035-cf4fd11a98fd] received
[2025-07-18 14:04:37,656: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 14:04:37,657: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 14:04:37,658: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 14:04:37,659: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[5d10efab-4e17-4ddc-b035-cf4fd11a98fd] succeeded in 0.009676127985585481s: 'Success: B2C trade state checked'
[2025-07-18 14:09:37,371: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[0aa0b478-2cbb-45f0-bc2d-1dbf964642b1] received
[2025-07-18 14:09:37,372: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 14:09:37,394: ERROR/ForkPoolWorker-2] 清理 RollRoomBet 记录时发生错误: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
[2025-07-18 14:09:37,405: ERROR/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[0aa0b478-2cbb-45f0-bc2d-1dbf964642b1] raised unexpected: OperationalError(1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
  File "/app/roll/tasks.py", line 37, in cleanup_duplicate_roll_room_bets
    latest_room = RollRoom.objects.filter(enable=True).order_by('-create_time').first()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 674, in first
    for obj in (self if self.ordered else self.order_by('pk'))[:1]:
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 280, in __iter__
    self._fetch_all()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 1324, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 51, in __iter__
    results = compiler.execute_sql(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/compiler.py", line 1175, in execute_sql
    cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.OperationalError: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
[2025-07-18 14:09:37,441: INFO/MainProcess] Task thworker.update_case_records_cache[dd84ab0f-6082-4ed2-b5e5-1b316f9a0e4b] received
[2025-07-18 14:09:37,442: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 14:09:37,447: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 14:09:37,447: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 14:09:37,453: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[dd84ab0f-6082-4ed2-b5e5-1b316f9a0e4b] succeeded in 0.010524450015509501s: 'Success: Updated 100 case records cache'
[2025-07-18 14:09:37,657: INFO/MainProcess] Task thworker.check_b2c_trade_state[01269204-6ad7-4052-8c0a-aacef4c471fa] received
[2025-07-18 14:09:37,659: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 14:09:37,659: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 14:09:37,661: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 14:09:37,663: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[01269204-6ad7-4052-8c0a-aacef4c471fa] succeeded in 0.004817430017283186s: 'Success: B2C trade state checked'
[2025-07-18 14:14:37,453: INFO/MainProcess] Task thworker.update_case_records_cache[a5dfb1c4-dd64-413d-8289-7751daaa2b66] received
[2025-07-18 14:14:37,461: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 14:14:37,462: INFO/MainProcess] Task thworker.update_case_records_cache[649eaa2f-9929-421e-b783-814ee7aef261] received
[2025-07-18 14:14:37,463: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 14:14:49,570: ERROR/ForkPoolWorker-2] 更新 CaseRecord 缓存时发生错误: (1054, "Unknown column 'box_case.name_zh' in 'field list'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 173, in __get__
    rel_obj = self.field.get_cached_value(instance)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/mixins.py", line 15, in get_cached_value
    return instance._state.fields_cache[cache_name]
KeyError: 'case'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'box_case.name_zh' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/app/box/tasks.py", line 58, in update_case_records_cache
    batch_data = CaseRecordSerializer(batch_records, many=True).data
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 795, in data
    ret = super().data
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 249, in data
    self._data = self.to_representation(self.instance)
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 713, in to_representation
    return [
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 714, in <listcomp>
    self.child.to_representation(item) for item in iterable
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 538, in to_representation
    ret[field.field_name] = field.to_representation(attribute)
  File "/usr/local/lib/python3.8/site-packages/rest_framework/fields.py", line 1872, in to_representation
    return method(value)
  File "/app/box/serializers.py", line 117, in get_case_info
    return CaseSerializer(obj.case, fields=('id', 'case_key', 'name', 'name_en', 'name_zh_hans', 'cover', 'price', 'open_count')).data
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 187, in __get__
    rel_obj = self.get_object(instance)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 154, in get_object
    return qs.get(self.field.get_reverse_related_filter(instance))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 431, in get
    num = len(clone)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 262, in __len__
    self._fetch_all()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 1324, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 51, in __iter__
    results = compiler.execute_sql(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/compiler.py", line 1175, in execute_sql
    cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.OperationalError: (1054, "Unknown column 'box_case.name_zh' in 'field list'")
[2025-07-18 14:14:49,571: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 14:14:49,572: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[a5dfb1c4-dd64-413d-8289-7751daaa2b66] succeeded in 12.111890275991755s: 'Success: Updated 100 case records cache'
[2025-07-18 14:14:49,620: ERROR/ForkPoolWorker-1] 更新 CaseRecord 缓存时发生错误: (1054, "Unknown column 'box_case.name_zh' in 'field list'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 173, in __get__
    rel_obj = self.field.get_cached_value(instance)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/mixins.py", line 15, in get_cached_value
    return instance._state.fields_cache[cache_name]
KeyError: 'case'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'box_case.name_zh' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/app/box/tasks.py", line 58, in update_case_records_cache
    batch_data = CaseRecordSerializer(batch_records, many=True).data
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 795, in data
    ret = super().data
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 249, in data
    self._data = self.to_representation(self.instance)
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 713, in to_representation
    return [
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 714, in <listcomp>
    self.child.to_representation(item) for item in iterable
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 538, in to_representation
    ret[field.field_name] = field.to_representation(attribute)
  File "/usr/local/lib/python3.8/site-packages/rest_framework/fields.py", line 1872, in to_representation
    return method(value)
  File "/app/box/serializers.py", line 117, in get_case_info
    return CaseSerializer(obj.case, fields=('id', 'case_key', 'name', 'name_en', 'name_zh_hans', 'cover', 'price', 'open_count')).data
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 187, in __get__
    rel_obj = self.get_object(instance)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 154, in get_object
    return qs.get(self.field.get_reverse_related_filter(instance))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 431, in get
    num = len(clone)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 262, in __len__
    self._fetch_all()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 1324, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 51, in __iter__
    results = compiler.execute_sql(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/compiler.py", line 1175, in execute_sql
    cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.OperationalError: (1054, "Unknown column 'box_case.name_zh' in 'field list'")
[2025-07-18 14:14:49,625: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 14:14:49,627: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[649eaa2f-9929-421e-b783-814ee7aef261] succeeded in 12.164627233985811s: 'Success: Updated 100 case records cache'
[2025-07-18 14:19:37,477: INFO/MainProcess] Task thworker.update_case_records_cache[072ff1b5-43a8-48f8-a9b0-99ff502d2cf0] received
[2025-07-18 14:19:37,482: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 14:19:37,503: INFO/MainProcess] Task thworker.update_case_records_cache[033ff6cc-c342-4358-91af-002128a23497] received
[2025-07-18 14:19:37,504: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 14:19:37,505: INFO/ForkPoolWorker-1] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 14:19:37,505: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 14:19:37,506: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[033ff6cc-c342-4358-91af-002128a23497] succeeded in 0.0025463429919909686s: 'Success: Updated 100 case records cache'
[2025-07-18 14:19:37,656: INFO/MainProcess] Task thworker.check_b2c_trade_state[1a03465e-c744-419b-973d-7f314ac4d3aa] received
[2025-07-18 14:19:37,657: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 14:19:37,658: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 14:19:37,662: INFO/ForkPoolWorker-1] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 14:19:37,663: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[1a03465e-c744-419b-973d-7f314ac4d3aa] succeeded in 0.006739518023096025s: 'Success: B2C trade state checked'
[2025-07-18 14:19:49,420: ERROR/ForkPoolWorker-2] 更新 CaseRecord 缓存时发生错误: (1054, "Unknown column 'box_case.name_zh' in 'field list'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 173, in __get__
    rel_obj = self.field.get_cached_value(instance)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/mixins.py", line 15, in get_cached_value
    return instance._state.fields_cache[cache_name]
KeyError: 'case'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'box_case.name_zh' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/app/box/tasks.py", line 58, in update_case_records_cache
    batch_data = CaseRecordSerializer(batch_records, many=True).data
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 795, in data
    ret = super().data
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 249, in data
    self._data = self.to_representation(self.instance)
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 713, in to_representation
    return [
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 714, in <listcomp>
    self.child.to_representation(item) for item in iterable
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 538, in to_representation
    ret[field.field_name] = field.to_representation(attribute)
  File "/usr/local/lib/python3.8/site-packages/rest_framework/fields.py", line 1872, in to_representation
    return method(value)
  File "/app/box/serializers.py", line 117, in get_case_info
    return CaseSerializer(obj.case, fields=('id', 'case_key', 'name', 'name_en', 'name_zh_hans', 'cover', 'price', 'open_count')).data
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 187, in __get__
    rel_obj = self.get_object(instance)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 154, in get_object
    return qs.get(self.field.get_reverse_related_filter(instance))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 431, in get
    num = len(clone)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 262, in __len__
    self._fetch_all()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 1324, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 51, in __iter__
    results = compiler.execute_sql(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/compiler.py", line 1175, in execute_sql
    cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.OperationalError: (1054, "Unknown column 'box_case.name_zh' in 'field list'")
[2025-07-18 14:19:49,421: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 14:19:49,423: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[072ff1b5-43a8-48f8-a9b0-99ff502d2cf0] succeeded in 11.941192026017234s: 'Success: Updated 100 case records cache'
[2025-07-18 14:24:37,615: INFO/MainProcess] Task thworker.update_case_records_cache[b04c1a7d-3b22-4492-8c8e-5b2b63a32163] received
[2025-07-18 14:24:37,627: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 14:24:37,658: INFO/MainProcess] Task thworker.update_case_records_cache[cc83e199-0b91-4d3d-bed6-9d66dd9fb2f4] received
[2025-07-18 14:24:37,664: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 14:24:37,676: INFO/ForkPoolWorker-1] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 14:24:37,676: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 14:24:37,686: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[cc83e199-0b91-4d3d-bed6-9d66dd9fb2f4] succeeded in 0.02187767199939117s: 'Success: Updated 100 case records cache'
[2025-07-18 14:24:49,660: ERROR/ForkPoolWorker-2] 更新 CaseRecord 缓存时发生错误: (1054, "Unknown column 'box_case.name_zh' in 'field list'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 173, in __get__
    rel_obj = self.field.get_cached_value(instance)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/mixins.py", line 15, in get_cached_value
    return instance._state.fields_cache[cache_name]
KeyError: 'case'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'box_case.name_zh' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/app/box/tasks.py", line 58, in update_case_records_cache
    batch_data = CaseRecordSerializer(batch_records, many=True).data
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 795, in data
    ret = super().data
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 249, in data
    self._data = self.to_representation(self.instance)
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 713, in to_representation
    return [
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 714, in <listcomp>
    self.child.to_representation(item) for item in iterable
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 538, in to_representation
    ret[field.field_name] = field.to_representation(attribute)
  File "/usr/local/lib/python3.8/site-packages/rest_framework/fields.py", line 1872, in to_representation
    return method(value)
  File "/app/box/serializers.py", line 117, in get_case_info
    return CaseSerializer(obj.case, fields=('id', 'case_key', 'name', 'name_en', 'name_zh_hans', 'cover', 'price', 'open_count')).data
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 187, in __get__
    rel_obj = self.get_object(instance)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 154, in get_object
    return qs.get(self.field.get_reverse_related_filter(instance))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 431, in get
    num = len(clone)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 262, in __len__
    self._fetch_all()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 1324, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 51, in __iter__
    results = compiler.execute_sql(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/compiler.py", line 1175, in execute_sql
    cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.OperationalError: (1054, "Unknown column 'box_case.name_zh' in 'field list'")
[2025-07-18 14:24:49,660: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 14:24:49,691: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[b04c1a7d-3b22-4492-8c8e-5b2b63a32163] succeeded in 12.074919078993844s: 'Success: Updated 100 case records cache'
[2025-07-18 14:29:37,639: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[124a4c47-50e4-4d5b-98e4-2963a8cda8f9] received
[2025-07-18 14:29:37,641: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 14:29:37,657: ERROR/ForkPoolWorker-2] 清理 RollRoomBet 记录时发生错误: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
[2025-07-18 14:29:37,678: ERROR/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[124a4c47-50e4-4d5b-98e4-2963a8cda8f9] raised unexpected: OperationalError(1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
  File "/app/roll/tasks.py", line 37, in cleanup_duplicate_roll_room_bets
    latest_room = RollRoom.objects.filter(enable=True).order_by('-create_time').first()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 674, in first
    for obj in (self if self.ordered else self.order_by('pk'))[:1]:
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 280, in __iter__
    self._fetch_all()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 1324, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 51, in __iter__
    results = compiler.execute_sql(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/compiler.py", line 1175, in execute_sql
    cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.OperationalError: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
[2025-07-18 14:29:37,688: INFO/MainProcess] Task thworker.check_b2c_trade_state[12d3f680-fe97-4e4d-bfb7-2328ed9f682c] received
[2025-07-18 14:29:37,691: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 14:29:37,692: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 14:29:37,694: INFO/ForkPoolWorker-1] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 14:29:37,695: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[12d3f680-fe97-4e4d-bfb7-2328ed9f682c] succeeded in 0.005728280026232824s: 'Success: B2C trade state checked'
[2025-07-18 14:29:37,706: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[b2a60d5d-34ab-4650-943e-2cb6aac477d6] received
[2025-07-18 14:29:37,707: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 14:29:37,708: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 14:29:37,710: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[b2a60d5d-34ab-4650-943e-2cb6aac477d6] succeeded in 0.0026111930201295763s: None
[2025-07-18 14:29:37,732: INFO/MainProcess] Task thworker.check_b2c_trade_state[42e61199-48a0-4550-9b8c-00fce18a2570] received
[2025-07-18 14:29:37,737: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 14:29:37,737: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 14:29:37,741: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 14:29:37,741: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[42e61199-48a0-4550-9b8c-00fce18a2570] succeeded in 0.008415126998443156s: 'Success: B2C trade state checked'
[2025-07-18 14:34:37,768: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[95526659-06c3-4309-b060-e036d4e2be3a] received
[2025-07-18 14:34:37,769: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 14:34:37,783: ERROR/ForkPoolWorker-2] 清理 RollRoomBet 记录时发生错误: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
[2025-07-18 14:34:37,786: INFO/MainProcess] Task thworker.check_b2c_trade_state[4fe920c0-ba1c-4f0c-be24-ba06a96b55c6] received
[2025-07-18 14:34:37,789: ERROR/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[95526659-06c3-4309-b060-e036d4e2be3a] raised unexpected: OperationalError(1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
  File "/app/roll/tasks.py", line 37, in cleanup_duplicate_roll_room_bets
    latest_room = RollRoom.objects.filter(enable=True).order_by('-create_time').first()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 674, in first
    for obj in (self if self.ordered else self.order_by('pk'))[:1]:
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 280, in __iter__
    self._fetch_all()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 1324, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 51, in __iter__
    results = compiler.execute_sql(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/compiler.py", line 1175, in execute_sql
    cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.OperationalError: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
[2025-07-18 14:34:37,794: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 14:34:37,794: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 14:34:37,816: INFO/MainProcess] Task thworker.update_case_records_cache[219971b4-4494-478d-bb66-bae16541af29] received
[2025-07-18 14:34:37,816: INFO/ForkPoolWorker-1] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 14:34:37,817: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 14:34:37,818: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 14:34:37,818: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 14:34:37,819: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[219971b4-4494-478d-bb66-bae16541af29] succeeded in 0.0029334900027606636s: 'Success: Updated 100 case records cache'
[2025-07-18 14:34:37,824: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[4fe920c0-ba1c-4f0c-be24-ba06a96b55c6] succeeded in 0.03713348301243968s: 'Success: B2C trade state checked'
