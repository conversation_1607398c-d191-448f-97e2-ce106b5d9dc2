# Debug 调试脚本

本目录包含用于系统调试和问题排查的脚本工具。

## 脚本列表

### 用户和房间调试

#### debug_user_rooms.py
- **功能**：调试用户房间相关问题
- **适用场景**：
  - 用户无法进入房间
  - 房间状态显示异常
  - 用户数据不一致问题
- **运行方式**：
  ```bash
  cd /www/wwwroot/csgoskins.com.cn/server
  python ../scripts/debug/debug_user_rooms.py --user-id 12345
  ```
- **输出信息**：
  - 用户基本信息
  - 用户当前房间状态
  - 房间详细信息
  - 相关错误日志

## 调试工具功能

### 1. 数据检查
- 检查数据完整性
- 验证数据关联关系
- 查找数据异常

### 2. 状态分析
- 分析系统状态
- 检查服务运行状态
- 监控资源使用情况

### 3. 日志分析
- 解析错误日志
- 查找异常模式
- 生成分析报告

### 4. 性能分析
- 分析性能瓶颈
- 监控响应时间
- 查找慢查询

## 使用指南

### 基本调试流程

1. **问题确认**
   ```bash
   # 查看系统整体状态
   python ../scripts/debug/debug_user_rooms.py --system-status
   ```

2. **用户问题调试**
   ```bash
   # 调试特定用户问题
   python ../scripts/debug/debug_user_rooms.py --user-id 12345 --verbose
   ```

3. **房间问题调试**
   ```bash
   # 调试特定房间问题
   python ../scripts/debug/debug_user_rooms.py --room-id 67890 --detailed
   ```

4. **批量问题排查**
   ```bash
   # 批量检查问题用户
   python ../scripts/debug/debug_user_rooms.py --check-all --since "2025-06-28"
   ```

### 调试参数说明

#### 通用参数
- `--verbose, -v`：详细输出模式
- `--quiet, -q`：静默模式，只输出错误
- `--output FILE`：输出结果到文件
- `--format FORMAT`：输出格式（text/json/csv）

#### 用户相关参数
- `--user-id ID`：指定用户ID
- `--username NAME`：指定用户名
- `--user-list FILE`：从文件读取用户列表

#### 房间相关参数
- `--room-id ID`：指定房间ID
- `--room-status STATUS`：按房间状态过滤
- `--room-type TYPE`：按房间类型过滤

#### 时间范围参数
- `--since DATE`：起始时间
- `--until DATE`：结束时间
- `--last-hours N`：最近N小时

### 调试输出示例

```
========================================
用户房间调试报告
========================================
调试时间: 2025-06-28 14:30:00
目标用户: ID=12345, username=test_user

用户基本信息:
- 用户ID: 12345
- 用户名: test_user
- 注册时间: 2025-01-15 10:30:00
- 最后登录: 2025-06-28 14:25:00
- 账户状态: 正常

当前房间状态:
- 当前房间: ID=67890
- 房间状态: 等待中
- 进入时间: 2025-06-28 14:20:00
- 房间类型: 2v2对战

房间详细信息:
- 房间创建时间: 2025-06-28 14:18:00
- 当前人数: 3/4
- 房间创建者: user_abc
- 游戏状态: 等待玩家加入

潜在问题:
⚠️  房间等待时间过长 (10分钟)
⚠️  房间人数接近满员但未开始

建议操作:
1. 检查房间匹配逻辑
2. 确认其他玩家连接状态
3. 考虑重置房间状态

相关日志:
[14:20:15] INFO: User 12345 joined room 67890
[14:25:30] WARN: Room 67890 waiting timeout warning
[14:28:45] ERROR: Failed to start room 67890 - insufficient players

========================================
```

## 调试脚本开发

### 脚本结构模板

```python
#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
调试脚本：[调试功能描述]

用途：[具体用途说明]
适用场景：[适用的问题场景]

作者：[作者名]
创建时间：[创建日期]
"""

import os
import sys
import django
import logging
import argparse
from datetime import datetime, timedelta

# Django环境设置
sys.path.append('./')
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "steambase.settings")
django.setup()

# 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DebugTool:
    """调试工具基类"""
    
    def __init__(self, args):
        self.args = args
        self.setup_logging()
    
    def setup_logging(self):
        """设置日志级别"""
        if self.args.verbose:
            logging.getLogger().setLevel(logging.DEBUG)
        elif self.args.quiet:
            logging.getLogger().setLevel(logging.ERROR)
    
    def output_result(self, data, title="调试结果"):
        """输出结果"""
        if self.args.format == 'json':
            import json
            print(json.dumps(data, indent=2, ensure_ascii=False))
        elif self.args.format == 'csv':
            self.output_csv(data)
        else:
            self.output_text(data, title)
    
    def output_text(self, data, title):
        """文本格式输出"""
        print("=" * 40)
        print(title)
        print("=" * 40)
        print(f"调试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        # 具体输出逻辑
    
    def output_csv(self, data):
        """CSV格式输出"""
        import csv
        # CSV输出逻辑
        pass

def main():
    parser = argparse.ArgumentParser(description='调试工具')
    
    # 通用参数
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    parser.add_argument('--quiet', '-q', action='store_true', help='静默模式')
    parser.add_argument('--output', help='输出文件')
    parser.add_argument('--format', choices=['text', 'json', 'csv'], 
                        default='text', help='输出格式')
    
    # 具体参数
    parser.add_argument('--user-id', type=int, help='用户ID')
    parser.add_argument('--room-id', type=int, help='房间ID')
    
    args = parser.parse_args()
    
    try:
        debug_tool = DebugTool(args)
        # 执行调试逻辑
        
    except Exception as e:
        logger.error(f"调试失败: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main()
```

### 开发规范

1. **函数设计**
   - 每个调试功能独立函数
   - 支持多种输出格式
   - 包含错误处理

2. **数据收集**
   - 收集相关的所有数据
   - 包含时间戳信息
   - 记录数据来源

3. **分析逻辑**
   - 明确问题判断标准
   - 提供修复建议
   - 标记风险等级

4. **输出格式**
   - 支持人类可读格式
   - 支持机器处理格式
   - 包含完整上下文

## 常见调试场景

### 1. 用户登录问题
```bash
python debug_user_rooms.py --user-id 12345 --check-login
```

### 2. 房间匹配问题
```bash
python debug_user_rooms.py --room-status waiting --check-matching
```

### 3. 性能问题排查
```bash
python debug_user_rooms.py --performance-check --last-hours 1
```

### 4. 数据一致性检查
```bash
python debug_user_rooms.py --consistency-check --detailed
```

## 高级调试技巧

### 1. 数据库查询分析
```python
from django.db import connection

def analyze_queries():
    """分析数据库查询"""
    queries = connection.queries
    for query in queries:
        print(f"SQL: {query['sql']}")
        print(f"Time: {query['time']}")
```

### 2. 缓存状态检查
```python
from django.core.cache import cache

def check_cache_status():
    """检查缓存状态"""
    # 检查缓存键
    # 分析缓存命中率
    # 查找缓存异常
```

### 3. 实时监控
```python
import time

def real_time_monitor():
    """实时监控"""
    while True:
        # 收集实时数据
        # 分析状态变化
        time.sleep(1)
```

## 故障排查流程

### 1. 问题定位
- 收集错误现象
- 确定影响范围
- 分析错误时间

### 2. 数据分析
- 检查相关数据
- 分析数据变化
- 查找异常模式

### 3. 原因分析
- 分析可能原因
- 验证假设
- 确定根本原因

### 4. 解决方案
- 制定修复方案
- 评估影响范围
- 实施修复措施

### 5. 验证和监控
- 验证修复效果
- 持续监控状态
- 更新预防措施

## 注意事项

1. **数据安全**
   - 不在日志中输出敏感信息
   - 保护用户隐私数据
   - 遵守数据保护规定

2. **系统影响**
   - 避免影响正常业务
   - 控制资源使用
   - 在低峰期执行

3. **权限控制**
   - 确认操作权限
   - 记录操作日志
   - 限制访问范围

最后更新：2025-06-28
