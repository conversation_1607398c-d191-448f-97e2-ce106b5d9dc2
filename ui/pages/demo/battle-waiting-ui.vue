<template>
  <div class="demo-page">
    <div class="demo-header">
      <h1 class="demo-title">🎨 对战等待状态UI演示</h1>
      <p class="demo-description">
        展示优化后的BattlePlayerDisplay组件等待状态UI，包含现代化设计、动态效果和CSGO主题风格
      </p>
    </div>

    <div class="demo-controls">
      <div class="control-group">
        <label class="control-label">玩家数量:</label>
        <div class="control-buttons">
          <button 
            v-for="count in [1, 2, 3, 4]" 
            :key="count"
            @click="playerCount = count"
            :class="['control-btn', { active: playerCount === count }]"
          >
            {{ count }}
          </button>
        </div>
      </div>

      <div class="control-group">
        <label class="control-label">最大玩家数:</label>
        <div class="control-buttons">
          <button 
            v-for="count in [2, 3, 4]" 
            :key="count"
            @click="maxPlayers = count"
            :class="['control-btn', { active: maxPlayers === count }]"
          >
            {{ count }}
          </button>
        </div>
      </div>

      <div class="control-group">
        <label class="control-label">语言:</label>
        <div class="control-buttons">
          <button 
            @click="locale = 'zh-hans'"
            :class="['control-btn', { active: locale === 'zh-hans' }]"
          >
            中文
          </button>
          <button 
            @click="locale = 'en'"
            :class="['control-btn', { active: locale === 'en' }]"
          >
            English
          </button>
        </div>
      </div>
    </div>

    <div class="demo-content">
      <!-- 模拟对战数据 -->
      <BattlePlayerDisplay
        :players="mockPlayers"
        :current-round="1"
        :total-rounds="3"
        :max-players="maxPlayers"
        :is-battle-started="false"
        :is-battle-finished="false"
        :current-case-detail="mockCaseDetail"
        :current-case-items="mockCaseItems"
        :is-user-creator="false"
        :is-user-joined="false"
        :current-user-id="undefined"
        @join-battle="handleJoinBattle"
        @start-battle="handleStartBattle"
        @leave-battle="handleLeaveBattle"
        @dismiss-battle="handleDismissBattle"
        @animation-complete="handleAnimationComplete"
      />
    </div>

    <div class="demo-info">
      <h3 class="info-title">🎨 设计特性</h3>
      <div class="info-grid">
        <div class="info-item">
          <Icon name="heroicons:sparkles" class="w-6 h-6 text-blue-400" />
          <h4>现代化设计</h4>
          <p>毛玻璃背景、渐变装饰、圆角设计</p>
        </div>
        <div class="info-item">
          <Icon name="heroicons:bolt" class="w-6 h-6 text-yellow-400" />
          <h4>动态效果</h4>
          <p>浮动粒子、脉冲光环、进度条动画</p>
        </div>
        <div class="info-item">
          <Icon name="heroicons:device-phone-mobile" class="w-6 h-6 text-green-400" />
          <h4>响应式布局</h4>
          <p>完美适配桌面端和移动端</p>
        </div>
        <div class="info-item">
          <Icon name="heroicons:globe-alt" class="w-6 h-6 text-purple-400" />
          <h4>国际化支持</h4>
          <p>完整的中英文多语言支持</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 🎯 国际化设置
const { locale } = useI18n();

// 🎯 演示状态
const playerCount = ref(1);
const maxPlayers = ref(4);

// 🎯 模拟对战数据
const mockPlayers = computed(() => {
  const players = [];
  for (let i = 0; i < playerCount.value; i++) {
    players.push({
      uid: `player-${i + 1}`,
      user: {
        uid: `user-${i + 1}`,
        profile: {
          nickname: `玩家${i + 1}`,
          avatar: `/demo/avatar${i + 1}.png`
        }
      },
      open_items: [],
      win_items: [],
      victory: false,
      win_amount: 0,
      open_amount: 0,
      win_items_count: 0
    });
  }
  return players;
});

const mockCaseDetail = ref({
  id: 'demo-case-1',
  key: 'demo-case-1',
  name: '演示箱子',
  name_zh_hans: '演示箱子',
  name_en: 'Demo Case',
  cover: '/demo/case1.png',
  price: 10.00
});

const mockCaseItems = ref([
  {
    id: 'item-1',
    name: 'AK-47 | 血腥运动',
    name_zh_hans: 'AK-47 | 血腥运动',
    name_en: 'AK-47 | Bloodsport',
    image: '/demo/item1.png',
    price: 25.50,
    rarity_color: '#D32CE6'
  },
  {
    id: 'item-2',
    name: 'M4A4 | 龙王',
    name_zh_hans: 'M4A4 | 龙王',
    name_en: 'M4A4 | Dragon King',
    image: '/demo/item2.png',
    price: 15.20,
    rarity_color: '#8847FF'
  }
]);

// 🎯 事件处理器
const handleJoinBattle = () => {
  console.log('[🎨DEMO] 加入对战');
  if (playerCount.value < maxPlayers.value) {
    playerCount.value++;
  }
};

const handleStartBattle = () => {
  console.log('[🎨DEMO] 开始对战');
};

const handleLeaveBattle = () => {
  console.log('[🎨DEMO] 离开对战');
  if (playerCount.value > 0) {
    playerCount.value--;
  }
};

const handleDismissBattle = () => {
  console.log('[🎨DEMO] 解散对战');
  playerCount.value = 0;
};

const handleAnimationComplete = (playerIndex: number, result: any) => {
  console.log('[🎨DEMO] 动画完成:', { playerIndex, result });
};

// 🎯 SEO元数据
useSeoMeta({
  title: '对战等待状态UI演示 - CSGO开箱网站',
  description: '展示优化后的BattlePlayerDisplay组件等待状态UI，包含现代化设计、动态效果和CSGO主题风格',
  keywords: '对战,等待状态,UI设计,动态效果,CSGO',
});
</script>

<style lang="scss" scoped>
.demo-page {
  min-height: 100vh;
  background: var(--gradient-dark, linear-gradient(135deg, #12171c 0%, #0a0c0f 100%));
  padding: 2rem;
}

.demo-header {
  text-align: center;
  margin-bottom: 3rem;
  
  .demo-title {
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--color-primary, #00a8ff), var(--color-secondary, #ff4d00));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
  }
  
  .demo-description {
    color: var(--color-text-secondary, rgba(255, 255, 255, 0.8));
    font-size: 1.125rem;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
  }
}

.demo-controls {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
  
  .control-group {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    
    .control-label {
      color: var(--color-text-secondary, rgba(255, 255, 255, 0.8));
      font-size: 0.875rem;
      font-weight: 600;
    }
    
    .control-buttons {
      display: flex;
      gap: 0.5rem;
      
      .control-btn {
        padding: 0.5rem 1rem;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: var(--radius-md, 0.5rem);
        color: var(--color-text-secondary, rgba(255, 255, 255, 0.8));
        font-size: 0.875rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        
        &:hover {
          background: rgba(255, 255, 255, 0.1);
          border-color: rgba(255, 255, 255, 0.2);
        }
        
        &.active {
          background: var(--color-primary, #00a8ff);
          border-color: var(--color-primary, #00a8ff);
          color: white;
          box-shadow: 0 0 10px rgba(0, 168, 255, 0.3);
        }
      }
    }
  }
}

.demo-content {
  max-width: 1200px;
  margin: 0 auto;
  margin-bottom: 3rem;
}

.demo-info {
  max-width: 1000px;
  margin: 0 auto;
  
  .info-title {
    text-align: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--color-text, #ffffff);
    margin-bottom: 2rem;
  }
  
  .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    
    .info-item {
      text-align: center;
      padding: 1.5rem;
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: var(--radius-lg, 0.75rem);
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;
      
      &:hover {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.2);
        transform: translateY(-2px);
      }
      
      h4 {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--color-text, #ffffff);
        margin: 0.75rem 0;
      }
      
      p {
        color: var(--color-text-secondary, rgba(255, 255, 255, 0.8));
        font-size: 0.875rem;
        line-height: 1.5;
      }
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .demo-page {
    padding: 1rem;
  }
  
  .demo-header .demo-title {
    font-size: 2rem;
  }
  
  .demo-controls {
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
}
</style> 