// 对战状态枚举 - 根据后端提供的GameState
export enum GameState {
  Initial = 1,      // 初始状态
  Joinable = 2,     // 可加入
  Joining = 3,      // 加入中  
  Full = 4,         // 已满员
  Running = 5,      // 运行中
  End = 11,         // 已结束
  Cancelled = 20    // 已取消
}

// 前端状态枚举 - 与后端GameState对应
export enum BattleStatus {
  WAITING = 2,        // 等待中 (对应Joinable)
  IN_PROGRESS = 5,    // 进行中 (对应Running)
  COMPLETED = 11,     // 已完成 (对应End)
  CANCELLED = 20      // 已取消 (对应Cancelled)
}

// 饰品价格信息
export interface ItemPrice {
  price: number
  update_time: string
}

// 饰品分类信息
export interface ItemCategory {
  cate_id: number
  cate_name: string
  cate_name_en: string
  cate_name_zh_hans: string
  icon: string
}

// 饰品品质信息
export interface ItemQuality {
  quality_id: number
  quality_name: string
  quality_name_en: string
  quality_name_zh_hans: string
  quality_color: string
}

// 饰品稀有度信息
export interface ItemRarity {
  rarity_id: number
  rarity_name: string
  rarity_name_en: string
  rarity_name_zh_hans: string
  rarity_color: string
}

// 饰品外观信息
export interface ItemExterior {
  exterior_id: number
  exterior_name: string
  exterior_name_en: string
  exterior_name_zh_hans: string
  exterior_color: string
}

// 饰品详细信息
export interface BattleItem {
  uid: string
  item_id: number
  name: string
  name_en: string
  name_zh_hans: string
  image: string
  item_price: ItemPrice
  item_category: ItemCategory
  item_quality: ItemQuality
  item_rarity: ItemRarity
  item_exterior: ItemExterior
}

// 对战箱子信息
export interface BattleCase {
  id: string
  key: string
  name: string
  name_en: string
  name_zh_hans: string
  price: number
  cover: string
  count: number
}

// 对战轮次信息
export interface BattleRound {
  case: {
    case_key: string
    name: string
    name_en: string
    name_zh_hans: string
    cover: string
    item: string
    price: number
  }
}

// 用户信息
export interface BattleUser {
  uid: string
  profile: {
    nickname: string
    avatar: string
  }
}

// 对战参与者信息
export interface BattleBet {
  uid: string
  user: BattleUser
  victory: boolean | null
  open_amount: number
  win_amount: number
  win_items_count: number
  open_items: BattleItem[]
  win_items: BattleItem[]
}

// 对战房间详情 - 根据API文档
export interface BattleDetail {
  uid: string
  short_id: string
  create_time: string
  update_time: string
  max_joiner: number
  price: number
  state: number  // 使用数字状态，与后端保持一致
  type: number
  joiner_count: number
  private: boolean  // 修复：应该是boolean而不是number
  round_count: number
  round_count_current: number
  
  // 🎯 新增API字段 - 详细轮次信息 (2025-01-09)
  round_count_completed?: number  // 已完成的轮次数
  round_count_remaining?: number  // 剩余轮次数
  round_progress_percent?: number // 轮次进度百分比 (0-100)
  is_first_round?: boolean       // 是否是第一轮
  is_last_round?: boolean        // 是否是最后一轮
  round_state?: string           // 轮次状态描述
  
  rounds: BattleRound[]
  bets: BattleBet[]
  user: BattleUser
}

// 对战房间列表项
export interface BattleRoom {
  uid: string
  short_id: string
  user: BattleUser
  max_joiner: number
  price: number
  state: number
  type: number
  rounds: BattleRound[]
  bets: BattleBet[]
  joiner_count: number
  create_time: string
}

// 对战结果
export interface BattleResult {
  winner: BattleUser
  total_value: number
  win_items: BattleItem[]
}

// 可用箱子接口
export interface AvailableCase {
  case_key: string
  name: string
  name_en: string
  name_zh_hans: string
  price: number
  open_count: number
  discount: number
  cover: string
}

// API参数接口
export interface BattleListParams {
  state?: string
  assigner?: string
  page?: number
  pageSize?: number
}

export interface JoinRoomParams {
  uid: string
  team: number
}

export interface CreateBattleParams {
  cases_key: string[]
  max_joiner: number
  private?: number
}

// 对战统计信息
export interface BattleStats {
  total_battles: number
  wins: number
  total_value: number
  win_rate: number
} 