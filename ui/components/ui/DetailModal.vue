<template>
  <Teleport to="body">
    <Transition name="modal-fade" appear>
      <div
        v-if="visible"
        class="fixed inset-0 z-[9999] flex items-center justify-center p-4"
        @click.self="handleClose"
      >
        <!-- 背景遮罩 -->
        <div class="absolute inset-0 bg-black/80 backdrop-blur-sm"></div>
        
        <!-- 模态框内容 -->
        <div class="relative w-full max-w-4xl transform transition-all duration-300">
          <div class="relative bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur-xl border border-gray-600/30 rounded-2xl shadow-2xl overflow-hidden">
            <!-- 装饰性背景 -->
            <div class="absolute inset-0 overflow-hidden pointer-events-none">
              <div class="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-blue-500/5 to-purple-500/5"></div>
              <div class="absolute -top-20 -right-20 w-40 h-40 bg-gradient-to-br from-blue-500/10 to-transparent rounded-full blur-xl"></div>
              <div class="absolute -bottom-20 -left-20 w-40 h-40 bg-gradient-to-br from-purple-500/10 to-transparent rounded-full blur-xl"></div>
            </div>

            <!-- 关闭按钮 -->
            <button
              @click="handleClose"
              class="absolute top-4 right-4 w-8 h-8 flex items-center justify-center rounded-full bg-gray-700/50 hover:bg-gray-600/50 text-gray-400 hover:text-white transition-all duration-200 z-20"
            >
              <Icon name="material-symbols:close" class="w-5 h-5" />
            </button>

            <!-- 内容区域 -->
            <div class="relative z-10">
              <!-- 头部区域 -->
              <div class="p-8 pb-6">
                <div class="flex items-center space-x-4 mb-6">
                  <div class="w-16 h-16 rounded-full bg-gradient-to-br from-blue-500/20 to-purple-500/20 flex items-center justify-center text-blue-400 border border-blue-500/30">
                    <Icon :name="icon" class="w-8 h-8" />
                  </div>
                  <div class="flex-1">
                    <h3 class="text-2xl font-bold text-white mb-1">{{ title }}</h3>
                    <p v-if="subtitle" class="text-gray-400 text-sm">{{ subtitle }}</p>
                  </div>
                  <div v-if="badge" class="flex-shrink-0">
                    <span 
                      class="px-3 py-1 rounded-full text-xs font-medium"
                      :class="badgeClasses"
                    >
                      {{ badge }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- 主体内容 -->
              <div class="px-8 pb-8">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  <!-- 左侧：图片/媒体区域 -->
                  <div v-if="image || gallery" class="space-y-4">
                    <!-- 主图片 -->
                    <div v-if="image" class="relative">
                      <img 
                        :src="image" 
                        :alt="imageAlt || title"
                        class="w-full h-64 object-cover rounded-lg shadow-lg border border-gray-600/30"
                        @error="handleImageError"
                      />
                      <div v-if="imageOverlay" class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent rounded-lg"></div>
                      <div v-if="imageOverlay" class="absolute bottom-4 left-4 text-white">
                        <p class="text-sm font-medium">{{ imageOverlay }}</p>
                      </div>
                    </div>
                    
                    <!-- 图片画廊 -->
                    <div v-if="gallery && gallery.length > 0" class="grid grid-cols-4 gap-2">
                      <div 
                        v-for="(img, index) in gallery" 
                        :key="index"
                        class="relative cursor-pointer group"
                        @click="selectedImage = img"
                      >
                        <img 
                          :src="img.src" 
                          :alt="img.alt || `${title} - 图片 ${index + 1}`"
                          class="w-full h-16 object-cover rounded border border-gray-600/30 group-hover:border-blue-500/50 transition-colors"
                          @error="handleGalleryImageError"
                        />
                        <div class="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors rounded"></div>
                      </div>
                    </div>
                  </div>

                  <!-- 右侧：详细信息 -->
                  <div class="space-y-6">
                    <!-- 基本信息 -->
                    <div v-if="basicInfo && Object.keys(basicInfo).length > 0" class="bg-gray-800/50 rounded-lg p-4 border border-gray-600/30">
                      <h4 class="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                        <Icon name="material-symbols:info" class="w-5 h-5 text-blue-400" />
                        基本信息
                      </h4>
                      <div class="space-y-3 text-sm">
                        <div v-for="(value, key) in basicInfo" :key="key" class="flex justify-between">
                          <span class="text-gray-400">{{ key }}:</span>
                          <span class="text-white font-medium">{{ value }}</span>
                        </div>
                      </div>
                    </div>

                    <!-- 详细描述 -->
                    <div v-if="description" class="bg-gray-800/50 rounded-lg p-4 border border-gray-600/30">
                      <h4 class="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                        <Icon name="material-symbols:description" class="w-5 h-5 text-blue-400" />
                        详细描述
                      </h4>
                      <div class="prose prose-invert max-w-none text-sm">
                        <div v-html="description"></div>
                      </div>
                    </div>

                    <!-- 统计数据 -->
                    <div v-if="stats && stats.length > 0" class="bg-gray-800/50 rounded-lg p-4 border border-gray-600/30">
                      <h4 class="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                        <Icon name="material-symbols:analytics" class="w-5 h-5 text-blue-400" />
                        统计数据
                      </h4>
                      <div class="grid grid-cols-2 gap-4">
                        <div 
                          v-for="stat in stats" 
                          :key="stat.label"
                          class="text-center p-3 bg-gray-700/50 rounded-lg border border-gray-600/30"
                        >
                          <div class="text-2xl font-bold text-blue-400 mb-1">{{ stat.value }}</div>
                          <div class="text-xs text-gray-400">{{ stat.label }}</div>
                        </div>
                      </div>
                    </div>

                    <!-- 标签列表 -->
                    <div v-if="tags && tags.length > 0" class="bg-gray-800/50 rounded-lg p-4 border border-gray-600/30">
                      <h4 class="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                        <Icon name="material-symbols:label" class="w-5 h-5 text-blue-400" />
                        标签
                      </h4>
                      <div class="flex flex-wrap gap-2">
                        <span 
                          v-for="tag in tags" 
                          :key="tag"
                          class="px-3 py-1 bg-blue-500/20 text-blue-400 text-xs rounded-full border border-blue-500/30"
                        >
                          {{ tag }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 操作按钮 -->
                <div v-if="actions && actions.length > 0" class="flex flex-wrap gap-3 justify-end mt-8 pt-6 border-t border-gray-600/30">
                  <button
                    v-for="action in actions"
                    :key="action.key"
                    @click="handleAction(action.key)"
                    :disabled="action.disabled || loading"
                    class="px-6 py-3 rounded-lg font-medium text-white transition-all duration-300 flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                    :class="action.class || 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700'"
                  >
                    <Icon v-if="action.loading" name="material-symbols:sync" class="w-4 h-4 animate-spin" />
                    <Icon v-else :name="action.icon" class="w-4 h-4" />
                    {{ action.loading ? action.loadingText || '处理中...' : action.label }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
interface GalleryImage {
  src: string
  alt?: string
}

interface StatItem {
  label: string
  value: string | number
}

interface ActionItem {
  key: string
  label: string
  icon: string
  class?: string
  disabled?: boolean
  loading?: boolean
  loadingText?: string
}

interface DetailModalProps {
  visible: boolean
  title: string
  subtitle?: string
  icon?: string
  badge?: string
  badgeType?: 'success' | 'error' | 'warning' | 'info'
  image?: string
  imageAlt?: string
  imageOverlay?: string
  gallery?: GalleryImage[]
  basicInfo?: Record<string, any>
  description?: string
  stats?: StatItem[]
  tags?: string[]
  actions?: ActionItem[]
  loading?: boolean
}

interface DetailModalEmits {
  (e: 'close'): void
  (e: 'action', key: string): void
}

const props = withDefaults(defineProps<DetailModalProps>(), {
  icon: 'material-symbols:info',
  badgeType: 'info',
  loading: false
})

const emit = defineEmits<DetailModalEmits>()

// 响应式数据
const selectedImage = ref<GalleryImage | null>(null)

// 计算属性
const badgeClasses = computed(() => {
  switch (props.badgeType) {
    case 'success':
      return 'bg-green-500/20 text-green-400 border border-green-500/30'
    case 'error':
      return 'bg-red-500/20 text-red-400 border border-red-500/30'
    case 'warning':
      return 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30'
    case 'info':
    default:
      return 'bg-blue-500/20 text-blue-400 border border-blue-500/30'
  }
})

// 方法
const handleClose = () => {
  emit('close')
}

const handleAction = (key: string) => {
  emit('action', key)
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/images/placeholder.svg'
}

const handleGalleryImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/images/placeholder.svg'
}
</script>

<style scoped>
.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: opacity 0.3s ease;
}

.modal-fade-enter-from,
.modal-fade-leave-to {
  opacity: 0;
}
</style> 