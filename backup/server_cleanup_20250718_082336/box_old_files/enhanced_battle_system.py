#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版开箱对战系统
基于现有代码分析，修复发现的设计缺陷和代码漏洞
"""

import logging
import time
import json
import threading
from typing import Optional, Dict, List, Any, Tuple
from contextlib import contextmanager
from datetime import datetime, timedelta

from django.db import transaction, connection
from django.core.cache import cache
from django.utils import timezone
from django.conf import settings

from box.models import CaseRoom, CaseRoomBet, CaseRoomRound, CaseRoomItem
from steambase.enums import GameState, RoomType, BetTeamType, RespCode
from steambase.utils import ParamException

# 导入修复模块
from .compat_async import get_async_processor, AsyncCompat
from .message_utils import sanitize_websocket_data, BattleMessageBuilder, WebSocketMessageSender
from .system_fixes import safe_database_query, TimezoneFixer
from django_redis import get_redis_connection

logger = logging.getLogger(__name__)

class EnhancedBattleSystemManager:
    """增强版对战系统管理器 - 修复版"""
    
    def __init__(self):
        self.async_processor = get_async_processor()
        self.message_builder = BattleMessageBuilder()
        self.message_sender = WebSocketMessageSender()
        self.timezone_fixer = TimezoneFixer()
        self.compat_manager = AsyncCompat()
        
        # 系统配置
        self.config = {
            'max_room_processing_time': 300,  # 5分钟最大处理时间
            'round_timeout': 60,              # 轮次超时时间
            'message_dedup_timeout': 300,     # 消息去重超时
            'lock_timeout': 120,              # 分布式锁超时
            'max_concurrent_rooms': 50,       # 最大并发房间数
            'room_cleanup_interval': 3600,    # 房间清理间隔
        }
        
        # 统计指标
        self.metrics = {
            'rooms_processed': 0,
            'errors_count': 0,
            'avg_processing_time': 0,
            'last_cleanup_time': None
        }
        
    def get_system_health(self) -> dict:
        """获取系统健康状态"""
        import sys
        from django.db import connection
        
        health_status = {
            'status': 'healthy',
            'details': {}
        }
        
        try:
            # 检查Python版本
            python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
            health_status['details']['python_version'] = python_version
            
            # 检查异步支持
            async_support = 'full' if sys.version_info >= (3, 7) else 'compatibility'
            health_status['details']['async_support'] = async_support
            
            # 检查数据库连接
            try:
                with connection.cursor() as cursor:
                    cursor.execute("SELECT 1")
                health_status['details']['database'] = 'connected'
            except Exception as e:
                health_status['details']['database'] = f'error: {str(e)}'
                health_status['status'] = 'warning'
            
            # 检查Redis连接
            try:
                redis_conn = get_redis_connection("default")
                redis_conn.ping()
                health_status['details']['redis'] = 'connected'
            except Exception as e:
                health_status['details']['redis'] = f'error: {str(e)}'
                health_status['status'] = 'warning'
            
            # 添加系统指标
            health_status['details']['metrics'] = self.metrics.copy()
            
            # 如果Python版本过低，设置警告状态
            if sys.version_info < (3, 7):
                health_status['status'] = 'warning'
                health_status['details']['warnings'] = ['Python版本建议升级到3.7+']
                
        except Exception as e:
            health_status['status'] = 'error'
            health_status['details']['error'] = str(e)
            
        return health_status
    
    def process_room_enhanced(self, room_uid: str) -> bool:
        """增强版房间处理"""
        start_time = time.time()
        processing_key = f"room_processing:{room_uid}"
        
        try:
            # 1. 获取分布式锁
            if not self._acquire_room_lock(processing_key):
                logger.debug(f"房间 {room_uid} 正在被其他进程处理")
                return False
            
            try:
                # 2. 安全获取房间
                room = self._get_room_safely(room_uid)
                if not room:
                    return False
                
                # 3. 验证房间状态
                if not self._validate_room_state(room):
                    return False
                
                # 4. 处理房间逻辑
                return self._process_room_logic(room)
                
            finally:
                self._release_room_lock(processing_key)
                
        except Exception as e:
            logger.error(f"房间处理失败: {room_uid}, error={e}")
            self._handle_room_error(room_uid, e)
            self.metrics['errors_count'] += 1
            return False
        finally:
            # 记录处理时间
            processing_time = time.time() - start_time
            self._update_metrics(processing_time)
    
    def _get_room_safely(self, room_uid: str) -> Optional[CaseRoom]:
        """安全获取房间"""
        try:
            return safe_database_query(
                lambda: CaseRoom.objects.select_for_update().get(uid=room_uid)
            )
        except CaseRoom.DoesNotExist:
            logger.warning(f"房间不存在: {room_uid}")
            return None
        except Exception as e:
            logger.error(f"获取房间失败: {room_uid}, error={e}")
            return None
    
    def _validate_room_state(self, room: CaseRoom) -> bool:
        """验证房间状态"""
        valid_states = [GameState.Full.value, GameState.Running.value]
        
        if room.state not in valid_states:
            logger.debug(f"房间状态无效: {room.short_id}, state={room.state}")
            return False
        
        # 检查房间是否超时
        if self._is_room_timeout(room):
            logger.warning(f"房间超时，强制结束: {room.short_id}")
            self._force_end_room(room)
            return False
        
        return True
    
    def _is_room_timeout(self, room: CaseRoom) -> bool:
        """检查房间是否超时"""
        now = self.timezone_fixer.get_current_time()
        timeout_threshold = now - timedelta(seconds=self.config['max_room_processing_time'])
        
        return room.update_time < timeout_threshold
    
    def _process_room_logic(self, room: CaseRoom) -> bool:
        """处理房间逻辑"""
        try:
            with transaction.atomic():
                # 获取未开启的轮次
                room_round = self._get_next_round(room)
                
                if room_round:
                    return self._process_round(room, room_round)
                else:
                    return self._finalize_battle(room)
                    
        except Exception as e:
            logger.error(f"房间逻辑处理失败: {room.short_id}, error={e}")
            return False
    
    def _get_next_round(self, room: CaseRoom) -> Optional[CaseRoomRound]:
        """获取下一个轮次"""
        return CaseRoomRound.objects.select_for_update().filter(
            room=room, 
            opened=False
        ).order_by('create_time').first()
    
    def _process_round(self, room: CaseRoom, room_round: CaseRoomRound) -> bool:
        """处理轮次"""
        try:
            # 检查轮次是否已被处理
            if room_round.opened:
                logger.warning(f"轮次已开启: room={room.short_id}, round={room_round.id}")
                return False
            
            # 更新房间状态
            if room.state == GameState.Full.value:
                room.state = GameState.Running.value
                room.save()
            
            # 标记轮次为已开启
            room_round.opened = True
            room_round.save()
            
            # 处理开箱逻辑
            return self._execute_case_opening(room, room_round)
            
        except Exception as e:
            logger.error(f"轮次处理失败: room={room.short_id}, round={room_round.id}, error={e}")
            return False
    
    def _execute_case_opening(self, room: CaseRoom, room_round: CaseRoomRound) -> bool:
        """执行开箱逻辑"""
        try:
            case = room_round.case
            available_drops = case.drops.all()
            
            if not available_drops.exists():
                logger.error(f"箱子无掉落物品: case={case.case_key}, room={room.short_id}")
                return False
            
            # 获取参与者
            bets = CaseRoomBet.objects.filter(room=room)
            if not bets.exists():
                logger.warning(f"房间无参与者: {room.short_id}")
                self._force_end_room(room)
                return False
            
            # 执行开箱
            return self._perform_case_opening(room, room_round, case, bets, available_drops)
            
        except Exception as e:
            logger.error(f"开箱执行失败: room={room.short_id}, error={e}")
            return False
    
    def _perform_case_opening(self, room, room_round, case, bets, available_drops) -> bool:
        """执行开箱操作"""
        try:
            # 为每个参与者开箱
            for bet in bets:
                # 随机选择掉落物品
                import random
                drop_item = random.choice(available_drops)
                
                # 创建开箱记录
                self._create_case_item(room, room_round, bet, drop_item)
            
            # 发送轮次消息
            self._send_round_messages(room, room_round, bets)
            
            return True
            
        except Exception as e:
            logger.error(f"开箱操作失败: room={room.short_id}, error={e}")
            return False
    
    def _create_case_item(self, room, room_round, bet, drop_item):
        """创建开箱物品记录"""
        try:
            from package.service.item import get_item_price_by_id
            
            # 获取物品价格
            price = get_item_price_by_id(drop_item.item_info.id).price
            
            # 创建房间物品记录
            CaseRoomItem.objects.create(
                room=room,
                bet=bet,
                winner=bet,
                item_info=drop_item.item_info,
                item_type=1,  # 普通物品
                price=price
            )
            
            # 更新参与者的开箱金额
            if bet.open_amount is None:
                bet.open_amount = 0
            bet.open_amount += price
            bet.save()
            
        except Exception as e:
            logger.error(f"创建开箱物品失败: room={room.short_id}, bet={bet.id}, error={e}")
    
    def _send_round_messages(self, room, room_round, bets):
        """发送轮次消息"""
        try:
            # 准备消息数据
            current_round = CaseRoomRound.objects.filter(
                room=room, 
                opened=True
            ).count()
            
            total_rounds = CaseRoomRound.objects.filter(room=room).count()
            
            # 获取轮次结果
            round_items = CaseRoomItem.objects.filter(
                room=room,
                bet__in=bets
            ).select_related('bet__user', 'item_info')
            
            # 构建消息数据
            message_data = {
                'room_uid': room.uid,
                'round': current_round,
                'total_rounds': total_rounds,
                'items': []
            }
            
            for item in round_items:
                item_data = {
                    'user_uid': item.bet.user.uid,
                    'item_name': item.item_info.name,
                    'item_price': float(item.price),
                    'item_image': item.item_info.image_url if hasattr(item.item_info, 'image_url') else ''
                }
                message_data['items'].append(item_data)
            
            # 清理消息数据
            clean_data = sanitize_websocket_data(message_data)
            
            # 发送WebSocket消息
            self._send_websocket_message(clean_data, 'round_result')
            
        except Exception as e:
            logger.error(f"发送轮次消息失败: room={room.short_id}, error={e}")
    
    def _finalize_battle(self, room: CaseRoom) -> bool:
        """完成对战"""
        try:
            bets = CaseRoomBet.objects.filter(room=room).order_by('-open_amount')
            
            if not bets.exists():
                logger.warning(f"房间无参与者，取消房间: {room.short_id}")
                room.state = GameState.Cancelled.value
                room.save()
                return False
            
            # 重置所有参与者状态
            for bet in bets:
                bet.victory = 0
                if bet.win_amount is None:
                    bet.win_amount = 0
                if bet.win_items_count is None:
                    bet.win_items_count = 0
                bet.save()
            
            # 确定获胜者
            winner = self._determine_winner(bets)
            winner.victory = 1
            winner.save()
            
            # 更新房间状态
            room.state = GameState.End.value
            room.save()
            
            # 发送对战结束消息
            self._send_battle_end_messages(room, winner, bets)
            
            logger.info(f"对战完成: room={room.short_id}, winner={winner.user.username}")
            return True
            
        except Exception as e:
            logger.error(f"对战完成失败: room={room.short_id}, error={e}")
            return False
    
    def _determine_winner(self, bets) -> CaseRoomBet:
        """确定获胜者"""
        # 按开箱金额排序，最高者获胜
        top_bets = bets.filter(open_amount=bets[0].open_amount)
        
        if top_bets.count() > 1:
            # 如果有平局，随机选择获胜者
            import random
            return random.choice(list(top_bets))
        
        return bets[0]
    
    def _send_battle_end_messages(self, room, winner, bets):
        """发送对战结束消息"""
        try:
            # 准备获胜者数据
            winner_data = {
                'user': {'uid': winner.user.uid},
                'total_amount': float(winner.open_amount or 0),
                'victory': 1
            }
            
            # 准备最终结果
            final_results = []
            for bet in bets:
                result_data = {
                    'user': {'uid': bet.user.uid},
                    'victory': bet.victory,
                    'total_amount': float(bet.open_amount or 0)
                }
                final_results.append(result_data)
            
            # 清理数据
            clean_winner_data = sanitize_websocket_data(winner_data)
            clean_results = sanitize_websocket_data(final_results)
            
            # 发送消息
            battle_end_data = {
                'room_uid': room.uid,
                'winner': clean_winner_data,
                'results': clean_results
            }
            
            self._send_websocket_message(battle_end_data, 'battle_end')
            
        except Exception as e:
            logger.error(f"发送对战结束消息失败: room={room.short_id}, error={e}")
    
    def _send_websocket_message(self, data: dict, message_type: str):
        """发送WebSocket消息"""
        try:
            from django_redis import get_redis_connection
            
            # 准备消息
            message = {
                'type': message_type,
                'data': data,
                'timestamp': self.timezone_fixer.to_timestamp(
                    self.timezone_fixer.get_current_time()
                )
            }
            
            # 序列化消息
            message_json = json.dumps(message, ensure_ascii=False)
            
            # 发送到Redis
            redis_client = get_redis_connection('default')
            redis_client.publish('ws_channel', message_json)
            
        except Exception as e:
            logger.error(f"WebSocket消息发送失败: type={message_type}, error={e}")
    
    def _force_end_room(self, room: CaseRoom):
        """强制结束房间"""
        try:
            with transaction.atomic():
                room.state = GameState.Cancelled.value
                room.save()
                
                # 退款给所有参与者
                bets = CaseRoomBet.objects.filter(room=room)
                for bet in bets:
                    bet.user.update_balance(room.price, '房间超时退款')
                
                logger.info(f"房间已强制结束: {room.short_id}")
                
        except Exception as e:
            logger.error(f"强制结束房间失败: room={room.short_id}, error={e}")
    
    def _acquire_room_lock(self, lock_key: str) -> bool:
        """获取房间锁"""
        try:
            return cache.add(lock_key, "1", timeout=self.config['lock_timeout'])
        except Exception as e:
            logger.error(f"获取锁失败: {lock_key}, error={e}")
            return False
    
    def _release_room_lock(self, lock_key: str):
        """释放房间锁"""
        try:
            cache.delete(lock_key)
        except Exception as e:
            logger.error(f"释放锁失败: {lock_key}, error={e}")
    
    def _handle_room_error(self, room_uid: str, error: Exception):
        """处理房间错误"""
        try:
            room = CaseRoom.objects.filter(uid=room_uid).first()
            if room and room.state in [GameState.Full.value, GameState.Running.value]:
                room.state = GameState.Cancelled.value
                room.save()
                logger.info(f"错误处理：房间已标记为取消: {room.short_id}")
        except Exception as e:
            logger.error(f"错误处理失败: room={room_uid}, error={e}")
    
    def _update_metrics(self, processing_time: float):
        """更新统计指标"""
        self.metrics['rooms_processed'] += 1
        
        # 计算平均处理时间
        total_time = self.metrics['avg_processing_time'] * (self.metrics['rooms_processed'] - 1) + processing_time
        self.metrics['avg_processing_time'] = total_time / self.metrics['rooms_processed']
    
    def get_system_status(self) -> dict:
        """获取系统状态"""
        return {
            'metrics': self.metrics.copy(),
            'config': self.config.copy(),
            'timestamp': self.timezone_fixer.to_timestamp(
                self.timezone_fixer.get_current_time()
            )
        }
    
    def cleanup_stale_rooms(self):
        """清理卡住的房间"""
        try:
            now = self.timezone_fixer.get_current_time()
            stale_threshold = now - timedelta(seconds=self.config['room_cleanup_interval'])
            
            # 查找卡住的房间
            stale_rooms = CaseRoom.objects.filter(
                state__in=[GameState.Running.value],
                update_time__lt=stale_threshold
            )
            
            cleaned_count = 0
            for room in stale_rooms:
                self._force_end_room(room)
                cleaned_count += 1
            
            if cleaned_count > 0:
                logger.info(f"清理了 {cleaned_count} 个卡住的房间")
            
            self.metrics['last_cleanup_time'] = now
            
        except Exception as e:
            logger.error(f"房间清理失败: {e}")


# 全局实例
enhanced_battle_manager = EnhancedBattleSystemManager()


def process_battle_room_enhanced(room_uid: str) -> bool:
    """增强版房间处理入口"""
    return enhanced_battle_manager.process_room_enhanced(room_uid)


def get_battle_system_status() -> dict:
    """获取对战系统状态"""
    return enhanced_battle_manager.get_system_status()


def cleanup_battle_rooms():
    """清理对战房间"""
    enhanced_battle_manager.cleanup_stale_rooms()
