import string
import random
import logging

from uuid import uuid1
from decimal import Decimal

from django.conf import settings
from django.contrib.auth.base_user import Abstract<PERSON>ase<PERSON><PERSON>, BaseUserManager
from django.contrib.auth.models import PermissionsMixin, User
from django.utils import timezone
from django.db import models, transaction
from django.utils.translation import gettext_lazy as _

from steambase.enums import PackageState
from steambase.models import ModelBase, USER_MODEL
from steambase.utils import ParamException
from sitecfg.interfaces import get_user_point_max, get_freegive_box_count

_logger = logging.getLogger(__name__)

def uid_gen():
    return uuid1().hex


class UserProfile(models.Model):
    user = models.OneToOneField(USER_MODEL, on_delete=models.CASCADE, related_name='profile')
    nickname = models.CharField(_('nick name'), max_length=255, default=None, null=True, blank=True)
    avatar = models.ImageField(_('avatar'), max_length=256, default='users/default_avatar.png', null=True, blank=True,
                               upload_to='users/%Y%m')

    class Meta:
        verbose_name = _('Profile')
        verbose_name_plural = _('Profile')

    def __str__(self):
        return self.user.username


class UserAsset(models.Model):
    user = models.OneToOneField(USER_MODEL, on_delete=models.CASCADE, related_name='asset')
    tradeurl = models.CharField(_('Steam trade url'), max_length=300, null=True, default=None, blank=True)
    balance = models.FloatField(_('balance'), default=0.0)
    points = models.FloatField(_("points"), default=0.0)
    diamond = models.FloatField(_("Diamond"), default=0.0)
    active_point = models.FloatField(_("active point"), default=0.0)
    total_charge_balance = models.FloatField(_('total charge balance'), default=0.0)
    # 用户每天可以充值的额度，0表示不限制
    daily_charge_limit = models.FloatField(_('daily charge limit'), default=0.0)
    # 提取总金额
    total_withdraw_balance = models.DecimalField(_('total withdraw balance'), max_digits=10, decimal_places=2, default=0)

    class Meta:
        verbose_name = _('Asset')
        verbose_name_plural = _('Asset')

    def __str__(self):
        return self.user.username


class UserSteam(models.Model):
    user = models.OneToOneField(USER_MODEL, on_delete=models.CASCADE, related_name='steam')
    steamid = models.CharField(_('Steamid'), max_length=32, default=None, null=True, blank=True)
    personaname = models.CharField(_('Steam name'), max_length=128, default=None, null=True, blank=True)
    profileurl = models.CharField(_('profile url'), max_length=300, default=None, null=True, blank=True)
    avatar = models.CharField(_('small avatar'), max_length=255, default=None, null=True, blank=True)
    avatarmedium = models.CharField(_('medium avatar'), max_length=255, default=None, null=True, blank=True)
    avatarfull = models.CharField(_('big avatar'), max_length=255, default=None, null=True, blank=True)
    level = models.IntegerField(_('Steam level'), default=0)
    own_games_count = models.IntegerField(_('own games count'), default=0)
    dota2_playtime = models.IntegerField(_('Dota2 playtime'), default=0)

    class Meta:
        verbose_name = _('Steam')
        verbose_name_plural = _('Steam')

    def __str__(self):
        return self.user.username


class UserExtra(models.Model):
    BOX_CHANCE_TYPE = (
        ('a', _('Box chance A')),
        ('b', _('Box chance B')),
        ('c', _('Box chance C')),
        ('d', _('Box chance D')),
        ('e', _('Box chance E')),
    )
    LUCKY_BOX_RATE_TYPE = (
        ('a', _("Lucky Box Rate Type A")),
        ('b', _("Lucky Box Rate Type B")),
        ('c', _("Lucky Box Rate Type C")),
        ('d', _("Lucky Box Rate Type D")),
        ('e', _("Lucky Box Rate Type E")),
    )
    user = models.OneToOneField(USER_MODEL, on_delete=models.CASCADE, related_name='extra')
    box_chance_type = models.CharField(_('box chance type'), max_length=4, default='a', choices=BOX_CHANCE_TYPE)
    box_free_count = models.IntegerField(_('box free count'), default=0)
    box_free_last = models.DateTimeField(_('box free last'), default=None, null=True, blank=True)
    box_free_zero_count = models.IntegerField(_('box free level 0 count'), default=0)
    box_free_zero_last = models.DateTimeField(_('box free level 0 last'), default=None, null=True, blank=True)
    ban_chat = models.BooleanField(_('ban chat'), default=False)
    ban_deposit = models.BooleanField(_('ban deposit'), default=False)
    ban_withdraw = models.BooleanField(_('ban withdraw'), default=False)
    ban_exchange = models.BooleanField(_('ban exchange'), default=False)
    ban_shop = models.BooleanField(_('ban shop'), default=False)
    ban_roll = models.BooleanField(_('ban roll room'), default=False)
    ban_create_roll_room = models.BooleanField(_("ban create roll room"), default=True)
    ban_charge_room = models.BooleanField(_('ban charge room'), default=False)
    box_freegive_count = models.IntegerField(_('box free give count'), default=get_freegive_box_count)
    freebox_lv1_count = models.IntegerField(_('freebox lv1 count'), default=0)
    freebox_lv2_count = models.IntegerField(_('freebox lv2 count'), default=0)
    freebox_lv3_count = models.IntegerField(_('freebox lv3 count'), default=0)
    freebox_lv4_count = models.IntegerField(_('freebox lv4 count'), default=0)
    freebox_lv5_count = models.IntegerField(_('freebox lv5 count'), default=0)
    freebox_lv6_count = models.IntegerField(_('freebox lv6 count'), default=0)
    freebox_lv7_count = models.IntegerField(_('freebox lv7 count'), default=0)
    freebox_lv8_count = models.IntegerField(_('freebox lv8 count'), default=0)
    freebox_lv9_count = models.IntegerField(_('freebox lv9 count'), default=0)
    freebox_lv10_count = models.IntegerField(_('freebox lv10 count'), default=0)
    freebox_lv1_limit = models.IntegerField(_('freebox lv1 limit'), default=1)
    freebox_lv2_limit = models.IntegerField(_('freebox lv2 limit'), default=1)
    freebox_lv3_limit = models.IntegerField(_('freebox lv3 limit'), default=1)
    freebox_lv4_limit = models.IntegerField(_('freebox lv4 limit'), default=1)
    freebox_lv5_limit = models.IntegerField(_('freebox lv5 limit'), default=1)
    freebox_lv6_limit = models.IntegerField(_('freebox lv6 limit'), default=1)
    freebox_lv7_limit = models.IntegerField(_('freebox lv7 limit'), default=1)
    freebox_lv8_limit = models.IntegerField(_('freebox lv8 limit'), default=1)
    freebox_lv9_limit = models.IntegerField(_('freebox lv9 limit'), default=1)
    freebox_lv10_limit = models.IntegerField(_('freebox lv10 limit'), default=1)
    luckybox_rate_type = models.CharField(_('luckybox rate type'), max_length=128, default='a', choices=LUCKY_BOX_RATE_TYPE)
    promotion_box_level = models.IntegerField(_('box promotion level'), default=0)

    ban_withdraw_reason = models.TextField(_('ban withdraw reason'), max_length=256, default=None, null=True, blank=True)
    ban_deposit_reason = models.TextField(_('ban deposit reason'), max_length=256, default=None, null=True, blank=True)

    ban_battle = models.BooleanField(_('ban battle'), default=True)
    
    ban_battle_reason = models.TextField(_('ban battle reason'), max_length=256, default=None, null=True, blank=True)

    ban_rename = models.BooleanField(_('ban rename'), default=False)

    locked_box_chance = models.BooleanField(_('locked box chance'), default=False)

    exam_passed = models.BooleanField(_('exam passed'), default=False)
    exam_time = models.DateTimeField(_('exam time'), default=None, null=True, blank=True)

    ban_avatar = models.BooleanField(_('ban avatar'), default=False)

    # 新增2个字段：止盈和止损阈值 profit_limit loss_limit
    profit_limit = models.DecimalField(_('profit limit'), default=0.0, max_digits=10, decimal_places=2)
    loss_limit = models.DecimalField(_('loss limit'), default=0.0, max_digits=10, decimal_places=2)
    


    class Meta:
        verbose_name = _('Extra')
        verbose_name_plural = _('Extra')

    def __str__(self):
        return self.user.username
    

    def update_freebox_lv1_count(self, user, count=1):
        with transaction.atomic():
            extra = UserExtra.objects.select_for_update().get(user=user)
            if count != 1:
                raise ParamException(_('Invalid count change'))
            if extra.freebox_lv1_count - count < 0:
                raise ParamException(_('Invalid count change'))
            extra.freebox_lv1_count = extra.freebox_lv1_count - count
            extra.save()

    def update_freebox_lv2_count(self, user, count=1):
        with transaction.atomic():
            extra = UserExtra.objects.select_for_update().get(user=user)
            if count != 1:
                raise ParamException(_('Invalid count change'))
            if extra.freebox_lv2_count - count < 0:
                raise ParamException(_('Invalid count change'))
            extra.freebox_lv2_count = extra.freebox_lv2_count - count
            extra.save()

    def update_freebox_lv3_count(self, user, count=1):
        with transaction.atomic():
            extra = UserExtra.objects.select_for_update().get(user=user)
            if count != 1:
                raise ParamException(_('Invalid count change'))
            if extra.freebox_lv3_count - count < 0:
                raise ParamException(_('Invalid count change'))
            extra.freebox_lv3_count = extra.freebox_lv3_count - count
            extra.save()

    def update_freebox_lv4_count(self, user, count=1):
        with transaction.atomic():
            extra = UserExtra.objects.select_for_update().get(user=user)
            if count != 1:
                raise ParamException(_('Invalid count change'))
            if extra.freebox_lv4_count - count < 0:
                raise ParamException(_('Invalid count change'))
            extra.freebox_lv4_count = extra.freebox_lv4_count - count
            extra.save()

    def update_freebox_lv5_count(self, user, count=1):
        with transaction.atomic():
            extra = UserExtra.objects.select_for_update().get(user=user)
            if count != 1:
                raise ParamException(_('Invalid count change'))
            if extra.freebox_lv5_count - count < 0:
                raise ParamException(_('Invalid count change'))
            extra.freebox_lv5_count = extra.freebox_lv5_count - count
            extra.save()

    def update_freebox_lv6_count(self, user, count=1):
        with transaction.atomic():
            extra = UserExtra.objects.select_for_update().get(user=user)
            if count != 1:
                raise ParamException(_('Invalid count change'))
            if extra.freebox_lv6_count - count < 0:
                raise ParamException(_('Invalid count change'))
            extra.freebox_lv6_count = extra.freebox_lv6_count - count
            extra.save()

    def update_freebox_lv7_count(self, user, count=1):
        with transaction.atomic():
            extra = UserExtra.objects.select_for_update().get(user=user)
            if count != 1:
                raise ParamException(_('Invalid count change'))
            if extra.freebox_lv7_count - count < 0:
                raise ParamException(_('Invalid count change'))
            extra.freebox_lv7_count = extra.freebox_lv7_count - count
            extra.save()

    def update_freebox_lv8_count(self, user, count=1):
        with transaction.atomic():
            extra = UserExtra.objects.select_for_update().get(user=user)
            if count != 1:
                raise ParamException(_('Invalid count change'))
            if extra.freebox_lv8_count - count < 0:
                raise ParamException(_('Invalid count change'))
            extra.freebox_lv8_count = extra.freebox_lv8_count - count
            extra.save()

    def update_freebox_lv9_count(self, user, count=1):
        with transaction.atomic():
            extra = UserExtra.objects.select_for_update().get(user=user)
            if count != 1:
                raise ParamException(_('Invalid count change'))
            if extra.freebox_lv9_count - count < 0:
                raise ParamException(_('Invalid count change'))
            extra.freebox_lv9_count = extra.freebox_lv9_count - count
            extra.save()

    def update_freebox_lv10_count(self, user, count=1):
        with transaction.atomic():
            extra = UserExtra.objects.select_for_update().get(user=user)
            if count != 1:
                raise ParamException(_('Invalid count change'))
            if extra.freebox_lv10_count - count < 0:
                raise ParamException(_('Invalid count change'))
            extra.freebox_lv10_count = extra.freebox_lv10_count - count
            extra.save()

    def update_freebox_limit(self, user, level, count=1):
        with transaction.atomic():
            extra = UserExtra.objects.select_for_update().get(user=user)
            if count != 1:
                raise ParamException(_('Invalid count change'))
            result = eval(f"extra.freebox_lv{level}_limit - count")
            if result < 0:
                raise ParamException(_('该等级箱子开启超过限制'))
            exec(f"extra.freebox_lv{level}_limit = extra.freebox_lv{level}_limit  - count")
            extra.save()

class UserBalanceRecord(ModelBase):
    user = models.ForeignKey(USER_MODEL, verbose_name=_("user"), related_name='balance_records')
    balance_changed = models.FloatField(_("changed"), default=0.0)
    balance_before = models.FloatField(_("change before"), default=0.0)
    balance_after = models.FloatField(_("change after"), default=0.0)
    reason = models.CharField(_("reason"), max_length=256, default=None, null=True, blank=True)

    class Meta:
        verbose_name = _('User Balance Record')
        verbose_name_plural = _('User Balance Record')

    def __str__(self):
        return self.uid


class UserPointsRecord(ModelBase):
    user = models.ForeignKey(USER_MODEL, verbose_name=_("user"), related_name='points_records')
    points_changed = models.FloatField(_("changed"), default=0.0)
    points_before = models.FloatField(_("change before"), default=0.0)
    points_after = models.FloatField(_("change after"), default=0.0)
    reason = models.CharField(_("reason"), max_length=256, default=None, null=True, blank=True)

    class Meta:
        verbose_name = _('User Points Record')
        verbose_name_plural = _('User Points Record')

    def __str__(self):
        return self.uid


class UserDiamondRecord(ModelBase):
    user = models.ForeignKey(USER_MODEL, verbose_name=_("user"), related_name='diamond_records')
    diamond_changed = models.FloatField(_("changed"), default=0.0)
    diamond_before = models.FloatField(_("change before"), default=0.0)
    diamond_after = models.FloatField(_("change after"), default=0.0)
    reason = models.CharField(_("reason"), max_length=256, default=None, null=True, blank=True)

    class Meta:
        verbose_name = _('User Diamond Record')
        verbose_name_plural = _('User Diamond Record')

    def __str__(self):
        return self.uid


class UserActivePointRecord(ModelBase):
    user = models.ForeignKey(USER_MODEL, verbose_name=_("user"), related_name='active_point_records')
    active_point_changed = models.FloatField(_("changed"), default=0.0)
    active_point_before = models.FloatField(_("change before"), default=0.0)
    active_point_after = models.FloatField(_("change after"), default=0.0)
    reason = models.CharField(_("reason"), max_length=256, default=None, null=True, blank=True)

    class Meta:
        verbose_name = _('User Active Point Record')
        verbose_name_plural = _('User Active Point Record')

    def __str__(self):
        return self.uid


class AuthUserManager(BaseUserManager):
    def _create_user(self, password, **extra_fields):
        """
        Creates and saves a User with the given steamid and password.
        """
        user = self.model(**extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        UserAsset.objects.create(user=user)
        UserSteam.objects.create(user=user)
        UserProfile.objects.create(user=user)
        UserExtra.objects.create(user=user)
        UserStatisticsDay.update_count(1)
        return user

    def create_user(self, password=None, **extra_fields):
        extra_fields.setdefault('is_staff', False)
        extra_fields.setdefault('is_superuser', False)
        return self._create_user(password, **extra_fields)

    def create_superuser(self, password, **extra_fields):
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)

        if extra_fields.get('is_staff') is not True:
            raise ValueError('Superuser must have is_staff=True.')
        if extra_fields.get('is_superuser') is not True:
            raise ValueError('Superuser must have is_superuser=True.')

        return self._create_user(password, **extra_fields)


class AuthUser(AbstractBaseUser, PermissionsMixin):
    USERNAME_FIELD = 'username'

    uid = models.CharField(_('user uid'), max_length=128, unique=True, default=uid_gen)
    username = models.CharField(_('user name'), max_length=255, unique=True, default=None, null=True, blank=True)
    email = models.CharField(_('email address'), max_length=128, default=None, null=True, blank=True)
    phone = models.CharField(_('phone'), max_length=32, default=None, null=True, blank=True)
    date_joined = models.DateTimeField(_('date joined'), default=timezone.now)
    domain = models.CharField(_('Domain'), max_length=300, null=True, default=None, blank=True)
    reg_ip = models.CharField(_('registered ip'), max_length=128, default=None, null=True, blank=True)
    login_ip = models.CharField(_('login ip'), max_length=128, default=None, null=True, blank=True)
    login_domain = models.CharField(_('login domain'), max_length=300, null=True, default=None, blank=True)
    login_time = models.DateTimeField(_('login time'), default=timezone.now)

    # 新增一个文本字段，用于后台备注用户
    note = models.TextField(_('note'), default=None, null=True, blank=True)
    is_vip = models.BooleanField(_('is vip'), default=False)



    

    is_staff = models.BooleanField(
        _('staff status'),
        default=False,
        help_text=_('Designates whether the user can log into this admin site.'),
    )
    is_agent = models.BooleanField(_('agent status'), default=False)
    is_active = models.BooleanField(
        _('active'),
        default=True,
        help_text=_(
            'Designates whether this user should be treated as active. '
            'Unselect this instead of deleting accounts.'
        ),
    )

    ban_active_reasons = models.CharField(_('用户锁定原因'), max_length=256, default=None, null=True, blank=True)

    # is_charge = models.BooleanField(_('充值状态'), default=True)
    # ban_charge_reasons = models.CharField(_('充值禁用原因'), max_length=256, default=None, null=True, blank=True)
    
    objects = AuthUserManager()

    def update_box_chance_type(self, type):
        with transaction.atomic():
            extra = UserExtra.objects.select_for_update().get(user=self)
            extra.box_chance_type = type
            extra.save()

    def update_locked_box_chance(self, chance):
        with transaction.atomic():
            extra = UserExtra.objects.select_for_update().get(user=self)
            extra.locked_box_chance = chance
            extra.save()


    def ban_users_with_same_ip(self):
        """
        检查当天相同注册 IP 的用户数，如果达到或超过 5，则将这些用户的 ban_deposit 设置为 True。
        """
        today = timezone.localdate()

        # 查询当天相同注册 IP 的用户数, 处理批量注册
        same_ip_users = AuthUser.objects.filter(
            reg_ip=self.reg_ip,  # 匹配注册 IP
            date_joined__date=today  # 限制在当天注册
        )

        if same_ip_users.count() >= 3:
            # 批量更新这些用户的 extra.ban_deposit 为 True
            with transaction.atomic():
                for user in same_ip_users:
                    user.extra.box_chance_type = 'c'
                    user.extra.locked_box_chance = True
                    user.extra.exam_passed = False
                    user.extra.ban_deposit = True
                    user.extra.save()

            # 日志记录
            _logger.info(f"Ban deposit set to True for users registered with IP: {self.reg_ip}")


    def update_balance(self, amount, remark=None):
        """
        更新用户余额。

        参数:
        - amount (float | Decimal): 余额变动的金额，可以为正（增加）或负（减少）。
        - remark (str): 余额变动的原因备注。

        抛出:
        - ParamException: 如果余额不足或变动无效。
        """
        with transaction.atomic():
            # 确保金额类型为 Decimal，并保留两位小数
            amount = Decimal(str(amount)).quantize(Decimal('0.00'))

            if amount == 0:
                # _logger.info(f"用户 {self.steam.personaname} 的余额变动金额为 0，无需更新")
                return

            # 锁定用户资产记录
            asset = UserAsset.objects.select_for_update().get(user=self)

            # 检查余额是否足够
            if Decimal(asset.balance) + amount < 0:
                _logger.error(f"用户 {self.steam.personaname} 的余额不足以减少 {amount}，当前余额为 {asset.balance}")
                raise ParamException(_('Invalid balance change'))

            # 记录余额变动前后的信息
            balance_before = asset.balance
            balance_after = Decimal(balance_before) + amount
            balance_after = balance_after.quantize(Decimal('0.00'))  # 确保精度

            # 更新用户资产余额
            asset.balance = balance_after
            asset.save()

            # 创建余额变动记录
            UserBalanceRecord.objects.create(
                user=self,
                balance_changed=amount,
                balance_before=balance_before,
                balance_after=balance_after,
                reason=remark
            )

            # 只记录普通用户的余额变动

            if not self.is_staff:
                # 日志记录余额更新
                _logger.info(
                    f"用户 {self.steam.personaname} 的余额变动成功: 变动金额 {amount}, 原余额 {balance_before}, 新余额 {balance_after}, 原因: {remark}"
                )
    
            
           
            
    def update_exams_passed(self, passed):
        with transaction.atomic():
            extra = UserExtra.objects.select_for_update().get(user=self)
            extra.exam_passed = passed
            extra.exam_time = timezone.now()
            extra.save()

    def update_points(self, amount, remark=None, shop=False):
        with transaction.atomic():
            asset = UserAsset.objects.select_for_update().get(user=self)
            amount = round(amount, 2)
            if shop and asset.points + amount < 0:
                raise ParamException(_('Invalid points change'))
            if asset.points + amount < 0:
                amount = -asset.points
            elif asset.points + amount > get_user_point_max():
                amount = get_user_point_max() - asset.points
            if amount == 0:
                return
            points_before = asset.points
            asset.points += amount
            asset.save()
            points_after = asset.points
            UserPointsRecord.objects.create(
                user=self, points_changed=amount, points_before=points_before,
                points_after=points_after, reason=remark)

    def update_diamond(self, amount, remark=None):
        with transaction.atomic():
            if amount == 0:
                return
            asset = UserAsset.objects.select_for_update().get(user=self)
            amount = round(amount, 2)
            if asset.diamond + amount < 0:
                raise ParamException(_('Invalid diamond change'))
            diamond_before = asset.diamond
            asset.diamond += amount
            asset.diamond = round(asset.diamond, 2)
            asset.save()
            diamond_after = asset.diamond
            UserDiamondRecord.objects.create(
                user=self, diamond_changed=amount, diamond_before=diamond_before,
                diamond_after=diamond_after, reason=remark)

    def update_active_point(self, amount, remark=None):
        with transaction.atomic():
            if amount == 0:
                return
            asset = UserAsset.objects.select_for_update().get(user=self)
            amount = round(amount, 2)
            if asset.active_point + amount < 0:
                raise ParamException(_('Invalid active point change'))
            active_point_before = asset.active_point
            asset.active_point += amount
            asset.active_point = round(asset.active_point, 2)
            asset.save()
            active_point_after = asset.active_point
            UserActivePointRecord.objects.create(
                user=self, active_point_changed=amount, active_point_before=active_point_before,
                active_point_after=active_point_after, reason=remark)

    def update_box_free_count(self, count):
        with transaction.atomic():
            if count == 0:
                return
            extra = UserExtra.objects.select_for_update().get(user=self)
            if extra.box_free_count + count < 0:
                raise ParamException(_('No enough count'))
            extra.box_free_count += count
            extra.box_free_last = timezone.now()
            extra.save()

    def update_box_free_zero_count(self, count):
        with transaction.atomic():
            if count == 0:
                return
            extra = UserExtra.objects.select_for_update().get(user=self)
            if extra.box_free_zero_count + count < 0:
                raise ParamException(_('No enough count'))
            extra.box_free_zero_count += count
            extra.box_free_zero_last = timezone.now()
            extra.save()



    def update_total_charge_balance(self, amount, remark=None):
        """
        更新用户累计充值余额。

        参数:
        - amount (float | Decimal): 累计充值余额的变动金额，可以为正（增加）或负（减少）。
        - remark (str): 变动原因备注。

        抛出:
        - ParamException: 如果余额不足或变动无效。
        """
        with transaction.atomic():
            # 确保金额类型为 Decimal，并保留两位小数
            amount = Decimal(str(amount)).quantize(Decimal('0.00'))

            if amount == 0:
                # _logger.info(f"用户 {self.steam.personaname} 的累计充值变动金额为 0，无需更新")
                return

            # 锁定用户资产记录
            asset = UserAsset.objects.select_for_update().get(user=self)

            # 检查累计充值余额是否足够
            if Decimal(asset.total_charge_balance) + amount < 0:
                _logger.error(
                    f"用户 {self.steam.personaname} 的累计充值余额不足以减少 {amount}，当前累计余额为 {asset.total_charge_balance}"
                )
                raise ParamException(_('Invalid total charge balance change'))

            # 记录余额变动前后的信息
            balance_before = asset.total_charge_balance
            balance_after = Decimal(balance_before) + amount
            balance_after = balance_after.quantize(Decimal('0.00'))  # 确保精度

            # 更新累计充值余额
            asset.total_charge_balance = balance_after
            asset.save()

            # 日志记录
            _logger.info(
                f"用户 {self.steam.personaname} 的累计充值余额变动成功: 变动金额 {amount}, 原累计余额 {balance_before}, 新累计余额 {balance_after}, 原因: {remark}"
            )

    def update_case_key(self, key):
        with transaction.atomic():
            case_key = self.package_items.filter(item_info=key, state=PackageState.Available.value).first()
            if not case_key:
                raise ParamException(_('No enough count'))
            case_key.state = PackageState.Invalid.value
            case_key.save()

    class Meta:
        verbose_name = _('Auth User')
        verbose_name_plural = _('Auth User')

    def get_short_name(self):
        return self.username

    def get_full_name(self):
        return self.username

    def __str__(self):
        return self.username


class UserStatisticsDay(ModelBase):
    date = models.DateField(_("date"), default=timezone.now)
    count = models.IntegerField(_("count"), default=0)

    @classmethod
    def update_count(cls, count):
        with transaction.atomic():
            today = timezone.localdate()
            if not cls.objects.filter(date=today):
                cls.objects.create(date=today)
            record = cls.objects.select_for_update().get(date=today)
            record.count += count
            record.save()

    def __str__(self):
        return str(self.date)

    class Meta:
        ordering = ('-create_time',)
        verbose_name = _('User Statistics Day')
        verbose_name_plural = _('User Statistics Day')

# 用户获取手机验证码记录
class PhoneCodeRecord(ModelBase):
    # phone 改成 联系方式 contact
    phone = models.CharField(_('phone'), max_length=128)
    # contact = models.CharField(_('contact'), max_length=128, default=None, null=True, blank=True)
    
    code = models.CharField(_('code'), max_length=6)
    ip = models.CharField(_('ip'), max_length=128, default=None, null=True, blank=True)
    domain = models.CharField(_('domain'), max_length=128, default=None, null=True, blank=True)


    def __str__(self):
        return str(self.phone)
    
    class Meta:
        verbose_name = _('Phone Code Record')
        verbose_name_plural = _('Phone Code Record')

# steam ID 黑名单
class SteamBlackList(ModelBase):
    steam_id = models.CharField(_('steam id'), max_length=128, unique=True)
    reason = models.CharField(_('reason'), max_length=256, default=None, null=True, blank=True)
    enable = models.BooleanField(_('enable'), default=True)

    def __str__(self):
        return str(self.steam_id)
    
    class Meta:
        verbose_name = _('Steam Black List')
        verbose_name_plural = _('Steam Black List')

# 允许注册的邮箱后缀
class EmailSuffix(ModelBase):
    suffix = models.CharField(_('suffix'), max_length=128, unique=True)
    enable = models.BooleanField(_('enable'), default=True)
    def __str__(self):
        return str(self.suffix)
    
    class Meta:
        verbose_name = _('Email Suffix')
        verbose_name_plural = _('Email Suffix')