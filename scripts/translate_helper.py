#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
翻译助手脚本 - 用于批量翻译 Django .po 文件中的常见英文词汇
"""

import re
import os

# 常见英文词汇到中文的映射
TRANSLATION_MAP = {
    # 基础词汇
    "Name": "名称",
    "Title": "标题",
    "Description": "描述",
    "Content": "内容",
    "Status": "状态",
    "Type": "类型",
    "Category": "分类",
    "Tag": "标签",
    "Price": "价格",
    "Amount": "金额",
    "Quantity": "数量",
    "Date": "日期",
    "Time": "时间",
    "Created": "创建时间",
    "Updated": "更新时间",
    "Active": "激活",
    "Inactive": "未激活",
    "Enabled": "启用",
    "Disabled": "禁用",
    "Success": "成功",
    "Failed": "失败",
    "Error": "错误",
    "Warning": "警告",
    "Info": "信息",

    # 模型名称翻译
    "Agent": "代理商",
    "Agents": "代理商",
    "Article": "文章",
    "Articles": "文章",
    "Article Category": "文章分类",
    "Article Categories": "文章分类",
    "Profile": "个人资料",
    "Asset": "资产",
    "Steam": "Steam",
    "Extra": "其他",
    "Auth User": "注册用户",
    "User Statistics Day": "日用户统计",
    "Phone Code Record": "手机验证码记录",
    "Steam Black List": "Steam黑名单",
    "Email Suffix": "邮箱后缀",
    "B2C Market Item": "市场物品",
    "ZBT Trade Record": "交易记录",
    "ZBTBlackList": "黑名单",
    "B2COfficialTrade Record": "交易记录",
    "B2C Item list": "市场物品列表",
    "B2COfficial Account": "官方账户",
    "B2CTrade Statistics Day": "日交易统计",
    "B2CTrade Statistics Month": "月交易统计",
    "Case Type": "箱子种类",
    "LuckyBox Group": "拉货分组",
    "LuckyBox Recommend Group": "拉货热门分组",
    "LuckyBox Item": "拉货饰品",
    "LuckyBoxCategory": "拉货分类",
    "LuckyBox Record": "幸运饰品追梦记录",
    "MarketItem": "商城饰品",
    "Item Info": "饰品信息",
    "Item Category": "饰品分类",
    "Item Quality": "饰品品质",
    "Item Rarity": "饰品稀有度",
    "Item Exterior": "饰品外观",
    "Item Price": "饰品价格",
    "Package Item": "饰品背包",
    "Item Price Rate Config": "饰品价格比率配置",
    "Exchange Record": "出售记录",
    "Shop record": "商城记录",
    "Trade Record": "交易记录",
    "Trade Item": "交易饰品",
    "Trade Bot Config": "交易机器人配置",
    "Trade Bot Inventory": "交易机器人库存",
    "Shop Bot Config": "商城机器人配置",
    "BlackList": "黑名单",
    "Item Statistics Day": "日饰品统计",
    "Item Statistics Month": "月饰品统计",
    "Item Unlock Time Config": "饰品未锁定时间配置",
    "Lock Items Statistics": "锁定饰品统计",
    "Item Whitelist": "饰品白名单",
    "User Promotion": "用户推广信息",
    "Promotion Record": "推广记录",
    "Promotion Level Config": "推广等级配置",
    "PromotionCaseConfig": "推广箱子配置",
    "Roll Room": "roll房",
    "Roll Room Bet": "Roll房加入",
    "Roll Room Item": "Roll房饰品",
    "Roll Room Pump Day": "日Crash Pump",
    "Roll Room Pump Month": "月Crash Pump",
    "Rollroom Bot Config": "Roll房机器人配置",
    "Roll房机器人": "Roll房机器人",
    "Rollroom Statistics Day": "Roll房日统计",
    "Rollroom Statistics Month": "Roll房月统计",
    "Site Config": "站点配置",
    "Site Config Category": "站点配置类别",
    "Announcee": "公告",
    "Footer": "页脚",
    "FAQ": "FAQ",
    "Support": "支持",
    "Banner": "横幅",
    "SEO": "SEO",
    "Tradeup Game": "Tradeup游戏",
    "Tradeup Bet Item": "Tradeup Bet Item",
    "Coins Tradeup Bet amount": "Coins Tradeup Bet amount",
    "Tradeup Target Item": "Tradeup Target Item",
    "Tradeup Inventory Item": "Tradeup Inventory Item",
    "Tradeup Pump Day": "Tradeup Pump Day",
    "Tradeup Pump Month": "Tradeup Pump Month",
    "Tradeup Statistics Day": "Tradeup Statistics Day",
    "Tradeup Statistics Month": "Tradeup Statistics Month",
    "Tradeup Win Day Rank": "Tradeup日获胜排名",
    "Tradeup Win Week Rank": "Tradeup周获胜排名",
    "Tradeup Bot Config": "Tradeup机器人配置",
    "Withdraw Item": "提取饰品",
    "Wxp Trade Offer": "Wxp交易记录",
    "Trade Statistics Day": "日交易统计",
    "Trade Statistics Month": "月交易统计",

    # 用户相关
    "User": "用户",
    "Users": "用户",
    "Username": "用户名",
    "Password": "密码",
    "Email": "邮箱",
    "Phone": "电话",
    "Profile": "个人资料",
    "Account": "账户",
    "Balance": "余额",
    "Login": "登录",
    "Logout": "退出",
    "Register": "注册",

    # 字段名称翻译
    "Agent Name": "代理商名称",
    "Agent Phone": "代理商电话",
    "Agent Email": "代理商邮箱",
    "Agent Wechat": "代理商微信",
    "Agent QQ": "代理商QQ",
    "Agent Alipay": "代理商支付宝",
    "Agent Wechatpay": "代理商微信支付",
    "Agent Bankcard": "代理商银行卡",
    "Agent Bank": "代理商银行",
    "Agent Bankname": "代理商银行名称",
    "Enable": "启用",
    "Create Time": "创建时间",
    "Update Time": "更新时间",
    "Remark": "备注",
    "Init": "初始化",
    "Alipay": "支付宝",
    "Wechatpay": "微信支付",
    "Bankcard": "银行卡",
    "Pay Type": "支付类型",
    "Agent Withdrawal Order": "代理商提现订单",
    "Agent Withdrawal Orders": "代理商提现订单",
    "Balance Changed": "余额变更",
    "Balance Before": "变更前余额",
    "Balance After": "变更后余额",
    "User Recharge": "用户充值",
    "User Withdrawal": "用户提现",
    "Other": "其他",
    "Out Trade No": "外部交易号",
    "Agent Balance Record": "代理商余额记录",
    "Agent Balance Records": "代理商余额记录",
    "nick name": "昵称",
    "avatar": "头像",
    "Steam trade url": "Steam交易链接",
    "balance": "余额",
    "points": "积分",
    "Diamond": "钻石",
    "active point": "活跃积分",
    "total charge balance": "总充值余额",
    "daily charge limit": "每日充值限制",
    "total withdraw balance": "总提现余额",
    "Steamid": "Steamid",
    "Steam name": "Steam名字",
    "profile url": "个人资料链接",
    "small avatar": "小头像",
    "medium avatar": "中头像",
    "big avatar": "大头像",
    "Steam level": "Steam等级",
    "own games count": "拥有游戏数量",
    "Dota2 playtime": "Dota2游戏时间",
    
    # 管理相关
    "Admin": "管理员",
    "Administration": "管理",
    "Dashboard": "仪表板",
    "Settings": "设置",
    "Configuration": "配置",
    "Management": "管理",
    "System": "系统",
    "Database": "数据库",
    "Cache": "缓存",
    "Log": "日志",
    "Logs": "日志",
    "Django site admin": "Django 站点管理",
    "Django administration": "Django 管理",

    # 状态相关
    "Initialed": "已初始化",
    "Accepted": "已接受",
    "Cancelled": "已取消",
    "Trading": "取回中",
    "Cancelling": "取消中",
    "PriceCancelled": "溢价取消",
    "OutOfStock": "缺货",
    "ZBTCancelled": "卖家已取消",
    "WaitForPay": "等待购买",
    "WaitForSend": "等待发送",
    "WaitForTrade": "等待交易",
    "Receive": "接受",
    "Market": "市场",
    "Package": "饰品背包",
    "trade url": "交易链接",
    "amount": "金额",
    "WaitForBuy": "等待购买",
    "RequestBuy": "请求购买",
    "WaitUnlock": "等待解锁",
    "TradeReady": "准备交易",
    "BuyerCancelled": "买家取消",
    "Purchase": "购买",
    "Stocks": "库存",
    "account": "账户",
    "Invalid items": "无效饰品",
    "Month": "月",
    "Box chance A": "开箱概率A",
    "Box chance B": "开箱概率B",
    "Box chance C": "开箱概率C",
    "Box chance D": "开箱概率D",
    "Box chance E": "开箱概率E",
    "Lucky Box Rate Type A": "拉货概率折扣A",
    "Lucky Box Rate Type B": "拉货概率折扣B",
    "Lucky Box Rate Type C": "拉货概率折扣C",
    "Lucky Box Rate Type D": "拉货概率折扣D",
    "Lucky Box Rate Type E": "拉货概率折扣E",
    "box chance type": "开箱概率种类",
    "box free count": "免费开箱次数",
    "box free last": "上次免费开箱时间",
    "box free level 0 count": "0级免费开箱次数",
    "box free level 0 last": "0级上次免费开箱时间",
    "ban chat": "禁止聊天",
    "ban deposit": "禁止存入",
    "ban withdraw": "禁止取回",
    "ban exchange": "禁止交换",
    "ban shop": "禁止开店",
    "ban roll room": "禁止roll房",
    "ban create roll room": "禁止创建roll房",
    "ban charge room": "禁止收费房间",
    "box free give count": "白给箱剩余次数",
    "freebox lv1 count": "免费等级1箱子次数",
    "freebox lv2 count": "免费等级2箱子次数",
    "freebox lv3 count": "免费等级3箱子次数",
    "freebox lv4 count": "免费等级4箱子次数",
    "freebox lv5 count": "免费等级5箱子次数",
    "freebox lv6 count": "免费等级6箱子次数",
    "freebox lv7 count": "免费等级7箱子次数",
    "freebox lv8 count": "免费等级8箱子次数",
    "freebox lv9 count": "免费等级9箱子次数",
    "freebox lv10 count": "免费等级10箱子次数",
    "freebox lv1 limit": "免费等级1箱子次数限制",
    "freebox lv2 limit": "免费等级2箱子次数限制",
    "freebox lv3 limit": "免费等级3箱子次数限制",
    "freebox lv4 limit": "免费等级4箱子次数限制",
    "freebox lv5 limit": "免费等级5箱子次数限制",
    "freebox lv6 limit": "免费等级6箱子次数限制",
    "freebox lv7 limit": "免费等级7箱子次数限制",
    "freebox lv8 limit": "免费等级8箱子次数限制",
    "freebox lv9 limit": "免费等级9箱子次数限制",
    "freebox lv10 limit": "免费等级10箱子次数限制",
    "luckybox rate type": "拉货概率折扣类型",
    "box promotion level": "箱子推广等级",
    "ban withdraw reason": "禁止取回原因",
    "ban deposit reason": "禁止存入原因",
    "ban battle": "禁止对战",
    "ban battle reason": "禁止对战原因",
    "ban rename": "禁止重命名",
    "locked box chance": "锁定箱子概率",
    "exam passed": "考试通过",
    "exam time": "考试时间",
    "ban avatar": "禁止头像",
    "profit limit": "收益限制",
    "loss limit": "损失限制",

    # 业务消息翻译
    "用户充值": "用户充值",
    "User asset not found": "用户资产未找到",
    "您提交的URL没有任何改变": "您提交的URL没有任何改变",
    "该steam链接已绑定其他用户,一个链接仅支持绑定一个账户": "该steam链接已绑定其他用户,一个链接仅支持绑定一个账户",
    "更新失败，未知错误": "更新失败，未知错误",
    "更新失败，系统检测到本次操作存在安全问题": "更新失败，系统检测到本次操作存在安全问题",
    "验证码错误，请重试": "验证码错误，请重试",
    "该手机已绑定其他用户，请重试": "该手机已绑定其他用户，请重试",
    "该邮箱已绑定其他用户，请重试": "该邮箱已绑定其他用户，请重试",
    "超过验证码发送限制": "超过验证码发送限制",
    "验证码发送失败请重试": "验证码发送失败请重试",
    "用户名长度必须在2-10之间": "用户名长度必须在2-10之间",
    "用户名不能是手机号": "用户名不能是手机号",
    "缺少参数": "缺少参数",
    "密码必须8-16位": "密码必须8-16位",
    "同一IP注册过多，请稍后重试": "同一IP注册过多，请稍后重试",
    "验证码尝试次数过多，请稍后重试": "验证码尝试次数过多，请稍后重试",
    "验证码过期": "验证码过期",
    "验证码错误": "验证码错误",
    "Token不匹配": "Token不匹配",
    "Token或验证码验证失败": "Token或验证码验证失败",
    "Token或验证码已过期": "Token或验证码已过期",
    "手机号已存在": "手机号已存在",
    "无效的手机号码": "无效的手机号码",
    "密码不能为空": "密码不能为空",
    "系统拦截，请不要批量注册账户或企图使用非法手段攻击本站": "系统拦截，请不要批量注册账户或企图使用非法手段攻击本站",
    "登录成功": "登录成功",
    "账号或密码错误": "账号或密码错误",
    "该账号不存在": "该账号不存在",
    "验证码已过期，请重新获取": "验证码已过期，请重新获取",
    "密码重置成功": "密码重置成功",
    "参数错误": "参数错误",
    "用户名长度不能超过10个字符": "用户名长度不能超过10个字符",
    "邮箱已存在": "邮箱已存在",
    "Email service not configured": "邮件服务未配置",
    "Please wait 5 minutes before requesting another code.": "请等待5分钟后再请求验证码",
    "Failed to send verification code": "验证码发送失败",
    "Email service not available": "邮件服务不可用",
    "System error while sending verification code.": "发送验证码时系统错误",
    "验证码不能为空": "验证码不能为空",
    "缺少验证码标识": "缺少验证码标识",
    "邮箱地址无效": "邮箱地址无效",
    "邮箱格式不正确": "邮箱格式不正确",
    "请先登录": "请先登录",
    "您被禁止修改昵称": "您被禁止修改昵称",
    "用户名长度必须大于2小于16": "用户名长度必须大于2小于16",
    "您被禁止修改头像": "您被禁止修改头像",
    "图片数据为空": "图片数据为空",
    "仅支持 jpeg, jpg, png 格式文件": "仅支持 jpeg, jpg, png 格式文件",
    "用户资料不存在": "用户资料不存在",
    "上传头像时发生错误": "上传头像时发生错误",
    "Token is required": "需要Token",
    "Token validation failed": "Token验证失败",
    "SMS service not configured": "短信服务未配置",
    "手机号已经被注册": "手机号已经被注册",
    "Failed to send verification code.": "验证码发送失败",
    "用户不存在": "用户不存在",
    "恭喜您通过了考试": "恭喜您通过了考试",
    "很遗憾，您没有通过规则考试，请重考": "很遗憾，您没有通过规则考试，请重考",
    "发生了错误": "发生了错误",
    "该邮箱不存在": "该邮箱不存在",
    "无效的验证类型": "无效的验证类型",
    "联系信息不能为空": "联系信息不能为空",
    "无效的手机号码格式": "无效的手机号码格式",
    "无效的邮箱格式": "无效的邮箱格式",
    "无效的客户端": "无效的客户端",
    "网站不允许": "网站不允许",
    "系统维护中": "系统维护中",
    "超出每日令牌限制": "超出每日令牌限制",
    "同一IP请求过多不同联系方式": "同一IP请求过多不同联系方式",
    "令牌生成成功": "令牌生成成功",
    "发生异常": "发生异常",
    "验证码参数不完整": "验证码参数不完整",
    "验证码已过期，请刷新": "验证码已过期，请刷新",
    "验证成功": "验证成功",

    # 更多字段名称翻译
    "余额": "余额",
    "总充值余额": "总充值余额",
    "昵称": "昵称",
    "该等级箱子开启超过限制": "该等级箱子开启超过限制",
    "Domain": "域名",
    "registered ip": "注册IP",
    "login ip": "登录IP",
    "login domain": "登录域名",
    "note": "备注",
    "is vip": "是否VIP",
    "用户锁定原因": "用户锁定原因",
    "ip": "IP地址",
    "domain": "域名",
    "suffix": "后缀",
    "用户导出EXCEL": "用户导出EXCEL",
    "余额记录导出EXCEL": "余额记录导出EXCEL",
    "ZBT取回": "ZBT取回",
    "请在 会员中心>账户设置 提交您的交易链接后再试": "请在 会员中心>账户设置 提交您的交易链接后再试",
    "系统接口维护中，请稍后再试": "系统接口维护中，请稍后再试",
    "提现数量超过最大限制": "提现数量超过最大限制",
    "饰品不存在或不可提取": "饰品不存在或不可提取",
    "此饰品只能出售": "此饰品只能出售",
    "取回数量超过限制": "取回数量超过限制",
    "请选择提取饰品": "请选择提取饰品",
    "An error occurred during the process.": "处理过程中发生错误",
    "扎比特购买失败请稍后重试.": "扎比特购买失败请稍后重试",
    "Sync failed not search b2c account": "同步失败，未找到B2C账户",
    "扎比特取回记录导出Excel": "扎比特取回记录导出Excel",
    "取回权限被限制，请联系在线客服。": "取回权限被限制，请联系在线客服",
    "该轮游戏已结束，请更换盒子": "该轮游戏已结束，请更换盒子",
    "位置错误，请重新选择": "位置错误，请重新选择",
    "该位置已开启": "该位置已开启",
    "BlindBox": "盲盒",
    "BlindBoxDrop": "盲盒掉落",
    "random seed": "随机种子",
    "BlindBoxGame": "盲盒游戏",
    "index": "索引",
    "Blind Box Record": "盲盒记录",
    "期望价格": "期望价格",
    "箱子正在维护中，请稍后再试1": "箱子正在维护中，请稍后再试",
    "箱子正在维护中，请稍后再试2": "箱子正在维护中，请稍后再试",
    "余额不足": "余额不足",
    "箱子没有配置掉落物品": "箱子没有配置掉落物品",
    "开箱过程中发生错误，请重试": "开箱过程中发生错误，请重试",
    "对战功能需要至少一件饰品有效提取才能自动开通，请先提取后再试。": "对战功能需要至少一件饰品有效提取才能自动开通，请先提取后再试",
    "对战具有一定的残酷性，禁止超过账户余额的一半金额创建房间。": "对战具有一定的残酷性，禁止超过账户余额的一半金额创建房间",
    "创建对战": "创建对战",
    "对战具有一定的残酷性，禁止超过账户余额的一半金额参加对战。": "对战具有一定的残酷性，禁止超过账户余额的一半金额参加对战",
    "对战": "对战",
    "房主不能退出房间，请使用解散功能": "房主不能退出房间，请使用解散功能",
    "只有房主可以解散房间": "只有房主可以解散房间",
    "Case room dismissed by owner": "房间已被房主解散",
    "Type Id": "类型ID",
    "Type Name": "类型名称",
    "Cate Id": "分类ID",
    "is show": "是否显示",
    "标签颜色": "标签颜色",
    "SEO标题": "SEO标题",
    "SEO关键词": "SEO关键词",
    "SEO描述": "SEO描述",
    "开箱概率": "开箱概率",
    "GiveawayItems": "赠品物品",
    "导出EXCEL": "导出EXCEL",
    "箱子掉落导出EXCEL": "箱子掉落导出EXCEL",
    "成功": "成功",
    "搜索失败": "搜索失败",
    "服务器异常": "服务器异常",
    "Access denied": "访问被拒绝",
    "充值金额必须为整数": "充值金额必须为整数",
    "充值通道维护": "充值通道维护",
    "防沉迷系统提示：您今日充值总额已超出每日充值限制，请明日再试。": "防沉迷系统提示：您今日充值总额已超出每日充值限制，请明日再试",
    "支付请求失败": "支付请求失败",
    "金额错误": "金额错误",
    "charge system api error": "充值系统API错误",
    "pay method not found": "支付方式未找到",
    "防沉迷系统提示：您今日充值总额已超出每日充值限制，请明日再试": "防沉迷系统提示：您今日充值总额已超出每日充值限制，请明日再试",
    "无效的充值金额": "无效的充值金额",
    "Other Pay": "其他支付",
    "nonce": "随机数",
    "充值赠送金额配置": "充值赠送金额配置",
    "suggest": "建议",
    "daily limit": "每日限制",
    "single limit": "单次限制",
    "rate": "比率",
    "PayMethod": "支付方式",
    "充值记录导出为Excel": "充值记录导出为Excel",
    "充值等级导出为Excel": "充值等级导出为Excel",
    "充值日统计导出为Excel": "充值日统计导出为Excel",
    "用户等级重置": "用户等级重置",
    "请进入会员中心先通过规则考试后再充值": "请进入会员中心先通过规则考试后再充值",
    "不支持的支付方式": "不支持的支付方式",
    "接口维护中": "接口维护中",

    # 剩余条目翻译
    "Message not found or permission denied": "消息未找到或权限被拒绝",
    "Message marked as read": "消息已标记为已读",
    "Sender": "发送者",
    "Sender Name": "发送者姓名",
    "Recipient": "接收者",
    "Recipient Name": "接收者姓名",
    "Subject": "主题",
    "Body": "内容",
    "Sent At": "发送时间",
    "Read At": "阅读时间",
    "hash": "哈希值",
    "Quantity must be greater than 2": "数量必须大于2",
    "口令不存在": "口令不存在",
    "该红包未开始领取或已过期": "该红包未开始领取或已过期",
    "不满足条件": "不满足条件",
    "该口令红包已领取": "该口令红包已领取",
    "该红包已抢光": "该红包已抢光",
    "口令红包": "口令红包",
    "准备中": "准备中",
    "未开始": "未开始",
    "已开始": "已开始",
    "已结束": "已结束",
    "有限库存": "有限库存",
    "红包记录导出Excel": "红包记录导出Excel",
    "系统维护中，请稍等再试": "系统维护中，请稍等再试",
    "Room is not Joinable": "房间无法加入",
    "user max choice": "用户最大选择",
    "coins to one card": "金币兑换卡片",
    "position last": "最后位置",
    "grab position": "抢夺位置",
    "sort": "排序",
    "默认图片": "默认图片",
    "自定义图片": "自定义图片",
    "自定义点亮图片": "自定义点亮图片",
    "拉货维护中": "拉货维护中",
    "余额不足, 请充值": "余额不足，请充值",
    "一些饰品不存在或已被出售": "一些饰品不存在或已被出售",
    "出售饰品": "出售饰品",
    "没有可出售的饰品": "没有可出售的饰品",
    "物品ID不能为空": "物品ID不能为空",
    "没有找到该饰品": "没有找到该饰品",
    "蓝色": "蓝色",
    "灰色": "灰色",
    "粉色": "粉色",
    "红色": "红色",
    "金色": "金色",
    "紫色": "紫色",
    "深蓝色": "深蓝色",
    "Is Show": "是否显示",
    "Icon": "图标",
    "Rarity Name": "稀有度名称",
    "Giveaway": "赠品",
    "背包饰品导出EXCEL": "背包饰品导出EXCEL",
    "饰品售出子系统正在维护，请稍后再试。": "饰品售出子系统正在维护，请稍后再试",
    "邀请人数不足": "邀请人数不足",
    "您的收益不足5B": "您的收益不足5B",
    "推广记录导出Excel": "推广记录导出Excel",
    "存入数量必须大于获胜人数": "存入数量必须大于获胜人数",
    "充值金额小于参加条件": "充值金额小于参加条件",
    "Failed to update balance": "更新余额失败",
    "发生错误": "发生错误",
    "存入数量必须等于获胜人数": "存入数量必须等于获胜人数",
    "主播": "主播",
    "官方": "官方",
    "official": "官方",
    "debug": "调试",
    "月度福利板块正在维护": "月度福利板块正在维护",
    "福利房间维护中，请稍后再试": "福利房间维护中，请稍后再试",
    "PC": "电脑",
    "Mobile": "手机",
    "is simple": "是否简单",
    "background class": "背景类",
    "glow class": "发光类",
    "primary button text": "主按钮文本",
    "primary button link": "主按钮链接",
    "secondary button text": "次按钮文本",
    "secondary button link": "次按钮链接",
    "Keywords": "关键词",
    "ICP": "ICP备案",
    "IP": "IP地址",
    "Forgotten your password or username?": "忘记密码或用户名？",
    "WXP取回": "WXP取回",
    "您需累计充值15$才能提取饰品": "您需累计充值15$才能提取饰品",
    "Over withdraw price": "超过提现价格",
    "success": "成功",

    # 剩余条目翻译
    "Message not found or permission denied": "消息未找到或权限被拒绝",
    "Message marked as read": "消息已标记为已读",
    "Sender": "发送者",
    "Sender Name": "发送者姓名",
    "Recipient": "接收者",
    "Recipient Name": "接收者姓名",
    "Subject": "主题",
    "Body": "内容",
    "Sent At": "发送时间",
    "Read At": "阅读时间",
    "hash": "哈希值",
    "Quantity must be greater than 2": "数量必须大于2",
    "口令不存在": "口令不存在",
    "该红包未开始领取或已过期": "该红包未开始领取或已过期",
    "不满足条件": "不满足条件",
    "该口令红包已领取": "该口令红包已领取",
    "该红包已抢光": "该红包已抢光",
    "口令红包": "口令红包",
    "准备中": "准备中",
    "未开始": "未开始",
    "已开始": "已开始",
    "已结束": "已结束",
    "有限库存": "有限库存",
    "红包记录导出Excel": "红包记录导出Excel",
    "系统维护中，请稍等再试": "系统维护中，请稍等再试",
    "Room is not Joinable": "房间无法加入",
    "user max choice": "用户最大选择",
    "coins to one card": "金币兑换卡片",
    "position last": "最后位置",
    "grab position": "抢夺位置",
    "sort": "排序",
    "默认图片": "默认图片",
    "自定义图片": "自定义图片",
    "自定义点亮图片": "自定义点亮图片",
    "拉货维护中": "拉货维护中",
    "余额不足, 请充值": "余额不足，请充值",
    "一些饰品不存在或已被出售": "一些饰品不存在或已被出售",
    "出售饰品": "出售饰品",
    "没有可出售的饰品": "没有可出售的饰品",
    "物品ID不能为空": "物品ID不能为空",
    "没有找到该饰品": "没有找到该饰品",
    "蓝色": "蓝色",
    "灰色": "灰色",
    "粉色": "粉色",
    "红色": "红色",
    "金色": "金色",
    "紫色": "紫色",
    "深蓝色": "深蓝色",
    "Is Show": "是否显示",
    "Icon": "图标",
    "Rarity Name": "稀有度名称",
    "Giveaway": "赠品",
    "背包饰品导出EXCEL": "背包饰品导出EXCEL",
    "饰品售出子系统正在维护，请稍后再试。": "饰品售出子系统正在维护，请稍后再试",
    "邀请人数不足": "邀请人数不足",
    "您的收益不足5B": "您的收益不足5B",
    "推广记录导出Excel": "推广记录导出Excel",
    "存入数量必须大于获胜人数": "存入数量必须大于获胜人数",
    "充值金额小于参加条件": "充值金额小于参加条件",
    "Failed to update balance": "更新余额失败",
    "发生错误": "发生错误",
    "存入数量必须等于获胜人数": "存入数量必须等于获胜人数",
    "主播": "主播",
    "官方": "官方",
    "official": "官方",
    "debug": "调试",
    "月度福利板块正在维护": "月度福利板块正在维护",
    "福利房间维护中，请稍后再试": "福利房间维护中，请稍后再试",
    "PC": "电脑",
    "Mobile": "手机",
    "is simple": "是否简单",
    "background class": "背景类",
    "glow class": "发光类",
    "primary button text": "主按钮文本",
    "primary button link": "主按钮链接",
    "secondary button text": "次按钮文本",
    "secondary button link": "次按钮链接",
    "Keywords": "关键词",
    "ICP": "ICP备案",
    "IP": "IP地址",
    "Forgotten your password or username?": "忘记密码或用户名？",
    "WXP取回": "WXP取回",
    "您需累计充值15$才能提取饰品": "您需累计充值15$才能提取饰品",
    "Over withdraw price": "超过提现价格",
    "success": "成功",
    
    # 操作相关
    "Add": "添加",
    "Edit": "编辑",
    "Delete": "删除",
    "Update": "更新",
    "Create": "创建",
    "Save": "保存",
    "Cancel": "取消",
    "Submit": "提交",
    "Search": "搜索",
    "Filter": "筛选",
    "Sort": "排序",
    "Export": "导出",
    "Import": "导入",
    "Download": "下载",
    "Upload": "上传",
    
    # 业务相关
    "Order": "订单",
    "Orders": "订单",
    "Payment": "支付",
    "Charge": "充值",
    "Withdraw": "提现",
    "Transaction": "交易",
    "Item": "物品",
    "Items": "物品",
    "Product": "产品",
    "Products": "产品",
    "Case": "箱子",
    "Cases": "箱子",
    "Box": "盒子",
    "Boxes": "盒子",
    "Skin": "皮肤",
    "Skins": "皮肤",
    "Weapon": "武器",
    "Weapons": "武器",

    # 游戏相关字段
    "Dota2": "Dota2",
    "CSGO": "CSGO",
    "PUBG": "PUBG",
    "H1Z1": "H1Z1",
    "item info": "饰品信息",
    "price": "价格",
    "zbt price": "扎比特价格",
    "unlimited": "未受限",
    "create time": "创建时间",
    "update time": "更新时间",
    "count": "数量",
    "buy price": "购买价格",
    "trade status": "交易状态",
    "zbt trade status": "扎比特交易状态",
    "accept time": "接受时间",
    "appid": "appid",
    "assetid": "assetid",
    "zbt order id": "扎比特订单号",
    "trade No.": "交易编号",
    "out trade no": "交易编号",
    "trade source": "来源",
    "package item": "饰品背包",
    "is extract": "是否提取",
    "error msg": "错误信息",
    "seller nickname": "昵称",
    "buyer": "买家",
    "expire time": "过期时间",
    "item type": "饰品种类",
    "customid": "自定义ID",
    "test amount": "测试金额",
    "name": "名字",
    "category": "类别",
    "order": "排序",
    "key": "钥匙",
    "discount(%)": "折扣（%）",
    "Normal": "正常",
    "Top": "顶级",
    "Free": "免费",
    "Festival": "节日",
    "FreeGive": "白给",
    "market hash name": "市场hash名称",
    "weapon": "武器",
    "icon url": "图标链接",
    "rarity": "稀有性",
    "item rarity cn": "稀有度",
    "item exterior cn": "品质",
    "item dark gold": "暗金",
    
    # 状态相关
    "Pending": "待处理",
    "Processing": "处理中",
    "Completed": "已完成",
    "Cancelled": "已取消",
    "Approved": "已批准",
    "Rejected": "已拒绝",
    "Published": "已发布",
    "Draft": "草稿",
    "Hidden": "隐藏",
    "Visible": "可见",
    
    # 其他常用词
    "Total": "总计",
    "Count": "数量",
    "Limit": "限制",
    "Rate": "比率",
    "Percentage": "百分比",
    "Level": "等级",
    "Rank": "排名",
    "Score": "分数",
    "Point": "积分",
    "Points": "积分",
    "Coin": "金币",
    "Coins": "金币",
    "Credit": "信用",
    "Credits": "信用",
    "Bonus": "奖励",
    "Reward": "奖励",
    "Gift": "礼品",
    "Prize": "奖品",
    "Winner": "获胜者",
    "Loser": "失败者",
    "Game": "游戏",
    "Games": "游戏",
    "Room": "房间",
    "Rooms": "房间",
    "Battle": "对战",
    "Battles": "对战",
    "Match": "匹配",
    "Matches": "匹配",
    "Round": "回合",
    "Rounds": "回合",
    
    # 技术相关
    "API": "接口",
    "URL": "链接",
    "Link": "链接",
    "Image": "图片",
    "File": "文件",
    "Files": "文件",
    "Folder": "文件夹",
    "Directory": "目录",
    "Path": "路径",
    "Size": "大小",
    "Format": "格式",
    "Version": "版本",
    "Build": "构建",
    "Release": "发布",
    "Debug": "调试",
    "Test": "测试",
    "Production": "生产",
    "Development": "开发",
    
    # 时间相关
    "Today": "今天",
    "Yesterday": "昨天",
    "Tomorrow": "明天",
    "Week": "周",
    "Month": "月",
    "Year": "年",
    "Hour": "小时",
    "Minute": "分钟",
    "Second": "秒",
    "Daily": "每日",
    "Weekly": "每周",
    "Monthly": "每月",
    "Yearly": "每年",
    
    # 权限相关
    "Permission": "权限",
    "Permissions": "权限",
    "Role": "角色",
    "Roles": "角色",
    "Group": "组",
    "Groups": "组",
    "Access": "访问",
    "Denied": "拒绝",
    "Allowed": "允许",
    "Forbidden": "禁止",
    "Authorized": "已授权",
    "Unauthorized": "未授权",
}

def translate_po_file(po_file_path):
    """
    翻译 .po 文件中的空翻译条目
    """
    if not os.path.exists(po_file_path):
        print(f"文件不存在: {po_file_path}")
        return
    
    with open(po_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 统计翻译前的空条目数量
    empty_msgstr_count = len(re.findall(r'msgstr ""', content))
    print(f"翻译前空翻译条目数量: {empty_msgstr_count}")
    
    # 进行翻译替换
    translated_count = 0
    for english, chinese in TRANSLATION_MAP.items():
        # 匹配模式：msgid "English" 后面跟着 msgstr ""
        pattern = rf'(msgid "{re.escape(english)}"\s*msgstr) ""'
        replacement = rf'\1 "{chinese}"'
        
        new_content, count = re.subn(pattern, replacement, content, flags=re.MULTILINE)
        if count > 0:
            content = new_content
            translated_count += count
            print(f"翻译了 '{english}' -> '{chinese}' ({count} 处)")
    
    # 统计翻译后的空条目数量
    empty_msgstr_count_after = len(re.findall(r'msgstr ""', content))
    
    # 写回文件
    with open(po_file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"\n翻译完成!")
    print(f"总共翻译了 {translated_count} 个条目")
    print(f"翻译后剩余空翻译条目数量: {empty_msgstr_count_after}")
    print(f"减少了 {empty_msgstr_count - empty_msgstr_count_after} 个空条目")

if __name__ == "__main__":
    po_file = "locale/zh_hans/LC_MESSAGES/django.po"
    translate_po_file(po_file)
