<template>
  <div class="min-h-screen relative">
    <!-- 背景层 -->
    <div class="absolute inset-0 overflow-hidden">
      <div class="absolute inset-0 bg-gradient-to-b from-background-darker via-background to-background-dark z-0" />
      <div class="particle-container absolute inset-0 z-0" />
      <div class="absolute top-0 left-0 w-full h-full overflow-hidden opacity-15 z-0">
        <div class="light-beam light-beam-1" />
        <div class="light-beam light-beam-3" />
        <div class="light-beam light-beam-2" />
      </div>
      <div class="absolute inset-0 bg-grid-pattern opacity-[0.04] z-0" />
    </div>

    <!-- PC版内容 -->
    <div class="container mx-auto px-4 py-6 relative z-10" v-if="!isMobile">
      <div class="max-w-6xl mx-auto">
        <!-- 页面头部 -->
        <div class="page-header mb-12">
          <h1 class="text-4xl font-bold text-white mb-4">{{ $t('recharge.title') }}</h1>
          <p class="text-gray-300 text-lg">{{ $t('recharge.description') }}</p>
        </div>

        <!-- 余额信息 -->
        <div class="balance-section mb-8">
          <div class="bg-white/5 backdrop-blur-md border border-white/10 rounded-2xl p-8">
            <div class="flex items-center justify-between">
              <div>
                <h2 class="text-2xl font-semibold text-white mb-2">{{ $t('recharge.current_balance') }}</h2>
                <div class="text-4xl font-bold text-primary">${{ (userBalance || 0).toFixed(2) }}</div>
              </div>
              <div class="text-right">
                <div class="text-gray-300 text-sm mb-1">{{ $t('recharge.last_recharge') }}</div>
                <div class="text-white">{{ lastRechargeDate }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 充值套餐 -->
        <div class="packages-section mb-8">
          <h2 class="text-2xl font-semibold text-white mb-6">{{ $t('recharge.packages.title') }}</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div
              v-for="pkg in packages"
              :key="pkg.id"
              @click="selectPackage(pkg)"
              :class="[
                'package-card cursor-pointer transition-all duration-300 rounded-2xl p-6 border-2',
                selectedPackage?.id === pkg.id
                  ? 'bg-primary/20 border-primary shadow-lg shadow-primary/25'
                  : 'bg-white/5 border-white/10 hover:bg-white/10 hover:border-white/20'
              ]"
            >
              <div class="text-center">
                <div class="text-3xl font-bold text-white mb-2">${{ pkg.amount }}</div>
                <div class="text-gray-300 mb-4">{{ $t(`recharge.packages.${pkg.id}.name`) }}</div>
                <div v-if="pkg.bonus" class="text-primary text-sm font-medium mb-2">
                  +{{ pkg.bonus }}% {{ $t('recharge.packages.bonus') }}
                </div>
                <div class="text-xs text-gray-400">{{ $t(`recharge.packages.${pkg.id}.description`) }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 支付方式 -->
        <div class="payment-section mb-8">
          <h2 class="text-2xl font-semibold text-white mb-6">{{ $t('recharge.payment.title') }}</h2>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div
              v-for="method in paymentMethods"
              :key="method.id"
              @click="selectPaymentMethod(method)"
              :class="[
                'payment-method cursor-pointer transition-all duration-300 rounded-2xl p-6 border-2',
                selectedPaymentMethod?.id === method.id
                  ? 'bg-primary/20 border-primary shadow-lg shadow-primary/25'
                  : 'bg-white/5 border-white/10 hover:bg-white/10 hover:border-white/20'
              ]"
            >
              <div class="flex items-center space-x-4">
                <i :class="`${method.icon} w-8 h-8 text-white`" />
                <div>
                  <div class="text-white font-medium">{{ $t(`recharge.payment.${method.id}.name`) }}</div>
                  <div class="text-gray-400 text-sm">{{ $t(`recharge.payment.${method.id}.description`) }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 充值按钮 -->
        <div class="recharge-action mb-8" v-if="selectedPackage && selectedPaymentMethod">
          <div class="bg-white/5 backdrop-blur-md border border-white/10 rounded-2xl p-8 text-center">
            <div class="text-2xl font-semibold text-white mb-4">
              {{ $t('recharge.total_amount') }}: ${{ (totalAmount || 0).toFixed(2) }}
            </div>
            <button
              @click="processRecharge"
              :disabled="isProcessing"
              class="bg-primary hover:bg-primary-dark text-white font-semibold py-4 px-8 rounded-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span v-if="!isProcessing">{{ $t('recharge.process_button') }}</span>
              <span v-else class="flex items-center space-x-2">
                <i class="i-heroicons-arrow-path w-5 h-5 animate-spin" />
                <span>{{ $t('recharge.processing') }}</span>
              </span>
            </button>
          </div>
        </div>

        <!-- 充值历史 -->
        <div class="history-section">
          <h2 class="text-2xl font-semibold text-white mb-6">{{ $t('recharge.history.title') }}</h2>
          <div class="bg-white/5 backdrop-blur-md border border-white/10 rounded-2xl overflow-hidden">
            <div class="overflow-x-auto">
              <table class="w-full">
                <thead class="bg-white/10">
                  <tr>
                    <th class="px-6 py-4 text-left text-white font-medium">{{ $t('recharge.history.date') }}</th>
                    <th class="px-6 py-4 text-left text-white font-medium">{{ $t('recharge.history.amount') }}</th>
                    <th class="px-6 py-4 text-left text-white font-medium">{{ $t('recharge.history.method') }}</th>
                    <th class="px-6 py-4 text-left text-white font-medium">{{ $t('recharge.history.status') }}</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    v-for="record in rechargeHistory"
                    :key="record.id"
                    class="border-t border-white/10 hover:bg-white/5 transition-colors duration-200"
                  >
                    <td class="px-6 py-4 text-gray-300">{{ formatDate(record.date) }}</td>
                    <td class="px-6 py-4 text-white font-medium">${{ (record.amount || 0).toFixed(2) }}</td>
                    <td class="px-6 py-4 text-gray-300">{{ $t(`recharge.payment.${record.method}.name`) }}</td>
                    <td class="px-6 py-4">
                      <span
                        :class="[
                          'px-3 py-1 rounded-full text-xs font-medium',
                          record.status === 'completed' ? 'bg-green-500/20 text-green-400' :
                          record.status === 'pending' ? 'bg-yellow-500/20 text-yellow-400' :
                          'bg-red-500/20 text-red-400'
                        ]"
                      >
                        {{ $t(`recharge.history.status_${record.status}`) }}
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 移动版内容 -->
    <div class="container mx-auto px-4 py-4 relative z-10" v-else>
      <div class="max-w-4xl mx-auto">
        <!-- 页面头部 -->
        <div class="page-header mb-8">
          <h1 class="text-2xl font-bold text-white mb-3">{{ $t('recharge.title') }}</h1>
          <p class="text-gray-300 text-sm">{{ $t('recharge.description') }}</p>
        </div>

        <!-- 余额信息 -->
        <div class="balance-section mb-6">
          <div class="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-6">
            <div class="text-center">
              <h2 class="text-lg font-semibold text-white mb-2">{{ $t('recharge.current_balance') }}</h2>
              <div class="text-3xl font-bold text-primary">${{ (userBalance || 0).toFixed(2) }}</div>
              <div class="text-gray-300 text-xs mt-2">
                {{ $t('recharge.last_recharge') }}: {{ lastRechargeDate }}
              </div>
            </div>
          </div>
        </div>

        <!-- 充值套餐 -->
        <div class="packages-section mb-6">
          <h2 class="text-xl font-semibold text-white mb-4">{{ $t('recharge.packages.title') }}</h2>
          <div class="grid grid-cols-2 gap-3">
            <div
              v-for="pkg in packages"
              :key="pkg.id"
              @click="selectPackage(pkg)"
              :class="[
                'package-card cursor-pointer transition-all duration-300 rounded-xl p-4 border-2 text-center',
                selectedPackage?.id === pkg.id
                  ? 'bg-primary/20 border-primary shadow-lg shadow-primary/25'
                  : 'bg-white/5 border-white/10 hover:bg-white/10 hover:border-white/20'
              ]"
            >
              <div class="text-2xl font-bold text-white mb-1">${{ pkg.amount }}</div>
              <div class="text-gray-300 text-sm mb-2">{{ $t(`recharge.packages.${pkg.id}.name`) }}</div>
              <div v-if="pkg.bonus" class="text-primary text-xs font-medium">
                +{{ pkg.bonus }}% {{ $t('recharge.packages.bonus') }}
              </div>
            </div>
          </div>
        </div>

        <!-- 支付方式 -->
        <div class="payment-section mb-6">
          <h2 class="text-xl font-semibold text-white mb-4">{{ $t('recharge.payment.title') }}</h2>
          <div class="space-y-3">
            <div
              v-for="method in paymentMethods"
              :key="method.id"
              @click="selectPaymentMethod(method)"
              :class="[
                'payment-method cursor-pointer transition-all duration-300 rounded-xl p-4 border-2',
                selectedPaymentMethod?.id === method.id
                  ? 'bg-primary/20 border-primary shadow-lg shadow-primary/25'
                  : 'bg-white/5 border-white/10 hover:bg-white/10 hover:border-white/20'
              ]"
            >
              <div class="flex items-center space-x-3">
                <i :class="`${method.icon} w-6 h-6 text-white`" />
                <div>
                  <div class="text-white font-medium text-sm">{{ $t(`recharge.payment.${method.id}.name`) }}</div>
                  <div class="text-gray-400 text-xs">{{ $t(`recharge.payment.${method.id}.description`) }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 充值按钮 -->
        <div class="recharge-action mb-6" v-if="selectedPackage && selectedPaymentMethod">
          <div class="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-6 text-center">
            <div class="text-xl font-semibold text-white mb-3">
              {{ $t('recharge.total_amount') }}: ${{ (totalAmount || 0).toFixed(2) }}
            </div>
            <button
              @click="processRecharge"
              :disabled="isProcessing"
              class="w-full bg-primary hover:bg-primary-dark text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span v-if="!isProcessing">{{ $t('recharge.process_button') }}</span>
              <span v-else class="flex items-center justify-center space-x-2">
                <i class="i-heroicons-arrow-path w-4 h-4 animate-spin" />
                <span>{{ $t('recharge.processing') }}</span>
              </span>
            </button>
          </div>
        </div>

        <!-- 充值历史 -->
        <div class="history-section">
          <h2 class="text-xl font-semibold text-white mb-4">{{ $t('recharge.history.title') }}</h2>
          <div class="space-y-3">
            <div
              v-for="record in rechargeHistory"
              :key="record.id"
              class="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-4"
            >
              <div class="flex justify-between items-center mb-2">
                <div class="text-white font-medium">${{ (record.amount || 0).toFixed(2) }}</div>
                <span
                  :class="[
                    'px-2 py-1 rounded-full text-xs font-medium',
                    record.status === 'completed' ? 'bg-green-500/20 text-green-400' :
                    record.status === 'pending' ? 'bg-yellow-500/20 text-yellow-400' :
                    'bg-red-500/20 text-red-400'
                  ]"
                >
                  {{ $t(`recharge.history.status_${record.status}`) }}
                </span>
              </div>
              <div class="flex justify-between text-gray-300 text-sm">
                <span>{{ formatDate(record.date) }}</span>
                <span>{{ $t(`recharge.payment.${record.method}.name`) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 国际化
const { t } = useI18n()

// 设备检测 - 使用响应式状态
const isMobile = ref(false)

// 在客户端检测设备类型
onMounted(() => {
  const checkMobile = () => {
    isMobile.value = window.innerWidth < 768
  }
  
  checkMobile()
  window.addEventListener('resize', checkMobile)
  
  onUnmounted(() => {
    window.removeEventListener('resize', checkMobile)
  })
})

// SEO配置
useSeoMeta({
  title: () => t('recharge.seo.title'),
  description: () => t('recharge.seo.description'),
  keywords: () => t('recharge.seo.keywords'),
  ogTitle: () => t('recharge.seo.title'),
  ogDescription: () => t('recharge.seo.description'),
  twitterTitle: () => t('recharge.seo.title'),
  twitterDescription: () => t('recharge.seo.description')
})

// 响应式数据
const userBalance = ref(1250.50)
const lastRechargeDate = ref('2024-01-15')
const selectedPackage = ref<any>(null)
const selectedPaymentMethod = ref<any>(null)
const isProcessing = ref(false)

// 充值套餐
const packages = [
  { id: 'basic', amount: 10, bonus: 0, name: 'recharge.packages.basic.name', description: 'recharge.packages.basic.description' },
  { id: 'standard', amount: 25, bonus: 5, name: 'recharge.packages.standard.name', description: 'recharge.packages.standard.description' },
  { id: 'premium', amount: 50, bonus: 10, name: 'recharge.packages.premium.name', description: 'recharge.packages.premium.description' },
  { id: 'vip', amount: 100, bonus: 15, name: 'recharge.packages.vip.name', description: 'recharge.packages.vip.description' }
]

// 支付方式
const paymentMethods = [
  { id: 'credit_card', icon: 'i-heroicons-credit-card', name: 'recharge.payment.credit_card.name', description: 'recharge.payment.credit_card.description' },
  { id: 'paypal', icon: 'i-simple-icons-paypal', name: 'recharge.payment.paypal.name', description: 'recharge.payment.paypal.description' },
  { id: 'crypto', icon: 'i-cryptocurrency-btc', name: 'recharge.payment.crypto.name', description: 'recharge.payment.crypto.description' }
]

// 充值历史
const rechargeHistory = ref([
  { id: 1, date: '2024-01-15', amount: 50, method: 'credit_card', status: 'completed' },
  { id: 2, date: '2024-01-10', amount: 25, method: 'paypal', status: 'completed' },
  { id: 3, date: '2024-01-05', amount: 100, method: 'crypto', status: 'pending' }
])

// 计算属性
const totalAmount = computed(() => {
  if (!selectedPackage.value) return 0
  const baseAmount = selectedPackage.value.amount
  const bonus = selectedPackage.value.bonus || 0
  return baseAmount * (1 + bonus / 100)
})

// 方法
const selectPackage = (pkg: any) => {
  selectedPackage.value = pkg
}

const selectPaymentMethod = (method: any) => {
  selectedPaymentMethod.value = method
}

const processRecharge = async () => {
  if (!selectedPackage.value || !selectedPaymentMethod.value) return
  
  isProcessing.value = true
  
  try {
    // 模拟充值处理
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 更新余额
    userBalance.value += totalAmount.value
    
    // 添加到历史记录
    rechargeHistory.value.unshift({
      id: Date.now(),
      date: new Date().toISOString().split('T')[0],
      amount: totalAmount.value,
      method: selectedPaymentMethod.value.id,
      status: 'completed'
    })
    
    // 重置选择
    selectedPackage.value = null
    selectedPaymentMethod.value = null
    
    // 显示成功消息
    console.log('充值成功')
  } catch (error) {
    console.error('充值失败:', error)
  } finally {
    isProcessing.value = false
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString()
}
</script>

<style scoped>
/* 粒子效果 */
.particle-container {
  background-image: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
}

/* 动态光线效果 */
.light-beam {
  position: absolute;
  width: 2px;
  height: 100%;
  background: linear-gradient(to bottom, transparent, var(--color-primary), transparent);
  animation: lightBeam 8s infinite linear;
}

.light-beam-1 {
  left: 10%;
  animation-delay: 0s;
}

.light-beam-2 {
  left: 50%;
  animation-delay: 2.5s;
}

.light-beam-3 {
  left: 90%;
  animation-delay: 5s;
}

@keyframes lightBeam {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(100%);
    opacity: 0;
  }
}

/* 网格背景 */
.bg-grid-pattern {
  background-image: linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
}

/* 页面头部样式 */
.page-header {
  text-align: center;
  position: relative;
}

.page-header::after {
  content: '';
  position: absolute;
  bottom: -1rem;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
  border-radius: 2px;
}

/* 套餐卡片样式 */
.package-card {
  transition: all 0.3s ease;
}

.package-card:hover {
  transform: translateY(-4px);
}

/* 支付方式样式 */
.payment-method {
  transition: all 0.3s ease;
}

.payment-method:hover {
  transform: translateY(-2px);
}

/* 表格样式 */
table th {
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .page-header h1 {
    font-size: 1.75rem;
  }
  
  .package-card {
    padding: 1rem;
  }
  
  .payment-method {
    padding: 1rem;
  }
}
</style> 