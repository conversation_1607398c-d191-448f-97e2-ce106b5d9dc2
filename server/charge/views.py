import logging

from django.contrib import messages
from django.http.response import HttpResponse
from django.shortcuts import redirect, reverse
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator

from rest_framework.views import APIView
from rest_framework.permissions import AllowAny
from rest_framework.parsers import FormParser, JSONParser
from rest_framework.response import Response
from rest_framework_xml.parsers import XMLParser

from steambase.enums import RespCode, PayType, ChargeState
from django.conf import settings

from .models import ChargeAmountConfig  # 确保导入路径正确


from steambase.utils import reformat_resp, current_user, get_ip
from sitecfg.interfaces import get_maintenance, get_maintenance_charge
# from charge.business import user_pay, confirm_pay_result, get_charge_status, get_charge_record, get_charge_week_rank
# from charge.business import user_card_pay
# from charge.service import alipay, wechat, lywangl
from charge.business import user_pay, get_charge_status, get_charge_record, get_charge_week_rank, get_charge_info, \
    user_alipay_pay, alipay_notify_return, alipay_notify, user_wexin_pay, wechat_notify, \
    get_user_level_rank, get_charge_config, user_hupi_pay, user_jiujia_pay, user_fuxinka_pay, user_umi_pay,  exchange_cdkey, user_nanshanka_pay, user_helloka_pay, get_charge_detail 
from charge.business import express_notify_result, user_cancel_pay, hpj_notify_result,  jiujia_notify_result, fuxinka_notify_result, snap_notify_result, \
    umi_notify_result, umi_notify_result, cxka_notify_result, nanshanka_notify_result, helloka_notify_result
from charge.business import get_cxka_cards_info, user_cxka_pay, check_pay, get_user_charge_status, get_gift_config, get_pay_method, user_alipay_pay_tyss, alipay_tyss_notify_return, alipay_tyss_notify, user_alipay_pay_ydsk, alipay_ydsk_notify_return, alipay_ydsk_notify, alipay_lyhs_notify_return, alipay_lyhs_notify, user_alipay_pay_lyhs, user_alipay_pay_general, alipay_xyt_notify, alipay_xyt_notify_return, user_alipay_pay_xyt, sahepay_notify_return, sahepay_notify, sahepay_pay, user_alipay_pay_sdwl, alipay_sdwl_notify, alipay_sdwl_notify_return, user_alipay_pay_wsxh, alipay_wsxh_notify, alipay_wsxh_notify_return, user_alipay_pay_hysj, alipay_hysj_notify_return, alipay_hysj_notify, user_alipay_pay_ysyl, alipay_ysyl_notify_return, alipay_ysyl_notify


_logger = logging.getLogger(__name__)


class UserPayView(APIView):

    def post(self, request):
        try:
            user = current_user(request)

            if get_maintenance_charge():
                return reformat_resp(RespCode.Maintenance.value, {},
                                     _('Charge is under maintenance, please wait for a while.'))
            # if not user.is_superuser:
            # return RespCode.InvalidParams.value, _('only superuse can charge in test')
            
            amount = request.data.get('amount', None)
            # currency = request.data.get('currency', 'USD')
            pay_type = request.data.get('pay_type', PayType.Wechat.value)
            ip = get_ip(request)
            code, resp = user_pay(user, amount, pay_type, ip)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class PayCxkaView(APIView):
    def post(self, request):
        try:
            user = current_user(request)

            if get_maintenance_charge():
                return reformat_resp(RespCode.Maintenance.value, {},
                                     _('Charge is under maintenance, please wait for a while.'))

            uid = request.data.get('uid', None)
            pay_type = request.data.get('pay_type', PayType.Wechat.value)
            count = request.data.get('count', None)
            is_mobile = request.data.get('is_mobile', 0)
            ip = get_ip(request)
            _logger.info(request.data)
            code, resp = user_cxka_pay(user, uid, int(pay_type), ip, is_mobile, int(count))
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetChargeStatusView(APIView):

    def get(self, request):
        try:
            user = current_user(request)

            out_trade_no = request.query_params.get('no', None)
            code, resp = get_charge_status(user, out_trade_no)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetChargeRecordView(APIView):

    def get(self, request):
        try:
            user = current_user(request)

            query = {
                'out_trade_no': request.query_params.get('out_trade_no', None),
                'pay_type': request.query_params.get('pay_type', None),
                'state': request.query_params.get('state', None),
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            fields = (
                'uid', 'out_trade_no', 'amount', 'currency', 'state', 'pay_type', 'pay_amount', 'pay_time', 'pay_url',
                'create_time')
            code, resp = get_charge_record(user, query, fields, page, page_size)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class UserCancelPayView(APIView):

    def post(self, request):
        try:
            user = current_user(request)
            if not user:
                return RespCode.InvalidParams.value, _('Invalid Params')

            if get_maintenance_charge():
                return reformat_resp(RespCode.Maintenance.value, {},
                                     _('Charge is under maintenance, please wait for a while.'))
            uid = request.data.get('uid', '')
            code, resp = user_cancel_pay(user, uid)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetChargeWeekRankView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:

            query = {
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            code, resp = get_charge_week_rank(10)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetChargeInfoView(APIView):

    def get(self, request):
        try:
            user = current_user(request)

            query = {
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            fields = ('user', 'amount', 'level', 'next_amount')
            code, resp = get_charge_info(user, fields)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class ExpressNotifyView(APIView):
    permission_classes = [AllowAny]
    parser_classes = (FormParser,)

    def post(self, request):
        try:
            data = request.data.dict()
            _logger.info('Express notify body: {}'.format(data))
            #     if data["trade_status"] in ("TRADE_SUCCESS", "TRADE_FINISHED"):
            #         out_trade_no = data['out_trade_no']
            res = express_notify_result(data)
            if res:
                return Response({'resultCode': 'success', 'resultMsg': 'success'})
            else:
                return Response('failed')
        except Exception as e:
            _logger.exception(e)
            return Response('failed')


class HPJNotifyView(APIView):
    permission_classes = [AllowAny]
    parser_classes = (FormParser,)

    def post(self, request):
        try:
            data = request.data.dict()
            _logger.info('HPj notify body: {}'.format(data))
            #     if data["trade_status"] in ("TRADE_SUCCESS", "TRADE_FINISHED"):
            #         out_trade_no = data['out_trade_no']
            res = hpj_notify_result(data)
            if res:
                return Response('success')
            else:
                return Response('failed')
        except Exception as e:
            _logger.exception(e)
            return Response('failed')

class FUXINKAPayView(APIView):
 
    permission_classes = [AllowAny]

    def post(self, request):
        try:
            if get_maintenance_charge():
                return reformat_resp(RespCode.Maintenance.value, {},
                                     _('充值渠道维护，具体恢复时间请查看网站公告。'))

            user = current_user(request)
            coins = request.data.get('coins', 0)
            charge_type = request.data.get('charge_type', PayType.Ali.value)
            currency = request.data.get('currency', 'CNY')
            code, resp = user_fuxinka_pay(user, coins, currency, int(charge_type))
            if code == RespCode.Succeed.value:
                return HttpResponse('1')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')
    
# class JIUJIANotifyView(APIView):
#     permission_classes = [AllowAny]

#     def post(self, request):
#         try:
#             data = request.data.dict()
#             _logger.info('Jjk notify body: {}'.format(data))
#             res = jiujia_notify_result(data)
#             if res:
#                 return Response(success)
#             else:
#                 return Response(fail)
#         except Exception as e:
#             _logger.exception(e)
#             return Response(fail)

class JIUJIANotifyView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        try:
            data = request.data.dict()
            _logger.info('Jjk notify body: {}'.format(data))
            res = jiujia_notify_result(data)
            if res:
                return HttpResponse('success')
                #return Response({'status': 'success'})
            else:
                return HttpResponse('fail')
                #return Response({'status': 'fail'})
        except Exception as e:
            _logger.exception(e)
            return HttpResponse('fail')
            #return Response({'status': 'fail'})


# class JIUJIANotifyView(APIView):
#     permission_classes = [AllowAny]

#     def post(self, request):
#         try:
#             data = request.data.dict()
#             _logger.info('Jjk notify body: {}'.format(data))
#             res = jiujia_notify_result(data)
#             if res:
#                 return Response(success)
#             else:
#                 return Response(fail)
#         except Exception as e:
#             _logger.exception(e)
#             return Response('failed')
class UMIPAYNotifyView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        try:
            data = request.data.dict()
            _logger.info('UMI notify body: {}'.format(data))
            res = umi_notify_result(data)
            if res:
                #return Response(success)
                return Response({'status': 'ok'})
            else:
                return Response({'status': 'failed'})
                #return Response('failed')
        except Exception as e:
            _logger.exception(e)
            return Response('failed')

class NANSHANKANotifyView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        try:
            data = request.data.dict()
            _logger.info('NANSHANKA notify body: {}'.format(data))
            res = nanshanka_notify_result(data)
            if res:
                return HttpResponse('ok')
            else:
                return HttpResponse('failed')
        except Exception as e:
            _logger.exception(e)
            return Response('failed')

class AllPayView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        try:
            user = current_user(request)

            if not user:
                return reformat_resp(RespCode.BadRequest.value, {}, _('用户不存在'))
            if not user.asset.tradeurl:
                return reformat_resp(RespCode.BadRequest.value, {}, _('您还没有设置steam链接，请在 会员中心>我的账户>账户设置 更新完善steam链接后再试'))
            if not user.extra.exam_passed:
                return reformat_resp(RespCode.BadRequest.value, {}, _('请进入会员中心先通过规则考试后再充值'))

            if get_maintenance_charge():
                return reformat_resp(RespCode.Maintenance.value, {}, _('充值渠道维护，具体恢复时间请查看网站公告。'))

            # 获取请求参数
            coins = request.data.get('coins', 0)
            paymethod = request.data.get('charge_type', 0)
            currency = request.data.get('currency', 'CNY')
            mobile = request.data.get('mobile', 0)
            ip = get_ip(request)

            # 验证充值金额配置（非管理员用户需要验证）
            if not ChargeAmountConfig.objects.filter(coins=coins, enable=True).exists() and not user.is_staff:
                return reformat_resp(RespCode.InvalidParams.value, {}, _('无效的充值金额'))

            # 支付方式映射
            pay_methods = {
                #1: self.pay_method_unsupported,
                2: user_alipay_pay,
                3: user_hupi_pay,
                4: user_alipay_pay_tyss,
                5: user_alipay_pay_ydsk,
                6: user_alipay_pay_lyhs,
                7: user_alipay_pay_xyt,
                8: sahepay_pay,
                9: user_alipay_pay_sdwl,
                10: user_alipay_pay_wsxh,
                11: user_alipay_pay_hysj,
                12: user_alipay_pay_ysyl,
            }

            # 获取支付函数
            pay_function = pay_methods.get(paymethod, self.pay_method_invalid)

            

            # 执行支付逻辑
            code, resp = pay_function(user, coins, ip, mobile, paymethod)

            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, 'Exception')

    @staticmethod
    def pay_method_invalid(*args, **kwargs):
        return RespCode.InvalidParams.value, _('不支持的支付方式')

    @staticmethod
    def pay_method_unsupported(*args, **kwargs):
        return RespCode.InvalidParams.value, _('接口维护中')

    





class FUXINKANotifyView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        try:
            data = request.data
            _logger.info('FUXINKA notify body: {}'.format(data))
            #     if data["trade_status"] in ("TRADE_SUCCESS", "TRADE_FINISHED"):
            #         out_trade_no = data['out_trade_no']
            res = fuxinka_notify_result(data)
            if res:
                return Response(1)
            else:
                return Response(0)
        except Exception as e:
            _logger.exception(e)
            return Response('failed')

class SNAPNotifyView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        try:
            data = request.data
            _logger.info('SNAP Pay notify body: {}'.format(data))
            #     if data["trade_status"] in ("TRADE_SUCCESS", "TRADE_FINISHED"):
            #         out_trade_no = data['out_trade_no']
            res = snap_notify_result(data)
            if res:
                return Response({"code": "0"})
            else:
                return Response('failed')
        except Exception as e:
            _logger.exception(e)
            return Response('failed')


class CxkaPayView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        try:
            data = request.data
            _logger.info('Cxka Pay notify body: {}'.format(data))
            #     if data["trade_status"] in ("TRADE_SUCCESS", "TRADE_FINISHED"):
            #         out_trade_no = data['out_trade_no']
            res = cxka_notify_result(data)
            if res:
                return Response('success')
            else:
                return Response('failed')
        except Exception as e:
            _logger.exception(e)
            return Response('fail')


class GetCxkaCardsInfoView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            fields = ('uid', 'good_id', 'good_name')
            code, resp = get_cxka_cards_info(fields)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class AlipayPayView(APIView):

    def post(self, request):
        try:

            if get_maintenance_charge():
                return reformat_resp(RespCode.Maintenance.value, {},
                                     _('充值渠道维护，具体恢复时间请查看网站公告。'))

            user = current_user(request)
            user_ip = get_ip(request)
            coins = request.data.get('coins', 0)
            mobile = request.data.get('mobile', 0)
            code, resp = user_alipay_pay(user, float(coins), user_ip, int(mobile))
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class AlipayNotifyView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            code, resp = alipay_notify_return(request)
            if code == RespCode.Succeed.value:
                return redirect('/')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')

    def post(self, request):
        try:
            code, resp = alipay_notify(request)
            if code == RespCode.Succeed.value:
                return HttpResponse('success')
            else:
                return HttpResponse('fail')
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')

class AlipayTyssNotifyView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            code, resp = alipay_tyss_notify_return(request)
            if code == RespCode.Succeed.value:
                return redirect('/')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')

    def post(self, request):
        try:
            code, resp = alipay_tyss_notify(request)
            if code == RespCode.Succeed.value:
                return HttpResponse('success')
            else:
                return HttpResponse('fail')
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')

class AlipayYdskNotifyView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            code, resp = alipay_ydsk_notify_return(request)
            if code == RespCode.Succeed.value:
                return redirect('/')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')

    def post(self, request):
        try:
            code, resp = alipay_ydsk_notify(request)
            if code == RespCode.Succeed.value:
                return HttpResponse('success')
            else:
                return HttpResponse('fail')
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')

class AlipayLyhsNotifyView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            code, resp = alipay_lyhs_notify_return(request)
            if code == RespCode.Succeed.value:
                return redirect('/')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')

    def post(self, request):
        try:
            code, resp = alipay_lyhs_notify(request)
            if code == RespCode.Succeed.value:
                return HttpResponse('success')
            else:
                return HttpResponse('fail')
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')
    
class AlipayXytNotifyView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            code, resp = alipay_xyt_notify_return(request)
            if code == RespCode.Succeed.value:
                return redirect('/')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')

    def post(self, request):
        try:
            code, resp = alipay_xyt_notify(request)
            if code == RespCode.Succeed.value:
                return HttpResponse('success')
            else:
                return HttpResponse('fail')
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')

class AlipaySdwlNotifyView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            code, resp = alipay_sdwl_notify_return(request)
            if code == RespCode.Succeed.value:
                return redirect('/')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')

    def post(self, request):
        try:
            # _logger.info('AlipaySdwlNotifyView+++++++')
            code, resp = alipay_sdwl_notify(request)
            if code == RespCode.Succeed.value:
                return HttpResponse('success')
            else:
                return HttpResponse('fail')
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')

class AlipayWsxhNotifyView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            code, resp = alipay_wsxh_notify_return(request)
            if code == RespCode.Succeed.value:
                return redirect('/')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')

    def post(self, request):
        try:
            code, resp = alipay_wsxh_notify(request)
            if code == RespCode.Succeed.value:
                return HttpResponse('success')
            else:
                return HttpResponse('fail')
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')

class AlipayHysjNotifyView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            code, resp = alipay_hysj_notify_return(request)
            if code == RespCode.Succeed.value:
                return redirect('/')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')

    def post(self, request):
        try:
            code, resp = alipay_hysj_notify(request)
            if code == RespCode.Succeed.value:
                return HttpResponse('success')
            else:
                return HttpResponse('fail')
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')

class AlipayYsylNotifyView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            code, resp = alipay_ysyl_notify_return(request)
            if code == RespCode.Succeed.value:
                return redirect('/')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')

    def post(self, request):
        try:
            code, resp = alipay_ysyl_notify(request)
            if code == RespCode.Succeed.value:
                return HttpResponse('success')
            else:
                return HttpResponse('fail')
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')

class WechatPayView(APIView):

    def post(self, request):
        try:
            if get_maintenance_charge():
                return reformat_resp(RespCode.Maintenance.value, {},
                                     _('充值渠道维护，具体恢复时间请查看网站公告。'))

            user = current_user(request)
            user_ip = get_ip(request)
            coins = request.data.get('coins', 0)
            mobile = request.data.get('mobile', 0)
            code, resp = user_wexin_pay(user, float(coins), user_ip, int(mobile))
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class WechatPaymentXMLParser(XMLParser):
    media_type = 'text/xml'


class WechatNotifyView(APIView):
    permission_classes = [AllowAny]
    parser_classes = (WechatPaymentXMLParser,)

    def post(self, request):
        try:
            code, resp = wechat_notify(request.data)
            if code == RespCode.Succeed.value:
                return HttpResponse('success')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class HPJPayView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        try:
            if get_maintenance_charge():
                return reformat_resp(RespCode.Maintenance.value, {},
                                     _('充值渠道维护，具体恢复时间请查看网站公告。'))

            user = current_user(request)
            coins = request.data.get('coins', 0)
            charge_type = request.data.get('charge_type', PayType.Ali.value)
            currency = request.data.get('currency', 'CNY')
            code, resp = user_hupi_pay(user, coins, currency, int(charge_type))
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')

class JIUJIAPayView(APIView):
 
    permission_classes = [AllowAny]

    def post(self, request):
        try:
            if get_maintenance_charge():
                return reformat_resp(RespCode.Maintenance.value, {},
                                     _('充值渠道维护，具体恢复时间请查看网站公告。'))

            user = current_user(request)
            coins = request.data.get('coins', 0)
            charge_type = request.data.get('charge_type', PayType.Ali.value)
            currency = request.data.get('currency', 'CNY')
            code, resp = user_jiujia_pay(user, coins, currency, int(charge_type))
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')

class GetUserLevelRankView(APIView):

    def get(self, request):
        try:
            user = current_user(request)
            code, resp = get_user_level_rank(10)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetChargeLevelConfigView(APIView):
    #permission_classes = [AllowAny]

    def get(self, request):
        try:
            user = current_user(request)
            code, resp = get_charge_config(user)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')

class GetNewUserGiftView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            code, resp = get_gift_config()
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class ExchangeCDKeyView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        try:
            user = current_user(request)
            key = request.data.get("key", "")
            code, resp = exchange_cdkey(user, key)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, "Succeed")
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')

class GetChargeDetailView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            user = current_user(request)

            out_trade_no = request.query_params.get('out_trade_no', None)
            code, resp = get_charge_detail(out_trade_no)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')
    
class HELLOPAYNotifyView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        try:
            data = request.data
            _logger.info('HELLOKA notify body: {}'.format(data))
            res = helloka_notify_result(data)
            if res:
                return HttpResponse('success')
            else:
                return HttpResponse('failed')
        except Exception as e:
            _logger.exception(e)
            return Response('failed')
        
class CHECKPAYView(APIView):    
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            out_trade_no = request.query_params.get('out_trade_no', None)    
            
            data = {
                 "api_key": settings.HELLOKA_PAY_KEY,
                 "order_id": out_trade_no
            }       
            res = check_pay(data)
            
            if res:
                return Response(res)
                #return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(res, {}, res)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')
    
    
class PayListView(APIView):
 
    # permission_classes = [AllowAny]
    def get(self, request):
        try:
            user = current_user(request)
            fields = ('id', 'name', 'enable', 'suggest', 'type', 'color', 'single_limit')
            code, resp = get_pay_method(user, fields)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')
       

@method_decorator(csrf_exempt, name='dispatch')
class SahePayNotifyView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            code, resp = sahepay_notify_return(request)
            if code == RespCode.Succeed.value:
                return redirect('/')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, 'Exception')

    def post(self, request):
        try:
            code, resp = sahepay_notify(request)
            if code == RespCode.Succeed.value:
                return HttpResponse('success')
            else:
                return HttpResponse('fail')
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')




