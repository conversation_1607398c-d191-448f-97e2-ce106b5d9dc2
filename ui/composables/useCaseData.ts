import { caseApi, type CaseDetailItem } from '~/services/case-api'
import type { CaseItem } from '~/types/case'
import { transformCaseItem, transformCaseItems } from '~/utils/case-transformer'

export const useCaseData = () => {
  const { locale } = useI18n()
  
  // 获取当前语言 - 使用响应式的locale
  const getCurrentLocale = (): string => {
    return locale.value || 'zh_hans'
  }

  /**
   * 转换单个case数据（在组件中使用）
   */
  const transformCase = (apiCase: CaseDetailItem): CaseItem => {
    return transformCaseItem(apiCase, getCurrentLocale())
  }

  /**
   * 批量转换case数据（在组件中使用）
   */
  const transformCases = (apiCases: CaseDetailItem[]): CaseItem[] => {
    return transformCaseItems(apiCases, getCurrentLocale())
  }

  /**
   * 获取热门箱子（组件级别使用）
   */
  const getHotCases = async (num: number = 10): Promise<{ data: CaseItem[], success: boolean, message: string }> => {
    try {
      console.log('[useCaseData] 开始调用热门箱子API，num:', num)
      const response = await caseApi.getHotCases({ num })
      
      console.log('[useCaseData] 热门箱子API原始响应:', { success: response.success, dataLength: response.data.length })
      
      if (response.success) {
        const transformedCases = transformCases(response.data)
        console.log('[useCaseData] 热门箱子数据转换完成，转换后数量:', transformedCases.length)
        return {
          data: transformedCases,
          success: true,
          message: response.message
        }
      } else {
        return {
          data: [],
          success: false,
          message: response.message
        }
      }
    } catch (error: any) {
      console.error('[useCaseData] 获取热门箱子失败:', error)
      return {
        data: [],
        success: false,
        message: error.message || '获取热门箱子失败'
      }
    }
  }

  /**
   * 获取新品箱子（组件级别使用）
   */
  const getNewCases = async (num: number = 10): Promise<{ data: CaseItem[], success: boolean, message: string }> => {
    try {
      const response = await caseApi.getNewCases({ num })
      
      if (response.success) {
        const transformedCases = transformCases(response.data)
        return {
          data: transformedCases,
          success: true,
          message: response.message
        }
      } else {
        return {
          data: [],
          success: false,
          message: response.message
        }
      }
    } catch (error: any) {
      console.error('[useCaseData] 获取新品箱子失败:', error)
      return {
        data: [],
        success: false,
        message: error.message || '获取新品箱子失败'
      }
    }
  }

  /**
   * 获取折扣箱子（组件级别使用）
   */
  const getDiscountCases = async (num: number = 10): Promise<{ data: CaseItem[], success: boolean, message: string }> => {
    try {
      const response = await caseApi.getDiscountCases({ num })
      
      if (response.success) {
        const transformedCases = transformCases(response.data)
        return {
          data: transformedCases,
          success: true,
          message: response.message
        }
      } else {
        return {
          data: [],
          success: false,
          message: response.message
        }
      }
    } catch (error: any) {
      console.error('[useCaseData] 获取折扣箱子失败:', error)
      return {
        data: [],
        success: false,
        message: error.message || '获取折扣箱子失败'
      }
    }
  }

  /**
   * 获取所有箱子（组件级别使用）
   */
  const getAllCases = async (): Promise<{ data: CaseItem[], success: boolean, message: string }> => {
    try {
      const response = await caseApi.getAllCases()
      
      if (response.success) {
        const transformedCases = transformCases(response.data)
        return {
          data: transformedCases,
          success: true,
          message: response.message
        }
      } else {
        return {
          data: [],
          success: false,
          message: response.message
        }
      }
    } catch (error: any) {
      console.error('[useCaseData] 获取箱子列表失败:', error)
      return {
        data: [],
        success: false,
        message: error.message || '获取箱子列表失败'
      }
    }
  }

  return {
    transformCase,
    transformCases,
    getHotCases,
    getNewCases,
    getDiscountCases,
    getAllCases
  }
} 