import logging

from django.utils.translation import gettext_lazy as _

from rest_framework.permissions import AllowAny
from rest_framework.views import APIView

from steambase.enums import RespCode
from steambase.utils import reformat_resp, current_user
from market.business import get_market_item, get_market_query_term, buy_market_items


_logger = logging.getLogger(__name__)


class GetItemInventoryView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            page = request.query_params.get('page')
            limit = request.query_params.get('pageSize')
            category = request.query_params.get('category')
            rarity = request.query_params.get('rarity')
            exterior = request.query_params.get('ext')
            dark = request.query_params.get('dark')
            query = request.query_params.get('q')
            min_price = request.query_params.get('min')
            max_price = request.query_params.get('max')
            sort = request.query_params.get('sort')
            code, resp = get_market_item(page, limit, category, rarity, exterior, dark, query, max_price, min_price, sort)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetQueryTermView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            code, resp = get_market_query_term()
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class BuyMarketItemView(APIView):

    def post(self, request):
        try:
            user = current_user(request)
            uid = request.data.get('uid', None)
            code, resp = buy_market_items(user, uid)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')