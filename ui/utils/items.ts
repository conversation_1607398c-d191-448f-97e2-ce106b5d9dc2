export type ItemRarity = 'consumer' | 'industrial' | 'mil-spec' | 'restricted' | 'classified' | 'covert' | 'contraband'

export const getRarityClass = (rarity: ItemRarity | string | undefined): string => {
  if (!rarity) return 'consumer'
  
  const rarityMap: Record<string, string> = {
    'consumer': 'consumer',
    'industrial': 'industrial',
    'mil-spec': 'mil-spec',
    'restricted': 'restricted',
    'classified': 'classified',
    'covert': 'covert',
    'contraband': 'contraband'
  }
  
  return rarityMap[rarity.toLowerCase()] || 'consumer'
} 