// TypeScript接口定义
export interface LoginCredentials {
  email: string
  password: string
  captcha?: string
  uuid?: string
}

export interface RegisterData {
  email: string
  password: string
  name: string  // 根据新文档使用name
  verify_code: string
  ref_code?: string  // 推荐码
}

export interface ResetPasswordData {
  email: string
  verify_code: string
  newPassword: string
}

export interface ChangePasswordData {
  oldPassword: string
  newPassword: string
  confirmPassword: string
}

// 统一响应格式 - 根据新文档，所有响应都使用body字段
export interface AuthResponse<T = any> {
  code: number
  message: string
  body?: T
  success?: boolean
  usesCookieAuth?: boolean
}

// 验证码响应接口
export interface CaptchaResponse {
  uuid: string
  captcha: string  // base64图片数据
}

// Token响应接口
export interface TokenResponse {
  token: string
}

// 用户数据接口 - 根据新文档更新
export interface UserData {
  uid: string
  email: string
  nickname: string
  profile: {
    avatar?: string
  }
  steam?: {
    steamid?: string
    personaname?: string
    level?: number
    avatar?: string
    avatarmedium?: string
    avatarfull?: string
  }
  asset?: {
    balance?: number | string  // 支持数字或字符串格式
    points?: number
    diamond?: number
    active_point?: number
    tradeurl?: string
  }
  extra?: {
    box_chance_type?: string
    box_free_count?: number
    ban_chat?: boolean
    ban_deposit?: boolean
    ban_withdraw?: boolean
  }
  level?: number
  is_agent?: boolean
  is_active?: boolean
  is_vip?: boolean
  login_time?: string
  login_ip?: string
  date_joined?: string
}

// CSRF Token接口
export interface CsrfResponse {
  csrf_token: string
}

// API服务类
export class AuthApi {
  private baseUrl = '/api/auth'
  private csrfToken: string | null = null

  /**
   * 获取CSRF Token
   */
  async getCsrfToken(): Promise<string> {
    try {
      const response = await $fetch<{code: number, message: string, body: CsrfResponse}>(`${this.baseUrl}/csrf/`, {
        credentials: 'include'
      })
      
      if (response.code === 0 && response.body) {
        this.csrfToken = response.body.csrf_token
        return this.csrfToken
      } else {
        throw new Error(response.message || '获取CSRF token失败')
      }
    } catch (error: any) {
      console.error('[AUTH API] 获取CSRF token失败:', error)
      throw error
    }
  }

  /**
   * 确保有CSRF Token
   */
  private async ensureCsrfToken(): Promise<string> {
    if (!this.csrfToken) {
      return await this.getCsrfToken()
    }
    return this.csrfToken
  }

  /**
   * 获取验证码
   */
  async getCaptcha(): Promise<CaptchaResponse> {
    try {
      // 根据新文档，API返回的是body字段
      const response = await $fetch<{code: number, message: string, body: CaptchaResponse}>(`${this.baseUrl}/captcha/`)
      
      if (response.code === 0 && response.body) {
        return response.body
      } else {
        throw new Error(response.message || '获取验证码失败')
      }
    } catch (error: any) {
      console.error('[AUTH API] 获取验证码失败:', error)
      throw error
    }
  }

  /**
   * 获取Token用于发送验证码
   */
  async getToken(email: string, captcha: string, uuid: string): Promise<string> {
    try {
      const response = await $fetch<{code: number, message: string, body: TokenResponse}>(`${this.baseUrl}/token/`, {
        method: 'POST',
        body: {
          type: 'email',
          contact: email,
          code: captcha,
          uuid: uuid
        }
      })
      
      if (response.code === 0 && response.body) {
        return response.body.token
      } else {
        throw new Error(response.message || '获取token失败')
      }
    } catch (error: any) {
      console.error('[AUTH API] 获取token失败:', error)
      throw error
    }
  }

  /**
   * 发送邮箱验证码
   */
  async sendEmailCode(email: string, type: number, token: string): Promise<AuthResponse> {
    try {
      const response = await $fetch<AuthResponse>(`${this.baseUrl}/email/code/`, {
        method: 'POST',
        body: {
          email,
          type, // 1: 注册, 2: 找回密码
          token
        }
      })
      
      return {
        code: response.code,
        message: response.message || '发送成功',
        success: response.code === 0,
        body: response.body
      }
    } catch (error: any) {
      console.error('[AUTH API] 发送验证码失败:', error)
      throw error
    }
  }

  /**
   * 获取余额记录
   */
  async getBalanceRecord(page: number = 1, pageSize: number = 10): Promise<AuthResponse> {
    try {
      const response = await $fetch<AuthResponse>(`${this.baseUrl}/balancerecord/`, {
        method: 'GET',
        query: { page, pageSize },
        credentials: 'include'
      })
      
      return {
        code: response.code,
        message: response.message || '获取成功',
        success: response.code === 0,
        body: response.body
      }
    } catch (error: any) {
      console.error('[AUTH API] 获取余额记录失败:', error)
      throw error
    }
  }

  /**
   * 设置用户邮箱 - 需要CSRF token
   */
  async setEmail(email: string, verifyCode: string): Promise<AuthResponse> {
    try {
      // 确保有CSRF token
      const csrfToken = await this.ensureCsrfToken()
      
      const response = await $fetch<AuthResponse>(`${this.baseUrl}/set/email/`, {
        method: 'POST',
        headers: {
          'X-CSRFToken': csrfToken,
          'Content-Type': 'application/json'
        },
        body: {
          email,
          verify_code: verifyCode
        },
        credentials: 'include'
      })
      
      return {
        code: response.code,
        message: response.message || '设置成功',
        success: response.code === 0,
        body: response.body
      }
    } catch (error: any) {
      console.error('[AUTH API] 设置邮箱失败:', error)
      throw error
    }
  }

  /**
   * 用户登录 - 根据新文档，已豁免CSRF检查
   */
  async login(credentials: LoginCredentials): Promise<AuthResponse<UserData>> {
    try {
      console.log('[AUTH API] 开始登录，参数:', {
        email: credentials.email,
        captcha: credentials.captcha,
        uuid: credentials.uuid
      })
      
      const response = await $fetch<{code: number, message: string, body: {user: UserData}}>(`${this.baseUrl}/email/login/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: {
          email: credentials.email,
          password: credentials.password,
          captcha: credentials.captcha,
          uuid: credentials.uuid
        },
        credentials: 'include'  // 包含cookie
      })
      
      console.log('[AUTH API] 登录响应:', response)
      
      return {
        code: response.code,
        message: response.message || '登录成功',
        success: response.code === 0,
        body: response.body?.user,
        usesCookieAuth: true
      }
    } catch (error: any) {
      console.error('[AUTH API] 登录失败:', error)
      throw error
    }
  }

  /**
   * 用户注册 - 根据新文档，已豁免CSRF检查
   */
  async register(userData: RegisterData): Promise<AuthResponse<UserData>> {
    try {
      const response = await $fetch<{code: number, message: string, body: {user_id: number}}>(`${this.baseUrl}/email/register/`, {
        method: 'POST',
        body: {
          email: userData.email,
          name: userData.name,  // 使用name而不是nickname
          verify_code: userData.verify_code,
          password: userData.password,
          ref_code: userData.ref_code
        },
        credentials: 'include'
      })
      
      return {
        code: response.code,
        message: response.message || '注册成功',
        success: response.code === 0,
        body: response.body as any
      }
    } catch (error: any) {
      console.error('[AUTH API] 注册失败:', error)
      throw error
    }
  }

  /**
   * 重置密码 - 根据新文档，已豁免CSRF检查
   */
  async resetPassword(resetData: ResetPasswordData): Promise<AuthResponse> {
    try {
      const response = await $fetch<AuthResponse>(`${this.baseUrl}/email/reset-password/`, {
        method: 'POST',
        body: {
          email: resetData.email,
          verify_code: resetData.verify_code,
          newPassword: resetData.newPassword
        },
        credentials: 'include'
      })
      
      return {
        code: response.code,
        message: response.message || '重置成功',
        success: response.code === 0,
        body: response.body
      }
    } catch (error: any) {
      console.error('[AUTH API] 重置密码错误:', error)
      throw error
    }
  }

  /**
   * 获取当前用户信息
   */
  async getCurrentUser(): Promise<UserData | null> {
    try {
      const response = await $fetch<{code: number, message: string, body: UserData}>(`${this.baseUrl}/userinfo/`, {
        credentials: 'include'
      })
      
      if (response.code === 0 && response.body) {
        return response.body
      } else if (response.code === 100) {
        // 未登录
        return null
      } else {
        throw new Error(response.message || '获取用户信息失败')
      }
    } catch (error: any) {
      console.error('[AUTH API] 获取用户信息失败:', error)
      return null
    }
  }

  /**
   * 检查登录状态
   */
  async checkLogin(): Promise<boolean> {
    try {
      const response = await $fetch<{code: number, message: string, body: boolean}>(`${this.baseUrl}/checklogin/`, {
        credentials: 'include'
      })
      
      return response.code === 0 && response.body === true
    } catch (error: any) {
      console.error('[AUTH API] 检查登录状态失败:', error)
      return false
    }
  }

  /**
   * 用户登出
   */
  async logout(): Promise<void> {
    try {
      await $fetch(`${this.baseUrl}/logout/`, {
        credentials: 'include'
      })
    } catch (error: any) {
      console.error('[AUTH API] 登出失败:', error)
      throw error
    }
  }

  /**
   * 修改密码 - 需要CSRF token
   */
  async changePassword(passwordData: ChangePasswordData): Promise<AuthResponse> {
    try {
      // 确保有CSRF token
      const csrfToken = await this.ensureCsrfToken()
      
      const response = await $fetch<AuthResponse>(`${this.baseUrl}/change-password/`, {
        method: 'POST',
        headers: {
          'X-CSRFToken': csrfToken,
          'Content-Type': 'application/json'
        },
        body: {
          oldPassword: passwordData.oldPassword,
          newPassword: passwordData.newPassword,
          confirmPassword: passwordData.confirmPassword
        },
        credentials: 'include'
      })
      
      return {
        code: response.code,
        message: response.message || '修改成功',
        success: response.code === 0,
        body: response.body
      }
    } catch (error: any) {
      console.error('[AUTH API] 修改密码失败:', error)
      throw error
    }
  }

  /**
   * 设置用户昵称 - 需要CSRF token
   */
  async setNickname(nickname: string): Promise<AuthResponse> {
    try {
      // 确保有CSRF token
      const csrfToken = await this.ensureCsrfToken()
      
      const response = await $fetch<AuthResponse>(`${this.baseUrl}/set/name/`, {
        method: 'POST',
        headers: {
          'X-CSRFToken': csrfToken,
          'Content-Type': 'application/json'
        },
        body: {
          name: nickname
        },
        credentials: 'include'
      })
      
      return {
        code: response.code,
        message: response.message || '设置成功',
        success: response.code === 0,
        body: response.body
      }
    } catch (error: any) {
      console.error('[AUTH API] 设置昵称失败:', error)
      throw error
    }
  }

  /**
   * 设置用户头像 - 需要CSRF token
   */
  async setAvatar(avatarData: string): Promise<AuthResponse> {
    try {
      // 确保有CSRF token
      const csrfToken = await this.ensureCsrfToken()
      
      const response = await $fetch<AuthResponse>(`${this.baseUrl}/set/avatar/`, {
        method: 'POST',
        headers: {
          'X-CSRFToken': csrfToken,
          'Content-Type': 'application/json'
        },
        body: {
          body: avatarData  // 根据文档，参数名是body而不是data
        },
        credentials: 'include'
      })
      
      return {
        code: response.code,
        message: response.message || '设置成功',
        success: response.code === 0,
        body: response.body
      }
    } catch (error: any) {
      console.error('[AUTH API] 设置头像失败:', error)
      throw error
    }
  }

  /**
   * 设置Steam交易链接 - 需要CSRF token
   */
  async setSteamTradeUrl(tradeUrl: string): Promise<AuthResponse> {
    try {
      // 确保有CSRF token
      const csrfToken = await this.ensureCsrfToken()
      
      const response = await $fetch<AuthResponse>(`${this.baseUrl}/set/steamlink/`, {
        method: 'POST',
        headers: {
          'X-CSRFToken': csrfToken,
          'Content-Type': 'application/json'
        },
        body: {
          tradeUrl
        },
        credentials: 'include'
      })
      
      return {
        code: response.code,
        message: response.message || '设置成功',
        success: response.code === 0,
        body: response.body
      }
    } catch (error: any) {
      console.error('[AUTH API] 设置Steam交易链接失败:', error)
      throw error
    }
  }
}

// 导出单例实例
export const authApi = new AuthApi() 