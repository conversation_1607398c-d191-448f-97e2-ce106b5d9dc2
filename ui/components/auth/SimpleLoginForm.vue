<template>
  <div class="p-6 bg-gray-800 rounded-lg border border-gray-600">
    <h2 class="text-xl font-semibold text-white mb-6 text-center">登录</h2>
    
    <form @submit.prevent="handleLogin" class="space-y-4">
      <!-- 邮箱 -->
      <div>
        <label class="block text-sm font-medium text-gray-300 mb-2">邮箱</label>
        <input 
          type="email" 
          v-model="email" 
          class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
          placeholder="请输入邮箱"
          required
        />
      </div>
      
      <!-- 密码 -->
      <div>
        <label class="block text-sm font-medium text-gray-300 mb-2">密码</label>
        <input 
          type="password" 
          v-model="password" 
          class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
          placeholder="请输入密码"
          required
        />
      </div>
      
      <!-- 提交按钮 -->
      <button 
        type="submit" 
        :disabled="isLoading"
        class="w-full py-2 px-4 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white font-medium rounded transition-colors"
      >
        {{ isLoading ? '登录中...' : '登录' }}
      </button>
      
      <!-- 错误信息 -->
      <div v-if="error" class="p-3 bg-red-500/20 border border-red-500 rounded">
        <p class="text-red-300 text-sm">{{ error }}</p>
      </div>
    </form>
    
    <!-- 链接 -->
    <div class="mt-6 text-center space-y-2">
      <p class="text-gray-400 text-sm">
        没有账号？
        <NuxtLink to="/auth/register" class="text-blue-400 hover:text-blue-300">立即注册</NuxtLink>
      </p>
      <p class="text-gray-400 text-sm">
        <NuxtLink to="/auth/forgot-password" class="text-blue-400 hover:text-blue-300">忘记密码</NuxtLink>
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
const email = ref('')
const password = ref('')
const isLoading = ref(false)
const error = ref('')
const userStore = useUserStore()
const router = useRouter()

const handleLogin = async () => {
  if (!email.value || !password.value) {
    error.value = '请填写邮箱和密码'
    return
  }
  
  try {
    isLoading.value = true
    error.value = ''
    
    await userStore.login({
      email: email.value,
      password: password.value
    })
    
    // 登录成功后跳转
    await router.push('/')
  } catch (err: any) {
    error.value = err.message || '登录失败，请重试'
  } finally {
    isLoading.value = false
  }
}
</script> 