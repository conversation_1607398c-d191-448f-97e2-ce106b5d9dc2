# Generated by Django 3.2.25 on 2025-07-19 03:46

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('roll', '0009_auto_20250718_1647'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='rollroom',
            options={'ordering': ('-create_time',), 'verbose_name': '滚轮房间', 'verbose_name_plural': 'Roll Room'},
        ),
        migrations.AlterModelOptions(
            name='rollroombet',
            options={'ordering': ('-create_time',), 'verbose_name': '滚轮房间下注', 'verbose_name_plural': 'Roll Room Bet'},
        ),
        migrations.AlterModelOptions(
            name='rollroombetbot',
            options={'verbose_name': '滚轮房间机器人', 'verbose_name_plural': 'Roll房机器人'},
        ),
        migrations.AlterModelOptions(
            name='rollroombot',
            options={'verbose_name': '滚轮房间机器人配置', 'verbose_name_plural': 'Rollroom Bot Config'},
        ),
        migrations.AlterModelOptions(
            name='rollroomitem',
            options={'verbose_name': '滚轮房间物品', 'verbose_name_plural': 'Roll Room Item'},
        ),
        migrations.AlterModelOptions(
            name='rollroompumpday',
            options={'ordering': ('-create_time',), 'verbose_name': '滚轮房间抽水日统计', 'verbose_name_plural': 'Roll Room Pump Day'},
        ),
        migrations.AlterModelOptions(
            name='rollroompumpmonth',
            options={'ordering': ('-create_time',), 'verbose_name': '滚轮房间抽水月统计', 'verbose_name_plural': 'Roll Room Pump Month'},
        ),
        migrations.AlterModelOptions(
            name='rollroomstatisticsday',
            options={'ordering': ('-create_time',), 'verbose_name': '滚轮房间日统计', 'verbose_name_plural': 'Rollroom Statistics Day'},
        ),
        migrations.AlterModelOptions(
            name='rollroomstatisticsmonth',
            options={'ordering': ('-create_time',), 'verbose_name': '滚轮房间月统计', 'verbose_name_plural': 'Rollroom Statistics Month'},
        ),
        migrations.AlterField(
            model_name='rollroom',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='roll_room', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
        migrations.AlterField(
            model_name='rollroombet',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='roll_bets', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
        migrations.AlterField(
            model_name='rollroombot',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='roll_bot', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
    ]
