<template>
  <div class="calculation-progress">
    <!-- 主进度条 -->
    <div class="bg-slate-800/60 backdrop-blur-xl border border-white/20 rounded-2xl p-6 shadow-xl mb-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="font-bold text-white flex items-center gap-2">
          <Icon 
            :name="progress >= 100 ? 'material-symbols:check-circle' : 'material-symbols:calculate'" 
            class="w-5 h-5"
            :class="progress >= 100 ? 'text-green-400' : 'text-yellow-400'"
          />
          {{ progress >= 100 ? titleComplete : title }}
        </h3>
        <span class="text-sm text-white/60">{{ Math.round(progress) }}%</span>
      </div>

      <div class="relative">
        <div class="w-full bg-slate-700/50 rounded-full h-4 overflow-hidden">
          <div
            class="h-full bg-gradient-to-r from-blue-500 via-purple-500 to-cyan-500 rounded-full transition-all duration-1000 ease-out relative overflow-hidden"
            :style="{ width: progress + '%' }"
          >
            <div 
              v-if="progress < 100"
              class="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-pulse" 
              style="animation-delay: 0.5s"
            ></div>
            <div 
              v-if="progress >= 100"
              class="absolute inset-0 bg-gradient-to-r from-green-400 via-white to-green-400 animate-pulse" 
              style="animation-duration: 2s"
            ></div>
          </div>
        </div>
        
        <div
          class="absolute top-1/2 transform -translate-y-1/2 w-6 h-6 rounded-full shadow-lg animate-pulse"
          :class="progress >= 100 ? 'bg-green-400 shadow-green-400/50' : 'bg-yellow-400 shadow-yellow-400/50'"
          :style="{ left: `calc(${progress}% - 12px)` }"
        >
          <Icon 
            v-if="progress >= 100"
            name="material-symbols:check" 
            class="w-4 h-4 text-white absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
          />
        </div>
      </div>
      
      <div 
        v-if="progress >= 100 && showCompletionMessage"
        class="mt-4 text-center"
      >
        <p class="text-green-400 font-medium animate-pulse">
          ✅ {{ completionMessage }}
        </p>
      </div>
    </div>

    <!-- 计算步骤 -->
    <div v-if="showSteps" class="bg-slate-800/60 backdrop-blur-xl border border-white/20 rounded-2xl p-6 shadow-xl">
      <h3 class="font-bold text-white mb-4 text-center">{{ stepsTitle }}</h3>
      
      <div class="space-y-3">
        <div
          v-for="(step, index) in calculationSteps"
          :key="index"
          class="flex items-center gap-3 p-3 rounded-lg transition-all duration-500"
          :class="{
            'bg-blue-500/20 border border-blue-400/30': step.active,
            'bg-slate-700/30': !step.active,
          }"
        >
          <div
            class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold transition-all duration-500"
            :class="{
              'bg-blue-500 text-white': step.active,
              'bg-slate-600 text-white/60': !step.active,
            }"
          >
            {{ index + 1 }}
          </div>
          <div class="flex-1">
            <p class="text-white" :class="{ 'text-blue-300': step.active }">
              {{ step.text }}
            </p>
          </div>
          <div v-if="step.active" class="w-4 h-4">
            <div class="w-4 h-4 border-2 border-blue-400 border-t-transparent rounded-full animate-spin"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// Props 定义
const props = defineProps({
  progress: {
    type: Number,
    default: 0,
  },
  title: {
    type: String,
    default: '计算进度',
  },
  titleComplete: {
    type: String,
    default: '计算完成',
  },
  completionMessage: {
    type: String,
    default: '计算完成！正在确定结果...',
  },
  showCompletionMessage: {
    type: Boolean,
    default: true,
  },
  showSteps: {
    type: Boolean,
    default: true,
  },
  stepsTitle: {
    type: String,
    default: '计算过程',
  },
  customSteps: {
    type: Array,
    default: () => [],
  },
});

// 计算属性
const calculationSteps = computed(() => {
  console.log(`[🎰CALC-PROGRESS] 🔍 计算步骤 - 进度: ${props.progress}%, 自定义步骤:`, props.customSteps);
  
  // 检查是否有自定义步骤（包括空数组的情况）
  if (props.customSteps && props.customSteps.length >= 0) {
    const progress = props.progress;
    const stepCount = props.customSteps.length;
    const activeStepIndex = Math.floor((progress / 100) * stepCount);
    
    console.log(`[🎰CALC-PROGRESS] 📊 使用自定义步骤 - 总数: ${stepCount}, 活跃索引: ${activeStepIndex}`);
    
    return props.customSteps.map((step, index) => ({
      ...step,
      active: index <= activeStepIndex,
    }));
  }
  
  const defaultSteps = [
    { text: '分析数据...', active: false },
    { text: '计算价值...', active: false },
    { text: '比较结果...', active: false },
    { text: '确定获胜者...', active: false },
    { text: '生成分配...', active: false },
  ];
  
  const progress = props.progress;
  const stepCount = defaultSteps.length;
  const activeStepIndex = Math.floor((progress / 100) * stepCount);
  
  console.log(`[🎰CALC-PROGRESS] 📊 使用默认步骤 - 总数: ${stepCount}, 活跃索引: ${activeStepIndex}`);
  
  return defaultSteps.map((step, index) => ({
    ...step,
    active: index <= activeStepIndex,
  }));
});

// 监听进度变化
watch(() => props.progress, (newProgress) => {
  console.log(`[🎰CALC-PROGRESS] 📈 进度更新: ${newProgress}%`);
}, { immediate: true });

onMounted(() => {
  console.log("[🎰CALC-PROGRESS] 组件挂载完成");
});
</script>

<style scoped>
@keyframes pulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
</style> 