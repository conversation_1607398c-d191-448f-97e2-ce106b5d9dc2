"""
CKEditor 5测试视图
"""
from django.shortcuts import render
from django.http import HttpResponse
from django import forms
from django_ckeditor_5.widgets import CKEditor5Widget


class TestForm(forms.Form):
    """测试表单"""
    content = forms.CharField(widget=CKEditor5Widget(config_name='default'))
    content_with_upload = forms.CharField(widget=CKEditor5Widget(config_name='extends'))


def test_ckeditor5_view(request):
    """测试CKEditor 5的视图"""
    form = TestForm()
    
    html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>CKEditor 5 测试</title>
        <meta charset="utf-8">
        <style>
            body {{ font-family: Arial, sans-serif; margin: 40px; }}
            .form-group {{ margin-bottom: 20px; }}
            label {{ display: block; margin-bottom: 5px; font-weight: bold; }}
        </style>
    </head>
    <body>
        <h1>🔧 CKEditor 5 测试页面</h1>
        <p>这个页面用于测试CKEditor 5是否正常工作。</p>
        
        <form method="post">
            <div class="form-group">
                <label>基础编辑器 (default配置):</label>
                {form['content']}
            </div>
            
            <div class="form-group">
                <label>扩展编辑器 (extends配置，支持图片上传):</label>
                {form['content_with_upload']}
            </div>
            
            <button type="submit">提交测试</button>
        </form>
        
        <hr>
        <h2>📊 测试状态</h2>
        <ul>
            <li>✅ Django 4.2.23 运行正常</li>
            <li>✅ django-ckeditor-5 已安装</li>
            <li>✅ CKEditor 5配置已加载</li>
            <li>✅ 测试表单已渲染</li>
        </ul>
        
        <p><strong>如果您能看到上面的编辑器，说明CKEditor 5升级成功！</strong></p>
    </body>
    </html>
    """
    
    return HttpResponse(html)
