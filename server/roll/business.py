import random
import time
import threading
import numpy
import json
import logging
import pytz

from django.conf import settings
from django.core.cache import cache
from django.core.paginator import Pa<PERSON>ator, EmptyPage, PageNotAnInteger
from django.core.serializers.json import DjangoJSONEncoder

from django.db import connection, transaction
from django.db.models import Q, Sum, F
from django.db import transaction, IntegrityError
from django.utils import timezone
from django.utils.translation import gettext_lazy as _


from datetime import timedelta
from dateutil.parser import parse as parseDate

from steambase.enums import RespCode, GameState, WinResult, RollType, PackageState, CaseItemType, ChargeState
from steambase.utils import is_connection_usable, aware_timestamp_to_timezone, ParamException
from sitecfg.interfaces import get_roll_pump_percentage, get_roll_due_day_max, get_roll_create_count_max, \
    get_roll_join_min_charge_limit, get_roll_bet_join
from package.models import PackageItem
from package.interfaces import get_item_price_by_name
from package.service.item import get_item_price_by_package, get_item_price
from roll.models import <PERSON><PERSON><PERSON>, RollRoomBet, RollRoom<PERSON><PERSON>, <PERSON>roomBot, Roll<PERSON>oomPumpDay, RollRoomPumpMonth, \
    RollroomStatisticsDay, RollroomStatisticsMonth
from roll.serializers import RollRoomSerializer, RollRoomBetSerializer, RollRoomItemSerializer, RollRoomCacheSerializer, RollRoomWinnerSerializer
from package.models import ItemPrice, ItemInfo

_logger = logging.getLogger(__name__)

_steam_img_base = 'https://steamcommunity-a.akamaihd.net/economy/image/'



def create_roll_room(user, name, min_joiner, max_joiner, max_winner, begin_time, due_time,
                     type=RollType.Nocharge.value, desc=None, password=None, fee=0, items=None,
                     deposit_enable=False, charge_limit=None, charge_begin_time=None, charge_end_time=None, debug=True):
    """
    创建房间
    :param type: 房间类型
    :param name: 房间名
    :param desc: 房间描述
    :param password: 房间密码
    :param fee: 房间参与费
    :param min_joiner: 最小人数
    :param max_joiner: 最大人数
    :param max_winner: 中奖人数
    :param begin_time: 开始时间
    :param due_time: 截止时间
    :param items: 存入物品
    :param deposit_enable: 允许存入加入
    :param charge_limit: 充值金额限制
    :param charge_begin_time: 充值开始限制
    :param charge_end_time: 充值结束限制
    :return: 房间uid
    """
    if user.extra.ban_create_roll_room == 1:
        return RespCode.NoPermission.value, _('无权执行此操作')
    dt_now = timezone.now()
    begin_ts = parseDate(begin_time) if begin_time else dt_now
    due_ts = parseDate(due_time) if due_time else dt_now
    if (not name or not type or max_joiner < 1 or max_winner <= 0 or max_joiner < max_winner or
            begin_ts < dt_now or due_ts < dt_now or begin_ts >= due_ts or max_joiner < min_joiner or
            due_ts - timedelta(days=get_roll_due_day_max()) > dt_now):
        return RespCode.InvalidParams.value, _('Invalid params')
    if type == RollType.Charge.value and fee <= 0:
        return RespCode.InvalidParams.value, _('Invalid fee')

    last_room_count = RollRoom.objects.filter(
        user=user,
        type=type,
        state__in=[
            GameState.Initial.value,
            GameState.Joinable.value,
            GameState.Joining.value,
            GameState.Full.value,
            GameState.Running.value,
        ]).count()
    if last_room_count >= get_roll_create_count_max():
        return RespCode.InvalidParams.value, _('Over max room count')

    room_info = {
        'official':1,
        'user': user,
        'type': type,
        'name': name,
        'desc': desc,
        'password': password,
        'fee': fee,
        'min_joiner': min_joiner,
        'max_joiner': max_joiner,
        'max_winner': max_winner,
        'begin_ts': begin_ts,
        'due_ts': due_ts,
        'deposit_enable': deposit_enable,
        'debug': debug,
    }
    if charge_limit:
        charge_limit = float(charge_limit)
        if charge_limit > 0:
            room_info.update({'charge_limit': charge_limit})
    if charge_begin_time:
        room_info.update({'charge_begin_time': parseDate(charge_begin_time) if charge_begin_time else dt_now})
    if charge_end_time:
        room_info.update({'charge_end_time': parseDate(charge_end_time) if charge_end_time else dt_now})
    with transaction.atomic():
        room = RollRoom.objects.select_for_update().create(**room_info)
        if items:
            packages = PackageItem.update_state(items, PackageState.Available.value, PackageState.Gaming.value, user)
            for package in packages:
                price = get_item_price_by_package(package)
                # price = get_item_price_by_name(package.item_info.market_hash_name, appid=package.item_info.appid)
                RollRoomItem.objects.create(room=room, package=package, price=price)
                room.total_amount += price
                room.items_count += 1
            room.save()
            update_roll_statistics_journal(room.total_amount)
        data = {
            'room': room.uid,
        }
        return RespCode.Succeed.value, data


def edit_roll_room(user, uid, name, desc, password, fee, max_joiner, max_winner, begin_time, due_time):
    """
    编辑房间
    :param uid: 房间uid
    :param name: 房间名
    :param desc: 房间描述
    :param password: 房间密码
    :param fee: 房间参与费
    :param max_joiner: 最大人数
    :param max_winner: 中奖人数
    :param begin_time: 开始时间
    :param due_time: 截止时间
    :return: 房间uid
    """
    dt_now = timezone.now()
    current_tz = timezone.get_current_timezone()
    begin_ts = timezone.make_aware(parseDate(begin_time), current_tz)
    due_ts = timezone.make_aware(parseDate(due_time), current_tz)
    if (max_joiner <= 0 or max_winner <= 0 or max_joiner < max_winner or
            begin_ts < dt_now or due_ts < dt_now or begin_ts >= due_ts):
        return RespCode.InvalidParams.value, _('Invalid params')

    room_info = {
        'name': name,
        'desc': desc,
        'password': password,
        'fee': fee,
        'max_joiner': max_joiner,
        'max_winner': max_winner,
        'begin_ts': begin_ts,
        'due_ts': due_ts,
    }

    with transaction.atomic():
        room = RollRoom.objects.select_for_update().filter(user=user, uid=uid, state=GameState.Initial.value)
        if not room:
            return RespCode.InvalidParams.value, _('Invalid room')
        room.update(**room_info)
        data = {
            'room': room.first().uid,
        }
        return RespCode.Succeed.value, data


def deposit_roll_items(user, uid, items, coins):
    """
    存入物品
    :param uid: 房间uid
    :param items: 放入的物品
    :return:
    """
    total_balance = 0
    for _balance in coins:
        if _balance < 0:
            return RespCode.InvalidParams.value, _('Invalid params')
        total_balance += _balance
    if user.asset.balance < total_balance:
        return RespCode.InvalidParams.value, _('Not enough balance')
    with transaction.atomic():
        room = RollRoom.objects.select_for_update().filter(
            user=user, uid=uid,
            state__in=[GameState.Initial.value, GameState.Joinable.value]
        ).first()
        if not room:
            return RespCode.InvalidParams.value, _('Invalid room')
        if room.max_winner > len(items) + len(coins):
            return RespCode.InvalidParams.value, _('存入数量必须大于获胜人数')

        packages = PackageItem.update_state(items, PackageState.Available.value, PackageState.Gaming.value, user)
        # packages = items  
        for package in packages:
            # _logger.info(f'package: {package}')
            price = get_item_price_by_package(package)
            
            
            # price = get_item_price_by_name(package.item_info.market_hash_name, appid=package.item_info.appid)

            RollRoomItem.objects.create(room=room, price=price, item_type=CaseItemType.Assets.value, item_id=package.item_info.id)
            room.total_amount = round(room.total_amount + price, 2)
            room.items_count += 1
        # for balance in coins:
        #     balance = round(balance, 2)
        #     user.update_balance(-balance, '存入roll房奖品')
        #     RollRoomItem.objects.create(room=room, balance=balance, item_type=CaseItemType.Balance.value)
        #     room.total_amount = round(room.total_amount + balance, 2)
        #     room.items_count += 1

        if room.state == GameState.Initial.value and room.items_count:
            room.state = GameState.Joining.value
        room.save()
        # update_roll_statistics_journal(room.total_amount)
        return RespCode.Succeed.value, {}




def join_roll_room(user_param, uid, password=None):
    """
    加入房间
    :param user_param: 当前用户
    :param uid: 房间uid
    :param password: 输入的密码
    :return:
    """
    china_tz = pytz.timezone("Asia/Shanghai")

    # 获取房间信息并初步检查
    room = RollRoom.objects.filter(uid=uid, state=GameState.Joinable.value).exclude(user=user_param).first()
    if not room:
        return RespCode.InvalidParams.value, _('Invalid room')

    is_bot = RollroomBot.objects.filter(user=user_param, enable=True).exists()
    if not is_bot:
        if user_param.extra.ban_roll == 1:
            return RespCode.NoPermission.value, _('无权执行此操作')
        if password != room.password:
            return RespCode.InvalidParams.value, _('Invalid password')
        if RollRoomBet.objects.filter(user=user_param, room=room).exists():
            return RespCode.InvalidParams.value, _('Already joined')
        
        # 判断充值金额限制，优化数据库查询
        if room.charge_limit:
            charge_filter = user_param.charges.filter(state=ChargeState.Succeed.value)
            if room.charge_begin_time:
                charge_begin_time = timezone.make_aware(room.charge_begin_time, china_tz) if timezone.is_naive(room.charge_begin_time) else room.charge_begin_time
                charge_filter = charge_filter.filter(pay_time__gte=charge_begin_time)
            if room.charge_end_time:
                charge_end_time = timezone.make_aware(room.charge_end_time, china_tz) if timezone.is_naive(room.charge_end_time) else room.charge_end_time
                charge_filter = charge_filter.filter(pay_time__lte=charge_end_time)
            
            charge_amount = charge_filter.aggregate(Sum('amount')).get('amount__sum') or 0
            if room.charge_limit > charge_amount:
                return RespCode.BusinessError.value, _('充值金额小于参加条件')

        # 如果房间为收费类型，尝试更新余额和费用
        if room.type == RollType.Charge.value:
            update_result = user_param.update_balance(-room.fee, _('Roll room bet'))
            if not update_result:
                return RespCode.UpdateFailed.value, _('Failed to update balance')

    # 在事务中锁定房间状态并创建投注
    with transaction.atomic():
        room = RollRoom.objects.select_for_update().filter(uid=uid).first()
        if not room:
            return RespCode.InvalidParams.value, _('Invalid room')

        bet = RollRoomBet.objects.create(user=user_param, room=room)

        # 更新房间状态和费用
        if room.bets.count() >= room.max_joiner:
            room.state = GameState.End.value
            _logger.info(f'Roll room full: {room.uid}')
        
        if room.type == RollType.Charge.value:
            room.total_fee += room.fee
        room.save(update_fields=['total_fee', 'state'])

        # 清除缓存以便获取最新状态
        cache_key = f'new_api_roll:detail:{uid}'
        cache.delete(cache_key)

    return RespCode.Succeed.value, {'bet': bet.uid}



def cancel_roll_room(user, uid, compulsory=False):
    """
    取消房间
    :param uid: 房间uid
    :return:
    """
    with transaction.atomic():
        room = RollRoom.objects.select_for_update().filter(uid=uid,
                                                           user=user,
                                                           state__in=[
                                                               GameState.Initial.value,
                                                               GameState.Joinable.value,
                                                               GameState.Joining.value,
                                                               GameState.Full.value,
                                                           ]).first()
        if not room:
            return RespCode.InvalidParams.value, _('Invalid room')
        if room.bets.count() > 0 and not compulsory:
            return RespCode.InvalidParams.value, _('Somebody has joined')

        room.state = GameState.Cancelled.value
        bets = room.bets.all()
        for bet in bets:
            fee = room.fee
            bet.user.update_balance(fee, _('Roll room cancel'))
        #_logger.info('Roll room {} cancel by user'.format(room.uid))
        room.save()
        return RespCode.Succeed.value, {}


def get_room_list(user, query, fields, page, page_size, is_creator, is_joiner, items_preview_count, no_password,
                  is_running, is_end, language):
    queryset = RollRoom.objects.filter(enable=True).filter(
        Q(state__in=[GameState.Joinable.value, GameState.Joining.value, GameState.Full.value, GameState.End.value]) |
        (Q(user=user) & Q(state=GameState.Initial.value)) |
        (Q(state=GameState.End.value) & Q(due_ts__gte=timezone.now() - timedelta(days=get_roll_due_day_max())))
    ).filter(**query).order_by('state', '-official', '-create_time')
    if is_running:
        queryset = queryset.filter(state__in=[GameState.Joinable.value, GameState.Joining.value, GameState.Full.value])
    if is_end:
        queryset = queryset.filter(state=GameState.End.value)
    if is_creator:
        queryset = queryset.filter(user=user)
    if is_joiner:
        queryset = queryset.filter(bets__user=user)
    if no_password:
        queryset = queryset.filter(password="")
    paginator = Paginator(queryset, page_size)
    rooms = paginator.page(page)
    rooms_data = RollRoomSerializer(rooms, many=True, fields=fields,
    # rooms_data = RollRoomSerializer(rooms, many=True, fields=fields,
                                    context={'user': user, 'items_preview_count': items_preview_count,
                                           "language": language}).data
    
    resp = {
        'rooms': rooms_data,
        'total': paginator.count
    }
    return RespCode.Succeed.value, resp


def get_joinable_num():
    queryset = RollRoom.objects.filter(state__in=[GameState.Joinable.value, GameState.Joining.value])
    total_amount = 0
    for room in queryset:
        total_amount += room.total_amount
    resp = {
        'total_amount': round(total_amount, 2),
        'total': len(queryset)
    }
    return RespCode.Succeed.value, resp


def get_room_detail(user, query, fields, language):
    room = RollRoom.objects.filter(
        Q(state__in=[
            GameState.Joinable.value,
            GameState.Joining.value,
            GameState.Full.value,
            GameState.End.value
        ]) |
        (Q(user=user) & Q(state__in=[
            GameState.Initial.value,
            GameState.Joinable.value,
            GameState.Joining.value,
            GameState.Full.value,
            GameState.End.value,
        ]))
    ).filter(**query).first()
    resp = {}
    if room:
        room_data = RollRoomSerializer(room, fields=fields, context={'user': user, "language": language}).data
        resp['room'] = room_data
        resp['total_amount'] = room.total_amount
        
    return RespCode.Succeed.value, resp


def get_room_items(query, fields, page, page_size):
    # 获取房间的状态
    rid = query.get('room__uid')
    roll_room = RollRoom.objects.filter(uid=rid).first()
    if not roll_room:
        return RespCode.InvalidParams.value, "房间不存在"
    # 缓存键
    cache_key = f'roll:items:{rid}:{page}:{page_size}:{query}'
    cached_data = cache.get(cache_key)
    if cached_data:
            #print('get from cache')
            return RespCode.Succeed.value, cached_data

    queryset = RollRoomItem.objects.filter(**query).order_by('-price')

    paginator = Paginator(queryset, page_size)
    items = paginator.page(page)
    items_data = RollRoomItemSerializer(items, many=True, fields=fields).data

    resp = {
        'items': items_data,
        'total': paginator.count,
        'page': page,
        'limit': page_size
    }
    # 将数据存入缓存
    cache.set(cache_key, resp, timeout=60 * 60 * 24)
    return RespCode.Succeed.value, resp


def get_room_bets(query, fields, page, page_size, items_preview_count):
    # 获取房间的状态
    rid = query.get('room__uid')
    roll_room = RollRoom.objects.filter(uid=rid).first()
    if not roll_room:
        return RespCode.InvalidParams.value, "房间不存在"
    
    roll_state = roll_room.state

    # 缓存键
    cache_key = f'roll:bets:{rid}:{page}:{page_size}:{query}'

    # 如果游戏已经结束，则直接从缓存中获取数据
    if roll_state == GameState.End.value:
        cached_data = cache.get(cache_key)
        if cached_data:
            #print('get from cache')
            return RespCode.Succeed.value, cached_data

    # 查询数据库
    queryset = RollRoomBet.objects.filter(**query).order_by('-create_time')

    # 分页处理
    paginator = Paginator(queryset, page_size)
    items = paginator.page(page)
    items_data = RollRoomBetSerializer(items, many=True, fields=fields,
                                       context={'items_preview_count': items_preview_count}).data

    resp = {
        'items': items_data,
        'total': paginator.count,
        'page': page,
        'limit': page_size
    }

    # 如果游戏已经结束，则将数据存入缓存
    if roll_state == GameState.End.value:
        cache.set(cache_key, resp, timeout=60 * 60 * 24)

    return RespCode.Succeed.value, resp



def check_roll_room():
    while True:
        try:
            if not is_connection_usable():
                connection.close()
                continue  # 考虑在这里加入重试连接的逻辑，或跳出循环

            keys = cache.keys('roll:*')
            for key in keys:
                val = cache.get(key)
                if isinstance(val, str):
                    try:
                        val = json.loads(val)  # 尝试将字符串解析为字典
                    except json.JSONDecodeError:
                        #_logger.error(f"Cannot decode JSON for key {key}: {val}")
                        continue  # 如果转换失败，跳过当前循环

                if not isinstance(val, dict):  # 检查值是否为正确的字典格式
                    #_logger.error(f"Invalid data type for key {key}: {type(val)}")
                    continue

                uid = val.get('uid')
                if not RollRoom.objects.filter(uid=uid).exists():
                    #_logger.info(f'Roll room invalid: {uid}, skip')
                    cache.delete(key)
                    continue

                # 从字典中提取数据
                items_count = val.get('items_count')
                min_joiner = val.get('min_joiner')
                state = val.get('state')
                bets = val.get('bets')
                begin_ts_val = val.get('begin_ts_val')
                due_ts_val = val.get('due_ts_val')
                begin_ts = aware_timestamp_to_timezone(begin_ts_val)
                due_ts = aware_timestamp_to_timezone(due_ts_val)
                #_logger.debug(f'begin_ts: {begin_ts}, due_ts: {due_ts}')

                # 根据时间和状态更新房间状态
                if begin_ts <= timezone.now() and items_count > 0 and state == GameState.Joining.value:
                    RollRoom.update_state(uid, GameState.Joining.value, GameState.Joinable.value)
                    #_logger.info(f'Roll room joinable: {uid}')
                elif due_ts <= timezone.now() and len(bets) >= min_joiner and state in [GameState.Joinable.value, GameState.Full.value]:
                    RollRoom.update_state(uid, state, GameState.End.value)
                    #_logger.info(f'Roll room end: {uid}')
                elif due_ts <= timezone.now() and len(bets) < min_joiner and state == GameState.Joinable.value:
                    RollRoom.update_state(uid, GameState.Joinable.value, GameState.Cancelled.value)
                    #_logger.info(f'Roll room cancel: {uid}')
                elif due_ts <= timezone.now() and state == GameState.Initial.value:
                    RollRoom.update_state(uid, GameState.Initial.value, GameState.Cancelled.value)
                    #_logger.info(f'Roll room cancel: {uid}')
        except Exception as e:
            _logger.exception(f"An exception occurred: {e}")
        finally:
            time.sleep(1)  # 可以考虑根据实际情况调整休眠时间




def update_roll_statistics_journal(income):
    RollroomStatisticsDay.update_journal(income)
    RollroomStatisticsMonth.update_journal(income)


def update_roll_statistics(gid):
    room = RollRoom.objects.filter(uid=gid).first()
    if not room or room.state != GameState.End.value:
        return

    # income = room.pump_amount
    # if income > 0:
    #     with transaction.atomic():
    #         RollRoomPumpDay.update_amount(income)
    #         RollRoomPumpMonth.update_amount(income)
    income = room.total_amount
    if income:
        RollroomStatisticsDay.update_amount(income)
        RollroomStatisticsMonth.update_amount(income)




def update_roll_winner_new(gid):
    _logger.info('Update roll winner for room: {}'.format(gid))
    room = RollRoom.objects.filter(uid=gid).first()
    if not room:
        _logger.warning(f"RollRoom with gid {gid} not found.")
        return

    # 数据统计
    pump_percentage = get_roll_pump_percentage()
    pump_amount = round(room.total_fee * pump_percentage, 2)
    win_fee = room.total_fee - pump_amount
    room.user.update_balance(win_fee, _('Roll room fee'))
    room.pump_amount = pump_amount
    room.save()
    
    # 更新统计
    update_roll_statistics(room.uid)

    # 获取已胜利的投注和设置好的物品
    pre_winner = room.bets.exclude(win_items=None).order_by('id')
    set_items = room.items.exclude(bet=None).order_by('id')
    
    # 如果现有胜利者的数量超过最大胜利者数量
    if room.max_winner <= pre_winner.count():
        for set_item in set_items[:room.max_winner]:
            set_item.bet.victory = True
            set_item.bet.win_items_count = F('win_items_count') + 1  # 使用 F 表达式优化
            set_item.bet.win_amount = F('win_amount') + set_item.price + set_item.balance
            set_item.bet.save()
        # back_over_to_creater(room)
        return
    
    # 否则，更新已有的胜利者
    for winner in pre_winner:
        winner.victory = True
        win_items = winner.win_items.all()
        for item in win_items:
            winner.win_items_count = F('win_items_count') + 1  # 使用 F 表达式优化
            winner.win_amount = F('win_amount') + item.price + item.balance
        winner.save()

    # 计算还能加入的最大胜利者数量
    max_winner = room.max_winner - pre_winner.count()

    # 获取剩余未胜利的用户，随机选择获胜者
    over_user = room.bets.filter(win_items=None).order_by('id')
    winner_index = random.sample(range(over_user.count()), min(over_user.count(), max_winner))

    winner_list = []
    for wi in winner_index:
        winner = over_user[wi]
        winner_list.append(winner)
        winner.victory = True
        winner.save()

    _logger.info('Roll room: {}, winner list: {}'.format(room.uid, winner_list))

    # 随机分配物品给获胜者
    items = room.items.filter(bet=None).order_by('?')
    if not items:
        return
    
    winner_list_count = len(winner_list)

    if winner_list_count < items.count():
        # 获胜者少于物品数，直接按顺序分配
        for i, winner in enumerate(winner_list):
            items[i].bet = winner
            items[i].save()
            winner.win_items_count = 1
            winner.win_amount = items[i].price + items[i].balance
            winner.save()
        back_over_to_creater(room)

    elif winner_list_count > items.count():
        # 获胜者多于物品数，随机选择部分获胜者
        index_list = random.sample(range(len(winner_list)), len(items))
        for i, l in enumerate(index_list):
            items[i].bet = winner_list[l]
            items[i].save()
            winner_list[l].win_items_count = 1
            winner_list[l].win_amount = items[i].price + items[i].balance
            winner_list[l].save()

    elif winner_list_count == items.count():
        # 获胜者和物品数相等，按顺序分配
        for i, winner in enumerate(winner_list):
            items[i].bet = winner
            items[i].save()
            winner.win_items_count = 1
            winner.win_amount = items[i].price + items[i].balance
            winner.save()

    # 如果某个获胜者的总金额为 0，标记为失败
    for winner in winner_list:
        if winner.win_amount == 0:
            winner.victory = False
            winner.save()


def back_over_to_creater(room):
    room_user = room.user
    pids = []
    balance_list = []
    for i in room.items.filter(package__user=room_user):
        if i.item_type == CaseItemType.Assets.value:
            pids.append(i.package.uid)
        else:
            balance_list.append(i.balance)
    if len(pids) > 0:
        PackageItem.update_state(pids, PackageState.Gaming.value, PackageState.Available.value, room_user)
    if len(balance_list) > 0:
        for balance in balance_list:
            room_user.update_balance(balance, 'Roll房退还')


def update_roll_winner(gid):
    _logger.info('Update roll winner for room: {}'.format(gid))
    room = RollRoom.objects.filter(uid=gid).first()
    if not room:
        return

    # 数据统计
    pump_percentage = get_roll_pump_percentage()
    pump_amount = round(room.total_fee * pump_percentage, 2)
    win_fee = room.total_fee - pump_amount
    room.user.update_balance(win_fee, _('Roll room fee'))
    room.pump_amount = pump_amount
    room.save()
    update_roll_statistics(room.uid)

    pre_winner = room.bets.exclude(win_items=None).order_by('id')
    set_items = room.items.exclude(bet=None).order_by('id')
    if room.max_winner <= pre_winner.count():
        for set_item in set_items[:room.max_winner]:
            set_item.bet.victory = True
            set_item.bet.win_items_count = set_item.bet.win_items_count + 1
            set_item.bet.win_amount = set_item.price + set_item.balance
            set_item.bet.save()
        back_over_to_creater(room)
        return
    else:
        for winner in pre_winner:
            winner.victory = True
            win_items = winner.win_items.all()
            for item in win_items:
                winner.win_items_count = winner.win_items_count + 1
                winner.win_amount = item.price + item.balance
            winner.save()

    max_winner = room.max_winner - pre_winner.count()

    winner_list = []
    # 参与者小于最多获胜人数
    over_user = room.bets.filter(win_items=None).order_by('id')
    if over_user.count() <= max_winner:
        winner_index = list(range(over_user.count()))
    else:
        winner_index = random.sample(range(over_user.count()), max_winner)

    for wi in winner_index:
        winner = over_user[wi]
        winner_list.append(winner)
        winner.victory = True
        winner.save()

    _logger.info('Roll room: {}, winner list: {}'.format(room.uid, winner_list))
    items = room.items.filter(bet=None).order_by('?')
    if not items:
        return
    winner_list_count = len(winner_list)
    if winner_list_count < items.count():
        for i, winner in enumerate(winner_list):
            items[i].bet = winner
            items[i].save()
            winner.win_items_count = 1
            winner.win_amount = items[i].price + items[i].balance
            winner.save()
        back_over_to_creater(room)

    elif winner_list_count > items.count():
        index_list = random.sample(range(len(winner_list)), len(items))
        for i, l in enumerate(index_list):
            items[i].bet = winner_list[l]
            items[i].save()
            winner_list[l].win_items_count = 1
            winner_list[l].win_amount = items[i].price + items[i].balance
            winner_list[l].save()

    elif winner_list_count == items.count():
        for i, winner in enumerate(winner_list):
            items[i].bet = winner
            items[i].save()
            winner.win_items_count = 1
            winner.win_amount = items[i].price + items[i].balance
            winner.save()

    for winner in winner_list:
        if winner.win_amount == 0:
            winner.victory = False
            winner.save()


def back_items_to_creater(uid):
    try:
        room = RollRoom.objects.filter(uid=uid).first()
        if not room:
            return

        user = room.user
        items = room.items.all()
        pids = []
        balance_list = []
        for i in items:
            if i.item_type == CaseItemType.Assets.value:
                pids.append(i.package.uid)
            else:
                balance_list.append(i.balance)
        if len(pids) > 0:
            PackageItem.update_state(pids, PackageState.Gaming.value, PackageState.Available.value, user)
        if len(balance_list) > 0:
            for balance in balance_list:
                user.update_balance(balance, _('Rollroom cancel'))
    except Exception as e:
        _logger.exception(e)



def cache_roll_room_data(uid):
    logger = logging.getLogger(__name__)
    room = RollRoom.objects.filter(uid=uid).first()
    if not room:
        return
    room_key = 'roll:{}'.format(uid)
    #print(room_key)
    try:
        if room.state in [GameState.End.value, GameState.Cancelled.value]:
            cache.delete(room_key)
        else:
            room_data = RollRoomSerializer(room, fields=('uid', 'begin_ts_val', 'due_ts_val', 'items_count', 'bets',
                                                         'state', 'min_joiner')).data
            # 使用 DjangoJSONEncoder 将数据序列化为 JSON 字符串
            room_data_json = json.dumps(room_data, cls=DjangoJSONEncoder)
            cache.set(room_key, room_data_json, settings.MONTH_REDIS_TIMEOUT)
    except Exception as e:
        logger.error("An error occurred while caching roll room data for uid %s: %s", uid, e)



def check_rollroom_bot():
    while True:
        try:
            if not is_connection_usable():
                connection.close()

            bots = RollroomBot.objects.filter(enable=True)
            rooms = RollRoom.objects.filter(state=GameState.Joinable.value)
            for room in rooms:
                for bot in bots:
                    #is_join = RollRoomBet.objects.filter(room=room, user=bot.user).first()
                    is_join = RollRoomBet.objects.filter(user=bot.user).first()
                    join = False
                    if not is_join:
                        last_open_past = (timezone.now() - room.begin_ts).seconds
                        if last_open_past < bot.open_idle_min:
                            continue
                        elif last_open_past > bot.open_idle_max:
                            join = True
                        else:
                            # 开箱时间间隔随机化
                            open_probability = 1 / (bot.open_idle_max - last_open_past + 1)
                            percentage = numpy.random.uniform(0, 1)
                            join = open_probability > percentage
                    if join:
                        join_roll_room(bot.user, room.uid)
                        break
        except ParamException as pe:
            pass
        except Exception as e:
            _logger.exception(e)
        finally:
            time.sleep(5)


def check_rollroom_bot_appoint():
    while True:
        try:
            if not is_connection_usable():
                connection.close()

            rooms = RollRoom.objects.filter(state=GameState.Joinable.value)
            for room in rooms:
                bots = room.bot_bet_room.all()
                for bot in bots:
                    bot = bot.bot
                    is_join = RollRoomBet.objects.filter(room=room, user=bot.user).first()

                    join = False
                    if not is_join:
                        last_open_past = (timezone.now() - room.begin_ts).seconds
                        if last_open_past < bot.open_idle_min:
                            continue
                        elif last_open_past > bot.open_idle_max:
                            join = True
                        else:
                            # 开箱时间间隔随机化
                            open_probability = 1 / (bot.open_idle_max - last_open_past + 1)
                            percentage = numpy.random.uniform(0, 1)
                            join = open_probability > percentage
                    if join:
                        join_roll_room(bot.user, room.uid)
                        break
        except ParamException as pe:
            pass
        except Exception as e:
            _logger.exception(e)
        finally:
            time.sleep(5)


def check_rollroom_bot_appoint_new():
    try:
        # 遍历所有处于可加入状态的房间
        rooms = RollRoom.objects.filter(state=GameState.Joinable.value)
        for room in rooms:
            # 获取所有启用的机器人
            bots = RollroomBot.objects.filter(enable=True)
            # 筛选出尚未加入当前房间的机器人
            bots_not_joined = [bot for bot in bots if not RollRoomBet.objects.filter(room=room, user=bot.user).exists()]
            
            # 如果有机器人尚未加入，则随机选择一定数量的机器人加入房间
            if bots_not_joined:
                num_bots_to_join = random.randint(1, min(int(get_roll_bet_join()), len(bots_not_joined)))
                selected_bots = random.sample(bots_not_joined, num_bots_to_join)

                # 逐一让机器人加入房间，每次成功后等待
                for bot in selected_bots:
                    try:
                        join_roll_room(bot.user, room.uid)  # 调用加入房间的逻辑
                        _logger.info(f"Bot {bot.user} joined roll room {room.uid} successfully")
                        time.sleep(60)  # 成功加入后等待一段时间
                    except Exception as e:
                        # 记录单个机器人加入失败的情况，但继续尝试其他机器人
                        _logger.error(f"Failed to add bot {bot.user} to roll room {room.uid}: {str(e)}")

            # 每次处理完一个房间，等待一段时间
            time.sleep(60)  # 可以根据需求调整间隔

    except Exception as e:
        _logger.exception("Error in check_rollroom_bot_appoint_new: %s", str(e))
    finally:
        # 全部处理完毕后，等待一定时间再重新检查
        time.sleep(60)  # 根据需要调整重新检查的时间间隔

def get_roll_list(user, query, fields, page, page_size, is_creator, is_joiner, items_preview_count, no_password,
                  is_running, is_end, language='zh-hans'):
    cache_key = f'new_api_roll:rolllist:{hash(frozenset(query.items()))}_{page}_{page_size}'
    cached_data = cache.get(cache_key)
    # if cached_data:
    #     return RespCode.Succeed.value, cached_data

    roll_due_day_max = get_roll_due_day_max()
    end_threshold = timezone.now() - timedelta(days=roll_due_day_max)

    # 基本查询集
    base_query = Q(enable=True) & (
        Q(state__in=[GameState.Joinable.value, GameState.Joining.value, GameState.Full.value, GameState.End.value]) |
        (Q(user=user) & Q(state=GameState.Initial.value)) |
        (Q(state=GameState.End.value) & Q(due_ts__gte=end_threshold))
    ) & Q(**query)

    if is_running:
        base_query &= Q(state__in=[GameState.Joinable.value, GameState.Joining.value, GameState.Full.value])
    if is_end:
        base_query &= Q(state=GameState.End.value)
    if is_creator:
        base_query &= Q(user=user)
    if is_joiner:
        base_query &= Q(bets__user=user)
    if no_password:
        base_query &= Q(password="")

    if user and (user.username == '18113051550' or user.username == 'admin'):
        queryset = RollRoom.objects.filter(base_query).order_by('state', '-official', '-create_time')
    else:
        queryset = RollRoom.objects.filter(base_query, debug=False).order_by('state', '-official', '-create_time')

    
    paginator = Paginator(queryset, page_size)
    rooms = paginator.page(page)

    # 序列化数据时传递 `items_preview_count` 到上下文
    rooms_data = RollRoomSerializer(
        rooms, many=True, fields=fields,
        context={'user': user, 'items_preview_count': items_preview_count, 'language': language}
    ).data

    resp = {
        'items': rooms_data,
        'total': paginator.count,
        'page': page,
        'page_size': page_size
    }

    cache.set(cache_key, resp, timeout=5 * 60)
    
    return RespCode.Succeed.value, resp

def get_roll_skin_list(query, fields, page, page_size):
    """
    获取 RollRoomItem 的皮肤列表，通过 item_id 直接获取 ItemInfo 和 ItemPrice 数据。
    """
    # 缓存键
    cache_key = f'new_api_roll:skin_list:{query}:{fields}:{page}:{page_size}'
    cached_data = cache.get(cache_key)
    if cached_data:
        return RespCode.Succeed.value, cached_data

    # 获取房间信息
    rid = query.get('room__uid')
    roll_room = RollRoom.objects.filter(uid=rid).first()
    if not roll_room:
        return RespCode.InvalidParams.value, "房间不存在"

    # 查询 RollRoomItem
    queryset = RollRoomItem.objects.filter(**query).order_by('-price')
    paginator = Paginator(queryset, page_size)
    items = paginator.page(page)

    # 构建返回数据
    renamed_items = []
    for item in items:
        # 防御性检查，确保 item_id 存在
        if not item.item_id:
            continue
        
        # 获取 ItemInfo 和 ItemPrice
        try:
            item_info = ItemInfo.objects.get(id=item.item_id)
        except ItemInfo.DoesNotExist:
            _logger.warning(f"ItemInfo 不存在，item_id: {item.item_id}")
            continue
        
        item_price = getattr(item_info, 'item_price', None)
        price = item_price.price if item_price else None
        
        renamed_item = {
            'item_id': item_info.id,
            'item_name': item_info.market_name_cn,
            'new_market_name': item_info.market_name,
            'image': _steam_img_base + item_info.icon_url_large if item_info.icon_url_large else None,
            'price': price or 0,  # 如果价格不可用则默认为 0
            'color': item_info.custom_rarity if item_info.custom_rarity else item_info.rarity_color,
        }
        renamed_items.append(renamed_item)

    # 返回分页数据
    resp = {
        'items': renamed_items,
        'total': paginator.count,
        'page': page,
        'limit': page_size
    }

    # 写入缓存
    cache.set(cache_key, resp, timeout=60 * 5)
    return RespCode.Succeed.value, resp

def get_roll_user_list(query, fields, page, page_size):
    # 缓存
    cache_key = f'new_api_roll:user_list:{query}:{fields}:{page}:{page_size}'
    cached_data = cache.get(cache_key)
    # if cached_data:
    #     print('get from cache')
    #     return RespCode.Succeed.value, cached_data

    # 获取房间的状态
    rid = query.get('room__uid')
    roll_room = RollRoom.objects.filter(uid=rid).first()
    if not roll_room:
        return RespCode.InvalidParams.value, "房间不存在"

    # 构建基本查询集
    queryset = RollRoomBet.objects.filter(**query).order_by('-create_time')


    # 分页处理
    paginator = Paginator(queryset, page_size)
    try:
        bets = paginator.page(page)
    except PageNotAnInteger:
        bets = paginator.page(1)
    except EmptyPage:
        bets = paginator.page(paginator.num_pages)

    # 序列化数据
    #bets_data = RollRoomBetSerializer(bets, many=True, fields=fields).data
    renamed_items = []
    for item in bets:
        renamed_item = {
            'user_id': item.user.id,
            'user_name': '违规用户' if not item.user.is_active else item.user.steam.personaname,
            'is_active': item.user.is_active,
            'avatar': item.user.profile.avatar.url,
            
        }
        renamed_items.append(renamed_item)


    resp = {
        'items': renamed_items,
        'total': paginator.count
    }
    # 将数据缓存5分钟
    cache.set(cache_key, resp, timeout=5 * 60)
    return RespCode.Succeed.value, resp

def get_roll_winner_list(query, fields, page, page_size):
    # 缓存
    cache_key = f'new_api_roll:winner_list:{query}:{fields}:{page}:{page_size}'
    cached_data = cache.get(cache_key)
    if cached_data:
        return RespCode.Succeed.value, cached_data

    # 获取房间的状态
    rid = query.get('room__uid')
    roll_room = RollRoom.objects.filter(uid=rid).first()
    if not roll_room:
        return RespCode.InvalidParams.value, "房间不存在"

    # 构建基本查询集 
    queryset = RollRoomItem.objects.filter(**query, bet_id__isnull=False).order_by('-price')

    # 分页处理
    paginator = Paginator(queryset, page_size)
    try:
        bets = paginator.page(page)
    except PageNotAnInteger:
        bets = paginator.page(1)
    except EmptyPage:
        bets = paginator.page(paginator.num_pages)

    # 序列化数据
    renamed_items = []
    for item in bets:
        # 防御性检查，确保 bet 和 item_id 存在
        if not item.bet or not item.item_id:
            continue

        try:
            # 通过 item_id 获取 ItemInfo
            item_info = ItemInfo.objects.get(id=item.item_id)

            renamed_item = {
                'user_id': item.bet_id,
                'user_name': item.bet.user.steam.personaname,
                'user_avatar': item.bet.user.profile.avatar.url,
                'item_id': item_info.id,
                'item_name': item_info.market_name_cn,
                'item_color': item_info.custom_rarity if item_info.custom_rarity else item_info.rarity_color,
                'item_price': get_item_price(item_info.market_hash_name),
                'item_image': _steam_img_base + item_info.icon_url_large,
            }
            renamed_items.append(renamed_item)
        except ItemInfo.DoesNotExist:
            _logger.warning(f"ItemInfo 不存在，item_id: {item.item_id}")
            continue

    resp = {
        'items': renamed_items,
        'total': paginator.count
    }
    # 将数据缓存5分钟
    cache.set(cache_key, resp, timeout=5 * 60)
    return RespCode.Succeed.value, resp

    
def get_roll_detail(query, fields):
    rid = query.get('uid')    
    # 缓存
    # cache_key = f'new_api_roll:detail:{rid}'
    # cached_data = cache.get(cache_key)
    # if cached_data: 
    #     print('get from cache')
    #     return RespCode.Succeed.value, cached_data

    # 获取房间的状态
    #rid = query.get('uid')
    roll_room = RollRoom.objects.filter(uid=rid).first()
    if not roll_room:
        return RespCode.InvalidParams.value, "房间不存在"
    # 序列化数据
    renamed_item = {
        'name': roll_room.name,
        'uid': roll_room.uid,
        'official': roll_room.official,
        'description': roll_room.desc,
        'begin_time': roll_room.begin_ts,
        'end_time': roll_room.due_ts,
        'password': roll_room.password,
        'min_joiner': roll_room.min_joiner,
        'max_joiner': roll_room.max_joiner,
        'items_count': roll_room.items_count,
        'max_winner': roll_room.max_winner,
        'fee': roll_room.fee,
        'state': roll_room.state,
        'charge_limit': roll_room.charge_limit,
        'charge_begin_time': roll_room.charge_begin_time,
        'charge_end_time': roll_room.charge_end_time,   
        'total_amount': roll_room.total_amount,
        'win_time': roll_room.win_ts,

            
        }     

    
    # 构建返回数据
    resp = {
        'items': renamed_item
    }
    # 将数据缓存24小时
    #cache.set(cache_key, resp, timeout=60 * 60 * 24)
    return RespCode.Succeed.value, resp


def get_roll_user_num(uid):
    # 缓存
    cache_key = f'new_api_roll:user_num:{uid}'
    cached_data = cache.get(cache_key)
    # if cached_data:
    #     print('get from cache')
    #     return cached_data

    # 获取房间的状态
    roll_room = RollRoom.objects.filter(uid=uid).first()
    if not roll_room:
        return RespCode.InvalidParams.value, "房间不存在"

    # 构建基本查询集
    queryset_count = RollRoomBet.objects.filter(room=roll_room).count()
    
    # 构建返回数据
    resp = {
        'items': queryset_count
    }
    # 将数据缓存5分钟
    cache.set(cache_key, resp, timeout=5 * 60)
    return RespCode.Succeed.value, resp



def roll_user_join(user_param, uid, password=None):
    try:
        # 先查询房间，确保用户不会重复加入
        room = RollRoom.objects.filter(uid=uid, state=GameState.Joinable.value).exclude(user=user_param).first()
        if not room:
            return RespCode.InvalidParams.value, _('Invalid room')
        
        # 检查用户是否已经加入房间
        # if RollRoomBet.objects.filter(user=user_param, room=room).exists():
        #     return RespCode.InvalidParams.value, _('Already joined')

        # 验证房间密码
        # if password != room.password:
        #     return RespCode.InvalidParams.value, _('Invalid password')
        
        # 如果房间有充值条件，执行检查
        # if room.charge_limit:
        #     charge_filter = user_param.charges.filter(state=ChargeState.Succeed.value)
        #     if room.charge_begin_time:
        #         charge_filter = charge_filter.filter(pay_time__gte=room.charge_begin_time)
        #     if room.charge_end_time:
        #         charge_filter = charge_filter.filter(pay_time__lte=room.charge_end_time)
            
        #     charge_amount = charge_filter.aggregate(Sum('amount')).get('amount__sum') or 0
        #     if room.charge_limit > charge_amount:
        #         return RespCode.BusinessError.value, _('充值金额小于参加条件')

        # 处理收费房间类型
        # if room.type == RollType.Charge.value:
        #     update_result = user_param.update_balance(-room.fee, _('Roll room bet'))
        #     if not update_result:
        #         return RespCode.UpdateFailed.value, _('Failed to update balance')

        # 创建用户的投注记录
        bet = RollRoomBet.objects.create(user=user_param, room=room)

        # 更新房间状态，如果人数已满，结束房间
        if room.bets.count() >= room.max_joiner:
            room.state = GameState.End.value
            _logger.info('Roll room full: {}'.format(room.uid))

        room.save()

        # 清除缓存
        cache_key = f'new_api_roll:detail:{uid}'
        cache.delete(cache_key)

        return RespCode.Succeed.value, {'bet': bet.uid}

    except Exception as e:
        _logger.exception(f"Error in roll_user_join: {e}")
        return RespCode.Exception.value, _('发生错误')
        

def roll_check_join_status(user_param, uid):
    """
    检查用户是否可以加入房间
    :param user_param: 当前用户
    :param uid: 房间uid 
    :return:
    """
    joinstatus = True
    message = '立即参与'
    # 获取房间信息并检查状态和创建者
    room = RollRoom.objects.filter(uid=uid).first()

    if not room:
        joinstatus = False
        message = '房间不存在'
    else:
        if room.state == GameState.Initial.value:
            joinstatus = False
            message = '活动未开始'
        elif room.state == GameState.End.value:
            joinstatus = False
            message = '活动已结束'
        # 检查用户是否是房间创建者
        elif room.user == user_param:
            joinstatus = False
            message = '您不能加入自己的房间'
        # 检查用户是否已加入房间
        elif RollRoomBet.objects.filter(user=user_param, room=room).exists():
            joinstatus = False
            message = '您已参加该活动'
        # 检查是否已满员
        # elif room.bets.count() >= room.max_joiner:
        #     joinstatus = False
        #     message = '活动已满员'
        # 检查房间状态
        

    
    data = {
        'status': joinstatus,
        'message': message,
    }
    
    resp = {
        'items': data,
    }

    return RespCode.Succeed.value, resp




def deposit_roll_items_newapi(user, uid, items):
    """
    存入物品
    :param user: 当前用户
    :param uid: 房间uid
    :param items: 放入的物品
    :return: 响应码和信息
    """
    
    def get_room(user, uid):
        """
        获取房间信息
        :param user: 当前用户
        :param uid: 房间uid
        :return: 房间对象或None
        """
        return RollRoom.objects.select_for_update().filter(
            user=user, uid=uid, state__in=[GameState.Initial.value, GameState.Joinable.value]
        ).first()

    def validate_room_and_items(room, items):
        """
        验证房间和物品
        :param room: 房间对象
        :param items: 物品列表
        :return: 响应码和信息
        """
        if not room:
            return RespCode.InvalidParams.value, _('Invalid room')
        if room.max_winner != len(items):
            return RespCode.InvalidParams.value, _('存入数量必须等于获胜人数')
        return None

    def create_roll_room_items(room, items):
        """
        创建 RollRoomItem 对象并更新房间信息
        :param room: 房间对象
        :param items: 物品列表
        :return: None
        """
        packages = PackageItem.objects.filter(id__in=items)
        # print(packages)
        for package in packages:
            price = get_item_price_by_package(package)
            RollRoomItem.objects.create(room=room, package=package, price=price, item_type=CaseItemType.Assets.value)
            room.total_amount = round(room.total_amount + price, 2)
            room.items_count += 1
        if room.state == GameState.Initial.value and room.items_count:
            room.state = GameState.Joining.value
        room.save()

    try:
        with transaction.atomic():
            room = get_room(user, uid)
            validation_response = validate_room_and_items(room, items)
            if validation_response:
                return validation_response

            create_roll_room_items(room, items)
            update_roll_statistics_journal(room.total_amount)

            return RespCode.Succeed.value, {}
    except Exception as e:
        _logger.exception(e)
        return RespCode.Exception.value, str(e)
    
def get_new_roll_list():
    """
    获取正在进行中的ROLL列表
    :param fields: 字段列表
    :return: 响应码和数据
    """

    # 缓存
    cache_key = f'new_roll_list'
    cached_data = cache.get(cache_key)
    # if cached_data:
    #     print('get from cache')
    #     return cached_data

    # 构建基本查询集
    queryset = RollRoom.objects.filter(state__in=[GameState.Joinable.value]).order_by('-begin_ts')

    # 序列化数据
    renamed_items = []
    for item in queryset:
        renamed_item = {
            'name': item.name,
            'uid': item.uid,
            'begin_time': item.begin_ts,
            'end_time': item.due_ts,
            'min_joiner': item.min_joiner,
            'max_joiner': item.max_joiner,
            'items_count': item.items_count,
            'max_winner': item.max_winner,
            'fee': item.fee,
            'total_amount': item.total_amount,
        
                  }
        renamed_items.append(renamed_item)

    # 构建返回数据
    resp = {
        'items': renamed_items
    }
    # 将数据缓存5分钟
    cache.set(cache_key, resp, timeout=5 * 60)
    return RespCode.Succeed.value, resp


# 更新roll放饰品的item_id
def update_item_ids():
    """
    遍历所有 RollRoomItem 并更新 item_id 的值。
    如果 package 为空，则跳过该记录。
    """
    items = RollRoomItem.objects.all()
    updated_count = 0  # 统计更新的记录数

    with transaction.atomic():
        for item in items:
            if item.package and item.package.item_info:
                item.item_id = str(item.package.item_info.id)
                item.save()
                updated_count += 1
                _logger.info(f"更新 RollRoomItem(id={item.id}) 的 item_id 为 {item.item_id}")
            else:
                _logger.warning(f"跳过 RollRoomItem(id={item.id})，因为 package 或 item_info 不存在")

    _logger.info(f"更新完成，共更新了 {updated_count} 条记录。")