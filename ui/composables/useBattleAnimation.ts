import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useBattleStore } from '~/stores/battle'

// 在文件顶部最前面插入
let _globalBattleAnimationListenersRegistered = false

// 🎯 对战动画管理器
export const useBattleAnimation = () => {
  const battleStore = useBattleStore()
  
  // 动画状态
  const animationState = ref<'idle' | 'preparing' | 'opening' | 'revealing' | 'finished'>('idle')
  const currentRound = ref(0)
  const totalRounds = ref(0)
  const animationId = ref<string>('')
  const serverTimestamp = ref(0)
  
  // 同步配置
  const syncConfig = ref({
    tolerance_ms: 100,
    max_delay_compensation: 500,
    enable_client_sync: true,
    adaptive_tolerance: true,
    high_latency_tolerance_ms: 200
  })
  
  // 参与者动画状态
  const participantsAnimation = ref<Map<string, {
    progress: number
    stage: string
    animation_duration: number
  }>>(new Map())
  
  // 计算属性
  const isAnimationActive = computed(() => animationState.value !== 'idle')
  const isOpening = computed(() => animationState.value === 'opening')
  const isRevealing = computed(() => animationState.value === 'revealing')
  
  // 记录第几轮 opening_start，用于控制台标记
  let openingCounter = 0
  
  // 🎯 处理回合开始消息
  const handleRoundStart = (data: any) => {
    console.log('[🎮BATTLE-ANIMATION] 回合开始:', data)
    
    animationState.value = 'preparing'
    currentRound.value = data.round || 0
    totalRounds.value = data.total_rounds || 0
    serverTimestamp.value = data.server_timestamp || Date.now()
    
    // 更新同步配置
    if (data.sync_config) {
      syncConfig.value = { ...syncConfig.value, ...data.sync_config }
    }
    
    // 初始化参与者动画状态
    if (data.participants) {
      participantsAnimation.value.clear()
      data.participants.forEach((participant: any) => {
        participantsAnimation.value.set(participant.user.username, {
          progress: 0,
          stage: 'waiting',
          animation_duration: participant.animation_duration || 8000
        })
      })
    }
    
    // 派发回合开始事件
    window.dispatchEvent(new CustomEvent('battle:round_start', { detail: data }))
  }
  
  // 🎯 处理开箱动画开始消息
  const handleOpeningStart = (data: any) => {
    // 🔄 去重：相同animation_id短时间内只处理一次
    if (animationId.value && data.animation_id && animationId.value === data.animation_id && animationState.value === 'opening') {
      console.log('[🎮BATTLE-ANIMATION] ⚠ 重复 opening_start 忽略', data.animation_id)
      return
    }
    openingCounter += 1
    const roundTag = openingCounter === 1 ? '⭐️[R1]' : `[R${openingCounter}]`
    console.log(`[🎮BATTLE-ANIMATION]${roundTag} 开箱动画开始:`, data)
    
    animationState.value = 'opening'
    animationId.value = data.animation_id || ''
    serverTimestamp.value = data.server_timestamp || Date.now()
    
    // 更新同步配置
    if (data.sync_config) {
      syncConfig.value = { ...syncConfig.value, ...data.sync_config }
    }
    
    // 初始化所有参与者为开始状态
    if (data.participants) {
      data.participants.forEach((participant: any) => {
        participantsAnimation.value.set(participant.user.username, {
          progress: 0,
          stage: 'case_opening',
          animation_duration: participant.animation_duration || 8000
        })
      })
    }
    
    // 派发开箱开始事件
    window.dispatchEvent(new CustomEvent('battle:opening_start', { detail: data }))
  }
  
  // 🎯 处理动画进度同步消息
  const handleAnimationProgress = (data: any) => {
    console.log('[🎮BATTLE-ANIMATION] 动画进度同步:', data)
    
    if (data.participants) {
      data.participants.forEach((participant: any) => {
        const current = participantsAnimation.value.get(participant.user.username)
        if (current) {
          current.progress = participant.progress || 0
          current.stage = participant.current_stage || 'case_opening'
        }
      })
    }
    
    // 派发进度同步事件
    window.dispatchEvent(new CustomEvent('battle:animation_progress', { detail: data }))
  }
  
  // 🎯 处理回合结果消息
  const handleRoundResult = (data: any) => {
    console.log('[🎮BATTLE-ANIMATION] 回合结果 Raw:', data)

    animationState.value = 'revealing'

    // WebSocket包装层可能把真正payload放到 data.data 中
    const payload = data?.data ?? data

    const results: any[] | undefined = Array.isArray(payload) ? payload : payload?.results

    if (Array.isArray(results)) {
      results.forEach((res: any, idx: number) => {
        const winnerItem =
          res.items?.[0] /* ✅ 优先新版字段 */
          || res.open_items?.[0] /* ✅ 兼容旧版字段 */
          || res.item /* 单个字段 */
          || null
        const detail = {
          playerIndex: idx,
          playerId: res.user?.uid || res.user?.id || res.user?.username || null,
          playerName: res.user?.username || res.user?.nickname || null,
          item: winnerItem,
          raw: res
        }

        console.log('[🎮BATTLE-ANIMATION] ▶ 派发 decelerate:', detail)

        // 1) 通知对应的 BattleCaseAnimation 组件减速到获胜物品
        window.dispatchEvent(new CustomEvent('battle:decelerate', { detail }))

        // 2) 将战果添加至记录列表
        window.dispatchEvent(new CustomEvent('battle:add_record', { detail }))
      })
    } else {
      console.warn('[🎮BATTLE-ANIMATION] ⚠ 未找到 results 数组', payload)
    }

    // 广播回合结果（供其他模块使用）
    window.dispatchEvent(new CustomEvent('battle:round_result', { detail: payload }))

    // 回合结果后重置openingCounter在下一轮重新计数
    openingCounter = 0

    // 2 秒后重置动画状态，准备下一轮
    setTimeout(() => {
      animationState.value = 'idle'
    }, 2000)
  }
  
  // 🎯 处理对战结束消息
  const handleBattleEnd = (data: any) => {
    console.log('[🎮BATTLE-ANIMATION] 对战结束:', data)
    
    animationState.value = 'finished'
    
    // 派发对战结束事件
    window.dispatchEvent(new CustomEvent('battle:battle_end', { detail: data }))
  }
  
  // 🎯 处理时钟同步请求
  const handleTimeSyncRequest = (data: any) => {
    console.log('[🎮BATTLE-ANIMATION] 时钟同步请求:', data)
    
    // 计算客户端延迟
    const clientTimestamp = Date.now()
    const serverTimestamp = data.sync_request_timestamp || 0
    const latency = clientTimestamp - serverTimestamp
    
    console.log(`[🎮BATTLE-ANIMATION] 客户端延迟: ${latency}ms`)
    
    // 派发时钟同步事件
    window.dispatchEvent(new CustomEvent('battle:time_sync', { 
      detail: { latency, serverTimestamp, clientTimestamp } 
    }))
  }
  
  // 🎯 设置WebSocket监听器
  const setupSocketListeners = () => {
    if (_globalBattleAnimationListenersRegistered) return () => {}
    _globalBattleAnimationListenersRegistered = true
    console.log('[🎮BATTLE-ANIMATION] 设置WebSocket监听器')
    
    // 监听WebSocket事件
    const events = [
      { name: 'socket:round_start', handler: handleRoundStart },
      { name: 'socket:opening_start', handler: handleOpeningStart },
      { name: 'socket:animation_progress', handler: handleAnimationProgress },
      { name: 'socket:round_result', handler: handleRoundResult },
      { name: 'socket:battle_end', handler: handleBattleEnd },
      { name: 'socket:time_sync_request', handler: handleTimeSyncRequest }
    ]
    
    events.forEach(({ name, handler }) => {
      window.addEventListener(name, (event: any) => {
        console.log(`[🛰️BATTLE-ANIMATION] 👂 收到事件 ${name}`, event.detail)
        try {
          handler(event.detail)
        } catch (err) {
          console.error(`[🛰️BATTLE-ANIMATION] 处理 ${name} 出错`, err)
        }
      })
    })
    
    return () => {
      events.forEach(({ name, handler }) => {
        window.removeEventListener(name, (event: any) => {
          handler(event.detail)
        })
      })
    }
  }
  
  // 🎯 获取参与者的动画状态
  const getParticipantAnimation = (username: string) => {
    return participantsAnimation.value.get(username) || {
      progress: 0,
      stage: 'waiting',
      animation_duration: 8000
    }
  }
  
  // 🎯 检查是否为当前开箱玩家
  const isCurrentOpeningPlayer = (username: string) => {
    const participant = getParticipantAnimation(username)
    return participant.stage === 'case_opening' && participant.progress > 0
  }
  
  // 🎯 重置动画状态
  const resetAnimation = () => {
    animationState.value = 'idle'
    currentRound.value = 0
    totalRounds.value = 0
    animationId.value = ''
    serverTimestamp.value = 0
    participantsAnimation.value.clear()
  }
  
  // 组件挂载时设置监听器
  onMounted(() => {
    const cleanup = setupSocketListeners()
    
    onUnmounted(() => {
      cleanup()
    })
  })
  
  return {
    // 状态
    animationState: readonly(animationState),
    currentRound: readonly(currentRound),
    totalRounds: readonly(totalRounds),
    animationId: readonly(animationId),
    serverTimestamp: readonly(serverTimestamp),
    syncConfig: readonly(syncConfig),
    participantsAnimation: readonly(participantsAnimation),
    
    // 计算属性
    isAnimationActive,
    isOpening,
    isRevealing,
    
    // 方法
    getParticipantAnimation,
    isCurrentOpeningPlayer,
    resetAnimation,
    
    // 事件处理
    handleRoundStart,
    handleOpeningStart,
    handleAnimationProgress,
    handleRoundResult,
    handleBattleEnd,
    handleTimeSyncRequest
  }
} 