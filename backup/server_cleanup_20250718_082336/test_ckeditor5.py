#!/usr/bin/env python3
"""
测试CKEditor 5配置
"""
import os
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')
django.setup()

from django_ckeditor_5.fields import CKEditor5Field
from steambase.ckeditor5_fields import RichTextField5, RichTextUploadingField5

def test_ckeditor5_import():
    """测试CKEditor 5导入"""
    try:
        print("✅ CKEditor5Field导入成功")
        print("✅ RichTextField5导入成功")
        print("✅ RichTextUploadingField5导入成功")
        return True
    except Exception as e:
        print(f"❌ CKEditor 5导入失败: {e}")
        return False

def test_ckeditor5_config():
    """测试CKEditor 5配置"""
    try:
        from django.conf import settings
        configs = getattr(settings, 'CKEDITOR_5_CONFIGS', None)
        if configs:
            print(f"✅ CKEditor 5配置加载成功，包含 {len(configs)} 个配置")
            for config_name in configs.keys():
                print(f"  - {config_name}")
            return True
        else:
            print("❌ CKEditor 5配置未找到")
            return False
    except Exception as e:
        print(f"❌ CKEditor 5配置测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 开始测试CKEditor 5配置...")
    
    success_count = 0
    total_tests = 2
    
    if test_ckeditor5_import():
        success_count += 1
    
    if test_ckeditor5_config():
        success_count += 1
    
    print(f"\n📊 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 CKEditor 5配置测试全部通过！")
        return True
    else:
        print("❌ 部分测试失败，需要检查配置")
        return False

if __name__ == '__main__':
    main()
