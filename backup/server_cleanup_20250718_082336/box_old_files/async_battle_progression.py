#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
异步轮次推进系统
实现更可靠的轮次管理和WebSocket消息发送机制

核心特性：
1. 异步轮次推进，避免阻塞
2. 状态机驱动的对战流程
3. 消息队列确保消息顺序
4. 自动重试和错误恢复
5. 详细的状态跟踪和日志
"""

import asyncio
import logging
import time
import json
from datetime import datetime, timedelta
from enum import Enum
from django.core.cache import cache
from django.db import transaction
from typing import Dict, List, Optional, Tuple

logger = logging.getLogger(__name__)

class BattleState(Enum):
    """对战状态枚举"""
    WAITING = "waiting"
    ROUND_STARTING = "round_starting"
    OPENING_STARTING = "opening_starting"
    OPENING_PROGRESS = "opening_progress"
    ROUND_RESULT = "round_result"
    BATTLE_ENDING = "battle_ending"
    COMPLETED = "completed"
    ERROR = "error"

class RoundState(Enum):
    """轮次状态枚举"""
    PENDING = "pending"
    STARTING = "starting"
    OPENING = "opening"
    CALCULATING = "calculating"
    COMPLETED = "completed"
    ERROR = "error"

class AsyncBattleProgressionManager:
    """异步对战推进管理器"""
    
    def __init__(self, room_uid: str):
        self.room_uid = room_uid
        self.cache_prefix = f"async_battle:{room_uid}"
        self.message_queue_key = f"{self.cache_prefix}:messages"
        self.state_key = f"{self.cache_prefix}:state"
        self.round_state_key = f"{self.cache_prefix}:round_state"
        
    async def initialize_battle_progression(self, total_rounds: int) -> bool:
        """初始化对战推进状态"""
        try:
            initial_state = {
                'battle_state': BattleState.WAITING.value,
                'current_round': 1,
                'total_rounds': total_rounds,
                'round_states': {str(i): RoundState.PENDING.value for i in range(1, total_rounds + 1)},
                'created_at': datetime.now().isoformat(),
                'last_update': datetime.now().isoformat(),
                'error_count': 0,
                'completed_rounds': 0
            }
            
            cache.set(self.state_key, initial_state, 3600)  # 1小时过期
            cache.delete(self.message_queue_key)  # 清空消息队列
            
            logger.info(f"对战推进状态初始化成功: room={self.room_uid}, rounds={total_rounds}")
            return True
            
        except Exception as e:
            logger.error(f"对战推进状态初始化失败: room={self.room_uid}, error={e}")
            return False
    
    async def get_battle_state(self) -> Optional[Dict]:
        """获取当前对战状态"""
        try:
            state = cache.get(self.state_key)
            if not state:
                logger.warning(f"对战状态不存在: room={self.room_uid}")
                return None
            return state
        except Exception as e:
            logger.error(f"获取对战状态失败: room={self.room_uid}, error={e}")
            return None
    
    async def update_battle_state(self, **updates) -> bool:
        """更新对战状态"""
        try:
            state = await self.get_battle_state()
            if not state:
                return False
            
            state.update(updates)
            state['last_update'] = datetime.now().isoformat()
            
            cache.set(self.state_key, state, 3600)
            
            logger.debug(f"对战状态更新: room={self.room_uid}, updates={updates}")
            return True
            
        except Exception as e:
            logger.error(f"对战状态更新失败: room={self.room_uid}, error={e}")
            return False
    
    async def update_round_state(self, round_num: int, new_state: RoundState) -> bool:
        """更新指定轮次的状态"""
        try:
            state = await self.get_battle_state()
            if not state:
                return False
            
            state['round_states'][str(round_num)] = new_state.value
            state['last_update'] = datetime.now().isoformat()
            
            cache.set(self.state_key, state, 3600)
            
            logger.info(f"轮次状态更新: room={self.room_uid}, round={round_num}, state={new_state.value}")
            return True
            
        except Exception as e:
            logger.error(f"轮次状态更新失败: room={self.room_uid}, round={round_num}, error={e}")
            return False
    
    async def queue_message(self, message_type: str, data: Dict, delay: float = 0) -> bool:
        """将消息加入队列"""
        try:
            message = {
                'type': message_type,
                'data': data,
                'timestamp': time.time(),
                'scheduled_time': time.time() + delay,
                'retry_count': 0,
                'room_uid': self.room_uid
            }
            
            # 获取当前队列
            queue = cache.get(self.message_queue_key, [])
            queue.append(message)
            
            # 按计划时间排序
            queue.sort(key=lambda x: x['scheduled_time'])
            
            cache.set(self.message_queue_key, queue, 3600)
            
            logger.debug(f"消息已加入队列: room={self.room_uid}, type={message_type}, delay={delay}s")
            return True
            
        except Exception as e:
            logger.error(f"消息入队失败: room={self.room_uid}, type={message_type}, error={e}")
            return False
    
    async def process_message_queue(self) -> int:
        """处理消息队列中的消息"""
        try:
            queue = cache.get(self.message_queue_key, [])
            if not queue:
                return 0
            
            current_time = time.time()
            processed_count = 0
            remaining_queue = []
            
            for message in queue:
                if message['scheduled_time'] <= current_time:
                    # 处理消息
                    success = await self._send_websocket_message(message)
                    if success:
                        processed_count += 1
                        logger.debug(f"消息处理成功: room={self.room_uid}, type={message['type']}")
                    else:
                        # 重试逻辑
                        message['retry_count'] += 1
                        if message['retry_count'] < 3:
                            message['scheduled_time'] = current_time + (message['retry_count'] * 2)  # 指数退避
                            remaining_queue.append(message)
                            logger.warning(f"消息处理失败，将重试: room={self.room_uid}, type={message['type']}, retry={message['retry_count']}")
                        else:
                            logger.error(f"消息处理最终失败: room={self.room_uid}, type={message['type']}")
                else:
                    # 未到处理时间
                    remaining_queue.append(message)
            
            # 更新队列
            cache.set(self.message_queue_key, remaining_queue, 3600)
            
            if processed_count > 0:
                logger.info(f"消息队列处理完成: room={self.room_uid}, processed={processed_count}, remaining={len(remaining_queue)}")
            
            return processed_count
            
        except Exception as e:
            logger.error(f"消息队列处理失败: room={self.room_uid}, error={e}")
            return 0
    
    async def _send_websocket_message(self, message: Dict) -> bool:
        """发送WebSocket消息"""
        try:
            from .business_room import (
                ws_send_round_start, ws_send_opening_start, 
                ws_send_round_result, ws_send_battle_end
            )
            
            msg_type = message['type']
            data = message['data']
            
            if msg_type == 'round_start':
                ws_send_round_start(
                    self.room_uid, 
                    data['round_num'], 
                    data['total_rounds'],
                    data.get('current_round', data['round_num'])
                )
            elif msg_type == 'opening_start':
                ws_send_opening_start(
                    self.room_uid, 
                    data['round_num'],
                    data.get('current_round', data['round_num'])
                )
            elif msg_type == 'round_result':
                ws_send_round_result(
                    self.room_uid, 
                    data['results'], 
                    data['round_num'],
                    data.get('current_round', data['round_num'])
                )
            elif msg_type == 'battle_end':
                ws_send_battle_end(
                    self.room_uid, 
                    data['winner'], 
                    data['final_results'],
                    data.get('current_round', data['round_num'])
                )
            else:
                logger.warning(f"未知消息类型: {msg_type}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"WebSocket消息发送失败: room={self.room_uid}, type={message['type']}, error={e}")
            return False
    
    async def start_next_round(self) -> bool:
        """开始下一轮次"""
        try:
            state = await self.get_battle_state()
            if not state:
                return False
            
            current_round = state['current_round']
            total_rounds = state['total_rounds']
            
            # 检查是否还有轮次
            if current_round > total_rounds:
                logger.warning(f"已达到最大轮次: room={self.room_uid}, current={current_round}, total={total_rounds}")
                return False
            
            # 更新轮次状态
            await self.update_round_state(current_round, RoundState.STARTING)
            await self.update_battle_state(battle_state=BattleState.ROUND_STARTING.value)
            
            # 队列消息：发送round_start
            await self.queue_message('round_start', {
                'round_num': current_round,
                'total_rounds': total_rounds,
                'current_round': current_round
            })
            
            # 延迟发送opening_start消息
            await self.queue_message('opening_start', {
                'round_num': current_round,
                'current_round': current_round
            }, delay=1.0)
            
            # 更新状态
            await self.update_battle_state(battle_state=BattleState.OPENING_STARTING.value)
            await self.update_round_state(current_round, RoundState.OPENING)
            
            logger.info(f"轮次开始: room={self.room_uid}, round={current_round}/{total_rounds}")
            return True
            
        except Exception as e:
            logger.error(f"轮次开始失败: room={self.room_uid}, error={e}")
            await self.update_battle_state(battle_state=BattleState.ERROR.value, error_count=state.get('error_count', 0) + 1)
            return False
    
    async def complete_round(self, round_results: List[Dict]) -> bool:
        """完成当前轮次"""
        try:
            state = await self.get_battle_state()
            if not state:
                return False
            
            current_round = state['current_round']
            total_rounds = state['total_rounds']
            
            # 更新轮次状态
            await self.update_round_state(current_round, RoundState.CALCULATING)
            await self.update_battle_state(battle_state=BattleState.ROUND_RESULT.value)
            
            # 队列消息：发送round_result
            await self.queue_message('round_result', {
                'results': round_results,
                'round_num': current_round,
                'current_round': current_round
            })
            
            # 标记轮次完成
            await self.update_round_state(current_round, RoundState.COMPLETED)
            completed_rounds = state.get('completed_rounds', 0) + 1
            await self.update_battle_state(completed_rounds=completed_rounds)
            
            # 检查是否需要进行下一轮次
            if current_round < total_rounds:
                # 推进到下一轮次
                next_round = current_round + 1
                await self.update_battle_state(current_round=next_round)
                
                # 延迟开始下一轮次 (Python 3.6兼容性)
                if hasattr(asyncio, 'create_task'):
                    asyncio.create_task(self._delayed_next_round(delay=2.0))
                else:
                    asyncio.ensure_future(self._delayed_next_round(delay=2.0))
                
                logger.info(f"轮次完成，将开始下一轮: room={self.room_uid}, completed={current_round}, next={next_round}")
            else:
                # 所有轮次完成，准备结束对战
                await self.update_battle_state(battle_state=BattleState.BATTLE_ENDING.value)
                logger.info(f"所有轮次完成，准备结束对战: room={self.room_uid}")
            
            return True
            
        except Exception as e:
            logger.error(f"轮次完成失败: room={self.room_uid}, error={e}")
            await self.update_battle_state(battle_state=BattleState.ERROR.value)
            return False
    
    async def _delayed_next_round(self, delay: float = 2.0):
        """延迟开始下一轮次"""
        try:
            await asyncio.sleep(delay)
            await self.start_next_round()
        except Exception as e:
            logger.error(f"延迟开始下一轮次失败: room={self.room_uid}, error={e}")
    
    async def end_battle(self, winner_data: Dict, final_results: List[Dict]) -> bool:
        """结束对战"""
        try:
            state = await self.get_battle_state()
            if not state:
                return False
            
            current_round = state['current_round']
            
            # 队列消息：发送battle_end
            await self.queue_message('battle_end', {
                'winner': winner_data,
                'final_results': final_results,
                'round_num': current_round,
                'current_round': current_round
            })
            
            # 更新状态为完成
            await self.update_battle_state(battle_state=BattleState.COMPLETED.value)
            
            logger.info(f"对战结束: room={self.room_uid}")
            return True
            
        except Exception as e:
            logger.error(f"对战结束失败: room={self.room_uid}, error={e}")
            return False
    
    async def cleanup(self):
        """清理对战状态和消息队列"""
        try:
            cache.delete(self.state_key)
            cache.delete(self.message_queue_key)
            logger.info(f"对战状态清理完成: room={self.room_uid}")
        except Exception as e:
            logger.error(f"对战状态清理失败: room={self.room_uid}, error={e}")

# 全局消息处理任务
class AsyncBattleMessageProcessor:
    """异步对战消息处理器"""
    
    @staticmethod
    async def process_all_battles():
        """处理所有对战房间的消息队列"""
        try:
            # 获取所有活跃的对战房间
            from .models import CaseRoom, GameState
            
            active_rooms = CaseRoom.objects.filter(
                state__in=[GameState.Running.value]
            ).values_list('uid', flat=True)
            
            processed_total = 0
            for room_uid in active_rooms:
                manager = AsyncBattleProgressionManager(room_uid)
                count = await manager.process_message_queue()
                processed_total += count
            
            if processed_total > 0:
                logger.info(f"全局消息处理完成: processed={processed_total}, rooms={len(active_rooms)}")
            
            return processed_total
            
        except Exception as e:
            logger.error(f"全局消息处理失败: error={e}")
            return 0
