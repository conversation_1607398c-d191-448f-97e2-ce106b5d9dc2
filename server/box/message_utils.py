#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WebSocket消息处理修复模块
解决"expected str instance, list found"等数据类型错误
"""

import json
import logging
from typing import Any, Dict, List, Union, Optional
from decimal import Decimal
from datetime import datetime

logger = logging.getLogger(__name__)


class MessageDataSanitizer:
    """消息数据清理器"""
    
    @staticmethod
    def sanitize_for_websocket(data: Any) -> Any:
        """清理数据以确保WebSocket兼容性"""
        try:
            if isinstance(data, dict):
                return {k: MessageDataSanitizer.sanitize_for_websocket(v) 
                       for k, v in data.items()}
            
            elif isinstance(data, list):
                return [MessageDataSanitizer.sanitize_for_websocket(item) 
                       for item in data]
            
            elif isinstance(data, tuple):
                return [MessageDataSanitizer.sanitize_for_websocket(item) 
                       for item in data]
            
            elif isinstance(data, Decimal):
                return float(data)
            
            elif isinstance(data, datetime):
                return data.isoformat()
            
            elif isinstance(data, (int, float, str, bool)) or data is None:
                return data
            
            else:
                # 其他类型转换为字符串
                return str(data)
                
        except Exception as e:
            logger.error(f"数据清理失败: {e}, data={data}")
            return str(data) if data is not None else None
    
    @staticmethod
    def validate_message_structure(message: Dict[str, Any]) -> bool:
        """验证消息结构"""
        try:
            # 检查必要字段
            required_fields = ['type', 'data']
            for field in required_fields:
                if field not in message:
                    logger.error(f"消息缺少必要字段: {field}")
                    return False
            
            # 检查数据类型
            if not isinstance(message['type'], str):
                logger.error("消息类型必须是字符串")
                return False
            
            # 验证数据可序列化
            try:
                json.dumps(message, ensure_ascii=False)
            except (TypeError, ValueError) as e:
                logger.error(f"消息无法序列化: {e}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"消息结构验证失败: {e}")
            return False


class BattleMessageBuilder:
    """对战消息构建器"""
    
    def __init__(self):
        self.sanitizer = MessageDataSanitizer()
    
    def build_opening_start_message(self, room_uid: str, animation_id: str, 
                                   round_num: int, start_ts: int, 
                                   sequence: int) -> Dict[str, Any]:
        """构建开箱开始消息"""
        try:
            message = {
                'type': 'opening_start',
                'data': {
                    'room_uid': str(room_uid),
                    'animation_id': str(animation_id),
                    'round': int(round_num),
                    'start_ts': int(start_ts),
                    'sequence': int(sequence),
                    'timestamp': int(datetime.now().timestamp() * 1000)
                }
            }
            
            # 清理数据
            clean_message = self.sanitizer.sanitize_for_websocket(message)
            
            # 验证结构
            if not self.sanitizer.validate_message_structure(clean_message):
                raise ValueError("消息结构验证失败")
            
            return clean_message
            
        except Exception as e:
            logger.error(f"构建开箱开始消息失败: {e}")
            raise
    
    def build_round_result_message(self, room_uid: str, round_num: int, 
                                  animation_id: str, results: List[Dict], 
                                  sequence: int) -> Dict[str, Any]:
        """构建轮次结果消息"""
        try:
            # 确保results是列表且每个元素都是字典
            safe_results = []
            for result in results:
                if isinstance(result, dict):
                    safe_result = self.sanitizer.sanitize_for_websocket(result)
                    safe_results.append(safe_result)
                else:
                    logger.warning(f"跳过无效的结果数据: {type(result)} - {result}")
            
            message = {
                'type': 'round_result',
                'data': {
                    'room_uid': str(room_uid),
                    'round': int(round_num),
                    'animation_id': str(animation_id),
                    'results': safe_results,
                    'sequence': int(sequence),
                    'timestamp': int(datetime.now().timestamp() * 1000)
                }
            }
            
            # 清理数据
            clean_message = self.sanitizer.sanitize_for_websocket(message)
            
            # 验证结构
            if not self.sanitizer.validate_message_structure(clean_message):
                raise ValueError("消息结构验证失败")
            
            return clean_message
            
        except Exception as e:
            logger.error(f"构建轮次结果消息失败: {e}")
            raise
    
    def build_room_status_message(self, room_uid: str, status: str, 
                                 **kwargs) -> Dict[str, Any]:
        """构建房间状态消息"""
        try:
            data = {
                'room_uid': str(room_uid),
                'status': str(status),
                'timestamp': int(datetime.now().timestamp() * 1000)
            }
            
            # 添加额外参数
            for key, value in kwargs.items():
                data[key] = self.sanitizer.sanitize_for_websocket(value)
            
            message = {
                'type': 'room_status',
                'data': data
            }
            
            # 清理数据
            clean_message = self.sanitizer.sanitize_for_websocket(message)
            
            # 验证结构
            if not self.sanitizer.validate_message_structure(clean_message):
                raise ValueError("消息结构验证失败")
            
            return clean_message
            
        except Exception as e:
            logger.error(f"构建房间状态消息失败: {e}")
            raise


class WebSocketMessageSender:
    """WebSocket消息发送器"""
    
    def __init__(self):
        self.message_builder = BattleMessageBuilder()
    
    def send_message_safe(self, socket_handler, message: Dict[str, Any], 
                         room_uid: str = None) -> bool:
        """安全发送WebSocket消息"""
        try:
            # 验证消息
            if not isinstance(message, dict):
                logger.error(f"消息必须是字典类型: {type(message)}")
                return False
            
            # 清理消息数据
            clean_message = MessageDataSanitizer.sanitize_for_websocket(message)
            
            # 验证清理后的消息
            if not MessageDataSanitizer.validate_message_structure(clean_message):
                logger.error("消息结构验证失败")
                return False
            
            # 序列化消息
            try:
                message_json = json.dumps(clean_message, ensure_ascii=False)
            except (TypeError, ValueError) as e:
                logger.error(f"消息序列化失败: {e}")
                return False
            
            # 发送消息
            if socket_handler and hasattr(socket_handler, 'send'):
                try:
                    socket_handler.send(message_json)
                    logger.debug(f"WebSocket消息发送成功: room={room_uid}, "
                               f"type={clean_message.get('type')}")
                    return True
                except Exception as e:
                    logger.error(f"WebSocket发送失败: {e}")
                    return False
            else:
                logger.warning("WebSocket处理器无效或不可用")
                return False
                
        except Exception as e:
            logger.error(f"发送WebSocket消息时发生错误: {e}")
            return False
    
    def broadcast_to_room(self, room_uid: str, message: Dict[str, Any], 
                         socket_manager=None) -> int:
        """向房间广播消息"""
        try:
            if not socket_manager:
                logger.warning("WebSocket管理器未提供")
                return 0
            
            # 清理消息
            clean_message = MessageDataSanitizer.sanitize_for_websocket(message)
            
            # 获取房间中的所有连接
            connections = getattr(socket_manager, 'get_room_connections', lambda x: [])(room_uid)
            
            success_count = 0
            for connection in connections:
                if self.send_message_safe(connection, clean_message, room_uid):
                    success_count += 1
            
            logger.info(f"房间广播完成: room={room_uid}, 成功={success_count}, "
                       f"总数={len(connections)}")
            
            return success_count
            
        except Exception as e:
            logger.error(f"房间广播失败: room={room_uid}, error={e}")
            return 0


# 全局实例
message_builder = BattleMessageBuilder()
message_sender = WebSocketMessageSender()


def build_opening_start_message_safe(*args, **kwargs):
    """安全构建开箱开始消息"""
    return message_builder.build_opening_start_message(*args, **kwargs)


def build_round_result_message_safe(*args, **kwargs):
    """安全构建轮次结果消息"""
    return message_builder.build_round_result_message(*args, **kwargs)


def send_websocket_message_safe(*args, **kwargs):
    """安全发送WebSocket消息"""
    return message_sender.send_message_safe(*args, **kwargs)


def sanitize_websocket_data(data):
    """清理WebSocket数据"""
    return MessageDataSanitizer.sanitize_for_websocket(data)
