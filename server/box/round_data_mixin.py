#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
轮次数据格式统一修复器
解决前端反馈的轮次遗漏和数据不完整问题

主要修复内容：
1. 统一 round_count_current 字段定义和计算逻辑
2. 集成轮次管理器到序列化器中
3. 添加数据验证和边界检查
4. 提供清晰的字段语义和文档
"""

import logging

logger = logging.getLogger(__name__)

class RoundDataMixin:
    """轮次数据统一处理混入类"""
    
    def get_round_data_unified(self, obj):
        """
        统一的轮次数据获取方法
        
        Returns:
            dict: {
                'round_count': int,           # 总轮次数（房间设定的最大轮次）
                'round_count_current': int,   # 当前轮次（1-based，正在进行的轮次）
                'round_count_completed': int, # 已完成轮次数（0-based）
                'round_count_remaining': int, # 剩余轮次数
                'round_progress_percent': float, # 轮次进度百分比
                'is_first_round': bool,       # 是否是第一轮
                'is_last_round': bool,        # 是否是最后一轮
                'game_state': int,           # 游戏状态
                'round_state': str           # 轮次状态描述
            }
        """
        from .round_manager import BattleRoundManager
        from steambase.enums import GameState
        
        try:
            # 使用轮次管理器获取准确数据
            round_manager = BattleRoundManager(obj.uid)
            
            # 基础轮次数据
            current_round = round_manager.get_current_round()
            total_rounds = round_manager.get_total_rounds()
            completed_rounds = max(0, current_round - 1)
            remaining_rounds = max(0, total_rounds - current_round + 1)
            
            # 进度计算
            progress_percent = (completed_rounds / total_rounds * 100) if total_rounds > 0 else 0.0
            
            # 状态判断
            is_first_round = current_round == 1
            is_last_round = current_round >= total_rounds
            
            # 轮次状态描述
            if obj.state == GameState.Initial.value:
                round_state = "initial"
            elif obj.state in [GameState.Joinable.value, GameState.Joining.value]:
                round_state = "waiting"
            elif obj.state == GameState.Full.value:
                round_state = "ready"
            elif obj.state == GameState.Running.value:
                round_state = "running"
            elif obj.state == GameState.End.value:
                round_state = "ended"
            else:
                round_state = "unknown"
            
            # 数据验证
            if current_round <= 0:
                logger.warning(f"房间 {obj.uid}: 当前轮次异常 ({current_round})，使用默认值")
                current_round = 1
                completed_rounds = 0
                
            if total_rounds <= 0:
                logger.warning(f"房间 {obj.uid}: 总轮次异常 ({total_rounds})，使用默认值")
                total_rounds = 1
                
            # 边界检查
            if current_round > total_rounds:
                logger.error(f"房间 {obj.uid}: 当前轮次 ({current_round}) 超出总轮次 ({total_rounds})")
                current_round = total_rounds
                completed_rounds = total_rounds
                remaining_rounds = 0
                
            return {
                'round_count': total_rounds,
                'round_count_current': current_round,
                'round_count_completed': completed_rounds,
                'round_count_remaining': remaining_rounds,
                'round_progress_percent': round(progress_percent, 2),
                'is_first_round': is_first_round,
                'is_last_round': is_last_round,
                'game_state': obj.state,
                'round_state': round_state
            }
            
        except Exception as e:
            logger.error(f"房间 {obj.uid}: 轮次数据获取失败 - {e}")
            
            # 失败时返回安全的默认值
            return {
                'round_count': 1,
                'round_count_current': 1,
                'round_count_completed': 0,
                'round_count_remaining': 1,
                'round_progress_percent': 0.0,
                'is_first_round': True,
                'is_last_round': False,
                'game_state': obj.state,
                'round_state': 'error'
            }

class RoundDataValidator:
    """轮次数据验证器"""
    
    @staticmethod
    def validate_round_data(round_data, room_uid):
        """验证轮次数据的合理性"""
        issues = []
        
        # 基础类型检查
        required_fields = [
            'round_count', 'round_count_current', 'round_count_completed',
            'round_count_remaining', 'round_progress_percent'
        ]
        
        for field in required_fields:
            if field not in round_data:
                issues.append(f"缺少必需字段: {field}")
            elif not isinstance(round_data[field], (int, float)):
                issues.append(f"字段类型错误: {field} 应为数字类型")
        
        # 数值范围检查
        if round_data.get('round_count_current', 0) <= 0:
            issues.append("当前轮次必须大于0")
            
        if round_data.get('round_count', 0) <= 0:
            issues.append("总轮次必须大于0")
            
        if round_data.get('round_count_completed', -1) < 0:
            issues.append("已完成轮次不能小于0")
            
        # 逻辑一致性检查
        if (round_data.get('round_count_current', 0) > 
            round_data.get('round_count', 0)):
            issues.append("当前轮次不能超过总轮次")
            
        if (round_data.get('round_count_completed', 0) >= 
            round_data.get('round_count_current', 1)):
            issues.append("已完成轮次应小于当前轮次")
            
        # 进度百分比检查
        progress = round_data.get('round_progress_percent', 0)
        if not 0 <= progress <= 100:
            issues.append("进度百分比应在0-100之间")
        
        if issues:
            logger.error(f"房间 {room_uid}: 轮次数据验证失败 - {issues}")
            return False, issues
        
        return True, []
