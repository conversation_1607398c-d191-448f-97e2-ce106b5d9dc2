#!/usr/bin/env python
"""
创建示例文章和公告数据
"""
import os
import sys
import django
from django.utils import timezone
from django.utils.text import slugify
import uuid

# 添加项目路径
sys.path.append('/www/wwwroot/csgoskins.com.cn/server')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')

# 初始化Django
django.setup()

from django.db import connection

def create_sample_data():
    """创建示例数据"""
    
    print("🎯 创建示例文章和公告数据...")
    
    # 创建标签
    create_sample_tags()
    
    # 创建示例文章
    create_sample_articles()
    
    # 创建示例公告
    create_sample_announcements()
    
    print("✅ 示例数据创建完成！")

def create_sample_tags():
    """创建示例标签"""
    print("🏷️  创建示例标签...")
    
    tags = [
        {'name': '重要', 'slug': 'important', 'color': '#dc3545'},
        {'name': '新功能', 'slug': 'new-feature', 'color': '#28a745'},
        {'name': '更新', 'slug': 'update', 'color': '#007bff'},
        {'name': '维护', 'slug': 'maintenance', 'color': '#ffc107'},
        {'name': '活动', 'slug': 'event', 'color': '#17a2b8'},
        {'name': '帮助', 'slug': 'help', 'color': '#6c757d'},
        {'name': '教程', 'slug': 'tutorial', 'color': '#6f42c1'},
        {'name': 'CSGO', 'slug': 'csgo', 'color': '#fd7e14'},
    ]
    
    with connection.cursor() as cursor:
        for tag in tags:
            cursor.execute("""
                INSERT IGNORE INTO articles_contenttag 
                (name, slug, color, is_active, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s)
            """, [
                tag['name'], tag['slug'], tag['color'], True, 
                timezone.now(), timezone.now()
            ])
            print(f"  ✓ 创建标签: {tag['name']}")

def create_sample_articles():
    """创建示例文章"""
    print("📝 创建示例文章...")
    
    with connection.cursor() as cursor:
        # 获取分类ID
        cursor.execute("SELECT id FROM articles_contentcategory WHERE slug = 'help-docs' LIMIT 1")
        help_category_id = cursor.fetchone()
        help_category_id = help_category_id[0] if help_category_id else None
        
        cursor.execute("SELECT id FROM articles_contentcategory WHERE slug = 'game-news' LIMIT 1")
        news_category_id = cursor.fetchone()
        news_category_id = news_category_id[0] if news_category_id else None
        
        articles = [
            {
                'title': '如何开始使用CSGO皮肤交易平台',
                'subtitle': '新手入门指南',
                'content': '''
                <h2>欢迎来到CSGO皮肤交易平台</h2>
                <p>本指南将帮助您快速了解如何使用我们的平台进行CSGO皮肤交易。</p>
                
                <h3>第一步：注册账户</h3>
                <p>1. 点击页面右上角的"注册"按钮</p>
                <p>2. 使用Steam账户登录</p>
                <p>3. 完成邮箱验证</p>
                
                <h3>第二步：充值</h3>
                <p>支持多种充值方式：</p>
                <ul>
                    <li>支付宝</li>
                    <li>微信支付</li>
                    <li>银行卡</li>
                </ul>
                
                <h3>第三步：开始交易</h3>
                <p>浏览我们的皮肤库存，选择您喜欢的物品进行购买。</p>
                ''',
                'category_id': help_category_id,
                'content_type': 'guide',
                'is_featured': True,
                'tags': ['帮助', '教程', 'CSGO']
            },
            {
                'title': 'CSGO新赛季更新内容解析',
                'subtitle': '最新游戏更新详情',
                'content': '''
                <h2>CSGO新赛季重大更新</h2>
                <p>Valve发布了CSGO的最新更新，带来了许多令人兴奋的新内容。</p>
                
                <h3>新地图</h3>
                <p>本次更新新增了两张竞技地图：</p>
                <ul>
                    <li>de_ancient - 古代遗迹主题</li>
                    <li>de_vertigo - 重制版摩天大楼</li>
                </ul>
                
                <h3>武器平衡性调整</h3>
                <p>多款武器的伤害和后坐力进行了微调，提升游戏平衡性。</p>
                
                <h3>新皮肤系列</h3>
                <p>推出了全新的"破碎网络"皮肤系列，包含多款精美设计。</p>
                ''',
                'category_id': news_category_id,
                'content_type': 'news',
                'is_featured': True,
                'tags': ['新功能', '更新', 'CSGO']
            },
            {
                'title': '交易安全须知',
                'subtitle': '保护您的账户安全',
                'content': '''
                <h2>交易安全指南</h2>
                <p>为了保护您的账户和财产安全，请仔细阅读以下安全须知。</p>
                
                <h3>账户安全</h3>
                <ul>
                    <li>使用强密码</li>
                    <li>开启两步验证</li>
                    <li>不要分享账户信息</li>
                </ul>
                
                <h3>交易安全</h3>
                <ul>
                    <li>仔细核对物品信息</li>
                    <li>使用平台担保交易</li>
                    <li>警惕钓鱼网站</li>
                </ul>
                
                <h3>遇到问题怎么办</h3>
                <p>如果遇到任何问题，请及时联系客服团队。</p>
                ''',
                'category_id': help_category_id,
                'content_type': 'article',
                'is_featured': False,
                'tags': ['重要', '帮助']
            }
        ]
        
        for article in articles:
            # 生成slug
            slug = slugify(article['title'])[:240] + '-' + str(uuid.uuid4())[:8]
            
            # 生成摘要
            excerpt = article.get('subtitle', '')
            
            # 插入文章
            cursor.execute("""
                INSERT INTO articles_content 
                (title, slug, subtitle, excerpt, content, content_type, status, priority, 
                 is_featured, is_pinned, publish_date, category_id, sort_order, 
                 view_count, allow_comments, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, [
                article['title'], slug, article.get('subtitle'), excerpt, 
                article['content'], article['content_type'], 'published', 'normal',
                article['is_featured'], False, timezone.now(), article['category_id'], 0,
                0, True, timezone.now(), timezone.now()
            ])
            
            # 获取插入的文章ID
            content_id = cursor.lastrowid
            
            # 添加标签关联
            for tag_name in article.get('tags', []):
                cursor.execute("SELECT id FROM articles_contenttag WHERE name = %s", [tag_name])
                tag_result = cursor.fetchone()
                if tag_result:
                    tag_id = tag_result[0]
                    cursor.execute("""
                        INSERT IGNORE INTO articles_content_tags (content_id, contenttag_id)
                        VALUES (%s, %s)
                    """, [content_id, tag_id])
            
            print(f"  ✓ 创建文章: {article['title']}")

def create_sample_announcements():
    """创建示例公告"""
    print("📢 创建示例公告...")
    
    with connection.cursor() as cursor:
        # 获取分类ID
        cursor.execute("SELECT id FROM articles_contentcategory WHERE slug = 'system-announcements' LIMIT 1")
        system_category_id = cursor.fetchone()
        system_category_id = system_category_id[0] if system_category_id else None
        
        cursor.execute("SELECT id FROM articles_contentcategory WHERE slug = 'event-announcements' LIMIT 1")
        event_category_id = cursor.fetchone()
        event_category_id = event_category_id[0] if event_category_id else None
        
        announcements = [
            {
                'title': '平台维护通知',
                'content': '''
                <p>尊敬的用户：</p>
                <p>为了提升服务质量，我们将于<strong>2024年1月20日 02:00-06:00</strong>进行系统维护。</p>
                <p>维护期间，平台将暂停服务，给您带来的不便敬请谅解。</p>
                <p>维护内容包括：</p>
                <ul>
                    <li>服务器性能优化</li>
                    <li>安全系统升级</li>
                    <li>新功能部署</li>
                </ul>
                <p>感谢您的理解与支持！</p>
                ''',
                'category_id': system_category_id,
                'priority': 'urgent',
                'is_pinned': True,
                'tags': ['重要', '维护']
            },
            {
                'title': '春节活动开启',
                'content': '''
                <h2>春节特别活动</h2>
                <p>春节期间，我们为大家准备了丰富的活动！</p>
                
                <h3>活动时间</h3>
                <p>2024年2月10日 - 2024年2月25日</p>
                
                <h3>活动内容</h3>
                <ul>
                    <li>每日签到送红包</li>
                    <li>充值返利活动</li>
                    <li>限时皮肤折扣</li>
                    <li>幸运抽奖</li>
                </ul>
                
                <p>更多详情请关注活动页面！</p>
                ''',
                'category_id': event_category_id,
                'priority': 'high',
                'is_pinned': False,
                'tags': ['活动', '新功能']
            }
        ]
        
        for announcement in announcements:
            # 生成slug
            slug = slugify(announcement['title'])[:240] + '-' + str(uuid.uuid4())[:8]
            
            # 插入公告
            cursor.execute("""
                INSERT INTO articles_content 
                (title, slug, content, content_type, status, priority, 
                 is_featured, is_pinned, publish_date, category_id, sort_order, 
                 view_count, allow_comments, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, [
                announcement['title'], slug, announcement['content'], 'announcement', 
                'published', announcement['priority'], True, announcement['is_pinned'], 
                timezone.now(), announcement['category_id'], 0, 0, True, 
                timezone.now(), timezone.now()
            ])
            
            # 获取插入的公告ID
            content_id = cursor.lastrowid
            
            # 添加标签关联
            for tag_name in announcement.get('tags', []):
                cursor.execute("SELECT id FROM articles_contenttag WHERE name = %s", [tag_name])
                tag_result = cursor.fetchone()
                if tag_result:
                    tag_id = tag_result[0]
                    cursor.execute("""
                        INSERT IGNORE INTO articles_content_tags (content_id, contenttag_id)
                        VALUES (%s, %s)
                    """, [content_id, tag_id])
            
            print(f"  ✓ 创建公告: {announcement['title']}")

if __name__ == '__main__':
    create_sample_data()
