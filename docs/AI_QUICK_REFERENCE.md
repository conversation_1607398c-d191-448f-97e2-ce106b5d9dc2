# 🚀 AI智能体快速参考卡片

## 📋 系统快速识别

### 项目类型
**CS:GO皮肤交易平台** - Django后端 + Vue3前端 + WebSocket实时通信

### 核心目录结构
```
/www/wwwroot/csgoskins.com.cn/
├── server/          # Django后端 (Python 3.8+)
├── ui/             # Nuxt3前端 (Node.js)
├── scripts/        # 所有脚本统一存放
├── docs/           # 项目文档
├── logs/           # 日志文件
└── backup/         # 备份文件
```

### 技术栈识别
- **后端**: Django 1.11 + DRF + MySQL + Redis + Celery
- **前端**: Vue3 + Nuxt3 + TypeScript + Socket.IO
- **部署**: Docker + Supervisor + Nginx

## 🎯 问题分析3步法

### 1️⃣ 信息收集
```bash
# 必须先执行
codebase-retrieval "分析[具体问题]相关的代码结构和实现"
git-commit-retrieval "查找[相关功能]的历史修改记录"
```

### 2️⃣ 问题定位
**后端问题** → 检查 `server/[app]/` 目录
**前端问题** → 检查 `ui/` 目录  
**配置问题** → 检查 `server/steambase/settings.py`
**数据库问题** → 检查 `server/[app]/models.py` 和迁移文件

### 3️⃣ 解决方案
**创建修复脚本** → `scripts/fixes/fix_[问题].py`
**更新配置** → 使用包管理器，不直接编辑配置文件
**测试验证** → 创建测试脚本验证修复效果

## 📁 文件创建位置速查

| 文件类型 | 正确位置 | 错误位置 |
|---------|---------|---------|
| 修复脚本 | `scripts/fixes/` | ❌ `server/` |
| 工具脚本 | `scripts/tools/` | ❌ `server/tools/` |
| 测试脚本 | `scripts/tests/` | ❌ `server/` |
| 临时文件 | `scripts/temp/` | ❌ `server/` |
| 备份文件 | `backup/` | ❌ `server/` |
| 配置文件 | 根目录或`server/` | ❌ 重复位置 |

## 🔧 常用命令速查

### Django操作
```bash
# 进入server目录
cd /www/wwwroot/csgoskins.com.cn/server

# 数据库迁移
python manage.py makemigrations
python manage.py migrate

# Django shell
python manage.py shell

# 收集静态文件
python manage.py collectstatic
```

### 包管理
```bash
# Python包
pip install [package_name]
pip uninstall [package_name]

# Node.js包 (在ui目录)
npm install [package_name]
npm uninstall [package_name]
```

### Docker操作
```bash
# 查看状态
docker-compose ps

# 重启服务
docker-compose restart [service_name]

# 查看日志
docker-compose logs -f [service_name]
```

## 🐛 Bug类型快速识别

### 🔴 CKEditor问题
**症状**: 编辑器加载失败、安全警告
**检查**: `server/steambase/settings.py` CKEDITOR配置
**修复**: 使用 `scripts/ckeditor/` 中的脚本

### 🟡 国际化问题  
**症状**: 翻译显示错误、语言切换失败
**检查**: `server/locale/zh_hans/LC_MESSAGES/django.po`
**修复**: 使用 `scripts/tools/fix_po_*.py`

### 🟢 WebSocket问题
**症状**: 实时功能失效、连接断开
**检查**: `server/websocket/` 和 `ui/plugins/socket.client.ts`
**修复**: 检查Redis连接和路由配置

### 🔵 数据库问题
**症状**: 迁移失败、数据查询错误
**检查**: `server/[app]/migrations/` 和 `models.py`
**修复**: 重新生成迁移或修复模型定义

## 📊 应用模块速查

### 核心模块
- `steambase/` - 项目配置和核心工具
- `authentication/` - 用户认证和权限
- `articles/` - 内容管理系统
- `websocket/` - WebSocket通信

### 业务模块
- `box/` - 开箱和对战系统
- `package/` - 物品包裹管理  
- `market/` - 交易市场
- `charge/` - 充值系统

### 游戏模块
- `crash/` - 崩盘游戏
- `roll/` - Roll点游戏
- `grab/` - 抢夺游戏
- `lottery/` - 彩票系统

## 🛡️ 安全检查清单

### ✅ 修改前检查
- [ ] 使用 `codebase-retrieval` 了解现有实现
- [ ] 使用 `git-commit-retrieval` 查看历史修改
- [ ] 确认修改不会影响其他功能
- [ ] 检查是否需要数据库迁移

### ✅ 修改后验证
- [ ] 运行相关测试
- [ ] 检查日志是否有错误
- [ ] 验证功能是否正常工作
- [ ] 确认没有引入新的安全问题

## 🚨 紧急问题处理

### 服务无法启动
1. 检查 `logs/supervisord.log`
2. 验证数据库连接
3. 检查Redis服务状态
4. 查看端口占用情况

### 数据库连接失败
1. 检查 `server/steambase/settings.py` 数据库配置
2. 验证MySQL服务状态
3. 检查网络连接
4. 查看数据库用户权限

### 静态文件404
1. 运行 `python manage.py collectstatic`
2. 检查Nginx配置
3. 验证文件权限
4. 查看STATIC_ROOT设置

## 💡 最佳实践提醒

### 🎯 代码修改
- 优先创建脚本而非直接修改
- 使用包管理器安装依赖
- 遵循现有代码风格
- 添加适当的注释和文档

### 🔍 问题调试
- 先查看日志文件
- 使用Django shell调试
- 检查Redis缓存状态
- 验证数据库数据完整性

### 📝 文档更新
- 重要修改要更新文档
- 创建修复脚本要添加README
- 新功能要添加使用说明
- 保持文档与代码同步

---

**⚡ 记住**: 理解系统 → 分析问题 → 规范修改 → 验证结果
