#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对战轮次管理器 - 统一轮次逻辑，避免硬编码问题
作者: Backend Engineering Team
日期: 2025-07-05
目的: 解决前端反馈的轮次硬编码和不一致问题
"""

import time
import logging
from django.core.cache import cache
from django.db import models
from .models import CaseRoom, CaseRoomRound

_logger = logging.getLogger(__name__)

class BattleRoundManager:
    """对战轮次管理器 - 统一轮次逻辑"""
    
    REDIS_KEY_PREFIX = "battle_round:"
    CACHE_TIMEOUT = 3600  # 1小时过期
    
    def __init__(self, room_uid):
        self.room_uid = room_uid
        self.redis_key = f"{self.REDIS_KEY_PREFIX}{room_uid}"
        
    def get_current_round(self):
        """获取当前轮次（1-based）"""
        try:
            # 优先从Redis缓存获取
            cached_round = cache.get(self.redis_key)
            if cached_round is not None:
                return int(cached_round)
            
            # 从数据库计算当前轮次
            room = CaseRoom.objects.filter(uid=self.room_uid).first()
            if not room:
                _logger.warning(f"房间不存在: {self.room_uid}")
                return 1
            
            # 获取总轮次数
            total_rounds = self.get_total_rounds()
            
            # 计算已开启的轮次数
            opened_rounds = CaseRoomRound.objects.filter(room=room, opened=True).count()
            
            # 修复逻辑：如果所有轮次都已开启，当前轮次应该是最后一轮
            if opened_rounds >= total_rounds:
                current_round = total_rounds
            else:
                current_round = opened_rounds + 1
            
            # 边界检查
            current_round = max(1, min(current_round, total_rounds))
            
            # 缓存到Redis
            cache.set(self.redis_key, current_round, self.CACHE_TIMEOUT)
            
            _logger.info(f"计算当前轮次: room={self.room_uid}, current_round={current_round}, opened_rounds={opened_rounds}, total_rounds={total_rounds}")
            return current_round
            
        except Exception as e:
            _logger.error(f"获取当前轮次失败: room={self.room_uid}, error={e}")
            return 1
    
    def get_total_rounds(self):
        """获取总轮次数"""
        try:
            room = CaseRoom.objects.filter(uid=self.room_uid).first()
            if not room:
                _logger.warning(f"房间不存在: {self.room_uid}")
                return 3  # 默认3轮
            
            # 优先使用round_count字段，如果没有则使用max_joiner
            total_rounds = getattr(room, 'round_count', room.max_joiner)
            return max(total_rounds, 1)  # 至少1轮
            
        except Exception as e:
            _logger.error(f"获取总轮次数失败: room={self.room_uid}, error={e}")
            return 3
    
    def advance_round(self):
        """推进到下一轮次"""
        try:
            current = self.get_current_round()
            next_round = current + 1
            total = self.get_total_rounds()
            
            # 轮次单调递增检查
            if next_round > total:
                raise ValueError(f"轮次超出限制: {next_round} > {total}")
            
            # 更新Redis缓存
            cache.set(self.redis_key, next_round, self.CACHE_TIMEOUT)
            
            _logger.info(f"轮次推进成功: room={self.room_uid}, {current} -> {next_round}/{total}")
            return next_round
            
        except Exception as e:
            _logger.error(f"推进轮次失败: room={self.room_uid}, error={e}")
            raise
    
    def validate_round(self, round_num):
        """验证轮次合理性"""
        try:
            total = self.get_total_rounds()
            
            if not isinstance(round_num, int):
                raise ValueError(f"轮次必须是整数: {round_num}")
            
            if round_num < 1:
                raise ValueError(f"轮次不能小于1: {round_num}")
            
            if round_num > total:
                raise ValueError(f"轮次超出总数: {round_num} > {total}")
            
            _logger.debug(f"轮次验证通过: room={self.room_uid}, round={round_num}/{total}")
            return True
            
        except Exception as e:
            _logger.error(f"轮次验证失败: room={self.room_uid}, round={round_num}, error={e}")
            return False
            
    def clear_cache(self):
        """清除轮次缓存"""
        try:
            cache.delete(self.redis_key)
            _logger.info(f"已清除轮次缓存: room={self.room_uid}")
        except Exception as e:
            _logger.error(f"清除轮次缓存失败: room={self.room_uid}, error={e}")
    
    def refresh_current_round(self):
        """刷新当前轮次（强制重新计算）"""
        self.clear_cache()
        return self.get_current_round()
    
    def reset_rounds(self):
        """重置轮次（房间重新开始时）"""
        try:
            cache.set(self.redis_key, 1, self.CACHE_TIMEOUT)
            _logger.info(f"轮次重置成功: room={self.room_uid}")
            
        except Exception as e:
            _logger.error(f"轮次重置失败: room={self.room_uid}, error={e}")
            raise
    
    def get_round_status(self):
        """获取轮次状态信息"""
        try:
            current = self.get_current_round()
            total = self.get_total_rounds()
            
            return {
                'current_round': current,
                'total_rounds': total,
                'is_first_round': current == 1,
                'is_last_round': current >= total,
                'progress_percent': min((current / total) * 100, 100),
                'remaining_rounds': max(total - current, 0)
            }
            
        except Exception as e:
            _logger.error(f"获取轮次状态失败: room={self.room_uid}, error={e}")
            return {
                'current_round': 1,
                'total_rounds': 3,
                'is_first_round': True,
                'is_last_round': False,
                'progress_percent': 33.33,
                'remaining_rounds': 2
            }
    
    def cleanup_cache(self):
        """清理缓存（房间结束时调用）"""
        try:
            cache.delete(self.redis_key)
            _logger.info(f"轮次缓存清理成功: room={self.room_uid}")
            
        except Exception as e:
            _logger.error(f"轮次缓存清理失败: room={self.room_uid}, error={e}")


class BattleRoundValidator:
    """轮次验证器 - 用于服务器端验证"""
    
    @staticmethod
    def validate_message_round(room_uid, message_type, expected_round):
        """验证消息中的轮次是否正确"""
        try:
            manager = BattleRoundManager(room_uid)
            current_round = manager.get_current_round()
            
            if expected_round != current_round:
                _logger.warning(
                    f"轮次不匹配: room={room_uid}, message={message_type}, "
                    f"expected={expected_round}, current={current_round}"
                )
                return False
            
            return True
            
        except Exception as e:
            _logger.error(f"轮次验证失败: room={room_uid}, message={message_type}, error={e}")
            return False
    
    @staticmethod
    def get_safe_round(room_uid, fallback_round=1):
        """安全获取轮次，失败时返回fallback值"""
        try:
            manager = BattleRoundManager(room_uid)
            return manager.get_current_round()
            
        except Exception as e:
            _logger.error(f"安全获取轮次失败: room={room_uid}, error={e}, 使用fallback={fallback_round}")
            return fallback_round


# 使用示例和测试函数
def test_round_manager():
    """测试轮次管理器功能"""
    room_uid = "test_room_123"
    manager = BattleRoundManager(room_uid)
    
    print(f"当前轮次: {manager.get_current_round()}")
    print(f"总轮次: {manager.get_total_rounds()}")
    print(f"轮次状态: {manager.get_round_status()}")
    
    # 测试轮次验证
    try:
        manager.validate_round(1)
        print("轮次1验证通过")
    except ValueError as e:
        print(f"轮次1验证失败: {e}")
    
    # 测试轮次推进
    try:
        next_round = manager.advance_round()
        print(f"推进到轮次: {next_round}")
    except ValueError as e:
        print(f"轮次推进失败: {e}")
    
    # 清理
    manager.cleanup_cache()


if __name__ == "__main__":
    test_round_manager()
