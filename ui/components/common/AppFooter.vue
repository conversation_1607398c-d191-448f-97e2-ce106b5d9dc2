<!-- components/common/AppFooter.vue -->
<template>
  <footer
    class="relative overflow-hidden bg-gradient-to-br from-slate-950 via-slate-900 to-slate-950 border-t border-white/10 pt-16 pb-8 min-h-[600px]"
  >
    <!-- 动态背景装饰 -->
    <div class="absolute inset-0 pointer-events-none overflow-hidden">
      <div class="bg-orb bg-orb-1"></div>
      <div class="bg-orb bg-orb-2"></div>
      <div class="bg-orb bg-orb-3"></div>
      <div class="bg-grid"></div>
      <div class="bg-gradient"></div>
    </div>

    <div class="relative z-10">
      <div class="container mx-auto px-6 lg:px-8">
        <!-- 主要内容区域 -->
        <div class="mb-6">
          <div class="grid grid-cols-1 lg:grid-cols-12 gap-6 lg:gap-12">
            <!-- 品牌区域 - 占据更多空间 -->
            <div class="lg:col-span-4 flex flex-col gap-8">
              <!-- Logo区域 -->
              <div class="mb-2">
                <NuxtLink
                  to="/"
                  class="brand-logo group inline-block transition-all duration-500 hover:-translate-y-0.5"
                >
                  <div class="logo-wrapper">
                    <span class="logo-text logo-text-csgo">CSGO</span>
                    <div class="logo-icon">
                      <img
                        src="/images/logo.svg"
                        alt="CSGOSKINS"
                        class="h-12 w-auto filter drop-shadow-lg transition-all duration-500"
                      />
                      <div class="logo-glow"></div>
                    </div>
                    <span class="logo-text logo-text-skins">SKINS</span>
                    <div class="logo-shine"></div>
                  </div>
                </NuxtLink>
              </div>

              <!-- 品牌描述 -->
              <div class="mb-2">
                <p class="text-white/70 text-base leading-relaxed mb-2">
                  {{ $t("footer.description") }}
                </p>
                <!-- <div class="flex items-center justify-center gap-6 p-5 bg-white/5 border border-white/10 rounded-2xl backdrop-blur-xl">
                  <div class="flex flex-col items-center gap-1">
                    <span class="text-xl font-extrabold text-blue-400 drop-shadow-lg">1M+</span>
                    <span class="text-xs text-white/50 uppercase tracking-wider">{{ $t("footer.users") }}</span>
                  </div>
                  <div class="w-px h-8 bg-white/10"></div>
                  <div class="flex flex-col items-center gap-1">
                    <span class="text-xl font-extrabold text-blue-400 drop-shadow-lg">50K+</span>
                    <span class="text-xs text-white/50 uppercase tracking-wider">{{ $t("footer.cases_opened") }}</span>
                  </div>
                </div> -->
              </div>

              <!-- 社交媒体 -->
              <div class="flex flex-col gap-4">
                <h5
                  class="text-sm font-semibold text-white/70 uppercase tracking-wider"
                >
                  {{ $t("footer.follow_us") }}
                </h5>
                <div class="flex gap-3 flex-wrap items-center">
                  <a
                    v-for="social in socialLinks"
                    :key="social.name"
                    :href="social.url"
                    :title="social.name"
                    class="social-link group relative flex items-center justify-center w-12 h-12 rounded-2xl bg-white/5 border border-white/10 text-white/70 hover:text-blue-400 transition-all duration-500 hover:-translate-y-1 hover:scale-105 hover:border-blue-400/50 hover:bg-blue-400/10 hover:shadow-lg hover:shadow-blue-400/30 backdrop-blur-xl overflow-hidden"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <Icon :name="social.icon" class="w-5 h-5 z-10 relative" />
                    <span class="social-tooltip">{{ social.name }}</span>
                    <div class="social-ripple"></div>
                  </a>
                </div>
              </div>
            </div>

            <!-- 导航链接区域 -->
            <div class="lg:col-span-8 flex flex-col">
              <div class="grid grid-cols-1 sm:grid-cols-3 gap-8">
                <!-- 产品服务 -->
                <div class="flex flex-col gap-4">
                  <h4
                    class="flex items-center gap-3 text-lg font-bold text-white mb-2 relative"
                  >
                    <Icon
                      name="game-icons:treasure-map"
                      class="w-5 h-5 text-blue-400"
                    />
                    {{ $t("footer.services") }}
                    <div
                      class="absolute -bottom-2 left-0 w-8 h-0.5 bg-gradient-to-r from-blue-400 to-transparent rounded-full"
                    ></div>
                  </h4>
                  <ul class="flex flex-col gap-2 list-none p-0 m-0">
                    <li
                      v-for="service in services"
                      :key="service.key"
                      class="relative"
                    >
                      <NuxtLink
                        :to="service.path"
                        class="group flex items-center gap-3 py-2.5 px-3.5 rounded-lg text-white/70 hover:text-white hover:bg-white/5 hover:border-white/10 border border-transparent transition-all duration-300 hover:translate-x-2"
                      >
                        <Icon
                          :name="service.icon"
                          class="w-4 h-4 text-white/50 group-hover:text-blue-400 group-hover:scale-110 transition-all duration-300 flex-shrink-0"
                        />
                        <span class="text-sm font-medium flex-1">{{
                          $t(`footer.${service.key}`)
                        }}</span>
                        <Icon
                          name="heroicons:arrow-right"
                          class="w-3.5 h-3.5 text-blue-400 opacity-0 group-hover:opacity-100 -translate-x-2 group-hover:translate-x-0 transition-all duration-300 flex-shrink-0"
                        />
                      </NuxtLink>
                    </li>
                  </ul>
                </div>

                <!-- 帮助支持 -->
                <div class="flex flex-col gap-4">
                  <h4
                    class="flex items-center gap-3 text-lg font-bold text-white mb-2 relative"
                  >
                    <Icon
                      name="heroicons:question-mark-circle"
                      class="w-5 h-5 text-blue-400"
                    />
                    {{ $t("footer.help") }}
                    <div
                      class="absolute -bottom-2 left-0 w-8 h-0.5 bg-gradient-to-r from-blue-400 to-transparent rounded-full"
                    ></div>
                  </h4>
                  <ul class="flex flex-col gap-2 list-none p-0 m-0">
                    <li
                      v-for="help in helpLinks"
                      :key="help.key"
                      class="relative"
                    >
                      <NuxtLink
                        :to="help.path"
                        class="group flex items-center gap-3 py-2.5 px-3.5 rounded-lg text-white/70 hover:text-white hover:bg-white/5 hover:border-white/10 border border-transparent transition-all duration-300 hover:translate-x-2"
                      >
                        <Icon
                          :name="help.icon"
                          class="w-4 h-4 text-white/50 group-hover:text-blue-400 group-hover:scale-110 transition-all duration-300 flex-shrink-0"
                        />
                        <span class="text-sm font-medium flex-1">{{
                          $t(`footer.${help.key}`)
                        }}</span>
                        <Icon
                          name="heroicons:arrow-right"
                          class="w-3.5 h-3.5 text-blue-400 opacity-0 group-hover:opacity-100 -translate-x-2 group-hover:translate-x-0 transition-all duration-300 flex-shrink-0"
                        />
                      </NuxtLink>
                    </li>
                  </ul>
                </div>

                <!-- 法律信息 -->
                <div class="flex flex-col gap-4">
                  <h4
                    class="flex items-center gap-3 text-lg font-bold text-white mb-2 relative"
                  >
                    <Icon
                      name="heroicons:shield-check"
                      class="w-5 h-5 text-blue-400"
                    />
                    {{ $t("footer.legal") }}
                    <div
                      class="absolute -bottom-2 left-0 w-8 h-0.5 bg-gradient-to-r from-blue-400 to-transparent rounded-full"
                    ></div>
                  </h4>
                  <ul class="flex flex-col gap-2 list-none p-0 m-0">
                    <li
                      v-for="legal in legalLinks"
                      :key="legal.key"
                      class="relative"
                    >
                      <NuxtLink
                        :to="legal.path"
                        class="group flex items-center gap-3 py-2.5 px-3.5 rounded-lg text-white/70 hover:text-white hover:bg-white/5 hover:border-white/10 border border-transparent transition-all duration-300 hover:translate-x-2"
                      >
                        <Icon
                          :name="legal.icon"
                          class="w-4 h-4 text-white/50 group-hover:text-blue-400 group-hover:scale-110 transition-all duration-300 flex-shrink-0"
                        />
                        <span class="text-sm font-medium flex-1">{{
                          $t(`footer.${legal.key}`)
                        }}</span>
                        <Icon
                          name="heroicons:arrow-right"
                          class="w-3.5 h-3.5 text-blue-400 opacity-0 group-hover:opacity-100 -translate-x-2 group-hover:translate-x-0 transition-all duration-300 flex-shrink-0"
                        />
                      </NuxtLink>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 邮件订阅区域 -->
        <div
          class="mb-12 py-8 px-6 bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-blue-500/10 border border-white/10 rounded-3xl backdrop-blur-xl"
        >
          <div class="max-w-4xl mx-auto">
            <div
              class="flex flex-col lg:flex-row items-center lg:items-start gap-8"
            >
              <div class="flex items-center gap-4 flex-shrink-0 lg:flex-1">
                <div
                  class="relative flex items-center justify-center w-16 h-16 rounded-2xl bg-blue-500/20 border border-blue-400/30"
                >
                  <Icon
                    name="heroicons:envelope"
                    class="w-8 h-8 text-blue-400"
                  />
                  <div
                    class="absolute inset-0 rounded-2xl bg-blue-400/20 animate-pulse"
                  ></div>
                </div>
                <div class="flex flex-col gap-1">
                  <h4 class="text-xl font-bold text-white">
                    {{ $t("footer.newsletter") }}
                  </h4>
                  <p class="text-sm text-white/60 max-w-xs">
                    {{ $t("footer.newsletter_desc") }}
                  </p>
                </div>
              </div>

              <div class="flex-1 w-full lg:max-w-md">
                <div class="flex flex-col sm:flex-row gap-3 w-full">
                  <input
                    v-model="email"
                    type="email"
                    :placeholder="$t('footer.email_placeholder')"
                    class="flex-1 px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-white/40 focus:outline-none focus:border-blue-400/50 focus:bg-white/10 transition-all duration-300 backdrop-blur-xl"
                    @keyup.enter="subscribeNewsletter"
                  />
                  <button
                    @click="subscribeNewsletter"
                    :disabled="subscribing || !email"
                    class="group relative flex items-center justify-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-blue-500/30 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 overflow-hidden whitespace-nowrap"
                  >
                    <span
                      v-if="!subscribing"
                      class="flex items-center gap-2 relative z-10"
                    >
                      <Icon
                        name="heroicons:paper-airplane"
                        class="w-4 h-4 group-hover:rotate-12 transition-transform duration-300"
                      />
                      {{ $t("footer.subscribe") }}
                    </span>
                    <span v-else class="flex items-center gap-2 relative z-10">
                      <Icon
                        name="heroicons:arrow-path"
                        class="w-4 h-4 animate-spin"
                      />
                      {{ $t("footer.subscribing") }}
                    </span>
                    <div
                      class="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-purple-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                    ></div>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 底部版权区域 -->
        <div class="pt-8 border-t border-white/10">
          <div
            class="flex flex-col lg:flex-row items-center justify-between gap-6"
          >
            <div class="flex flex-col lg:flex-row items-center gap-6">
              <p class="text-sm text-white/60 text-center lg:text-left">
                © {{ currentYear }}
                <span class="font-semibold text-blue-400">CSGOSKINS</span>.
                {{ $t("footer.rights") }}
              </p>
              <div class="flex items-center gap-4">
                <div
                  class="flex items-center gap-2 px-3 py-1.5 bg-green-500/10 border border-green-400/20 rounded-lg"
                >
                  <Icon
                    name="heroicons:shield-check"
                    class="w-4 h-4 text-green-400"
                  />
                  <span class="text-xs font-medium text-green-400">{{
                    $t("footer.secure")
                  }}</span>
                </div>
                <div
                  class="flex items-center gap-2 px-3 py-1.5 bg-blue-500/10 border border-blue-400/20 rounded-lg"
                >
                  <Icon
                    name="heroicons:lock-closed"
                    class="w-4 h-4 text-blue-400"
                  />
                  <span class="text-xs font-medium text-blue-400">SSL</span>
                </div>
              </div>
            </div>

            <div class="flex items-center gap-4">
              <div class="flex items-center gap-2 text-xs text-white/40">
                <span class="flex items-center gap-1">
                  <Icon name="heroicons:code-bracket" class="w-3.5 h-3.5" />
                  {{ $t("footer.version") }}: {{ version }}
                </span>
                <span class="flex items-center gap-1">
                  <Icon name="heroicons:calendar" class="w-3.5 h-3.5" />
                  {{ $t("footer.build") }}: {{ buildTime }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

// 响应式数据
const email = ref("");
const subscribing = ref(false);

// 计算属性
const currentYear = computed(() => new Date().getFullYear());
const version = computed(() => "2.0.0");
const buildTime = computed(() => "2025-01-15");

// 社交媒体链接
const socialLinks = [
  { name: "Discord", url: "#", icon: "simple-icons:discord" },
  { name: "Twitter", url: "#", icon: "simple-icons:twitter" },
  { name: "Instagram", url: "#", icon: "simple-icons:instagram" },
  { name: "YouTube", url: "#", icon: "simple-icons:youtube" },
  { name: "Telegram", url: "#", icon: "simple-icons:telegram" },
];

// 服务链接
const services = [
  { key: "cases", path: "/cases", icon: "game-icons:locked-chest" },
  { key: "skins", path: "/skins", icon: "heroicons:sparkles" },
  { key: "battle", path: "/battle", icon: "game-icons:crossed-swords" },
  { key: "activity", path: "/activities", icon: "heroicons:gift" },
  { key: "market", path: "/", icon: "heroicons:shopping-bag" },
];

// 帮助链接
const helpLinks = [
  { key: "faq", path: "/faq", icon: "heroicons:question-mark-circle" },
  {
    key: "support",
    path: "/support",
    icon: "heroicons:chat-bubble-left-right",
  },
  { key: "contact", path: "/contact", icon: "heroicons:envelope" },
  { key: "tutorial", path: "/", icon: "heroicons:academic-cap" },
];

// 法律链接
const legalLinks = [
  { key: "about", path: "/about", icon: "heroicons:information-circle" },
  { key: "terms", path: "/terms", icon: "heroicons:document-text" },
  { key: "privacy", path: "/privacy", icon: "heroicons:shield-check" },
  {
    key: "disclaimer",
    path: "/",
    icon: "heroicons:exclamation-triangle",
  },
];

// 邮件订阅
const subscribeNewsletter = async () => {
  if (!email.value || subscribing.value) return;

  subscribing.value = true;
  try {
    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1500));
    console.log("Newsletter subscription:", email.value);
    email.value = "";
    // 这里可以添加成功提示
  } catch (error) {
    console.error("Subscription failed:", error);
    // 这里可以添加错误提示
  } finally {
    subscribing.value = false;
  }
};
</script>

<style lang="scss" scoped>
// 只保留必要的自定义样式，其余使用原子化CSS

// 背景装饰动画
.bg-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(80px);
  opacity: 0.15;
  animation: float 8s ease-in-out infinite;

  &.bg-orb-1 {
    top: 10%;
    right: 10%;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, #00a8ff, transparent);
    animation-delay: 0s;
  }

  &.bg-orb-2 {
    bottom: 20%;
    left: 5%;
    width: 250px;
    height: 250px;
    background: radial-gradient(circle, #ff4d00, transparent);
    animation-delay: 3s;
  }

  &.bg-orb-3 {
    top: 60%;
    right: 30%;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, #7149c6, transparent);
    animation-delay: 6s;
  }
}

.bg-grid {
  background-image: linear-gradient(
      to right,
      rgba(255, 255, 255, 0.03) 1px,
      transparent 1px
    ),
    linear-gradient(to bottom, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
  background-size: 40px 40px;
  opacity: 0.5;
}

.bg-gradient {
  background: linear-gradient(
    180deg,
    transparent 0%,
    rgba(0, 168, 255, 0.05) 50%,
    transparent 100%
  );
}

// Logo特效样式
.logo-wrapper {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  position: relative;
  overflow: hidden;
  padding: 0.5rem 0;
}

.logo-text {
  font-family: "Trivial", sans-serif;
  font-size: 2rem;
  font-weight: 900;
  letter-spacing: -0.02em;

  &.logo-text-csgo {
    color: #00a8ff;
    text-shadow: 0 0 20px rgba(0, 168, 255, 0.5);
  }

  &.logo-text-skins {
    color: #ff4d00;
    text-shadow: 0 0 20px rgba(255, 77, 0, 0.5);
  }
}

.logo-icon {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-glow {
  position: absolute;
  inset: -10px;
  background: radial-gradient(circle, #00a8ff, transparent);
  opacity: 0;
  transition: all 0.4s ease;
  border-radius: 50%;
  filter: blur(20px);
}

.logo-shine {
  position: absolute;
  top: 0;
  left: -100%;
  width: 50%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.3) 50%,
    transparent 100%
  );
  transform: translateX(-100%);
  transition: transform 1.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.brand-logo:hover {
  .logo-shine {
    transform: translateX(200%);
  }

  .logo-glow {
    opacity: 1;
    transform: scale(1.2);
  }
}

// 社交媒体工具提示
.social-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(-80%);
  background: #0a0b0f;
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  font-size: 0.75rem;
  font-weight: 500;
  white-space: nowrap;
  opacity: 0;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  z-index: 10;

  &::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: rgba(255, 255, 255, 0.1);
  }
}

.social-ripple {
  position: absolute;
  inset: 0;
  background: radial-gradient(circle, #00a8ff, transparent);
  transform: scale(0);
  opacity: 0.3;
  transition: all 0.6s ease;
  border-radius: inherit;
}

// 删除了大部分样式，改用原子化CSS

// 动画定义
@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-20px) rotate(1deg);
  }
  66% {
    transform: translateY(-10px) rotate(-1deg);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.1);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 响应式设计已通过原子化CSS实现
</style>
