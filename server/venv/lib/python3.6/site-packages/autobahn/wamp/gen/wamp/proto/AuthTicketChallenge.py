# automatically generated by the FlatBuffers compiler, do not modify

# namespace: proto

import flatbuffers

class AuthTicket<PERSON>hallenge(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAsAuthTicketChallenge(cls, buf, offset):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = AuthTicketChallenge()
        x.Init(buf, n + offset)
        return x

    # AuthTicketChallenge
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

def AuthTicketChallengeStart(builder): builder.StartObject(0)
def AuthTicketChallengeEnd(builder): return builder.EndObject()
