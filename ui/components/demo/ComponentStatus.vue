<template>
  <div class="component-status flex items-center justify-between p-2 bg-gray-700 rounded">
    <span class="text-white text-sm">{{ name }}</span>
    <div class="flex items-center">
      <div 
        class="w-3 h-3 rounded-full mr-2"
        :class="exists ? 'bg-green-500' : 'bg-red-500'"
      ></div>
      <span 
        class="text-xs font-medium"
        :class="exists ? 'text-green-400' : 'text-red-400'"
      >
        {{ exists ? '正常' : '缺失' }}
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  name: string
  exists: boolean
}

defineProps<Props>()
</script>

<style scoped>
.component-status {
  transition: all 0.2s ease;
}

.component-status:hover {
  background-color: rgb(55, 65, 81);
}
</style> 