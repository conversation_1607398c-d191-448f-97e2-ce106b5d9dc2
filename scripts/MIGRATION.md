# 脚本迁移说明

## 迁移概述

为了更好地组织和管理项目中的各种脚本，我们将散落在不同位置的脚本统一迁移到 `scripts/` 目录下，按功能进行分类管理。

## 迁移详情

### 已迁移脚本

#### 从 `server/tools/` 迁移到 `scripts/tools/`
- `import_csgoinfo.py` - CSGO物品信息导入脚本
- `initChargeLevel.py` - 充值等级初始化脚本
- `initItemPrice.py` - 物品价格初始化脚本
- `sync_csgoinfo.py` - CSGO物品信息同步脚本
- `sync_user_level.py` - 用户等级同步脚本

#### 从 `server/thworker/` 迁移到 `scripts/tools/`
- `temp_data.py` - 临时数据处理脚本

#### 从 `server/` 迁移到 `scripts/tests/`
- `test_battle_api_i18n.py` - 对战API国际化测试脚本

### 新创建的分类目录

- `scripts/debug/` - 调试脚本目录
- `scripts/fixes/` - 修复脚本目录  
- `scripts/monitoring/` - 监控脚本目录
- `scripts/tests/` - 测试脚本目录
- `scripts/tools/` - 工具脚本目录

## 目录结构

```
scripts/
├── README.md                    # 总体说明文档
├── diagnosis.sh                 # 系统诊断脚本
├── debug/                       # 调试脚本
│   ├── README.md               
│   └── debug_user_rooms.py     
├── fixes/                       # 修复脚本
│   ├── README.md               
│   ├── fix_full_rooms.py       
│   ├── fix_rooms_with_no_rounds.py
│   ├── fix_running_rooms.py    
│   ├── fix_stuck_rooms.py      
│   └── fix_team_battle_rooms.py
├── monitoring/                  # 监控脚本
│   ├── README.md               
│   └── monitor_rooms.py        
├── tests/                       # 测试脚本
│   ├── README.md               
│   └── test_battle_api_i18n.py 
└── tools/                       # 工具脚本
    ├── README.md               
    ├── __init__.py             
    ├── import_csgoinfo.py      
    ├── initChargeLevel.py      
    ├── initItemPrice.py        
    ├── sync_csgoinfo.py        
    ├── sync_user_level.py      
    └── temp_data.py            
```

## 使用方式变更

### 原来的使用方式

```bash
# 原来在server目录下运行
cd /www/wwwroot/csgoskins.com.cn/server
python tools/sync_user_level.py
python thworker/temp_data.py
python test_battle_api_i18n.py
```

### 新的使用方式

```bash
# 现在需要在server目录下，用相对路径运行scripts目录中的脚本
cd /www/wwwroot/csgoskins.com.cn/server
python ../scripts/tools/sync_user_level.py
python ../scripts/tools/temp_data.py  
python ../scripts/tests/test_battle_api_i18n.py
```

## 迁移后的优势

### 1. 更好的组织结构
- 按功能分类，清晰明了
- 统一管理，避免散落
- 便于维护和查找

### 2. 完善的文档体系
- 每个目录都有详细的README文档
- 包含脚本用途、运行方式、注意事项
- 提供开发规范和最佳实践

### 3. 统一的运行规范
- 统一的环境配置要求
- 标准化的参数传递方式
- 一致的日志输出格式

### 4. 便于团队协作
- 新成员可以快速了解各脚本用途
- 规范化的开发和使用流程
- 减少重复开发和混乱

## 注意事项

### 1. 路径更新
如果有其他脚本或配置文件引用了这些迁移的脚本，需要更新路径：

```bash
# 检查是否有其他文件引用了旧路径
grep -r "tools/sync_user_level.py" /www/wwwroot/csgoskins.com.cn/
grep -r "thworker/temp_data.py" /www/wwwroot/csgoskins.com.cn/
```

### 2. 定时任务更新
如果有crontab或其他定时任务使用了这些脚本，需要更新路径：

```bash
# 检查当前用户的定时任务
crontab -l | grep -E "(tools/|thworker/)"
```

### 3. 部署脚本更新
如果部署脚本中使用了这些工具，需要更新相应的路径。

### 4. 文档更新
相关的操作文档、运维手册等需要更新脚本路径。

## 清理工作

### 可以安全删除的文件

由于脚本已经迁移到新位置，以下原位置的文件可以考虑删除：

```bash
# server/tools/ 目录下的Python脚本（已迁移）
server/tools/import_csgoinfo.py
server/tools/initChargeLevel.py  
server/tools/initItemPrice.py
server/tools/sync_csgoinfo.py
server/tools/sync_user_level.py

# server/thworker/ 目录下的临时脚本（已迁移）
server/thworker/temp_data.py

# server/ 目录下的测试脚本（已迁移）
server/test_battle_api_i18n.py
```

### 保留的文件

以下文件需要保留：

```bash
# Django应用相关文件
server/tools/__init__.py        # Django应用标识
server/tools/csgo_info0924.csv  # 数据文件
server/tools/default_avatar.jpg # 资源文件

# thworker应用的正常模块文件
server/thworker/__init__.py
server/thworker/admin.py
server/thworker/apps.py
server/thworker/models.py
server/thworker/views.py
# ... 其他Django应用文件
```

## 验证清单

迁移完成后，请验证以下内容：

- [ ] 所有脚本都能在新位置正常运行
- [ ] 相关文档已经更新路径引用
- [ ] 定时任务已经更新路径
- [ ] 部署脚本已经更新路径  
- [ ] 团队成员已经了解新的目录结构
- [ ] 旧位置的冗余文件已经清理

## 问题反馈

如果在使用过程中遇到问题，请：

1. 检查是否在正确的目录下运行脚本
2. 确认脚本路径是否正确
3. 查看对应目录下的README文档
4. 联系开发团队获得支持

---

迁移完成日期：2025-06-28  
文档维护：开发团队
