
from django.db import models, transaction
from django.utils.translation import gettext_lazy as _

from steambase.models import ModelBase, USER_MODEL


class EnvelopeRule(ModelBase):
    RULE_STATE = (
        (0, _("准备中")),
        (1, _("未开始")),
        (2, _("已开始")),
        (3, _("已结束"))
    )
    title = models.CharField(_('title'), max_length=128)
    rule_start_time = models.DateTimeField(_("rule start time"), default=None)
    rule_end_time = models.DateTimeField(_("rule_end_time"), default=None)
    rule_coins = models.FloatField(_("rule coins"), default=0)

    stock = models.SmallIntegerField(_("stock"), default=0)
    limited = models.BooleanField(_("有限库存"), default=True)

    start_time = models.DateTimeField(_("start time"), default=None)
    end_time = models.DateTimeField(_("end time"), default=None)
    handsel_min = models.FloatField(_("handsel min"), default=0)
    handsel_max = models.FloatField(_("handsel max"), default=0)

    password = models.CharField(_("envelop password"), max_length=64, default=None)
    state = models.SmallIntegerField(_("state"), default=0, choices=RULE_STATE)
    desc = models.TextField(_("description"), max_length=1024, default=None, null=True, blank=True)
    order = models.SmallIntegerField(_("order"), default=0)

    # 新增字段，用于控制前端是否显示
    is_show = models.BooleanField(_("is show"), default=True)

    class Meta:
        verbose_name = _("EnvelopeRule")
        verbose_name_plural = _("EnvelopeRule")

    def __str__(self):
        return self.title


class EnvelopeRecord(ModelBase):
    user = models.ForeignKey(USER_MODEL, on_delete=models.SET_NULL, verbose_name=_("user"), related_name='envelop_record', default=None, null=True, blank=True)
    rule = models.ForeignKey(EnvelopeRule, on_delete=models.SET_NULL, verbose_name=_("rule"), related_name='envelop_rule', default=None, null=True, blank=True)
    password = models.CharField(_("envelop password"), max_length=64, default=None, null=True, blank=True)
    handsel = models.FloatField(_("handsel"), default=0)

    class Meta:
        verbose_name = _("EnvelopeRecord")
        verbose_name_plural = _("EnvelopeRecord")

    def __str__(self):
        return self.uid
