#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
异步对战推进管理器
处理对战房间的异步推进逻辑
"""

import asyncio
import logging
import time
import threading
from typing import Optional, Dict, Any, List
from concurrent.futures import ThreadPoolExecutor

from .compat_async import AsyncCompat, get_async_processor
from .battle_config import BattleSystemConfig

logger = logging.getLogger(__name__)


class AsyncBattleProgressionManager:
    """异步对战推进管理器"""
    
    def __init__(self, room_uid: str):
        self.room_uid = room_uid
        self.compat = AsyncCompat()
        self.async_processor = get_async_processor()
        self.config = BattleSystemConfig()
        
        # 状态管理
        self.current_round = 0
        self.total_rounds = 0
        self.is_running = False
        self.is_paused = False
        
        # 异步任务管理
        self.active_tasks = []
        self.round_futures = {}
        
        # 性能监控
        self.start_time = None
        self.round_times = []
        
        logger.info(f"初始化异步对战推进管理器: room_uid={room_uid}")
    
    async def initialize_async(self, total_rounds: int) -> bool:
        """异步初始化"""
        try:
            self.total_rounds = total_rounds
            self.current_round = 0
            self.start_time = time.time()
            
            logger.info(f"异步初始化完成: room={self.room_uid}, total_rounds={total_rounds}")
            return True
            
        except Exception as e:
            logger.error(f"异步初始化失败: room={self.room_uid}, error={e}")
            return False
    
    def initialize_sync(self, total_rounds: int) -> bool:
        """同步初始化（降级方案）"""
        try:
            self.total_rounds = total_rounds
            self.current_round = 0
            self.start_time = time.time()
            
            logger.info(f"同步初始化完成: room={self.room_uid}, total_rounds={total_rounds}")
            return True
            
        except Exception as e:
            logger.error(f"同步初始化失败: room={self.room_uid}, error={e}")
            return False
    
    async def start_first_round_async(self) -> bool:
        """异步开始第一轮"""
        try:
            if self.total_rounds <= 0:
                logger.warning(f"无效的轮次数: room={self.room_uid}, total_rounds={self.total_rounds}")
                return False
            
            self.current_round = 1
            self.is_running = True
            
            # 创建第一轮的异步任务
            round_task = await self.process_round_async(1)
            
            logger.info(f"异步第一轮开始: room={self.room_uid}")
            return True
            
        except Exception as e:
            logger.error(f"异步第一轮开始失败: room={self.room_uid}, error={e}")
            return False
    
    def start_first_round_sync(self) -> bool:
        """同步开始第一轮（降级方案）"""
        try:
            if self.total_rounds <= 0:
                logger.warning(f"无效的轮次数: room={self.room_uid}, total_rounds={self.total_rounds}")
                return False
            
            self.current_round = 1
            self.is_running = True
            
            # 同步处理第一轮
            self.process_round_sync(1)
            
            logger.info(f"同步第一轮开始: room={self.room_uid}")
            return True
            
        except Exception as e:
            logger.error(f"同步第一轮开始失败: room={self.room_uid}, error={e}")
            return False
    
    async def process_round_async(self, round_num: int) -> Dict[str, Any]:
        """异步处理轮次"""
        try:
            round_start_time = time.time()
            
            # 模拟轮次处理
            await asyncio.sleep(0.1)  # 非阻塞延迟
            
            # 记录轮次时间
            round_duration = time.time() - round_start_time
            self.round_times.append(round_duration)
            
            result = {
                'room_uid': self.room_uid,
                'round': round_num,
                'status': 'completed',
                'duration': round_duration,
                'timestamp': int(time.time() * 1000)
            }
            
            logger.info(f"异步轮次处理完成: room={self.room_uid}, round={round_num}, duration={round_duration:.3f}s")
            return result
            
        except Exception as e:
            logger.error(f"异步轮次处理失败: room={self.room_uid}, round={round_num}, error={e}")
            raise
    
    def process_round_sync(self, round_num: int) -> Dict[str, Any]:
        """同步处理轮次（降级方案）"""
        try:
            round_start_time = time.time()
            
            # 模拟轮次处理
            time.sleep(0.05)  # 同步延迟
            
            # 记录轮次时间
            round_duration = time.time() - round_start_time
            self.round_times.append(round_duration)
            
            result = {
                'room_uid': self.room_uid,
                'round': round_num,
                'status': 'completed_sync',
                'duration': round_duration,
                'timestamp': int(time.time() * 1000)
            }
            
            logger.info(f"同步轮次处理完成: room={self.room_uid}, round={round_num}, duration={round_duration:.3f}s")
            return result
            
        except Exception as e:
            logger.error(f"同步轮次处理失败: room={self.room_uid}, round={round_num}, error={e}")
            raise
    
    def advance_to_next_round(self) -> bool:
        """推进到下一轮"""
        try:
            if self.current_round >= self.total_rounds:
                logger.info(f"所有轮次已完成: room={self.room_uid}")
                self.is_running = False
                return False
            
            self.current_round += 1
            
            # 尝试异步处理下一轮
            try:
                coro = self.process_round_async(self.current_round)
                task = self.compat.create_task_compat(coro)
                self.active_tasks.append(task)
                
                logger.info(f"推进到下一轮: room={self.room_uid}, round={self.current_round}")
                return True
                
            except Exception as e:
                logger.warning(f"异步推进失败，使用同步方式: room={self.room_uid}, error={e}")
                self.process_round_sync(self.current_round)
                return True
                
        except Exception as e:
            logger.error(f"推进到下一轮失败: room={self.room_uid}, error={e}")
            return False
    
    def pause_progression(self) -> bool:
        """暂停推进"""
        try:
            self.is_paused = True
            logger.info(f"对战推进已暂停: room={self.room_uid}")
            return True
        except Exception as e:
            logger.error(f"暂停推进失败: room={self.room_uid}, error={e}")
            return False
    
    def resume_progression(self) -> bool:
        """恢复推进"""
        try:
            self.is_paused = False
            logger.info(f"对战推进已恢复: room={self.room_uid}")
            return True
        except Exception as e:
            logger.error(f"恢复推进失败: room={self.room_uid}, error={e}")
            return False
    
    def stop_progression(self) -> bool:
        """停止推进"""
        try:
            self.is_running = False
            self.is_paused = False
            
            # 取消所有活跃任务
            for task in self.active_tasks:
                if hasattr(task, 'cancel'):
                    task.cancel()
            
            self.active_tasks.clear()
            
            logger.info(f"对战推进已停止: room={self.room_uid}")
            return True
            
        except Exception as e:
            logger.error(f"停止推进失败: room={self.room_uid}, error={e}")
            return False
    
    def get_progression_status(self) -> Dict[str, Any]:
        """获取推进状态"""
        try:
            total_duration = time.time() - self.start_time if self.start_time else 0
            avg_round_time = sum(self.round_times) / len(self.round_times) if self.round_times else 0
            
            return {
                'room_uid': self.room_uid,
                'current_round': self.current_round,
                'total_rounds': self.total_rounds,
                'is_running': self.is_running,
                'is_paused': self.is_paused,
                'progress_percentage': (self.current_round / self.total_rounds * 100) if self.total_rounds > 0 else 0,
                'total_duration': total_duration,
                'average_round_time': avg_round_time,
                'active_tasks_count': len(self.active_tasks),
                'completed_rounds': len(self.round_times)
            }
            
        except Exception as e:
            logger.error(f"获取推进状态失败: room={self.room_uid}, error={e}")
            return {'error': str(e)}
    
    def cleanup(self):
        """清理资源"""
        try:
            self.stop_progression()
            self.round_futures.clear()
            logger.info(f"异步对战推进管理器清理完成: room={self.room_uid}")
        except Exception as e:
            logger.error(f"清理失败: room={self.room_uid}, error={e}")


# 工厂函数
def create_progression_manager(room_uid: str) -> AsyncBattleProgressionManager:
    """创建异步对战推进管理器"""
    return AsyncBattleProgressionManager(room_uid)


# 兼容性函数
def initialize_progression_safe(room_uid: str, total_rounds: int) -> AsyncBattleProgressionManager:
    """安全初始化推进管理器"""
    try:
        manager = create_progression_manager(room_uid)
        
        # 尝试异步初始化，失败则降级到同步
        try:
            # 这里应该使用异步，但为了兼容性，直接使用同步
            success = manager.initialize_sync(total_rounds)
            if success:
                manager.start_first_round_sync()
            return manager
        except Exception as e:
            logger.warning(f"异步初始化失败，使用同步方式: room={room_uid}, error={e}")
            manager.initialize_sync(total_rounds)
            manager.start_first_round_sync()
            return manager
            
    except Exception as e:
        logger.error(f"初始化推进管理器失败: room={room_uid}, error={e}")
        raise
