# -*- coding: utf-8 -*-
# Generated by Django 1.11.13 on 2021-10-26 03:31
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import steambase.models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODELL),
        ('charge', '0003_auto_20210422_1631'),
    ]

    operations = [
        migrations.CreateModel(
            name='CDKey',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('key', models.CharField(max_length=128, unique=True, verbose_name='CDKey')),
                ('amount', models.FloatField(blank=True, default=0, null=True, verbose_name='amount')),
                ('state', models.SmallIntegerField(choices=[(0, 'used'), (1, 'unused')], default=1, verbose_name='Key state')),
            ],
            options={
                'verbose_name': 'CDKey info',
                'verbose_name_plural': 'CDKey info',
            },
        ),
        migrations.CreateModel(
            name='CDKeyRecord',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('key', models.CharField(default=None, max_length=128, null=True, verbose_name='CDKey')),
                ('amount', models.FloatField(default=0, verbose_name='amount')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODELL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'CDKey record',
                'verbose_name_plural': 'CDKey record',
            },
        ),
        migrations.CreateModel(
            name='GenerateCDKey',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('amount', models.FloatField(default=0, verbose_name='amount')),
                ('count', models.SmallIntegerField(default=0, verbose_name='count')),
            ],
            options={
                'verbose_name': 'generate CDKey',
                'verbose_name_plural': 'generate CDKey',
            },
        ),
    ]
