import logging

from django.utils import timezone
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.utils.translation import gettext_lazy as _
from django.core.cache import cache
from roll.serializers import RollRoomCacheSerializer
from steambase.enums import GameState, PackageState, PackageSourceType, CaseItemType
from roll.models import RollRoom, RollRoomBet
from roll.business import update_roll_winner, cache_roll_room_data, back_items_to_creater, update_roll_winner_new
from package.models import PackageItem
from steambase.utils import get_serializer_cache_key, get_serializer_cache_key_ectype
from package.models import ItemInfo

_logger = logging.getLogger(__name__)


@receiver(post_save, sender=RollRoom, dispatch_uid="calc_roll_room_result")
def calc_roll_room_result(sender, instance, **kwargs):
    cache_roll_room_data(instance.uid)
    if instance.state == GameState.End.value and instance.win_ts is None:
        instance.win_ts = timezone.now()
        instance.save()
        # update_roll_winner(instance.uid)
        update_roll_winner_new(instance.uid)
    elif instance.state == GameState.Cancelled.value:
        back_items_to_creater(instance.uid)


@receiver(post_save, sender=RollRoomBet, dispatch_uid="send_bet_winner_items")
def send_bet_winner_items(sender, instance, **kwargs):
    if instance.victory:
        user = instance.user
        win_items = instance.win_items.all()
        balance_list = []
        
        # 分类中奖物品，资产类物品和余额类物品
        for i in win_items:
            if i.item_type == CaseItemType.Assets.value:
                item_info = ItemInfo.objects.filter(id=i.item_id).first()  # 直接通过 item_id 获取物品信息
                if item_info:
                    # 创建新的 PackageItem 并分配给用户
                    package_item = PackageItem.objects.create(
                        user=user,
                        item_info=item_info,
                        assetid='0', 
                        instanceid='0',
                        amount=item_info.item_price.price,  
                        case_name="每月抽奖",
                        state=PackageState.Available.value,  # 默认设置为 Available 状态
                        source=PackageSourceType.Roll.value  # 设置来源为 Roll
                    )
                    _logger.info(f'Created package item {package_item} for user {user}')
            else:
                balance_list.append(i.balance)  # 余额类物品处理

        # 更新余额
        if balance_list:
            for balance in balance_list:
                user.update_balance(balance, _('Rollroom win'))

        _logger.info(f'User {user} received win items: {win_items}')


@receiver(post_save, sender=RollRoom, dispatch_uid="cancel_roll_room_cache")
def cancel_roll_room_cache(sender, instance, **kwargs):
    key = get_serializer_cache_key_ectype(instance, RollRoomCacheSerializer)
    cache.delete(key)
