<template>
  <div class="compact-card">
    <div class="flex items-center space-x-3 p-3 bg-gray-800/40 rounded-lg border-l-4 border-blue-400 hover:bg-gray-800/60 transition-all duration-200">
      <!-- 缩略图 -->
      <div class="flex-shrink-0">
        <img
          :src="record.item_info?.image"
          :alt="record.item_info?.name"
          class="w-12 h-12 object-contain rounded bg-gray-700"
          @error="(e) => (e.target as HTMLImageElement).src = 'https://placehold.co/48x48/374151/9ca3af?text=?'"
        />
      </div>
      
      <!-- 信息列 -->
      <div class="flex-grow grid grid-cols-4 gap-3 text-sm">
        <!-- 用户信息 -->
        <div class="min-w-0">
          <div class="text-blue-400 font-medium truncate">{{ record.user_name }}</div>
          <div class="text-white/40 text-xs">Player</div>
        </div>
        
        <!-- 箱子信息 -->
        <div class="min-w-0">
          <div class="text-white truncate">{{ getCaseName() }}</div>
          <div class="text-white/40 text-xs">Case</div>
        </div>
        
        <!-- 物品信息 -->
        <div class="min-w-0">
          <div class="text-yellow-400 font-medium truncate">{{ getSkinName(record.item_info?.name) }}</div>
          <div class="text-white/40 text-xs">{{ getExteriorName() }}</div>
        </div>
        
        <!-- 时间和稀有度 -->
        <div class="text-right">
          <div class="text-green-400 text-xs">Just now</div>
          <div class="text-orange-400 text-xs">Covert</div>
        </div>
      </div>
      
      <!-- 状态指示器 -->
      <div class="flex-shrink-0">
        <div class="w-2 h-8 bg-gradient-to-b from-green-400 to-green-600 rounded-full animate-pulse"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  record: any
}

const props = defineProps<Props>()

const getSkinName = (fullName?: string): string => {
  if (!fullName) return 'Unknown Item'
  const parts = fullName.split(' | ')
  if (parts.length < 2) return fullName
  const skinPart = parts[1].trim()
  return skinPart.split(' (')[0].trim()
}

const getCaseName = (): string => {
  const name = props.record.case_info?.name || 'Unknown Case'
  return name.length > 20 ? name.substring(0, 20) + '...' : name
}

const getExteriorName = (): string => {
  return props.record.item_info?.item_exterior?.exterior_name || 'Factory New'
}
</script>

<style lang="scss" scoped>
.compact-card {
  animation: slideInRight 0.4s ease-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
</style> 