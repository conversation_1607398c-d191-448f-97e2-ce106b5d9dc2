#!/usr/bin/env python
"""
测试Django翻译是否正常工作
"""

import os
import sys
import django
from django.utils import translation
from django.utils.translation import gettext as _

# 设置Django环境
sys.path.append('/www/wwwroot/csgoskins.com.cn/server')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')
django.setup()

def test_translation():
    print("🧪 测试Django翻译功能...")
    
    # 激活中文翻译
    translation.activate('zh-hans')
    
    # 测试一些已知的翻译
    test_strings = [
        "CDKey info",
        "CDKey record", 
        "generate CDKey",
        "user",
        "amount",
        "Charge Record",
        "PayMethod"
    ]
    
    print(f"当前语言: {translation.get_language()}")
    print(f"可用语言: {translation.get_language_info('zh-hans')}")
    
    for test_str in test_strings:
        translated = _(test_str)
        status = "✅" if translated != test_str else "❌"
        print(f"{status} '{test_str}' -> '{translated}'")
    
    # 测试模型的verbose_name
    print("\n📋 测试模型翻译...")
    
    try:
        from charge.models import CDKey, ChargeRecord, PayMethod
        
        print(f"✅ CDKey模型: {CDKey._meta.verbose_name} / {CDKey._meta.verbose_name_plural}")
        print(f"✅ ChargeRecord模型: {ChargeRecord._meta.verbose_name} / {ChargeRecord._meta.verbose_name_plural}")
        print(f"✅ PayMethod模型: {PayMethod._meta.verbose_name} / {PayMethod._meta.verbose_name_plural}")
        
    except Exception as e:
        print(f"❌ 模型测试失败: {e}")
    
    # 测试Admin中的显示
    print("\n🔧 Admin翻译测试...")
    try:
        from django.contrib import admin
        from charge.models import CDKey
        
        # 获取admin配置
        admin_class = admin.site._registry.get(CDKey)
        if admin_class:
            print(f"✅ CDKey Admin已注册")
            # 检查字段翻译
            if hasattr(admin_class, 'list_display'):
                print(f"   list_display: {admin_class.list_display}")
        else:
            print("❌ CDKey Admin未注册")
            
    except Exception as e:
        print(f"❌ Admin测试失败: {e}")

if __name__ == "__main__":
    test_translation()
