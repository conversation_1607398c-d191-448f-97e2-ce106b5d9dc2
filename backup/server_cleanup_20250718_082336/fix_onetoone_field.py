#!/usr/bin/env python3
"""
修复OneToOneField缺少on_delete参数的问题
"""
import os
import re

def fix_onetoone_field(file_path):
    """修复单个文件中的OneToOneField"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 匹配OneToOneField模式，但排除已经有on_delete的
        # 模式：models.OneToOneField(Model, on_delete=models.CASCADE, 但不包含on_delete
        pattern = r'models\.OneToOneField\s*\(\s*([^,\)]+)(?!\s*,\s*on_delete)'
        
        def replace_onetoone(match):
            model_name = match.group(1).strip()
            # 对于OneToOneField，通常使用CASCADE
            return f'models.OneToOneField({model_name, on_delete=models.CASCADE}, on_delete=models.CASCADE'
        
        # 执行替换
        new_content = re.sub(pattern, replace_onetoone, content)
        
        if new_content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            print(f"✅ 修复了 {file_path}")
            return True
        else:
            print(f"⏭️  跳过 {file_path} (无需修复)")
            return False
            
    except Exception as e:
        print(f"❌ 修复 {file_path} 时出错: {e}")
        return False

def main():
    """主函数"""
    print("🔧 开始修复OneToOneField的on_delete参数...")
    
    # 查找所有包含OneToOneField的Python文件
    model_files = []
    for root, dirs, files in os.walk('.'):
        # 跳过虚拟环境和迁移文件
        if 'venv' in root or 'migrations' in root:
            continue
            
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if 'models.OneToOneField' in content:
                            model_files.append(file_path)
                except:
                    continue
    
    print(f"📁 找到 {len(model_files)} 个包含OneToOneField的文件")
    
    fixed_count = 0
    for file_path in model_files:
        if fix_onetoone_field(file_path):
            fixed_count += 1
    
    print(f"\n🎉 修复完成！共修复了 {fixed_count} 个文件")

if __name__ == '__main__':
    main()
