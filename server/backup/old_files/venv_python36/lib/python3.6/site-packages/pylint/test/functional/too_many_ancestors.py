# pylint: disable=missing-docstring, too-few-public-methods

class Aaaa(object):
    pass
class Bbbb(object):
    pass
class Cccc(object):
    pass
class Dddd(object):
    pass
class <PERSON>eee(object):
    pass
class Ffff(object):
    pass
class Gggg(object):
    pass
class Hhhh(object):
    pass

class Iiii(A<PERSON><PERSON>, <PERSON>bbb, <PERSON>ccc, <PERSON>dd<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>fff, Gggg, Hhhh): # [too-many-ancestors]
    pass

class Jjjj(Iiii): # [too-many-ancestors]
    pass
