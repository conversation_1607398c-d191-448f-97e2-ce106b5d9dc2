#!/bin/bash
# Docker清理脚本 - 清理不使用的镜像、容器、卷等

set -e

echo "🧹 开始Docker清理..."
echo "================================"

# 显示清理前的状态
echo "📊 清理前的Docker状态:"
echo "镜像数量: $(docker images -q | wc -l)"
echo "容器数量: $(docker ps -a -q | wc -l)"
echo "卷数量: $(docker volume ls -q | wc -l)"
echo ""

# 1. 清理停止的容器
echo "🗑️  清理停止的容器..."
STOPPED_CONTAINERS=$(docker ps -a -q -f status=exited)
if [ ! -z "$STOPPED_CONTAINERS" ]; then
    docker rm $STOPPED_CONTAINERS
    echo "✅ 已清理停止的容器"
else
    echo "ℹ️  没有停止的容器需要清理"
fi
echo ""

# 2. 清理悬空镜像
echo "🗑️  清理悬空镜像..."
DANGLING_IMAGES=$(docker images -f "dangling=true" -q)
if [ ! -z "$DANGLING_IMAGES" ]; then
    docker rmi $DANGLING_IMAGES
    echo "✅ 已清理悬空镜像"
else
    echo "ℹ️  没有悬空镜像需要清理"
fi
echo ""

# 3. 清理未使用的镜像
echo "🗑️  清理未使用的镜像..."
docker image prune -a -f
echo ""

# 4. 清理未使用的容器
echo "🗑️  清理未使用的容器..."
docker container prune -f
echo ""

# 5. 清理未使用的网络
echo "🗑️  清理未使用的网络..."
docker network prune -f
echo ""

# 6. 清理未使用的卷
echo "🗑️  清理未使用的卷..."
docker volume prune -f
echo ""

# 7. 清理构建缓存
echo "🗑️  清理构建缓存..."
docker builder prune -f
echo ""

# 显示清理后的状态
echo "📊 清理后的Docker状态:"
echo "镜像数量: $(docker images -q | wc -l)"
echo "容器数量: $(docker ps -a -q | wc -l)"
echo "卷数量: $(docker volume ls -q | wc -l)"
echo ""

# 显示当前镜像
echo "📋 当前保留的镜像:"
docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
echo ""

# 显示正在运行的容器
echo "🏃 正在运行的容器:"
docker ps --format "table {{.Names}}\t{{.Image}}\t{{.Status}}\t{{.Ports}}"
echo ""

# 尝试显示磁盘使用情况（可能会超时）
echo "💾 Docker磁盘使用情况:"
timeout 10 docker system df 2>/dev/null || echo "⚠️  磁盘使用情况查询超时，但清理已完成"
echo ""

echo "🎉 Docker清理完成！"
echo "================================"

# 可选：显示系统磁盘使用情况
echo "💿 系统磁盘使用情况:"
df -h / | tail -1
echo ""

echo "💡 提示:"
echo "  - 如需更彻底的清理，可以运行: docker system prune -a -f --volumes"
echo "  - 定期运行此脚本可以保持Docker环境整洁"
echo "  - 清理前请确保重要的容器和镜像已备份"
