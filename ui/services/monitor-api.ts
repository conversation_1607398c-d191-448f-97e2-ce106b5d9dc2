// services/monitor-api.ts

// 修正接口定义以匹配实际API响应
interface StatsData {
  user_number: number
  online_number: number
  case_number: number
  battle_number: number
}

interface UserProfile {
  avatar: string
  nickname: string | null
}

interface UserInfo {
  profile: UserProfile
  uid: string
}

interface ItemPrice {
  update_time: string
  price: number
}

interface ItemCategory {
  cate_id: number
  cate_name: string
  cate_name_en: string
  cate_name_zh_hans: string
  icon: string | null
}

interface ItemQuality {
  quality_id: number
  quality_name: string
  quality_name_en: string
  quality_name_zh_hans: string
  quality_color: string
}

interface ItemRarity {
  rarity_id: number
  rarity_name: string
  rarity_name_en: string
  rarity_name_zh_hans: string
  rarity_color: string
}

interface ItemExterior {
  exterior_id: number
  exterior_name: string
  exterior_name_en: string
  exterior_name_zh_hans: string
  exterior_color: string
}

interface ItemInfo {
  id: number
  item_price: ItemPrice | null
  item_category: ItemCategory | null
  item_quality: ItemQuality
  item_rarity: ItemRarity
  item_exterior: ItemExterior | null
  image: string | null
  name: string
  name_en: string
  name_zh_hans: string
}

interface CaseInfo {
  id: number
  cover: string
  name: string
  name_en: string
  name_zh_hans: string
  case_key: string
  price: number
  open_count: number
}

interface CaseRecord {
  id: number
  user_info: UserInfo
  case_info: CaseInfo
  item_info: ItemInfo
  uid: string
  create_time: string
  update_time: string
  price: number
  source: number
  user: number
  case: number
  open_count: number
}

interface MonitorApiData {
  stats: StatsData
  case_records: CaseRecord[]
}

interface MonitorApiResponse {
  code: number
  body: MonitorApiData
  message: string
}

interface ProcessedMonitorResponse {
  success: boolean
  data: MonitorApiData | null
  message: string
}

class MonitorApi {
  /**
   * 获取监控数据（统计数据和开箱记录）
   */
  async getMonitorData(): Promise<ProcessedMonitorResponse> {
    try {
      const response = await $fetch<MonitorApiResponse>('/api/monitor/data/')

      if (response.code === 0 && response.body) {
        return {
          success: true,
          data: response.body,
          message: response.message || 'Success'
        }
      } else {
        console.error('[Monitor API] 获取监控数据失败:', response)
        return {
          success: false,
          data: null,
          message: response.message || '获取监控数据失败'
        }
      }
    } catch (error: any) {
      console.error('[Monitor API] 获取监控数据请求出错:', error)
      return {
        success: false,
        data: null,
        message: error.message || '获取监控数据请求出错'
      }
    }
  }

  /**
   * 获取站点统计数据
   */
  async getStatsData(): Promise<ProcessedMonitorResponse> {
    try {
      const result = await this.getMonitorData()
      
      if (result.success && result.data) {
        return {
          success: true,
          data: {
            stats: result.data.stats,
            case_records: []
          },
          message: '获取统计数据成功'
        }
      } else {
        return {
          success: false,
          data: null,
          message: result.message || '获取统计数据失败'
        }
      }
    } catch (error: any) {
      console.error('[Monitor API] 获取统计数据出错:', error)
      return {
        success: false,
        data: null,
        message: error.message || '获取统计数据出错'
      }
    }
  }

  /**
   * 获取开箱记录
   */
  async getCaseRecords(): Promise<ProcessedMonitorResponse> {
    try {
      const result = await this.getMonitorData()
      
      if (result.success && result.data) {
        return {
          success: true,
          data: {
            stats: result.data.stats,
            case_records: result.data.case_records
          },
          message: '获取开箱记录成功'
        }
      } else {
        return {
          success: false,
          data: null,
          message: result.message || '获取开箱记录失败'
        }
      }
    } catch (error: any) {
      console.error('[Monitor API] 获取开箱记录出错:', error)
      return {
        success: false,
        data: null,
        message: error.message || '获取开箱记录出错'
      }
    }
  }
}

// 导出单例实例
export const monitorApi = new MonitorApi()

// 保持向后兼容性
export const MonitorApiService = MonitorApi
export { MonitorApi as default }

// 导出类型
export type { 
  StatsData, 
  CaseRecord, 
  MonitorApiData, 
  MonitorApiResponse, 
  ProcessedMonitorResponse,
  UserInfo,
  CaseInfo,
  ItemInfo
} 