# thworker 异步任务框架

thworker是项目的自定义异步任务处理框架，基于Python的threading和schedule库实现，用于替代Celery处理定时任务和后台作业。

## 📋 设计理念

### 为什么选择thworker而不是Celery？
1. **兼容性更好**: 避免Celery在某些环境下的兼容性问题
2. **轻量化**: 相比Celery，thworker更加轻量，依赖更少
3. **可控性强**: 自定义实现，便于调试和维护
4. **集成简单**: 与Django项目深度集成，配置简单

### 核心特性
- **定时任务**: 基于schedule库的灵活定时调度
- **线程池**: 多线程并发执行任务
- **Redis集成**: 使用Redis作为数据存储和锁机制
- **热重载**: 支持动态任务调度配置
- **错误恢复**: 内置错误处理和重试机制

## 🏗️ 架构设计

### 核心组件
```
thworker/
├── __init__.py             # 模块初始化
├── apps.py                 # Django应用配置
├── temp_data.py           # 临时数据处理和模拟任务
├── management/            # Django管理命令
│   └── commands/
│       ├── setupworker.py # 主要的worker启动命令
│       ├── randomprice.py # 随机价格更新任务
│       └── ...            # 其他专用任务命令
└── migrations/            # 数据库迁移文件
```

### 工作原理
```python
# 基本工作流程
1. setupworker.py 启动主调度器
2. 使用 schedule 库注册定时任务
3. threading.Thread 创建工作线程
4. Redis 作为任务状态存储
5. 各业务模块通过 interfaces.py 注册任务
```

## 🚀 核心功能

### 1. 定时任务调度

```python
# 示例：setupworker.py中的任务配置
import schedule
import threading

def setup_schedule_task():
    # 每5分钟更新缓存
    schedule.every(5).minutes.do(update_case_records_cache, count=100)
    
    # 每天凌晨3点10分同步物品价格
    schedule.every().day.at('03:10').do(setup_sync_items_price_worker)
    
    # 每天0点重置用户充值限额
    schedule.every().day.at('00:00').do(lambda: update_daily_recharge_limit(200))
    
    # 每天9点调整充值限额
    schedule.every().day.at('09:00').do(lambda: update_daily_recharge_limit(800))
```

### 2. 业务任务集成

各业务模块通过interfaces.py暴露worker设置函数：

```python
# box/interfaces.py
def setup_case_room_worker():
    """设置对战房间检查任务"""
    pass

def setup_case_bot_worker():
    """设置开箱机器人任务"""
    pass

# charge/interfaces.py  
def setup_charge_check_worker():
    """设置充值状态检查任务"""
    pass

# authentication/interfaces.py
def setup_reset_box_free_worker():
    """设置免费箱子重置任务"""
    pass
```

### 3. 线程池管理

```python
def set_up_threading():
    """启动各种后台线程"""
    
    # 启动定时任务调度线程
    th_schedule = threading.Thread(target=run_schedule, args=())
    th_schedule.daemon = True
    th_schedule.start()
    
    # 启动各业务模块的worker线程
    setup_case_room_worker()
    setup_charge_check_worker()
    setup_roll_room_worker()
    # ... 其他业务worker
```

### 4. Redis数据处理

```python
# temp_data.py中的Redis操作示例
from steambase.redis_con import get_redis

def temp_confirm():
    """模拟交易确认处理"""
    r = get_redis()
    
    while True:
        time.sleep(0.1)
        try:
            # 从Redis队列获取待处理任务
            keys = r.lrange(_trade_list_key, 0, -1)
            
            for key in keys:
                val = r.get(key)
                if val:
                    # 处理业务逻辑
                    deposit = json.loads(val)
                    # ... 业务处理
                    r.set(key, json.dumps(deposit))
                    
        except Exception as e:
            _logger.error(f'处理异常: {e}')
```

## 🔧 使用方法

### 1. 启动Worker

```bash
# 启动主要的worker进程
python manage.py setupworker

# 启动特定的任务
python manage.py randomprice
python manage.py inititemwhitelist
```

### 2. 添加新的定时任务

在setupworker.py中添加新任务：

```python
def setup_schedule_task():
    # 添加新的定时任务
    schedule.every(10).minutes.do(your_custom_task)
    schedule.every().day.at('02:00').do(your_daily_task)
    
    # 启动调度线程
    th = threading.Thread(target=run_schedule, args=())
    th.start()
```

### 3. 创建业务Worker

```python
# 在对应业务模块的interfaces.py中添加
def setup_your_business_worker():
    """设置你的业务worker"""
    
    def worker_function():
        while True:
            try:
                # 你的业务逻辑
                do_business_logic()
                time.sleep(30)  # 执行间隔
            except Exception as e:
                _logger.error(f'业务处理异常: {e}')
                time.sleep(60)  # 异常后等待更长时间
    
    # 启动worker线程
    th = threading.Thread(target=worker_function)
    th.daemon = True
    th.start()
```

## 📊 任务类型

### 1. 系统维护任务
- **缓存更新**: 定期更新Redis缓存数据
- **数据清理**: 清理过期数据和临时文件
- **状态检查**: 检查各服务组件状态

### 2. 业务处理任务  
- **订单处理**: 充值订单状态检查和处理
- **交易确认**: Steam交易状态同步和确认
- **房间管理**: 对战房间状态检查和推进
- **价格同步**: 物品价格定时更新

### 3. 用户相关任务
- **等级计算**: 用户等级和积分计算
- **限额重置**: 每日充值限额重置
- **奖励发放**: 定时奖励和活动处理

### 4. 监控告警任务
- **性能监控**: 系统性能数据收集
- **异常告警**: 系统异常检测和通知
- **数据统计**: 业务数据统计和报表

## 🔒 安全和稳定性

### 1. 锁机制
```python
# 使用Redis实现分布式锁
def with_redis_lock(lock_key, timeout=30):
    def decorator(func):
        def wrapper(*args, **kwargs):
            r = get_redis()
            if r.set(lock_key, '1', nx=True, ex=timeout):
                try:
                    return func(*args, **kwargs)
                finally:
                    r.delete(lock_key)
            else:
                _logger.warning(f'获取锁失败: {lock_key}')
        return wrapper
    return decorator
```

### 2. 错误处理
```python
def safe_task_wrapper(task_func):
    """安全的任务包装器"""
    def wrapper(*args, **kwargs):
        try:
            return task_func(*args, **kwargs)
        except Exception as e:
            _logger.error(f'任务执行异常 {task_func.__name__}: {e}')
            # 可以添加重试逻辑或告警
    return wrapper
```

### 3. 资源管理
- **内存监控**: 定期检查内存使用情况
- **连接池**: 合理管理数据库和Redis连接
- **线程管理**: 避免线程泄漏和资源浪费

## 🚀 性能优化

### 1. 任务优化
- **批量处理**: 合并多个小任务为批量操作
- **异步执行**: 非阻塞的任务执行方式
- **智能调度**: 根据系统负载调整任务频率

### 2. 资源优化
```python
# 连接池复用
def get_optimized_redis():
    """获取优化的Redis连接"""
    return redis.ConnectionPool(
        host='redis', 
        port=6379, 
        max_connections=20,
        socket_keepalive=True,
        socket_keepalive_options={}
    )
```

### 3. 监控指标
- **任务执行时间**: 监控各任务的执行效率
- **错误率**: 统计任务失败率和原因
- **资源使用**: CPU、内存、网络使用情况

## 🔍 调试和维护

### 1. 日志配置
```python
import logging

# 配置thworker专用日志
logger = logging.getLogger('thworker')
logger.setLevel(logging.INFO)

handler = logging.FileHandler('/logs/thworker.log')
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
handler.setFormatter(formatter)
logger.addHandler(handler)
```

### 2. 健康检查
```bash
# 检查worker进程状态
ps aux | grep setupworker

# 检查任务执行日志
tail -f /logs/thworker.log

# 检查Redis中的任务数据
redis-cli keys "*worker*"
```

### 3. 常见问题排查
- **任务不执行**: 检查schedule配置和线程状态
- **内存泄漏**: 检查长时间运行的线程
- **Redis连接**: 检查Redis连接池配置
- **死锁问题**: 检查锁的获取和释放逻辑

## 📞 最佳实践

### 1. 任务设计原则
- **幂等性**: 任务多次执行结果一致
- **原子性**: 任务要么完全成功要么完全失败  
- **可重试**: 支持任务失败后的重试机制
- **状态记录**: 记录任务执行状态和结果

### 2. 性能考虑
- **避免长时间运行**: 将长任务拆分为多个短任务
- **合理设置间隔**: 根据业务需求设置任务执行频率
- **资源清理**: 及时释放不再使用的资源
- **异常处理**: 完善的异常捕获和处理逻辑

### 3. 监控和维护
- **实时监控**: 监控任务执行状态和系统资源
- **日志记录**: 详细记录任务执行过程和结果
- **定期检查**: 定期检查任务配置和性能指标
- **版本管理**: 对任务配置进行版本控制

---

*最后更新时间: 2025-06-18*
*文档版本: v1.0*
