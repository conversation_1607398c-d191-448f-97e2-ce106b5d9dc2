# Changelog 目录

此目录用于存放项目的修复记录、更新历史和变更日志等文档。

## 最新更新

### 2025-06-29
- ✅ **battle-animation-config-implementation-report.md** - 对战动画配置接口后端实现完成报告

### 2025-06-28  
- ✅ **battle-api-internationalization-issues.md** - 对战API国际化问题修复记录
- ✅ **battle-api-update-summary.md** - 对战API系统性梳理总结
- ✅ **battle-api-item-fields-update.md** - 对战API饰品字段补充记录
- ✅ **scripts-organization-summary.md** - 项目脚本整理归类总结

## 目录结构

- `battle-api-*`: 对战系统API相关的修复和更新记录
- `scripts-*`: 项目脚本和工具整理相关记录
- 其他模块的变更记录将按照类似的命名规范存放

## 文档类型

- **问题分析记录**: 详细记录发现的问题、分析过程和解决方案
- **更新总结**: 总结某次重大更新的内容和影响
- **修复记录**: 记录具体的修复步骤和验证结果
- **变更日志**: 按时间顺序记录的系统变更历史

## 注意事项

- 标准的API文档说明应放在 `docs/api/` 目录下
- 架构和设计文档应放在 `docs/backend/` 目录下
- 部署相关文档应放在 `docs/deployment/` 目录下
- 前端相关文档应放在 `docs/frontend/` 目录下
