from django.urls import re_path as url

from charge import views, model_signals



app_name = 'charge'
urlpatterns = [
    url(r'^pay/', views.UserPayView.as_view()),
    url(r'^status/', views.GetChargeStatusView.as_view()),
    url(r'^record/', views.GetChargeRecordView.as_view()),
    url(r'^cancel/', views.UserCancelPayView.as_view()),
    url(r'^rank/chargeweek', views.GetChargeWeekRankView.as_view()),
    url(r'^info/', views.GetChargeInfoView.as_view()),
    url(r'^rank/', views.GetUserLevelRankView.as_view()),
    url(r'^config/', views.GetChargeLevelConfigView.as_view()),
    url(r'^giftconfig/', views.GetNewUserGiftView.as_view()),

    url(r'^detail/', views.GetChargeDetailView.as_view()),

    # 虎皮椒
    url(r'^hppay/pay/', views.HPJPayView.as_view()),
    url(r'^hppay/notify/', views.HPJNotifyView.as_view()),
     # 九嘉
    url(r'^jjpay/pay/', views.JIUJIAPayView.as_view()),
    url(r'^jjpay/notify/', views.JIUJIANotifyView.as_view()),
    # 富信
    url(r'^fuxinkapay/pay/', views.FUXINKAPayView.as_view()),
    url(r'^fuxinkapay/notify/', views.FUXINKANotifyView.as_view()),
    # 优米
    #url(r'^umipay/pay/', views.UMIPAYView.as_view()),
    url(r'^umipay/notify/', views.UMIPAYNotifyView.as_view()),
    # 南山卡
    url(r'^nanshankapay/notify/', views.NANSHANKANotifyView.as_view()),
    # 畅想发卡
    url(r'^cxkapay/cards/', views.GetCxkaCardsInfoView.as_view()),
    url(r'^cxkapay/pay/', views.PayCxkaView.as_view()),
    url(r'^cxkapay/notify/', views.CxkaPayView.as_view()),
    # 微信Native
    url(r'^wechat/pay/', views.WechatPayView.as_view()),
    url(r'^wechat/notify/', views.WechatNotifyView.as_view()),
    # 支付宝
    # url(r'^alipay/pay/', views.AlipayPayView.as_view()),
    url(r'^alipay/notify/', views.AlipayNotifyView.as_view()),
    url(r'^alipay/tyss/notify/', views.AlipayTyssNotifyView.as_view()),
    url(r'^alipay/ydsk/notify/', views.AlipayYdskNotifyView.as_view()),
    url(r'^alipay/lyhs/notify/', views.AlipayLyhsNotifyView.as_view()),
    url(r'^alipay/xyt/notify/', views.AlipayXytNotifyView.as_view()),
    url(r'^alipay/sdwl/notify/', views.AlipaySdwlNotifyView.as_view()),
    url(r'^alipay/wsxh/notify/', views.AlipayWsxhNotifyView.as_view()),
    url(r'^alipay/hysj/notify/', views.AlipayHysjNotifyView.as_view()),
    url(r'^alipay/ysyl/notify/', views.AlipayYsylNotifyView.as_view()),

    # 云闪付 - SahePay
    url(r'^sahepay/notify/', views.SahePayNotifyView.as_view()),
    url(r'^sahepay/return/', views.SahePayNotifyView.as_view()),


    # cdkey
    url(r'^cdkey/pay/', views.ExchangeCDKeyView.as_view()),
    # 全部支付
    url(r'^allpay/pay/', views.AllPayView.as_view()),
    # helloka
    url(r'^hellopay/notify/', views.HELLOPAYNotifyView.as_view()),
    # 查询是否支付成功
    url(r'^paycheck/', views.CHECKPAYView.as_view()),
    # 支付列表
    url(r'^paylist', views.PayListView.as_view()),
]