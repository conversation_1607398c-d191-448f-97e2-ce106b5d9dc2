<template>
  <div 
    v-if="show" 
    class="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4"
    @click="handleClose"
  >
    <div 
      class="bg-gray-800/90 backdrop-blur-md border border-gray-700/50 rounded-2xl p-6 w-full max-w-sm"
      @click.stop
    >
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-bold text-transparent bg-clip-text bg-gradient-primary">
          请输入验证码
        </h3>
        <button 
          @click="handleClose"
          class="text-white/60 hover:text-white transition-colors"
        >
          <i class="i-ph-x-circle text-xl"></i>
        </button>
      </div>
      
      <div class="space-y-4">
        <div class="flex gap-3 items-center">
          <div class="relative flex-1">
            <i class="i-ph-shield-check absolute left-3 top-1/2 -translate-y-1/2 text-white/50"></i>
            <input 
              type="text" 
              v-model="captchaInput"
              class="w-full pl-10 pr-4 py-3 rounded-xl bg-gray-700/50 border border-gray-700/50 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50 text-white placeholder:text-gray-400"
              placeholder="请输入验证码" 
              @keyup.enter="handleConfirm"
            />
          </div>
          <div
            class="relative w-24 h-12 overflow-hidden rounded-xl border border-gray-700/50 bg-gray-700/50 flex items-center justify-center cursor-pointer group"
            @click="handleRefresh"
          >
            <img v-if="image" :src="image" alt="captcha" class="w-full h-full object-cover" />
            <div v-else class="text-sm text-primary animate-pulse">加载中...</div>
            <div class="absolute inset-0 flex items-center justify-center bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity">
              <i class="i-ph-arrow-clockwise text-white text-lg"></i>
            </div>
          </div>
        </div>
        
        <p v-if="error" class="text-sm text-red-400 flex items-center">
          <i class="i-ph-warning-circle mr-1"></i>
          {{ error }}
        </p>
        
        <div class="flex gap-3">
          <button 
            @click="handleClose"
            class="flex-1 py-3 px-4 bg-gray-600 hover:bg-gray-500 text-white font-medium rounded-xl transition-colors"
          >
            取消
          </button>
          <button 
            @click="handleConfirm"
            :disabled="loading"
            class="flex-1 py-3 px-4 bg-gradient-primary hover:opacity-90 text-white font-medium rounded-xl transition-all disabled:opacity-50 flex items-center justify-center"
          >
            <span v-if="loading" class="loading-spinner mr-2"></span>
            确认
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface CaptchaDialogProps {
  show: boolean
  image?: string
  error?: string
  loading?: boolean
}

interface CaptchaDialogEmits {
  (e: 'close'): void
  (e: 'confirm', value: string): void
  (e: 'refresh'): void
}

const props = withDefaults(defineProps<CaptchaDialogProps>(), {
  show: false,
  image: '',
  error: '',
  loading: false
})

const emit = defineEmits<CaptchaDialogEmits>()

const captchaInput = ref('')

const handleClose = () => {
  captchaInput.value = ''
  emit('close')
}

const handleConfirm = () => {
  emit('confirm', captchaInput.value)
}

const handleRefresh = () => {
  emit('refresh')
}

// 监听弹窗状态变化，清空输入
watch(() => props.show, (newValue) => {
  if (!newValue) {
    nextTick(() => {
      captchaInput.value = ''
    })
  }
}, { immediate: false })

// 组件卸载时清理
onBeforeUnmount(() => {
  captchaInput.value = ''
})
</script> 