import logging

from django.contrib import admin

# # from jet.filters import DateRangeFilter  # 暂时禁用，等待Django 4.x兼容版本  # 暂时禁用，等待Django 4.x兼容版本

from steambase.admin import ReadOnlyAdmin
from crash.models import CrashGame, CrashBet, CrashPumpDay, CrashPumpMonth, CrashStatisticsDay, CrashStatisticsMonth
# from crash.models import AutoCrashBet


_logger = logging.getLogger(__name__)


@admin.register(CrashGame)
class CrashGameAdmin(ReadOnlyAdmin):
    fields = ('id', 'hash', 'secret', 'percentage', 'total_amount', 'pump_amount', 'win_amount', 'crash_point',
              'state', 'joinable_time', 'run_time', 'end_time')
    list_display = ('id', 'hash', 'total_amount', 'state', 'crash_point', 'win_amount', 'end_time')
    list_filter = ()  # DateRangeFilter暂时禁用, # 暂时禁用)
    search_fields = ('id', 'hash')
    ordering = ('-create_time',)
    list_per_page = 50


@admin.register(CrashBet)
class CrashBetAdmin(ReadOnlyAdmin):
    fields = ('uid', 'user', 'game', 'amount', 'out_point', 'win_amount', 'win_result', 'create_time')
    list_display = ('uid', 'user', 'game', 'amount', 'out_point', 'win_amount', 'win_result', 'create_time')
    ordering = ('-create_time',)
    list_filter = ()  # DateRangeFilter暂时禁用)
    search_fields = ('uid', 'user__username', 'game__id', 'game__hash')
    list_per_page = 50

# @admin.register(AutoCrashBet)
# class AutoCrashBetAdmin(ReadOnlyAdmin):
#     fields = ('uid', 'user', 'amount', 'auto_out_point', 'enable', 'disposable')
#     list_display = ('uid', 'user', 'amount', 'auto_out_point', 'enable', 'disposable')
#     list_filter = ('amount', 'auto_out_point', 'enable')
#     search_fields = ('uid', 'user__username', )
#     list_per_page = 50


# @admin.register(CrashPumpDay)
# class CrashPumpDayAdmin(ReadOnlyAdmin):
#     fields = ('date', 'amount')
#     list_display = ('date', 'amount')
#     list_filter = ()  # DateRangeFilter暂时禁用,)
#     list_per_page = 50
# 
# 
# @admin.register(CrashPumpMonth)
# class CrashPumpMonthAdmin(ReadOnlyAdmin):
#     fields = ('month', 'amount')
#     extra_readonly_fields = ('month',)
#     list_display = ('month', 'amount')
#     list_filter = ()  # DateRangeFilter暂时禁用,)
#     list_per_page = 50


@admin.register(CrashStatisticsDay)
class CrashStatisticsDayAdmin(ReadOnlyAdmin):
    fields = ('date', 'win_amount', 'lose_amount', 'journal', 'amount', 'test_amount')
    list_display = ('date', 'win_amount', 'lose_amount', 'journal', 'amount', 'test_amount')
    list_filter = ()  # DateRangeFilter暂时禁用,)
    list_per_page = 50


@admin.register(CrashStatisticsMonth)
class CrashStatisticsMonthAdmin(ReadOnlyAdmin):
    fields = ('month', 'win_amount', 'lose_amount', 'journal', 'amount', 'test_amount')
    extra_readonly_fields = ('month',)
    list_display = ('month', 'win_amount', 'lose_amount', 'journal','amount', 'test_amount')
    list_filter = ()  # DateRangeFilter暂时禁用,)
    list_per_page = 50
