<template>
  <div class="p-8">
    <h1 class="text-2xl font-bold mb-6">🔍 Socket.IO 事件调试</h1>
    
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 连接状态 -->
      <div class="bg-gray-100 p-4 rounded">
        <h2 class="font-bold mb-2">连接状态:</h2>
        <div class="text-sm space-y-1">
          <div :class="socketStore.isConnected ? 'text-green-600' : 'text-red-600'">
            状态: {{ socketStore.isConnected ? '✅ 已连接' : '❌ 未连接' }}
          </div>
          <div v-if="socketStore.socketId">Socket ID: {{ socketStore.socketId }}</div>
          <div>消息接收数: {{ socketStore.statistics.messagesReceived }}</div>
          <div>错误数: {{ socketStore.statistics.errors }}</div>
          <div v-if="socketStore.lastMessage">
            最后消息: {{ formatTime(socketStore.lastMessage) }}
          </div>
        </div>
      </div>

      <!-- 控制面板 -->
      <div class="bg-blue-100 p-4 rounded">
        <h2 class="font-bold mb-2">控制面板:</h2>
        <div class="space-y-2">
          <button @click="requestStats" class="bg-blue-500 text-white px-3 py-1 rounded text-sm mr-2">
            请求统计数据
          </button>
          <button @click="requestCaseRecords" class="bg-green-500 text-white px-3 py-1 rounded text-sm mr-2">
            请求开箱记录
          </button>
          <button @click="joinMonitor" class="bg-purple-500 text-white px-3 py-1 rounded text-sm mr-2">
            加入监控频道
          </button>
          <button @click="testDirectConnection" class="bg-red-500 text-white px-3 py-1 rounded text-sm mr-2">
            测试直连后端
          </button>
          <button @click="clearEvents" class="bg-gray-500 text-white px-3 py-1 rounded text-sm">
            清空事件
          </button>
        </div>
      </div>

      <!-- 接收到的事件 -->
      <div class="bg-green-100 p-4 rounded lg:col-span-2">
        <h2 class="font-bold mb-2">接收到的事件 (最近20条):</h2>
        <div class="text-sm font-mono max-h-60 overflow-y-auto space-y-1">
          <div v-for="(event, index) in events" :key="index" class="border-b pb-1">
            <div class="text-blue-600">
              [{{ formatTime(event.timestamp) }}] {{ event.type }}
            </div>
            <div class="text-gray-600 ml-2">
              {{ JSON.stringify(event.data, null, 2) }}
            </div>
          </div>
          <div v-if="events.length === 0" class="text-gray-500">
            暂无事件接收...
          </div>
        </div>
      </div>

      <!-- 发送的消息 -->
      <div class="bg-yellow-100 p-4 rounded lg:col-span-2">
        <h2 class="font-bold mb-2">发送的消息:</h2>
        <div class="text-sm font-mono max-h-40 overflow-y-auto space-y-1">
          <div v-for="(msg, index) in sentMessages" :key="index" class="border-b pb-1">
            <div class="text-orange-600">
              [{{ formatTime(msg.timestamp) }}] {{ msg.event }} → {{ JSON.stringify(msg.data) }}
            </div>
          </div>
          <div v-if="sentMessages.length === 0" class="text-gray-500">
            暂无发送消息...
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const socketStore = useSocketStore()
const events = ref<any[]>([])
const sentMessages = ref<any[]>([])

// 监听所有 Socket 事件
const eventListeners: (() => void)[] = []

function addEvent(type: string, data: any) {
  events.value.unshift({
    type,
    data,
    timestamp: new Date()
  })
  
  // 只保留最近20条
  if (events.value.length > 20) {
    events.value = events.value.slice(0, 20)
  }
}

function addSentMessage(event: string, data: any) {
  sentMessages.value.unshift({
    event,
    data,
    timestamp: new Date()
  })
  
  // 只保留最近10条
  if (sentMessages.value.length > 10) {
    sentMessages.value = sentMessages.value.slice(0, 10)
  }
}

function formatTime(date: Date | string | number) {
  const d = new Date(date)
  return d.toLocaleTimeString()
}

function clearEvents() {
  events.value = []
  sentMessages.value = []
}

// 控制函数
function requestStats() {
  if (socketStore.socket) {
    socketStore.socket.emit('monitor', ['get_stats'])
    addSentMessage('monitor', ['get_stats'])
  }
}

function requestCaseRecords() {
  if (socketStore.socket) {
    socketStore.socket.emit('monitor', ['case_records'])
    addSentMessage('monitor', ['case_records'])
  }
}

function joinMonitor() {
  if (socketStore.socket) {
    socketStore.socket.emit('join', 'monitor')
    addSentMessage('join', 'monitor')
  }
}

async function testDirectConnection() {
  try {
    addEvent('测试', '开始直连后端测试...')

    // 动态导入 Socket.IO
    const io = (await import('socket.io-client')).default

    // 使用与主应用相同的配置（开发环境使用代理）
    const isDev = process.env.NODE_ENV === 'development'
    const socketUrl = isDev ? window.location.origin : 'https://socket.cs2.net.cn'

    const testSocket = io(socketUrl, {
      transports: ['polling', 'websocket'],
      timeout: 10000,
      forceNew: true,
      path: '/socket.io/',
      query: {
        page: 'ws',
        EIO: '4'
      }
    })

    testSocket.on('connect', () => {
      addEvent('直连测试', `连接成功! Socket ID: ${testSocket.id}`)

      // 发送测试消息
      testSocket.emit('join', 'monitor')
      testSocket.emit('monitor', ['get_stats'])
      testSocket.emit('join', 'ws_channel')

      addEvent('直连测试', '已发送测试消息')
    })

    testSocket.on('disconnect', (reason: any) => {
      addEvent('直连测试', `连接断开: ${reason}`)
    })

    testSocket.on('connect_error', (error: any) => {
      addEvent('直连测试', `连接错误: ${error.message}`)
    })

    // 监听所有可能的事件
    const events = ['message', 'ws_channel', 'monitor', 'case_records', 'data', 'update', 'stats']
    events.forEach(eventName => {
      testSocket.on(eventName, (data: any) => {
        addEvent(`直连-${eventName}`, data)
      })
    })

    // 使用 onAny 捕获所有事件
    if (typeof (testSocket as any).onAny === 'function') {
      (testSocket as any).onAny((eventName: string, ...args: any[]) => {
        addEvent(`直连-ANY-${eventName}`, args[0])
      })
    }

    // 5秒后断开
    setTimeout(() => {
      testSocket.disconnect()
      addEvent('直连测试', '主动断开连接')
    }, 5000)

  } catch (error: any) {
    addEvent('直连测试', `错误: ${error.message}`)
  }
}

onMounted(() => {
  // 监听所有可能的 Socket 事件
  const socketEvents = [
    'socket:connected',
    'socket:disconnected', 
    'socket:message',
    'socket:monitor_update',
    'socket:case_records_update',
    'socket:case_opened',
    'socket:room_created',
    'socket:room_updated'
  ]

  socketEvents.forEach(eventName => {
    const listener = (event: CustomEvent) => {
      addEvent(eventName, event.detail)
    }
    window.addEventListener(eventName, listener as EventListener)
    eventListeners.push(() => window.removeEventListener(eventName, listener as EventListener))
  })

  // 如果已经连接，立即请求数据
  if (socketStore.isConnected) {
    setTimeout(() => {
      requestStats()
      requestCaseRecords()
    }, 1000)
  }
})

onUnmounted(() => {
  // 清理事件监听器
  eventListeners.forEach(cleanup => cleanup())
})
</script>
