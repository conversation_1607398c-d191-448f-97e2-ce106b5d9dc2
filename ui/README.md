# CS:GO Skins Frontend - Vue3/Nuxt3

CS:GO皮肤交易平台的前端应用，基于 Vue 3 + Nuxt 3 + TypeScript 构建，集成了实时WebSocket功能和现代化的用户界面。

## Setup

Make sure to install dependencies:

```bash
# npm
npm install

# pnpm
pnpm install

# yarn
yarn install

# bun
bun install
```

## Development Server

Start the development server on `http://localhost:3000`:

```bash
# npm
npm run dev

# pnpm
pnpm dev

# yarn
yarn dev

# bun
bun run dev
```

## Production

Build the application for production:

```bash
# npm
npm run build

# pnpm
pnpm build

# yarn
yarn build

# bun
bun run build
```

Locally preview production build:

```bash
# npm
npm run preview

# pnpm
pnpm preview

# yarn
yarn preview

# bun
bun run preview
```

## 项目文档
- [开发指南](docs/development-guide.md)