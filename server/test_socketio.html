<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Socket.IO 连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .connecting { background-color: #fff3cd; color: #856404; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        input[type="text"] {
            width: 300px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔌 Socket.IO 连接测试</h1>
        <p>测试Socket.IO服务器连接状态和功能</p>
        
        <div id="status" class="status disconnected">
            ❌ 未连接
        </div>
        
        <div>
            <label>Socket.IO服务器地址:</label><br>
            <input type="text" id="serverUrl" value="https://socket.cs2.net.cn" placeholder="输入Socket.IO服务器地址">
            <button onclick="connect()">连接</button>
            <button onclick="disconnect()">断开</button>
        </div>
        
        <div>
            <label>测试消息:</label><br>
            <input type="text" id="testMessage" value="Hello Socket.IO!" placeholder="输入测试消息">
            <button onclick="sendMessage()" id="sendBtn" disabled>发送消息</button>
            <button onclick="sendRedisTest()" id="redisBtn" disabled>测试Redis</button>
            <button onclick="joinRoom()" id="joinBtn" disabled>加入房间</button>
        </div>
        
        <div>
            <button onclick="clearLog()">清空日志</button>
            <button onclick="testConnection()">测试连接</button>
        </div>
        
        <div class="log" id="log">
            <div>📝 连接日志将显示在这里...</div>
        </div>
    </div>

    <script src="https://cdn.socket.io/4.7.0/socket.io.min.js"></script>
    <script>
        let socket = null;
        const statusEl = document.getElementById('status');
        const logEl = document.getElementById('log');
        const sendBtn = document.getElementById('sendBtn');
        const joinBtn = document.getElementById('joinBtn');
        const redisBtn = document.getElementById('redisBtn');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#dc3545' : type === 'success' ? '#28a745' : '#6c757d';
            logEl.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            logEl.scrollTop = logEl.scrollHeight;
        }
        
        function updateStatus(status, message) {
            statusEl.className = `status ${status}`;
            statusEl.textContent = message;
        }
        
        function connect() {
            const url = document.getElementById('serverUrl').value;
            if (socket) {
                socket.disconnect();
            }
            
            log(`🔄 正在连接到: ${url}`, 'info');
            updateStatus('connecting', '🔄 连接中...');
            
            socket = io(url, {
                transports: ['websocket', 'polling'],
                timeout: 10000,
                reconnection: true,
                reconnectionAttempts: 3,
                reconnectionDelay: 1000
            });
            
            socket.on('connect', () => {
                log(`✅ 连接成功! Socket ID: ${socket.id}`, 'success');
                updateStatus('connected', `✅ 已连接 (${socket.id})`);
                sendBtn.disabled = false;
                joinBtn.disabled = false;
                redisBtn.disabled = false;
            });
            
            socket.on('disconnect', (reason) => {
                log(`❌ 连接断开: ${reason}`, 'error');
                updateStatus('disconnected', '❌ 连接断开');
                sendBtn.disabled = true;
                joinBtn.disabled = true;
                redisBtn.disabled = true;
            });
            
            socket.on('connect_error', (error) => {
                log(`❌ 连接错误: ${error.message}`, 'error');
                updateStatus('disconnected', '❌ 连接失败');
                sendBtn.disabled = true;
                joinBtn.disabled = true;
                redisBtn.disabled = true;
            });
            
            socket.on('message', (data) => {
                log(`📨 收到消息: ${JSON.stringify(data)}`, 'success');
            });
            
            socket.on('game_update', (data) => {
                log(`🎮 游戏更新: ${JSON.stringify(data)}`, 'success');
            });
            
            socket.on('chat_message', (data) => {
                log(`💬 聊天消息: ${JSON.stringify(data)}`, 'success');
            });

            socket.on('test_response', (data) => {
                log(`✅ 测试响应: ${JSON.stringify(data)}`, 'success');
            });

            socket.on('test_error', (data) => {
                log(`❌ 测试错误: ${JSON.stringify(data)}`, 'error');
            });

            socket.on('room_joined', (data) => {
                log(`🏠 房间加入成功: ${JSON.stringify(data)}`, 'success');
            });

            socket.on('room_error', (data) => {
                log(`❌ 房间错误: ${JSON.stringify(data)}`, 'error');
            });
        }
        
        function disconnect() {
            if (socket) {
                socket.disconnect();
                socket = null;
                log('🔌 手动断开连接', 'info');
            }
        }
        
        function sendMessage() {
            if (!socket || !socket.connected) {
                log('❌ 未连接到服务器', 'error');
                return;
            }

            const message = document.getElementById('testMessage').value;
            socket.emit('test_message', { message, timestamp: Date.now() });
            log(`📤 发送消息: ${message}`, 'info');
        }

        function sendRedisTest() {
            if (!socket || !socket.connected) {
                log('❌ 未连接到服务器', 'error');
                return;
            }

            const message = document.getElementById('testMessage').value;
            socket.emit('test_message', {
                message: message,
                timestamp: Date.now(),
                testRedis: true  // 标记为Redis测试
            });
            log(`📤 发送Redis测试消息: ${message}`, 'info');
        }
        
        function joinRoom() {
            if (!socket || !socket.connected) {
                log('❌ 未连接到服务器', 'error');
                return;
            }
            
            const roomData = {
                room: 'test_room_123',
                user_id: 'test_user',
                page: 'test'
            };
            socket.emit('join_room', roomData);
            log(`🏠 加入房间: ${JSON.stringify(roomData)}`, 'info');
        }
        
        function testConnection() {
            if (!socket || !socket.connected) {
                log('❌ 未连接到服务器', 'error');
                return;
            }
            
            const startTime = Date.now();
            socket.emit('ping', startTime);
            
            socket.once('pong', (timestamp) => {
                const latency = Date.now() - timestamp;
                log(`🏓 Ping测试: ${latency}ms`, 'success');
            });
            
            log('🏓 发送Ping测试...', 'info');
        }
        
        function clearLog() {
            logEl.innerHTML = '<div>📝 日志已清空</div>';
        }
        
        // 页面加载时自动连接
        window.onload = function() {
            log('🚀 Socket.IO测试页面已加载', 'info');
            log('💡 点击"连接"按钮开始测试', 'info');
        };
    </script>
</body>
</html>
