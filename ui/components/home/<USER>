<template>
  <div class="live-openings-container my-6 relative overflow-hidden">
    <!-- 装饰性背景元素 -->
    <div class="absolute top-0 -left-32 w-64 h-64 rounded-full blur-3xl bg-primary/5 animate-pulse-slow"></div>
    <div class="absolute bottom-0 -right-32 w-64 h-64 rounded-full blur-3xl bg-secondary/5 animate-pulse-slow"></div>
    <div class="absolute inset-0 bg-grid opacity-10"></div>

    <div class="bg-gray-900/80 border border-gray-800/30 shadow-xl backdrop-blur-md rounded-xl overflow-hidden transition-all duration-500 ease-out hover:border-gray-700/50">
      <div class="container mx-auto px-6 py-6">
        <!-- 标题区域 -->
        <div class="flex justify-between items-center mb-6 border-b border-gray-800/50 pb-4">
          <div class="flex items-center">
            <div class="relative">
              <div class="absolute inset-0 bg-green-500/20 blur-md rounded-full animate-breathing"></div>
              <div class="relative text-green-400 text-xl mr-1 animate-pulse-subtle">
                <i
                class="iconfont icon-Live relative text-success text-xl mr-1 animate-pulse-subtle"
              ></i>
              </div>
            </div>
            <div>
              <h3 class="text-lg font-bold">
                <span class="text-gradient animate-gradient">{{ i18nTexts.title }}</span>
              </h3>
            </div>
          </div>

          <button
            class="live-refresh-btn w-10 h-10 flex items-center justify-center bg-transparent hover:bg-primary/10 rounded-full transition-all duration-300 group"
            @click="refreshOpenings()"
            :title="i18nTexts.refresh"
            :disabled="isRefreshing"
          >
            <div 
              class="text-primary text-lg transition-all duration-300 group-hover:rotate-180 group-hover:scale-110"
              :class="{ 'animate-spin': isRefreshing }"
            >
              <i class="iconfont icon-icon_shuaxin"></i>
            </div>
          </button>
        </div>

        <!-- 有数据显示记录 -->
        <div
          v-if="displayRecordsComputed.length > 0"
          ref="recordContainerEl"
          class="relative min-h-[190px] h-[190px] overflow-hidden"
          @mouseenter="pauseAutoScroll"
          @mouseleave="resumeAutoScroll"
        >
          <!-- 暂停指示器 -->
          <Transition name="pause-indicator">
            <div
              v-if="isPaused"
              class="absolute top-3 right-3 bg-black/70 text-white px-3 py-1.5 rounded-lg text-sm backdrop-blur-sm z-20 border border-gray-600/50"
            >
              <i class="i-ph-pause mr-2 animate-pulse"></i>{{ i18nTexts.paused }}
            </div>
          </Transition>

          <!-- 水平滚动容器 -->
          <div class="live-records-container overflow-hidden h-full">
            <div class="grid grid-cols-8 gap-4 xl:grid-cols-8 lg:grid-cols-7 md:grid-cols-6 sm:grid-cols-5 xs:grid-cols-4 h-full">
              <TransitionGroup
                name="record-horizontal"
                tag="div"
                class="contents col-span-full grid grid-cols-8 gap-4 xl:grid-cols-8 lg:grid-cols-7 md:grid-cols-6 sm:grid-cols-5 xs:grid-cols-4 h-full"
                appear
                :css="true"
              >
                <div
                  v-for="(record, index) in displayRecordsComputed.slice(0, maxDisplayCount)"
                  :key="record.id"
                  class="group cursor-pointer relative w-full h-full"
                  :style="{ '--stagger-delay': `${index * 0.05}s` }"
                  @click="navigateToCaseDetail(record)"
                  @mouseenter="(e) => showTooltip(e, record)"
                  @mouseleave="hideTooltip"
                >
                  <div
                    class="record-card-wrapper relative overflow-hidden rounded-md transition-all duration-300 group-hover:scale-105 group-hover:shadow-glow transform-gpu h-full"
                  >
                    <!-- 新记录闪光效果 -->
                    <div
                      v-if="record.isNew"
                      class="absolute inset-0 bg-gradient-to-r from-transparent via-primary/20 to-transparent animate-shimmer z-10 pointer-events-none"
                    ></div>

                    <!-- 悬停遮罩 -->
                    <div
                      class="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-all duration-300 z-10 flex items-center justify-center opacity-0 group-hover:opacity-100 backdrop-blur-sm"
                    >
                      <div
                        class="bg-white/90 text-gray-900 px-2 py-1 rounded-full text-xs font-medium transform scale-75 group-hover:scale-100 transition-transform duration-200"
                      >
                        {{ i18nTexts.viewDetails }}
                      </div>
                    </div>

                    <OpenedSkinCardSimple
                      :open-record="record"
                      class="w-full h-full transition-all duration-300 transform-gpu"
                    />
                  </div>
                </div>
              </TransitionGroup>
              <!-- 占位项，保持布局不向左偏移 -->
              <div
                class="placeholder h-full"
                v-if="displayRecordsComputed.length >= maxDisplayCount"
              >
                <div style="visibility: hidden;" class="h-full">{{ i18nTexts.placeholder }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 无数据显示骨架图 -->
        <div v-else class="skeleton-container min-h-[190px] h-[190px]">
          <div class="grid grid-cols-8 gap-4 xl:grid-cols-8 lg:grid-cols-7 md:grid-cols-6 sm:grid-cols-5 xs:grid-cols-4 h-full">
            <div 
              v-for="index in 8" 
              :key="`skeleton-${index}`"
              class="h-full"
            >
              <div class="skeleton-card h-full">
                <!-- 骨架屏内容 -->
                <div class="skeleton-image"></div>
                <div class="skeleton-content">
                  <div class="skeleton-line skeleton-line-short"></div>
                  <div class="skeleton-line skeleton-line-medium"></div>
                  <div class="skeleton-line skeleton-line-long"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 工具提示 -->
    <Teleport to="body">
      <Transition name="tooltip" appear>
        <div
          v-if="tooltip.show"
          ref="tooltipEl"
          class="live-tooltip fixed z-50 pointer-events-none"
          :style="tooltip.style"
        >
          <div
            class="bg-gray-900/95 border border-gray-700/50 rounded-lg p-4 shadow-2xl backdrop-blur-md max-w-xs"
          >
            <div v-if="tooltip.record">
              <!-- 箱子信息 -->
              <div class="mb-3">
                <h5 class="text-white/70 text-xs font-medium mb-2">{{ i18nTexts.tooltip.caseInfo }}</h5>
                <div class="flex items-center">
                  <img
                    :src="tooltip.record.case_info?.image || tooltip.record.case_info?.cover || getPlaceholderCaseImage()"
                    :alt="getCaseName(tooltip.record)"
                    class="w-12 h-12 object-contain mr-3 rounded transition-transform duration-200 hover:scale-110"
                    @error="(e) => handleImageError(e, 'case')"
                    :title="getCaseName(tooltip.record)"
                  />
                  <div>
                    <h4 class="text-white font-medium text-sm" :title="getCaseName(tooltip.record)">
                      {{ getCaseName(tooltip.record) }}
                    </h4>
                  </div>
                </div>
              </div>

              <!-- 开出物品信息 -->
              <div class="border-t border-gray-700/50 pt-3">
                <h5 class="text-white/70 text-xs font-medium mb-2">{{ i18nTexts.tooltip.itemInfo }}</h5>
                <div class="flex items-center">
                  <img
                    :src="tooltip.record.item_info?.image || getPlaceholderItemImage()"
                    :alt="getSkinName(tooltip.record)"
                    class="w-8 h-8 object-contain mr-2 transition-transform duration-200 hover:scale-110"
                    @error="(e) => handleImageError(e, 'item')"
                    :title="getSkinName(tooltip.record)"
                  />
                  <div>
                    <p class="text-white/80 text-xs font-medium" :title="getSkinName(tooltip.record)">
                      {{ getSkinName(tooltip.record) }}
                    </p>
                    <p class="text-white/50 text-xs" :title="getExteriorName(tooltip.record)">
                      {{ getExteriorName(tooltip.record) }}
                    </p>
                  </div>
                </div>
              </div>

              <!-- 用户信息 -->
              <div class="border-t border-gray-700/50 pt-3 mt-3">
                <h5 class="text-white/70 text-xs font-medium mb-2">{{ i18nTexts.tooltip.userInfo }}</h5>
                <div class="flex items-center">
                  <img
                    :src="tooltip.record.user_info?.profile?.avatar || getPlaceholderUserImage()"
                    :alt="getUserName(tooltip.record)"
                    class="w-8 h-8 object-cover mr-2 rounded-full transition-transform duration-200 hover:scale-110"
                    @error="(e) => handleImageError(e, 'user')"
                    :title="getUserName(tooltip.record)"
                  />
                  <div>
                    <p class="text-white/80 text-xs font-medium" :title="getUserName(tooltip.record)">
                      {{ getUserName(tooltip.record) }}
                    </p>
                    <p class="text-white/50 text-xs" :title="i18nTexts.tooltip.openedTime">
                      {{ formatTime(tooltip.record.create_time || tooltip.record.created_at) }}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Transition>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
// @ts-nocheck
import {
  ref,
  computed,
  onMounted,
  onUnmounted,
  onActivated,
  onDeactivated,
  watch,
  nextTick,
} from "vue";
import { useRouter } from "vue-router";
import { useSocketStore } from "~/stores/socket";
import { useI18n } from "vue-i18n";
import { useNuxtApp } from "#app";
import OpenedSkinCardSimple from "~/components/skin/OpenedSkinCardSimple.vue";

// 获取 Store 实例
const router = useRouter();
const socketStore = useSocketStore();
const { t, locale } = useI18n();

// 定义状态与常量
const displayedRecords = ref([]);
const maxDisplayCount = ref(8); // 单行显示8个
const isRefreshing = ref(false);
let newRecordTimers = {};
const isPaused = ref(false); // 暂停状态变量
const recordContainerEl = ref(null); // 容器元素引用

// 工具提示相关状态
const tooltip = ref({
  show: false,
  record: null,
  style: {},
});
const tooltipEl = ref(null);

let unwatchRecords: any = null;
let unwatchLang: any = null;

// 记录上次刷新和数据更新时间，避免频繁刷新
let lastRefreshTime = 0;
let lastDataUpdateTime = 0;
const MIN_REFRESH_INTERVAL = 10000; // 最小刷新间隔，10秒

// 状态变量
const socketConnected = ref(false); // 跟踪socket连接状态

// 国际化文本
const i18nTexts = computed(() => ({
  title: t("live_openings.title") || "实时开箱",
  refresh: t("live_openings.refresh") || "刷新",

  mysteryCase: t("live_openings.mystery_case") || "神秘箱子",
  paused: t("live_openings.paused") || "已暂停",
  viewDetails: t("live_openings.view_details") || "查看详情",
  placeholder: t("live_openings.placeholder") || "占位",
  unknownItem: t("live_openings.unknown_item") || "未知物品",
  // 工具提示相关文本
  tooltip: {
    caseInfo: t("live_openings.tooltip.case_info") || "箱子信息",
    itemInfo: t("live_openings.tooltip.item_info") || "开出物品",
    openedTime: t("live_openings.tooltip.opened_time") || "开出时间",
    userInfo: t("live_openings.tooltip.user_info") || "开箱用户",
    unknownCase: t("live_openings.tooltip.unknown_case") || "未知箱子",
    unknownSkin: t("live_openings.tooltip.unknown_skin") || "未知皮肤",
    unknownExterior: t("live_openings.tooltip.unknown_exterior") || "未知磨损",
    unknownUser: t("live_openings.tooltip.unknown_user") || "匿名用户",
    caseImageError: t("live_openings.tooltip.case_image_error") || "箱子图片加载失败",
    itemImageError: t("live_openings.tooltip.item_image_error") || "物品图片加载失败",
    userImageError: t("live_openings.tooltip.user_image_error") || "用户头像加载失败"
  }
}));

// 工具函数 - 使用vue-i18n的locale而不是工具函数
const { currentLocale } = useLanguage();

// 根据语言获取本地化名称（使用vue-i18n的locale）
const getLocalizedName = (item: any, fieldName: string): string => {
  if (!item) return ''
  
  const currentLocale = locale.value === 'zh-hans' ? 'zh_hans' : locale.value
  
  // 尝试获取本地化字段
  if (currentLocale === 'zh_hans') {
    return item[`${fieldName}_zh_hans`] || item[`${fieldName}_zh`] || item[fieldName] || ''
  } else if (currentLocale === 'en') {
    return item[`${fieldName}_en`] || item[fieldName] || ''
  }
  
  return item[fieldName] || ''
};

// 获取皮肤名称（使用内部逻辑）
const getSkinName = (record: any): string => {
  if (!record?.item_info) {
    return i18nTexts.value.tooltip.unknownSkin;
  }
  
  // 获取本地化的完整名称
  const localizedFullName = getLocalizedName(record.item_info, 'name');
  const nameToSplit = localizedFullName || record.item_info.name || '';
  
  if (!nameToSplit) {
    return i18nTexts.value.tooltip.unknownSkin;
  }
  
  // 使用简单的分割逻辑
  const parts = nameToSplit.split(' | ');
  if (parts.length < 2) return nameToSplit;
  
  const skinPart = parts[1].trim();
  return skinPart.split(' (')[0].trim();
};

// 获取箱子名称（支持国际化）
const getCaseName = (record: any): string => {
  if (!record?.case_info) return i18nTexts.value.tooltip.unknownCase;
  
  // 优先使用本地化名称
  const localizedName = getLocalizedName(record.case_info, 'name');
  return localizedName || record.case_info.name || record.case_name || i18nTexts.value.tooltip.unknownCase;
};

// 获取磨损名称（支持国际化）
const getExteriorName = (record: any): string => {
  if (!record?.item_info?.item_exterior) return i18nTexts.value.tooltip.unknownExterior;
  
  const localizedName = getLocalizedName(record.item_info.item_exterior, 'exterior_name');
  return localizedName || i18nTexts.value.tooltip.unknownExterior;
};

// 获取用户名称（支持国际化）
const getUserName = (record: any): string => {
  if (!record?.user_info?.profile) return i18nTexts.value.tooltip.unknownUser;
  
  // 根据实际数据结构获取用户昵称
  const userName = record.user_info.profile.nickname || record.user_info.profile.username || record.user_info.profile.name;
  return userName || i18nTexts.value.tooltip.unknownUser;
};

const formatTime = (timestamp: string | undefined): string => {
  if (!timestamp) return t("live_openings.unknown_time") || "未知时间";

  const now = new Date();
  const date = new Date(timestamp);
  const diff = Math.floor((now.getTime() - date.getTime()) / 1000); // 秒数差

  if (diff < 60) {
    return t("live_openings.seconds_ago", { n: diff }) || `${diff}秒前`;
  }
  if (diff < 3600) {
    const minutes = Math.floor(diff / 60);
    return t("live_openings.minutes_ago", { n: minutes }) || `${minutes}分钟前`;
  }
  if (diff < 86400) {
    const hours = Math.floor(diff / 3600);
    return t("live_openings.hours_ago", { n: hours }) || `${hours}小时前`;
  }

  // 对于超过一天的时间，根据语言显示不同格式
  if (locale.value === 'zh-hans') {
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${month}月${day}日`;
  } else {
    // 英文格式
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  }
};

// 获取占位符图片
const getPlaceholderCaseImage = () => "https://placehold.co/64x64/1f2937/ffffff/png?text=📦";
const getPlaceholderItemImage = () => "https://placehold.co/64x64/1f2937/ffffff/png?text=🔫";
const getPlaceholderUserImage = () => "https://placehold.co/32x32/1f2937/ffffff/png?text=👤";

// 图片错误处理
const handleImageError = (event: any, type: 'case' | 'item' | 'user' = 'item') => {
  const target = event.target as HTMLImageElement;
  if (!target) return;
  
  // 避免无限循环
  if (target.src.includes('placehold.co')) return;
  
  // 设置占位符图片
  let placeholder = '';
  switch (type) {
    case 'case':
      placeholder = "https://placehold.co/64x64/1f2937/ffffff/png?text=📦";
      target.alt = i18nTexts.value.tooltip.caseImageError;
      target.title = i18nTexts.value.tooltip.caseImageError;
      break;
    case 'user':
      placeholder = "https://placehold.co/32x32/1f2937/ffffff/png?text=👤";
      target.alt = i18nTexts.value.tooltip.userImageError;
      target.title = i18nTexts.value.tooltip.userImageError;
      break;
    default:
      placeholder = "https://placehold.co/64x64/1f2937/ffffff/png?text=🔫";
      target.alt = i18nTexts.value.tooltip.itemImageError;
      target.title = i18nTexts.value.tooltip.itemImageError;
  }
  
  target.src = placeholder;
};

// 导航到箱子详情页
const navigateToCaseDetail = (record: any) => {
  if (!record?.case_info?.case_key && !record?.case_info?.id) {
    console.warn("[LiveOpenings] 箱子信息不完整，无法导航:", record);
    return;
  }

  // 隐藏工具提示
  hideTooltip();

  // 跳转到箱子详情页面
  const caseKey = record.case_info?.case_key || record.case_info?.id;
  router.push(`/cases/${caseKey}`);
};

// 显示工具提示
const showTooltip = async (event: any, record: any) => {
  tooltip.value.record = record;
  tooltip.value.show = true;

  await nextTick();

  if (tooltipEl.value) {
    updateTooltipPosition(event);
  }
};

// 隐藏工具提示
const hideTooltip = () => {
  tooltip.value.show = false;
  tooltip.value.record = null;
};

// 更新工具提示位置
const updateTooltipPosition = (event: any) => {
  if (!tooltipEl.value) return;

  const rect = (tooltipEl.value as HTMLElement).getBoundingClientRect();
  const viewportWidth = window.innerWidth;
  const viewportHeight = window.innerHeight;

  let left = event.clientX + 15;
  let top = event.clientY - rect.height / 2;

  // 防止超出右边界
  if (left + rect.width > viewportWidth) {
    left = event.clientX - rect.width - 15;
  }

  // 防止超出上边界
  if (top < 10) {
    top = 10;
  }

  // 防止超出下边界
  if (top + rect.height > viewportHeight - 10) {
    top = viewportHeight - rect.height - 10;
  }

  tooltip.value.style = {
    left: `${left}px`,
    top: `${top}px`,
  };
};

// 鼠标移动时更新工具提示位置
const handleMouseMove = (event: any) => {
  if (tooltip.value.show) {
    updateTooltipPosition(event);
  }
};

// 计算过滤后的显示记录
const displayRecordsComputed = computed(() => {
  return displayedRecords.value.filter((record: any) => {
    if (!record || !record.id) return false;
    return record.item_info && record.case_info;
  });
});

// 添加新记录的逐个动画
const addNewRecords = (newRecords: any[]) => {
  if (!newRecords.length) return;

  let index = 0;
  const addNextRecord = () => {
    if (index >= newRecords.length) return;

    const recordToAdd = { ...newRecords[index], isNew: true };
    
    // 添加到开头
    displayedRecords.value.unshift(recordToAdd);

    // 如果超出最大显示数量，移除末尾的记录
    if (displayedRecords.value.length > maxDisplayCount.value) {
      const recordsToRemove = displayedRecords.value.splice(maxDisplayCount.value);
      // 清除被移除记录的计时器
      recordsToRemove.forEach((record: any) => {
        if ((newRecordTimers as any)[record.id]) {
          clearTimeout((newRecordTimers as any)[record.id]);
          delete (newRecordTimers as any)[record.id];
        }
      });
    }

    // 设置计时器移除新记录标识
    if ((newRecordTimers as any)[recordToAdd.id]) {
      clearTimeout((newRecordTimers as any)[recordToAdd.id]);
    }

    newRecordTimers[recordToAdd.id] = setTimeout(() => {
      const idx = displayedRecords.value.findIndex(
        (r) => r.id === recordToAdd.id
      );
      if (idx !== -1) {
        const updatedRecord = { ...displayedRecords.value[idx], isNew: false };
        displayedRecords.value.splice(idx, 1, updatedRecord);
      }
      delete newRecordTimers[recordToAdd.id];
    }, 6000);

    index++;
    requestAnimationFrame(() => {
      setTimeout(addNextRecord, 250);
    });
  };

  addNextRecord();
};

// 暂停自动滚动
const pauseAutoScroll = () => {
  isPaused.value = true;
};

// 恢复自动滚动
const resumeAutoScroll = () => {
  isPaused.value = false;
};

// 处理case_records更新事件
const handleCaseRecordsUpdated = (event) => {
  lastDataUpdateTime = Date.now();

  if (!isRefreshing.value) {
    isRefreshing.value = true;
    setTimeout(() => {
      isRefreshing.value = false;
    }, 1000);
  }

  if (displayedRecords.value.length === 0) {
    setupWatchers();
  }
};

// 静默刷新数据
const silentRefreshData = async () => {
  try {
    // console.log('[LiveOpenings] 静默刷新数据 - 依赖API数据更新');
    // 依赖首页的API数据更新，不主动请求
  } catch (error) {
    console.error('[LiveOpenings] 静默刷新数据失败:', error);
  }
};

// 刷新开箱记录
const refreshOpenings = async (forceRefresh = false) => {
  const now = Date.now();
  if (!forceRefresh && now - lastRefreshTime < MIN_REFRESH_INTERVAL) {
    return;
  }

  if (isRefreshing.value && !forceRefresh) return;

  isRefreshing.value = true;
  lastRefreshTime = now;

  try {
    // console.log('[LiveOpenings] 触发刷新事件，通知首页重新获取API数据');
    if (typeof window !== "undefined") {
      window.dispatchEvent(new CustomEvent("refreshOpenings"));
    }

    setTimeout(() => {
      isRefreshing.value = false;
    }, 2000);

  } catch (error) {
    console.error('[LiveOpenings] 刷新失败:', error);
    isRefreshing.value = false;
  }
};

// 处理Socket消息事件
const handleSocketMessage = (event: Event) => {
  try {
    const customEvent = event as CustomEvent
    const eventData = customEvent.detail
    
    // 新的插件发送格式: { event: 'eventName', data: actualData }
    const eventName = eventData.event || 'unknown'
    const data = eventData.data || eventData
    
    // console.log(`[LiveOpenings] 收到Socket消息 - 事件: ${eventName}`, data) // 调试时可启用
    
    // 根据事件类型处理不同的数据
    if (eventName === 'monitor' && data && data.user_number !== undefined) {
      // 统计数据更新
      // console.log('[LiveOpenings] 处理monitor事件中的统计数据') // 调试时可启用
      socketStore.setStatsData(data)
      return
    }
    
    // 检查是否是开箱记录相关的事件
    const openingEvents = ['case-records', 'case_records', 'opening', 'openings', 'new-opening', 'live_openings', 'box', 'newBox']
    if (openingEvents.includes(eventName) || !eventName || eventName === 'unknown') {
      // 处理开箱记录数据
      if (Array.isArray(data) && data.length > 0) {
        // 检查是否是有效的开箱记录数组
        const validRecords = data.filter(item => 
          item && item.id && (item.item_info || item.item) && (item.case_info || item.case)
        )
        
        if (validRecords.length > 0) {
          // console.log(`[LiveOpenings] 处理${eventName}事件中的开箱记录数组:`, validRecords.length) // 调试时可启用
          socketStore.setCaseRecords(validRecords)
          return
        }
      } else if (data && data.id && (data.item_info || data.item) && (data.case_info || data.case)) {
        // 单个开箱记录
        // console.log(`[LiveOpenings] 处理${eventName}事件中的单个开箱记录:`, data.id) // 调试时可启用
        const currentRecords = [...socketStore.caseRecords]
        // 检查是否已存在
        const exists = currentRecords.find(record => record.id === data.id)
        if (!exists) {
          currentRecords.unshift(data) // 添加到开头
          // 限制数量，避免内存过度使用
          if (currentRecords.length > 100) {
            currentRecords.splice(100)
          }
          socketStore.setCaseRecords(currentRecords)
        }
        return
      }
    }
    
    // 处理其他可能的统计数据格式
    if (data && data.stats) {
      // console.log(`[LiveOpenings] 处理${eventName}事件中的嵌套统计数据`) // 调试时可启用
      socketStore.setStatsData(data.stats)
    } else if (data && data.user_number !== undefined) {
      // console.log(`[LiveOpenings] 处理${eventName}事件中的直接统计数据`) // 调试时可启用
      socketStore.setStatsData(data)
    } else {
      // console.log(`[LiveOpenings] 未识别的Socket消息格式 - 事件: ${eventName}`, data) // 调试时可启用
    }
  } catch (error) {
    console.error('[LiveOpenings] 处理Socket消息失败:', error)
  }
}

// 处理Socket连接事件
const handleSocketConnected = () => {
  // console.log('[LiveOpenings] Socket已连接') // 调试时可启用
  socketConnected.value = true
}

// 处理Socket断开事件  
const handleSocketDisconnected = () => {
  // console.log('[LiveOpenings] Socket已断开') // 调试时可启用
  socketConnected.value = false
}

// 设置事件监听
const setupEventListeners = () => {
  if (typeof window === "undefined") return;

  // 监听Socket消息事件 - 这是关键的新增！
  window.addEventListener("socket:message", handleSocketMessage);
  window.addEventListener("socket:connected", handleSocketConnected);
  window.addEventListener("socket:disconnected", handleSocketDisconnected);
  
  // 保留原有的事件监听
  window.addEventListener("case-records-updated", handleCaseRecordsUpdated);
};

// 移除事件监听
const removeEventListeners = () => {
  if (typeof window === "undefined") return;

  // 移除Socket事件监听
  window.removeEventListener("socket:message", handleSocketMessage);
  window.removeEventListener("socket:connected", handleSocketConnected);
  window.removeEventListener("socket:disconnected", handleSocketDisconnected);
  
  // 移除原有的事件监听
  window.removeEventListener("case-records-updated", handleCaseRecordsUpdated);
};

// 清除所有计时器
const clearAllTimers = () => {
  Object.keys(newRecordTimers).forEach((id) => {
    if (newRecordTimers[id]) {
      clearTimeout(newRecordTimers[id]);
      delete newRecordTimers[id];
    }
  });
};

// 设置数据监听
const setupWatchers = () => {
  if (unwatchRecords) unwatchRecords();
  if (unwatchLang) unwatchLang();

  // 监听开箱记录数据变化
  unwatchRecords = watch(
    () => socketStore.caseRecords,
    (newRecords) => {
      try {
        const validRecords = newRecords.filter((record) => {
          if (!record || !record.id) return false;
          return record.item_info && record.case_info;
        });

        if (validRecords.length > 0) {
          if (!isRefreshing.value) {
            isRefreshing.value = true;
            setTimeout(() => {
              isRefreshing.value = false;
            }, 1000);
          }

          if (displayedRecords.value.length === 0) {
            // 初始渲染
            displayedRecords.value = validRecords
              .slice(0, maxDisplayCount.value)
              .map((r) => ({ ...r, isNew: false }));
          } else if (!isPaused.value) {
            // 检查新记录
            const latestSlice = validRecords.slice(0, maxDisplayCount.value);
            const existingIds = new Set(displayedRecords.value.map((r) => r.id));
            const newRecordsFiltered = latestSlice
              .filter((record) => !existingIds.has(record.id))
              .slice(0, 3);

            if (newRecordsFiltered.length > 0) {
              addNewRecords(newRecordsFiltered);
            }
          }
        }
      } catch (error) {
        console.error('[LiveOpenings] 处理记录变更时出错:', error);
      }
    },
    { deep: true, immediate: true }
  );

  // 监听语言变更（使用 currentLocale 确保与子组件一致）
  unwatchLang = watch(
    () => currentLocale.value,
    (newLocale) => {
      // console.log("[LiveOpenings] 语言已切换至:", newLocale);
      
      // 如果工具提示正在显示，更新其内容
      if (tooltip.value.show && tooltip.value.record) {
        nextTick();
      }
      
      // 触发重新渲染以更新所有国际化文本
      refreshOpenings();
    }
  );

};

// 初始化组件
const initializeComponent = async () => {
  try {
    setupEventListeners();
    setupWatchers();

    // console.log('[LiveOpenings] 组件初始化完成，等待API数据');
  } catch (error) {
    console.error('[LiveOpenings] 组件初始化失败:', error);
  }
};

// 组件清理
const cleanupComponent = () => {
  removeEventListeners();
  if (unwatchRecords) unwatchRecords();
  if (unwatchLang) unwatchLang();
  clearAllTimers();
};

// 生命周期钩子
onMounted(() => {
  initializeComponent();
  lastRefreshTime = Date.now();
  lastDataUpdateTime = Date.now();

  if (typeof window !== "undefined") {
    window.addEventListener("mousemove", handleMouseMove);
  }
});

onUnmounted(() => {
  cleanupComponent();
  if (typeof window !== "undefined") {
    window.removeEventListener("mousemove", handleMouseMove);
  }
});

onActivated(() => {
  setupEventListeners();
  const now = Date.now();
  if (now - lastDataUpdateTime > MIN_REFRESH_INTERVAL * 2) {
    silentRefreshData();
    lastRefreshTime = now;
  }
});

onDeactivated(() => {
  cleanupComponent();
});
</script>

<style scoped>
/* CSS变量定义 */
:root {
  --color-primary: #f59e0b;
  --color-primary-rgb: 245, 158, 11;
  --color-secondary: #8b5cf6;
  --color-secondary-rgb: 139, 92, 246;
  --color-success: #10b981;
  --gradient-primary: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
  --neon-primary: 0 0 20px rgba(245, 158, 11, 0.3);
}

/* 启用硬件加速的 GPU 加速类 */
.transform-gpu {
  transform: translate3d(0, 0, 0);
  will-change: transform;
  backface-visibility: hidden;
}

/* 文本渐变效果 */
.text-gradient {
  background: linear-gradient(
    135deg,
    var(--color-primary),
    var(--color-secondary),
    var(--color-primary)
  );
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

/* 动态渐变动画 */
.animate-gradient {
  animation: gradient-shift 3s ease-in-out infinite;
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* 背景网格 */
.bg-grid {
  background-image: linear-gradient(
      to right,
      rgba(255, 255, 255, 0.05) 0.0625rem,
      transparent 0.0625rem
    ),
    linear-gradient(
      to bottom,
      rgba(255, 255, 255, 0.05) 0.0625rem,
      transparent 0.0625rem
    );
  background-size: 1.25rem 1.25rem;
}

/* 改进的慢脉冲动画 */
.animate-pulse-slow {
  animation: pulse-slow 8s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse-slow {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 0.4;
    transform: scale(1.05);
  }
}

/* 精细脉冲动画 */
.animate-pulse-subtle {
  animation: pulse-subtle 2s ease-in-out infinite;
}

@keyframes pulse-subtle {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

/* 呼吸动画 */
.animate-breathing {
  animation: breathing 4s ease-in-out infinite;
}

@keyframes breathing {
  0%, 100% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
}

/* 脉冲环形动画 */
.animate-pulse-ring {
  animation: pulse-ring 2s cubic-bezier(0.455, 0.03, 0.515, 0.955) infinite;
}

@keyframes pulse-ring {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
  }
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

/* 弹跳微动动画 */
.animate-bounce-subtle {
  animation: bounce-subtle 2s infinite;
}

@keyframes bounce-subtle {
  0%, 20%, 53%, 80%, 100% {
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    transform: translateY(0);
  }
  40%, 43% {
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    transform: translateY(-8px);
  }
  70% {
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    transform: translateY(-4px);
  }
  90% {
    transform: translateY(-2px);
  }
}

/* 漂浮动画 */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* 淡入上升动画 */
.animate-fade-in-up {
  animation: fade-in-up 0.6s ease-out;
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 闪光动画 */
.animate-shimmer {
  animation: shimmer 2s ease-in-out infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* 发光阴影效果 */
.shadow-glow {
  box-shadow: 0 0 20px rgba(245, 158, 11, 0.3);
}

/* 刷新按钮样式 */
.live-refresh-btn {
  backdrop-filter: blur(8px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.live-refresh-btn:hover {
  background: rgba(245, 158, 11, 0.1);
  transform: scale(1.05);
}

/* CSGO按钮样式 */
.csgo-btn-primary {
  background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
  color: #ffffff;
  border: none;
  border-radius: 0.5rem;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 14px 0 rgba(245, 158, 11, 0.3);
}

.csgo-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px 0 rgba(245, 158, 11, 0.4);
}

/* 主容器样式 */
.live-openings-container {
  position: relative;
  isolation: isolate;
  contain: layout style paint;
}

/* 单行水平滚动样式 */
.live-records-container {
  overflow: hidden;
  contain: layout style paint;
}

/* 水平移动动画效果 */
.record-horizontal-move {
  transition: all 0.8s cubic-bezier(0.16, 1, 0.3, 1);
}

/* 新记录从左侧进入 */
.record-horizontal-enter-from {
  opacity: 0;
  transform: translateX(-100%) scale(0.9);
}

.record-horizontal-enter-active {
  transition: all 0.8s cubic-bezier(0.16, 1, 0.3, 1);
  transition-delay: var(--stagger-delay, 0s);
}

.record-horizontal-enter-to {
  opacity: 1;
  transform: translateX(0) scale(1);
}

/* 最末记录淡出消失，不移动 */
.record-horizontal-leave-from {
  opacity: 1;
  transform: translateX(0) scale(1);
}

.record-horizontal-leave-active {
  transition: opacity 0.5s cubic-bezier(0.4, 0, 1, 1),
              transform 0.5s cubic-bezier(0.4, 0, 1, 1);
}

.record-horizontal-leave-to {
  opacity: 0;
  transform: translateX(0) scaleX(1) scaleY(1);
}

/* 暂停提示的过渡效果 */
.pause-indicator-enter-active,
.pause-indicator-leave-active {
  transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
}

.pause-indicator-enter-from,
.pause-indicator-leave-to {
  opacity: 0;
  transform: translateY(-10px) scale(0.9);
}

.pause-indicator-enter-to,
.pause-indicator-leave-from {
  opacity: 1;
  transform: translateY(0) scale(1);
}

/* 工具提示过渡动画 */
.tooltip-enter-active,
.tooltip-leave-active {
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

.tooltip-enter-from,
.tooltip-leave-to {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
}

.tooltip-enter-to,
.tooltip-leave-from {
  opacity: 1;
  transform: translateY(0) scale(1);
}

/* 记录项悬停效果 */
.group:hover {
  transform: translateY(-2px);
}

.group.is-new {
  animation: new-record-highlight 1s ease-out;
}

@keyframes new-record-highlight {
  0% {
    transform: scale(0.9);
    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.7);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 20px rgba(245, 158, 11, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0);
  }
}

/* 卡片包装器样式 */
.record-card-wrapper {
  overflow: hidden;
  border-radius: 0.375rem;
  transform: translate3d(0, 0, 0);
  will-change: transform;
}

/* 悬停遮罩动画优化 */
.group .record-card-wrapper .absolute {
  transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  will-change: opacity, transform;
}

/* 骨架屏样式 */
.skeleton-container {
  opacity: 0.8;
  animation: skeleton-fade-in 0.5s ease-out;
}

@keyframes skeleton-fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 0.8;
    transform: translateY(0);
  }
}

.skeleton-card {
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  border: 1px solid #374151/50;
  border-radius: 0.75rem;
  padding: 1rem;
  height: 220px;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.skeleton-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  animation: skeleton-shimmer 2s infinite;
}

.skeleton-image {
  width: 100%;
  height: 120px;
  background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
  border-radius: 0.5rem;
  position: relative;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.skeleton-image::after {
  content: '🔫';
  font-size: 2rem;
  opacity: 0.3;
  animation: skeleton-pulse 2s ease-in-out infinite;
}

.skeleton-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.skeleton-line {
  height: 14px;
  background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
  border-radius: 7px;
  animation: skeleton-pulse 1.5s ease-in-out infinite;
}

.skeleton-line:nth-child(1) {
  animation-delay: 0s;
}

.skeleton-line:nth-child(2) {
  animation-delay: 0.2s;
}

.skeleton-line:nth-child(3) {
  animation-delay: 0.4s;
}

.skeleton-line-short {
  width: 65%;
  height: 16px; /* 稍微高一点，像标题 */
}

.skeleton-line-medium {
  width: 85%;
  height: 12px;
}

.skeleton-line-long {
  width: 75%;
  height: 10px;
}

/* 骨架屏动画 */
@keyframes skeleton-shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes skeleton-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 减少动画重绘 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 高性能设备增强 */
@media (min-width: 1024px) and (min-height: 768px) {
  .group {
    transition: all 0.5s cubic-bezier(0.16, 1, 0.3, 1);
  }
  
  .record-horizontal-enter-active {
    transition: all 1s cubic-bezier(0.16, 1, 0.3, 1);
  }
  
  .group:hover {
    filter: brightness(1.1) saturate(1.1);
  }
}

/* 打印样式 */
@media print {
  .live-openings-container {
    background: white !important;
    color: black !important;
  }
  
  .live-openings-container * {
    animation: none !important;
    transition: none !important;
  }
}
</style>
