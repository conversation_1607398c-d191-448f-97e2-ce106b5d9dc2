from django.contrib import admin, messages
from django.contrib.admin import TabularInline, StackedInline
from django.contrib.admin.views.main import ChangeList
from django.db.models import Sum

# # from jet.filters import DateRangeFilter  # 暂时禁用，等待Django 4.x兼容版本  # 暂时禁用，等待Django 4.x兼容版本

from steambase.admin import ReadOnlyAdmin, ChangeOnlyAdmin
from b2ctrade.service.admin_actions import sync_b2c_market_price, zbtrecord_export_to_excel
from package.models import ItemPrice, PackageItem
from b2ctrade.models import B2COfficialAccount, B2CItemlist, B2CTradeRecord, B2CTradeStatisticsDay, \
    B2CTradeStatisticsMonth, ZBTBlackList
from b2ctrade.models import ZBTradeRecord, B2CMarketItem
from b2ctrade.service.admin_actions import sync_b2c_trade_bot, sync_b2c_trade_list, b2ctrade_accept, b2ctrade_cancel, zbt_buy


@admin.register(ZBTradeRecord)
class ZBTradeRecordAdmin(ChangeOnlyAdmin):
    fields = ('uid', 'user', 'trade_url', 'item_info', 'amount', 'buy_price', 'state', 'zbt_state', 'zbt_orderid', 'create_time', 'accept_time', 'appid', 'zbt_error', 'package_item_state', 'package_item_source', 'trade_no', 'out_trade_no')
    readonly_fields = ('uid', 'user', 'item_info', 'amount', 'zbt_orderid', 'create_time', 'accept_time', 'buy_price', 'appid', 'trade_url', 'zbt_error', 'package_item_state', 'package_item_source', 'trade_no', 'zbt_state', 'state')
    list_display = ('uid', 'user', 'zbt_orderid', 'market_name_cn','amount', 'buy_price', 'state', 'zbt_state', 'create_time', 'accept_time')
    list_filter = ()  # DateRangeFilter暂时禁用, # 暂时禁用, )
    search_fields = ('user__username', 'item_info__market_hash_name', 'item_info__market_name_cn', 'zbt_orderid', 'uid')
    list_per_page = 50
    actions = [b2ctrade_accept, b2ctrade_cancel, zbt_buy, zbtrecord_export_to_excel]

    def market_name_cn(self, obj):
        if obj.item_info:
            return obj.item_info.market_name_cn
        else:
            return '-'
    
    def market_hash_name(self, obj):
        if obj.item_info:
            return obj.item_info.market_hash_name
        else:
            return '-'
    
    def package_item_state(self, obj):
        if obj.package:
            return obj.package.state
        else:
            return '-'
    
    def package_item_source(self, obj):
        if obj.package:
            return obj.package.source
        else:
            return '-'


@admin.register(ZBTBlackList)
class ZBTBlackListAdmin(admin.ModelAdmin):
    list_display = ('nickname', 'create_time')


# @admin.register(B2CMarketItem)
# class B2CMarketItemAdmin(admin.ModelAdmin):
#     list_display = ('id', 'item_info', 'market_hash_name', 'count', 'unlimited', 'enable', 'price')
#     list_editable = ('enable',)
#     raw_id_fields = ['item_info']
#     search_fields = ('item_info__market_name_cn', 'item_info__market_hash_name')
#     list_filter = ('item_info__appid',)
#     list_per_page = 50
#     actions = [sync_b2c_market_price]

#     def market_hash_name(self, obj):
#         if obj.item_info:
#             return obj.item_info.market_hash_name
#         else:
#             return '-'



# @admin.register(B2CTradeRecord)
# class B2CTradeRecordAdmin(ChangeOnlyAdmin):
#     fields = ('account', 'buyer', 'item_info', 'amount', 'state', 'expire_time', 'create_time', 'accept_time')
#     readonly_fields = ('account', 'buyer', 'item_info', 'amount', 'expire_time', 'create_time', 'accept_time')
#     list_display = ('account', 'buyer', 'market_hash_name', 'market_name_cn','amount', 'state', 'expire_time', 'create_time', 'accept_time')
#     list_editable = ('state',)
#     list_filter = ()  # DateRangeFilter暂时禁用, # 暂时禁用, # 暂时禁用)
#     search_fields = ('account__username', 'buyer__username', 'item_info__market_hash_name', 'item_info__market_name_cn')
#     list_per_page = 50

#     def market_name_cn(self, obj):
#         if obj.item_info:
#             return obj.item_info.market_name_cn
#         else:
#             return '-'
    
#     def market_hash_name(self, obj):
#         if obj.item_info:
#             return obj.item_info.market_hash_name
#         else:
#             return '-'


# @admin.register(B2COfficialAccount)
# class B2COfficialAccountAdmin(admin.ModelAdmin):    
#     list_display = ('user', 'remark', 'customid', 'enable')
#     list_editable = ('enable',)
#     list_per_page = 50
#     actions = [sync_b2c_trade_bot]
# 
# 
# @admin.register(B2CItemlist)
# class B2CItemlistAdmin(admin.ModelAdmin):
#     list_display = ('id', 'item_info', 'market_hash_name', 'count', 'enable', 'price')
#     list_editable = ('enable',)
#     raw_id_fields = ['item_info']
#     search_fields = ('item_info__market_name_cn', 'item_info__market_hash_name')
#     list_per_page = 50
#     actions = [sync_b2c_trade_list]
# 
#     def market_hash_name(self, obj):
#         if obj.item_info:
#             return obj.item_info.market_hash_name
#         else:
#             return '-'
#     
#     def price(self, obj):
#         queryset = ItemPrice.objects.filter(item_info=obj.item_info.id).first()
#         if queryset:
#             return queryset.price
#         else:
#             return '-'
#     price.admin_order_field = 'item_info__item_price'


@admin.register(B2CTradeStatisticsDay)
class B2CTradeStatisticsDayAdmin(ReadOnlyAdmin):
    fields = ('date', 'amount', 'test_amount', 'appid')
    list_display = ('date', 'amount', 'test_amount', 'appid')
    list_filter = ()  # DateRangeFilter暂时禁用,)
    list_per_page = 50


@admin.register(B2CTradeStatisticsMonth)
class B2CTradeStatisticsMonthAdmin(ReadOnlyAdmin):
    fields = ('month', 'amount', 'test_amount', 'appid')
    extra_readonly_fields = ('month',)
    list_display = ('month', 'amount', 'test_amount', 'appid')
    list_filter = ()  # DateRangeFilter暂时禁用, )
    list_per_page = 50

