# -*- coding: utf-8 -*-
# Generated by Django 1.11.13 on 2021-04-14 02:09
from __future__ import unicode_literals

import authentication.models
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import steambase.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0008_alter_user_username_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='AuthUser',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('uid', models.Char<PERSON>ield(default=authentication.models.uid_gen, max_length=128, unique=True, verbose_name='user uid')),
                ('username', models.CharField(blank=True, default=None, max_length=255, null=True, unique=True, verbose_name='user name')),
                ('email', models.CharField(blank=True, default=None, max_length=128, null=True, verbose_name='email address')),
                ('phone', models.CharField(blank=True, default=None, max_length=32, null=True, verbose_name='phone')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.Group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.Permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': 'Auth User',
                'verbose_name_plural': 'Auth User',
            },
        ),
        migrations.CreateModel(
            name='UserActivePointRecord',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('active_point_changed', models.FloatField(default=0.0, verbose_name='changed')),
                ('active_point_before', models.FloatField(default=0.0, verbose_name='change before')),
                ('active_point_after', models.FloatField(default=0.0, verbose_name='change after')),
                ('reason', models.CharField(blank=True, default=None, max_length=256, null=True, verbose_name='reason')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='active_point_records', to=settings.AUTH_USER_MODELL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'User Active Point Record',
                'verbose_name_plural': 'User Active Point Record',
            },
        ),
        migrations.CreateModel(
            name='UserAsset',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tradeurl', models.CharField(blank=True, default=None, max_length=300, null=True, verbose_name='Steam trade url')),
                ('balance', models.FloatField(default=0.0, verbose_name='balance')),
                ('points', models.FloatField(default=0.0, verbose_name='points')),
                ('diamond', models.FloatField(default=0.0, verbose_name='Diamond')),
                ('active_point', models.FloatField(default=0.0, verbose_name='active point')),
                ('total_charge_balance', models.FloatField(default=0.0, verbose_name='total charge balance')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='asset', to=settings.AUTH_USER_MODELL)),
            ],
            options={
                'verbose_name': 'Asset',
                'verbose_name_plural': 'Asset',
            },
        ),
        migrations.CreateModel(
            name='UserBalanceRecord',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('balance_changed', models.FloatField(default=0.0, verbose_name='changed')),
                ('balance_before', models.FloatField(default=0.0, verbose_name='change before')),
                ('balance_after', models.FloatField(default=0.0, verbose_name='change after')),
                ('reason', models.CharField(blank=True, default=None, max_length=256, null=True, verbose_name='reason')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='balance_records', to=settings.AUTH_USER_MODELL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'User Balance Record',
                'verbose_name_plural': 'User Balance Record',
            },
        ),
        migrations.CreateModel(
            name='UserDiamondRecord',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('diamond_changed', models.FloatField(default=0.0, verbose_name='changed')),
                ('diamond_before', models.FloatField(default=0.0, verbose_name='change before')),
                ('diamond_after', models.FloatField(default=0.0, verbose_name='change after')),
                ('reason', models.CharField(blank=True, default=None, max_length=256, null=True, verbose_name='reason')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='diamond_records', to=settings.AUTH_USER_MODELL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'User Diamond Record',
                'verbose_name_plural': 'User Diamond Record',
            },
        ),
        migrations.CreateModel(
            name='UserExtra',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('box_chance_type', models.CharField(choices=[('a', 'Box chance A'), ('b', 'Box chance B'), ('c', 'Box chance C'), ('d', 'Box chance D'), ('e', 'Box chance E')], default='a', max_length=4, verbose_name='box chance type')),
                ('box_free_count', models.IntegerField(default=0, verbose_name='box free count')),
                ('box_free_last', models.DateTimeField(blank=True, default=None, null=True, verbose_name='box free last')),
                ('box_free_zero_count', models.IntegerField(default=0, verbose_name='box free level 0 count')),
                ('box_free_zero_last', models.DateTimeField(blank=True, default=None, null=True, verbose_name='box free level 0 last')),
                ('ban_chat', models.BooleanField(default=False, verbose_name='ban chat')),
                ('ban_deposit', models.BooleanField(default=False, verbose_name='ban deposit')),
                ('ban_withdraw', models.BooleanField(default=False, verbose_name='ban withdraw')),
                ('ban_exchange', models.BooleanField(default=False, verbose_name='ban exchange')),
                ('ban_shop', models.BooleanField(default=False, verbose_name='ban shop')),
                ('ban_roll_room', models.BooleanField(default=False, verbose_name='ban roll room')),
                ('ban_create_roll_room', models.BooleanField(default=True, verbose_name='ban create roll room')),
                ('ban_charge_room', models.BooleanField(default=False, verbose_name='ban charge room')),
                ('box_freegive_count', models.IntegerField(default=3, verbose_name='box free give count')),
                ('freebox_lv1_count', models.IntegerField(default=0, verbose_name='freebox lv1 count')),
                ('freebox_lv2_count', models.IntegerField(default=0, verbose_name='freebox lv2 count')),
                ('freebox_lv3_count', models.IntegerField(default=0, verbose_name='freebox lv3 count')),
                ('freebox_lv4_count', models.IntegerField(default=0, verbose_name='freebox lv4 count')),
                ('freebox_lv5_count', models.IntegerField(default=0, verbose_name='freebox lv5 count')),
                ('luckybox_rate_type', models.CharField(choices=[('a', 'Lucky Box Rate Type A'), ('b', 'Lucky Box Rate Type B'), ('c', 'Lucky Box Rate Type C'), ('d', 'Lucky Box Rate Type D'), ('e', 'Lucky Box Rate Type E')], default='a', max_length=128, verbose_name='luckybox rate type')),
                ('promotion_box_level', models.IntegerField(default=0, verbose_name='box promotion level')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='extra', to=settings.AUTH_USER_MODELL)),
            ],
            options={
                'verbose_name': 'Extra',
                'verbose_name_plural': 'Extra',
            },
        ),
        migrations.CreateModel(
            name='UserPointsRecord',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('points_changed', models.FloatField(default=0.0, verbose_name='changed')),
                ('points_before', models.FloatField(default=0.0, verbose_name='change before')),
                ('points_after', models.FloatField(default=0.0, verbose_name='change after')),
                ('reason', models.CharField(blank=True, default=None, max_length=256, null=True, verbose_name='reason')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='points_records', to=settings.AUTH_USER_MODELL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'User Points Record',
                'verbose_name_plural': 'User Points Record',
            },
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nickname', models.CharField(blank=True, default=None, max_length=255, null=True, verbose_name='nick name')),
                ('avatar', models.CharField(blank=True, default=None, max_length=256, null=True, verbose_name='avatar')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='profile', to=settings.AUTH_USER_MODELL)),
            ],
            options={
                'verbose_name': 'Profile',
                'verbose_name_plural': 'Profile',
            },
        ),
        migrations.CreateModel(
            name='UserStatisticsDay',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('date', models.DateField(default=django.utils.timezone.now, verbose_name='date')),
                ('count', models.IntegerField(default=0, verbose_name='count')),
            ],
            options={
                'verbose_name': 'User Statistics Day',
                'verbose_name_plural': 'User Statistics Day',
                'ordering': ('-create_time',),
            },
        ),
        migrations.CreateModel(
            name='UserSteam',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('steamid', models.CharField(blank=True, default=None, max_length=32, null=True, verbose_name='Steamid')),
                ('personaname', models.CharField(blank=True, default=None, max_length=128, null=True, verbose_name='Steam name')),
                ('profileurl', models.CharField(blank=True, default=None, max_length=300, null=True, verbose_name='profile url')),
                ('avatar', models.CharField(blank=True, default=None, max_length=255, null=True, verbose_name='small avatar')),
                ('avatarmedium', models.CharField(blank=True, default=None, max_length=255, null=True, verbose_name='medium avatar')),
                ('avatarfull', models.CharField(blank=True, default=None, max_length=255, null=True, verbose_name='big avatar')),
                ('level', models.IntegerField(default=0, verbose_name='Steam level')),
                ('own_games_count', models.IntegerField(default=0, verbose_name='own games count')),
                ('dota2_playtime', models.IntegerField(default=0, verbose_name='Dota2 playtime')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='steam', to=settings.AUTH_USER_MODELL)),
            ],
            options={
                'verbose_name': 'Steam',
                'verbose_name_plural': 'Steam',
            },
        ),
    ]
