#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ThWorker到Celery的迁移命令
用于启动Celery任务来替代原有的thworker功能
"""

import logging
import time
from django.core.management.base import BaseCommand
from django.core.cache import cache
from datetime import datetime, timedelta

_logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Migrate thworker functionality to Celery'

    def add_arguments(self, parser):
        parser.add_argument(
            '--init-only',
            action='store_true',
            help='Only run initialization tasks',
        )
        parser.add_argument(
            '--continuous-only', 
            action='store_true',
            help='Only start continuous tasks',
        )

    def handle(self, *args, **options):
        _logger.info('[Celery Migration] 开始迁移thworker到Celery')
        
        # 防止重复执行
        time_limit = timedelta(minutes=1)
        cache_key = 'celery_migration:last_run_time'
        
        last_run_time_str = cache.get(cache_key)
        if last_run_time_str:
            last_run_time = datetime.fromisoformat(last_run_time_str)
            if datetime.now() - last_run_time < time_limit:
                _logger.info('[Celery Migration] 最近已执行，跳过')
                return
        
        # 更新执行时间
        cache.set(cache_key, datetime.now().isoformat(), timeout=None)
        
        try:
            if options['init_only']:
                self.run_initialization()
            elif options['continuous_only']:
                self.start_continuous_tasks()
            else:
                # 默认执行所有
                self.run_initialization()
                self.start_continuous_tasks()
                
            _logger.info('[Celery Migration] 迁移完成')
            
        except Exception as e:
            _logger.error(f'[Celery Migration] 迁移失败: {e}')
            raise

    def run_initialization(self):
        """运行初始化任务"""
        _logger.info('[Celery Migration] 执行初始化任务')
        
        try:
            from thworker.tasks import init_system_task
            result = init_system_task.delay()
            _logger.info(f'[Celery Migration] 初始化任务已提交: {result.id}')
        except Exception as e:
            _logger.error(f'[Celery Migration] 初始化任务失败: {e}')

    def start_continuous_tasks(self):
        """启动持续运行的任务"""
        _logger.info('[Celery Migration] 启动持续运行任务')
        
        # 持续运行的任务列表
        continuous_tasks = [
            'thworker.case_room_worker',
            'thworker.case_bot_worker',
            'thworker.roll_room_worker', 
            'thworker.check_room_state',
            'thworker.zbt_buy_worker',
            'thworker.case_pk_worker',
            'thworker.case_pk_join_worker',
            'thworker.check_envelop_state',
            'thworker.check_pendclose_trade',
            'thworker.update_monitor_data',
        ]
        
        from celery import current_app
        
        for task_name in continuous_tasks:
            try:
                # 启动任务
                result = current_app.send_task(task_name)
                _logger.info(f'[Celery Migration] 启动任务: {task_name} -> {result.id}')
                
                # 短暂延迟避免同时启动过多任务
                time.sleep(1)
                
            except Exception as e:
                _logger.error(f'[Celery Migration] 启动任务失败 {task_name}: {e}')

    def show_migration_status(self):
        """显示迁移状态"""
        self.stdout.write(
            self.style.SUCCESS(
                '\n=== ThWorker到Celery迁移状态 ===\n'
                '✅ 定时任务: 已迁移到Celery Beat\n'
                '✅ 异步任务: 已迁移到Celery Worker\n'
                '✅ 持续任务: 通过兼容层启动\n'
                '✅ 监控: 可通过Flower或Celery命令监控\n'
                '\n=== 使用说明 ===\n'
                '1. 查看任务状态: celery -A steambase inspect active\n'
                '2. 查看定时任务: python manage.py shell -c "from django_celery_beat.models import PeriodicTask; print(PeriodicTask.objects.all())"\n'
                '3. 手动执行任务: celery -A steambase call thworker.task_name\n'
                '4. 监控界面: pip install flower && celery -A steambase flower\n'
            )
        )
