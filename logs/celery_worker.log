 
 -------------- celery@iZj6c3ddfpik1c7yalqdt4Z v5.5.3 (immunity)
--- ***** ----- 
-- ******* ---- Linux-5.10.134-18.al8.x86_64-x86_64-with-glibc2.34 2025-07-18 10:20:38
- *** --- * --- 
- ** ---------- [config]
- ** ---------- .> app:         steambase:0x7fe5c7f8f310
- ** ---------- .> transport:   redis://localhost:6379/0
- ** ---------- .> results:     redis://localhost:6379/0
- *** --- * --- .> concurrency: 2 (prefork)
-- ******* ---- .> task events: OFF (enable -E to monitor tasks in this worker)
--- ***** ----- 
 -------------- [queues]
                .> celery           exchange=celery(direct) key=celery
                

[tasks]
  . authentication.tasks.delete_old_balance_records
  . authentication.tasks.delete_old_log_entries
  . authentication.tasks.delete_old_user_active_point_records
  . authentication.tasks.update_staff_balance
  . box.tasks.delete_old_case_records
  . box.tasks.update_case_records_cache
  . charge.tasks.update_enable_based_on_coins
  . charge.tasks.update_enable_pay_method
  . charge.tasks.update_month_amount
  . charge.tasks.update_today_amount
  . package.tasks.delete_old_package_items
  . roll.tasks.cleanup_duplicate_roll_room_bets
  . sitecfg.tasks.update_daily_recharge_limit
  . sitecfg.tasks.update_domain_verify_switch
  . sitecfg.tasks.update_enable_rule_exam
  . sitecfg.tasks.update_new_user_recharge_limit
  . sitecfg.tasks.update_recharge_box_chance_type
  . sitecfg.tasks.update_register_box_chance_type
  . thworker.case_bot_worker
  . thworker.case_pk_join_worker
  . thworker.case_pk_worker
  . thworker.case_room_worker
  . thworker.check_b2c_trade_state
  . thworker.check_envelop_state
  . thworker.check_pendclose_trade
  . thworker.check_room_state
  . thworker.daily_lottery
  . thworker.hourly_lottery
  . thworker.init_system
  . thworker.roll_room_worker
  . thworker.sync_items_price
  . thworker.update_case_records_cache
  . thworker.update_monitor_data
  . thworker.weekly_lottery
  . thworker.zbt_buy_worker


worker: Warm shutdown (MainProcess)
 
 -------------- celery@iZj6c3ddfpik1c7yalqdt4Z v5.5.3 (immunity)
--- ***** ----- 
-- ******* ---- Linux-5.10.134-18.al8.x86_64-x86_64-with-glibc2.34 2025-07-18 10:24:31
- *** --- * --- 
- ** ---------- [config]
- ** ---------- .> app:         steambase:0x7f679fff62e0
- ** ---------- .> transport:   redis://localhost:6379/0
- ** ---------- .> results:     redis://localhost:6379/0
- *** --- * --- .> concurrency: 2 (prefork)
-- ******* ---- .> task events: OFF (enable -E to monitor tasks in this worker)
--- ***** ----- 
 -------------- [queues]
                .> celery           exchange=celery(direct) key=celery
                

[tasks]
  . authentication.tasks.delete_old_balance_records
  . authentication.tasks.delete_old_log_entries
  . authentication.tasks.delete_old_user_active_point_records
  . authentication.tasks.update_staff_balance
  . box.tasks.delete_old_case_records
  . box.tasks.update_case_records_cache
  . charge.tasks.update_enable_based_on_coins
  . charge.tasks.update_enable_pay_method
  . charge.tasks.update_month_amount
  . charge.tasks.update_today_amount
  . package.tasks.delete_old_package_items
  . roll.tasks.cleanup_duplicate_roll_room_bets
  . sitecfg.tasks.update_daily_recharge_limit
  . sitecfg.tasks.update_domain_verify_switch
  . sitecfg.tasks.update_enable_rule_exam
  . sitecfg.tasks.update_new_user_recharge_limit
  . sitecfg.tasks.update_recharge_box_chance_type
  . sitecfg.tasks.update_register_box_chance_type
  . thworker.case_bot_worker
  . thworker.case_pk_join_worker
  . thworker.case_pk_worker
  . thworker.case_room_worker
  . thworker.check_b2c_trade_state
  . thworker.check_envelop_state
  . thworker.check_pendclose_trade
  . thworker.check_room_state
  . thworker.daily_lottery
  . thworker.hourly_lottery
  . thworker.init_system
  . thworker.roll_room_worker
  . thworker.sync_items_price
  . thworker.update_case_records_cache
  . thworker.update_monitor_data
  . thworker.weekly_lottery
  . thworker.zbt_buy_worker


worker: Warm shutdown (MainProcess)
 
 -------------- celery@iZj6c3ddfpik1c7yalqdt4Z v5.5.3 (immunity)
--- ***** ----- 
-- ******* ---- Linux-5.10.134-18.al8.x86_64-x86_64-with-glibc2.34 2025-07-18 10:30:30
- *** --- * --- 
- ** ---------- [config]
- ** ---------- .> app:         steambase:0x7f3853486310
- ** ---------- .> transport:   redis://localhost:6379/0
- ** ---------- .> results:     redis://localhost:6379/0
- *** --- * --- .> concurrency: 2 (prefork)
-- ******* ---- .> task events: OFF (enable -E to monitor tasks in this worker)
--- ***** ----- 
 -------------- [queues]
                .> celery           exchange=celery(direct) key=celery
                

[tasks]
  . authentication.tasks.delete_old_balance_records
  . authentication.tasks.delete_old_log_entries
  . authentication.tasks.delete_old_user_active_point_records
  . authentication.tasks.update_staff_balance
  . box.tasks.delete_old_case_records
  . box.tasks.update_case_records_cache
  . charge.tasks.update_enable_based_on_coins
  . charge.tasks.update_enable_pay_method
  . charge.tasks.update_month_amount
  . charge.tasks.update_today_amount
  . package.tasks.delete_old_package_items
  . roll.tasks.cleanup_duplicate_roll_room_bets
  . sitecfg.tasks.update_daily_recharge_limit
  . sitecfg.tasks.update_domain_verify_switch
  . sitecfg.tasks.update_enable_rule_exam
  . sitecfg.tasks.update_new_user_recharge_limit
  . sitecfg.tasks.update_recharge_box_chance_type
  . sitecfg.tasks.update_register_box_chance_type
  . thworker.case_bot_worker
  . thworker.case_pk_join_worker
  . thworker.case_pk_worker
  . thworker.case_room_worker
  . thworker.check_b2c_trade_state
  . thworker.check_envelop_state
  . thworker.check_pendclose_trade
  . thworker.check_room_state
  . thworker.daily_lottery
  . thworker.hourly_lottery
  . thworker.init_system
  . thworker.roll_room_worker
  . thworker.sync_items_price
  . thworker.update_case_records_cache
  . thworker.update_monitor_data
  . thworker.weekly_lottery
  . thworker.zbt_buy_worker


worker: Warm shutdown (MainProcess)
 
 -------------- celery@iZj6c3ddfpik1c7yalqdt4Z v5.5.3 (immunity)
--- ***** ----- 
-- ******* ---- Linux-5.10.134-18.al8.x86_64-x86_64-with-glibc2.34 2025-07-18 10:33:20
- *** --- * --- 
- ** ---------- [config]
- ** ---------- .> app:         steambase:0x7f744a5c8340
- ** ---------- .> transport:   redis://localhost:6379/0
- ** ---------- .> results:     redis://localhost:6379/0
- *** --- * --- .> concurrency: 2 (prefork)
-- ******* ---- .> task events: OFF (enable -E to monitor tasks in this worker)
--- ***** ----- 
 -------------- [queues]
                .> celery           exchange=celery(direct) key=celery
                

[tasks]
  . authentication.tasks.delete_old_balance_records
  . authentication.tasks.delete_old_log_entries
  . authentication.tasks.delete_old_user_active_point_records
  . authentication.tasks.update_staff_balance
  . box.tasks.delete_old_case_records
  . box.tasks.update_case_records_cache
  . charge.tasks.update_enable_based_on_coins
  . charge.tasks.update_enable_pay_method
  . charge.tasks.update_month_amount
  . charge.tasks.update_today_amount
  . package.tasks.delete_old_package_items
  . roll.tasks.cleanup_duplicate_roll_room_bets
  . sitecfg.tasks.update_daily_recharge_limit
  . sitecfg.tasks.update_domain_verify_switch
  . sitecfg.tasks.update_enable_rule_exam
  . sitecfg.tasks.update_new_user_recharge_limit
  . sitecfg.tasks.update_recharge_box_chance_type
  . sitecfg.tasks.update_register_box_chance_type
  . thworker.case_bot_worker
  . thworker.case_pk_join_worker
  . thworker.case_pk_worker
  . thworker.case_room_worker
  . thworker.check_b2c_trade_state
  . thworker.check_envelop_state
  . thworker.check_pendclose_trade
  . thworker.check_room_state
  . thworker.daily_lottery
  . thworker.hourly_lottery
  . thworker.init_system
  . thworker.roll_room_worker
  . thworker.sync_items_price
  . thworker.update_case_records_cache
  . thworker.update_monitor_data
  . thworker.weekly_lottery
  . thworker.zbt_buy_worker


worker: Warm shutdown (MainProcess)
 
 -------------- celery@iZj6c3ddfpik1c7yalqdt4Z v5.5.3 (immunity)
--- ***** ----- 
-- ******* ---- Linux-5.10.134-18.al8.x86_64-x86_64-with-glibc2.34 2025-07-18 10:40:03
- *** --- * --- 
- ** ---------- [config]
- ** ---------- .> app:         steambase:0x7ff37b8a1340
- ** ---------- .> transport:   redis://localhost:6379/0
- ** ---------- .> results:     redis://localhost:6379/0
- *** --- * --- .> concurrency: 2 (prefork)
-- ******* ---- .> task events: OFF (enable -E to monitor tasks in this worker)
--- ***** ----- 
 -------------- [queues]
                .> celery           exchange=celery(direct) key=celery
                

[tasks]
  . authentication.tasks.delete_old_balance_records
  . authentication.tasks.delete_old_log_entries
  . authentication.tasks.delete_old_user_active_point_records
  . authentication.tasks.update_staff_balance
  . box.tasks.delete_old_case_records
  . box.tasks.update_case_records_cache
  . charge.tasks.update_enable_based_on_coins
  . charge.tasks.update_enable_pay_method
  . charge.tasks.update_month_amount
  . charge.tasks.update_today_amount
  . package.tasks.delete_old_package_items
  . roll.tasks.cleanup_duplicate_roll_room_bets
  . sitecfg.tasks.update_daily_recharge_limit
  . sitecfg.tasks.update_domain_verify_switch
  . sitecfg.tasks.update_enable_rule_exam
  . sitecfg.tasks.update_new_user_recharge_limit
  . sitecfg.tasks.update_recharge_box_chance_type
  . sitecfg.tasks.update_register_box_chance_type
  . thworker.case_bot_worker
  . thworker.case_pk_join_worker
  . thworker.case_pk_worker
  . thworker.case_room_worker
  . thworker.check_b2c_trade_state
  . thworker.check_envelop_state
  . thworker.check_pendclose_trade
  . thworker.check_room_state
  . thworker.daily_lottery
  . thworker.hourly_lottery
  . thworker.init_system
  . thworker.roll_room_worker
  . thworker.sync_items_price
  . thworker.update_case_records_cache
  . thworker.update_monitor_data
  . thworker.weekly_lottery
  . thworker.zbt_buy_worker


worker: Warm shutdown (MainProcess)
 
 -------------- celery@iZj6c3ddfpik1c7yalqdt4Z v5.5.3 (immunity)
--- ***** ----- 
-- ******* ---- Linux-5.10.134-18.al8.x86_64-x86_64-with-glibc2.34 2025-07-18 11:28:05
- *** --- * --- 
- ** ---------- [config]
- ** ---------- .> app:         steambase:0x7f72c2b76340
- ** ---------- .> transport:   redis://localhost:6379/0
- ** ---------- .> results:     redis://localhost:6379/0
- *** --- * --- .> concurrency: 2 (prefork)
-- ******* ---- .> task events: OFF (enable -E to monitor tasks in this worker)
--- ***** ----- 
 -------------- [queues]
                .> celery           exchange=celery(direct) key=celery
                

[tasks]
  . authentication.tasks.delete_old_balance_records
  . authentication.tasks.delete_old_log_entries
  . authentication.tasks.delete_old_user_active_point_records
  . authentication.tasks.update_staff_balance
  . box.tasks.delete_old_case_records
  . box.tasks.update_case_records_cache
  . charge.tasks.update_enable_based_on_coins
  . charge.tasks.update_enable_pay_method
  . charge.tasks.update_month_amount
  . charge.tasks.update_today_amount
  . package.tasks.delete_old_package_items
  . roll.tasks.cleanup_duplicate_roll_room_bets
  . sitecfg.tasks.update_daily_recharge_limit
  . sitecfg.tasks.update_domain_verify_switch
  . sitecfg.tasks.update_enable_rule_exam
  . sitecfg.tasks.update_new_user_recharge_limit
  . sitecfg.tasks.update_recharge_box_chance_type
  . sitecfg.tasks.update_register_box_chance_type
  . thworker.case_bot_worker
  . thworker.case_pk_join_worker
  . thworker.case_pk_worker
  . thworker.case_room_worker
  . thworker.check_b2c_trade_state
  . thworker.check_envelop_state
  . thworker.check_pendclose_trade
  . thworker.check_room_state
  . thworker.daily_lottery
  . thworker.hourly_lottery
  . thworker.init_system
  . thworker.roll_room_worker
  . thworker.sync_items_price
  . thworker.update_case_records_cache
  . thworker.update_monitor_data
  . thworker.weekly_lottery
  . thworker.zbt_buy_worker


worker: Warm shutdown (MainProcess)
 
 -------------- celery@iZj6c3ddfpik1c7yalqdt4Z v5.5.3 (immunity)
--- ***** ----- 
-- ******* ---- Linux-5.10.134-18.al8.x86_64-x86_64-with-glibc2.34 2025-07-18 11:32:49
- *** --- * --- 
- ** ---------- [config]
- ** ---------- .> app:         steambase:0x7fe2c145b2e0
- ** ---------- .> transport:   redis://localhost:6379/0
- ** ---------- .> results:     redis://localhost:6379/0
- *** --- * --- .> concurrency: 2 (prefork)
-- ******* ---- .> task events: OFF (enable -E to monitor tasks in this worker)
--- ***** ----- 
 -------------- [queues]
                .> celery           exchange=celery(direct) key=celery
                

[tasks]
  . authentication.tasks.delete_old_balance_records
  . authentication.tasks.delete_old_log_entries
  . authentication.tasks.delete_old_user_active_point_records
  . authentication.tasks.update_staff_balance
  . box.tasks.delete_old_case_records
  . box.tasks.update_case_records_cache
  . charge.tasks.update_enable_based_on_coins
  . charge.tasks.update_enable_pay_method
  . charge.tasks.update_month_amount
  . charge.tasks.update_today_amount
  . package.tasks.delete_old_package_items
  . roll.tasks.cleanup_duplicate_roll_room_bets
  . sitecfg.tasks.update_daily_recharge_limit
  . sitecfg.tasks.update_domain_verify_switch
  . sitecfg.tasks.update_enable_rule_exam
  . sitecfg.tasks.update_new_user_recharge_limit
  . sitecfg.tasks.update_recharge_box_chance_type
  . sitecfg.tasks.update_register_box_chance_type
  . thworker.case_bot_worker
  . thworker.case_pk_join_worker
  . thworker.case_pk_worker
  . thworker.case_room_worker
  . thworker.check_b2c_trade_state
  . thworker.check_envelop_state
  . thworker.check_pendclose_trade
  . thworker.check_room_state
  . thworker.daily_lottery
  . thworker.hourly_lottery
  . thworker.init_system
  . thworker.roll_room_worker
  . thworker.sync_items_price
  . thworker.update_case_records_cache
  . thworker.update_monitor_data
  . thworker.weekly_lottery
  . thworker.zbt_buy_worker


worker: Warm shutdown (MainProcess)
 
 -------------- celery@iZj6c3ddfpik1c7yalqdt4Z v5.5.3 (immunity)
--- ***** ----- 
-- ******* ---- Linux-5.10.134-18.al8.x86_64-x86_64-with-glibc2.34 2025-07-18 11:59:08
- *** --- * --- 
- ** ---------- [config]
- ** ---------- .> app:         steambase:0x7f16af4522e0
- ** ---------- .> transport:   redis://localhost:6379/0
- ** ---------- .> results:     redis://localhost:6379/0
- *** --- * --- .> concurrency: 2 (prefork)
-- ******* ---- .> task events: OFF (enable -E to monitor tasks in this worker)
--- ***** ----- 
 -------------- [queues]
                .> celery           exchange=celery(direct) key=celery
                

[tasks]
  . authentication.tasks.delete_old_balance_records
  . authentication.tasks.delete_old_log_entries
  . authentication.tasks.delete_old_user_active_point_records
  . authentication.tasks.update_staff_balance
  . box.tasks.delete_old_case_records
  . box.tasks.update_case_records_cache
  . charge.tasks.update_enable_based_on_coins
  . charge.tasks.update_enable_pay_method
  . charge.tasks.update_month_amount
  . charge.tasks.update_today_amount
  . package.tasks.delete_old_package_items
  . roll.tasks.cleanup_duplicate_roll_room_bets
  . sitecfg.tasks.update_daily_recharge_limit
  . sitecfg.tasks.update_domain_verify_switch
  . sitecfg.tasks.update_enable_rule_exam
  . sitecfg.tasks.update_new_user_recharge_limit
  . sitecfg.tasks.update_recharge_box_chance_type
  . sitecfg.tasks.update_register_box_chance_type
  . thworker.case_bot_worker
  . thworker.case_pk_join_worker
  . thworker.case_pk_worker
  . thworker.case_room_worker
  . thworker.check_b2c_trade_state
  . thworker.check_envelop_state
  . thworker.check_pendclose_trade
  . thworker.check_room_state
  . thworker.daily_lottery
  . thworker.hourly_lottery
  . thworker.init_system
  . thworker.roll_room_worker
  . thworker.sync_items_price
  . thworker.update_case_records_cache
  . thworker.update_monitor_data
  . thworker.weekly_lottery
  . thworker.zbt_buy_worker


worker: Warm shutdown (MainProcess)
 
 -------------- celery@iZj6c3ddfpik1c7yalqdt4Z v5.5.3 (immunity)
--- ***** ----- 
-- ******* ---- Linux-5.10.134-18.al8.x86_64-x86_64-with-glibc2.34 2025-07-18 12:09:34
- *** --- * --- 
- ** ---------- [config]
- ** ---------- .> app:         steambase:0x7fba2cee4310
- ** ---------- .> transport:   redis://localhost:6379/0
- ** ---------- .> results:     redis://localhost:6379/0
- *** --- * --- .> concurrency: 2 (prefork)
-- ******* ---- .> task events: OFF (enable -E to monitor tasks in this worker)
--- ***** ----- 
 -------------- [queues]
                .> celery           exchange=celery(direct) key=celery
                

[tasks]
  . authentication.tasks.delete_old_balance_records
  . authentication.tasks.delete_old_log_entries
  . authentication.tasks.delete_old_user_active_point_records
  . authentication.tasks.update_staff_balance
  . box.tasks.delete_old_case_records
  . box.tasks.update_case_records_cache
  . charge.tasks.update_enable_based_on_coins
  . charge.tasks.update_enable_pay_method
  . charge.tasks.update_month_amount
  . charge.tasks.update_today_amount
  . package.tasks.delete_old_package_items
  . roll.tasks.cleanup_duplicate_roll_room_bets
  . sitecfg.tasks.update_daily_recharge_limit
  . sitecfg.tasks.update_domain_verify_switch
  . sitecfg.tasks.update_enable_rule_exam
  . sitecfg.tasks.update_new_user_recharge_limit
  . sitecfg.tasks.update_recharge_box_chance_type
  . sitecfg.tasks.update_register_box_chance_type
  . thworker.case_bot_worker
  . thworker.case_pk_join_worker
  . thworker.case_pk_worker
  . thworker.case_room_worker
  . thworker.check_b2c_trade_state
  . thworker.check_envelop_state
  . thworker.check_pendclose_trade
  . thworker.check_room_state
  . thworker.daily_lottery
  . thworker.hourly_lottery
  . thworker.init_system
  . thworker.roll_room_worker
  . thworker.sync_items_price
  . thworker.update_case_records_cache
  . thworker.update_monitor_data
  . thworker.weekly_lottery
  . thworker.zbt_buy_worker


worker: Warm shutdown (MainProcess)
 
 -------------- celery@iZj6c3ddfpik1c7yalqdt4Z v5.5.3 (immunity)
--- ***** ----- 
-- ******* ---- Linux-5.10.134-18.al8.x86_64-x86_64-with-glibc2.34 2025-07-18 12:18:12
- *** --- * --- 
- ** ---------- [config]
- ** ---------- .> app:         steambase:0x7efc220552e0
- ** ---------- .> transport:   redis://localhost:6379/0
- ** ---------- .> results:     redis://localhost:6379/0
- *** --- * --- .> concurrency: 2 (prefork)
-- ******* ---- .> task events: OFF (enable -E to monitor tasks in this worker)
--- ***** ----- 
 -------------- [queues]
                .> celery           exchange=celery(direct) key=celery
                

[tasks]
  . authentication.tasks.delete_old_balance_records
  . authentication.tasks.delete_old_log_entries
  . authentication.tasks.delete_old_user_active_point_records
  . authentication.tasks.update_staff_balance
  . box.tasks.delete_old_case_records
  . box.tasks.update_case_records_cache
  . charge.tasks.update_enable_based_on_coins
  . charge.tasks.update_enable_pay_method
  . charge.tasks.update_month_amount
  . charge.tasks.update_today_amount
  . package.tasks.delete_old_package_items
  . roll.tasks.cleanup_duplicate_roll_room_bets
  . sitecfg.tasks.update_daily_recharge_limit
  . sitecfg.tasks.update_domain_verify_switch
  . sitecfg.tasks.update_enable_rule_exam
  . sitecfg.tasks.update_new_user_recharge_limit
  . sitecfg.tasks.update_recharge_box_chance_type
  . sitecfg.tasks.update_register_box_chance_type
  . thworker.case_bot_worker
  . thworker.case_pk_join_worker
  . thworker.case_pk_worker
  . thworker.case_room_worker
  . thworker.check_b2c_trade_state
  . thworker.check_envelop_state
  . thworker.check_pendclose_trade
  . thworker.check_room_state
  . thworker.daily_lottery
  . thworker.hourly_lottery
  . thworker.init_system
  . thworker.roll_room_worker
  . thworker.sync_items_price
  . thworker.update_case_records_cache
  . thworker.update_monitor_data
  . thworker.weekly_lottery
  . thworker.zbt_buy_worker


worker: Warm shutdown (MainProcess)
 
 -------------- celery@iZj6c3ddfpik1c7yalqdt4Z v5.5.3 (immunity)
--- ***** ----- 
-- ******* ---- Linux-5.10.134-18.al8.x86_64-x86_64-with-glibc2.34 2025-07-18 12:23:17
- *** --- * --- 
- ** ---------- [config]
- ** ---------- .> app:         steambase:0x7fd14b534310
- ** ---------- .> transport:   redis://localhost:6379/0
- ** ---------- .> results:     redis://localhost:6379/0
- *** --- * --- .> concurrency: 2 (prefork)
-- ******* ---- .> task events: OFF (enable -E to monitor tasks in this worker)
--- ***** ----- 
 -------------- [queues]
                .> celery           exchange=celery(direct) key=celery
                

[tasks]
  . authentication.tasks.delete_old_balance_records
  . authentication.tasks.delete_old_log_entries
  . authentication.tasks.delete_old_user_active_point_records
  . authentication.tasks.update_staff_balance
  . box.tasks.delete_old_case_records
  . box.tasks.update_case_records_cache
  . charge.tasks.update_enable_based_on_coins
  . charge.tasks.update_enable_pay_method
  . charge.tasks.update_month_amount
  . charge.tasks.update_today_amount
  . package.tasks.delete_old_package_items
  . roll.tasks.cleanup_duplicate_roll_room_bets
  . sitecfg.tasks.update_daily_recharge_limit
  . sitecfg.tasks.update_domain_verify_switch
  . sitecfg.tasks.update_enable_rule_exam
  . sitecfg.tasks.update_new_user_recharge_limit
  . sitecfg.tasks.update_recharge_box_chance_type
  . sitecfg.tasks.update_register_box_chance_type
  . thworker.case_bot_worker
  . thworker.case_pk_join_worker
  . thworker.case_pk_worker
  . thworker.case_room_worker
  . thworker.check_b2c_trade_state
  . thworker.check_envelop_state
  . thworker.check_pendclose_trade
  . thworker.check_room_state
  . thworker.daily_lottery
  . thworker.hourly_lottery
  . thworker.init_system
  . thworker.roll_room_worker
  . thworker.sync_items_price
  . thworker.update_case_records_cache
  . thworker.update_monitor_data
  . thworker.weekly_lottery
  . thworker.zbt_buy_worker


worker: Warm shutdown (MainProcess)
 
 -------------- celery@iZj6c3ddfpik1c7yalqdt4Z v5.5.3 (immunity)
--- ***** ----- 
-- ******* ---- Linux-5.10.134-18.al8.x86_64-x86_64-with-glibc2.34 2025-07-18 12:29:00
- *** --- * --- 
- ** ---------- [config]
- ** ---------- .> app:         steambase:0x7fdb357e1310
- ** ---------- .> transport:   redis://localhost:6379/0
- ** ---------- .> results:     redis://localhost:6379/0
- *** --- * --- .> concurrency: 2 (prefork)
-- ******* ---- .> task events: OFF (enable -E to monitor tasks in this worker)
--- ***** ----- 
 -------------- [queues]
                .> celery           exchange=celery(direct) key=celery
                

[tasks]
  . authentication.tasks.delete_old_balance_records
  . authentication.tasks.delete_old_log_entries
  . authentication.tasks.delete_old_user_active_point_records
  . authentication.tasks.update_staff_balance
  . box.tasks.delete_old_case_records
  . box.tasks.update_case_records_cache
  . charge.tasks.update_enable_based_on_coins
  . charge.tasks.update_enable_pay_method
  . charge.tasks.update_month_amount
  . charge.tasks.update_today_amount
  . package.tasks.delete_old_package_items
  . roll.tasks.cleanup_duplicate_roll_room_bets
  . sitecfg.tasks.update_daily_recharge_limit
  . sitecfg.tasks.update_domain_verify_switch
  . sitecfg.tasks.update_enable_rule_exam
  . sitecfg.tasks.update_new_user_recharge_limit
  . sitecfg.tasks.update_recharge_box_chance_type
  . sitecfg.tasks.update_register_box_chance_type
  . thworker.case_bot_worker
  . thworker.case_pk_join_worker
  . thworker.case_pk_worker
  . thworker.case_room_worker
  . thworker.check_b2c_trade_state
  . thworker.check_envelop_state
  . thworker.check_pendclose_trade
  . thworker.check_room_state
  . thworker.daily_lottery
  . thworker.hourly_lottery
  . thworker.init_system
  . thworker.roll_room_worker
  . thworker.sync_items_price
  . thworker.update_case_records_cache
  . thworker.update_monitor_data
  . thworker.weekly_lottery
  . thworker.zbt_buy_worker


worker: Warm shutdown (MainProcess)
 
 -------------- celery@iZj6c3ddfpik1c7yalqdt4Z v5.5.3 (immunity)
--- ***** ----- 
-- ******* ---- Linux-5.10.134-18.al8.x86_64-x86_64-with-glibc2.34 2025-07-18 15:35:00
- *** --- * --- 
- ** ---------- [config]
- ** ---------- .> app:         steambase:0x7fe2f261c340
- ** ---------- .> transport:   redis://localhost:6379/0
- ** ---------- .> results:     redis://localhost:6379/0
- *** --- * --- .> concurrency: 2 (prefork)
-- ******* ---- .> task events: OFF (enable -E to monitor tasks in this worker)
--- ***** ----- 
 -------------- [queues]
                .> celery           exchange=celery(direct) key=celery
                

[tasks]
  . authentication.tasks.delete_old_balance_records
  . authentication.tasks.delete_old_log_entries
  . authentication.tasks.delete_old_user_active_point_records
  . authentication.tasks.update_staff_balance
  . box.tasks.delete_old_case_records
  . box.tasks.update_case_records_cache
  . charge.tasks.update_enable_based_on_coins
  . charge.tasks.update_enable_pay_method
  . charge.tasks.update_month_amount
  . charge.tasks.update_today_amount
  . package.tasks.delete_old_package_items
  . roll.tasks.cleanup_duplicate_roll_room_bets
  . sitecfg.tasks.update_daily_recharge_limit
  . sitecfg.tasks.update_domain_verify_switch
  . sitecfg.tasks.update_enable_rule_exam
  . sitecfg.tasks.update_new_user_recharge_limit
  . sitecfg.tasks.update_recharge_box_chance_type
  . sitecfg.tasks.update_register_box_chance_type
  . thworker.case_bot_worker
  . thworker.case_pk_join_worker
  . thworker.case_pk_worker
  . thworker.case_room_worker
  . thworker.check_b2c_trade_state
  . thworker.check_envelop_state
  . thworker.check_pendclose_trade
  . thworker.check_room_state
  . thworker.daily_lottery
  . thworker.hourly_lottery
  . thworker.init_system
  . thworker.roll_room_worker
  . thworker.sync_items_price
  . thworker.update_case_records_cache
  . thworker.update_monitor_data
  . thworker.weekly_lottery
  . thworker.zbt_buy_worker


worker: Warm shutdown (MainProcess)
 
 -------------- celery@iZj6c3ddfpik1c7yalqdt4Z v5.5.3 (immunity)
--- ***** ----- 
-- ******* ---- Linux-5.10.134-18.al8.x86_64-x86_64-with-glibc2.34 2025-07-18 15:44:23
- *** --- * --- 
- ** ---------- [config]
- ** ---------- .> app:         steambase:0x7fa5a5a68340
- ** ---------- .> transport:   redis://localhost:6379/0
- ** ---------- .> results:     redis://localhost:6379/0
- *** --- * --- .> concurrency: 2 (prefork)
-- ******* ---- .> task events: OFF (enable -E to monitor tasks in this worker)
--- ***** ----- 
 -------------- [queues]
                .> celery           exchange=celery(direct) key=celery
                

[tasks]
  . authentication.tasks.delete_old_balance_records
  . authentication.tasks.delete_old_log_entries
  . authentication.tasks.delete_old_user_active_point_records
  . authentication.tasks.update_staff_balance
  . box.tasks.delete_old_case_records
  . box.tasks.update_case_records_cache
  . charge.tasks.update_enable_based_on_coins
  . charge.tasks.update_enable_pay_method
  . charge.tasks.update_month_amount
  . charge.tasks.update_today_amount
  . package.tasks.delete_old_package_items
  . roll.tasks.cleanup_duplicate_roll_room_bets
  . sitecfg.tasks.update_daily_recharge_limit
  . sitecfg.tasks.update_domain_verify_switch
  . sitecfg.tasks.update_enable_rule_exam
  . sitecfg.tasks.update_new_user_recharge_limit
  . sitecfg.tasks.update_recharge_box_chance_type
  . sitecfg.tasks.update_register_box_chance_type
  . thworker.case_bot_worker
  . thworker.case_pk_join_worker
  . thworker.case_pk_worker
  . thworker.case_room_worker
  . thworker.check_b2c_trade_state
  . thworker.check_envelop_state
  . thworker.check_pendclose_trade
  . thworker.check_room_state
  . thworker.daily_lottery
  . thworker.hourly_lottery
  . thworker.init_system
  . thworker.roll_room_worker
  . thworker.sync_items_price
  . thworker.update_case_records_cache
  . thworker.update_monitor_data
  . thworker.weekly_lottery
  . thworker.zbt_buy_worker


worker: Warm shutdown (MainProcess)
 
 -------------- celery@iZj6c3ddfpik1c7yalqdt4Z v5.5.3 (immunity)
--- ***** ----- 
-- ******* ---- Linux-5.10.134-18.al8.x86_64-x86_64-with-glibc2.34 2025-07-18 15:53:12
- *** --- * --- 
- ** ---------- [config]
- ** ---------- .> app:         steambase:0x7ffa8e306340
- ** ---------- .> transport:   redis://localhost:6379/0
- ** ---------- .> results:     redis://localhost:6379/0
- *** --- * --- .> concurrency: 2 (prefork)
-- ******* ---- .> task events: OFF (enable -E to monitor tasks in this worker)
--- ***** ----- 
 -------------- [queues]
                .> celery           exchange=celery(direct) key=celery
                

[tasks]
  . authentication.tasks.delete_old_balance_records
  . authentication.tasks.delete_old_log_entries
  . authentication.tasks.delete_old_user_active_point_records
  . authentication.tasks.update_staff_balance
  . box.tasks.delete_old_case_records
  . box.tasks.update_case_records_cache
  . charge.tasks.update_enable_based_on_coins
  . charge.tasks.update_enable_pay_method
  . charge.tasks.update_month_amount
  . charge.tasks.update_today_amount
  . package.tasks.delete_old_package_items
  . roll.tasks.cleanup_duplicate_roll_room_bets
  . sitecfg.tasks.update_daily_recharge_limit
  . sitecfg.tasks.update_domain_verify_switch
  . sitecfg.tasks.update_enable_rule_exam
  . sitecfg.tasks.update_new_user_recharge_limit
  . sitecfg.tasks.update_recharge_box_chance_type
  . sitecfg.tasks.update_register_box_chance_type
  . thworker.case_bot_worker
  . thworker.case_pk_join_worker
  . thworker.case_pk_worker
  . thworker.case_room_worker
  . thworker.check_b2c_trade_state
  . thworker.check_envelop_state
  . thworker.check_pendclose_trade
  . thworker.check_room_state
  . thworker.daily_lottery
  . thworker.hourly_lottery
  . thworker.init_system
  . thworker.roll_room_worker
  . thworker.sync_items_price
  . thworker.update_case_records_cache
  . thworker.update_monitor_data
  . thworker.weekly_lottery
  . thworker.zbt_buy_worker

