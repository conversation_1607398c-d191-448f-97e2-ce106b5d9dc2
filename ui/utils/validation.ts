// 验证相关的工具函数

export interface ValidationResult {
  valid: boolean
  message?: string
}

export interface PasswordStrengthResult extends ValidationResult {
  strength: 'weak' | 'medium' | 'strong'
  score: number
}

// 邮箱验证
export const validateEmail = (email: string): ValidationResult => {
  if (!email) {
    return {
      valid: false,
      message: 'auth.email_required'
    }
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  
  if (!emailRegex.test(email)) {
    return {
      valid: false,
      message: 'auth.email_invalid'
    }
  }

  return { valid: true }
}

// 密码验证和强度检测
export const validatePassword = (password: string): PasswordStrengthResult => {
  if (!password) {
    return {
      valid: false,
      strength: 'weak',
      score: 0,
      message: 'auth.password_required'
    }
  }

  if (password.length < 8) {
    return {
      valid: false,
      strength: 'weak',
      score: 0,
      message: 'auth.password_too_short'
    }
  }

  let score = 0
  let strength: 'weak' | 'medium' | 'strong' = 'weak'

  // 长度评分
  if (password.length >= 8) score += 1
  if (password.length >= 12) score += 1

  // 字符类型评分
  if (/[a-z]/.test(password)) score += 1 // 小写字母
  if (/[A-Z]/.test(password)) score += 1 // 大写字母
  if (/[0-9]/.test(password)) score += 1 // 数字
  if (/[^A-Za-z0-9]/.test(password)) score += 1 // 特殊字符

  // 确定强度
  if (score >= 5) {
    strength = 'strong'
  } else if (score >= 3) {
    strength = 'medium'
  } else {
    strength = 'weak'
  }

  return {
    valid: true,
    strength,
    score,
    message: `auth.password_${strength}`
  }
}

// 确认密码验证
export const validateConfirmPassword = (password: string, confirmPassword: string): ValidationResult => {
  if (!confirmPassword) {
    return {
      valid: false,
      message: 'auth.confirm_password_required'
    }
  }

  if (password !== confirmPassword) {
    return {
      valid: false,
      message: 'auth.password_mismatch'
    }
  }

  return { valid: true }
}

// 验证码验证
export const validateVerificationCode = (code: string): ValidationResult => {
  if (!code) {
    return {
      valid: false,
      message: 'auth.code_required'
    }
  }

  // 通常验证码是4-6位数字或字母
  if (code.length < 4 || code.length > 6) {
    return {
      valid: false,
      message: 'auth.code_required'
    }
  }

  return { valid: true }
}

// 验证码（图片验证码）验证
export const validateCaptcha = (captcha: string): ValidationResult => {
  if (!captcha) {
    return {
      valid: false,
      message: 'auth.captcha_required'
    }
  }

  // 通常图片验证码是4位
  if (captcha.length !== 4) {
    return {
      valid: false,
      message: 'auth.captcha_required'
    }
  }

  return { valid: true }
}

// 昵称验证
export const validateNickname = (nickname: string): ValidationResult => {
  // 昵称是可选的
  if (!nickname) {
    return { valid: true }
  }

  // 昵称长度限制
  if (nickname.length < 2 || nickname.length > 20) {
    return {
      valid: false,
      message: '昵称长度应在2-20个字符之间'
    }
  }

  // 不允许特殊字符（保留字母、数字、中文、下划线）
  const nicknameRegex = /^[\w\u4e00-\u9fa5]+$/
  if (!nicknameRegex.test(nickname)) {
    return {
      valid: false,
      message: '昵称只能包含字母、数字、中文和下划线'
    }
  }

  return { valid: true }
}

// 表单整体验证
export const validateLoginForm = (email: string, password: string, captcha?: string) => {
  const emailResult = validateEmail(email)
  const passwordResult = validatePassword(password)
  const captchaResult = captcha ? validateCaptcha(captcha) : { valid: true }

  return {
    email: emailResult,
    password: passwordResult,
    captcha: captchaResult,
    isValid: emailResult.valid && passwordResult.valid && captchaResult.valid
  }
}

export const validateRegisterForm = (
  email: string, 
  password: string, 
  confirmPassword: string, 
  verifyCode: string, 
  nickname?: string
) => {
  const emailResult = validateEmail(email)
  const passwordResult = validatePassword(password)
  const confirmPasswordResult = validateConfirmPassword(password, confirmPassword)
  const codeResult = validateVerificationCode(verifyCode)
  const nicknameResult = validateNickname(nickname || '')

  return {
    email: emailResult,
    password: passwordResult,
    confirmPassword: confirmPasswordResult,
    code: codeResult,
    nickname: nicknameResult,
    isValid: emailResult.valid && 
             passwordResult.valid && 
             confirmPasswordResult.valid && 
             codeResult.valid && 
             nicknameResult.valid
  }
}

export const validateResetPasswordForm = (
  email: string, 
  code: string, 
  newPassword: string, 
  confirmPassword: string
) => {
  const emailResult = validateEmail(email)
  const codeResult = validateVerificationCode(code)
  const passwordResult = validatePassword(newPassword)
  const confirmPasswordResult = validateConfirmPassword(newPassword, confirmPassword)

  return {
    email: emailResult,
    code: codeResult,
    password: passwordResult,
    confirmPassword: confirmPasswordResult,
    isValid: emailResult.valid && 
             codeResult.valid && 
             passwordResult.valid && 
             confirmPasswordResult.valid
  }
} 