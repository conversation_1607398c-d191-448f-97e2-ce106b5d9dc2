# Battle API 饰品详细字段补充更新

## 更新时间
2025-06-28 下午

## 更新内容

### 主要变更
1. **补充房间详细信息接口（GET /api/box/battle/detail/）的饰品详细字段**
   - 在 `bets` → `open_items` 和 `win_items` 字段中补充了完整的饰品属性信息
   - 修正了 `rounds` 字段的数据结构，使其与后端实际实现一致

### 新增饰品字段

#### item_price（饰品价格信息）
```json
"item_price": {
    "price": 125.50,
    "update_time": "2023-12-01T10:30:00Z"
}
```

#### item_category（饰品类别信息）
```json
"item_category": {
    "cate_id": 1,
    "cate_name": "步枪",
    "cate_name_en": "Rifle",
    "cate_name_zh_hans": "步枪",
    "icon": "rifle_icon.png"
}
```

#### item_quality（饰品磨损度信息）
```json
"item_quality": {
    "quality_id": 4,
    "quality_name": "久经沙场",
    "quality_name_en": "Field-Tested", 
    "quality_name_zh_hans": "久经沙场",
    "quality_color": "#8847ff"
}
```

#### item_rarity（饰品稀有度信息）
```json
"item_rarity": {
    "rarity_id": 5,
    "rarity_name": "保密",
    "rarity_name_en": "Classified",
    "rarity_name_zh_hans": "保密",
    "rarity_color": "#d32ce6"
}
```

#### item_exterior（饰品外观信息）
```json
"item_exterior": {
    "exterior_id": 4,
    "exterior_name": "久经沙场",
    "exterior_name_en": "Field-Tested",
    "exterior_name_zh_hans": "久经沙场",
    "exterior_color": "#8847ff"
}
```

### 数据结构修正

#### 修正前的 rounds 字段（不准确）
```json
"rounds": [
    {
        "round_number": 1,
        "case_key": "ak47_redline_case",
        "case_name": "AK-47 红线箱子",
        "case_price": 10.50,
        "results": [
            {
                "user_nickname": "玩家1",
                "item_name": "AK-47 | 红线",
                "item_value": 125.50,
                "win": true
            }
        ]
    }
]
```

#### 修正后的 rounds 字段（与后端一致）
```json
"rounds": [
    {
        "case": {
            "case_key": "ak47_redline_case",
            "name": "AK-47 红线箱子",
            "name_en": "AK-47 Redline Case", 
            "name_zh_hans": "AK-47 红线箱子",
            "cover": "https://example.com/case_cover.jpg",
            "item": "https://example.com/case_items.jpg",
            "price": 10.50
        }
    }
]
```

### 重要说明

1. **饰品详细信息位置**：实际的开箱结果和饰品详细信息都在 `bets` 字段的 `open_items` 和 `win_items` 中，而不是在 `rounds` 字段中

2. **国际化支持**：所有新增的饰品属性字段都完整支持国际化，包含 `name`、`name_en`、`name_zh_hans` 等字段

3. **数据来源**：这些字段来自后端的 `CaseRoomItemSerializer`，确保文档与实际API响应一致

4. **失败者赠送饰品修复**：修复了失败者的赠送饰品同时出现在 `open_items` 和 `win_items` 中的问题
   - 赠送饰品现在只出现在失败者的 `win_items` 中，不再出现在 `open_items` 中
   - 此修复只对新创建的对战房间生效，不影响历史数据
   - 历史数据量大，暂不进行批量修复

## 影响范围

- **文件修改**：`/docs/api/battle-api.md`
- **接口影响**：主要影响房间详细信息接口的文档示例
- **向后兼容**：文档更新不影响实际API，只是让文档更准确

## 验证状态

- ✅ 文档语法检查通过
- ✅ 字段结构与后端序列化器一致
- ✅ 国际化字段完整覆盖
- ✅ 示例数据完整且合理

## 前端使用建议

1. 使用房间详细信息接口获取完整的饰品属性信息
2. 根据用户语言偏好选择合适的国际化字段
3. 利用颜色字段（如 `quality_color`、`rarity_color`）提升UI显示效果
4. 缓存饰品价格信息，注意价格更新时间
