// services/site-api.ts
import type { BannerItem } from '~/types/banner'

interface SiteApiResponse<T = any> {
  success: boolean
  data: T | null
  message: string
}

class SiteApi {
  /**
   * 获取Banner轮播数据
   */
  async getBanner(): Promise<SiteApiResponse<BannerItem[]>> {
    console.log('[Site API] 开始获取Banner数据')
    
    // 首先尝试使用代理
    try {
      const response = await $fetch<{
        code: number
        body: {
          items: BannerItem[]
        }
        message: string
      }>('/api/sitecfg/banner/', {
        timeout: 10000, // 10秒超时
        retry: 2, // 重试2次
        retryDelay: 1000 // 重试延迟1秒
      })

      console.log('[Site API] Banner API 响应 (代理):', { code: response.code, itemsLength: response.body?.items?.length })

      if (response.code === 0) {
        return {
          success: true,
          data: response.body?.items || [],
          message: '获取Banner成功'
        }
      } else {
        console.error('[Site API] 获取Banner失败 (代理):', response)
        return {
          success: false,
          data: null,
          message: response.message || '获取Banner失败'
        }
      }
    } catch (proxyError: any) {
      console.warn('[Site API] 代理请求失败，尝试直接请求:', proxyError.message)
      
      // 如果代理失败，尝试直接请求API
      try {
        const directResponse = await $fetch<{
          code: number
          body: {
            items: BannerItem[]
          }
          message: string
        }>('/api/sitecfg/banner/', {
          timeout: 10000,
          retry: 1,
          retryDelay: 1000
        })

        console.log('[Site API] Banner API 响应 (直接):', { code: directResponse.code, itemsLength: directResponse.body?.items?.length })

        if (directResponse.code === 0) {
          return {
            success: true,
            data: directResponse.body?.items || [],
            message: '获取Banner成功 (直接请求)'
          }
        } else {
          return {
            success: false,
            data: null,
            message: directResponse.message || '获取Banner失败'
          }
        }
      } catch (directError: any) {
        console.error('[Site API] 直接请求也失败:', directError)
        return {
          success: false,
          data: null,
          message: directError.message || '获取Banner请求出错'
        }
      }
    }
  }
}

// 导出单例实例
export const siteApi = new SiteApi()

// 保持向后兼容性
export const SiteApiService = SiteApi
export { SiteApi as default }
export type { BannerItem, SiteApiResponse } 