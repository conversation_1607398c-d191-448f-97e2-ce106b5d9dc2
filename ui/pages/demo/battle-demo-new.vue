<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-950 via-slate-900 to-slate-950 relative overflow-hidden">
    <!-- 背景效果 -->
    <div class="absolute inset-0">
      <div class="absolute top-20 left-20 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
      <div class="absolute top-40 right-32 w-80 h-80 bg-orange-500/10 rounded-full blur-3xl animate-pulse" style="animation-delay: 1s"></div>
      <div class="absolute bottom-40 left-1/3 w-72 h-72 bg-purple-500/10 rounded-full blur-3xl animate-pulse" style="animation-delay: 2s"></div>
    </div>

    <!-- 主内容 -->
    <div class="relative z-10 container mx-auto px-4 py-8">
      <!-- ⚔️ 对战头部信息 -->
      <BattleHeader
        class="mb-6"
        :battle-data="battleHeaderData"
        :is-user-joined="true"
        :is-user-creator="true"
        @copy-id="() => navigator.clipboard.writeText(roomId)"
      />
      <!-- 音效控制面板 -->
        <UiAudioController
          v-model:muted="isMuted"
          v-model:volume-value="volume"
          :show-test-buttons="false"
          :theme="'minimal'"
        class="absolute top-4 right-4 z-20"
          @volume-change="setVolume"
          @mute-toggle="toggleMute"
        />
      <!-- 顶部状态栏 -->
      <div class="mb-8">
        <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
          <div class="flex items-center gap-4">
            <div class="bg-gradient-to-r from-blue-600 to-purple-600 px-4 py-2 rounded-lg">
              <span class="text-sm text-blue-100">房间ID</span>
              <span class="ml-2 text-lg font-bold text-white">{{ roomId }}</span>
            </div>
          </div>
          <div class="flex gap-4">
            <div class="text-center">
              <div class="text-2xl font-bold text-cyan-400">{{ currentRound }}</div>
              <div class="text-xs text-gray-400">当前轮次</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-green-400">${{ totalValue.toFixed(2) }}</div>
              <div class="text-xs text-gray-400">总价值</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 🎯 对战状态显示组件 -->
      <BattleStateDisplay
        :battle-state="gameState"
        :current-round="currentRound"
        :total-rounds="totalRounds"
        :opening-case-id="currentOpeningCaseId"
        :is-battle-started="gameState !== 'waiting'"
        :is-battle-finished="gameState === 'finished'"
      />

      <!-- 🎯 箱子展示组件 -->
      <BattleCaseDisplay
        v-if="demoCase.length > 0"
        :cases="demoCase"
        :current-round="currentRound"
        :total-rounds="totalRounds"
        :opening-case-id="currentOpeningCaseId"
        :is-battle-started="gameState !== 'waiting'"
        :is-battle-finished="gameState === 'finished'"
      />

      <!-- 配置面板 -->
      <div class="mb-8" v-if="gameState === 'waiting'">
        <div class="bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-xl p-6">
          <h3 class="text-xl font-bold text-white mb-4">对战配置</h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label class="block text-sm font-medium text-gray-300 mb-2">玩家数量</label>
              <select v-model="playerCount" class="w-full bg-gray-800 border border-gray-600 rounded-lg px-3 py-2 text-white">
                <option value="2">2人对战</option>
                <option value="3">3人混战</option>
                <option value="4">4人大乱斗</option>
              </select>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-300 mb-2">对战轮次</label>
              <select v-model="totalRounds" class="w-full bg-gray-800 border border-gray-600 rounded-lg px-3 py-2 text-white">
                <option value="1">单轮决胜</option>
                <option value="2">两轮制</option>
                <option value="3">三轮制</option>
                <option value="4">四轮制</option>
              </select>
            </div>
            <div class="flex items-end">
              <button @click="startBattle" class="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-500 hover:to-blue-500 text-white font-bold py-2 px-4 rounded-lg transition-all duration-300">
                开始对战
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 🏟️ 玩家竞技场 -->
      <div class="mb-8">
        <div class="grid gap-6" :class="playerGridClass">
          <div
            v-for="(player, index) in players"
            :key="player.id"
            class="player-arena-card bg-gradient-to-br from-gray-900/80 to-gray-800/60 backdrop-blur-sm border rounded-xl p-6 transition-all duration-300"
            :class="{
              'border-cyan-500 shadow-cyan-500/20 shadow-2xl': isPlayerAnimating(index),
              'border-gray-700 hover:border-gray-600': !isPlayerAnimating(index),
              'ring-2 ring-yellow-500 ring-opacity-50': player.isWinner,
            }"
          >
            <!-- 玩家头部信息 -->
            <div class="flex items-center gap-4 mb-4">
              <div class="relative">
                <div
                  class="w-14 h-14 rounded-full bg-gradient-to-r flex items-center justify-center text-white font-bold text-lg border-2"
                  :class="[
                    getPlayerColorClasses(index),
                    isPlayerAnimating(index) ? 'animate-pulse' : '',
                  ]"
                >
                  {{ player.name.charAt(0) }}
                </div>
                <!-- 动画状态指示器 -->
                <div
                  v-if="isPlayerAnimating(index)"
                  class="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full animate-ping"
                ></div>
                <!-- 获胜者王冠 -->
                <div
                  v-if="player.isWinner"
                  class="absolute -top-2 -right-2 text-yellow-500 text-xl animate-bounce"
                >
                  🏆
          </div>
        </div>
              <div class="flex-1">
                <div class="flex items-center gap-2">
                  <span class="text-white font-bold text-lg">{{ player.name }}</span>
                  <span v-if="player.isBot" class="text-xs bg-gray-700 text-gray-300 px-2 py-1 rounded">BOT</span>
                </div>
                <div class="text-gray-400 text-sm">等级 {{ player.level }} • {{ player.wins }} 胜</div>
                <div class="flex items-center gap-2 mt-1">
                  <span class="text-green-400 font-bold">${{ player.totalValue.toFixed(2) }}</span>
                  <span class="text-gray-500 text-xs">总价值</span>
                </div>
              </div>
              <!-- 开箱动画区域 -->
              <div v-if="isPlayerAnimating(index)" class="text-right">
                <div class="text-cyan-400 text-sm animate-pulse">🎲 开箱中...</div>
                <div class="text-xs text-gray-400 mt-1">{{ getAnimationProgress(index) }}%</div>
              </div>
      </div>

            <!-- 🎰 滚动轨道区域 -->
            <div class="slot-machine-container mb-4 bg-gray-800/50 rounded-lg p-4 min-h-[120px]">
              <div class="text-sm text-gray-400 mb-2 flex items-center gap-2">
                <span>🎰</span>
                <span>开箱轨道</span>
                <span v-if="isPlayerAnimating(index)" class="text-cyan-400 animate-pulse">● 滚动中</span>
        </div>
        
              <!-- 滚动物品轨道 -->
              <div
                class="slot-track relative overflow-hidden h-16 bg-gray-900/50 rounded border-2"
                :class="isPlayerAnimating(index) ? 'border-cyan-400' : 'border-gray-600'"
              >
                <!-- 中奖指示线 -->
                <div
                  class="absolute top-0 bottom-0 left-1/2 w-0.5 bg-red-500 z-10 transform -translate-x-px"
                  :class="isPlayerAnimating(index) ? 'shadow-red-500 shadow-lg' : ''"
                ></div>

                <!-- 滚动物品序列 -->
                <div class="slot-items flex h-full" :class="isPlayerAnimating(index) ? 'animate-slot-spin' : ''" :style="getSlotTransform(index)">
                  <div
                    v-for="item in getSlotItems(index)"
                    :key="item.id"
                    class="slot-item flex-shrink-0 w-14 h-14 m-1 bg-gray-700 rounded border-2 flex items-center justify-center text-xs text-white"
                    :style="{ borderColor: item.color || '#6b7280' }"
                  >
                    {{ item.name ? item.name.substring(0, 2) : '?' }}
                  </div>
                </div>
            </div>
          </div>

            <!-- 📊 轮次记录 -->
            <div class="rounds-history space-y-2 max-h-32 overflow-y-auto">
              <div
                v-for="(round, roundIndex) in player.rounds"
                :key="roundIndex"
                class="round-record bg-gray-800/70 rounded-lg p-3 border-l-4"
                :class="{
                  'border-cyan-400 bg-cyan-900/20': roundIndex === currentRound - 1 && gameState === 'battle',
                  'border-gray-600': roundIndex !== currentRound - 1 || gameState !== 'battle',
                }"
              >
                <div class="flex items-center justify-between mb-2">
                  <span class="text-sm font-medium text-gray-300">第{{ roundIndex + 1 }}轮</span>
                  <span class="text-xs text-gray-500">${{ round.reduce((sum, item) => sum + item.value, 0).toFixed(2) }}</span>
            </div>
                <div class="grid grid-cols-1 gap-1">
                  <div
                    v-for="item in round"
                    :key="item.id"
                    class="flex justify-between items-center text-sm bg-gray-900/50 rounded px-2 py-1"
                  >
                    <div class="flex items-center gap-2">
                      <div class="w-2 h-2 rounded-full" :style="{ backgroundColor: item.color || '#6b7280' }"></div>
                      <span class="text-white text-xs truncate">{{ item.name }}</span>
                    </div>
                    <span class="text-green-400 font-medium text-xs">${{ item.value.toFixed(2) }}</span>
                  </div>
          </div>
        </div>

              <!-- 空状态提示 -->
              <div v-if="player.rounds.length === 0" class="text-center py-4 text-gray-500 text-sm">等待开箱结果...</div>
            </div>
          </div>
          </div>
        </div>

      <!-- 🧮 计算阶段显示 -->
      <div class="mb-8" v-if="gameState === 'calculating'">
        <div class="bg-gradient-to-br from-blue-900/50 to-purple-900/50 backdrop-blur-sm border border-blue-500 rounded-xl p-8">
          <div class="text-center mb-6">
            <div class="text-3xl font-bold text-white mb-2 animate-pulse">
              🧮 战果计算中...
        </div>
            <div class="text-gray-300">正在分析所有玩家的开箱结果</div>
      </div>

          <!-- 计算进度条 -->
          <div class="progress-container mb-6">
            <div class="bg-gray-700 rounded-full h-6 overflow-hidden">
              <div class="progress-bar bg-gradient-to-r from-blue-500 via-cyan-500 to-green-500 h-full rounded-full transition-all duration-500 relative"
                   :style="{ width: calculationProgress + '%' }">
                <div class="absolute inset-0 bg-white/20 animate-shimmer"></div>
              </div>
            </div>
            <div class="text-center mt-2 text-blue-300 font-medium">
              {{ calculationProgress }}% 完成
            </div>
      </div>

          <!-- 计算步骤 -->
          <div class="calculation-steps grid grid-cols-1 md:grid-cols-3 gap-4">
            <div v-for="(step, index) in currentCalculationSteps" :key="index" 
                 class="step-item flex items-center gap-3 p-3 rounded-lg transition-all duration-300"
                 :class="step.active ? 'bg-green-900/30 text-green-400 border border-green-500' : 'bg-gray-800/30 text-gray-500'">
              <div class="step-icon">
                <div v-if="step.active" class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center animate-pulse">
                  <span class="text-white text-sm">✓</span>
                </div>
                <div v-else class="w-6 h-6 border-2 border-gray-500 rounded-full"></div>
              </div>
              <span class="text-sm font-medium">{{ step.text }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 🏆 胜利结果展示 -->
      <div class="mb-8" v-if="gameState === 'finished'">
        <div class="bg-gradient-to-br from-yellow-900/50 to-orange-900/50 backdrop-blur-sm border border-yellow-500 rounded-xl p-8">
          <div class="text-center mb-6">
            <div class="text-4xl font-bold text-yellow-400 mb-2 animate-bounce">
              🏆 对战结束！
            </div>
            <div class="text-xl text-white mb-4">
              恭喜 <span class="text-yellow-400 font-bold">{{ winner?.name }}</span> 获得胜利！
            </div>
          </div>
          
          <!-- 排行榜 -->
          <div class="leaderboard max-w-2xl mx-auto">
            <div v-for="(player, index) in sortedPlayers" :key="player.id" 
                 class="leaderboard-item flex items-center gap-4 p-4 mb-3 rounded-lg border transition-all duration-300"
                 :class="{
                   'bg-yellow-900/30 border-yellow-500 ring-2 ring-yellow-500': index === 0,
                   'bg-gray-800/50 border-gray-600': index !== 0
                 }">
              <div class="rank-badge w-12 h-12 rounded-full flex items-center justify-center font-bold text-lg"
                   :class="{
                     'bg-yellow-500 text-yellow-900': index === 0,
                     'bg-gray-600 text-white': index !== 0
                   }">
                {{ index + 1 }}
              </div>
              <div class="player-info flex-1">
                <div class="flex items-center gap-2">
                  <span class="text-white font-bold">{{ player.name }}</span>
                  <span v-if="index === 0" class="text-xl animate-bounce">👑</span>
                </div>
                <div class="text-sm text-gray-400">{{ player.rounds.length }} 轮 • 等级 {{ player.level }}</div>
              </div>
              <div class="player-value text-right">
                <div class="text-2xl font-bold" :class="index === 0 ? 'text-yellow-400' : 'text-green-400'">
                  ${{ player.totalValue.toFixed(2) }}
                </div>
                <div class="text-xs text-gray-500">
                  {{ player.rounds.reduce((sum, round) => sum + round.length, 0) }} 件物品
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 控制按钮 -->
      <div class="flex justify-center gap-4">
        <button @click="resetGame" class="bg-gray-600 hover:bg-gray-500 text-white font-bold py-2 px-4 rounded-lg transition-all duration-300">
          重置游戏
        </button>
        <button v-if="gameState === 'finished'" @click="newGame" class="bg-gradient-to-r from-green-600 to-blue-600 text-white font-bold py-2 px-4 rounded-lg">
          新游戏
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, reactive, onMounted, watch } from 'vue'

// SEO设置
useSeoMeta({
  title: "CSGO开箱对战演示",
  description: "CSGO风格的多人开箱对战游戏演示"
});

// 响应式数据
const gameState = ref('waiting'); // waiting, battle, calculating, finished
const roomId = ref('DEMO-' + Math.random().toString(36).substr(2, 6).toUpperCase());
const playerCount = ref(3);
const totalRounds = ref(3);
const currentRound = ref(1);
const totalValue = ref(0);
const players = ref([]);

// 🎯 箱子数据 - 为组件提供的演示数据
const demoCase = ref([
  {
    id: 'demo_case_1',
    name: 'Gamma Case',
    name_zh_hans: 'Gamma武器箱',
    name_en: 'Gamma Case',
    cover: '/demo/case1.png',
    price: 2.50,
    count: 1
  },
  {
    id: 'demo_case_2', 
    name: 'Spectrum Case',
    name_zh_hans: 'Spectrum武器箱',
    name_en: 'Spectrum Case',
    cover: '/demo/case2.png',
    price: 1.80,
    count: 1
  },
  {
    id: 'demo_case_3',
    name: 'Danger Zone Case',
    name_zh_hans: '大逃杀武器箱',
    name_en: 'Danger Zone Case', 
    cover: '/demo/case3.png',
    price: 3.20,
    count: 1
  }
]);

// 🎯 当前开箱的箱子ID
const currentOpeningCaseId = ref('');

// 🎰 动画相关状态
const animatingPlayers = ref(new Set());
const animationProgress = ref(new Map());
const slotItems = ref(new Map());

// 🎮 游戏配置
const gameConfig = reactive({
  animationDuration: 4000, // 动画持续时间
  pauseBetweenRounds: 2000, // 轮次间暂停
  slotItemCount: 20, // 滚动轨道物品数量
});

// 🧮 计算相关状态
const calculationProgress = ref(0);
const calculationSteps = ref([
  { text: '分析开箱数据', active: false },
  { text: '统计物品价值', active: false },
  { text: '计算总分排名', active: false },
  { text: '确定获胜者', active: false },
  { text: '生成奖励分配', active: false },
]);

// 🏆 获胜者和排行榜
const winner = ref(null);

// 🎵 音效系统
import { useBattleAudioGlobal } from '~/composables/useBattleAudio'

const {
  isMuted,
  volume,
  setVolume,
  toggleMute,
  playBattleSequence,
  playSound,
  initAudio,
} = useBattleAudioGlobal()

// 计算属性
const playerGridClass = computed(() => {
  const classes = {
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
  };
  return classes[playerCount.value];
});

// 🧮 当前计算步骤
const currentCalculationSteps = computed(() => {
  return calculationSteps.value.map((step, index) => ({
    ...step,
    active: calculationProgress.value >= (index + 1) * 20
    }));
});

// 🏆 排序后的玩家列表
const sortedPlayers = computed(() => {
  return [...players.value].sort((a, b) => b.totalValue - a.totalValue);
});

// 🎨 获取玩家颜色类
const getPlayerColorClasses = (index) => {
  const colorClasses = [
    'from-blue-500 to-blue-600 border-blue-400',
    'from-red-500 to-red-600 border-red-400',
    'from-green-500 to-green-600 border-green-400',
    'from-orange-500 to-orange-600 border-orange-400',
  ];
  return colorClasses[index] || 'from-gray-500 to-gray-600 border-gray-400';
};

// 🎰 检查玩家是否在动画中
const isPlayerAnimating = (index) => {
  return animatingPlayers.value.has(index);
};

// 📊 获取动画进度
const getAnimationProgress = (index) => {
  return Math.round(animationProgress.value.get(index) || 0);
};

// 🎲 获取滚动轨道物品
const getSlotItems = (index) => {
  return slotItems.value.get(index) || [];
};

// 🎨 获取滚动变换样式
const getSlotTransform = (index) => {
  if (!isPlayerAnimating(index)) return '';
  const progress = animationProgress.value.get(index) || 0;
  const offset = -progress * 4; // 滚动速度
  return `transform: translateX(${offset}px)`;
};

// 🎰 生成滚动轨道物品
const generateSlotItems = (winnerItem) => {
  const items = [];
  const winnerIndex = Math.floor(gameConfig.slotItemCount / 2); // 中间位置
  
  for (let i = 0; i < gameConfig.slotItemCount; i++) {
    if (i === winnerIndex) {
      items.push(winnerItem);
  } else {
      items.push(generateItem());
    }
  }
  return items;
};

const startBattle = async () => {
  gameState.value = 'battle';
  initPlayers();
  
  // 播放对战开始音效
  playBattleSequence.battleStart()

  // 🎮 执行多轮对战
  for (let round = 1; round <= totalRounds.value; round++) {
    currentRound.value = round;
    
    // 🎯 设置当前开箱的箱子ID（轮次循环）
    const caseIndex = (round - 1) % demoCase.value.length;
    currentOpeningCaseId.value = demoCase.value[caseIndex].id;
    
    console.log(`[🎮] 开始第 ${round} 轮对战，开箱: ${currentOpeningCaseId.value}`);
    
    // 🎰 所有玩家同时开箱动画
    await performRoundBattle(round);
    
    // ⏱️ 轮次间停顿
    if (round < totalRounds.value) {
      currentOpeningCaseId.value = ''; // 清空开箱ID
      await new Promise(resolve => setTimeout(resolve, gameConfig.pauseBetweenRounds));
    }
  }
  
  // 🧮 计算最终结果
  currentOpeningCaseId.value = ''; // 清空开箱ID
  await calculateFinalResults();
};

// 🎯 执行单轮对战
const performRoundBattle = async (roundNumber) => {
  const roundPromises = players.value.map(async (player, playerIndex) => {
    // 🎲 生成获胜物品
    const winnerItem = generateItem();
    
    // 🎰 生成滚动轨道物品
    const trackItems = generateSlotItems(winnerItem);
    slotItems.value.set(playerIndex, trackItems);
    
    // 🚀 开始动画
    animatingPlayers.value.add(playerIndex);
    animationProgress.value.set(playerIndex, 0);
    
    // 🎬 执行滚动动画
    await animatePlayerSlot(playerIndex, winnerItem);
    
    // 📝 记录结果
    if (!player.rounds[roundNumber - 1]) {
      player.rounds[roundNumber - 1] = [];
    }
    player.rounds[roundNumber - 1].push(winnerItem);
    player.totalValue += winnerItem.value;
    totalValue.value += winnerItem.value;
    
    // 🏁 结束动画
    animatingPlayers.value.delete(playerIndex);
  });
  
  await Promise.all(roundPromises);
};

// 🎰 执行玩家老虎机动画
const animatePlayerSlot = async (playerIndex, winnerItem) => {
  return new Promise((resolve) => {
    const startTime = Date.now();
    const duration = gameConfig.animationDuration;
    
    // 播放开箱开始音效（仅第一个玩家触发一次）
    if (playerIndex === 0) {
      playBattleSequence.rollStart()
    }
    
    // 📈 创建缓动函数 (慢启动 -> 快速 -> 慢结束)
    const easeInOutCubic = (t) => {
      return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
    };
    
    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);
      const easedProgress = easeInOutCubic(progress);
      
      // 📊 更新进度
      animationProgress.value.set(playerIndex, easedProgress * 100);
      
      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        // 🎉 动画完成
        if (playerIndex === 0) {
          playBattleSequence.rollEnd()
        }
        setTimeout(resolve, 500); // 短暂停顿展示结果
      }
    };
    
    requestAnimationFrame(animate);
  });
};

// 🧮 计算最终结果
const calculateFinalResults = async () => {
  gameState.value = 'calculating';
  calculationProgress.value = 0;
  
  // 🎬 模拟计算过程动画
  for (let i = 0; i < 5; i++) {
    // 更新进度
    const targetProgress = (i + 1) * 20;
    
    // 渐进式进度动画
    while (calculationProgress.value < targetProgress) {
      calculationProgress.value = Math.min(calculationProgress.value + 2, targetProgress);
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    console.log(`[🧮] ${calculationSteps.value[i].text}...`);
    await new Promise(resolve => setTimeout(resolve, 600));
  }
  
  // 🏆 确定获胜者
  const sortedPlayersList = [...players.value].sort((a, b) => b.totalValue - a.totalValue);
  sortedPlayersList[0].isWinner = true;
  winner.value = sortedPlayersList[0];
  
  gameState.value = 'finished';
  
  console.log(`[🏆] 对战结束！获胜者: ${winner.value.name} ($${winner.value.totalValue.toFixed(2)})`);
};

const resetGame = () => {
  gameState.value = 'waiting';
  currentRound.value = 1;
  totalValue.value = 0;
  players.value = [];
  currentOpeningCaseId.value = '';
  
  // 清理动画状态
  animatingPlayers.value.clear();
  animationProgress.value.clear();
  slotItems.value.clear();
  
  // 重置计算状态
  calculationProgress.value = 0;
  calculationSteps.value.forEach(step => step.active = false);
  winner.value = null;
};

const newGame = () => {
  resetGame();
  roomId.value = 'DEMO-' + Math.random().toString(36).substr(2, 6).toUpperCase();
};

// 初始化
onMounted(() => {
  resetGame();
});

// 📝 BattleHeader 数据
const battleHeaderData = computed(() => {
  // 根据 gameState 转换为数值状态
  const stateMap = {
    waiting: 2,
    battle: 5,
    calculating: 5,
    finished: 11
  }
  return {
    short_id: roomId.value,
    state: stateMap[gameState.value],
    user: {
      uid: 'user_1',
      nickname: players.value[0]?.name || 'Player',
      avatar: '/demo/avatar1.png'
    },
    create_time: new Date().toISOString(),
    joiner_count: players.value.length,
    max_joiner: playerCount.value,
    bets: players.value.map((p) => ({
      uid: p.id,
      user: {
        uid: p.id,
        profile: {
          nickname: p.name,
          avatar: '/demo/avatar1.png'
        }
      }
    }))
  }
})

// 在关键流程播放音效 (计算/结束)
watch(gameState, (newState) => {
  if (newState === 'calculating') {
    playBattleSequence.calculationStart()
  } else if (newState === 'finished') {
    playBattleSequence.battleComplete()
  }
})

// 在每轮开始播放音效
watch(currentRound, (val, oldVal) => {
  if (gameState.value === 'battle' && val !== oldVal) {
    playBattleSequence.roundStart()
  }
})

// 初始化音频并播放点击声加载
initAudio().then(() => playSound('buttonClick'))
</script>

<style scoped>
/* 🎨 CSGO风格样式系统 */

/* 🎰 老虎机动画 */
@keyframes slot-spin {
  0% { transform: translateX(0); }
  100% { transform: translateX(-200px); }
}

.animate-slot-spin {
  animation: slot-spin 0.1s linear infinite;
}

/* 🌟 光效动画 */
@keyframes shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: 200px 0; }
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  background-size: 200px 100%;
  animation: shimmer 2s infinite;
}

/* 🎮 玩家卡片增强 */
.player-arena-card {
  transform: translateZ(0); /* 启用硬件加速 */
  will-change: transform, box-shadow;
}

.player-arena-card:hover {
  transform: translateY(-2px);
}

/* 🎰 滚动轨道样式 */
.slot-track {
  position: relative;
  overflow: hidden;
}

.slot-items {
  will-change: transform;
}

.slot-item {
  position: relative;
  transition: border-color 0.3s ease;
  font-size: 10px;
  font-weight: bold;
}

.slot-item:hover {
  transform: scale(1.05);
}

/* 📊 轮次记录滚动条 */
.rounds-history::-webkit-scrollbar {
  width: 4px;
}

.rounds-history::-webkit-scrollbar-track {
  background: rgba(31, 41, 55, 0.3);
  border-radius: 2px;
}

.rounds-history::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #3b82f6, #1d4ed8);
  border-radius: 2px;
}

.rounds-history::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #2563eb, #1e40af);
}

/* 🏆 排行榜动画 */
.leaderboard-item {
  transform: translateZ(0);
  will-change: transform;
}

.leaderboard-item:hover {
  transform: translateX(5px);
}

/* 📊 进度条增强 */
.progress-bar {
  position: relative;
  background-size: 200% 100%;
  animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* 🎯 状态指示器增强 */
.status-dot {
  box-shadow: 0 0 20px currentColor;
}

/* 🔥 光球背景增强 */
.light-orb {
  filter: blur(60px);
  animation: float-glow 8s ease-in-out infinite;
}

@keyframes float-glow {
  0%, 100% { 
    transform: translateY(0px) scale(1);
    opacity: 0.6;
  }
  25% { 
    transform: translateY(-30px) scale(1.1);
    opacity: 0.8;
  }
  50% { 
    transform: translateY(0px) scale(1.2);
    opacity: 1;
  }
  75% { 
    transform: translateY(-15px) scale(1.1);
    opacity: 0.8;
  }
}

/* 🎮 响应式优化 */
@media (max-width: 768px) {
  .player-arena-card {
    padding: 1rem;
  }
  
  .slot-machine-container {
    min-height: 100px;
  }
  
  .slot-track {
    height: 3rem;
  }
  
  .slot-item {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 8px;
  }
  
  .rounds-history {
    max-height: 6rem;
  }
}

/* 🎨 自定义滚动条全局 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(17, 24, 39, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #3b82f6, #1d4ed8);
  border-radius: 4px;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #2563eb, #1e40af);
  border-color: rgba(37, 99, 235, 0.5);
}

::-webkit-scrollbar-corner {
  background: rgba(17, 24, 39, 0.3);
}

/* 🌟 特殊效果 */
.animate-bounce-slow {
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    transform: translate3d(0,-30px,0);
  }
  70% {
    transform: translate3d(0,-15px,0);
  }
  90% {
    transform: translate3d(0,-4px,0);
  }
}

/* 🎯 聚焦状态增强 */
button:focus,
select:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* 💫 加载动画 */
.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.8);
  }
}
</style>