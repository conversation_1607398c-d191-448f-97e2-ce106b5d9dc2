#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Django PO文件修复脚本
修复重复条目、语法错误等问题
"""

import re
import sys
from collections import defaultdict

def fix_po_file(file_path):
    """修复PO文件中的错误"""
    print(f"🔧 修复PO文件: {file_path}")
    
    # 读取文件
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复常见的语法错误
    print("📝 修复语法错误...")
    
    # 1. 修复多余的引号
    content = re.sub(r'msgid "([^"]*)""\s*$', r'msgid "\1"', content, flags=re.MULTILINE)
    content = re.sub(r'msgstr "([^"]*)""\s*$', r'msgstr "\1"', content, flags=re.MULTILINE)
    
    # 2. 修复字符串内的换行符
    content = re.sub(r'msgid "([^"]*)\n([^"]*)"', r'msgid "\1\\n\2"', content)
    content = re.sub(r'msgstr "([^"]*)\n([^"]*)"', r'msgstr "\1\\n\2"', content)
    
    # 3. 分析重复条目
    print("🔍 检查重复条目...")
    
    # 解析PO文件条目
    entries = {}
    duplicates = []
    
    # 匹配msgid和msgstr对
    pattern = r'#:([^\n]*)\nmsgid "([^"]*)"\nmsgstr "([^"]*)"'
    matches = re.findall(pattern, content, re.MULTILINE)
    
    for comment, msgid, msgstr in matches:
        if msgid in entries:
            # 发现重复条目
            duplicates.append({
                'msgid': msgid,
                'existing': entries[msgid],
                'duplicate': {'comment': comment.strip(), 'msgstr': msgstr}
            })
            print(f"⚠️  重复条目: {msgid[:50]}...")
        else:
            entries[msgid] = {'comment': comment.strip(), 'msgstr': msgstr}
    
    # 移除重复条目，保留第一个
    if duplicates:
        print(f"🗑️  移除 {len(duplicates)} 个重复条目...")
        
        for dup in duplicates:
            # 构建要移除的重复条目模式
            dup_pattern = rf'#:\s*{re.escape(dup["duplicate"]["comment"])}\s*\nmsgid "{re.escape(dup["msgid"])}"\nmsgstr "{re.escape(dup["duplicate"]["msgstr"])}"'
            content = re.sub(dup_pattern, '', content, flags=re.MULTILINE)
    
    # 4. 清理多余的空行
    content = re.sub(r'\n\n\n+', '\n\n', content)
    
    # 5. 确保文件以换行符结尾
    if not content.endswith('\n'):
        content += '\n'
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ PO文件修复完成!")
    return True

def validate_po_file(file_path):
    """验证PO文件语法"""
    print(f"🔍 验证PO文件: {file_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        errors = []
        in_msgid = False
        in_msgstr = False
        line_num = 0
        
        for i, line in enumerate(lines, 1):
            line = line.strip()
            
            # 检查未闭合的引号
            if line.startswith('msgid '):
                if line.count('"') % 2 != 0:
                    errors.append(f"第{i}行: msgid引号未闭合")
                in_msgid = True
                in_msgstr = False
                
            elif line.startswith('msgstr '):
                if line.count('"') % 2 != 0:
                    errors.append(f"第{i}行: msgstr引号未闭合")
                in_msgid = False
                in_msgstr = True
                
            elif line.startswith('"') and line.endswith('"'):
                # 多行字符串
                if not (in_msgid or in_msgstr):
                    errors.append(f"第{i}行: 孤立的字符串")
        
        if errors:
            print("❌ 发现错误:")
            for error in errors[:10]:  # 只显示前10个错误
                print(f"  {error}")
            if len(errors) > 10:
                print(f"  ... 还有 {len(errors) - 10} 个错误")
            return False
        else:
            print("✅ PO文件语法正确")
            return True
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    po_file = "locale/zh_hans/LC_MESSAGES/django.po"
    
    print("🚀 Django PO文件修复工具")
    print("=" * 40)
    
    # 备份原文件
    import shutil
    backup_file = po_file + ".backup"
    shutil.copy2(po_file, backup_file)
    print(f"📋 已备份原文件到: {backup_file}")
    
    # 修复文件
    if fix_po_file(po_file):
        # 验证修复结果
        if validate_po_file(po_file):
            print("\n🎉 修复成功! 可以尝试重新编译消息:")
            print("   python manage.py compilemessages")
        else:
            print("\n⚠️  修复后仍有问题，可能需要手动检查")
    else:
        print("\n❌ 修复失败")

if __name__ == "__main__":
    main()
