# API文档更正建议

## 🎯 更正说明

**重要**: 前端在本地开发，后端在服务器部署，因此开发环境和生产环境都使用相同的服务器域名。

## 📝 需要更正的文档内容

### 1. websocket-api.md 更正

#### 更正基础信息部分 (第19-26行)

**当前内容**:
```markdown
## 基础信息

- **连接方式**: WebSocket（Django Channels）
- **WebSocket URL**: `ws://domain/ws/battle/{room_short_id}/`
- **认证方式**: Session Cookie 或 Token
- **消息格式**: JSON 数组 `[messageType, action, messageData, socketId?]`
- **事件监听**: 通过 `socket.on('message', ...)` 接收消息
```

**更正为**:
```markdown
## 基础信息

- **连接方式**: Socket.IO (Node.js服务器，端口4000)
- **WebSocket URL**: `https://socket.cs2.net.cn/socket.io/`
- **认证方式**: 当前跳过认证（测试配置）
- **消息格式**: JSON 数组 `[messageType, action, messageData, socketId?]`
- **事件监听**: 通过 `socket.on('message', ...)` 接收消息
- **部署架构**: 前端本地开发 → 后端服务器部署
```

#### 更正连接管理部分 (第27-35行)

**当前内容**:
```markdown
### Socket.IO连接流程

1. 使用Socket.IO库连接到 `http://domain/socket.io/`
2. 监听 `message` 事件接收实时消息
3. 接收实时消息（数组格式）
4. 根据消息类型和action处理相应业务逻辑
```

**更正为**:
```markdown
### Socket.IO连接流程

1. 使用Socket.IO库连接到 `https://socket.cs2.net.cn/socket.io/`
2. 监听 `message` 事件接收实时消息
3. 接收实时消息（数组格式）
4. 根据消息类型和action处理相应业务逻辑

### 前端连接示例

```javascript
// 统一使用服务器域名（前端本地开发也连接服务器）
const socket = io('https://socket.cs2.net.cn');

socket.on('message', (data) => {
    const [messageType, action, payload, socketId] = data;
    // 处理消息
});
```
```

### 2. battle-api.md 更正

#### 更正Socket.IO实时通知系统部分 (第390-411行)

**当前内容**:
```markdown
- ✅ **连接地址**: `https://socket.cs2.net.cn/socket.io/` (生产环境) 或 `http://localhost:4000/socket.io/` (开发环境)

**前端连接示例**：
```javascript
// 生产环境
const socket = io('https://socket.cs2.net.cn');

// 开发环境
const socket = io('http://localhost:4000');
```
```

**更正为**:
```markdown
- ✅ **连接地址**: `https://socket.cs2.net.cn/socket.io/` (统一域名，开发和生产环境都使用)

**前端连接示例**：
```javascript
// 统一使用服务器域名（前端本地开发也连接服务器）
const socket = io('https://socket.cs2.net.cn');
```
```

#### 添加环境配置说明

在battle-api.md中添加新的章节：

```markdown
## 环境配置说明

### 部署架构
- **前端**: 本地开发环境 (localhost:3000)
- **后端**: 服务器部署 (api.cs2.net.cn, socket.cs2.net.cn)
- **开发模式**: 前端本地 → 后端服务器

### 统一域名配置
开发环境和生产环境都使用相同的服务器域名：

- **API域名**: `https://api.cs2.net.cn`
- **WebSocket域名**: `https://socket.cs2.net.cn`

### 前端配置 (本地开发)
```bash
# ui/.env
NUXT_PUBLIC_API_TARGET=https://api.cs2.net.cn
NUXT_PUBLIC_SOCKET_TARGET=https://socket.cs2.net.cn
NUXT_PUBLIC_SITE_URL=http://localhost:3000
```

### 后端配置 (服务器部署)
```bash
# server/.env
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_DB_INDEX=0

# WebSocket服务器配置
API_URL=https://api.cs2.net.cn
NODE_ENV=production
SKIP_AUTH=true  # 当前测试配置
```
```

### 3. 更正集成使用示例 (第674-718行)

**当前内容**:
```javascript
// 2. 连接Socket.IO
const socket = io('http://domain:4000');
```

**更正为**:
```javascript
// 2. 连接Socket.IO
const socket = io('https://socket.cs2.net.cn');
```

## 🔧 WebSocket服务器配置确认

### 当前实际配置

根据最新的修复，WebSocket服务器的实际配置为：

```javascript
// deployment/node/nodejs/ws_server.js
const CONFIG = {
  skipAPIVerification: process.env.SKIP_AUTH === 'true' || true,  // 跳过认证
  debug: process.env.NODE_ENV !== 'production' || true,  // 启用调试
  apiBaseUrl: process.env.API_URL || 'https://api.cs2.net.cn'  // 正确的API域名
}
```

### 前端环境变量配置

```bash
# ui/.env
NUXT_PUBLIC_SOCKET_TARGET=https://socket.cs2.net.cn
NUXT_PUBLIC_API_TARGET=https://api.cs2.net.cn
NUXT_PUBLIC_SITE_URL=http://localhost:3000
```

### 后端环境变量配置

```bash
# server/.env
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_DB_INDEX=0
REDIS_PASSWORD=

# WebSocket容器环境变量
API_URL=https://api.cs2.net.cn
SKIP_AUTH=true
NODE_ENV=production
```

## 📊 更正优先级

### 立即更正 ⚡
1. **WebSocket连接地址**: 统一为 `https://socket.cs2.net.cn`
2. **前端连接示例**: 移除localhost选项，统一使用服务器域名
3. **API域名**: 统一为 `https://api.cs2.net.cn`

### 补充说明 📝
1. **部署架构说明**: 明确前端本地开发，后端服务器部署的架构
2. **环境变量配置**: 添加前端和后端的环境变量配置示例
3. **认证状态**: 明确当前跳过认证的测试配置

## 🎯 更正后的架构图

```
前端本地开发 (localhost:3000)
        ↓ HTTPS
服务器反向代理 (Nginx/Cloudflare)
        ↓
┌─────────────────┬─────────────────┐
│   Django API    │  WebSocket      │
│ api.cs2.net.cn  │ socket.cs2.net.cn│
│   (8000端口)    │   (4000端口)    │
└─────────────────┴─────────────────┘
        ↓
    Redis (6379端口)
```

## ✅ 更正完成后的效果

更正后，文档将准确反映：

1. **统一域名**: 开发和生产环境都使用服务器域名
2. **正确技术栈**: Socket.IO而非Django Channels
3. **实际配置**: 反映当前的认证和API配置
4. **部署架构**: 明确前端本地开发的架构模式

这样前端开发人员就能够正确理解和配置连接，避免尝试连接不存在的localhost服务。

---

**更正建议完成时间**: 2025-07-21 13:00  
**建议人员**: Augment Agent  
**更正状态**: 准备应用到文档
