from django.urls import re_path as url

from luckybox import views


app_name = 'luckybox'
urlpatterns = [
    url(r'^$', views.GetLuckyBoxView.as_view()),
    url(r'^inventory/$', views.GetLuckyInventoryView.as_view()),
    url(r'^inventory/item/', views.GetLuckyInventoryItemView.as_view()),
    url(r'^inventory/search/', views.SearchLuckyInventoryItemView.as_view()),
    url(r'^inventory/random/', views.RandomLuckyInventoryItemView.as_view()),
    url(r'^percent/', views.GetLuckyPercentView.as_view()),
    url(r'^lucky/', views.TryLuckyBoxView.as_view()),
    url(r'^record/', views.GetLuckyRecord.as_view()),
    url(r'^history/', views.GetLuckyHistory.as_view()),
    url(r'^rule', views.GetLuckyRule.as_view()),
]
