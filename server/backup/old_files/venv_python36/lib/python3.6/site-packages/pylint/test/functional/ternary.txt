consider-using-ternary:5::Consider using ternary (true_value if condition else false_value):HIGH
consider-using-ternary:6::Consider using ternary (true_value if condition else false_value):HIGH
consider-using-ternary:16:func2:Consider using ternary (true_value if condition else false_value):HIGH
consider-using-ternary:21:func3:Consider using ternary (true_value if condition else false_value):HIGH
consider-using-ternary:24::Consider using ternary ('ERROR' if some_callable(condition) else 'SUCCESS'):HIGH
consider-using-ternary:25::Consider using ternary ('greater' if SOME_VALUE1 > 3 else 'not greater'):HIGH
consider-using-ternary:26::Consider using ternary ('both' if (SOME_VALUE2 > 4) and (SOME_VALUE3) else 'not'):HIGH
simplify-boolean-expression:29::Boolean expression may be simplified to SOME_VALUE2:HIGH
