from channels.routing import ProtocolTypeRouter, URLRouter
from channels.auth import AuthMiddlewareStack
from django.conf.urls import url
from box.consumers import BattleConsumer

# WebSocket URL patterns
websocket_urlpatterns = [
    url(r'ws/battle/(?P<room_id>[^/]+)/$', BattleConsumer),
]

# ASGI application
application = ProtocolTypeRouter({
    'websocket': AuthMiddlewareStack(
        URLRouter(websocket_urlpatterns)
    ),
})
