import logging

from rest_framework.views import APIView
from rest_framework.permissions import AllowAny
from django.utils.translation import gettext_lazy as _

from envelope.business import user_rob_envelop, get_envelop_rules, get_envelop_records
from sitecfg.interfaces import get_maintenance_envelop
from steambase.enums import RespCode
from steambase.utils import current_user, reformat_resp

_logger = logging.getLogger(__name__)


class GetEnvelopRulesView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            user = current_user(request)
            exclude = ('id', 'create_time', 'update_time', 'title_en', 'desc_en', 'stock', 'desc_zh_hans', 'handsel_min', 'handsel_max', 'order')
            code, resp = get_envelop_rules(user, exclude)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class UserRobEnvelopView(APIView):

    def post(self, request):
        try:
            if get_maintenance_envelop():
                return RespCode.Maintenance.value, _("系统维护中，请稍等再试")
            user = current_user(request)
            trade_url = user.asset.tradeurl
            if not trade_url:
                return reformat_resp(RespCode.InvalidParams.value, {},
                                     _('Please set a real and valid Steam link.'))
            password = request.data.get('password', None)
            code, resp = user_rob_envelop(user, password)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetEnvelopRecordView(APIView):

    def get(self, request):
        try:
            user = current_user(request)
            code, resp = get_envelop_records(user)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')