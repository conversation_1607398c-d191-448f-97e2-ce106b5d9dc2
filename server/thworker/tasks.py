#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ThWorker迁移到Celery的任务定义
将原有的thworker任务转换为Celery任务
"""

import logging
from celery import shared_task
from django.core.cache import cache
from datetime import datetime, timedelta

# 导入原有的业务逻辑
from envelope.business import run_check_envelop_state
from grab.business import setup_check_run_grab_room_worker, setup_check_room_state_worker
from steambase.redis_con import init_conn
from authentication.interfaces import setup_reset_box_free_worker, setup_reduce_point_worker
from charge.interfaces import setup_charge_cancel_worker, setup_decrease_level_every_day, setup_charge_check_worker, setup_hpj_charge_check_order
from package.interfaces import setup_sync_items_price_worker
from box.interfaces import setup_case_bot_worker, init_case_data, setup_case_room_worker, init_case_cache, setup_case_pk_worker, setup_case_pk_join_worker, set_bot_cache_worker
from roll.interfaces import init_roll_room_data, setup_roll_room_worker, setup_check_rollroom_bot
from lottery.interfaces import setup_run_hourly_lottery_worker, setup_run_daily_lottery_worker, setup_run_weekly_lottery_worker
from tools.initChargeLevel import setup_init_charge_level
from withdraw.interfaces import setup_check_waxpeer_offer, setup_check_pendclose_trade, setup_wxp_buy_worker
from monitor.interfaces import setup_update_monitor_data
from b2ctrade.interfaces import setup_check_b2c_trade_state, setup_zbt_buy_worker, setup_zbt_buy_normal_worker
from lottery.business import add_lottery_joiner
from sitecfg.business import setup_update_base_count_worker

_logger = logging.getLogger(__name__)

# ==================== 定时任务 ====================

@shared_task(bind=True, name='thworker.update_case_records_cache')
def update_case_records_cache_task(self, count=100):
    """更新CaseRecords缓存 - 每5分钟执行"""
    try:
        from box.tasks import update_case_records_cache
        update_case_records_cache(count=count)
        _logger.info(f"[Celery] 更新CaseRecords缓存完成，处理数量: {count}")
        return f"Success: Updated {count} case records cache"
    except Exception as e:
        _logger.error(f"[Celery] 更新CaseRecords缓存失败: {e}")
        raise self.retry(countdown=60, max_retries=3)

@shared_task(bind=True, name='thworker.check_b2c_trade_state')
def check_b2c_trade_state_task(self):
    """检测B2C交易状态 - 每5分钟执行"""
    try:
        setup_check_b2c_trade_state()
        _logger.info("[Celery] B2C交易状态检查完成")
        return "Success: B2C trade state checked"
    except Exception as e:
        _logger.error(f"[Celery] B2C交易状态检查失败: {e}")
        raise self.retry(countdown=60, max_retries=3)

@shared_task(bind=True, name='thworker.sync_items_price')
def sync_items_price_task(self):
    """同步物品价格 - 每天凌晨3:10执行"""
    try:
        setup_sync_items_price_worker()
        _logger.info("[Celery] 物品价格同步完成")
        return "Success: Items price synced"
    except Exception as e:
        _logger.error(f"[Celery] 物品价格同步失败: {e}")
        raise self.retry(countdown=300, max_retries=2)

@shared_task(bind=True, name='thworker.hourly_lottery')
def hourly_lottery_task(self):
    """每小时抽奖任务"""
    try:
        setup_run_hourly_lottery_worker()
        _logger.info("[Celery] 每小时抽奖任务完成")
        return "Success: Hourly lottery completed"
    except Exception as e:
        _logger.error(f"[Celery] 每小时抽奖任务失败: {e}")
        raise self.retry(countdown=300, max_retries=2)

@shared_task(bind=True, name='thworker.daily_lottery')
def daily_lottery_task(self):
    """每日抽奖任务"""
    try:
        setup_run_daily_lottery_worker()
        _logger.info("[Celery] 每日抽奖任务完成")
        return "Success: Daily lottery completed"
    except Exception as e:
        _logger.error(f"[Celery] 每日抽奖任务失败: {e}")
        raise self.retry(countdown=600, max_retries=2)

@shared_task(bind=True, name='thworker.weekly_lottery')
def weekly_lottery_task(self):
    """每周抽奖任务"""
    try:
        setup_run_weekly_lottery_worker()
        _logger.info("[Celery] 每周抽奖任务完成")
        return "Success: Weekly lottery completed"
    except Exception as e:
        _logger.error(f"[Celery] 每周抽奖任务失败: {e}")
        raise self.retry(countdown=1200, max_retries=2)

# ==================== 持续运行任务 ====================

@shared_task(bind=True, name='thworker.case_room_worker')
def case_room_worker_task(self):
    """对战房间工作任务"""
    try:
        setup_case_room_worker()
        _logger.info("[Celery] 对战房间工作任务完成")
        return "Success: Case room worker completed"
    except Exception as e:
        _logger.error(f"[Celery] 对战房间工作任务失败: {e}")
        raise self.retry(countdown=30, max_retries=5)

@shared_task(bind=True, name='thworker.case_bot_worker')
def case_bot_worker_task(self):
    """开箱机器人任务"""
    try:
        setup_case_bot_worker()
        _logger.info("[Celery] 开箱机器人任务完成")
        return "Success: Case bot worker completed"
    except Exception as e:
        _logger.error(f"[Celery] 开箱机器人任务失败: {e}")
        raise self.retry(countdown=30, max_retries=5)

@shared_task(bind=True, name='thworker.roll_room_worker')
def roll_room_worker_task(self):
    """Roll房间工作任务"""
    try:
        setup_roll_room_worker()
        _logger.info("[Celery] Roll房间工作任务完成")
        return "Success: Roll room worker completed"
    except Exception as e:
        _logger.error(f"[Celery] Roll房间工作任务失败: {e}")
        raise self.retry(countdown=30, max_retries=5)

@shared_task(bind=True, name='thworker.check_room_state')
def check_room_state_task(self):
    """检查房间状态任务"""
    try:
        setup_check_room_state_worker()
        _logger.info("[Celery] 房间状态检查完成")
        return "Success: Room state checked"
    except Exception as e:
        _logger.error(f"[Celery] 房间状态检查失败: {e}")
        raise self.retry(countdown=30, max_retries=5)

@shared_task(bind=True, name='thworker.zbt_buy_worker')
def zbt_buy_worker_task(self):
    """ZBT自动采购任务"""
    try:
        setup_zbt_buy_worker()
        _logger.info("[Celery] ZBT自动采购任务完成")
        return "Success: ZBT buy worker completed"
    except Exception as e:
        _logger.error(f"[Celery] ZBT自动采购任务失败: {e}")
        raise self.retry(countdown=60, max_retries=3)

@shared_task(bind=True, name='thworker.case_pk_worker')
def case_pk_worker_task(self):
    """对战PK创建任务"""
    try:
        setup_case_pk_worker()
        _logger.info("[Celery] 对战PK创建任务完成")
        return "Success: Case PK worker completed"
    except Exception as e:
        _logger.error(f"[Celery] 对战PK创建任务失败: {e}")
        raise self.retry(countdown=30, max_retries=5)

@shared_task(bind=True, name='thworker.case_pk_join_worker')
def case_pk_join_worker_task(self):
    """对战PK加入任务"""
    try:
        setup_case_pk_join_worker()
        _logger.info("[Celery] 对战PK加入任务完成")
        return "Success: Case PK join worker completed"
    except Exception as e:
        _logger.error(f"[Celery] 对战PK加入任务失败: {e}")
        raise self.retry(countdown=30, max_retries=5)

@shared_task(bind=True, name='thworker.check_envelop_state')
def check_envelop_state_task(self):
    """检查信封状态任务"""
    try:
        run_check_envelop_state()
        _logger.info("[Celery] 信封状态检查完成")
        return "Success: Envelop state checked"
    except Exception as e:
        _logger.error(f"[Celery] 信封状态检查失败: {e}")
        raise self.retry(countdown=60, max_retries=3)

@shared_task(bind=True, name='thworker.check_pendclose_trade')
def check_pendclose_trade_task(self):
    """检查待关闭交易任务"""
    try:
        setup_check_pendclose_trade()
        _logger.info("[Celery] 待关闭交易检查完成")
        return "Success: Pending close trade checked"
    except Exception as e:
        _logger.error(f"[Celery] 待关闭交易检查失败: {e}")
        raise self.retry(countdown=60, max_retries=3)

@shared_task(bind=True, name='thworker.update_monitor_data')
def update_monitor_data_task(self):
    """更新监控数据任务"""
    try:
        setup_update_monitor_data()
        _logger.info("[Celery] 监控数据更新完成")
        return "Success: Monitor data updated"
    except Exception as e:
        _logger.error(f"[Celery] 监控数据更新失败: {e}")
        raise self.retry(countdown=60, max_retries=3)

# ==================== 初始化任务 ====================

@shared_task(bind=True, name='thworker.init_system')
def init_system_task(self):
    """系统初始化任务"""
    try:
        init_conn()
        init_case_data()
        init_case_cache()
        init_roll_room_data()
        _logger.info("[Celery] 系统初始化完成")
        return "Success: System initialized"
    except Exception as e:
        _logger.error(f"[Celery] 系统初始化失败: {e}")
        raise self.retry(countdown=60, max_retries=3)

# ==================== 工具函数 ====================

def start_continuous_tasks():
    """启动持续运行的任务"""
    tasks = [
        'thworker.case_room_worker',
        'thworker.case_bot_worker', 
        'thworker.roll_room_worker',
        'thworker.check_room_state',
        'thworker.zbt_buy_worker',
        'thworker.case_pk_worker',
        'thworker.case_pk_join_worker',
        'thworker.check_envelop_state',
        'thworker.check_pendclose_trade',
        'thworker.update_monitor_data',
    ]
    
    for task_name in tasks:
        try:
            # 使用apply_async启动任务
            from celery import current_app
            current_app.send_task(task_name)
            _logger.info(f"[Celery] 启动持续任务: {task_name}")
        except Exception as e:
            _logger.error(f"[Celery] 启动任务失败 {task_name}: {e}")
