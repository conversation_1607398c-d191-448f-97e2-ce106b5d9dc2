#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级PO文件修复脚本
处理所有常见的PO文件错误

移动到: scripts/tools/fix_po_advanced.py
"""

import re
import sys
import subprocess
from collections import defaultdict

def fix_po_file_advanced(file_path):
    """高级PO文件修复"""
    print(f"🔧 高级修复PO文件: {file_path}")
    
    # 读取文件
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    print(f"📊 原文件行数: {len(lines)}")
    
    # 修复后的行
    fixed_lines = []
    i = 0
    removed_duplicates = 0
    fixed_syntax = 0
    
    # 用于跟踪已见过的msgid
    seen_msgids = set()
    current_entry = {'msgid': '', 'msgstr': '', 'comment': '', 'lines': []}
    in_entry = False
    
    while i < len(lines):
        line = lines[i].rstrip()
        original_line = line
        
        # 1. 修复多余的引号
        if line.startswith('msgid ') and line.endswith('""'):
            line = line[:-1]  # 移除最后一个引号
            fixed_syntax += 1
            print(f"🔧 修复第{i+1}行的多余引号")
            
        elif line.startswith('msgstr ') and line.endswith('""'):
            line = line[:-1]  # 移除最后一个引号
            fixed_syntax += 1
            print(f"🔧 修复第{i+1}行的多余引号")
        
        # 2. 检测条目开始
        if line.startswith('#:'):
            # 如果之前有条目，检查是否重复
            if in_entry and current_entry['msgid']:
                if current_entry['msgid'] in seen_msgids:
                    print(f"🗑️  跳过重复条目: {current_entry['msgid'][:50]}...")
                    removed_duplicates += 1
                else:
                    # 添加非重复条目
                    seen_msgids.add(current_entry['msgid'])
                    fixed_lines.extend(current_entry['lines'])
            
            # 开始新条目
            current_entry = {'msgid': '', 'msgstr': '', 'comment': line, 'lines': [line]}
            in_entry = True
            
        elif line.startswith('msgid '):
            # 提取msgid内容
            match = re.match(r'msgid\s+"([^"]*)"', line)
            if match:
                current_entry['msgid'] = match.group(1)
            current_entry['lines'].append(line)
            
        elif line.startswith('msgstr '):
            # 提取msgstr内容
            match = re.match(r'msgstr\s+"([^"]*)"', line)
            if match:
                current_entry['msgstr'] = match.group(1)
            current_entry['lines'].append(line)
            
        elif line.startswith('"') and in_entry:
            # 多行字符串
            current_entry['lines'].append(line)
            
        elif line.strip() == '' and in_entry:
            # 条目结束
            if current_entry['msgid'] and current_entry['msgid'] not in seen_msgids:
                seen_msgids.add(current_entry['msgid'])
                current_entry['lines'].append(line)
                fixed_lines.extend(current_entry['lines'])
            elif current_entry['msgid'] in seen_msgids:
                print(f"🗑️  跳过重复条目: {current_entry['msgid'][:50]}...")
                removed_duplicates += 1
            else:
                # 空条目，保留
                current_entry['lines'].append(line)
                fixed_lines.extend(current_entry['lines'])
            
            in_entry = False
            current_entry = {'msgid': '', 'msgstr': '', 'comment': '', 'lines': []}
            
        else:
            # 其他行（文件头等）
            if not in_entry:
                fixed_lines.append(line)
            else:
                current_entry['lines'].append(line)
        
        i += 1
    
    # 处理最后一个条目
    if in_entry and current_entry['msgid']:
        if current_entry['msgid'] not in seen_msgids:
            seen_msgids.add(current_entry['msgid'])
            fixed_lines.extend(current_entry['lines'])
        else:
            print(f"🗑️  跳过重复条目: {current_entry['msgid'][:50]}...")
            removed_duplicates += 1
    
    # 3. 清理多余的空行
    final_lines = []
    prev_empty = False
    for line in fixed_lines:
        if line.strip() == '':
            if not prev_empty:
                final_lines.append(line)
            prev_empty = True
        else:
            final_lines.append(line)
            prev_empty = False
    
    # 4. 确保文件以换行符结尾
    if final_lines and not final_lines[-1].endswith('\n'):
        final_lines[-1] += '\n'
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        for line in final_lines:
            if not line.endswith('\n'):
                line += '\n'
            f.write(line)
    
    print(f"✅ 修复完成!")
    print(f"📊 修复统计:")
    print(f"  - 语法错误修复: {fixed_syntax}")
    print(f"  - 重复条目移除: {removed_duplicates}")
    print(f"  - 最终行数: {len(final_lines)}")
    
    return True

def test_compilation(file_path):
    """测试编译"""
    print(f"🧪 测试编译: {file_path}")
    
    try:
        # 尝试使用msgfmt验证
        result = subprocess.run(['msgfmt', '--check', file_path], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ msgfmt验证通过")
            return True
        else:
            print("❌ msgfmt验证失败:")
            print(result.stderr)
            return False
            
    except FileNotFoundError:
        print("⚠️  msgfmt未找到，跳过验证")
        return True

def main():
    po_file = "locale/zh_hans/LC_MESSAGES/django.po"
    
    print("🚀 高级Django PO文件修复工具")
    print("=" * 50)
    
    # 备份原文件
    import shutil
    backup_file = po_file + ".advanced_backup"
    shutil.copy2(po_file, backup_file)
    print(f"📋 已备份原文件到: {backup_file}")
    
    # 修复文件
    if fix_po_file_advanced(po_file):
        # 测试编译
        if test_compilation(po_file):
            print("\n🎉 修复成功! 可以尝试重新编译消息:")
            print("   python manage.py compilemessages")
        else:
            print("\n⚠️  修复后仍有问题，可能需要进一步检查")
    else:
        print("\n❌ 修复失败")

if __name__ == "__main__":
    main()
