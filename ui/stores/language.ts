import { defineStore } from 'pinia'
import { ref } from 'vue'

export type Language = 'zh_hans' | 'en'

export const useLanguageStore = defineStore('language', () => {
  // 状态
  const currentLanguage = ref<Language>('zh_hans')
  const messages = ref<Record<Language, Record<string, string>>>({
    zh_hans: {},
    en: {}
  })

  // 切换语言
  const setLanguage = (lang: Language) => {
    currentLanguage.value = lang
    if (process.client) {
      localStorage.setItem('language', lang)
      window.dispatchEvent(new Event('languageChanged'))
    }
  }

  // 获取翻译
  const t = (key: string) => {
    return messages.value[currentLanguage.value][key] || key
  }

  // 初始化语言
  const initLanguage = () => {
    if (process.client) {
      const savedLang = localStorage.getItem('language') as Language
      if (savedLang && (savedLang === 'zh_hans' || savedLang === 'en')) {
        currentLanguage.value = savedLang
      }
    }
  }

  return {
    currentLanguage,
    messages,
    setLanguage,
    t,
    initLanguage
  }
}) 