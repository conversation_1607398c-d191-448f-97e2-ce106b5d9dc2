#!/usr/bin/env python3
"""
为所有URL文件添加app_name
"""
import os
import re

def add_app_name(file_path, app_name):
    """为单个URL文件添加app_name"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 检查是否已经有app_name
        if 'app_name' in content:
            print(f"⏭️  跳过 {file_path} (已有app_name)")
            return False
        
        # 在urlpatterns之前添加app_name
        if 'urlpatterns' in content:
            content = re.sub(
                r'(.*?)(\nurlpatterns\s*=)',
                rf'\1\n\napp_name = '{app_name}'\2',
                content,
                flags=re.DOTALL
            )
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 为 {file_path} 添加了 app_name = '{app_name}'")
            return True
        else:
            print(f"⚠️  {file_path} 没有找到urlpatterns")
            return False
            
    except Exception as e:
        print(f"❌ 处理 {file_path} 时出错: {e}")
        return False

def main():
    """主函数"""
    print("🔧 开始为URL文件添加app_name...")
    
    # 需要添加app_name的应用列表
    apps_to_fix = [
        ('promotion', 'promotion'),
        ('sitecfg', 'sitecfg'),
        ('package', 'package'),
        ('box', 'box'),
        ('custombox', 'custombox'),
        ('luckybox', 'luckybox'),
        ('envelope', 'envelope'),
        ('roll', 'roll'),
        ('crash', 'crash'),
        ('grab', 'grab'),
        ('lottery', 'lottery'),
        ('blindbox', 'blindbox'),
        ('tradeup', 'tradeup'),
        ('b2ctrade', 'b2ctrade'),
        ('charge', 'charge'),
        ('withdraw', 'withdraw'),
        ('chat', 'chat'),
        ('market', 'market'),
        ('agent', 'agent'),
    ]
    
    fixed_count = 0
    for app_dir, app_name in apps_to_fix:
        urls_file = os.path.join(app_dir, 'urls.py')
        if os.path.exists(urls_file):
            if add_app_name(urls_file, app_name):
                fixed_count += 1
        else:
            print(f"⚠️  {urls_file} 不存在")
    
    print(f"\n🎉 处理完成！共为 {fixed_count} 个文件添加了app_name")

if __name__ == '__main__':
    main()
