<template>
  <div 
    class="stat-card p-6 rounded-xl border transition-all duration-300 hover:scale-105 hover:shadow-lg"
    :class="cardClasses"
  >
    <div v-if="loading" class="animate-pulse">
      <div class="h-8 bg-gray-600 rounded mb-2"></div>
      <div class="h-4 bg-gray-600 rounded w-3/4"></div>
    </div>
    
    <div v-else class="flex items-center justify-between">
      <div>
        <div class="text-3xl font-bold mb-1" :class="textClasses">
          {{ formatValue(value) }}
        </div>
        <div class="text-gray-300 text-sm font-medium">
          {{ label }}
        </div>
      </div>
      
      <div class="text-2xl opacity-70">
        {{ icon }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  value: number
  label: string
  icon: string
  color: 'blue' | 'green' | 'purple' | 'orange'
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

// 根据颜色计算样式类
const cardClasses = computed(() => {
  const colorMap = {
    blue: 'bg-blue-600/20 border-blue-500/30 hover:bg-blue-600/30',
    green: 'bg-green-600/20 border-green-500/30 hover:bg-green-600/30',
    purple: 'bg-purple-600/20 border-purple-500/30 hover:bg-purple-600/30',
    orange: 'bg-orange-600/20 border-orange-500/30 hover:bg-orange-600/30'
  }
  return colorMap[props.color]
})

const textClasses = computed(() => {
  const colorMap = {
    blue: 'text-blue-400',
    green: 'text-green-400',
    purple: 'text-purple-400',
    orange: 'text-orange-400'
  }
  return colorMap[props.color]
})

// 格式化数值显示
const formatValue = (value: number): string => {
  if (value >= 1000000) {
    return (value / 1000000).toFixed(1) + 'M'
  } else if (value >= 1000) {
    return (value / 1000).toFixed(1) + 'K'
  }
  return value.toLocaleString()
}
</script>

<style scoped>
.stat-card {
  backdrop-filter: blur(8px);
}
</style> 