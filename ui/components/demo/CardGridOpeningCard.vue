<template>
  <div class="card-grid-item bg-gray-800/90 backdrop-blur-md border border-gray-700/50 rounded-lg overflow-hidden hover:border-primary/40 transition-all duration-300 group relative cursor-pointer"
       :style="cardBorderStyle">
    
    <!-- 稀有度背景光效 -->
    <div 
      class="absolute inset-0 opacity-20 group-hover:opacity-30 transition-opacity duration-300"
      :style="cardInlineStyle"
    ></div>

    <!-- 装饰性网格背景 -->
    <div class="absolute inset-0 bg-grid opacity-10"></div>

    <div class="relative z-10 p-4 h-full flex flex-col">
      <!-- StatTrak 和外观标签 -->
      <div class="flex justify-between items-start mb-3">
        <div 
          v-if="isStatTrak"
          class="bg-orange-500/90 rounded px-2 py-1 text-xs font-bold text-white border border-orange-500/50"
        >
          <i class="i-ph-lightning mr-1"></i>ST
        </div>
        
        <div 
          class="text-xs font-bold text-white bg-black/40 px-2 py-1 rounded backdrop-blur-sm"
          :style="{ borderColor: rarityColor + '50' }"
        >
          {{ getExteriorShort() }}
        </div>
      </div>

      <!-- 物品图片区域 -->
      <div class="flex-1 flex items-center justify-center mb-3 relative">
        <!-- 物品背景光效 -->
        <div 
          class="absolute inset-0 blur-lg opacity-30"
          :style="cardInlineStyle"
        ></div>
        
        <img
          :src="record.item_info?.image"
          :alt="record.item_info?.name"
          class="relative z-10 h-20 w-20 object-contain drop-shadow-lg group-hover:scale-110 transition-transform duration-300"
          @error="(e) => (e.target as HTMLImageElement).src = 'https://placehold.co/80x80/dc2626/fecaca?text=ITEM'"
        />
      </div>

      <!-- 物品名称 -->
      <div class="text-center mb-3">
        <div class="text-white font-bold text-sm truncate mb-1" :title="record.item_info?.name">
          {{ getSkinName(record.item_info?.name) }}
        </div>
        <div class="text-white/70 text-xs truncate">
          {{ getWeaponName(record.item_info?.name) }}
        </div>
      </div>

      <!-- 稀有度指示器 -->
      <div class="text-center mb-3">
        <div 
          class="text-xs font-medium px-2 py-1 rounded inline-block"
          :style="{ 
            color: rarityColor,
            backgroundColor: rarityColor + '20',
            borderColor: rarityColor + '50'
          }"
        >
          {{ getRarityName() }}
        </div>
      </div>

      <!-- 底部信息 -->
      <div class="border-t border-gray-700/50 pt-3 mt-auto">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <img
              :src="record.user_avatar || 'https://placehold.co/24x24/374151/9ca3af?text=U'"
              :alt="record.user_name"
              class="w-6 h-6 rounded-full border border-gray-600"
              @error="(e) => (e.target as HTMLImageElement).src = 'https://placehold.co/24x24/374151/9ca3af?text=U'"
            />
            <div class="text-blue-400 text-xs font-medium truncate">{{ record.user_name }}</div>
          </div>
          <div class="text-white/60 text-xs">{{ formatTime() }}</div>
        </div>
        
        <div class="flex items-center justify-center mt-2 text-white/60 text-xs">
          <img
            :src="record.case_info?.cover"
            :alt="record.case_info?.name"
            class="w-4 h-4 object-contain mr-2"
            @error="(e) => (e.target as HTMLImageElement).src = 'https://placehold.co/16x16/374151/9ca3af?text=C'"
          />
          <span class="truncate">{{ record.case_info?.name }}</span>
        </div>
      </div>

      <!-- 悬停时显示更多信息 -->
      <div class="absolute inset-1 bg-black/80 opacity-0 group-hover:opacity-100 transition-all duration-300 flex flex-col items-center justify-center p-3 z-30 rounded-lg">
        <div class="text-center">
          <div class="text-sm font-bold text-white mb-2">
            {{ getSkinName(record.item_info?.name) }}
          </div>
          <div class="text-xs text-white/80 mb-1">
            {{ getExteriorName() }}
          </div>
          <div class="text-xs text-primary font-medium">
            <i class="i-ph-cursor-click mr-1"></i>
            查看箱子详情
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  record: any
}

const props = defineProps<Props>()

const getSkinName = (fullName?: string): string => {
  if (!fullName) return 'Unknown Item'
  const parts = fullName.split(' | ')
  if (parts.length < 2) return fullName
  const skinPart = parts[1].trim()
  return skinPart.split(' (')[0].trim()
}

const getWeaponName = (fullName?: string): string => {
  if (!fullName) return 'Unknown Weapon'
  const parts = fullName.split(' | ')
  return parts[0].trim()
}

const getExteriorName = (): string => {
  return props.record.item_info?.item_exterior?.exterior_name || 'Factory New'
}

const getExteriorShort = (): string => {
  const exterior = getExteriorName()
  const shortMap: Record<string, string> = {
    'Factory New': 'FN',
    'Minimal Wear': 'MW', 
    'Field-Tested': 'FT',
    'Well-Worn': 'WW',
    'Battle-Scarred': 'BS'
  }
  return shortMap[exterior] || exterior.substring(0, 2).toUpperCase()
}

const getRarityName = (): string => {
  return 'Covert'
}

const isStatTrak = computed(() => {
  return props.record.item_info?.name?.includes('StatTrak™') || false
})

// 将十六进制颜色转换为 rgba 字符串
const hexToRgba = (hex: string, alpha = 1): string => {
  if (!hex) return `rgba(17, 24, 39, ${alpha})`
  hex = hex.replace('#', '').trim()

  if (hex.length === 3) {
    hex = hex.split('').map((c) => c + c).join('')
  }

  if (hex.length !== 6) return `rgba(17, 24, 39, ${alpha})`

  const r = parseInt(hex.substring(0, 2), 16)
  const g = parseInt(hex.substring(2, 4), 16)
  const b = parseInt(hex.substring(4, 6), 16)
  return `rgba(${r}, ${g}, ${b}, ${alpha})`
}

const rarityColor = computed(() => {
  const rarities: Record<string, string> = {
    'Covert': '#eb4b4b',
    'Classified': '#d32ce6', 
    'Restricted': '#8847ff',
    'Mil-Spec': '#4b69ff',
    'Industrial': '#5e98d9',
    'Consumer': '#b0c3d9'
  }
  return rarities[getRarityName()] || '#eb4b4b'
})

// 卡片边框样式
const cardBorderStyle = computed(() => ({
  borderColor: hexToRgba(rarityColor.value, 0.3)
}))

// 卡片内联样式
const cardInlineStyle = computed(() => ({
  background: `linear-gradient(135deg, ${hexToRgba(rarityColor.value, 0.1)}, transparent 70%)`
}))

const formatTime = (): string => {
  return 'just now'
}
</script>

<style lang="scss" scoped>
.card-grid-item {
  animation: scaleInCard 0.5s ease-out;
  height: 280px; // 固定高度确保网格对齐
}

.bg-grid {
  background-image: 
    linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
  background-size: 20px 20px;
}

@keyframes scaleInCard {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}
</style> 