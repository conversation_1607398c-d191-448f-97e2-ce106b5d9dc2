# Docker部署指南

## 🐳 概述

本文档介绍如何使用Docker和Docker Compose部署完整的CSGOSkins.com.cn系统，包括Web服务、任务调度、WebSocket服务等。

## 📋 系统架构

### 服务组件

```
┌─────────────────────────────────────────────────────────────┐
│                    CSGOSkins.com.cn                        │
│                     Docker架构                              │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────┬─────────────────┬─────────────────┬─────────────────┐
│   Redis服务     │   Django Web    │   任务调度       │  WebSocket服务  │
│                 │                 │                 │                 │
│ • 缓存存储      │ • Web API       │ • Celery Worker │ • 实时通信      │
│ • 消息队列      │ • 管理后台      │ • Celery Beat   │ • 事件推送      │
│ • 会话存储      │ • 静态文件      │ • ThWorker      │ • Redis订阅     │
└─────────────────┴─────────────────┴─────────────────┴─────────────────┘
```

### 容器列表

| 容器名称 | 服务 | 端口 | 功能描述 |
|---------|------|------|----------|
| csgoskins-redis | Redis | 6379 | 缓存、消息队列、会话存储 |
| csgoskins-web | Django | 8000 | Web API、管理后台 |
| csgoskins-celery-worker | Celery Worker | - | 异步任务处理 |
| csgoskins-celery-beat | Celery Beat | - | 定时任务调度 |
| csgoskins-thworker | ThWorker | - | 自定义任务调度 |
| csgoskins-websocket | WebSocket | 4000 | 实时通信服务 |

## 🚀 快速开始

### 1. 环境准备

```bash
# 检查Docker版本
docker --version
docker-compose --version

# 克隆项目（如果需要）
git clone <repository-url>
cd csgoskins.com.cn
```

### 2. 配置环境

```bash
# 复制环境配置文件
cp .env.docker .env

# 编辑环境配置
vim .env
```

### 3. 一键部署

```bash
# 使用部署脚本
./docker-deploy.sh start

# 或者手动部署
docker-compose up -d
```

### 4. 验证部署

```bash
# 检查服务状态
./docker-deploy.sh status

# 检查任务调度
./monitor-tasks.sh status
```

## 📝 详细配置

### 环境变量配置

编辑 `.env` 文件：

```bash
# Django配置
DEBUG=False
SECRET_KEY=your-secret-key-here
ALLOWED_HOSTS=localhost,127.0.0.1,csgoskins.com.cn

# 数据库配置
DATABASE_URL=mysql://root:password@localhost:3306/csgogo

# Redis配置
REDIS_URL=redis://localhost:6379/0

# Celery配置
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0
CELERY_TIMEZONE=Asia/Shanghai
```

### 数据库初始化

```bash
# 进入Web容器
docker exec -it csgoskins-web bash

# 运行数据库迁移
python manage.py migrate

# 创建超级用户
python manage.py createsuperuser

# 收集静态文件
python manage.py collectstatic --noinput
```

## 🔧 运维管理

### 服务管理

```bash
# 启动所有服务
docker-compose up -d

# 停止所有服务
docker-compose down

# 重启特定服务
docker-compose restart web

# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f web
```

### 任务调度管理

```bash
# 检查任务状态
./monitor-tasks.sh status

# 重启任务服务
./monitor-tasks.sh restart

# 查看任务日志
./monitor-tasks.sh logs

# 清理任务队列
./monitor-tasks.sh clear
```

### 数据备份

```bash
# 备份数据库
docker exec csgoskins-web python manage.py dumpdata > backup.json

# 备份Redis数据
docker exec csgoskins-redis redis-cli save
docker cp csgoskins-redis:/data/dump.rdb ./redis-backup.rdb

# 备份媒体文件
tar -czf media-backup.tar.gz server/media/
```

## 🔍 监控和调试

### 健康检查

```bash
# Django健康检查
curl http://localhost:8000/api/health/

# WebSocket健康检查
curl http://localhost:4000/health

# Redis健康检查
docker exec csgoskins-redis redis-cli ping
```

### 日志查看

```bash
# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs web
docker-compose logs celery-worker

# 实时日志
docker-compose logs -f
```

### 性能监控

```bash
# 查看容器资源使用
docker stats

# 查看Celery任务统计
docker exec csgoskins-celery-worker celery -A steambase inspect stats

# 查看Redis信息
docker exec csgoskins-redis redis-cli info
```

## 🛠️ 故障排除

### 常见问题

1. **容器启动失败**
   ```bash
   # 查看详细错误信息
   docker-compose logs <service-name>
   
   # 重新构建镜像
   docker-compose build --no-cache <service-name>
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库配置
   cat .env | grep DATABASE
   
   # 测试数据库连接
   docker exec csgoskins-web python manage.py dbshell
   ```

3. **Redis连接失败**
   ```bash
   # 检查Redis状态
   docker exec csgoskins-redis redis-cli ping
   
   # 重启Redis服务
   docker-compose restart redis
   ```

4. **任务不执行**
   ```bash
   # 检查Celery Worker状态
   ./monitor-tasks.sh worker
   
   # 检查任务队列
   docker exec csgoskins-redis redis-cli llen celery
   ```

### 调试模式

```bash
# 以调试模式启动服务
docker-compose -f docker-compose.yml -f docker-compose.debug.yml up

# 进入容器调试
docker exec -it csgoskins-web bash
```

## 📚 相关文档

- [API文档](./api/)
- [任务调度文档](./backend/thworker.md)
- [WebSocket文档](./websocket/)
- [部署指南](./deployment/)

## 🔄 更新和维护

### 更新代码

```bash
# 拉取最新代码
git pull origin main

# 重新构建和部署
./docker-deploy.sh restart
```

### 数据库迁移

```bash
# 生成迁移文件
docker exec csgoskins-web python manage.py makemigrations

# 应用迁移
docker exec csgoskins-web python manage.py migrate
```

### 清理和优化

```bash
# 清理未使用的镜像
docker image prune

# 清理未使用的容器
docker container prune

# 清理未使用的数据卷
docker volume prune
```
