# 部署运维文档

本目录包含环境搭建、部署流程、运维监控等相关文档。

## 📋 文档列表

- [**环境搭建**](setup.md) - 开发和生产环境配置指南
- [**部署指南**](deployment.md) - Docker部署和CI/CD流程
- [**升级建议**](upgrade-recommendations.md) - 系统优化和升级路线

## 🐳 Docker部署

### 快速启动
```bash
# 克隆项目
git clone <repository-url>
cd csgoskins.com.cn

# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps
```

### 服务架构
```yaml
services:
  web:        # Django Web服务
  nginx:      # 反向代理
  mysql:      # 数据库
  redis:      # 缓存和数据存储
  thworker:   # 异步任务处理 (自定义框架)
  nodejs:     # Node.js服务 (WebSocket等)
```

## 🔧 环境配置

### 开发环境
```bash
# Python虚拟环境
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# 环境变量
cp server/example.env server/.env
vim server/.env
```

### 生产环境
```bash
# 系统依赖
sudo apt update
sudo apt install docker.io docker-compose

# SSL证书 (Let's Encrypt)
certbot certonly --standalone -d yourdomain.com
```

## 📊 监控运维

### 日志管理
```bash
# 查看服务日志
docker-compose logs -f web
docker-compose logs -f nginx

# 日志轮转
logrotate /etc/logrotate.d/docker-logs
```

### 性能监控
- **系统监控**: CPU、内存、磁盘使用率
- **服务监控**: Docker容器状态
- **应用监控**: Django接口响应时间
- **数据库监控**: MySQL慢查询日志

### 备份策略
```bash
# 数据库备份
docker-compose exec mysql mysqldump -u root -p database > backup.sql

# 文件备份
tar -czf media_backup.tar.gz server/media/

# 自动备份脚本
0 2 * * * /path/to/backup.sh
```

## 🚀 CI/CD流程

### Git工作流
```
main分支    # 生产环境
├── develop # 开发环境
├── feature # 功能分支
└── hotfix  # 紧急修复
```

### 自动部署
```yaml
# .github/workflows/deploy.yml
name: Deploy
on:
  push:
    branches: [main]
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to server
        run: |
          ssh user@server 'cd /app && git pull && docker-compose up -d'
```

## 🛡️ 安全配置

### 服务器安全
- SSH密钥认证
- 防火墙配置
- 自动安全更新
- 失败登录监控

### 应用安全
- HTTPS强制跳转
- CSRF保护
- SQL注入防护
- 输入验证和过滤

### 数据安全
- 数据库加密
- 敏感信息脱敏
- 访问日志记录
- 定期安全扫描

## 📈 性能优化

### 服务器优化
```bash
# 内核参数优化
echo 'net.core.somaxconn = 65535' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_max_syn_backlog = 65535' >> /etc/sysctl.conf

# 文件描述符限制
echo '* soft nofile 65535' >> /etc/security/limits.conf
echo '* hard nofile 65535' >> /etc/security/limits.conf
```

### 应用优化
- 数据库连接池
- Redis缓存策略
- 静态文件CDN
- 图片压缩优化

### 数据库优化
```sql
-- 索引优化
CREATE INDEX idx_user_id ON box_caseroom(user_id);
CREATE INDEX idx_status ON box_caseroom(status);

-- 查询优化
EXPLAIN SELECT * FROM box_caseroom WHERE status = 1;
```

## 🔍 故障排查

### 常见问题
1. **服务无法启动** - 检查端口占用、配置文件
2. **数据库连接失败** - 检查网络、权限配置
3. **Redis连接超时** - 检查内存使用、连接数
4. **WebSocket断连** - 检查Nginx配置、防火墙

### 排查工具
```bash
# 系统资源
htop
iostat
netstat -tlnp

# Docker服务
docker stats
docker-compose logs

# 数据库状态
SHOW PROCESSLIST;
SHOW STATUS;
```

## 📞 应急响应

### 紧急联系人
- **运维负责人**: <EMAIL>
- **开发负责人**: <EMAIL>
- **产品负责人**: <EMAIL>

### 应急流程
1. **问题发现** - 监控告警或用户反馈
2. **快速评估** - 影响范围和严重程度
3. **临时措施** - 降级服务或切换备用
4. **根因分析** - 定位问题并修复
5. **复盘总结** - 改进预防措施

---

*更新时间: 2025-06-18*
