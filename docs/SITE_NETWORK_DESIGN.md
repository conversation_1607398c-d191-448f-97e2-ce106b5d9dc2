# 🌐 站群管理系统设计方案

## 📋 概述

基于现有sitecfg中的SEO功能，设计一个独立的站群管理系统，支持代理商下多个网站的统一管理。

## 🎯 核心需求

### 1. 站群概念
- **代理商下多个网站**: 每个代理商可以管理多个独立域名的网站
- **用户域名关联**: 用户注册时自动关联访问的域名信息
- **独立配置**: 每个网站独立配置域名、SEO、功能开关等

### 2. 文章系统集成
- **域名归属**: 每篇文章可以设置归属的特定域名
- **全局显示**: 未设置归属域名的文章在所有网站显示
- **文章系统开关**: 每个网站可以独立开关文章功能

### 3. 功能开关
- **模块化控制**: 每个网站可以独立控制各功能模块的开关
- **灵活配置**: 支持细粒度的功能配置

## 🏗️ 系统架构设计

### 应用命名
建议使用 `sitegroup` 或 `multisite` 作为新应用名称，避免与SEO概念混淆。

### 核心模型设计

#### 1. SiteGroup (站群)
```python
class SiteGroup(ModelBase):
    """站群模型 - 代理商的网站集合"""
    agent = models.ForeignKey(USER_MODEL, on_delete=models.CASCADE, 
                             verbose_name=_('Agent'), related_name='site_groups')
    name = models.CharField(_('Group Name'), max_length=128)
    description = models.TextField(_('Description'), blank=True, null=True)
    is_active = models.BooleanField(_('Is Active'), default=True)
    
    class Meta:
        verbose_name = _('Site Group')
        verbose_name_plural = _('Site Groups')
```

#### 2. Site (网站)
```python
class Site(ModelBase):
    """网站模型 - 单个域名网站"""
    site_group = models.ForeignKey(SiteGroup, on_delete=models.CASCADE,
                                  verbose_name=_('Site Group'), related_name='sites')
    
    # 基础信息
    domain = models.CharField(_('Domain'), max_length=255, unique=True)
    name = models.CharField(_('Site Name'), max_length=128)
    subtitle = models.CharField(_('Subtitle'), max_length=128, blank=True, null=True)
    
    # SEO信息
    seo_title = models.CharField(_('SEO Title'), max_length=128, blank=True, null=True)
    seo_keywords = models.CharField(_('SEO Keywords'), max_length=255, blank=True, null=True)
    seo_description = models.TextField(_('SEO Description'), max_length=255, blank=True, null=True)
    icp_number = models.CharField(_('ICP Number'), max_length=128, blank=True, null=True)
    
    # 功能开关
    articles_enabled = models.BooleanField(_('Articles Enabled'), default=True)
    announcements_enabled = models.BooleanField(_('Announcements Enabled'), default=True)
    case_battle_enabled = models.BooleanField(_('Case Battle Enabled'), default=True)
    market_enabled = models.BooleanField(_('Market Enabled'), default=True)
    lottery_enabled = models.BooleanField(_('Lottery Enabled'), default=True)
    
    # 状态
    is_active = models.BooleanField(_('Is Active'), default=True)
    is_default = models.BooleanField(_('Is Default'), default=False)
    
    class Meta:
        verbose_name = _('Site')
        verbose_name_plural = _('Sites')
```

#### 3. SiteConfig (网站配置)
```python
class SiteConfig(ModelBase):
    """网站配置模型"""
    site = models.ForeignKey(Site, on_delete=models.CASCADE,
                            verbose_name=_('Site'), related_name='configs')
    
    # 配置项
    config_key = models.CharField(_('Config Key'), max_length=64)
    config_value = models.TextField(_('Config Value'), blank=True, null=True)
    config_type = models.CharField(_('Config Type'), max_length=20, 
                                  choices=[
                                      ('string', _('String')),
                                      ('integer', _('Integer')),
                                      ('float', _('Float')),
                                      ('boolean', _('Boolean')),
                                      ('json', _('JSON')),
                                  ], default='string')
    
    # 元信息
    description = models.CharField(_('Description'), max_length=255, blank=True, null=True)
    is_active = models.BooleanField(_('Is Active'), default=True)
    
    class Meta:
        verbose_name = _('Site Config')
        verbose_name_plural = _('Site Configs')
        unique_together = ['site', 'config_key']
```

### 与现有系统的集成

#### 1. 用户模型扩展
```python
# 在 authentication/models.py 中的 AuthUser 模型已有 domain 字段
# 需要关联到新的 Site 模型

class AuthUser(AbstractBaseUser, PermissionsMixin):
    # ... 现有字段 ...
    domain = models.CharField(_('Domain'), max_length=300, null=True, default=None, blank=True)
    
    # 新增关联字段
    registered_site = models.ForeignKey('sitegroup.Site', on_delete=models.SET_NULL,
                                       verbose_name=_('Registered Site'), 
                                       null=True, blank=True, related_name='users')
```

#### 2. 文章系统集成
```python
# 在 articles/models.py 中的 Content 模型添加站点关联

class Content(ModelBase):
    # ... 现有字段 ...
    
    # 站点关联
    target_sites = models.ManyToManyField('sitegroup.Site', 
                                         verbose_name=_('Target Sites'),
                                         blank=True, related_name='contents',
                                         help_text=_('Leave empty to show on all sites'))
```

## 🔧 业务逻辑设计

### 1. 用户注册流程
```python
def register_user_with_site(request, user_data):
    """用户注册时关联站点"""
    # 获取访问域名
    domain = request.get_host()
    
    # 查找对应站点
    site = Site.objects.filter(domain=domain, is_active=True).first()
    
    # 创建用户
    user = AuthUser.objects.create(**user_data)
    user.domain = domain
    user.registered_site = site
    user.save()
    
    return user
```

### 2. 内容显示逻辑
```python
def get_site_contents(site, content_type=None):
    """获取站点相关内容"""
    # 获取指定站点的内容
    site_specific = Content.objects.filter(
        target_sites=site,
        status='published'
    )
    
    # 获取全局内容（未指定站点的）
    global_contents = Content.objects.filter(
        target_sites__isnull=True,
        status='published'
    )
    
    # 合并并去重
    contents = (site_specific | global_contents).distinct()
    
    if content_type:
        contents = contents.filter(content_type=content_type)
    
    return contents
```

### 3. 功能开关检查
```python
def check_feature_enabled(site, feature_name):
    """检查站点功能是否启用"""
    if not site or not site.is_active:
        return False
    
    feature_map = {
        'articles': site.articles_enabled,
        'announcements': site.announcements_enabled,
        'case_battle': site.case_battle_enabled,
        'market': site.market_enabled,
        'lottery': site.lottery_enabled,
    }
    
    return feature_map.get(feature_name, False)
```

## 📊 数据迁移方案

### 1. 从sitecfg.SEO迁移
```python
def migrate_seo_to_sites():
    """将现有SEO数据迁移到新的站群系统"""
    from sitecfg.models import SEO
    from sitegroup.models import Site, SiteGroup
    
    for seo in SEO.objects.all():
        # 为每个代理商创建站群
        if seo.agent:
            site_group, created = SiteGroup.objects.get_or_create(
                agent=seo.agent,
                defaults={'name': f'{seo.agent.username} Sites'}
            )
        else:
            # 默认站群
            site_group = SiteGroup.objects.filter(agent__isnull=True).first()
            if not site_group:
                site_group = SiteGroup.objects.create(
                    name='Default Sites',
                    agent=None
                )
        
        # 创建站点
        Site.objects.create(
            site_group=site_group,
            domain=seo.url,
            name=seo.name,
            subtitle=seo.subtitle,
            seo_title=seo.title,
            seo_keywords=seo.keywords,
            seo_description=seo.description,
            icp_number=seo.icp,
            articles_enabled=seo.news_enable,
            is_active=seo.enable
        )
```

### 2. 用户域名关联
```python
def associate_users_with_sites():
    """关联现有用户到对应站点"""
    from authentication.models import AuthUser
    from sitegroup.models import Site
    
    for user in AuthUser.objects.filter(domain__isnull=False):
        site = Site.objects.filter(domain=user.domain).first()
        if site:
            user.registered_site = site
            user.save(update_fields=['registered_site'])
```

## 🔌 API设计

### 1. 站点信息API
```python
# GET /api/sitegroup/site-info/
{
    "code": 0,
    "message": "Success",
    "body": {
        "domain": "www.example.com",
        "name": "Example Site",
        "subtitle": "Best CSGO Site",
        "seo_title": "CSGO Skins Trading",
        "seo_keywords": "csgo,skins,trading",
        "seo_description": "Professional CSGO skins trading platform",
        "features": {
            "articles_enabled": true,
            "announcements_enabled": true,
            "case_battle_enabled": true,
            "market_enabled": false,
            "lottery_enabled": true
        }
    }
}
```

### 2. 站点内容API
```python
# GET /api/sitegroup/contents/
{
    "code": 0,
    "message": "Success", 
    "body": {
        "items": [
            {
                "id": 1,
                "title": "Welcome Article",
                "content_type": "article",
                "is_global": false,
                "target_sites": ["www.example.com"]
            }
        ]
    }
}
```

## 🎯 实施计划

### 阶段1: 基础架构 (1周)
1. 创建 `sitegroup` 应用
2. 设计和实现核心模型
3. 创建管理后台界面
4. 编写基础API接口

### 阶段2: 数据迁移 (3天)
1. 编写迁移脚本
2. 测试数据迁移
3. 验证数据完整性

### 阶段3: 系统集成 (1周)
1. 集成用户注册流程
2. 集成文章系统
3. 实现功能开关逻辑
4. 更新前端调用

### 阶段4: 测试优化 (3天)
1. 功能测试
2. 性能优化
3. 文档完善

---

**🎯 总结**: 
- 独立的站群管理系统，避免与SEO概念混淆
- 支持代理商多站点管理
- 灵活的功能开关和配置
- 与现有系统无缝集成
- 渐进式迁移，降低风险
