#!/usr/bin/env python3
"""
修复重复的on_delete参数
"""
import os
import re

def fix_duplicate_ondelete(file_path):
    """修复单个文件中重复的on_delete参数"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 修复重复的on_delete参数
        # 匹配模式：on_delete=xxx, ... on_delete=yyy
        pattern = r'(on_delete=models\.[A-Z_]+)([^,\)]*,\s*[^,\)]*)*,\s*(on_delete=models\.[A-Z_]+)'
        
        def replace_duplicate(match):
            # 只保留第一个on_delete
            return match.group(1)
        
        # 多次执行替换，直到没有重复为止
        while True:
            new_content = re.sub(pattern, replace_duplicate, content)
            if new_content == content:
                break
            content = new_content
        
        if new_content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            print(f"✅ 修复了 {file_path}")
            return True
        else:
            print(f"⏭️  跳过 {file_path} (无需修复)")
            return False
            
    except Exception as e:
        print(f"❌ 修复 {file_path} 时出错: {e}")
        return False

def main():
    """主函数"""
    print("🔧 开始修复重复的on_delete参数...")
    
    # 查找所有Python文件
    model_files = []
    for root, dirs, files in os.walk('.'):
        # 跳过虚拟环境
        if 'venv' in root:
            continue
            
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if 'on_delete=' in content:
                            model_files.append(file_path)
                except:
                    continue
    
    print(f"📁 找到 {len(model_files)} 个包含on_delete的文件")
    
    fixed_count = 0
    for file_path in model_files:
        if fix_duplicate_ondelete(file_path):
            fixed_count += 1
    
    print(f"\n🎉 修复完成！共修复了 {fixed_count} 个文件")

if __name__ == '__main__':
    main()
