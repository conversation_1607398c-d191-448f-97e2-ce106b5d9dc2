from rest_framework import serializers
from django.contrib.auth import get_user_model

from steambase.serializers import CustomFieldsSerializer
from sitegroup.models import Site, SiteGroup, SiteConfig, SiteTheme, SiteAnalytics

User = get_user_model()


class SiteGroupSerializer(CustomFieldsSerializer):
    """站群序列化器"""
    agent_name = serializers.CharField(source='agent.username', read_only=True)
    active_sites_count = serializers.SerializerMethodField()
    
    class Meta:
        model = SiteGroup
        fields = [
            'id', 'name', 'description', 'is_active', 'created_at', 'updated_at',
            'agent', 'agent_name', 'active_sites_count'
        ]
    
    def get_active_sites_count(self, obj):
        return obj.get_active_sites_count()


class SiteThemeSerializer(CustomFieldsSerializer):
    """站点主题序列化器"""
    
    class Meta:
        model = SiteTheme
        fields = [
            'theme_name', 'header_background', 'header_text_color',
            'footer_background', 'footer_text_color', 'font_family',
            'font_size_base', 'container_width', 'border_radius'
        ]


class SiteConfigSerializer(CustomFieldsSerializer):
    """站点配置序列化器"""
    typed_value = serializers.SerializerMethodField()
    
    class Meta:
        model = SiteConfig
        fields = [
            'id', 'config_key', 'config_value', 'config_type',
            'category', 'description', 'is_active', 'typed_value'
        ]
    
    def get_typed_value(self, obj):
        return obj.get_typed_value()


class SiteSerializer(CustomFieldsSerializer):
    """站点序列化器"""
    group_name = serializers.CharField(source='site_group.name', read_only=True)
    agent_name = serializers.CharField(source='site_group.agent.username', read_only=True)
    features = serializers.SerializerMethodField()
    theme = SiteThemeSerializer(read_only=True)
    registered_users_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Site
        fields = [
            'id', 'domain', 'name', 'subtitle', 'logo_url', 'favicon_url',
            'seo_title', 'seo_keywords', 'seo_description', 'icp_number',
            'primary_color', 'secondary_color', 'custom_css',
            'is_active', 'is_default', 'maintenance_mode', 'maintenance_message',
            'created_at', 'updated_at',
            'group_name', 'agent_name', 'features', 'theme', 'registered_users_count'
        ]
    
    def get_features(self, obj):
        return obj.get_feature_status()
    
    def get_registered_users_count(self, obj):
        return obj.get_registered_users_count()


class SiteAnalyticsSerializer(CustomFieldsSerializer):
    """站点统计序列化器"""
    
    class Meta:
        model = SiteAnalytics
        fields = [
            'id', 'date', 'page_views', 'unique_visitors', 'new_registrations',
            'active_users', 'case_openings', 'battles_created', 'market_transactions'
        ]


class SiteListSerializer(CustomFieldsSerializer):
    """站点列表序列化器（简化版）"""
    group_name = serializers.CharField(source='site_group.name', read_only=True)
    
    class Meta:
        model = Site
        fields = [
            'id', 'domain', 'name', 'subtitle', 'is_active', 'is_default',
            'maintenance_mode', 'group_name'
        ]


class SiteCreateSerializer(serializers.ModelSerializer):
    """站点创建序列化器"""
    
    class Meta:
        model = Site
        fields = [
            'domain', 'name', 'subtitle', 'logo_url', 'favicon_url',
            'seo_title', 'seo_keywords', 'seo_description', 'icp_number',
            'primary_color', 'secondary_color'
        ]
    
    def validate_domain(self, value):
        """验证域名格式"""
        if not value:
            raise serializers.ValidationError("Domain is required")
        
        # 检查域名是否已存在
        if Site.objects.filter(domain=value).exists():
            raise serializers.ValidationError("Domain already exists")
        
        # 简单的域名格式验证
        if not value.replace('.', '').replace('-', '').isalnum():
            raise serializers.ValidationError("Invalid domain format")
        
        return value


class SiteUpdateSerializer(serializers.ModelSerializer):
    """站点更新序列化器"""
    
    class Meta:
        model = Site
        fields = [
            'name', 'subtitle', 'logo_url', 'favicon_url',
            'seo_title', 'seo_keywords', 'seo_description', 'icp_number',
            'primary_color', 'secondary_color', 'custom_css',
            'maintenance_mode', 'maintenance_message'
        ]


class SiteFeaturesSerializer(serializers.Serializer):
    """站点功能开关序列化器"""
    articles = serializers.BooleanField(required=False)
    announcements = serializers.BooleanField(required=False)
    case_battle = serializers.BooleanField(required=False)
    case_opening = serializers.BooleanField(required=False)
    market = serializers.BooleanField(required=False)
    lottery = serializers.BooleanField(required=False)
    roll = serializers.BooleanField(required=False)
    crash = serializers.BooleanField(required=False)
    
    def validate(self, data):
        """验证至少有一个功能开关"""
        if not data:
            raise serializers.ValidationError("At least one feature must be specified")
        return data


class SiteInfoSerializer(CustomFieldsSerializer):
    """站点信息序列化器（用于前端获取站点信息）"""
    features = serializers.SerializerMethodField()
    theme_config = serializers.SerializerMethodField()
    
    class Meta:
        model = Site
        fields = [
            'domain', 'name', 'subtitle', 'logo_url', 'favicon_url',
            'seo_title', 'seo_keywords', 'seo_description', 'icp_number',
            'primary_color', 'secondary_color',
            'is_active', 'maintenance_mode', 'maintenance_message',
            'features', 'theme_config'
        ]
    
    def get_features(self, obj):
        return obj.get_feature_status()
    
    def get_theme_config(self, obj):
        if hasattr(obj, 'theme'):
            return SiteThemeSerializer(obj.theme).data
        return None


class SiteStatsSerializer(serializers.Serializer):
    """站点统计数据序列化器"""
    total_page_views = serializers.IntegerField()
    total_unique_visitors = serializers.IntegerField()
    total_registrations = serializers.IntegerField()
    total_case_openings = serializers.IntegerField()
    daily_stats = serializers.ListField(child=serializers.DictField())


class AgentSitesSerializer(CustomFieldsSerializer):
    """代理商站点列表序列化器"""
    sites = SiteListSerializer(many=True, read_only=True)
    
    class Meta:
        model = SiteGroup
        fields = ['id', 'name', 'description', 'sites']
