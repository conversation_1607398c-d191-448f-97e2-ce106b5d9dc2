export interface BannerItem {
  id?: string
  image: string
  title: string
  title_en?: string
  title_zh_hans?: string
  description?: string
  description_en?: string
  description_zh_hans?: string
  link?: string
  type?: number
  is_simple?: boolean
  order?: number
  background_class?: string
  glow_class?: string
  primary_button_text?: string
  primary_button_text_en?: string
  primary_button_text_zh_hans?: string
  primary_button_link?: string
  secondary_button_text?: string
  secondary_button_text_en?: string
  secondary_button_text_zh_hans?: string
  secondary_button_link?: string
  is_active?: boolean
  created_at?: string
  updated_at?: string
}

export interface BannerResponse {
  banners: BannerItem[]
  total: number
} 