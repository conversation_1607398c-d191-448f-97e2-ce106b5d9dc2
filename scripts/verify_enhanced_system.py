#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版对战系统快速验证脚本
简化版本，主要用于快速检查系统状态
"""

import sys
import os

# Django环境设置
sys.path.insert(0, 'server')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')

def main():
    try:
        import django
        django.setup()
        
        # 快速验证模块导入
        from box.enhanced_battle_system import enhanced_battle_manager
        from box.compat_async import get_async_processor
        from box.message_utils import sanitize_websocket_data
        from box.system_fixes import get_system_health
        
        print("✅ 所有核心模块导入成功")
        
        # 快速健康检查
        health = enhanced_battle_manager.get_system_health()
        status = health.get('status', 'unknown')
        
        if status in ['healthy', 'warning']:
            print(f"✅ 系统状态: {status}")
            print("🎉 验证通过！")
            return 0
        else:
            print(f"❌ 系统状态: {status}")
            return 1
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
