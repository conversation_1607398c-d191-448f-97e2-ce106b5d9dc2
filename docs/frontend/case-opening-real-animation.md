# 开箱动画真实性设计方案

## 问题分析

### 当前后端流程的问题：
1. **API立即返回结果** → 用户没有悬念感
2. **WebSocket延迟15秒发送** → 其他用户看到的太晚  
3. **结果已确定** → 动画只是"演戏"，不够真实

### 用户体验目标：
- 开箱过程要有真实的悬念感
- 动画期间用户不知道结果
- 其他用户能实时看到开箱过程
- 结果揭晓的时机要精确控制

## 优化方案

### 方案一：分阶段API设计（推荐）

#### 后端API改进：

```python
# 新增：开始开箱API
@api_view(['POST'])
def start_opening_case(request):
    """开始开箱 - 不立即返回结果"""
    try:
        user = request.user
        case_key = request.data.get('case_key')
        count = request.data.get('count', 1)
        
        # 1. 验证参数和余额
        case = Case.objects.filter(case_key=case_key, enable=True).first()
        if not case:
            return reformat_resp(RespCode.InvalidParams.value, {}, _('Case not found'))
        
        total_cost = case.price * count
        if user.extra.balance < total_cost:
            return reformat_resp(RespCode.InsufficientBalance.value, {}, _('Insufficient balance'))
        
        # 2. 创建开箱会话
        opening_session = CaseOpeningSession.objects.create(
            user=user,
            case=case,
            count=count,
            total_cost=total_cost,
            status='starting',  # starting -> animating -> completed
            animation_duration=5000  # 5秒动画
        )
        
        # 3. 立即发送"开始开箱"WebSocket消息
        opening_data = {
            'opening_id': opening_session.uid,
            'user': {
                'uid': user.uid,
                'profile': {
                    'nick_name': user.steam.personaname,
                    'avatar': user.steam.avatarfull
                }
            },
            'case': CaseSerializer(case).data,
            'count': count,
            'animation_duration': 5000,
            'timestamp': timezone.now().isoformat()
        }
        ws_send_box_game(opening_data, 'start_opening')
        
        # 4. 异步处理开箱逻辑
        process_case_opening.delay(opening_session.uid)
        
        # 5. 返回开箱ID，不返回结果
        return reformat_resp(RespCode.Succeed.value, {
            'opening_id': opening_session.uid,
            'animation_duration': 5000
        }, _('Opening started'))
        
    except Exception as e:
        _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, _('Exception'))

# thworker异步任务：处理开箱逻辑
@shared_task
def process_case_opening(opening_session_id):
    """异步处理开箱逻辑"""
    try:
        session = CaseOpeningSession.objects.get(uid=opening_session_id)
        user = session.user
        case = session.case
        count = session.count
        
        # 1. 更新会话状态
        session.status = 'animating'
        session.save()
        
        # 2. 等待动画时间（让前端播放完动画）
        time.sleep(session.animation_duration / 1000)  # 转换为秒
        
        # 3. 执行真正的开箱逻辑
        user.update_balance(-session.total_cost, 'Case opening')
        
        win_items = []
        for i in range(count):
            # 执行开箱算法
            drop = get_random_drop(case)
            item_info = drop.item_info
            price = get_drop_price(drop)
            
            # 创建物品包
            package = PackageItem.objects.create(
                user=user,
                item_info=item_info,
                assetid='0',
                instanceid='0',
                state=PackageState.Available.value,
                amount=price,
                case_name=case.name,
                case_cover=case.cover,
                case_key=case.case_key
            )
            
            # 创建开箱记录
            record = CaseRecord.objects.create(
                user=user,
                case=case,
                item_info=item_info,
                price=price
            )
            
            item_data = {
                'pid': package.uid,
                'record_id': record.uid,
                'user': {
                    'uid': user.uid,
                    'profile': {
                        'nick_name': user.steam.personaname,
                        'avatar': user.steam.avatarfull
                    }
                },
                'case': CaseSerializer(case).data,
                'item': {
                    'market_name': item_info.market_name,
                    'icon_url': item_info.icon_url,
                    'item_price': {'price': price},
                    'item_rarity': {
                        'rarity_color': item_info.rarity_color
                    }
                },
                'timestamp': timezone.now().isoformat()
            }
            win_items.append(item_data)
        
        # 4. 发送开箱结果WebSocket消息
        result_data = {
            'opening_id': session.uid,
            'user': {
                'uid': user.uid,
                'profile': {
                    'nick_name': user.steam.personaname,
                    'avatar': user.steam.avatarfull
                }
            },
            'case': CaseSerializer(case).data,
            'items': win_items,
            'timestamp': timezone.now().isoformat()
        }
        ws_send_box_game(result_data, 'opening_result')
        
        # 5. 更新会话状态
        session.status = 'completed'
        session.result_data = result_data
        session.save()
        
        # 6. 更新统计数据
        update_base_count('open_base_count', count)
        case.open_count = F('open_count') + count
        case.save()
        
    except Exception as e:
        _logger.exception(f"Opening session {opening_session_id} failed: {e}")
        # 失败时回滚用户余额
        try:
            session = CaseOpeningSession.objects.get(uid=opening_session_id)
            session.status = 'failed'
            session.save()
            # 发送失败消息
            ws_send_box_game({
                'opening_id': opening_session_id,
                'error': 'Opening failed'
            }, 'opening_error')
        except:
            pass

# 新增：开箱会话模型
class CaseOpeningSession(models.Model):
    uid = models.CharField(max_length=36, primary_key=True, default=uuid4_string)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    case = models.ForeignKey(Case, on_delete=models.CASCADE)
    count = models.IntegerField(default=1)
    total_cost = models.FloatField()
    status = models.CharField(max_length=20, default='starting')  # starting, animating, completed, failed
    animation_duration = models.IntegerField(default=5000)  # 毫秒
    result_data = models.JSONField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'case_opening_session'
```

#### 前端实现 (基于GSAP)：

```javascript
class RealCaseOpeningController {
    constructor() {
        this.currentOpenings = new Map(); // 跟踪当前的开箱会话
        this.animationDuration = 5000; // 5秒动画
        this.gsapTimelines = new Map(); // 存储GSAP时间线
        this.audioManager = new CaseAudioManager(); // 音效管理器
        
        // 初始化GSAP插件
        gsap.registerPlugin(TextPlugin, MotionPathPlugin);
    }
    
    // 开始开箱
    async startOpening(caseKey, count = 1) {
        try {
            // 1. 调用开始开箱API
            const response = await this.$api.post('/box/start-opening/', {
                case_key: caseKey,
                count: count
            });
            
            if (response.data.code === 0) {
                const { opening_id, animation_duration } = response.data.body;
                
                // 2. 创建动画容器
                const animationContainer = this.createAnimationContainer(opening_id, caseKey);
                
                // 3. 开始播放开箱动画
                this.playOpeningAnimation(opening_id, caseKey, animation_duration, animationContainer);
                
                // 4. 注册结果监听器
                this.waitForResult(opening_id);
                
                return opening_id;
            } else {
                throw new Error(response.data.message);
            }
        } catch (error) {
            console.error('开箱失败:', error);
            this.showError(error.message);
        }
    }
    
    // 创建动画容器
    createAnimationContainer(openingId, caseKey) {
        const container = document.createElement('div');
        container.className = 'case-opening-container';
        container.id = `opening-${openingId}`;
        
        container.innerHTML = `
            <div class="case-box" data-case="${caseKey}">
                <div class="case-lid"></div>
                <div class="case-body"></div>
                <div class="case-glow"></div>
            </div>
            <div class="opening-effects">
                <div class="particles-container"></div>
                <div class="light-rays"></div>
                <div class="smoke-effect"></div>
            </div>
            <div class="opening-status">
                <div class="status-text">准备开箱...</div>
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
            </div>
            <div class="result-container" style="display: none;">
                <div class="item-showcase"></div>
                <div class="item-details"></div>
            </div>
        `;
        
        document.querySelector('.opening-area').appendChild(container);
        return container;
    }
    
    // 播放开箱动画 (使用GSAP)
    playOpeningAnimation(openingId, caseKey, duration, container) {
        this.currentOpenings.set(openingId, {
            caseKey,
            container,
            startTime: Date.now(),
            duration,
            status: 'animating'
        });
        
        // 创建主时间线
        const mainTimeline = gsap.timeline({
            onComplete: () => {
                // 动画完成后等待结果
                this.showWaitingForResult(openingId);
            }
        });
        
        this.gsapTimelines.set(openingId, mainTimeline);
        
        // 阶段1: 入场动画 (0-0.5秒)
        mainTimeline.add(this.createEntryAnimation(container), 0);
        
        // 阶段2: 箱子晃动 (0.5-2秒)
        mainTimeline.add(this.createShakingAnimation(container), 0.5);
        
        // 阶段3: 箱子打开 (2-3.5秒)
        mainTimeline.add(this.createOpeningAnimation(container), 2);
        
        // 阶段4: 悬念等待 (3.5-5秒)
        mainTimeline.add(this.createSuspenseAnimation(container), 3.5);
        
        // 播放音效
        this.audioManager.playOpeningSequence(openingId, duration);
    }
    
    // 入场动画
    createEntryAnimation(container) {
        const tl = gsap.timeline();
        const caseBox = container.querySelector('.case-box');
        const statusText = container.querySelector('.status-text');
        
        // 箱子从上方滑入
        tl.fromTo(caseBox, 
            { y: -100, opacity: 0, scale: 0.8 },
            { y: 0, opacity: 1, scale: 1, duration: 0.5, ease: "back.out(1.7)" }
        )
        // 状态文字渐入
        .fromTo(statusText,
            { opacity: 0, y: 20 },
            { opacity: 1, y: 0, duration: 0.3, ease: "power2.out" },
            "-=0.2"
        )
        // 添加光环效果
        .fromTo(container.querySelector('.case-glow'),
            { opacity: 0, scale: 0.5 },
            { opacity: 0.6, scale: 1.2, duration: 0.8, ease: "power2.inOut" },
            "-=0.3"
        );
        
        return tl;
    }
    
    // 晃动动画
    createShakingAnimation(container) {
        const tl = gsap.timeline();
        const caseBox = container.querySelector('.case-box');
        const statusText = container.querySelector('.status-text');
        const progressFill = container.querySelector('.progress-fill');
        
        // 更新状态文字
        tl.to(statusText, {
            duration: 0.3,
            text: "正在开箱...",
            ease: "none"
        })
        // 进度条动画
        .to(progressFill, {
            width: "40%",
            duration: 1.5,
            ease: "power2.inOut"
        }, 0)
        // 箱子左右晃动
        .to(caseBox, {
            rotation: 3,
            duration: 0.1,
            yoyo: true,
            repeat: 15,
            ease: "power2.inOut"
        }, 0.3)
        // 箱子上下跳动
        .to(caseBox, {
            y: -5,
            duration: 0.2,
            yoyo: true,
            repeat: 7,
            ease: "power2.inOut"
        }, 0.5)
        // 光晕脉动
        .to(container.querySelector('.case-glow'), {
            opacity: 1,
            scale: 1.5,
            duration: 0.3,
            yoyo: true,
            repeat: 5,
            ease: "power2.inOut"
        }, 0.3);
        
        return tl;
    }
    
    // 开箱动画
    createOpeningAnimation(container) {
        const tl = gsap.timeline();
        const caseLid = container.querySelector('.case-lid');
        const caseBody = container.querySelector('.case-body');
        const statusText = container.querySelector('.status-text');
        const progressFill = container.querySelector('.progress-fill');
        const particlesContainer = container.querySelector('.particles-container');
        
        // 更新状态文字
        tl.to(statusText, {
            duration: 0.2,
            text: "箱子正在打开...",
            ease: "none"
        })
        // 进度条更新
        .to(progressFill, {
            width: "75%",
            duration: 1,
            ease: "power2.out"
        }, 0)
        // 盖子打开
        .to(caseLid, {
            rotationX: -120,
            z: 50,
            duration: 0.8,
            ease: "power2.out",
            transformOrigin: "bottom center"
        }, 0.3)
        // 箱体放大
        .to(caseBody, {
            scale: 1.1,
            duration: 0.6,
            ease: "power2.out"
        }, 0.5)
        // 创建粒子效果
        .add(() => {
            this.createParticleEffect(particlesContainer);
        }, 0.6)
        // 光线效果
        .fromTo(container.querySelector('.light-rays'),
            { opacity: 0, scale: 0.5 },
            { opacity: 0.8, scale: 1.5, duration: 0.8, ease: "power2.out" },
            0.7
        );
        
        return tl;
    }
    
    // 悬念等待动画
    createSuspenseAnimation(container) {
        const tl = gsap.timeline();
        const statusText = container.querySelector('.status-text');
        const progressFill = container.querySelector('.progress-fill');
        const lightRays = container.querySelector('.light-rays');
        
        // 更新状态文字
        tl.to(statusText, {
            duration: 0.3,
            text: "即将揭晓结果...",
            ease: "none"
        })
        // 进度条完成
        .to(progressFill, {
            width: "90%",
            duration: 1,
            ease: "power1.inOut"
        }, 0)
        // 光线旋转
        .to(lightRays, {
            rotation: 360,
            duration: 1.5,
            ease: "none",
            repeat: -1
        }, 0.2)
        // 悬念脉动
        .to(container.querySelector('.case-glow'), {
            opacity: 0.3,
            scale: 2,
            duration: 0.5,
            yoyo: true,
            repeat: -1,
            ease: "power2.inOut"
        }, 0.3);
        
        return tl;
    }
    
    // 创建粒子效果
    createParticleEffect(container) {
        const particleCount = 20;
        const particles = [];
        
        for (let i = 0; i < particleCount; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.cssText = `
                position: absolute;
                width: 4px;
                height: 4px;
                background: ${this.getRandomColor()};
                border-radius: 50%;
                left: 50%;
                top: 50%;
            `;
            container.appendChild(particle);
            particles.push(particle);
            
            // 粒子飞散动画
            gsap.to(particle, {
                x: gsap.utils.random(-150, 150),
                y: gsap.utils.random(-100, -200),
                opacity: 0,
                scale: gsap.utils.random(0.5, 2),
                duration: gsap.utils.random(1, 2),
                ease: "power2.out",
                onComplete: () => particle.remove()
            });
        }
    }
    
    // 获取随机颜色
    getRandomColor() {
        const colors = ['#FFD700', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'];
        return colors[Math.floor(Math.random() * colors.length)];
    }
    
    // 等待结果阶段
    showWaitingForResult(openingId) {
        const opening = this.currentOpenings.get(openingId);
        if (!opening) return;
        
        const statusText = opening.container.querySelector('.status-text');
        const progressFill = opening.container.querySelector('.progress-fill');
        
        // 文字闪烁效果
        gsap.to(statusText, {
            opacity: 0.5,
            duration: 0.5,
            yoyo: true,
            repeat: -1,
            ease: "power2.inOut"
        });
        
        // 进度条最后冲刺
        gsap.to(progressFill, {
            width: "98%",
            duration: 2,
            ease: "power1.out"
        });
        
        opening.status = 'waiting_result';
    }
    
    // 等待开箱结果
    waitForResult(openingId) {
        // 设置WebSocket消息监听器
        this.gameSocket.onOpeningResult = (data) => {
            if (data.opening_id === openingId) {
                this.handleOpeningResult(openingId, data);
            }
        };
        
        this.gameSocket.onOpeningError = (data) => {
            if (data.opening_id === openingId) {
                this.handleOpeningError(openingId, data);
            }
        };
    }
    
    // 处理开箱结果
    handleOpeningResult(openingId, resultData) {
        const opening = this.currentOpenings.get(openingId);
        if (!opening) return;
        
        opening.status = 'completed';
        opening.result = resultData;
        
        // 停止当前动画
        const timeline = this.gsapTimelines.get(openingId);
        if (timeline) {
            timeline.kill();
        }
        
        // 播放结果揭晓动画
        this.showOpeningResult(openingId, resultData.items);
    }
    
    // 结果揭晓动画
    showOpeningResult(openingId, items) {
        const opening = this.currentOpenings.get(openingId);
        if (!opening) return;
        
        // 适配API数据
        const adaptedItems = items.map(item => ApiDataAdapter.adaptItemData(item));
        
        const container = opening.container;
        const resultContainer = container.querySelector('.result-container');
        const itemShowcase = container.querySelector('.item-showcase');
        const progressFill = container.querySelector('.progress-fill');
        const statusText = container.querySelector('.status-text');
        
        // 创建结果时间线
        const resultTimeline = gsap.timeline({
            onComplete: () => {
                setTimeout(() => this.cleanupOpening(openingId), 3000);
            }
        });
        
        // 进度条完成
        resultTimeline.to(progressFill, {
            width: "100%",
            duration: 0.3,
            ease: "power2.out"
        })
        // 状态文字更新
        .to(statusText, {
            text: "开箱完成！",
            duration: 0.2,
            onStart: () => {
                statusText.classList.add('text-green-600');
                statusText.classList.remove('text-gray-700');
            }
        }, 0.1)
        // 结果容器显示
        .fromTo(resultContainer,
            { opacity: 0, scale: 0.8, display: 'block' },
            { opacity: 1, scale: 1, duration: 0.5, ease: "back.out(1.7)" },
            0.3
        );
        
        // 为每个物品创建展示动画
        adaptedItems.forEach((item, index) => {
            const itemElement = this.createItemElement(item);
            itemShowcase.appendChild(itemElement);
            
            // 应用CSS自定义属性
            const cssProps = ApiDataAdapter.generateCSSProperties(item);
            Object.entries(cssProps).forEach(([prop, value]) => {
                itemElement.style.setProperty(prop, value);
            });
            
            // 添加原子化CSS类
            itemElement.classList.add(
                'transform', 'transition-all', 'duration-500',
                'hover:scale-105', 'hover:shadow-xl'
            );
            
            // 物品入场动画
            resultTimeline.fromTo(itemElement,
                { opacity: 0, y: RemConverter.remToPx('3.125rem'), rotationY: 180 },
                { 
                    opacity: 1, 
                    y: 0, 
                    rotationY: 0, 
                    duration: 0.6, 
                    ease: "back.out(1.7)",
                    delay: index * 0.1
                },
                0.5
            );
        });
        
        // 播放成功音效 (基于适配后的数据)
        this.audioManager.playResultReveal(adaptedItems);
        
        // 添加庆祝粒子效果
        resultTimeline.add(() => {
            this.createCelebrationEffect(container, adaptedItems);
        }, 0.8);
    }
    
    // 创建物品元素
    createItemElement(item) {
        const element = document.createElement('div');
        element.className = 'item-element';
        
        // 使用API提供的稀有度颜色
        const rarityColor = item.item_rarity?.rarity_color || '#cccccc';
        element.style.setProperty('--rarity-color', rarityColor);
        
        element.innerHTML = `
            <div class="item-glow"></div>
            <div class="item-image">
                <img src="${item.image}" alt="${item.name}" />
            </div>
            <div class="item-info">
                <div class="item-name">${item.name}</div>
                <div class="item-price">¥${item.item_price?.price || item.price}</div>
                <div class="item-rarity">${item.item_rarity?.rarity_name || '未知'}</div>
            </div>
        `;
        return element;
    }
    
    // 庆祝效果
    createCelebrationEffect(container, items) {
        const celebrationContainer = document.createElement('div');
        celebrationContainer.className = 'celebration-effect';
        container.appendChild(celebrationContainer);
        
        // 根据物品价值决定庆祝强度
        const hasHighValueItem = items.some(item => ApiDataAdapter.isHighValueItem(item));
        const particleCount = hasHighValueItem ? 50 : 30;
        
        // 创建彩带效果
        for (let i = 0; i < particleCount; i++) {
            const confetti = document.createElement('div');
            confetti.className = 'confetti';
            
            // 高价值物品使用金色系，普通物品使用彩色
            const colors = hasHighValueItem 
                ? ['#FFD700', '#FFA500', '#FF8C00', '#DAA520'] 
                : ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'];
                
            confetti.style.cssText = `
                width: ${RemConverter.pxToRem(8)};
                height: ${RemConverter.pxToRem(16)};
                background: ${colors[Math.floor(Math.random() * colors.length)]};
                left: ${Math.random() * 100}%;
                top: ${RemConverter.pxToRem(-20)};
            `;
            
            celebrationContainer.appendChild(confetti);
            
            gsap.to(confetti, {
                y: RemConverter.remToPx('25rem'), // 400px
                rotation: Math.random() * 720,
                duration: gsap.utils.random(2, 4),
                ease: "power2.in",
                onComplete: () => confetti.remove()
            });
        }
        
        // 移除庆祝容器
        setTimeout(() => celebrationContainer.remove(), 5000);
    }
    
    // 处理开箱错误
    handleOpeningError(openingId, errorData) {
        const opening = this.currentOpenings.get(openingId);
        if (!opening) return;
        
        // 停止动画
        const timeline = this.gsapTimelines.get(openingId);
        if (timeline) {
            timeline.kill();
        }
        
        // 显示错误信息
        const statusText = opening.container.querySelector('.status-text');
        gsap.to(statusText, {
            text: "开箱失败",
            color: "#F44336",
            duration: 0.3
        });
        
        // 播放错误音效
        this.audioManager.playError();
        
        // 清理
        setTimeout(() => this.cleanupOpening(openingId), 2000);
    }
    
    // 清理开箱会话
    cleanupOpening(openingId) {
        const opening = this.currentOpenings.get(openingId);
        if (opening) {
            // 移除DOM元素
            gsap.to(opening.container, {
                opacity: 0,
                scale: 0.8,
                duration: 0.5,
                ease: "power2.in",
                onComplete: () => {
                    opening.container.remove();
                }
            });
            
            // 清理数据
            this.currentOpenings.delete(openingId);
            this.gsapTimelines.delete(openingId);
        }
    }
    
    // WebSocket消息处理
    handleWebSocketMessage(message) {
        const { action, data } = JSON.parse(message);
        
        switch (action) {
            case 'opening_start':
                // 其他用户开始开箱
                this.handleOtherUserOpening(data);
                break;
                
            case 'opening_result':
                // 其他用户开箱结果
                if (this.currentOpenings.has(data.opening_id)) {
                    this.handleOpeningResult(data.opening_id, data);
                } else {
                    this.handleOtherUserResult(data);
                }
                break;
        }
    }
    
    // 处理其他用户的开箱
    handleOtherUserOpening(data) {
        // 在全局开箱记录中显示
        this.addToGlobalOpeningList({
            user: data.user,
            case: data.case,
            status: 'opening',
            startTime: Date.now(),
            duration: data.animation_duration
        });
    }
    
    handleOtherUserResult(data) {
        // 更新全局开箱记录
        this.updateGlobalOpeningResult(data);
        
        // 显示在开箱记录流中
        this.addToOpeningStream(data);
    }
}

// 音效管理器
class CaseAudioManager {
    constructor() {
        this.sounds = {
            shake: new Audio('/static/sounds/case-shake.mp3'),
            open: new Audio('/static/sounds/case-open.mp3'),
            suspense: new Audio('/static/sounds/suspense.mp3'),
            reveal: new Audio('/static/sounds/reveal.mp3'),
            success: new Audio('/static/sounds/success.mp3'),
            error: new Audio('/static/sounds/error.mp3'),
            rare_item: new Audio('/static/sounds/rare-item.mp3')
        };
        
        // 预加载音效
        Object.values(this.sounds).forEach(sound => {
            sound.preload = 'auto';
            sound.volume = 0.7;
        });
    }
    
    playOpeningSequence(openingId, duration) {
        // 晃动音效
        setTimeout(() => this.play('shake'), 500);
        
        // 开箱音效
        setTimeout(() => this.play('open'), 2000);
        
        // 悬念音效
        setTimeout(() => this.play('suspense'), 3500);
    }
    
    playResultReveal(items) {
        this.play('reveal');
        
        // 检查是否有稀有物品
        const hasRareItem = items.some(item => {
            const rarityId = item.item_rarity?.rarity_id || 0;
            return rarityId >= 5; // 假设rarity_id >= 5为稀有物品
        });
        
        if (hasRareItem) {
            setTimeout(() => this.play('rare_item'), 500);
        } else {
            setTimeout(() => this.play('success'), 500);
        }
    }
    
    playError() {
        this.play('error');
    }
    
    play(soundName) {
        const sound = this.sounds[soundName];
        if (sound) {
            sound.currentTime = 0;
            sound.play().catch(console.error);
        }
    }
}
```

### GSAP CSS样式配合 (使用rem单位和原子化CSS)

```css
/* CSS变量定义 */
:root {
    /* 基础单位 */
    --base-font-size: 16px;
    
    /* 开箱动画尺寸 (使用rem) */
    --case-width: 12.5rem;         /* 200px */
    --case-height: 12.5rem;        /* 200px */
    --case-lid-height: 1.875rem;   /* 30px */
    --container-width: 18.75rem;   /* 300px */
    --container-height: 25rem;     /* 400px */
    
    /* 间距 */
    --spacing-xs: 0.25rem;   /* 4px */
    --spacing-sm: 0.5rem;    /* 8px */
    --spacing-md: 0.75rem;   /* 12px */
    --spacing-lg: 1rem;      /* 16px */
    --spacing-xl: 1.25rem;   /* 20px */
    --spacing-2xl: 1.5rem;   /* 24px */
    
    /* 动画时间 */
    --duration-fast: 0.2s;
    --duration-normal: 0.3s;
    --duration-slow: 0.5s;
    --duration-slower: 0.8s;
    
    /* 阴影 */
    --shadow-sm: 0 0.125rem 0.25rem rgba(0,0,0,0.075);
    --shadow-md: 0 0.25rem 0.5rem rgba(0,0,0,0.15);
    --shadow-lg: 0 0.5rem 1rem rgba(0,0,0,0.25);
    --shadow-glow: 0 0 1.25rem rgba(255,215,0,0.5);
    
    /* 边框圆角 */
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.625rem;
    --radius-xl: 0.75rem;
    --radius-full: 50%;
}

/* 原子化工具类 */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.text-center { text-align: center; }
.relative { position: relative; }
.absolute { position: absolute; }
.overflow-hidden { overflow: hidden; }
.pointer-events-none { pointer-events: none; }

/* 间距原子类 */
.p-0 { padding: 0; }
.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

.m-0 { margin: 0; }
.m-auto { margin: auto; }
.mx-auto { margin-left: auto; margin-right: auto; }
.mb-xs { margin-bottom: var(--spacing-xs); }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }

/* 尺寸原子类 */
.w-full { width: 100%; }
.h-full { height: 100%; }
.w-80 { width: 80%; }
.h-6 { height: 0.375rem; }

/* 字体原子类 */
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.font-bold { font-weight: 700; }

/* 颜色原子类 */
.text-gray-700 { color: #374151; }
.text-green-600 { color: #059669; }
.text-white { color: #ffffff; }

/* 圆角原子类 */
.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-full { border-radius: var(--radius-full); }

/* 阴影原子类 */
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }

/* 开箱动画容器 */
.case-opening-container {
    @apply relative mx-auto;
    width: var(--container-width);
    height: var(--container-height);
    perspective: 62.5rem; /* 1000px */
}

/* 箱子样式 */
.case-box {
    @apply relative mx-auto;
    width: var(--case-width);
    height: var(--case-height);
    transform-style: preserve-3d;
}

.case-lid {
    @apply absolute rounded-lg shadow-md;
    top: 0;
    left: 0;
    width: 100%;
    height: var(--case-lid-height);
    background: linear-gradient(45deg, #8B4513, #A0522D);
    border: 0.125rem solid #654321; /* 2px */
    border-radius: var(--radius-lg) var(--radius-lg) var(--radius-sm) var(--radius-sm);
    transform-origin: bottom center;
    box-shadow: var(--shadow-lg);
}

.case-body {
    @apply absolute rounded-lg shadow-lg;
    top: 1.75rem; /* 28px */
    left: 0;
    width: 100%;
    height: 10.625rem; /* 170px */
    background: linear-gradient(135deg, #D4AF37, #FFD700, #B8860B);
    border: 0.125rem solid #B8860B;
    border-radius: var(--radius-sm) var(--radius-sm) var(--radius-lg) var(--radius-lg);
    box-shadow: 
        inset 0 0.3125rem 0.9375rem rgba(255,255,255,0.3),
        var(--shadow-lg);
}

.case-body::before {
    content: '';
    @apply absolute rounded-sm;
    top: 1.25rem; /* 20px */
    left: 1.25rem;
    right: 1.25rem;
    bottom: 1.25rem;
    background: linear-gradient(45deg, 
                rgba(255,255,255,0.1) 25%, 
                transparent 25%, 
                transparent 75%, 
                rgba(255,255,255,0.1) 75%);
    background-size: 1.25rem 1.25rem; /* 20px */
    border-radius: var(--radius-sm);
}

/* 光环效果 */
.case-glow {
    @apply absolute rounded-full;
    top: -0.625rem; /* -10px */
    left: -0.625rem;
    right: -0.625rem;
    bottom: -0.625rem;
    background: radial-gradient(circle, 
                rgba(255,215,0,0.6) 0%, 
                rgba(255,215,0,0.2) 50%, 
                transparent 100%);
    animation: glow-pulse 2s ease-in-out infinite alternate;
}

@keyframes glow-pulse {
    0% { 
        box-shadow: var(--shadow-glow);
        transform: scale(1);
    }
    100% { 
        box-shadow: 0 0 2.5rem rgba(255,215,0,0.8);
        transform: scale(1.1);
    }
}

/* 特效容器 */
.opening-effects {
    @apply absolute top-0 left-0 w-full h-full pointer-events-none;
}

.particles-container {
    @apply absolute flex items-center justify-center;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.particle {
    @apply absolute rounded-full;
    filter: blur(0.03125rem); /* 0.5px */
    box-shadow: 0 0 0.375rem currentColor; /* 6px */
}

/* 光线效果 */
.light-rays {
    @apply absolute rounded-full;
    top: 50%;
    left: 50%;
    width: 12.5rem; /* 200px */
    height: 12.5rem;
    transform: translate(-50%, -50%);
    background: conic-gradient(
        from 0deg,
        transparent 0deg,
        rgba(255,255,0,0.3) 45deg,
        transparent 90deg,
        rgba(255,255,0,0.3) 135deg,
        transparent 180deg,
        rgba(255,255,0,0.3) 225deg,
        transparent 270deg,
        rgba(255,255,0,0.3) 315deg,
        transparent 360deg
    );
    filter: blur(0.1875rem); /* 3px */
}

/* 烟雾效果 */
.smoke-effect {
    @apply absolute;
    bottom: 0;
    left: 50%;
    width: 3.125rem; /* 50px */
    height: 6.25rem; /* 100px */
    background: linear-gradient(
        to top,
        rgba(200,200,200,0.6) 0%,
        rgba(200,200,200,0.3) 50%,
        transparent 100%
    );
    transform: translateX(-50%);
    filter: blur(0.5rem); /* 8px */
    animation: smoke-rise 3s ease-out infinite;
}

@keyframes smoke-rise {
    0% {
        opacity: 0;
        transform: translateX(-50%) scale(0.5);
    }
    50% {
        opacity: 1;
        transform: translateX(-50%) scale(1) translateY(-1.25rem);
    }
    100% {
        opacity: 0;
        transform: translateX(-50%) scale(1.5) translateY(-3.75rem);
    }
}

/* 状态显示 */
.opening-status {
    @apply absolute bottom-0 left-0 right-0 text-center text-gray-700;
}

.status-text {
    @apply text-lg font-bold mb-md;
    text-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.3);
}

.progress-bar {
    @apply w-80 h-6 mx-auto rounded-sm overflow-hidden;
    background: rgba(0,0,0,0.2);
}

.progress-fill {
    @apply w-0 h-full rounded-sm;
    background: linear-gradient(90deg, #4CAF50, #8BC34A);
    transition: width var(--duration-normal) ease;
    box-shadow: 0 0 0.625rem rgba(76,175,80,0.5);
}

/* 结果展示 */
.result-container {
    @apply absolute left-0 right-0 text-center;
    top: 13.75rem; /* 220px */
}

.item-showcase {
    @apply flex justify-center flex-wrap mb-xl;
    gap: 0.9375rem; /* 15px */
}

.item-element {
    @apply relative rounded-lg p-lg shadow-lg overflow-hidden;
    width: 7.5rem; /* 120px */
    background: linear-gradient(145deg, #f0f0f0, #d9d9d9);
}

.item-element::before {
    content: '';
    @apply absolute top-0 left-0 right-0;
    height: 0.1875rem; /* 3px */
    background: var(--rarity-color, #cccccc);
}

.item-glow {
    @apply absolute rounded-xl;
    top: -0.3125rem; /* -5px */
    left: -0.3125rem;
    right: -0.3125rem;
    bottom: -0.3125rem;
    background: radial-gradient(circle, var(--rarity-color, #cccccc) 0%, transparent 70%);
    opacity: 0.6;
    z-index: -1;
}

.item-image {
    @apply mx-auto mb-md relative;
    width: 5rem; /* 80px */
    height: 5rem;
}

.item-image img {
    @apply w-full h-full object-contain;
    filter: drop-shadow(0 0.25rem 0.5rem rgba(0,0,0,0.2));
}

.item-info {
    @apply text-center;
}

.item-name {
    @apply text-xs font-bold text-gray-700 mb-xs;
    line-height: 1.2;
}

.item-price {
    @apply text-sm font-bold;
    color: var(--rarity-color, #cccccc);
}

.item-rarity {
    @apply text-xs mt-xs;
    color: var(--rarity-color, #cccccc);
    opacity: 0.8;
}

/* 庆祝效果 */
.celebration-effect {
    @apply absolute top-0 left-0 w-full h-full pointer-events-none overflow-hidden;
}

.confetti {
    @apply absolute;
    border-radius: 0.125rem; /* 2px */
    animation: confetti-fall 3s linear forwards;
}

@keyframes confetti-fall {
    0% {
        transform: translateY(-100vh) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: translateY(100vh) rotate(720deg);
        opacity: 0;
    }
}

/* 响应式设计 */
@media (max-width: 48rem) { /* 768px */
    :root {
        --container-width: 15.625rem; /* 250px */
        --container-height: 21.875rem; /* 350px */
        --case-width: 9.375rem; /* 150px */
        --case-height: 9.375rem; /* 150px */
    }
    
    .item-element {
        width: 6.25rem; /* 100px */
        @apply p-md;
    }
    
    .item-image {
        width: 3.75rem; /* 60px */
        height: 3.75rem;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .item-element {
        background: linear-gradient(145deg, #2a2a2a, #1a1a1a);
    }
    
    .status-text {
        @apply text-white;
    }
}
```

### JavaScript工具函数 (配合API数据结构)

```javascript
// API数据结构适配工具
class ApiDataAdapter {
    
    // 适配开箱结果数据
    static adaptOpeningResult(apiResult) {
        return {
            opening_id: apiResult.opening_id,
            user: apiResult.user,
            case: apiResult.case,
            items: apiResult.items.map(item => this.adaptItemData(item)),
            timestamp: apiResult.timestamp
        };
    }
    
    // 适配物品数据结构
    static adaptItemData(apiItem) {
        return {
            id: apiItem.id,
            name: apiItem.name,
            name_en: apiItem.name_en,
            name_zh_hans: apiItem.name_zh_hans,
            image: apiItem.image,
            item_price: apiItem.item_price,
            item_category: apiItem.item_category,
            item_quality: apiItem.item_quality,
            item_rarity: apiItem.item_rarity,
            item_exterior: apiItem.item_exterior,
            
            // 为了兼容性，也提供简化字段
            price: apiItem.item_price?.price || 0,
            rarity_color: apiItem.item_rarity?.rarity_color || '#cccccc',
            rarity_name: apiItem.item_rarity?.rarity_name || '未知',
            quality_name: apiItem.item_quality?.quality_name || '标准',
            category_name: apiItem.item_category?.category_name || '未知'
        };
    }
    
    // 获取稀有度等级 (用于音效判断)
    static getRarityLevel(item) {
        const rarityId = item.item_rarity?.rarity_id || 0;
        
        if (rarityId >= 8) return 'immortal';      // 不朽
        if (rarityId >= 7) return 'legendary';     // 传说
        if (rarityId >= 6) return 'mythical';      // 神话
        if (rarityId >= 5) return 'epic';          // 史诗
        if (rarityId >= 4) return 'rare';          // 稀有
        if (rarityId >= 3) return 'uncommon';      // 不常见
        return 'common';                           // 普通
    }
    
    // 检查是否为高价值物品
    static isHighValueItem(item) {
        const price = item.item_price?.price || 0;
        const rarityId = item.item_rarity?.rarity_id || 0;
        
        return price >= 100 || rarityId >= 6; // 价格≥100或稀有度≥6
    }
    
    // 生成CSS自定义属性
    static generateCSSProperties(item) {
        return {
            '--rarity-color': item.item_rarity?.rarity_color || '#cccccc',
            '--quality-color': item.item_quality?.quality_color || '#ffffff',
            '--exterior-color': item.item_exterior?.exterior_color || '#888888'
        };
    }
}

// rem单位转换工具
class RemConverter {
    static baseSize = 16; // 基础字体大小(px)
    
    // px转rem
    static pxToRem(px) {
        return `${px / this.baseSize}rem`;
    }
    
    // rem转px
    static remToPx(rem) {
        return parseFloat(rem) * this.baseSize;
    }
    
    // 响应式rem计算
    static responsiveRem(basePx, mobilePx = null) {
        const baseRem = this.pxToRem(basePx);
        if (!mobilePx) return baseRem;
        
        const mobileRem = this.pxToRem(mobilePx);
        return `clamp(${mobileRem}, ${baseRem}, ${baseRem})`;
    }
}

// 原子化CSS工具
class AtomicCSSHelper {
    
    // 生成间距类名
    static spacing(size, property = 'p') {
        const sizeMap = {
            0: '0', 1: 'xs', 2: 'sm', 3: 'md', 
            4: 'lg', 5: 'xl', 6: '2xl'
        };
        return `${property}-${sizeMap[size] || size}`;
    }
    
    // 生成flex类名组合
    static flexCenter() {
        return 'flex items-center justify-center';
    }
    
    // 生成响应式显示类名
    static responsive(base, sm = null, md = null, lg = null) {
        let classes = [base];
        if (sm) classes.push(`sm:${sm}`);
        if (md) classes.push(`md:${md}`);
        if (lg) classes.push(`lg:${lg}`);
        return classes.join(' ');
    }
    
    // 生成动画类名
    static animation(name, duration = 'normal', timing = 'ease-in-out') {
        return `animate-${name} duration-${duration} timing-${timing}`;
    }
}
``````markdown
## 12. 老虎机滚轮式开箱动画方案

### 12.1 设计理念

老虎机滚轮式开箱采用垂直滚动的物品展示形式，通过多个滚轮同步或异步滚动，最终停在目标物品上，营造类似赌场老虎机的紧张刺激感。

### 12.2 UI结构设计

```html
<!-- 老虎机开箱容器 -->
<div class="slot-machine-container">
    <!-- 开箱按钮区域 -->
    <div class="case-info">
        <img src="case-image.jpg" class="case-image" alt="箱子">
        <button class="open-case-btn">开启箱子</button>
        <div class="balance">余额: ¥1,234.56</div>
    </div>
    
    <!-- 滚轮机器主体 -->
    <div class="slot-machine">
        <!-- 外框装饰 -->
        <div class="machine-frame">
            <div class="frame-top"></div>
            <div class="frame-sides"></div>
            <div class="frame-bottom"></div>
        </div>
        
        <!-- 滚轮组 (3-5个滚轮) -->
        <div class="reels-container">
            <div class="reel" data-reel="0">
                <div class="reel-strip">
                    <!-- 动态生成物品列表 -->
                </div>
                <div class="reel-window"></div>
            </div>
            <div class="reel" data-reel="1">
                <div class="reel-strip">
                    <!-- 动态生成物品列表 -->
                </div>
                <div class="reel-window"></div>
            </div>
            <div class="reel" data-reel="2">
                <div class="reel-strip">
                    <!-- 动态生成物品列表 -->
                </div>
                <div class="reel-window"></div>
            </div>
        </div>
        
        <!-- 中奖线 -->
        <div class="win-line"></div>
        
        <!-- 结果展示区 -->
        <div class="result-display">
            <div class="winning-item">
                <img src="" alt="" class="item-image">
                <div class="item-info">
                    <span class="item-name"></span>
                    <span class="item-value"></span>
                    <span class="item-rarity"></span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 音效控制 -->
    <div class="audio-controls">
        <button class="mute-toggle">🔊</button>
    </div>
</div>
```

### 12.3 CSS样式设计

```scss
// 老虎机容器
.slot-machine-container {
    width: 100%;
    max-width: 50rem;
    margin: 0 auto;
    padding: 1rem;
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    border-radius: 1rem;
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.5);
    
    // 响应式设计
    @media (max-width: 768px) {
        max-width: 95vw;
        padding: 0.5rem;
    }
}

// 箱子信息区
.case-info {
    text-align: center;
    margin-bottom: 2rem;
    
    .case-image {
        width: 8rem;
        height: 8rem;
        object-fit: cover;
        border-radius: 0.5rem;
        margin-bottom: 1rem;
        transition: transform 0.3s ease;
        
        &:hover {
            transform: scale(1.05);
        }
    }
    
    .open-case-btn {
        padding: 0.75rem 2rem;
        font-size: 1.1rem;
        font-weight: bold;
        background: linear-gradient(45deg, #ff6b6b, #feca57);
        border: none;
        border-radius: 2rem;
        color: white;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-bottom: 0.5rem;
        
        &:hover {
            transform: translateY(-0.2rem);
            box-shadow: 0 0.5rem 1rem rgba(255, 107, 107, 0.3);
        }
        
        &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
    }
    
    .balance {
        color: #ffd700;
        font-size: 0.9rem;
    }
}

// 老虎机主体
.slot-machine {
    position: relative;
    background: linear-gradient(145deg, #2c3e50, #34495e);
    border-radius: 1rem;
    padding: 2rem 1rem;
    box-shadow: inset 0 0.5rem 1rem rgba(0, 0, 0, 0.3);
    
    // 机器外框
    .machine-frame {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border: 0.3rem solid #ffd700;
        border-radius: 1rem;
        pointer-events: none;
        
        &::before {
            content: '';
            position: absolute;
            top: -0.5rem;
            left: -0.5rem;
            right: -0.5rem;
            bottom: -0.5rem;
            border: 0.2rem solid rgba(255, 215, 0, 0.3);
            border-radius: 1.2rem;
        }
    }
}

// 滚轮容器
.reels-container {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 1rem;
    
    @media (max-width: 768px) {
        gap: 0.5rem;
    }
}

// 单个滚轮
.reel {
    position: relative;
    width: 8rem;
    height: 15rem;
    background: #000;
    border: 0.2rem solid #333;
    border-radius: 0.5rem;
    overflow: hidden;
    
    @media (max-width: 768px) {
        width: 6rem;
        height: 12rem;
    }
    
    // 滚轮带
    .reel-strip {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        display: flex;
        flex-direction: column;
        transform: translateY(0);
        
        .reel-item {
            width: 100%;
            height: 5rem;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border-bottom: 0.1rem solid #333;
            padding: 0.5rem;
            transition: all 0.3s ease;
            
            @media (max-width: 768px) {
                height: 4rem;
                padding: 0.25rem;
            }
            
            .item-image {
                width: 3rem;
                height: 3rem;
                object-fit: cover;
                border-radius: 0.25rem;
                margin-bottom: 0.25rem;
                
                @media (max-width: 768px) {
                    width: 2.5rem;
                    height: 2.5rem;
                }
            }
            
            .item-name {
                font-size: 0.7rem;
                color: white;
                text-align: center;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                width: 100%;
                
                @media (max-width: 768px) {
                    font-size: 0.6rem;
                }
            }
            
            // 稀有度颜色
            &.rarity-normal { border-left: 0.2rem solid #b0b0b0; }
            &.rarity-unusual { border-left: 0.2rem solid #8847ff; }
            &.rarity-rare { border-left: 0.2rem solid #4b69ff; }
            &.rarity-mythical { border-left: 0.2rem solid #8650ac; }
            &.rarity-legendary { border-left: 0.2rem solid #d2d2d2; }
            &.rarity-ancient { border-left: 0.2rem solid #eb4b4b; }
            &.rarity-immortal { border-left: 0.2rem solid #e4ae39; }
        }
    }
    
    // 显示窗口
    .reel-window {
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 5rem;
        transform: translateY(-50%);
        border: 0.2rem solid #ffd700;
        border-radius: 0.25rem;
        pointer-events: none;
        
        @media (max-width: 768px) {
            height: 4rem;
        }
        
        &::before, &::after {
            content: '';
            position: absolute;
            left: -0.5rem;
            right: -0.5rem;
            height: 0.1rem;
            background: linear-gradient(90deg, transparent, #ffd700, transparent);
        }
        
        &::before { top: -0.1rem; }
        &::after { bottom: -0.1rem; }
    }
}

// 中奖线
.win-line {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 0.2rem;
    background: linear-gradient(90deg, transparent, #ff0000, transparent);
    transform: translateY(-50%);
    opacity: 0;
    animation: pulseLine 1s ease-in-out infinite;
    
    &.active {
        opacity: 1;
    }
}

@keyframes pulseLine {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

// 结果展示
.result-display {
    text-align: center;
    padding: 1rem;
    opacity: 0;
    transform: translateY(2rem);
    transition: all 0.5s ease;
    
    &.show {
        opacity: 1;
        transform: translateY(0);
    }
    
    .winning-item {
        display: inline-flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem;
        background: linear-gradient(45deg, #1a1a2e, #16213e);
        border-radius: 0.5rem;
        border: 0.1rem solid #ffd700;
        
        .item-image {
            width: 4rem;
            height: 4rem;
            object-fit: cover;
            border-radius: 0.25rem;
        }
        
        .item-info {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            
            .item-name {
                font-size: 1.1rem;
                font-weight: bold;
                color: white;
                margin-bottom: 0.25rem;
            }
            
            .item-value {
                font-size: 1rem;
                color: #ffd700;
                margin-bottom: 0.25rem;
            }
            
            .item-rarity {
                font-size: 0.8rem;
                padding: 0.2rem 0.5rem;
                border-radius: 1rem;
                font-weight: bold;
            }
        }
    }
}
```

### 12.4 JavaScript动画控制器

```javascript
class SlotMachineAnimation {
    constructor(container, options = {}) {
        this.container = container;
        this.options = {
            reelsCount: 3,
            itemsPerReel: 20,
            spinDuration: 3000,
            maxSpinSpeed: 2000,
            easeType: "power4.out",
            staggerDelay: 300,
            ...options
        };
        
        this.reels = [];
        this.isSpinning = false;
        this.audioManager = new AudioManager();
        this.resultItem = null;
        
        this.init();
    }
    
    init() {
        this.setupReels();
        this.bindEvents();
        this.preloadSounds();
    }
    
    // 设置滚轮
    setupReels() {
        const reelsContainer = this.container.querySelector('.reels-container');
        
        for (let i = 0; i < this.options.reelsCount; i++) {
            const reel = this.createReel(i);
            reelsContainer.appendChild(reel);
            this.reels.push(reel);
        }
    }
    
    // 创建单个滚轮
    createReel(index) {
        const reel = document.createElement('div');
        reel.className = 'reel';
        reel.dataset.reel = index;
        
        const reelStrip = document.createElement('div');
        reelStrip.className = 'reel-strip';
        
        const reelWindow = document.createElement('div');
        reelWindow.className = 'reel-window';
        
        reel.appendChild(reelStrip);
        reel.appendChild(reelWindow);
        
        return reel;
    }
    
    // 填充滚轮物品
    async populateReels(items) {
        this.reels.forEach((reel, reelIndex) => {
            const reelStrip = reel.querySelector('.reel-strip');
            reelStrip.innerHTML = '';
            
            // 为每个滚轮生成随机物品序列
            const reelItems = this.generateReelItems(items, this.options.itemsPerReel);
            
            reelItems.forEach((item, itemIndex) => {
                const itemElement = this.createReelItem(item, itemIndex);
                reelStrip.appendChild(itemElement);
            });
        });
    }
    
    // 生成滚轮物品序列
    generateReelItems(items, count) {
        const reelItems = [];
        
        // 确保目标物品在适当位置
        for (let i = 0; i < count; i++) {
            const randomItem = items[Math.floor(Math.random() * items.length)];
            reelItems.push(randomItem);
        }
        
        return reelItems;
    }
    
    // 创建滚轮物品元素
    createReelItem(item, index) {
        const itemElement = document.createElement('div');
        itemElement.className = `reel-item rarity-${item.rarity}`;
        itemElement.dataset.index = index;
        
        itemElement.innerHTML = `
            <img src="${item.image}" alt="${item.name}" class="item-image">
            <span class="item-name">${item.name}</span>
        `;
        
        return itemElement;
    }
    
    // 开始旋转动画
    async startSpin(caseId) {
        if (this.isSpinning) return;
        
        this.isSpinning = true;
        this.hideResult();
        
        try {
            // 1. 获取箱子物品列表
            const caseItems = await this.fetchCaseItems(caseId);
            await this.populateReels(caseItems);
            
            // 2. 启动开箱请求
            const openingPromise = this.startCaseOpening(caseId);
            
            // 3. 开始滚轮动画
            await this.animateReels();
            
            // 4. 等待开箱结果
            const result = await openingPromise;
            this.resultItem = result;
            
            // 5. 停止在目标物品
            await this.stopAtResult(result);
            
            // 6. 显示结果
            await this.showResult(result);
            
        } catch (error) {
            console.error('开箱动画错误:', error);
            this.handleError(error);
        } finally {
            this.isSpinning = false;
        }
    }
    
    // 滚轮动画
    async animateReels() {
        const timeline = gsap.timeline();
        
        // 播放开始音效
        this.audioManager.play('spin_start');
        
        this.reels.forEach((reel, index) => {
            const reelStrip = reel.querySelector('.reel-strip');
            const staggerDelay = index * this.options.staggerDelay / 1000;
            
            // 滚轮开始旋转
            timeline.to(reelStrip, {
                y: -this.options.maxSpinSpeed,
                duration: this.options.spinDuration / 1000,
                ease: "none",
                repeat: -1,
                delay: staggerDelay
            }, 0);
            
            // 播放滚动音效
            setTimeout(() => {
                this.audioManager.play('reel_spin', { loop: true });
            }, staggerDelay * 1000);
        });
        
        return timeline;
    }
    
    // 停止在结果物品
    async stopAtResult(result) {
        const stopPromises = this.reels.map((reel, index) => {
            return new Promise(resolve => {
                const reelStrip = reel.querySelector('.reel-strip');
                const delay = index * this.options.staggerDelay;
                
                setTimeout(() => {
                    // 停止循环动画
                    gsap.killTweensOf(reelStrip);
                    this.audioManager.stop('reel_spin');
                    
                    // 计算目标位置
                    const targetPosition = this.calculateTargetPosition(reel, result);
                    
                    // 缓动到目标位置
                    gsap.to(reelStrip, {
                        y: targetPosition,
                        duration: 1.5,
                        ease: this.options.easeType,
                        onComplete: () => {
                            // 播放停止音效
                            this.audioManager.play('reel_stop');
                            resolve();
                        }
                    });
                }, delay);
            });
        });
        
        await Promise.all(stopPromises);
        
        // 激活中奖线
        this.activateWinLine();
        this.audioManager.play('win_line');
    }
    
    // 计算目标停止位置
    calculateTargetPosition(reel, result) {
        const reelStrip = reel.querySelector('.reel-strip');
        const items = reelStrip.querySelectorAll('.reel-item');
        const itemHeight = items[0]?.offsetHeight || 80;
        const windowCenter = reel.offsetHeight / 2;
        
        // 找到匹配的物品或使用中间位置
        let targetIndex = Math.floor(items.length / 2);
        
        for (let i = 0; i < items.length; i++) {
            const item = items[i];
            const img = item.querySelector('.item-image');
            if (img && img.src.includes(result.image_name)) {
                targetIndex = i;
                break;
            }
        }
        
        // 计算使目标物品居中的位置
        return -(targetIndex * itemHeight - windowCenter + itemHeight / 2);
    }
    
    // 激活中奖线
    activateWinLine() {
        const winLine = this.container.querySelector('.win-line');
        winLine.classList.add('active');
        
        setTimeout(() => {
            winLine.classList.remove('active');
        }, 2000);
    }
    
    // 显示结果
    async showResult(result) {
        const resultDisplay = this.container.querySelector('.result-display');
        const winningItem = resultDisplay.querySelector('.winning-item');
        
        // 更新结果信息
        const itemImage = winningItem.querySelector('.item-image');
        const itemName = winningItem.querySelector('.item-name');
        const itemValue = winningItem.querySelector('.item-value');
        const itemRarity = winningItem.querySelector('.item-rarity');
        
        itemImage.src = result.image;
        itemImage.alt = result.name;
        itemName.textContent = result.name;
        itemValue.textContent = `¥${result.price}`;
        itemRarity.textContent = result.rarity_display;
        itemRarity.className = `item-rarity rarity-${result.rarity}`;
        
        // 显示动画
        resultDisplay.classList.add('show');
        
        // 播放胜利音效
        this.audioManager.play('win_reveal');
        
        // 粒子效果
        this.createWinParticles();
    }
    
    // 创建胜利粒子效果
    createWinParticles() {
        const particlesContainer = document.createElement('div');
        particlesContainer.className = 'win-particles';
        this.container.appendChild(particlesContainer);
        
        for (let i = 0; i < 20; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.cssText = `
                position: absolute;
                width: 0.5rem;
                height: 0.5rem;
                background: #ffd700;
                border-radius: 50%;
                top: 50%;
                left: 50%;
            `;
            
            particlesContainer.appendChild(particle);
            
            // 粒子动画
            gsap.to(particle, {
                x: (Math.random() - 0.5) * 400,
                y: (Math.random() - 0.5) * 400,
                rotation: Math.random() * 360,
                scale: Math.random() * 2 + 0.5,
                opacity: 0,
                duration: 2,
                ease: "power2.out",
                onComplete: () => particle.remove()
            });
        }
        
        setTimeout(() => {
            particlesContainer.remove();
        }, 3000);
    }
    
    // API调用方法
    async fetchCaseItems(caseId) {
        const response = await fetch(`/api/box/case/${caseId}/items/`);
        if (!response.ok) throw new Error('获取箱子物品失败');
        const data = await response.json();
        return data.items || [];
    }
    
    async startCaseOpening(caseId) {
        const response = await fetch('/api/box/case/open/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': this.getCSRFToken()
            },
            body: JSON.stringify({ case_id: caseId })
        });
        
        if (!response.ok) throw new Error('开箱请求失败');
        const data = await response.json();
        return data.item;
    }
    
    // 工具方法
    hideResult() {
        const resultDisplay = this.container.querySelector('.result-display');
        resultDisplay.classList.remove('show');
    }
    
    handleError(error) {
        alert(`开箱失败: ${error.message}`);
        // 重置UI状态
        this.reels.forEach(reel => {
            const reelStrip = reel.querySelector('.reel-strip');
            gsap.killTweensOf(reelStrip);
        });
        this.audioManager.stopAll();
    }
    
    getCSRFToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]')?.value || '';
    }
    
    preloadSounds() {
        const sounds = ['spin_start', 'reel_spin', 'reel_stop', 'win_line', 'win_reveal'];
        sounds.forEach(sound => this.audioManager.preload(sound));
    }
    
    bindEvents() {
        const openButton = this.container.querySelector('.open-case-btn');
        openButton.addEventListener('click', () => {
            if (!this.isSpinning) {
                const caseId = this.container.dataset.caseId;
                this.startSpin(caseId);
            }
        });
    }
}
```

### 12.5 音效管理器

```javascript
class AudioManager {
    constructor() {
        this.sounds = {};
        this.volume = 0.7;
        this.muted = false;
        
        this.soundUrls = {
            spin_start: '/static/audio/slot_spin_start.mp3',
            reel_spin: '/static/audio/slot_reel_spin.mp3',
            reel_stop: '/static/audio/slot_reel_stop.mp3',
            win_line: '/static/audio/slot_win_line.mp3',
            win_reveal: '/static/audio/slot_win_reveal.mp3'
        };
    }
    
    preload(soundName) {
        if (this.sounds[soundName]) return;
        
        const audio = new Audio(this.soundUrls[soundName]);
        audio.volume = this.volume;
        audio.preload = 'auto';
        this.sounds[soundName] = audio;
    }
    
    play(soundName, options = {}) {
        if (this.muted) return;
        
        const sound = this.sounds[soundName];
        if (!sound) return;
        
        sound.currentTime = 0;
        sound.loop = options.loop || false;
        sound.play().catch(console.error);
        
        return sound;
    }
    
    stop(soundName) {
        const sound = this.sounds[soundName];
        if (sound) {
            sound.pause();
            sound.currentTime = 0;
                       sound.loop = false;
        }
    }
    
    stopAll() {
        Object.values(this.sounds).forEach(sound => {
            sound.pause();
            sound.currentTime = 0;
            sound.loop = false;
        });
    }
    
    setVolume(volume) {
        this.volume = Math.max(0, Math.min(1, volume));
        Object.values(this.sounds).forEach(sound => {
            sound.volume = this.volume;
        });
    }
    
    toggleMute() {
        this.muted = !this.muted;
        return this.muted;
    }
}
```

### 12.6 使用示例

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>老虎机开箱动画</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <link rel="stylesheet" href="slot-machine.css">
</head>
<body>
    <div class="slot-machine-container" data-case-id="123">
        <!-- 老虎机HTML结构 -->
    </div>
    
    <script src="slot-machine.js"></script>
    <script>
        // 初始化老虎机动画
        document.addEventListener('DOMContentLoaded', () => {
            const container = document.querySelector('.slot-machine-container');
            const slotMachine = new SlotMachineAnimation(container, {
                reelsCount: 3,
                spinDuration: 4000,
                staggerDelay: 500
            });
        });
    </script>
</body>
</html>
```

### 12.7 后端API适配

老虎机动画需要以下API支持：

#### 12.7.1 获取箱子物品列表
```python
# views.py
@api_view(['GET'])
def get_case_items(request, case_id):
    """获取箱子内所有可能的物品用于滚轮显示"""
    try:
        case = Case.objects.get(id=case_id)
        items = case.case_items.all().select_related('item__item_rarity')
        
        items_data = []
        for case_item in items:
            items_data.append({
                'id': case_item.item.id,
                'name': case_item.item.name,
                'image': case_item.item.image.url if case_item.item.image else '',
                'image_name': case_item.item.image.name if case_item.item.image else '',
                'rarity': case_item.item.item_rarity.name,
                'rarity_display': case_item.item.item_rarity.display_name,
                'rarity_color': case_item.item.item_rarity.rarity_color,
                'price': float(case_item.item.price),
                'probability': float(case_item.probability)
            })
        
        return Response({
            'success': True,
            'items': items_data
        })
        
    except Case.DoesNotExist:
        return Response({
            'success': False,
            'message': '箱子不存在'
        }, status=404)
```

#### 12.7.2 URLs配置
```python
# urls.py
urlpatterns = [
    # ...existing code...
    path('case/<int:case_id>/items/', views.get_case_items, name='get_case_items'),
    # ...existing code...
]
```

### 12.8 性能优化建议

1. **预加载优化**
   - 提前加载音频文件
   - 预渲染物品图片
   - 使用 Web Workers 处理复杂计算

2. **动画性能**
   - 使用 CSS transform 而非改变位置属性
   - 启用硬件加速 (`will-change: transform`)
   - 控制同时运行的动画数量

3. **内存管理**
   - 及时清理动画对象
   - 复用DOM元素
   - 限制粒子效果数量

4. **响应式适配**
   - 移动端简化动画效果
   - 根据设备性能调整动画质量
   - 提供动画关闭选项

### 12.9 扩展功能

1. **多重中奖**
   - 支持同时显示多个获得物品
   - 连击效果动画

2. **累积奖池**
   - 显示当前奖池金额
   - 大奖特殊动画效果

3. **社交分享**
   - 截图分享获得的物品
   - 动画录制功能

4. **个性化设置**
   - 音效开关
   - 动画速度调节
   - 主题皮肤选择

这个老虎机滚轮式方案相比传统开箱动画更具娱乐性和视觉冲击力，通过多滚轮异步停止营造紧张感，结合音效和粒子效果提升用户体验。
