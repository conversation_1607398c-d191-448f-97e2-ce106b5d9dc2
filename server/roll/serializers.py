import logging
from rest_framework import serializers
from steambase import settings
from django.core.cache import cache
from package.service.item import get_item_price
from steambase.enums import CaseItemType
from steambase.serializers import CustomFieldsSerializer
from steambase.utils import aware_datetime_to_timestamp, get_serializer_cache_key, get_serializer_cache_key_ectype
from roll.models import Roll<PERSON><PERSON>, RollRoomBet, RollRoomItem
from package.serializers import PackageItemSerializer, ItemInfoSerializer
from authentication.serializers import SmallAuthUserSerializer
from package.models import ItemInfo

_STEAM_IMG_BASE = 'https://steamcommunity-a.akamaihd.net/economy/image/{icon_url}/'


_logger = logging.getLogger(__name__)

class RollRoomItemSerializer(serializers.ModelSerializer):
    market_name_cn = serializers.SerializerMethodField()
    market_name = serializers.SerializerMethodField()
    icon_url = serializers.SerializerMethodField()
    rarity_color = serializers.SerializerMethodField()

    class Meta:
        model = RollRoomItem
        fields = ['id', 'item_id', 'price', 'balance', 'market_name_cn', 'market_name', 'icon_url', 'rarity_color']

    def get_market_name_cn(self, obj):
        if obj.item_id:
            try:
                # 根据 item_id 查找 ItemInfo
                item_info = ItemInfo.objects.get(id=obj.item_id)
                return item_info.market_name_cn or item_info.market_name
            except ItemInfo.DoesNotExist:
                return None  # 如果没有找到，返回空
        return None  # 如果没有 item_id，返回空
    def get_market_name(self, obj):
        if obj.item_id:
            try:
                # 根据 item_id 查找 ItemInfo
                item_info = ItemInfo.objects.get(id=obj.item_id)
                return item_info.market_name
            except ItemInfo.DoesNotExist:
                return None  # 如果没有找到，返回空
        return None  # 如果没有 item_id，返回空
    def get_icon_url(self, obj):
        if obj.item_id:
            try:
                # 根据 item_id 查找 ItemInfo
                item_info = ItemInfo.objects.get(id=obj.item_id)
                
                # 尝试获取 icon_url 或 icon_url_large
                icon_url = getattr(item_info, 'icon_url', None) or getattr(item_info, 'icon_url_large', None)

                if icon_url:
                    # 检查 URL 格式是否正确，并修正
                    if "/" in icon_url:
                        icon_url = icon_url.split("/")[0]  # 截取 URL 前缀部分

                    # 返回格式化的 URL
                    return _STEAM_IMG_BASE.format(icon_url=icon_url)  # 格式化 URL 如果有值
                
                return None  # 如果没有 icon_url 或 icon_url_large，则返回 None

            except ItemInfo.DoesNotExist:
                return None  # 如果没有找到，返回空
        return None  # 如果没有 item_id，返回空
    
    def get_rarity_color(self, obj):
        if obj.item_id:
            try:
                # 根据 item_id 查找 ItemInfo
                item_info = ItemInfo.objects.get(id=obj.item_id)
                return item_info.rarity_color
            except ItemInfo.DoesNotExist:
                return None  # 如果没有找到，返回空
        return None  # 如果没有 item_id，返回空 




class RollRoomBetSerializer(CustomFieldsSerializer):
    user = SmallAuthUserSerializer(read_only=True)
    win_items = RollRoomItemSerializer(many=True, read_only=True)
    items_preview = serializers.SerializerMethodField()

    class Meta:
        model = RollRoomBet
        fields = '__all__'

    def get_items_preview(self, obj):
        items_preview_count = self.context.get('items_preview_count', 4)
        if items_preview_count:
            items = obj.win_items.all()[:items_preview_count]
            items_data = RollRoomItemSerializer(items, many=True).data
            return items_data
        return []

class RollRoomWinnerSerializer(CustomFieldsSerializer):
    package = PackageItemSerializer(read_only=True, fields=('bet_id', 
        'market_name_cn', 'market_name', 'icon_url', 'price', 'part', 'amount', 'rarity_color'))
    price = serializers.SerializerMethodField()

    user = SmallAuthUserSerializer(read_only=True)





    class Meta:
        model = RollRoomItem
        fields = '__all__'

    def get_price(self, obj):
        # 通过 item_id 获取价格
        if not obj.item_id:
            return None  # 如果没有 item_id，则返回 None
        try:
            # 从 ItemInfo 获取相关价格
            item_info = ItemInfo.objects.get(id=obj.item_id)
            return item_info.item_price.price if hasattr(item_info, 'item_price') else None
        except ItemInfo.DoesNotExist:
            _logger.warning(f"ItemInfo 不存在，item_id: {obj.item_id}")
            return None


class RollRoomSerializer(CustomFieldsSerializer):
    user = SmallAuthUserSerializer(read_only=True)
    items = RollRoomItemSerializer(many=True, read_only=True)
    bets = RollRoomBetSerializer(many=True, read_only=True)
    winners = serializers.SerializerMethodField()
    joiners_count = serializers.SerializerMethodField()
    has_password = serializers.SerializerMethodField()
    begin_ts_val = serializers.SerializerMethodField()
    due_ts_val = serializers.SerializerMethodField()
    is_creator = serializers.SerializerMethodField()
    is_joiner = serializers.SerializerMethodField()
    items_preview = serializers.SerializerMethodField()
    official = serializers.SerializerMethodField()

    class Meta:
        model = RollRoom
        fields = '__all__'

    def get_winners(self, obj):
        winner = obj.bets.filter(victory=1)
        return RollRoomBetSerializer(winner, many=True).data

    def get_joiners_count(self, obj):
        return obj.bets.count()

    def get_has_password(self, obj):
        if obj.password:
            return True
        return False

    def get_begin_ts_val(self, obj):
        return aware_datetime_to_timestamp(obj.begin_ts)

    def get_due_ts_val(self, obj):
        return aware_datetime_to_timestamp(obj.due_ts)

    def get_is_creator(self, obj):
        user = self.context.get('user', None)
        if obj.user == user:
            return True
        return False

    def get_is_joiner(self, obj):
        user = self.context.get('user', None)
        if obj.bets.filter(user=user):
            return True
        return False

    def get_items_preview(self, obj):
        items_preview_count = self.context.get('items_preview_count', None)
        if items_preview_count:
            items = obj.items.order_by('-price').all()[:items_preview_count]

            # 使用新的 RollRoomItemSerializer，确保不依赖 package
            items_data = RollRoomItemSerializer(items, many=True).data
            return items_data
        return []

    def get_official(self, obj):
        language = self.context.get("language", "")
        
        # 如果 official 为 0，根据语言返回对应的字符串
        if obj.official == 0:
            if "zh-hans" in language:
                return "主播"  # STREAMER
            elif "en" in language:
                return "STREAMER"
        
        # 如果 official 为 1，根据语言返回对应的字符串
        if obj.official == 1:
            if "zh-hans" in language:
                return "官方"  # OFFICIAL
            elif "en" in language:
                return "OFFICIAL"
        
        # 默认返回值
        return "OFFICIAL"


class RollRoomCacheSerializer(CustomFieldsSerializer):
    user = SmallAuthUserSerializer(read_only=True)
    items = RollRoomItemSerializer(many=True, read_only=True)
    bets = RollRoomBetSerializer(many=True, read_only=True)
    winners = serializers.SerializerMethodField()
    joiners_count = serializers.SerializerMethodField()
    has_password = serializers.SerializerMethodField()
    begin_ts_val = serializers.SerializerMethodField()
    due_ts_val = serializers.SerializerMethodField()
    is_creator = serializers.SerializerMethodField()
    is_joiner = serializers.SerializerMethodField()
    items_preview = serializers.SerializerMethodField()
    official = serializers.SerializerMethodField()

    class Meta:
        model = RollRoom
        fields = '__all__'

    def get_winners(self, obj):
        winner = obj.bets.filter(victory=1)
        return RollRoomBetSerializer(winner, many=True).data

    def get_joiners_count(self, obj):
        return obj.bets.count()

    def get_has_password(self, obj):
        if obj.password:
            return True
        return False

    def get_begin_ts_val(self, obj):
        return aware_datetime_to_timestamp(obj.begin_ts)

    def get_due_ts_val(self, obj):
        return aware_datetime_to_timestamp(obj.due_ts)

    def get_is_creator(self, obj):
        user = self.context.get('user', None)
        if obj.user == user:
            return True
        return False

    def get_is_joiner(self, obj):
        user = self.context.get('user', None)
        if obj.bets.filter(user=user):
            return True
        return False

    def get_items_preview(self, obj):
        items_preview_count = self.context.get('items_preview_count', None)
        if items_preview_count:
            #items = obj.items.order_by('-price').all()[:items_preview_count]
            items = obj.items.order_by('-price').all()[:4]
            items_data = RollRoomItemSerializer(items, many=True).data
            return items_data
        return []

    def get_official(self, obj):
        language = self.context.get("language", "")
        
        # 如果 official 为 0，根据语言返回对应的字符串
        if obj.official == 0:
            if "zh-hans" in language:
                return "主播"  # STREAMER
            elif "en" in language:
                return "STREAMER"
        
        # 如果 official 为 1，根据语言返回对应的字符串
        if obj.official == 1:
            if "zh-hans" in language:
                return "官方"  # OFFICIAL
            elif "en" in language:
                return "OFFICIAL"
        
        # 默认返回值
        return "OFFICIAL"

    def to_representation(self, instance):
        key = get_serializer_cache_key_ectype(instance, self.__class__)
        cached = cache.get(key)
        language = self.context.get("language", "")  # 安全地获取 language

        if cached:
            if cached["official"] in ["OFFICIAL", "官方"]:
                if "zh-hans" in language:
                    cached["official"] = "官方"
                elif "en" in language:
                    cached["official"] = "OFFICIAL"

            elif cached["official"] in ["STREAMER", "主播"]:
                if "zh-hans" in language:
                    cached["official"] = "主播"
                elif "en" in language:
                    cached["official"] = "STREAMER"

            return cached

        # 如果缓存不存在，则生成新的表示并缓存
        result = super(RollRoomCacheSerializer, self).to_representation(instance)
        cache.set(key, result, settings.DAY_REDIS_TIMEOUT)
        return result