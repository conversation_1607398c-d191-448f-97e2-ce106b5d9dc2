# -*- coding: utf-8 -*-
# Generated by Django 1.11.29 on 2025-04-28 15:35
from __future__ import unicode_literals

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('box', '0032_case_tag_color'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='case',
            options={'ordering': ('case_type', 'sort'), 'verbose_name': 'Case', 'verbose_name_plural': 'Case'},
        ),
        migrations.AlterModelOptions(
            name='casecategory',
            options={'ordering': ('sort',), 'verbose_name': 'Case Category', 'verbose_name_plural': 'Case Category'},
        ),
        migrations.AlterModelOptions(
            name='casetype',
            options={'ordering': ('sort',), 'verbose_name': 'Case Type', 'verbose_name_plural': 'Case Type'},
        ),
        migrations.RenameField(
            model_name='case',
            old_name='order',
            new_name='sort',
        ),
        migrations.<PERSON>ameField(
            model_name='casecategory',
            old_name='order',
            new_name='sort',
        ),
        migrations.RenameField(
            model_name='casetype',
            old_name='order',
            new_name='sort',
        ),
    ]
