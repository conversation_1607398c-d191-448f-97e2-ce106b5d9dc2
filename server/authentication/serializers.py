import re
import pytz
from rest_framework import serializers
from django.core.cache import cache
from steambase import settings
from steambase.serializers import CustomFieldsSerializer
from authentication.models import AuthUser, UserAsset, UserSteam, UserExtra, UserProfile, UserStatisticsDay, UserBalanceRecord
from promotion.models import PromotionRecord
from steambase.utils import get_serializer_cache_key


class UserProfileSerializer(CustomFieldsSerializer):
    avatar = serializers.SerializerMethodField()

    def get_avatar(self, obj):
        if obj.user.profile.avatar:
            avatar_url = obj.user.profile.avatar.name  # 使用 name 而不是 url
            # 确保 avatar_url 是字符串
            avatar_url = str(avatar_url) if avatar_url else ""
            if avatar_url.startswith('http'):
                return avatar_url
            else:
                return settings.OSS_MEDIA_URL + avatar_url
        return obj.avatar

    class Meta:
        model = UserProfile
        fields = '__all__'

class UserBalanceRecordSerializer(CustomFieldsSerializer):
    create_time = serializers.SerializerMethodField()

    class Meta:
        model = UserBalanceRecord
        fields = '__all__'
    #格式化时间戳 create_time
    def get_create_time(self, obj):
        # 将时间转换为北京时间
        beijing_tz = pytz.timezone('Asia/Shanghai')
        create_time_beijing = obj.create_time.astimezone(beijing_tz)
        return create_time_beijing.strftime('%Y-%m-%d %H:%M:%S')
   

class UserSteamSerializer(CustomFieldsSerializer):
    avatar = serializers.SerializerMethodField()
    avatarfull = serializers.SerializerMethodField()
    avatarmedium = serializers.SerializerMethodField()

    class Meta:
        model = UserSteam
        fields = '__all__'

    def get_avatar(self, obj):
        if obj.user.profile.avatar:
            avatar_url = obj.user.profile.avatar.name  # 使用 name 而不是 url
            # 确保 avatar_url 是字符串
            avatar_url = str(avatar_url) if avatar_url else ""
            if avatar_url.startswith('http'):
                return avatar_url
            else:
                return settings.OSS_MEDIA_URL + avatar_url
        return obj.avatar

    def get_avatarfull(self, obj):
        if obj.user.profile.avatar:
            avatar_url = obj.user.profile.avatar.name  # 使用 name 而不是 url
            # 确保 avatar_url 是字符串
            avatar_url = str(avatar_url) if avatar_url else ""
            if avatar_url.startswith('http'):
                return avatar_url
            else:
                return settings.OSS_MEDIA_URL + avatar_url
        return obj.avatarfull

    def get_avatarmedium(self, obj):
        if obj.user.profile.avatar:
            avatar_url = obj.user.profile.avatar.name  # 使用 name 而不是 url
            # 确保 avatar_url 是字符串
            avatar_url = str(avatar_url) if avatar_url else ""
            if avatar_url.startswith('http'):
                return avatar_url
            else:
                return settings.OSS_MEDIA_URL + avatar_url
        return obj.avatarmedium


class UserAssetSerializer(CustomFieldsSerializer):
    class Meta:
        model = UserAsset
        fields = '__all__'


class UserExtraSerializer(CustomFieldsSerializer):
    free_level_count = serializers.SerializerMethodField()

    class Meta:
        model = UserExtra
        fields = '__all__'

    def get_free_level_count(self, obj):
        return [
            obj.freebox_lv1_count, obj.freebox_lv2_count, obj.freebox_lv3_count,
            obj.freebox_lv4_count, obj.freebox_lv5_count, obj.freebox_lv6_count,
            obj.freebox_lv7_count, obj.freebox_lv8_count, obj.freebox_lv9_count,
            obj.freebox_lv10_count,
        ]


class AuthUserSerializer(CustomFieldsSerializer):
    profile = UserProfileSerializer(fields=('nickname', 'avatar'))
    steam = UserSteamSerializer(fields=('steamid', 'personaname', 'profileurl', 'avatar', 'avatarmedium', 'avatarfull'))
    asset = UserAssetSerializer()
    extra = UserExtraSerializer(fields=(
        'ban_create_roll_room', 'free_level_count', 'ban_withdraw', 'ban_withdraw_reason', 'ban_deposit',
        'ban_deposit_reason', 'ban_exchange', 'ban_battle', 'ban_battle_reason', 'exam_passed', 'exam_time'
    ))
    # nick_name = serializers.SerializerMethodField()
    # avatarmedium = serializers.SerializerMethodField()
    # promo = serializers.SerializerMethodField()
    level = serializers.SerializerMethodField()
    id = serializers.IntegerField(source='uid', read_only=True)
    # avatar = serializers.SerializerMethodField()
    

    class Meta:
        model = AuthUser
        fields = '__all__'

    def get_avatar(self, obj):
        if obj.steam.avatar:
            # avatar_url = obj.steam.avatar.replace('/media', 'users')
            avatar_url = obj.profile.avatar
            # 检查 avatar_url 是 ImageFieldFile 对象，如果是则获取其 url 或 name 属性
            if hasattr(avatar_url, 'url'):
                avatar_url_str = str(avatar_url.url) if hasattr(avatar_url, 'url') else str(avatar_url.name)
            else:
                avatar_url_str = str(avatar_url)
                
            if avatar_url_str.startswith('http'):
                return avatar_url_str
            else:
                # OSS拼接 settings.ALIYUN_OSS_ENDPOINT
                return f'https://{settings.ALIYUN_OSS_BUCKET_NAME}.{settings.ALIYUN_OSS_ENDPOINT}/{avatar_url_str}'
        # 返回默认头像路径而不是obj.avatar
        #return settings.OSS_MEDIA_URL + 'users/default_avatar.png'
        return f'https://{settings.ALIYUN_OSS_BUCKET_NAME}.{settings.ALIYUN_OSS_ENDPOINT}/users/default_avatar.png'
    
    def get_nick_name(self, obj):
        if obj.steam.personaname:
            return obj.steam.personaname
        elif obj.phone:
            reg = re.match(r'(\d{3})\d{4}(\d{4})', obj.phone)
            return '{}****{}'.format(reg.group(1), reg.group(2))
        else:
            return '-'

    def get_avatarmedium(self, obj):
        return obj.steam.avatarmedium

    def get_promo(self, obj):
        p = PromotionRecord.objects.filter(user=obj).first()
        if p:
            return p.ref.code
        else:
            return ''

    def get_level(self, obj):
        charge_level = obj.charge_level.first()
        if not charge_level:
            return 0
        return charge_level.level


class AuthUserCacheSerializer(CustomFieldsSerializer):
    profile = UserProfileSerializer()
    steam = UserSteamSerializer(
        fields=('steamid', 'personaname', 'profileurl', 'avatar', 'avatarmedium', 'avatarfull'))
    asset = UserAssetSerializer()
    extra = UserExtraSerializer(fields=(
        'ban_create_roll_room', 'free_level_count'
    ))
    nick_name = serializers.SerializerMethodField()
    avatarmedium = serializers.SerializerMethodField()
    promo = serializers.SerializerMethodField()
    level = serializers.SerializerMethodField()

    class Meta:
        model = AuthUser
        fields = '__all__'

    def get_nick_name(self, obj):
        if obj.steam.personaname:
            return obj.steam.personaname
        elif obj.phone:
            reg = re.match(r'(\d{3})\d{4}(\d{4})', obj.phone)
            return '{}****{}'.format(reg.group(1), reg.group(2))
        else:
            return '-'

    def get_avatarmedium(self, obj):
        return obj.steam.avatarmedium

    def get_promo(self, obj):
        p = PromotionRecord.objects.filter(user=obj).first()
        if p:
            return p.ref.code
        else:
            return ''

    def get_level(self, obj):
        charge_level = obj.charge_level.first()
        if not charge_level:
            return 0
        return charge_level.level

    def to_representation(self, instance):
        key = get_serializer_cache_key(instance, self.__class__)
        cached = cache.get(key)
        if cached:
            return cached

        result = super(AuthUserCacheSerializer, self).to_representation(instance)
        cache.set(key, result, settings.MINUTES_REDIS_TIMEOUT * 10)
        return result


class UserStatisticsDaySerializer(CustomFieldsSerializer):
    class Meta:
        model = UserStatisticsDay
        fields = '__all__'


class SmallAuthUserSerializer(CustomFieldsSerializer):
    profile = UserProfileSerializer(fields=('nickname', 'avatar'))
    steam = UserSteamSerializer(fields=('steamid', 'personaname', 'profileurl', 'avatar', 'avatarmedium',
                                        'avatarfull', 'level'))
    asset = UserAssetSerializer(fields=('level', 'email_reward', 'phone_reward'))

    class Meta:
        model = AuthUser
        fields = (
        'uid', 'username', 'date_joined', 'last_login', 'profile', 'steam', 'asset', 'is_superuser', 'is_staff')
