import logging

from django.utils.translation import gettext_lazy as _

from rest_framework.permissions import AllowAny
from rest_framework.views import APIView

from sitecfg.interfaces import get_custombox_maintenance
from steambase.enums import RespCode
from steambase.utils import reformat_resp, current_user
from custombox.business import (
    get_custombox_list, get_box_skin_list, get_custombox_items, save_custombox_config, get_custombox_term,
    get_box_detail, open_custombox, get_custombox_record
)

_logger = logging.getLogger(__name__)


class GetCustomBoxListView(APIView):

    def get(self, request):
        try:
            user = current_user(request)
            code, resp = get_custombox_list(user)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetCustomBoxTermView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            code, resp = get_custombox_term()
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetBoxSkinView(APIView):

    def get(self, request):
        try:
            code, resp = get_box_skin_list()
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetCustomBoxEnableItemView(APIView):

    def get(self, request):
        try:
            query = request.query_params.get('q', None)
            ext = request.query_params.get('ext', None)
            min_price = request.query_params.get('min', None)
            max_price = request.query_params.get('max', None)
            category = request.query_params.get('type', None)
            order = request.query_params.get('sort', None)
            page = request.query_params.get('page', 1)
            page_size = request.query_params.get('pageSize', 15)
            code, resp = get_custombox_items(min_price, max_price, ext, category, order, query, page, page_size)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class SaveCustomBoxConfView(APIView):

    def post(self, request):
        try:
            if get_custombox_maintenance():
                return reformat_resp(RespCode.Maintenance.value, {},
                                     _('The Custom Box is under maintenance, please wait for a while.'))
            user = current_user(request)
            name = request.data.get('name', '')
            skin = request.data.get('skin', {})
            items = request.data.get('items', [])
            code, resp = save_custombox_config(user, skin, items, name)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetCustomBoxDetailView(APIView):

    def get(self, request):
        try:
            user = current_user(request)
            key = request.query_params.get('key', '')
            code, resp = get_box_detail(user, key)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class OpenCustomBoxView(APIView):

    def post(self, request):
        try:
            if get_custombox_maintenance():
                return reformat_resp(RespCode.Maintenance.value, {},
                                     _('The Custom Box is under maintenance, please wait for a while.'))
            user = current_user(request)
            key = request.data.get('key', '')
            count = request.data.get('count', 1)
            code, resp = open_custombox(user, key, count)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetCustomBoxRecordView(APIView):

    def get(self, request):
        try:
            user = current_user(request)
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            code, resp = get_custombox_record(user, page, page_size)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')
