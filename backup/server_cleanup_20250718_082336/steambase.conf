server
{
    listen 80;
  	listen 443 ssl http2;
    server_name www.csgoskins.com.cn csgoskins.com.cn;
    
    
    
    ssl_certificate    /www/server/panel/vhost/cert/csgoskins.com.cn/fullchain.pem;
    ssl_certificate_key    /www/server/panel/vhost/cert/csgoskins.com.cn/privkey.pem;
    ssl_protocols TLSv1.1 TLSv1.2 TLSv1.3;
    ssl_ciphers EECDH+CHACHA20:EECDH+CHACHA20-draft:EECDH+AES128:RSA+AES128:EECDH+AES256:RSA+AES256:EECDH+3DES:RSA+3DES:!MD5;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    add_header Strict-Transport-Security "max-age=31536000";
    error_page 497  https://$host$request_uri;

    location / {
		proxy_http_version 1.1;
		proxy_set_header Upgrade $http_upgrade;
		proxy_set_header Connection "upgrade";
		proxy_set_header Host $host;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header X-Forwarded-Proto $scheme;
		proxy_set_header X-Nginx-Proxy true;
		proxy_cache_bypass $http_upgrade;
		# proxy_pass http://nodenuxt;
		try_files $uri $uri/ /index.html;
		root /www/wwwroot/csgoskins.com.cn/ui/dist;
        index  index.html;
	}

    location /static {
    alias  /www/wwwroot/csgoskins.com.cn/server/static;  #静态文件路径
    }
    
    location @router {
		rewrite ^.*$ /index.html last;
	}
	
    location ~ /purge(/.*) {
        proxy_cache_purge cache_one $host$1$is_args$args;
        #access_log  /www/wwwlogs/bbg.qibaolou.com_purge_cache.log;
    }

	include enable-php-00.conf;
  
    #禁止访问的文件或目录
    location ~ ^/(\.user.ini|\.htaccess|\.git|\.svn|\.project|LICENSE|README.md)
    {
        return 404;
    }
    
    #一键申请SSL证书验证目录相关设置
    location ~ \.well-known{
        allow all;
    }
    
    location /api/ {
		proxy_pass http://127.0.0.1:8000;
		proxy_connect_timeout 500s;
		proxy_read_timeout 500s;
		proxy_send_timeout 500s;
		proxy_set_header Host $host;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header X-Forwarded-Proto $scheme;
	}

	location /auth/ {
		proxy_pass http://127.0.0.1:8000;
		proxy_connect_timeout 500s;
		proxy_read_timeout 500s;
		proxy_send_timeout 500s;
		proxy_set_header Host $host;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header X-Forwarded-Proto $scheme;
	}

	location /social/ {
		proxy_pass http://127.0.0.1:8000;
		proxy_connect_timeout 500s;
		proxy_read_timeout 500s;
		proxy_send_timeout 500s;
		proxy_set_header Host $host;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header X-Forwarded-Proto $scheme;
	}

	location /socket.io/ {
		proxy_pass http://127.0.0.1:4000;
		proxy_redirect off;
		proxy_connect_timeout 500s;
		proxy_read_timeout 500s;
		proxy_send_timeout 500s;
		proxy_http_version 1.1;
		proxy_set_header Upgrade $http_upgrade;
		proxy_set_header Connection "upgrade";
	}

	location /super/ {
		proxy_pass http://127.0.0.1:8000;
		proxy_connect_timeout 500s;
		proxy_read_timeout 500s;
		proxy_send_timeout 500s;
		proxy_set_header Host $host;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header X-Forwarded-Proto $scheme;
	}

	location /jet/ {
		proxy_pass http://127.0.0.1:8000;
		proxy_connect_timeout 500s;
		proxy_read_timeout 500s;
		proxy_send_timeout 500s;
		proxy_set_header Host $host;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header X-Forwarded-Proto $scheme;
	}

	location /ckeditor/ {
		proxy_pass http://127.0.0.1:8000;
		proxy_connect_timeout 500s;
		proxy_read_timeout 500s;
		proxy_send_timeout 500s;
		proxy_set_header Host $host;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header X-Forwarded-Proto $scheme;
	}

	location /static/ {
		autoindex on;
		alias /www/wwwroot/csgoskins.com.cn/server/static/;
		proxy_connect_timeout 500s;
		proxy_read_timeout 500s;
		proxy_send_timeout 500s;
		fastcgi_param HTTPS on;
	}

	location /media/ {
		autoindex on;
		alias /www/wwwroot/csgoskins.com.cn/server/media/;
		proxy_connect_timeout 500s;
		proxy_read_timeout 500s;
		proxy_send_timeout 500s;
		fastcgi_param HTTPS on;
	}
    
    access_log  /www/wwwlogs/csgoskins.com.cn.log;
    error_log  /www/wwwlogs/csgoskins.com.cn.error.log;
}
