import logging
from rest_framework.response import Response
from rest_framework.permissions import AllowAny
from rest_framework.views import APIView

from steambase.enums import RespCode
from steambase.utils import reformat_resp
from sitegroup.business import get_site_by_domain, check_feature_enabled

_logger = logging.getLogger(__name__)


class GetSiteInfoView(APIView):
    """获取站点信息API"""
    permission_classes = [AllowAny]
    
    def get(self, request):
        """获取当前域名的站点信息"""
        try:
            # 获取域名
            domain = request.get_host()
            
            # 获取站点信息
            code, data = get_site_by_domain(domain)
            
            return reformat_resp(code, data)
                
        except Exception as e:
            _logger.error(f"Error in GetSiteInfoView: {e}")
            return reformat_resp(RespCode.ServerError.value, 'Server error')


class CheckFeatureView(APIView):
    """检查功能开关API"""
    permission_classes = [AllowAny]
    
    def get(self, request):
        """检查指定功能是否启用"""
        try:
            domain = request.get_host()
            feature = request.GET.get('feature')
            
            if not feature:
                return reformat_resp(RespCode.InvalidParams.value, 'Feature parameter is required')

            enabled = check_feature_enabled(domain, feature)

            return reformat_resp(RespCode.Succeed.value, {
                'feature': feature,
                'enabled': enabled
            })

        except Exception as e:
            _logger.error(f"Error in CheckFeatureView: {e}")
            return reformat_resp(RespCode.ServerError.value, 'Server error')
