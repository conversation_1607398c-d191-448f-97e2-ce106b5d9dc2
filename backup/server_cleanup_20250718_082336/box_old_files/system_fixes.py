#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Django时区和配置修复模块
解决时区警告和相关配置问题
"""

import os
import logging
from datetime import datetime, timezone
from django.conf import settings
from django.utils import timezone as django_timezone

logger = logging.getLogger(__name__)


class TimezoneFixer:
    """时区修复器"""
    
    @staticmethod
    def get_current_time():
        """获取当前时间（时区感知）"""
        if settings.USE_TZ:
            return django_timezone.now()
        else:
            return datetime.now()
    
    @staticmethod
    def make_aware(dt):
        """使datetime对象时区感知"""
        if dt and not django_timezone.is_aware(dt):
            return django_timezone.make_aware(dt)
        return dt
    
    @staticmethod
    def to_timestamp(dt):
        """转换为时间戳"""
        if django_timezone.is_naive(dt) and settings.USE_TZ:
            dt = TimezoneFixer.make_aware(dt)
        return int(dt.timestamp() * 1000)
    
    @staticmethod
    def fix_model_datetime_fields(model_instance):
        """修复模型实例的时间字段"""
        for field in model_instance._meta.fields:
            if hasattr(field, 'auto_now') or hasattr(field, 'auto_now_add'):
                field_name = field.name
                field_value = getattr(model_instance, field_name, None)
                if field_value and not django_timezone.is_aware(field_value):
                    setattr(model_instance, field_name, django_timezone.make_aware(field_value))
        return model_instance


class DatabaseConnectionManager:
    """数据库连接管理器"""
    
    @staticmethod
    def ensure_connection():
        """确保数据库连接可用"""
        from django.db import connection
        
        try:
            # 测试连接
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
            return True
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            # 尝试重新连接
            try:
                connection.close()
                with connection.cursor() as cursor:
                    cursor.execute("SELECT 1")
                logger.info("数据库重连成功")
                return True
            except Exception as re_e:
                logger.error(f"数据库重连失败: {re_e}")
                return False
    
    @staticmethod
    def safe_query(query_func, *args, **kwargs):
        """安全执行数据库查询"""
        try:
            if not DatabaseConnectionManager.ensure_connection():
                raise Exception("数据库连接不可用")
            
            return query_func(*args, **kwargs)
            
        except Exception as e:
            logger.error(f"数据库查询失败: {e}")
            raise


class BattleSystemHealthChecker:
    """对战系统健康检查器"""
    
    def __init__(self):
        self.timezone_fixer = TimezoneFixer()
        self.db_manager = DatabaseConnectionManager()
    
    def check_django_configuration(self):
        """检查Django配置"""
        issues = []
        
        # 检查时区设置
        if not hasattr(settings, 'USE_TZ') or not settings.USE_TZ:
            issues.append("建议启用Django时区支持 (USE_TZ = True)")
        
        if not hasattr(settings, 'TIME_ZONE'):
            issues.append("未设置时区 (TIME_ZONE)")
        
        # 检查数据库配置
        if not hasattr(settings, 'DATABASES') or not settings.DATABASES:
            issues.append("数据库配置缺失")
        
        # 检查缓存配置
        if not hasattr(settings, 'CACHES') or not settings.CACHES:
            issues.append("缓存配置缺失")
        
        # 检查WebSocket配置
        if not hasattr(settings, 'CHANNEL_LAYERS'):
            issues.append("WebSocket通道配置可能缺失")
        
        return issues
    
    def check_system_resources(self):
        """检查系统资源"""
        issues = []
        
        try:
            import psutil
            
            # 检查内存使用
            memory = psutil.virtual_memory()
            if memory.percent > 85:
                issues.append(f"内存使用率过高: {memory.percent}%")
            
            # 检查磁盘使用
            disk = psutil.disk_usage('/')
            if disk.percent > 90:
                issues.append(f"磁盘使用率过高: {disk.percent}%")
            
        except ImportError:
            issues.append("建议安装psutil以进行系统监控")
        except Exception as e:
            issues.append(f"系统资源检查失败: {e}")
        
        return issues
    
    def check_battle_specific_issues(self):
        """检查对战系统特定问题"""
        issues = []
        
        try:
            from box.models import CaseRoom
            from steambase.enums import GameState
            
            # 检查长时间运行的房间
            long_running_rooms = self.db_manager.safe_query(
                lambda: CaseRoom.objects.filter(
                    state=GameState.Running.value,
                    create_time__lt=self.timezone_fixer.get_current_time() - 
                                   django_timezone.timedelta(hours=1)
                ).count()
            )
            
            if long_running_rooms > 0:
                issues.append(f"发现 {long_running_rooms} 个长时间运行的房间")
            
            # 检查孤立的投注记录
            from box.models import CaseRoomBet
            orphaned_bets = self.db_manager.safe_query(
                lambda: CaseRoomBet.objects.filter(
                    room__state=GameState.End.value,
                    open_amount__isnull=True
                ).count()
            )
            
            if orphaned_bets > 0:
                issues.append(f"发现 {orphaned_bets} 个孤立的投注记录")
                
        except Exception as e:
            issues.append(f"对战系统检查失败: {e}")
        
        return issues
    
    def get_health_report(self):
        """获取健康报告"""
        report = {
            'timestamp': self.timezone_fixer.to_timestamp(
                self.timezone_fixer.get_current_time()
            ),
            'django_issues': self.check_django_configuration(),
            'system_issues': self.check_system_resources(),
            'battle_issues': self.check_battle_specific_issues(),
        }
        
        # 计算总体健康状态
        total_issues = (len(report['django_issues']) + 
                       len(report['system_issues']) + 
                       len(report['battle_issues']))
        
        if total_issues == 0:
            report['status'] = 'healthy'
        elif total_issues <= 3:
            report['status'] = 'warning'
        else:
            report['status'] = 'critical'
        
        return report


def get_timezone_aware_now():
    """获取时区感知的当前时间"""
    return TimezoneFixer.get_current_time()


def make_datetime_aware(dt):
    """使datetime时区感知"""
    return TimezoneFixer.make_aware(dt)


def safe_database_query(query_func, *args, **kwargs):
    """安全执行数据库查询"""
    return DatabaseConnectionManager.safe_query(query_func, *args, **kwargs)


def get_system_health():
    """获取系统健康状态"""
    checker = BattleSystemHealthChecker()
    return checker.get_health_report()
