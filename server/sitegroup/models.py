from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import URLValidator
from django.core.exceptions import ValidationError

from steambase.models import ModelBase, USER_MODEL


class SiteGroup(ModelBase):
    """站群模型 - 代理商的网站集合"""
    agent = models.ForeignKey(
        USER_MODEL, 
        on_delete=models.CASCADE, 
        verbose_name=_('Agent'), 
        related_name='site_groups',
        limit_choices_to={'is_agent': True, 'is_active': True}
    )
    name = models.CharField(_('Group Name'), max_length=128)
    description = models.TextField(_('Description'), blank=True, null=True)
    is_active = models.BooleanField(_('Is Active'), default=True)
    
    class Meta:
        verbose_name = _('Site Group')
        verbose_name_plural = _('Site Groups')
        ordering = ['name']
    
    def __str__(self):
        return f"{self.agent.username} - {self.name}"
    
    def get_active_sites_count(self):
        """获取活跃站点数量"""
        return self.sites.filter(is_active=True).count()


class Site(ModelBase):
    """网站模型 - 单个域名网站"""
    site_group = models.ForeignKey(
        SiteGroup, 
        on_delete=models.CASCADE,
        verbose_name=_('Site Group'), 
        related_name='sites'
    )
    
    # 基础信息
    domain = models.CharField(_('Domain'), max_length=255, unique=True)
    name = models.CharField(_('Site Name'), max_length=128)
    subtitle = models.CharField(_('Subtitle'), max_length=128, blank=True, null=True)
    logo_url = models.URLField(_('Logo URL'), blank=True, null=True)
    favicon_url = models.URLField(_('Favicon URL'), blank=True, null=True)
    
    # SEO信息
    seo_title = models.CharField(_('SEO Title'), max_length=128, blank=True, null=True)
    seo_keywords = models.CharField(_('SEO Keywords'), max_length=255, blank=True, null=True)
    seo_description = models.TextField(_('SEO Description'), max_length=500, blank=True, null=True)
    icp_number = models.CharField(_('ICP Number'), max_length=128, blank=True, null=True)
    
    # 功能开关
    articles_enabled = models.BooleanField(_('Articles Enabled'), default=True)
    announcements_enabled = models.BooleanField(_('Announcements Enabled'), default=True)
    case_battle_enabled = models.BooleanField(_('Case Battle Enabled'), default=True)
    case_opening_enabled = models.BooleanField(_('Case Opening Enabled'), default=True)
    market_enabled = models.BooleanField(_('Market Enabled'), default=True)
    lottery_enabled = models.BooleanField(_('Lottery Enabled'), default=True)
    roll_enabled = models.BooleanField(_('Roll Enabled'), default=True)
    crash_enabled = models.BooleanField(_('Crash Enabled'), default=True)
    
    # 样式配置
    primary_color = models.CharField(_('Primary Color'), max_length=7, default='#007bff')
    secondary_color = models.CharField(_('Secondary Color'), max_length=7, default='#6c757d')
    custom_css = models.TextField(_('Custom CSS'), blank=True, null=True)
    
    # 状态
    is_active = models.BooleanField(_('Is Active'), default=True)
    is_default = models.BooleanField(_('Is Default'), default=False)
    maintenance_mode = models.BooleanField(_('Maintenance Mode'), default=False)
    maintenance_message = models.TextField(_('Maintenance Message'), blank=True, null=True)
    
    class Meta:
        verbose_name = _('Site')
        verbose_name_plural = _('Sites')
        ordering = ['domain']
        indexes = [
            models.Index(fields=['domain']),
            models.Index(fields=['is_active']),
            models.Index(fields=['is_default']),
        ]
    
    def __str__(self):
        return f"{self.domain} - {self.name}"
    
    def clean(self):
        """验证域名格式"""
        if self.domain:
            # 简单的域名格式验证
            if not self.domain.replace('.', '').replace('-', '').isalnum():
                raise ValidationError(_('Invalid domain format'))
    
    def save(self, *args, **kwargs):
        # 确保只有一个默认站点
        if self.is_default:
            Site.objects.filter(
                site_group=self.site_group, 
                is_default=True
            ).exclude(pk=self.pk).update(is_default=False)
        super().save(*args, **kwargs)
    
    def get_feature_status(self):
        """获取功能开关状态"""
        return {
            'articles': self.articles_enabled,
            'announcements': self.announcements_enabled,
            'case_battle': self.case_battle_enabled,
            'case_opening': self.case_opening_enabled,
            'market': self.market_enabled,
            'lottery': self.lottery_enabled,
            'roll': self.roll_enabled,
            'crash': self.crash_enabled,
        }
    
    def get_registered_users_count(self):
        """获取注册用户数量"""
        from authentication.models import AuthUser
        return AuthUser.objects.filter(domain=self.domain, is_active=True).count()


class SiteConfig(ModelBase):
    """网站配置模型"""
    CONFIG_TYPES = [
        ('string', _('String')),
        ('integer', _('Integer')),
        ('float', _('Float')),
        ('boolean', _('Boolean')),
        ('json', _('JSON')),
        ('url', _('URL')),
        ('email', _('Email')),
    ]
    
    site = models.ForeignKey(
        Site, 
        on_delete=models.CASCADE,
        verbose_name=_('Site'), 
        related_name='configs'
    )
    
    # 配置项
    config_key = models.CharField(_('Config Key'), max_length=64)
    config_value = models.TextField(_('Config Value'), blank=True, null=True)
    config_type = models.CharField(
        _('Config Type'), 
        max_length=20, 
        choices=CONFIG_TYPES, 
        default='string'
    )
    
    # 元信息
    category = models.CharField(_('Category'), max_length=64, default='general')
    description = models.CharField(_('Description'), max_length=255, blank=True, null=True)
    is_active = models.BooleanField(_('Is Active'), default=True)
    is_sensitive = models.BooleanField(_('Is Sensitive'), default=False)  # 敏感配置，如API密钥
    
    class Meta:
        verbose_name = _('Site Config')
        verbose_name_plural = _('Site Configs')
        unique_together = ['site', 'config_key']
        ordering = ['category', 'config_key']
    
    def __str__(self):
        return f"{self.site.domain} - {self.config_key}"
    
    def get_typed_value(self):
        """根据类型返回转换后的值"""
        if not self.config_value:
            return None
            
        try:
            if self.config_type == 'integer':
                return int(self.config_value)
            elif self.config_type == 'float':
                return float(self.config_value)
            elif self.config_type == 'boolean':
                return self.config_value.lower() in ('true', '1', 'yes', 'on')
            elif self.config_type == 'json':
                import json
                return json.loads(self.config_value)
            else:
                return self.config_value
        except (ValueError, TypeError):
            return self.config_value


class SiteTheme(ModelBase):
    """网站主题模型"""
    site = models.OneToOneField(
        Site,
        on_delete=models.CASCADE,
        verbose_name=_('Site'),
        related_name='theme'
    )
    
    # 主题配置
    theme_name = models.CharField(_('Theme Name'), max_length=64, default='default')
    header_background = models.CharField(_('Header Background'), max_length=7, default='#ffffff')
    header_text_color = models.CharField(_('Header Text Color'), max_length=7, default='#000000')
    footer_background = models.CharField(_('Footer Background'), max_length=7, default='#f8f9fa')
    footer_text_color = models.CharField(_('Footer Text Color'), max_length=7, default='#6c757d')
    
    # 字体配置
    font_family = models.CharField(_('Font Family'), max_length=128, default='Arial, sans-serif')
    font_size_base = models.IntegerField(_('Base Font Size'), default=14)
    
    # 布局配置
    container_width = models.CharField(_('Container Width'), max_length=20, default='1200px')
    border_radius = models.IntegerField(_('Border Radius'), default=4)
    
    class Meta:
        verbose_name = _('Site Theme')
        verbose_name_plural = _('Site Themes')
    
    def __str__(self):
        return f"{self.site.domain} - {self.theme_name}"


class SiteAnalytics(ModelBase):
    """网站统计模型"""
    site = models.ForeignKey(
        Site,
        on_delete=models.CASCADE,
        verbose_name=_('Site'),
        related_name='analytics'
    )
    
    # 统计数据
    date = models.DateField(_('Date'))
    page_views = models.IntegerField(_('Page Views'), default=0)
    unique_visitors = models.IntegerField(_('Unique Visitors'), default=0)
    new_registrations = models.IntegerField(_('New Registrations'), default=0)
    active_users = models.IntegerField(_('Active Users'), default=0)
    
    # 业务数据
    case_openings = models.IntegerField(_('Case Openings'), default=0)
    battles_created = models.IntegerField(_('Battles Created'), default=0)
    market_transactions = models.IntegerField(_('Market Transactions'), default=0)
    
    class Meta:
        verbose_name = _('Site Analytics')
        verbose_name_plural = _('Site Analytics')
        unique_together = ['site', 'date']
        ordering = ['-date']
    
    def __str__(self):
        return f"{self.site.domain} - {self.date}"
