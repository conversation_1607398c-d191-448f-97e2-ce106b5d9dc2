#!/bin/bash
# Docker日志清理脚本

set -e

echo "📋 Docker日志管理工具"
echo "================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数：显示帮助信息
show_help() {
    echo "使用方法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -c, --check     检查日志大小"
    echo "  -s, --size      清理指定大小以上的日志 (默认: 100M)"
    echo "  -a, --all       清理所有容器日志"
    echo "  -t, --truncate  截断日志而不是删除"
    echo "  -h, --help      显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 -c                    # 检查日志大小"
    echo "  $0 -s 50M               # 清理50MB以上的日志"
    echo "  $0 -a                   # 清理所有日志"
    echo "  $0 -t                   # 截断所有日志"
}

# 函数：检查日志大小
check_logs() {
    echo -e "${BLUE}📊 检查Docker容器日志大小...${NC}"
    echo ""
    
    total_size=0
    
    # 检查每个容器的日志
    for container_id in $(docker ps -q); do
        container_name=$(docker ps --format "{{.Names}}" --filter "id=$container_id")
        log_file="/var/lib/docker/containers/$container_id/$container_id-json.log"
        
        if [ -f "$log_file" ]; then
            size=$(du -h "$log_file" | cut -f1)
            size_bytes=$(du -b "$log_file" | cut -f1)
            total_size=$((total_size + size_bytes))
            
            # 根据大小设置颜色
            if [ $size_bytes -gt 104857600 ]; then  # 100MB
                color=$RED
            elif [ $size_bytes -gt 52428800 ]; then  # 50MB
                color=$YELLOW
            else
                color=$GREEN
            fi
            
            echo -e "${color}📦 $container_name: $size${NC}"
        fi
    done
    
    echo ""
    total_size_mb=$((total_size / 1024 / 1024))
    echo -e "${BLUE}📊 总日志大小: ${total_size_mb}MB${NC}"
    echo ""
}

# 函数：清理大日志文件
cleanup_large_logs() {
    local size_limit=${1:-100M}
    echo -e "${YELLOW}🧹 清理大于 $size_limit 的日志文件...${NC}"
    echo ""
    
    # 转换大小限制为字节
    size_bytes=$(echo $size_limit | sed 's/M/*1024*1024/g; s/G/*1024*1024*1024/g' | bc)
    
    for container_id in $(docker ps -q); do
        container_name=$(docker ps --format "{{.Names}}" --filter "id=$container_id")
        log_file="/var/lib/docker/containers/$container_id/$container_id-json.log"
        
        if [ -f "$log_file" ]; then
            file_size=$(du -b "$log_file" | cut -f1)
            
            if [ $file_size -gt $size_bytes ]; then
                echo -e "${RED}🗑️  清理 $container_name 日志 ($(du -h "$log_file" | cut -f1))${NC}"
                
                # 备份最后1000行
                tail -n 1000 "$log_file" > "/tmp/${container_name}_backup.log"
                
                # 清空日志文件
                > "$log_file"
                
                # 恢复最后1000行
                cat "/tmp/${container_name}_backup.log" > "$log_file"
                rm "/tmp/${container_name}_backup.log"
                
                echo -e "${GREEN}✅ $container_name 日志已清理${NC}"
            fi
        fi
    done
}

# 函数：清理所有日志
cleanup_all_logs() {
    echo -e "${YELLOW}🧹 清理所有容器日志...${NC}"
    echo ""
    
    for container_id in $(docker ps -q); do
        container_name=$(docker ps --format "{{.Names}}" --filter "id=$container_id")
        log_file="/var/lib/docker/containers/$container_id/$container_id-json.log"
        
        if [ -f "$log_file" ]; then
            old_size=$(du -h "$log_file" | cut -f1)
            
            # 备份最后100行
            tail -n 100 "$log_file" > "/tmp/${container_name}_backup.log"
            
            # 清空日志文件
            > "$log_file"
            
            # 恢复最后100行
            cat "/tmp/${container_name}_backup.log" > "$log_file"
            rm "/tmp/${container_name}_backup.log"
            
            new_size=$(du -h "$log_file" | cut -f1)
            echo -e "${GREEN}✅ $container_name: $old_size → $new_size${NC}"
        fi
    done
}

# 函数：截断日志
truncate_logs() {
    echo -e "${YELLOW}✂️  截断所有容器日志...${NC}"
    echo ""
    
    for container_id in $(docker ps -q); do
        container_name=$(docker ps --format "{{.Names}}" --filter "id=$container_id")
        log_file="/var/lib/docker/containers/$container_id/$container_id-json.log"
        
        if [ -f "$log_file" ]; then
            old_size=$(du -h "$log_file" | cut -f1)
            
            # 截断到最后1000行
            tail -n 1000 "$log_file" > "/tmp/${container_name}_truncated.log"
            cat "/tmp/${container_name}_truncated.log" > "$log_file"
            rm "/tmp/${container_name}_truncated.log"
            
            new_size=$(du -h "$log_file" | cut -f1)
            echo -e "${GREEN}✅ $container_name: $old_size → $new_size${NC}"
        fi
    done
}

# 主逻辑
case "${1:-}" in
    -c|--check)
        check_logs
        ;;
    -s|--size)
        size_limit=${2:-100M}
        check_logs
        cleanup_large_logs "$size_limit"
        echo ""
        echo -e "${GREEN}🎉 日志清理完成！${NC}"
        check_logs
        ;;
    -a|--all)
        check_logs
        cleanup_all_logs
        echo ""
        echo -e "${GREEN}🎉 所有日志清理完成！${NC}"
        check_logs
        ;;
    -t|--truncate)
        check_logs
        truncate_logs
        echo ""
        echo -e "${GREEN}🎉 日志截断完成！${NC}"
        check_logs
        ;;
    -h|--help)
        show_help
        ;;
    "")
        echo -e "${BLUE}📋 Docker日志管理工具${NC}"
        echo ""
        check_logs
        echo -e "${YELLOW}💡 使用 -h 查看更多选项${NC}"
        ;;
    *)
        echo -e "${RED}❌ 未知选项: $1${NC}"
        show_help
        exit 1
        ;;
esac
