from django.db import models, transaction
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from steambase.models import ModelBase, USER_MODEL
from steambase.enums import WinResult, AppType, TradeupBetItemType
from steambase.utils import current_year, current_week
from package.models import PackageItem, ItemInfo


class TradeupGame(ModelBase):
    APP_TYPE = (
        (AppType.Dota2.value, _('Dota2')),
        (AppType.CSGO.value, _('CSGO')),
        (AppType.PUBG.value, _('PUBG')),
        (AppType.H1Z1.value, _('H1Z1')),
    )
    WIN_RESULT_TYPE = (
        (WinResult.NotEnd.value, _('Not End')),
        (WinResult.Win.value, _('Win')),
        (WinResult.Lose.value, _('Lose'))
    )
    user = models.ForeignKey(USER_MODEL, on_delete=models.CASCADE, verbose_name = "用户", related_name='tradeup_bets')
    hash = models.CharField(_("hash"), max_length=255)
    secret = models.CharField(_("secret"), max_length=32)
    percentage = models.FloatField(_("percentage"), default=0)
    bet_percentage = models.FloatField(_("bet percentage"), default=0)
    upper = models.BooleanField(_('upper'), default=False)
    bet_amount = models.FloatField(_('origin amount'), default=0)
    target_amount = models.FloatField(_('target amount'), default=0)
    win_amount = models.FloatField(_("win amount"), default=0)
    win_result = models.SmallIntegerField(_("win result"), default=WinResult.NotEnd.value, choices=WIN_RESULT_TYPE)
    appid = models.CharField(_('appid'), max_length=64, choices=APP_TYPE, default=AppType.CSGO.value)

    class Meta:
        ordering = ('-create_time',)
        verbose_name = "合成游戏"
        verbose_name_plural = _('Tradeup Game')

    def __str__(self):
        return self.uid


class TradeupBetItem(ModelBase):
    BET_ITEM_TYPE = (
        (TradeupBetItemType.Item.value, _('Item')),
        (TradeupBetItemType.Amount.value, _('Amount')),
    )
    game = models.ForeignKey(TradeupGame, on_delete=models.CASCADE, verbose_name=_("game"), related_name='bet_items')
    package = models.ForeignKey(PackageItem, on_delete=models.SET_NULL, verbose_name=_('package item'), related_name='tradeup_bet_items', null=True, blank=True)
    price = models.FloatField(_('price'), default=0)
    type = models.SmallIntegerField(_("bet item type"), choices=BET_ITEM_TYPE, default=TradeupBetItemType.Item.value)

    class Meta:
        verbose_name = "合成下注物品"
        verbose_name_plural = _('Tradeup Bet Item')

    def __str__(self):
        return self.package.item_info.market_name_cn or self.package.item_info.market_name


class TradeupCoinsBetItem(ModelBase):
    APP_TYPE = (
        (AppType.Dota2.value, _('Dota2')),
        (AppType.CSGO.value, _('CSGO')),
        (AppType.PUBG.value, _('PUBG')),
        (AppType.H1Z1.value, _('H1Z1')),
    )
    game = models.ForeignKey(TradeupGame, on_delete=models.CASCADE, verbose_name=_("game"), related_name='bet_amounts')
    amount = models.FloatField(_('amount'), default=0)
    appid = models.CharField(_('appid'), max_length=64, choices=APP_TYPE, default=AppType.CSGO.value)

    class Meta:
        verbose_name = "金币合成下注金额"
        verbose_name_plural = _('Coins Tradeup Bet amount')

    def __str__(self):
        return self.uid


class TradeupTargetItem(ModelBase):
    game = models.ForeignKey(TradeupGame, on_delete=models.CASCADE, verbose_name=_("game"), related_name='target_items')
    item_info = models.ForeignKey(ItemInfo, on_delete=models.CASCADE, verbose_name=_('item info'), related_name='tradeup_target_items')
    price = models.FloatField(_('price'), default=0)

    class Meta:
        verbose_name = "合成目标物品"
        verbose_name_plural = _('Tradeup Target Item')

    def __str__(self):
        return self.item_info.market_name_cn or self.item_info.market_name


class TradeupInventory(ModelBase):
    item_info = models.OneToOneField(ItemInfo, on_delete=models.CASCADE, verbose_name=_('item info'), related_name='tradeup_inventory')
    price = models.FloatField(_('price'), default=0)
    count = models.IntegerField(_('count'), default=0)
    unlimited = models.BooleanField(_('unlimited'), default=True)
    enable = models.BooleanField(_('enable'), default=True)

    class Meta:
        verbose_name = "合成库存物品"
        verbose_name_plural = _('Tradeup Inventory Item')

    def __str__(self):
        return self.item_info.market_name_cn or self.item_info.market_name


class TradeupPumpDay(ModelBase):
    date = models.DateField(_("date"), default=timezone.now)
    amount = models.FloatField(_("amount"), default=0)

    class Meta:
        ordering = ('-create_time',)
        verbose_name = "合成抽水日统计"
        verbose_name_plural = _('Tradeup Pump Day')

    def __str__(self):
        return str(self.date)

    @classmethod
    def update_amount(cls, amount):
        with transaction.atomic():
            today = timezone.localdate()
            if not cls.objects.filter(date=today):
                cls.objects.create(date=today)
            record = cls.objects.select_for_update().get(date=today)
            amount = round(amount, 2)
            record.amount += amount
            record.save()


class TradeupPumpMonth(ModelBase):
    date = models.DateField(_("date"), default=timezone.now)
    amount = models.FloatField(_("amount"), default=0)

    class Meta:
        ordering = ('-create_time',)
        verbose_name = "合成抽水月统计"
        verbose_name_plural = _('Tradeup Pump Month')

    def __str__(self):
        return self.month()

    @classmethod
    def update_amount(cls, amount):
        with transaction.atomic():
            today = timezone.localdate()
            month_day_first = today.replace(day=1)
            if not cls.objects.filter(date=month_day_first):
                cls.objects.create(date=month_day_first)
            record = cls.objects.select_for_update().get(date=month_day_first)
            amount = round(amount, 2)
            record.amount += amount
            record.save()

    def month(self):
        return self.date.strftime("%Y-%m")
    month.short_description = _('Month')


class TradeupStatisticsDay(ModelBase):
    date = models.DateField(_("date"), default=timezone.now)
    amount = models.FloatField(_("game amount"), default=0)
    journal = models.FloatField(_("game journal"), default=0)
    win_amount = models.FloatField(_("win amount journal"), default=0)
    lose_amount = models.FloatField(_("lose amount journal"), default=0)
    test_amount = models.FloatField(_("test game amount"), default=0)
    admin_amount = models.FloatField(_("admin game amount"), default=0)

    class Meta:
        ordering = ('-create_time',)
        verbose_name = "合成日统计"
        verbose_name_plural = _('Tradeup Statistics Day')

    def __str__(self):
        return str(self.date)

    @classmethod
    def update_amount(cls, amount):
        with transaction.atomic():
            today = timezone.localdate()
            if not cls.objects.filter(date=today):
                cls.objects.create(date=today)
            record = cls.objects.select_for_update().get(date=today)
            amount = round(amount, 2)
            record.amount += amount
            record.save()
       
    @classmethod
    def update_journal(cls, journal):
        with transaction.atomic():
            today = timezone.localdate()
            if not cls.objects.filter(date=today):
                cls.objects.create(date=today)
            record = cls.objects.select_for_update().get(date=today)
            journal = round(journal, 2)
            record.journal += journal
            record.save()

    @classmethod
    def update_win_amount(cls, win_amount):
        with transaction.atomic():
            today = timezone.localdate()
            if not cls.objects.filter(date=today):
                cls.objects.create(date=today)
            record = cls.objects.select_for_update().get(date=today)
            win_amount = round(win_amount, 2)
            record.win_amount += win_amount
            record.save()
    
    @classmethod
    def update_lose_amount(cls, lose_amount):
        with transaction.atomic():
            today = timezone.localdate()
            if not cls.objects.filter(date=today):
                cls.objects.create(date=today)
            record = cls.objects.select_for_update().get(date=today)
            lose_amount = round(lose_amount, 2)
            record.lose_amount += lose_amount
            record.save()

    @classmethod
    def update_test_amount(cls, amount):
        with transaction.atomic():
            today = timezone.localdate()
            if not cls.objects.filter(date=today):
                cls.objects.create(date=today)
            record = cls.objects.select_for_update().get(date=today)
            amount = round(amount, 2)
            record.test_amount += amount
            record.save()

    @classmethod
    def update_admin_amount(cls, amount):
        with transaction.atomic():
            today = timezone.localdate()
            if not cls.objects.filter(date=today):
                cls.objects.create(date=today)
            record = cls.objects.select_for_update().get(date=today)
            amount = round(amount, 2)
            record.admin_amount += amount
            record.save()


class TradeupStatisticsMonth(ModelBase):
    date = models.DateField(_("date"), default=timezone.now)
    amount = models.FloatField(_("game amount"), default=0)
    journal = models.FloatField(_("game journal"), default=0)
    win_amount = models.FloatField(_("win amount journal"), default=0)
    lose_amount = models.FloatField(_("lose amount journal"), default=0)
    test_amount = models.FloatField(_("test game amount"), default=0)
    admin_amount = models.FloatField(_("admin game amount"), default=0)

    class Meta:
        ordering = ('-create_time',)
        verbose_name = "合成月统计"
        verbose_name_plural = _('Tradeup Statistics Month')

    def __str__(self):
        return self.month()

    @classmethod
    def update_amount(cls, amount):
        with transaction.atomic():
            today = timezone.localdate()
            month_day_first = today.replace(day=1)
            if not cls.objects.filter(date=month_day_first):
                cls.objects.create(date=month_day_first)
            record = cls.objects.select_for_update().get(date=month_day_first)
            amount = round(amount, 2)
            record.amount += amount
            record.save()
       
    @classmethod
    def update_journal(cls, journal):
        with transaction.atomic():
            today = timezone.localdate()
            month_day_first = today.replace(day=1)
            if not cls.objects.filter(date=month_day_first):
                cls.objects.create(date=month_day_first)
            record = cls.objects.select_for_update().get(date=month_day_first)
            journal = round(journal, 2)
            record.journal += journal
            record.save()

    @classmethod
    def update_win_amount(cls, win_amount):
        with transaction.atomic():
            today = timezone.localdate()
            month_day_first = today.replace(day=1)
            if not cls.objects.filter(date=month_day_first):
                cls.objects.create(date=month_day_first)
            record = cls.objects.select_for_update().get(date=month_day_first)
            win_amount = round(win_amount, 2)
            record.win_amount += win_amount
            record.save()
    
    @classmethod
    def update_lose_amount(cls, lose_amount):
        with transaction.atomic():
            today = timezone.localdate()
            month_day_first = today.replace(day=1)
            if not cls.objects.filter(date=month_day_first):
                cls.objects.create(date=month_day_first)
            record = cls.objects.select_for_update().get(date=month_day_first)
            lose_amount = round(lose_amount, 2)
            record.lose_amount += lose_amount
            record.save()

    @classmethod
    def update_test_amount(cls, amount):
        with transaction.atomic():
            today = timezone.localdate()
            month_day_first = today.replace(day=1)
            if not cls.objects.filter(date=month_day_first):
                cls.objects.create(date=month_day_first)
            record = cls.objects.select_for_update().get(date=month_day_first)
            amount = round(amount, 2)
            record.test_amount += amount
            record.save()

    @classmethod
    def update_admin_amount(cls, amount):
        with transaction.atomic():
            today = timezone.localdate()
            month_day_first = today.replace(day=1)
            if not cls.objects.filter(date=month_day_first):
                cls.objects.create(date=month_day_first)
            record = cls.objects.select_for_update().get(date=month_day_first)
            amount = round(amount, 2)
            record.admin_amount += amount
            record.save()
            
    def month(self):
        return self.date.strftime("%Y-%m")
    month.short_description = _('Month')


class TradeupWinDayRank(ModelBase):
    user = models.ForeignKey(USER_MODEL, on_delete=models.CASCADE, verbose_name = "用户", related_name='tradeup_win_day_rank')
    date = models.DateField(_('date'), default=timezone.now)
    amount = models.FloatField(_('amount'), default=0)

    class Meta:
        verbose_name = "合成赢取日排行"
        verbose_name_plural = _('Tradeup Win Day Rank')

    def __str__(self):
        return self.uid

    @classmethod
    def update_amount(cls, user, amount):
        with transaction.atomic():
            today = timezone.localdate()
            if not cls.objects.filter(user=user, date=today):
                cls.objects.create(user=user, date=today)
            record = cls.objects.select_for_update().get(user=user, date=today)
            amount = round(amount, 2)
            record.amount += amount
            record.save()


class TradeupWinWeekRank(ModelBase):
    user = models.ForeignKey(USER_MODEL, on_delete=models.CASCADE, verbose_name = "用户", related_name='tradeup_win_week_rank')
    year = models.SmallIntegerField(_('year'), default=0)
    week = models.SmallIntegerField(_('week'), default=0)
    amount = models.FloatField(_('amount'), default=0)

    class Meta:
        verbose_name = "合成赢取周排行"
        verbose_name_plural = _('Tradeup Win Week Rank')

    def __str__(self):
        return self.uid

    @classmethod
    def update_amount(cls, user, amount):
        with transaction.atomic():
            year = current_year()
            week = current_week()
            if not cls.objects.filter(user=user, year=year, week=week):
                cls.objects.create(user=user, year=year, week=week)
            record = cls.objects.select_for_update().get(user=user, year=year, week=week)
            amount = round(amount, 2)
            record.amount += amount
            record.save()

    def weeks(self):
        return '{}-{}'.format(self.year, self.week)
    weeks.short_description = _('Weeks')


class TradeupBot(models.Model):
    user = models.OneToOneField(USER_MODEL, on_delete=models.CASCADE, verbose_name = "用户", related_name='tradeup_bot')
    bet_idle_min = models.IntegerField(_('bet idle min(seconds)'), default=0)
    bet_idle_max = models.IntegerField(_('bet idle max(seconds)'), default=0)
    chance_min = models.FloatField(_('bot tradeup min chance'), default=0)
    chance_max = models.FloatField(_('bot tradeup max chance'), default=100)
    price_min = models.FloatField(_('bot item price min'), default=0)
    price_max = models.FloatField(_('bot item price max'), default=9999)
    remark = models.CharField(_('remark'), max_length=128)
    enable = models.BooleanField(_('enable'), default=True)

    class Meta:
        verbose_name = "合成机器人配置"
        verbose_name_plural = _('Tradeup Bot Config')

    def __str__(self):
        return self.remark


# class TradeupBotInventory(ModelBase):
#     package_item_info = models.OneToOneField(PackageItem, on_delete=models.CASCADE, verbose_name=_('package item info'), related_name='tradeup_bot_inventory')
#     enable = models.BooleanField(_('enable'), default=True)
# 
#     class Meta:
#         verbose_name = _('Tradeup Bot Inventory Item')
#         verbose_name_plural = _('Tradeup Bot Inventory Item')
# 
#     def __str__(self):
#         return self.package_item_info.item_info.market_name_cn or self.paceage_item_info.item_info.market_name
# 
