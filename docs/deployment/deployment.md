# 部署指南

## 生产环境部署

### 系统要求

#### 服务器配置
- **CPU**: 4核心及以上
- **内存**: 8GB及以上
- **存储**: 100GB SSD
- **网络**: 100Mbps带宽
- **操作系统**: Ubuntu 20.04 LTS / CentOS 8

#### 依赖服务
- **Docker**: 20.10+
- **Docker Compose**: 1.29+
- **MySQL**: 8.0+ (可使用云数据库)
- **Redis**: 6.0+ (可使用云缓存)
- **Nginx**: 1.18+ (反向代理)

### 1. 服务器准备

#### 系统更新
```bash
# Ubuntu
sudo apt update && sudo apt upgrade -y

# CentOS
sudo yum update -y
```

#### 安装Docker
```bash
# Ubuntu
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/1.29.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

#### 防火墙配置
```bash
# Ubuntu (UFW)
sudo ufw allow 22      # SSH
sudo ufw allow 80      # HTTP
sudo ufw allow 443     # HTTPS
sudo ufw enable

# CentOS (firewalld)
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

### 2. 项目部署

#### 下载项目
```bash
# 创建项目目录
sudo mkdir -p /www/wwwroot
cd /www/wwwroot

# 克隆项目 (需要配置SSH密钥)
<NAME_EMAIL>:your-org/csgoskins.com.cn.git
cd csgoskins.com.cn

# 设置目录权限
sudo chown -R $USER:$USER /www/wwwroot/csgoskins.com.cn
```

#### 环境配置
```bash
# 复制生产环境配置
cp server/example.env server/.env

# 编辑生产环境配置
vim server/.env
```

#### 生产环境配置 (.env)
```env
# 基本配置
DEBUG=False
ALLOWED_HOSTS=your-domain.com,www.your-domain.com,localhost

# 数据库配置 (使用云数据库)
DATABASE_URL=mysql://username:password@your-rds-host:3306/database_name

# Redis配置 (使用云缓存)
REDIS_URL=rediscache://:password@your-redis-host:6379/0

# 安全配置
SECRET_KEY=your-very-secret-key-here
CSRF_COOKIE_SECURE=True
SESSION_COOKIE_SECURE=True

# SSL配置
SECURE_SSL_REDIRECT=True
SECURE_HSTS_SECONDS=31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS=True

# 阿里云OSS (生产环境)
ALIYUN_OSS_BUCKET_NAME=your-production-bucket
ALIYUN_OSS_ACCESS_KEY_ID=your-access-key-id
ALIYUN_OSS_ACCESS_KEY_SECRET=your-access-key-secret
ALIYUN_OSS_ENDPOINT=https://oss-cn-hangzhou.aliyuncs.com

# 支付配置 (生产环境密钥)
ALIPAY_APP_ID=your-production-app-id
ALIPAY_PRIVATE_KEY=your-production-private-key
WECHAT_APP_ID=your-wechat-app-id
WECHAT_MCH_ID=your-mch-id

# 邮件配置
SMTP_SERVER=smtp.your-domain.com
SENDER_EMAIL=<EMAIL>
SMTP_TOKEN=your-smtp-token
SMTP_PORT=587

# 监控配置
SENTRY_DSN=your-sentry-dsn

# 性能配置
THWORKER_THREADS=4
GUNICORN_WORKERS=4
GUNICORN_MAX_REQUESTS=1000
```

### 3. 数据库初始化

#### MySQL数据库准备
```sql
-- 连接到MySQL服务器
mysql -h your-rds-host -u root -p

-- 创建数据库和用户
CREATE DATABASE csgoskins CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'csgoskins'@'%' IDENTIFIED BY 'strong-password';
GRANT ALL PRIVILEGES ON csgoskins.* TO 'csgoskins'@'%';
FLUSH PRIVILEGES;
```

#### 数据库迁移
```bash
# 构建镜像
docker-compose build

# 运行数据库迁移
docker-compose run --rm web python manage.py migrate

# 创建超级用户
docker-compose run --rm web python manage.py createsuperuser

# 收集静态文件
docker-compose run --rm web python manage.py collectstatic --noinput

# 加载初始数据
docker-compose run --rm web python manage.py loaddata initial_data.json
```

### 4. 启动服务

#### 启动所有服务
```bash
# 后台启动
docker-compose up -d

# 查看运行状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

#### 服务健康检查
```bash
# 检查Django服务
curl http://localhost:8000/api/monitor/health/

# 检查WebSocket服务
curl http://localhost:4000/

# 检查数据库连接
docker-compose exec web python manage.py dbshell

# 检查Redis连接
docker-compose exec web python -c "
from django.core.cache import cache
cache.set('test', 'value')
print(cache.get('test'))
"
```

### 5. Nginx配置

#### 安装Nginx
```bash
# Ubuntu
sudo apt install nginx -y

# CentOS
sudo yum install nginx -y
```

#### Nginx配置文件
```nginx
# /etc/nginx/sites-available/csgoskins.com.cn
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;
    
    # SSL证书配置
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # 安全头
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    
    # 客户端上传限制
    client_max_body_size 50M;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_comp_level 6;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # 静态文件
    location /static/ {
        alias /www/wwwroot/csgoskins.com.cn/server/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    location /media/ {
        alias /www/wwwroot/csgoskins.com.cn/server/media/;
        expires 1M;
        add_header Cache-Control "public";
    }
    
    # WebSocket代理
    location /socket.io/ {
        proxy_pass http://127.0.0.1:4000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # API和管理后台
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 健康检查
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
```

#### 启用Nginx配置
```bash
# 创建软链接
sudo ln -s /etc/nginx/sites-available/csgoskins.com.cn /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
sudo systemctl enable nginx
```

### 6. SSL证书配置

#### 使用Let's Encrypt (免费)
```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx -y

# 获取SSL证书
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# 自动续期
sudo crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

#### 使用商业SSL证书
```bash
# 将证书文件上传到服务器
sudo mkdir -p /etc/ssl/certs/
sudo mkdir -p /etc/ssl/private/

# 复制证书文件
sudo cp your-certificate.crt /etc/ssl/certs/
sudo cp your-private.key /etc/ssl/private/

# 设置权限
sudo chmod 644 /etc/ssl/certs/your-certificate.crt
sudo chmod 600 /etc/ssl/private/your-private.key
```

### 7. 监控和日志

#### 系统监控脚本
```bash
#!/bin/bash
# /opt/csgoskins/monitor.sh

# 检查服务状态
check_service() {
    if docker-compose ps | grep -q "Up"; then
        echo "✓ Services are running"
    else
        echo "✗ Some services are down"
        docker-compose up -d
    fi
}

# 检查磁盘空间
check_disk() {
    usage=$(df / | grep -vE '^Filesystem' | awk '{ print $5 }' | sed 's/%//g')
    if [ $usage -gt 80 ]; then
        echo "✗ Disk usage is ${usage}%"
    else
        echo "✓ Disk usage is ${usage}%"
    fi
}

# 清理旧日志
clean_logs() {
    find /www/wwwroot/csgoskins.com.cn/logs/ -name "*.log" -mtime +7 -delete
    docker system prune -f
}

check_service
check_disk
clean_logs
```

#### 设置定时任务
```bash
# 编辑crontab
sudo crontab -e

# 添加监控任务
*/5 * * * * /opt/csgoskins/monitor.sh >> /var/log/csgoskins-monitor.log 2>&1

# 每天凌晨备份数据库
0 2 * * * /opt/csgoskins/backup.sh

# 每周重启服务 (可选)
0 3 * * 0 cd /www/wwwroot/csgoskins.com.cn && docker-compose restart
```

### 8. 备份策略

#### 数据库备份脚本
```bash
#!/bin/bash
# /opt/csgoskins/backup.sh

DATE=$(date +"%Y%m%d_%H%M%S")
BACKUP_DIR="/backup/csgoskins"
DB_NAME="csgoskins"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
mysqldump -h your-rds-host -u username -p'password' $DB_NAME > $BACKUP_DIR/db_$DATE.sql

# 备份上传文件
tar -czf $BACKUP_DIR/media_$DATE.tar.gz /www/wwwroot/csgoskins.com.cn/server/media/

# 删除7天前的备份
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete

# 上传到云存储 (可选)
# aws s3 cp $BACKUP_DIR/ s3://your-backup-bucket/ --recursive
```

### 9. 性能优化

#### Docker配置优化
```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  web:
    build: ./server/
    command: gunicorn --config=server.conf --workers=4 --max-requests=1000 steambase.wsgi:application
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
    
  worker:
    build: ./server/
    command: python manage.py setupworker
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
    
  websocket:
    build: ./deployment/node
    command: node ws_server.js
    restart: unless-stopped
    environment:
      - NODE_ENV=production
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
```

#### 数据库优化
```sql
-- MySQL优化配置
-- /etc/mysql/mysql.conf.d/mysqld.cnf

[mysqld]
# InnoDB设置
innodb_buffer_pool_size = 2G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT

# 查询缓存
query_cache_type = 1
query_cache_size = 256M

# 连接设置
max_connections = 200
wait_timeout = 300
```

#### Redis优化
```conf
# /etc/redis/redis.conf

# 内存设置
maxmemory 1gb
maxmemory-policy allkeys-lru

# 持久化设置
save 900 1
save 300 10
save 60 10000

# 网络设置
tcp-keepalive 300
timeout 0
```

### 10. 安全加固

#### 系统安全
```bash
# 禁用root SSH登录
sudo vim /etc/ssh/sshd_config
# PermitRootLogin no
# PasswordAuthentication no

# 安装fail2ban
sudo apt install fail2ban -y

# 配置fail2ban
sudo vim /etc/fail2ban/jail.local
```

#### 应用安全
```python
# settings.py 安全配置
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_HSTS_SECONDS = 31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
X_FRAME_OPTIONS = 'DENY'
CSRF_COOKIE_SECURE = True
SESSION_COOKIE_SECURE = True
SESSION_COOKIE_HTTPONLY = True
```

### 11. 故障排除

#### 常见问题
1. **服务无法启动**
```bash
# 查看详细日志
docker-compose logs web
docker-compose logs worker
docker-compose logs websocket
```

2. **数据库连接失败**
```bash
# 检查数据库配置
docker-compose exec web python manage.py dbshell

# 检查网络连通性
telnet your-rds-host 3306
```

3. **Redis连接失败**
```bash
# 检查Redis连接
docker-compose exec web python -c "
from django_redis import get_redis_connection
r = get_redis_connection('default')
print(r.ping())
"
```

4. **静态文件404**
```bash
# 重新收集静态文件
docker-compose exec web python manage.py collectstatic --noinput

# 检查Nginx配置
sudo nginx -t
```

#### 紧急恢复
```bash
# 快速回滚到上一个版本
git checkout previous-stable-tag
docker-compose down
docker-compose up -d

# 从备份恢复数据库
mysql -h your-rds-host -u username -p'password' csgoskins < /backup/csgoskins/db_latest.sql
```

### 12. 更新部署

#### 零停机更新流程
```bash
#!/bin/bash
# /opt/csgoskins/deploy.sh

echo "开始部署..."

# 拉取最新代码
git pull origin main

# 备份当前版本
DATE=$(date +"%Y%m%d_%H%M%S")
cp docker-compose.yml docker-compose.yml.backup.$DATE

# 构建新镜像
docker-compose build

# 运行数据库迁移
docker-compose run --rm web python manage.py migrate

# 收集静态文件
docker-compose run --rm web python manage.py collectstatic --noinput

# 重启服务
docker-compose down
docker-compose up -d

# 健康检查
sleep 30
curl -f http://localhost:8000/api/monitor/health/ || {
    echo "部署失败，回滚..."
    cp docker-compose.yml.backup.$DATE docker-compose.yml
    docker-compose down
    docker-compose up -d
    exit 1
}

echo "部署成功！"
```

这份部署指南涵盖了生产环境的完整部署流程，包括安全配置、性能优化、监控备份等关键环节。请根据实际环境调整相关配置参数。
