<template>
  <div
    class="min-h-screen bg-gradient-to-br from-slate-950 via-slate-900 to-slate-950 relative overflow-hidden"
  >
    <!-- 整站风格背景装饰 -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      <!-- 动态光球效果 -->
      <div
        class="absolute top-20 left-20 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse"
      ></div>
      <div
        class="absolute top-40 right-32 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl animate-pulse"
        style="animation-delay: 1s"
      ></div>
      <div
        class="absolute bottom-40 left-1/3 w-72 h-72 bg-cyan-500/10 rounded-full blur-3xl animate-pulse"
        style="animation-delay: 2s"
      ></div>

      <!-- 网格背景 -->
      <div
        class="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:50px_50px]"
      ></div>

      <!-- 渐变叠加 -->
      <div
        class="absolute inset-0 bg-gradient-to-b from-transparent via-slate-900/50 to-slate-950/80"
      ></div>
    </div>

    <div class="relative z-10 container mx-auto px-4 py-8">
      <!-- 音效控制浮动面板 - 右上角 -->
      <div class="absolute top-4 right-4 z-20">
        <UiAudioController
          v-model:muted="isMuted"
          v-model:volume-value="volume"
          :show-test-buttons="false"
          :theme="'minimal'"
          @volume-change="setVolume"
          @mute-toggle="toggleMute"
        />
      </div>

      <!-- 页面标题 -->
      <div class="text-center mb-12">
        <div class="inline-flex items-center gap-4 mb-6">
          <div
            class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center"
          >
            <Icon name="material-symbols:casino" class="w-6 h-6 text-white" />
          </div>
          <h1
            class="title-i18n text-5xl font-bold bg-gradient-to-r from-blue-400 via-white to-purple-400 bg-clip-text text-transparent"
          >
            {{ battleInfo.shortId }}
          </h1>
          <div
            class="w-12 h-12 bg-gradient-to-r from-purple-500 to-blue-500 rounded-xl flex items-center justify-center"
          >
            <Icon
              name="material-symbols:sports-esports"
              class="w-6 h-6 text-white"
            />
          </div>
        </div>
      </div>

      <!-- 测试选项控制面板 -->
      <div class="mb-8 p-6 bg-white/5 backdrop-blur-sm rounded-2xl border border-white/10">
        <div class="text-center mb-4">
          <h2 class="text-xl font-bold text-white mb-2">测试配置</h2>
          <p class="text-gray-400 text-sm">调整对战参数进行不同场景测试</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- 人数选择 -->
          <div class="space-y-3">
            <label class="block text-sm font-medium text-white">玩家数量</label>
            <div class="flex gap-2">
              <button
                v-for="count in [2, 3, 4]"
                :key="count"
                @click="setPlayerCount(count)"
                :disabled="battleState !== 'waiting'"
                class="flex-1 py-3 px-4 rounded-lg font-medium transition-all duration-200"
                :class="
                  playerCount === count
                    ? 'bg-blue-500 text-white shadow-lg shadow-blue-500/30'
                    : 'bg-white/10 text-gray-300 hover:bg-white/20 border border-white/20'
                "
              >
                {{ count }} 人
              </button>
            </div>
          </div>

          <!-- 轮次选择 -->
          <div class="space-y-3">
            <label class="block text-sm font-medium text-white">对战轮次</label>
            <div class="flex gap-2">
              <button
                v-for="rounds in [1, 2, 3, 4]"
                :key="rounds"
                @click="setTotalRounds(rounds)"
                :disabled="battleState !== 'waiting'"
                class="flex-1 py-3 px-4 rounded-lg font-medium transition-all duration-200"
                :class="
                  totalRounds === rounds
                    ? 'bg-purple-500 text-white shadow-lg shadow-purple-500/30'
                    : 'bg-white/10 text-gray-300 hover:bg-white/20 border border-white/20'
                "
              >
                {{ rounds }} 轮
              </button>
            </div>
          </div>
        </div>

        <!-- 当前配置显示 -->
        <div class="mt-4 p-3 bg-white/5 rounded-lg">
          <div class="flex items-center justify-between text-sm">
            <span class="text-gray-400">当前配置:</span>
            <span class="text-white font-medium">
              {{ playerCount }}人 × {{ totalRounds }}轮 = {{ playerCount * totalRounds }}次开箱
            </span>
          </div>
        </div>

        <!-- 快速重置按钮 -->
        <div class="mt-4 flex justify-center">
          <button
            @click="resetTestConfig"
            :disabled="battleState !== 'waiting'"
            class="px-4 py-2 text-sm text-gray-400 hover:text-white transition-colors duration-200 disabled:opacity-50"
          >
            <Icon name="material-symbols:refresh" class="w-4 h-4 inline mr-1" />
            重置为默认配置
          </button>
        </div>
      </div>

      <!-- 对战信息面板 -->
      <div class="mb-8">
        <BattleInfo
          :battle-id="battleInfo.shortId"
          :battle-state="battleState"
          :created-at="battleInfo.createdAt"
          :current-round="currentRound"
          :total-rounds="totalRounds"
          :total-value="totalValue"
          :player-count="players.length"
          :case-count="displayCases.length"
          @copy-id="handleCopyId"
        />
      </div>

      <!-- 箱子展示区域 - 4:3 比例 -->
      <div class="mb-8">
        <BattleCaseDisplay
          :cases="displayCases"
          :show-total-value="true"
          :opening-case-id="openingCaseId"
          :opening-player-name="openingPlayerName"
          :current-round="currentRound"
          :total-rounds="totalRounds"
        />
      </div>

      <!-- 对战动画区域 - 已移动到用户列表中，这里隐藏 -->
      <!-- <div v-if="['battle', 'calculating'].includes(battleState)" class="mb-8">
        <BattleAnimation
          ref="battleAnimationRef"
          :players="players"
          :current-round="currentRound"
          :total-rounds="totalRounds"
          :is-animating="isAnimating"
          :show-round-info="true"
          @animation-complete="handleAnimationComplete"
          @animation-start="handleAnimationStart"
          @round-complete="handleRoundComplete"
          @opening-state-change="handleOpeningStateChange"
        />
      </div> -->

      <!-- 玩家列表 -->
      <BattlePlayerList
        :players="players"
        :max-players="4"
        :current-round="currentRound"
        :is-battle-finished="battleState === 'finished'"
        :current-round-results="currentRoundResults"
        :is-calculating="battleState === 'calculating'"
        :calculation-progress="calculationProgress"
        :current-player-id="openingPlayerIndex !== null ? players[openingPlayerIndex]?.id : null"
        :selected-cases="getSelectedCasesWithPlayerIds()"
        :battle-state="battleState"
        :opening-case-id="openingCaseId"
        :opening-player-name="openingPlayerName"
        :opening-player-index="openingPlayerIndex"
        @player-animation-start="handlePlayerAnimationStart"
        @player-animation-complete="handlePlayerAnimationComplete"
        class="flex-1"
        ref="battlePlayerListRef"
      />

      <!-- 计算进度组件 -->
      <div v-if="battleState === 'calculating'" class="mb-8">
        <CalculationProgress
          :progress="calculationProgress"
          title="整体计算进度"
          title-complete="计算完成"
          completion-message="计算完成！正在确定获胜者..."
          :show-steps="true"
          steps-title="计算过程"
          :custom-steps="[
            { text: '分析开箱数据...', active: false },
            { text: '统计物品价值...', active: false },
            { text: '计算玩家总分...', active: false },
            { text: '比较玩家成绩...', active: false },
            { text: '确定获胜者...', active: false },
            { text: '生成奖励分配...', active: false },
          ]"
        />
      </div>

      <!-- 胜利者庆祝特效 -->
      <BattleWinnerModal
        :visible="showWinnerModal && winner"
        :winner="winner"
        @close="handleWinnerModalClose"
      />

      <!-- 控制按钮区域 -->
      <div class="flex flex-wrap items-center justify-center gap-4">
        <button
          v-if="battleState === 'waiting'"
          @click="async () => { await playSound('buttonClick'); startAutoBattle(); }"
          :disabled="isOperating"
          class="group relative flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-bold rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-xl hover:shadow-blue-500/30 border border-blue-400/30 overflow-hidden disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <Icon name="material-symbols:play-arrow" class="w-6 h-6" />
          <span class="font-i18n">开始自动对战</span>
        </button>

        <button
          v-if="battleState === 'finished'"
          @click="async () => { await playSound('buttonClick'); resetBattle(); }"
          class="group relative flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white font-bold rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-xl hover:shadow-green-500/30 border border-green-400/30 overflow-hidden"
        >
          <Icon name="material-symbols:refresh" class="w-6 h-6" />
          <span class="font-i18n">重新开始</span>
        </button>

        <button
          @click="async () => { await playSound('buttonClick'); toggleAnimation(); }"
          class="group relative flex items-center gap-3 px-6 py-3 text-white font-medium rounded-xl transition-all duration-300 hover:scale-105 border border-white/20 hover:border-white/40 overflow-hidden"
          :class="
            animationEnabled
              ? 'bg-green-600 hover:bg-green-500'
              : 'bg-red-600 hover:bg-red-500'
          "
        >
          <Icon
            :name="
              animationEnabled
                ? 'material-symbols:animation'
                : 'material-symbols:animation-off'
            "
            class="w-5 h-5 transition-transform duration-300"
          />
          <span class="relative z-10 font-i18n">{{
            animationEnabled
              ? $t("demo.battle_real.animation_on")
              : $t("demo.battle_real.animation_off")
          }}</span>
          <div
            class="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
            :class="animationEnabled ? 'bg-green-500/20' : 'bg-red-500/20'"
          ></div>
        </button>

        <!-- 简单测试按钮 -->
        <button
          @click="async () => { await playSound('buttonClick'); testCalculate(); }"
          class="group relative flex items-center gap-3 px-6 py-3 text-white font-medium rounded-xl transition-all duration-300 hover:scale-105 border border-white/20 hover:border-white/40 overflow-hidden bg-purple-600 hover:bg-purple-500"
        >
          <Icon name="material-symbols:calculate" class="w-5 h-5 transition-transform duration-300" />
          <span class="relative z-10 font-i18n">测试计算</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
// SEO 元数据
useSeoMeta({
  title: "真实对战演示 - CSGO开箱网站",
  description: "完整的多轮对战系统演示，包含玩家管理、开箱动画、结果计算等功能",
  keywords: "CSGO对战演示,开箱动画,多轮对战,实时演示",
});

// 国际化
const { t } = useI18n();

// 响应式数据
const isDev = process.env.NODE_ENV === "development";
const isOperating = ref(false);
const animationEnabled = ref(true);

// 测试配置
const playerCount = ref(3); // 默认3人
const totalRounds = ref(3); // 默认3轮

// 对战状态
const battleState = ref("waiting"); // waiting, battle, calculating, finished
const currentRound = ref(1);
const isAnimating = ref(false);

// 计算进度
const calculationProgress = ref(0);
const calculationInterval = ref(null);

// 玩家显示价值（用于数字滚动动画）
const playerDisplayValues = ref({});

// 正在开箱的箱子状态
const openingCaseId = ref(null);
const openingPlayerName = ref("");
const openingPlayerIndex = ref(null);

// 获胜者
const winner = ref(null);

// 新增：控制胜利者弹窗显示状态
const showWinnerModal = ref(false);

// 音效管理
const { 
  isMuted, 
  volume, 
  playSound, 
  toggleMute, 
  setVolume,
  playBattleSequence 
} = useBattleAudio();

// 对战信息
const battleInfo = reactive({
  shortId: "A1B2C3",
  createdAt: new Date(),
  duration: 0,
});

// 玩家数据 - 简化版
const players = ref([
  {
    id: 1,
    nickname: "ProGamer2024",
    avatar: "/demo/avatar1.png",
    totalValue: 0,
    isHost: true,
    isWinner: false,
    animationData: null,
    currentRoundResult: null,
    openingHistory: [], // 添加开箱历史记录
  },
  {
    id: 2,
    nickname: "SkillMaster",
    avatar: "/demo/avatar2.png",
    totalValue: 0,
    isHost: false,
    isWinner: false,
    animationData: null,
    currentRoundResult: null,
    openingHistory: [], // 添加开箱历史记录
  },
  {
    id: 3,
    nickname: "CyberWarrior",
    avatar: "/demo/avatar3.png",
    totalValue: 0,
    isHost: false,
    isWinner: false,
    animationData: null,
    currentRoundResult: null,
    openingHistory: [], // 添加开箱历史记录
  },
]);

// 箱子数据 - 4:3 比例图片
const availableCases = ref([
  {
    id: 1,
    name: "武器箱",
    name_zh_hans: "武器箱",
    name_en: "Weapon Case",
    price: 2.5,
    image: "/demo/case1.png",
    description: "经典武器箱，包含多种热门武器皮肤",
  },
  {
    id: 2,
    name: "幻彩武器箱",
    name_zh_hans: "幻彩武器箱",
    name_en: "Chroma Case",
    price: 3.0,
    image: "/demo/case2.png",
    description: "炫酷的幻彩系列皮肤合集",
  },
  {
    id: 3,
    name: "Gamma武器箱",
    name_zh_hans: "Gamma武器箱",
    name_en: "Gamma Case",
    price: 4.5,
    image: "/demo/case3.png",
    description: "稀有的Gamma系列限定皮肤",
  },
  {
    id: 4,
    name: "光谱武器箱",
    name_zh_hans: "光谱武器箱",
    name_en: "Spectrum Case",
    price: 5.0,
    image: "/demo/case4.png",
    description: "炫彩光谱系列，稀有度极高",
  },
]);

// 组件引用
const battleAnimationRef = ref(null);
const battlePlayerListRef = ref(null);

// 计算属性
const displayCases = computed(() => {
  // 根据轮次数量动态调整箱子数量
  const caseCount = totalRounds.value;
  return availableCases.value.slice(0, caseCount);
});

// 获取带有玩家ID的箱子数据
const getSelectedCasesWithPlayerIds = () => {
  return players.value.map((player, index) => {
    // 根据当前轮次选择箱子
    const selectedCase = displayCases.value[(currentRound.value - 1) % displayCases.value.length];
    return {
      ...selectedCase,
      playerId: player.id,
      key: selectedCase.id
    };
  });
};

const totalValue = computed(() => {
  // 计算单个用户的总花费：箱子价格 × 轮次数量
  if (players.value.length === 0 || displayCases.value.length === 0) return 0;
  
  // 获取第一个玩家的箱子价格作为参考（所有玩家使用相同箱子）
  const selectedCase = displayCases.value[(currentRound.value - 1) % displayCases.value.length];
  const casePrice = selectedCase ? Number(selectedCase.price) || 0 : 0;
  
  // 单个用户的总花费 = 箱子价格 × 轮次数量
  const singleUserTotalSpent = casePrice * totalRounds.value;
  
  return singleUserTotalSpent;
});

// 玩家数据（包含开箱记录）
const playersWithOpeningRecords = computed(() => {
  return players.value.map((player) => {
    // console.log(`[🎰DEMO] 玩家 ${player.nickname} 开箱记录:`, {
    //   openingHistory: player.openingHistory?.length || 0,
    //   currentRoundResult: !!player.currentRoundResult,
    //   records:
    //     player.openingHistory?.map((r) => ({
    //       name: r.name,
    //       round: r.round,
    //       value: r.price,
    //     })) || [],
    // });

    return {
      ...player,
      id: player.nickname, // 使用昵称作为ID
      username: player.nickname,
      // 当前轮次结果
      currentRoundResult: player.currentRoundResult,
      // 开箱历史记录
      openingHistory: player.openingHistory || [],
    };
  });
});

// 当前轮次结果
const currentRoundResults = computed(() => {
  return players.value
    .filter((player) => player.currentRoundResult)
    .map((player) => ({
      playerId: player.nickname,
      roundResult: player.currentRoundResult,
    }));
});

// 方法
const formatDuration = (seconds) => {
  const minutes = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${minutes.toString().padStart(2, "0")}:${secs
    .toString()
    .padStart(2, "0")}`;
};

const handleCopyId = (id) => {
  // console.log(`[🎰DEMO] 对战ID已复制: ${id}`);
  // 这里可以显示复制成功的提示
};

// 处理添加开箱记录
const handleAddOpeningRecord = (data) => {
  // console.log("[🎰DEMO] 收到开箱记录:", data);

  const { playerId, roundResult } = data;

  // 找到对应的玩家
  const player = players.value.find(
    (p) => p.nickname === playerId || p.id === playerId
  );
  if (!player) {
    // console.warn("[🎰DEMO] 未找到玩家:", playerId);
    return;
  }

  // 添加到玩家的开箱历史记录
  if (!player.openingHistory) {
    player.openingHistory = [];
  }

  player.openingHistory.push({
    ...roundResult,
    round: currentRound.value,
    timestamp: Date.now(),
  });

  // console.log(
  //   `[🎰DEMO] 玩家 ${player.nickname} 开箱记录已添加，当前记录数: ${player.openingHistory.length}`
  // );
};

// 生成模拟开箱数据
const generateMockAnimationData = (player, playerIndex) => {
  // 🔧 修复：每一轮所有玩家都使用同一个箱子
  // 根据当前轮次选择箱子，确保每轮所有玩家使用相同箱子
  const selectedCase =
    displayCases.value[(currentRound.value - 1) % displayCases.value.length];

  // 生成模拟的箱子物品数据
  const caseItems = Array.from({ length: 50 }, (_, index) => ({
    id: index,
    name: `物品 ${index + 1}`,
    name_zh_hans: `物品 ${index + 1}`,
    name_en: `Item ${index + 1}`,
    image: `/demo/item${(index % 30) + 1}.png`,
    price: Math.random() * 500 + 10,
    rarity_color: [
      "#b0c3d9",
      "#5e98d9",
      "#4b69ff",
      "#8847ff",
      "#d32ce6",
      "#eb4b4b",
    ][Math.floor(Math.random() * 6)],
  }));

  return {
    selectedCase,
    caseItems,
  };
};

// 🔧 新增：为下一轮重新生成动画数据
const regenerateAnimationDataForNextRound = () => {
  console.log(`[🎰DEMO] 🔄 为第${currentRound.value}轮重新生成动画数据`);
  
  players.value.forEach((player, playerIndex) => {
    // 为每个玩家生成新的动画数据
    player.animationData = generateMockAnimationData(player, playerIndex);
    
    // 生成获胜物品
    const randomIndex = Math.floor(Math.random() * player.animationData.caseItems.length);
    player.animationData.dropItem = player.animationData.caseItems[randomIndex];
    
    console.log(`[🎰DEMO] 玩家 ${player.nickname} 第${currentRound.value}轮动画数据已生成`);
  });
};

// 开始自动对战
const startAutoBattle = async () => {
  console.log("[🎰DEMO] 开始自动对战");
  
  // 播放对战开始音效
  await playBattleSequence.battleStart();
  
  // 重置状态
  battleState.value = "waiting";
  currentRound.value = 1;
  players.value.forEach((player) => {
    player.openingHistory = [];
    player.totalValue = 0;
    player.isWinner = false;
    player.rewardItems = [];
  });
  
  // 🔧 修复：生成第一轮的动画数据
  regenerateAnimationDataForNextRound();
  
  // 进入对战状态
  battleState.value = "battle";
  isAnimating.value = true;
  isOperating.value = true;
  
  console.log("[🎰DEMO] ✅ 自动对战已开始，进入对战状态");
  
  // 🔧 修复：确保组件完全渲染后再触发动画
  await nextTick();
  // 等待更长时间确保所有子组件都完全渲染
  await new Promise(resolve => setTimeout(resolve, 1500));
  
  console.log(`[🎰DEMO] 准备触发第${currentRound.value}轮所有玩家同步动画`);
  // 触发BattlePlayerList组件的动画
  if (battlePlayerListRef.value && battlePlayerListRef.value.triggerAnimations) {
    console.log("[🎰DEMO] ✅ 找到BattlePlayerList组件，触发动画");
    battlePlayerListRef.value.triggerAnimations();
  } else {
    console.warn("[🎰DEMO] ⚠️ 未找到BattlePlayerList组件或triggerAnimations方法");
  }
};

const resetBattle = () => {
  console.log("[🎰DEMO] 重置对战");

  // 停止滚轮音效
  playBattleSequence.stopRollRun();

  // 播放重置音效
  playSound('buttonClick');

  // 重置对战状态
  battleState.value = "waiting";
  currentRound.value = 1;
  isOperating.value = false;
  isAnimating.value = false;
  calculationProgress.value = 0;
  winner.value = null;
  showWinnerModal.value = false; // 重置弹窗显示状态
  openingCaseId.value = null;
  openingPlayerName.value = "";
  openingPlayerIndex.value = null;

  // 重置玩家数据（保持玩家数量不变）
  players.value.forEach((player) => {
    player.totalValue = 0;
    player.isWinner = false;
    player.animationData = null;
    player.currentRoundResult = null;
    player.openingHistory = []; // 清空开箱历史记录
  });

  // 重置动画组件
  if (battleAnimationRef.value) {
    battleAnimationRef.value.resetAnimation();
  }
  
  console.log(`[🎰DEMO] 对战已重置，保持测试配置: ${playerCount.value}人 × ${totalRounds.value}轮`);
};

const toggleAnimation = () => {
  animationEnabled.value = !animationEnabled.value;
  // console.log("[🎰DEMO] 动画开关:", animationEnabled.value);
};

// 简单测试计算
const testCalculate = async () => {
  console.log("[🎰TEST] 点击测试计算按钮");
  
  // 为玩家添加一些数据
  players.value.forEach((player, index) => {
    player.totalValue = (index + 1) * 100; // 100, 200, 300
    player.openingHistory = [
      { name: `测试物品${index + 1}`, price: player.totalValue, round: 1 }
    ];
  });
  
  console.log("[🎰TEST] 设置玩家数据:", players.value.map(p => ({
    nickname: p.nickname,
    totalValue: p.totalValue
  })));
  
  // 直接调用计算
  await calculateFinalResult();
};

// 动画事件处理
const handleAnimationStart = ({ playerIndex }) => {
  // console.log(`[🎰DEMO] 玩家${playerIndex + 1}动画开始`);
  
  // 播放滚轮音效
  playBattleSequence.rollRun();
};

const handleAnimationComplete = ({ playerIndex, result }) => {
  console.log(`[🎰DEMO] BattleAnimation组件 - 玩家${playerIndex + 1}动画完成:`, result);

  // 停止滚轮音效
  playBattleSequence.stopRollRun();

  // 更新玩家总价值
  if (players.value[playerIndex]) {
    const player = players.value[playerIndex];
    player.totalValue += result.price || 0;

    // 设置当前轮次结果
    player.currentRoundResult = {
      ...result,
      round: currentRound.value,
      timestamp: Date.now(),
    };

    // 添加到开箱历史记录
    if (!player.openingHistory) {
      player.openingHistory = [];
    }

    player.openingHistory.push({
      ...result,
      round: currentRound.value,
      timestamp: Date.now(),
    });

    console.log(`[🎰DEMO] BattleAnimation组件 - 玩家 ${player.nickname} 第 ${currentRound.value} 轮开箱记录已添加`);
  }
  
  // 🔧 注意：这个方法不处理轮次切换，轮次切换由handlePlayerAnimationComplete处理
};

// 🔧 新增：处理开箱状态变化事件
const handleOpeningStateChange = ({ caseId, playerName, playerIndex }) => {
  // console.log(`[🎰DEMO] 开箱状态变化:`, { caseId, playerName, playerIndex });

  // 更新页面级别的开箱状态
  openingCaseId.value = caseId;
  openingPlayerName.value = playerName;
  openingPlayerIndex.value = playerIndex;

  if (caseId) {
    // console.log(`[🎰DEMO] ✅ 设置正在开箱箱子: ${caseId}，玩家: ${playerName}`);
  } else {
    // console.log(`[🎰DEMO] 🧹 清除开箱状态`);
  }
};

const handleRoundComplete = async ({ round, results }) => {
  console.log(`[🎰DEMO] 第${round}轮完成:`, results);

  // 停止滚轮音效
  playBattleSequence.stopRollRun();

  // 等待展示结果
  await new Promise((resolve) => setTimeout(resolve, 2000));

  // 🔧 注意：这个方法已被注释，轮次切换由handlePlayerAnimationComplete处理
  // 以下代码被注释掉，避免重复的轮次切换逻辑
  /*
  if (currentRound.value < totalRounds.value) {
    // 进入下一轮
    currentRound.value++;
    console.log(`[🎰DEMO] 开始第${currentRound.value}轮`);

    // 播放轮次开始音效
    await playBattleSequence.roundStart();

    // 🔧 修复：为下一轮重新生成动画数据
    regenerateAnimationDataForNextRound();

    // 重置动画组件
    if (battleAnimationRef.value) {
      battleAnimationRef.value.resetForNextRound();
    }

    // 清除当前轮次结果，保持动画数据不变
    players.value.forEach((player) => {
      player.currentRoundResult = null;
    });

    // 🔧 修复：重置开箱状态，确保下一轮从第一个玩家开始
    openingCaseId.value = null;
    openingPlayerName.value = "";
    openingPlayerIndex.value = null;
    console.log(`[🎰DEMO] 🔄 第${currentRound.value}轮开始，重置开箱状态`);

    // 🔧 修复：自动开始下一轮动画
    await nextTick();
    if (battleAnimationRef.value) {
      console.log(`[🎰DEMO] 自动开始第${currentRound.value}轮动画`);
      battleAnimationRef.value.startAnimation();
    }
  } else {
    // 对战结束，进入统计阶段
    console.log(`[🎰DEMO] 所有轮次完成，进入统计阶段`);
    await calculateFinalResult();
  }
  */
};

// 获取玩家开箱记录总数
const getTotalOpeningCount = (player) => {
  return player.openingHistory?.length || 0;
};

// 获取价值百分比（用于进度条）
const getValuePercentage = (value) => {
  const maxValue = Math.max(...players.value.map(p => p.totalValue || 0));
  return maxValue > 0 ? (value / maxValue) * 100 : 0;
};

// 显示玩家价值（带数字滚动动画）
const displayPlayerValue = (player, index) => {
  const targetValue = player.totalValue || 0;
  const currentValue = playerDisplayValues.value[player.id] || 0;
  
  // 如果计算进度为0，显示0
  if (calculationProgress.value === 0) {
    return '0.00';
  }
  
  // 如果计算完成，显示最终值
  if (calculationProgress.value >= 100) {
    return formatNumber(targetValue);
  }
  
  // 计算当前应该显示的值（基于进度）
  const progressRatio = calculationProgress.value / 100;
  const displayValue = targetValue * progressRatio;
  
  return formatNumber(displayValue);
};

// 启动玩家价值动画
const startPlayerValueAnimation = () => {
  console.log("[🎰CALC] 🚀 启动玩家价值动画");
  
  players.value.forEach(player => {
    const targetValue = player.totalValue;
    const startValue = 0;
    const duration = 5000; // 5秒动画
    const startTime = Date.now();
    
    console.log(`[🎰CALC] 🎯 玩家 ${player.nickname} 目标价值: $${targetValue}`);
    
    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);
      
      // 使用缓动函数
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);
      const currentValue = startValue + (targetValue - startValue) * easeOutQuart;
      
      playerDisplayValues.value[player.id] = currentValue;
      
      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        playerDisplayValues.value[player.id] = targetValue;
        console.log(`[🎰CALC] ✅ 玩家 ${player.nickname} 计算完成: $${targetValue}`);
      }
    };
    
    // 延迟启动，让进度条先开始
    const delay = 2000 + Math.random() * 2000; // 2-4秒随机延迟
    setTimeout(() => {
      console.log(`[🎰CALC] 🎬 玩家 ${player.nickname} 开始价值动画`);
      animate();
    }, delay);
  });
};

// 计算最终结果和获胜者
const calculateFinalResult = async () => {
  console.log("[🎰CALC] 开始计算最终结果");

  // 停止滚轮音效
  playBattleSequence.stopRollRun();

  // 播放计算开始音效
  await playSound('roundEnd');

  // 🔧 修复：进入计算阶段时清除开箱状态
  openingCaseId.value = null;
  openingPlayerName.value = "";
  openingPlayerIndex.value = null;
  console.log("[🎰CALC] 🧹 计算阶段清除开箱状态");

  // 🔧 修复：确保BattleAnimation组件的状态也被清除
  if (battleAnimationRef.value) {
    battleAnimationRef.value.resetAnimation();
    console.log("[🎰CALC] 🧹 清除BattleAnimation组件状态");
  }

  // 进入计算状态
  battleState.value = "calculating";
  isAnimating.value = false;
  calculationProgress.value = 0;
  
  console.log("[🎰CALC] ✅ 进入计算状态，battleState = 'calculating'");
  console.log("[🎰CALC] 📊 玩家数据:", players.value.map(p => ({
    nickname: p.nickname,
    totalValue: p.totalValue,
    openingHistory: p.openingHistory?.length || 0
  })));

  // 强制触发响应式更新
  await nextTick();
  
  // 计算每个玩家的总价值（基于开箱记录）
  players.value.forEach((player) => {
    const totalValue = player.openingHistory?.reduce((sum, record) => {
      return sum + (record.price || 0);
    }, 0) || 0;
    
    player.totalValue = totalValue;
    console.log(`[🎰CALC] 💰 ${player.nickname} 总价值: $${totalValue}`);
  });

  // 开始计算动画
  await startCalculationAnimation();
  
  // 计算完成后短暂停顿，展示计算完成状态
  console.log("[🎰CALC] ⏸️ 计算完成，开始短暂停顿");
  await new Promise((resolve) => setTimeout(resolve, 2000)); // 停顿2秒
  
  // 计算完成后确定获胜者
  const winnerPlayer = players.value.reduce((max, player) => {
    return (player.totalValue || 0) > (max.totalValue || 0) ? player : max;
  }, players.value[0]);

  console.log(`[🎰CALC] 🏆 获胜者: ${winnerPlayer.nickname}，价值: $${winnerPlayer.totalValue}`);

  // 设置获胜者标识
  players.value.forEach(player => {
    player.isWinner = player.id === winnerPlayer.id;
  });

  // 分配奖励物品
  players.value.forEach(player => {
    console.log(`[🎰CALC] 开始分配奖励给玩家 ${player.nickname}:`, {
      isWinner: player.isWinner,
      openingHistoryLength: player.openingHistory?.length || 0,
      currentRewardItems: player.rewardItems?.length || 0
    });
    
    if (player.isWinner) {
      // 获胜者获得所有玩家的开箱记录（除了自己的）
      const allItems = [];
      players.value.forEach(otherPlayer => {
        if (otherPlayer.id !== player.id && otherPlayer.openingHistory) {
          allItems.push(...otherPlayer.openingHistory);
        }
      });
      player.rewardItems = allItems;
      console.log(`[🎰CALC] 🎁 获胜者 ${player.nickname} 获得 ${allItems.length} 件饰品:`, allItems);
    } else {
      // 失败者获得系统赠送饰品
      const giftItem = {
        id: `gift-${player.id}-${Date.now()}`,
        name: "获取饰品",
        name_zh_hans: "获取饰品",
        name_en: "Acquired Item",
        image: `/demo/item${Math.floor(Math.random() * 30) + 1}.png`,
        price: Math.random() * 100 + 10,
        rarity_color: "#b0c3d9",
        rarity_name_zh_hans: "普通",
        rarity_name_en: "Common",
        exterior: "FN",
        quality: "consumer",
        isStatTrak: Math.random() > 0.8,
        isSystemGift: true,
      };
      player.rewardItems = [giftItem];
      console.log(`[🎰CALC] 🎁 失败者 ${player.nickname} 获得系统赠送饰品:`, giftItem);
    }
    
    console.log(`[🎰CALC] 玩家 ${player.nickname} 最终rewardItems:`, player.rewardItems);
  });

  // 进入完成状态
  battleState.value = "finished";
  winner.value = winnerPlayer;
  
  // 显示胜利者弹窗
  showWinnerModal.value = true;
  
  // 播放获胜者宣布音效
  await playBattleSequence.announceWinner();
  
  console.log("[🎰CALC] ✅ 计算完成，进入完成状态");
};

// 开始计算动画
const startCalculationAnimation = async () => {
  console.log("[🎰CALC] 🎬 开始计算动画");
  
  const animationDuration = 5000; // 5秒动画
  const updateInterval = 100; // 每100ms更新一次
  const totalSteps = animationDuration / updateInterval;
  let currentStep = 0;
  
  // 清除之前的定时器
  if (calculationInterval.value) {
    clearInterval(calculationInterval.value);
  }
  
  return new Promise((resolve) => {
    calculationInterval.value = setInterval(() => {
      currentStep++;
      const progress = Math.min((currentStep / totalSteps) * 100, 100);
      calculationProgress.value = progress;
      
      console.log(`[🎰CALC] 📊 计算进度: ${Math.round(progress)}%`);
      
      if (progress >= 100) {
        clearInterval(calculationInterval.value);
        calculationInterval.value = null;
        console.log("[🎰CALC] ✅ 计算动画完成，开始停顿");
        
        // 停顿1.5秒，展示"计算完成"状态
        setTimeout(() => {
          console.log("[🎰CALC] 🎉 停顿结束，计算完成");
          resolve();
        }, 1500);
      }
    }, updateInterval);
  });
};

// 组件生命周期
onMounted(() => {
  console.log("[🎰DEMO] 组件挂载完成");

  // 更新持续时间计时器
  const timer = setInterval(() => {
    if (battleState.value !== "waiting") {
      battleInfo.duration++;
    }
  }, 1000);

  onUnmounted(() => {
    clearInterval(timer);
    // 清理计算进度定时器
    if (calculationInterval.value) {
      clearInterval(calculationInterval.value);
      calculationInterval.value = null;
    }
  });
});

// 开始开箱动画
const startOpeningAnimation = async (playerIndex) => {
  const player = players.value[playerIndex];
  if (!player || !player.animationData) return;

  console.log(`[🎰DEMO] 玩家${player.nickname}开始开箱动画`);

  // 播放开箱开始音效
  await playBattleSequence.rollStart();

  // 设置开箱状态
  openingCaseId.value = player.animationData.selectedCase.key;
  openingPlayerName.value = player.nickname;
  openingPlayerIndex.value = playerIndex;

  // 模拟开箱动画完成
  setTimeout(async () => {
    // 播放开箱结束音效
    await playBattleSequence.rollEnd();

    // 清除开箱状态
    openingCaseId.value = null;
    openingPlayerName.value = "";
    openingPlayerIndex.value = null;

    // 添加开箱记录
    const dropItem = player.animationData.dropItem;
    player.openingHistory.push({
      round: currentRound.value,
      item: dropItem,
      timestamp: Date.now(),
    });

    // 更新总价值
    player.totalValue += dropItem.price || 0;

    console.log(
      `[🎰DEMO] 玩家${player.nickname}开箱完成，获得: ${dropItem.name_zh_hans}`
    );
  }, 3000);
};

// 处理胜利者弹窗关闭
const handleWinnerModalClose = () => {
  console.log("[🎰DEMO] 关闭胜利者庆祝特效");
  // 只隐藏弹窗，保持finished状态以显示奖励物品
  showWinnerModal.value = false;
  console.log("[🎰DEMO] 保持finished状态，奖励物品继续显示");
};

// 格式化数字显示
const formatNumber = (num) => {
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2
  }).format(num);
};

// 获取奖励总价值
const getRewardTotalValue = (player) => {
  if (!player.rewardItems) return 0;
  return player.rewardItems.reduce((total, item) => total + (Number(item.price) || 0), 0);
};

// 处理音效测试
const handleTestSound = async (soundType) => {
  console.log(`[🎰DEMO] 🎵 测试音效: ${soundType}`);
  try {
    await playSound(soundType);
  } catch (error) {
    console.warn(`[🎰DEMO] 音效测试失败: ${soundType}`, error);
  }
};

// 新增：处理玩家动画事件
const handlePlayerAnimationStart = (playerIndex) => {
  // console.log(`[🎰DEMO] 玩家${playerIndex + 1}动画开始`);
  
  // 播放滚轮音效
  playBattleSequence.rollRun();
};

const handlePlayerAnimationComplete = (playerIndex, result) => {
  console.log(`[🎰DEMO] 玩家${playerIndex + 1}动画完成:`, result);

  // 停止滚轮音效
  playBattleSequence.stopRollRun();

  // 更新玩家总价值
  if (players.value[playerIndex]) {
    const player = players.value[playerIndex];
    player.totalValue += result.price || 0;

    // 设置当前轮次结果
    player.currentRoundResult = {
      ...result,
      round: currentRound.value,
      timestamp: Date.now(),
    };

    // 添加到开箱历史记录
    if (!player.openingHistory) {
      player.openingHistory = [];
    }

    player.openingHistory.push({
      ...result,
      round: currentRound.value,
      timestamp: Date.now(),
    });

    console.log(`[🎰DEMO] 玩家 ${player.nickname} 第 ${currentRound.value} 轮开箱记录已添加`);
  }

  // 检查是否所有玩家都完成了当前轮次
  const allPlayersCompleted = players.value.every(player => player.currentRoundResult);
  
  if (allPlayersCompleted) {
    console.log(`[🎰DEMO] 第${currentRound.value}轮所有玩家完成，准备进入下一轮`);
    
    // 延迟一点时间展示结果
    setTimeout(async () => {
      if (currentRound.value < totalRounds.value) {
        // 进入下一轮
        await startNextRound();
      } else {
        // 所有轮次完成，进入计算阶段
        await startCalculationPhase();
      }
    }, 2000);
  }
};

// 新增：开始下一轮
const startNextRound = async () => {
  console.log(`[🎰DEMO] 准备开始第${currentRound.value + 1}轮`);
  
  // 播放轮次开始音效
  await playBattleSequence.roundStart();
  
  // 进入下一轮
  currentRound.value++;
  console.log(`[🎰DEMO] ✅ 当前轮次已更新为: ${currentRound.value}`);
  
  // 重新生成动画数据
  regenerateAnimationDataForNextRound();
  
  // 清除当前轮次结果
  players.value.forEach((player) => {
    player.currentRoundResult = null;
  });
  
  // 清除开箱状态
  openingCaseId.value = null;
  openingPlayerName.value = "";
  openingPlayerIndex.value = null;
  
  console.log(`[🎰DEMO] 第${currentRound.value}轮准备完成，等待动画组件重新渲染`);
  
  // 🔧 修复：确保组件完全渲染后再触发动画
  await nextTick();
  // 等待更长时间确保所有子组件都完全渲染
  await new Promise(resolve => setTimeout(resolve, 1500));
  
  console.log(`[🎰DEMO] 准备触发第${currentRound.value}轮所有玩家同步动画`);
  // 触发BattlePlayerList组件的动画
  if (battlePlayerListRef.value && battlePlayerListRef.value.triggerAnimations) {
    console.log("[🎰DEMO] ✅ 找到BattlePlayerList组件，触发动画");
    battlePlayerListRef.value.triggerAnimations();
  } else {
    console.warn("[🎰DEMO] ⚠️ 未找到BattlePlayerList组件或triggerAnimations方法");
  }
};

// 新增：开始计算阶段
const startCalculationPhase = async () => {
  console.log(`[🎰DEMO] 所有轮次完成，开始计算阶段`);
  
  // 停止滚轮音效
  playBattleSequence.stopRollRun();
  
  // 播放计算开始音效
  await playSound('roundEnd');
  
  // 清除开箱状态
  openingCaseId.value = null;
  openingPlayerName.value = "";
  openingPlayerIndex.value = null;
  
  // 进入计算状态
  battleState.value = "calculating";
  isAnimating.value = false;
  calculationProgress.value = 0;
  
  // 计算每个玩家的总价值
  players.value.forEach((player) => {
    const totalValue = player.openingHistory?.reduce((sum, record) => {
      return sum + (record.price || 0);
    }, 0) || 0;
    
    player.totalValue = totalValue;
    console.log(`[🎰CALC] 💰 ${player.nickname} 总价值: $${totalValue}`);
  });
  
  // 开始计算动画
  await startCalculationAnimation();
  
  // 计算完成后确定获胜者
  const winnerPlayer = players.value.reduce((max, player) => {
    return (player.totalValue || 0) > (max.totalValue || 0) ? player : max;
  }, players.value[0]);

  console.log(`[🎰CALC] 🏆 获胜者: ${winnerPlayer.nickname}，价值: $${winnerPlayer.totalValue}`);

  // 设置获胜者标识
  players.value.forEach(player => {
    player.isWinner = player.id === winnerPlayer.id;
  });

  // 分配奖励物品
  players.value.forEach(player => {
    console.log(`[🎰CALC] 开始分配奖励给玩家 ${player.nickname}:`, {
      isWinner: player.isWinner,
      openingHistoryLength: player.openingHistory?.length || 0,
      currentRewardItems: player.rewardItems?.length || 0
    });
    
    if (player.isWinner) {
      // 获胜者获得所有玩家的开箱记录（除了自己的）
      const allItems = [];
      players.value.forEach(otherPlayer => {
        if (otherPlayer.id !== player.id && otherPlayer.openingHistory) {
          allItems.push(...otherPlayer.openingHistory);
        }
      });
      player.rewardItems = allItems;
      console.log(`[🎰CALC] 🎁 获胜者 ${player.nickname} 获得 ${allItems.length} 件饰品:`, allItems);
    } else {
      // 失败者获得系统赠送饰品
      const giftItem = {
        id: `gift-${player.id}-${Date.now()}`,
        name: "获取饰品",
        name_zh_hans: "获取饰品",
        name_en: "Acquired Item",
        image: `/demo/item${Math.floor(Math.random() * 30) + 1}.png`,
        price: Math.random() * 100 + 10,
        rarity_color: "#b0c3d9",
        rarity_name_zh_hans: "普通",
        rarity_name_en: "Common",
        exterior: "FN",
        quality: "consumer",
        isStatTrak: Math.random() > 0.8,
        isSystemGift: true,
      };
      player.rewardItems = [giftItem];
      console.log(`[🎰CALC] 🎁 失败者 ${player.nickname} 获得系统赠送饰品:`, giftItem);
    }
    
    console.log(`[🎰CALC] 玩家 ${player.nickname} 最终rewardItems:`, player.rewardItems);
  });

  // 进入完成状态
  battleState.value = "finished";
  winner.value = winnerPlayer;
  
  // 显示胜利者弹窗
  showWinnerModal.value = true;
  
  // 播放获胜者宣布音效
  await playBattleSequence.announceWinner();
  
  console.log("[🎰CALC] ✅ 计算完成，进入完成状态");
};

// 测试配置相关方法
const setPlayerCount = (count) => {
  console.log(`[🎰TEST] 设置玩家数量: ${count}`);
  playerCount.value = count;
  
  // 重新生成玩家数据
  regeneratePlayers(count);
};

const setTotalRounds = (rounds) => {
  console.log(`[🎰TEST] 设置对战轮次: ${rounds}`);
  totalRounds.value = rounds;
  
  // 确保计算属性重新计算
  console.log(`[🎰TEST] 箱子数量已调整为: ${displayCases.value.length}个`);
  console.log(`[🎰TEST] 可用箱子:`, displayCases.value.map(c => c.name));
};

const resetTestConfig = () => {
  console.log(`[🎰TEST] 重置测试配置为默认值`);
  playerCount.value = 3;
  totalRounds.value = 3;
  
  // 重新生成玩家数据
  regeneratePlayers(3);
  
  // 确保计算属性重新计算
  console.log(`[🎰TEST] 箱子数量已重置为: ${displayCases.value.length}个`);
  console.log(`[🎰TEST] 可用箱子:`, displayCases.value.map(c => c.name));
};

// 重新生成玩家数据
const regeneratePlayers = (count) => {
  const playerNames = [
    "ProGamer2024",
    "SkillMaster", 
    "CyberWarrior",
    "ElitePlayer"
  ];
  
  const playerAvatars = [
    "/demo/avatar1.png",
    "/demo/avatar2.png", 
    "/demo/avatar3.png",
    "/demo/avatar4.png"
  ];
  
  const newPlayers = [];
  
  for (let i = 0; i < count; i++) {
    newPlayers.push({
      id: i + 1,
      nickname: playerNames[i],
      avatar: playerAvatars[i],
      totalValue: 0,
      isHost: i === 0, // 第一个玩家是房主
      isWinner: false,
      animationData: null,
      currentRoundResult: null,
      openingHistory: [],
    });
  }
  
  players.value = newPlayers;
  console.log(`[🎰TEST] 重新生成 ${count} 个玩家数据`);
};
</script>

<style scoped>
/* 音量滑块样式 */
.slider::-webkit-slider-thumb {
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider::-webkit-slider-track {
  background: linear-gradient(to right, #3b82f6, #8b5cf6);
  border-radius: 8px;
  height: 8px;
}

.slider::-moz-range-track {
  background: linear-gradient(to right, #3b82f6, #8b5cf6);
  border-radius: 8px;
  height: 8px;
}
</style>