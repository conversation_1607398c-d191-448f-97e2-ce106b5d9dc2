#!/bin/bash

# 任务监控脚本
# 用于监控Celery和ThWorker任务执行状态

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Celery Worker状态
check_celery_worker() {
    log_info "检查Celery Worker状态..."
    
    if docker exec csgoskins-celery-worker celery -A steambase inspect ping > /dev/null 2>&1; then
        log_success "Celery Worker运行正常"
        
        # 显示活跃任务
        echo "活跃任务:"
        docker exec csgoskins-celery-worker celery -A steambase inspect active
        
        # 显示注册的任务
        echo -e "\n注册的任务:"
        docker exec csgoskins-celery-worker celery -A steambase inspect registered
        
    else
        log_error "Celery Worker未响应"
    fi
}

# 检查Celery Beat状态
check_celery_beat() {
    log_info "检查Celery Beat状态..."
    
    # 检查容器是否运行
    if docker ps | grep csgoskins-celery-beat > /dev/null; then
        log_success "Celery Beat容器运行中"
        
        # 显示最近的日志
        echo "最近的Celery Beat日志:"
        docker logs --tail=20 csgoskins-celery-beat
        
    else
        log_error "Celery Beat容器未运行"
    fi
}

# 检查ThWorker状态
check_thworker() {
    log_info "检查ThWorker状态..."
    
    # 检查容器是否运行
    if docker ps | grep csgoskins-thworker > /dev/null; then
        log_success "ThWorker容器运行中"
        
        # 显示最近的日志
        echo "最近的ThWorker日志:"
        docker logs --tail=20 csgoskins-thworker
        
    else
        log_error "ThWorker容器未运行"
    fi
}

# 检查Redis连接
check_redis() {
    log_info "检查Redis连接..."
    
    if docker exec csgoskins-redis redis-cli ping > /dev/null 2>&1; then
        log_success "Redis连接正常"
        
        # 显示Redis信息
        echo "Redis信息:"
        docker exec csgoskins-redis redis-cli info server | head -10
        
        # 显示队列长度
        echo -e "\nCelery队列长度:"
        docker exec csgoskins-redis redis-cli llen celery
        
    else
        log_error "Redis连接失败"
    fi
}

# 显示任务统计
show_task_stats() {
    log_info "显示任务统计..."
    
    # Celery任务统计
    echo "Celery任务统计:"
    docker exec csgoskins-celery-worker celery -A steambase inspect stats 2>/dev/null || log_warning "无法获取Celery统计信息"
    
    # 显示定时任务
    echo -e "\n定时任务列表:"
    docker exec csgoskins-web python manage.py shell -c "
from django_celery_beat.models import PeriodicTask
for task in PeriodicTask.objects.filter(enabled=True):
    print(f'{task.name}: {task.crontab or task.interval}')
" 2>/dev/null || log_warning "无法获取定时任务列表"
}

# 重启任务服务
restart_task_services() {
    log_info "重启任务服务..."
    
    # 重启Celery服务
    docker-compose restart celery-worker celery-beat
    
    # 重启ThWorker
    docker-compose restart thworker
    
    log_success "任务服务重启完成"
    
    # 等待服务启动
    sleep 10
    
    # 重新检查状态
    check_celery_worker
    check_thworker
}

# 清理任务队列
clear_task_queues() {
    log_warning "清理任务队列..."
    
    # 清理Celery队列
    docker exec csgoskins-redis redis-cli flushdb
    
    log_success "任务队列已清理"
}

# 显示实时日志
show_live_logs() {
    log_info "显示实时任务日志..."
    
    # 显示所有任务相关容器的日志
    docker-compose logs -f celery-worker celery-beat thworker
}

# 主函数
main() {
    case "${1:-status}" in
        "status")
            log_info "检查所有任务服务状态"
            check_redis
            echo ""
            check_celery_worker
            echo ""
            check_celery_beat
            echo ""
            check_thworker
            echo ""
            show_task_stats
            ;;
        "worker")
            check_celery_worker
            ;;
        "beat")
            check_celery_beat
            ;;
        "thworker")
            check_thworker
            ;;
        "redis")
            check_redis
            ;;
        "stats")
            show_task_stats
            ;;
        "restart")
            restart_task_services
            ;;
        "clear")
            clear_task_queues
            ;;
        "logs")
            show_live_logs
            ;;
        *)
            echo "用法: $0 {status|worker|beat|thworker|redis|stats|restart|clear|logs}"
            echo "  status   - 检查所有任务服务状态（默认）"
            echo "  worker   - 检查Celery Worker状态"
            echo "  beat     - 检查Celery Beat状态"
            echo "  thworker - 检查ThWorker状态"
            echo "  redis    - 检查Redis连接"
            echo "  stats    - 显示任务统计"
            echo "  restart  - 重启任务服务"
            echo "  clear    - 清理任务队列"
            echo "  logs     - 显示实时任务日志"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
