DEBUG=True
ALLOWED_HOSTS=*
DATABASE_URL=mysql://csgoskins:<EMAIL>/csgoskins

STEAM_API_KEY=6B81028E346FF7F5756537FC7D595361

PROXY_ENABLED=False
HTTPS_PROXY=
HTTP_PROXY=
SOCKS_PROXY=

REDIS_URL=rediscache://@127.0.0.1:6379/0

ENABLE_LOG_CONFIG_FILE=False
LOG_CONFIG_FILE_PATH=

JACKPOT_GAME_COUNTDOWN=5
JACKPOT_GAME_WAIT=10
JACKPOT_BET_AMOUNT_MAX=1000

DOUBLE_GAME_COUNTDOWN=5
DOUBLE_GAME_WAIT=10
DOUBLE_BET_AMOUNT_MAX=1000

ROLL_ROOM_CREATE_MAX=3
CASE_ROOM_CREATE_MAX=3

DEPOSIT_EXCHANGE_AUTO=True
PACKAGE_EXCHANGE_AUTO=True
PACKAGE_EXCHANGE_DELAY=60

ITEM_PRICE_CURRENCY=CNY
PACKAGE_ITEM_PRICE_MIN=0.1
PACKAGE_ITEM_PRICE_RATE=1
SHOP_ITEM_PRICE_RATE=1
BOX_ITEM_PRICE_RATE=1

CHARGE_PAY_AMOUNT_MIN=1
CHARGE_PAY_AMOUNT_MAX=20000

PROMOTION_PROFIT_PICK_UNIT=5
PROMOTION_PROFIT_PICK_IMMEDIATELY=False
PROMOTION_NEW_USER_REWARD=1

TRADE_TEST=False
CHARGE_TEST=False

CHAT_MESSAGE_LAST_COUNT=10

WAXPEER_API_KEY=



ALISMS_ACCESS_KEY_ID=
ALISMS_ACCESS_KEY_SECRET=
ALISMS_SIGN_NAME=
ALISMS_TEMPLATE_CODE=



WEIXIN_SMS_APPKEY=
WEIXIN_SMS_SECRET=


QYB_USERNAME=
QYB_PASSWORD=


LOK_USERNAME=17318918880
LOK_PASSWORD=Bb123456!!
LOK_TOKEN=38564c62
LOK_TEMPLATE=B5A10A79

ZBT_APP_KEY=ebd107df98179c121e343134fde87451
ZBT_APP_SECRET=c02364cb99a349fdab93b534e6eeb3fa


HUPI_WEXIN_PAY_APPID=2021112900587
HUPI_WEXIN_PAY_SECRET_KEY=3269508c5030e6938b467937ec9e28b4
HUPI_ALIPAY_APPID=2021112900594
HUPI_ALIPAY_SECRET_KEY=60a0f8fe9e7658745b04dc186281b96d
HUPIJIAO_NOTIFY_URL=https://www.csgoskins.com.cn/api/charge/hppay/notify/
HUPIJIAO_RETURN_URL=https://www.csgoskins.com.cn/
HUPIJIAO_CALLBACK_URL=https://www.csgoskins.com.cn/

JIUJIA_PAY_APPID=55ebe802987681
JIUJIA_PAY_SECRET_KEY=01fc6874985e847ef7
JIUJIA_PAY_NOTIFY_URL=https://www.csgoskins.com.cn/api/charge/jjpay/notify/
JIUJIA_PAY_RETURN_URL=https://www.csgoskins.com.cn/
JIUJIA_PAY_CALLBACK_URL=https://www.csgoskins.com.cn/api/charge/jjpay/notify/


CXKA_API_KEY=
CXKA_API_SECRET=
CXKA_DOMAIN=
CXKA_CALLBACK_URL=https://www.m4skins.com/api/charge/cxkapay/notify/


ALIPAY_APP_ID=
ALIPAY_NOTIFY_URL=
ALI_PAY_URL='https://openapi.alipay.com/gateway.do'


WEXIN_PAY_APPID=
WEXIN_PAY_MCH_ID=
WEXIN_PAY_API_KEY=
WEXIN_PAY_NOTIFY_URL=


CUSTOMBOX_MIN_ITEMS=2


SMTP_SERVER='smtp.exmail.qq.com'
SENDER_EMAIL='<EMAIL>'
SMTP_TOKEN='Kehufuwu618!!'
SMTP_PORT='465'