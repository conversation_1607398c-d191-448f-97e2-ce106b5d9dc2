<template>
  <div class="battle-detail-skeleton">
    <!-- 头部骨架 -->
    <div class="skeleton-header">
      <div class="skeleton-info">
        <div class="skeleton-line w-32 h-6"></div>
        <div class="skeleton-line w-24 h-4 mt-2"></div>
      </div>
      <div class="skeleton-avatars">
        <div class="skeleton-avatar" v-for="i in 4" :key="i"></div>
      </div>
      <div class="skeleton-actions">
        <div class="skeleton-button" v-for="i in 3" :key="i"></div>
      </div>
    </div>
    
    <!-- 状态骨架 -->
    <div class="skeleton-state">
      <div class="skeleton-line w-40 h-5"></div>
      <div class="skeleton-progress mt-2"></div>
    </div>
    
    <!-- 箱子骨架 -->
    <div class="skeleton-cases">
      <div class="skeleton-case" v-for="i in 5" :key="i">
        <div class="skeleton-case-image"></div>
        <div class="skeleton-line w-20 h-4 mt-2"></div>
      </div>
    </div>
    
    <!-- 玩家骨架 -->
    <div class="skeleton-players">
      <div class="skeleton-player" v-for="i in 4" :key="i">
        <div class="skeleton-player-header">
          <div class="skeleton-avatar"></div>
          <div class="skeleton-line w-16 h-4 ml-2"></div>
        </div>
        <div class="skeleton-rounds">
          <div class="skeleton-round" v-for="j in 5" :key="j"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 纯展示骨架屏，无props
</script>

<style scoped lang="scss">
.battle-detail-skeleton {
  @apply flex flex-col gap-8 px-4 py-8 max-w-5xl mx-auto w-full;
  
  .skeleton-header {
    @apply flex items-center justify-between gap-6 mb-4 flex-wrap;
    
    .skeleton-info {
      @apply flex flex-col gap-1 flex-1 min-w-[120px];
    }
    .skeleton-avatars {
      @apply flex gap-2 flex-1 min-w-[120px] justify-center;
      .skeleton-avatar {
        @apply w-10 h-10 rounded-full;
      }
    }
    .skeleton-actions {
      @apply flex gap-2 flex-1 min-w-[120px] justify-end;
      .skeleton-button {
        @apply w-16 h-8 rounded;
      }
    }
  }
  
  .skeleton-state {
    @apply flex flex-col gap-2 mb-4;
    .skeleton-line {
      @apply mb-1;
    }
    .skeleton-progress {
      @apply w-full h-2 rounded;
    }
  }
  
  .skeleton-cases {
    @apply grid grid-cols-5 gap-4 mb-4;
    .skeleton-case {
      @apply flex flex-col items-center gap-2;
      .skeleton-case-image {
        @apply w-16 h-16 rounded-lg;
      }
    }
  }
  
  .skeleton-players {
    @apply grid grid-cols-4 gap-4;
    .skeleton-player {
      @apply flex flex-col gap-2 items-center p-4 rounded-lg bg-gray-800/40;
      .skeleton-player-header {
        @apply flex items-center gap-2 mb-2;
        .skeleton-avatar {
          @apply w-8 h-8 rounded-full;
        }
        .skeleton-line {
          @apply w-16 h-4;
        }
      }
      .skeleton-rounds {
        @apply flex gap-1;
        .skeleton-round {
          @apply w-6 h-6 rounded bg-gray-700;
        }
      }
    }
  }
}

// 骨架动画
.skeleton-line,
.skeleton-avatar,
.skeleton-button,
.skeleton-case-image,
.skeleton-round,
.skeleton-progress {
  @apply bg-gradient-to-r from-gray-800 via-gray-700 to-gray-800;
  background-size: 200% 100%;
  animation: skeleton-shimmer 1.5s ease-in-out infinite;
}

@keyframes skeleton-shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

// 响应式布局
@media (max-width: 1024px) {
  .battle-detail-skeleton {
    .skeleton-cases {
      @apply grid-cols-3;
    }
    .skeleton-players {
      @apply grid-cols-2;
    }
  }
}
@media (max-width: 640px) {
  .battle-detail-skeleton {
    @apply px-1 py-4;
    .skeleton-header {
      @apply flex-col gap-2 items-stretch;
    }
    .skeleton-cases {
      @apply grid-cols-2;
    }
    .skeleton-players {
      @apply grid-cols-1;
    }
  }
}
</style> 