from django.shortcuts import render
from django.utils.translation import gettext_lazy as _
import logging
import oss2
import uuid
from django.http import JsonResponse
from django.conf import settings

from django.views.decorators.csrf import csrf_exempt



from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import AllowAny
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON>, JSONParser
from sitecfg.models import SEO

from agent.business import get_agent_site, get_agent_site_user, get_agent_site_user_charge_records, get_agent_site_user_withdraw_records, get_agent_site_user_skin_records, manage_article_category, get_article_category, manage_agent_article, get_article_categories, get_article, get_article_list, delete_article, edit_site, get_site_info, get_site_article_list, get_agent_balance_records, get_agent_info, edit_agent_info, agent_withdrawal, get_agent_withdrawal_records
from agent.models import ArticleCategory, Agent

from steambase.enums import RespCode
from steambase.utils import reformat_resp

# 初始化 OSS 认证
auth = oss2.Auth(settings.ALIYUN_OSS_ACCESS_KEY_ID, settings.ALIYUN_OSS_ACCESS_KEY_SECRET)
bucket = oss2.Bucket(auth, settings.ALIYUN_OSS_ENDPOINT, settings.ALIYUN_OSS_BUCKET_NAME)


logging.getLogger("oss2").setLevel(logging.WARNING)
logging.getLogger("oss2").setLevel(logging.WARNING)

_logger = logging.getLogger(__name__)

# Create your views here.

class CheckAgentView(APIView):

    def get(self, request):
        try:
            user = request.user

            if user.is_agent:

                is_agent = True
            else:
                is_agent = False
            #print(is_agent)
            return reformat_resp(RespCode.Succeed.value, is_agent, 'Succeed')
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')

class AgentSiteListView(APIView):
    

    def get(self, request):
        try:
            user = request.user
            fields =( 'id', 'name', 'url', 'title', 'subtitle', 'keywords', 'description', 'icp', 'enable')
            code, resp = get_agent_site(user, fields)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            return reformat_resp(RespCode.Exception.value, {}, _('Exception'))
        
class AgentSiteUserView(APIView):

    def get(self, request):
        try:
            user = request.user
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            fields = ('create_time', 'email', 'phone', 'domain', 'is_active', 'reg_ip', 'login_ip', 'balance', 'total_charge_balance', 'personaname')
            code, resp = get_agent_site_user(user, fields, page, page_size)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, _('Exception'))
    

    
class AgentSiteUserChargeRecordView(APIView):

    def get(self, request):
        try:
            user = request.user
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            fields = ('create_time', 'personaname', 'amount', 'out_trade_no', 'pay_type', 'state', 'pay_time', 'domain')
            code, resp = get_agent_site_user_charge_records(user, fields, page, page_size)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, _('Exception'))

class AgentSiteUserWithdrawRecordView(APIView):

    def get(self, request):
        try:
            user = request.user
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            fields = ('create_time', 'personaname', 'amount', 'out_trade_no', 'skin_name', 'state', 'domain')
            code, resp = get_agent_site_user_withdraw_records(user, fields, page, page_size)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, _('Exception'))
        
class AgentSiteUserSkinRecordView(APIView):

    def get(self, request):
        try:
            user = request.user
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            fields = ('create_time', 'personaname', 'amount', 'source', 'skin_name', 'state', 'domain')
            code, resp = get_agent_site_user_skin_records(user, fields, page, page_size)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, _('Exception'))

class ManageAgentArticleCategoryView(APIView):
    permission_classes = (AllowAny,)

    def post(self, request):
        user = request.user
        site_id = request.data.get('site')
        category = request.data.get('category')
        category_id = request.data.get('id')

        code, message = manage_article_category(user, site_id, category, category_id)
        return reformat_resp(code, {}, message)

class GetAgentArticleCategoryView(APIView):
    permission_classes = (AllowAny,)

    def get(self, request, id):
        try:
            code, resp  = get_article_category(id)
            if code == RespCode.Succeed.value:
                    return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, _('Exception'))
    

class AgentArticleCategoryListView(APIView):
    permission_classes = (AllowAny,)

    def get(self, request):
        try:
            user = request.user
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            code, resp = get_article_categories(user, page, page_size)
            return reformat_resp(code, resp, 'Succeed')
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, 'Exception')
        
class ManageAgentArticleView(APIView):
    permission_classes = (AllowAny,)

    def post(self, request):
        try:
            user = request.user
            title = request.data.get('title')
            content = request.data.get('content')
            category_id = request.data.get('category')
            article_id = request.data.get('id')
            if not title or not content or not category_id:
                return reformat_resp(RespCode.InvalidParams.value, {}, 'Invalid parameters')
            code, resp = manage_agent_article(user, title, content, category_id, article_id)
            return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, 'Exception')
        
class GetAgentArticleView(APIView):
    permission_classes = (AllowAny,)

    def get(self, request, id):
        try:
            code, resp  = get_article(id)
            if code == RespCode.Succeed.value:
                    return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, _('Exception'))

class GetAgentArticleListView(APIView):
    permission_classes = (AllowAny,)

    def get(self, request):
        try:
            user = request.user
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            try:
                category = int(request.query_params.get('category'))
            except:
                category = None
            code, resp = get_article_list(user, page, page_size, category)
            return reformat_resp(code, resp, 'Succeed')
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, 'Exception')

class DeleteAgentArticleView(APIView):

    def post(self, request):
        try:
            user = request.user
            article_id = request.data.get('id')
            #print(article_id)
            code, resp = delete_article(user, article_id)
            return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, 'Exception')

class EditSiteView(APIView):
    permission_classes = (AllowAny,)

    def post(self, request):
        try:
            user = request.user
            site_id = request.data.get('id')
            subtitle = request.data.get('subtitle')
            title = request.data.get('title')
            keywords = request.data.get('keywords')
            description = request.data.get('description')
            code, resp = edit_site(user, site_id, subtitle, title, keywords, description)
            return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, 'Exception')
        
class GetSiteDetailView(APIView):
    permission_classes = (AllowAny,)

    def get(self, request, id):
        try:
            user = request.user
            code, resp = get_site_info(user, id)
            return reformat_resp(code, resp, 'Succeed')
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, 'Exception')
        
class GetAgentSiteArticleListView(APIView):
    permission_classes = (AllowAny,)

    def get(self, request):
        try:
            site = request.query_params.get('site')
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            code, resp = get_site_article_list(site, page, page_size)
            return reformat_resp(code, resp, 'Succeed')
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, 'Exception')
        
class GetAgentBalanceRecordsView(APIView):

    def get(self, request):
        try:
            user = request.user
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))

            # 获取Agent实例
            agent = Agent.objects.filter(user=user).first()
            if not agent:
                return reformat_resp(RespCode.NotFound.value, {}, 'Agent not found')

            code, resp = get_agent_balance_records(agent, page, page_size)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, _('Exception'))

class GetAgentInfoView(APIView):

    def get(self, request):
        try:
            user = request.user
            code, resp = get_agent_info(user)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, _('Exception'))
        
class EditAgentInfoView(APIView):

    def post(self, request):
        try:
            user = request.user
            agent_name = request.data.get('agent_name')
            agent_phone = request.data.get('agent_phone')
            agent_email = request.data.get('agent_email')
            agent_qq = request.data.get('agent_qq')
            agent_wechat = request.data.get('agent_wechat')
            agent_alipay = request.data.get('agent_alipay')
            agent_wechatpay = request.data.get('agent_wechatpay')
            agent_bankname = request.data.get('agent_bankname')
            agent_bankcard = request.data.get('agent_bankcard')
            agent_bank = request.data.get('agent_bank')
            
            code, resp = edit_agent_info(user, agent_name, agent_phone, agent_email, agent_qq, agent_wechat, agent_alipay, agent_wechatpay, agent_bankname, agent_bankcard, agent_bank)
            return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, _('Exception'))

class AgentWithdrawalView(APIView):
    
        def post(self, request):
            try:
                user = request.user
                amount = request.data.get('amount')
                pay_type = request.data.get('pay_type')
                if not amount:
                    return reformat_resp(RespCode.InvalidParams.value, {}, 'Invalid parameters')
                code, resp = agent_withdrawal(user, amount, pay_type)
                return reformat_resp(code, {}, resp)
            except Exception as e:
                _logger.exception(e)
                return reformat_resp(RespCode.Exception.value, {}, _('Exception'))

class GetAgentWithdrawalRecordsView(APIView):
    
        def get(self, request):
            try:
                user = request.user
                page = int(request.query_params.get('page', 1))
                page_size = int(request.query_params.get('pageSize', 10))
    
                # 获取Agent实例
                agent = Agent.objects.filter(user=user).first()
                if not agent:
                    return reformat_resp(RespCode.NotFound.value, {}, 'Agent not found')
    
                code, resp = get_agent_withdrawal_records(agent, page, page_size)
                if code == RespCode.Succeed.value:
                    return reformat_resp(code, resp, 'Succeed')
                else:
                    return reformat_resp(code, {}, resp)
            except Exception as e:
                _logger.exception(e)
                return reformat_resp(RespCode.Exception.value, {}, _('Exception'))

@csrf_exempt       
def upload_to_oss(request):
    if request.method == 'POST':
        file_obj = request.FILES.get('file')
        file_type = request.POST.get('type')
        if not file_obj:
            return JsonResponse({'error': 'No file provided'}, status=400)

        # 指定上传目录，例如 'uploads/'
        if file_type == 'image':
            directory = 'agent_image/'
        elif file_type == 'video':
            directory = 'agent_video/'
        else:
            directory = 'agent_upload/'
         # 获取文件后缀
        file_extension = file_obj.name.split('.')[-1]

        # 生成唯一文件名
        unique_filename = f"{uuid.uuid4()}.{file_extension}"

        # 完整文件路径
        file_name = directory + unique_filename
        try:
            # 上传文件到 OSS
            result = bucket.put_object(file_name, file_obj)
            file_url = f'https://{settings.ALIYUN_OSS_BUCKET_NAME}.{settings.ALIYUN_OSS_ENDPOINT}/{file_name}'
            return JsonResponse({'file_url': file_url}, status=200)
        except oss2.exceptions.OssError as e:
            return JsonResponse({'error': str(e)}, status=500)
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)

    return JsonResponse({'error': 'Invalid request method'}, status=405)