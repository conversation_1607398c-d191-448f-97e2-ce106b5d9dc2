# -*- coding: utf-8 -*-
# Generated by Django 1.11.13 on 2021-04-14 02:09
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import steambase.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODELL),
    ]

    operations = [
        migrations.CreateModel(
            name='ChargeLevel',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('amount', models.FloatField(default=0, verbose_name='amount')),
                ('level', models.IntegerField(default=0, verbose_name='level')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='charge_level', to=settings.AUTH_USER_MODELL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'Charge Level',
                'verbose_name_plural': 'Charge Level',
                'ordering': ('-create_time',),
            },
        ),
        migrations.CreateModel(
            name='ChargeLevelConfig',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('level', models.IntegerField(default=0, verbose_name='level')),
                ('min_amount', models.FloatField(default=0, verbose_name='min amount')),
                ('max_amount', models.FloatField(default=0, verbose_name='max amount')),
            ],
            options={
                'verbose_name': 'Charge Level Config',
                'verbose_name_plural': 'Charge Level Config',
                'ordering': ('-create_time',),
            },
        ),
        migrations.CreateModel(
            name='ChargeRecord',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('out_trade_no', models.CharField(max_length=64, unique=True, verbose_name='out trade No')),
                ('amount', models.FloatField(default=0.0, verbose_name='amount')),
                ('currency', models.CharField(default='USD', max_length=4, verbose_name='currency')),
                ('state', models.SmallIntegerField(choices=[(0, 'Initialed'), (1, 'Actived'), (2, 'Succeed'), (3, 'Failed'), (4, 'Canceled')], default=0, verbose_name='status')),
                ('pay_type', models.SmallIntegerField(choices=[(1, 'WeChat'), (2, 'Ali')], default=1, verbose_name='pay type')),
                ('pay_amount', models.FloatField(default=0.0, verbose_name='pay amount')),
                ('pay_time', models.DateTimeField(blank=True, default=None, null=True, verbose_name='pay time')),
                ('nonce', models.CharField(blank=True, default=None, max_length=16, null=True, verbose_name='nonce')),
                ('timestamp', models.CharField(blank=True, default=None, max_length=16, null=True, verbose_name='timestamp')),
                ('clientIp', models.CharField(blank=True, default=None, max_length=128, null=True, verbose_name='client ip')),
                ('qr_code', models.CharField(blank=True, default=None, max_length=128, null=True, verbose_name='qr code')),
                ('pay_url', models.CharField(blank=True, default=None, max_length=1024, null=True, verbose_name='pay url')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='charges', to=settings.AUTH_USER_MODELL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'Charge Record',
                'verbose_name_plural': 'Charge Record',
                'ordering': ('-create_time',),
            },
        ),
        migrations.CreateModel(
            name='ChargeStatisticsDay',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('date', models.DateField(default=django.utils.timezone.now, verbose_name='date')),
                ('amount', models.FloatField(default=0.0, verbose_name='amount')),
            ],
            options={
                'verbose_name': 'Charge Statistics Day',
                'verbose_name_plural': 'Charge Statistics Day',
                'ordering': ('-create_time',),
            },
        ),
        migrations.CreateModel(
            name='ChargeStatisticsMonth',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('date', models.DateField(default=django.utils.timezone.now, verbose_name='date')),
                ('amount', models.FloatField(default=0.0, verbose_name='amount')),
            ],
            options={
                'verbose_name': 'Charge Statistics Month',
                'verbose_name_plural': 'Charge Statistics Month',
                'ordering': ('-create_time',),
            },
        ),
        migrations.CreateModel(
            name='ChargeWeekRank',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('year', models.SmallIntegerField(default=0, verbose_name='year')),
                ('week', models.SmallIntegerField(default=0, verbose_name='week')),
                ('amount', models.FloatField(default=0, verbose_name='amount')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='charge_week_rank', to=settings.AUTH_USER_MODELL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'Charge Week Rank',
                'verbose_name_plural': 'Charge Week Rank',
            },
        ),
        migrations.CreateModel(
            name='CxkaGenerate',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('count', models.SmallIntegerField(default=0, null=True, verbose_name='generate count')),
            ],
            options={
                'verbose_name': 'Cxka Generate',
                'verbose_name_plural': 'Cxka Generate',
            },
        ),
        migrations.CreateModel(
            name='CxkaGoods',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('good_id', models.CharField(default=None, max_length=128, verbose_name='Cxka good id')),
                ('good_name', models.CharField(default=None, max_length=128, verbose_name='Cxka good name')),
                ('price', models.FloatField(default=None, null=True, verbose_name='price')),
            ],
            options={
                'verbose_name': 'Cxka goods',
                'verbose_name_plural': 'Cxka goods',
                'ordering': ('good_id',),
            },
        ),
    ]
