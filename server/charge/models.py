from django.db import models, transaction
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
from decimal import Decimal



from steambase.enums import ChargeState, PayType
from steambase.models import ModelBase, USER_MODEL, ItemBase
from steambase.utils import current_year, current_week


class ChargeRecord(ModelBase):
    CHARGE_STATUS = (
        (ChargeState.Initialed.value, _('Initialed')),
        (ChargeState.Actived.value, _('Actived')),
        (ChargeState.Succeed.value, _('Succeed')),
        (ChargeState.Failed.value, _('Failed')),
        (ChargeState.Canceled.value, _('Canceled')),
    )
    PAY_TYPE = (
        (PayType.Wechat.value, _('WeChat')),
        (PayType.Ali.value, _('Ali')),
        (PayType.Unionpay.value, _('Union Pay')),
        (PayType.OtherPay.value, _('Other Pay')),        
    )
    user = models.ForeignKey(USER_MODEL, on_delete=models.CASCADE, verbose_name=_('user'), related_name='charges')
    out_trade_no = models.CharField(_('out trade No'), max_length=64, unique=True)
    amount = models.FloatField(_('amount'), default=0.0)
    currency = models.CharField(_('currency'), max_length=4, default='USD')
    state = models.SmallIntegerField(_('status'), default=ChargeState.Initialed.value, choices=CHARGE_STATUS)
    pay_type = models.SmallIntegerField(_('pay type'), default=PayType.Wechat.value, choices=PAY_TYPE)
    pay_amount = models.FloatField(_('pay amount'), default=0.0)
    pay_time = models.DateTimeField(_("pay time"), default=None, null=True, blank=True)
    nonce = models.CharField(_("nonce"), max_length=16, default=None, null=True, blank=True)
    timestamp = models.CharField(_("timestamp"), max_length=16, default=None, null=True, blank=True)
    clientIp = models.CharField(_("client ip"), default=None, max_length=128, null=True, blank=True)

    qr_code = models.TextField(default=None, max_length=1024, null=True, verbose_name='qr code')
    pay_url = models.TextField(default=None, max_length=1024, null=True, verbose_name='pay url')

    
    paymethod = models.CharField(default=None, null=True, blank=True, max_length=128, verbose_name='pay method')

    
    
    
    
    



    class Meta:
        ordering = ('-create_time',)
        verbose_name = _('Charge Record')
        verbose_name_plural = _('Charge Record')

    def __str__(self):
        return self.out_trade_no


class ChargeStatisticsDay(ModelBase):
    date = models.DateField(_("date"), default=timezone.now)
    amount = models.FloatField(_("amount"), default=0.0)

    @classmethod
    def update_amount(cls, amount):
        """
        更新当天的金额记录。

        参数:
        - amount (float | Decimal): 需要增加或减少的金额。
        """
        with transaction.atomic():
            # 获取当前日期
            today = timezone.localdate()

            # 如果当天记录不存在，则创建记录
            if not cls.objects.filter(date=today).exists():
                cls.objects.create(date=today, amount=Decimal('0.00'))

            # 锁定当天记录以防并发问题
            record = cls.objects.select_for_update().get(date=today)

            # 将金额转换为 Decimal 类型，并保留两位小数
            amount = Decimal(str(amount)).quantize(Decimal('0.00'))

            # 更新金额
            record.amount = Decimal(record.amount) + amount
            record.save()

    def __str__(self):
        return str(self.date)

    class Meta:
        ordering = ('-create_time',)
        verbose_name = _('Charge Statistics Day')
        verbose_name_plural = _('Charge Statistics Day')


class ChargeStatisticsMonth(ModelBase):
    date = models.DateField(_("date"), default=timezone.now)
    amount = models.FloatField(_("amount"), default=0.0)

    class Meta:
        ordering = ('-create_time',)
        verbose_name = _('Charge Statistics Month')
        verbose_name_plural = _('Charge Statistics Month')

    @classmethod
    def update_amount(cls, amount):
        """
        更新当月的金额记录。

        参数:
        - amount (float | Decimal): 需要增加或减少的金额。
        """
        with transaction.atomic():
            # 获取当前日期
            today = timezone.localdate()
            # 获取当月第一天
            month_day_first = today.replace(day=1)

            # 如果记录不存在，则创建记录
            if not cls.objects.filter(date=month_day_first).exists():
                cls.objects.create(date=month_day_first)

            # 锁定当前月的记录
            record = cls.objects.select_for_update().get(date=month_day_first)

            # 将金额统一为 Decimal 类型
            amount = Decimal(str(amount)).quantize(Decimal('0.00'))

            # 更新金额
            record.amount = Decimal(record.amount) + amount
            record.save()

    def __str__(self):
        return self.month()

    def month(self):
        if isinstance(self.date, str):
            return self.date
        return self.date.strftime("%Y-%m")

    month.short_description = _('Month')


class ChargeWeekRank(ModelBase):
    user = models.ForeignKey(USER_MODEL, on_delete=models.CASCADE, verbose_name=_('user'), related_name='charge_week_rank')
    year = models.SmallIntegerField(_('year'), default=0)
    week = models.SmallIntegerField(_('week'), default=0)
    amount = models.FloatField(_('amount'), default=0)

    class Meta:
        verbose_name = _('Charge Week Rank')
        verbose_name_plural = _('Charge Week Rank')

    @classmethod
    def update_amount(cls, user, amount):
        """
        更新用户本周的金额记录。

        参数:
        - user (User): 关联的用户实例。
        - amount (float | Decimal): 需要增加或减少的金额。
        """
        with transaction.atomic():
            # 获取当前年份和周数
            year = current_year()
            week = current_week()

            # 如果本周记录不存在，则创建记录
            if not cls.objects.filter(user=user, year=year, week=week).exists():
                cls.objects.create(user=user, year=year, week=week, amount=Decimal('0.00'))

            # 锁定当前周的记录以防并发问题
            record = cls.objects.select_for_update().get(user=user, year=year, week=week)

            # 确保金额为 Decimal 类型，并保留两位小数
            amount = Decimal(str(amount)).quantize(Decimal('0.00'))

            # 更新金额
            record.amount = Decimal(record.amount) + amount
            record.save()

    def __str__(self):
        return self.uid


class ChargeLevel(ModelBase):
    user = models.ForeignKey(USER_MODEL, on_delete=models.CASCADE, verbose_name=_('user'), related_name='charge_level')
    amount = models.FloatField(_('amount'), default=0)
    level = models.IntegerField(_("level"), default=0)

    class Meta:
        ordering = ('-create_time',)
        verbose_name = _('Charge Level')
        verbose_name_plural = _('Charge Level')

    def __str__(self):
        return str(self.id)


class ChargeLevelConfig(ModelBase):
    level = models.IntegerField(_("level"), default=0)
    min_amount = models.FloatField(_('min amount'), default=0)
    max_amount = models.FloatField(_('max amount'), default=0)

    class Meta:
        ordering = ('-create_time',)
        verbose_name = _('Charge Level Config')
        verbose_name_plural = _('Charge Level Config')

    def __str__(self):
        return str(self.id)


class CxkaGoods(ModelBase):
    good_id = models.CharField(_('Cxka good id'), max_length=128, default=None)
    good_name = models.CharField(_('Cxka good name'), max_length=128, default=None)
    price = models.FloatField(_('price'), default=None, null=True)

    class Meta:
        ordering = ('good_id',)
        verbose_name = _('Cxka goods')
        verbose_name_plural = _('Cxka goods')

    def __str__(self):
        return self.good_name


class CxkaGenerate(models.Model):
    count = models.SmallIntegerField(_('generate count'), default=0, null=True)

    class Meta:
        verbose_name = _('Cxka Generate')
        verbose_name_plural = _('Cxka Generate')

    def __str__(self):
        return str(self.count)


class ChargeAmountConfig(models.Model):
    coins = models.FloatField(_('Coins'), default=0)
    sort_order = models.SmallIntegerField(_('sort order'), default=9)
    enable = models.BooleanField(_('enable'), default=True)
    image = models.ImageField(_('image'), upload_to='charge', default=None, null=True, blank=True)

    class Meta:
        verbose_name = _('ChargeAmountConfig')
        verbose_name_plural = _('ChargeAmountConfig')

    def __str__(self):
        return "充值金额：" + str(self.coins)


class ChargeHandselConfig(ModelBase):
    level = models.IntegerField(_("level"), default=0)
    min_amount = models.FloatField(_('min amount'), default=0)
    max_amount = models.FloatField(_('max amount'), default=0)
    handsel = models.FloatField(_('handsel'), default=0)

    class Meta:
        ordering = ('-create_time',)
        verbose_name = _('充值赠送金额配置')
        verbose_name_plural = _('充值赠送金额配置')

    def __str__(self):
        return str(self.id)


# 生成CDKey
class GenerateCDKey(ModelBase):
    amount = models.FloatField(_("amount"), default=0)
    count = models.SmallIntegerField(_("count"), default=0)

    class Meta:
        verbose_name = _("generate CDKey")
        verbose_name_plural = _("generate CDKey")

    def __str__(self):
        return "金额:{}".format(self.amount)


# CDKey兑换记录
class CDKeyRecord(ModelBase):
    user = models.ForeignKey(USER_MODEL, on_delete=models.CASCADE, verbose_name=_("user"))
    key = models.CharField(_("CDKey"), max_length=128, default=None, null=True)
    amount = models.FloatField(_("amount"), default=0)

    class Meta:
        verbose_name = _("CDKey record")
        verbose_name_plural = _("CDKey record")


# CDKey信息
class CDKey(ModelBase):
    CDKEY_STATE = (
        (0, _("used")),  # 已使用
        (1, _("unused"))  # 未使用
    )
    key = models.CharField(_("CDKey"), max_length=128, unique=True)
    amount = models.FloatField(_("amount"), default=0, null=True, blank=True)
    state = models.SmallIntegerField(_("Key state"), choices=CDKEY_STATE, default=1)

    class Meta:
        verbose_name = _("CDKey info")
        verbose_name_plural = _("CDKey info")

    def __str__(self):
        return self.key

# 支付方式
class PayMethod(ModelBase):
    PAY_TYPE = (
        (PayType.Wechat.value, _('WeChat')),
        (PayType.Ali.value, _('Ali')),
        (PayType.Unionpay.value, _('Union Pay')),
        (PayType.OtherPay.value, _('Other Pay')), 
    )
    name = models.CharField(_("name"), max_length=128, default=None, null=True)
    sort_order = models.SmallIntegerField(_("sort order"), default=0)
    # enable是否启用 unlock控制前端是否显示
    enable = models.BooleanField(_("enable"), default=True)
    unlock = models.BooleanField(_("unlock"), default=True)
    suggest = models.CharField(_("suggest"), max_length=128, default=None, null=True)
    type = models.SmallIntegerField(_("type"), default=PayType.Wechat.value, choices=PAY_TYPE)
    color = models.CharField(_("color"), max_length=128, default=None, null=True)

    # 每日限额
    daily_limit = models.DecimalField(_("daily limit"), max_digits=10, decimal_places=2, default=0)
    # 单笔限额
    single_limit = models.DecimalField(_("single limit"), max_digits=10, decimal_places=2, default=0)
    # 今日收款合计
    # 当月收款合计
    today_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    month_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    # 费率
    rate = models.DecimalField(
    _("rate"),
    max_digits=4,  # 总共允许 5 位数字
    decimal_places=3,  # 小数点后允许 2 位数字
    default=0.000
    )

    class Meta:
        verbose_name = _("PayMethod")
        verbose_name_plural = _("PayMethod")

    def __str__(self):
        return self.name
    
    def update_today_amount(self, amount):
        """
        更新 today's amount。如果 amount 无效则抛出异常。
        """
        # 验证 amount 是否为有效数字
        if not isinstance(amount, (int, float, Decimal)):
            raise ValidationError("Amount must be a number.")

        with transaction.atomic():
            # 确保数据是最新的
            if self.pk:
                self.refresh_from_db(fields=['today_amount'])

            # 初始化 today_amount 为 0（如果为 None），并增加 amount（转换为 Decimal 类型）
            self.today_amount = (self.today_amount or Decimal(0)) + Decimal(amount)
            # 检查是否超过每日限额
            if self.daily_limit > 0 and self.today_amount >= self.daily_limit:
                self.enable = False  # 禁用支付方式
                self.unlock = False  # 前端不显示该支付方式
            self.save(update_fields=['today_amount', 'enable', 'unlock'])



    def update_month_amount(self, amount):
        """
        更新 month's amount。如果 amount 无效则抛出异常。
        """
        # 验证 amount 是否为有效数字
        if not isinstance(amount, (int, float, Decimal)):
            raise ValidationError("Amount must be a number.")

        with transaction.atomic():
            # 确保数据是最新的
            if self.pk:
                self.refresh_from_db(fields=['month_amount'])

            # 初始化 month_amount 为 0（如果为 None），并增加 amount（转换为 Decimal 类型）
            self.month_amount = (self.month_amount or Decimal(0)) + Decimal(amount)
            self.save(update_fields=['month_amount'])
        

   