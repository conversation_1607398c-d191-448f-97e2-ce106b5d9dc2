# ASGI 配置：支持 HTTP 和 WebSocket
import os
import django
from channels.routing import ProtocolTypeRouter, URLRouter
from channels.auth import AuthMiddlewareStack
from django.core.handlers.asgi import ASGIHandler
import websocket.routing

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')
django.setup()

application = ProtocolTypeRouter({
    # HTTP 请求通过 Django ASGI 处理器
    'http': ASGIHandler(),
    # WebSocket 请求路由到 BattleConsumer
    'websocket': AuthMiddlewareStack(
        URLRouter(
            websocket.routing.websocket_urlpatterns
        )
    ),
})
