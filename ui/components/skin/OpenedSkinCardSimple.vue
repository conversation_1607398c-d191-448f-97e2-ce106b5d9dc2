<template>
  <div
    class="opened-skin-card bg-gray-800/90 backdrop-blur-md border border-gray-700/50 rounded-md overflow-hidden hover:border-primary/40 transition-all duration-300 group relative h-full"
    :style="cardBorderStyle"
  >
    <!-- 稀有度背景光效 -->
    <div
      class="absolute inset-0 opacity-20 group-hover:opacity-30 transition-opacity duration-300"
      :style="cardInlineStyle"
    ></div>

    <!-- 装饰性网格背景 -->
    <div class="absolute inset-0 bg-grid opacity-10"></div>

    <div class="relative z-10 p-4 h-full flex flex-col">
      <!-- StatTrak 角标 -->
      <div
        v-if="isStatTrak"
        class="absolute left-2 top-2 z-20 bg-secondary/90 rounded-md px-2 py-1 text-xs font-bold text-white shadow-lg backdrop-blur-sm border border-secondary/50"
      >
        <span class="mr-1">⚡</span>ST
      </div>

      <!-- 外观标签 -->
      <div
        v-if="
          getLocalizedNameForSkin(
            openRecord?.item_info?.item_exterior,
            'exterior_name'
          )
        "
        class="absolute right-2 top-2 z-20 text-xs font-bold text-gray-400 shadow-lg transition-all duration-300 px-2 py-1 rounded-md"
      >
        {{
          getLocalizedNameForSkin(
            openRecord?.item_info?.item_exterior,
            "exterior_name"
          )
        }}
      </div>

      <!-- 物品图片区域 -->
      <div class="flex-1 flex items-center justify-center mb-3">
        <div
          class="relative group-hover:scale-110 transition-transform duration-300"
        >
          <!-- 物品图片背景光效 -->
          <div
            class="absolute inset-0 blur-lg opacity-30"
            :style="cardInlineStyle"
          ></div>

          <img
            :src="
              openRecord?.item_info?.image || '/images/item-placeholder.svg'
            "
            :alt="openRecord?.item_info?.name"
            class="relative z-10 h-16 w-16 sm:h-20 sm:w-20 object-contain drop-shadow-lg top-4"
            @error="handleImageError"
          />
        </div>
      </div>

      <!-- 物品信息 -->
      <div class="text-center">
        <div
          class="text-sm sm:text-sm text-white truncate py-2 px-2 overflow-hidden text-ellipsis whitespace-nowrap"
          :title="fullName"
        >
          {{ getSkinName }}
        </div>
      </div>

      <!-- 价格和品质 -->
      <div class="flex items-center justify-between text-xs">
        <span class="text-primary text-sm font-bold">
          ${{ (openRecord?.item_info?.item_price?.price || 0).toFixed(2) }}
        </span>

        <span
          class="text-gray-400 text-sm text-white/70 truncate"
          :style="{ color: openRecord.item_info.item_rarity.rarity_color }"
        >
          {{
            getLocalizedNameForSkin(
              openRecord?.item_info?.item_rarity,
              "rarity_name"
            )
          }}
        </span>
      </div>

      <!-- 悬停时显示更多信息 -->
      <div
        class="absolute inset-1 bg-black/80 opacity-0 group-hover:opacity-100 transition-all duration-300 flex flex-col items-center justify-center p-3 z-30 rounded-md"
      >
        <div class="text-center">
          <div class="text-sm font-bold text-white mb-2">
            {{ getSkinName }}
          </div>
          <div class="text-xs text-white/80 mb-1">
            {{
              getLocalizedNameForSkin(
                openRecord?.item_info?.item_exterior,
                "exterior_name"
              )
            }}
          </div>
          <div class="text-xs text-primary font-medium">
            <span class="mr-1">👆</span>
            {{ t("live_openings.click_for_details") }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, watch, nextTick } from "vue";
import type { CaseRecord } from "~/services/monitor-api";

interface Props {
  openRecord: CaseRecord;
}

const props = defineProps<Props>();

// 使用新的语言管理系统
const { currentLocale } = useLanguage();
const { t, locale } = useI18n();

// 处理图片加载错误
const handleImageError = (e: Event) => {
  const target = e.target as HTMLImageElement;
  target.src = "/images/item-placeholder.svg";
};

// 根据语言获取本地化名称（响应式函数）
const getLocalizedNameForSkin = (item: any, fieldName: string): string => {
  if (!item) return "";

  const currentLang = locale.value === "zh-hans" ? "zh_hans" : locale.value;
  const itemWithI18n = item as any;

  // 获取本地化字段
  let localizedValue = item[fieldName] || "";

  if (currentLang === "zh_hans") {
    localizedValue =
      itemWithI18n[`${fieldName}_zh_hans`] ||
      itemWithI18n[`${fieldName}_zh`] ||
      item[fieldName] ||
      "";
  } else if (currentLang === "en") {
    localizedValue = itemWithI18n[`${fieldName}_en`] || item[fieldName] || "";
  }

  return localizedValue;
};

// 分离武器名称和皮肤名称（支持多语言，响应式计算）
const getSkinName = computed((): string => {
  if (!props.openRecord?.item_info) {
    return t("live_openings.mystery_item") || "Unknown Item";
  }

  // 获取本地化的完整名称（这会响应语言变化）
  const localizedFullName = getLocalizedNameForSkin(
    props.openRecord.item_info,
    "name"
  );

  // 如果没有本地化名称，使用原始名称作为回退
  const nameToSplit =
    localizedFullName || props.openRecord.item_info.name || "";

  if (!nameToSplit) {
    return t("live_openings.mystery_item") || "Unknown Item";
  }

  const parts = nameToSplit.split(" | ");

  if (parts.length < 2) return nameToSplit;
  const skinPart = parts[1].trim();
  return skinPart.split(" (")[0].trim();
});

// 是否是StatTrak
const isStatTrak = computed(() => {
  if (!props.openRecord?.item_info?.name) return false;
  return props.openRecord.item_info.name.includes("StatTrak™");
});

// 完整名称（武器+皮肤名，响应式计算）
const fullName = computed(() => {
  if (!props.openRecord?.item_info) {
    return t("live_openings.mystery_item") || "Unknown Item";
  }

  // 获取本地化的完整名称（会响应语言变化）
  const localizedName = getLocalizedNameForSkin(
    props.openRecord.item_info,
    "name"
  );
  return localizedName || t("live_openings.mystery_item") || "Unknown Item";
});

// 卡片边框样式
const cardBorderStyle = computed(() => {
  const rarity = props.openRecord?.item_info?.item_rarity;
  if (!rarity?.rarity_color) return {};

  return {
    borderColor: rarity.rarity_color + "80",
  };
});

// 卡片内联样式（光效）
const cardInlineStyle = computed(() => {
  const rarity = props.openRecord?.item_info?.item_rarity;
  if (!rarity?.rarity_color) return {};

  return {
    background: `radial-gradient(circle, ${rarity.rarity_color}20 0%, transparent 70%)`,
  };
});

// 监听语言变化，重新渲染组件
watch(locale, () => {
  // 触发响应式更新
  nextTick();
});
</script>

<style lang="scss" scoped>
// 背景纹理
.bg-grid {
  background-image: linear-gradient(
      rgba(255, 255, 255, 0.03) 1px,
      transparent 1px
    ),
    linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
  background-size: 20px 20px;
}

// 卡片悬停效果
.opened-skin-card {
  position: relative;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  }
}

// 稀有度光效
.group:hover .absolute.opacity-20 {
  opacity: 0.4 !important;
}

// 图片容器
.drop-shadow-lg {
  filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.3));
}

// 文本截断
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 响应式调整
@media (max-width: 640px) {
  .opened-skin-card {
    .p-4 {
      padding: 0.75rem;
    }

    .h-16 {
      height: 3rem;
      width: 3rem;
    }

    .text-sm {
      font-size: 0.75rem;
    }
  }
}
</style>
