/**
 * 战斗详情页状态管理
 * 管理页面的基础状态、计算属性和数据
 */
import { ref, computed } from 'vue'
import type { Ref, ComputedRef } from 'vue'

export interface BattleDetailState {
  // 状态数据
  isEventListenersSetup: Ref<boolean>
  showCalculationProgress: Ref<boolean>
  calculationProgress: Ref<number>
  calculationSteps: Ref<Array<{ text: string; active: boolean }>>
  hasCalculated: Ref<boolean>
  hasShownWinnerModal: Ref<boolean>
  showWinnerModal: Ref<boolean>
  winnerData: Ref<any>
  caseItemsCache: Ref<Map<string, any[]>>
  currentCaseItems: Ref<any[]>
  roundResults: Ref<Array<{ round: number; playerIndex: number; item: any }>>
  
  // 计算属性
  getOpeningCaseId: ComputedRef<string | null>
  battleData: ComputedRef<any>
  currentRound: ComputedRef<number>
  totalRounds: ComputedRef<number>
  battleStateString: ComputedRef<string>
  isUserCreator: ComputedRef<boolean>
  isUserJoined: ComputedRef<boolean>
  isBattleFinished: ComputedRef<boolean>
  isBattleInProgress: ComputedRef<boolean>
  isBattleWaiting: ComputedRef<boolean>
  isBattleStarted: ComputedRef<boolean>
  getPlayerCaseItems: ComputedRef<any[]>
  getCurrentCaseDetail: ComputedRef<any>
}

export function useBattleDetailState(): BattleDetailState {
  const battleCore = useBattleCore()
  const roundManager = useBattleRoundManager()
  const userStore = useUserStore()

  // 🎯 状态数据
  const isEventListenersSetup = ref(false)
  const showCalculationProgress = ref(false)
  const calculationProgress = ref(0)
  const calculationSteps = ref([
    { text: '收集轮次数据...', active: false },
    { text: '计算总价值...', active: false },
    { text: '比较玩家结果...', active: false },
    { text: '确定获胜者...', active: false },
    { text: '生成最终结果...', active: false },
  ])
  const hasCalculated = ref(false)
  const hasShownWinnerModal = ref(false)
  const showWinnerModal = ref(false)
  const winnerData = ref<any>(null)
  const caseItemsCache = ref<Map<string, any[]>>(new Map())
  const currentCaseItems = ref<any[]>([])
  const roundResults = ref<Array<{ round: number; playerIndex: number; item: any }>>([])

  // 🎯 计算属性
  const getOpeningCaseId = computed(() => {
    const openingCaseId = battleCore.battleState.openingCaseId.value
    const isInProgress = battleCore.battleState.isBattleInProgress.value
    
    if (!isInProgress) return null
    return openingCaseId
  })

  const battleData = computed(() => battleCore.battleState.battleData.value)
  const currentRound = computed(() => roundManager.currentRound.value)
  const totalRounds = computed(() => roundManager.totalRounds.value)

  const battleStateString = computed(() => {
    const state = battleCore.battleState.battleData.value?.state
    switch (state) {
      case 1: return 'waiting'
      case 5: return 'in_progress'
      case 11: return 'finished'
      default: return 'unknown'
    }
  })

  const isUserCreator = computed(() => {
    const battle = battleCore.battleState.battleData.value
    const currentUser = userStore.user
    return !!(battle?.creator_id && currentUser?.uid && battle.creator_id === currentUser.uid)
  })

  const isUserJoined = computed(() => {
    const battle = battleCore.battleState.battleData.value
    const currentUser = userStore.user
    if (!battle?.players || !currentUser?.uid) return false
    return battle.players.some((player: any) => player.user_id === currentUser.uid)
  })

  const isBattleFinished = computed(() => battleCore.battleState.isBattleFinished.value)
  const isBattleInProgress = computed(() => battleCore.battleState.isBattleInProgress.value)
  const isBattleWaiting = computed(() => battleCore.battleState.isBattleWaiting.value)

  const isBattleStarted = computed(() => {
    const apiState = battleCore.battleState.battleData.value?.state
    return apiState === 5 || apiState === 11 // 5 = 进行中, 11 = 已结束
  })

  const getPlayerCaseItems = computed(() => {
    console.log("[🎰BATTLE-PAGE] getPlayerCaseItems调试:", {
      currentCaseItemsLength: currentCaseItems.value?.length || 0,
      currentCaseItems: currentCaseItems.value?.slice(0, 3), // 只显示前3个
    })
    return currentCaseItems.value
  })

  const getCurrentCaseDetail = computed(() => {
    const current = currentRound.value
    const total = totalRounds.value
    
    if (!current || !total) return null
    
    // 🎯 使用统一的轮次索引计算
    const getRoundIndex = (roundNumber: number) => roundNumber - 1
    const roundIndex = getRoundIndex(current)
    const caseDetail = battleCore.battleState.displayCases.value[roundIndex]
    
    console.log("[🎰BATTLE-PAGE] 当前箱子详情:", {
      currentRound: current,
      totalRounds: total,
      roundIndex,
      caseDetail: caseDetail?.name || '未知箱子'
    })
    
    return caseDetail
  })

  return {
    // 状态数据
    isEventListenersSetup,
    showCalculationProgress,
    calculationProgress,
    calculationSteps,
    hasCalculated,
    hasShownWinnerModal,
    showWinnerModal,
    winnerData,
    caseItemsCache,
    currentCaseItems,
    roundResults,
    
    // 计算属性
    getOpeningCaseId,
    battleData,
    currentRound,
    totalRounds,
    battleStateString,
    isUserCreator,
    isUserJoined,
    isBattleFinished,
    isBattleInProgress,
    isBattleWaiting,
    isBattleStarted,
    getPlayerCaseItems,
    getCurrentCaseDetail,
  }
}
