<template>
  <div class="min-h-screen bg-gradient-to-br from-gray-900 to-gray-800 p-6">
    <div class="max-w-6xl mx-auto">
      <!-- 页面标题 -->
      <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-white mb-4">⏳ 加载动画大全</h1>
        <p class="text-gray-300 text-lg">20+种酷炫的加载动画效果 - LoadingSpinner组件 + 自定义CSS动画</p>
        <div class="mt-4 flex justify-center gap-4">
          <NuxtLink to="/demo" class="nav-button">
            <i class="fas fa-arrow-left mr-2"></i>
            返回演示中心
          </NuxtLink>
        </div>
      </div>

      <!-- 控制面板 -->
      <div class="bg-gray-800/50 backdrop-blur-lg rounded-lg p-6 mb-8">
        <h2 class="text-xl font-semibold text-white mb-4">⚙️ 控制面板</h2>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <!-- 尺寸选择 -->
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">尺寸大小</label>
            <select 
              v-model="selectedSize" 
              class="w-full bg-gray-700 text-white rounded-lg p-2 border border-gray-600"
            >
              <option value="small">小号 (Small)</option>
              <option value="medium">中号 (Medium)</option>
              <option value="large">大号 (Large)</option>
            </select>
          </div>

          <!-- 文字显示 -->
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">显示文字</label>
            <div class="flex items-center space-x-3">
              <label class="flex items-center">
                <input 
                  v-model="showText" 
                  type="checkbox" 
                  class="mr-2 text-blue-500"
                >
                <span class="text-gray-300">显示</span>
              </label>
            </div>
          </div>

          <!-- 自定义文字 -->
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">自定义文字</label>
            <input 
              v-model="customText" 
              type="text" 
              placeholder="输入加载文字"
              class="w-full bg-gray-700 text-white rounded-lg p-2 border border-gray-600"
            >
          </div>

          <!-- 控制按钮 -->
          <div class="flex items-end gap-2">
            <button 
              @click="startAllLoaders"
              class="px-4 py-2 bg-primary hover:bg-primary-dark text-white rounded-lg transition-all"
            >
              🚀 全部开始
            </button>
            <button 
              @click="stopAllLoaders"
              class="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-all"
            >
              ⏹️ 停止
            </button>
          </div>
        </div>
      </div>

      <!-- LoadingSpinner组件展示区 -->
      <div class="mb-12">
        <h2 class="text-2xl font-bold text-white mb-6 border-b border-gray-600/50 pb-2">
          🧩 LoadingSpinner 组件效果
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          
          <!-- CSGO霓虹风格 -->
          <div class="loading-demo-card">
            <h3 class="demo-card-title">🎮 CSGO霓虹</h3>
            <div class="demo-card-content">
              <LoadingSpinner 
                type="csgo" 
                :size="selectedSize" 
                :show-text="showText" 
                :text="customText || 'CSGO加载中...'"
              />
            </div>
            <div class="demo-card-code">
              <code>&lt;LoadingSpinner type="csgo" /&gt;</code>
            </div>
          </div>

          <!-- 三点跳跃风格 -->
          <div class="loading-demo-card">
            <h3 class="demo-card-title">🔵 三点跳跃</h3>
            <div class="demo-card-content">
              <LoadingSpinner 
                type="dots" 
                :size="selectedSize" 
                :show-text="showText" 
                :text="customText || '跳跃中...'"
              />
            </div>
            <div class="demo-card-code">
              <code>&lt;LoadingSpinner type="dots" /&gt;</code>
            </div>
          </div>

          <!-- 波纹脉冲风格 -->
          <div class="loading-demo-card">
            <h3 class="demo-card-title">🌊 波纹脉冲</h3>
            <div class="demo-card-content">
              <LoadingSpinner 
                type="pulse" 
                :size="selectedSize" 
                :show-text="showText" 
                :text="customText || '脉冲中...'"
              />
            </div>
            <div class="demo-card-code">
              <code>&lt;LoadingSpinner type="pulse" /&gt;</code>
            </div>
          </div>

          <!-- 极简旋转风格 -->
          <div class="loading-demo-card">
            <h3 class="demo-card-title">🔄 极简旋转</h3>
            <div class="demo-card-content">
              <LoadingSpinner 
                type="simple" 
                :size="selectedSize" 
                :show-text="showText" 
                :text="customText || '旋转中...'"
              />
            </div>
            <div class="demo-card-code">
              <code>&lt;LoadingSpinner type="simple" /&gt;</code>
            </div>
          </div>

          <!-- 星光闪烁风格 -->
          <div class="loading-demo-card">
            <h3 class="demo-card-title">⭐ 星光闪烁</h3>
            <div class="demo-card-content">
              <LoadingSpinner 
                type="star" 
                :size="selectedSize" 
                :show-text="showText" 
                :text="customText || '星光闪烁...'"
              />
            </div>
            <div class="demo-card-code">
              <code>&lt;LoadingSpinner type="star" /&gt;</code>
            </div>
          </div>

          <!-- 骨架shimmer风格 -->
          <div class="loading-demo-card">
            <h3 class="demo-card-title">💀 骨架屏</h3>
            <div class="demo-card-content">
              <LoadingSpinner 
                type="skeleton" 
                :size="selectedSize" 
                :show-text="showText" 
                :text="customText || '内容加载中...'"
              />
            </div>
            <div class="demo-card-code">
              <code>&lt;LoadingSpinner type="skeleton" /&gt;</code>
            </div>
          </div>

          <!-- 牛顿摆风格 -->
          <div class="loading-demo-card">
            <h3 class="demo-card-title">⚖️ 牛顿摆</h3>
            <div class="demo-card-content">
              <LoadingSpinner 
                type="newton" 
                :size="selectedSize" 
                :show-text="showText" 
                :text="customText || '物理计算中...'"
              />
            </div>
            <div class="demo-card-code">
              <code>&lt;LoadingSpinner type="newton" /&gt;</code>
            </div>
          </div>

          <!-- 电子风暴风格 -->
          <div class="loading-demo-card">
            <h3 class="demo-card-title">⚡ 电子风暴</h3>
            <div class="demo-card-content">
              <LoadingSpinner 
                type="electric" 
                :size="selectedSize" 
                :show-text="showText" 
                :text="customText || '电力传输中...'"
              />
            </div>
            <div class="demo-card-code">
              <code>&lt;LoadingSpinner type="electric" /&gt;</code>
            </div>
          </div>

          <!-- 液体波浪风格 -->
          <div class="loading-demo-card">
            <h3 class="demo-card-title">🌀 液体波浪</h3>
            <div class="demo-card-content">
              <LoadingSpinner 
                type="wave" 
                :size="selectedSize" 
                :show-text="showText" 
                :text="customText || '波浪流动中...'"
              />
            </div>
            <div class="demo-card-code">
              <code>&lt;LoadingSpinner type="wave" /&gt;</code>
            </div>
          </div>

          <!-- 魔方旋转风格 -->
          <div class="loading-demo-card">
            <h3 class="demo-card-title">🎲 魔方旋转</h3>
            <div class="demo-card-content">
              <LoadingSpinner 
                type="cube" 
                :size="selectedSize" 
                :show-text="showText" 
                :text="customText || '3D渲染中...'"
              />
            </div>
            <div class="demo-card-code">
              <code>&lt;LoadingSpinner type="cube" /&gt;</code>
            </div>
          </div>

          <!-- 彩虹圆环风格 -->
          <div class="loading-demo-card">
            <h3 class="demo-card-title">🌈 彩虹圆环</h3>
            <div class="demo-card-content">
              <LoadingSpinner 
                type="rainbow" 
                :size="selectedSize" 
                :show-text="showText" 
                :text="customText || '彩虹加载中...'"
              />
            </div>
            <div class="demo-card-code">
              <code>&lt;LoadingSpinner type="rainbow" /&gt;</code>
            </div>
          </div>

          <!-- DNA螺旋风格 -->
          <div class="loading-demo-card">
            <h3 class="demo-card-title">🧬 DNA螺旋</h3>
            <div class="demo-card-content">
              <LoadingSpinner 
                type="dna" 
                :size="selectedSize" 
                :show-text="showText" 
                :text="customText || '基因分析中...'"
              />
            </div>
            <div class="demo-card-code">
              <code>&lt;LoadingSpinner type="dna" /&gt;</code>
            </div>
          </div>

          <!-- 心跳脉搏风格 -->
          <div class="loading-demo-card">
            <h3 class="demo-card-title">💓 心跳脉搏</h3>
            <div class="demo-card-content">
              <LoadingSpinner 
                type="heartbeat" 
                :size="selectedSize" 
                :show-text="showText" 
                :text="customText || '生命体征检测中...'"
              />
            </div>
            <div class="demo-card-code">
              <code>&lt;LoadingSpinner type="heartbeat" /&gt;</code>
            </div>
          </div>

        </div>
      </div>

      <!-- 自定义CSS动画展示区 -->
      <div class="mb-12">
        <h2 class="text-2xl font-bold text-white mb-6 border-b border-gray-600/50 pb-2">
          🎨 自定义CSS动画效果
        </h2>
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          
          <!-- 旋转加载器 -->
          <div class="custom-animation-card">
            <h3 class="card-title">🔄 旋转加载器</h3>
            <div class="loader-container">
              <div class="spinner-loader" :class="{ 'active': isActive }">
                <div class="spinner-ring"></div>
                <div class="spinner-ring"></div>
                <div class="spinner-ring"></div>
              </div>
              <div class="loader-text">加载中...</div>
            </div>
          </div>

          <!-- 脉冲加载器 -->
          <div class="custom-animation-card">
            <h3 class="card-title">💓 脉冲加载器</h3>
            <div class="loader-container">
              <div class="pulse-loader" :class="{ 'active': isActive }">
                <div class="pulse-dot"></div>
                <div class="pulse-dot"></div>
                <div class="pulse-dot"></div>
              </div>
              <div class="loader-text">处理中...</div>
            </div>
          </div>

          <!-- 进度圆环 -->
          <div class="custom-animation-card">
            <h3 class="card-title">⭕ 进度圆环</h3>
            <div class="loader-container">
              <div class="progress-ring">
                <svg class="ring-svg" viewBox="0 0 120 120">
                  <circle class="ring-bg" cx="60" cy="60" r="50"></circle>
                  <circle 
                    class="ring-progress" 
                    cx="60" 
                    cy="60" 
                    r="50"
                    :stroke-dasharray="`${(ringProgress / 100) * 314} 314`"
                  ></circle>
                </svg>
                <div class="ring-text">{{ Math.round(ringProgress) }}%</div>
              </div>
              <div class="loader-text">上传中...</div>
            </div>
          </div>

          <!-- 波浪加载器 -->
          <div class="custom-animation-card">
            <h3 class="card-title">🌊 波浪加载器</h3>
            <div class="loader-container">
              <div class="wave-loader">
                <div class="wave-bar" v-for="i in 5" :key="i"></div>
              </div>
              <div class="loader-text">同步中...</div>
            </div>
          </div>

        </div>
      </div>

      <!-- 使用说明 -->
      <div class="bg-gray-800/30 rounded-lg p-6">
        <h2 class="text-xl font-semibold text-white mb-4">📖 使用说明</h2>
        <div class="text-gray-300 space-y-3">
          <p><strong>LoadingSpinner组件：</strong>项目中的通用加载组件，支持13种不同风格的动画效果。</p>
          <p><strong>自定义CSS动画：</strong>独立的CSS动画实现，可以直接复制样式代码使用。</p>
          <p><strong>参数说明：</strong></p>
          <ul class="list-disc list-inside ml-4 space-y-1">
            <li><code>type</code>: 动画类型 (csgo, dots, pulse, simple, star, skeleton, newton, electric, wave, cube, rainbow, dna, heartbeat)</li>
            <li><code>size</code>: 尺寸大小 (small, medium, large)</li>
            <li><code>showText</code>: 是否显示文字</li>
            <li><code>text</code>: 自定义文字内容</li>
          </ul>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import LoadingSpinner from '~/components/ui/LoadingSpinner.vue'

// 页面元数据
useHead({
  title: '加载动画大全 - CSGO Skins',
  meta: [
    { name: 'description', content: '20+种酷炫的加载动画效果演示，包含CSGO霓虹、三点跳跃、波纹脉冲等多种风格' }
  ]
})

// 控制面板状态
const selectedSize = ref<'small' | 'medium' | 'large'>('medium')
const showText = ref(true)
const customText = ref('')
const isActive = ref(true)

// 进度控制
const ringProgress = ref(0)
let progressTimer: ReturnType<typeof setInterval> | null = null

const startAllLoaders = () => {
  isActive.value = true
  // 开始进度圆环动画
  ringProgress.value = 0
  progressTimer = setInterval(() => {
    ringProgress.value += 1
    if (ringProgress.value >= 100) {
      ringProgress.value = 0
    }
  }, 100)
}

const stopAllLoaders = () => {
  isActive.value = false
  if (progressTimer) {
    clearInterval(progressTimer)
    progressTimer = null
  }
  ringProgress.value = 0
}

onMounted(() => {
  startAllLoaders()
})

onUnmounted(() => {
  stopAllLoaders()
})
</script>

<style lang="scss" scoped>
/* 导航按钮样式 */
.nav-button {
  @apply inline-flex items-center px-4 py-2 bg-gray-700/50 hover:bg-gray-600/50 text-white rounded-lg transition-colors border border-gray-600/50 hover:border-gray-500/50;
}

/* LoadingSpinner展示卡片 */
.loading-demo-card {
  @apply bg-gray-800/40 backdrop-blur-lg rounded-lg p-6 border border-gray-700/50 transition-all duration-300;
  
  &:hover {
    @apply border-primary/50 bg-gray-800/60 transform -translate-y-1;
    box-shadow: 0 8px 32px rgba(var(--color-primary-rgb), 0.15);
  }
}

.demo-card-title {
  @apply text-lg font-semibold text-white mb-4 text-center;
}

.demo-card-content {
  @apply flex items-center justify-center h-32 mb-4;
}

.demo-card-code {
  @apply bg-gray-900/50 rounded p-3 text-center;
  
  code {
    @apply text-xs text-gray-300 font-mono;
  }
}

/* 自定义动画卡片 */
.custom-animation-card {
  @apply bg-gray-800/40 backdrop-blur-lg rounded-lg p-6 border border-gray-700/50 transition-all duration-300;
  
  &:hover {
    @apply border-primary/50 bg-gray-800/60 transform -translate-y-1;
    box-shadow: 0 8px 32px rgba(0, 168, 255, 0.15);
  }
}

.card-title {
  @apply text-lg font-semibold text-white mb-4 pb-2 border-b border-gray-600/50;
}

.loader-container {
  @apply flex flex-col items-center justify-center h-48 space-y-4;
}

.loader-text {
  @apply text-sm text-gray-400 font-medium;
}

// 旋转加载器
.spinner-loader {
  @apply relative w-16 h-16;
  
  .spinner-ring {
    @apply absolute w-full h-full border-4 border-transparent border-t-primary rounded-full;
    animation: spin 1.2s linear infinite;
    
    &:nth-child(1) {
      animation-delay: 0s;
    }
    
    &:nth-child(2) {
      animation-delay: 0.4s;
      border-top-color: var(--color-secondary);
    }
    
    &:nth-child(3) {
      animation-delay: 0.8s;
      border-top-color: var(--color-primary-light);
    }
  }
}

// 脉冲加载器
.pulse-loader {
  @apply flex space-x-2;
  
  .pulse-dot {
    @apply w-3 h-3 bg-primary rounded-full;
    animation: pulse-bounce 1.4s ease-in-out infinite both;
    
    &:nth-child(1) { animation-delay: -0.32s; }
    &:nth-child(2) { animation-delay: -0.16s; }
    &:nth-child(3) { animation-delay: 0s; }
  }
}

// 波浪加载器
.wave-loader {
  @apply flex space-x-1;
  
  .wave-bar {
    @apply w-1 bg-primary;
    height: 30px;
    animation: wave-bounce 1.2s ease-in-out infinite;
    
    @for $i from 1 through 5 {
      &:nth-child(#{$i}) {
        animation-delay: #{$i * 0.1}s;
      }
    }
  }
}

// 进度圆环
.progress-ring {
  @apply relative w-20 h-20;
  
  .ring-svg {
    @apply w-full h-full transform -rotate-90;
  }
  
  .ring-bg {
    @apply fill-none stroke-gray-700 stroke-2;
  }
  
  .ring-progress {
    @apply fill-none stroke-primary stroke-2 transition-all duration-300;
    stroke-linecap: round;
    filter: drop-shadow(0 0 8px rgba(0, 168, 255, 0.6));
  }
  
  .ring-text {
    @apply absolute inset-0 flex items-center justify-center text-sm font-bold text-primary;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

@keyframes wave-bounce {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .loader-container {
    @apply h-32;
  }
  
  .spinner-loader {
    @apply w-12 h-12;
  }
  
  .progress-ring {
    @apply w-16 h-16;
  }
}
</style>
