<!-- 对战加入反馈模态框 -->
<template>
  <Teleport to="body">
    <Transition name="modal-fade" appear>
      <div
        v-if="visible"
        class="fixed inset-0 z-[9999] flex items-center justify-center p-4"
        @click.self="handleClose"
      >
        <!-- 背景遮罩 -->
        <div class="absolute inset-0 bg-black/80 backdrop-blur-sm"></div>

        <!-- 模态框内容 -->
        <div
          class="relative w-full max-w-md transform transition-all duration-300"
        >
          <div
            class="relative bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur-xl border border-gray-600/30 rounded-2xl p-8 shadow-2xl"
          >
            <!-- 装饰性背景 -->
            <div class="absolute inset-0 rounded-2xl overflow-hidden">
              <div
                class="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-blue-500/5 to-purple-500/5"
              ></div>
              <div
                class="absolute -top-20 -right-20 w-40 h-40 bg-gradient-to-br from-blue-500/10 to-transparent rounded-full blur-xl"
              ></div>
              <div
                class="absolute -bottom-20 -left-20 w-40 h-40 bg-gradient-to-br from-purple-500/10 to-transparent rounded-full blur-xl"
              ></div>
            </div>

            <!-- 关闭按钮 -->
            <button
              @click="handleClose"
              class="absolute top-4 right-4 w-8 h-8 flex items-center justify-center rounded-full bg-gray-700/50 hover:bg-gray-600/50 text-gray-400 hover:text-white transition-all duration-200 z-10"
            >
              <i class="fas fa-times text-sm"></i>
            </button>

            <!-- 内容区域 -->
            <div class="relative z-10">
              <!-- 图标区域 -->
              <div class="flex justify-center mb-6">
                <div
                  class="w-16 h-16 rounded-full flex items-center justify-center text-2xl"
                  :class="iconClasses"
                >
                  <i :class="iconName" class="animate-pulse"></i>
                </div>
              </div>

              <!-- 标题 -->
              <h3
                class="text-xl font-bold text-center mb-3"
                :class="titleClasses"
              >
                {{ title }}
              </h3>

              <!-- 描述 -->
              <p class="text-gray-300 text-center mb-6 leading-relaxed">
                {{ description }}
              </p>

              <!-- 详细信息 -->
              <div
                v-if="details"
                class="bg-gray-800/50 rounded-lg p-4 mb-6 border border-gray-600/30"
              >
                <div class="space-y-2 text-sm">
                  <div v-if="details.roomId" class="flex justify-left">
                    <span class="text-gray-400"
                      >{{ t("battle.modal.room_id") }}:</span
                    >
                    <span class="text-white font-mono">{{
                      details.roomId
                    }}</span>
                  </div>
                  <div v-if="details.price" class="flex justify-left">
                    <span class="text-gray-400"
                      >{{ t("battle.modal.room_price") }}:</span
                    >
                    <span class="text-green-400 font-semibold"
                      >${{ details.price.toFixed(2) }}</span
                    >
                  </div>
                </div>
              </div>

              <!-- 按钮区域 -->
              <div class="flex flex-col sm:flex-row gap-3 justify-center">
                <button
                  v-if="showRetry"
                  @click="handleRetry"
                  class="px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 rounded-lg font-medium text-white transition-all duration-300 flex items-center justify-center gap-2"
                >
                  <i class="fas fa-redo text-sm"></i>
                  {{ t("battle.modal.retry") }}
                </button>

                <button
                  v-if="showGoToLogin"
                  @click="handleGoToLogin"
                  class="px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 rounded-lg font-medium text-white transition-all duration-300 flex items-center justify-center gap-2"
                >
                  <i class="fas fa-sign-in-alt text-sm"></i>
                  {{ t("battle.modal.go_to_login") }}
                </button>

                <button
                  v-if="!showRetry && !showGoToLogin"
                  @click="handleClose"
                  class="px-6 py-3 bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 rounded-lg font-medium text-white transition-all duration-300"
                >
                  {{ t("battle.modal.back_to_list") }}
                </button>

                <button
                  v-if="!showRetry && !showGoToLogin"
                  @click="handleViewDetails"
                  class="px-6 py-3 bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 rounded-lg font-medium text-white transition-all duration-300"
                >
                  {{ t("battle.modal.view_details") }}
                </button>
              </div>
            </div>

            <!-- 加载动画 -->
            <div
              v-if="type === 'loading'"
              class="absolute inset-0 flex items-center justify-center bg-gray-900/50 backdrop-blur-sm rounded-2xl"
            >
              <div class="flex flex-col items-center">
                <div
                  class="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mb-3"
                ></div>
                <p class="text-white/80 text-sm">
                  {{ loadingText || t("common.processing") }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
interface BattleJoinModalProps {
  visible: boolean;
  type: "success" | "error" | "warning" | "info" | "loading";
  title: string;
  description: string;
  details?: {
    roomId?: string;
    price?: number;
    balance?: number;
    message?: string;
  };
  showRetry?: boolean;
  showGoToLogin?: boolean;
  loadingText?: string;
  autoClose?: boolean;
  autoCloseDelay?: number;
}

interface BattleJoinModalEmits {
  (e: "close"): void;
  (e: "retry"): void;
  (e: "goToLogin"): void;
  (e: "viewDetails"): void;
}

const props = withDefaults(defineProps<BattleJoinModalProps>(), {
  autoClose: false,
  autoCloseDelay: 3000,
  showRetry: false,
  showGoToLogin: false,
});

const emit = defineEmits<BattleJoinModalEmits>();

// 国际化
const { t } = useI18n();

// 计算属性
const iconName = computed(() => {
  switch (props.type) {
    case "success":
      return "fas fa-check-circle";
    case "error":
      return "fas fa-times-circle";
    case "warning":
      return "fas fa-exclamation-triangle";
    case "info":
      return "fas fa-info-circle";
    case "loading":
      return "fas fa-spinner fa-spin";
    default:
      return "fas fa-info-circle";
  }
});

const iconClasses = computed(() => {
  switch (props.type) {
    case "success":
      return "bg-gradient-to-br from-green-500/20 to-green-600/20 text-green-400 border border-green-500/30";
    case "error":
      return "bg-gradient-to-br from-red-500/20 to-red-600/20 text-red-400 border border-red-500/30";
    case "warning":
      return "bg-gradient-to-br from-yellow-500/20 to-orange-600/20 text-yellow-400 border border-yellow-500/30";
    case "info":
      return "bg-gradient-to-br from-blue-500/20 to-blue-600/20 text-blue-400 border border-blue-500/30";
    case "loading":
      return "bg-gradient-to-br from-purple-500/20 to-purple-600/20 text-purple-400 border border-purple-500/30";
    default:
      return "bg-gradient-to-br from-gray-500/20 to-gray-600/20 text-gray-400 border border-gray-500/30";
  }
});

const titleClasses = computed(() => {
  switch (props.type) {
    case "success":
      return "text-green-400";
    case "error":
      return "text-red-400";
    case "warning":
      return "text-yellow-400";
    case "info":
      return "text-blue-400";
    case "loading":
      return "text-purple-400";
    default:
      return "text-white";
  }
});

// 方法
const handleClose = () => {
  if (props.type !== "loading") {
    emit("close");
  }
};

// 查看详情
const handleViewDetails = () => {
  emit("viewDetails");
};

const handleRetry = () => {
  emit("retry");
};

const handleGoToLogin = () => {
  emit("goToLogin");
};

// 自动关闭
let autoCloseTimer: NodeJS.Timeout | null = null;

watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible && props.autoClose && props.type !== "loading") {
      autoCloseTimer = setTimeout(() => {
        handleClose();
      }, props.autoCloseDelay);
    } else if (autoCloseTimer) {
      clearTimeout(autoCloseTimer);
      autoCloseTimer = null;
    }
  }
);

onUnmounted(() => {
  if (autoCloseTimer) {
    clearTimeout(autoCloseTimer);
  }
});
</script>

<style scoped>
.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: all 0.3s ease;
}

.modal-fade-enter-from,
.modal-fade-leave-to {
  opacity: 0;
  transform: scale(0.9);
}

.modal-fade-enter-to,
.modal-fade-leave-from {
  opacity: 1;
  transform: scale(1);
}
</style>
