# -*- coding: utf-8 -*-
# Generated by Django 1.11.13 on 2021-04-14 02:09
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import grab.models
import steambase.models
import steambase.storage


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODELL),
        ('package', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='GrabBet',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('position', models.IntegerField(default=None, verbose_name='grab position')),
                ('victory', models.BooleanField(default=False, verbose_name='grab victory')),
            ],
            options={
                'verbose_name': 'GrabRoom',
                'verbose_name_plural': 'GrabRoom',
            },
        ),
        migrations.CreateModel(
            name='GrabCard',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('position', models.IntegerField(default=0, verbose_name='grab position')),
                ('image', models.ImageField(blank=True, default=None, null=True, storage=steambase.storage.ImageStorage(), upload_to='grab/%Y%m', verbose_name='grab card image')),
            ],
            options={
                'verbose_name': 'GrabCard',
                'verbose_name_plural': 'GrabCard',
            },
        ),
        migrations.CreateModel(
            name='GrabHistory',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('count', models.IntegerField(default=0, verbose_name='join count')),
                ('timestamp', models.CharField(default=0, max_length=128, verbose_name='join timestamp')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='GrabRoom',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('rid', models.CharField(default=grab.models.short_id_gen, max_length=128, null=True, unique=True, verbose_name='room id')),
                ('curreny', models.IntegerField(choices=[(0, 'Coupon'), (1, 'Coins')], default=1, verbose_name='currency')),
                ('position', models.IntegerField(default=0, verbose_name='total position')),
                ('user_max_choice', models.IntegerField(default=0, verbose_name='user max choice')),
                ('coins', models.IntegerField(default=1, verbose_name='coins to one card')),
                ('price', models.FloatField(default=0, verbose_name='item price')),
                ('state', models.IntegerField(choices=[(0, 'Joinable'), (1, 'Fulling'), (2, 'Handling'), (3, 'End')], default=0, verbose_name='room state')),
                ('position_last', models.IntegerField(default=0, verbose_name='position last')),
                ('item_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='grab_item', to='package.ItemInfo')),
            ],
            options={
                'verbose_name': 'GrabRoom',
                'verbose_name_plural': 'GrabRoom',
            },
        ),
        migrations.AddField(
            model_name='grabhistory',
            name='room',
            field=models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='grab_history', to='grab.GrabRoom'),
        ),
        migrations.AddField(
            model_name='grabhistory',
            name='user',
            field=models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='grab_history_user', to=settings.AUTH_USER_MODELL),
        ),
        migrations.AddField(
            model_name='grabbet',
            name='room',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bet_room', to='grab.GrabRoom'),
        ),
        migrations.AddField(
            model_name='grabbet',
            name='user',
            field=models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='grab_user', to=settings.AUTH_USER_MODELL),
        ),
    ]
