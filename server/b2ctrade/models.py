from uuid import uuid1
from django.conf import settings
from django.core.cache import cache
from django.db import models, transaction
from django.utils import timezone
from django.utils.translation import gettext_lazy as _


from steambase.enums import AppType, B2CTradeState, TradeType, B2CItemType, ZBTradeState, ZBTradeSource
from steambase.utils import ParamException
from steambase.models import ModelBase, USER_MODEL
from package.models import ItemInfo, PackageItem


def uid_gen():
    return uuid1().hex


class B2CMarketItem(models.Model):
    item_info = models.OneToOneField(ItemInfo, on_delete=models.CASCADE, verbose_name=_('item info'), related_name='b2c_market_item')
    price = models.FloatField(_('price'), default=0)
    zbt_price = models.FloatField(_('zbt price'), default=0.0)
    count = models.IntegerField(_('count'), default=0)
    unlimited = models.BooleanField(_('unlimited'), default=True)
    enable = models.BooleanField(_('enable'), default=True)
    create_time = models.DateTimeField(_("create time"), editable=False, auto_now_add=True, null=True, blank=True)
    update_time = models.DateTimeField(_("update time"), editable=False, auto_now=True, null=True, blank=True)

    class Meta:
        verbose_name = _('B2C Market Item')
        verbose_name_plural = _('B2C Market Item')

    def __str__(self):
        return self.item_info.market_name_cn or self.item_info.market_name


class ZBTradeRecord(ModelBase):
    APP_TYPE = (
        (AppType.Dota2.value, _('Dota2')),
        (AppType.CSGO.value, _('CSGO')),
        (AppType.PUBG.value, _('PUBG')),
        (AppType.H1Z1.value, _('H1Z1')),
    )
    B2CTRADE_STATE = (
        (B2CTradeState.Initialed.value, _('Initialed')),
        (B2CTradeState.Accepted.value, _('Accepted')),
        (B2CTradeState.Cancelled.value, _('Cancelled')),
        (B2CTradeState.Trading.value, _('Trading')),
        (B2CTradeState.Cancelling.value, _('Cancelling')),
        (B2CTradeState.PriceCancelled.value, _('PriceCancelled')),
        (B2CTradeState.OutOfStock.value, _('OutOfStock')),
        (B2CTradeState.ZBTCancelled.value, _('ZBTCancelled')),
    )
    ZBT_STATE = (
        (ZBTradeState.WaitForPay.value, _('WaitForPay')),
        (ZBTradeState.WaitForSend.value, _('WaitForSend')),
        (ZBTradeState.WaitForTrade.value, _('WaitForTrade')),
        (ZBTradeState.Receive.value, _('Receive')),
        (ZBTradeState.Accepted.value, _('Accepted')),
        (ZBTradeState.Cancelled.value, _('Cancelled')),
    )
    ZBT_Trade_Type = (
        (ZBTradeSource.Market.value, _('Market')),
        (ZBTradeSource.Package.value, _('Package')),
    )
    user = models.ForeignKey(USER_MODEL, on_delete=models.CASCADE, verbose_name=_("user"), related_name='zbt_user')
    trade_url = models.CharField(_('trade url'), max_length=256)
    amount = models.FloatField(_('amount'), default=0.0)
    buy_price = models.FloatField(_('buy price'), default=0.0)
    state = models.SmallIntegerField(_('trade status'), default=B2CTradeState.Initialed.value, choices=B2CTRADE_STATE)
    zbt_state = models.SmallIntegerField(_('zbt trade status'), default=ZBTradeState.WaitForPay.value, choices=ZBT_STATE)
    accept_time = models.DateTimeField(_("accept time"), default=None, null=True, blank=True)
    appid = models.CharField(_('appid'), max_length=64, choices=APP_TYPE, default=None, null=True, blank=True)
    item_info = models.ForeignKey(ItemInfo, on_delete=models.CASCADE, verbose_name=_("item info"))
    assetid = models.CharField(_('assetid'), max_length=64, default=None, null=True, blank=True)
    zbt_orderid = models.CharField(_('zbt order id'), max_length=64, default=None, null=True, blank=True)
    #zbt_error = models.CharField(_('zbt error'), max_length=256, default=None, null=True, blank=True)
    zbt_error = models.TextField(default=None, max_length=1024, null=True, verbose_name='zbt error')
    trade_no = models.CharField(_('trade No.'), max_length=32, default=None, null=True, blank=True)
    out_trade_no = models.CharField(_("out trade no"), max_length=64, default=None, null=True, blank=True)
    trade_source = models.SmallIntegerField(_('trade source'), default=ZBTradeSource.Market.value, choices=ZBT_Trade_Type)
    package = models.ForeignKey(PackageItem, on_delete=models.SET_NULL, verbose_name=_('package item'), default=None, null=True, blank=True,
                                related_name='zbtrade_records')
    
    # 增加一个字段，用于判断是否计入用户提取总额
    is_extract = models.BooleanField(_('is extract'), default=False)

    # 增加一个字段，用于给用户显示的错误信息
    error_msg = models.CharField(_('error msg'), max_length=256, default=None, null=True, blank=True)

    class Meta:
        ordering = ('-create_time',)
        verbose_name = _('ZBT Trade Record')
        verbose_name_plural = _('ZBT Trade Record')

    def __str__(self):
        return self.uid


class ZBTBlackList(ModelBase):
    nickname = models.CharField(_('seller nickname'), max_length=128, default=None)

    class Meta:
        verbose_name = _('ZBTBlackList')
        verbose_name_plural = _('ZBTBlackList')


class B2CTradeRecord(ModelBase):
    APP_TYPE = (
        (AppType.Dota2.value, _('Dota2')),
        (AppType.CSGO.value, _('CSGO')),
        (AppType.PUBG.value, _('PUBG')),
        (AppType.H1Z1.value, _('H1Z1')),
    )
    B2CTRADE_STATE = (
        (B2CTradeState.Initialed.value, _('Initialed')),
        (B2CTradeState.Accepted.value, _('Accepted')),
        (B2CTradeState.Cancelled.value, _('Cancelled')),
        (B2CTradeState.WaitForBuy.value, _('WaitForBuy')),
        (B2CTradeState.RequestBuy.value, _('RequestBuy')),
        (B2CTradeState.WaitUnlock.value, _('WaitUnlock')),
        (B2CTradeState.TradeReady.value, _('TradeReady')),
        (B2CTradeState.BuyerCancelled.value, _('BuyerCancelled')),
    )
    B2CITEM_TYPE = (
        (B2CItemType.Purchase.value, _('Purchase')),
        (B2CItemType.Stocks.value, _('Stocks')),
    )
    account = models.ForeignKey(USER_MODEL, on_delete=models.CASCADE, verbose_name=_("account"), related_name='b2c')
    buyer = models.ForeignKey(USER_MODEL, on_delete=models.SET_NULL, verbose_name=_("buyer"), related_name='b2c_buy', default=None, null=True, blank=True)
    seller_tradeurl = models.CharField(_('trade url'), max_length=256, default=None, null=True, blank=True)
    buyer_tradeurl = models.CharField(_('trade url'), max_length=256, default=None, null=True, blank=True)
    amount = models.FloatField(_('amount'), default=0.0)
    state = models.SmallIntegerField(_('trade status'), default=B2CTradeState.Initialed.value, choices=B2CTRADE_STATE)
    accept_time = models.DateTimeField(_("accept time"), default=None, null=True, blank=True)
    appid = models.CharField(_('appid'), max_length=64, choices=APP_TYPE, default=None, null=True, blank=True)
    item_info = models.ForeignKey(ItemInfo, on_delete=models.CASCADE, verbose_name=_("item info"))
    expire_time = models.DateTimeField(_("expire time"), default=None, null=True, blank=True)
    assetid = models.CharField(_('assetid'), max_length=64, default=None, null=True, blank=True)
    item_type= models.SmallIntegerField(_('item type'), default=B2CItemType.Stocks.value, choices=B2CITEM_TYPE)

    class Meta:
        ordering = ('-create_time',)
        verbose_name = _('B2COfficialTrade Record')
        verbose_name_plural = _('B2COfficialTrade Record')

    def __str__(self):
        return self.uid

    @classmethod
    def update_state(cls, uids, origin_state, target_state, user=None):
        with transaction.atomic():
            query = {
                'uid__in': uids
            }
            # if user:
            #     query['seller'] = user
            # else:
            #     raise ParamException(_('Invalid params'))
            items = cls.objects.select_for_update().filter(**query)
            items = items.filter(state=origin_state)
            if items.count() != len(uids):
                raise ParamException(_('Invalid items'))
            for item in items:
                item.state = target_state
                item.save()
            return items


class B2CItemlist(ModelBase):
    item_info = models.ForeignKey(ItemInfo, on_delete=models.SET_NULL, verbose_name=_('item info'), related_name='b2c_item',
                                  default=None, null=True, blank=True)
    enable = models.BooleanField(_('enable'), default=True)
    count = models.IntegerField(_('count'), default=0)

    class Meta:
        ordering = ('item_info__market_hash_name',)
        verbose_name = _('B2C Item list')
        verbose_name_plural = _('B2C Item list')

    def __str__(self):
        if self.item_info:
            return self.item_info.market_name_cn or self.item_info.market_name
        else:
            return '-'


class B2COfficialAccount(models.Model):
    remark = models.CharField(_('remark'), max_length=128)
    user = models.ForeignKey(USER_MODEL, on_delete=models.CASCADE, verbose_name=_("user"), related_name='b2c_official_account')
    enable = models.BooleanField(_('enable'), default=True)
    customid = models.CharField(_('customid'), max_length=128, default=None)
    # apikey = models.CharField(_('steam apikey'), max_length=64, default=None, null=True, blank=True)

    class Meta:
        verbose_name = _("B2COfficial Account")
        verbose_name_plural = _("B2COfficial Account")

    def __str__(self):
        return self.user.username


class B2CTradeStatisticsDay(ModelBase):
    APP_TYPE = (
        (AppType.Dota2.value, _('Dota2')),
        (AppType.CSGO.value, _('CSGO')),
        (AppType.PUBG.value, _('PUBG')),
        (AppType.H1Z1.value, _('H1Z1')),
    )

    date = models.DateField(_("date"), default=timezone.now)
    amount = models.FloatField(_("amount"), default=0.0)
    test_amount = models.FloatField(_("test amount"), default=0.0)
    appid = models.CharField(_('appid'), max_length=64, choices=APP_TYPE, default=None, null=True, blank=True)

    class Meta:
        ordering = ('-create_time',)
        verbose_name = _('B2CTrade Statistics Day')
        verbose_name_plural = _('B2CTrade Statistics Day')

    @classmethod
    def update_amount(cls, amount, appid):
        with transaction.atomic():
            today = timezone.localdate()
            if not cls.objects.filter(date=today, appid=appid):
                cls.objects.create(date=today, appid=appid)
            record = cls.objects.select_for_update().get(date=today, appid=appid)
            amount = round(amount, 2)
            record.amount += amount
            record.save()

    @classmethod
    def update_test_amount(cls, amount, appid):
        with transaction.atomic():
            today = timezone.localdate()
            if not cls.objects.filter(date=today, appid=appid):
                cls.objects.create(date=today, appid=appid)
            record = cls.objects.select_for_update().get(date=today, appid=appid)
            amount = round(amount, 2)
            record.test_amount += amount
            record.save()

    def __str__(self):
        return str(self.date)


class B2CTradeStatisticsMonth(ModelBase):
    APP_TYPE = (
        (AppType.Dota2.value, _('Dota2')),
        (AppType.CSGO.value, _('CSGO')),
        (AppType.PUBG.value, _('PUBG')),
        (AppType.H1Z1.value, _('H1Z1')),
    )
    date = models.DateField(_("date"), default=timezone.now)
    amount = models.FloatField(_("amount"), default=0.0)
    test_amount = models.FloatField(_("test amount"), default=0.0)
    appid = models.CharField(_('appid'), max_length=64, choices=APP_TYPE)

    class Meta:
        ordering = ('-create_time',)
        verbose_name = _('B2CTrade Statistics Month')
        verbose_name_plural = _('B2CTrade Statistics Month')


    @classmethod
    def update_amount(cls, amount, appid):
        with transaction.atomic():
            today = timezone.localdate()
            month_day_first = today.replace(day=1)
            if not cls.objects.filter(date=month_day_first, appid=appid):
                cls.objects.create(date=month_day_first, appid=appid)
            record = cls.objects.select_for_update().get(date=month_day_first, appid=appid)
            amount = round(amount, 2)
            record.test_amount += amount
            record.save()

    @classmethod
    def update_test_amount(cls, amount, appid):
        with transaction.atomic():
            today = timezone.localdate()
            month_day_first = today.replace(day=1)
            if not cls.objects.filter(date=month_day_first, appid=appid):
                cls.objects.create(date=month_day_first, appid=appid)
            record = cls.objects.select_for_update().get(date=month_day_first, appid=appid)
            amount = round(amount, 2)
            record.amount += amount
            record.save()

    def __str__(self):
        return self.month()

    def month(self):
        return self.date.strftime("%Y-%m")
    month.short_description = _('Month')