"""steambase URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/1.11/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  url(r'^$', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  url(r'^$', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import re_path as url, include
    2. Add a URL to urlpatterns:  url(r'^blog/', include('blog.urls'))
"""
from django.urls import re_path as url, include
from django.contrib import admin
from django.conf.urls.static import static
from django.contrib.staticfiles.urls import staticfiles_urlpatterns
from django.conf import settings
from django.http import HttpResponse

from steambase.redis_con import init_conn
from .middleware import MediaURLMiddleware

urlpatterns = [
    # url(r'^jet/', include('jet.urls', 'jet')),  # 暂时禁用，等待Django 4.x兼容版本
    url(r'^ckeditor/', include('ckeditor_uploader.urls')),
    url(r'^ckeditor5/', include('django_ckeditor_5.urls')),

    # CKEditor 5测试页面
    url(r'^test-ckeditor5/$', lambda request: __import__('test_ckeditor5_view').test_ckeditor5_view(request), name='test_ckeditor5'),

    # 静态文件测试页面
    url(r'^test-static/$', lambda request: __import__('test_static').test_static_view(request), name='test_static'),

    # Socket.IO测试页面
    url(r'^test-socketio/$', lambda request: HttpResponse(open('test_socketio.html').read(), content_type='text/html'), name='test_socketio'),

    url(r'^chenchen/', admin.site.urls),
    url(r'^api/monitor/', include('monitor.urls', namespace='monitor')),

    url(r'^social/', include('social_django.urls', namespace='social')),
    url(r'^api/auth/', include('authentication.urls', namespace='auth')),

    url(r'^api/promotion/', include('promotion.urls', namespace='promotion')),
    url(r'^api/package/', include('package.urls', namespace='package')),
    url(r'^api/charge/', include('charge.urls', namespace='charge')),
    url(r'^api/withdraw/', include('withdraw.urls', namespace='withdraw')),
    url(r'^api/sitecfg/', include('sitecfg.urls', namespace='sitecfg')),
    url(r'^api/blind/', include('blindbox.urls', namespace='blindbox')),
    url(r'^api/box/', include('box.urls', namespace='box')),
    url(r'^api/chat/', include('chat.urls', namespace='chat')),
    url(r'^api/lottery/', include('lottery.urls', namespace='lottery')),
    url(r'^api/roll/', include('roll.urls', namespace='roll')),
    url(r'^api/luckybox/', include('luckybox.urls', namespace='luckybox')),
    url(r'^api/custombox/', include('custombox.urls', namespace='custombox')),
    url(r'^api/market/', include('market.urls', namespace='market')),
    url(r'^api/grab/', include('grab.urls', namespace='grab')),
    url(r'^api/envelop/', include('envelope.urls', namespace='envelop')),
    url(r'^api/tradeup/', include('tradeup.urls', namespace='tradeup')),
    url(r'^api/crash/', include('crash.urls', namespace='crash')),
    url(r'^api/ws/', include('websocket.urls', namespace='websocket')),
    url(r'^api/b2c/', include('b2ctrade.urls', namespace='b2ctrade')),
    url(r'^api/agent/', include('agent.urls', namespace='agent')),
]

urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
urlpatterns += staticfiles_urlpatterns()


init_conn()


from django.apps import apps
from django.conf import settings

for app in settings.INSTALLED_APPS_STINT:
    models = apps.get_app_config(app).models
    for name, model in models.items():
        try:
            admin.site.unregister(model)
        except admin.sites.NotRegistered:
            pass