/**
 * 原生UI组件可组合函数
 * 提供便捷的组件使用方法和国际化支持，不依赖第三方UI库
 */
export const useUI = () => {
  const { t, locale } = useI18n()

  /**
   * 创建消息提示元素
   */
  const createMessageElement = (message: string, type: 'success' | 'warning' | 'info' | 'error' = 'info', duration: number = 3000) => {
    const messageEl = document.createElement('div')
    messageEl.className = `fixed top-4 right-4 z-50 px-4 py-3 rounded-lg shadow-lg backdrop-blur-lg border transition-all duration-300 transform translate-x-full ${
      type === 'success' ? 'bg-green-500/20 border-green-500/30 text-green-400' :
      type === 'error' ? 'bg-red-500/20 border-red-500/30 text-red-400' :
      type === 'warning' ? 'bg-yellow-500/20 border-yellow-500/30 text-yellow-400' :
      'bg-blue-500/20 border-blue-500/30 text-blue-400'
    }`
    
    messageEl.innerHTML = `
      <div class="flex items-center gap-2">
        <span class="text-sm font-medium">${message}</span>
        <button class="ml-2 text-gray-400 hover:text-white" onclick="this.parentElement.parentElement.remove()">×</button>
      </div>
    `
    
    document.body.appendChild(messageEl)
    
    // 动画进入
    setTimeout(() => {
      messageEl.classList.remove('translate-x-full')
    }, 10)
    
    // 自动移除
    setTimeout(() => {
      messageEl.classList.add('translate-x-full')
      setTimeout(() => {
        if (messageEl.parentElement) {
          messageEl.parentElement.removeChild(messageEl)
        }
      }, 300)
    }, duration)
    
    return messageEl
  }

  /**
   * 显示消息提示
   */
  const showMessage = (message: string, type: 'success' | 'warning' | 'info' | 'error' = 'info', duration: number = 3000) => {
    return createMessageElement(message, type, duration)
  }

  /**
   * 显示成功消息
   */
  const showSuccess = (message: string, duration?: number) => {
    return showMessage(message, 'success', duration)
  }

  /**
   * 显示错误消息
   */
  const showError = (message: string, duration?: number) => {
    return showMessage(message, 'error', duration)
  }

  /**
   * 显示警告消息
   */
  const showWarning = (message: string, duration?: number) => {
    return showMessage(message, 'warning', duration)
  }

  /**
   * 显示信息消息
   */
  const showInfo = (message: string, duration?: number) => {
    return showMessage(message, 'info', duration)
  }

  /**
   * 创建确认对话框
   */
  const createConfirmDialog = (message: string, title: string, options: any = {}) => {
    return new Promise((resolve, reject) => {
      const overlay = document.createElement('div')
      overlay.className = 'fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center'
      
      const dialog = document.createElement('div')
      dialog.className = 'bg-gray-800/95 backdrop-blur-lg border border-gray-700/30 rounded-lg shadow-xl max-w-md w-full mx-4'
      
      dialog.innerHTML = `
        <div class="p-6">
          <h3 class="text-lg font-semibold text-white mb-2">${title}</h3>
          <p class="text-gray-300 mb-6">${message}</p>
          <div class="flex justify-end gap-3">
            <button class="px-4 py-2 text-gray-400 hover:text-white transition-colors" data-action="cancel">
              ${t('common.cancel')}
            </button>
            <button class="px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded transition-colors" data-action="confirm">
              ${t('common.confirm')}
            </button>
          </div>
        </div>
      `
      
      overlay.appendChild(dialog)
      document.body.appendChild(overlay)
      
      const handleAction = (action: string) => {
        document.body.removeChild(overlay)
        if (action === 'confirm') {
          resolve(true)
        } else {
          reject(new Error('User cancelled'))
        }
      }
      
      dialog.addEventListener('click', (e) => {
        const target = e.target as HTMLElement
        if (target.dataset.action) {
          handleAction(target.dataset.action)
        }
      })
      
      overlay.addEventListener('click', (e) => {
        if (e.target === overlay) {
          handleAction('cancel')
        }
      })
    })
  }

  /**
   * 显示确认对话框
   */
  const showConfirm = (message: string, title: string = t('common.confirm'), options: any = {}) => {
    return createConfirmDialog(message, title, options)
  }

  /**
   * 显示警告对话框
   */
  const showAlert = (message: string, title: string = t('common.notice'), options: any = {}) => {
    return new Promise((resolve) => {
      const overlay = document.createElement('div')
      overlay.className = 'fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center'
      
      const dialog = document.createElement('div')
      dialog.className = 'bg-gray-800/95 backdrop-blur-lg border border-gray-700/30 rounded-lg shadow-xl max-w-md w-full mx-4'
      
      dialog.innerHTML = `
        <div class="p-6">
          <h3 class="text-lg font-semibold text-white mb-2">${title}</h3>
          <p class="text-gray-300 mb-6">${message}</p>
          <div class="flex justify-end">
            <button class="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded transition-colors" data-action="confirm">
              ${t('common.confirm')}
            </button>
          </div>
        </div>
      `
      
      overlay.appendChild(dialog)
      document.body.appendChild(overlay)
      
      const handleConfirm = () => {
        document.body.removeChild(overlay)
        resolve(true)
      }
      
      dialog.addEventListener('click', (e) => {
        const target = e.target as HTMLElement
        if (target.dataset.action === 'confirm') {
          handleConfirm()
        }
      })
      
      overlay.addEventListener('click', (e) => {
        if (e.target === overlay) {
          handleConfirm()
        }
      })
    })
  }

  return {
    // 消息提示
    showMessage,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    
    // 对话框
    showConfirm,
    showAlert
  }
} 