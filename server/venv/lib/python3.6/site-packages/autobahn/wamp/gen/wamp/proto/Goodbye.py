# automatically generated by the FlatBuffers compiler, do not modify

# namespace: proto

import flatbuffers

class Goodbye(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAsGoodbye(cls, buf, offset):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = Goodbye()
        x.Init(buf, n + offset)
        return x

    # Goodbye
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # Goodbye
    def Reason(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

    # Goodbye
    def Message(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

    # Goodbye
    def Resumable(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(8))
        if o != 0:
            return bool(self._tab.Get(flatbuffers.number_types.BoolFlags, o + self._tab.Pos))
        return False

def GoodbyeStart(builder): builder.StartObject(3)
def GoodbyeAddReason(builder, reason): builder.PrependUOffsetTRelativeSlot(0, flatbuffers.number_types.UOffsetTFlags.py_type(reason), 0)
def GoodbyeAddMessage(builder, message): builder.PrependUOffsetTRelativeSlot(1, flatbuffers.number_types.UOffsetTFlags.py_type(message), 0)
def GoodbyeAddResumable(builder, resumable): builder.PrependBoolSlot(2, resumable, 0)
def GoodbyeEnd(builder): return builder.EndObject()
