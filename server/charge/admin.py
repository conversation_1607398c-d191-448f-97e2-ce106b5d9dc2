from django.contrib import admin
from django.contrib.admin import TabularInline
from django.contrib.admin.views.main import ChangeList
from django.urls import reverse
from django.db.models import Sum
from django.utils.safestring import mark_safe
from django.utils.translation import gettext_lazy as _

# # from jet.filters import DateRangeFilter  # 暂时禁用，等待Django 4.x兼容版本  # 暂时禁用，等待Django 4.x兼容版本

from charge.service.admin_actions import startup_export_cdkey_to_txt, chargerecord_export_to_excel, \
    chargelevel_export_to_excel, chargestatistics_export_to_excel, init_charge_level, generate_cdkey
from steambase.admin import ReadOnlyAdmin

from authentication.models import AuthUser
from charge.models import ChargeRecord, ChargeLevel, ChargeLevelConfig, ChargeWeekRank, ChargeStatisticsDay, \
    ChargeStatisticsMonth, CxkaGoods, CxkaGenerate, ChargeHandselConfig, ChargeAmountConfig, CDKey, CDKeyRecord, \
    GenerateCDKey


@admin.register(ChargeRecord)
class ChargeRecordAdmin(ReadOnlyAdmin):
    fields = ('out_trade_no', 'user', 'amount', 'state', 'pay_type', 'pay_amount', 'create_time', 'pay_time',
              'qr_code', 'nonce', 'timestamp', 'clientIp')
    list_display = ('out_trade_no', 'user', 'amount', 'state', 'pay_type', 'pay_amount', 'create_time',
                    'pay_time')
    list_filter = ()  # DateRangeFilter暂时禁用, # 暂时禁用)
    search_fields = ('out_trade_no', 'user__username')
    ordering = ('-create_time',)
    list_per_page = 50
    actions = [chargerecord_export_to_excel]

    def get_changelist(self, request, **kwargs):
        return AddTotalChargeRecordChangeList


class AddTotalChargeRecordChangeList(ChangeList):

    def get_results(self, request):
        super().get_results(request)
        total = ChargeRecord()
        setattr(total, 'user', AuthUser(username='总和'))
        setattr(total, 'state', '-')
        setattr(total, 'pay_type', '-')
        setattr(total, 'pay_amount', '-')
        fields_to_total = ['amount']
        for field in fields_to_total:
            setattr(total, field, round(list(self.queryset.aggregate(Sum(field)).items())[0][1] or 0, 2))
        len(self.result_list)
        self.result_list._result_cache.insert(0, total)


@admin.register(ChargeLevel)
class ChargeLevelAdmin(admin.ModelAdmin):
    fields = ('user', 'level', 'amount', 'update_time')
    list_display = ('user', 'level', 'amount', 'update_time')
    list_filter = ()  # DateRangeFilter暂时禁用,)
    search_fields = ('user__username',)
    readonly_fields = ('update_time',)
    list_per_page = 50
    actions = [chargelevel_export_to_excel, init_charge_level]

    def changelist_view(self, request, extra_context=None):
        post = request.POST.copy()
        action = post.get('action')
        if action:
            if action == 'init_charge_level' and not post.get('_selected_action'):
                post.setlist('_selected_action', [l.id for l in ChargeLevel.objects.all()])
                request.POST = post
        return super(ChargeLevelAdmin, self).changelist_view(request, extra_context)


# @admin.register(ChargeStatisticsDay)
# class ChargeStatisticsDayAdmin(ReadOnlyAdmin):
#     fields = ('date', 'amount')
#     list_display = ('date', 'amount')
#     list_filter = ()  # DateRangeFilter暂时禁用,)
#     list_per_page = 50
#     actions = [chargestatistics_export_to_excel]

#     def get_changelist(self, request, **kwargs):
#         return ChargeStatisticsDayAddTotalChangeList


# class ChargeStatisticsDayAddTotalChangeList(ChangeList):

#     def get_results(self, request):
#         super().get_results(request)
#         total = ChargeStatisticsDay()
#         setattr(total, 'date', '总和')
#         fields_to_total = ['amount']
#         for field in fields_to_total:
#             setattr(total, field, round(list(self.queryset.aggregate(Sum(field)).items())[0][1] or 0, 2))
#         len(self.result_list)
#         self.result_list._result_cache.insert(0, total)


# @admin.register(ChargeStatisticsMonth)
# class ChargeStatisticsMonthAdmin(ReadOnlyAdmin):
#     fields = ('date', 'amount')
#     list_display = ('date', 'amount')
#     list_filter = ()  # DateRangeFilter暂时禁用,)
#     list_per_page = 50

#     def get_changelist(self, request, **kwargs):
#         return ChargeStatisticsMonthAddTotalChangeList


# class ChargeStatisticsMonthAddTotalChangeList(ChangeList):

#     def get_results(self, request):
#         super().get_results(request)
#         total = ChargeStatisticsMonth()
#         setattr(total, 'date', '总和')
#         fields_to_total = ['amount']
#         for field in fields_to_total:
#             setattr(total, field, round(list(self.queryset.aggregate(Sum(field)).items())[0][1] or 0, 2))
#         len(self.result_list)
#         self.result_list._result_cache.insert(0, total)


# @admin.register(ChargeWeekRank)
# class ChargeWeekRankAdmin(ReadOnlyAdmin):
#     fields = ('user', 'amount', 'week', 'year')
#     list_display = ('user', 'amount', 'week', 'year')
#     list_filter = ('week', 'year')
#     search_fields = ('user__username', 'uid')
#     list_per_page = 50


@admin.register(ChargeLevelConfig)
class ChargeLevelConfigAdmin(admin.ModelAdmin):
    fields = ('level', 'min_amount', 'max_amount')
    list_display = ('level', 'min_amount', 'max_amount')
    list_per_page = 50


@admin.register(CxkaGoods)
class CxkaGoodsAdmin(admin.ModelAdmin):
    fields = ('good_id', 'good_name', 'price', 'create_time')
    list_display = ('good_id', 'good_name', 'create_time')
    list_per_page = 50
    readonly_fields = ['create_time']


@admin.register(CxkaGenerate)
class CxkaGenerateAdmin(admin.ModelAdmin):
    ordering = ('count',)
    list_per_page = 50

    actions = [startup_export_cdkey_to_txt]


@admin.register(ChargeAmountConfig)
class ChargeAmountConfigAdmin(admin.ModelAdmin):
    ordering = ('sort_order',)
    list_editable = ('enable', 'sort_order')
    #fields = ('coins', 'image')
    list_display = ('coins', 'sort_order', 'enable')


@admin.register(ChargeHandselConfig)
class ChargeHandselConfigAdmin(admin.ModelAdmin):
    fields = ('level', 'min_amount', 'max_amount', 'handsel')
    list_display = ('level', 'min_amount', 'max_amount', 'handsel')
    list_per_page = 50


@admin.register(CDKey)
class CDKeyAdmin(admin.ModelAdmin):
    list_display = ('key', 'amount', 'state', 'update_time')
    list_per_page = 50
    list_filter = ('state', 'amount')
    search_fields = ('key',)


@admin.register(CDKeyRecord)
class CDKeyRecordAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'key', 'amount')


@admin.register(GenerateCDKey)
class GenerateCDKeyAdmin(admin.ModelAdmin):
    list_display = ('amount', 'count')
    actions = [generate_cdkey]
