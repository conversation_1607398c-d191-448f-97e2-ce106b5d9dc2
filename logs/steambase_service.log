2025-07-15 09:25:25 [1:gunicorn.error:271] [INFO] Handling signal: term
2025-07-15 17:25:25 [9:gunicorn.error:271] [INFO] Worker exiting (pid: 9)
2025-07-15 17:25:25 [12:gunicorn.error:271] [INFO] Worker exiting (pid: 12)
2025-07-15 17:25:26 [11:gunicorn.error:271] [INFO] Worker exiting (pid: 11)
2025-07-15 17:25:26 [10:gunicorn.error:271] [INFO] Worker exiting (pid: 10)
2025-07-15 09:25:26 [1:gunicorn.error:271] [INFO] Shutting down: Master
2025-07-15 09:25:47 [1:gunicorn.error:271] [INFO] Starting gunicorn 19.9.0
2025-07-15 09:25:47 [1:gunicorn.error:271] [INFO] Listening at: http://127.0.0.1:8000,unix:/tmp/steambase_server.sock (1)
2025-07-15 09:25:47 [1:gunicorn.error:271] [INFO] Using worker: gevent
2025-07-15 09:25:47 [9:gunicorn.error:271] [INFO] Booting worker with pid: 9
2025-07-15 09:25:47 [10:gunicorn.error:271] [INFO] Booting worker with pid: 10
2025-07-15 09:25:47 [11:gunicorn.error:271] [INFO] Booting worker with pid: 11
2025-07-15 09:25:47 [12:gunicorn.error:271] [INFO] Booting worker with pid: 12
2025-07-15 09:35:08 [1:gunicorn.error:271] [INFO] Handling signal: term
2025-07-15 17:35:08 [11:gunicorn.error:271] [INFO] Worker exiting (pid: 11)
2025-07-15 17:35:09 [10:gunicorn.error:271] [INFO] Worker exiting (pid: 10)
2025-07-15 17:35:09 [9:gunicorn.error:271] [INFO] Worker exiting (pid: 9)
2025-07-15 17:35:09 [12:gunicorn.error:271] [INFO] Worker exiting (pid: 12)
2025-07-15 09:35:09 [1:gunicorn.error:271] [INFO] Shutting down: Master
2025-07-15 09:35:20 [1:gunicorn.error:271] [INFO] Starting gunicorn 19.9.0
2025-07-15 09:35:20 [1:gunicorn.error:271] [INFO] Listening at: http://127.0.0.1:8000,unix:/tmp/steambase_server.sock (1)
2025-07-15 09:35:20 [1:gunicorn.error:271] [INFO] Using worker: gevent
2025-07-15 09:35:20 [9:gunicorn.error:271] [INFO] Booting worker with pid: 9
2025-07-15 09:35:20 [10:gunicorn.error:271] [INFO] Booting worker with pid: 10
2025-07-15 09:35:20 [11:gunicorn.error:271] [INFO] Booting worker with pid: 11
2025-07-15 09:35:20 [13:gunicorn.error:271] [INFO] Booting worker with pid: 13
2025-07-15 10:36:36 [1:gunicorn.error:271] [INFO] Handling signal: term
2025-07-15 18:36:36 [10:gunicorn.error:271] [INFO] Worker exiting (pid: 10)
2025-07-15 18:36:37 [11:gunicorn.error:271] [INFO] Worker exiting (pid: 11)
2025-07-15 18:36:37 [13:gunicorn.error:271] [INFO] Worker exiting (pid: 13)
2025-07-15 18:36:37 [9:gunicorn.error:271] [INFO] Worker exiting (pid: 9)
2025-07-15 10:36:37 [1:gunicorn.error:271] [INFO] Shutting down: Master
2025-07-15 10:36:48 [1:gunicorn.error:271] [INFO] Starting gunicorn 19.9.0
2025-07-15 10:36:48 [1:gunicorn.error:271] [INFO] Listening at: http://127.0.0.1:8000,unix:/tmp/steambase_server.sock (1)
2025-07-15 10:36:48 [1:gunicorn.error:271] [INFO] Using worker: gevent
2025-07-15 10:36:48 [9:gunicorn.error:271] [INFO] Booting worker with pid: 9
2025-07-15 10:36:48 [10:gunicorn.error:271] [INFO] Booting worker with pid: 10
2025-07-15 10:36:48 [11:gunicorn.error:271] [INFO] Booting worker with pid: 11
2025-07-15 10:36:48 [12:gunicorn.error:271] [INFO] Booting worker with pid: 12
2025-07-15 10:55:21 [1:gunicorn.error:271] [INFO] Handling signal: term
2025-07-15 18:55:21 [9:gunicorn.error:271] [INFO] Worker exiting (pid: 9)
2025-07-15 18:55:21 [12:gunicorn.error:271] [INFO] Worker exiting (pid: 12)
2025-07-15 18:55:22 [11:gunicorn.error:271] [INFO] Worker exiting (pid: 11)
2025-07-15 18:55:22 [10:gunicorn.error:271] [INFO] Worker exiting (pid: 10)
2025-07-15 10:55:23 [1:gunicorn.error:271] [INFO] Shutting down: Master
2025-07-15 10:55:43 [1:gunicorn.error:271] [INFO] Starting gunicorn 19.9.0
2025-07-15 10:55:43 [1:gunicorn.error:271] [INFO] Listening at: http://127.0.0.1:8000,unix:/tmp/steambase_server.sock (1)
2025-07-15 10:55:43 [1:gunicorn.error:271] [INFO] Using worker: gevent
2025-07-15 10:55:43 [9:gunicorn.error:271] [INFO] Booting worker with pid: 9
2025-07-15 10:55:43 [10:gunicorn.error:271] [INFO] Booting worker with pid: 10
2025-07-15 10:55:43 [11:gunicorn.error:271] [INFO] Booting worker with pid: 11
2025-07-15 10:55:43 [12:gunicorn.error:271] [INFO] Booting worker with pid: 12
2025-07-15 11:28:28 [1:gunicorn.error:271] [INFO] Handling signal: term
2025-07-15 19:28:28 [10:gunicorn.error:271] [INFO] Worker exiting (pid: 10)
2025-07-15 19:28:28 [9:gunicorn.error:271] [INFO] Worker exiting (pid: 9)
2025-07-15 19:28:28 [11:gunicorn.error:271] [INFO] Worker exiting (pid: 11)
2025-07-15 19:28:29 [12:gunicorn.error:271] [INFO] Worker exiting (pid: 12)
2025-07-15 11:28:29 [1:gunicorn.error:271] [INFO] Shutting down: Master
2025-07-15 11:28:50 [1:gunicorn.error:271] [INFO] Starting gunicorn 19.9.0
2025-07-15 11:28:50 [1:gunicorn.error:271] [INFO] Listening at: http://127.0.0.1:8000,unix:/tmp/steambase_server.sock (1)
2025-07-15 11:28:50 [1:gunicorn.error:271] [INFO] Using worker: gevent
2025-07-15 11:28:50 [9:gunicorn.error:271] [INFO] Booting worker with pid: 9
2025-07-15 11:28:50 [10:gunicorn.error:271] [INFO] Booting worker with pid: 10
2025-07-15 11:28:50 [11:gunicorn.error:271] [INFO] Booting worker with pid: 11
2025-07-15 11:28:50 [12:gunicorn.error:271] [INFO] Booting worker with pid: 12
2025-07-15 13:42:01 [1:gunicorn.error:271] [INFO] Handling signal: term
2025-07-15 21:42:01 [10:gunicorn.error:271] [INFO] Worker exiting (pid: 10)
2025-07-15 21:42:01 [12:gunicorn.error:271] [INFO] Worker exiting (pid: 12)
2025-07-15 21:42:02 [9:gunicorn.error:271] [INFO] Worker exiting (pid: 9)
2025-07-15 21:42:02 [11:gunicorn.error:271] [INFO] Worker exiting (pid: 11)
2025-07-15 13:42:02 [1:gunicorn.error:271] [INFO] Shutting down: Master
2025-07-15 13:42:23 [1:gunicorn.error:271] [INFO] Starting gunicorn 19.9.0
2025-07-15 13:42:23 [1:gunicorn.error:271] [INFO] Listening at: http://127.0.0.1:8000,unix:/tmp/steambase_server.sock (1)
2025-07-15 13:42:23 [1:gunicorn.error:271] [INFO] Using worker: gevent
2025-07-15 13:42:23 [9:gunicorn.error:271] [INFO] Booting worker with pid: 9
2025-07-15 13:42:23 [10:gunicorn.error:271] [INFO] Booting worker with pid: 10
2025-07-15 13:42:23 [11:gunicorn.error:271] [INFO] Booting worker with pid: 11
2025-07-15 13:42:23 [13:gunicorn.error:271] [INFO] Booting worker with pid: 13
2025-07-17 16:14:33 [1:gunicorn.error:271] [INFO] Handling signal: term
2025-07-18 00:14:33 [9:gunicorn.error:271] [INFO] Worker exiting (pid: 9)
2025-07-18 00:14:33 [13:gunicorn.error:271] [INFO] Worker exiting (pid: 13)
2025-07-18 00:14:33 [10:gunicorn.error:271] [INFO] Worker exiting (pid: 10)
2025-07-18 00:14:33 [11:gunicorn.error:271] [INFO] Worker exiting (pid: 11)
2025-07-17 16:14:34 [1:gunicorn.error:271] [INFO] Shutting down: Master
2025-07-17 16:14:55 [1:gunicorn.error:271] [INFO] Starting gunicorn 19.9.0
2025-07-17 16:14:55 [1:gunicorn.error:271] [INFO] Listening at: http://127.0.0.1:8000,unix:/tmp/steambase_server.sock (1)
2025-07-17 16:14:55 [1:gunicorn.error:271] [INFO] Using worker: gevent
2025-07-17 16:14:55 [9:gunicorn.error:271] [INFO] Booting worker with pid: 9
2025-07-17 16:14:55 [10:gunicorn.error:271] [INFO] Booting worker with pid: 10
2025-07-17 16:14:55 [11:gunicorn.error:271] [INFO] Booting worker with pid: 11
2025-07-17 16:14:55 [12:gunicorn.error:271] [INFO] Booting worker with pid: 12
2025-07-17 16:47:48 [1:gunicorn.error:271] [INFO] Handling signal: term
2025-07-18 00:47:48 [12:gunicorn.error:271] [INFO] Worker exiting (pid: 12)
2025-07-18 00:47:48 [11:gunicorn.error:271] [INFO] Worker exiting (pid: 11)
2025-07-18 00:47:48 [9:gunicorn.error:271] [INFO] Worker exiting (pid: 9)
2025-07-18 00:47:48 [10:gunicorn.error:271] [INFO] Worker exiting (pid: 10)
2025-07-17 16:47:49 [1:gunicorn.error:271] [INFO] Shutting down: Master
2025-07-18 01:30:32 [7:gunicorn.error:277] [INFO] Starting gunicorn 23.0.0
2025-07-18 01:30:32 [7:gunicorn.error:277] [INFO] Listening at: http://127.0.0.1:8000,unix:/tmp/steambase_server.sock (7)
2025-07-18 01:30:32 [7:gunicorn.error:277] [INFO] Using worker: gevent
2025-07-18 01:30:32 [14:gunicorn.error:277] [INFO] Booting worker with pid: 14
2025-07-18 01:30:32 [15:gunicorn.error:277] [INFO] Booting worker with pid: 15
2025-07-18 01:30:32 [16:gunicorn.error:277] [INFO] Booting worker with pid: 16
2025-07-18 01:30:32 [17:gunicorn.error:277] [INFO] Booting worker with pid: 17
2025-07-18 09:30:36 [17:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 91, in populate
    app_config = AppConfig.create(entry)
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 224, in create
    import_module(entry)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 973, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'django_ckeditor_5'
2025-07-18 09:30:36 [16:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 91, in populate
    app_config = AppConfig.create(entry)
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 224, in create
    import_module(entry)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 973, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'django_ckeditor_5'
2025-07-18 09:30:36 [14:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 91, in populate
    app_config = AppConfig.create(entry)
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 224, in create
    import_module(entry)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 973, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'django_ckeditor_5'
2025-07-18 09:30:36 [16:gunicorn.error:277] [INFO] Worker exiting (pid: 16)
2025-07-18 09:30:36 [17:gunicorn.error:277] [INFO] Worker exiting (pid: 17)
2025-07-18 09:30:36 [14:gunicorn.error:277] [INFO] Worker exiting (pid: 14)
2025-07-18 09:30:36 [15:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 91, in populate
    app_config = AppConfig.create(entry)
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 224, in create
    import_module(entry)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 973, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'django_ckeditor_5'
2025-07-18 09:30:36 [15:gunicorn.error:277] [INFO] Worker exiting (pid: 15)
2025-07-18 01:30:36 [7:gunicorn.error:271] [ERROR] Worker (pid:16) exited with code 3
2025-07-18 01:30:36 [7:gunicorn.error:271] [ERROR] Worker (pid:17) was sent SIGTERM!
2025-07-18 01:30:36 [7:gunicorn.error:271] [ERROR] Worker (pid:14) was sent SIGTERM!
2025-07-18 01:30:36 [7:gunicorn.error:271] [ERROR] Worker (pid:15) was sent SIGTERM!
2025-07-18 01:30:36 [7:gunicorn.error:271] [ERROR] Shutting down: Master
2025-07-18 01:30:36 [7:gunicorn.error:271] [ERROR] Reason: Worker failed to boot.
2025-07-18 01:30:38 [19:gunicorn.error:277] [INFO] Starting gunicorn 23.0.0
2025-07-18 01:30:38 [19:gunicorn.error:277] [INFO] Listening at: http://127.0.0.1:8000,unix:/tmp/steambase_server.sock (19)
2025-07-18 01:30:38 [19:gunicorn.error:277] [INFO] Using worker: gevent
2025-07-18 01:30:38 [27:gunicorn.error:277] [INFO] Booting worker with pid: 27
2025-07-18 01:30:38 [28:gunicorn.error:277] [INFO] Booting worker with pid: 28
2025-07-18 01:30:38 [29:gunicorn.error:277] [INFO] Booting worker with pid: 29
2025-07-18 01:30:38 [30:gunicorn.error:277] [INFO] Booting worker with pid: 30
2025-07-18 09:30:41 [27:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 91, in populate
    app_config = AppConfig.create(entry)
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 224, in create
    import_module(entry)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 973, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'django_ckeditor_5'
2025-07-18 09:30:41 [27:gunicorn.error:277] [INFO] Worker exiting (pid: 27)
2025-07-18 01:30:42 [19:gunicorn.error:271] [ERROR] Worker (pid:27) exited with code 3
2025-07-18 09:30:42 [30:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 91, in populate
    app_config = AppConfig.create(entry)
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 224, in create
    import_module(entry)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 973, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'django_ckeditor_5'
2025-07-18 09:30:42 [30:gunicorn.error:277] [INFO] Worker exiting (pid: 30)
2025-07-18 09:30:42 [29:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 91, in populate
    app_config = AppConfig.create(entry)
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 224, in create
    import_module(entry)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 973, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'django_ckeditor_5'
2025-07-18 09:30:42 [29:gunicorn.error:277] [INFO] Worker exiting (pid: 29)
2025-07-18 09:30:42 [28:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 91, in populate
    app_config = AppConfig.create(entry)
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 224, in create
    import_module(entry)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 973, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'django_ckeditor_5'
2025-07-18 09:30:42 [28:gunicorn.error:277] [INFO] Worker exiting (pid: 28)
2025-07-18 01:30:42 [19:gunicorn.error:271] [ERROR] Worker (pid:30) exited with code 3
2025-07-18 01:30:44 [37:gunicorn.error:277] [INFO] Starting gunicorn 23.0.0
2025-07-18 01:30:44 [37:gunicorn.error:277] [INFO] Listening at: http://127.0.0.1:8000,unix:/tmp/steambase_server.sock (37)
2025-07-18 01:30:44 [37:gunicorn.error:277] [INFO] Using worker: gevent
2025-07-18 01:30:44 [39:gunicorn.error:277] [INFO] Booting worker with pid: 39
2025-07-18 01:30:45 [40:gunicorn.error:277] [INFO] Booting worker with pid: 40
2025-07-18 01:30:45 [41:gunicorn.error:277] [INFO] Booting worker with pid: 41
2025-07-18 01:30:45 [42:gunicorn.error:277] [INFO] Booting worker with pid: 42
2025-07-18 09:30:47 [39:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 91, in populate
    app_config = AppConfig.create(entry)
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 224, in create
    import_module(entry)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 973, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'django_ckeditor_5'
2025-07-18 09:30:47 [39:gunicorn.error:277] [INFO] Worker exiting (pid: 39)
2025-07-18 01:30:48 [37:gunicorn.error:271] [ERROR] Worker (pid:39) exited with code 3
2025-07-18 09:30:48 [40:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 91, in populate
    app_config = AppConfig.create(entry)
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 224, in create
    import_module(entry)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 973, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'django_ckeditor_5'
2025-07-18 09:30:48 [40:gunicorn.error:277] [INFO] Worker exiting (pid: 40)
2025-07-18 09:30:48 [41:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 91, in populate
    app_config = AppConfig.create(entry)
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 224, in create
    import_module(entry)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 973, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'django_ckeditor_5'
2025-07-18 09:30:48 [42:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 91, in populate
    app_config = AppConfig.create(entry)
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 224, in create
    import_module(entry)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 973, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'django_ckeditor_5'
2025-07-18 09:30:48 [42:gunicorn.error:277] [INFO] Worker exiting (pid: 42)
2025-07-18 09:30:48 [41:gunicorn.error:277] [INFO] Worker exiting (pid: 41)
2025-07-18 01:30:48 [37:gunicorn.error:271] [ERROR] Worker (pid:40) exited with code 3
2025-07-18 01:30:52 [49:gunicorn.error:277] [INFO] Starting gunicorn 23.0.0
2025-07-18 01:30:52 [49:gunicorn.error:277] [INFO] Listening at: http://127.0.0.1:8000,unix:/tmp/steambase_server.sock (49)
2025-07-18 01:30:52 [49:gunicorn.error:277] [INFO] Using worker: gevent
2025-07-18 01:30:52 [51:gunicorn.error:277] [INFO] Booting worker with pid: 51
2025-07-18 01:30:52 [52:gunicorn.error:277] [INFO] Booting worker with pid: 52
2025-07-18 01:30:52 [53:gunicorn.error:277] [INFO] Booting worker with pid: 53
2025-07-18 01:30:52 [54:gunicorn.error:277] [INFO] Booting worker with pid: 54
2025-07-18 09:30:54 [51:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 91, in populate
    app_config = AppConfig.create(entry)
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 224, in create
    import_module(entry)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 973, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'django_ckeditor_5'
2025-07-18 09:30:54 [51:gunicorn.error:277] [INFO] Worker exiting (pid: 51)
2025-07-18 09:30:54 [52:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 91, in populate
    app_config = AppConfig.create(entry)
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 224, in create
    import_module(entry)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 973, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'django_ckeditor_5'
2025-07-18 09:30:54 [52:gunicorn.error:277] [INFO] Worker exiting (pid: 52)
2025-07-18 01:30:54 [49:gunicorn.error:271] [ERROR] Worker (pid:51) exited with code 3
2025-07-18 01:30:54 [49:gunicorn.error:271] [ERROR] Worker (pid:52) was sent SIGTERM!
2025-07-18 09:30:55 [54:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 91, in populate
    app_config = AppConfig.create(entry)
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 224, in create
    import_module(entry)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 973, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'django_ckeditor_5'
2025-07-18 09:30:55 [54:gunicorn.error:277] [INFO] Worker exiting (pid: 54)
2025-07-18 01:30:55 [49:gunicorn.error:271] [ERROR] Worker (pid:54) exited with code 3
2025-07-18 09:30:55 [53:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 91, in populate
    app_config = AppConfig.create(entry)
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 224, in create
    import_module(entry)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 973, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'django_ckeditor_5'
2025-07-18 09:30:55 [53:gunicorn.error:277] [INFO] Worker exiting (pid: 53)
2025-07-18 01:48:20 [7:gunicorn.error:277] [INFO] Starting gunicorn 23.0.0
2025-07-18 01:48:20 [7:gunicorn.error:277] [INFO] Listening at: http://127.0.0.1:8000,unix:/tmp/steambase_server.sock (7)
2025-07-18 01:48:20 [7:gunicorn.error:277] [INFO] Using worker: gevent
2025-07-18 01:48:20 [14:gunicorn.error:277] [INFO] Booting worker with pid: 14
2025-07-18 01:48:20 [15:gunicorn.error:277] [INFO] Booting worker with pid: 15
2025-07-18 01:48:20 [17:gunicorn.error:277] [INFO] Booting worker with pid: 17
2025-07-18 01:48:20 [18:gunicorn.error:277] [INFO] Booting worker with pid: 18
2025-07-18 09:48:25 [17:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
2025-07-18 09:48:25 [14:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
2025-07-18 09:48:25 [18:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
2025-07-18 09:48:25 [18:gunicorn.error:277] [INFO] Worker exiting (pid: 18)
2025-07-18 09:48:25 [15:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
2025-07-18 09:48:25 [15:gunicorn.error:277] [INFO] Worker exiting (pid: 15)
2025-07-18 09:48:25 [17:gunicorn.error:277] [INFO] Worker exiting (pid: 17)
2025-07-18 09:48:25 [14:gunicorn.error:277] [INFO] Worker exiting (pid: 14)
2025-07-18 01:48:25 [7:gunicorn.error:271] [ERROR] Worker (pid:18) exited with code 3
2025-07-18 01:48:25 [7:gunicorn.error:271] [ERROR] Worker (pid:15) was sent SIGTERM!
2025-07-18 01:48:25 [7:gunicorn.error:271] [ERROR] Worker (pid:14) was sent SIGTERM!
2025-07-18 01:48:25 [7:gunicorn.error:271] [ERROR] Worker (pid:17) was sent SIGTERM!
2025-07-18 01:48:25 [7:gunicorn.error:271] [ERROR] Shutting down: Master
2025-07-18 01:48:25 [7:gunicorn.error:271] [ERROR] Reason: Worker failed to boot.
2025-07-18 01:48:26 [20:gunicorn.error:277] [INFO] Starting gunicorn 23.0.0
2025-07-18 01:48:26 [20:gunicorn.error:277] [INFO] Listening at: http://127.0.0.1:8000,unix:/tmp/steambase_server.sock (20)
2025-07-18 01:48:26 [20:gunicorn.error:277] [INFO] Using worker: gevent
2025-07-18 01:48:26 [27:gunicorn.error:277] [INFO] Booting worker with pid: 27
2025-07-18 01:48:26 [28:gunicorn.error:277] [INFO] Booting worker with pid: 28
2025-07-18 01:48:26 [29:gunicorn.error:277] [INFO] Booting worker with pid: 29
2025-07-18 01:48:26 [30:gunicorn.error:277] [INFO] Booting worker with pid: 30
2025-07-18 09:48:30 [30:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
2025-07-18 09:48:30 [30:gunicorn.error:277] [INFO] Worker exiting (pid: 30)
2025-07-18 09:48:31 [28:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
2025-07-18 09:48:31 [28:gunicorn.error:277] [INFO] Worker exiting (pid: 28)
2025-07-18 09:48:31 [29:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
2025-07-18 09:48:31 [29:gunicorn.error:277] [INFO] Worker exiting (pid: 29)
2025-07-18 01:48:31 [20:gunicorn.error:271] [ERROR] Worker (pid:30) exited with code 3
2025-07-18 01:48:31 [20:gunicorn.error:271] [ERROR] Worker (pid:28) was sent SIGTERM!
2025-07-18 01:48:31 [20:gunicorn.error:271] [ERROR] Worker (pid:29) was sent SIGTERM!
2025-07-18 09:48:32 [27:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
2025-07-18 09:48:32 [27:gunicorn.error:277] [INFO] Worker exiting (pid: 27)
2025-07-18 01:48:33 [20:gunicorn.error:271] [ERROR] Worker (pid:27) exited with code 3
2025-07-18 01:48:34 [37:gunicorn.error:277] [INFO] Starting gunicorn 23.0.0
2025-07-18 01:48:34 [37:gunicorn.error:277] [INFO] Listening at: http://127.0.0.1:8000,unix:/tmp/steambase_server.sock (37)
2025-07-18 01:48:34 [37:gunicorn.error:277] [INFO] Using worker: gevent
2025-07-18 01:48:34 [39:gunicorn.error:277] [INFO] Booting worker with pid: 39
2025-07-18 01:48:34 [40:gunicorn.error:277] [INFO] Booting worker with pid: 40
2025-07-18 01:48:34 [41:gunicorn.error:277] [INFO] Booting worker with pid: 41
2025-07-18 01:48:34 [42:gunicorn.error:277] [INFO] Booting worker with pid: 42
2025-07-18 09:48:39 [39:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
2025-07-18 09:48:39 [39:gunicorn.error:277] [INFO] Worker exiting (pid: 39)
2025-07-18 09:48:39 [40:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
2025-07-18 09:48:39 [40:gunicorn.error:277] [INFO] Worker exiting (pid: 40)
2025-07-18 09:48:39 [41:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
2025-07-18 09:48:39 [41:gunicorn.error:277] [INFO] Worker exiting (pid: 41)
2025-07-18 01:48:39 [37:gunicorn.error:271] [ERROR] Worker (pid:39) exited with code 3
2025-07-18 01:48:39 [37:gunicorn.error:271] [ERROR] Worker (pid:41) was sent SIGTERM!
2025-07-18 01:48:39 [37:gunicorn.error:271] [ERROR] Worker (pid:40) was sent SIGTERM!
2025-07-18 09:48:39 [42:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
2025-07-18 09:48:39 [42:gunicorn.error:277] [INFO] Worker exiting (pid: 42)
2025-07-18 01:48:40 [37:gunicorn.error:271] [ERROR] Worker (pid:42) exited with code 3
2025-07-18 01:48:40 [49:gunicorn.error:277] [INFO] Starting gunicorn 23.0.0
2025-07-18 01:48:40 [49:gunicorn.error:277] [INFO] Listening at: http://127.0.0.1:8000,unix:/tmp/steambase_server.sock (49)
2025-07-18 01:48:40 [49:gunicorn.error:277] [INFO] Using worker: gevent
2025-07-18 01:48:40 [51:gunicorn.error:277] [INFO] Booting worker with pid: 51
2025-07-18 01:48:40 [52:gunicorn.error:277] [INFO] Booting worker with pid: 52
2025-07-18 01:48:40 [53:gunicorn.error:277] [INFO] Booting worker with pid: 53
2025-07-18 01:48:40 [54:gunicorn.error:277] [INFO] Booting worker with pid: 54
2025-07-18 09:48:44 [51:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
2025-07-18 09:48:44 [51:gunicorn.error:277] [INFO] Worker exiting (pid: 51)
2025-07-18 09:48:44 [53:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
2025-07-18 09:48:44 [53:gunicorn.error:277] [INFO] Worker exiting (pid: 53)
2025-07-18 09:48:44 [54:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
2025-07-18 09:48:44 [54:gunicorn.error:277] [INFO] Worker exiting (pid: 54)
2025-07-18 01:48:45 [49:gunicorn.error:271] [ERROR] Worker (pid:51) exited with code 3
2025-07-18 01:48:45 [49:gunicorn.error:271] [ERROR] Worker (pid:53) was sent SIGTERM!
2025-07-18 01:48:45 [49:gunicorn.error:271] [ERROR] Worker (pid:54) was sent SIGTERM!
2025-07-18 09:48:45 [52:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
2025-07-18 09:48:45 [52:gunicorn.error:277] [INFO] Worker exiting (pid: 52)
2025-07-18 01:48:45 [49:gunicorn.error:271] [ERROR] Worker (pid:52) exited with code 3
2025-07-18 01:48:46 [61:gunicorn.error:277] [INFO] Starting gunicorn 23.0.0
2025-07-18 01:48:46 [61:gunicorn.error:277] [INFO] Listening at: http://127.0.0.1:8000,unix:/tmp/steambase_server.sock (61)
2025-07-18 01:48:46 [61:gunicorn.error:277] [INFO] Using worker: gevent
2025-07-18 01:48:46 [63:gunicorn.error:277] [INFO] Booting worker with pid: 63
2025-07-18 01:48:46 [64:gunicorn.error:277] [INFO] Booting worker with pid: 64
2025-07-18 01:48:47 [65:gunicorn.error:277] [INFO] Booting worker with pid: 65
2025-07-18 01:48:47 [66:gunicorn.error:277] [INFO] Booting worker with pid: 66
2025-07-18 09:48:50 [64:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
2025-07-18 09:48:50 [64:gunicorn.error:277] [INFO] Worker exiting (pid: 64)
2025-07-18 09:48:50 [63:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
2025-07-18 09:48:50 [63:gunicorn.error:277] [INFO] Worker exiting (pid: 63)
2025-07-18 09:48:50 [65:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
2025-07-18 09:48:50 [65:gunicorn.error:277] [INFO] Worker exiting (pid: 65)
2025-07-18 01:48:50 [61:gunicorn.error:271] [ERROR] Worker (pid:64) exited with code 3
2025-07-18 01:48:50 [61:gunicorn.error:271] [ERROR] Worker (pid:63) was sent SIGTERM!
2025-07-18 01:48:50 [61:gunicorn.error:271] [ERROR] Worker (pid:65) was sent SIGTERM!
2025-07-18 09:48:51 [66:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
2025-07-18 09:48:51 [66:gunicorn.error:277] [INFO] Worker exiting (pid: 66)
2025-07-18 01:48:51 [61:gunicorn.error:271] [ERROR] Worker (pid:66) exited with code 3
2025-07-18 01:48:51 [77:gunicorn.error:277] [INFO] Starting gunicorn 23.0.0
2025-07-18 01:48:51 [77:gunicorn.error:277] [INFO] Listening at: http://127.0.0.1:8000,unix:/tmp/steambase_server.sock (77)
2025-07-18 01:48:51 [77:gunicorn.error:277] [INFO] Using worker: gevent
2025-07-18 01:48:51 [79:gunicorn.error:277] [INFO] Booting worker with pid: 79
2025-07-18 01:48:52 [80:gunicorn.error:277] [INFO] Booting worker with pid: 80
2025-07-18 01:48:52 [81:gunicorn.error:277] [INFO] Booting worker with pid: 81
2025-07-18 01:48:52 [82:gunicorn.error:277] [INFO] Booting worker with pid: 82
2025-07-18 09:48:55 [81:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
2025-07-18 09:48:55 [81:gunicorn.error:277] [INFO] Worker exiting (pid: 81)
2025-07-18 09:48:55 [79:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
2025-07-18 09:48:55 [79:gunicorn.error:277] [INFO] Worker exiting (pid: 79)
2025-07-18 01:48:56 [77:gunicorn.error:271] [ERROR] Worker (pid:81) exited with code 3
2025-07-18 01:48:56 [77:gunicorn.error:271] [ERROR] Worker (pid:79) was sent SIGTERM!
2025-07-18 09:48:56 [82:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
2025-07-18 09:48:56 [82:gunicorn.error:277] [INFO] Worker exiting (pid: 82)
2025-07-18 09:48:56 [80:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
2025-07-18 09:48:56 [80:gunicorn.error:277] [INFO] Worker exiting (pid: 80)
2025-07-18 01:48:56 [77:gunicorn.error:271] [ERROR] Worker (pid:82) exited with code 3
2025-07-18 01:48:57 [85:gunicorn.error:277] [INFO] Starting gunicorn 23.0.0
2025-07-18 01:48:57 [85:gunicorn.error:277] [INFO] Listening at: http://127.0.0.1:8000,unix:/tmp/steambase_server.sock (85)
2025-07-18 01:48:57 [85:gunicorn.error:277] [INFO] Using worker: gevent
2025-07-18 01:48:57 [87:gunicorn.error:277] [INFO] Booting worker with pid: 87
2025-07-18 01:48:57 [88:gunicorn.error:277] [INFO] Booting worker with pid: 88
2025-07-18 01:48:57 [89:gunicorn.error:277] [INFO] Booting worker with pid: 89
2025-07-18 01:48:57 [90:gunicorn.error:277] [INFO] Booting worker with pid: 90
2025-07-18 09:49:00 [88:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
2025-07-18 09:49:00 [88:gunicorn.error:277] [INFO] Worker exiting (pid: 88)
2025-07-18 01:49:00 [85:gunicorn.error:271] [ERROR] Worker (pid:88) exited with code 3
2025-07-18 09:49:00 [89:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
2025-07-18 09:49:00 [89:gunicorn.error:277] [INFO] Worker exiting (pid: 89)
2025-07-18 09:49:00 [87:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
2025-07-18 09:49:00 [87:gunicorn.error:277] [INFO] Worker exiting (pid: 87)
2025-07-18 01:49:00 [85:gunicorn.error:271] [ERROR] Worker (pid:89) exited with code 3
2025-07-18 09:49:00 [90:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
2025-07-18 09:49:00 [90:gunicorn.error:277] [INFO] Worker exiting (pid: 90)
2025-07-18 01:49:02 [91:gunicorn.error:277] [INFO] Starting gunicorn 23.0.0
2025-07-18 01:49:02 [91:gunicorn.error:277] [INFO] Listening at: http://127.0.0.1:8000,unix:/tmp/steambase_server.sock (91)
2025-07-18 01:49:02 [91:gunicorn.error:277] [INFO] Using worker: gevent
2025-07-18 01:49:02 [93:gunicorn.error:277] [INFO] Booting worker with pid: 93
2025-07-18 01:49:02 [94:gunicorn.error:277] [INFO] Booting worker with pid: 94
2025-07-18 01:49:02 [95:gunicorn.error:277] [INFO] Booting worker with pid: 95
2025-07-18 01:49:02 [96:gunicorn.error:277] [INFO] Booting worker with pid: 96
2025-07-18 09:49:04 [95:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
2025-07-18 09:49:04 [95:gunicorn.error:277] [INFO] Worker exiting (pid: 95)
2025-07-18 09:49:05 [94:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
2025-07-18 09:49:05 [94:gunicorn.error:277] [INFO] Worker exiting (pid: 94)
2025-07-18 01:49:05 [91:gunicorn.error:271] [ERROR] Worker (pid:95) exited with code 3
2025-07-18 01:49:05 [91:gunicorn.error:271] [ERROR] Worker (pid:94) was sent SIGTERM!
2025-07-18 09:49:05 [93:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
2025-07-18 09:49:05 [93:gunicorn.error:277] [INFO] Worker exiting (pid: 93)
2025-07-18 09:49:05 [96:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
2025-07-18 09:49:05 [96:gunicorn.error:277] [INFO] Worker exiting (pid: 96)
2025-07-18 01:49:05 [91:gunicorn.error:271] [ERROR] Worker (pid:93) exited with code 3
2025-07-18 01:49:07 [97:gunicorn.error:277] [INFO] Starting gunicorn 23.0.0
2025-07-18 01:49:07 [97:gunicorn.error:277] [INFO] Listening at: http://127.0.0.1:8000,unix:/tmp/steambase_server.sock (97)
2025-07-18 01:49:07 [97:gunicorn.error:277] [INFO] Using worker: gevent
2025-07-18 01:49:07 [99:gunicorn.error:277] [INFO] Booting worker with pid: 99
2025-07-18 01:49:07 [100:gunicorn.error:277] [INFO] Booting worker with pid: 100
2025-07-18 01:49:07 [101:gunicorn.error:277] [INFO] Booting worker with pid: 101
2025-07-18 01:49:07 [102:gunicorn.error:277] [INFO] Booting worker with pid: 102
2025-07-18 09:49:10 [99:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
2025-07-18 09:49:10 [99:gunicorn.error:277] [INFO] Worker exiting (pid: 99)
2025-07-18 01:49:10 [97:gunicorn.error:271] [ERROR] Worker (pid:99) exited with code 3
2025-07-18 09:49:11 [100:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
2025-07-18 09:49:11 [100:gunicorn.error:277] [INFO] Worker exiting (pid: 100)
2025-07-18 09:49:11 [102:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
2025-07-18 09:49:11 [102:gunicorn.error:277] [INFO] Worker exiting (pid: 102)
2025-07-18 09:49:11 [101:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
2025-07-18 09:49:11 [101:gunicorn.error:277] [INFO] Worker exiting (pid: 101)
2025-07-18 01:49:11 [97:gunicorn.error:271] [ERROR] Worker (pid:100) exited with code 3
2025-07-18 01:49:14 [103:gunicorn.error:277] [INFO] Starting gunicorn 23.0.0
2025-07-18 01:49:14 [103:gunicorn.error:277] [INFO] Listening at: http://127.0.0.1:8000,unix:/tmp/steambase_server.sock (103)
2025-07-18 01:49:14 [103:gunicorn.error:277] [INFO] Using worker: gevent
2025-07-18 01:49:14 [105:gunicorn.error:277] [INFO] Booting worker with pid: 105
2025-07-18 01:49:14 [106:gunicorn.error:277] [INFO] Booting worker with pid: 106
2025-07-18 01:49:14 [107:gunicorn.error:277] [INFO] Booting worker with pid: 107
2025-07-18 01:49:14 [108:gunicorn.error:277] [INFO] Booting worker with pid: 108
2025-07-18 09:49:17 [105:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
2025-07-18 09:49:17 [105:gunicorn.error:277] [INFO] Worker exiting (pid: 105)
2025-07-18 09:49:17 [108:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
2025-07-18 09:49:17 [108:gunicorn.error:277] [INFO] Worker exiting (pid: 108)
2025-07-18 01:49:17 [103:gunicorn.error:271] [ERROR] Worker (pid:108) exited with code 3
2025-07-18 01:49:17 [103:gunicorn.error:271] [ERROR] Worker (pid:105) was sent SIGTERM!
2025-07-18 09:49:17 [106:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
2025-07-18 09:49:17 [106:gunicorn.error:277] [INFO] Worker exiting (pid: 106)
2025-07-18 01:49:18 [103:gunicorn.error:271] [ERROR] Worker (pid:106) exited with code 3
2025-07-18 09:49:18 [107:gunicorn.error:283] [ERROR] Exception in worker process
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/gunicorn/arbiter.py", line 608, in spawn_worker
    worker.init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 146, in init_process
    super().init_process()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 135, in init_process
    self.load_wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base.py", line 147, in load_wsgi
    self.wsgi = self.app.wsgi()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/base.py", line 66, in wsgi
    self.callable = self.load()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 57, in load
    return self.load_wsgiapp()
  File "/usr/local/lib/python3.8/site-packages/gunicorn/app/wsgiapp.py", line 47, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/util.py", line 370, in import_app
    mod = importlib.import_module(module)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/steambase/wsgi.py", line 16, in <module>
    application = get_wsgi_application()
  File "/usr/local/lib/python3.8/site-packages/django/core/wsgi.py", line 12, in get_wsgi_application
    django.setup(set_prefix=False)
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
2025-07-18 09:49:18 [107:gunicorn.error:277] [INFO] Worker exiting (pid: 107)
2025-07-18 02:20:24 [6:gunicorn.error:277] [INFO] Starting gunicorn 23.0.0
2025-07-18 02:20:24 [6:gunicorn.error:277] [INFO] Listening at: http://127.0.0.1:8000,unix:/tmp/steambase_server.sock (6)
2025-07-18 02:20:24 [6:gunicorn.error:277] [INFO] Using worker: gevent
2025-07-18 02:20:24 [13:gunicorn.error:277] [INFO] Booting worker with pid: 13
2025-07-18 02:20:24 [14:gunicorn.error:277] [INFO] Booting worker with pid: 14
2025-07-18 02:20:24 [15:gunicorn.error:277] [INFO] Booting worker with pid: 15
2025-07-18 02:20:24 [16:gunicorn.error:277] [INFO] Booting worker with pid: 16
2025-07-18 10:21:34 [15:django.request:224] [WARNING] Not Found: /
2025-07-18 02:23:44 [6:gunicorn.error:277] [INFO] Handling signal: term
2025-07-18 10:23:45 [14:gunicorn.error:277] [INFO] Worker exiting (pid: 14)
2025-07-18 10:23:45 [15:gunicorn.error:277] [INFO] Worker exiting (pid: 15)
2025-07-18 10:23:45 [13:gunicorn.error:277] [INFO] Worker exiting (pid: 13)
2025-07-18 10:23:45 [16:gunicorn.error:277] [INFO] Worker exiting (pid: 16)
2025-07-18 02:23:46 [6:gunicorn.error:277] [INFO] Shutting down: Master
2025-07-18 02:24:19 [7:gunicorn.error:277] [INFO] Starting gunicorn 23.0.0
2025-07-18 02:24:19 [7:gunicorn.error:277] [INFO] Listening at: http://127.0.0.1:8000,unix:/tmp/steambase_server.sock (7)
2025-07-18 02:24:19 [7:gunicorn.error:277] [INFO] Using worker: gevent
2025-07-18 02:24:19 [15:gunicorn.error:277] [INFO] Booting worker with pid: 15
2025-07-18 02:24:19 [16:gunicorn.error:277] [INFO] Booting worker with pid: 16
2025-07-18 02:24:19 [17:gunicorn.error:277] [INFO] Booting worker with pid: 17
2025-07-18 02:24:19 [18:gunicorn.error:277] [INFO] Booting worker with pid: 18
2025-07-18 02:30:13 [7:gunicorn.error:277] [INFO] Handling signal: term
2025-07-18 10:30:13 [17:gunicorn.error:277] [INFO] Worker exiting (pid: 17)
2025-07-18 10:30:14 [18:gunicorn.error:277] [INFO] Worker exiting (pid: 18)
2025-07-18 10:30:14 [15:gunicorn.error:277] [INFO] Worker exiting (pid: 15)
2025-07-18 10:30:14 [16:gunicorn.error:277] [INFO] Worker exiting (pid: 16)
2025-07-18 02:30:14 [7:gunicorn.error:277] [INFO] Shutting down: Master
2025-07-18 02:30:17 [7:gunicorn.error:277] [INFO] Starting gunicorn 23.0.0
2025-07-18 02:30:17 [7:gunicorn.error:277] [INFO] Listening at: http://127.0.0.1:8000,unix:/tmp/steambase_server.sock (7)
2025-07-18 02:30:17 [7:gunicorn.error:277] [INFO] Using worker: gevent
2025-07-18 02:30:17 [14:gunicorn.error:277] [INFO] Booting worker with pid: 14
2025-07-18 02:30:17 [15:gunicorn.error:277] [INFO] Booting worker with pid: 15
2025-07-18 02:30:17 [16:gunicorn.error:277] [INFO] Booting worker with pid: 16
2025-07-18 02:30:17 [17:gunicorn.error:277] [INFO] Booting worker with pid: 17
2025-07-18 02:33:03 [7:gunicorn.error:277] [INFO] Handling signal: term
2025-07-18 10:33:04 [15:gunicorn.error:277] [INFO] Worker exiting (pid: 15)
2025-07-18 10:33:04 [14:gunicorn.error:277] [INFO] Worker exiting (pid: 14)
2025-07-18 10:33:04 [16:gunicorn.error:277] [INFO] Worker exiting (pid: 16)
2025-07-18 10:33:04 [17:gunicorn.error:277] [INFO] Worker exiting (pid: 17)
2025-07-18 02:33:05 [7:gunicorn.error:277] [INFO] Shutting down: Master
2025-07-18 02:33:07 [7:gunicorn.error:277] [INFO] Starting gunicorn 23.0.0
2025-07-18 02:33:07 [7:gunicorn.error:277] [INFO] Listening at: http://127.0.0.1:8000,unix:/tmp/steambase_server.sock (7)
2025-07-18 02:33:07 [7:gunicorn.error:277] [INFO] Using worker: gevent
2025-07-18 02:33:07 [15:gunicorn.error:277] [INFO] Booting worker with pid: 15
2025-07-18 02:33:07 [16:gunicorn.error:277] [INFO] Booting worker with pid: 16
2025-07-18 02:33:07 [17:gunicorn.error:277] [INFO] Booting worker with pid: 17
2025-07-18 02:33:07 [18:gunicorn.error:277] [INFO] Booting worker with pid: 18
2025-07-18 02:39:45 [7:gunicorn.error:277] [INFO] Handling signal: term
2025-07-18 10:39:45 [16:gunicorn.error:277] [INFO] Worker exiting (pid: 16)
2025-07-18 10:39:46 [18:gunicorn.error:277] [INFO] Worker exiting (pid: 18)
2025-07-18 10:39:46 [17:gunicorn.error:277] [INFO] Worker exiting (pid: 17)
2025-07-18 10:39:46 [15:gunicorn.error:277] [INFO] Worker exiting (pid: 15)
2025-07-18 02:39:46 [7:gunicorn.error:277] [INFO] Shutting down: Master
2025-07-18 02:39:49 [7:gunicorn.error:277] [INFO] Starting gunicorn 23.0.0
2025-07-18 02:39:49 [7:gunicorn.error:277] [INFO] Listening at: http://127.0.0.1:8000,unix:/tmp/steambase_server.sock (7)
2025-07-18 02:39:49 [7:gunicorn.error:277] [INFO] Using worker: gevent
2025-07-18 02:39:49 [15:gunicorn.error:277] [INFO] Booting worker with pid: 15
2025-07-18 02:39:49 [16:gunicorn.error:277] [INFO] Booting worker with pid: 16
2025-07-18 02:39:49 [17:gunicorn.error:277] [INFO] Booting worker with pid: 17
2025-07-18 02:39:49 [18:gunicorn.error:277] [INFO] Booting worker with pid: 18
2025-07-18 03:27:47 [7:gunicorn.error:277] [INFO] Handling signal: term
2025-07-18 11:27:47 [16:gunicorn.error:277] [INFO] Worker exiting (pid: 16)
2025-07-18 11:27:48 [17:gunicorn.error:277] [INFO] Worker exiting (pid: 17)
2025-07-18 11:27:48 [15:gunicorn.error:277] [INFO] Worker exiting (pid: 15)
2025-07-18 11:27:48 [18:gunicorn.error:277] [INFO] Worker exiting (pid: 18)
2025-07-18 03:27:49 [7:gunicorn.error:277] [INFO] Shutting down: Master
2025-07-18 03:27:51 [7:gunicorn.error:277] [INFO] Starting gunicorn 23.0.0
2025-07-18 03:27:51 [7:gunicorn.error:277] [INFO] Listening at: http://127.0.0.1:8000,unix:/tmp/steambase_server.sock (7)
2025-07-18 03:27:51 [7:gunicorn.error:277] [INFO] Using worker: gevent
2025-07-18 03:27:51 [15:gunicorn.error:277] [INFO] Booting worker with pid: 15
2025-07-18 03:27:51 [16:gunicorn.error:277] [INFO] Booting worker with pid: 16
2025-07-18 03:27:51 [17:gunicorn.error:277] [INFO] Booting worker with pid: 17
2025-07-18 03:27:51 [18:gunicorn.error:277] [INFO] Booting worker with pid: 18
2025-07-18 03:32:32 [7:gunicorn.error:277] [INFO] Handling signal: term
2025-07-18 11:32:32 [17:gunicorn.error:277] [INFO] Worker exiting (pid: 17)
2025-07-18 11:32:32 [15:gunicorn.error:277] [INFO] Worker exiting (pid: 15)
2025-07-18 11:32:32 [16:gunicorn.error:277] [INFO] Worker exiting (pid: 16)
2025-07-18 11:32:32 [18:gunicorn.error:277] [INFO] Worker exiting (pid: 18)
2025-07-18 03:32:33 [7:gunicorn.error:277] [INFO] Shutting down: Master
2025-07-18 03:32:35 [7:gunicorn.error:277] [INFO] Starting gunicorn 23.0.0
2025-07-18 03:32:35 [7:gunicorn.error:277] [INFO] Listening at: http://127.0.0.1:8000,unix:/tmp/steambase_server.sock (7)
2025-07-18 03:32:35 [7:gunicorn.error:277] [INFO] Using worker: gevent
2025-07-18 03:32:35 [14:gunicorn.error:277] [INFO] Booting worker with pid: 14
2025-07-18 03:32:35 [15:gunicorn.error:277] [INFO] Booting worker with pid: 15
2025-07-18 03:32:35 [16:gunicorn.error:277] [INFO] Booting worker with pid: 16
2025-07-18 03:32:35 [17:gunicorn.error:277] [INFO] Booting worker with pid: 17
2025-07-18 03:58:41 [7:gunicorn.error:277] [INFO] Handling signal: term
2025-07-18 11:58:41 [16:gunicorn.error:277] [INFO] Worker exiting (pid: 16)
2025-07-18 11:58:41 [17:gunicorn.error:277] [INFO] Worker exiting (pid: 17)
2025-07-18 11:58:41 [14:gunicorn.error:277] [INFO] Worker exiting (pid: 14)
2025-07-18 11:58:41 [15:gunicorn.error:277] [INFO] Worker exiting (pid: 15)
2025-07-18 03:58:43 [7:gunicorn.error:277] [INFO] Shutting down: Master
2025-07-18 03:58:46 [6:gunicorn.error:277] [INFO] Starting gunicorn 23.0.0
2025-07-18 03:58:46 [6:gunicorn.error:277] [INFO] Listening at: http://127.0.0.1:8000,unix:/tmp/steambase_server.sock (6)
2025-07-18 03:58:46 [6:gunicorn.error:277] [INFO] Using worker: gevent
2025-07-18 03:58:46 [13:gunicorn.error:277] [INFO] Booting worker with pid: 13
2025-07-18 03:58:46 [14:gunicorn.error:277] [INFO] Booting worker with pid: 14
2025-07-18 03:58:46 [15:gunicorn.error:277] [INFO] Booting worker with pid: 15
2025-07-18 03:58:46 [16:gunicorn.error:277] [INFO] Booting worker with pid: 16
2025-07-18 04:09:07 [6:gunicorn.error:277] [INFO] Handling signal: term
2025-07-18 12:09:08 [15:gunicorn.error:277] [INFO] Worker exiting (pid: 15)
2025-07-18 12:09:08 [14:gunicorn.error:277] [INFO] Worker exiting (pid: 14)
2025-07-18 12:09:08 [13:gunicorn.error:277] [INFO] Worker exiting (pid: 13)
2025-07-18 12:09:08 [16:gunicorn.error:277] [INFO] Worker exiting (pid: 16)
2025-07-18 04:09:09 [6:gunicorn.error:277] [INFO] Shutting down: Master
2025-07-18 04:09:12 [6:gunicorn.error:277] [INFO] Starting gunicorn 23.0.0
2025-07-18 04:09:12 [6:gunicorn.error:277] [INFO] Listening at: http://0.0.0.0:8000,unix:/tmp/steambase_server.sock (6)
2025-07-18 04:09:12 [6:gunicorn.error:277] [INFO] Using worker: gevent
2025-07-18 04:09:12 [14:gunicorn.error:277] [INFO] Booting worker with pid: 14
2025-07-18 04:09:13 [15:gunicorn.error:277] [INFO] Booting worker with pid: 15
2025-07-18 04:09:13 [16:gunicorn.error:277] [INFO] Booting worker with pid: 16
2025-07-18 04:09:13 [17:gunicorn.error:277] [INFO] Booting worker with pid: 17
2025-07-18 12:09:34 [14:django.request:224] [WARNING] Not Found: /static/admin/css/base.css
2025-07-18 12:09:34 [14:django.request:224] [WARNING] Not Found: /static/admin/js/nav_sidebar.js
2025-07-18 12:09:34 [16:django.request:224] [WARNING] Not Found: /static/admin/css/responsive.css
2025-07-18 12:09:35 [17:django.request:224] [WARNING] Not Found: /static/admin/css/login.css
2025-07-18 12:09:35 [15:django.request:224] [WARNING] Not Found: /static/admin/css/nav_sidebar.css
2025-07-18 12:09:35 [15:django.request:224] [WARNING] Not Found: /favicon.ico
2025-07-18 12:09:43 [15:django.request:224] [WARNING] Not Found: /static/admin/css/base.css
2025-07-18 12:09:43 [17:django.request:224] [WARNING] Not Found: /static/admin/css/nav_sidebar.css
2025-07-18 12:09:44 [14:django.request:224] [WARNING] Not Found: /static/admin/css/login.css
2025-07-18 12:09:44 [14:django.request:224] [WARNING] Not Found: /static/admin/css/responsive.css
2025-07-18 12:09:44 [15:django.request:224] [WARNING] Not Found: /static/admin/js/nav_sidebar.js
2025-07-18 12:09:59 [16:django.request:224] [WARNING] Not Found: /static/admin/css/base.css
2025-07-18 12:09:59 [15:django.request:224] [WARNING] Not Found: /static/admin/css/nav_sidebar.css
2025-07-18 12:09:59 [15:django.request:224] [WARNING] Not Found: /static/admin/css/responsive.css
2025-07-18 12:09:59 [17:django.request:224] [WARNING] Not Found: /static/admin/css/login.css
2025-07-18 12:09:59 [16:django.request:224] [WARNING] Not Found: /static/admin/js/nav_sidebar.js
2025-07-18 12:10:07 [16:django.request:224] [WARNING] Not Found: /static/admin/css/base.css
2025-07-18 12:10:07 [14:django.request:224] [WARNING] Not Found: /static/admin/css/nav_sidebar.css
2025-07-18 12:10:07 [16:django.request:224] [WARNING] Not Found: /static/admin/css/login.css
2025-07-18 12:10:07 [16:django.request:224] [WARNING] Not Found: /static/admin/css/responsive.css
2025-07-18 12:10:07 [16:django.request:224] [WARNING] Not Found: /static/admin/js/nav_sidebar.js
2025-07-18 12:10:16 [17:django.request:224] [WARNING] Not Found: /static/admin/css/base.css
2025-07-18 12:10:16 [14:django.request:224] [WARNING] Not Found: /static/admin/css/nav_sidebar.css
2025-07-18 12:10:16 [15:django.request:224] [WARNING] Not Found: /static/admin/js/nav_sidebar.js
2025-07-18 12:10:16 [15:django.request:224] [WARNING] Not Found: /static/admin/css/login.css
2025-07-18 12:10:16 [17:django.request:224] [WARNING] Not Found: /static/admin/css/responsive.css
2025-07-18 12:10:54 [14:django.request:224] [WARNING] Not Found: /static/admin/css/base.css
2025-07-18 12:10:54 [16:django.request:224] [WARNING] Not Found: /static/admin/css/nav_sidebar.css
2025-07-18 12:10:54 [15:django.request:224] [WARNING] Not Found: /static/admin/css/login.css
2025-07-18 12:10:54 [15:django.request:224] [WARNING] Not Found: /static/admin/css/responsive.css
2025-07-18 12:10:54 [17:django.request:224] [WARNING] Not Found: /static/admin/js/nav_sidebar.js
2025-07-18 12:10:57 [17:django.request:224] [WARNING] Not Found: /static/admin/css/base.css
2025-07-18 12:10:57 [14:django.request:224] [WARNING] Not Found: /static/admin/css/nav_sidebar.css
2025-07-18 12:10:57 [15:django.request:224] [WARNING] Not Found: /static/admin/css/login.css
2025-07-18 12:10:57 [17:django.request:224] [WARNING] Not Found: /static/admin/css/responsive.css
2025-07-18 12:10:57 [17:django.request:224] [WARNING] Not Found: /static/admin/js/nav_sidebar.js
2025-07-18 12:10:57 [17:django.request:224] [WARNING] Not Found: /favicon.ico
2025-07-18 12:10:58 [15:django.request:224] [WARNING] Not Found: /static/admin/css/login.css
2025-07-18 12:10:58 [17:django.request:224] [WARNING] Not Found: /static/admin/css/nav_sidebar.css
2025-07-18 12:10:58 [17:django.request:224] [WARNING] Not Found: /static/admin/css/base.css
2025-07-18 12:10:58 [15:django.request:224] [WARNING] Not Found: /static/admin/css/responsive.css
2025-07-18 12:10:58 [17:django.request:224] [WARNING] Not Found: /static/admin/js/nav_sidebar.js
2025-07-18 12:11:08 [17:django.request:224] [WARNING] Not Found: /static/admin/css/base.css
2025-07-18 12:11:08 [15:django.request:224] [WARNING] Not Found: /static/admin/css/nav_sidebar.css
2025-07-18 12:11:08 [15:django.request:224] [WARNING] Not Found: /static/admin/css/dashboard.css
2025-07-18 12:11:08 [15:django.request:224] [WARNING] Not Found: /static/admin/js/nav_sidebar.js
2025-07-18 12:11:08 [15:django.request:224] [WARNING] Not Found: /static/admin/css/responsive.css
2025-07-18 12:11:16 [17:django.request:224] [WARNING] Not Found: /static/admin/css/base.css
2025-07-18 12:11:17 [17:django.request:224] [WARNING] Not Found: /static/admin/css/responsive.css
2025-07-18 12:11:17 [17:django.request:224] [WARNING] Not Found: /static/admin/css/nav_sidebar.css
2025-07-18 12:11:17 [15:django.request:224] [WARNING] Not Found: /static/admin/css/dashboard.css
2025-07-18 12:11:17 [15:django.request:224] [WARNING] Not Found: /static/admin/js/nav_sidebar.js
2025-07-18 12:12:03 [14:django.request:224] [WARNING] Not Found: /admin/
2025-07-18 12:12:59 [15:django.request:224] [WARNING] Not Found: /static/admin/css/base.css
2025-07-18 12:13:14 [14:django.request:224] [WARNING] Not Found: /static/admin/css/base.css
2025-07-18 12:13:14 [15:django.request:224] [WARNING] Not Found: /static/admin/css/nav_sidebar.css
2025-07-18 12:13:14 [17:django.request:224] [WARNING] Not Found: /static/admin/css/dashboard.css
2025-07-18 12:13:14 [17:django.request:224] [WARNING] Not Found: /static/admin/css/responsive.css
2025-07-18 12:13:14 [16:django.request:224] [WARNING] Not Found: /static/admin/js/nav_sidebar.js
2025-07-18 12:13:17 [16:django.request:224] [WARNING] Not Found: /static/admin/css/base.css
2025-07-18 12:13:17 [15:django.request:224] [WARNING] Not Found: /static/admin/css/responsive.css
2025-07-18 12:13:17 [15:django.request:224] [WARNING] Not Found: /static/admin/css/nav_sidebar.css
2025-07-18 12:13:17 [15:django.request:224] [WARNING] Not Found: /static/admin/css/dashboard.css
2025-07-18 12:13:17 [14:django.request:224] [WARNING] Not Found: /static/admin/js/nav_sidebar.js
2025-07-18 12:13:18 [15:django.request:224] [WARNING] Not Found: /favicon.ico
2025-07-18 12:13:20 [15:django.request:224] [WARNING] Not Found: /static/admin/css/base.css
2025-07-18 12:13:20 [15:django.request:224] [WARNING] Not Found: /static/admin/css/nav_sidebar.css
2025-07-18 12:13:20 [15:django.request:224] [WARNING] Not Found: /static/admin/css/dashboard.css
2025-07-18 12:13:20 [15:django.request:224] [WARNING] Not Found: /static/admin/css/responsive.css
2025-07-18 12:13:20 [15:django.request:224] [WARNING] Not Found: /static/admin/js/nav_sidebar.js
2025-07-18 12:15:41 [17:django.request:224] [WARNING] Not Found: /static/admin/css/base.css
2025-07-18 12:15:41 [15:django.request:224] [WARNING] Not Found: /static/admin/css/nav_sidebar.css
2025-07-18 12:15:41 [17:django.request:224] [WARNING] Not Found: /static/admin/css/dashboard.css
2025-07-18 12:15:41 [15:django.request:224] [WARNING] Not Found: /static/admin/js/nav_sidebar.js
2025-07-18 12:15:41 [15:django.request:224] [WARNING] Not Found: /static/admin/css/responsive.css
2025-07-18 12:15:46 [15:django.request:224] [WARNING] Not Found: /static/admin/css/base.css
2025-07-18 12:15:46 [17:django.request:224] [WARNING] Not Found: /static/admin/css/nav_sidebar.css
2025-07-18 12:15:46 [17:django.request:224] [WARNING] Not Found: /static/admin/css/dashboard.css
2025-07-18 12:15:46 [17:django.request:224] [WARNING] Not Found: /static/admin/css/responsive.css
2025-07-18 12:15:46 [15:django.request:224] [WARNING] Not Found: /static/admin/js/nav_sidebar.js
2025-07-18 12:15:50 [15:django.request:224] [WARNING] Not Found: /
2025-07-18 12:15:50 [15:django.request:224] [WARNING] Not Found: /static/img/logo.png
2025-07-18 12:15:54 [14:django.request:224] [WARNING] Not Found: /static/admin/css/base.css
2025-07-18 12:15:54 [14:django.request:224] [WARNING] Not Found: /static/admin/css/nav_sidebar.css
2025-07-18 12:15:54 [14:django.request:224] [WARNING] Not Found: /static/admin/js/nav_sidebar.js
2025-07-18 12:15:54 [14:django.request:224] [WARNING] Not Found: /static/admin/css/responsive.css
2025-07-18 12:15:56 [14:django.request:224] [WARNING] Not Found: /static/admin/css/base.css
2025-07-18 12:15:56 [14:django.request:224] [WARNING] Not Found: /static/admin/css/nav_sidebar.css
2025-07-18 12:15:56 [15:django.request:224] [WARNING] Not Found: /static/admin/css/responsive.css
2025-07-18 12:15:56 [14:django.request:224] [WARNING] Not Found: /static/admin/css/login.css
2025-07-18 12:15:56 [14:django.request:224] [WARNING] Not Found: /static/admin/js/nav_sidebar.js
2025-07-18 04:17:46 [6:gunicorn.error:277] [INFO] Handling signal: term
2025-07-18 12:17:46 [15:gunicorn.error:277] [INFO] Worker exiting (pid: 15)
2025-07-18 12:17:47 [16:gunicorn.error:277] [INFO] Worker exiting (pid: 16)
2025-07-18 12:17:47 [14:gunicorn.error:277] [INFO] Worker exiting (pid: 14)
2025-07-18 12:17:47 [17:gunicorn.error:277] [INFO] Worker exiting (pid: 17)
2025-07-18 04:17:48 [6:gunicorn.error:277] [INFO] Shutting down: Master
2025-07-18 04:17:51 [7:gunicorn.error:277] [INFO] Starting gunicorn 23.0.0
2025-07-18 04:17:51 [7:gunicorn.error:277] [INFO] Listening at: http://0.0.0.0:8000,unix:/tmp/steambase_server.sock (7)
2025-07-18 04:17:51 [7:gunicorn.error:277] [INFO] Using worker: gevent
2025-07-18 04:17:51 [14:gunicorn.error:277] [INFO] Booting worker with pid: 14
2025-07-18 04:17:51 [15:gunicorn.error:277] [INFO] Booting worker with pid: 15
2025-07-18 04:17:51 [16:gunicorn.error:277] [INFO] Booting worker with pid: 16
2025-07-18 04:17:52 [17:gunicorn.error:277] [INFO] Booting worker with pid: 17
2025-07-18 12:18:36 [16:django.request:224] [WARNING] Not Found: /static/admin/css/base.css
2025-07-18 12:19:46 [16:django.request:224] [WARNING] Not Found: /static/admin/css/base.css
2025-07-18 12:19:46 [16:django.request:224] [WARNING] Not Found: /static/admin/css/nav_sidebar.css
2025-07-18 12:19:46 [16:django.request:224] [WARNING] Not Found: /static/admin/js/nav_sidebar.js
2025-07-18 12:19:47 [14:django.request:224] [WARNING] Not Found: /static/admin/css/login.css
2025-07-18 12:19:47 [17:django.request:224] [WARNING] Not Found: /static/admin/css/responsive.css
2025-07-18 12:19:57 [14:django.request:224] [WARNING] Not Found: /static/admin/css/base.css
2025-07-18 12:19:57 [17:django.request:224] [WARNING] Not Found: /static/admin/css/nav_sidebar.css
2025-07-18 12:19:57 [17:django.request:224] [WARNING] Not Found: /static/admin/css/responsive.css
2025-07-18 12:19:57 [17:django.request:224] [WARNING] Not Found: /static/admin/css/dashboard.css
2025-07-18 12:19:57 [14:django.request:224] [WARNING] Not Found: /static/admin/js/nav_sidebar.js
2025-07-18 12:20:52 [16:django.request:224] [WARNING] Not Found: /
2025-07-18 04:22:51 [7:gunicorn.error:277] [INFO] Handling signal: term
2025-07-18 12:22:52 [14:gunicorn.error:277] [INFO] Worker exiting (pid: 14)
2025-07-18 12:22:52 [16:gunicorn.error:277] [INFO] Worker exiting (pid: 16)
2025-07-18 12:22:52 [15:gunicorn.error:277] [INFO] Worker exiting (pid: 15)
2025-07-18 12:22:52 [17:gunicorn.error:277] [INFO] Worker exiting (pid: 17)
2025-07-18 04:22:53 [7:gunicorn.error:277] [INFO] Shutting down: Master
2025-07-18 04:22:57 [7:gunicorn.error:277] [INFO] Starting gunicorn 23.0.0
2025-07-18 04:22:57 [7:gunicorn.error:277] [INFO] Listening at: http://0.0.0.0:8000,unix:/tmp/steambase_server.sock (7)
2025-07-18 04:22:57 [7:gunicorn.error:277] [INFO] Using worker: gevent
2025-07-18 04:22:57 [14:gunicorn.error:277] [INFO] Booting worker with pid: 14
2025-07-18 04:22:57 [15:gunicorn.error:277] [INFO] Booting worker with pid: 15
2025-07-18 04:22:57 [16:gunicorn.error:277] [INFO] Booting worker with pid: 16
2025-07-18 04:22:57 [17:gunicorn.error:277] [INFO] Booting worker with pid: 17
2025-07-18 12:23:36 [16:django.request:224] [WARNING] Not Found: /static/admin/css/base.css
2025-07-18 12:24:40 [16:django.request:224] [WARNING] Not Found: /static/admin/css/base.css
2025-07-18 12:24:40 [15:django.request:224] [WARNING] Not Found: /static/admin/css/nav_sidebar.css
2025-07-18 12:24:40 [16:django.request:224] [WARNING] Not Found: /static/admin/css/dashboard.css
2025-07-18 12:24:40 [16:django.request:224] [WARNING] Not Found: /static/admin/js/nav_sidebar.js
2025-07-18 12:24:40 [15:django.request:224] [WARNING] Not Found: /static/admin/css/responsive.css
2025-07-18 12:24:57 [16:django.request:224] [WARNING] Not Found: /static/admin/css/base.css
2025-07-18 12:24:57 [16:django.request:224] [WARNING] Not Found: /static/admin/css/responsive.css
2025-07-18 12:24:57 [16:django.request:224] [WARNING] Not Found: /static/admin/js/nav_sidebar.js
2025-07-18 12:24:57 [16:django.request:224] [WARNING] Not Found: /static/admin/css/nav_sidebar.css
2025-07-18 12:24:58 [15:django.request:224] [WARNING] Not Found: /static/admin/css/dashboard.css
2025-07-18 12:24:59 [16:django.request:224] [WARNING] Not Found: /static/admin/css/responsive.css
2025-07-18 12:24:59 [16:django.request:224] [WARNING] Not Found: /static/admin/css/nav_sidebar.css
2025-07-18 12:24:59 [15:django.request:224] [WARNING] Not Found: /static/admin/css/base.css
2025-07-18 12:24:59 [16:django.request:224] [WARNING] Not Found: /static/admin/js/nav_sidebar.js
2025-07-18 12:25:02 [15:django.request:224] [WARNING] Not Found: /static/admin/css/base.css
2025-07-18 12:25:02 [15:django.request:224] [WARNING] Not Found: /static/admin/css/nav_sidebar.css
2025-07-18 12:25:03 [15:django.request:224] [WARNING] Not Found: /static/admin/css/login.css
2025-07-18 12:25:03 [14:django.request:224] [WARNING] Not Found: /static/admin/js/nav_sidebar.js
2025-07-18 12:25:03 [17:django.request:224] [WARNING] Not Found: /static/admin/css/responsive.css
2025-07-18 12:25:03 [17:django.request:224] [WARNING] Not Found: /favicon.ico
2025-07-18 12:25:06 [16:django.request:224] [WARNING] Not Found: /static/admin/css/base.css
2025-07-18 12:25:06 [17:django.request:224] [WARNING] Not Found: /static/admin/css/nav_sidebar.css
2025-07-18 12:25:06 [15:django.request:224] [WARNING] Not Found: /static/admin/css/login.css
2025-07-18 12:25:06 [17:django.request:224] [WARNING] Not Found: /static/admin/js/nav_sidebar.js
2025-07-18 12:25:06 [16:django.request:224] [WARNING] Not Found: /static/admin/css/responsive.css
2025-07-18 04:28:35 [7:gunicorn.error:277] [INFO] Handling signal: term
2025-07-18 12:28:36 [14:gunicorn.error:277] [INFO] Worker exiting (pid: 14)
2025-07-18 12:28:36 [17:gunicorn.error:277] [INFO] Worker exiting (pid: 17)
2025-07-18 12:28:36 [15:gunicorn.error:277] [INFO] Worker exiting (pid: 15)
2025-07-18 12:28:36 [16:gunicorn.error:277] [INFO] Worker exiting (pid: 16)
2025-07-18 04:28:38 [7:gunicorn.error:277] [INFO] Shutting down: Master
2025-07-18 04:28:42 [6:gunicorn.error:277] [INFO] Starting gunicorn 23.0.0
2025-07-18 04:28:42 [6:gunicorn.error:277] [INFO] Listening at: http://0.0.0.0:8000,unix:/tmp/steambase_server.sock (6)
2025-07-18 04:28:42 [6:gunicorn.error:277] [INFO] Using worker: gevent
2025-07-18 04:28:42 [14:gunicorn.error:277] [INFO] Booting worker with pid: 14
2025-07-18 04:28:42 [15:gunicorn.error:277] [INFO] Booting worker with pid: 15
2025-07-18 04:28:42 [16:gunicorn.error:277] [INFO] Booting worker with pid: 16
2025-07-18 04:28:43 [17:gunicorn.error:277] [INFO] Booting worker with pid: 17
2025-07-18 13:27:50 [15:django.request:224] [WARNING] Not Found: /
2025-07-18 13:28:40 [16:django.request:224] [WARNING] Not Found: /
2025-07-18 13:29:13 [16:django.request:224] [WARNING] Not Found: /
2025-07-18 13:35:10 [16:django.request:224] [WARNING] Not Found: /
2025-07-18 13:38:18 [16:django.request:224] [WARNING] Not Found: /
2025-07-18 13:39:57 [16:django.request:224] [WARNING] Not Found: /
2025-07-18 14:19:17 [16:django.request:224] [WARNING] Not Found: /
2025-07-18 14:23:11 [16:django.request:224] [WARNING] Not Found: /api
2025-07-18 15:14:54 [16:django.request:224] [WARNING] Not Found: /api/box/case/list/
2025-07-18 15:15:15 [14:django.request:224] [WARNING] Not Found: /api/box/case/list/
2025-07-18 15:15:15 [15:django.request:224] [WARNING] Not Found: /api/box/case/list/
2025-07-18 15:25:22 [15:django.request:224] [WARNING] Not Found: /
2025-07-18 15:25:48 [15:django.request:224] [WARNING] Not Found: /
2025-07-18 15:25:54 [15:django.request:224] [WARNING] Not Found: /
2025-07-18 07:34:44 [6:gunicorn.error:277] [INFO] Handling signal: term
2025-07-18 15:34:45 [14:gunicorn.error:277] [INFO] Worker exiting (pid: 14)
2025-07-18 15:34:45 [15:gunicorn.error:277] [INFO] Worker exiting (pid: 15)
2025-07-18 15:34:45 [16:gunicorn.error:277] [INFO] Worker exiting (pid: 16)
2025-07-18 15:34:45 [17:gunicorn.error:277] [INFO] Worker exiting (pid: 17)
2025-07-18 07:34:46 [6:gunicorn.error:277] [INFO] Shutting down: Master
2025-07-18 07:34:48 [7:gunicorn.error:277] [INFO] Starting gunicorn 23.0.0
2025-07-18 07:34:48 [7:gunicorn.error:277] [INFO] Listening at: http://0.0.0.0:8000,unix:/tmp/steambase_server.sock (7)
2025-07-18 07:34:48 [7:gunicorn.error:277] [INFO] Using worker: gevent
2025-07-18 07:34:48 [15:gunicorn.error:277] [INFO] Booting worker with pid: 15
2025-07-18 07:34:48 [16:gunicorn.error:277] [INFO] Booting worker with pid: 16
2025-07-18 07:34:48 [17:gunicorn.error:277] [INFO] Booting worker with pid: 17
2025-07-18 07:34:48 [18:gunicorn.error:277] [INFO] Booting worker with pid: 18
2025-07-18 07:44:07 [7:gunicorn.error:277] [INFO] Handling signal: term
2025-07-18 15:44:07 [15:gunicorn.error:277] [INFO] Worker exiting (pid: 15)
2025-07-18 15:44:07 [17:gunicorn.error:277] [INFO] Worker exiting (pid: 17)
2025-07-18 15:44:07 [18:gunicorn.error:277] [INFO] Worker exiting (pid: 18)
2025-07-18 15:44:07 [16:gunicorn.error:277] [INFO] Worker exiting (pid: 16)
2025-07-18 07:44:08 [7:gunicorn.error:277] [INFO] Shutting down: Master
2025-07-18 07:44:10 [7:gunicorn.error:277] [INFO] Starting gunicorn 23.0.0
2025-07-18 07:44:10 [7:gunicorn.error:277] [INFO] Listening at: http://0.0.0.0:8000,unix:/tmp/steambase_server.sock (7)
2025-07-18 07:44:10 [7:gunicorn.error:277] [INFO] Using worker: gevent
2025-07-18 07:44:10 [15:gunicorn.error:277] [INFO] Booting worker with pid: 15
2025-07-18 07:44:10 [16:gunicorn.error:277] [INFO] Booting worker with pid: 16
2025-07-18 07:44:10 [17:gunicorn.error:277] [INFO] Booting worker with pid: 17
2025-07-18 07:44:10 [18:gunicorn.error:277] [INFO] Booting worker with pid: 18
2025-07-18 15:46:25 [15:django.request:224] [WARNING] Not Found: /
2025-07-18 15:46:34 [17:django.request:224] [WARNING] Not Found: /api
2025-07-18 15:47:53 [18:django.request:224] [WARNING] Not Found: /api/box/case/list/
2025-07-18 15:48:04 [15:django.request:224] [WARNING] Not Found: /api/box/case/list/
2025-07-18 15:48:22 [18:django.request:224] [WARNING] Not Found: /api/box/case/list/
2025-07-18 15:48:26 [18:django.request:224] [WARNING] Not Found: /api/box/caselist/
2025-07-18 15:50:53 [15:django.request:224] [WARNING] Not Found: /api
2025-07-18 15:52:41 [17:django.request:224] [WARNING] Not Found: /api
2025-07-18 07:52:50 [7:gunicorn.error:277] [INFO] Handling signal: term
2025-07-18 15:52:51 [18:gunicorn.error:277] [INFO] Worker exiting (pid: 18)
2025-07-18 15:52:51 [16:gunicorn.error:277] [INFO] Worker exiting (pid: 16)
2025-07-18 15:52:51 [17:gunicorn.error:277] [INFO] Worker exiting (pid: 17)
2025-07-18 15:52:51 [15:gunicorn.error:277] [INFO] Worker exiting (pid: 15)
2025-07-18 07:52:52 [7:gunicorn.error:277] [INFO] Shutting down: Master
2025-07-18 07:52:55 [7:gunicorn.error:277] [INFO] Starting gunicorn 23.0.0
2025-07-18 07:52:55 [7:gunicorn.error:277] [INFO] Listening at: http://0.0.0.0:8000,unix:/tmp/steambase_server.sock (7)
2025-07-18 07:52:55 [7:gunicorn.error:277] [INFO] Using worker: gevent
2025-07-18 07:52:55 [14:gunicorn.error:277] [INFO] Booting worker with pid: 14
2025-07-18 07:52:55 [15:gunicorn.error:277] [INFO] Booting worker with pid: 15
2025-07-18 07:52:55 [16:gunicorn.error:277] [INFO] Booting worker with pid: 16
2025-07-18 07:52:55 [18:gunicorn.error:277] [INFO] Booting worker with pid: 18
2025-07-18 15:53:29 [18:django.request:224] [WARNING] Not Found: /api/box/case/list/
2025-07-18 15:53:48 [18:django.request:224] [WARNING] Not Found: /api/box/case/list/
2025-07-18 15:54:37 [16:django.request:224] [WARNING] Not Found: /api/box/case/list/
2025-07-18 08:13:46 [7:gunicorn.error:277] [INFO] Handling signal: term
2025-07-18 16:13:46 [16:gunicorn.error:277] [INFO] Worker exiting (pid: 16)
2025-07-18 16:13:46 [14:gunicorn.error:277] [INFO] Worker exiting (pid: 14)
2025-07-18 16:13:46 [15:gunicorn.error:277] [INFO] Worker exiting (pid: 15)
2025-07-18 16:13:47 [18:gunicorn.error:277] [INFO] Worker exiting (pid: 18)
2025-07-18 08:13:48 [7:gunicorn.error:277] [INFO] Shutting down: Master
2025-07-18 08:13:50 [7:gunicorn.error:277] [INFO] Starting gunicorn 23.0.0
2025-07-18 08:13:50 [7:gunicorn.error:277] [INFO] Listening at: http://0.0.0.0:8000,unix:/tmp/steambase_server.sock (7)
2025-07-18 08:13:50 [7:gunicorn.error:277] [INFO] Using worker: gevent
2025-07-18 08:13:50 [15:gunicorn.error:277] [INFO] Booting worker with pid: 15
2025-07-18 08:13:50 [16:gunicorn.error:277] [INFO] Booting worker with pid: 16
2025-07-18 08:13:50 [17:gunicorn.error:277] [INFO] Booting worker with pid: 17
2025-07-18 08:13:50 [18:gunicorn.error:277] [INFO] Booting worker with pid: 18
2025-07-18 08:13:59 [7:gunicorn.error:277] [INFO] Handling signal: term
2025-07-18 16:14:03 [17:gunicorn.error:277] [INFO] Worker exiting (pid: 17)
2025-07-18 16:14:03 [15:gunicorn.error:277] [INFO] Worker exiting (pid: 15)
2025-07-18 16:14:03 [16:gunicorn.error:277] [INFO] Worker exiting (pid: 16)
2025-07-18 16:14:05 [18:gunicorn.error:277] [INFO] Worker exiting (pid: 18)
2025-07-18 08:14:05 [7:gunicorn.error:277] [INFO] Shutting down: Master
2025-07-18 08:14:07 [7:gunicorn.error:277] [INFO] Starting gunicorn 23.0.0
2025-07-18 08:14:07 [7:gunicorn.error:277] [INFO] Listening at: http://0.0.0.0:8000,unix:/tmp/steambase_server.sock (7)
2025-07-18 08:14:07 [7:gunicorn.error:277] [INFO] Using worker: gevent
2025-07-18 08:14:07 [14:gunicorn.error:277] [INFO] Booting worker with pid: 14
2025-07-18 08:14:07 [15:gunicorn.error:277] [INFO] Booting worker with pid: 15
2025-07-18 08:14:07 [17:gunicorn.error:277] [INFO] Booting worker with pid: 17
2025-07-18 08:14:07 [18:gunicorn.error:277] [INFO] Booting worker with pid: 18
2025-07-18 16:15:55 [15:box.views:968] [ERROR] Cannot query "普通箱子": Must be "CaseType" instance.
Traceback (most recent call last):
  File "/app/box/views.py", line 960, in get
    code, resp = search_case(q, query, fields)
  File "/app/box/business.py", line 1125, in search_case
    cases = Case.objects.filter(
  File "/usr/local/lib/python3.8/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 941, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "/usr/local/lib/python3.8/site-packages/modeltranslation/manager.py", line 325, in _filter_or_exclude
    return super()._filter_or_exclude(negate, args, kwargs)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 961, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 968, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/query.py", line 1416, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/query.py", line 1435, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/query.py", line 1343, in build_filter
    self.check_related_objects(join_info.final_field, value, join_info.opts)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/query.py", line 1170, in check_related_objects
    self.check_query_object_type(value, opts, field)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/query.py", line 1151, in check_query_object_type
    raise ValueError(
ValueError: Cannot query "普通箱子": Must be "CaseType" instance.
2025-07-18 16:32:53 [18:box.views:986] [ERROR] local variable 'rarity_name_en' referenced before assignment
Traceback (most recent call last):
  File "/app/box/views.py", line 980, in get
    code, resp = get_box_skin_list(case_key, fields)
  File "/app/box/business.py", line 1261, in get_box_skin_list
    'rarity_id': rarity_id,
UnboundLocalError: local variable 'rarity_name_en' referenced before assignment
2025-07-18 08:51:15 [7:gunicorn.error:277] [INFO] Handling signal: term
2025-07-18 16:51:15 [17:gunicorn.error:277] [INFO] Worker exiting (pid: 17)
2025-07-18 16:51:15 [15:gunicorn.error:277] [INFO] Worker exiting (pid: 15)
2025-07-18 16:51:15 [14:gunicorn.error:277] [INFO] Worker exiting (pid: 14)
2025-07-18 16:51:16 [18:gunicorn.error:277] [INFO] Worker exiting (pid: 18)
2025-07-18 08:51:16 [7:gunicorn.error:277] [INFO] Shutting down: Master
2025-07-18 08:51:19 [7:gunicorn.error:277] [INFO] Starting gunicorn 23.0.0
2025-07-18 08:51:19 [7:gunicorn.error:277] [INFO] Listening at: http://0.0.0.0:8000,unix:/tmp/steambase_server.sock (7)
2025-07-18 08:51:19 [7:gunicorn.error:277] [INFO] Using worker: gevent
2025-07-18 08:51:19 [15:gunicorn.error:277] [INFO] Booting worker with pid: 15
2025-07-18 08:51:19 [16:gunicorn.error:277] [INFO] Booting worker with pid: 16
2025-07-18 08:51:19 [17:gunicorn.error:277] [INFO] Booting worker with pid: 17
2025-07-18 08:51:19 [18:gunicorn.error:277] [INFO] Booting worker with pid: 18
2025-07-18 09:08:42 [7:gunicorn.error:277] [INFO] Handling signal: term
2025-07-18 17:08:42 [16:gunicorn.error:277] [INFO] Worker exiting (pid: 16)
2025-07-18 17:08:42 [18:gunicorn.error:277] [INFO] Worker exiting (pid: 18)
2025-07-18 17:08:42 [17:gunicorn.error:277] [INFO] Worker exiting (pid: 17)
2025-07-18 17:08:43 [15:gunicorn.error:277] [INFO] Worker exiting (pid: 15)
2025-07-18 09:08:43 [7:gunicorn.error:277] [INFO] Shutting down: Master
2025-07-18 09:08:45 [7:gunicorn.error:277] [INFO] Starting gunicorn 23.0.0
2025-07-18 09:08:45 [7:gunicorn.error:277] [INFO] Listening at: http://0.0.0.0:8000,unix:/tmp/steambase_server.sock (7)
2025-07-18 09:08:45 [7:gunicorn.error:277] [INFO] Using worker: gevent
2025-07-18 09:08:45 [14:gunicorn.error:277] [INFO] Booting worker with pid: 14
2025-07-18 09:08:45 [15:gunicorn.error:277] [INFO] Booting worker with pid: 15
2025-07-18 09:08:45 [16:gunicorn.error:277] [INFO] Booting worker with pid: 16
2025-07-18 09:08:45 [18:gunicorn.error:277] [INFO] Booting worker with pid: 18
2025-07-18 09:16:51 [7:gunicorn.error:277] [INFO] Handling signal: term
2025-07-18 17:16:51 [16:gunicorn.error:277] [INFO] Worker exiting (pid: 16)
2025-07-18 17:16:51 [18:gunicorn.error:277] [INFO] Worker exiting (pid: 18)
2025-07-18 17:16:51 [14:gunicorn.error:277] [INFO] Worker exiting (pid: 14)
2025-07-18 17:16:51 [15:gunicorn.error:277] [INFO] Worker exiting (pid: 15)
2025-07-18 09:16:52 [7:gunicorn.error:277] [INFO] Shutting down: Master
2025-07-18 09:16:54 [7:gunicorn.error:277] [INFO] Starting gunicorn 23.0.0
2025-07-18 09:16:54 [7:gunicorn.error:277] [INFO] Listening at: http://0.0.0.0:8000,unix:/tmp/steambase_server.sock (7)
2025-07-18 09:16:54 [7:gunicorn.error:277] [INFO] Using worker: gevent
2025-07-18 09:16:54 [15:gunicorn.error:277] [INFO] Booting worker with pid: 15
2025-07-18 09:16:54 [16:gunicorn.error:277] [INFO] Booting worker with pid: 16
2025-07-18 09:16:54 [17:gunicorn.error:277] [INFO] Booting worker with pid: 17
2025-07-18 09:16:54 [18:gunicorn.error:277] [INFO] Booting worker with pid: 18
2025-07-18 09:23:31 [7:gunicorn.error:277] [INFO] Handling signal: term
2025-07-18 17:23:31 [17:gunicorn.error:277] [INFO] Worker exiting (pid: 17)
2025-07-18 17:23:32 [16:gunicorn.error:277] [INFO] Worker exiting (pid: 16)
2025-07-18 17:23:32 [18:gunicorn.error:277] [INFO] Worker exiting (pid: 18)
2025-07-18 17:23:32 [15:gunicorn.error:277] [INFO] Worker exiting (pid: 15)
2025-07-18 09:23:33 [7:gunicorn.error:277] [INFO] Shutting down: Master
2025-07-18 09:23:35 [7:gunicorn.error:277] [INFO] Starting gunicorn 23.0.0
2025-07-18 09:23:35 [7:gunicorn.error:277] [INFO] Listening at: http://0.0.0.0:8000,unix:/tmp/steambase_server.sock (7)
2025-07-18 09:23:35 [7:gunicorn.error:277] [INFO] Using worker: gevent
2025-07-18 09:23:35 [12:gunicorn.error:277] [INFO] Booting worker with pid: 12
2025-07-18 09:23:35 [13:gunicorn.error:277] [INFO] Booting worker with pid: 13
2025-07-18 09:23:35 [15:gunicorn.error:277] [INFO] Booting worker with pid: 15
2025-07-18 09:23:35 [16:gunicorn.error:277] [INFO] Booting worker with pid: 16
2025-07-18 09:26:50 [7:gunicorn.error:277] [INFO] Handling signal: term
2025-07-18 17:26:50 [13:gunicorn.error:277] [INFO] Worker exiting (pid: 13)
2025-07-18 17:26:50 [15:gunicorn.error:277] [INFO] Worker exiting (pid: 15)
2025-07-18 17:26:50 [12:gunicorn.error:277] [INFO] Worker exiting (pid: 12)
2025-07-18 17:26:51 [16:gunicorn.error:277] [INFO] Worker exiting (pid: 16)
2025-07-18 09:26:51 [7:gunicorn.error:277] [INFO] Shutting down: Master
2025-07-18 09:26:53 [7:gunicorn.error:277] [INFO] Starting gunicorn 23.0.0
2025-07-18 09:26:53 [7:gunicorn.error:277] [INFO] Listening at: http://0.0.0.0:8000,unix:/tmp/steambase_server.sock (7)
2025-07-18 09:26:53 [7:gunicorn.error:277] [INFO] Using worker: gevent
2025-07-18 09:26:53 [15:gunicorn.error:277] [INFO] Booting worker with pid: 15
2025-07-18 09:26:53 [16:gunicorn.error:277] [INFO] Booting worker with pid: 16
2025-07-18 09:26:53 [17:gunicorn.error:277] [INFO] Booting worker with pid: 17
2025-07-18 09:26:53 [18:gunicorn.error:277] [INFO] Booting worker with pid: 18
