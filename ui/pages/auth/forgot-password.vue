<template>
  <div class="auth-page min-h-screen flex flex-col justify-center items-center py-8 px-4 relative overflow-hidden">
    <!-- 背景装饰元素 -->
    <div class="fixed inset-0 -z-10">
      <div class="absolute top-0 -left-32 w-96 h-96 rounded-full blur-3xl bg-primary/5 animate-pulse-slow"></div>
      <div class="absolute bottom-0 -right-32 w-96 h-96 rounded-full blur-3xl bg-secondary/5 animate-pulse-slow"></div>
      <div class="absolute top-20 right-20 w-32 h-32 rounded-full blur-2xl bg-accent/5"></div>
      <div class="absolute bottom-20 left-20 w-48 h-48 rounded-full blur-2xl bg-primary/5"></div>
      <div class="absolute inset-0 bg-grid opacity-10"></div>
      <div class="absolute inset-0 bg-noise opacity-30 mix-blend-soft-light"></div>
    </div>

    <!-- 主内容容器 -->
    <div class="auth-form p-8 rounded-2xl shadow-2xl w-full max-w-md mx-auto bg-gray-800/40 backdrop-blur-md border border-gray-700/30 relative">
      <!-- Logo和标题 -->
      <div class="text-center mb-8">
        <div class="w-16 h-16 mx-auto mb-4 bg-gradient-primary rounded-2xl flex items-center justify-center">
          <i class="i-ph-lock-key-bold text-2xl text-white"></i>
        </div>
        <h1 class="text-3xl font-bold mb-2 text-transparent bg-clip-text bg-gradient-primary">
          {{ t('auth.forgot_password') }}
        </h1>
        <p class="text-white/60 text-sm">
          {{ t('auth.forgotPassword.description') }}
        </p>
      </div>

      <!-- 步骤指示器 -->
      <div class="mb-8">
        <div class="flex items-center justify-center space-x-4">
          <div v-for="(step, index) in steps" :key="index" class="flex items-center">
            <div :class="[
              'w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-300',
              currentStep >= index ? 'bg-gradient-primary text-white' : 'bg-gray-700/50 text-gray-400'
            ]">
              <i v-if="currentStep > index" class="i-ph-check-bold"></i>
              <span v-else>{{ index + 1 }}</span>
            </div>
            <div v-if="index < steps.length - 1" :class="[
              'w-8 h-0.5 transition-all duration-300',
              currentStep > index ? 'bg-gradient-primary' : 'bg-gray-700/50'
            ]"></div>
          </div>
        </div>
        <div class="flex justify-center mt-2">
          <span class="text-xs text-white/60">{{ steps[currentStep] }}</span>
        </div>
      </div>

      <!-- 步骤1: 验证邮箱 -->
      <div v-if="currentStep === 0" class="space-y-6">
        <form @submit.prevent="verifyEmail" class="space-y-4">
          <!-- 邮箱输入 -->
          <div class="form-group">
            <label for="email" class="block mb-2 text-sm font-medium text-white/80">
              {{ t('auth.email') }}
            </label>
            <div class="flex items-stretch">
              <div class="relative flex-1">
                <i class="i-ph-envelope-simple absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50"></i>
                <input 
                  type="email" 
                  id="email" 
                  v-model="formData.email" 
                  :class="[
                    'w-full pl-10 pr-4 py-3 rounded-l-xl bg-gray-700/50 border transition-all duration-300',
                    'focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50',
                    'placeholder:text-gray-400',
                    emailError ? 'border-red-500' : 'border-gray-700/50 hover:border-gray-600/50'
                  ]" 
                  :placeholder="t('auth.email_placeholder')" 
                  @blur="validateEmail"
                  autocomplete="email"
                />
              </div>
              <button 
                type="button"
                @click="sendVerificationCode"
                :disabled="!isEmailValid || codeCountdown > 0 || isLoading"
                class="px-4 py-3 rounded-r-xl bg-gradient-primary text-white font-medium transition-all duration-300 hover:opacity-90 disabled:opacity-50 disabled:cursor-not-allowed whitespace-nowrap flex items-center justify-center min-w-[100px]"
              >
                <span v-if="isLoading" class="loading-spinner"></span>
                <span v-else-if="codeCountdown > 0">{{ codeCountdown }}s</span>
                <span v-else>{{ t('auth.send_code') }}</span>
              </button>
            </div>
            <p v-if="emailError" class="mt-2 text-sm text-red-400 flex items-center">
              <i class="i-ph-warning-circle mr-1"></i>
              {{ emailError }}
            </p>
          </div>

          <!-- 验证码输入 -->
          <div class="form-group">
            <label for="code" class="block mb-2 text-sm font-medium text-white/80">
              {{ t('auth.email_verification_code') }}
            </label>
            <div class="relative">
              <i class="i-ph-shield-check absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50"></i>
              <input 
                type="text" 
                id="code" 
                v-model="formData.code" 
                :class="[
                  'w-full pl-10 pr-4 py-3 rounded-xl bg-gray-700/50 border transition-all duration-300',
                  'focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50',
                  'placeholder:text-gray-400',
                  codeError ? 'border-red-500' : 'border-gray-700/50 hover:border-gray-600/50'
                ]" 
                :placeholder="t('auth.email_verification_code_placeholder')" 
                maxlength="6"
                @blur="validateCode"
                autocomplete="one-time-code"
              />
            </div>
            <p v-if="codeError" class="mt-2 text-sm text-red-400 flex items-center">
              <i class="i-ph-warning-circle mr-1"></i>
              {{ codeError }}
            </p>
          </div>

          <!-- 提交按钮 -->
          <button 
            type="submit"
            :disabled="isLoading || !isFormValid"
            class="w-full bg-gradient-primary hover:from-primary-light hover:to-secondary-light text-white font-medium py-3 px-4 rounded-xl transition-all duration-300 flex items-center justify-center transform hover:-translate-y-0.5 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
          >
            <span v-if="isLoading" class="loading-spinner mr-2"></span>
            <i v-else class="i-ph-arrow-right mr-2"></i>
            {{ isLoading ? t('auth.verifying') : t('auth.verify_proceed') }}
          </button>
        </form>
      </div>

      <!-- 步骤2: 设置新密码 -->
      <div v-else-if="currentStep === 1" class="space-y-6">
        <form @submit.prevent="resetPassword" class="space-y-4">
          <!-- 新密码输入 -->
          <div class="form-group">
            <label for="newPassword" class="block mb-2 text-sm font-medium text-white/80">
              {{ t('auth.new_password') }}
            </label>
            <div class="relative">
              <i class="i-ph-lock-simple absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50"></i>
              <input 
                :type="showPassword ? 'text' : 'password'" 
                id="newPassword" 
                v-model="formData.newPassword" 
                :class="[
                  'w-full pl-10 pr-12 py-3 rounded-xl bg-gray-700/50 border transition-all duration-300',
                  'focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50',
                  'placeholder:text-gray-400',
                  passwordError ? 'border-red-500' : 'border-gray-700/50 hover:border-gray-600/50'
                ]" 
                :placeholder="t('auth.new_password_placeholder')" 
                @input="validatePassword"
                autocomplete="new-password"
              />
              <button 
                type="button"
                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/50 hover:text-white transition-colors"
                @click="togglePasswordVisibility"
              >
                <i :class="showPassword ? 'i-ph-eye-slash' : 'i-ph-eye'"></i>
              </button>
            </div>
            <p v-if="passwordError" class="mt-2 text-sm text-red-400 flex items-center">
              <i class="i-ph-warning-circle mr-1"></i>
              {{ passwordError }}
            </p>

            <!-- 密码强度指示器 -->
            <div v-if="formData.newPassword && !passwordError" class="mt-3">
              <div class="flex items-center mb-2">
                <div class="h-2 flex-1 bg-gray-700/70 rounded-full overflow-hidden">
                  <div class="h-full rounded-full transition-all duration-300" :class="{
                    'w-1/3 bg-red-500': passwordStrength === 'weak',
                    'w-2/3 bg-yellow-500': passwordStrength === 'medium',
                    'w-full bg-green-500': passwordStrength === 'strong'
                  }"></div>
                </div>
                <span class="ml-3 text-xs font-medium" :class="{
                  'text-red-400': passwordStrength === 'weak',
                  'text-yellow-400': passwordStrength === 'medium',
                  'text-green-400': passwordStrength === 'strong'
                }">
                  {{ passwordStrengthText }}
                </span>
              </div>
              <p class="text-xs text-white/50">{{ t('auth.password_requirements') }}</p>
            </div>
          </div>

          <!-- 确认密码输入 -->
          <div class="form-group">
            <label for="confirmPassword" class="block mb-2 text-sm font-medium text-white/80">
              {{ t('auth.confirm_password') }}
            </label>
            <div class="relative">
              <i class="i-ph-lock-key absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50"></i>
              <input 
                :type="showPassword ? 'text' : 'password'" 
                id="confirmPassword" 
                v-model="formData.confirmPassword" 
                :class="[
                  'w-full pl-10 pr-4 py-3 rounded-xl bg-gray-700/50 border transition-all duration-300',
                  'focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50',
                  'placeholder:text-gray-400',
                  confirmPasswordError ? 'border-red-500' : 'border-gray-700/50 hover:border-gray-600/50'
                ]" 
                :placeholder="t('auth.confirm_password_placeholder')" 
                @blur="validateConfirmPassword"
                autocomplete="new-password"
              />
            </div>
            <p v-if="confirmPasswordError" class="mt-2 text-sm text-red-400 flex items-center">
              <i class="i-ph-warning-circle mr-1"></i>
              {{ confirmPasswordError }}
            </p>
          </div>

          <!-- 提交按钮 -->
          <button 
            type="submit"
            :disabled="isLoading || !isPasswordFormValid"
            class="w-full bg-gradient-primary hover:from-primary-light hover:to-secondary-light text-white font-medium py-3 px-4 rounded-xl transition-all duration-300 flex items-center justify-center transform hover:-translate-y-0.5 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
          >
            <span v-if="isLoading" class="loading-spinner mr-2"></span>
            <i v-else class="i-ph-check-bold mr-2"></i>
            {{ isLoading ? t('auth.resetting') : t('auth.reset_password') }}
          </button>
        </form>
      </div>

      <!-- 步骤3: 重置成功 -->
      <div v-else-if="currentStep === 2" class="text-center py-8">
        <div class="mb-6 text-green-400">
          <div class="w-20 h-20 mx-auto bg-green-500/20 rounded-full flex items-center justify-center mb-4">
            <i class="i-ph-check-circle-bold text-4xl"></i>
          </div>
        </div>
        <h2 class="text-2xl font-bold mb-3 text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-green-600">
          {{ t('auth.reset_success') }}
        </h2>
        <p class="mb-8 text-white/70 leading-relaxed">
          {{ t('auth.reset_success_message') }}
        </p>
        <NuxtLink 
          :to="localePath('/auth/login')"
          class="bg-gradient-primary hover:from-primary-light hover:to-secondary-light text-white font-medium py-3 px-6 rounded-xl transition-all duration-300 inline-flex items-center justify-center transform hover:-translate-y-0.5 shadow-lg"
        >
          <i class="i-ph-sign-in mr-2"></i>
          {{ t('auth.back_to_login') }}
        </NuxtLink>
      </div>

      <!-- 错误信息显示 -->
      <div v-if="authError" class="mt-4 p-4 bg-red-500/20 border border-red-500/50 rounded-xl">
        <p class="text-red-400 text-sm flex items-center">
          <i class="i-ph-warning-circle mr-2"></i>
          {{ authError }}
        </p>
      </div>

      <!-- 成功信息显示 -->
      <div v-if="successMessage" class="mt-4 p-4 bg-green-500/20 border border-green-500/50 rounded-xl">
        <p class="text-green-400 text-sm flex items-center">
          <i class="i-ph-check-circle mr-2"></i>
          {{ successMessage }}
        </p>
      </div>

      <!-- 底部链接 -->
      <div v-if="currentStep !== 2" class="mt-8 text-center space-y-3">
        <div class="h-px bg-gradient-to-r from-transparent via-gray-600/50 to-transparent"></div>
        <p class="text-white/60 text-sm">
          {{ t('auth.forgotPassword.subtitle') }}
          <NuxtLink 
            :to="localePath('/auth/login')" 
            class="text-primary hover:text-primary-light transition-colors font-medium ml-1"
          >
            {{ t('auth.login_here') }}
          </NuxtLink>
        </p>
        <p class="text-white/60 text-sm">
          {{ t('auth.register.subtitle') }}
          <NuxtLink 
            :to="localePath('/auth/register')" 
            class="text-primary hover:text-primary-light transition-colors font-medium ml-1"
          >
            {{ t('auth.register_now') }}
          </NuxtLink>
        </p>
      </div>

      <!-- 图形验证码弹窗 -->
      <SafeCaptchaDialog
        :show="showCaptchaDialog"
        :image="captcha?.captcha"
        :error="captchaError"
        :loading="isLoading"
        @close="showCaptchaDialog = false"
        @confirm="confirmCaptcha"
        @refresh="refreshCaptcha"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
// 显式导入组件
import SafeCaptchaDialog from '~/components/ui/SafeCaptchaDialog.vue'

import { useAuth } from '~/composables/useAuth'
import { validateEmail as isValidEmail, validatePassword as checkPassword, validateVerificationCode } from '~/utils/validation'

// 国际化
const { t, locale } = useI18n()
const localePath = useLocalePath()
const router = useRouter()

// 认证相关
const {
  captcha,
  isLoadingCaptcha,
  codeCountdown,
  isLoading,
  authError,
  getCaptcha,
  refreshCaptcha,
  getToken,
  sendEmailVerificationCode,
  sendEmailVerificationWithToken,
  resetPassword: resetPasswordAPI
} = useAuth()

// 表单数据
const formData = reactive({
  email: '',
  code: '',
  newPassword: '',
  confirmPassword: ''
})

// 状态管理
const currentStep = ref(0)
const showPassword = ref(false)
const showCaptchaDialog = ref(false)
const captchaInput = ref('')
const captchaError = ref('')
const successMessage = ref('')
const captchaToken = ref('')

// 验证错误
const emailError = ref('')
const codeError = ref('')
const passwordError = ref('')
const confirmPasswordError = ref('')

// 步骤
const steps = computed(() => [
  t('auth.verify_email'),
  t('auth.set_new_password'),
  t('auth.complete')
])

// 验证状态
const isEmailValid = computed(() => formData.email && !emailError.value)
const isCodeValid = computed(() => formData.code && !codeError.value)
const isFormValid = computed(() => isEmailValid.value && isCodeValid.value)

const isPasswordValid = computed(() => formData.newPassword && !passwordError.value)
const isConfirmPasswordValid = computed(() => formData.confirmPassword && !confirmPasswordError.value)
const isPasswordFormValid = computed(() => isPasswordValid.value && isConfirmPasswordValid.value)

// 密码强度
const passwordStrength = computed(() => {
  if (!formData.newPassword) return ''
  const result = checkPassword(formData.newPassword)
  return result.strength
})

const passwordStrengthText = computed(() => {
  switch (passwordStrength.value) {
    case 'weak': return t('auth.password_weak')
    case 'medium': return t('auth.password_medium')
    case 'strong': return t('auth.password_strong')
    default: return ''
  }
})

// 验证方法
const validateEmail = () => {
  if (!formData.email) {
    emailError.value = t('auth.email_required')
    return false
  }
  if (!isValidEmail(formData.email)) {
    emailError.value = t('auth.email_invalid')
    return false
  }
  emailError.value = ''
  return true
}

const validateCode = () => {
  if (!formData.code) {
    codeError.value = t('auth.code_required')
    return false
  }
  if (!validateVerificationCode(formData.code)) {
    codeError.value = t('auth.code_invalid')
    return false
  }
  codeError.value = ''
  return true
}

const validatePassword = () => {
  if (!formData.newPassword) {
    passwordError.value = t('auth.password_required')
    return false
  }
  const result = checkPassword(formData.newPassword)
  if (!result.valid) {
    passwordError.value = t('auth.password_too_short')
    return false
  }
  passwordError.value = ''
  
  // 如果确认密码已输入，重新验证
  if (formData.confirmPassword) {
    validateConfirmPassword()
  }
  return true
}

const validateConfirmPassword = () => {
  if (!formData.confirmPassword) {
    confirmPasswordError.value = t('auth.confirm_password_required')
    return false
  }
  if (formData.confirmPassword !== formData.newPassword) {
    confirmPasswordError.value = t('auth.password_mismatch')
    return false
  }
  confirmPasswordError.value = ''
  return true
}

// 切换密码可见性
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value
}

// 发送验证码
const sendVerificationCode = async () => {
  if (!validateEmail()) return
  
  captchaError.value = ''
  
  // 如果已经有token，直接发送
  if (captchaToken.value) {
    try {
      await sendEmailVerificationWithToken(formData.email, captchaToken.value, 2)
      successMessage.value = t('auth.code_sent')
    } catch (error: any) {
      // token失效，需要重新获取
      captchaToken.value = ''
      showCaptchaDialog.value = true
      await getCaptcha()
    }
    return
  }
  
  // 如果没有验证码图片，先获取
  if (!captcha.value) {
    await getCaptcha()
  }
  
  // 显示验证码弹窗
  showCaptchaDialog.value = true
}

// 核心发送验证码逻辑
const sendCodeCore = async () => {
  try {
    if (!captcha.value?.uuid) {
      throw new Error('验证码UUID缺失')
    }

    // 第一步：使用图形验证码获取token
    const token = await getToken(formData.email, captchaInput.value, captcha.value.uuid)
    
    if (!token) {
      throw new Error(t('auth.captcha_invalid'))
    }

    captchaToken.value = token

    // 第二步：使用token发送邮箱验证码
    await sendEmailVerificationWithToken(formData.email, token, 2) // type: 2 = 忘记密码
    
    successMessage.value = t('auth.code_sent')
    showCaptchaDialog.value = false
    captchaInput.value = ''
    
    // 刷新验证码
    await refreshCaptcha()
  } catch (error: any) {
    captchaError.value = error.message || t('auth.code_send_failed')
    captchaToken.value = '' // 清除token
    await refreshCaptcha()
  }
}

// 确认验证码并发送
const confirmCaptcha = async (captchaValue: string) => {
  if (!captchaValue) {
    captchaError.value = t('auth.captcha_required')
    return
  }
  
  captchaInput.value = captchaValue
  captchaError.value = ''
  await sendCodeCore()
}

// 验证邮箱步骤
const verifyEmail = async () => {
  const isEmailOk = validateEmail()
  const isCodeOk = validateCode()
  
  if (!isEmailOk || !isCodeOk) return
  
  // 模拟验证过程
  try {
    successMessage.value = ''
    currentStep.value = 1
    successMessage.value = t('auth.verify_proceed')
  } catch (error: any) {
    // 处理验证错误
  }
}

// 重置密码
const resetPassword = async () => {
  const isPasswordOk = validatePassword()
  const isConfirmOk = validateConfirmPassword()
  
  if (!isPasswordOk || !isConfirmOk) return
  
  try {
    successMessage.value = ''
    
    const result = await resetPasswordAPI({
      email: formData.email,
      verify_code: formData.code,
      newPassword: formData.newPassword
    })
    
    if (result.success) {
      currentStep.value = 2
      successMessage.value = t('auth.reset_success')
    } else {
      throw new Error(result.error || t('auth.reset_failed'))
    }
  } catch (error: any) {
    // authError 由 useAuth 管理
    console.error('重置密码失败:', error)
  }
}

// SEO 设置
useSeoMeta({
  title: computed(() => t('auth.forgotPassword.title')),
  description: computed(() => t('auth.forgotPassword.description')),
  keywords: computed(() => t('auth.forgotPassword.keywords'))
})

useHead({
  title: computed(() => t('auth.forgotPassword.title')),
  meta: [
    { name: 'description', content: computed(() => t('auth.forgotPassword.description')) },
    { name: 'keywords', content: computed(() => t('auth.forgotPassword.keywords')) }
  ]
})

// 路由保护
definePageMeta({
  middleware: 'guest'
})
</script>

<style lang="scss" scoped>
.auth-page {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.95) 100%);
}

.auth-form {
  backdrop-filter: blur(20px);
  background: rgba(31, 41, 55, 0.4);
  border: 1px solid rgba(75, 85, 99, 0.3);
}

/* 背景网格 */
.bg-grid {
  background-image: 
    linear-gradient(to right, rgba(255, 255, 255, 0.05) 0.0625rem, transparent 0.0625rem),
    linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 0.0625rem, transparent 0.0625rem);
  background-size: 1.25rem 1.25rem;
}

/* 慢脉冲动画 */
.animate-pulse-slow {
  animation: pulse-slow 8s ease-in-out infinite;
}

@keyframes pulse-slow {
  0%, 100% { opacity: 0.4; }
  50% { opacity: 0.7; }
}

/* 加载动画 */
.loading-spinner {
  display: inline-block;
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 表单过渡动画 */
.form-group {
  transition: all 0.3s ease;
}

/* 渐变按钮悬停效果 */
button:hover:not(:disabled) {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* 输入框聚焦效果 */
input:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}
</style> 