services:
  # Redis服务 - 用于缓存、消息队列和任务调度
  redis:
    image: redis:7-alpine
    container_name: cs<PERSON>kins-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./logs/redis:/var/log/redis
    command: redis-server --appendonly yes --loglevel notice
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Django Web应用
  web:
    build: ./server/
    container_name: csgoskins-web
    command: supervisord -c /app/supervisord.conf
    volumes: &common-volumes
      - ./server/:/app/
      - ./logs/:/app/logs/
      - ./server/static:/app/static
      - ./server/media:/app/media
    restart: always
    network_mode: "host"
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "5"
    depends_on:
      redis:
        condition: service_healthy
    environment:
      - DJANGO_SETTINGS_MODULE=steambase.settings
      - CELERY_BROKER_URL=redis://localhost:6379/0
      - CELERY_RESULT_BACKEND=redis://localhost:6379/0
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8000/api/health/ || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s



  # Celery Worker - 异步任务处理
  celery-worker:
    build: ./server/
    container_name: csgoskins-celery-worker
    command: celery -A steambase worker --loglevel=info --concurrency=4
    volumes: *common-volumes
    restart: always
    network_mode: "host"
    logging:
      driver: "json-file"
      options:
        max-size: "20m"
        max-file: "3"
    depends_on:
      redis:
        condition: service_healthy
      web:
        condition: service_healthy
    environment:
      - DJANGO_SETTINGS_MODULE=steambase.settings
      - CELERY_BROKER_URL=redis://localhost:6379/0
      - CELERY_RESULT_BACKEND=redis://localhost:6379/0
    healthcheck:
      test: ["CMD", "celery", "-A", "steambase", "inspect", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Celery Beat - 定时任务调度
  celery-beat:
    build: ./server/
    container_name: csgoskins-celery-beat
    command: celery -A steambase beat --loglevel=info --scheduler=django_celery_beat.schedulers:DatabaseScheduler
    volumes: *common-volumes
    restart: always
    network_mode: "host"
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    depends_on:
      redis:
        condition: service_healthy
      web:
        condition: service_healthy
      celery-worker:
        condition: service_healthy
    environment:
      - DJANGO_SETTINGS_MODULE=steambase.settings
      - CELERY_BROKER_URL=redis://localhost:6379/0
      - CELERY_RESULT_BACKEND=redis://localhost:6379/0

  # ThWorker兼容层 - 启动持续运行的任务
  thworker-compat:
    build: ./server/
    container_name: csgoskins-thworker-compat
    command: python manage.py setupworker
    volumes: *common-volumes
    restart: always
    network_mode: "host"
    logging:
      driver: "json-file"
      options:
        max-size: "20m"
        max-file: "3"
    depends_on:
      redis:
        condition: service_healthy
      web:
        condition: service_healthy
      celery-worker:
        condition: service_healthy
    environment:
      - DJANGO_SETTINGS_MODULE=steambase.settings

  # WebSocket服务
  websocket:
    build: ./deployment/node
    container_name: csgoskins-websocket
    command: node ws_server.js
    volumes:
      - ./logs/:/app/logs/
      - ./deployment/node/nodejs:/app
    restart: always
    network_mode: "host"
    depends_on:
      redis:
        condition: service_healthy
      web:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - REDIS_HOST=localhost
      - REDIS_PORT=6379
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:4000/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

# 数据卷定义
volumes:
  redis_data:
    driver: local

# 网络定义（使用host网络模式，此处仅作文档说明）
networks:
  default:
    driver: bridge