<template>
  <div class="min-h-screen relative">
    <!-- 背景装饰 -->
    <div class="fixed inset-0 -z-10">
      <div class="absolute top-0 -left-32 w-64 h-64 rounded-full blur-3xl bg-primary/10 animate-pulse-slow"></div>
      <div class="absolute bottom-0 -right-32 w-64 h-64 rounded-full blur-3xl bg-secondary/10 animate-pulse-slow"></div>
      <div class="absolute inset-0 bg-grid-pattern opacity-20"></div>
      <div class="absolute inset-0 bg-noise opacity-40 mix-blend-soft-light"></div>
    </div>

    <!-- PC端内容 -->
    <div class="container mx-auto px-4 py-8 relative z-10" v-if="!isMobile">
      <!-- 页面标题区域 -->
      <div class="profile-header bg-white/5 backdrop-blur-md border border-white/10 rounded-2xl mb-8 relative overflow-hidden">
        <div class="p-8">
          <div class="user-info-container flex items-center">
            <div class="user-avatar relative">
              <img :src="user.avatar" alt="用户头像" class="w-20 h-20 rounded-full border-4 border-primary/30" />
              <div class="user-level absolute -bottom-2 -right-2 bg-primary text-white text-xs font-bold px-2 py-1 rounded-full">
                Lv.{{ user.level }}
              </div>
            </div>
            <div class="user-details ml-6 flex-1">
              <h1 class="user-name text-2xl font-bold text-white mb-2">{{ user.name }}</h1>
              <div class="user-id text-white/60 text-sm mb-3">ID: {{ user.id }}</div>
              <div class="user-tags flex space-x-2">
                <span
                  v-for="(tag, index) in user.tags"
                  :key="index"
                  class="user-tag px-3 py-1 rounded-full text-xs font-medium"
                  :class="tag.type"
                >
                  {{ tag.name }}
                </span>
              </div>
            </div>
            <div class="user-actions flex space-x-3">
              <button class="edit-profile-btn" @click="editProfile">
                <i class="i-heroicons-pencil-square mr-1"></i>
                {{ $t('profile.edit_profile') }}
              </button>
              <button class="settings-btn" @click="goToSettings">
                <i class="i-heroicons-cog-6-tooth mr-1"></i>
                {{ $t('profile.settings') }}
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 页面内容 -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- 左侧 - 用户统计 -->
        <div class="col-span-1">
          <!-- 账户信息卡片 -->
          <div class="profile-card mb-6">
            <div class="flex items-center mb-4">
              <i class="i-heroicons-user-circle text-primary mr-2 text-xl"></i>
              <h2 class="card-title mb-0">{{ $t('profile.account_info') }}</h2>
            </div>
            <div class="account-info space-y-4">
              <div class="account-item flex items-center justify-between">
                <div class="account-label">{{ $t('profile.coins') }}</div>
                <div class="account-value text-primary flex items-center">
                  <i class="i-heroicons-currency-dollar text-amber-400 mr-1"></i>
                  {{ user.coins }}
                </div>
                <button class="small-action-btn">{{ $t('profile.recharge') }}</button>
              </div>
              <div class="account-item flex items-center justify-between">
                <div class="account-label">{{ $t('profile.experience') }}</div>
                <div class="account-value flex items-center">
                  <i class="i-heroicons-star text-blue-400 mr-1"></i>
                  <span>{{ user.exp }}/{{ user.nextLevelExp }}</span>
                  <div class="w-16 h-1.5 bg-gray-700/60 rounded-full overflow-hidden ml-2">
                    <div
                      class="h-full bg-gradient-primary rounded-full"
                      :style="{ width: `${(user.exp / user.nextLevelExp) * 100}%` }"
                    ></div>
                  </div>
                </div>
              </div>
              <div class="account-item flex items-center justify-between">
                <div class="account-label">{{ $t('profile.register_date') }}</div>
                <div class="account-value text-white/70">
                  {{ formatTime(user.registerDate) }}
                </div>
              </div>
              <div class="account-item flex items-center justify-between">
                <div class="account-label">{{ $t('profile.last_login') }}</div>
                <div class="account-value text-white/70">
                  {{ formatTime(user.lastLoginTime) }}
                </div>
              </div>
              <div class="account-item flex items-center justify-between">
                <div class="account-label">{{ $t('profile.last_login_ip') }}</div>
                <div class="account-value text-white/70">
                  {{ user.lastLoginIp }}
                </div>
              </div>
            </div>
          </div>

          <!-- 统计数据卡片 -->
          <div class="profile-card mb-6">
            <div class="flex items-center mb-4">
              <i class="i-heroicons-chart-bar text-primary mr-2 text-xl"></i>
              <h2 class="card-title mb-0">{{ $t('profile.statistics') }}</h2>
            </div>
            <div class="stats-grid grid grid-cols-2 gap-4">
              <div class="stat-item">
                <div class="stat-value text-transparent bg-clip-text bg-gradient-primary">
                  {{ user.stats.openCount }}
                </div>
                <div class="stat-label">{{ $t('profile.open_count') }}</div>
              </div>
              <div class="stat-item">
                <div class="stat-value text-transparent bg-clip-text bg-gradient-primary">
                  {{ user.stats.battleCount }}
                </div>
                <div class="stat-label">{{ $t('profile.battle_count') }}</div>
              </div>
              <div class="stat-item">
                <div class="stat-value text-transparent bg-clip-text bg-gradient-primary">
                  {{ user.stats.rareItems }}
                </div>
                <div class="stat-label">{{ $t('profile.rare_items') }}</div>
              </div>
              <div class="stat-item">
                <div class="stat-value text-transparent bg-clip-text bg-gradient-primary">
                  {{ user.stats.winRate }}%
                </div>
                <div class="stat-label">{{ $t('profile.win_rate') }}</div>
              </div>
            </div>
          </div>

          <!-- 徽章展示 -->
          <div class="profile-card">
            <div class="flex items-center justify-between mb-4">
              <div class="flex items-center">
                <i class="i-heroicons-trophy text-primary mr-2 text-xl"></i>
                <h2 class="card-title mb-0">{{ $t('profile.badges') }}</h2>
              </div>
              <a href="#" class="text-primary text-sm hover:text-primary-light transition-colors">
                {{ $t('profile.view_all') }}
              </a>
            </div>
            <div class="badges-grid grid grid-cols-3 gap-3">
              <div
                v-for="(badge, index) in user.badges.slice(0, 6)"
                :key="index"
                class="badge-item"
                :title="badge.name"
              >
                <img :src="badge.icon" :alt="badge.name" class="badge-icon w-full h-full object-cover" />
              </div>
              <div
                v-if="user.badges.length > 6"
                class="badge-item more-badges"
              >
                +{{ user.badges.length - 6 }}
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧 - 历史记录和皮肤展示 -->
        <div class="col-span-1 lg:col-span-2">
          <!-- 最近开箱记录 -->
          <div class="profile-card mb-6">
            <div class="flex items-center justify-between mb-4">
              <div class="flex items-center">
                <i class="i-heroicons-gift text-primary mr-2 text-xl"></i>
                <h2 class="card-title mb-0">{{ $t('profile.recent_openings') }}</h2>
              </div>
              <NuxtLink to="/profile/openings" class="text-primary text-sm hover:text-primary-light transition-colors">
                {{ $t('profile.view_all') }}
              </NuxtLink>
            </div>
            <div class="openings-grid grid grid-cols-1 md:grid-cols-2 gap-4">
              <div
                v-for="opening in recentOpenings"
                :key="opening.id"
                class="opening-item"
              >
                <div class="opening-image">
                  <img :src="opening.item.image" :alt="opening.item.name" />
                </div>
                <div class="opening-info">
                  <div class="opening-item-name">{{ opening.item.name }}</div>
                  <div class="opening-time">{{ formatTime(opening.time) }}</div>
                  <div class="opening-case">{{ opening.caseName }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 收藏的皮肤 -->
          <div class="profile-card">
            <div class="flex items-center justify-between mb-4">
              <div class="flex items-center">
                <i class="i-heroicons-heart text-primary mr-2 text-xl"></i>
                <h2 class="card-title mb-0">{{ $t('profile.favorite_skins') }}</h2>
              </div>
              <NuxtLink to="/profile/inventory" class="text-primary text-sm hover:text-primary-light transition-colors">
                {{ $t('profile.view_all') }}
              </NuxtLink>
            </div>
            <div class="skins-grid grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              <div
                v-for="skin in favoriteSkins"
                :key="skin.id"
                class="skin-item"
              >
                <div class="skin-image">
                  <img :src="skin.image" :alt="skin.name" />
                </div>
                <div class="skin-info">
                  <div class="skin-name">{{ skin.name }}</div>
                  <div class="skin-rarity" :class="skin.rarityClass">{{ skin.rarity }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 移动端内容 -->
    <div class="container mx-auto px-4 py-4 relative z-10" v-else>
      <!-- 页面标题区域 -->
      <div class="profile-header bg-white/5 backdrop-blur-md border border-white/10 rounded-xl mb-6 relative overflow-hidden">
        <div class="p-6">
          <div class="user-info-container text-center">
            <div class="user-avatar relative mx-auto mb-4">
              <img :src="user.avatar" alt="用户头像" class="w-16 h-16 rounded-full border-4 border-primary/30" />
              <div class="user-level absolute -bottom-2 -right-2 bg-primary text-white text-xs font-bold px-2 py-1 rounded-full">
                Lv.{{ user.level }}
              </div>
            </div>
            <div class="user-details">
              <h1 class="user-name text-xl font-bold text-white mb-2">{{ user.name }}</h1>
              <div class="user-id text-white/60 text-sm mb-3">ID: {{ user.id }}</div>
              <div class="user-tags flex justify-center space-x-2 mb-4">
                <span
                  v-for="(tag, index) in user.tags"
                  :key="index"
                  class="user-tag px-2 py-1 rounded-full text-xs font-medium"
                  :class="tag.type"
                >
                  {{ tag.name }}
                </span>
              </div>
              <div class="user-actions flex justify-center space-x-3">
                <button class="edit-profile-btn-mobile" @click="editProfile">
                  <i class="i-heroicons-pencil-square mr-1"></i>
                  {{ $t('profile.edit_profile') }}
                </button>
                <button class="settings-btn-mobile" @click="goToSettings">
                  <i class="i-heroicons-cog-6-tooth mr-1"></i>
                  {{ $t('profile.settings') }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 账户信息 -->
      <div class="profile-card mb-6">
        <div class="flex items-center mb-4">
          <i class="i-heroicons-user-circle text-primary mr-2 text-lg"></i>
          <h2 class="card-title mb-0 text-lg">{{ $t('profile.account_info') }}</h2>
        </div>
        <div class="account-info space-y-3">
          <div class="account-item flex items-center justify-between">
            <div class="account-label">{{ $t('profile.coins') }}</div>
            <div class="account-value text-primary flex items-center">
              <i class="i-heroicons-currency-dollar text-amber-400 mr-1"></i>
              {{ user.coins }}
            </div>
            <button class="small-action-btn-mobile">{{ $t('profile.recharge') }}</button>
          </div>
          <div class="account-item flex items-center justify-between">
            <div class="account-label">{{ $t('profile.experience') }}</div>
            <div class="account-value flex items-center">
              <i class="i-heroicons-star text-blue-400 mr-1"></i>
              <span class="text-sm">{{ user.exp }}/{{ user.nextLevelExp }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 统计数据 -->
      <div class="profile-card mb-6">
        <div class="flex items-center mb-4">
          <i class="i-heroicons-chart-bar text-primary mr-2 text-lg"></i>
          <h2 class="card-title mb-0 text-lg">{{ $t('profile.statistics') }}</h2>
        </div>
        <div class="stats-grid-mobile grid grid-cols-2 gap-3">
          <div class="stat-item-mobile">
            <div class="stat-value text-transparent bg-clip-text bg-gradient-primary">
              {{ user.stats.openCount }}
            </div>
            <div class="stat-label">{{ $t('profile.open_count') }}</div>
          </div>
          <div class="stat-item-mobile">
            <div class="stat-value text-transparent bg-clip-text bg-gradient-primary">
              {{ user.stats.battleCount }}
            </div>
            <div class="stat-label">{{ $t('profile.battle_count') }}</div>
          </div>
          <div class="stat-item-mobile">
            <div class="stat-value text-transparent bg-clip-text bg-gradient-primary">
              {{ user.stats.rareItems }}
            </div>
            <div class="stat-label">{{ $t('profile.rare_items') }}</div>
          </div>
          <div class="stat-item-mobile">
            <div class="stat-value text-transparent bg-clip-text bg-gradient-primary">
              {{ user.stats.winRate }}%
            </div>
            <div class="stat-label">{{ $t('profile.win_rate') }}</div>
          </div>
        </div>
      </div>

      <!-- 最近开箱记录 -->
      <div class="profile-card mb-6">
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center">
            <i class="i-heroicons-gift text-primary mr-2 text-lg"></i>
            <h2 class="card-title mb-0 text-lg">{{ $t('profile.recent_openings') }}</h2>
          </div>
          <NuxtLink to="/profile/openings" class="text-primary text-sm hover:text-primary-light transition-colors">
            {{ $t('profile.view_all') }}
          </NuxtLink>
        </div>
        <div class="openings-list space-y-3">
          <div
            v-for="opening in recentOpenings.slice(0, 3)"
            :key="opening.id"
            class="opening-item-mobile"
          >
            <div class="opening-image">
              <img :src="opening.item.image" :alt="opening.item.name" />
            </div>
            <div class="opening-info">
              <div class="opening-item-name">{{ opening.item.name }}</div>
              <div class="opening-time">{{ formatTime(opening.time) }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 徽章展示 -->
      <div class="profile-card">
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center">
            <i class="i-heroicons-trophy text-primary mr-2 text-lg"></i>
            <h2 class="card-title mb-0 text-lg">{{ $t('profile.badges') }}</h2>
          </div>
          <a href="#" class="text-primary text-sm hover:text-primary-light transition-colors">
            {{ $t('profile.view_all') }}
          </a>
        </div>
        <div class="badges-grid-mobile grid grid-cols-4 gap-3">
          <div
            v-for="(badge, index) in user.badges.slice(0, 8)"
            :key="index"
            class="badge-item-mobile"
            :title="badge.name"
          >
            <img :src="badge.icon" :alt="badge.name" class="badge-icon w-full h-full object-cover" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 国际化
const { t } = useI18n()

// 设备检测 - 使用响应式状态
const isMobile = ref(false)

// 在客户端检测设备类型
onMounted(() => {
  const checkMobile = () => {
    isMobile.value = window.innerWidth < 768
  }
  
  checkMobile()
  window.addEventListener('resize', checkMobile)
  
  onUnmounted(() => {
    window.removeEventListener('resize', checkMobile)
  })
})

// SEO配置
useSeoMeta({
  title: '个人资料 - CSGO开箱网站',
  description: '查看和管理您的个人资料、统计数据、开箱记录和收藏',
  keywords: '个人资料,用户中心,开箱记录,统计数据',
  ogTitle: '个人资料 - CSGO开箱网站',
  ogDescription: '查看和管理您的个人资料、统计数据、开箱记录和收藏',
  twitterTitle: '个人资料 - CSGO开箱网站',
  twitterDescription: '查看和管理您的个人资料、统计数据、开箱记录和收藏'
})

// 模拟用户数据
const user = ref({
  id: 'CSGO123456',
  name: '玩家昵称',
  avatar: '/demo/avatar1.png',
  level: 25,
  coins: 1250,
  exp: 8500,
  nextLevelExp: 10000,
  registerDate: '2024-01-15T10:30:00Z',
  lastLoginTime: '2024-01-20T15:45:00Z',
  lastLoginIp: '*************',
  tags: [
    { name: 'VIP会员', type: 'vip' },
    { name: '开箱达人', type: 'achievement' },
    { name: '活跃用户', type: 'status' }
  ],
  stats: {
    openCount: 1250,
    battleCount: 89,
    rareItems: 45,
    winRate: 68
  },
  badges: [
    { name: '新手徽章', icon: '/demo/badge1.png' },
    { name: '开箱专家', icon: '/demo/badge2.png' },
    { name: '对战王者', icon: '/demo/badge3.png' },
    { name: '收藏家', icon: '/demo/badge4.png' },
    { name: '幸运星', icon: '/demo/badge5.png' },
    { name: '活跃玩家', icon: '/demo/badge6.png' },
    { name: 'VIP会员', icon: '/demo/badge7.png' },
    { name: '周年纪念', icon: '/demo/badge8.png' }
  ]
})

// 模拟最近开箱记录
const recentOpenings = ref([
  {
    id: 1,
    item: { name: 'AK-47 | 血腥运动', image: '/demo/item1.png' },
    time: '2024-01-20T14:30:00Z',
    caseName: '武器箱 #1'
  },
  {
    id: 2,
    item: { name: 'M4A4 | 龙王', image: '/demo/item2.png' },
    time: '2024-01-20T13:15:00Z',
    caseName: '武器箱 #2'
  },
  {
    id: 3,
    item: { name: 'AWP | 龙王', image: '/demo/item3.png' },
    time: '2024-01-20T12:00:00Z',
    caseName: '武器箱 #3'
  },
  {
    id: 4,
    item: { name: 'Desert Eagle | 龙王', image: '/demo/item4.png' },
    time: '2024-01-20T11:45:00Z',
    caseName: '武器箱 #4'
  }
])

// 模拟收藏的皮肤
const favoriteSkins = ref([
  { id: 1, name: 'AK-47 | 血腥运动', image: '/demo/item1.png', rarity: '稀有', rarityClass: 'rarity-rare' },
  { id: 2, name: 'M4A4 | 龙王', image: '/demo/item2.png', rarity: '史诗', rarityClass: 'rarity-epic' },
  { id: 3, name: 'AWP | 龙王', image: '/demo/item3.png', rarity: '传说', rarityClass: 'rarity-legendary' },
  { id: 4, name: 'Desert Eagle | 龙王', image: '/demo/item4.png', rarity: '稀有', rarityClass: 'rarity-rare' }
])

// 方法
const editProfile = () => {
  console.log('编辑资料')
  // TODO: 实现编辑资料功能
}

const goToSettings = () => {
  navigateTo('/profile/settings')
}

const formatTime = (timeString: string) => {
  return new Date(timeString).toLocaleDateString()
}
</script>

<style scoped>
/* 用户标签样式 */
.user-tag.vip {
  @apply bg-amber-500/20 text-amber-400 border border-amber-500/30;
}

.user-tag.achievement {
  @apply bg-blue-500/20 text-blue-400 border border-blue-500/30;
}

.user-tag.status {
  @apply bg-green-500/20 text-green-400 border border-green-500/30;
}

/* 卡片样式 */
.profile-card {
  @apply bg-white/5 backdrop-blur-md border border-white/10 rounded-2xl p-6;
  @apply hover:border-white/20 transition-all duration-300;
}

.card-title {
  @apply text-xl font-semibold text-white;
}

/* 账户信息样式 */
.account-item {
  @apply p-3 rounded-xl border border-white/10;
  @apply hover:border-white/20 transition-all duration-300;
}

.account-label {
  @apply text-white/70 text-sm;
}

.account-value {
  @apply text-white font-medium;
}

.small-action-btn {
  @apply px-3 py-1 rounded-lg text-xs font-medium;
  @apply bg-primary/20 text-primary border border-primary/30;
  @apply hover:bg-primary/30 transition-all duration-300;
}

.small-action-btn-mobile {
  @apply px-2 py-1 rounded text-xs font-medium;
  @apply bg-primary/20 text-primary border border-primary/30;
  @apply hover:bg-primary/30 transition-all duration-300;
}

/* 统计样式 */
.stats-grid {
  @apply grid grid-cols-2 gap-4;
}

.stat-item {
  @apply p-4 rounded-xl border border-white/10 text-center;
  @apply hover:border-primary/30 hover:bg-gray-800/50 transition-all duration-300;
  @apply hover:-translate-y-0.5 transform;
}

.stat-value {
  @apply text-2xl font-bold text-white mb-1;
}

.stat-label {
  @apply text-white/60 text-sm;
}

.stats-grid-mobile {
  @apply grid grid-cols-2 gap-3;
}

.stat-item-mobile {
  @apply p-3 rounded-lg border border-white/10 text-center;
  @apply hover:border-primary/30 transition-all duration-300;
}

.stat-item-mobile .stat-value {
  @apply text-lg font-bold text-white mb-1;
}

.stat-item-mobile .stat-label {
  @apply text-white/60 text-xs;
}

/* 徽章样式 */
.badges-grid {
  @apply grid grid-cols-3 gap-3;
}

.badge-item {
  @apply w-12 h-12 rounded-xl border border-white/10;
  @apply hover:border-primary/30 hover:-translate-y-0.5 transform transition-all duration-300;
  @apply overflow-hidden;
}

.badge-item.more-badges {
  @apply flex items-center justify-center text-white/60 text-xs font-medium;
  @apply bg-white/5;
}

.badges-grid-mobile {
  @apply grid grid-cols-4 gap-3;
}

.badge-item-mobile {
  @apply w-10 h-10 rounded-lg border border-white/10;
  @apply hover:border-primary/30 transition-all duration-300;
  @apply overflow-hidden;
}

/* 开箱记录样式 */
.openings-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-4;
}

.opening-item {
  @apply flex items-center p-3 rounded-xl border border-white/10;
  @apply hover:border-white/20 transition-all duration-300;
}

.opening-image {
  @apply w-12 h-12 rounded-lg overflow-hidden mr-3;
}

.opening-image img {
  @apply w-full h-full object-cover;
}

.opening-info {
  @apply flex-1;
}

.opening-item-name {
  @apply text-white font-medium text-sm mb-1;
}

.opening-time {
  @apply text-white/60 text-xs;
}

.opening-case {
  @apply text-white/40 text-xs;
}

.openings-list {
  @apply space-y-3;
}

.opening-item-mobile {
  @apply flex items-center p-2 rounded-lg border border-white/10;
  @apply hover:border-white/20 transition-all duration-300;
}

.opening-item-mobile .opening-image {
  @apply w-10 h-10 rounded-lg overflow-hidden mr-3;
}

.opening-item-mobile .opening-item-name {
  @apply text-white font-medium text-sm mb-1;
}

.opening-item-mobile .opening-time {
  @apply text-white/60 text-xs;
}

/* 皮肤样式 */
.skins-grid {
  @apply grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4;
}

.skin-item {
  @apply p-3 rounded-xl border border-white/10;
  @apply hover:border-white/20 transition-all duration-300;
}

.skin-image {
  @apply w-full h-24 rounded-lg overflow-hidden mb-2;
}

.skin-image img {
  @apply w-full h-full object-cover;
}

.skin-info {
  @apply text-center;
}

.skin-name {
  @apply text-white font-medium text-sm mb-1;
}

.skin-rarity {
  @apply text-xs font-medium;
}

.skin-rarity.rarity-rare {
  @apply text-blue-400;
}

.skin-rarity.rarity-epic {
  @apply text-purple-400;
}

.skin-rarity.rarity-legendary {
  @apply text-orange-400;
}

/* 按钮样式 */
.edit-profile-btn, .settings-btn {
  @apply px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300;
  @apply bg-white/5 border border-white/10 text-white/70;
  @apply hover:bg-white/10 hover:border-white/20 hover:text-white;
}

.edit-profile-btn-mobile, .settings-btn-mobile {
  @apply px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300;
  @apply bg-white/5 border border-white/10 text-white/70;
  @apply hover:bg-white/10 hover:border-white/20 hover:text-white;
}

/* 渐变文本 */
.text-gradient {
  @apply text-transparent bg-clip-text bg-gradient-to-r from-primary to-secondary;
}

/* 网格背景 */
.bg-grid-pattern {
  background-image: linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
}

/* 噪声背景 */
.bg-noise {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
}

/* 动画 */
.animate-pulse-slow {
  animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style> 