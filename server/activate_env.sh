#!/bin/bash
# 自动激活虚拟环境脚本

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
VENV_PATH="$SCRIPT_DIR/venv"

# 检查虚拟环境是否存在
if [ ! -d "$VENV_PATH" ]; then
    echo "错误: 虚拟环境不存在于 $VENV_PATH"
    exit 1
fi

# 激活虚拟环境
source "$VENV_PATH/bin/activate"

# 显示Python版本信息
echo "✅ 虚拟环境已激活"
echo "📍 项目目录: $SCRIPT_DIR"
echo "🐍 Python版本: $(python --version)"
echo "📦 虚拟环境: $VIRTUAL_ENV"

# 如果有参数，执行命令
if [ $# -gt 0 ]; then
    echo "🚀 执行命令: $@"
    exec "$@"
else
    # 启动新的bash会话，保持虚拟环境激活
    exec bash
fi
