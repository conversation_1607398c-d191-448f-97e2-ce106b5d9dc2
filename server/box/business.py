import re

import numpy
import json
import logging
import random
import threading
import time
import math
import json
import pytz




from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from django.db import connection, transaction
from django.db.models import Q, F, Sum, Count, Max
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from itertools import chain
from django_redis import get_redis_connection
from datetime import timedelta, datetime
# from libs.aws_sms import send_sms as aws_send_sms
from authentication.serializers import AuthUserSerializer, AuthUserCacheSerializer
from promotion.models import PromotionCaseConfig
from steambase.enums import RespCode, CaseCategoryType, PackageState, GameState, RoomType, BetTeamType, \
    CaseCategoryType, PackageSourceType, B2CTradeState, ZBTradeState, ChargeState
from steambase.redis_con import get_redis
from steambase.utils import is_connection_usable, ParamException
from steambase.utils import current_year, current_week, set_room_sid, remove_sid, delete_room, get_room_sids
from package.interfaces import get_item_price_by_name
from package.models import PackageItem, ItemInfo
from package.serializers import ItemInfoSerializer
from chat.business import get_last_chat_msg
from sitecfg.interfaces import get_box_festival_expired, get_box_festival_sms_content, get_user_point_max, \
    get_box_previous_enable, get_box_chance_previous_times, get_box_chance_previous_type, get_enable_dynamic_box_chance, get_maintenance, get_maintenance_box
from sitecfg.interfaces import get_box_room_expire, get_box_bot_num_max
from sitecfg.models import SiteConfig
# from charge.interfaces import get_charge_total
from box.models import Case, CaseType, CaseRecord, CaseBot, CaseStatisticsDay, CaseStatisticsMonth, RoomDayRank, \
    CaseKeyConfig, CaseCategory
from box.models import DropItem, FreeCaseConfig, FestivalCaseConfig, FestivalCaseDate, FestivalCaseRecord
from box.models import CaseRoom, CaseRoomRound, CaseRoomBet, CaseRoomItem, DropDayRank, IncomeDayRank, LoseWeekRank, CaseCategory, CaseType
from box.serializers import CaseSerializer, CaseCategorySerializer, CaseTypeSerializer, DropItemSerializer, CaseRecordSerializer, FreeCaseConfigSerializer, \
    CaseRoomBetSerializer
from box.serializers import CaseStatisticsDaySerializer, DropDayRankSerializer, IncomeDayRankSerializer
from box.serializers import FestivalCaseRecordSerializer, CaseRoomSerializer, RoomDayRankSerializer, \
    LoseWeekRankSerializer, DropItemSerializer
from box.business_room import get_case_room_list
from authentication.models import AuthUser, UserAsset, UserExtra
from charge.models import ChargeRecord
from b2ctrade.models import ZBTradeRecord
from box.utils import update_user_box_chance_type

from utils.cache_utils import get_or_set_cache, set_fixed_length_cache

# 2025
from sitecfg.interfaces import get_enable_cache
from package.service.item import get_item_price_by_id


_logger = logging.getLogger(__name__)
_box_redis_channel_key = 'ws_channel'
USER = get_user_model()
_steam_img_base = 'https://steamcommunity-a.akamaihd.net/economy/image/'



def ws_send_box_game(data, action):
    if data:
        r = get_redis_connection('default')
        rt_msg = ['box', action, data]
        r.publish(_box_redis_channel_key, json.dumps(rt_msg))


def ws_send_box_room_detail(data, action, sid):
    if data:
        r = get_redis_connection('default')
        rt_msg = ['boxroomdetail', action, data, sid]
        r.publish(_box_redis_channel_key, json.dumps(rt_msg))


def get_drop_price(drop):
    if drop.item_type == 1:
        return get_item_price_by_id(drop.item_info.id).price
    else:
        return drop.coins


def get_user_level(user):
    user_level = 0
    charge_level = user.charge_level.all()
    if len(charge_level) > 0:
        user_level = charge_level[0].level
    if user_level < 1:
        return 0
    else:
        return user_level


def get_user_promotion_level(user):
    promotion_level = user.promotion.level
    return promotion_level


def update_user_freebox_count(case, user):
    user_level = get_user_level(user)
    free_case = case.free_case.first()
    case_level = free_case.level
    if user_level < 1:
        return True
    if case_level > user_level:
        return True

    if case_level == 1:
        user.extra.update_freebox_lv1_count(user, 1)
    if case_level == 2:
        user.extra.update_freebox_lv2_count(user, 1)
    if case_level == 3:
        user.extra.update_freebox_lv3_count(user, 1)
    if case_level == 4:
        user.extra.update_freebox_lv4_count(user, 1)
    if case_level == 5:
        user.extra.update_freebox_lv5_count(user, 1)
    if case_level == 6:
        user.extra.update_freebox_lv6_count(user, 1)
    if case_level == 7:
        user.extra.update_freebox_lv7_count(user, 1)
    if case_level == 8:
        user.extra.update_freebox_lv8_count(user, 1)
    if case_level == 9:
        user.extra.update_freebox_lv9_count(user, 1)
    if case_level == 10:
        user.extra.update_freebox_lv10_count(user, 1)
    return False


def check_user_free_case(user, case):
    user_level = 0
    charge_level = user.charge_level.all()
    if len(charge_level) > 0:
        user_level = charge_level[0].level
    if user_level < 1:
        return RespCode.InvalidGame.value, _('Invalid level')
    now = datetime.now()
    early = datetime(now.year, now.month, now.day)
    records = CaseRecord.objects.filter(user=user, case__case_type=CaseCategoryType.Free.value,
                                        create_time__gte=early, create_time__lte=now).count()
    if records != 0:
        return RespCode.InvalidGame.value, _('Already opened case today')
    free_config = FreeCaseConfig.objects.filter(case=case).first()
    if not free_config:
        return RespCode.InvalidGame.value, _('Invalid case')
    if free_config.level != user_level:
        return RespCode.InvalidGame.value, _('Invalid level, please open correct level case.')
    return RespCode.Succeed.value, None


def check_user_promotion_box(user, case_key):
    box_opened = user.extra.promotion_box_level
    config = PromotionCaseConfig.objects.filter(case__key=case_key).first()
    case_level = config.level.level
    box_opened_bin = list('{:010b}'.format(box_opened))
    box_opened_bin.reverse()
    if int(box_opened_bin[case_level - 1]):
        return True
    with transaction.atomic():
        user.extra.promotion_box_level += 2 ** (case_level - 1)
        user.extra.save()
    return False


def check_user_promotion_level(user, case_key):
    level = user.promotion.level
    config = PromotionCaseConfig.objects.filter(case__key=case_key).first()
    case_level = config.level.level
    if level >= case_level:
        return True
    return False


def get_box_chance_pre(user):
    user_open_record = CaseRecord.objects.filter(user=user).count()
    is_enable = get_box_previous_enable()
    times = get_box_chance_previous_times()
    box_type = get_box_chance_previous_type()
    if not is_enable:
        return user.extra.box_chance_type
    if times <= user_open_record:
        return user.extra.box_chance_type
    return box_type.lower()


def range_chosen(case, weights_array):
    up_sill = case.up_sill
    down_sill = case.down_sill
    # dyntic 箱子成本-饰品价格
    dyntic = case.dyntic
    available_drops = case.drops.all()
    if dyntic > down_sill and dyntic < up_sill:
        chosen_drops = random.choices(available_drops, weights=weights_array, k=1)
    elif dyntic >= up_sill:
        _available_drops = case.drops.filter(up_sill_drop=True)
        if not _available_drops:
            chosen_drops = random.choices(available_drops, weights=weights_array, k=1)
        else:
            chosen_drops = random.choices(_available_drops)
    elif dyntic <= down_sill:
        _available_drops = case.drops.filter(down_sill_drop=True)
        if not _available_drops:
            chosen_drops = random.choices(available_drops, weights=weights_array, k=1)
        else:
            chosen_drops = random.choices(_available_drops)
    else:
        chosen_drops = random.choices(available_drops, weights=weights_array, k=1)
    drop = chosen_drops[0]
    # print(drop)
    price = get_drop_price(drop)
    # print('----')
    # print('up_sill', 'down_sill', 'dyntic', 'price', 'drop')
    # print(up_sill, down_sill, dyntic, price, drop)
    _dyntic = round(dyntic + case.cost - price, 2)
    # print('dyntic', 'case.cost', 'price', '_dyntic')
    # print(dyntic, case.cost, price, _dyntic)
    case.dyntic = _dyntic
    case.save()
    return chosen_drops


def check_user_case_key(user, key):
    if user.package_items.filter(item_info=key, state=PackageState.Available.value).exists():
        return True
    return False


def check_user_free_case_limit(user, case):
    user_level = get_user_level(user)
    free_case = case.free_case.first()
    case_level = free_case.level
    user.extra.update_freebox_limit(user, case_level, 1)
    return True


def user_open_case(user, case_key, count=1, simulate=False):
    """
    用户开箱操作
    
    参数:
        user: 用户对象
        case_key: 箱子的key
        count: 开箱次数，默认1次
        simulate: 是否模拟开箱（不实际扣费和发放物品）
        
    返回:
        (RespCode, 结果/错误信息)
    """
    # 检查系统维护状态
    if not user.is_superuser and (get_maintenance_box() or get_maintenance()):
        return RespCode.Maintenance.value, _('Current game is under maintenance, please wait for a while.')
    # 先获取箱子信息，避免先后判断逻辑错误
    case = Case.objects.filter(case_key=case_key, enable=True).first()
    
    # 验证箱子是否可用    
    
    if not case or not case.enable or not case.is_show:
        return RespCode.InvalidGame.value, _('箱子正在维护中，请稍后再试1')
    
    # 检查是否仅管理员可开箱
    if case.enable_admin and not user.is_superuser:
        return RespCode.InvalidGame.value, _('箱子正在维护中，请稍后再试2')
    
    # 验证用户是否有Steam交易链接（管理员和员工除外）
    if not user.asset.tradeurl and not user.is_superuser and not user.is_staff:
        return RespCode.BadRequest.value, _('您还没有设置steam链接，请在 会员中心>我的账户>账户设置 更新完善steam链接后再试')
    
    # 计算单次开箱成本
    if case.case_type in [3, 4]:  # 免费箱子或节日箱子
        cost = 0
    else:
        cost = round(case.price * case.discount / 100, 2)
    
    # 如果不是模拟且非免费箱，则预先检查余额是否足够
    if not simulate and cost > 0 and user.asset.balance < (cost * count):
        return RespCode.BusinessError.value, _('余额不足')
    
    # 获取可用掉落物品
    available_drops = list(case.drops.all())
    if not available_drops:
        return RespCode.InvalidGame.value, _('箱子没有配置掉落物品')
    
    win_items = []
    send_box_game_list = []
    
    try:
        with transaction.atomic():
            # 获取开箱概率类型和权重数组（放在循环外提高性能）
            box_chance = get_box_chance_pre(user)
            chance_type_name = f'drop_chance_{box_chance}'
            
            # 预序列化可用掉落物品，避免循环中重复操作
            serialized_drops = DropItemSerializer(
                available_drops, many=True, 
                fields=('drop_chance_a', 'drop_chance_b', 'drop_chance_c', 'drop_chance_d', 'drop_chance_e')
            ).data
            weights_array = [d.get(chance_type_name, 0) for d in serialized_drops]
            
            if sum(weights_array) <= 0:
                raise ParamException(_('Case is blocked, please open other case.'))
            
            for i in range(count):
                if not simulate:
                    # 处理各种箱子类型的开箱付费和积分更新
                    if case.case_type == 3:  # 免费箱子
                        user.update_active_point(0, '免费箱子，无积分赠送')
                    elif case.case_type == 4:  # 节日箱子
                        user.update_active_point(0, '节日箱子，无积分赠送')
                    else:  # 普通箱子
                        # 更新余额
                        user.update_balance(-cost, '开箱')
                        # 更新积分
                        user.update_active_point(cost, '开箱')
                    
                    # 更新箱子开箱次数
                    case.update_open_count(count=1)
                    
                    # 处理赠送物品
                    if case.present:
                        PackageItem.objects.create(
                            user=user, 
                            item_info=case.present, 
                            assetid='0', 
                            instanceid='0',
                            state=PackageState.Available.value, 
                            amount=0.0
                            #amount=case.present.item_price.price
                        )
                
                # 根据箱子的设置选择掉落物品
                if len(available_drops) > 1:
                    if case.algorithm_enable:
                        chosen_drops = range_chosen(case, weights_array)
                    else:
                        chosen_drops = random.choices(available_drops, weights=weights_array, k=1)
                else:
                    chosen_drops = available_drops
                
                if not chosen_drops:
                    break
                    
                drop = chosen_drops[0]
                price = get_drop_price(drop)
                
                # 构建物品数据
                item_data = {
                    'uid': drop.uid,
                    'item_type': drop.item_type,
                    # 'price': price
                }
                
                item_info = None
                if drop.item_type == 1:  # 物品类型
                    item_info = drop.item_info
                    item_data.update(ItemInfoSerializer(item_info, fields=('id', 'name', 'name_en', 'name_zh_hans', 'image', 'item_price', 'item_category', 'item_quality', 'item_rarity', 'item_exterior')).data)
                    
                    if not simulate:
                        # 创建物品包
                        package = PackageItem.objects.create(
                            user=user, 
                            item_info=item_info, 
                            assetid='0',
                            instanceid='0', 
                            state=PackageState.Available.value,
                            #amount=price,                            
                            amount=cost,
                            case_name=case.name, 
                            case_cover=case.cover,
                            case_key=case.case_key
                        )
                        item_data['pid'] = package.uid
                        
                elif drop.item_type == 2:  # 钻石类型
                    if not simulate:
                        user.update_diamond(price, 'Case diamond')
                _logger.info(f"开箱: {user.username} 开箱箱子: {case.name}({case.case_key}) 开箱物品: {item_data.get('name', '未知物品')} 价格: {price}")
                # 创建开箱记录
                if not simulate:
                    record = CaseRecord.objects.create(
                        user=user, 
                        case=case, 
                        item_info=item_info,
                        price=price
                    )
                    record_data = CaseRecordSerializer(record).data
                    send_box_game_list.append(record_data)
                
                win_items.append(item_data)
            
    
    except ParamException as pe:
        return RespCode.InvalidParams.value, str(pe)
    except Exception as e:
        logging.exception(f"用户开箱异常: {str(e)}")
        return RespCode.Exception.value, _('开箱过程中发生错误，请重试')
    
    
    resp = {
        'items': win_items
    }
    # API 返回后后台线程延迟推送 socket 消息
    if not simulate and send_box_game_list:
        import threading, time as _time
        def _delayed_push(data_list, _case, _count):
            _time.sleep(10)
            for game_data in data_list:
                game_data['open_count'] = _case.open_count + _count
                ws_send_box_game(game_data, 'new')
                set_fixed_length_cache('case_records', [game_data], 30, 60 * 60 * 12)
        threading.Thread(target=_delayed_push, args=(send_box_game_list, case, count), daemon=True).start()

    return RespCode.Succeed.value, resp


def get_case_list(user, query, fields):
    # TODO: 缓存、对战箱子和箱子独立
    cache_key = 'case_list'
    cache_data = cache.get(cache_key)
    if cache_data:
        return RespCode.Succeed.value, cache_data


    case_list = {
        'top': [],
        # 'free': None,
        'normal': [],
        'freegive': [],
        'ext': []
    }
    top_type = CaseType.objects.filter(category=CaseCategoryType.Top.value).order_by('order')
    for _type in top_type:
        top_case = Case.objects.filter(case_type=_type, enable=True, **query).order_by('order')
        if top_case:
            case_list['top'].append({
                'type': _type.name,
                'case': CaseSerializer(top_case, many=True, fields=fields).data,
                'image': _type.image.url if _type.image else None
            })

    # free_type = CaseType.objects.filter(category=CaseCategoryType.Free.value).first()
    # free_case = Case.objects.filter(case_type=free_type, enable=True, **query).first()
    # if free_case:
    #     case_list['free'] = CaseSerializer(free_case, fields=fields).data

    normal_type = CaseType.objects.filter(category=CaseCategoryType.Normal.value).order_by('order')
    # case_list['normal'] = CaseSerializer(normal_case, many=True, fields=fields).data
    for type in normal_type:
        normal_case = Case.objects.filter(case_type=type, enable=True, **query).order_by('order')
        if normal_case:
            case_list['normal'].append({
                'type': type.name,
                'case': CaseSerializer(normal_case, many=True, fields=fields).data,
                'image': type.image.url if type.image else None
            })

    freegive_type = CaseType.objects.filter(category=CaseCategoryType.FreeGive.value).order_by('order')
    for _type in freegive_type:
        freegive_case = Case.objects.filter(case_type=_type, enable=True, **query).order_by('order')
        if freegive_case:
            case_list['freegive'].append({
                'type': _type.name,
                'case': CaseSerializer(freegive_case, many=True, fields=fields).data,
                'image': _type.image.url if _type.image else None
            })

    ext_type = CaseType.objects.filter(category=CaseCategoryType.Key.value).order_by('order')
    for _type in ext_type:
        ext_case = Case.objects.filter(case_type=_type, enable=True, **query).order_by('order')
        if ext_case:
            case_list['ext'].append({
                'type': _type.name,
                'case': CaseSerializer(ext_case, many=True, fields=fields).data,
                'image': _type.image.url if _type.image else None
            })

    resp = {
        'case_list': case_list
    }
    cache.set(cache_key, resp, timeout = 60 * 30)
    return RespCode.Succeed.value, resp

def get_case_list_hot(num, fields):
    # 缓存
    cache_key = f"hot_box_{num}"

    cached_data = cache.get(cache_key)
    # if cached_data is not None:
    #     return RespCode.Succeed.value, cached_data  

    normal_case = Case.objects.filter(enable=True).order_by('-open_count')[:num]
    case_list =CaseSerializer(normal_case,many=True, fields=fields).data    
    resp = {
        'items': case_list
    }
    cache.set(cache_key, resp, timeout = 30 * 60)    
    return RespCode.Succeed.value, resp

def get_case_list_random(num, fields, domain, randomize=True):
    """
    获取热门案例列表，支持随机选取。
    
    参数:
    - num: 获取的案例数量
    - fields: 序列化输出字段
    - domain: 当前域名
    - randomize: 是否随机选取案例
    """
    cache_key = f"hot_box_{num}_{domain}_{'random' if randomize else 'ordered'}"
    cached_data = cache.get(cache_key)
    if cached_data is not None:
        return RespCode.Succeed.value, cached_data

    # 查询启用的案例
    query_set = Case.objects.filter(enable=True)

    if randomize:
        # 使用 random.sample 对查询结果随机选择 num 个案例
        case_count = query_set.count()
        if case_count <= num:
            selected_cases = query_set
        else:
            selected_ids = random.sample(list(query_set.values_list('id', flat=True)), num)
            selected_cases = Case.objects.filter(id__in=selected_ids)
    else:
        # 按照 open_count 降序排列
        selected_cases = query_set.order_by('-open_count')[:num]

    # 序列化数据
    case_list = CaseSerializer(selected_cases, many=True, fields=fields).data
    resp = {'items': case_list}

    # 缓存结果
    cache.set(cache_key, resp, timeout = 60 * 60 * 12)

    return RespCode.Succeed.value, resp

    
def get_case_list_new(num, fields):
    # 缓存
    cache_key = f"new_box_{num}"
    cached_data = cache.get(cache_key)
    if cached_data is not None:
        return RespCode.Succeed.value, cached_data  
    normal_case = Case.objects.filter(enable=True).order_by('-create_time')[:num]
    case_list =CaseSerializer(normal_case,many=True, fields=fields).data    
    resp = {
        'data': case_list
    }
    cache.set(cache_key, resp, timeout = 30 * 60)    
    return RespCode.Succeed.value, resp

def get_free_case_list(user, query, fields):
    free_case_list = cache.get('freecaselist')
    free_case_list = []
    if not free_case_list:
        free_case_list = []
        free_case = FreeCaseConfig.objects.filter(**query).order_by('level')
        if free_case:
            free_case_list = FreeCaseConfigSerializer(free_case, many=True, fields=fields).data
        cache.set('freecaselist', free_case_list, settings.DAY_REDIS_TIMEOUT)
    # 判断用户是否开过
    for case in free_case_list:
        if CaseRecord.objects.filter(user=user, case__key=case.get('case')['key']).exists():
            case.update(open=True)
        else:
            case.update(open=False)
    resp = {
        'free_case_list': free_case_list,
        'point_max': get_user_point_max()
    }
    return RespCode.Succeed.value, resp


# def get_festival_case(user, query, fields):
#     record = FestivalCaseRecord.objects.filter(user=user, opened=False, expired__gt=timezone.now(), **query).first()
#     if record:
#         data = FestivalCaseRecordSerializer(record, fields=fields).data
#         return RespCode.Succeed.value, data
#     else:
#         return RespCode.Succeed.value, None


def get_case_detail(user, case_key, fields):
    
    if not case_key or not Case.objects.filter(case_key=case_key).exists():
        return RespCode.InvalidParams.value, _('Invalid case key') 
    
    cache_key = f'case_detail_{case_key}'
    case_data = cache.get(cache_key)
    # if case_data:
    #     return RespCode.Succeed.value, {'case': case_data}
    
    case = Case.objects.filter(case_key=case_key).first()
    case_data = CaseSerializer(case, fields=fields).data 
           
    resp = case_data
    # 缓存
    cache.set(cache_key, resp, timeout = 60 * 10)
    return RespCode.Succeed.value, resp


def get_case_record(user, query, fields, page, page_size):
    queryset = CaseRecord.objects.filter(user=user, **query).order_by('-create_time')
    paginator = Paginator(queryset, page_size)
    records = paginator.page(page)
    records_data = CaseRecordSerializer(records, many=True, fields=fields).data
    resp = {
        'records': records_data,
        'total': paginator.count
    }
    return RespCode.Succeed.value, resp


def check_case_bot():
    if get_maintenance() or get_maintenance_box():
        _logger.info('系统维护中，开箱机器人暂停工作')
        return  

    # 检查开箱机器人状态并执行开箱操作
    while True:
        try:
            # if get_maintenance() or get_maintenance_box():
            #     _logger.info('pk系统维护中')
            #     break  
            if not is_connection_usable():  # 检查连接是否可用
                connection.close()  # 关闭连接            
            while True:
                # 设置缓存
                cache_key = 'box_bot_all'
                cached_data = cache.get(cache_key)
                # 如果存在缓存
                if cached_data is not None:
                    bots_list = cached_data  # 从缓存中获取机器人列表
                else:
                    bots_list = list(CaseBot.objects.filter(enable=True))  # 获取启用的机器人列表
                    # 缓存机器人列表
                    cache.set(cache_key, bots_list, timeout = 60 * 60)
                
                # 缓存数量 num
                cache_key_num = 'box_bot_num_max'
                cached_data_num = cache.get(cache_key_num)
                # 如果存在缓存
                if cached_data_num is not None:
                    num = cached_data_num  # 从缓存中获取数量
                else:
                    num = int(get_box_bot_num_max())  # 获取机器人数量上限
                    # 缓存数量
                    cache.set(cache_key_num, num, timeout = 60 * 5)

                if bots_list:               
                    bots = random.sample(bots_list, num)  # 从开箱机器人列表中随机选择指定数量的机器人
                    for bot in bots:
                        last_open = CaseRecord.objects.filter(user=bot.user).order_by('-create_time').first()  # 获取机器人的最后一次开箱记录
                        if last_open:
                            last_open_past = (timezone.now() - last_open.create_time).seconds  # 计算距离上次开箱的时间间隔
                            #if last_open_past < bot.open_idle_min:  # 若时间间隔未超过最小间隔，则跳过该机器人
                            if last_open_past < 3000:  # 若时间间隔未超过最小间隔，则跳过该机器人
                                continue
                            elif last_open_past > bot.open_idle_max:  # 若时间间隔超过最大间隔，则立即开箱
                                open = True
                            else:
                                # 开箱的概率根据时间间隔随机化
                                open_probability = 1 / (bot.open_idle_max - last_open_past + 1)
                                percentage = numpy.random.uniform(0, 1)
                                open = open_probability > percentage
                        else:
                            open = True
                        if open:
                            #case_list = bot.case.filter(enable=True, unlock=True)  # 获取该机器人可用且未解锁的开箱列表
                            cache_key_box = 'bot_box_all'
                            cached_data_box = cache.get(cache_key_box)
                            # 如果存在缓存
                            if cached_data_box is not None:
                                case_list = cached_data_box  # 从缓存中获取数量
                            else:
                                # 获取该机器人可用且未锁定的箱子列表
                                case_list = Case.objects.filter(enable=True, is_show=True)
                                cache.set(cache_key_box, case_list, timeout = 60 * 60)
                            
                            if case_list:
                                case = random.choice(case_list)  # 从可用开箱中随机选择一个
                                cost = round(case.price * case.discount / 100, 2)  # 计算开箱成本
                                if bot.user.asset.balance < cost:  # 若用户余额不足以支付成本，则跳过
                                    continue
                                user_open_case(bot.user, case.case_key)  # 用户开箱操作
                                break
                else:
                    time.sleep(5)  # 若机器人列表为空，则等待1秒
        except ParamException as pe:
            pass
        except Exception as e:
            _logger.exception(e)  # 记录异常日志
        finally:
            time.sleep(5)  # 最终等待1秒

def update_case_statistics(rid):
    record = CaseRecord.objects.filter(uid=rid).first()
    if not record:
        return

    user = record.user
    case = record.case
    item_info = record.item_info
    cost = round(record.case.price * record.case.discount / 100, 2)
    if cost > 0:
        with transaction.atomic():
            CaseStatisticsDay.update_amount(cost)
            CaseStatisticsMonth.update_amount(cost)

    price = round(record.price, 2)
    if price > 0:
        if DropDayRank.objects.filter(amount__gte=price, date=timezone.now()).count() < 20:
            DropDayRank.objects.create(user=user, amount=price, item_info=item_info)
        if IncomeDayRank.objects.filter(amount__gte=round(price - cost, 2), date=timezone.now()).count() < 20:
            IncomeDayRank.objects.create(user=user, amount=round(price - cost, 2), item_info=item_info, case=case)
        with transaction.atomic():
            LoseWeekRank.update_amount(user, round(cost - price, 2))


def update_statistics(rid):
    th = threading.Thread(target=update_case_statistics, args=(rid,))
    th.start()


def socket_connect(request, params):
    resp = []
    records = CaseRecord.objects.filter().order_by('-create_time')[0:14]
    records_data = CaseRecordSerializer(records, many=True).data
    msg = ['box', 'list', records_data]
    resp.append(msg)
    # msgs = get_last_chat_msg()
    # msg = ['message', msgs]
    # resp.append(msg)
    rooms = get_case_room_list()
    msg = ['boxroom', 'list', rooms]
    resp.append(msg)
    return RespCode.Succeed.value, resp


def socket_disconnect(request, params):
    return RespCode.Succeed.value, {}


def sync_box_show_chance():
    DropItem.objects.update(show_chance=F('drop_chance_a'))
    keys = cache.keys('case:*')
    for key in keys:
        cache.delete(key)


def setup_sync_box_show_chance_worker():
    th = threading.Thread(target=sync_box_show_chance, args=())
    th.start()


def get_case_statistics_day(count):
    case_statistics_day = CaseStatisticsDay.objects.all().order_by('-create_time')[:count]
    case_statistics_day_data = CaseStatisticsDaySerializer(case_statistics_day, many=True).data
    return case_statistics_day_data


def get_case_number():
    case_count = CaseRecord.objects.all().count()
    return case_count


def get_case_records(num):
    last_query_time_cache_key = 'last_case_record_time'
    cache_key = f'case_records_{num}'
    cache_timeout = 60 * 5

    def query_func():
        last_time = cache.get(last_query_time_cache_key)
        if last_time is None:
            records = CaseRecord.objects.all().order_by('-create_time')[:num]
        else:
            records = CaseRecord.objects.filter(create_time__gt=last_time).order_by('-create_time')[:num]
        return records if records.exists() else None  # 如果查询结果为空，返回 None

    case_records = get_or_set_cache(cache_key, cache_timeout, query_func)
    if not case_records:  # 如果查询结果为空
        return []  # 返回空列表，避免返回 None 或其他不期望的值

    # 更新缓存时间戳
    cache.set(last_query_time_cache_key, case_records.first().create_time)
    return CaseRecordSerializer(case_records, many=True).data


def get_case_recent_records(query, fields):
    if not query:
        return RespCode.Succeed.value, {}
    case = Case.objects.filter(**query).first()
    case_records = CaseRecord.objects.filter(case=case).order_by('-create_time')[:36]
    case_data = CaseRecordSerializer(case_records, many=True, fields=fields).data
    resp = case_data
    return RespCode.Succeed.value, resp


def get_drop_day_rank(count):
    drop_day_rank = DropDayRank.objects.filter(date=timezone.now()).order_by('-amount')[:count]
    drop_day_rank_data = DropDayRankSerializer(drop_day_rank, many=True).data
    return RespCode.Succeed.value, drop_day_rank_data


def get_income_day_rank(count):
    income_day_rank = IncomeDayRank.objects.filter(date=timezone.now(), amount__gt=0).order_by('-amount')[:count]
    income_day_rank_data = IncomeDayRankSerializer(income_day_rank, many=True).data
    return RespCode.Succeed.value, income_day_rank_data


def get_room_day_rank(count):
    room_day_rank = RoomDayRank.objects.filter(date=timezone.now()).order_by('-amount')[:count]
    room_day_rank_data = RoomDayRankSerializer(room_day_rank, many=True).data
    return RespCode.Succeed.value, room_day_rank_data


def get_lose_week_rank(count):
    year = current_year()
    week = current_week()
    lose_week_rank = LoseWeekRank.objects.filter(year=year, week=week, amount__gt=0).order_by('-amount')[:count]
    lose_week_rank_data = LoseWeekRankSerializer(lose_week_rank, many=True).data
    return RespCode.Succeed.value, lose_week_rank_data


def get_battle_info(user, count):
    # 获取胜负局数
    cache_user_win = cache.get("{}:user_win".format(user))
    if cache_user_win:
        user_win = cache_user_win
    else:
        user_win = CaseRoomBet.objects.filter(user=user, victory=1).count()
        cache.set("{}:user_win".format(user), user_win)
    cache_user_lose = cache.get("{}:user_lose".format(user))
    if cache_user_lose:
        user_lose = cache_user_lose
    else:
        user_lose = CaseRoomBet.objects.filter(user=user, victory=0).count()
        cache.set("{}:user_lose".format(user), user_lose)

    # 获取排行榜
    now = datetime.now()
    # this_week_start = now - timedelta(days=now.weekday()+7)
    # this_week_end = now + timedelta(days=6 - now.weekday())
    # room_day_rank = RoomDayRank.objects.filter(date__gte=this_week_start, date__lte=this_week_end).values(
    #     'user').annotate(
    #     total=Sum('amount')).order_by('-total')[:count]
    # 修改为获取过去24小时的数据 ，之前是7天
    one_day_ago = now - timedelta(hours=24)
    room_day_rank = RoomDayRank.objects.filter(date__gte=one_day_ago).values(
    'user').annotate(
    total=Sum('amount')).order_by('-total')[:count]
    cached = cache.get("rank_week")
    if cached:
        rank_week = cached
    else:
        rank_week = []
        for rank in room_day_rank:
            user = AuthUser.objects.filter(id=rank.get('user')).first()
            data = {
                # "user": AuthUserSerializer(user, read_only=True, fields=('uid', 'steam', 'profile', 'nick_name')).data,
                "user": AuthUserCacheSerializer(user, read_only=True,
                                                fields=('uid', 'steam', 'profile', 'nick_name')).data,
                "price": round(rank.get('total'), 2)
            }
            rank_week.append(data)
            #cache.set("rank_week", rank_week, settings.MINUTES_REDIS_TIMEOUT * 10)
            cache.set("rank_week", rank_week, 60 * 60 * 24)
    resp = {
        'user': {
            'win': user_win,
            'lose': user_lose,
        },
        'rank': rank_week
    }
    return RespCode.Succeed.value, resp



def get_online_number():
    queryset = CaseRoom.objects.filter(state__in=[
        GameState.Joinable.value,
        GameState.Joining.value,
        GameState.Full.value,
        GameState.Running.value,
    ]).all().count()
    return RespCode.Succeed, {"count": queryset}


def get_total_open_count():
    # 对 Case 模型的 open_count 字段求和
    total_open_count = Case.objects.aggregate(total=Sum('open_count'))
    total = total_open_count['total'] or 0
    return total


def update_base_count(key, count):
    # 尝试更新指定 key 的 SiteConfig 对象的 value 字段
    try:
        # 使用 F() 表达式直接在数据库层面上增加 count 值，避免了先查询再保存的额外操作
        SiteConfig.objects.filter(key=key).update(value=F('value') + count)
    except SiteConfig.DoesNotExist:
        # 处理键不存在的情况
        pass



def get_drop_day_price_rank(num):
    if num > 12:
        num = 12
    cache_key = f'drop_day_price_rank_{num}'
    cached_data = cache.get(cache_key)
    if cached_data is not None:
        return RespCode.Succeed.value, cached_data
    
    # 转换为北京时间
    beijing_tz = pytz.timezone('Asia/Shanghai')
    now = timezone.now().astimezone(beijing_tz)
    
    today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
    today_end = now.replace(hour=23, minute=59, second=59, microsecond=999999)
    
    case_records_today = CaseRecord.objects.filter(
        source=0,  
        create_time__range=(today_start, today_end)
    ).annotate(
        max_price=Max('price')
    ).order_by('-max_price').values(
        'create_time',
        'item_info', 
        'item_info__market_name_cn',
        'item_info__rarity_color',
        'item_info__icon_url_large',
        'price'
    )

    seen_item_ids = set()
    items_data = []
    for record in case_records_today:
        if record['item_info'] not in seen_item_ids:
            seen_item_ids.add(record['item_info'])
            items_data.append({
                'time': record['create_time'],
                'item_id': record['item_info'],
                'item_name': record['item_info__market_name_cn'],
                'color': record['item_info__rarity_color'],
                'image': _steam_img_base + record['item_info__icon_url_large'],
                'price': record['price'],
            })
            if len(items_data) >= num:
                break

    response_data = {
        "items": items_data
    }
    cache.set(cache_key, response_data, timeout=60 * 60)

    return RespCode.Succeed.value, response_data




def get_drop_day_count_rank(num):
    if num > 12:
        num = 12
    cache_key = f'drop_day_most_rank_{num}'
    cached_data = cache.get(cache_key)
    if cached_data is not None:
        return RespCode.Succeed.value, cached_data
    
    # 转换为北京时间
    beijing_tz = pytz.timezone('Asia/Shanghai')
    now = timezone.now().astimezone(beijing_tz)
    
    today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
    today_end = now.replace(hour=23, minute=59, second=59, microsecond=999999)

    most_frequent_items_today = CaseRecord.objects.filter(
        source=0, 
        create_time__range=(today_start, today_end)
    ).values(
        'item_info', 'price', 'item_info__market_name_cn', 'item_info__rarity_color', 'item_info__icon_url_large'
    ).annotate(
        drop_count=Count('item_info')
    ).order_by('-drop_count')[:num]

    items_data = [{
        'item_id': item_stat['item_info'],
        'count': item_stat['drop_count'],
        'price': item_stat['price'],
        'item_name': item_stat['item_info__market_name_cn'],
        'color': item_stat['item_info__rarity_color'],
        'image': _steam_img_base + item_stat['item_info__icon_url_large'],
    } for item_stat in most_frequent_items_today]

    response_data = {
        "items": items_data
    }
    cache.set(cache_key, response_data, timeout=60 * 60)  

    return RespCode.Succeed.value, response_data


# 获取对战排行
def get_battle_day_rank(num):
    # 获取当前北京时间
    beijing_tz = pytz.timezone('Asia/Shanghai')
    now = timezone.now().astimezone(beijing_tz)
    
    # 定义缓存键，包含具体时间范围
    cache_key = f'battle_day_rank_{now.strftime("%Y-%m-%d_%H")}'
    
    # 尝试从缓存中获取数据
    cached_data = cache.get(cache_key)
    if cached_data is not None:
        return RespCode.Succeed.value, cached_data  
    
    # 计算过去24小时的时间范围
    past_24_hours = now - timezone.timedelta(hours=24)
    
    # 进行数据库查询
    battle_day_rank = RoomDayRank.objects.filter(
        create_time__range=(past_24_hours, now)
    ).values('user__id', 'user__steam__personaname', 'user__steam__avatarfull').annotate(
        total=Sum('amount')
    ).order_by('-total')[:num]

    items_data = [{
        'user_info': {
            'nickname': item_stat['user__steam__personaname'],
            'avatar': item_stat['user__steam__avatarfull']
        },
        'total_battles': 0,  # 需要另外计算
        'total_wins': 0,     # 需要另外计算
        'win_rate': 0,       # 需要另外计算
        'net_profit': float(item_stat['total']) if item_stat['total'] else 0,
        'rank': idx + 1
    } for idx, item_stat in enumerate(battle_day_rank)]
    
    # 将查询结果缓存1小时，以减少对数据库的访问
    cache.set(cache_key, items_data, timeout=60 * 60)  

    return RespCode.Succeed.value, items_data

def get_tag_box_list(num, tag, fields):
    # 缓存
    cache_key = f'tag_box_list_{tag}_{num}'
    cached_data = cache.get(cache_key)
    # if cached_data is not None:
    #     return RespCode.Succeed.value, cached_data  
    # 获取标签对应的box列表
    case_list = Case.objects.filter(tag=tag, enable = True).order_by('sort')[:num]
    case_data = CaseSerializer(case_list, many=True, fields=fields).data
    response_data = {
        "items": case_data
    }
    # 缓存数据
    cache.set(cache_key, response_data, timeout=60 * 60)
    return RespCode.Succeed.value, response_data



def get_discount_box_list(fields, num):
    # 缓存
    cache_key = f'discount_box_list_{num}'
    cached_data = cache.get(cache_key)
    if cached_data is not None:
        return RespCode.Succeed.value, cached_data  
    # 获取打折的box列表 小于100
    # case_list = Case.objects.filter(discount__lt=100, enable = True).order_by('-discount')
    case_list = Case.objects.filter(discount__lt=100, enable = True).order_by('-discount')[:num]
    case_data = CaseSerializer(case_list, many=True, fields=fields).data
    response_data = {
        "items": case_data
    }
    # 缓存数据
    cache.set(cache_key, response_data, timeout=60 * 60)
    return RespCode.Succeed.value, response_data


def search_case(q, query, fields):
    """
    搜索箱子，按分类返回结果
    
    参数:
    - q: 搜索关键词 (如果为空则返回所有数据)
    - query: 其他过滤条件
    - fields: 返回字段
    
    返回:
    - 状态码和搜索结果
    """
    # 准备搜索关键词
    q = q.strip() if q else ""
    
    # 使用关键词和查询条件生成缓存键
    cache_params = '-'.join([f"{k}:{v}" for k, v in sorted(query.items())]) if query else 'no_params'
    cache_key = f'search_case_{q or "all"}_{cache_params}'
    
    # 尝试从缓存获取结果
    cached_data = cache.get(cache_key)
    if get_enable_cache() and cached_data:
        return RespCode.Succeed.value, cached_data

    # 未命中缓存，执行搜索
    data = []
    categories = CaseCategory.objects.filter(enable=True).order_by('sort')
    
    # 优化查询 - 使用select_related预加载关联的case_type数据
    for category in categories:
        # 根据是否有搜索条件构建查询
        if q:
            cases = Case.objects.filter(
                Q(name_en__icontains=q) |
                Q(name__contains=q) |
                Q(name_zh_hans__contains=q),
                case_type=category,
                enable=True,
                **query
            ).select_related('case_type').order_by('sort')
        else:
            # 无搜索条件时返回所有符合其他过滤条件的箱子
            cases = Case.objects.filter(
                case_type=category,
                enable=True,
                **query
            ).select_related('case_type').order_by('sort')
        
        # 优化内存使用 - 不再一个一个处理case
        if not cases:
            continue
            
        box_list = []
        for case in cases:
            case_data = CaseSerializer(case, fields=fields).data
            case_type = getattr(case, 'case_type', None)
            
            # 添加类型信息
            type_data = {
                'type_id': getattr(case_type, 'type_id', None),
                'type_name': getattr(case_type, 'type_name', None),
                'type_name_en': getattr(case_type, 'type_name_en', None),
                'type_name_zh_hans': getattr(case_type, 'type_name_zh_hans', None),
            }
            case_data.update(type_data)
            box_list.append(case_data)
            
        if box_list:
            category_data = {
                'cate_id': category.cate_id,
                'cate_name': category.cate_name,
                'cate_name_en': getattr(category, 'cate_name_en', None),
                'cate_name_zh_hans': getattr(category, 'cate_name_zh_hans', None),
                'icon': getattr(category, 'icon', None),
                'cases': box_list,
            }
            data.append(category_data)

    response_data = data
    
    # 缓存搜索结果 - 无搜索词时缓存时间稍长一些
    timeout = 10 * 60 if q else 20 * 60
    cache.set(cache_key, response_data, timeout=timeout)
    
    return RespCode.Succeed.value, response_data

            
def get_box_skin_list(case_key, fields):
    # 缓存
    cache_key = f'box_skin_list_{case_key}'
    cached_data = cache.get(cache_key)
    if cached_data and get_enable_cache():
        return RespCode.Succeed.value, cached_data  
    # 获取id值
    case = Case.objects.filter(case_key=case_key).first()
    if case is None:
        return RespCode.BusinessError.value, {'message': 'Case not found'}  # 处理无效key的情况    

    # 确保fields包含需要的字段 - 添加全面的字段列表
    extra_fields = (
        'item_info', 'drop_chance_a', 'id', 'uid', 'case', 'show_chance',
        'up_sill_drop', 'down_sill_drop', 'item_type'
    )

    item_info_fields = (
        'id', 'name', 'name_en', 'name_zh_hans', 'image', 'item_price',
        'item_category', 'item_quality', 'item_rarity', 'item_exterior'
    )

    # 扩展fields参数
    full_fields = fields
    for field in extra_fields:
        if field not in full_fields:
            full_fields = full_fields + (field,)

    # 确保item_info中包含item_rarity
    if 'item_info' in full_fields and isinstance(full_fields, dict) and 'fields' in full_fields['item_info']:
        for field in item_info_fields:
            if field not in full_fields['item_info']['fields']:
                full_fields['item_info']['fields'] = full_fields['item_info']['fields'] + (field,)

    # 获取box对应的skin列表
    drop_items = DropItem.objects.filter(case_id=case.id).order_by('id')
    
    # 序列化数据
    case_data = DropItemSerializer(drop_items, fields=fields, many=True).data
    
    # 按稀有度分组
    rarity_groups = {}
    total_items = len(case_data)
    total_chance = 0
    
    # 计算总掉落几率
    for item in case_data:
        chance_value = item.get('show_chance', 0)
        total_chance += chance_value
    
    # 计算每个物品的掉落几率百分比，并按稀有度分组整理数据
    for item in case_data:
        # 计算该物品的掉落几率百分比
        # item_chance = item.get('show_chance', 0)
        # chance_percentage = round((item_chance / total_chance) * 100, 4) if total_chance > 0 else 0
        
        # 添加掉落几率百分比到物品数据中
        # item['chance_percentage'] = chance_percentage
        
        # 获取稀有度信息
        item_info = item.get('item_info', {})
        item_rarity = item_info.get('item_rarity', {}) if item_info else {}
        
        # 默认值
        rarity_id = 0
        rarity_name = '未知'
        rarity_color = '#cccccc'
        
        # 尝试获取稀有度信息
        if item_rarity:
            rarity_id = item_rarity.get('rarity_id', 0)
            rarity_name = item_rarity.get('rarity_name', '未知')
            rarity_name_en = item_rarity.get('rarity_name_en', 'Unknown')
            rarity_name_zh_hans = item_rarity.get('rarity_name_zh_hans', '未知')
            rarity_color = item_rarity.get('rarity_color', '#cccccc')
        
        # 使用rarity_id作为唯一键
        if rarity_id not in rarity_groups:
            rarity_groups[rarity_id] = {
                'rarity_id': rarity_id,
                'rarity_name': rarity_name,
                'rarity_name_en': rarity_name_en,
                'rarity_name_zh_hans': rarity_name_zh_hans,
                'rarity_color': rarity_color,
                'items': [],
                'count': 0,
                'total_chance': 0
            }
        
        rarity_groups[rarity_id]['items'].append(item)
        rarity_groups[rarity_id]['count'] += 1
        # rarity_groups[rarity_id]['total_chance'] += item_chance
    
    # 计算每个稀有度的占比和几率百分比
    result = []
    for rarity_id, data in rarity_groups.items():
        count_percentage = round((data['count'] / total_items) * 100, 2) if total_items > 0 else 0
        # chance_percentage = round((data['total_chance'] / total_chance) * 100, 2) if total_chance > 0 else 0
        
        # 按照掉落几率从高到低排序每个稀有度内的物品
        # data['items'].sort(key=lambda x: x.get('chance_percentage', 0), reverse=True)
        # 价格排序
        data['items'].sort(key=lambda x: x.get('item_price', 0), reverse=True)
        
        result.append({
            'rarity_id': data['rarity_id'],
            'rarity_name': data['rarity_name'],
            'rarity_name_en': data['rarity_name_en'],
            'rarity_name_zh_hans': data['rarity_name_zh_hans'],
            'rarity_color': data['rarity_color'],
            'count': data['count'],
            'count_percentage': count_percentage,
            # 'chance_percentage': chance_percentage,
            'items': data['items']
        })
    
    # 按照稀有度ID排序（通常ID越高稀有度越高）
    result.sort(key=lambda x: x['rarity_id'], reverse=True)
    
    # 构建响应数据
    response_data = {
        # 按照稀有度ID排序（通常ID越高稀有度越高）
        'items': result,
        # 总数量
        'total': total_items
    }

    # 缓存数据
    cache.set(cache_key, response_data, timeout=60 * 60 * 1)  # 缓存1小时
    return RespCode.Succeed.value, response_data



def get_box_record(case_key, num, fields):
    # 限制数量
    if num > 30:
        num = 30
    # 缓存
    cache_key = f'box_record_{case_key}_{num}'
    cached_data = cache.get(cache_key)
    # if cached_data and get_enable_cache():
    #     return RespCode.Succeed.value, cached_data  

    # 获取id值
    case = Case.objects.filter(case_key=case_key).first()
    if case is None:
        return RespCode.BusinessError.value, {'message': 'Case not found'}  # 处理无效key的情况

    case_list = CaseRecord.objects.filter(case_id=case.id).order_by('-create_time')[:num]
    
    case_data = CaseRecordSerializer(case_list, fields=fields, many=True).data

    # 构建响应数据
    response_data = {'items': case_data}

    # 缓存数据
    cache.set(cache_key, response_data, timeout=60 * 5)  # 缓存5分钟

    return RespCode.Succeed.value, response_data


def get_case_category(fields):
    cache_key = 'case_category'
    cached_data = cache.get(cache_key)
    if cached_data and get_enable_cache():
        return RespCode.Succeed.value, cached_data

    case_category = CaseCategory.objects.filter(enable=True).order_by('sort')
    case_category_data = CaseCategorySerializer(case_category, many=True, fields=fields).data
    response_data = case_category_data
    cache.set(cache_key, response_data, timeout=60 * 60)
    return RespCode.Succeed.value, response_data

def get_case_type(fields):
    cache_key = 'case_type'
    cached_data = cache.get(cache_key)
    if cached_data and get_enable_cache():
        return RespCode.Succeed.value, cached_data

    case_type = CaseType.objects.filter(enable=True).order_by('sort')
    case_type_data = CaseTypeSerializer(case_type, many=True, fields=fields).data
    response_data = case_type_data
    cache.set(cache_key, response_data, timeout=60 * 60)
    return RespCode.Succeed.value, response_data

def get_battle_case_list(fields):
    cache_key = 'battle_case_list'
    cached_data = cache.get(cache_key)
    if cached_data and get_enable_cache():
        return RespCode.Succeed.value, cached_data

    case_list = Case.objects.filter(enable=True, enable_room=True, is_show=True, enable_admin=False, price__gt=0).order_by('sort')
    case_data = CaseSerializer(case_list, many=True, fields=fields).data
    
    cache.set(cache_key, case_data, timeout=60 * 60)
    return RespCode.Succeed.value, case_data