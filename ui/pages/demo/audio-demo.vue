<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-8">
    <div class="max-w-6xl mx-auto">
      <!-- 页面标题 -->
      <div class="text-center mb-8">
        <h1 class="text-4xl font-bold text-white mb-4">🎵 音效测试页面</h1>
        <p class="text-lg text-white/60">测试对战演示页面的所有音效功能</p>
      </div>

      <!-- AudioController 组件演示 -->
      <div class="mb-8">
        <h2 class="text-2xl font-bold text-white mb-6">🎛️ AudioController 组件演示</h2>
        
        <!-- 基础用法 -->
        <div class="mb-8">
          <h3 class="text-xl font-bold text-white mb-4">📱 基础用法</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- 默认配置 -->
            <div class="bg-slate-800/60 backdrop-blur-xl border border-white/20 rounded-xl p-6">
              <h4 class="text-lg font-semibold text-white mb-3">默认配置</h4>
              <UiAudioController
                v-model:muted="audioState1.muted"
                v-model:volume-value="audioState1.volume"
                @volume-change="handleVolumeChange1"
                @mute-toggle="handleMuteToggle1"
                @test-sound="handleTestSound1"
              />
              <div class="mt-3 text-sm text-white/60">
                <p>静音状态: {{ audioState1.muted ? '是' : '否' }}</p>
                <p>音量大小: {{ Math.round(audioState1.volume * 100) }}%</p>
              </div>
            </div>

            <!-- 自定义配置 -->
            <div class="bg-slate-800/60 backdrop-blur-xl border border-white/20 rounded-xl p-6">
              <h4 class="text-lg font-semibold text-white mb-3">自定义配置</h4>
              <UiAudioController
                v-model:muted="audioState2.muted"
                v-model:volume-value="audioState2.volume"
                :show-volume-slider="true"
                :show-test-buttons="true"
                :test-sounds="['buttonClick', 'roundStart', 'rollEnd']"
                @volume-change="handleVolumeChange2"
                @mute-toggle="handleMuteToggle2"
                @test-sound="handleTestSound2"
              />
              <div class="mt-3 text-sm text-white/60">
                <p>静音状态: {{ audioState2.muted ? '是' : '否' }}</p>
                <p>音量大小: {{ Math.round(audioState2.volume * 100) }}%</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 高级用法 -->
        <div class="mb-8">
          <h3 class="text-xl font-bold text-white mb-4">⚡ 高级用法</h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- 对战主题 -->
            <div class="bg-slate-800/60 backdrop-blur-xl border border-white/20 rounded-xl p-6">
              <h4 class="text-lg font-semibold text-white mb-3">🎮 对战主题</h4>
              <UiAudioController
                v-model:muted="audioState3.muted"
                v-model:volume-value="audioState3.volume"
                :show-volume-slider="true"
                :show-test-buttons="true"
                :test-sounds="['battleStart', 'roundStart', 'rollStart', 'rollEnd', 'winnerAnnounce']"
                :theme="'battle'"
                @volume-change="handleVolumeChange3"
                @mute-toggle="handleMuteToggle3"
                @test-sound="handleTestSound3"
              />
            </div>

            <!-- 简化版本 -->
            <div class="bg-slate-800/60 backdrop-blur-xl border border-white/20 rounded-xl p-6">
              <h4 class="text-lg font-semibold text-white mb-3">🎯 简化版本</h4>
              <UiAudioController
                v-model:muted="audioState4.muted"
                v-model:volume-value="audioState4.volume"
                :show-volume-slider="false"
                :show-test-buttons="false"
                @mute-toggle="handleMuteToggle4"
              />
            </div>

            <!-- 完整功能 -->
            <div class="bg-slate-800/60 backdrop-blur-xl border border-white/20 rounded-xl p-6">
              <h4 class="text-lg font-semibold text-white mb-3">🔧 完整功能</h4>
              <UiAudioController
                v-model:muted="audioState5.muted"
                v-model:volume-value="audioState5.volume"
                :show-volume-slider="true"
                :show-test-buttons="true"
                :test-sounds="['buttonClick', 'roundStart', 'roundEnd', 'rollStart', 'rollEnd', 'winnerAnnounce', 'calculate']"
                :theme="'full'"
                @volume-change="handleVolumeChange5"
                @mute-toggle="handleMuteToggle5"
                @test-sound="handleTestSound5"
              />
            </div>
          </div>
        </div>

        <!-- 状态同步演示 -->
        <div class="mb-8">
          <h3 class="text-xl font-bold text-white mb-4">🔄 状态同步演示</h3>
          <div class="bg-slate-800/60 backdrop-blur-xl border border-white/20 rounded-xl p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- 主控制器 -->
              <div>
                <h4 class="text-lg font-semibold text-white mb-3">主控制器</h4>
                <UiAudioController
                  v-model:muted="masterAudioState.muted"
                  v-model:volume-value="masterAudioState.volume"
                  :show-volume-slider="true"
                  :show-test-buttons="true"
                  :test-sounds="['buttonClick', 'roundStart', 'rollEnd']"
                  @volume-change="handleMasterVolumeChange"
                  @mute-toggle="handleMasterMuteToggle"
                  @test-sound="handleMasterTestSound"
                />
              </div>

              <!-- 同步状态显示 -->
              <div>
                <h4 class="text-lg font-semibold text-white mb-3">同步状态</h4>
                <div class="space-y-3">
                  <div class="flex items-center justify-between p-3 bg-slate-700/50 rounded-lg">
                    <span class="text-white/60">静音状态:</span>
                    <span class="text-white font-medium">{{ masterAudioState.muted ? '已静音' : '已开启' }}</span>
                  </div>
                  <div class="flex items-center justify-between p-3 bg-slate-700/50 rounded-lg">
                    <span class="text-white/60">音量大小:</span>
                    <span class="text-white font-medium">{{ Math.round(masterAudioState.volume * 100) }}%</span>
                  </div>
                  <div class="flex items-center justify-between p-3 bg-slate-700/50 rounded-lg">
                    <span class="text-white/60">最后测试:</span>
                    <span class="text-white font-medium">{{ lastTestSound || '无' }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 原有音效测试区域 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- 对战流程音效 -->
        <div class="bg-slate-800/60 backdrop-blur-xl border border-white/20 rounded-xl p-6">
          <h3 class="text-xl font-bold text-white mb-4">🎮 对战流程音效</h3>
          <div class="space-y-3">
            <button
              @click="async () => { await playSound('buttonClick'); playBattleSequence.battleStart(); }"
              class="w-full px-4 py-2 bg-blue-600 hover:bg-blue-500 text-white rounded-lg transition-colors"
            >
              开始对战
            </button>
            <button
              @click="async () => { await playSound('buttonClick'); playBattleSequence.roundStart(); }"
              class="w-full px-4 py-2 bg-green-600 hover:bg-green-500 text-white rounded-lg transition-colors"
            >
              轮次开始
            </button>
            <button
              @click="async () => { await playSound('buttonClick'); playBattleSequence.rollStart(); }"
              class="w-full px-4 py-2 bg-purple-600 hover:bg-purple-500 text-white rounded-lg transition-colors"
            >
              开箱开始
            </button>
            <button
              @click="async () => { await playSound('buttonClick'); playBattleSequence.rollEnd(); }"
              class="w-full px-4 py-2 bg-orange-600 hover:bg-orange-500 text-white rounded-lg transition-colors"
            >
              开箱结束
            </button>
            <button
              @click="async () => { await playSound('buttonClick'); playBattleSequence.announceWinner(); }"
              class="w-full px-4 py-2 bg-yellow-600 hover:bg-yellow-500 text-white rounded-lg transition-colors"
            >
              获胜宣布
            </button>
          </div>
        </div>

        <!-- 单独音效测试 -->
        <div class="bg-slate-800/60 backdrop-blur-xl border border-white/20 rounded-xl p-6">
          <h3 class="text-xl font-bold text-white mb-4">🔊 单独音效测试</h3>
          <div class="space-y-3">
            <button
              @click="async () => { await playSound('buttonClick'); }"
              class="w-full px-4 py-2 bg-gray-600 hover:bg-gray-500 text-white rounded-lg transition-colors"
            >
              按钮点击
            </button>
            <button
              @click="async () => { await playSound('roundStart'); }"
              class="w-full px-4 py-2 bg-indigo-600 hover:bg-indigo-500 text-white rounded-lg transition-colors"
            >
              轮次开始
            </button>
            <button
              @click="async () => { await playSound('roundEnd'); }"
              class="w-full px-4 py-2 bg-pink-600 hover:bg-pink-500 text-white rounded-lg transition-colors"
            >
              轮次结束
            </button>
            <button
              @click="async () => { await playSound('rollStart'); }"
              class="w-full px-4 py-2 bg-teal-600 hover:bg-teal-500 text-white rounded-lg transition-colors"
            >
              开箱开始
            </button>
            <button
              @click="async () => { await playSound('rollEnd'); }"
              class="w-full px-4 py-2 bg-cyan-600 hover:bg-cyan-500 text-white rounded-lg transition-colors"
            >
              开箱结束
            </button>
            <button
              @click="async () => { await playSound('winnerAnnounce'); }"
              class="w-full px-4 py-2 bg-amber-600 hover:bg-amber-500 text-white rounded-lg transition-colors"
            >
              获胜宣布
            </button>
          </div>
        </div>

        <!-- 背景音乐控制 -->
        <div class="bg-slate-800/60 backdrop-blur-xl border border-white/20 rounded-xl p-6">
          <h3 class="text-xl font-bold text-white mb-4">🎵 背景音乐控制</h3>
          <div class="space-y-3">
            <button
              @click="async () => { await playSound('buttonClick'); playBackgroundMusic(); }"
              class="w-full px-4 py-2 bg-emerald-600 hover:bg-emerald-500 text-white rounded-lg transition-colors"
            >
              播放背景音乐
            </button>
            <button
              @click="async () => { await playSound('buttonClick'); stopBackgroundMusic(); }"
              class="w-full px-4 py-2 bg-red-600 hover:bg-red-500 text-white rounded-lg transition-colors"
            >
              停止背景音乐
            </button>
            <button
              @click="async () => { await playSound('buttonClick'); stopAllSounds(); }"
              class="w-full px-4 py-2 bg-slate-600 hover:bg-slate-500 text-white rounded-lg transition-colors"
            >
              停止所有音效
            </button>
          </div>
        </div>
      </div>

      <!-- 音效文件信息 -->
      <div class="mt-8 bg-slate-800/60 backdrop-blur-xl border border-white/20 rounded-xl p-6">
        <h3 class="text-xl font-bold text-white mb-4">📁 音效文件信息</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div class="space-y-2">
            <div class="flex justify-between">
              <span class="text-white/60">轮次开始:</span>
              <span class="text-white">last-tick.mp3</span>
            </div>
            <div class="flex justify-between">
              <span class="text-white/60">轮次结束:</span>
              <span class="text-white">tick.mp3</span>
            </div>
            <div class="flex justify-between">
              <span class="text-white/60">开箱开始:</span>
              <span class="text-white">boost-on.mp3</span>
            </div>
            <div class="flex justify-between">
              <span class="text-white/60">开箱结束:</span>
              <span class="text-white">rare-special-item.mp3</span>
            </div>
          </div>
          <div class="space-y-2">
            <div class="flex justify-between">
              <span class="text-white/60">获胜宣布:</span>
              <span class="text-white">win.mp3</span>
            </div>
            <div class="flex justify-between">
              <span class="text-white/60">按钮点击:</span>
              <span class="text-white">tick.mp3</span>
            </div>
            <div class="flex justify-between">
              <span class="text-white/60">通知音效:</span>
              <span class="text-white">chat_sound.mp3</span>
            </div>
            <div class="flex justify-between">
              <span class="text-white/60">背景音乐:</span>
              <span class="text-white">boost-on.mp3</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// SEO 元数据
useSeoMeta({
  title: "音效测试 - CSGO开箱网站",
  description: "测试对战演示页面的所有音效功能",
  keywords: "音效测试,音频功能,对战音效",
});

// 国际化
const { t } = useI18n();

// 音效管理
const { 
  isMuted, 
  volume, 
  isLoading,
  isSupported,
  playSound, 
  toggleMute, 
  setVolume,
  playBattleSequence,
  playBackgroundMusic,
  stopBackgroundMusic,
  stopAllSounds
} = useBattleAudio();

// AudioController 组件演示状态
const audioState1 = ref({
  muted: false,
  volume: 0.7
});

const audioState2 = ref({
  muted: false,
  volume: 0.5
});

const audioState3 = ref({
  muted: false,
  volume: 0.8
});

const audioState4 = ref({
  muted: false,
  volume: 0.6
});

const audioState5 = ref({
  muted: false,
  volume: 0.9
});

const masterAudioState = ref({
  muted: false,
  volume: 0.7
});

const lastTestSound = ref('');

// 事件处理方法
const handleVolumeChange1 = (newVolume) => {
  console.log('[🎵AUDIO-TEST] 基础用法音量变化:', newVolume);
  audioState1.value.volume = newVolume;
};

const handleMuteToggle1 = (muted) => {
  console.log('[🎵AUDIO-TEST] 基础用法静音切换:', muted);
  audioState1.value.muted = muted;
};

const handleTestSound1 = async (soundType) => {
  console.log('[🎵AUDIO-TEST] 基础用法测试音效:', soundType);
  try {
    await playSound(soundType);
  } catch (error) {
    console.warn('[🎵AUDIO-TEST] 音效测试失败:', soundType, error);
  }
};

const handleVolumeChange2 = (newVolume) => {
  console.log('[🎵AUDIO-TEST] 自定义配置音量变化:', newVolume);
  audioState2.value.volume = newVolume;
};

const handleMuteToggle2 = (muted) => {
  console.log('[🎵AUDIO-TEST] 自定义配置静音切换:', muted);
  audioState2.value.muted = muted;
};

const handleTestSound2 = async (soundType) => {
  console.log('[🎵AUDIO-TEST] 自定义配置测试音效:', soundType);
  try {
    await playSound(soundType);
  } catch (error) {
    console.warn('[🎵AUDIO-TEST] 音效测试失败:', soundType, error);
  }
};

const handleVolumeChange3 = (newVolume) => {
  console.log('[🎵AUDIO-TEST] 对战主题音量变化:', newVolume);
  audioState3.value.volume = newVolume;
};

const handleMuteToggle3 = (muted) => {
  console.log('[🎵AUDIO-TEST] 对战主题静音切换:', muted);
  audioState3.value.muted = muted;
};

const handleTestSound3 = async (soundType) => {
  console.log('[🎵AUDIO-TEST] 对战主题测试音效:', soundType);
  try {
    await playSound(soundType);
  } catch (error) {
    console.warn('[🎵AUDIO-TEST] 音效测试失败:', soundType, error);
  }
};

const handleMuteToggle4 = (muted) => {
  console.log('[🎵AUDIO-TEST] 简化版本静音切换:', muted);
  audioState4.value.muted = muted;
};

const handleVolumeChange5 = (newVolume) => {
  console.log('[🎵AUDIO-TEST] 完整功能音量变化:', newVolume);
  audioState5.value.volume = newVolume;
};

const handleMuteToggle5 = (muted) => {
  console.log('[🎵AUDIO-TEST] 完整功能静音切换:', muted);
  audioState5.value.muted = muted;
};

const handleTestSound5 = async (soundType) => {
  console.log('[🎵AUDIO-TEST] 完整功能测试音效:', soundType);
  try {
    await playSound(soundType);
  } catch (error) {
    console.warn('[🎵AUDIO-TEST] 音效测试失败:', soundType, error);
  }
};

// 主控制器事件处理
const handleMasterVolumeChange = (newVolume) => {
  console.log('[🎵AUDIO-TEST] 主控制器音量变化:', newVolume);
  masterAudioState.value.volume = newVolume;
};

const handleMasterMuteToggle = (muted) => {
  console.log('[🎵AUDIO-TEST] 主控制器静音切换:', muted);
  masterAudioState.value.muted = muted;
};

const handleMasterTestSound = async (soundType) => {
  console.log('[🎵AUDIO-TEST] 主控制器测试音效:', soundType);
  lastTestSound.value = soundType;
  try {
    await playSound(soundType);
  } catch (error) {
    console.warn('[🎵AUDIO-TEST] 音效测试失败:', soundType, error);
  }
};
</script>

<style scoped>
/* 音量滑块样式 */
.slider::-webkit-slider-thumb {
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider::-webkit-slider-track {
  background: linear-gradient(to right, #3b82f6, #8b5cf6);
  border-radius: 8px;
  height: 8px;
}

.slider::-moz-range-track {
  background: linear-gradient(to right, #3b82f6, #8b5cf6);
  border-radius: 8px;
  height: 8px;
}
</style> 