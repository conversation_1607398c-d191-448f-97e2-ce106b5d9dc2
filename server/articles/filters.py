import django_filters
from django.db.models import Q
from .models import Content, ContentCategory, ContentTag


class ContentFilter(django_filters.FilterSet):
    """内容过滤器"""
    
    # 内容类型
    content_type = django_filters.ChoiceFilter(
        choices=Content.CONTENT_TYPE_CHOICES,
        field_name='content_type'
    )
    
    # 分类
    category = django_filters.ModelChoiceFilter(
        queryset=ContentCategory.objects.filter(is_active=True),
        field_name='category'
    )
    
    category_slug = django_filters.CharFilter(
        field_name='category__slug',
        lookup_expr='exact'
    )
    
    # 标签
    tags = django_filters.ModelMultipleChoiceFilter(
        queryset=ContentTag.objects.filter(is_active=True),
        field_name='tags'
    )
    
    tag_slug = django_filters.CharFilter(
        method='filter_by_tag_slug'
    )
    
    # 优先级
    priority = django_filters.ChoiceFilter(
        choices=Content.PRIORITY_CHOICES,
        field_name='priority'
    )
    
    # 是否推荐
    is_featured = django_filters.BooleanFilter(
        field_name='is_featured'
    )
    
    # 是否置顶
    is_pinned = django_filters.BooleanFilter(
        field_name='is_pinned'
    )
    
    # 日期范围
    publish_date_after = django_filters.DateTimeFilter(
        field_name='publish_date',
        lookup_expr='gte'
    )
    
    publish_date_before = django_filters.DateTimeFilter(
        field_name='publish_date',
        lookup_expr='lte'
    )
    
    # 作者
    author = django_filters.CharFilter(
        field_name='author__username',
        lookup_expr='icontains'
    )
    
    # 搜索关键词
    search = django_filters.CharFilter(
        method='filter_by_search'
    )
    
    class Meta:
        model = Content
        fields = [
            'content_type', 'category', 'category_slug', 'tags', 'tag_slug',
            'priority', 'is_featured', 'is_pinned', 'publish_date_after',
            'publish_date_before', 'author', 'search'
        ]
    
    def filter_by_tag_slug(self, queryset, name, value):
        """根据标签slug过滤"""
        return queryset.filter(tags__slug=value)
    
    def filter_by_search(self, queryset, name, value):
        """搜索过滤"""
        if not value:
            return queryset
        
        return queryset.filter(
            Q(title__icontains=value) |
            Q(subtitle__icontains=value) |
            Q(excerpt__icontains=value) |
            Q(content__icontains=value) |
            Q(tags__name__icontains=value)
        ).distinct()
