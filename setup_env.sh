#!/bin/bash
# 项目环境设置脚本

PROJECT_ROOT="/www/wwwroot/csgoskins.com.cn"
VENV_PATH="$PROJECT_ROOT/server/venv"

echo "🔧 设置项目环境..."

# 检查虚拟环境是否存在
if [ ! -d "$VENV_PATH" ]; then
    echo "❌ 错误: 虚拟环境不存在于 $VENV_PATH"
    exit 1
fi

cd "$PROJECT_ROOT"

# 创建Python相关符号链接
echo "📎 创建Python符号链接..."
ln -sf server/venv/bin/python python
ln -sf server/venv/bin/python3 python3
ln -sf server/venv/bin/pip pip
ln -sf server/venv/bin/pip3 pip3

# 创建Django管理命令的快捷方式
echo "📎 创建Django管理命令快捷方式..."
cat > manage << 'EOF'
#!/bin/bash
cd /www/wwwroot/csgoskins.com.cn/server
source venv/bin/activate
python manage.py "$@"
EOF
chmod +x manage

# 创建项目激活脚本
echo "📎 创建项目激活脚本..."
cat > activate << 'EOF'
#!/bin/bash
# 激活项目环境
PROJECT_ROOT="/www/wwwroot/csgoskins.com.cn"
cd "$PROJECT_ROOT/server"
source venv/bin/activate

echo "✅ 项目环境已激活"
echo "📍 项目根目录: $PROJECT_ROOT"
echo "📁 当前目录: $(pwd)"
echo "🐍 Python版本: $(python --version)"
echo "📦 虚拟环境: $VIRTUAL_ENV"
echo ""
echo "💡 可用命令:"
echo "  ./python    - 使用项目Python"
echo "  ./pip       - 使用项目pip"
echo "  ./manage    - Django管理命令"
echo ""

# 如果有参数，执行命令
if [ $# -gt 0 ]; then
    echo "🚀 执行命令: $@"
    exec "$@"
else
    # 启动新的bash会话
    exec bash
fi
EOF
chmod +x activate

# 验证设置
echo ""
echo "✅ 环境设置完成！"
echo ""
echo "📊 验证结果:"
echo "  系统Python: $(which python) -> $(/usr/bin/python --version 2>&1)"
echo "  项目Python: $PROJECT_ROOT/python -> $(./python --version)"
echo "  项目pip: $PROJECT_ROOT/pip -> $(./pip --version | head -1)"
echo ""
echo "🚀 使用方法:"
echo "  1. 直接使用: ./python --version"
echo "  2. 激活环境: ./activate"
echo "  3. Django命令: ./manage check"
echo "  4. 进入server目录: cd server && source venv/bin/activate"
echo ""
