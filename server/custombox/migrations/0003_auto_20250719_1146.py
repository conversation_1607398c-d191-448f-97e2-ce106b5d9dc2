# Generated by Django 3.2.25 on 2025-07-19 03:46

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('custombox', '0002_auto_20250718_1647'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='custombox',
            options={'verbose_name': '自定义箱子', 'verbose_name_plural': 'CustomBox'},
        ),
        migrations.AlterModelOptions(
            name='customboxcover',
            options={'verbose_name': '自定义箱子封面', 'verbose_name_plural': 'CustomBoxCover'},
        ),
        migrations.AlterModelOptions(
            name='customboxitem',
            options={'verbose_name': '自定义箱子物品', 'verbose_name_plural': 'CustomBoxItem'},
        ),
        migrations.AlterModelOptions(
            name='customboxiteminfo',
            options={'verbose_name': '自定义箱子物品信息', 'verbose_name_plural': 'CustomBoxItemInfo'},
        ),
        migrations.AlterModelOptions(
            name='customboxrecord',
            options={'verbose_name': '箱子记录', 'verbose_name_plural': 'Case Record'},
        ),
        migrations.AlterModelOptions(
            name='customdropitem',
            options={'verbose_name': '自定义掉落物品', 'verbose_name_plural': 'CustomDropItem'},
        ),
        migrations.AlterField(
            model_name='custombox',
            name='user',
            field=models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='custom_box', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
        migrations.AlterField(
            model_name='customboxrecord',
            name='user',
            field=models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='custombox_records', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
    ]
