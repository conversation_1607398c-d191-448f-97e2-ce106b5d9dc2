# Monitor 监控模块分析文档

## 模块概述

Monitor模块是平台的实时监控系统，主要用于管理员监控平台运营状况。该模块提供了实时统计数据展示、在线用户数量监控、业务指标追踪等功能，通过WebSocket实现数据的实时推送，为运营决策提供数据支撑。

## 目录结构

```
monitor/
├── __init__.py
├── apps.py                  # 应用配置
├── business.py              # 核心业务逻辑
├── interfaces.py            # 接口定义
├── model_signals.py         # 数据库信号处理
├── urls.py                  # URL路由配置
├── views.py                 # API视图
└── templates/
    └── admin/
        └── monitor.html     # 监控面板前端页面
```

**注意：** Monitor模块没有独立的models.py，数据来源于其他模块的统计接口。

## 功能特性

### 1. 实时数据监控
- **在线用户数量**：基于配置的基数和时间段系数动态计算
- **用户总数**：平台注册用户总数
- **开箱次数**：累计开箱操作统计
- **对战次数**：累计对战游戏统计

### 2. 历史数据统计
- **每日新增用户**：用户注册趋势分析
- **每日充值金额**：收入趋势监控
- **每日开箱金额**：开箱业务统计
- **每日取回物品金额**：提现业务统计

### 3. 实时数据推送
- 基于WebSocket的实时数据推送
- 定时更新机制（3秒刷新监控数据，10秒更新在线人数）
- 多频道数据分发

### 4. 时间段智能调节
- 根据不同时间段调整在线人数显示
- 模拟真实用户活跃度变化

## API 接口分析

### 1. 监控页面访问
**路径：** `GET /api/monitor/`

**功能：** 访问监控面板页面

**权限：** 仅限超级管理员

**实现：**
```python
def get(self, request, *args, **kwargs):
    if not request.user.is_superuser or not request.user.is_staff:
        return redirect('/system/')
    return super().get(request, *args, **kwargs)
```

### 2. 获取监控数据
**路径：** `GET /api/monitor/data/`

**功能：** 获取监控统计数据

**权限：** 无权限限制（AllowAny）

**参数：**
- `num`: 获取最近N天的数据（默认30天）

**响应格式：**
```json
{
    "code": 0,
    "body": {
        "stats": {
            "user_number": 10000,
            "online_number": 500,
            "case_number": 50000,
            "battle_number": 20000
        },
        "case_records": [
            {
                "date": "2025-06-19",
                "count": 1500,
                "amount": 15000.0
            }
        ]
    },
    "message": "Succeed"
}
```

## 核心业务逻辑分析

### 1. 实时统计数据生成
```python
def generate_monitor_stats():
    """生成监控统计数据"""
    # 获取基础配置数据
    users_number = get_users_base_count()                    # 用户基数
    online_users_base_count = get_online_users_base_count()  # 在线用户基数
    open_count = get_open_base_count()                       # 开箱基数
    pk_number = get_pk_base_count()                          # 对战基数

    # 根据时间段调整在线人数
    time_period_multiplier = get_time_period(time.time())
    online_number = random.randint(
        int(online_users_base_count * time_period_multiplier * 0.8),
        int(online_users_base_count * time_period_multiplier)
    )

    stats = {
        'user_number': users_number,
        'online_number': online_number,
        'case_number': open_count,
        'battle_number': pk_number,
    }
    return stats
```

### 2. 时间段系数计算
```python
def get_time_period(timestamp):
    """根据时间段返回活跃度系数"""
    beijing_tz = pytz.timezone('Asia/Shanghai')
    t = time.gmtime(timestamp - 8*60*60)  # 转换为UTC时间
    hour = t.tm_hour

    # 不同时间段的活跃度系数
    time_periods = {
        (0, 2): 2.5,    # 凌晨0-2点：高活跃（夜猫子）
        (2, 4): 2,      # 凌晨2-4点：较高活跃
        (4, 6): 1.3,    # 凌晨4-6点：低活跃
        (6, 8): 1.2,    # 早晨6-8点：低活跃
        (8, 10): 1.5,   # 上午8-10点：中等活跃
        (10, 12): 2.2,  # 上午10-12点：高活跃
        (12, 14): 2,    # 中午12-14点：较高活跃
        (14, 16): 2.2,  # 下午14-16点：高活跃
        (16, 18): 2.5,  # 下午16-18点：最高活跃
        (18, 20): 1.8,  # 晚上18-20点：较高活跃
        (20, 22): 1.5,  # 晚上20-22点：中等活跃
        (22, 24): 2     # 晚上22-24点：较高活跃
    }

    for (start_hour, end_hour), value in time_periods.items():
        if start_hour <= hour < end_hour:
            return value
    return 2
```

### 3. WebSocket实时推送
```python
def ws_send_monitor(data, action, channel='monitor'):
    """发送监控数据到WebSocket"""
    if data:
        r = get_redis_connection('default')
        rt_msg = [channel, action, data]
        r.publish(_box_redis_channel_key, json.dumps(rt_msg))

def update_monitor_data():
    """定时更新监控数据"""
    while True:
        try:
            stats = generate_monitor_stats()
            ws_send_monitor(stats, 'update', 'monitor')
        except Exception as e:
            _logger.error('update monitor data error: {}'.format(e))
        finally:
            time.sleep(3)  # 每3秒更新一次

def update_online_number():
    """定时更新在线人数"""
    while True:
        try:
            online_users_base_count = get_online_users_base_count()
            time_period_multiplier = get_time_period(time.time())
            
            online_number = random.randint(
                int(online_users_base_count * time_period_multiplier * 0.8),
                int(online_users_base_count * time_period_multiplier)
            )
            
            ws_send_monitor(online_number, 'update', 'online_number')
        except Exception as e:
            _logger.error('update monitor data error: {}'.format(e))
        finally:
            time.sleep(10)  # 每10秒更新一次
```

### 4. 监控面板前端
```javascript
// Vue.js实现的前端监控面板
var table = new Vue({
    el: '#table',
    delimiters: ['^{', '}'],
    data: {
        tableData: {}
    },
    created: function () {
        this.loadData();
    },
    methods: {
        loadData: function () {
            let that = this;
            let tUrl = '/super/monitor/data/';
            axios.get(tUrl).then(function (resp) {
                let result = resp.data;
                if (result.code === 0) {
                    that.tableData = result.body;
                } else {
                    that.$message({type: 'warning', message: result.message})
                }
            });
        }
    }
});
```

## 数据来源分析

### 1. 配置数据来源
Monitor模块的基础数据来自site_config配置表：

```python
# 来自sitecfg模块的配置接口
get_users_base_count()           # 用户基数配置
get_online_users_base_count()    # 在线用户基数配置
get_open_base_count()            # 开箱基数配置
get_pk_base_count()              # 对战基数配置
```

### 2. 统计数据来源
历史统计数据来自各业务模块：

```python
# 来自authentication模块
get_user_statistics_day_data()   # 每日用户注册统计

# 来自box模块
get_case_statistics_day_data()   # 每日开箱统计
get_case_records_data()          # 开箱记录统计

# 来自charge模块（推测）
# 每日充值统计

# 来自package模块（推测）
# 每日提现统计
```

### 3. 实时数据来源
```python
get_online_number()              # Redis中的在线用户数
get_total_open_count()           # 累计开箱次数
```

## 存在的问题与风险

### 1. 数据真实性问题

**问题描述：**
- 在线人数基于配置和随机数生成，非真实统计
- 基础数据来自配置表，可能与实际业务数据不符
- 缺少真实的用户活跃度统计
- 时间段系数是硬编码，缺乏数据支撑

**影响：**
- 监控数据失去参考价值
- 运营决策基于错误数据
- 可能误导业务发展方向

### 2. 权限控制缺失

**问题描述：**
- 监控数据接口无权限限制（AllowAny）
- 敏感的业务数据可能泄露
- 缺少访问日志记录
- 没有IP白名单限制

**影响：**
- 业务数据安全风险
- 竞争对手可能获取业务情报
- 合规性问题

### 3. 功能局限性

**问题描述：**
- 缺少告警机制
- 没有历史趋势分析
- 缺少异常检测
- 监控指标过于简单

**影响：**
- 无法及时发现业务异常
- 运营分析能力不足
- 故障响应滞后

### 4. 技术架构问题

**问题描述：**
- 定时任务使用threading，不够稳定
- WebSocket推送机制简单
- 缺少数据持久化
- 没有监控自身的健康检查

**影响：**
- 系统稳定性风险
- 数据丢失风险
- 难以排查问题

## 改进建议

### 1. 真实数据统计系统

#### 实时在线用户统计
```python
class RealTimeUserTracker:
    """真实在线用户追踪"""
    
    def __init__(self):
        self.redis_client = redis.Redis()
        self.online_timeout = 300  # 5分钟无活动视为离线
    
    def track_user_activity(self, user_id, activity_type='page_view'):
        """追踪用户活动"""
        timestamp = int(time.time())
        
        # 更新用户最后活动时间
        self.redis_client.hset(
            'user_activity',
            user_id,
            timestamp
        )
        
        # 记录活动类型
        self.redis_client.lpush(
            f'user_activity_log:{user_id}',
            json.dumps({
                'type': activity_type,
                'timestamp': timestamp
            })
        )
        
        # 保留最近100条活动记录
        self.redis_client.ltrim(f'user_activity_log:{user_id}', 0, 99)
    
    def get_online_users_count(self):
        """获取真实在线用户数"""
        current_time = int(time.time())
        cutoff_time = current_time - self.online_timeout
        
        # 获取所有用户活动时间
        user_activities = self.redis_client.hgetall('user_activity')
        
        online_count = 0
        for user_id, last_activity in user_activities.items():
            if int(last_activity) >= cutoff_time:
                online_count += 1
        
        return online_count
    
    def get_user_activity_distribution(self):
        """获取用户活动分布"""
        current_time = int(time.time())
        
        # 统计不同时间段的活动用户
        time_ranges = {
            '1min': current_time - 60,
            '5min': current_time - 300,
            '15min': current_time - 900,
            '1hour': current_time - 3600
        }
        
        distribution = {}
        user_activities = self.redis_client.hgetall('user_activity')
        
        for range_name, cutoff_time in time_ranges.items():
            count = sum(
                1 for last_activity in user_activities.values()
                if int(last_activity) >= cutoff_time
            )
            distribution[range_name] = count
        
        return distribution
```

#### 业务指标真实统计
```python
class BusinessMetricsCollector:
    """业务指标收集器"""
    
    def __init__(self):
        self.redis_client = redis.Redis()
    
    def collect_real_time_metrics(self):
        """收集实时业务指标"""
        current_time = timezone.now()
        today = current_time.date()
        
        # 今日用户注册数
        today_users = AuthUser.objects.filter(
            date_joined__date=today
        ).count()
        
        # 今日充值总额
        today_charges = ChargeRecord.objects.filter(
            create_time__date=today,
            state=1  # 成功状态
        ).aggregate(total=Sum('amount'))['total'] or 0
        
        # 今日开箱次数和金额
        today_cases = CaseRecord.objects.filter(
            create_time__date=today
        )
        case_count = today_cases.count()
        case_amount = today_cases.aggregate(total=Sum('amount'))['total'] or 0
        
        # 今日对战次数
        today_battles = BattleGame.objects.filter(
            create_time__date=today
        ).count()
        
        # 当前活跃游戏
        active_battles = BattleGame.objects.filter(
            state__in=[GameState.Waiting.value, GameState.Playing.value]
        ).count()
        
        metrics = {
            'timestamp': current_time.timestamp(),
            'daily_new_users': today_users,
            'daily_charge_amount': float(today_charges),
            'daily_case_count': case_count,
            'daily_case_amount': float(case_amount),
            'daily_battle_count': today_battles,
            'active_battles': active_battles,
            'online_users': self.get_real_online_count()
        }
        
        # 存储到Redis，保留24小时数据
        key = f"business_metrics:{today.strftime('%Y%m%d')}"
        self.redis_client.hset(key, current_time.strftime('%H%M'), json.dumps(metrics))
        self.redis_client.expire(key, 86400)  # 24小时过期
        
        return metrics
    
    def get_historical_trends(self, days=7):
        """获取历史趋势数据"""
        end_date = timezone.now().date()
        trends = []
        
        for i in range(days):
            date = end_date - timedelta(days=i)
            
            # 用户注册趋势
            daily_users = AuthUser.objects.filter(
                date_joined__date=date
            ).count()
            
            # 充值趋势
            daily_charges = ChargeRecord.objects.filter(
                create_time__date=date,
                state=1
            ).aggregate(total=Sum('amount'))['total'] or 0
            
            # 开箱趋势
            daily_cases = CaseRecord.objects.filter(
                create_time__date=date
            ).aggregate(
                count=Count('id'),
                amount=Sum('amount')
            )
            
            trends.append({
                'date': date.strftime('%Y-%m-%d'),
                'new_users': daily_users,
                'charge_amount': float(daily_charges),
                'case_count': daily_cases['count'] or 0,
                'case_amount': float(daily_cases['amount'] or 0)
            })
        
        return list(reversed(trends))
```

### 2. 完善的权限控制

#### 监控权限管理
```python
class MonitorPermissionMixin:
    """监控权限混合类"""
    
    def dispatch(self, request, *args, **kwargs):
        # 检查用户权限
        if not self.check_monitor_permission(request.user):
            return JsonResponse({
                'code': 403,
                'message': '无权限访问监控数据'
            }, status=403)
        
        # 记录访问日志
        self.log_monitor_access(request)
        
        return super().dispatch(request, *args, **kwargs)
    
    def check_monitor_permission(self, user):
        """检查监控权限"""
        # 超级管理员
        if user.is_superuser:
            return True
        
        # 检查特定监控权限
        if hasattr(user, 'monitor_permissions'):
            return user.monitor_permissions.filter(
                permission_type='view_dashboard',
                is_active=True
            ).exists()
        
        return False
    
    def log_monitor_access(self, request):
        """记录监控访问日志"""
        MonitorAccessLog.objects.create(
            user=request.user,
            ip_address=self.get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            endpoint=request.path,
            method=request.method
        )

class MonitorAccessLog(models.Model):
    """监控访问日志"""
    user = models.ForeignKey(USER_MODEL, on_delete=models.CASCADE)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField()
    endpoint = models.CharField(max_length=255)
    method = models.CharField(max_length=10)
    access_time = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        verbose_name = '监控访问日志'
```

### 3. 智能告警系统

#### 业务异常检测
```python
class MonitorAlertSystem:
    """监控告警系统"""
    
    def __init__(self):
        self.alert_rules = self.load_alert_rules()
        self.notification_service = NotificationService()
    
    def check_business_alerts(self, metrics):
        """检查业务告警"""
        alerts = []
        
        for rule in self.alert_rules:
            if self.evaluate_rule(rule, metrics):
                alert = self.create_alert(rule, metrics)
                alerts.append(alert)
                self.send_alert_notification(alert)
        
        return alerts
    
    def evaluate_rule(self, rule, metrics):
        """评估告警规则"""
        metric_value = metrics.get(rule['metric'])
        threshold = rule['threshold']
        operator = rule['operator']
        
        if operator == 'gt':
            return metric_value > threshold
        elif operator == 'lt':
            return metric_value < threshold
        elif operator == 'eq':
            return metric_value == threshold
        elif operator == 'ne':
            return metric_value != threshold
        
        return False
    
    def load_alert_rules(self):
        """加载告警规则"""
        return [
            {
                'name': '在线用户数异常低',
                'metric': 'online_users',
                'operator': 'lt',
                'threshold': 10,
                'severity': 'warning',
                'description': '在线用户数量异常偏低'
            },
            {
                'name': '充值金额异常高',
                'metric': 'daily_charge_amount',
                'operator': 'gt',
                'threshold': 100000,
                'severity': 'info',
                'description': '单日充值金额异常偏高，需要关注'
            },
            {
                'name': '新用户注册停滞',
                'metric': 'daily_new_users',
                'operator': 'eq',
                'threshold': 0,
                'severity': 'critical',
                'description': '今日无新用户注册，需要紧急处理'
            }
        ]
    
    def create_alert(self, rule, metrics):
        """创建告警"""
        return MonitorAlert.objects.create(
            rule_name=rule['name'],
            metric_name=rule['metric'],
            metric_value=metrics.get(rule['metric']),
            threshold_value=rule['threshold'],
            severity=rule['severity'],
            description=rule['description'],
            alert_time=timezone.now()
        )

class MonitorAlert(models.Model):
    """监控告警记录"""
    rule_name = models.CharField(max_length=255)
    metric_name = models.CharField(max_length=100)
    metric_value = models.FloatField()
    threshold_value = models.FloatField()
    severity = models.CharField(max_length=20, choices=[
        ('info', '信息'),
        ('warning', '警告'),
        ('critical', '严重'),
    ])
    description = models.TextField()
    alert_time = models.DateTimeField()
    resolved_time = models.DateTimeField(null=True)
    is_resolved = models.BooleanField(default=False)
    
    class Meta:
        verbose_name = '监控告警'
```

### 4. 高级监控面板

#### 实时数据可视化
```python
class AdvancedMonitorView(APIView):
    """高级监控视图"""
    
    def get(self, request):
        """获取完整监控数据"""
        # 实时指标
        real_time_metrics = BusinessMetricsCollector().collect_real_time_metrics()
        
        # 历史趋势
        historical_trends = BusinessMetricsCollector().get_historical_trends(7)
        
        # 用户活动分布
        activity_distribution = RealTimeUserTracker().get_user_activity_distribution()
        
        # 系统健康状态
        system_health = self.get_system_health()
        
        # 告警信息
        active_alerts = MonitorAlert.objects.filter(
            is_resolved=False,
            alert_time__gte=timezone.now() - timedelta(hours=24)
        ).order_by('-alert_time')
        
        return Response({
            'real_time_metrics': real_time_metrics,
            'historical_trends': historical_trends,
            'activity_distribution': activity_distribution,
            'system_health': system_health,
            'active_alerts': MonitorAlertSerializer(active_alerts, many=True).data
        })
    
    def get_system_health(self):
        """获取系统健康状态"""
        # 数据库连接状态
        db_status = self.check_database_health()
        
        # Redis连接状态  
        redis_status = self.check_redis_health()
        
        # WebSocket服务状态
        websocket_status = self.check_websocket_health()
        
        # 后台任务状态
        task_status = self.check_background_tasks()
        
        return {
            'database': db_status,
            'redis': redis_status,
            'websocket': websocket_status,
            'background_tasks': task_status,
            'overall_status': 'healthy' if all([
                db_status['healthy'],
                redis_status['healthy'],
                websocket_status['healthy'],
                task_status['healthy']
            ]) else 'unhealthy'
        }
```

## 监控指标体系

### 核心业务指标
```python
MONITOR_METRICS = {
    'user_metrics': {
        'total_users': '总用户数',
        'daily_new_users': '日新增用户',
        'online_users': '在线用户数',
        'active_users_1d': '日活跃用户',
        'active_users_7d': '周活跃用户',
        'user_retention_rate': '用户留存率'
    },
    'financial_metrics': {
        'daily_revenue': '日收入',
        'daily_charge_amount': '日充值金额',
        'daily_withdrawal_amount': '日提现金额',
        'arpu': '平均用户收入',
        'conversion_rate': '付费转化率'
    },
    'game_metrics': {
        'daily_case_opens': '日开箱次数',
        'daily_battles': '日对战次数',
        'active_games': '进行中游戏',
        'game_completion_rate': '游戏完成率'
    },
    'system_metrics': {
        'response_time': '响应时间',
        'error_rate': '错误率',
        'server_load': '服务器负载',
        'database_performance': '数据库性能'
    }
}
```

## 总结

Monitor模块当前实现了基础的监控功能，但存在数据真实性、权限控制、功能完整性等方面的显著问题。通过实施真实数据统计、完善权限管理、建立智能告警系统和升级监控面板等改进措施，可以将其打造成一个功能完善、数据准确、安全可靠的运营监控平台。

这些改进将显著提升运营团队的数据洞察能力，帮助及时发现和解决业务问题，为平台的健康发展提供有力支撑。

## 接口清单

| 接口名称 | 方法 | 路径 | 状态 | 功能描述 |
|---------|------|------|------|----------|
| 监控面板 | GET | `/api/monitor/` | 正常 | 访问监控面板页面 |
| 获取监控数据 | GET | `/api/monitor/data/` | 正常 | 获取统计数据 |

**注：** 建议增加实时指标、历史趋势、告警管理、系统健康等专业监控接口。
