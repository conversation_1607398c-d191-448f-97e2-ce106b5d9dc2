# automatically generated by the FlatBuffers compiler, do not modify

# namespace: proto

import flatbuffers

class Abort(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAsAbort(cls, buf, offset):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = Abort()
        x.Init(buf, n + offset)
        return x

    # Abort
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # Abort
    def Reason(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

    # Abort
    def Message(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

def AbortStart(builder): builder.StartObject(2)
def AbortAddReason(builder, reason): builder.PrependUOffsetTRelativeSlot(0, flatbuffers.number_types.UOffsetTFlags.py_type(reason), 0)
def AbortAddMessage(builder, message): builder.PrependUOffsetTRelativeSlot(1, flatbuffers.number_types.UOffsetTFlags.py_type(message), 0)
def AbortEnd(builder): return builder.EndObject()
