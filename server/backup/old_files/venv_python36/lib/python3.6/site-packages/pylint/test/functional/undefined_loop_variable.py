# pylint: disable=missing-docstring

def do_stuff(some_random_list):
    for var in some_random_list:
        pass
    return var # [undefined-loop-variable]


def do_else(some_random_list):
    for var in some_random_list:
        if var == 42:
            break
    else:
        var = 84
    return var

__revision__ = 'yo'

TEST_LC = [C for C in __revision__ if C.isalpha()]
B = [B for B in  __revision__ if B.isalpha()]
VAR2 = B # nor this one

for var1, var2 in TEST_LC:
    var1 = var2 + 4
VAR3 = var1 # [undefined-loop-variable]

for note in __revision__:
    note.something()
for line in __revision__:
    for note in line:
        A = note.anotherthing()


for x in []:
    pass
for x in range(3):
    VAR5 = (lambda: x)()
