#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI智能体文档验证脚本
验证AI智能体相关文档的完整性和正确性
"""

import os
import sys
from pathlib import Path

def check_file_exists(file_path, description):
    """检查文件是否存在"""
    if os.path.exists(file_path):
        print(f"✅ {description}: {file_path}")
        return True
    else:
        print(f"❌ {description}: {file_path} (文件不存在)")
        return False

def check_directory_structure():
    """检查目录结构是否符合规范"""
    print("🔍 检查目录结构...")
    
    # 项目根目录
    root_dir = Path(__file__).parent.parent.parent
    
    # 必须存在的目录
    required_dirs = [
        "docs",
        "scripts/tools",
        "scripts/fixes", 
        "scripts/ckeditor",
        "scripts/maintenance",
        "scripts/monitoring",
        "scripts/tests",
        "server",
        "ui",
        "logs",
        "backup"
    ]
    
    all_good = True
    for dir_path in required_dirs:
        full_path = root_dir / dir_path
        if full_path.exists():
            print(f"✅ 目录存在: {dir_path}")
        else:
            print(f"❌ 目录缺失: {dir_path}")
            all_good = False
    
    return all_good

def check_ai_documents():
    """检查AI智能体文档是否存在"""
    print("\n📚 检查AI智能体文档...")
    
    root_dir = Path(__file__).parent.parent.parent
    docs_dir = root_dir / "docs"
    
    # AI智能体相关文档
    ai_docs = [
        ("AI_AGENT_RULES.md", "AI智能体操作规则"),
        ("AI_QUICK_REFERENCE.md", "AI快速参考卡片"),
        ("SYSTEM_CONFIG.md", "系统配置信息"),
        ("FILE_ORGANIZATION.md", "文件组织规范")
    ]
    
    all_good = True
    for filename, description in ai_docs:
        file_path = docs_dir / filename
        if not check_file_exists(file_path, description):
            all_good = False
    
    return all_good

def check_key_config_files():
    """检查关键配置文件"""
    print("\n⚙️ 检查关键配置文件...")
    
    root_dir = Path(__file__).parent.parent.parent
    
    # 关键配置文件
    config_files = [
        ("supervisord.conf", "Supervisor配置"),
        ("docker-compose.yml", "Docker编排配置"),
        ("server/steambase/settings.py", "Django主配置"),
        ("server/requirements.txt", "Python依赖"),
        ("ui/package.json", "Node.js依赖"),
        ("ui/nuxt.config.ts", "Nuxt配置")
    ]
    
    all_good = True
    for filename, description in config_files:
        file_path = root_dir / filename
        if not check_file_exists(file_path, description):
            all_good = False
    
    return all_good

def check_forbidden_files():
    """检查是否存在不应该存在的文件"""
    print("\n🚫 检查禁止的文件位置...")
    
    root_dir = Path(__file__).parent.parent.parent
    server_dir = root_dir / "server"
    
    # 不应该在server根目录的文件模式
    forbidden_patterns = [
        "fix_*.py",
        "temp_*.py", 
        "test_*.py",
        "*.mo",  # 应该在locale目录
        "tools/",  # 应该在scripts/tools
        "scripts/"  # 应该在根目录的scripts
    ]
    
    issues_found = False
    
    if server_dir.exists():
        for item in server_dir.iterdir():
            if item.is_file():
                # 检查临时文件
                if (item.name.startswith('fix_') or 
                    item.name.startswith('temp_') or 
                    item.name.startswith('test_')):
                    print(f"⚠️ 发现不规范文件: {item}")
                    print(f"   建议移动到: scripts/tools/ 或 scripts/fixes/")
                    issues_found = True
                
                # 检查.mo文件
                if item.name.endswith('.mo'):
                    print(f"⚠️ 发现错位的翻译文件: {item}")
                    print(f"   建议移动到: server/locale/zh_hans/LC_MESSAGES/")
                    issues_found = True
            
            elif item.is_dir():
                # 检查不应该存在的目录
                if item.name in ['tools', 'scripts']:
                    print(f"⚠️ 发现重复目录: {item}")
                    print(f"   建议合并到: scripts/{item.name}/")
                    issues_found = True
    
    if not issues_found:
        print("✅ 未发现不规范的文件位置")
    
    return not issues_found

def main():
    """主函数"""
    print("🤖 AI智能体文档验证工具")
    print("=" * 50)
    
    # 执行各项检查
    checks = [
        ("目录结构", check_directory_structure),
        ("AI文档", check_ai_documents), 
        ("配置文件", check_key_config_files),
        ("文件规范", check_forbidden_files)
    ]
    
    all_passed = True
    results = []
    
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
            if not result:
                all_passed = False
        except Exception as e:
            print(f"❌ {check_name}检查失败: {e}")
            results.append((check_name, False))
            all_passed = False
    
    # 输出总结
    print("\n" + "=" * 50)
    print("📊 检查结果总结:")
    
    for check_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {check_name}: {status}")
    
    if all_passed:
        print("\n🎉 所有检查都通过了！AI智能体文档配置正确。")
        return 0
    else:
        print("\n⚠️ 发现问题，请根据上述提示进行修复。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
