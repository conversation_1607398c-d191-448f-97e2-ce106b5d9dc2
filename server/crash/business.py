import hashlib
import json
import logging
import numpy
import time
import datetime

from django.conf import settings
from django.core.cache import cache
from django.core.paginator import Paginator
from django.db import connection, transaction
from django.db.models import F, Q
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from datetime import timed<PERSON>ta
from django_redis import get_redis_connection
from steambase.redis_con import get_redis

from steambase.enums import RespC<PERSON>, GameState, WinResult
from steambase.utils import is_connection_usable, ParamException, secret_generator, current_year, current_week
from sitecfg.interfaces import get_crash_percentage_discount, get_crash_run_countdown, get_crash_end_countdown
from sitecfg.interfaces import get_maintenance, get_maintenance_crash, get_crash_bet_amount_max
from crash.models import CrashGame, CrashBet, CrashPumpDay, CrashPumpMonth, CrashStatisticsDay
from crash.models import CrashStatisticsMonth, CrashWinDayRank, CrashWinWeekRank
from crash.serializers import CrashGameSerializer, CrashBetSerializer
from crash.serializers import CrashWinDayRankSerializer, CrashWinWeekRankSerializer, CrashUserBetSerializer
from authentication.models import AuthUser


_logger = logging.getLogger(__name__)
_current_crash_game_key = 'current_crash_game'
_ws_channel_key = 'ws_channel'
_crash_bet_list = 'crash_bet_list'


def ws_send_crash_game(data, action):
    if data:
        r = get_redis_connection('default')
        msg = ['crash', action, data]
        r.publish(_ws_channel_key, json.dumps(msg))


def create_new_game_hash():
    percentage = numpy.random.uniform(0, 100)
    secret = secret_generator(size=10)
    data = '{0:.13f}:{1}'.format(percentage, secret)
    md5_data = hashlib.md5(data.encode('utf-8'))
    hash_key = md5_data.hexdigest()
    ret = {
        'percentage': percentage,
        'secret': secret,
        'hash': hash_key
    }
    return ret


def create_crash_game():
    data = create_new_game_hash()
    data['crash_point'] = round(1 / (1 - data['percentage'] / 100) * get_crash_percentage_discount() / 100, 2)
    if data['crash_point'] < 1:
        data['crash_point'] = 1
    if data['crash_point'] > 999.99:
        data['crash_point'] = 999.99
    game = CrashGame.objects.create(**data)
    return game


def get_current_crash_game(create=False):
    game = None
    game_uid = cache.get(_current_crash_game_key)
    if game_uid:
        game = CrashGame.objects.filter(uid=game_uid).first()
    if create and (not game or game.state == GameState.End.value):
        game = create_crash_game()
        cache.set(_current_crash_game_key, game.uid, settings.NEVER_REDIS_TIMEOUT)
    return game


def check_current_crash_game():
    while True:
        try:
            if not is_connection_usable():
                connection.close()

            r = get_redis_connection('default')
            r_conn = get_redis()
            dt_now = timezone.now()
            run_countdown = get_crash_run_countdown()
            end_countdown = get_crash_end_countdown()
            game = get_current_crash_game(create=True)
            if not game:
                raise Exception('Create crash game fail')
            if (game.state == GameState.Initial.value and game.create_time <= dt_now - timedelta(seconds=end_countdown)
                and not get_maintenance() and not get_maintenance_crash()):
                _logger.info('Crash game ready to joinable, id:{}'.format(game.id))
                game.state = GameState.Joinable.value
                game.joinable_time = dt_now
                game.save()
                game_data = CrashGameSerializer(game, fields=('id', 'uid', 'hash', 'secret', 'state', 'joinable_time',
                                                                 'run_time', 'end_time', 'countdown', 'past')).data
                # print(game_data)
                ws_send_crash_game(game_data, 'new')
            # if game.state == GameState.Joinable.value:
                # print(game.joinable_time, dt_now - timedelta(seconds=run_countdown), game.joinable_time <= dt_now - timedelta(seconds=run_countdown))
            if game.state == GameState.Joinable.value and game.joinable_time <= dt_now - timedelta(seconds=run_countdown):
                _logger.info('Crash game ready to running, id:{}'.format(game.id))
                game.state = GameState.Running.value
                game.run_time = dt_now
                game.save()
                game_data = CrashGameSerializer(game, fields=('id', 'uid', 'hash', 'state', 'joinable_time',
                                                                 'run_time', 'end_time', 'countdown', 'past', 'crash_point')).data
                # print(game_data)
                ws_send_crash_game(game_data, 'end')
                _logger.info('Crash game ready to end, id:{}'.format(game.id))
                with transaction.atomic():
                    game.state = GameState.End.value
                    game.end_time = dt_now
                    game.save()
                time.sleep(10)
            if game.state == GameState.End.value:
                calc_crash_game(game)
                update_crash_statistics(game)
                bets = CrashBet.objects.filter(game=game)
                bets_data = CrashBetSerializer(bets, many=True,fields=('user', 'uid', 'amount', 'gid', 'out_point',
                                                                'win_amount')).data
                # print(bets_data)
                ws_send_crash_game(bets_data, 'bet_res')
                next_game = get_current_crash_game(create=True)
                _logger.info('Crash new game ready to begin, id:{}'.format(next_game.id))
        except Exception as e:
            _logger.exception(e)
        finally:
            time.sleep(1)


def calc_crash_game_winner(game, bet):
    win_amount = round(bet.amount * bet.out_point, 2)
    bet.win_result = WinResult.Win.value
    bet.win_amount = win_amount
    bet.save()
    game.win_amount += win_amount
    game.save()
    bet.user.update_balance(win_amount, _('Crash game win'))
    update_crash_rank(bet.user, bet.win_amount - bet.amount)


def calc_crash_game(game):
    bets = game.bets.filter(game=game, win_result=WinResult.NotEnd.value)
    for bet in bets:
        if bet.win_result == WinResult.NotEnd.value:
            if 1 < bet.out_point <= game.crash_point:
                calc_crash_game_winner(game, bet)
            else:
                bet.win_result = WinResult.Lose.value
                bet.save()
    game.save()


def update_crash_statistics(game):
    pump = game.pump_amount
    if pump > 0:
        CrashPumpDay.update_amount(pump)
        CrashPumpMonth.update_amount(pump)


    test_total_income = 0
    test_total_bet = 0
    for bet in game.bets.all():
        if bet.user.is_superuser:
            _income = bet.amount - bet.win_amount
            CrashStatisticsDay.update_test_amount(_income)
            CrashStatisticsMonth.update_test_amount(_income)
            test_total_income += _income

    CrashStatisticsDay.update_journal(game.total_amount - test_total_bet)
    CrashStatisticsMonth.update_journal(game.total_amount - test_total_bet)
    income = game.total_amount - game.win_amount - test_total_income
    if income:
        CrashStatisticsDay.update_amount(income)
        CrashStatisticsMonth.update_amount(income)

        CrashStatisticsDay.update_win_amount(game.win_amount)
        CrashStatisticsMonth.update_win_amount(game.win_amount)

        CrashStatisticsDay.update_lose_amount(income)
        CrashStatisticsMonth.update_lose_amount(income)
    


def update_crash_rank(user, amount):
    if amount > 0:
        with transaction.atomic():
            CrashWinDayRank.update_amount(user, amount)
            CrashWinWeekRank.update_amount(user, amount)


def bet_crash_game(user, amount, out_point):
    if amount <= 0:
        return RespCode.InvalidParams.value, _('Invalid bet amount')
    if out_point < 1.01:
        out_point = 1.01
    if out_point > 999.99:
        return RespCode.InvalidParams.value, _('Auto crashout should between 1.01~999.99')
    amount = round(amount, 2)
    out_point = round(out_point, 2)

    try:
        bet_data = None
        with transaction.atomic():
            game = get_current_crash_game()
            if not game:
                raise ParamException(_('Invalid game'))
            if game.state != GameState.Joinable.value:
                raise ParamException(_('Game is running, please wait on'))
            # if CrashBet.objects.filter(user=user, game=game).exists():
            #     raise ParamException(_('Already joined'))
            if amount > get_crash_bet_amount_max():
                raise ParamException(_('Over bet max amount'))

            _logger.info('steamer {} join crash game {}'.format(user, game.id))
            deposit_data = {
                # 'amount': amount,
                'user': user,
                'out_point': out_point,
                'game': game
            }
            bet = CrashBet.objects.filter(**deposit_data).first()
            if bet:
                bet.amount = round(bet.amount + amount, 2)
                bet.save()
            else:
                deposit_data['amount'] = amount
                serializer = CrashBetSerializer(data=deposit_data)
                if not serializer.is_valid():
                    raise ParamException(serializer.errors)
                bet = serializer.save(**deposit_data)
            bet_data = CrashBetSerializer(bet, fields=('user', 'uid', 'amount', 'gid', 'out_point')).data
            user.update_balance(-amount, _('Crash game bet'))
            CrashGame.objects.filter(uid=game.uid).update(total_amount=F('total_amount') + amount)

        if bet_data:
            ws_send_crash_game(bet_data, 'bet')
        resp = {
            'uid': bet_data['uid']
        }
        return RespCode.Succeed.value, resp
    except ParamException as e:
        return RespCode.InvalidParams.value, str(e)


def out_crash_game(user, out_point):
    out_point = round(out_point, 2)
    game = get_current_crash_game()
    if not game or game.state != GameState.Running.value:
        return RespCode.InvalidParams.value, _('Game is not running')
    if not out_point or out_point < 1.01:
        return RespCode.InvalidParams.value, _('Invalid out point')

    with transaction.atomic():
        bet = CrashBet.objects.select_for_update().filter(
            Q(game=game, user=user, out_point=0, win_result=WinResult.NotEnd.value) &
            (Q(auto_out_point=0) | Q(auto_out_point__gte=out_point))).first()
        if not bet:
            return RespCode.InvalidParams.value, _('Invalid operation')
        bet.auto_out_point = out_point
        bet.save()
    redis_cache_point(game.uid, bet.uid, out_point)
    resp = {
        'uid': bet.uid
    }
    return RespCode.Succeed.value, resp


def redis_cache_point(gid, uid, point):
    r = get_redis_connection('default')
    key = '{}:{}'.format(gid, uid)
    r.set(key, point, 60*60)


def get_current_crash_game_data(fields):
    game = get_current_crash_game()
    game_data = CrashGameSerializer(game, fields=fields).data
    bets = game.bets.all()
    bets_data = CrashBetSerializer(bets, many=True, fields=('uid', 'user', 'amount', 'out_point', 'team', 'win_amount', 'win_result',
                                                            'create_time')).data
    game_data['bets'] = bets_data
    return RespCode.Succeed.value, game_data


def get_current_crash_bet(user):
    game = get_current_crash_game()
    bet = CrashBet.objects.filter(user=user, game=game)
    data = CrashBetSerializer(bet, exclude=('game',), many=True).data
    resp = data
    return RespCode.Succeed.value, resp


def get_crash_bet(user, query, fields, page, page_size):
    queryset = CrashBet.objects.filter(user=user, game__state=GameState.End.value, **query).order_by('-create_time')
    paginator = Paginator(queryset, page_size)
    items = paginator.page(page)
    items_data = CrashUserBetSerializer(items, many=True, fields=fields).data
    resp = {
        'items': items_data,
        'total': paginator.count,
        'page': page,
        'limit': page_size
    }
    return RespCode.Succeed.value, resp


def get_crash_game_history(query, fields, page, page_size):
    queryset = CrashGame.objects.filter(state=GameState.End.value, **query).order_by('-create_time')
    paginator = Paginator(queryset, page_size)
    items = paginator.page(page)
    items_data = CrashGameSerializer(items, many=True, fields=fields).data
    resp = {
        'items': items_data,
        'total': paginator.count,
        'page': page,
        'limit': page_size
    }
    return RespCode.Succeed.value, resp


def get_crash_day_rank(query, fields, page, page_size, count):
    today = timezone.localdate()
    queryset = CrashWinDayRank.objects.filter(date=today, **query).order_by('-amount')[:count]
    paginator = Paginator(queryset, page_size)
    items = paginator.page(page)
    items_data = CrashWinDayRankSerializer(items, many=True, fields=fields).data
    resp = {
        'items': items_data,
        'total': paginator.count,
        'page': page,
        'limit': page_size
    }
    return RespCode.Succeed.value, resp


def get_crash_week_rank(query, fields, page, page_size, count):
    year = current_year()
    week = current_week()
    queryset = CrashWinWeekRank.objects.filter(year=year, week=week, **query).order_by('-amount')[:count]
    paginator = Paginator(queryset, page_size)
    items = paginator.page(page)
    items_data = CrashWinWeekRankSerializer(items, many=True, fields=fields).data
    resp = {
        'items': items_data,
        'total': paginator.count,
        'page': page,
        'limit': page_size
    }
    return RespCode.Succeed.value, resp


def verify_crash(percentage, secret):
    percentage = float(percentage)
    data = '{0:.13f}:{1}'.format(percentage, secret)
    md5_data = hashlib.md5(data.encode('utf-8'))
    hash_key = md5_data.hexdigest()
    crash_point = round(1 / (1 - percentage / 100) * get_crash_percentage_discount() / 100, 2)
    if crash_point < 1:
        crash_point = 1
    if crash_point > 999.99:
        crash_point = 999.99
    resp = {
        'hash_key': hash_key,
        'crash_point': crash_point
    }
    return RespCode.Succeed.value, resp
