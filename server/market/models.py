from django.conf import settings
from django.db import models
from django.utils.translation import gettext_lazy as _

from package.models import ItemInfo
from steambase.models import ModelBase

USER_MODELL = settings.AUTH_USER_MODELL


class MarketItem(ModelBase):

    item_info = models.OneToOneField(ItemInfo, on_delete=models.CASCADE)
    rarity_cn = models.CharField(_('item rarity cn'), max_length=128, null=True, blank=True, default=None)
    exterior = models.CharField(_('item exterior cn'), max_length=128, null=True, blank=True, default=None)
    dark_gold = models.BooleanField(_('item dark gold'), default=False)

    class Meta:
        verbose_name = _('MarketItem')
        verbose_name_plural = _('MarketItem')

    def __str__(self):
        return str(self.item_info.market_hash_name)

