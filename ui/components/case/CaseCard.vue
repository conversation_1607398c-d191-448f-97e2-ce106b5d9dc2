<template>
  <div class="case-card-container relative overflow-hidden">
    <div
      v-if="displayCases.length > 0"
      class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4"
    >
      <div
        v-for="(caseItem, index) in displayCases"
        :key="caseItem.id || index"
        class="case-card relative overflow-hidden transition-all group cursor-pointer border shadow-xl"
        :class="getSectionCardClass()"
        @click="handleCaseClick(caseItem)"
      >
        <!-- 图片区 -->
        <div
          class="relative aspect-square overflow-hidden flex items-center justify-center rounded-t-xl"
        >
          <!-- 装饰光晕效果 - 根据区块类型调整颜色 -->
          <div
            class="absolute inset-0 z-0"
            :class="getSectionBackgroundClass()"
          ></div>

          <!-- 图片 -->
          <img
            :src="caseItem.cover || caseItem.image"
            :alt="caseItem.name"
            class="absolute inset-0 w-[80%] h-[80%] object-contain p-4 transition-transform duration-700 group-hover:scale-110 text-center justify-center items-center mx-auto left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2"
            @error="handleCaseImageError"
          />

          <!-- 标签 - 根据区块类型和物品属性显示 -->
          <div
            v-if="shouldShowTag(caseItem)"
            class="absolute top-2 left-2 z-10 backdrop-blur-sm border shadow-md rounded-xl text-xs font-bold px-2 py-1 flex items-center gap-1"
            :class="getTagClass(caseItem)"
          >
            <i :class="getTagIcon(caseItem)"></i>
            <span class="text-white">{{ getTagContent(caseItem) }}</span>
          </div>

          <!-- 标签颜色条 -->
          <div class="absolute bottom-0 left-0 right-0 h-1 flex z-20">
            <div
              class="h-full w-full transition-all duration-500"
              :class="getSectionIndicatorClass()"
            ></div>
          </div>
        </div>

        <!-- 信息区 - 根据区块类型调整颜色 -->
        <div
          class="p-3 transition-colors flex flex-col justify-between bg-gradient-to-b from-gray-800/50 to-gray-900/70 rounded-b-xl"
        >
          <h3
            class="font-semibold text-sm mb-2 text-center overflow-hidden text-ellipsis whitespace-nowrap transition-colors duration-300"
            :class="getSectionTitleClass()"
          >
            {{ caseItem.name }}
          </h3>

          <div class="flex justify-between items-center">
            <!-- 价格 - 根据区块类型调整颜色和显示逻辑 -->
            <div class="flex items-center text-xs">
              <!-- 折扣区块：显示折扣价 + 原价删除线 -->
              <template
                v-if="
                  props.sectionType === 'discount' &&
                  caseItem.discount &&
                  caseItem.discount > 0
                "
              >
                <span :class="getSectionPriceClass()" class="mr-1">$</span>
                <span :class="getSectionPriceClass()" class="font-semibold">{{
                  formatDiscountPrice(caseItem.price, caseItem.discount)
                }}</span>
                <span class="line-through ml-2 text-xs text-gray-400">
                  ${{ formatPrice(caseItem.price) }}
                </span>
              </template>
              <!-- 其他区块：正常显示价格 -->
              <template v-else>
                <span :class="getSectionPriceClass()" class="mr-1">$</span>
                <span :class="getSectionPriceClass()" class="font-semibold">{{
                  formatPrice(caseItem.price)
                }}</span>
                <span
                  v-if="
                    caseItem.original_price &&
                    caseItem.original_price > caseItem.price
                  "
                  class="line-through ml-2 text-xs text-gray-400"
                >
                  ${{ formatPrice(caseItem.original_price) }}
                </span>
              </template>
            </div>

            <!-- 稀有度或开箱数量 -->
            <div class="flex items-center text-xs text-white/60">
              <template v-if="caseItem.is_hot">
                <i
                  :class="getSectionIconClass()"
                  class="fas fa-box-open mr-1"
                ></i>
                <span>{{ formatOpenCount(caseItem.open_count || 0) }}</span>
              </template>
              <template v-else-if="caseItem.rarity">
                <i :class="getSectionIconClass()" class="fas fa-gem mr-1"></i>
                <span>{{ caseItem.rarity }}</span>
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 - 根据区块类型调整颜色 -->
    <div
      v-else
      class="text-center py-8 border shadow-xl rounded-xl relative overflow-hidden"
      :class="getSectionCardClass()"
    >
      <!-- 装饰背景元素 - 根据区块类型 -->
      <div
        class="absolute -right-10 -top-10 w-32 h-32 rounded-full blur-3xl"
        :style="getSectionEmptyDecoration().right"
      ></div>
      <div
        class="absolute -left-10 -bottom-10 w-24 h-24 rounded-full blur-3xl"
        :style="getSectionEmptyDecoration().left"
      ></div>

      <div class="relative z-10">
        <div class="empty-icon mb-4">
          <i class="fas fa-box-open text-4xl text-white/30"></i>
        </div>
        <h3 class="text-white/70 text-lg font-medium mb-2">
          {{ $t("cases.no_cases_available") || "暂无箱子" }}
        </h3>
        <p class="text-white/50 text-sm">
          {{ $t("cases.check_back_later") || "请稍后再试" }}
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { CaseItem } from "~/types/case";

// Props
const props = defineProps<{
  cases: CaseItem[];
  num?: number;
  sectionType?: "discount" | "hot" | "new" | "default";
}>();

const emit = defineEmits<{
  caseClick: [caseItem: CaseItem];
}>();

const { t, locale } = useI18n();

// 显示的case数量，使用本地化名称
const displayCases = computed(() => {
  if (!props.cases || props.cases.length === 0) return [];

  return props.cases.slice(0, props.num || 5).map((caseItem) => {
    // 获取当前语言
    const currentLocale = locale.value === "zh-hans" ? "zh_hans" : locale.value;

    // 获取本地化名称，使用类型断言
    let localizedName = caseItem.name || "Unknown Case";
    const itemWithI18n = caseItem as any;

    // 尝试获取本地化字段
    if (currentLocale === "zh_hans") {
      localizedName =
        itemWithI18n.name_zh_hans ||
        itemWithI18n.name_zh ||
        caseItem.name ||
        "Unknown Case";
    } else if (currentLocale === "en") {
      localizedName = itemWithI18n.name_en || caseItem.name || "Unknown Case";
    }

    return {
      ...caseItem,
      name: localizedName,
    };
  });
});

// 价格格式化
const formatPrice = (price: number): string => {
  if (!price) return "0.00";
  return price.toFixed(2);
};

// 折扣价格计算
const formatDiscountPrice = (
  originalPrice: number,
  discountPercent: number
): string => {
  if (!originalPrice || !discountPercent) return formatPrice(originalPrice);
  const discountPrice = originalPrice * (1 - discountPercent / 100);
  return formatPrice(discountPrice);
};

// 开箱数量格式化
const formatOpenCount = (count: number): string => {
  if (!count || count === 0) return "0";
  if (count >= 1000000) {
    return (count / 1000000).toFixed(1) + "M";
  } else if (count >= 1000) {
    return (count / 1000).toFixed(1) + "K";
  } else {
    return count.toString();
  }
};

// 日期格式化
const formatDate = (dateString: string): string => {
  if (!dateString) return "NEW";
  try {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays <= 1) return "NEW";
    if (diffDays <= 7) return `${diffDays}天前`;
    if (diffDays <= 30) return `${Math.ceil(diffDays / 7)}周前`;
    return "NEW";
  } catch {
    return "NEW";
  }
};

// 处理箱子点击
const handleCaseClick = (caseItem: CaseItem) => {
  emit("caseClick", caseItem);
  // 如果有case_key，优先使用case_key，否则使用id
  const caseIdentifier = (caseItem as any).case_key || caseItem.id;
  navigateTo(`/cases/${caseIdentifier}`);
};

// 图片错误处理
const handleCaseImageError = (event: Event) => {
  const target = event.target as HTMLImageElement;
  if (!target) return;

  // 避免无限循环
  if (target.src.includes("placehold.co")) return;

  target.src = "https://placehold.co/64x64/1f2937/ffffff/png?text=📦";
  target.alt = t("cases.image_error", "箱子图片加载失败");
  target.title = t("cases.image_error", "箱子图片加载失败");
};

// 根据区块类型获取卡片样式
const getSectionCardClass = (): string => {
  const baseClass = "rounded-xl";
  switch (props.sectionType) {
    case "discount":
      return `${baseClass} border-red-400/30 hover:shadow-red-500/20`;
    case "hot":
      return `${baseClass} border-orange-400/30 hover:shadow-orange-500/20`;
    case "new":
      return `${baseClass} border-emerald-400/30 hover:shadow-emerald-500/20`;
    default:
      return `${baseClass} border-gray-800/30 hover:shadow-neon-primary`;
  }
};

// 根据区块类型获取背景光晕
const getSectionBackgroundClass = (): string => {
  switch (props.sectionType) {
    case "discount":
      return "bg-gradient-to-br from-red-500/5 via-transparent to-red-500/5";
    case "hot":
      return "bg-gradient-to-br from-orange-500/5 via-transparent to-orange-500/5";
    case "new":
      return "bg-gradient-to-br from-emerald-500/5 via-transparent to-emerald-500/5";
    default:
      return "bg-gradient-to-br from-blue-500/5 via-transparent to-orange-500/5";
  }
};

// 根据区块类型获取指示条颜色
const getSectionIndicatorClass = (): string => {
  switch (props.sectionType) {
    case "discount":
      return "bg-red-500";
    case "hot":
      return "bg-orange-500";
    case "new":
      return "bg-emerald-500";
    default:
      return "bg-gray-500";
  }
};

// 根据区块类型获取标题颜色
const getSectionTitleClass = (): string => {
  switch (props.sectionType) {
    case "discount":
      return "text-white group-hover:text-red-400";
    case "hot":
      return "text-white group-hover:text-orange-400";
    case "new":
      return "text-white group-hover:text-emerald-400";
    default:
      return "text-white group-hover:text-secondary-500";
  }
};

// 根据区块类型获取价格颜色
const getSectionPriceClass = (): string => {
  switch (props.sectionType) {
    case "discount":
      return "text-red-400";
    case "hot":
      return "text-orange-400";
    case "new":
      return "text-emerald-400";
    default:
      return "text-secondary-500";
  }
};

// 根据区块类型获取图标颜色
const getSectionIconClass = (): string => {
  switch (props.sectionType) {
    case "discount":
      return "text-red-400";
    case "hot":
      return "text-orange-400";
    case "new":
      return "text-emerald-400";
    default:
      return "text-secondary-500";
  }
};

// 根据区块类型获取空状态装饰
const getSectionEmptyDecoration = (): { right: string; left: string } => {
  switch (props.sectionType) {
    case "discount":
      return {
        right: "background-color: rgba(239, 68, 68, 0.05)",
        left: "background-color: rgba(220, 38, 38, 0.05)",
      };
    case "hot":
      return {
        right: "background-color: rgba(251, 146, 60, 0.05)",
        left: "background-color: rgba(234, 88, 12, 0.05)",
      };
    case "new":
      return {
        right: "background-color: rgba(52, 211, 153, 0.05)",
        left: "background-color: rgba(16, 185, 129, 0.05)",
      };
    default:
      return {
        right: "background-color: rgba(255, 77, 0, 0.05)",
        left: "background-color: rgba(0, 168, 255, 0.05)",
      };
  }
};

// 根据区块类型和物品属性显示标签
const shouldShowTag = (caseItem: CaseItem): boolean => {
  return (
    !!(caseItem.discount && caseItem.discount > 0) ||
    !!caseItem.is_hot ||
    !!caseItem.is_new
  );
};

// 根据区块类型获取标签类
const getTagClass = (caseItem: CaseItem): string => {
  switch (props.sectionType) {
    case "discount":
      return "bg-red-500/90 border-red-400/50";
    case "hot":
      return "bg-orange-500/90 border-orange-400/50";
    case "new":
      return "bg-emerald-500/90 border-emerald-400/50";
    default:
      // 根据物品属性决定颜色
      if (caseItem.discount && caseItem.discount > 0) {
        return "bg-red-500/90 border-red-400/50";
      } else if (caseItem.is_hot) {
        return "bg-orange-500/90 border-orange-400/50";
      } else if (caseItem.is_new) {
        return "bg-emerald-500/90 border-emerald-400/50";
      }
      return "bg-gray-500/90 border-gray-400/50";
  }
};

// 根据区块类型获取标签图标
const getTagIcon = (caseItem: CaseItem): string => {
  switch (props.sectionType) {
    case "discount":
      return "fas fa-percent text-red-100";
    case "hot":
      return "fas fa-fire text-orange-100";
    case "new":
      return "fas fa-sparkles text-emerald-100";
    default:
      // 根据物品属性决定图标
      if (caseItem.discount && caseItem.discount > 0) {
        return "fas fa-percent text-red-100";
      } else if (caseItem.is_hot) {
        return "fas fa-fire text-orange-100";
      } else if (caseItem.is_new) {
        return "fas fa-sparkles text-emerald-100";
      }
      return "fas fa-box-open text-gray-100";
  }
};

// 根据区块类型获取标签内容
const getTagContent = (caseItem: CaseItem): string => {
  switch (props.sectionType) {
    case "discount":
      return `-${caseItem.discount || 0}%`;
    case "hot":
      return formatOpenCount(caseItem.open_count || 0);
    case "new":
      return formatDate(caseItem.created_at || "");
    default:
      // 根据物品属性决定内容
      if (caseItem.discount && caseItem.discount > 0) {
        return `-${caseItem.discount}%`;
      } else if (caseItem.is_hot) {
        return formatOpenCount(caseItem.open_count || 0);
      } else if (caseItem.is_new) {
        return formatDate(caseItem.created_at || "");
      }
      return "HOT";
  }
};
</script>

<style lang="scss" scoped>
.case-card-container {
  .case-card {
    min-height: 240px;
    background: var(--gradient-card, linear-gradient(135deg, #171c21, #0d0f12));
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
    }
  }
}

// 使用首页的颜色系统
.text-secondary-500 {
  color: #6c5ce7;
}

.shadow-neon-primary {
  box-shadow: 0 0 20px rgba(255, 107, 0, 0.3);
}
</style>
