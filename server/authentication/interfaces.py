import logging
import threading

from django.contrib.auth import get_user_model
from django.db import transaction
from django.utils.translation import gettext_lazy as _

from authentication.business import reset_box_free_count, reduce_user_point, user_update_points 
from authentication.business import get_user_statistics_day, get_users_number 
from authentication.models import UserExtra


def setup_reset_box_free_worker():
    th = threading.Thread(target=reset_box_free_count, args=())
    th.start()


def setup_reduce_point_worker():
    th = threading.Thread(target=reduce_user_point, args=())
    th.start()


def set_user_box_chance(value):
    if value not in ['a', 'd', 'e']:
        return
    UserExtra.objects.filter(box_chance_type__in=['a', 'd', 'e']).update(box_chance_type=value)


def update_user_points(user, amount, remark=None):
    user_update_points(user, amount, remark)


def get_user_statistics_day_data(count=5):
    return get_user_statistics_day(count)


def get_users_number_data():
    return get_users_number()
