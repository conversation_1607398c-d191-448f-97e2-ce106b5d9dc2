# automatically generated by the FlatBuffers compiler, do not modify

# namespace: proto

import flatbuffers

class Interrupt(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAsInterrupt(cls, buf, offset):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = Interrupt()
        x.Init(buf, n + offset)
        return x

    # Interrupt
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # Interrupt
    def Request(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint64Flags, o + self._tab.Pos)
        return 0

    # Interrupt
    def Mode(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint8Flags, o + self._tab.Pos)
        return 1

def InterruptStart(builder): builder.StartObject(2)
def InterruptAddRequest(builder, request): builder.PrependUint64Slot(0, request, 0)
def InterruptAddMode(builder, mode): builder.PrependUint8Slot(1, mode, 1)
def InterruptEnd(builder): return builder.EndObject()
