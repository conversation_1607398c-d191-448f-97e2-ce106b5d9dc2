import { defineNuxtPlugin } from '#app'
import { useBattleAudioSync } from '~/composables/useBattleAudioSync'
import { useBattleAudioGlobal } from '~/composables/useBattleAudio'

export default defineNuxtPlugin(() => {
  if (process.client) {
    const audio = useBattleAudioGlobal()
    if (audio && typeof audio.initAudio === 'function') {
      audio.initAudio()
    }
    useBattleAudioSync()
  }
}) 