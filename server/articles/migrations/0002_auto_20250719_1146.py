# Generated by Django 3.2.25 on 2025-07-19 03:46

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('articles', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='content',
            options={'ordering': ['-is_pinned', '-priority', '-publish_date'], 'verbose_name': '内容', 'verbose_name_plural': 'Contents'},
        ),
        migrations.AlterModelOptions(
            name='contentattachment',
            options={'verbose_name': '内容附件', 'verbose_name_plural': 'Content Attachments'},
        ),
        migrations.AlterModelOptions(
            name='contentcategory',
            options={'ordering': ['sort_order', 'name'], 'verbose_name': '内容分类', 'verbose_name_plural': 'Content Categories'},
        ),
        migrations.AlterModelOptions(
            name='contenttag',
            options={'ordering': ['name'], 'verbose_name': '内容标签', 'verbose_name_plural': 'Content Tags'},
        ),
        migrations.AlterModelOptions(
            name='contentview',
            options={'verbose_name': '内容浏览', 'verbose_name_plural': 'Content Views'},
        ),
        migrations.RemoveIndex(
            model_name='content',
            name='articles_co_content_b8b7b8_idx',
        ),
        migrations.RemoveIndex(
            model_name='content',
            name='articles_co_publish_b8b7b8_idx',
        ),
        migrations.RemoveIndex(
            model_name='content',
            name='articles_co_is_feat_b8b7b8_idx',
        ),
        migrations.RemoveIndex(
            model_name='content',
            name='articles_co_is_pinn_b8b7b8_idx',
        ),
        migrations.AlterField(
            model_name='content',
            name='category',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='contents', to='articles.contentcategory', verbose_name='分类'),
        ),
        migrations.AlterField(
            model_name='content',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='contentattachment',
            name='content',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attachments', to='articles.content', verbose_name='内容'),
        ),
        migrations.AlterField(
            model_name='contentattachment',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='contentcategory',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='contenttag',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='contentview',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AddIndex(
            model_name='content',
            index=models.Index(fields=['content_type', 'status'], name='articles_co_content_408ec6_idx'),
        ),
        migrations.AddIndex(
            model_name='content',
            index=models.Index(fields=['publish_date'], name='articles_co_publish_e479e5_idx'),
        ),
        migrations.AddIndex(
            model_name='content',
            index=models.Index(fields=['is_featured'], name='articles_co_is_feat_152de4_idx'),
        ),
        migrations.AddIndex(
            model_name='content',
            index=models.Index(fields=['is_pinned'], name='articles_co_is_pinn_bed0e8_idx'),
        ),
    ]
