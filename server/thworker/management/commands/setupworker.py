import threading
import time
import schedule
import logging
import random

from django.conf import settings
from django.core.management.base import BaseCommand
from django.core.cache import cache
from datetime import datetime, timedelta


# from box.business_room import set_room_rounds_cache, run_set_rounds_cache
from envelope.business import run_check_envelop_state
from grab.business import setup_check_run_grab_room_worker, setup_check_room_state_worker
from steambase.redis_con import init_conn
from thworker import temp_data
from authentication.interfaces import setup_reset_box_free_worker, setup_reduce_point_worker
from charge.interfaces import setup_charge_cancel_worker, setup_decrease_level_every_day, setup_charge_check_worker, setup_hpj_charge_check_order
# from package.interfaces import setup_trade_checker, setup_auto_exchange_worker, setup_update_price_worker
from package.interfaces import setup_sync_items_price_worker
from box.interfaces import setup_case_bot_worker, init_case_data, setup_case_room_worker, init_case_cache, setup_case_pk_worker, setup_case_pk_join_worker, set_bot_cache_worker
# from chat.interfaces import setup_chat_bot_worker
from roll.interfaces import init_roll_room_data, setup_roll_room_worker, setup_check_rollroom_bot
from lottery.interfaces import setup_run_hourly_lottery_worker, setup_run_daily_lottery_worker, setup_run_weekly_lottery_worker
# from crash.interfaces import setup_crash_game_worker
from tools.initChargeLevel import setup_init_charge_level
from withdraw.interfaces import setup_check_waxpeer_offer, setup_check_pendclose_trade, setup_wxp_buy_worker
from monitor.interfaces import setup_update_monitor_data
from b2ctrade.interfaces import setup_check_b2c_trade_state, setup_zbt_buy_worker, setup_zbt_buy_normal_worker
from lottery.business import add_lottery_joiner
from sitecfg.business import setup_update_base_count_worker
from sitecfg.tasks import update_new_user_recharge_limit, update_daily_recharge_limit, update_enable_rule_exam, update_register_box_chance_type, update_recharge_box_chance_type, update_domain_verify_switch
from charge.tasks import update_enable_based_on_coins, update_enable_pay_method, update_month_amount, update_today_amount
from authentication.tasks import delete_old_balance_records, delete_old_log_entries
from box.tasks import delete_old_case_records, update_case_records_cache
from package.tasks import delete_old_package_items
from roll.tasks import cleanup_duplicate_roll_room_bets

_logger = logging.getLogger(__name__)

_tasks_setup_lock = threading.Lock()
_tasks_setup = False

def run_schedule():
    while True:
        schedule.run_pending()
        time.sleep(5)

def random_hourly_job():
    # 获取当前时间的小时数和分钟数
    current_hour = time.localtime().tm_hour
    current_minute = time.localtime().tm_min    
    
    # 生成一个随机分钟数，范围在0到距离下一个整点的分钟数之间
    random_minute = random.randint(1, 59)

    random_current_minute = random.randint(current_minute, 59)
    
    # 计算下一个整点的小时数
    next_hour = current_hour + 1
    
    # 将任务安排在当前小时的下一个整点，加上随机分钟数
    scheduled_time = f"{next_hour:02d}:{random_minute:02d}"

    current_time = f"{current_hour:02d}:{random_current_minute:02d}"
    
    # 执行任务
    #schedule.every().hour.at(current_time).do(add_lottery_joiner)
    schedule.every().hour.at(scheduled_time).do(add_lottery_joiner)

    th = threading.Thread(target=run_schedule, args=())
    th.start()


def setup_schedule_task():
    global _tasks_setup

    with _tasks_setup_lock:
        if _tasks_setup:
            _logger.info("Schedule tasks already set up. Skipping.")
            return

        _logger.info("Setting up schedule tasks...")

        # 清除所有现有的任务
        # schedule.clear()
        
        random_minute = random.randint(0, 59)

        # 更新 CaseRecords 缓存
        schedule.every(5).minutes.do(update_case_records_cache, count=100)

        # 5分钟检测一次订单状态
        schedule.every(5).minutes.do(setup_check_b2c_trade_state)


        # 10分钟轮换对战机器人
        # schedule.every(10).minutes.do(setup_case_room_worker)

        # schedule.every().day.at('00:00').do(setup_reset_box_free_worker)
        # schedule.every().day.at('00:00').do(setup_reduce_point_worker)
        
        schedule.every().day.at('03:10').do(setup_sync_items_price_worker) 
        # 新用户充值限制恢复默认
        # schedule.every().day.at('10:30').do(reset_new_user_recharge_limit(100))

        # 用户每日限额
        schedule.every().day.at('00:00').do(lambda: update_daily_recharge_limit(200))
        schedule.every().day.at('09:00').do(lambda: update_daily_recharge_limit(800))

        # schedule.every().day.at('18:00').do(lambda: update_daily_recharge_limit(800))
        # schedule.every().day.at('00:00').do(update_daily_recharge_limit(100))
        # schedule.every().day.at('09:00').do(update_daily_recharge_limit(600))
        # schedule.every().day.at('18:00').do(update_daily_recharge_limit(800))

        # 0点更新充值配置

        # schedule.every().day.at('00:00').do(lambda: update_enable_based_on_coins(50, 50))
        # schedule.every().day.at('09:00').do(lambda: update_enable_based_on_coins(50, 600))

        schedule.every().day.at('00:00').do(lambda: update_today_amount())

        # 自动切换充值渠道 2-厨王，4-太一， 5-月定山空， 7-昕运泰, 8-云闪付

        schedule.every().day.at('00:00').do(lambda: update_enable_pay_method([4, 5, 8]))
        schedule.every().day.at('09:00').do(lambda: update_enable_pay_method([2, 8]))

        # 规则考试
        # schedule.every().day.at('00:00').do(lambda: update_enable_rule_exam(True))
        # schedule.every().day.at('09:00').do(lambda: update_enable_rule_exam(False))

        # 切换注册类型
        # schedule.every().day.at('00:12').do(lambda: update_register_box_chance_type('c'))
        # schedule.every().day.at('09:00').do(lambda: update_register_box_chance_type('a'))

        # 切换充值类型
        # schedule.every().day.at('00:12').do(lambda: update_recharge_box_chance_type('c'))
        # schedule.every().day.at('09:00').do(lambda: update_recharge_box_chance_type('a'))

        # 域名验证开关 
        # schedule.every().day.at('00:00').do(lambda: update_domain_verify_switch(True))
        # schedule.every().day.at('09:00').do(lambda: update_domain_verify_switch(False))


        # 清理重复用户
        # schedule.every().hour.at('00:40').do(cleanup_duplicate_roll_room_bets) 

        #schedule.every().hour.at('00:00').do(setup_update_base_count_worker)

        # 更新显示数据
        schedule.every().hour.at(f'{random_minute:02d}:00').do(setup_update_base_count_worker)

        #schedule.every().hour.at(f'{random_minute:02d}:00').do(setup_check_rollroom_bot)

        # ROLL添加机器人
        # schedule.every(10).minutes.do(setup_check_rollroom_bot)  


        # 删除过期数据
        # schedule.every().day.at('02:10').do(delete_old_balance_records)
        # schedule.every().day.at('02:30').do(delete_old_case_records)
        # schedule.every().day.at('02:50').do(delete_old_package_items)
        #schedule.every().day.at('18:10').do(delete_old_log_entries)

        # 抽奖
        schedule.every().hour.at('00:00').do(setup_run_hourly_lottery_worker) 
        schedule.every().day.at('00:00').do(setup_run_daily_lottery_worker) 
        schedule.every().monday.at('00:00').do(setup_run_weekly_lottery_worker) 

        # schedule.every().day.at('00:00').do(setup_decrease_level_every_day)
        
        # schedule.every(5).minutes.do(setup_charge_check_worker) 
        
        # th = threading.Thread(target=run_schedule, args=())
        # th.start()
        _tasks_setup = True
        _logger.info("Schedule tasks setup completed.")


def set_up_threading():
    try:
        init_conn()
        init_case_data()
        init_case_cache()
        init_roll_room_data()
        setup_roll_room_worker()
        setup_check_room_state_worker()
        setup_check_run_grab_room_worker()

        # 自动采购
        setup_zbt_buy_worker()
        # 自动检测取回状态
        # setup_check_b2c_trade_state()

        # 对战
        setup_case_room_worker()
        
        # 创建对战
        setup_case_pk_worker()
        # 加入对战        
        setup_case_pk_join_worker()
        # 随机时间任务 添加抽奖
        random_hourly_job()        

        setup_case_bot_worker()
        run_check_envelop_state()
        setup_check_pendclose_trade()
        setup_update_monitor_data()

        # 定时任务
        setup_schedule_task()
        threading.Thread(target=run_schedule, daemon=True).start()

        # setup_trade_checker()
        # setup_package_unlock_checker()
        # setup_auto_exchange_worker()       
                
        # setup_zbt_buy_normal_worker()
        # setup_check_rollroom_bot()
        # setup_crash_game_worker()
        # setup_chat_bot_worker()
        # setup_charge_order_check_worker()
        # setup_charge_cancel_worker()
        # setup_hpj_charge_check_order()        
        # setup_check_waxpeer_offer()        
        # setup_wxp_buy_worker()        
        # setup_init_charge_level()
        # if settings.TRADE_TEST:
        #     temp_data.setup_test_confirmer()
        #     temp_data.setup_test_sender()

        _logger.info('Setup bot command end')
    except Exception as e:
        _logger.exception(e)




class Command(BaseCommand):
    def handle(self, *args, **options):
        _logger.info('Setup bot command begin')

        time_limit = timedelta(minutes=1)  # 设置时间限制为1分钟
        cache_key = 'setupworker:last_run_time'

        # 检查缓存中的上次运行时间
        last_run_time_str = cache.get(cache_key)
        if last_run_time_str:
            # 使用 strptime 解析时间字符串
            last_run_time = datetime.strptime(last_run_time_str, '%Y-%m-%dT%H:%M:%S.%f')
            
            # 如果上次运行时间在时间限制内，则跳过执行
            if datetime.now() - last_run_time < time_limit:
                _logger.info('Command recently executed, skipping.')
                return

        # 更新缓存中的上次运行时间
        cache.set(cache_key, datetime.now().isoformat(), timeout=None)  # 可以设置一个具体的过期时间

        # 执行主要任务
        set_up_threading()

        # _logger.info('setup bot command end')



