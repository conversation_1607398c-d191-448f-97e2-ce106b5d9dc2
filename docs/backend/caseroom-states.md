# CaseRoom 状态分析 - 前端映射参考

## 概述

本文档详细分析 CSGO 皮肤交易平台中 CaseRoom（开箱对战房间）的状态定义和生命周期，为前端开发提供状态映射参考。

## 状态枚举定义

### GameState 枚举值

基于 `steambase/enums.py` 中的 `GameState` 定义：

```python
class GameState(IntEnum):
    Initial = 1      # 初始状态
    Joinable = 2     # 可加入
    Joining = 3      # 加入中  
    Full = 4         # 已满员
    Running = 5      # 运行中
    End = 11         # 已结束
    Cancelled = 20   # 已取消
```

## 前端状态映射

### 1. 基础状态映射

```javascript
const CASE_ROOM_STATES = {
  // 数字状态值到字符串的映射
  1: 'initial',     // 初始状态
  2: 'joinable',    // 可加入
  3: 'joining',     // 加入中
  4: 'full',        // 已满员  
  5: 'running',     // 运行中
  11: 'end',        // 已结束
  20: 'cancelled'   // 已取消
};

// 字符串状态到数字的映射 (用于API调用)
const STATE_TO_NUMBER = {
  'initial': '1',
  'joinable': '2', 
  'joining': '3',
  'full': '4',
  'running': '5',
  'end': '11',
  'cancelled': '20'
};
```

### 2. API调用状态映射

根据 `get_battle_list` 函数的状态映射：

```javascript
const API_STATE_MAPPING = {
  '1': 1,   // Initial
  '2': 2,   // Joinable
  '3': 3,   // Joining  
  '4': 4,   // Full
  '5': 5,   // Running
  '11': 11, // End
  '20': 20  // Cancelled
};
```

### 3. 显示文本映射

```javascript
const STATE_DISPLAY_TEXT = {
  1: {
    zh: '初始化',
    en: 'Initial'
  },
  2: {
    zh: '可加入',  
    en: 'Joinable'
  },
  3: {
    zh: '加入中',
    en: 'Joining'
  },
  4: {
    zh: '已满员',
    en: 'Full'
  },
  5: {
    zh: '进行中',
    en: 'Running'  
  },
  11: {
    zh: '已结束',
    en: 'Ended'
  },
  20: {
    zh: '已取消',
    en: 'Cancelled'
  }
};
```

### 4. 状态颜色映射

```javascript
const STATE_COLORS = {
  1: '#909399',   // 初始状态 - 灰色
  2: '#67C23A',   // 可加入 - 绿色
  3: '#E6A23C',   // 加入中 - 橙色  
  4: '#F56C6C',   // 已满员 - 红色
  5: '#409EFF',   // 运行中 - 蓝色
  11: '#909399',  // 已结束 - 灰色
  20: '#F56C6C'   // 已取消 - 红色
};
```

## 状态生命周期

### 1. 房间创建流程

```
创建房间 → Initial(1) → Joinable(2)
```

**代码位置**: `create_case_room()` 函数
```python
# 创建房间后立即设置为可加入状态
CaseRoom.objects.filter(short_id=room.short_id).update(state=GameState.Joinable.value)
```

### 2. 加入房间流程

```
Joinable(2) → [人数已满] → Full(4)
```

**代码位置**: `join_case_room()` 函数
```python
# 当房间人数达到最大值时
if room.bets.count() == room.max_joiner:
    room.state = GameState.Full.value
```

### 3. 游戏运行流程

```
Full(4) → Running(5) → End(11)
```

**代码位置**: `run_battle_room()` 函数
```python
# 开始游戏
if room.state == GameState.Full.value:
    room.state = GameState.Running.value

# 游戏结束
if room.state == GameState.Running.value:
    room.state = GameState.End.value
```

### 4. 房间取消流程

```
Initial(1) / Joinable(2) / Joining(3) → Cancelled(20)
```

**代码位置**: `cancel_case_room()` 函数
```python
room.state = GameState.Cancelled.value
```

## 前端业务逻辑建议

### 1. 状态过滤器

```javascript
// 获取不同状态的房间列表
const getActiveRooms = () => {
  return ['2', '3', '4', '5']; // Joinable, Joining, Full, Running
};

const getEndedRooms = () => {
  return ['11']; // End
};

const getCancelledRooms = () => {
  return ['20']; // Cancelled
};
```

### 2. 用户操作权限

```javascript
const canJoinRoom = (roomState, currentUser, roomCreator) => {
  // 只有可加入状态的房间才能加入
  if (roomState !== 2) return false;
  
  // 不能加入自己创建的房间
  if (currentUser.id === roomCreator.id) return false;
  
  return true;
};

const canCancelRoom = (roomState, currentUser, roomCreator) => {
  // 只有房间创建者可以取消
  if (currentUser.id !== roomCreator.id) return false;
  
  // 只有未开始的房间可以取消
  return [1, 2, 3].includes(roomState);
};
```

### 3. 状态显示组件

```vue
<template>
  <div class="room-state" :class="stateClass">
    <span class="state-dot" :style="{ backgroundColor: stateColor }"></span>
    <span class="state-text">{{ stateText }}</span>
  </div>
</template>

<script>
export default {
  props: {
    state: {
      type: Number,
      required: true
    },
    language: {
      type: String,
      default: 'zh'
    }
  },
  computed: {
    stateText() {
      return STATE_DISPLAY_TEXT[this.state]?.[this.language] || '未知状态';
    },
    stateColor() {
      return STATE_COLORS[this.state] || '#909399';
    },
    stateClass() {
      return `state-${CASE_ROOM_STATES[this.state] || 'unknown'}`;
    }
  }
}
</script>
```

### 4. API 调用示例

```javascript
// 获取特定状态的房间列表
const fetchRooms = async (states = ['2', '4', '5']) => {
  try {
    const response = await api.post('/battle/list/', {
      state_list: states,
      page: 1,
      page_size: 20
    });
    return response.data;
  } catch (error) {
    console.error('获取房间列表失败:', error);
  }
};

// 加入房间
const joinRoom = async (roomUid) => {
  try {
    const response = await api.post('/battle/join/', {
      uid: roomUid
    });
    return response.data;
  } catch (error) {
    console.error('加入房间失败:', error);
  }
};
```

## 状态查询场景

### 1. 房间列表查询

根据 `get_room_list()` 函数的实现：

```python
# 后端查询活跃房间的状态
queryset = CaseRoom.objects.filter(state__in=[
    GameState.Joinable.value,    # 2
    GameState.Joining.value,     # 3
    GameState.Full.value,        # 4
    GameState.Running.value,     # 5
    GameState.End.value          # 11
])
```

**前端对应**:
```javascript
const ACTIVE_STATES = ['2', '3', '4', '5', '11'];
```

### 2. 用户自己的房间

根据 `get_room_self()` 函数的实现：

```python
# 查询用户创建的活跃房间
queryset = CaseRoom.objects.filter(state__in=[
    GameState.Joinable.value,    # 2
    GameState.Joining.value,     # 3  
    GameState.Full.value,        # 4
    GameState.Running.value      # 5
]).filter(user=user)
```

**前端对应**:
```javascript
const USER_ACTIVE_STATES = ['2', '3', '4', '5'];
```

### 3. 历史房间查询

根据 `get_room_history()` 函数的实现：

```python
# 只查询已结束的房间
queryset = CaseRoom.objects.filter(
    private=0, 
    state__in=[GameState.End.value]  # 11
)
```

**前端对应**:
```javascript
const HISTORY_STATES = ['11'];
```

## WebSocket 事件状态更新

### 1. WebSocket消息监听和处理

```javascript
// WebSocket 连接和消息处理
const ws = new WebSocket('ws://localhost:8000/ws/');

ws.onmessage = function(event) {
  const message = JSON.parse(event.data);
  
  // 解析消息格式: [messageType, action, data, ...]
  const [messageType, action, data] = message;
  
  switch (messageType) {
    case 'boxroom':
      // 房间级别状态变化
      handleRoomUpdate(action, data);
      break;
      
    case 'boxroomdetail':
      // 房间详情变化（回合、结束）
      const socketId = message[3];
      handleRoomDetailUpdate(action, data, socketId);
      break;
  }
};

// 房间状态变更处理
const handleRoomUpdate = (action, roomData) => {
  switch (action) {
    case 'new':
      // 新房间创建 (state: 2)
      addRoomToList(roomData);
      break;
      
    case 'update':
      // 房间状态更新: 加入(2)、满员(4)等
      updateRoomInList(roomData);
      break;
      
    case 'start':
      // 对战开始 (state: 5)
      markRoomAsStarted(roomData);
      break;
      
    case 'cancel':
      // 房间取消 (state: 20)
      markRoomAsCancelled(roomData);
      break;
  }
};

// 房间详情变更处理
const handleRoomDetailUpdate = (action, betData, socketId) => {
  switch (action) {
    case 'round':
      // 回合开启，显示开箱结果
      updateRoomRound(betData);
      break;
      
    case 'end':
      // 对战结束 (state: 11)
      markRoomAsEnded(betData);
      break;
  }
};
```

### 2. 实时状态同步

```javascript
const syncRoomState = (roomUid, newState) => {
  // 更新本地状态
  const room = rooms.value.find(r => r.uid === roomUid);
  if (room) {
    const oldState = room.state;
    room.state = newState;
    
    // 触发UI更新
    emits('room-state-changed', {
      uid: roomUid,
      oldState: oldState,
      newState: newState
    });
  }
};
  }
};
```

## 注意事项

### 1. 状态值类型

- **后端**: 使用整数值 (1, 2, 3, 4, 5, 11, 20)
- **API**: 使用字符串值 ('1', '2', '3', '4', '5', '11', '20')
- **前端**: 建议统一使用数字值进行比较和判断

### 2. 状态变更顺序

正常流程: `1 → 2 → 4 → 5 → 11`

异常流程: `1/2/3 → 20` (取消)

### 3. 过期房间处理

根据 `check_case_room()` 函数，过期的房间会自动取消：

```python
# 查询过期房间
expire_rooms = CaseRoom.objects.filter(state__in=[
    GameState.Initial.value,    # 1
    GameState.Joinable.value,   # 2
    GameState.Joining.value     # 3
], update_time__lt=(timezone.now() - timedelta(seconds=seconds)))
```

前端应定期刷新房间列表以移除过期/取消的房间。

### 4. 权限控制

- `state = 1`: 只有创建者可见
- `state = 2`: 所有用户可见，可加入
- `state = 3`: 加入中状态（临时状态）
- `state = 4`: 已满员，不可加入
- `state = 5`: 游戏进行中，可观战
- `state = 11`: 已结束，可查看结果
- `state = 20`: 已取消，应从列表移除

## 总结

CaseRoom 的状态系统设计清晰，前端可以根据状态值进行相应的UI展示和交互控制。建议前端统一使用数字状态值，并通过映射对象来处理显示文本、颜色和用户操作权限。

---

## 对战房间相关 API 接口详细说明

> **接口路径前缀：所有对战相关API路径为 `/box/battle/`**
> 
> **重要说明：team参数处理**
> - 虽然API代码中保留了team参数(队伍选择功能)，但前端不再使用队伍对战模式
> - 创建房间时，team参数可固定传1，系统会自动处理队伍分配
> - 加入房间时，team参数可固定传1，系统会自动分配到人数较少的队伍
> - 房间类型固定为TeamBattle(type=3)，最大参与人数固定为4人
> - 前端无需实现队伍选择UI，直接使用默认值即可

### 1. 对战房间列表 API

#### 1.1 获取对战房间列表
- **接口路径**：`GET /box/battle/list/`
- **功能说明**：获取满足条件的对战房间列表，支持状态筛选和分页
- **请求参数**(Query参数)：
  - `state`: 房间状态列表，逗号分隔(如 `"2,4,5"`)
  - `page`: 页码，从1开始(默认1)
  - `pageSize`: 每页数量(默认10)
  - `assigner`: 是否只查询用户相关房间(可选)
- **响应格式**：
```json
{
  "code": 200,
  "message": "success", 
  "data": {
    "items": [
      {
        "uid": "room_123456",           // 房间唯一ID
        "short_id": "ABC123",           // 房间短ID
        "state": 2,                     // 房间状态
        "max_joiner": 4,                // 最大参与人数(固定为4)
        "type": 3,                      // 房间类型(固定为3=TeamBattle)
        "private": 0,                   // 是否私有(0=公开, 1=私有)
        "price": 77.40,                 // 房间价格(所有箱子价格总和)
        "create_time": "2024-01-01 12:00:00",
        "update_time": "2024-01-01 12:05:00",
        "user": {                       // 房间创建者信息
          "uid": "user_123",
          "profile": {
            "nick_name": "张三",
            "avatar": "https://example.com/avatar.jpg"
          }
        },
        "joiner_count": 1,              // 当前参与人数
        "round_count": 3,               // 总回合数
        "round_count_current": 0,       // 当前进行回合数
        "rounds": [                     // 回合信息(箱子统计)
          {
            "case": {
              "key": "case_ak47",
              "name": "AK47武器箱",
              "name_en": "AK47 Weapon Case",
              "name_zh_hans": "AK47武器箱",
              "cover": "https://example.com/case.jpg",
              "price": 25.80,
              "discount": 100,
              "count": 3                // 该箱子的回合数
            }
          }
        ],
        "bets": [                       // 参与者信息(简化)
          {
            "user": {
              "uid": "user_123",
              "profile": {
                "nick_name": "张三",
                "avatar": "https://example.com/avatar.jpg"
              }
            },
            "victory": null,            // 胜负结果(游戏结束后才有值)
            "team": 1                   // 队伍编号(1=CT, 2=T)
          }
        ]
      }
    ],
    "total": 158                        // 总记录数
  }
}
```

#### 1.2 获取当前用户参与的房间列表
- **接口路径**：`GET /box/battle/self/`
- **功能说明**：获取当前用户创建的对战房间
- **请求参数**(Query参数)：
  - `page`: 页码，从1开始(默认1)
  - `pageSize`: 每页数量(默认10)
- **响应格式**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "rooms": [
      // 与房间列表API相同的房间结构
    ],
    "total": 15                         // 总记录数
  }
}
```

### 2. 对战房间操作 API

#### 2.1 创建对战房间
- **接口路径**：`POST /box/battle/create/`
- **功能说明**：创建新的对战房间(固定为TeamBattle类型，4人房间)
- **请求参数**：
```json
{
  "cases_key": ["case_ak47", "case_m4a4", "case_awp"],  // 箱子key列表(必填)
  "team": 1,                           // 队伍选择(必填，1=CT, 2=T)
  "private": 0                         // 是否私有(0=公开, 1=私有)
}
```
- **重要说明**：
  - 房间类型固定为 `TeamBattle` (type=3)
  - 最大参与人数固定为 4 人
  - team参数虽然API需要传递，但前端可以固定传1，系统会自动分配队伍
  - 房间创建后，创建者自动加入指定队伍
```json
{
  "cases_key": ["case_ak47", "case_m4a4"],  // 箱子Key列表(必填，1-4个箱子)
  "team": 1,                               // 创建者队伍(必填，1=CT, 2=T)
  "private": 0                             // 是否私有(0=公开, 1=私有)
}
```
- **响应格式**：
```json
{
  "code": 200,
  "message": "房间创建成功",
  "data": {
    "items": {
      "uid": "room_789012",            // 新创建房间的唯一ID
      "short_id": "XYZ789"             // 新创建房间的短ID
    }
  }
}
```

#### 2.2 加入对战房间
- **接口路径**：`POST /box/battle/join/`
- **功能说明**：用户加入指定的对战房间
- **请求参数**：
```json
{
  "uid": "room_123456",               // 房间唯一ID(必填)
  "team": 1                           // 队伍编号(必填，1=CT, 2=T)
}
```
- **重要说明**：
  - team参数虽然API需要传递，但前端可以固定传1
  - 系统会自动根据当前队伍人数分配到人数较少的队伍
  - 前端不需要实现队伍选择UI，直接传固定值即可
- **响应格式**：
```json
{
  "code": 200,
  "message": "加入房间成功",
  "data": {
    "items": {
      "uid": "room_123456",           // 房间唯一ID  
      "short_id": "ABC123"            // 房间短ID
    }
  }
}
```

#### 2.3 退出对战房间
- **接口路径**：`POST /box/battle/quit/`
- **功能说明**：
  - 创建者退出: 取消整个房间，所有参与者退款，房间状态变为20(Cancelled)
  - 普通参与者退出: 只退出自己，房间继续存在
  - 仅限未开始的房间(state=2)
- **请求参数**：
```json
{
  "uid": "room_123456"               // 房间唯一ID(必填)
}
```
- **⚠️ API缺陷警告**：
  - 当前API实现中，quit操作没有发送WebSocket消息
  - 前端无法实时接收到房间取消或用户退出的通知
  - 建议后端补充WebSocket消息发送逻辑
- **响应格式**：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "items": {
      "uid": "room_123456",
      "short_id": "ABC123"
    }
  }
}
```

#### 2.4 获取对战房间详情
- **接口路径**：`GET /box/battle/detail/`
- **功能说明**：获取房间的完整详细信息，包括所有参与者、回合、物品等
- **请求参数**(Query参数)：
  - `uid`: 房间唯一ID(必填)
- **响应格式**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "items": {
      "uid": "room_123456",
      "short_id": "ABC123",
      "state": 11,                      // 房间状态
      "max_joiner": 4,                  // 固定为4人
      "type": 3,                        // 固定为TeamBattle
      "private": 0,
      "price": 77.40,
      "create_time": "2024-01-01 12:00:00",
      "update_time": "2024-01-01 12:15:00",
      "user": {                         // 房间创建者
        "uid": "user_123",
        "profile": {
          "nick_name": "张三",
          "avatar": "https://example.com/avatar.jpg"
        }
      },
      "joiner_count": 4,
      "round_count": 3,
      "round_count_current": 3,
      "rounds": [                       // 详细回合信息
        {
          "case": {
            "key": "case_ak47",
            "name": "AK47武器箱",
            "name_en": "AK47 Weapon Case", 
            "name_zh_hans": "AK47武器箱",
            "cover": "https://example.com/case.jpg",
            "price": 25.80,
            "room_drops": [             // 可能掉落的物品
              {
                "uid": "item_001",
                "market_name_cn": "AK47 红线",
                "show_chance": 15.5,
                "rarity_color": "#eb4b4b",
                "price": 128.50
              }
            ]
          }
        }
      ],
      "bets": [                         // 详细参与者信息
        {
          "uid": "bet_123",
          "user": {
            "uid": "user_123",
            "profile": {
              "nick_name": "张三",
              "avatar": "https://example.com/avatar.jpg"
            }
          },
          "team": 1,                    // 队伍编号(1=CT, 2=T)
          "victory": true,              // 是否获胜
          "open_amount": 77.40,         // 开箱总价值
          "win_amount": 156.80,         // 获胜总价值
          "win_items_count": 2,         // 获胜物品数量
          "open_items": [               // 开出的所有物品
            {
              "uid": "room_item_001",
              "item_id": 1001,
              "name": "AK47 红线",
              "name_en": "AK-47 | Redline",
              "name_zh_hans": "AK47 红线", 
              "image": "https://example.com/item.jpg",
              "item_price": {
                "price": 128.50,
                "update_time": "2024-01-01 10:00:00"
              },
              "item_category": {
                "cate_id": 1,
                "cate_name": "步枪",
                "cate_name_en": "Rifle",
                "cate_name_zh_hans": "步枪",
                "icon": "rifle.svg"
              },
              "item_quality": {
                "quality_id": 1,
                "quality_name": "久经沙场",
                "quality_name_en": "Field-Tested",
                "quality_name_zh_hans": "久经沙场",
                "quality_color": "#4b69ff"
              },
              "item_rarity": {
                "rarity_id": 4,
                "rarity_name": "保密",
                "rarity_name_en": "Classified",
                "rarity_name_zh_hans": "保密",
                "rarity_color": "#eb4b4b"
              },
              "item_exterior": {
                "exterior_id": 2,
                "exterior_name": "久经沙场",
                "exterior_name_en": "Field-Tested",
                "exterior_name_zh_hans": "久经沙场",
                "exterior_color": "#4b69ff"
              }
            }
          ],
          "win_items": [                // 获胜的物品(winner获得的所有物品)
            // 结构同 open_items
          ]
        }
      ]
    }
  }
}
```

### 3. WebSocket 实时消息格式

#### 3.1 消息频道和格式

系统使用统一的WebSocket频道发送不同类型的对战房间实时通知：

**WebSocket频道名称**: `ws_channel` (系统通用频道)

**消息类型**:

**1. 房间状态变化消息 (boxroom)**
- **消息格式**: `["boxroom", action, roomData]`
- **用途**: 房间级别的状态变化（创建、开始、更新、取消）
- **触发场景**: 房间创建、用户加入、房间满员、开始对战、房间取消

**2. 房间详情变化消息 (boxroomdetail)**  
- **消息格式**: `["boxroomdetail", action, betData, socketId]`
- **用途**: 房间内部详情变化（回合结果、对战结束）
- **触发场景**: 回合开启显示开箱结果、对战最终结束

> **频道设计说明**: 
> - 系统使用统一的`ws_channel`频道发送所有WebSocket消息
> - 通过消息数组的第一个元素（`boxroom`、`boxroomdetail`等）来区分不同类型的消息
> - 这种设计避免了多频道管理的复杂性，前端只需监听一个频道即可

#### 3.2 支持的Action类型

**boxroom频道消息类型**:
- `new`: 新房间创建 ✅
- `start`: 对战开始 ✅
- `update`: 房间状态更新(加入、满员等) ✅
- `cancel`: 房间取消 ✅

**boxroomdetail频道消息类型**:
- `round`: 回合开启，更新参与者开箱结果 ✅
- `end`: 对战结束，显示最终结果 ✅

#### 3.3 实际WebSocket消息示例

##### 新房间创建
```json
["boxroom", "new", {
  "uid": "room_789012",
  "short_id": "XYZ789",
  "state": 2,
  "max_joiner": 4,                  // 固定为4人
  "type": 3,                        // 固定为TeamBattle
  "user": {
    "uid": "user_123",
    "profile": {
      "nick_name": "张三",
      "avatar": "https://example.com/avatar.jpg"
    }
  },
  "joiner_count": 1,
  "round_count": 3,
  "create_time": "2024-01-01 12:00:00"
}]
```
##### 房间状态更新
```json
["boxroom", "update", {
  "uid": "room_123456",
  "state": 4,                       // 状态变为已满员
  "joiner_count": 4,                // 参与人数更新(满员4人)
  "bets": [                         // 更新后的参与者列表
    {
      "user": {
        "uid": "user_123",
        "profile": {
          "nick_name": "张三",
          "avatar": "https://example.com/avatar.jpg"
        }
      },
      "victory": null,
      "team": 1                     // 队伍信息
    },
    {
      "user": {
        "uid": "user_456", 
        "profile": {
          "nick_name": "李四",
          "avatar": "https://example.com/avatar2.jpg"
        }
      },
      "victory": null,
      "team": 2                     // 队伍信息
    }
  ],
  "update_time": "2024-01-01 12:05:00"
}]
```
##### 游戏开始
```json
{
  "action": "start",
  "room": {
    "uid": "room_123456",
    "state": 5,                       // 状态变为运行中
    "round_count_current": 0,         // 当前回合初始化
    "update_time": "2024-01-01 12:06:00"
  }
}
```

##### 回合进行中
```json
{
  "action": "round_progress",
  "room": {
    "uid": "room_123456", 
    "round_count_current": 2,         // 当前完成回合数
    "bets": [                         // 参与者当前状态
      {
        "uid": "bet_123",
        "user": {
          "uid": "user_123",
          "profile": {
            "nick_name": "张三"
          }
        },
        "open_amount": 51.60,         // 已开箱价值
        "open_items": [               // 新开出的物品
          {
            "uid": "room_item_002",
            "name": "M4A4 龙王",
            "item_price": {
              "price": 25.80
            },
            "item_rarity": {
              "rarity_color": "#d32ce6"
            }
          }
        ]
      }
    ]
  }
}
```

##### 游戏结束
```json
{
  "action": "end",
  "room": {
    "uid": "room_123456",
    "state": 11,                      // 状态变为已结束
    "round_count_current": 3,         // 所有回合完成
    "bets": [                         // 最终结算结果
      {
        "uid": "bet_123",
        "user": {
          "uid": "user_123",
          "profile": {
            "nick_name": "张三"
          }
        },
        "victory": true,              // 获胜者
        "open_amount": 77.40,
        "win_amount": 156.80,
        "win_items_count": 2,
        "win_items": [                // 获胜物品
          // 完整物品信息
        ]
      },
      {
        "uid": "bet_456", 
        "user": {
          "uid": "user_456",
          "profile": {
            "nick_name": "李四"
          }
        },
        "victory": false,             // 失败者
        "open_amount": 79.20,
        "win_amount": 0,
        "win_items_count": 0
      }
    ],
    "update_time": "2024-01-01 12:15:00"
  }
}
```

##### 房间取消
```json
{
  "action": "cancel",
  "room": {
    "uid": "room_123456",
    "state": 20,                      // 状态变为已取消
    "update_time": "2024-01-01 12:03:00"
  }
}
```

#### 3.2 用户操作事件

##### 用户加入房间
```json
{
  "event": "user_joined",
  "room_uid": "room_123456",
  "user": {
    "uid": "user_456",
    "profile": {
      "nick_name": "李四",
      "avatar": "https://example.com/avatar2.jpg"
    }
  },
  "bet_uid": "bet_456",
  "joiner_count": 2,
  "timestamp": "2024-01-01 12:05:00"
}
```

##### 用户退出房间
```json
{
  "event": "user_left",
  "room_uid": "room_123456",
  "user_uid": "user_456",
  "joiner_count": 1,
  "timestamp": "2024-01-01 12:04:00"
}
```

#### 3.3 开箱结果事件

##### 单次开箱结果
```json
{
  "event": "box_result",
  "room_uid": "room_123456",
  "user_id": "user_123",
  "round": 2,                         // 当前回合
  "case": {
    "key": "case_ak47",
    "name": "AK47武器箱"
  },
  "items": [                          // 本次开出的物品
    {
      "uid": "room_item_003",
      "name": "AK47 红线",
      "item_price": {
        "price": 128.50
      },
      "item_rarity": {
        "rarity_color": "#eb4b4b"
      }
    }
  ],
  "timestamp": "2024-01-01 12:10:00"
}
```

### 4. 错误码说明

#### 4.1 通用错误码
- `400`: 请求参数错误
- `401`: 用户未登录
- `403`: 权限不足
- `404`: 房间不存在
- `500`: 服务器内部错误

#### 4.2 业务错误码
- `10001`: 房间状态不允许此操作
- `10002`: 房间人数已满
- `10003`: 不能加入自己创建的房间
- `10004`: 余额不足
- `10005`: 箱子配置错误
- `10006`: 房间已过期
- `10007`: 用户已在房间中
- `10008`: 只有创建者可以取消房间
- `10009`: 队伍选择错误(TeamBattle) - 注：前端固定传team=1即可避免此错误
- `10010`: 房间类型不支持

#### 4.3 错误响应格式
```json
{
  "code": 10002,
  "message": "房间人数已满，无法加入",
  "data": null
}
```

### 5. 前端集成建议

#### 5.1 WebSocket连接
```javascript
// 连接WebSocket (具体地址根据实际环境配置)
const socket = io('ws://localhost:8000/ws/');

// 监听消息事件
socket.onmessage = function(event) {
  const message = JSON.parse(event.data);
  
  // 检查消息格式: [messageType, action, data, ...]
  if (Array.isArray(message)) {
    const [messageType, action, data] = message;
    
    if (messageType === 'boxroom') {
      // 房间级别状态变化
      handleRoomUpdate(action, data);
    } else if (messageType === 'boxroomdetail') {
      // 房间详情变化
      const socketId = message[3];
      handleRoomDetailUpdate(action, data, socketId);
    }
  }
};

// 连接错误处理
socket.onerror = function(error) {
  console.error('WebSocket连接错误:', error);
};

// 连接关闭处理
socket.onclose = function() {
  console.log('WebSocket连接已关闭，尝试重连...');
  // 实现重连逻辑
};
```

#### 5.2 状态管理
```javascript
// 房间状态更新处理
const handleRoomUpdate = (action, roomData) => {
  switch (action) {
    case 'new':
      // 新房间创建 (state: 2)
      addRoomToList(roomData);
      break;
      
    case 'update':
      // 房间状态更新：用户加入、满员等 (state: 2→4)
      updateRoomInList(roomData);
      break;
      
    case 'start':
      // 对战开始 (state: 5)
      markRoomAsStarted(roomData);
      break;
      
    case 'cancel':
      // 房间取消 (state: 20)
      removeRoomFromList(roomData.uid);
      break;
  }
};

// 房间详情更新处理
const handleRoomDetailUpdate = (action, betData, socketId) => {
  switch (action) {
    case 'round':
      // 回合开启，显示开箱结果
      updateRoomRound(betData);
      break;
      
    case 'end':
      // 对战结束 (state: 11)
      showFinalResult(betData);
      break;
  }
};
```

#### 5.3 实时数据同步
```javascript
// 保持房间列表与详情页的数据同步
const syncRoomData = (roomUid, newData) => {
  // 更新列表页
  if (roomListStore.rooms) {
    const index = roomListStore.rooms.findIndex(r => r.uid === roomUid);
    if (index !== -1) {
      roomListStore.rooms[index] = { ...roomListStore.rooms[index], ...newData };
    }
  }
  
  // 更新详情页
  if (roomDetailStore.room?.uid === roomUid) {
    roomDetailStore.room = { ...roomDetailStore.room, ...newData };
  }
};
```

### 6. 注意事项

1. **状态一致性**: 前端应始终以WebSocket推送的数据为准，API返回的数据可能存在延迟
2. **错误处理**: 所有API调用都应该有完善的错误处理机制
3. **重连机制**: WebSocket断线后应自动重连并重新加入相关频道
4. **数据缓存**: 合理使用本地缓存减少API调用频率
5. **权限验证**: 前端应根据用户角色控制操作按钮的显示
6. **状态展示**: 不同房间状态应有明确的视觉区分
7. **实时同步**: 确保列表页和详情页的数据实时同步
8. **team参数说明**: 虽然API保留team参数，但前端无需实现队伍选择功能，固定传1即可
9. **房间规格**: 所有对战房间固定为4人TeamBattle模式，前端UI可不显示队伍相关元素

---

完整API接口文档，包含详细的字段说明、请求响应示例、WebSocket消息格式和前端集成建议。
