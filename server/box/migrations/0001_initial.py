# -*- coding: utf-8 -*-
# Generated by Django 1.11.13 on 2021-04-14 02:09
from __future__ import unicode_literals

import box.models
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import steambase.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODELL),
        ('package', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Case',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('name', models.CharField(max_length=128, verbose_name='name')),
                ('key', models.CharField(max_length=128, unique=True, verbose_name='key')),
                ('price', models.FloatField(default=0, verbose_name='price')),
                ('discount', models.SmallIntegerField(default=100, verbose_name='discount(%)')),
                ('cover', models.ImageField(blank=True, default=None, null=True, upload_to='cases', verbose_name='cover image')),
                ('item', models.ImageField(blank=True, default=None, null=True, upload_to='cases', verbose_name='item image')),
                ('order', models.IntegerField(default=0, verbose_name='order')),
                ('unlock', models.BooleanField(default=True, verbose_name='unlock')),
                ('enable', models.BooleanField(default=True, verbose_name='enable')),
                ('enable_room', models.BooleanField(default=False, verbose_name='enable room')),
                ('enable_case', models.BooleanField(default=True, verbose_name='superuser enable case')),
                ('tag', models.CharField(blank=True, default=None, max_length=8, null=True, verbose_name='tag')),
                ('tag_color', models.SmallIntegerField(blank=True, choices=[(1, 'blue'), (2, 'purple'), (3, 'red')], default=None, null=True, verbose_name='tag color')),
                ('cost', models.FloatField(default=0, verbose_name='case cost')),
                ('up_sill', models.FloatField(default=0, verbose_name='up sill')),
                ('down_sill', models.FloatField(default=0, verbose_name='down sill')),
                ('dyntic', models.FloatField(default=0, verbose_name='dyntic')),
                ('algorithm_enable', models.BooleanField(default=False, verbose_name='algorithm_enable')),
            ],
            options={
                'verbose_name': 'Case',
                'verbose_name_plural': 'Case',
                'ordering': ('case_type', 'order'),
            },
        ),
        migrations.CreateModel(
            name='CaseBot',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('open_idle_min', models.IntegerField(default=0, verbose_name='open idle min(seconds)')),
                ('open_idle_max', models.IntegerField(default=0, verbose_name='open idle max(seconds)')),
                ('remark', models.CharField(max_length=128, verbose_name='remark')),
                ('enable', models.BooleanField(default=True, verbose_name='enable')),
                ('case', models.ManyToManyField(related_name='case_bot', to='box.Case', verbose_name='case')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='case_bot', to=settings.AUTH_USER_MODELL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'Case Bot Config',
                'verbose_name_plural': 'Case Bot Config',
            },
        ),
        migrations.CreateModel(
            name='CaseRecord',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('price', models.FloatField(default=0, verbose_name='price')),
                ('source', models.SmallIntegerField(choices=[(0, 'Case'), (1, 'Battle'), (2, 'Equality'), (3, 'TeamBattle')], default=0, verbose_name='type')),
                ('case', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='records', to='box.Case', verbose_name='case')),
                ('item_info', models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='case_records', to='package.ItemInfo', verbose_name='item info')),
                ('user', models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='case_records', to=settings.AUTH_USER_MODELL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'Case Record',
                'verbose_name_plural': 'Case Record',
            },
        ),
        migrations.CreateModel(
            name='CaseRoom',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('short_id', models.CharField(default=box.models.short_id_gen, editable=False, max_length=64, unique=True, verbose_name='short id')),
                ('max_joiner', models.IntegerField(default=0, verbose_name='max joiner')),
                ('price', models.FloatField(default=0, verbose_name='price')),
                ('state', models.SmallIntegerField(choices=[(1, 'Initial'), (2, 'Joinable'), (3, 'Joining'), (4, 'Full'), (5, 'Running'), (11, 'End'), (20, 'Cancelled')], default=1, verbose_name='status')),
                ('type', models.SmallIntegerField(choices=[(1, 'Battle'), (2, 'Equality'), (3, 'TeamBattle')], default=1, verbose_name='type')),
                ('private', models.SmallIntegerField(default=0, verbose_name='private')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='case_room', to=settings.AUTH_USER_MODELL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'Case Room',
                'verbose_name_plural': 'Case Room',
            },
        ),
        migrations.CreateModel(
            name='CaseRoomBet',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('open_amount', models.FloatField(default=0.0, verbose_name='open amount')),
                ('win_amount', models.FloatField(default=0.0, verbose_name='win amount')),
                ('win_items_count', models.IntegerField(default=0, verbose_name='win items count')),
                ('victory', models.BooleanField(default=False, verbose_name='victory')),
                ('team', models.SmallIntegerField(choices=[(0, 'null team'), (1, 'team T'), (2, 'team CT')], default=0, verbose_name='bet team')),
                ('room', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bets', to='box.CaseRoom', verbose_name='room')),
                ('user', models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='case_room_bets', to=settings.AUTH_USER_MODELL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'Case Room Bet',
                'verbose_name_plural': 'Case Room Bet',
            },
        ),
        migrations.CreateModel(
            name='CaseRoomItem',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('item_type', models.SmallIntegerField(choices=[(1, 'Assets'), (2, 'Free')], default=1, verbose_name='item type')),
                ('price', models.FloatField(default=0, verbose_name='price')),
                ('part', models.BooleanField(default=False, verbose_name='part price of item')),
                ('split', models.BooleanField(default=False, verbose_name='item need to split')),
                ('bet', models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='open_items', to='box.CaseRoomBet', verbose_name='bet')),
                ('item_info', models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='case_room_items', to='package.ItemInfo', verbose_name='item info')),
                ('room', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='box.CaseRoom', verbose_name='room')),
            ],
            options={
                'verbose_name': 'Case Room Item',
                'verbose_name_plural': 'Case Room Item',
            },
        ),
        migrations.CreateModel(
            name='CaseRoomRound',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('opened', models.BooleanField(default=False, verbose_name='opened')),
                ('case', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='room_rounds', to='box.Case', verbose_name='case')),
                ('room', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='rounds', to='box.CaseRoom', verbose_name='room')),
            ],
            options={
                'verbose_name': 'Case Room Round',
                'verbose_name_plural': 'Case Room Round',
            },
        ),
        migrations.CreateModel(
            name='CaseStatisticsDay',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('date', models.DateField(default=django.utils.timezone.now, verbose_name='date')),
                ('amount', models.FloatField(default=0.0, verbose_name='amount')),
            ],
            options={
                'verbose_name': 'Case Statistics Day',
                'verbose_name_plural': 'Case Statistics Day',
                'ordering': ('-create_time',),
            },
        ),
        migrations.CreateModel(
            name='CaseStatisticsMonth',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('date', models.DateField(default=django.utils.timezone.now, verbose_name='date')),
                ('amount', models.FloatField(default=0.0, verbose_name='amount')),
            ],
            options={
                'verbose_name': 'Case Statistics Month',
                'verbose_name_plural': 'Case Statistics Month',
                'ordering': ('-create_time',),
            },
        ),
        migrations.CreateModel(
            name='CaseType',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128, verbose_name='name')),
                ('category', models.SmallIntegerField(choices=[(1, 'Normal'), (2, 'Top'), (3, 'Free'), (4, 'Festival'), (5, 'FreeGive'), (6, 'Promotion')], verbose_name='category')),
                ('order', models.IntegerField(default=0, verbose_name='order')),
            ],
            options={
                'verbose_name': 'Case Type',
                'verbose_name_plural': 'Case Type',
                'ordering': ('category', 'order'),
            },
        ),
        migrations.CreateModel(
            name='DropDayRank',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('date', models.DateField(default=django.utils.timezone.now, verbose_name='date')),
                ('amount', models.FloatField(default=0, verbose_name='amount')),
                ('item_info', models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='drop_day_rank', to='package.ItemInfo', verbose_name='item info')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='drop_day_rank', to=settings.AUTH_USER_MODELL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'Drop Day Rank',
                'verbose_name_plural': 'Drop Day Rank',
            },
        ),
        migrations.CreateModel(
            name='DropItem',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('show_chance', models.FloatField(default=0, verbose_name='show chance')),
                ('drop_chance_a', models.FloatField(default=0, verbose_name='drop chance a')),
                ('drop_chance_b', models.FloatField(default=0, verbose_name='drop chance b')),
                ('drop_chance_c', models.FloatField(default=0, verbose_name='drop chance c')),
                ('drop_chance_d', models.FloatField(default=0, verbose_name='drop chance d')),
                ('drop_chance_e', models.FloatField(default=0, verbose_name='drop chance e')),
                ('item_type', models.SmallIntegerField(choices=[(1, 'Assets')], default=1, verbose_name='item type')),
                ('coins', models.FloatField(default=0, verbose_name='diamond')),
                ('custom_enable', models.BooleanField(default=False, verbose_name='custom enable')),
                ('custom_price', models.FloatField(blank=True, default=0, null=True, verbose_name='custom price')),
                ('up_sill_drop', models.BooleanField(default=False, verbose_name='up sill drop')),
                ('down_sill_drop', models.BooleanField(default=False, verbose_name='down sill drop')),
                ('case', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='drops', to='box.Case', verbose_name='case')),
                ('item_info', models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='case_drops', to='package.ItemInfo', verbose_name='item info')),
            ],
            options={
                'verbose_name': 'Drop Item',
                'verbose_name_plural': 'Drop Item',
            },
        ),
        migrations.CreateModel(
            name='FestivalCaseConfig',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('level', models.IntegerField(default=0, verbose_name='level')),
                ('min_amount', models.FloatField(default=0, verbose_name='min charge total amount')),
                ('max_amount', models.FloatField(default=0, verbose_name='max charge total amount')),
                ('case', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='festival_case', to='box.Case', verbose_name='case')),
            ],
            options={
                'verbose_name': 'Festival Case Config',
                'verbose_name_plural': 'Festival Case Config',
                'ordering': ('level',),
            },
        ),
        migrations.CreateModel(
            name='FestivalCaseDate',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('month', models.IntegerField(default=1, verbose_name='month')),
                ('day', models.IntegerField(default=1, verbose_name='day')),
                ('enable', models.BooleanField(default=True, verbose_name='enable')),
            ],
            options={
                'verbose_name': 'Festival Case Date',
                'verbose_name_plural': 'Festival Case Date',
                'ordering': ('month', 'day'),
            },
        ),
        migrations.CreateModel(
            name='FestivalCaseRecord',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('opened', models.BooleanField(default=False, verbose_name='opened')),
                ('expired', models.DateTimeField(verbose_name='expired time')),
                ('case', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='festival_record', to='box.Case', verbose_name='case')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='festival_record', to=settings.AUTH_USER_MODELL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'Festival Case Record',
                'verbose_name_plural': 'Festival Case Record',
            },
        ),
        migrations.CreateModel(
            name='FreeCaseConfig',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('level', models.IntegerField(default=0, verbose_name='level')),
                ('min_amount', models.FloatField(default=0, verbose_name='min point')),
                ('max_amount', models.FloatField(default=0, verbose_name='max point')),
                ('case', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='free_case', to='box.Case', verbose_name='case')),
            ],
            options={
                'verbose_name': 'Free Case Config',
                'verbose_name_plural': 'Free Case Config',
                'ordering': ('level',),
            },
        ),
        migrations.CreateModel(
            name='IncomeDayRank',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('date', models.DateField(default=django.utils.timezone.now, verbose_name='date')),
                ('amount', models.FloatField(default=0, verbose_name='amount')),
                ('case', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='income_day_rank', to='box.Case', verbose_name='case')),
                ('item_info', models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='income_day_rank', to='package.ItemInfo', verbose_name='item info')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='income_day_rank', to=settings.AUTH_USER_MODELL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'Income Day Rank',
                'verbose_name_plural': 'Income Day Rank',
            },
        ),
        migrations.CreateModel(
            name='LoseWeekRank',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('year', models.SmallIntegerField(default=0, verbose_name='year')),
                ('week', models.SmallIntegerField(default=0, verbose_name='week')),
                ('amount', models.FloatField(default=0, verbose_name='amount')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lose_week_rank', to=settings.AUTH_USER_MODELL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'Lose Week Rank',
                'verbose_name_plural': 'Lose Week Rank',
            },
        ),
        migrations.CreateModel(
            name='RoomDayRank',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('date', models.DateField(default=django.utils.timezone.now, verbose_name='date')),
                ('amount', models.FloatField(default=0, verbose_name='amount')),
                ('room', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='room_day_rank', to='box.CaseRoom', verbose_name='room')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='room_day_rank', to=settings.AUTH_USER_MODELL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'Room Day Rank',
                'verbose_name_plural': 'Room Day Rank',
            },
        ),
        migrations.AddField(
            model_name='caseroomitem',
            name='round',
            field=models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='items', to='box.CaseRoomRound', verbose_name='round'),
        ),
        migrations.AddField(
            model_name='caseroomitem',
            name='winner',
            field=models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='win_items', to='box.CaseRoomBet', verbose_name='winner'),
        ),
        migrations.AddField(
            model_name='case',
            name='case_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='cases', to='box.CaseType', verbose_name='case type'),
        ),
        migrations.AddField(
            model_name='case',
            name='present',
            field=models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='case_present', to='package.ItemInfo', verbose_name='present'),
        ),
    ]
