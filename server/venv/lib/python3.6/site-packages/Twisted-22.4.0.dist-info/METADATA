Metadata-Version: 2.1
Name: Twisted
Version: 22.4.0
Summary: An asynchronous networking framework written in Python
Home-page: https://twistedmatrix.com/
Author: Twisted Matrix Laboratories
Author-email: <EMAIL>
Maintainer: Gly<PERSON>
Maintainer-email: <EMAIL>
License: MIT
Project-URL: Documentation, https://docs.twistedmatrix.com/
Project-URL: Source, https://github.com/twisted/twisted
Project-URL: Issues, https://twistedmatrix.com/trac/report
Project-URL: Twitter, https://twitter.com/twistedmatrix
Project-URL: Changelog, https://github.com/twisted/twisted/blob/HEAD/NEWS.rst
Platform: UNKNOWN
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Requires-Python: >=3.6.7
Description-Content-Type: text/x-rst
License-File: LICENSE
Requires-Dist: zope.interface (>=4.4.2)
Requires-Dist: constantly (>=15.1)
Requires-Dist: incremental (>=21.3.0)
Requires-Dist: Automat (>=0.8.0)
Requires-Dist: hyperlink (>=17.1.1)
Requires-Dist: attrs (>=19.2.0)
Requires-Dist: typing-extensions (>=3.6.5)
Requires-Dist: twisted-iocpsupport (<2,>=1.0.2) ; platform_system == "Windows"
Provides-Extra: all_non_platform
Requires-Dist: cython-test-exception-raiser (<2,>=1.0.2) ; extra == 'all_non_platform'
Requires-Dist: PyHamcrest (>=1.9.0) ; extra == 'all_non_platform'
Requires-Dist: pyopenssl (>=16.0.0) ; extra == 'all_non_platform'
Requires-Dist: service-identity (>=18.1.0) ; extra == 'all_non_platform'
Requires-Dist: idna (>=2.4) ; extra == 'all_non_platform'
Requires-Dist: pyasn1 ; extra == 'all_non_platform'
Requires-Dist: cryptography (>=2.6) ; extra == 'all_non_platform'
Requires-Dist: appdirs (>=1.4.0) ; extra == 'all_non_platform'
Requires-Dist: bcrypt (>=3.0.0) ; extra == 'all_non_platform'
Requires-Dist: pyserial (>=3.0) ; extra == 'all_non_platform'
Requires-Dist: h2 (<5.0,>=3.0) ; extra == 'all_non_platform'
Requires-Dist: priority (<2.0,>=1.1.0) ; extra == 'all_non_platform'
Requires-Dist: pywin32 (!=226) ; (platform_system == "Windows") and extra == 'all_non_platform'
Requires-Dist: contextvars (<3,>=2.4) ; (python_version < "3.7") and extra == 'all_non_platform'
Provides-Extra: conch
Requires-Dist: pyasn1 ; extra == 'conch'
Requires-Dist: cryptography (>=2.6) ; extra == 'conch'
Requires-Dist: appdirs (>=1.4.0) ; extra == 'conch'
Requires-Dist: bcrypt (>=3.0.0) ; extra == 'conch'
Provides-Extra: conch_nacl
Requires-Dist: pyasn1 ; extra == 'conch_nacl'
Requires-Dist: cryptography (>=2.6) ; extra == 'conch_nacl'
Requires-Dist: appdirs (>=1.4.0) ; extra == 'conch_nacl'
Requires-Dist: bcrypt (>=3.0.0) ; extra == 'conch_nacl'
Requires-Dist: PyNaCl ; extra == 'conch_nacl'
Provides-Extra: contextvars
Requires-Dist: contextvars (<3,>=2.4) ; (python_version < "3.7") and extra == 'contextvars'
Provides-Extra: dev
Requires-Dist: towncrier (~=19.2) ; extra == 'dev'
Requires-Dist: sphinx-rtd-theme (~=0.5) ; extra == 'dev'
Requires-Dist: readthedocs-sphinx-ext (~=2.1) ; extra == 'dev'
Requires-Dist: sphinx (<6,>=4.1.2) ; extra == 'dev'
Requires-Dist: pyflakes (~=2.2) ; extra == 'dev'
Requires-Dist: twistedchecker (~=0.7) ; extra == 'dev'
Requires-Dist: coverage (<7,>=6b1) ; extra == 'dev'
Requires-Dist: python-subunit (~=1.4) ; (python_version < "3.10") and extra == 'dev'
Requires-Dist: pydoctor (~=21.9.0) ; (python_version >= "3.6") and extra == 'dev'
Provides-Extra: dev_release
Requires-Dist: towncrier (~=19.2) ; extra == 'dev_release'
Requires-Dist: sphinx-rtd-theme (~=0.5) ; extra == 'dev_release'
Requires-Dist: readthedocs-sphinx-ext (~=2.1) ; extra == 'dev_release'
Requires-Dist: sphinx (<6,>=4.1.2) ; extra == 'dev_release'
Requires-Dist: pydoctor (~=21.9.0) ; (python_version >= "3.6") and extra == 'dev_release'
Provides-Extra: http2
Requires-Dist: h2 (<5.0,>=3.0) ; extra == 'http2'
Requires-Dist: priority (<2.0,>=1.1.0) ; extra == 'http2'
Provides-Extra: macos_platform
Requires-Dist: pyobjc-core ; extra == 'macos_platform'
Requires-Dist: pyobjc-framework-CFNetwork ; extra == 'macos_platform'
Requires-Dist: pyobjc-framework-Cocoa ; extra == 'macos_platform'
Requires-Dist: cython-test-exception-raiser (<2,>=1.0.2) ; extra == 'macos_platform'
Requires-Dist: PyHamcrest (>=1.9.0) ; extra == 'macos_platform'
Requires-Dist: pyopenssl (>=16.0.0) ; extra == 'macos_platform'
Requires-Dist: service-identity (>=18.1.0) ; extra == 'macos_platform'
Requires-Dist: idna (>=2.4) ; extra == 'macos_platform'
Requires-Dist: pyasn1 ; extra == 'macos_platform'
Requires-Dist: cryptography (>=2.6) ; extra == 'macos_platform'
Requires-Dist: appdirs (>=1.4.0) ; extra == 'macos_platform'
Requires-Dist: bcrypt (>=3.0.0) ; extra == 'macos_platform'
Requires-Dist: pyserial (>=3.0) ; extra == 'macos_platform'
Requires-Dist: h2 (<5.0,>=3.0) ; extra == 'macos_platform'
Requires-Dist: priority (<2.0,>=1.1.0) ; extra == 'macos_platform'
Requires-Dist: pywin32 (!=226) ; (platform_system == "Windows") and extra == 'macos_platform'
Requires-Dist: contextvars (<3,>=2.4) ; (python_version < "3.7") and extra == 'macos_platform'
Provides-Extra: mypy
Requires-Dist: mypy (==0.930) ; extra == 'mypy'
Requires-Dist: mypy-zope (==0.3.4) ; extra == 'mypy'
Requires-Dist: types-setuptools ; extra == 'mypy'
Requires-Dist: types-pyOpenSSL ; extra == 'mypy'
Requires-Dist: towncrier (~=19.2) ; extra == 'mypy'
Requires-Dist: sphinx-rtd-theme (~=0.5) ; extra == 'mypy'
Requires-Dist: readthedocs-sphinx-ext (~=2.1) ; extra == 'mypy'
Requires-Dist: sphinx (<6,>=4.1.2) ; extra == 'mypy'
Requires-Dist: pyflakes (~=2.2) ; extra == 'mypy'
Requires-Dist: twistedchecker (~=0.7) ; extra == 'mypy'
Requires-Dist: coverage (<7,>=6b1) ; extra == 'mypy'
Requires-Dist: cython-test-exception-raiser (<2,>=1.0.2) ; extra == 'mypy'
Requires-Dist: PyHamcrest (>=1.9.0) ; extra == 'mypy'
Requires-Dist: pyopenssl (>=16.0.0) ; extra == 'mypy'
Requires-Dist: service-identity (>=18.1.0) ; extra == 'mypy'
Requires-Dist: idna (>=2.4) ; extra == 'mypy'
Requires-Dist: pyasn1 ; extra == 'mypy'
Requires-Dist: cryptography (>=2.6) ; extra == 'mypy'
Requires-Dist: appdirs (>=1.4.0) ; extra == 'mypy'
Requires-Dist: bcrypt (>=3.0.0) ; extra == 'mypy'
Requires-Dist: pyserial (>=3.0) ; extra == 'mypy'
Requires-Dist: h2 (<5.0,>=3.0) ; extra == 'mypy'
Requires-Dist: priority (<2.0,>=1.1.0) ; extra == 'mypy'
Requires-Dist: PyNaCl ; extra == 'mypy'
Requires-Dist: pywin32 (!=226) ; (platform_system == "Windows") and extra == 'mypy'
Requires-Dist: python-subunit (~=1.4) ; (python_version < "3.10") and extra == 'mypy'
Requires-Dist: contextvars (<3,>=2.4) ; (python_version < "3.7") and extra == 'mypy'
Requires-Dist: pydoctor (~=21.9.0) ; (python_version >= "3.6") and extra == 'mypy'
Provides-Extra: osx_platform
Requires-Dist: pyobjc-core ; extra == 'osx_platform'
Requires-Dist: pyobjc-framework-CFNetwork ; extra == 'osx_platform'
Requires-Dist: pyobjc-framework-Cocoa ; extra == 'osx_platform'
Requires-Dist: cython-test-exception-raiser (<2,>=1.0.2) ; extra == 'osx_platform'
Requires-Dist: PyHamcrest (>=1.9.0) ; extra == 'osx_platform'
Requires-Dist: pyopenssl (>=16.0.0) ; extra == 'osx_platform'
Requires-Dist: service-identity (>=18.1.0) ; extra == 'osx_platform'
Requires-Dist: idna (>=2.4) ; extra == 'osx_platform'
Requires-Dist: pyasn1 ; extra == 'osx_platform'
Requires-Dist: cryptography (>=2.6) ; extra == 'osx_platform'
Requires-Dist: appdirs (>=1.4.0) ; extra == 'osx_platform'
Requires-Dist: bcrypt (>=3.0.0) ; extra == 'osx_platform'
Requires-Dist: pyserial (>=3.0) ; extra == 'osx_platform'
Requires-Dist: h2 (<5.0,>=3.0) ; extra == 'osx_platform'
Requires-Dist: priority (<2.0,>=1.1.0) ; extra == 'osx_platform'
Requires-Dist: pywin32 (!=226) ; (platform_system == "Windows") and extra == 'osx_platform'
Requires-Dist: contextvars (<3,>=2.4) ; (python_version < "3.7") and extra == 'osx_platform'
Provides-Extra: serial
Requires-Dist: pyserial (>=3.0) ; extra == 'serial'
Requires-Dist: pywin32 (!=226) ; (platform_system == "Windows") and extra == 'serial'
Provides-Extra: test
Requires-Dist: cython-test-exception-raiser (<2,>=1.0.2) ; extra == 'test'
Requires-Dist: PyHamcrest (>=1.9.0) ; extra == 'test'
Provides-Extra: tls
Requires-Dist: pyopenssl (>=16.0.0) ; extra == 'tls'
Requires-Dist: service-identity (>=18.1.0) ; extra == 'tls'
Requires-Dist: idna (>=2.4) ; extra == 'tls'
Provides-Extra: windows_platform
Requires-Dist: pywin32 (!=226) ; extra == 'windows_platform'
Requires-Dist: cython-test-exception-raiser (<2,>=1.0.2) ; extra == 'windows_platform'
Requires-Dist: PyHamcrest (>=1.9.0) ; extra == 'windows_platform'
Requires-Dist: pyopenssl (>=16.0.0) ; extra == 'windows_platform'
Requires-Dist: service-identity (>=18.1.0) ; extra == 'windows_platform'
Requires-Dist: idna (>=2.4) ; extra == 'windows_platform'
Requires-Dist: pyasn1 ; extra == 'windows_platform'
Requires-Dist: cryptography (>=2.6) ; extra == 'windows_platform'
Requires-Dist: appdirs (>=1.4.0) ; extra == 'windows_platform'
Requires-Dist: bcrypt (>=3.0.0) ; extra == 'windows_platform'
Requires-Dist: pyserial (>=3.0) ; extra == 'windows_platform'
Requires-Dist: h2 (<5.0,>=3.0) ; extra == 'windows_platform'
Requires-Dist: priority (<2.0,>=1.1.0) ; extra == 'windows_platform'
Requires-Dist: pywin32 (!=226) ; (platform_system == "Windows") and extra == 'windows_platform'
Requires-Dist: contextvars (<3,>=2.4) ; (python_version < "3.7") and extra == 'windows_platform'

Twisted
=======

|gitter|_
|rtd|_
|pypi|_
|mypy|_

For information on changes in this release, see the `NEWS <https://github.com/twisted/twisted/blob/trunk/NEWS.rst>`_ file.


What is this?
-------------

Twisted is an event-based framework for internet applications, supporting Python 3.6+.
It includes modules for many different purposes, including the following:

- ``twisted.web``: HTTP clients and servers, HTML templating, and a WSGI server
- ``twisted.conch``: SSHv2 and Telnet clients and servers and terminal emulators
- ``twisted.words``: Clients and servers for IRC, XMPP, and other IM protocols
- ``twisted.mail``: IMAPv4, POP3, SMTP clients and servers
- ``twisted.positioning``: Tools for communicating with NMEA-compatible GPS receivers
- ``twisted.names``: DNS client and tools for making your own DNS servers
- ``twisted.trial``: A unit testing framework that integrates well with Twisted-based code.

Twisted supports all major system event loops -- ``select`` (all platforms), ``poll`` (most POSIX platforms), ``epoll`` (Linux), ``kqueue`` (FreeBSD, macOS), IOCP (Windows), and various GUI event loops (GTK+2/3, Qt, wxWidgets).
Third-party reactors can plug into Twisted, and provide support for additional event loops.


Installing
----------

To install the latest version of Twisted using pip::

  $ pip install twisted

Additional instructions for installing this software are in `the installation instructions <https://github.com/twisted/twisted/blob/trunk/INSTALL.rst>`_.


Documentation and Support
-------------------------

Twisted's documentation is available from the `Twisted Matrix website <https://twistedmatrix.com/documents/current/>`_.
This documentation contains how-tos, code examples, and an API reference.

Help is also available on the `Twisted mailing list <https://twistedmatrix.com/cgi-bin/mailman/listinfo/twisted-python>`_.

There is also a pair of very lively IRC channels, ``#twisted`` (for general Twisted questions) and ``#twisted.web`` (for Twisted Web),
on `irc.libera.chat <https://web.libera.chat/>_`.


Unit Tests
----------

Twisted has a comprehensive test suite, which can be run by ``tox``::

  $ tox -l                       # to view all test environments
  $ tox -e nocov                 # to run all the tests without coverage
  $ tox -e withcov               # to run all the tests with coverage
  $ tox -e alldeps-withcov-posix # install all dependencies, run tests with coverage on POSIX platform


You can test running the test suite under the different reactors with the ``TWISTED_REACTOR`` environment variable::

  $ env TWISTED_REACTOR=epoll tox -e alldeps-withcov-posix

Some of these tests may fail if you:

* don't have the dependencies required for a particular subsystem installed,
* have a firewall blocking some ports (or things like Multicast, which Linux NAT has shown itself to do), or
* run them as root.


Static Code Checkers
--------------------

You can ensure that code complies to Twisted `coding standards <https://twistedmatrix.com/documents/current/core/development/policy/coding-standard.html>`_::

  $ tox -e lint   # run pre-commit to check coding stanards
  $ tox -e mypy   # run MyPy static type checker to check for type errors

Or, for speed, use pre-commit directly::

  $ pipx run pre-commit run


Copyright
---------

All of the code in this distribution is Copyright (c) 2001-2022 Twisted Matrix Laboratories.

Twisted is made available under the MIT license.
The included `LICENSE <https://github.com/twisted/twisted/blob/trunk/LICENSE>`_ file describes this in detail.


Warranty
--------

  THIS SOFTWARE IS PROVIDED "AS IS" WITHOUT WARRANTY OF ANY KIND, EITHER
  EXPRESSED OR IMPLIED, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
  OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.  THE ENTIRE RISK AS
  TO THE USE OF THIS SOFTWARE IS WITH YOU.

  IN NO EVENT WILL ANY COPYRIGHT HOLDER, OR ANY OTHER PARTY WHO MAY MODIFY
  AND/OR REDISTRIBUTE THE LIBRARY, BE LIABLE TO YOU FOR ANY DAMAGES, EVEN IF
  SUCH HOLDER OR OTHER PARTY HAS BEEN ADVISED OF THE POSSIBILITY OF SUCH
  DAMAGES.

Again, see the included `LICENSE <https://github.com/twisted/twisted/blob/trunk/LICENSE>`_ file for specific legal details.


.. |pypi| image:: https://img.shields.io/pypi/v/twisted.svg
.. _pypi: https://pypi.python.org/pypi/twisted

.. |gitter| image:: https://img.shields.io/gitter/room/twisted/twisted.svg
.. _gitter: https://gitter.im/twisted/twisted

.. |mypy| image:: https://github.com/twisted/twisted/workflows/mypy/badge.svg
.. _mypy: https://github.com/twisted/twisted

.. |rtd| image:: https://readthedocs.org/projects/twisted/badge/?version=latest&style=flat
.. _rtd: https://docs.twistedmatrix.com


