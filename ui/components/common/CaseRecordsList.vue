<!-- components/common/CaseRecordsList.vue -->
<template>
  <div class="case-records-list">
    <div class="records-header">
      <h3 class="records-title">{{ $t('caseRecords.title') }}</h3>
      <div class="connection-status" :class="connectionStatusClass">
        <div class="status-indicator">
          <div class="status-dot"></div>
          <div v-if="socketStore.isConnected" class="pulse-ring"></div>
        </div>
        <span>{{ connectionStatusText }}</span>
      </div>
    </div>
    
    <div class="records-container">
      <TransitionGroup name="record-fade" tag="div" class="records-list">
        <div 
          v-for="record in visibleRecords" 
          :key="record.id" 
          class="record-item"
        >
          <div class="record-user">
            <i class="fas fa-user"></i>
            <span>{{ record.user_name || $t('caseRecords.anonymous') }}</span>
          </div>
          <div class="record-details">
            <div class="record-case">{{ record.case_name }}</div>
            <div class="record-arrow">
              <i class="fas fa-arrow-right"></i>
            </div>
            <div class="record-skin" :class="getSkinRarityClass(record.skin_name)">
              {{ record.skin_name }}
              <span v-if="record.skin_quality" class="record-quality">
                ({{ record.skin_quality }})
              </span>
            </div>
          </div>
          <div class="record-time">{{ formatTime(record.created_at) }}</div>
        </div>
      </TransitionGroup>
      
      <div v-if="visibleRecords.length === 0" class="no-records">
        <i class="fas fa-box-open"></i>
        <p>{{ $t('caseRecords.noRecords') }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useSocketStore } from '@/stores/socket'
import { useI18n } from 'vue-i18n'

interface CaseRecord {
  id: string | number
  user_name: string
  case_name: string
  skin_name: string
  skin_quality?: string
  created_at: string
}

const socketStore = useSocketStore()
const { t } = useI18n()

// 控制显示的记录数量
const maxRecords = 10

// 计算可见记录
const visibleRecords = computed(() => {
  return socketStore.caseRecords.slice(0, maxRecords)
})

// 连接状态样式类
const connectionStatusClass = computed(() => ({
  'connected': socketStore.isConnected,
  'connecting': !socketStore.isConnected && !socketStore.connectionError,
  'error': !!socketStore.connectionError
}))

// 连接状态文本
const connectionStatusText = computed(() => {
  if (socketStore.connectionError) {
    return t('caseRecords.connectionError')
  }
  return socketStore.isConnected ? t('caseRecords.connected') : t('caseRecords.connecting')
})

// 格式化时间
const formatTime = (timeStr: string): string => {
  if (!timeStr) return ''
  
  try {
    if (!process.client) {
      return timeStr
    }
    
    const date = new Date(timeStr)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    
    // 如果小于1分钟，显示"刚刚"
    if (diffMs < 60000) {
      return t('caseRecords.justNow')
    }
    
    // 如果小于1小时，显示几分钟前
    if (diffMs < 3600000) {
      const minutes = Math.floor(diffMs / 60000)
      return t('caseRecords.minutesAgo', { minutes })
    }
    
    // 如果是今天，显示时:分
    if (date.toDateString() === now.toDateString()) {
      const hours = date.getHours().toString().padStart(2, '0')
      const minutes = date.getMinutes().toString().padStart(2, '0')
      return t('caseRecords.today', { time: `${hours}:${minutes}` })
    }
    
    // 如果是昨天
    const yesterday = new Date(now)
    yesterday.setDate(yesterday.getDate() - 1)
    if (date.toDateString() === yesterday.toDateString()) {
      const hours = date.getHours().toString().padStart(2, '0')
      const minutes = date.getMinutes().toString().padStart(2, '0')
      return t('caseRecords.yesterday', { time: `${hours}:${minutes}` })
    }
    
    // 其他情况显示年-月-日
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    return `${year}-${month}-${day}`
  } catch (e) {
    console.error('Error formatting time:', e)
    return timeStr
  }
}

// 根据皮肤名称获取稀有度样式类
const getSkinRarityClass = (skinName: string): string => {
  if (!skinName) return 'rarity-normal'
  
  // 刀具检测
  if (skinName.includes('★')) return 'rarity-knife'
  
  // 稀有度关键词映射
  const rarityMap = [
    { keywords: ['暗金'], class: 'rarity-gold' },
    { keywords: ['红色', '霓虹骑士', '伽玛多普勒', '渐变大理石', '多普勒', '虎牙', '猛虎'], class: 'rarity-red' },
    { keywords: ['粉色', '血腥运动', '咆哮', '二西莫夫', '野火', '皇帝', '黑色魅影'], class: 'rarity-pink' },
    { keywords: ['紫色'], class: 'rarity-purple' },
    { keywords: ['蓝色'], class: 'rarity-blue' },
    { keywords: ['浅蓝'], class: 'rarity-lightblue' },
    { keywords: ['白色'], class: 'rarity-white' }
  ]
  
  for (const rarity of rarityMap) {
    if (rarity.keywords.some(keyword => skinName.includes(keyword))) {
      return rarity.class
    }
  }
  
  return 'rarity-normal'
}

// 组件挂载时初始化Socket连接
onMounted(() => {
  if (process.client && !socketStore.isConnected) {
    socketStore.initializeSocket()
  }
})
</script>

<style lang="scss" scoped>
$header-bg: rgba(17, 24, 39, 0.95);
$container-bg: rgba(31, 41, 55, 0.9);
$item-bg: rgba(55, 65, 81, 0.8);
$border-color: rgba(107, 114, 128, 0.3);

.case-records-list {
  width: 100%;
  border-radius: 0.75rem;
  overflow: hidden;
  background: $container-bg;
  backdrop-filter: blur(10px);
  border: 1px solid $border-color;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.records-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: $header-bg;
  border-bottom: 1px solid $border-color;
}

.records-title {
  font-size: 1.125rem;
  font-weight: 700;
  color: white;
  margin: 0;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  
  &.connected {
    color: #10b981;
  }
  
  &.connecting {
    color: #f59e0b;
  }
  
  &.error {
    color: #ef4444;
  }
}

.status-indicator {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ef4444;
  position: relative;
  z-index: 2;
}

.pulse-ring {
  position: absolute;
  width: 16px;
  height: 16px;
  border: 2px solid #10b981;
  border-radius: 50%;
  animation: pulse 2s infinite;
  z-index: 1;
}

.connection-status.connected .status-dot {
  background: #10b981;
}

.connection-status.connecting .status-dot {
  background: #f59e0b;
  animation: blink 1.5s infinite;
}

.records-container {
  padding: 1rem;
  max-height: 24rem;
  overflow-y: auto;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(107, 114, 128, 0.1);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(107, 114, 128, 0.3);
    border-radius: 3px;
    
    &:hover {
      background: rgba(107, 114, 128, 0.5);
    }
  }
}

.records-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.record-item {
  padding: 0.75rem;
  background: $item-bg;
  border-radius: 0.5rem;
  border: 1px solid rgba(107, 114, 128, 0.2);
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(75, 85, 99, 0.8);
    border-color: rgba(107, 114, 128, 0.4);
    transform: translateY(-1px);
  }
}

.record-user {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #fbbf24;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
  font-weight: 500;
  
  i {
    opacity: 0.8;
  }
}

.record-details {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin-bottom: 0.5rem;
}

.record-case {
  color: #d1d5db;
  font-size: 0.875rem;
  font-weight: 500;
}

.record-arrow {
  color: #6b7280;
  font-size: 0.75rem;
  
  i {
    opacity: 0.7;
  }
}

.record-skin {
  font-weight: 600;
  font-size: 0.875rem;
  flex: 1;
}

.record-quality {
  color: #9ca3af;
  font-size: 0.75rem;
  margin-left: 0.25rem;
  font-weight: 400;
}

.record-time {
  text-align: right;
  color: #9ca3af;
  font-size: 0.75rem;
  font-weight: 400;
}

.no-records {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  padding: 2.5rem 1rem;
  text-align: center;
  
  i {
    font-size: 2.5rem;
    margin-bottom: 0.75rem;
    opacity: 0.6;
  }
  
  p {
    margin: 0;
    font-size: 0.875rem;
  }
}

// 稀有度样式
.rarity-knife {
  color: #fbbf24;
  text-shadow: 0 0 10px rgba(251, 191, 36, 0.5);
}

.rarity-gold {
  color: #fbbf24;
  text-shadow: 0 0 8px rgba(251, 191, 36, 0.4);
}

.rarity-red {
  color: #f87171;
  text-shadow: 0 0 8px rgba(248, 113, 113, 0.4);
}

.rarity-pink {
  color: #f472b6;
  text-shadow: 0 0 8px rgba(244, 114, 182, 0.4);
}

.rarity-purple {
  color: #a78bfa;
  text-shadow: 0 0 8px rgba(167, 139, 250, 0.4);
}

.rarity-blue {
  color: #60a5fa;
  text-shadow: 0 0 8px rgba(96, 165, 250, 0.4);
}

.rarity-lightblue {
  color: #7dd3fc;
  text-shadow: 0 0 8px rgba(125, 211, 252, 0.4);
}

.rarity-white {
  color: #e5e7eb;
}

.rarity-normal {
  color: white;
}

// 动画
@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.8);
    opacity: 0;
  }
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0.3;
  }
}

// 过渡动画
.record-fade-enter-active,
.record-fade-leave-active {
  transition: all 0.5s ease;
}

.record-fade-enter-from {
  opacity: 0;
  transform: translateY(-10px);
}

.record-fade-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

.record-fade-move {
  transition: transform 0.5s ease;
}

// 响应式设计
@media (max-width: 768px) {
  .records-header {
    padding: 0.75rem;
  }
  
  .records-title {
    font-size: 1rem;
  }
  
  .connection-status {
    font-size: 0.75rem;
  }
  
  .record-item {
    padding: 0.5rem;
  }
  
  .record-details {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
  
  .record-arrow {
    display: none;
  }
}
</style>
