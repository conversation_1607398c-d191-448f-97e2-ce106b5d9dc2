import string

from django.conf import settings
from django.db import models
from django.utils.translation import gettext_lazy as _

from package.models import ItemInfo
from steambase.models import ModelBase
from steambase.enums import GrabCurrencyType, GrabRoomStatus
from steambase.storage import ImageStorage
from steambase.utils import id_generator

USER_MODEL = settings.AUTH_USER_MODEL


def short_id_gen():
    return id_generator(size=9, chars=string.ascii_letters + string.digits)


class GrabRoom(ModelBase):
    Currency = (
        (GrabCurrencyType.Coupon.value, _("Coupon")),
        (GrabCurrencyType.Coins.value, _("Coins"))
    )
    State = (
        (GrabRoomStatus.Joinable.value, _("Joinable")),
        (GrabRoomStatus.Fulling.value, _("Fulling")),
        (GrabRoomStatus.Handling.value, _("Handling")),
        (GrabRoomStatus.End.value, _("End"))
    )
    rid = models.CharField(_("room id"), unique=True, max_length=128, default=short_id_gen, null=True)
    item_info = models.ForeignKey(ItemInfo, on_delete=models.CASCADE)
    curreny = models.IntegerField(_("currency"), default=GrabCurrencyType.Coins.value, choices=Currency)
    position = models.IntegerField(_("total position"), default=0)
    user_max_choice = models.IntegerField(_('user max choice'), default=0)
    coins = models.IntegerField(_('coins to one card'), default=1)
    price = models.FloatField(_('item price'), default=0)
    state = models.IntegerField(_("room state"), default=GrabRoomStatus.Joinable.value, choices=State)
    position_last = models.IntegerField(_("position last"), default=0)

    class Meta:
        verbose_name = _('GrabRoom')
        verbose_name_plural = _('GrabRoom')

    def __str__(self):
        return str(self.item_info)


class GrabBet(ModelBase):
    user = models.ForeignKey(USER_MODEL, on_delete=models.SET_NULL, default=None, null=True)
    room = models.ForeignKey(GrabRoom, on_delete=models.CASCADE)
    position = models.IntegerField(_('grab position'), default=None)
    victory = models.BooleanField(_('grab victory'), default=False)

    class Meta:
        verbose_name = _('GrabRoom')
        verbose_name_plural = _('GrabRoom')

    def __str__(self):
        return "".join([str(self.position), ":", str(self.room)])


class GrabCard(models.Model):
    position = models.IntegerField(_("grab position"), default=0)
    image = models.ImageField(_("grab card image"), default=None, upload_to='grab/%Y%m', storage=ImageStorage(),
                                   null=True, blank=True)

    class Meta:
        verbose_name = _('GrabCard')
        verbose_name_plural = _('GrabCard')

    def __str__(self):
        return "".join(["位置:", str(self.position)])


class GrabHistory(ModelBase):
    user = models.ForeignKey(USER_MODEL, on_delete=models.SET_NULL, default=None, null=True)
    room = models.ForeignKey(GrabRoom, on_delete=models.SET_NULL, default=None, null=True)
    count = models.IntegerField(_("join count"), default=0)
    timestamp = models.CharField(_("join timestamp"), default=0, max_length=128)