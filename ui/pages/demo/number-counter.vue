<template>
  <div class="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-6">
    <div class="max-w-4xl mx-auto">
      <!-- 页面标题 -->
      <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-white mb-4">🔢 数字滚动动画</h1>
        <p class="text-gray-300">展示各种酷炫的数字计数和滚动动画效果</p>
      </div>

      <!-- 动画控制 -->
      <div class="flex justify-center mb-8 space-x-4">
        <button 
          @click="startAllAnimations"
          class="px-6 py-3 bg-primary hover:bg-primary-dark text-white rounded-lg transition-all duration-300 hover:scale-105"
        >
          🚀 开始所有动画
        </button>
        <button 
          @click="resetAllAnimations"
          class="px-6 py-3 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-all duration-300"
        >
          🔄 重置动画
        </button>
      </div>

      <!-- 动画展示区域 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        
        <!-- 基础数字滚动 -->
        <div class="animation-card">
          <h3 class="card-title">📊 基础数字滚动</h3>
          <div class="number-display">
            <span class="number-value">{{ formatNumber(basicCounter) }}</span>
            <span class="number-label">基础计数</span>
          </div>
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: `${(basicCounter / 10000) * 100}%` }"></div>
          </div>
        </div>

        <!-- 价格滚动动画 -->
        <div class="animation-card">
          <h3 class="card-title">💰 价格滚动动画</h3>
          <div class="number-display price-display">
            <span class="currency">$</span>
            <span class="number-value">{{ formatPrice(priceCounter) }}</span>
            <span class="number-label">物品价值</span>
          </div>
          <div class="price-graph">
            <div class="graph-bar" v-for="(value, index) in priceHistory" :key="index" 
                 :style="{ height: `${(value / 1000) * 100}%` }"></div>
          </div>
        </div>

        <!-- 百分比动画 -->
        <div class="animation-card">
          <h3 class="card-title">📈 百分比动画</h3>
          <div class="percentage-display">
            <div class="percentage-circle">
              <svg class="percentage-svg" viewBox="0 0 120 120">
                <circle class="percentage-bg" cx="60" cy="60" r="50"></circle>
                <circle 
                  class="percentage-fill" 
                  cx="60" 
                  cy="60" 
                  r="50"
                  :stroke-dasharray="`${(percentageCounter / 100) * 314} 314`"
                ></circle>
              </svg>
              <div class="percentage-text">
                <span class="percentage-value">{{ Math.round(percentageCounter) }}%</span>
                <span class="percentage-label">完成度</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 矩阵数字雨 -->
        <div class="animation-card">
          <h3 class="card-title">🌧️ 矩阵数字雨</h3>
          <div class="matrix-container">
            <div class="matrix-column" v-for="(column, colIndex) in matrixData" :key="colIndex">
              <div 
                class="matrix-digit" 
                v-for="(digit, rowIndex) in column" 
                :key="rowIndex"
                :style="{ 
                  animationDelay: `${rowIndex * 0.1}s`,
                  color: digit === '1' ? 'var(--color-primary)' : 'var(--color-secondary)'
                }"
              >
                {{ digit }}
              </div>
            </div>
          </div>
        </div>

        <!-- 粒子计数动画 -->
        <div class="animation-card">
          <h3 class="card-title">✨ 粒子计数动画</h3>
          <div class="particle-counter">
            <div class="particle-display">
              <span class="particle-number">{{ particleCounter }}</span>
              <span class="particle-label">粒子数量</span>
            </div>
            <div class="particle-container">
              <div 
                class="particle" 
                v-for="p in particleList" 
                :key="p.id"
                :style="{ left: `${p.left}%`, top: `${p.top}%`, animationDelay: `${(p.id % 10) * 0.2}s` }"
              ></div>
            </div>
          </div>
        </div>

        <!-- 统计卡片动画 -->
        <div class="animation-card">
          <h3 class="card-title">📊 统计卡片动画</h3>
          <div class="stats-grid">
            <div class="stat-item" v-for="(stat, index) in stats" :key="index">
              <div class="stat-icon">{{ stat.icon }}</div>
              <div class="stat-content">
                <div class="stat-value">{{ stat.value }}</div>
                <div class="stat-label">{{ stat.label }}</div>
              </div>
              <div class="stat-bar" :style="{ width: `${(stat.value / stat.max) * 100}%`, transition: 'width 1s cubic-bezier(0.33,1,0.68,1)' }"></div>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'

// 响应式数据
const basicCounter = ref(0)
const priceCounter = ref(0)
const percentageCounter = ref(0)
const particleCounter = ref(0)
const priceHistory = ref<number[]>([])
const matrixData = ref<string[][]>([])
const stats = ref([
  { icon: '🎯', label: '命中率', value: 0, max: 100 },
  { icon: '⚡', label: '速度', value: 0, max: 200 },
  { icon: '💎', label: '稀有度', value: 0, max: 1000 },
  { icon: '🔥', label: '热度', value: 0, max: 500 }
])

// 粒子动画相关
const particleList = ref<{left: number, top: number, id: number}[]>([])
let particleIdSeed = 1

// 动画定时器
let animationTimers: number[] = []

// 格式化数字
const formatNumber = (num: number): string => {
  return num.toLocaleString()
}

// 格式化价格
const formatPrice = (num: number): string => {
  return num.toFixed(2)
}

// 数字动画函数
const animateNumber = (target: any, start: number, end: number, duration: number) => {
  const startTime = Date.now()
  const startValue = target.value
  const change = end - startValue
  
  const animate = () => {
    const elapsed = Date.now() - startTime
    const progress = Math.min(elapsed / duration, 1)
    const easeOutQuart = 1 - Math.pow(1 - progress, 4)
    const currentValue = startValue + change * easeOutQuart
    
    target.value = Math.round(currentValue * 100) / 100
    
    if (progress < 1) {
      const timer = requestAnimationFrame(animate)
      animationTimers.push(timer)
    }
  }
  
  animate()
}

// 统计卡片动画
const animateStat = (stat: any, end: number, duration: number) => {
  const startTime = Date.now()
  const startValue = stat.value
  const change = end - startValue
  
  const animate = () => {
    const elapsed = Date.now() - startTime
    const progress = Math.min(elapsed / duration, 1)
    const easeOutQuart = 1 - Math.pow(1 - progress, 4)
    const currentValue = startValue + change * easeOutQuart
    stat.value = Math.round(currentValue)
    if (progress < 1) {
      const timer = requestAnimationFrame(animate)
      animationTimers.push(timer)
    }
  }
  animate()
}

// 开始所有动画
const startAllAnimations = () => {
  resetAllAnimations()
  
  animateNumber(basicCounter, 0, 10000, 3000)
  animateNumber(priceCounter, 0, 1234.56, 2500)
  animateNumber(percentageCounter, 0, 87.5, 2000)
  animateNumber(particleCounter, 0, 50, 2000) // 限制最大50个粒子

  // 统计动画
  stats.value.forEach((stat, index) => {
    setTimeout(() => {
      animateStat(stat, stat.max, 1500)
    }, index * 200)
  })

  animatePriceHistory()
  startMatrixAnimation()
}

// 价格历史动画
const animatePriceHistory = () => {
  priceHistory.value = []
  for (let i = 0; i < 20; i++) {
    setTimeout(() => {
      priceHistory.value.push(Math.random() * 1000)
    }, i * 100)
  }
}

// 矩阵动画
const startMatrixAnimation = () => {
  const columns = 15
  const rows = 20
  
  matrixData.value = Array(columns).fill(null).map(() => 
    Array(rows).fill(null).map(() => Math.random() > 0.5 ? '1' : '0')
  )
}

// 粒子计数动画：数量变化时动态增减
watch(particleCounter, (newVal, oldVal) => {
  if (newVal > oldVal) {
    // 增加粒子
    for (let i = 0; i < newVal - oldVal; i++) {
      particleList.value.push({
        left: Math.random() * 100,
        top: Math.random() * 100,
        id: particleIdSeed++
      })
    }
  } else if (newVal < oldVal) {
    // 减少粒子
    particleList.value.splice(newVal)
  }
})

// 重置所有动画
const resetAllAnimations = () => {
  animationTimers.forEach(timer => cancelAnimationFrame(timer))
  animationTimers = []
  basicCounter.value = 0
  priceCounter.value = 0
  percentageCounter.value = 0
  particleCounter.value = 0
  priceHistory.value = []
  stats.value.forEach(stat => stat.value = 0)
  particleList.value = []
  particleIdSeed = 1
}

onMounted(() => {
  startMatrixAnimation()
})

onUnmounted(() => {
  resetAllAnimations()
})
</script>

<style lang="scss" scoped>
.animation-card {
  @apply bg-gray-800/40 backdrop-blur-lg rounded-lg p-6 border border-gray-700/50 transition-all duration-300;
  
  &:hover {
    @apply border-primary/50 bg-gray-800/60 transform -translate-y-1;
    box-shadow: 0 8px 32px rgba(var(--color-primary-rgb), 0.15);
  }
}

.card-title {
  @apply text-lg font-semibold text-white mb-4 pb-2 border-b border-gray-600/50;
}

.number-display {
  @apply text-center mb-4;
  
  .number-value {
    @apply text-4xl font-bold text-primary block mb-2;
    text-shadow: 0 0 20px rgba(0, 168, 255, 0.5);
  }
  
  .number-label {
    @apply text-sm text-gray-400;
  }
}

.price-display {
  .currency {
    @apply text-2xl text-green-400 mr-2;
  }
  
  .number-value {
    @apply text-green-400;
  }
}

.progress-bar {
  @apply h-2 bg-gray-700 rounded-full overflow-hidden;
  
  .progress-fill {
    @apply h-full bg-gradient-to-r from-primary to-primary-light rounded-full transition-all duration-1000;
    box-shadow: 0 0 10px rgba(0, 168, 255, 0.5);
  }
}

.price-graph {
  @apply flex items-end justify-between h-20 mt-4 space-x-1;
  
  .graph-bar {
    @apply bg-gradient-to-t from-primary to-primary-light rounded-t transition-all duration-300;
    min-width: 3px;
    box-shadow: 0 0 5px rgba(0, 168, 255, 0.3);
  }
}

.percentage-display {
  @apply flex justify-center;
  
  .percentage-circle {
    @apply relative w-32 h-32;
    
    .percentage-svg {
      @apply w-full h-full transform -rotate-90;
    }
    
    .percentage-bg {
      @apply fill-none stroke-gray-700 stroke-2;
    }
    
    .percentage-fill {
      @apply fill-none stroke-primary stroke-2 transition-all duration-1000;
      stroke-linecap: round;
      filter: drop-shadow(0 0 5px rgba(0, 168, 255, 0.5));
    }
    
    .percentage-text {
      @apply absolute inset-0 flex flex-col items-center justify-center;
      
      .percentage-value {
        @apply text-2xl font-bold text-primary;
      }
      
      .percentage-label {
        @apply text-xs text-gray-400;
      }
    }
  }
}

.matrix-container {
  @apply flex justify-center space-x-1 h-40 overflow-hidden;
  
  .matrix-column {
    @apply flex flex-col space-y-1;
    
    .matrix-digit {
      @apply text-xs font-mono;
      animation: matrix-fall 2s linear infinite;
      text-shadow: 0 0 5px currentColor;
    }
  }
}

.particle-counter {
  @apply relative;
  
  .particle-display {
    @apply text-center mb-4;
    
    .particle-number {
      @apply text-3xl font-bold text-secondary block;
      text-shadow: 0 0 15px rgba(255, 77, 0, 0.5);
    }
    
    .particle-label {
      @apply text-sm text-gray-400;
    }
  }
  
  .particle-container {
    @apply relative h-32 bg-gray-900/50 rounded-lg overflow-hidden;
    
    .particle {
      @apply absolute w-2 h-2 bg-secondary rounded-full;
      animation: particle-float 3s linear infinite;
      box-shadow: 0 0 10px rgba(255, 77, 0, 0.8);
    }
  }
}

.stats-grid {
  @apply grid grid-cols-2 gap-4;
  
  .stat-item {
    @apply relative bg-gray-900/50 rounded-lg p-4 border border-gray-700/30;
    
    .stat-icon {
      @apply text-2xl mb-2;
    }
    
    .stat-content {
      @apply mb-2;
      
      .stat-value {
        @apply text-lg font-bold text-white;
      }
      
      .stat-label {
        @apply text-xs text-gray-400;
      }
    }
    
    .stat-bar {
      @apply h-1 bg-gradient-to-r from-primary to-primary-light rounded-full transition-all duration-1000;
      box-shadow: 0 0 5px rgba(0, 168, 255, 0.3);
    }
  }
}

@keyframes matrix-fall {
  0% {
    opacity: 1;
    transform: translateY(-100%);
  }
  100% {
    opacity: 0;
    transform: translateY(100vh);
  }
}

@keyframes particle-float {
  0%, 100% {
    transform: translateY(0) scale(1);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px) scale(1.2);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-grid {
    @apply grid-cols-1;
  }
  
  .matrix-container {
    @apply h-32;
  }
  
  .percentage-circle {
    @apply w-24 h-24;
  }
}
</style> 