from aenum import Enum, IntEnum


class RespCode(IntEnum):
    Succeed = 0
    NotLogin = 100
    BusinessError = 101
    InvalidParams = 102
    NoBalance = 103
    MaxSending = 104
    NoProfit = 105
    NoPermission = 106
    NoBotOnline = 111
    AmountNotMatched = 112
    NoInventory = 201
    NoLotteryCount = 202
    WheelBlocked = 203
    NotOwnedGame = 204
    GameFull = 301
    InvalidGame = 302
    BadRequest = 400
    Maintenance = 401
    NotFound = 404
    Exception = 500
    NoData = 204
    TooManyRequests = 429


class AppType(Enum):
    Dota2 = '570'
    CSGO = '730'
    H1Z1 = '433850'
    PUBG = '578080'


class ExteriorType(Enum):
    WearCategory0 = 'WearCategory0'
    WearCategory1 = 'WearCategory1'
    WearCategory2 = 'WearCategory2'
    WearCategory3 = 'WearCategory3'
    WearCategory4 = 'WearCategory4'


class PackageState(IntEnum):
    Initialed = 0
    Available = 1
    Blocked = 2
    Gaming = 3
    Withdrawing = 4
    Withdrawn = 5
    Exchanged = 6
    Invalid = 7


class TradeType(IntEnum):
    Deposit = 1
    Withdraw = 2


class TradeState(IntEnum):
    Initialed = 0
    Accepted = 1
    Cancelled = 2
    Submitted = 11
    TradeNoUpdated = 12
    Active = 13
    WaitForRatify = 21
    Ratified = 22


class TradeRecordState(IntEnum):
    Initialed = 0
    Active = 1
    Closed = 2
    PendClose = 3


class WxpWithdrawStatus(IntEnum):
    WaitForBuy = 0
    Proccessing = 1
    WaitForSellConfirm = 2
    TradeSent = 4
    Completed = 5
    Declined = 6


class GameState(IntEnum):
    Initial = 1
    Joinable = 2
    Joining = 3
    Full = 4
    Running = 5
    End = 11
    Cancelled = 20




class RoomType(IntEnum):
    Battle = 1
    Equality = 2
    TeamBattle = 3


class CaseRecordType(IntEnum):
    Case = 0
    Battle = 1
    Equality = 2
    TeamBattle = 3


class BetTeamType(IntEnum):
    Null = 0
    CT = 1
    T = 2


class GameEnd(IntEnum):
    NotEnd = 1
    Handling = 2
    End = 3


class JoinState(Enum):
    Initialed = 1
    Cancelled = 2
    Joining = 11
    Accepted = 20


class WinResult(IntEnum):
    Joining = 1
    Win = 2
    Lose = 3
    NotEnd = 4


class GameType(IntEnum):
    Coinflip = 1
    Jackpot = 2


class RollType(IntEnum):
    Nocharge = 1
    Charge = 2


class CaseItemType(IntEnum):
    Assets = 1
    Balance = 2


class DoubleTeamType(IntEnum):
    Green = 0
    Red = 1
    Black = 2


class ChargeState(Enum):
    Initialed = 0
    Actived = 1
    Succeed = 2
    Failed = 3
    Canceled = 4


class PayType(Enum):
    Wechat = 1
    Ali = 2
    Unionpay = 3
    #Jiujia = 3
    #Fuxinka = 4
    Card = 5 
    OtherPay = 6


class CaseCategoryType(IntEnum):
    Normal = 1
    Top = 2
    Free = 3
    Festival = 4
    FreeGive = 5
    Promotion = 6
    Key = 7


class PackageSourceType(IntEnum):
    Case = 0
    Shop = 1
    Market = 2
    Tradeup = 3
    Roll = 4
    BattleRoom = 5
    LuckyBox = 6
    Grab = 7
    Giveaway = 8
    HourlyLottery = 10
    DailyLottery = 11
    WeeklyLottery = 12


class TradeupBetItemType(IntEnum):
    Item = 1
    Amount = 2


class RarityColor(Enum):
    Common = 'b0c3d9'
    Uncommon = '5e98d9'
    Rare = '4b69ff'
    Mythical = '8847ff'
    Legendary = 'd32ce6'
    Ancient = 'eb4b4b'


class ZBTradeState(IntEnum):
    WaitForPay = 0
    WaitForSend = 1
    WaitForTrade = 3
    Receive = 4
    Accepted = 10
    Cancelled = 11


class B2CTradeState(IntEnum):
    Initialed = 0 
    Accepted = 1
    Cancelled = 2

    #useless
    WaitForBuy = 11
    RequestBuy = 12
    WaitUnlock = 13
    TradeReady = 14
    BuyerCancelled = 21

    Trading = 15
    Cancelling = 16
    PriceCancelled = 22
    OutOfStock = 23
    ZBTCancelled = 24


class B2CItemType(IntEnum):
    Purchase = 1
    Stocks = 2


class ZBTradeSource(IntEnum):
    Market = 0
    Package = 1


class GrabCurrencyType(IntEnum):
    Coupon = 0
    Coins = 1


class GrabRoomStatus(IntEnum):
    Joinable = 0
    Fulling = 1
    Handling = 2
    End = 3

class BlindBoxCategory(IntEnum):
    Normal = 1
    Free = 2