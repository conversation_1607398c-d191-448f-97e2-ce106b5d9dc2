#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
站群管理API测试脚本
"""

import os
import sys
import django
import json

# 设置Django环境
sys.path.append('/www/wwwroot/csgoskins.com.cn/server')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')
django.setup()

from django.test import Client
from django.contrib.auth import get_user_model
from sitegroup.models import SiteGroup, Site

User = get_user_model()

def test_site_info_api():
    """测试站点信息API"""
    print("🔍 测试站点信息API...")
    
    client = Client()
    
    # 测试默认站点信息
    response = client.get('/api/sitegroup/site-info/', HTTP_HOST='localhost')
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        try:
            data = response.json()
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        except:
            print(f"响应内容: {response.content.decode()[:200]}...")
    else:
        print(f"错误响应: {response.content.decode()}")

def test_check_feature_api():
    """测试功能检查API"""
    print("\n🔍 测试功能检查API...")
    
    client = Client()
    
    # 测试检查文章功能
    response = client.get('/api/sitegroup/check-feature/?feature=articles', HTTP_HOST='localhost')
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        try:
            data = response.json()
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        except:
            print(f"响应内容: {response.content.decode()[:200]}...")
    else:
        print(f"错误响应: {response.content.decode()}")

def test_with_existing_site():
    """使用现有站点测试"""
    print("\n🔍 使用现有站点测试...")
    
    # 查找测试站点
    site = Site.objects.filter(domain='test.example.com').first()
    if not site:
        print("❌ 未找到测试站点")
        return
    
    print(f"✅ 找到测试站点: {site.name}")
    
    client = Client()
    
    # 使用测试站点域名测试
    response = client.get('/api/sitegroup/site-info/', HTTP_HOST='test.example.com')
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        try:
            data = response.json()
            print(f"站点信息: {json.dumps(data, indent=2, ensure_ascii=False)}")
        except:
            print(f"响应内容: {response.content.decode()[:200]}...")

def test_admin_access():
    """测试管理后台访问"""
    print("\n🔍 测试管理后台数据...")
    
    # 统计数据
    site_groups = SiteGroup.objects.count()
    sites = Site.objects.count()
    
    print(f"站群数量: {site_groups}")
    print(f"站点数量: {sites}")
    
    if sites > 0:
        site = Site.objects.first()
        print(f"示例站点: {site.name} ({site.domain})")
        print(f"功能状态: 文章={site.articles_enabled}, 对战={site.case_battle_enabled}")

def main():
    """主函数"""
    print("🌐 站群管理系统API测试")
    print("=" * 50)
    
    try:
        # 测试基本API
        test_site_info_api()
        test_check_feature_api()
        
        # 测试现有站点
        test_with_existing_site()
        
        # 测试管理后台数据
        test_admin_access()
        
        print("\n" + "=" * 50)
        print("✅ 测试完成！")
        print("\n📝 访问管理后台:")
        print("   http://localhost:8000/chenchen/sitegroup/")
        print("\n📝 API端点:")
        print("   GET /api/sitegroup/site-info/")
        print("   GET /api/sitegroup/check-feature/?feature=articles")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
