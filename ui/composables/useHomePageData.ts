import { monitorApi } from '~/services/monitor-api'

export const useHomePageData = (socketStore: any) => {
  const fetchHomePageData = async () => {
    try {
      console.log('[首页] 开始获取统计和开箱记录数据')

      // 获取数据并保存到 socketStore
      const result = await monitorApi.getMonitorData()
      
      if (result.success && result.data) {
        if (result.data.stats) {
          socketStore.setStatsData(result.data.stats)
        }
        
        if (result.data.case_records) {
          socketStore.setCaseRecords(result.data.case_records)
        }
        
        console.log('[首页] 成功获取统计和开箱记录数据')

        // 显式触发组件刷新事件
        if (typeof window !== 'undefined') {
          // 触发 LiveOpenings 组件刷新
          window.dispatchEvent(
            new CustomEvent('case-records-updated', {
              detail: { source: 'fetchHomePageData', timestamp: Date.now() }
            })
          )

          // 触发 HomeStats 组件刷新
          window.dispatchEvent(
            new CustomEvent('stats-data-updated', {
              detail: { source: 'fetchHomePageData', timestamp: Date.now() }
            })
          )
        }
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      console.error('[首页] 获取统计和开箱记录数据出错:', error)
    }
  }

  return {
    fetchHomePageData
  }
} 