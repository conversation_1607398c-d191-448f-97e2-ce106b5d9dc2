<template>
  <div class="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-6">
    <div class="max-w-4xl mx-auto">
      <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-white mb-4">📊 进度展示大全</h1>
        <p class="text-gray-300 text-lg">展示各种酷炫的进度条动画效果和可复用的进度组件</p>
        <div class="mt-4 flex justify-center gap-4">
          <NuxtLink to="/demo" class="nav-button">
            <i class="fas fa-arrow-left mr-2"></i>
            返回演示中心
          </NuxtLink>
        </div>
      </div>

      <div class="flex justify-center mb-8 space-x-4">
        <button 
          @click="startAllAnimations"
          class="px-6 py-3 bg-primary hover:bg-primary-dark text-white rounded-lg transition-all duration-300 hover:scale-105"
        >
          🚀 开始所有动画
        </button>
        <button 
          @click="resetAllAnimations"
          class="px-6 py-3 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-all duration-300"
        >
          🔄 重置动画
        </button>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        
        <!-- 基础进度条 -->
        <div class="animation-card">
          <h3 class="card-title">📈 基础进度条</h3>
          <div class="progress-item">
            <div class="progress-label">加载进度</div>
            <div class="progress-bar-basic">
              <div class="progress-fill-basic" :style="{ width: `${basicProgress}%` }"></div>
            </div>
            <div class="progress-text">{{ Math.round(basicProgress) }}%</div>
          </div>
        </div>

        <!-- 渐变进度条 -->
        <div class="animation-card">
          <h3 class="card-title">🌈 渐变进度条</h3>
          <div class="progress-item">
            <div class="progress-label">系统状态</div>
            <div class="progress-bar-gradient">
              <div class="progress-fill-gradient" :style="{ width: `${gradientProgress}%` }"></div>
            </div>
            <div class="progress-text">{{ Math.round(gradientProgress) }}%</div>
          </div>
        </div>

        <!-- 脉冲进度条 -->
        <div class="animation-card">
          <h3 class="card-title">💓 脉冲进度条</h3>
          <div class="progress-item">
            <div class="progress-label">心跳检测</div>
            <div class="progress-bar-pulse">
              <div class="progress-fill-pulse" :style="{ width: `${pulseProgress}%` }"></div>
            </div>
            <div class="progress-text">{{ Math.round(pulseProgress) }}%</div>
          </div>
        </div>

        <!-- 环形进度条 -->
        <div class="animation-card">
          <h3 class="card-title">⭕ 环形进度条</h3>
          <div class="circular-progress">
            <svg class="circular-svg" viewBox="0 0 120 120">
              <circle class="circular-bg" cx="60" cy="60" r="50"></circle>
              <circle 
                class="circular-fill" 
                cx="60" 
                cy="60" 
                r="50"
                :stroke-dasharray="`${(circularProgress / 100) * 314} 314`"
              ></circle>
            </svg>
            <div class="circular-text">
              <div class="circular-value">{{ Math.round(circularProgress) }}%</div>
              <div class="circular-label">完成度</div>
            </div>
          </div>
        </div>

        <!-- 波浪进度条 -->
        <div class="animation-card">
          <h3 class="card-title">🌊 波浪进度条</h3>
          <div class="wave-progress">
            <div class="wave-container">
              <div class="wave-fill" :style="{ height: `${waveProgress}%` }">
                <div class="wave-animation"></div>
              </div>
            </div>
            <div class="wave-text">{{ Math.round(waveProgress) }}%</div>
          </div>
        </div>

        <!-- 粒子进度条 -->
        <div class="animation-card">
          <h3 class="card-title">✨ 粒子进度条</h3>
          <div class="particle-progress">
            <div class="particle-bar">
              <div class="particle-fill" :style="{ width: `${particleProgress}%` }">
                <div class="particle" v-for="i in 20" :key="i"></div>
              </div>
            </div>
            <div class="particle-text">{{ Math.round(particleProgress) }}%</div>
          </div>
        </div>

      </div>

      <!-- UiCalculationProgress 组件演示区 -->
      <div class="mt-16">
        <div class="text-center mb-8">
          <h2 class="text-3xl font-bold text-white mb-4">🧩 UiCalculationProgress 组件演示</h2>
          <p class="text-gray-300">展示可复用的计算进度组件的各种配置和用法</p>
        </div>

        <!-- 控制面板 -->
        <div class="bg-gray-800/50 backdrop-blur-lg rounded-lg p-6 mb-8">
          <h3 class="text-xl font-semibold text-white mb-4">⚙️ 组件控制面板</h3>
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-300 mb-2">进度控制</label>
              <input 
                v-model.number="componentProgress" 
                type="range" 
                min="0" 
                max="100" 
                class="w-full bg-gray-700 rounded-lg"
              />
              <div class="text-gray-400 text-sm mt-1">{{ componentProgress }}%</div>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-300 mb-2">显示步骤</label>
              <div class="flex items-center">
                <input 
                  v-model="showSteps" 
                  type="checkbox" 
                  class="mr-2 text-blue-500"
                />
                <span class="text-gray-300">启用步骤显示</span>
              </div>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-300 mb-2">显示完成消息</label>
              <div class="flex items-center">
                <input 
                  v-model="showCompletionMessage" 
                  type="checkbox" 
                  class="mr-2 text-blue-500"
                />
                <span class="text-gray-300">启用完成消息</span>
              </div>
            </div>
            <div class="flex items-end">
              <button 
                @click="autoDemo"
                class="px-4 py-2 bg-primary hover:bg-primary-dark text-white rounded-lg transition-all"
              >
                🎮 自动演示
              </button>
            </div>
          </div>
        </div>

        <!-- 组件演示网格 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          
          <!-- 基础配置 -->
          <div class="animation-card">
            <h3 class="card-title">📈 基础配置</h3>
            <UiCalculationProgress
              :progress="componentProgress"
              title="基础计算进度"
              title-complete="计算完成"
              completion-message="基础计算已完成！"
              :show-completion-message="showCompletionMessage"
              :show-steps="showSteps"
            />
          </div>

          <!-- 对战主题 -->
          <div class="animation-card">
            <h3 class="card-title">⚔️ 对战主题</h3>
            <UiCalculationProgress
              :progress="componentProgress"
              title="对战结果计算"
              title-complete="对战计算完成"
              completion-message="获胜者已确定！"
              :show-completion-message="showCompletionMessage"
              :show-steps="showSteps"
              steps-title="对战计算过程"
              :custom-steps="battleSteps"
            />
          </div>

          <!-- 简化版本 -->
          <div class="animation-card">
            <h3 class="card-title">🎯 简化版本</h3>
            <UiCalculationProgress
              :progress="componentProgress"
              title="简化进度"
              title-complete="完成"
              completion-message="处理完成"
              :show-completion-message="showCompletionMessage"
              :show-steps="false"
            />
          </div>

          <!-- 最简测试 -->
          <div class="animation-card">
            <h3 class="card-title">🔍 最简测试</h3>
            <UiCalculationProgress :progress="componentProgress" />
          </div>

        </div>

        <!-- 调试信息 -->
        <div class="bg-gray-800/50 backdrop-blur-lg rounded-lg p-6 mt-8">
          <h3 class="text-xl font-semibold text-white mb-4">🔧 调试信息</h3>
          <div class="text-gray-300 space-y-2 text-sm">
            <p>当前进度: {{ componentProgress }}%</p>
            <p>显示步骤: {{ showSteps }}</p>
            <p>显示完成消息: {{ showCompletionMessage }}</p>
            <p>对战步骤数量: {{ battleSteps.length }}</p>
          </div>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

const basicProgress = ref(0)
const gradientProgress = ref(0)
const pulseProgress = ref(0)
const circularProgress = ref(0)
const waveProgress = ref(0)
const particleProgress = ref(0)

// UiCalculationProgress 组件相关变量
const componentProgress = ref(0)
const showSteps = ref(true)
const showCompletionMessage = ref(true)

// 对战步骤
const battleSteps = ref([
  { text: '分析开箱数据...', active: false },
  { text: '统计物品价值...', active: false },
  { text: '计算玩家总分...', active: false },
  { text: '比较玩家成绩...', active: false },
  { text: '确定获胜者...', active: false },
  { text: '生成奖励分配...', active: false },
])

let animationTimers: number[] = []
let componentDemoTimer: ReturnType<typeof setInterval> | null = null

const animateProgress = (target: any, end: number, duration: number, delay: number = 0) => {
  setTimeout(() => {
    const startTime = Date.now()
    const startValue = target.value
    const change = end - startValue
    
    const animate = () => {
      const elapsed = Date.now() - startTime
      const progress = Math.min(elapsed / duration, 1)
      const easeOutQuart = 1 - Math.pow(1 - progress, 4)
      const currentValue = startValue + change * easeOutQuart
      
      target.value = Math.round(currentValue * 100) / 100
      
      if (progress < 1) {
        const timer = requestAnimationFrame(animate)
        animationTimers.push(timer)
      }
    }
    
    animate()
  }, delay)
}

const startAllAnimations = () => {
  resetAllAnimations()
  
  animateProgress(basicProgress, 85, 2000)
  animateProgress(gradientProgress, 92, 2500, 300)
  animateProgress(pulseProgress, 78, 1800, 600)
  animateProgress(circularProgress, 95, 3000, 900)
  animateProgress(waveProgress, 88, 2200, 1200)
  animateProgress(particleProgress, 96, 2800, 1500)
}

const resetAllAnimations = () => {
  animationTimers.forEach(timer => cancelAnimationFrame(timer))
  animationTimers = []
  
  basicProgress.value = 0
  gradientProgress.value = 0
  pulseProgress.value = 0
  circularProgress.value = 0
  waveProgress.value = 0
  particleProgress.value = 0
}

// UiCalculationProgress 组件自动演示
const autoDemo = () => {
  if (componentDemoTimer) {
    clearInterval(componentDemoTimer)
  }
  
  componentProgress.value = 0
  componentDemoTimer = setInterval(() => {
    if (componentProgress.value < 100) {
      componentProgress.value += Math.random() * 5 + 1
    } else {
      componentProgress.value = 0
    }
  }, 200)
}

// 页面元数据
useHead({
  title: '进度展示大全 - CSGO Skins',
  meta: [
    { name: 'description', content: '展示各种酷炫的进度条动画效果和可复用的进度组件' }
  ]
})

onUnmounted(() => {
  resetAllAnimations()
  if (componentDemoTimer) {
    clearInterval(componentDemoTimer)
  }
})
</script>

<style lang="scss" scoped>
.animation-card {
  @apply bg-gray-800/40 backdrop-blur-lg rounded-lg p-6 border border-gray-700/50 transition-all duration-300;
  
  &:hover {
    @apply border-primary/50 bg-gray-800/60 transform -translate-y-1;
    box-shadow: 0 8px 32px rgba(0, 168, 255, 0.15);
  }
}

.card-title {
  @apply text-lg font-semibold text-white mb-4 pb-2 border-b border-gray-600/50;
}

.nav-button {
  @apply inline-flex items-center px-4 py-2 bg-gray-700/50 hover:bg-gray-600/50 text-white rounded-lg transition-colors border border-gray-600/50 hover:border-gray-500/50;
}

.progress-item {
  @apply space-y-3;
  
  .progress-label {
    @apply text-sm text-gray-300 font-medium;
  }
  
  .progress-text {
    @apply text-sm text-primary font-bold text-right;
  }
}

.progress-bar-basic {
  @apply h-3 bg-gray-700 rounded-full overflow-hidden;
  
  .progress-fill-basic {
    @apply h-full bg-primary rounded-full transition-all duration-1000;
    box-shadow: 0 0 10px rgba(0, 168, 255, 0.5);
  }
}

.progress-bar-gradient {
  @apply h-3 bg-gray-700 rounded-full overflow-hidden;
  
  .progress-fill-gradient {
    @apply h-full rounded-full transition-all duration-1000;
    background: linear-gradient(90deg, #00A8FF, #FF4D00, #00A8FF);
    background-size: 200% 100%;
    animation: gradient-shift 2s ease-in-out infinite;
    box-shadow: 0 0 15px rgba(0, 168, 255, 0.6);
  }
}

.progress-bar-pulse {
  @apply h-3 bg-gray-700 rounded-full overflow-hidden;
  
  .progress-fill-pulse {
    @apply h-full bg-primary rounded-full transition-all duration-1000;
    animation: pulse-glow 1.5s ease-in-out infinite;
  }
}

.circular-progress {
  @apply relative flex justify-center items-center;
  
  .circular-svg {
    @apply w-32 h-32 transform -rotate-90;
  }
  
  .circular-bg {
    @apply fill-none stroke-gray-700 stroke-2;
  }
  
  .circular-fill {
    @apply fill-none stroke-primary stroke-2 transition-all duration-1000;
    stroke-linecap: round;
    filter: drop-shadow(0 0 8px rgba(0, 168, 255, 0.6));
  }
  
  .circular-text {
    @apply absolute text-center;
    
    .circular-value {
      @apply text-2xl font-bold text-primary;
    }
    
    .circular-label {
      @apply text-xs text-gray-400;
    }
  }
}

.wave-progress {
  @apply relative h-32 bg-gray-700 rounded-lg overflow-hidden;
  
  .wave-container {
    @apply relative h-full;
  }
  
  .wave-fill {
    @apply absolute bottom-0 left-0 right-0 bg-primary transition-all duration-1000;
    box-shadow: 0 0 20px rgba(0, 168, 255, 0.4);
    
    .wave-animation {
      @apply absolute top-0 left-0 right-0 h-8 bg-primary/30;
      animation: wave-move 2s ease-in-out infinite;
    }
  }
  
  .wave-text {
    @apply absolute top-4 right-4 text-white font-bold text-lg;
  }
}

.particle-progress {
  @apply space-y-3;
  
  .particle-bar {
    @apply h-4 bg-gray-700 rounded-full overflow-hidden;
    
    .particle-fill {
      @apply h-full bg-primary rounded-full transition-all duration-1000 relative;
      box-shadow: 0 0 15px rgba(0, 168, 255, 0.5);
      
      .particle {
        @apply absolute w-1 h-1 bg-white rounded-full;
        animation: particle-float 2s ease-in-out infinite;
        
        @for $i from 1 through 20 {
          &:nth-child(#{$i}) {
            left: #{($i - 1) * 5}%;
            animation-delay: #{$i * 0.1}s;
          }
        }
      }
    }
  }
  
  .particle-text {
    @apply text-sm text-primary font-bold text-right;
  }
}

@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 10px rgba(0, 168, 255, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(0, 168, 255, 0.8), 0 0 30px rgba(0, 168, 255, 0.4);
  }
}

@keyframes wave-move {
  0%, 100% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(100%);
  }
}

@keyframes particle-float {
  0%, 100% {
    transform: translateY(0) scale(1);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-8px) scale(1.2);
    opacity: 1;
  }
}

@media (max-width: 768px) {
  .circular-svg {
    @apply w-24 h-24;
  }
  
  .wave-progress {
    @apply h-24;
  }
}
</style> 