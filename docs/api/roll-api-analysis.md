# Roll模块API分析文档

## 概述

Roll模块是一个竞拍抽奖系统，用户可以创建Roll房间并投入物品，其他用户加入房间参与竞拍，系统随机选择中奖者分配物品。支持免费房间和收费房间两种模式。

## 现有API接口

### 1. 房间管理接口

#### POST /api/roll/create/ - 创建Roll房间

**功能**: 创建新的Roll房间。

**请求参数**:
```json
{
    "name": "房间名称",
    "desc": "房间描述",
    "type": 0,                    // 0-免费房间, 1-收费房间
    "password": "房间密码",        // 可选
    "fee": 10.0,                  // 收费房间的入场费
    "min_joiner": 2,              // 最小参与人数
    "max_joiner": 100,            // 最大参与人数
    "max_winner": 3,              // 最大中奖人数
    "begin_time": "2023-12-01 10:00:00",
    "due_time": "2023-12-01 20:00:00",
    "items": [1, 2, 3],          // 投入的物品ID列表
    "deposit_enable": true,       // 是否允许其他人存入物品
    "charge_limit": 100.0,       // 充值限制
    "charge_begin_time": "2023-11-01 00:00:00",
    "charge_end_time": "2023-12-01 00:00:00",
    "debug": true
}
```

**响应格式**:
```json
{
    "status": 200,
    "data": {
        "room": "房间UID"
    },
    "msg": "Succeed"
}
```

#### POST /api/roll/edit/ - 编辑Roll房间

**功能**: 编辑已创建的Roll房间（仅在初始状态可编辑）。

#### POST /api/roll/deposit/ - 存入物品

**功能**: 向Roll房间存入物品和余额。

**请求参数**:
```json
{
    "uid": "房间UID",
    "items": [1, 2, 3],          // 物品ID列表
    "coins": [10.0, 20.0]        // 余额列表
}
```

#### POST /api/roll/cancel/ - 取消房间

**功能**: 取消Roll房间，退还物品。

### 2. 房间查询接口

#### GET /api/roll/list - 获取房间列表

**功能**: 获取Roll房间列表，支持多种筛选条件。

**请求参数**:
- `page`: 页码
- `pageSize`: 每页大小
- `type`: 房间类型
- `isCreator`: 是否只显示自己创建的房间
- `isJoiner`: 是否只显示自己参与的房间
- `noPassword`: 是否只显示无密码房间
- `isRunning`: 是否只显示进行中的房间
- `isEnd`: 是否只显示已结束的房间
- `itemsPreviewCount`: 预览物品数量

**响应格式**:
```json
{
    "status": 200,
    "data": {
        "items": [
            {
                "id": 1,
                "uid": "房间UID",
                "user": {...},
                "type": 0,
                "name": "房间名称",
                "desc": "房间描述",
                "fee": 0.0,
                "begin_ts_val": 1701408000,
                "due_ts_val": 1701444000,
                "total_amount": 150.0,
                "official": 1,
                "amount": 150.0,
                "items_count": 5,
                "joiners_count": 3,
                "state": 2,
                "items_preview": [...],
                "has_password": false
            }
        ],
        "total": 100,
        "page": 1,
        "limit": 10
    },
    "msg": "Succeed"
}
```

#### GET /api/roll/detail - 获取房间详情

**功能**: 获取指定房间的详细信息。

**请求参数**:
- `uid`: 房间UID
- `type`: 房间类型

#### GET /api/roll/items - 获取房间物品

**功能**: 获取房间内的所有物品列表。

#### GET /api/roll/bets - 获取房间参与者

**功能**: 获取房间的参与者列表和中奖信息。

### 3. 新版API接口

#### GET /api/roll/rolllist - 新版房间列表

**功能**: 新版房间列表接口，功能类似/list但响应格式可能有差异。

#### GET /api/roll/rolldetail - 新版房间详情

**功能**: 新版房间详情接口。

**请求参数**:
- `rid`: 房间UID

#### GET /api/roll/rollskin - 获取房间饰品

**功能**: 获取房间内的饰品信息。

**请求参数**:
- `rid`: 房间UID
- `page`, `pageSize`: 分页参数

**响应格式**:
```json
{
    "status": 200,
    "data": {
        "items": [
            {
                "market_name_cn": "AK-47 | 红线",
                "market_name": "AK-47 | Redline",
                "icon_url": "https://steamcommunity-a.akamaihd.net/economy/image/xxx/",
                "price": 150.0,
                "part": "...",
                "amount": 1,
                "rarity_color": "#4B69FF"
            }
        ],
        "total": 10,
        "page": 1,
        "limit": 10
    },
    "msg": "Succeed"
}
```

#### GET /api/roll/rolluser - 获取房间用户

**功能**: 获取房间的参与用户列表。

#### GET /api/roll/rollwinner - 获取中奖信息

**功能**: 获取房间的中奖物品和用户信息。

#### GET /api/roll/usernum - 获取用户数量

**功能**: 获取房间的参与用户数量。

**请求参数**:
- `rid`: 房间UID

#### POST /api/roll/userjoin - 用户加入房间

**功能**: 用户加入Roll房间参与竞拍。

**请求参数**:
- `rid`: 房间UID (查询参数)
- `password`: 房间密码 (查询参数，可选)

**响应格式**:
```json
{
    "status": 200,
    "data": {},
    "msg": "Succeed"
}
```

#### POST /api/roll/checkstatus - 检查参与状态

**功能**: 检查用户在房间中的参与状态。

### 4. 其他接口

#### GET /api/roll/joinablenum/ - 获取可参与房间数量

**功能**: 获取当前可参与的房间数量统计。

#### GET /api/roll/newroll - 获取新Roll列表

**功能**: 获取新的Roll房间列表（可能是特定的推荐房间）。

#### GET /api/roll/clearupduplicateuser/ - 清理重复用户

**功能**: 清理Roll房间中的重复参与记录（管理功能）。

## 业务逻辑分析

### 房间状态管理

Roll房间有以下状态：
- `Initial(0)`: 初始状态，可以编辑
- `Joinable(1)`: 可加入状态
- `Joining(2)`: 加入中状态  
- `Full(3)`: 已满员状态
- `Running(4)`: 运行中状态
- `End(5)`: 已结束状态
- `Cancelled(6)`: 已取消状态

### 参与机制

1. **免费房间**: 用户可直接加入参与
2. **收费房间**: 需要支付入场费
3. **密码房间**: 需要输入正确密码
4. **充值限制**: 可设置参与者的充值要求

### 开奖算法

系统使用随机算法从参与者中选择中奖者，支持多个中奖者分配不同物品。

### 统计系统

- 日统计和月统计数据
- 抽水比例统计
- 房间创建和参与统计

## 存在的问题和缺陷

### 1. API设计问题

**问题**:
- 新旧API接口重复，功能相似但URL不一致
- 参数命名不统一（如`pageSize`和`limit`、`uid`和`rid`）
- 部分接口使用查询参数传递关键数据（如密码）

**建议**:
```python
# 统一API设计
urlpatterns = [
    # 房间管理
    url(r'^rooms/$', views.RoomListView.as_view()),           # GET列表, POST创建
    url(r'^rooms/(?P<room_id>\w+)/$', views.RoomDetailView.as_view()),  # GET详情, PUT编辑, DELETE取消
    url(r'^rooms/(?P<room_id>\w+)/items/$', views.RoomItemsView.as_view()),     # GET物品, POST存入
    url(r'^rooms/(?P<room_id>\w+)/participants/$', views.ParticipantsView.as_view()),  # GET参与者
    url(r'^rooms/(?P<room_id>\w+)/join/$', views.JoinRoomView.as_view()),       # POST加入
    url(r'^rooms/(?P<room_id>\w+)/winners/$', views.WinnersView.as_view()),     # GET中奖信息
]
```

### 2. 安全问题

**问题**:
- 密码通过查询参数传递，容易泄露
- 缺少用户输入验证和过滤
- 没有防刷机制和频率限制

**建议**:
```python
# 密码应通过POST body传递
def join_room(request, room_id):
    password = request.data.get('password')  # 而不是查询参数
    
# 添加频率限制
from django_ratelimit.decorators import ratelimit

@ratelimit(key='user', rate='10/m', method='POST')
def join_room_view(request):
    pass
```

### 3. 数据一致性问题

**问题**:
- 房间状态更新可能存在并发问题
- 物品状态变更缺少完整的事务保护
- 重复参与清理逻辑复杂且可能影响性能

**建议**:
```python
# 使用数据库约束防止重复参与
class RollRoomBet(models.Model):
    user = models.ForeignKey(USER_MODEL)
    room = models.ForeignKey(RollRoom)
    
    class Meta:
        unique_together = ['user', 'room']  # 防止重复参与

# 优化状态更新
@transaction.atomic
def join_room_safely(user, room):
    room = RollRoom.objects.select_for_update().get(id=room.id)
    # 状态检查和更新逻辑
```

### 4. 性能问题

**问题**:
- 序列化器中频繁查询ItemInfo
- 缺少必要的数据库索引
- 房间列表查询可能很慢

**建议**:
```python
# 优化序列化器查询
class RollRoomItemSerializer(serializers.ModelSerializer):
    def to_representation(self, instance):
        # 使用select_related预加载关联数据
        if hasattr(instance, '_prefetched_item_info'):
            item_info = instance._prefetched_item_info
        else:
            item_info = ItemInfo.objects.get(id=instance.item_id)
        
# 添加数据库索引
class RollRoom(models.Model):
    class Meta:
        indexes = [
            models.Index(fields=['state', '-create_time']),
            models.Index(fields=['user', 'state']),
        ]
```

### 5. 代码质量问题

**问题**:
- business.py文件过大（1435行），职责不清晰
- 新旧代码混合，维护困难
- 缺少完整的错误处理和日志记录

**建议**:
```python
# 拆分业务逻辑
# roll/services/room_service.py
class RoomService:
    def create_room(self, user, **kwargs):
        pass
    
    def join_room(self, user, room_id, password=None):
        pass

# roll/services/lottery_service.py  
class LotteryService:
    def draw_winners(self, room):
        pass
```

### 6. 用户体验问题

**问题**:
- 错误信息不够详细和友好
- 缺少实时状态更新
- 没有参与历史和统计信息

**建议**:
```python
# 改进错误提示
def join_room(user, room_id):
    room = get_room_or_404(room_id)
    
    if room.state != GameState.Joinable.value:
        return RespCode.InvalidParams.value, "房间当前不可加入，状态：已开始"
    
    if room.max_joiner <= room.participants.count():
        return RespCode.InvalidParams.value, f"房间已满员（{room.max_joiner}人）"

# 添加WebSocket实时通知
def notify_room_update(room_id, event, data):
    channel = f'roll_room_{room_id}'
    ws_send_msg(channel, {
        'type': 'room_update',
        'event': event,
        'data': data
    })
```

### 7. 业务逻辑缺陷

**问题**:
- 开奖算法缺少公平性验证
- 机器人逻辑混合在业务代码中
- 缺少房间活跃度管理

**建议**:
```python
# 公平的开奖算法
import secrets
from hashlib import sha256

def draw_winners_fairly(room):
    participants = list(room.participants.all())
    if not participants:
        return []
    
    # 生成可验证的随机种子
    seed_data = f"{room.id}_{room.due_ts}_{len(participants)}"
    seed = sha256(seed_data.encode()).hexdigest()
    
    # 记录开奖过程
    _logger.info(f"Room {room.id} lottery seed: {seed}")
    
    # 使用密码学安全的随机选择
    winners = secrets.SystemRandom().sample(participants, room.max_winner)
    return winners
```

## 改进建议

### 1. API重构建议

**统一的RESTful API设计**:
```python
# roll/urls.py
urlpatterns = [
    # 房间资源
    url(r'^rooms/$', views.RoomListCreateView.as_view()),
    url(r'^rooms/(?P<room_id>\w+)/$', views.RoomDetailView.as_view()),
    url(r'^rooms/(?P<room_id>\w+)/items/$', views.RoomItemsView.as_view()),
    url(r'^rooms/(?P<room_id>\w+)/participants/$', views.ParticipantsView.as_view()),
    url(r'^rooms/(?P<room_id>\w+)/join/$', views.JoinRoomView.as_view()),
    url(r'^rooms/(?P<room_id>\w+)/leave/$', views.LeaveRoomView.as_view()),
    url(r'^rooms/(?P<room_id>\w+)/winners/$', views.WinnersView.as_view()),
    
    # 用户相关
    url(r'^my/rooms/$', views.MyRoomsView.as_view()),
    url(r'^my/participations/$', views.MyParticipationsView.as_view()),
    url(r'^my/winnings/$', views.MyWinningsView.as_view()),
    
    # 统计
    url(r'^statistics/$', views.StatisticsView.as_view()),
]
```

### 2. 数据模型优化

**改进的模型设计**:
```python
class RollRoom(models.Model):
    # 添加更多状态和字段
    estimated_draw_time = models.DateTimeField(null=True, blank=True)
    actual_draw_time = models.DateTimeField(null=True, blank=True) 
    draw_seed = models.CharField(max_length=64, null=True, blank=True)
    
    # 添加索引
    class Meta:
        indexes = [
            models.Index(fields=['state', '-create_time']),
            models.Index(fields=['user', 'state']),
            models.Index(fields=['begin_ts', 'due_ts']),
        ]

class RoomActivity(models.Model):
    """房间活跃度记录"""
    room = models.ForeignKey(RollRoom)
    activity_type = models.CharField(max_length=20)  # join, leave, item_add
    user = models.ForeignKey(USER_MODEL)
    timestamp = models.DateTimeField(auto_now_add=True)
```

### 3. 服务层重构

**清晰的服务层设计**:
```python
# roll/services/__init__.py
from .room_service import RoomService
from .lottery_service import LotteryService  
from .statistics_service import StatisticsService

# roll/services/room_service.py
class RoomService:
    def create_room(self, user, **kwargs):
        """创建房间"""
        pass
    
    def join_room(self, user, room_id, password=None):
        """加入房间"""
        pass
    
    def get_room_list(self, user, filters):
        """获取房间列表"""
        pass

# roll/services/lottery_service.py  
class LotteryService:
    def can_draw(self, room):
        """检查是否可以开奖"""
        pass
    
    def draw_winners(self, room):
        """执行开奖"""
        pass
    
    def distribute_items(self, room, winners):
        """分配物品"""
        pass
```

### 4. 缓存策略

**合理的缓存设计**:
```python
class RollCacheManager:
    @staticmethod
    def get_room_list_cache_key(user_id, filters):
        filter_hash = hashlib.md5(str(sorted(filters.items())).encode()).hexdigest()
        return f'roll_room_list_{user_id}_{filter_hash}'
    
    @staticmethod
    def get_room_detail_cache_key(room_id):
        return f'roll_room_detail_{room_id}'
    
    @staticmethod
    def invalidate_room_cache(room_id):
        """房间状态变化时清除相关缓存"""
        patterns = [
            f'roll_room_detail_{room_id}',
            f'roll_room_list_*',
            f'roll_room_items_{room_id}',
        ]
        for pattern in patterns:
            cache.delete_pattern(pattern)
```

### 5. WebSocket集成

**实时通知系统**:
```python
def send_room_notification(room_id, event_type, data):
    """发送房间相关的实时通知"""
    notification = {
        'type': 'roll_room_update',
        'room_id': room_id,
        'event': event_type,  # 'user_joined', 'item_added', 'draw_started', 'draw_completed'
        'data': data,
        'timestamp': timezone.now().isoformat()
    }
    
    # 发送给房间订阅者
    channel = f'roll_room_{room_id}'
    ws_send_msg(channel, notification)
    
    # 发送给全局Roll更新频道
    ws_send_msg('roll_global', notification)
```

### 6. 任务队列优化

**使用thworker处理异步任务**:
```python
from thworker import task

@task(queue='roll_high_priority')
def process_room_draw(room_id):
    """处理房间开奖"""
    try:
        room = RollRoom.objects.get(id=room_id)
        service = LotteryService()
        
        # 执行开奖
        winners = service.draw_winners(room)
        
        # 分配物品
        service.distribute_items(room, winners)
        
        # 发送通知
        send_room_notification(room_id, 'draw_completed', {
            'winners': [w.user.id for w in winners]
        })
        
    except Exception as e:
        _logger.exception(f"Room {room_id} draw failed: {e}")
        # 发送错误通知

@task(queue='roll_cleanup')
def cleanup_expired_rooms():
    """清理过期房间"""
    pass
```

## 总结

Roll模块是一个功能相对复杂的竞拍抽奖系统，但在API设计、代码质量、性能优化等方面存在较多问题。建议：

1. **短期改进**: 统一API接口、修复安全问题、优化数据库查询
2. **中期重构**: 重构业务逻辑、实现服务层分离、添加缓存策略  
3. **长期优化**: 实现完整的实时通知系统、添加详细的统计分析、提升系统公平性和透明度

通过这些改进，可以将Roll模块打造成一个更加稳定、高效、用户友好的竞拍抽奖平台。
