<template>
  <Transition name="calculation" appear>
    <div v-if="show" class="battle-calculation-animation">
      <!-- 动画头部 -->
      <div class="animation-header">
        <h2 class="animation-title">{{ t('battle.animation.opening_cases') }}</h2>
        <div class="global-progress">
          <div class="progress-bar" :style="{ width: progressPercentage + '%' }"></div>
          <span class="progress-text">{{ Math.round(progressPercentage) }}%</span>
  </div>
      </div>

      <!-- 参与者网格 -->
      <div class="participants-grid" :class="gridClass">
        <div 
          v-for="participant in participants" 
          :key="participant.user.username"
          :data-user="participant.user.username"
          class="participant-area"
        >
          <!-- 箱子动画区域 -->
          <div class="case-animation-area">
            <div class="case-box" :class="{ opening: isOpening }">
              <img 
                :src="participant.case.cover" 
                :alt="getCaseName(participant.case)"
                class="case-image"
                    @error="handleImageError"
                  />
              <!-- 旋转和特效容器 -->
              <div class="animation-effects">
                <div class="rotation-ring"></div>
                <div class="particle-container">
                  <div 
                    v-for="i in 20" 
                    :key="i" 
                    class="particle"
                    :style="getParticleStyle(i)"
                  ></div>
                </div>
                <div class="glow-effect"></div>
              </div>
            </div>

            <!-- 玩家信息 -->
            <div class="player-info">
              <img 
                :src="participant.user.avatar || '/demo/avatar1.png'" 
                class="player-avatar"
                @error="handleImageError"
              />
              <span class="player-name">{{ participant.user.nickname || participant.user.username }}</span>
            </div>
          </div>

          <!-- 个人开箱记录区域 -->
          <div class="personal-records">
            <div class="records-header">
              <Icon name="heroicons:gift" class="w-4 h-4" />
              <span>{{ t('battle.animation.opening_items') }}</span>
              <span class="records-count">({{ getParticipantRecordCount(participant.user.username) }})</span>
            </div>
            <div class="records-list">
              <div 
                v-for="item in getParticipantRecords(participant.user.username)" 
                :key="item.uid"
                class="record-item"
                :style="{ borderLeftColor: item.item_rarity?.rarity_color || '#6b7280' }"
              >
                <img 
                  :src="item.image || '/demo/item1.png'" 
                  :alt="getItemName(item)"
                  class="item-image"
                  @error="handleImageError"
                />
                <div class="item-info">
                  <div class="item-name">{{ getItemName(item) }}</div>
                  <div class="item-rarity">{{ item.item_rarity?.rarity_name || 'Unknown' }}</div>
              </div>
                <div class="item-price">${{ formatPrice(item.item_price?.price || 0) }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 跳过按钮 -->
      <button v-if="allowSkip && !isAnimationComplete" @click="skipAnimation" class="skip-button">
        <Icon name="heroicons:forward" class="w-4 h-4" />
        {{ t('battle.animation.skip') }}
      </button>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useI18n } from 'vue-i18n'

// Props 接口定义
interface BattleCalculationAnimationProps {
  show: boolean
  animationConfig: {
    animation_id: string
    animation_start_timestamp: number
    animation_duration: number
    server_timestamp: number
  }
  participants: {
    user: { 
      username: string
      nickname?: string
      avatar?: string 
    }
    case: {
      case_key: string
      name: string
      name_en?: string
      name_zh_hans?: string
      cover: string
    }
    openingItems?: {
      uid: string
      item_id: number
      name: string
      name_en?: string
      name_zh_hans?: string
      image: string
      item_price: { price: number }
      item_rarity: {
        rarity_id: number
        rarity_name: string
        rarity_color: string
      }
      reveal_order: number
      reveal_timestamp?: number
    }[]
  }[]
  syncConfig: {
    tolerance_ms: number
    max_delay_compensation: number
    enable_client_sync: boolean
  }
}

// Emits 接口定义
interface BattleCalculationAnimationEmits {
  'animation-complete': []
  'item-revealed': [participant: any, item: any]
}

// Props
const props = defineProps<BattleCalculationAnimationProps>()

// Emits
const emit = defineEmits<{
  'animation-complete': []
  'item-revealed': [participant: any, item: any]
}>()

// 国际化
const { t } = useI18n()

// 响应式状态
const isOpening = ref(false)
const isAnimationComplete = ref(false)
const progressPercentage = ref(0)
const allowSkip = ref(true)
const animationStartTime = ref<number | null>(null)
const animationTimer = ref<NodeJS.Timeout | null>(null)
const progressTimer = ref<NodeJS.Timeout | null>(null)

// 个人开箱记录存储
const personalRecords = ref<Map<string, any[]>>(new Map())

// 计算属性
const gridClass = computed(() => {
  const count = props.participants.length
  if (count === 2) return 'grid-2'
  if (count === 3) return 'grid-3'
  return 'grid-4'
})

// 方法
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  if (img.classList.contains('case-image')) {
    img.src = '/demo/case1.png'
  } else if (img.classList.contains('player-avatar')) {
    img.src = '/demo/avatar1.png'
  } else {
    img.src = '/demo/item1.png'
  }
}

const getCaseName = (caseData: any) => {
  return caseData.name_zh_hans || caseData.name_en || caseData.name || 'Unknown Case'
}

const getItemName = (item: any) => {
  return item.name_zh_hans || item.name_en || item.name || 'Unknown Item'
}

const formatPrice = (price: number) => {
  return price.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

const getParticleStyle = (index: number) => {
  const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3']
  const color = colors[index % colors.length]
  const left = Math.random() * 100
  const animationDelay = Math.random() * 2
  const animationDuration = 1 + Math.random() * 2
  
  return {
    left: `${left}%`,
    backgroundColor: color,
    animationDelay: `${animationDelay}s`,
    animationDuration: `${animationDuration}s`
  }
}

const getParticipantRecords = (username: string) => {
  return personalRecords.value.get(username) || []
}

const getParticipantRecordCount = (username: string) => {
  return getParticipantRecords(username).length
}

// 核心动画逻辑
const startSimultaneousBoxOpeningWithRecords = () => {
  const { animation_start_timestamp, server_timestamp, animation_duration } = props.animationConfig
  
  // 计算本地开始时间
  const networkDelay = Date.now() - server_timestamp
  const localStartTime = animation_start_timestamp - networkDelay
  const delayUntilStart = localStartTime - Date.now()
  
  setTimeout(() => {
    // 开始动画
    isOpening.value = true
    animationStartTime.value = Date.now()
    
    // 启动全局进度指示器
    startGlobalProgress(animation_duration)
    
    // 为每个参与者启动个人开箱记录动画
    props.participants.forEach((participant) => {
      startPersonalItemsReveal(participant, {
        duration: animation_duration,
        items: participant.openingItems || []
      })
    })
    
    // 设置动画完成定时器
    animationTimer.value = setTimeout(() => {
      completeAnimation()
    }, animation_duration)
    
  }, Math.max(delayUntilStart, 0))
}

const startPersonalItemsReveal = (participant: any, config: any) => {
  const { user, openingItems = [] } = participant
  const { duration } = config
  
  // 为每个物品计算揭晓时间
  openingItems.forEach((item: any, index: number) => {
    const revealDelay = (duration / openingItems.length) * index
    
    setTimeout(() => {
      // 添加物品到个人记录
      addItemToPersonalRecord(user.username, item)
      
      // 触发物品揭晓事件
      emit('item-revealed', participant, item)
      
    }, revealDelay)
  })
}

const addItemToPersonalRecord = (username: string, item: any) => {
  const currentRecords = personalRecords.value.get(username) || []
  const newRecords = [...currentRecords, item]
  personalRecords.value.set(username, newRecords)
}

const startGlobalProgress = (duration: number) => {
  const startTime = Date.now()
  
  progressTimer.value = setInterval(() => {
    const elapsed = Date.now() - startTime
    progressPercentage.value = Math.min((elapsed / duration) * 100, 100)
    
    if (progressPercentage.value >= 100) {
      if (progressTimer.value) {
        clearInterval(progressTimer.value)
      }
    }
  }, 100)
}

const completeAnimation = () => {
  isOpening.value = false
  isAnimationComplete.value = true
  progressPercentage.value = 100
  
  // 清理定时器
  if (animationTimer.value) {
    clearTimeout(animationTimer.value)
  }
  if (progressTimer.value) {
    clearInterval(progressTimer.value)
  }
  
  // 触发完成事件
  emit('animation-complete')
}

const skipAnimation = () => {
  completeAnimation()
}

// 监听 props 变化
watch(() => props.show, (newShow) => {
  if (newShow && props.animationConfig) {
    // 重置状态
    isOpening.value = false
    isAnimationComplete.value = false
    progressPercentage.value = 0
    personalRecords.value.clear()
    
    // 开始动画
    startSimultaneousBoxOpeningWithRecords()
  }
})

// 生命周期
onUnmounted(() => {
  if (animationTimer.value) {
    clearTimeout(animationTimer.value)
  }
  if (progressTimer.value) {
    clearInterval(progressTimer.value)
  }
})
</script>

<style scoped lang="scss">
.battle-calculation-animation {
  @apply fixed inset-0 z-50 flex flex-col items-center justify-center p-4 bg-black/90 backdrop-blur-sm;
  
  .animation-header {
    @apply text-center mb-8;
    
    .animation-title {
      @apply text-3xl font-bold text-white mb-4;
    }
    
    .global-progress {
      @apply relative w-80 h-3 bg-gray-800 rounded-full overflow-hidden;
      
      .progress-bar {
        @apply h-full bg-gradient-to-r from-blue-500 to-purple-500 transition-all duration-100 ease-out;
      }
      
      .progress-text {
        @apply absolute inset-0 flex items-center justify-center text-white text-sm font-medium;
      }
    }
  }
  
  .participants-grid {
    @apply grid gap-6 w-full max-w-6xl;
    
    &.grid-2 {
      @apply grid-cols-2;
    }
    
    &.grid-3 {
      @apply grid-cols-3;
      @media (max-width: 768px) {
        @apply grid-cols-2;
      }
    }
    
    &.grid-4 {
      @apply grid-cols-2;
      @media (min-width: 1024px) {
        @apply grid-cols-4;
      }
    }
  }
  
  .participant-area {
    @apply flex flex-col gap-4;
    
    .case-animation-area {
      @apply text-center;
      
      .case-box {
        @apply relative inline-block mb-4;
        
        .case-image {
          @apply w-24 h-24 rounded-lg object-cover;
        }
        
        &.opening {
          .case-image {
            animation: case-rotation 2s linear infinite;
          }
          
          .animation-effects {
            .rotation-ring {
              @apply absolute inset-0 border-2 border-blue-400/50 rounded-lg;
              animation: ring-rotation 1.5s linear infinite reverse;
            }
            
            .particle-container {
              @apply absolute inset-0 overflow-hidden;
              
              .particle {
                @apply absolute w-1 h-1 rounded-full;
                animation: particle-float linear infinite;
              }
            }
            
            .glow-effect {
              @apply absolute inset-0 rounded-lg;
              animation: glow-pulse 2s ease-in-out infinite;
            }
          }
        }
      }
      
      .player-info {
        @apply flex flex-col items-center gap-2;
        
        .player-avatar {
          @apply w-12 h-12 rounded-full border-2 border-gray-600;
        }
        
        .player-name {
          @apply text-white font-medium text-sm;
        }
      }
    }
    
    .personal-records {
      @apply flex-1 min-h-48 max-h-64 bg-gray-900/50 rounded-lg border border-gray-700/30 overflow-hidden;
      
      .records-header {
        @apply flex items-center gap-2 p-3 border-b border-gray-700/30 bg-gray-800/50;
        
        .records-count {
          @apply text-gray-400 text-sm;
        }
      }
      
      .records-list {
        @apply p-3 space-y-2 max-h-40 overflow-y-auto;
        
        .record-item {
          @apply flex items-center gap-3 p-2 bg-gray-800/30 rounded border-l-4 transition-all duration-300;
          
          .item-image {
            @apply w-8 h-8 rounded object-cover flex-shrink-0;
          }
          
          .item-info {
            @apply flex-1 min-w-0;
            
            .item-name {
              @apply text-white text-sm font-medium truncate;
            }
            
            .item-rarity {
              @apply text-gray-400 text-xs;
            }
          }
          
          .item-price {
            @apply text-green-400 text-sm font-semibold flex-shrink-0;
          }
        }
      }
    }
  }
  
  .skip-button {
    @apply mt-8 px-6 py-3 bg-gray-700 hover:bg-gray-600 text-white rounded-lg font-medium transition-colors flex items-center gap-2;
  }
}

// 动画定义
@keyframes case-rotation {
  0% { transform: rotateY(0deg); }
  100% { transform: rotateY(360deg); }
}

@keyframes ring-rotation {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes particle-float {
  0% {
    transform: translateY(100%) translateX(0);
    opacity: 1;
  }
  100% {
    transform: translateY(-100%) translateX(20px);
    opacity: 0;
  }
}

@keyframes glow-pulse {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.6);
  }
}

// 模态框过渡动画
.calculation-enter-active,
.calculation-leave-active {
  transition: all 0.3s ease;
}

.calculation-enter-from,
.calculation-leave-to {
  opacity: 0;
  transform: scale(0.9);
}

// 响应式设计
@media (max-width: 768px) {
  .battle-calculation-animation {
    @apply p-2;
    
    .animation-header {
      @apply mb-4;
      
      .animation-title {
        @apply text-2xl;
      }
      
      .global-progress {
        @apply w-64;
      }
    }
    
    .participants-grid {
      @apply gap-3;
    }
    
    .participant-area {
      .case-animation-area .case-box .case-image {
        @apply w-20 h-20;
      }
      
      .personal-records {
        @apply min-h-40;
      }
    }
  }
}
</style> 