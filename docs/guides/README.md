# 使用指南

本目录包含快速开始指南、常见问题解答等用户导向的文档。

## 📋 文档列表

- [**快速开始**](quickstart.md) - 5分钟快速上手平台开发
- [**常见问题**](faq.md) - 开发和使用过程中的常见问题解答

## 🚀 新手入门

### 1. 环境准备
确保你的开发环境具备以下条件：
- **Docker** 20.10+
- **Docker Compose** 2.0+
- **Git** 2.0+
- **Node.js** 16+ (可选，用于前端开发)

### 2. 项目启动
```bash
# 1. 克隆项目
git clone <repository-url>
cd csgoskins.com.cn

# 2. 配置环境变量
cp server/example.env server/.env

# 3. 启动服务
docker-compose up -d

# 4. 初始化数据库
docker-compose exec web python manage.py migrate
docker-compose exec web python manage.py createsuperuser
```

### 3. 验证部署
- 🌐 **API服务**: http://localhost:8000
- 🛠️ **管理后台**: http://localhost:8000/admin
- 💬 **WebSocket**: ws://localhost:3001

## 🎯 开发流程

### API开发
1. 查看 [API规范](../api/overview.md)
2. 实现认证流程
3. 开发业务接口
4. 编写接口文档
5. 进行集成测试

### 前端开发
1. 了解 [前端架构](../frontend/overview.md)
2. 集成API接口
3. 实现动画效果
4. 响应式适配
5. 性能优化

### 功能测试
```bash
# 运行测试套件
docker-compose exec web python manage.py test

# 测试特定模块
docker-compose exec web python manage.py test authentication
docker-compose exec web python manage.py test box
```

## 📚 学习路径

### 后端开发者
1. **Django基础** - 熟悉Django框架和ORM
2. **REST API** - 掌握DRF开发规范
3. **WebSocket** - 了解实时通信机制
4. **数据库设计** - 学习数据模型优化
5. **部署运维** - 掌握Docker部署

### 前端开发者
1. **HTML/CSS/JS** - 前端基础技能
2. **GSAP动画** - 学习动画库使用
3. **WebSocket客户端** - 实时通信集成
4. **响应式设计** - 移动端适配
5. **性能优化** - 前端性能调优

### 全栈开发者
1. 完成后端和前端学习路径
2. **API设计** - 前后端接口协作
3. **状态管理** - 数据同步机制
4. **用户体验** - 交互设计优化
5. **系统架构** - 整体技术选型

## 🔗 相关资源

### 官方文档
- [Django官方文档](https://docs.djangoproject.com/)
- [Django REST Framework](https://www.django-rest-framework.org/)
- [GSAP动画库](https://greensock.com/docs/)
- [Docker官方文档](https://docs.docker.com/)

### 社区资源
- [Django中文社区](https://djangochina.cn/)
- [前端动画教程](https://animista.net/)
- [WebSocket最佳实践](https://websocket.org/)

### 开发工具
- **IDE**: PyCharm, VS Code
- **API测试**: Postman, Insomnia
- **数据库管理**: MySQL Workbench, phpMyAdmin
- **版本控制**: Git, GitHub/GitLab

## 📝 贡献指南

### 代码贡献
1. Fork项目到个人仓库
2. 创建功能分支 `git checkout -b feature/new-feature`
3. 提交更改 `git commit -m 'Add new feature'`
4. 推送分支 `git push origin feature/new-feature`
5. 创建Pull Request

### 文档贡献
1. 发现文档错误或不足
2. 编辑相应的Markdown文件
3. 提交Pull Request
4. 等待审核和合并

### 问题反馈
- 🐛 **Bug报告**: 使用Issue模板提交
- 💡 **功能建议**: 详细描述需求和场景
- ❓ **使用问题**: 先查看FAQ，再提问

---

*更新时间: 2025-06-18*
