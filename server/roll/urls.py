from django.urls import re_path as url

from roll import views, model_signals



app_name = 'roll'
urlpatterns = [
    url(r'^create/', views.CreateRollRoomView.as_view()), #
    url(r'^edit/', views.EditRollRoomView.as_view()), #
    url(r'^deposit/', views.DepositRollItemsView.as_view()), #
    url(r'^bet/', views.JoinRollRoomView.as_view()),
    url(r'^cancel/', views.CancelRollRoomView.as_view()), #
    url(r'^list', views.GetRollRoomListView.as_view()), #
    url(r'^detail', views.GetRollRoomDetailView.as_view()), #
    url(r'^items', views.GetRollRoomItemsView.as_view()), #
    url(r'^bets', views.GetRollRoomBetsView.as_view()),
    url(r'^joinablenum/', views.GetRollRoomJoinableNumView.as_view()),

    # 新接口
    url(r'^rolldetail', views.GetRollDetailView.as_view()), 
    url(r'^rolllist', views.GetRollListView.as_view()), 
    url(r'^rollskin', views.GetRollSkinView.as_view()), 
    url(r'^rolluser', views.GetRollUserView.as_view()), 
    url(r'^usernum', views.GetRollUserNumView.as_view()), 
    url(r'^userjoin', views.RollUserJoinView.as_view()), 
    url(r'^checkstatus', views.RollCheckStatusView.as_view()), 
    url(r'^rollwinner', views.GetRollWinnerView.as_view()), 

    # test
    url(r'^clearupduplicateuser/', views.ClearupDuplicateRollUserView.as_view()),

    # 新的roll房
    url(r'^newroll', views.GetNewRollListView.as_view()), 

    # 强制开奖
    # url(r'^rollint/', views.InitRollRoomDataView.as_view()),

    # url(r'^open/', views.OpenRollView.as_view()), 
]
