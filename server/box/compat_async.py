#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python 3.6兼容的异步处理模块
解决asyncio.create_task在Python 3.6中不存在的问题
"""

import sys
import asyncio
import logging
from typing import Any, Coroutine, Optional, Union
from concurrent.futures import ThreadPoolExecutor
import threading

logger = logging.getLogger(__name__)


class AsyncCompat:
    """异步兼容性管理器"""
    
    def __init__(self):
        self.python_version = sys.version_info
        self.supports_create_task = hasattr(asyncio, 'create_task')
        self.loop = None
        self.executor = ThreadPoolExecutor(max_workers=4)
        
    def create_task_compat(self, coro: Coroutine) -> Any:
        """兼容的任务创建方法"""
        try:
            # 检查是否有运行的事件循环 (Python 3.6兼容)
            try:
                if self.python_version >= (3, 7):
                    loop = asyncio.get_running_loop()
                else:
                    # Python 3.6 使用 get_event_loop 并检查是否在运行
                    loop = asyncio.get_event_loop()
                    if not loop.is_running():
                        raise RuntimeError("No running event loop")
                
                # 有运行的事件循环，直接创建任务
                if self.supports_create_task:
                    return asyncio.create_task(coro)
                else:
                    return asyncio.ensure_future(coro)
                    
            except RuntimeError:
                # 没有运行的事件循环，使用线程池执行
                logger.warning("没有运行的事件循环，使用线程池执行异步代码")
                return self.run_in_thread(self.run_async_safe, coro)
                
        except Exception as e:
            logger.error(f"创建异步任务失败: {e}")
            raise
    
    def run_async_safe(self, coro: Coroutine, timeout: Optional[float] = None):
        """安全运行异步协程"""
        try:
            if self.python_version >= (3, 7):
                # Python 3.7+ 使用 run
                return asyncio.run(coro)
            else:
                # Python 3.6 使用 run_until_complete
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    if timeout:
                        return loop.run_until_complete(
                            asyncio.wait_for(coro, timeout=timeout)
                        )
                    else:
                        return loop.run_until_complete(coro)
                finally:
                    loop.close()
        except Exception as e:
            logger.error(f"运行异步协程失败: {e}")
            raise
    
    def create_future_compat(self):
        """兼容的Future创建"""
        try:
            if self.loop is None:
                self.loop = asyncio.get_event_loop()
            return self.loop.create_future()
        except RuntimeError:
            # 没有运行的事件循环
            loop = asyncio.new_event_loop()
            return loop.create_future()
    
    def run_in_thread(self, func, *args, **kwargs):
        """在线程池中运行同步函数"""
        return self.executor.submit(func, *args, **kwargs)
    
    def schedule_callback(self, callback, delay: float = 0):
        """调度回调函数"""
        def run_callback():
            import time
            if delay > 0:
                time.sleep(delay)
            try:
                callback()
            except Exception as e:
                logger.error(f"回调函数执行失败: {e}")
        
        thread = threading.Thread(target=run_callback)
        thread.daemon = True
        thread.start()
        return thread


class AsyncBattleProcessor:
    """异步对战处理器"""
    
    def __init__(self):
        self.compat = AsyncCompat()
        self.processing_queue = []
        self.max_queue_size = 100
        
    async def process_round_async(self, room_uid: str, round_num: int, 
                                 callback_data: dict) -> dict:
        """异步处理轮次"""
        try:
            # 模拟异步处理
            await asyncio.sleep(0.1)  # 非阻塞延迟
            
            result = {
                'room_uid': room_uid,
                'round': round_num,
                'status': 'completed',
                'timestamp': self._get_timestamp(),
                'data': callback_data
            }
            
            logger.info(f"异步轮次处理完成: room={room_uid}, round={round_num}")
            return result
            
        except Exception as e:
            logger.error(f"异步轮次处理失败: room={room_uid}, error={e}")
            raise
    
    def process_round_sync_fallback(self, room_uid: str, round_num: int, 
                                   callback_data: dict) -> dict:
        """同步降级处理"""
        try:
            import time
            time.sleep(0.05)  # 模拟处理时间
            
            result = {
                'room_uid': room_uid,
                'round': round_num,
                'status': 'completed_sync',
                'timestamp': self._get_timestamp(),
                'data': callback_data
            }
            
            logger.info(f"同步轮次处理完成: room={room_uid}, round={round_num}")
            return result
            
        except Exception as e:
            logger.error(f"同步轮次处理失败: room={room_uid}, error={e}")
            raise
    
    def schedule_round_processing(self, room_uid: str, round_num: int, 
                                 callback_data: dict, use_async: bool = True):
        """调度轮次处理"""
        try:
            if use_async and self.compat.supports_create_task:
                # 尝试异步处理
                coro = self.process_round_async(room_uid, round_num, callback_data)
                
                try:
                    # 使用兼容的方式创建任务
                    task = self.compat.create_task_compat(coro)
                    
                    # 添加完成回调
                    if hasattr(task, 'add_done_callback'):
                        task.add_done_callback(
                            lambda t: self._handle_async_result(t, room_uid, round_num)
                        )
                    
                    return task
                    
                except Exception as e:
                    logger.warning(f"异步处理失败，降级到同步: {e}")
                    return self._fallback_to_sync(room_uid, round_num, callback_data)
            else:
                # 直接使用同步处理
                return self._fallback_to_sync(room_uid, round_num, callback_data)
                
        except Exception as e:
            logger.error(f"调度轮次处理失败: room={room_uid}, error={e}")
            return self._fallback_to_sync(room_uid, round_num, callback_data)
    
    def _fallback_to_sync(self, room_uid: str, round_num: int, callback_data: dict):
        """降级到同步处理"""
        future = self.compat.run_in_thread(
            self.process_round_sync_fallback,
            room_uid, round_num, callback_data
        )
        
        # 记录降级事件
        from box.battle_config import BattleSystemMetrics
        metrics = BattleSystemMetrics()
        metrics.increment_counter('async_fallbacks_today')
        
        return future
    
    def _handle_async_result(self, task, room_uid: str, round_num: int):
        """处理异步结果"""
        try:
            if task.exception():
                logger.error(f"异步任务失败: room={room_uid}, round={round_num}, "
                           f"error={task.exception()}")
            else:
                result = task.result()
                logger.debug(f"异步任务完成: room={room_uid}, result={result}")
        except Exception as e:
            logger.error(f"处理异步结果失败: {e}")
    
    def create_task_safe(self, coro):
        """安全创建异步任务 - 兼容方法"""
        try:
            return self.compat.create_task_compat(coro)
        except Exception as e:
            logger.error(f"创建异步任务失败: {e}")
            # 返回一个简单的Future，表示失败状态
            future = self.compat.create_future_compat()
            future.set_exception(e)
            return future
    
    def _get_timestamp(self):
        """获取时间戳"""
        import time
        return int(time.time() * 1000)


# 全局实例
async_processor = AsyncBattleProcessor()


def get_async_processor():
    """获取异步处理器实例"""
    return async_processor


def create_task_safe(coro: Coroutine):
    """安全创建异步任务"""
    return async_processor.compat.create_task_compat(coro)


def run_async_safe(coro: Coroutine, timeout: Optional[float] = None):
    """安全运行异步协程"""
    return async_processor.compat.run_async_safe(coro, timeout)


def schedule_round_processing_safe(room_uid: str, round_num: int, 
                                  callback_data: dict, use_async: bool = True):
    """安全调度轮次处理"""
    return async_processor.schedule_round_processing(
        room_uid, round_num, callback_data, use_async
    )
