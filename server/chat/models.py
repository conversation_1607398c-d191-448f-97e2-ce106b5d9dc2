from django.db import models
from django.utils import timezone
from django.contrib.auth.models import User
from django.utils.translation import gettext_lazy as _
from django.utils.timezone import make_aware
from datetime import datetime, timedelta, timezone as dt_timezone

from steambase.models import ModelBase, USER_MODEL, ItemBase

# 定义东八区时区
CST_TZ = dt_timezone(timedelta(hours=8))  # 东八区

class Message(models.Model):
    sender = models.ForeignKey(USER_MODEL, on_delete=models.SET_NULL,
        related_name='sent_messages',
        verbose_name=_("Sender"),
        null=True,
        blank=True
    )
    sender_name = models.CharField(_("Sender Name"), max_length=150, null=True, blank=True)
    recipient = models.ForeignKey(USER_MODEL, on_delete=models.SET_NULL,
        related_name='received_messages',
        verbose_name=_("Recipient"),
        null=True,
        blank=True
    )
    recipient_name = models.CharField(_("Recipient Name"), max_length=150, null=True, blank=True)
    subject = models.CharField(_("Subject"), max_length=255, null=True, blank=True)
    body = models.TextField(_("Body"), null=True, blank=True)
    sent_at = models.DateTimeField(_("Sent At"), default=timezone.now)
    read_at = models.DateTimeField(_("Read At"), null=True, blank=True)  # 用于记录阅读时间，None 表示未读

    class Meta:
        verbose_name = _("Message")
        verbose_name_plural = _("Messages")
        ordering = ['-sent_at']

    def __str__(self):
        return f"Message from {self.sender_name or 'Unknown'} to {self.recipient_name or 'Unknown'} - {self.subject or 'No Subject'}"
    
    def save(self, *args, **kwargs):
        """在首次保存时记录 sender_name 和 recipient_name"""
        if not self.sender_name and self.sender:
            self.sender_name = self.sender.username
        if not self.recipient_name and self.recipient:
            self.recipient_name = self.recipient.username
        super().save(*args, **kwargs)

    def mark_as_read(self):
        """标记消息为已读并记录阅读时间"""
        if not self.read_at:
            self.read_at = timezone.now().astimezone(CST_TZ)  # 记录东八区时间
            self.save(update_fields=['read_at'])

    @property
    def is_read(self):
        """返回消息是否已读"""
        return self.read_at is not None
  


class ChatBotMessage(models.Model):
    message = models.TextField(_('Message'))
    remark = models.CharField(_('remark'), max_length=128)
    enable = models.BooleanField(_('enable'), default=True)

    class Meta:
        verbose_name = _('Chat Bot Message')
        verbose_name_plural = _('Chat Bot Message')

    def __str__(self):
        return self.remark


class ChatBot(models.Model):
    user = models.OneToOneField(USER_MODEL, on_delete=models.CASCADE, verbose_name=_("user"), related_name='chat_bot')
    chat_idle_min = models.IntegerField(_('chat idle min(seconds)'), default=0)
    chat_idle_max = models.IntegerField(_('chat idle max(seconds)'), default=0)
    message = models.ManyToManyField(ChatBotMessage, verbose_name=_('message'), related_name='chat_bot',
                                     default=None, blank=True)
    remark = models.CharField(_('remark'), max_length=128)
    enable = models.BooleanField(_('enable'), default=True)

    class Meta:
        verbose_name = _('Chat Bot Config')
        verbose_name_plural = _('Chat Bot Config')

    def __str__(self):
        return self.remark
