from django.db import models
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.contrib.auth import get_user_model

# from ckeditor_uploader.fields import RichTextUploadingField
# from steambase.models import ModelBase

User = get_user_model()


class ContentCategory(models.Model):
    """内容分类"""
    created_at = models.DateTimeField(_("Created At"), auto_now_add=True)
    updated_at = models.DateTimeField(_("Updated At"), auto_now=True)
    name = models.CharField(_("Category Name"), max_length=128)
    slug = models.SlugField(_("Slug"), max_length=128, unique=True)
    description = models.TextField(_("Description"), blank=True, null=True)
    icon = models.CharField(_("Icon"), max_length=255, blank=True, null=True)
    color = models.CharField(_("Color"), max_length=7, default="#007bff", help_text=_("Hex color code"))
    sort_order = models.IntegerField(_("Sort Order"), default=0)
    is_active = models.BooleanField(_("Is Active"), default=True)
    
    class Meta:
        verbose_name = _("Content Category")
        verbose_name_plural = _("Content Categories")
        ordering = ['sort_order', 'name']
    
    def __str__(self):
        return self.name


class ContentTag(models.Model):
    """内容标签"""
    created_at = models.DateTimeField(_("Created At"), auto_now_add=True)
    updated_at = models.DateTimeField(_("Updated At"), auto_now=True)
    name = models.CharField(_("Tag Name"), max_length=64)
    slug = models.SlugField(_("Slug"), max_length=64, unique=True)
    color = models.CharField(_("Color"), max_length=7, default="#6c757d", help_text=_("Hex color code"))
    is_active = models.BooleanField(_("Is Active"), default=True)
    
    class Meta:
        verbose_name = _("Content Tag")
        verbose_name_plural = _("Content Tags")
        ordering = ['name']
    
    def __str__(self):
        return self.name


class Content(models.Model):
    """通用内容模型 - 支持文章和公告"""
    
    CONTENT_TYPE_CHOICES = [
        ('article', _('Article')),
        ('announcement', _('Announcement')),
        ('news', _('News')),
        ('guide', _('Guide')),
        ('faq', _('FAQ')),
        ('policy', _('Policy')),
        ('notice', _('Notice')),
    ]
    
    STATUS_CHOICES = [
        ('draft', _('Draft')),
        ('published', _('Published')),
        ('archived', _('Archived')),
        ('scheduled', _('Scheduled')),
    ]
    
    PRIORITY_CHOICES = [
        ('low', _('Low')),
        ('normal', _('Normal')),
        ('high', _('High')),
        ('urgent', _('Urgent')),
    ]
    
    # 时间戳
    created_at = models.DateTimeField(_("Created At"), auto_now_add=True)
    updated_at = models.DateTimeField(_("Updated At"), auto_now=True)

    # 基本信息
    title = models.CharField(_("Title"), max_length=255)
    slug = models.SlugField(_("Slug"), max_length=255, unique=True, blank=True)
    subtitle = models.CharField(_("Subtitle"), max_length=255, blank=True, null=True)
    excerpt = models.TextField(_("Excerpt"), max_length=500, blank=True, null=True, 
                              help_text=_("Brief description or summary"))
    content = models.TextField(_("Content"), blank=True, null=True)
    
    # 分类和标签
    content_type = models.CharField(_("Content Type"), max_length=20, 
                                   choices=CONTENT_TYPE_CHOICES, default='article')
    category = models.ForeignKey(ContentCategory, on_delete=models.SET_NULL, 
                                verbose_name=_("Category"), null=True, blank=True,
                                related_name='contents')
    tags = models.ManyToManyField(ContentTag, verbose_name=_("Tags"), blank=True,
                                 related_name='contents')
    
    # 状态和发布
    status = models.CharField(_("Status"), max_length=20, choices=STATUS_CHOICES, default='draft')
    priority = models.CharField(_("Priority"), max_length=20, choices=PRIORITY_CHOICES, default='normal')
    is_featured = models.BooleanField(_("Is Featured"), default=False)
    is_pinned = models.BooleanField(_("Is Pinned"), default=False)
    
    # 时间管理
    publish_date = models.DateTimeField(_("Publish Date"), default=timezone.now)
    expire_date = models.DateTimeField(_("Expire Date"), blank=True, null=True)
    
    # 作者和编辑
    author = models.ForeignKey(User, on_delete=models.SET_NULL, verbose_name=_("Author"), 
                              null=True, blank=True, related_name='authored_contents')
    editor = models.ForeignKey(User, on_delete=models.SET_NULL, verbose_name=_("Last Editor"), 
                              null=True, blank=True, related_name='edited_contents')
    
    # 媒体
    featured_image = models.CharField(_("Featured Image"), max_length=500, blank=True, null=True)
    thumbnail = models.CharField(_("Thumbnail"), max_length=500, blank=True, null=True)
    
    # SEO
    seo_title = models.CharField(_("SEO Title"), max_length=255, blank=True, null=True)
    seo_description = models.TextField(_("SEO Description"), max_length=300, blank=True, null=True)
    seo_keywords = models.CharField(_("SEO Keywords"), max_length=255, blank=True, null=True)
    
    # 显示设置
    sort_order = models.IntegerField(_("Sort Order"), default=0)
    view_count = models.PositiveIntegerField(_("View Count"), default=0)
    allow_comments = models.BooleanField(_("Allow Comments"), default=True)
    
    # 样式设置
    custom_css_class = models.CharField(_("Custom CSS Class"), max_length=255, blank=True, null=True)
    background_color = models.CharField(_("Background Color"), max_length=7, blank=True, null=True)
    text_color = models.CharField(_("Text Color"), max_length=7, blank=True, null=True)
    
    class Meta:
        verbose_name = _("Content")
        verbose_name_plural = _("Contents")
        ordering = ['-is_pinned', '-priority', '-publish_date']
        indexes = [
            models.Index(fields=['content_type', 'status']),
            models.Index(fields=['publish_date']),
            models.Index(fields=['is_featured']),
            models.Index(fields=['is_pinned']),
        ]
    
    def __str__(self):
        return f"[{self.get_content_type_display()}] {self.title}"
    
    def save(self, *args, **kwargs):
        if not self.slug:
            from django.utils.text import slugify
            import uuid
            self.slug = slugify(self.title)[:240] + '-' + str(uuid.uuid4())[:8]
        super().save(*args, **kwargs)
    
    @property
    def is_published(self):
        return (self.status == 'published' and 
                self.publish_date <= timezone.now() and
                (not self.expire_date or self.expire_date > timezone.now()))
    
    @property
    def is_expired(self):
        return self.expire_date and self.expire_date <= timezone.now()


class ContentAttachment(models.Model):
    """内容附件"""
    created_at = models.DateTimeField(_("Created At"), auto_now_add=True)
    updated_at = models.DateTimeField(_("Updated At"), auto_now=True)
    content = models.ForeignKey(Content, on_delete=models.CASCADE,
                               verbose_name=_("Content"), related_name='attachments')
    name = models.CharField(_("File Name"), max_length=255)
    file_path = models.CharField(_("File Path"), max_length=500)
    file_size = models.PositiveIntegerField(_("File Size"), help_text=_("Size in bytes"))
    file_type = models.CharField(_("File Type"), max_length=100)
    description = models.TextField(_("Description"), blank=True, null=True)
    download_count = models.PositiveIntegerField(_("Download Count"), default=0)
    
    class Meta:
        verbose_name = _("Content Attachment")
        verbose_name_plural = _("Content Attachments")
    
    def __str__(self):
        return f"{self.content.title} - {self.name}"


class ContentView(models.Model):
    """内容浏览记录"""
    content = models.ForeignKey(Content, on_delete=models.CASCADE, related_name='views')
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    ip_address = models.GenericIPAddressField(_("IP Address"))
    user_agent = models.TextField(_("User Agent"), blank=True, null=True)
    viewed_at = models.DateTimeField(_("Viewed At"), auto_now_add=True)
    
    class Meta:
        verbose_name = _("Content View")
        verbose_name_plural = _("Content Views")
        unique_together = ['content', 'user', 'ip_address']
    
    def __str__(self):
        return f"{self.content.title} - {self.ip_address}"
