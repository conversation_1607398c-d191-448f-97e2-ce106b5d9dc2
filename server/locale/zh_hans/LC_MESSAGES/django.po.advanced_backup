# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-19 09:21+0800\n"
"PO-Revision-Date: 2020-09-18 19:09+0800\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: zh\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 2.2.3\n"

#: agent/business.py:489
#, python-brace-format
msgid "用户 {user.username} 提取饰品金额 {amount}，手续费 {fee}"
msgstr "用户 {user.username} 提取饰品金额 {amount}，手续费 {fee}"

#: agent/business.py:521
msgid "用户充值"
msgstr "用户充值"

#: agent/business.py:611
#, python-brace-format
msgid "代理商提现 {amount}"
msgstr "代理商提现 {amount}"

#: agent/models.py:10 agent/models.py:119 agent/models.py:133
#, fuzzy
#| msgid "Auth User"
msgid "User"
msgstr "注册用户"

#: agent/models.py:11
msgid "Agent Name"
msgstr "代理商名称"

#: agent/models.py:12
msgid "Agent Phone"
msgstr "代理商电话"

#: agent/models.py:13
msgid "Agent Email"
msgstr "代理商邮箱"

#: agent/models.py:14
#, fuzzy
#| msgid "ban chat"
msgid "Agent Wechat"
msgstr "禁止聊天"

#: agent/models.py:15
msgid "Agent QQ"
msgstr "代理商QQ"

#: agent/models.py:16
msgid "Agent Alipay"
msgstr "代理商支付宝"

#: agent/models.py:17
msgid "Agent Wechatpay"
msgstr "代理商微信支付"

#: agent/models.py:18
msgid "Agent Bankcard"
msgstr "代理商银行卡"

#: agent/models.py:19
msgid "Agent Bank"
msgstr "代理商银行"

#: agent/models.py:20
msgid "Agent Bankname"
msgstr "代理商银行名称"

#: agent/models.py:21 roll/models.py:93
msgid "Balance"
msgstr "余额"

#: agent/models.py:22 agent/models.py:115 agent/models.py:131
#, fuzzy
#| msgid "enable"
msgid "Enable"
msgstr "可用"

#: agent/models.py:23 agent/models.py:75 agent/models.py:101
#: agent/models.py:116
#, fuzzy
#| msgid "create time"
msgid "Create Time"
msgstr "创建时间"

#: agent/models.py:24 agent/models.py:76 agent/models.py:102
#: agent/models.py:117
#, fuzzy
#| msgid "update time"
msgid "Update Time"
msgstr "更新时间"

#: agent/models.py:25 agent/models.py:74 agent/models.py:100
#, fuzzy
#| msgid "remark"
msgid "Remark"
msgstr "备注"

#: agent/models.py:30 agent/models.py:62 agent/models.py:88
#: sitecfg/models.py:192
msgid "Agent"
msgstr "代理商"

#: agent/models.py:31
msgid "Agents"
msgstr "代理商"

#: agent/models.py:63 tradeup/models.py:47
msgid "Amount"
msgstr "金额"

#: agent/models.py:65
#, fuzzy
#| msgid "Initial"
msgid "Init"
msgstr "初始"

#: agent/models.py:66
#, fuzzy
#| msgid "Succeed"
msgid "Success"
msgstr "成功"

#: agent/models.py:67 charge/models.py:19
msgid "Failed"
msgstr "失败"

#: agent/models.py:68 articles/admin.py:108 articles/models.py:100
#, fuzzy
#| msgid "status"
msgid "Status"
msgstr "状态"

#: agent/models.py:70
msgid "Alipay"
msgstr "支付宝"

#: agent/models.py:71
msgid "Wechatpay"
msgstr "微信支付"

#: agent/models.py:72
msgid "Bankcard"
msgstr "银行卡"

#: agent/models.py:73
#, fuzzy
#| msgid "Case Type"
msgid "Pay Type"
msgstr "箱子种类"

#: agent/models.py:80 agent/models.py:95
msgid "Agent Withdrawal Order"
msgstr "代理商提现订单"

#: agent/models.py:81
msgid "Agent Withdrawal Orders"
msgstr "代理商提现订单"

#: agent/models.py:89
#, fuzzy
#| msgid "Invalid balance change"
msgid "Balance Changed"
msgstr "余额不足，请先充值"

#: agent/models.py:90
#, fuzzy
#| msgid "User Balance Record"
msgid "Balance Before"
msgstr "用户余额记录"

#: agent/models.py:91
#, fuzzy
#| msgid "Balance"
msgid "Balance After"
msgstr "余额"

#: agent/models.py:93
#, fuzzy
#| msgid "charge"
msgid "User Recharge"
msgstr "充值"

#: agent/models.py:94
#, fuzzy
#| msgid "Withdraw"
msgid "User Withdrawal"
msgstr "取回"

#: agent/models.py:96
msgid "Other"
msgstr "其他"

#: agent/models.py:97
#, fuzzy
#| msgid "Case Type"
msgid "Type"
msgstr "箱子种类"

#: agent/models.py:98
#, fuzzy
#| msgid "out trade No"
msgid "Out Trade No"
msgstr "交易编号"

#: agent/models.py:106
#, fuzzy
#| msgid "User Balance Record"
msgid "Agent Balance Record"
msgstr "用户余额记录"

#: agent/models.py:107
#, fuzzy
#| msgid "User Balance Record"
msgid "Agent Balance Records"
msgstr "用户余额记录"

#: agent/models.py:113 articles/models.py:83 sitecfg/models.py:184
#: templates/admin_backup/base_copy_backup.html:305
#: templates/admin_backup/base_jet_backup.html:289
msgid "Title"
msgstr "标题"

#: agent/models.py:114 articles/models.py:88 articles/models.py:135
#: articles/models.py:171
#, fuzzy
#| msgid "content"
msgid "Content"
msgstr "描述内容"

#: agent/models.py:118 articles/models.py:94 package/models.py:37
#, fuzzy
#| msgid "category"
msgid "Category"
msgstr "类别"

#: agent/models.py:123 articles/models.py:55
msgid "Article"
msgstr "文章"

#: agent/models.py:124
msgid "Articles"
msgstr "文章"

#: agent/models.py:130 sitecfg/models.py:182
msgid "Name"
msgstr "名称"

#: agent/models.py:132
msgid "Site"
msgstr "站点"

#: agent/models.py:137 sitecfg/models.py:150 sitecfg/models.py:151
#, fuzzy
#| msgid "category"
msgid "Article Category"
msgstr "类别"

#: agent/models.py:138
msgid "Article Categories"
msgstr "文章分类"

#: agent/views.py:67 agent/views.py:84 agent/views.py:103 agent/views.py:120
#: agent/views.py:137 agent/views.py:163 agent/views.py:210 agent/views.py:306
#: agent/views.py:320 agent/views.py:342 agent/views.py:357 agent/views.py:379
#: authentication/business.py:876 blindbox/views.py:39 blindbox/views.py:59
#: blindbox/views.py:78 blindbox/views.py:98 blindbox/views.py:119
#: blindbox/views.py:139 blindbox/views.py:163 blindbox/views.py:183
#: box/views.py:52 box/views.py:565 crash/views.py:135 crash/views.py:160
#: crash/views.py:185 crash/views.py:203 tradeup/views.py:70
#: tradeup/views.py:92 tradeup/views.py:115 tradeup/views.py:162
#: tradeup/views.py:188 tradeup/views.py:213 tradeup/views.py:238
#: tradeup/views.py:261 tradeup/views.py:283 tradeup/views.py:306
#: websocket/views.py:29
msgid "Exception"
msgstr "异常"

#: articles/admin.py:20 articles/admin.py:39
#, fuzzy
#| msgid "content"
msgid "Content Count"
msgstr "描述内容"

#: articles/admin.py:35
#, fuzzy
#| msgid "review"
msgid "Tag Preview"
msgstr "审核"

#: articles/admin.py:64
msgid "Basic Information"
msgstr "基本信息"

#: articles/admin.py:67
#, fuzzy
#| msgid "Applications"
msgid "Classification"
msgstr "应用"

#: articles/admin.py:70
msgid "Publishing"
msgstr "发布"

#: articles/admin.py:73
msgid "Author & Editor"
msgstr "编辑"

#: articles/admin.py:76
msgid "Media"
msgstr "媒体"

#: articles/admin.py:80 sitecfg/models.py:195 sitecfg/models.py:196
msgid "SEO"
msgstr "SEO"

#: articles/admin.py:84
msgid "Display Settings"
msgstr "显示设置"

#: articles/admin.py:88
#, fuzzy
#| msgid "Lottery Setting"
msgid "Style Settings"
msgstr "抽奖设置"

#: articles/admin.py:122 articles/models.py:101
#, fuzzy
#| msgid "rarity"
msgid "Priority"
msgstr "稀有性"

#: articles/admin.py:135
msgid "Mark selected contents as published"
msgstr "将选定的内容标记为已发布"

#: articles/admin.py:140
msgid "Mark selected contents as draft"
msgstr "将选定的内容标记为草稿"

#: articles/admin.py:145
msgid "Mark selected contents as featured"
msgstr "将选定的内容标记为推荐"

#: articles/admin.py:150
msgid "Remove featured from selected contents"
msgstr "从选定的内容中移除推荐"

#: articles/admin.py:166 articles/models.py:174
msgid "File Size"
msgstr "文件大小"

#: articles/apps.py:8
msgid "Articles & Announcements Management"
msgstr "文章和公告管理"

#: articles/models.py:14 articles/models.py:35 articles/models.py:79
#: articles/models.py:168
#, fuzzy
#| msgid "Read At"
msgid "Created At"
msgstr "创建时间"

#: articles/models.py:15 articles/models.py:36 articles/models.py:80
#: articles/models.py:169
#, fuzzy
#| msgid "update time"
msgid "Updated At"
msgstr "更新时间"

#: articles/models.py:16
#, fuzzy
#| msgid "create time"
msgid "Category Name"
msgstr "类别名称"

#: articles/models.py:17 articles/models.py:38 articles/models.py:84
msgid "Slug"
msgstr "别名"

#: articles/models.py:18 articles/models.py:176 sitecfg/models.py:186
#, fuzzy
#| msgid "description"
msgid "Description"
msgstr "备注"

#: articles/models.py:19 package/models.py:57
msgid "Icon"
msgstr "图标"

#: articles/models.py:20 articles/models.py:39
#, fuzzy
#| msgid "tag color"
msgid "Color"
msgstr "标签颜色"

#: articles/models.py:20 articles/models.py:39
msgid "Hex color code"
msgstr "十六进制颜色代码"

#: articles/models.py:21 articles/models.py:125
#, fuzzy
#| msgid "order"
msgid "Sort Order"
msgstr "排序"

#: articles/models.py:22 articles/models.py:40
#, fuzzy
#| msgid "Active"
msgid "Is Active"
msgstr "活跃的"

#: articles/models.py:25
#, fuzzy
#| msgid "WearCategory0"
msgid "Content Category"
msgstr "崭新出厂"

#: articles/models.py:26
#, fuzzy
#| msgid "Article Categories"
msgid "Content Categories"
msgstr "文章分类"

#: articles/models.py:37
#, fuzzy
#| msgid "Type Name"
msgid "Tag Name"
msgstr "类型名称"

#: articles/models.py:43
#, fuzzy
#| msgid "content"
msgid "Content Tag"
msgstr "描述内容"

#: articles/models.py:44
#, fuzzy
#| msgid "content"
msgid "Content Tags"
msgstr "描述内容"

#: articles/models.py:56
#, fuzzy
#| msgid "Announce"
msgid "Announcement"
msgstr "声明"

#: articles/models.py:57
msgid "News"
msgstr "新闻"

#: articles/models.py:58
msgid "Guide"
msgstr "指南"

#: articles/models.py:59 sitecfg/models.py:89 sitecfg/models.py:90
msgid "FAQ"
msgstr "FAQ"

#: articles/models.py:60
msgid "Policy"
msgstr "政策"

#: articles/models.py:61
msgid "Notice"
msgstr "公告"

#: articles/models.py:65
msgid "Draft"
msgstr "草稿"

#: articles/models.py:66
msgid "Published"
msgstr "已发布"

#: articles/models.py:67
#, fuzzy
#| msgid "Actived"
msgid "Archived"
msgstr "活跃的"

#: articles/models.py:68
msgid "Scheduled"
msgstr "已安排"

#: articles/models.py:72
msgid "Low"
msgstr "低"

#: articles/models.py:73 blindbox/models.py:12 blindbox/models.py:87
#: sitecfg/models.py:42
msgid "Normal"
msgstr "正常"

#: articles/models.py:74
msgid "High"
msgstr "高"

#: articles/models.py:75
#, fuzzy
#| msgid "Agent"
msgid "Urgent"
msgstr "代理商"

#: articles/models.py:85 sitecfg/models.py:183
#, fuzzy
#| msgid "title"
msgid "Subtitle"
msgstr "标题"

#: articles/models.py:86
#, fuzzy
#| msgid "Exception"
msgid "Excerpt"
msgstr "异常"

#: articles/models.py:87
#, fuzzy
#| msgid "description"
msgid "Brief description or summary"
msgstr "备注"

#: articles/models.py:91
#, fuzzy
#| msgid "content"
msgid "Content Type"
msgstr "描述内容"

#: articles/models.py:96
msgid "Tags"
msgstr "标签"

#: articles/models.py:102
msgid "Is Featured"
msgstr "是否推荐"

#: articles/models.py:103
#, fuzzy
#| msgid "winner"
msgid "Is Pinned"
msgstr "获胜者"

#: articles/models.py:106
msgid "Publish Date"
msgstr "发布时间"

#: articles/models.py:107
#, fuzzy
#| msgid "expire time"
msgid "Expire Date"
msgstr "过期时间"

#: articles/models.py:110
#, fuzzy
#| msgid "Auth User"
msgid "Author"
msgstr "注册用户"

#: articles/models.py:112
msgid "Last Editor"
msgstr "最后编辑"

#: articles/models.py:116
msgid "Featured Image"
msgstr "推荐图片"

#: articles/models.py:117
msgid "Thumbnail"
msgstr "缩略图"

#: articles/models.py:120
#, fuzzy
#| msgid "Title"
msgid "SEO Title"
msgstr "标题"

#: articles/models.py:121
#, fuzzy
#| msgid "description"
msgid "SEO Description"
msgstr "备注"

#: articles/models.py:122
#, fuzzy
#| msgid "Keywords"
msgid "SEO Keywords"
msgstr "关键词"

#: articles/models.py:126
#, fuzzy
#| msgid "View on site"
msgid "View Count"
msgstr "View on site"

#: articles/models.py:127
msgid "Allow Comments"
msgstr "允许评论"

#: articles/models.py:130
msgid "Custom CSS Class"
msgstr "自定义CSS类"

#: articles/models.py:131
#, fuzzy
#| msgid "background class"
msgid "Background Color"
msgstr "背景类"

#: articles/models.py:132
#, fuzzy
#| msgid "exterior"
msgid "Text Color"
msgstr "外观"

#: articles/models.py:136
#, fuzzy
#| msgid "content"
msgid "Contents"
msgstr "描述内容"

#: articles/models.py:172
#, fuzzy
#| msgid "Type Name"
msgid "File Name"
msgstr "类型名称"

#: articles/models.py:173
msgid "File Path"
msgstr "文件路径"

#: articles/models.py:174
msgid "Size in bytes"
msgstr "大小"

#: articles/models.py:175
#, fuzzy
#| msgid "Case Type"
msgid "File Type"
msgstr "箱子种类"

#: articles/models.py:177
#, fuzzy
#| msgid "Invalid count"
msgid "Download Count"
msgstr "无效数量"

#: articles/models.py:180
msgid "Content Attachment"
msgstr "内容附件"

#: articles/models.py:181
msgid "Content Attachments"
msgstr "内容附件"

#: articles/models.py:191
#, fuzzy
#| msgid "email address"
msgid "IP Address"
msgstr "邮箱地址"

#: articles/models.py:192
#, fuzzy
#| msgid "Agent"
msgid "User Agent"
msgstr "代理商"

#: articles/models.py:193
#, fuzzy
#| msgid "View site"
msgid "Viewed At"
msgstr "浏览站点"

#: articles/models.py:196
#, fuzzy
#| msgid "content"
msgid "Content View"
msgstr "描述内容"

#: articles/models.py:197
#, fuzzy
#| msgid "content"
msgid "Content Views"
msgstr "描述内容"

#: authentication/admin.py:82
msgid "Permissions"
msgstr "许可"

#: authentication/admin.py:83
msgid "Important dates"
msgstr "重要日期"

#: authentication/admin.py:126
msgid "余额"
msgstr "余额"

#: authentication/admin.py:131
msgid "总充值余额"
msgstr "总充值余额"

#: authentication/admin.py:135
msgid "昵称"
msgstr "昵称"

#: authentication/admin.py:152
msgid "box_chance_type"
msgstr "开箱概率类型"

#: authentication/apps.py:7
msgid "Account"
msgstr "账户"

#: authentication/business.py:92 authentication/business.py:100
#: package/business.py:227
msgid "Invalid trade url"
msgstr "无效交易链接"

#: authentication/business.py:106
msgid "User asset not found"
msgstr "用户资产未找到"

#: authentication/business.py:108
msgid "No changes were detected in the URL you provided."
msgstr "您提交的URL没有任何改变。"

#: authentication/business.py:140
#, fuzzy
#| msgid "Invalid trade url"
msgid "Invalid trade URL format"
msgstr "无效交易链接"

#: authentication/business.py:151
msgid "This Steam link is already in use. Each link can bind only one account."
msgstr "该steam链接已绑定其他用户,一个链接仅支持绑定一个账户"

#: authentication/business.py:156
msgid "Failed to update. An unknown error occurred."
msgstr "更新失败，未知错误"

#: authentication/business.py:160
msgid "Update failed. The system detected a security issue with this operation. Please ensure your actions comply with security policies."
msgstr "更新失败，系统检测到本次操作存在安全问题。请确保您的操作符合安全政策。"

#: authentication/business.py:186 authentication/business.py:215
#: authentication/business.py:242 authentication/business.py:275
msgid "Oops! Wrong CAPTCHA. Try again."
msgstr "验证码错误，请重试"

#: authentication/business.py:219
msgid "This phone number has already been bound to a different user. Please check and retry."
msgstr "该手机已绑定其他用户，请重试"

#: authentication/business.py:279
msgid "Oops! This email is already taken."
msgstr "该邮箱已绑定其他用户，请重试"

#: authentication/business.py:295
msgid "Exceeded verification code sending limit. Please try again later."
msgstr "超过验证码发送限制"

#: authentication/business.py:307
msgid "Failed to send verification code. Please ensure your network connection is stable and try again."
msgstr "验证码发送失败，请确保您的网络连接稳定并重试。"

#: authentication/business.py:339
msgid "Username must be 2–10 characters. Please adjust and try again."
msgstr "用户名长度必须在2-10字符之间。请调整并重试。"

#: authentication/business.py:342
msgid "Error: Username cannot contain a phone number. Please try another username."
msgstr "用户名不能是手机号"

#: authentication/business.py:345 authentication/business.py:492
#: authentication/business.py:891
msgid "API request failed due to missing parameters."
msgstr "缺少参数"

#: authentication/business.py:347 authentication/business.py:496
#: authentication/business.py:628 authentication/business.py:898
msgid "Error: Password must be 8 to 16 characters."
msgstr "密码必须8-16位"

#: authentication/business.py:353 authentication/business.py:641
msgid "Security alert: High registration activity detected from this IP. Please try again later."
msgstr "同一IP注册过多，请稍后重试"

#: authentication/business.py:360 authentication/business.py:648
msgid "Too many verification code attempts. Please try again later."
msgstr "验证码尝试次数过多，请稍后重试"

#: authentication/business.py:367 authentication/business.py:655
msgid "Verification failed: Code expired."
msgstr "验证码过期"

#: authentication/business.py:371 authentication/business.py:512
#: authentication/business.py:659 authentication/business.py:920
#: authentication/business.py:1198 authentication/business.py:1344
msgid "Verification failed: Incorrect code."
msgstr "验证码错误"

#: authentication/business.py:375
msgid "The token does not match. Please try again."
msgstr "Token不匹配"

#: authentication/business.py:377
msgid "Token or verification code validation failed. Please ensure you have entered the correct information and try again."
msgstr "Token或验证码验证失败"

#: authentication/business.py:379
msgid "Verification failed: Token or code has expired."
msgstr "Token或验证码已过期"

#: authentication/business.py:388
msgid "Error: Phone number already in use. Please try another one."
msgstr "手机号已存在"

#: authentication/business.py:429
msgid "Invalid phone number."
msgstr "无效的手机号码"

#: authentication/business.py:431 authentication/business.py:843
msgid "You must enter a password to proceed."
msgstr "密码不能为空"

#: authentication/business.py:436
msgid "System interception: We have detected bulk registration attempts or malicious activity. Please comply with our terms of service and avoid such behavior."
msgstr "系统拦截，请不要批量注册账户或企图使用非法手段攻击本站"

#: authentication/business.py:458 authentication/business.py:871
msgid "Login successful"
msgstr "登录成功"

#: authentication/business.py:461 authentication/business.py:873
msgid "Invalid username or password. Please try again."
msgstr "账号或密码错误"

#: authentication/business.py:464 authentication/business.py:519
#: authentication/business.py:860 authentication/business.py:903
#: authentication/business.py:950
msgid "This account does not exist."
msgstr "该账号不存在"

#: authentication/business.py:503 authentication/business.py:911
#: authentication/business.py:1189
msgid "Verification code is expired. Please try again with a new one."
msgstr "验证码已过期，请重新获取"

#: authentication/business.py:525 authentication/business.py:928
msgid "Password reset successful"
msgstr "密码重置成功"

#: authentication/business.py:626 blindbox/business.py:231
#: blindbox/business.py:302 envelope/business.py:36 market/business.py:100
msgid "API request failed due to missing parameters."
msgstr "参数错误"

#: authentication/business.py:632
msgid "Username length cannot exceed 10 characters."
msgstr "用户名长度不能超过10个字符"

#: authentication/business.py:666
msgid "Email already registered"
msgstr "邮箱已存在"

#: authentication/business.py:754 authentication/business.py:1031
#, fuzzy
#| msgid "Invalid room type"
msgid "Invalid verification type"
msgstr "无效房间种类"

#: authentication/business.py:761
msgid "Email service not configured"
msgstr "邮件服务未配置"

#: authentication/business.py:766
#, fuzzy
#| msgid "Code is already exists"
msgid "Email already registered"
msgstr "代码已经存在"

#: authentication/business.py:768 authentication/business.py:1069
#, fuzzy
#| msgid "User Promotion"
msgid "User not found"
msgstr "用户推广信息"

#: authentication/business.py:792 authentication/business.py:1062
msgid "Please wait 5 minutes before requesting another code."
msgstr "请等待5分钟后再请求验证码"

#: authentication/business.py:807
msgid "Failed to send verification code"
msgstr "验证码发送失败"

#: authentication/business.py:809
msgid "Email service not available"
msgstr "邮件服务不可用"

#: authentication/business.py:814 authentication/business.py:1099
msgid "System error while sending verification code."
msgstr "发送验证码时系统错误"

#: authentication/business.py:837
msgid "CAPTCHA cannot be empty"
msgstr "验证码不能为空"

#: authentication/business.py:839
msgid "Missing CAPTCHA identifier"
msgstr "缺少验证码标识"

#: authentication/business.py:841
msgid "Invalid email address"
msgstr "邮箱地址无效"

#: authentication/business.py:894
msgid "Invalid email format"
msgstr "邮箱格式不正确"

#: authentication/business.py:952
msgid "You are not allowed to change your nickname"
msgstr "您被禁止修改昵称"

#: authentication/business.py:954
msgid "Username must be 2–16 characters. Please adjust and try again."
msgstr "用户名长度必须大于2小于16"

#: authentication/business.py:964 charge/business.py:1232
#: charge/business.py:1268 charge/business.py:1305 charge/business.py:1367
#: charge/business.py:1420 charge/business.py:1577 envelope/business.py:34
msgid "Please log in first"
msgstr "请先登录"

#: authentication/business.py:966
msgid "You are not allowed to change your avatar"
msgstr "您被禁止修改头像"

#: authentication/business.py:968
msgid "Image data is empty"
msgstr "图片数据为空"

#: authentication/business.py:970
msgid "Only jpeg, jpg, and png files are supported"
msgstr "仅支持 jpeg, jpg, png 格式文件"

#: authentication/business.py:976
msgid "User profile does not exist"
msgstr "用户资料不存在"

#: authentication/business.py:990
msgid "Error occurred while uploading avatar"
msgstr "上传头像时发生错误"

#: authentication/business.py:1035
msgid "Token is required"
msgstr "需要Token"

#: authentication/business.py:1040
#, fuzzy
#| msgid "Invalid operation"
msgid "Invalid or expired token"
msgstr "无效操作"

#: authentication/business.py:1042
#, fuzzy
#| msgid "Code is already exists"
msgid "Token already used"
msgstr "代码已经存在"

#: authentication/business.py:1048
msgid "Token validation failed"
msgstr "Token验证失败"

#: authentication/business.py:1057
msgid "SMS service not configured"
msgstr "短信服务未配置"

#: authentication/business.py:1067
msgid "Phone number already registered"
msgstr "手机号已经被注册"

#: authentication/business.py:1096
msgid "Failed to send verification code."
msgstr "验证码发送失败"

#: authentication/business.py:1154 authentication/business.py:1171
#: charge/views.py:356
msgid "User does not exist"
msgstr "用户不存在"

#: authentication/business.py:1159
msgid "Congratulations on passing the exam"
msgstr "恭喜您通过了考试"

#: authentication/business.py:1159
msgid "Unfortunately, you did not pass the exam. Please retake it."
msgstr "很遗憾，您没有通过规则考试，请重考"

#: authentication/business.py:1174 authentication/business.py:1215
msgid "An error occurred"
msgstr "发生了错误"

#: authentication/business.py:1182
msgid "This email does not exist"
msgstr "该邮箱不存在"

#: authentication/business.py:1240
msgid "Invalid verification type"
msgstr "无效的验证类型"

#: authentication/business.py:1242
msgid "Contact information cannot be empty"
msgstr "联系信息不能为空"

#: authentication/business.py:1246
msgid "Invalid phone number format"
msgstr "无效的手机号码格式"

#: authentication/business.py:1248
msgid "Invalid email format"
msgstr "无效的邮箱格式"

#: authentication/business.py:1258
msgid "Invalid client"
msgstr "无效的客户端"

#: authentication/business.py:1264
msgid "Website not allowed"
msgstr "网站不允许"

#: authentication/business.py:1271
msgid "Under maintenance"
msgstr "系统维护中"

#: authentication/business.py:1279
msgid "Exceeded daily token limit"
msgstr "超出每日令牌限制"

#: authentication/business.py:1291
msgid "Too many requests from the same IP for different contact methods"
msgstr "同一IP请求过多不同联系方式"

#: authentication/business.py:1312
msgid "Token generated successfully"
msgstr "令牌生成成功"

#: authentication/business.py:1316
msgid "An error occurred"
msgstr "发生异常"

#: authentication/business.py:1335
msgid "Verification code parameters are incomplete"
msgstr "验证码参数不完整"

#: authentication/business.py:1340
msgid "Verification code expired. Please refresh."
msgstr "验证码已过期，请刷新"

#: authentication/business.py:1348
msgid "Verification successful"
msgstr "验证成功"

#: authentication/models.py:28
msgid "nick name"
msgstr "昵称"

#: authentication/models.py:29
msgid "avatar"
msgstr "头像"

#: authentication/models.py:33 authentication/models.py:34
msgid "Profile"
msgstr "个人资料"

#: authentication/models.py:42
msgid "Steam trade url"
msgstr "Steam交易链接"

#: authentication/models.py:43 roll/models.py:98
msgid "balance"
msgstr "余额"

#: authentication/models.py:44
msgid "points"
msgstr "积分"

#: authentication/models.py:45 package/models.py:238
msgid "Diamond"
msgstr "钻石"

#: authentication/models.py:46
msgid "active point"
msgstr "活跃积分"

#: authentication/models.py:47
msgid "total charge balance"
msgstr "总充值余额"

#: authentication/models.py:49
#, fuzzy
#| msgid "charge limit amount"
msgid "daily charge limit"
msgstr "充值金额限制"

#: authentication/models.py:51
#, fuzzy
#| msgid "total charge balance"
msgid "total withdraw balance"
msgstr "总充值余额"

#: authentication/models.py:54 authentication/models.py:55
msgid "Asset"
msgstr "资产"

#: authentication/models.py:63 package/models.py:324
msgid "Steamid"
msgstr "Steamid"

#: authentication/models.py:64
msgid "Steam name"
msgstr "Steam名字"

#: authentication/models.py:65
msgid "profile url"
msgstr "个人资料链接"

#: authentication/models.py:66
msgid "small avatar"
msgstr "小头像"

#: authentication/models.py:67
msgid "medium avatar"
msgstr "中头像"

#: authentication/models.py:68
msgid "big avatar"
msgstr "大头像"

#: authentication/models.py:69
msgid "Steam level"
msgstr "Steam等级"

#: authentication/models.py:70
msgid "own games count"
msgstr "拥有游戏数量"

#: authentication/models.py:71
msgid "Dota2 playtime"
msgstr "Dota2游戏时间"

#: authentication/models.py:74 authentication/models.py:75
msgid "Steam"
msgstr "Steam"

#: authentication/models.py:83
msgid "Box chance A"
msgstr "开箱概率A"

#: authentication/models.py:84
msgid "Box chance B"
msgstr "开箱概率B"

#: authentication/models.py:85
msgid "Box chance C"
msgstr "开箱概率C"

#: authentication/models.py:86
msgid "Box chance D"
msgstr "开箱概率D"

#: authentication/models.py:87
msgid "Box chance E"
msgstr "开箱概率E"

#: authentication/models.py:90
msgid "Lucky Box Rate Type A"
msgstr "拉货概率折扣A"

#: authentication/models.py:91
msgid "Lucky Box Rate Type B"
msgstr "拉货概率折扣B"

#: authentication/models.py:92
msgid "Lucky Box Rate Type C"
msgstr "拉货概率折扣C"

#: authentication/models.py:93
msgid "Lucky Box Rate Type D"
msgstr "拉货概率折扣D"

#: authentication/models.py:94
msgid "Lucky Box Rate Type E"
msgstr "拉货概率折扣E"

#: authentication/models.py:97
msgid "box chance type"
msgstr "开箱概率种类"

#: authentication/models.py:98
msgid "box free count"
msgstr "box free count"

#: authentication/models.py:99
msgid "box free last"
msgstr "box free last"

#: authentication/models.py:100
msgid "box free level 0 count"
msgstr "box free level 0 count"

#: authentication/models.py:101
msgid "box free level 0 last"
msgstr "box free level 0 last"

#: authentication/models.py:102
msgid "ban chat"
msgstr "禁止聊天"

#: authentication/models.py:103 package/models.py:374
msgid "ban deposit"
msgstr "禁止存入"

#: authentication/models.py:104 package/models.py:375
msgid "ban withdraw"
msgstr "禁止取回"

#: authentication/models.py:105 package/models.py:376
msgid "ban exchange"
msgstr "禁止交换"

#: authentication/models.py:106 package/models.py:377
msgid "ban shop"
msgstr "禁止开店"

#: authentication/models.py:107
msgid "ban roll room"
msgstr "禁止roll房"

#: authentication/models.py:108
msgid "ban create roll room"
msgstr "禁止创建roll房"

#: authentication/models.py:109
msgid "ban charge room"
msgstr "禁止收费房间"

#: authentication/models.py:110
msgid "box free give count"
msgstr "白给箱剩余次数"

#: authentication/models.py:111
msgid "freebox lv1 count"
msgstr "免费等级1箱子次数"

#: authentication/models.py:112
msgid "freebox lv2 count"
msgstr "免费等级2箱子次数"

#: authentication/models.py:113
msgid "freebox lv3 count"
msgstr "免费等级3箱子次数"

#: authentication/models.py:114
msgid "freebox lv4 count"
msgstr "免费等级4箱子次数"

#: authentication/models.py:115
msgid "freebox lv5 count"
msgstr "免费等级5箱子次数"

#: authentication/models.py:116
msgid "freebox lv6 count"
msgstr "免费等级6箱子次数"

#: authentication/models.py:117
msgid "freebox lv7 count"
msgstr "免费等级7箱子次数"

#: authentication/models.py:118
msgid "freebox lv8 count"
msgstr "免费等级8箱子次数"

#: authentication/models.py:119
msgid "freebox lv9 count"
msgstr "免费等级9箱子次数"

#: authentication/models.py:120
msgid "freebox lv10 count"
msgstr "免费等级10箱子次数"

#: authentication/models.py:121
msgid "freebox lv1 limit"
msgstr "免费等级1箱子次数限制"

#: authentication/models.py:122
msgid "freebox lv2 limit"
msgstr "免费等级2箱子次数限制"

#: authentication/models.py:123
msgid "freebox lv3 limit"
msgstr "免费等级3箱子次数限制"

#: authentication/models.py:124
msgid "freebox lv4 limit"
msgstr "免费等级4箱子次数限制"

#: authentication/models.py:125
msgid "freebox lv5 limit"
msgstr "免费等级5箱子次数限制"

#: authentication/models.py:126
msgid "freebox lv6 limit"
msgstr "免费等级6箱子次数限制"

#: authentication/models.py:127
msgid "freebox lv7 limit"
msgstr "免费等级7箱子次数限制"

#: authentication/models.py:128
msgid "freebox lv8 limit"
msgstr "免费等级8箱子次数限制"

#: authentication/models.py:129
msgid "freebox lv9 limit"
msgstr "免费等级9箱子次数限制"

#: authentication/models.py:130
msgid "freebox lv10 limit"
msgstr "免费等级10箱子次数限制"

#: authentication/models.py:131
msgid "luckybox rate type"
msgstr "拉货概率折扣类型"

#: authentication/models.py:132
#, fuzzy
#| msgid "Promotion Level Config"
msgid "box promotion level"
msgstr "推广等级配置"

#: authentication/models.py:134
#, fuzzy
#| msgid "ban withdraw"
msgid "ban withdraw reason"
msgstr "禁止取回"

#: authentication/models.py:135
#, fuzzy
#| msgid "ban deposit"
msgid "ban deposit reason"
msgstr "禁止存入"

#: authentication/models.py:137
#, fuzzy
#| msgid "ban chat"
msgid "ban battle"
msgstr "禁止聊天"

#: authentication/models.py:139
msgid "ban battle reason"
msgstr "禁止对战原因"

#: authentication/models.py:141
#, fuzzy
#| msgid "market name"
msgid "ban rename"
msgstr "市场名称"

#: authentication/models.py:143
#, fuzzy
#| msgid "Base by chance"
msgid "locked box chance"
msgstr "基于概率"

#: authentication/models.py:145
msgid "exam passed"
msgstr "考试通过"

#: authentication/models.py:146
#, fuzzy
#| msgid "pay time"
msgid "exam time"
msgstr "支付时间"

#: authentication/models.py:148
#, fuzzy
#| msgid "big avatar"
msgid "ban avatar"
msgstr "大头像"

#: authentication/models.py:151
#, fuzzy
#| msgid "profit"
msgid "profit limit"
msgstr "收益"

#: authentication/models.py:152
msgid "loss limit"
msgstr "损失限制"

#: authentication/models.py:157 authentication/models.py:158
msgid "Extra"
msgstr "其他"

#: authentication/models.py:168 authentication/models.py:170
#: authentication/models.py:178 authentication/models.py:180
#: authentication/models.py:188 authentication/models.py:190
#: authentication/models.py:198 authentication/models.py:200
#: authentication/models.py:208 authentication/models.py:210
#: authentication/models.py:218 authentication/models.py:220
#: authentication/models.py:228 authentication/models.py:230
#: authentication/models.py:238 authentication/models.py:240
#: authentication/models.py:248 authentication/models.py:250
#: authentication/models.py:258 authentication/models.py:260
#: authentication/models.py:268
#, fuzzy
#| msgid "Invalid points change"
msgid "Invalid count change"
msgstr "无效积分变更"

#: authentication/models.py:271
msgid "Opening limit exceeded for this level case."
msgstr "该等级箱子开启超过限制"

#: authentication/models.py:276 authentication/models.py:291
#: authentication/models.py:306 authentication/models.py:321
#: b2ctrade/models.py:66 b2ctrade/models.py:190 blindbox/models.py:73
#: blindbox/models.py:90 blindbox/models.py:125 box/models.py:154
#: box/models.py:189 box/models.py:240 box/models.py:257 box/models.py:372
#: box/models.py:386 box/models.py:401 box/models.py:417 box/models.py:431
#: charge/models.py:28 charge/models.py:151 charge/models.py:193
#: charge/models.py:289 chat/models.py:76 crash/models.py:47
#: crash/models.py:296 crash/models.py:320 custombox/models.py:12
#: custombox/models.py:82 envelope/models.py:45 lottery/models.py:44
#: package/models.py:180 package/models.py:240 package/models.py:257
#: package/models.py:285 package/models.py:372 promotion/models.py:19
#: promotion/models.py:39 roll/models.py:28 roll/models.py:75
#: roll/models.py:176 tradeup/models.py:23 tradeup/models.py:341
#: tradeup/models.py:365 tradeup/models.py:395 withdraw/models.py:37
msgid "user"
msgstr "用户"

#: authentication/models.py:277 authentication/models.py:292
#: authentication/models.py:307 authentication/models.py:322
msgid "changed"
msgstr "已变更"

#: authentication/models.py:278 authentication/models.py:293
#: authentication/models.py:308 authentication/models.py:323
msgid "change before"
msgstr "变更以前"

#: authentication/models.py:279 authentication/models.py:294
#: authentication/models.py:309 authentication/models.py:324
msgid "change after"
msgstr "变更之后"

#: authentication/models.py:280 authentication/models.py:295
#: authentication/models.py:310 authentication/models.py:325
#: authentication/models.py:699 withdraw/models.py:78
msgid "reason"
msgstr "原因"

#: authentication/models.py:283 authentication/models.py:284
msgid "User Balance Record"
msgstr "用户余额记录"

#: authentication/models.py:298 authentication/models.py:299
msgid "User Points Record"
msgstr "用户积分记录"

#: authentication/models.py:313 authentication/models.py:314
msgid "User Diamond Record"
msgstr "用户钻石记录"

#: authentication/models.py:328 authentication/models.py:329
msgid "User Active Point Record"
msgstr "用户活跃积分记录"

#: authentication/models.py:370
msgid "user uid"
msgstr "用户uid"

#: authentication/models.py:371
msgid "user name"
msgstr "用户名字"

#: authentication/models.py:372
msgid "email address"
msgstr "邮箱地址"

#: authentication/models.py:373 authentication/models.py:681
msgid "phone"
msgstr "电话"

#: authentication/models.py:374
msgid "date joined"
msgstr "加入时间"

#: authentication/models.py:375 sitecfg/models.py:181
msgid "Domain"
msgstr "域名"

#: authentication/models.py:376
msgid "registered ip"
msgstr "注册IP"

#: authentication/models.py:377
msgid "login ip"
msgstr "登录IP"

#: authentication/models.py:378
msgid "login domain"
msgstr "登录域名"

#: authentication/models.py:379
#, fuzzy
#| msgid "begin time"
msgid "login time"
msgstr "开始时间"

#: authentication/models.py:382
msgid "note"
msgstr "备注"

#: authentication/models.py:383
msgid "is vip"
msgstr "是否VIP"

#: authentication/models.py:390
msgid "staff status"
msgstr "用户状态"

#: authentication/models.py:392
msgid "Designates whether the user can log into this admin site."
msgstr "指定用户是否可以登录此管理站点。"

#: authentication/models.py:394
#, fuzzy
#| msgid "trade status"
msgid "agent status"
msgstr "交易状态"

#: authentication/models.py:396 promotion/models.py:42
msgid "active"
msgstr "活跃的"

#: authentication/models.py:399
msgid ""
"Designates whether this user should be treated as active. Unselect this "
"instead of deleting accounts."
msgstr "指定是否应将此用户视为活动用户。取消选择它而不是删除账户。"

#: authentication/models.py:404
msgid "用户锁定原因"
msgstr "用户锁定原因"

#: authentication/models.py:475
msgid "Invalid balance change"
msgstr "余额不足，请先充值"

#: authentication/models.py:518
msgid "Invalid points change"
msgstr "无效积分变更"

#: authentication/models.py:540
msgid "Invalid diamond change"
msgstr "无效钻石变更"

#: authentication/models.py:557
msgid "Invalid active point change"
msgstr "无效活跃积分变更"

#: authentication/models.py:573 authentication/models.py:584
#: authentication/models.py:638
msgid "No enough count"
msgstr "没有足够的数量"

#: authentication/models.py:618
#, fuzzy
#| msgid "Invalid balance change"
msgid "Invalid total charge balance change"
msgstr "余额不足，请先充值"

#: authentication/models.py:643 authentication/models.py:644
msgid "Auth User"
msgstr "注册用户"

#: authentication/models.py:657 b2ctrade/models.py:211 b2ctrade/models.py:254
#: box/models.py:273 box/models.py:297 box/models.py:387 box/models.py:402
#: box/models.py:418 charge/models.py:64 charge/models.py:103
#: crash/models.py:65 crash/models.py:89 crash/models.py:118
#: crash/models.py:202 crash/models.py:297 package/models.py:392
#: package/models.py:421 roll/models.py:122 roll/models.py:146
#: roll/models.py:203 roll/models.py:263 tradeup/models.py:110
#: tradeup/models.py:134 tradeup/models.py:163 tradeup/models.py:247
#: tradeup/models.py:342 withdraw/models.py:100 withdraw/models.py:143
msgid "date"
msgstr "日期"

#: authentication/models.py:658 b2ctrade/models.py:23 b2ctrade/models.py:174
#: charge/models.py:277 tradeup/models.py:97
msgid "count"
msgstr "数量"

#: authentication/models.py:675 authentication/models.py:676
msgid "User Statistics Day"
msgstr "日用户统计"

#: authentication/models.py:684
#, fuzzy
#| msgid "qr code"
msgid "code"
msgstr "二维码"

#: authentication/models.py:685
msgid "ip"
msgstr "IP地址"

#: authentication/models.py:686
msgid "domain"
msgstr "域名"

#: authentication/models.py:693 authentication/models.py:694
#, fuzzy
#| msgid "EnvelopeRecord"
msgid "Phone Code Record"
msgstr "红包记录"

#: authentication/models.py:698 withdraw/models.py:39
msgid "steam id"
msgstr "steam id"

#: authentication/models.py:700 authentication/models.py:712
#: b2ctrade/models.py:25 b2ctrade/models.py:173 b2ctrade/models.py:191
#: blindbox/models.py:41 blindbox/models.py:130 box/models.py:18
#: box/models.py:32 box/models.py:61 box/models.py:262 box/models.py:360
#: box/models.py:470 charge/models.py:248 charge/models.py:326
#: chat/models.py:65 chat/models.py:82 lottery/models.py:22
#: lottery/models.py:72 package/models.py:326 package/models.py:348
#: package/models.py:361 package/models.py:373 package/models.py:479
#: roll/models.py:180 sitecfg/models.py:25 sitecfg/models.py:48
#: sitecfg/models.py:71 sitecfg/models.py:85 sitecfg/models.py:101
#: sitecfg/models.py:120 sitecfg/models.py:146 sitecfg/models.py:163
#: sitecfg/models.py:187 sitecfg/models.py:206 tradeup/models.py:99
#: tradeup/models.py:403
msgid "enable"
msgstr "可用"

#: authentication/models.py:706 authentication/models.py:707
#, fuzzy
#| msgid "BlackList"
msgid "Steam Black List"
msgstr "黑名单"

#: authentication/models.py:711
msgid "suffix"
msgstr "后缀"

#: authentication/models.py:717 authentication/models.py:718
msgid "Email Suffix"
msgstr "邮箱后缀"

#: authentication/pipeline.py:85 authentication/pipeline.py:88
#: authentication/pipeline.py:93
msgid "This Steam account is already in bind."
msgstr "该Steam账户已绑定。"

#: authentication/service/admin_actions.py:39
msgid "Send fail, no admin message settings"
msgstr "发送失败，无管理员消息设置"

#: authentication/service/admin_actions.py:42
msgid "Send message request complete"
msgstr "发送消息请求完成"

#: authentication/service/admin_actions.py:44
msgid "Admin send sms message"
msgstr "管理员发送短信"

#: authentication/service/admin_actions.py:54
msgid "User Chance Type Change To A"
msgstr "B概率用户设置A概率成功"

#: authentication/service/admin_actions.py:56
msgid "User Chance Type Change To B"
msgstr "A概率用户设置B概率成功"

#: authentication/service/admin_actions.py:59
msgid "Change User Chance A B"
msgstr "A、B概率转换"

#: authentication/service/admin_actions.py:94
msgid "用户导出EXCEL"
msgstr "用户导出EXCEL"

#: authentication/service/admin_actions.py:121
msgid "余额记录导出EXCEL"
msgstr "余额记录导出EXCEL"

#: b2ctrade/apps.py:7
msgid "ZBT取回"
msgstr "ZBT取回"

#: b2ctrade/business.py:80 b2ctrade/business.py:1133 envelope/views.py:42
#: roll/views.py:477
msgid "Please set a real and valid Steam link."
msgstr "请先输入您的交易链接"

#: b2ctrade/business.py:83 b2ctrade/business.py:1137
msgid "系统接口维护中，请稍后再试"
msgstr "系统接口维护中，请稍后再试"

#: b2ctrade/business.py:101
msgid "提现数量超过最大限制"
msgstr "提现数量超过最大限制"

#: b2ctrade/business.py:104 b2ctrade/business.py:1160
msgid "提取饰品需要至少充值{}金币"
msgstr "提取饰品需要至少充值{}金币"

#: b2ctrade/business.py:109 b2ctrade/business.py:1212
msgid "饰品不存在或不可提取"
msgstr "饰品不存在或不可提取"

#: b2ctrade/business.py:111 b2ctrade/business.py:1215
msgid "This skin can only be sold."
msgstr "此饰品只能出售"

#: b2ctrade/business.py:115 b2ctrade/business.py:1220
msgid "请提取{}金币以上的饰品"
msgstr "请提取{}金币以上的饰品"

#: b2ctrade/business.py:157 b2ctrade/business.py:218 b2ctrade/business.py:454
#: package/business.py:177 package/business.py:222 package/business.py:324
#: package/business.py:380 package/business.py:421 roll/business.py:65
#: roll/business.py:245 withdraw/business.py:35 withdraw/business.py:100
#: withdraw/business.py:105 withdraw/business.py:127 withdraw/business.py:132
#: withdraw/business.py:156 withdraw/business.py:243 withdraw/business.py:248
msgid "Access denied: You are not authorized to perform this action."
msgstr "无权执行此操作"

#: b2ctrade/business.py:160 b2ctrade/business.py:221 b2ctrade/business.py:457
#: package/business.py:182 withdraw/business.py:38 withdraw/business.py:159
msgid "Please set a real and valid Steam link."
msgstr "请先输入您的交易链接"

#: b2ctrade/business.py:168
msgid "取回数量超过限制"
msgstr "取回数量超过限制"

#: b2ctrade/business.py:170
msgid "提取饰品需要至少充值{}$"
msgstr "提取饰品需要至少充值{}$"

#: b2ctrade/business.py:172
msgid "Please select a weapon skin to extract."
msgstr "请选择提取饰品"

#: b2ctrade/business.py:228
msgid "Over withdarw max limit at sametime"
msgstr "超过同时取回最大订单限制"

#: b2ctrade/business.py:235
msgid "Can not withdraw your deposit item"
msgstr "不能提取该物品"

#: b2ctrade/business.py:256
msgid "Withdraw busy please try later."
msgstr "取回繁忙请稍后再试"

#: b2ctrade/business.py:310 b2ctrade/business.py:464 b2ctrade/business.py:500
#: b2ctrade/business.py:502 withdraw/business.py:164
msgid "Invalid trade"
msgstr "无效交易链接"

#: b2ctrade/business.py:312
msgid "Cancel must waite 20 minutes later."
msgstr "取消必须等待20分钟后"

#: b2ctrade/business.py:466
msgid "Already withdraw by other users."
msgstr "已经被其他用户取回"

#: b2ctrade/business.py:468 luckybox/business.py:262 roll/business.py:188
msgid "Not enough balance"
msgstr "余额不足"

#: b2ctrade/business.py:470
msgid "Not enough available balance"
msgstr "余额不足"

#: b2ctrade/business.py:484
msgid "B2C trade buy"
msgstr "扎比特购买"

#: b2ctrade/business.py:507
#, fuzzy
#| msgid "bot tradeup min chance"
msgid "B2C trade cancel"
msgstr "bot tradeup min chance"

#: b2ctrade/business.py:1155
#, fuzzy
#| msgid "Over withdarw max limit at sametime"
msgid "Over withdraw max limit at sametime"
msgstr "超过同时取回最大订单限制"

#: b2ctrade/models.py:20 b2ctrade/models.py:74 b2ctrade/models.py:137
#: b2ctrade/models.py:171 blindbox/models.py:55 blindbox/models.py:91
#: box/models.py:107 box/models.py:215 box/models.py:243 box/models.py:389
#: box/models.py:404 box/models.py:469 custombox/models.py:29
#: custombox/models.py:85 lottery/models.py:19 lottery/models.py:49
#: package/models.py:104 package/models.py:182 package/models.py:241
#: package/models.py:258 package/models.py:307 package/models.py:343
#: package/models.py:464 package/models.py:477 tradeup/models.py:83
#: tradeup/models.py:95 withdraw/models.py:80
msgid "item info"
msgstr "饰品信息"

#: b2ctrade/models.py:21 blindbox/models.py:34 blindbox/models.py:93
#: box/models.py:48 box/models.py:156 box/models.py:218 box/models.py:245
#: charge/models.py:223 custombox/models.py:16 custombox/models.py:87
#: luckybox/models.py:17 luckybox/models.py:62 package/admin.py:81
#: package/models.py:105 package/models.py:244 package/models.py:261
#: package/models.py:312 roll/models.py:97 tradeup/models.py:51
#: tradeup/models.py:84 tradeup/models.py:96 withdraw/models.py:76
msgid "price"
msgstr "价格"

#: b2ctrade/models.py:22
msgid "zbt price"
msgstr "扎比特价格"

#: b2ctrade/models.py:24 tradeup/models.py:98
msgid "unlimited"
msgstr "未受限"

#: b2ctrade/models.py:26 steambase/models.py:22
msgid "create time"
msgstr "创建时间"

#: b2ctrade/models.py:27 package/models.py:27 package/models.py:103
#: steambase/models.py:23
msgid "update time"
msgstr "更新时间"

#: b2ctrade/models.py:30 b2ctrade/models.py:31
msgid "B2C Market Item"
msgstr "市场物品"

#: b2ctrade/models.py:39 b2ctrade/models.py:110 b2ctrade/models.py:205
#: b2ctrade/models.py:249 steambase/models.py:31 tradeup/models.py:13
#: tradeup/models.py:64 withdraw/models.py:13 withdraw/models.py:94
#: withdraw/models.py:138
msgid "Dota2"
msgstr "Dota2"

#: b2ctrade/models.py:40 b2ctrade/models.py:111 b2ctrade/models.py:206
#: b2ctrade/models.py:250 steambase/models.py:32 tradeup/models.py:14
#: tradeup/models.py:65 withdraw/models.py:14 withdraw/models.py:95
#: withdraw/models.py:139
msgid "CSGO"
msgstr "CSGO"

#: b2ctrade/models.py:41 b2ctrade/models.py:112 b2ctrade/models.py:207
#: b2ctrade/models.py:251 steambase/models.py:33 tradeup/models.py:15
#: tradeup/models.py:66 withdraw/models.py:15 withdraw/models.py:96
#: withdraw/models.py:140
msgid "PUBG"
msgstr "PUBG"

#: b2ctrade/models.py:42 b2ctrade/models.py:113 b2ctrade/models.py:208
#: b2ctrade/models.py:252 steambase/models.py:34 tradeup/models.py:16
#: tradeup/models.py:67 withdraw/models.py:16 withdraw/models.py:97
#: withdraw/models.py:141
msgid "H1Z1"
msgstr "H1Z1"

#: b2ctrade/models.py:45 b2ctrade/models.py:116 charge/models.py:16
#: package/models.py:162 package/models.py:278 withdraw/models.py:20
msgid "Initialed"
msgstr "已初始化"

#: b2ctrade/models.py:46 b2ctrade/models.py:59 b2ctrade/models.py:117
#: package/models.py:279
msgid "Accepted"
msgstr "已接受"

#: b2ctrade/models.py:47 b2ctrade/models.py:60 b2ctrade/models.py:118
#: box/models.py:146 crash/models.py:18 package/models.py:280 roll/models.py:18
msgid "Cancelled"
msgstr "已取消"

#: b2ctrade/models.py:48
msgid "Trading"
msgstr "取回中"

#: b2ctrade/models.py:49
msgid "Cancelling"
msgstr "取消中"

#: b2ctrade/models.py:50
msgid "PriceCancelled"
msgstr "溢价取消"

#: b2ctrade/models.py:51
msgid "OutOfStock"
msgstr "缺货"

#: b2ctrade/models.py:52
msgid "ZBTCancelled"
msgstr "卖家已取消"

#: b2ctrade/models.py:55
msgid "WaitForPay"
msgstr "等待购买"

#: b2ctrade/models.py:56
msgid "WaitForSend"
msgstr "等待发送"

#: b2ctrade/models.py:57
msgid "WaitForTrade"
msgstr "等待交易"

#: b2ctrade/models.py:58
msgid "Receive"
msgstr "接受"

#: b2ctrade/models.py:63
msgid "Market"
msgstr "市场"

#: b2ctrade/models.py:64
msgid "Package"
msgstr "饰品背包"

#: b2ctrade/models.py:67 b2ctrade/models.py:131 b2ctrade/models.py:132
#: package/models.py:286 package/models.py:325 withdraw/models.py:38
msgid "trade url"
msgstr "交易链接"

#: b2ctrade/models.py:68 b2ctrade/models.py:133 b2ctrade/models.py:212
#: b2ctrade/models.py:255 box/models.py:274 box/models.py:298 box/models.py:388
#: box/models.py:403 box/models.py:419 box/models.py:434 charge/models.py:30
#: charge/models.py:65 charge/models.py:104 charge/models.py:154
#: charge/models.py:194 charge/models.py:276 charge/models.py:291
#: charge/models.py:305 crash/models.py:49 crash/models.py:66
#: crash/models.py:90 crash/models.py:298 crash/models.py:323
#: package/models.py:189 package/models.py:288 package/models.py:393
#: package/models.py:422 roll/models.py:123 roll/models.py:147
#: tradeup/models.py:70 tradeup/models.py:111 tradeup/models.py:135
#: tradeup/models.py:343 tradeup/models.py:368 withdraw/models.py:40
#: withdraw/models.py:101 withdraw/models.py:144
msgid "amount"
msgstr "金额"

#: b2ctrade/models.py:69
msgid "buy price"
msgstr "购买价格"

#: b2ctrade/models.py:70 b2ctrade/models.py:134 package/models.py:289
msgid "trade status"
msgstr "交易状态"

#: b2ctrade/models.py:71
msgid "zbt trade status"
msgstr "扎比特交易状态"

#: b2ctrade/models.py:72 b2ctrade/models.py:135
msgid "accept time"
msgstr "接受时间"

#: b2ctrade/models.py:73 b2ctrade/models.py:136 b2ctrade/models.py:214
#: b2ctrade/models.py:257 steambase/models.py:49 tradeup/models.py:33
#: tradeup/models.py:71 withdraw/models.py:41 withdraw/models.py:103
#: withdraw/models.py:146
msgid "appid"
msgstr "appid"

#: b2ctrade/models.py:75 b2ctrade/models.py:139 package/models.py:183
#: package/models.py:310 package/models.py:344
msgid "assetid"
msgstr "assetid"

#: b2ctrade/models.py:76
msgid "zbt order id"
msgstr "扎比特订单号"

#: b2ctrade/models.py:79 package/models.py:290
msgid "trade No."
msgstr "交易编号"

#: b2ctrade/models.py:80
msgid "out trade no"
msgstr "交易编号"

#: b2ctrade/models.py:81
msgid "trade source"
msgstr "来源"

#: b2ctrade/models.py:82 package/models.py:242 package/models.py:259
#: package/models.py:309 tradeup/models.py:50 withdraw/models.py:58
msgid "package item"
msgstr "饰品背包"

#: b2ctrade/models.py:86
msgid "is extract"
msgstr "是否提取"

#: b2ctrade/models.py:89
msgid "error msg"
msgstr "错误信息"

#: b2ctrade/models.py:93 b2ctrade/models.py:94
msgid "ZBT Trade Record"
msgstr "交易记录"

#: b2ctrade/models.py:101
msgid "seller nickname"
msgstr "昵称"

#: b2ctrade/models.py:104 b2ctrade/models.py:105
msgid "ZBTBlackList"
msgstr "黑名单"

#: b2ctrade/models.py:119
msgid "WaitForBuy"
msgstr "等待购买"

#: b2ctrade/models.py:120
msgid "RequestBuy"
msgstr "请求购买"

#: b2ctrade/models.py:121
msgid "WaitUnlock"
msgstr "等待解锁"

#: b2ctrade/models.py:122
msgid "TradeReady"
msgstr "准备交易"

#: b2ctrade/models.py:123
msgid "BuyerCancelled"
msgstr "买家取消"

#: b2ctrade/models.py:126
msgid "Purchase"
msgstr "购买"

#: b2ctrade/models.py:127
msgid "Stocks"
msgstr "库存"

#: b2ctrade/models.py:129 package/models.py:329 package/models.py:359
msgid "account"
msgstr "账户"

#: b2ctrade/models.py:130
msgid "buyer"
msgstr "买家"

#: b2ctrade/models.py:138
msgid "expire time"
msgstr "过期时间"

#: b2ctrade/models.py:140 box/models.py:116 box/models.py:217 roll/models.py:99
msgid "item type"
msgstr "饰品种类"

#: b2ctrade/models.py:144 b2ctrade/models.py:145
msgid "B2COfficialTrade Record"
msgstr "交易记录"

#: b2ctrade/models.py:163 package/models.py:205
msgid "Invalid items"
msgstr "无效饰品"

#: b2ctrade/models.py:178 b2ctrade/models.py:179
#, fuzzy
#| msgid "Item Whitelist"
msgid "B2C Item list"
msgstr "饰品白名单"

#: b2ctrade/models.py:189 blindbox/models.py:129 box/models.py:261
#: box/models.py:471 chat/models.py:64 chat/models.py:81 package/models.py:323
#: package/models.py:360 roll/models.py:179 sitecfg/models.py:26
#: sitecfg/models.py:45 sitecfg/models.py:64 sitecfg/models.py:97
#: tradeup/models.py:402
msgid "remark"
msgstr "备注"

#: b2ctrade/models.py:192
msgid "customid"
msgstr "自定义ID"

#: b2ctrade/models.py:196 b2ctrade/models.py:197
msgid "B2COfficial Account"
msgstr "官方账户"

#: b2ctrade/models.py:213 b2ctrade/models.py:256 withdraw/models.py:102
#: withdraw/models.py:145
msgid "test amount"
msgstr "测试金额"

#: b2ctrade/models.py:218 b2ctrade/models.py:219
msgid "B2CTrade Statistics Day"
msgstr "日交易统计"

#: b2ctrade/models.py:261 b2ctrade/models.py:262
msgid "B2CTrade Statistics Month"
msgstr "月交易统计"

#: b2ctrade/models.py:294 box/models.py:316 charge/models.py:147
#: crash/models.py:114 crash/models.py:292 package/models.py:439
#: roll/models.py:164 roll/models.py:328 tradeup/models.py:159
#: tradeup/models.py:337 withdraw/models.py:185
msgid "Month"
msgstr "月"

#: b2ctrade/service/admin_actions.py:48
msgid "b2c trade accept complete"
msgstr "订单修改完成"

#: b2ctrade/service/admin_actions.py:51
msgid "b2c trade accept"
msgstr "订单修改完成"

#: b2ctrade/service/admin_actions.py:73
msgid "An error occurred during the process."
msgstr "处理过程中发生错误"

#: b2ctrade/service/admin_actions.py:75
msgid "b2c trade cancel complete"
msgstr "订单修改取消成功"

#: b2ctrade/service/admin_actions.py:78
msgid "b2c trade cancel"
msgstr "订单修改取消"

#: b2ctrade/service/admin_actions.py:105
msgid "扎比特购买失败请稍后重试."
msgstr "扎比特购买失败请稍后重试"

#: b2ctrade/service/admin_actions.py:116
msgid "zbt_buy complete"
msgstr "扎比特购买完成"

#: b2ctrade/service/admin_actions.py:123
msgid "扎比特接口错误：{}"
msgstr "扎比特接口错误：{}"

#: b2ctrade/service/admin_actions.py:129
msgid "zbt_buy"
msgstr "扎比特购买"

#: b2ctrade/service/admin_actions.py:161
msgid "Sync b2c market price complete"
msgstr "同步市场价格完成"

#: b2ctrade/service/admin_actions.py:164
msgid "Sync b2c market price"
msgstr "Steam市场价格（USD）-0"

#: b2ctrade/service/admin_actions.py:212 b2ctrade/service/admin_actions.py:252
#: package/service/admin_actions.py:75
msgid "Sync complete"
msgstr "同步完成"

#: b2ctrade/service/admin_actions.py:215 b2ctrade/service/admin_actions.py:255
#: package/service/admin_actions.py:78
msgid "Sync trade bot inventory"
msgstr "同步交易机器人库存"

#: b2ctrade/service/admin_actions.py:224
msgid "Sync failed not search b2c account"
msgstr "同步失败，未找到B2C账户"

#: b2ctrade/service/admin_actions.py:338
msgid "扎比特取回记录导出Excel"
msgstr "扎比特取回记录导出Excel"

#: b2ctrade/views.py:51 b2ctrade/views.py:78 b2ctrade/views.py:99
#: b2ctrade/views.py:119 b2ctrade/views.py:139 package/views.py:234
#: withdraw/views.py:31 withdraw/views.py:56
msgid "Withdraw is under maintenance, please wait for a while."
msgstr "取回正在维护中，请稍候。"

#: b2ctrade/views.py:53
#, fuzzy
#| msgid "Permissions"
msgid "No permission"
msgstr "许可"

#: b2ctrade/views.py:75 chat/views.py:31
msgid "系统维护中，请稍后再试，具体恢复时间请关注网站公告。"
msgstr "该网站正在维护中，请稍候。"

#: b2ctrade/views.py:272
#, fuzzy
#| msgid "系统维护中，请稍后再试，具体恢复时间请关注网站公告。"
msgid "取回接口维护，稍后再试，恢复时间请参考网站公告。"
msgstr "该网站正在维护中，请稍候。"

#: b2ctrade/views.py:274
msgid "取回权限被限制，请联系在线客服。"
msgstr "取回权限被限制，请联系在线客服"

#: blindbox/business.py:68 box/business.py:313 custombox/business.py:168
msgid "Case is blocked, please open other case."
msgstr "箱子维护中，请稍后再试。"

#: blindbox/business.py:72
msgid "该轮游戏已结束，请更换盒子"
msgstr "该轮游戏已结束，请更换盒子"

#: blindbox/business.py:74
msgid "位置错误，请重新选择"
msgstr "位置错误，请重新选择"

#: blindbox/business.py:80
msgid "该位置已开启"
msgstr "该位置已开启"

#: blindbox/business.py:88
msgid "Invalid count"
msgstr "无效数量"

#: blindbox/business.py:193 box/business.py:599
msgid "Invalid case key"
msgstr "无效钥匙"

#: blindbox/models.py:13
msgid "Top"
msgstr "顶级"

#: blindbox/models.py:14 blindbox/models.py:88 box/models.py:208
msgid "Free"
msgstr "免费"

#: blindbox/models.py:15
msgid "Festival"
msgstr "节日"

#: blindbox/models.py:16
msgid "FreeGive"
msgstr "白给 "

#: blindbox/models.py:18 blindbox/models.py:32 box/models.py:44
#: charge/models.py:323 custombox/models.py:14 luckybox/models.py:77
#: roll/models.py:30 sitecfg/models.py:10 sitecfg/models.py:145
#: steambase/models.py:43
msgid "name"
msgstr "名字"

#: blindbox/models.py:19 luckybox/models.py:73 sitecfg/models.py:21
#: sitecfg/models.py:167
msgid "category"
msgstr "类别"

#: blindbox/models.py:20 blindbox/models.py:39 box/models.py:19
#: box/models.py:33 box/models.py:57 envelope/models.py:31
msgid "order"
msgstr "排序"

#: blindbox/models.py:24 blindbox/models.py:25 box/models.py:25
#: box/models.py:26
msgid "Case Type"
msgstr "箱子种类"

#: blindbox/models.py:33 box/models.py:47 custombox/models.py:15
msgid "key"
msgstr "钥匙"

#: blindbox/models.py:35 box/models.py:49
msgid "discount(%)"
msgstr "折扣（%）"

#: blindbox/models.py:36 box/models.py:52 custombox/models.py:17
#: custombox/models.py:48
msgid "cover image"
msgstr "封面图案"

#: blindbox/models.py:37 box/models.py:53 custombox/models.py:18
#: custombox/models.py:59
msgid "item image"
msgstr "饰品图案"

#: blindbox/models.py:38
#, fuzzy
#| msgid "Invalid room type"
msgid "blindbox type"
msgstr "无效房间种类"

#: blindbox/models.py:40 charge/models.py:327
msgid "unlock"
msgstr "未锁定"

#: blindbox/models.py:42
#, fuzzy
#| msgid "timestamp"
msgid "limit timestamp"
msgstr "时间戳"

#: blindbox/models.py:43
#, fuzzy
#| msgid "end time"
msgid "limited times"
msgstr "结束时间"

#: blindbox/models.py:47 blindbox/models.py:48
msgid "BlindBox"
msgstr "盲盒"

#: blindbox/models.py:56 blindbox/models.py:92 blindbox/models.py:113
#: blindbox/models.py:128 box/models.py:109 box/models.py:171 box/models.py:242
#: box/models.py:260 box/models.py:331 box/models.py:346 box/models.py:373
#: box/models.py:406 box/models.py:457 custombox/models.py:31
#: custombox/models.py:84 promotion/models.py:78
msgid "case"
msgstr "箱子"

#: blindbox/models.py:57 custombox/models.py:32
msgid "show chance"
msgstr "显示概率"

#: blindbox/models.py:58 box/models.py:111 custombox/models.py:33
msgid "drop chance a"
msgstr "掉落概率a"

#: blindbox/models.py:59 box/models.py:112 custombox/models.py:34
msgid "drop chance b"
msgstr "掉落概率b"

#: blindbox/models.py:60 box/models.py:113 custombox/models.py:35
msgid "drop chance c"
msgstr "掉落概率c"

#: blindbox/models.py:61 box/models.py:114 custombox/models.py:36
msgid "drop chance d"
msgstr "掉落概率d"

#: blindbox/models.py:62 box/models.py:115 custombox/models.py:37
msgid "drop chance e"
msgstr "掉落概率e"

#: blindbox/models.py:63 box/models.py:117
msgid "diamond"
msgstr "钻石"

#: blindbox/models.py:64
msgid "custom enable"
msgstr "启用自定义价格"

#: blindbox/models.py:65
msgid "custom price"
msgstr "自定义价格"

#: blindbox/models.py:68 blindbox/models.py:69
msgid "BlindBoxDrop"
msgstr "盲盒掉落"

#: blindbox/models.py:74
#, fuzzy
#| msgid "game"
msgid "game id"
msgstr "游戏"

#: blindbox/models.py:75
msgid "random seed"
msgstr "随机种子"

#: blindbox/models.py:78 blindbox/models.py:79
msgid "BlindBoxGame"
msgstr "盲盒游戏"

#: blindbox/models.py:94 box/models.py:158 box/models.py:246
#: charge/models.py:329 luckybox/models.py:15 roll/models.py:29
#: sitecfg/models.py:44 sitecfg/models.py:65 sitecfg/models.py:119
#: steambase/models.py:58
msgid "type"
msgstr "种类"

#: blindbox/models.py:95
msgid "index"
msgstr "索引"

#: blindbox/models.py:96 crash/models.py:48 tradeup/models.py:49
#: tradeup/models.py:69 tradeup/models.py:82
msgid "game"
msgstr "游戏"

#: blindbox/models.py:99 blindbox/models.py:100
msgid "Blind Box Record"
msgstr "盲盒记录"

#: blindbox/models.py:110 box/models.py:328 box/models.py:343
#: charge/models.py:195 charge/models.py:207 charge/models.py:260
#: package/models.py:220 promotion/models.py:59
msgid "level"
msgstr "等级"

#: blindbox/models.py:111 box/models.py:329
msgid "min point"
msgstr "最小积分"

#: blindbox/models.py:112 box/models.py:330
msgid "max point"
msgstr "最大积分"

#: blindbox/models.py:117 blindbox/models.py:118 box/models.py:335
#: box/models.py:336
msgid "Free Case Config"
msgstr "免费箱子配置"

#: blindbox/models.py:126 box/models.py:258
msgid "open idle min(seconds)"
msgstr "最小打开间隔（秒）"

#: blindbox/models.py:127 box/models.py:259
msgid "open idle max(seconds)"
msgstr "最大打开间隔（秒）"

#: blindbox/models.py:133 blindbox/models.py:134 box/models.py:265
#: box/models.py:266
msgid "Case Bot Config"
msgstr "箱子机器人配置"

#: blindbox/views.py:26 box/business.py:264 crash/views.py:42
#: tradeup/views.py:58 tradeup/views.py:80 tradeup/views.py:102
#: tradeup/views.py:150 tradeup/views.py:248 tradeup/views.py:271
#: tradeup/views.py:293
msgid "Current game is under maintenance, please wait for a while."
msgstr "当前游戏正在维护中，请稍候。"

#: blindbox/views.py:32 blindbox/views.py:50 blindbox/views.py:71
#: blindbox/views.py:91 blindbox/views.py:112 blindbox/views.py:132
#: blindbox/views.py:156 blindbox/views.py:176 box/views.py:45
#: box/views.py:1220 box/views.py:1241 box/views.py:1264 charge/models.py:18
#: crash/views.py:130 crash/views.py:155 crash/views.py:180 crash/views.py:198
#: tradeup/views.py:65 tradeup/views.py:87 tradeup/views.py:110
#: tradeup/views.py:157 tradeup/views.py:183 tradeup/views.py:208
#: tradeup/views.py:233 tradeup/views.py:256 tradeup/views.py:278
#: tradeup/views.py:301
msgid "Succeed"
msgstr "成功"

#: box/admin.py:83
msgid "期望价格"
msgstr "期望价格"

#: box/admin.py:94
msgid "price_expectation_b"
msgstr "B概率期望价格"

#: box/admin.py:105
msgid "price_expectation_c"
msgstr "C概率期望价格"

#: box/admin.py:116
msgid "price_expectation_d"
msgstr "D概率期望价格"

#: box/admin.py:127
msgid "price_expectation_e"
msgstr "E概率期望价格"

#: box/apps.py:7
msgid "Box"
msgstr "箱子"

#: box/business.py:149
msgid "Invalid level"
msgstr "无效等级"

#: box/business.py:155
msgid "Already opened case today"
msgstr "今天已经开箱"

#: box/business.py:158
msgid "Invalid case"
msgstr "无效箱子"

#: box/business.py:160
msgid "Invalid level, please open correct level case."
msgstr "无效等级，请打开正确等级的箱子。"

#: box/business.py:271
msgid "This weapon case is under maintenance. Please try again later."
msgstr "该武器箱正在维护中，请稍后再试。"

#: box/business.py:275
msgid "箱子正在维护中，请稍后再试2"
msgstr "箱子正在维护中，请稍后再试"

#: box/business.py:279 charge/views.py:358
msgid "Please set a real and valid Steam link."
msgstr "请设置一个有效的Steam链接。"

#: box/business.py:289 box/business_room.py:209 box/business_room.py:295
msgid "余额不足"
msgstr "余额不足"

#: box/business.py:294
msgid "箱子没有配置掉落物品"
msgstr "箱子没有配置掉落物品"

#: box/business.py:408
msgid "开箱过程中发生错误，请重试"
msgstr "开箱过程中发生错误，请重试"

#: box/business_room.py:167 box/business_room.py:273
msgid "对战功能需要至少一件饰品有效提取才能自动开通，请先提取后再试。"
msgstr "对战功能需要至少一件饰品有效提取才能自动开通，请先提取后再试"

#: box/business_room.py:177 box/business_room.py:284 box/business_room.py:374
#: roll/business.py:87
msgid "Over max room count"
msgstr "您还有未结束的对战，每人限制同时开启6个对战"

#: box/business_room.py:183
msgid "Invalid cases count"
msgstr "箱子数量每次限制4个"

#: box/business_room.py:188
msgid "Invalid joiners count"
msgstr "参加者每次最多4人"

#: box/business_room.py:199
#, fuzzy
#| msgid "Case is blocked, please open other case."
msgid "Case is block, please choice other case."
msgstr "箱子维护中，请稍后再试。"

#: box/business_room.py:204
msgid "Invalid cases"
msgstr "箱子正在维护"

#: box/business_room.py:211
msgid "对战具有一定的残酷性，禁止超过账户余额的一半金额创建房间。"
msgstr "对战具有一定的残酷性，禁止超过账户余额的一半金额创建房间"

#: box/business_room.py:243
msgid "创建对战"
msgstr "创建对战"

#: box/business_room.py:297
msgid "对战具有一定的残酷性，禁止超过账户余额的一半金额参加对战。"
msgstr "对战具有一定的残酷性，禁止超过账户余额的一半金额参加对战"

#: box/business_room.py:309 box/business_room.py:378 box/business_room.py:424
#: box/business_room.py:459 roll/business.py:167 roll/business.py:195
#: roll/business.py:240 roll/business.py:275 roll/business.py:312
#: roll/business.py:1205 roll/business.py:1334 roll/models.py:60
msgid "Invalid room"
msgstr "无效房间"

#: box/business_room.py:313 box/business_room.py:381 roll/business.py:249
msgid "Already joined"
msgstr "已经加入"

#: box/business_room.py:321 box/business_room.py:386
msgid "对战"
msgstr "对战"

#: box/business_room.py:359
msgid "Invalid room type"
msgstr "无效房间种类"

#: box/business_room.py:384
msgid "Invalid team"
msgstr "无效团队"

#: box/business_room.py:427
#, fuzzy
#| msgid "Invalid  bet"
msgid "Invalid bet"
msgstr "无效对战"

#: box/business_room.py:431
msgid "房主不能退出房间，请使用解散功能"
msgstr "房主不能退出房间，请使用解散功能"

#: box/business_room.py:434
msgid "Case room bet quit"
msgstr "对战退出"

#: box/business_room.py:463
msgid "只有房主可以解散房间"
msgstr "只有房主可以解散房间"

#: box/business_room.py:473
msgid "Case room dismissed by owner"
msgstr "房间已被房主解散"

#: box/business_room.py:800 box/business_room.py:809
msgid "Case chance invalid."
msgstr "无效开箱概率。"

#: box/business_room.py:1254 box/business_room.py:1349
msgid "Case drops invalid."
msgstr "无效开箱掉落。"

#: box/business_room.py:1405
msgid "Case room cancel"
msgstr "开箱房间取消"

#: box/models.py:16
msgid "Type Id"
msgstr "类型ID"

#: box/models.py:17
msgid "Type Name"
msgstr "类型名称"

#: box/models.py:30 package/models.py:51
msgid "Cate Id"
msgstr "分类ID"

#: box/models.py:31 package/models.py:52
#, fuzzy
#| msgid "create time"
msgid "Cate Name"
msgstr "创建时间"

#: box/models.py:34 luckybox/models.py:76
#, fuzzy
#| msgid "icon url"
msgid "icon"
msgstr "图标链接"

#: box/models.py:40 box/models.py:41
#, fuzzy
#| msgid "WearCategory0"
msgid "Case Category"
msgstr "崭新出厂"

#: box/models.py:50
msgid "present"
msgstr "礼物"

#: box/models.py:54
msgid "back image"
msgstr "背景图"

#: box/models.py:55
msgid "case type"
msgstr "箱子种类"

#: box/models.py:56
#, fuzzy
#| msgid "category"
msgid "case category"
msgstr "类别"

#: box/models.py:59 envelope/models.py:34
msgid "is show"
msgstr "是否显示"

#: box/models.py:62
msgid "enable room"
msgstr "可用房间"

#: box/models.py:64
#, fuzzy
#| msgid "superuser enable case"
msgid "superuser enable"
msgstr "管理员可开"

#: box/models.py:67 sitecfg/models.py:161
msgid "tag"
msgstr "标签"

#: box/models.py:68
msgid "标签颜色"
msgstr "标签颜色"

#: box/models.py:69
msgid "open count"
msgstr "打开数量"

#: box/models.py:70
msgid "case cost"
msgstr "箱子成本价"

#: box/models.py:71
msgid "up sill"
msgstr "上限阈值"

#: box/models.py:72
msgid "down sill"
msgstr "下限阈值"

#: box/models.py:73
msgid "dyntic"
msgstr "浮动值"

#: box/models.py:74
msgid "algorithm_enable"
msgstr "使用新算法"

#: box/models.py:75 lottery/models.py:69 package/models.py:29
#: sitecfg/models.py:47 sitecfg/models.py:84 sitecfg/models.py:99
#: sitecfg/models.py:162
msgid "content"
msgstr "描述内容"

#: box/models.py:77
msgid "SEO标题"
msgstr "SEO标题"

#: box/models.py:78
msgid "SEO关键词"
msgstr "SEO关键词"

#: box/models.py:79
msgid "SEO描述"
msgstr "SEO描述"

#: box/models.py:86 box/models.py:87 box/models.py:235 package/models.py:172
msgid "Case"
msgstr "箱子"

#: box/models.py:99
msgid "Base by count and chance"
msgstr "基于次数及概率"

#: box/models.py:100
msgid "Base by count"
msgstr "基于次数"

#: box/models.py:101
msgid "Base by chance"
msgstr "基于概率"

#: box/models.py:104 box/models.py:207 roll/models.py:92
msgid "Assets"
msgstr "资产"

#: box/models.py:110
msgid "开箱概率"
msgstr "开箱概率"

#: box/models.py:122
msgid "up sill drop"
msgstr "上限掉落"

#: box/models.py:123
msgid "down sill drop"
msgstr "下限掉落"

#: box/models.py:125 box/models.py:126
msgid "Drop Item"
msgstr "掉落饰品"

#: box/models.py:140 crash/models.py:12 roll/models.py:12
msgid "Initial"
msgstr "初始"

#: box/models.py:141 crash/models.py:13 grab/models.py:26 roll/models.py:13
msgid "Joinable"
msgstr "可加入"

#: box/models.py:142 crash/models.py:14 roll/models.py:14
msgid "Joining"
msgstr "正在加入"

#: box/models.py:143 crash/models.py:15 lottery/models.py:41 roll/models.py:15
msgid "Full"
msgstr "已满"

#: box/models.py:144 crash/models.py:16 roll/models.py:16
msgid "Running"
msgstr "正在跑"

#: box/models.py:145 crash/models.py:17 grab/models.py:29 lottery/models.py:42
#: roll/models.py:17
msgid "End"
msgstr "结束"

#: box/models.py:149 box/models.py:236
msgid "Battle"
msgstr "对战"

#: box/models.py:150 box/models.py:237
msgid "Equality"
msgstr "平等"

#: box/models.py:151 box/models.py:238
msgid "TeamBattle"
msgstr "团队对战"

#: box/models.py:153
msgid "short id"
msgstr "短id"

#: box/models.py:155 lottery/models.py:21 roll/models.py:35
msgid "max joiner"
msgstr "最大参加者"

#: box/models.py:157 charge/models.py:32 crash/models.py:30
#: package/models.py:185 package/models.py:187 roll/models.py:44
msgid "status"
msgstr "状态"

#: box/models.py:159
msgid "private"
msgstr "私人的"

#: box/models.py:162 box/models.py:163
msgid "Case Room"
msgstr "开箱房间"

#: box/models.py:170 box/models.py:188 box/models.py:210 box/models.py:420
#: roll/models.py:76 roll/models.py:95 roll/models.py:192
msgid "room"
msgstr "房间"

#: box/models.py:172 box/models.py:374
msgid "opened"
msgstr "已打开"

#: box/models.py:175 box/models.py:176
msgid "Case Room Round"
msgstr "开箱房间轮数"

#: box/models.py:191
msgid "open amount"
msgstr "打开金额"

#: box/models.py:192 crash/models.py:26 crash/models.py:52 roll/models.py:77
#: tradeup/models.py:31
msgid "win amount"
msgstr "获胜金额"

#: box/models.py:193 roll/models.py:78
msgid "win items count"
msgstr "获胜饰品计数"

#: box/models.py:194 roll/models.py:79
msgid "victory"
msgstr "胜利"

#: box/models.py:195
msgid "bet team"
msgstr "对战团队"

#: box/models.py:198 box/models.py:199
msgid "Case Room Bet"
msgstr "对战"

#: box/models.py:211
msgid "bet"
msgstr "对战"

#: box/models.py:213
msgid "winner"
msgstr "获胜者"

#: box/models.py:219
msgid "part price of item"
msgstr "饰品的部分价格"

#: box/models.py:220
msgid "item need to split"
msgstr "需要拆分的饰品"

#: box/models.py:223 box/models.py:224
msgid "Case Room Item"
msgstr "开箱房间饰品"

#: box/models.py:249 box/models.py:250 custombox/models.py:90
#: custombox/models.py:91
msgid "Case Record"
msgstr "箱子记录"

#: box/models.py:292 box/models.py:293
msgid "Case Statistics Day"
msgstr "日箱子统计"

#: box/models.py:323 box/models.py:324
msgid "Case Statistics Month"
msgstr "月箱子统计"

#: box/models.py:344
msgid "min charge total amount"
msgstr "最低充值总额"

#: box/models.py:345
msgid "max charge total amount"
msgstr "最高充值总额"

#: box/models.py:350 box/models.py:351
msgid "Festival Case Config"
msgstr "节日箱子配置"

#: box/models.py:358
msgid "month"
msgstr "月"

#: box/models.py:359
msgid "day"
msgstr "日"

#: box/models.py:364 box/models.py:365
msgid "Festival Case Date"
msgstr "节日箱子日期"

#: box/models.py:375
msgid "expired time"
msgstr "过期时间"

#: box/models.py:378 box/models.py:379
msgid "Festival Case Record"
msgstr "节日箱子记录"

#: box/models.py:393 box/models.py:394
msgid "Drop Day Rank"
msgstr "日掉落排名"

#: box/models.py:409 box/models.py:410
msgid "Income Day Rank"
msgstr "日收入排名"

#: box/models.py:423 box/models.py:424
msgid "Room Day Rank"
msgstr "日房间排名"

#: box/models.py:432 charge/models.py:152 crash/models.py:321
#: tradeup/models.py:366
msgid "year"
msgstr "年"

#: box/models.py:433 charge/models.py:153 crash/models.py:322
#: tradeup/models.py:367
msgid "week"
msgstr "周"

#: box/models.py:437 box/models.py:438
msgid "Lose Week Rank"
msgstr "周失败排名"

#: box/models.py:458
msgid "case_key"
msgstr "钥匙"

#: box/models.py:461 box/models.py:462
msgid "CaseKeyConfig"
msgstr "钥匙专属箱子配置"

#: box/models.py:474 box/models.py:475
msgid "GiveawayItems"
msgstr "赠品物品"

#: box/service/admin_actions.py:48
msgid "导出EXCEL"
msgstr "导出EXCEL"

#: box/service/admin_actions.py:100
msgid "箱子掉落导出EXCEL"
msgstr "箱子掉落导出EXCEL"

#: box/views.py:963 package/views.py:72 package/views.py:649
msgid "成功"
msgstr "成功"

#: box/views.py:965 package/views.py:651
msgid "搜索失败"
msgstr "搜索失败"

#: box/views.py:969 package/views.py:78 package/views.py:655
msgid "服务器异常"
msgstr "服务器异常"

#: box/views.py:1138
#, fuzzy
#| msgid "room uid"
msgid "room_uid required"
msgstr "房间"

#: box/views.py:1146
#, fuzzy
#| msgid "Good Not Found"
msgid "Room not found"
msgstr "商品未找到"

#: box/views.py:1152
msgid "Access denied"
msgstr "访问被拒绝"

#: box/views.py:1224 box/views.py:1245 box/views.py:1267
#, fuzzy
#| msgid "zbt error"
msgid "Internal error"
msgstr "扎比特错误"

#: charge/apps.py:7
msgid "charge"
msgstr "充值"

#: charge/business.py:83
msgid "Invalid charge amount"
msgstr "无效充值金额"

#: charge/business.py:85
msgid "充值金额必须为整数"
msgstr "充值金额必须为整数"

#: charge/business.py:87 charge/business.py:158 custombox/business.py:41
#: custombox/business.py:142 grab/business.py:56 grab/business.py:132
#: grab/business.py:154 grab/business.py:178 luckybox/business.py:235
#: market/business.py:98
msgid "Please Login First"
msgstr "请先登录"

#: charge/business.py:89
#, fuzzy
#| msgid "Invalid shop pay type"
msgid "Invalid pay type"
msgstr "无效商城支付种类"

#: charge/business.py:154 charge/business.py:160 charge/business.py:208
#: charge/views.py:142 luckybox/business.py:97 luckybox/business.py:160
#: luckybox/business.py:162 withdraw/business.py:287 withdraw/business.py:317
msgid "Invalid Params"
msgstr "无效参数"

#: charge/business.py:156
msgid "count must be int"
msgstr "数量必须为整数"

#: charge/business.py:163
msgid "Good Not Found"
msgstr "商品未找到"

#: charge/business.py:172
msgid "Charge System Error, Please Try Again Later"
msgstr "充值系统错误，请稍后再试"

#: charge/business.py:840 charge/business.py:885 charge/business.py:930
#: charge/business.py:975 charge/business.py:1019 charge/business.py:1741
#: charge/business.py:1887 charge/business.py:1966
msgid "充值通道维护"
msgstr "充值通道维护"

#: charge/business.py:852 charge/business.py:896 charge/business.py:941
#: charge/business.py:986 charge/business.py:1030 charge/business.py:1752
#: charge/business.py:1977
msgid "防沉迷系统提示：您今日充值总额已超出每日充值限制，请明日再试。"
msgstr "防沉迷系统提示：您今日充值总额已超出每日充值限制，请明日再试"

#: charge/business.py:855 charge/business.py:899 charge/business.py:944
#: charge/business.py:989 charge/business.py:1033 charge/business.py:1175
#: charge/business.py:1755 charge/business.py:1980
#, fuzzy
#| msgid "Invalid items"
msgid "Invalid Coins"
msgstr "无效饰品"

#: charge/business.py:871 charge/business.py:915 charge/business.py:960
#: charge/business.py:1005 charge/business.py:1049 charge/business.py:1771
#: charge/business.py:1926 charge/business.py:1996
msgid "支付请求失败"
msgstr "支付请求失败"

#: charge/business.py:1189
#, fuzzy
#| msgid "Charge Statistics Day"
msgid "Charge Api is Busy"
msgstr "日充值统计"

#: charge/business.py:1235 charge/business.py:1271 charge/business.py:1308
#: charge/business.py:1370 charge/business.py:1423 charge/business.py:1580
msgid "金额错误"
msgstr "金额错误"

#: charge/business.py:1240 charge/business.py:1277 charge/business.py:1349
#: charge/business.py:1402 charge/business.py:1447 charge/business.py:1601
#, fuzzy
#| msgid "Invalid params"
msgid "invalid params"
msgstr "无效参数"

#: charge/business.py:1248 charge/business.py:1285 charge/business.py:1354
#: charge/business.py:1407 charge/business.py:1452 charge/business.py:1609
msgid "charge system api error"
msgstr "充值系统API错误"

#: charge/business.py:1548
msgid "Please login first"
msgstr "请先登录"

#: charge/business.py:1552
msgid "CDKey is invalid"
msgstr "该CDKey无效。"

#: charge/business.py:1558
msgid "CDKey top-up"
msgstr "CDKey充值"

#: charge/business.py:1716
#, fuzzy
#| msgid "Page Not Found"
msgid "user not found"
msgstr "页面未找到"

#: charge/business.py:1732
msgid "pay method not found"
msgstr "支付方式未找到"

#: charge/business.py:1896
msgid "防沉迷系统提示：您今日充值总额已超出每日充值限制，请明日再试"
msgstr "防沉迷系统提示：您今日充值总额已超出每日充值限制，请明日再试"

#: charge/business.py:1900 charge/views.py:374
msgid "无效的充值金额"
msgstr "无效的充值金额"

#: charge/models.py:17
msgid "Actived"
msgstr "活跃的"

#: charge/models.py:20
msgid "Canceled"
msgstr "已取消"

#: charge/models.py:23 charge/models.py:318
msgid "WeChat"
msgstr "微信"

#: charge/models.py:24 charge/models.py:319
msgid "Ali"
msgstr "支付宝"

#: charge/models.py:25 charge/models.py:320
#, fuzzy
#| msgid "Union"
msgid "Union Pay"
msgstr "银联"

#: charge/models.py:26 charge/models.py:321
msgid "Other Pay"
msgstr "其他支付"

#: charge/models.py:29
msgid "out trade No"
msgstr "交易编号"

#: charge/models.py:31 grab/models.py:33 package/models.py:245
msgid "currency"
msgstr "货币"

#: charge/models.py:33
msgid "pay type"
msgstr "支付种类"

#: charge/models.py:34
msgid "pay amount"
msgstr "支付金额"

#: charge/models.py:35
msgid "pay time"
msgstr "支付时间"

#: charge/models.py:36
msgid "nonce"
msgstr "随机数"

#: charge/models.py:37
msgid "timestamp"
msgstr "时间戳"

#: charge/models.py:38
msgid "client ip"
msgstr "客户端IP"

#: charge/models.py:56 charge/models.py:57
msgid "Charge Record"
msgstr "充值记录"

#: charge/models.py:98 charge/models.py:99
msgid "Charge Statistics Day"
msgstr "日充值统计"

#: charge/models.py:108 charge/models.py:109
msgid "Charge Statistics Month"
msgstr "月充值统计"

#: charge/models.py:157 charge/models.py:158
msgid "Charge Week Rank"
msgstr "周充值排名"

#: charge/models.py:199 charge/models.py:200
msgid "Charge Level"
msgstr "充值等级"

#: charge/models.py:208 charge/models.py:261 promotion/models.py:64
msgid "min amount"
msgstr "最小金额"

#: charge/models.py:209 charge/models.py:262 promotion/models.py:65
msgid "max amount"
msgstr "最大金额"

#: charge/models.py:213 charge/models.py:214
msgid "Charge Level Config"
msgstr "充值等级配置"

#: charge/models.py:221
msgid "Cxka good id"
msgstr "商品ID"

#: charge/models.py:222
msgid "Cxka good name"
msgstr "商品名称"

#: charge/models.py:227 charge/models.py:228
msgid "Cxka goods"
msgstr "充值卡"

#: charge/models.py:235
msgid "generate count"
msgstr "生成数量"

#: charge/models.py:238 charge/models.py:239
msgid "Cxka Generate"
msgstr "充值卡生成"

#: charge/models.py:246 grab/models.py:23 package/models.py:237
msgid "Coins"
msgstr "金币"

#: charge/models.py:247 charge/models.py:324
#, fuzzy
#| msgid "order"
msgid "sort order"
msgstr "排序"

#: charge/models.py:249 luckybox/models.py:74 sitecfg/models.py:124
#: sitecfg/models.py:165
msgid "image"
msgstr "图片"

#: charge/models.py:252 charge/models.py:253
msgid "ChargeAmountConfig"
msgstr "充值金额配置"

#: charge/models.py:263 envelope/models.py:48
msgid "handsel"
msgstr "金额"

#: charge/models.py:267 charge/models.py:268
msgid "充值赠送金额配置"
msgstr "充值赠送金额配置"

#: charge/models.py:280 charge/models.py:281
msgid "generate CDKey"
msgstr "CDKey生成"

#: charge/models.py:290 charge/models.py:304
msgid "CDKey"
msgstr "CDK"

#: charge/models.py:294 charge/models.py:295
msgid "CDKey record"
msgstr "CDKey记录"

#: charge/models.py:301
msgid "used"
msgstr "已使用"

#: charge/models.py:302
msgid "unused"
msgstr "未使用"

#: charge/models.py:306
msgid "Key state"
msgstr "CDKey状态"

#: charge/models.py:309 charge/models.py:310
msgid "CDKey info"
msgstr "CDKey信息"

#: charge/models.py:328
msgid "suggest"
msgstr "建议"

#: charge/models.py:330 sitecfg/models.py:164
#, fuzzy
#| msgid "tag color"
msgid "color"
msgstr "标签颜色"

#: charge/models.py:333
msgid "daily limit"
msgstr "每日限制"

#: charge/models.py:335
msgid "single limit"
msgstr "单次限制"

#: charge/models.py:342
msgid "rate"
msgstr "比率"

#: charge/models.py:349 charge/models.py:350
msgid "PayMethod"
msgstr "支付方式"

#: charge/service/admin_actions.py:34
msgid "startup_export_txt"
msgstr "生成充值卡并导出"

#: charge/service/admin_actions.py:96
msgid "充值记录导出为Excel"
msgstr "充值记录导出为Excel"

#: charge/service/admin_actions.py:129
msgid "充值等级导出为Excel"
msgstr "充值等级导出为Excel"

#: charge/service/admin_actions.py:160
msgid "充值日统计导出为Excel"
msgstr "充值日统计导出为Excel"

#: charge/service/admin_actions.py:187
msgid "用户等级重置"
msgstr "用户等级重置"

#: charge/service/admin_actions.py:200
msgid "Generate CDKey"
msgstr "CDKey生成"

#: charge/views.py:47 charge/views.py:72 charge/views.py:146
msgid "Charge is under maintenance, please wait for a while."
msgstr "充值正在维护中，请稍候。"

#: charge/views.py:248 charge/views.py:363 charge/views.py:501
#: charge/views.py:749 charge/views.py:792 charge/views.py:815
#, fuzzy
#| msgid "系统维护中，请稍后再试，具体恢复时间请关注网站公告。"
msgid "充值渠道维护，具体恢复时间请查看网站公告。"
msgstr "该网站正在维护中，请稍候。"

#: charge/views.py:360
msgid "请进入会员中心先通过规则考试后再充值"
msgstr "请进入会员中心先通过规则考试后再充值"

#: charge/views.py:410
msgid "不支持的支付方式"
msgstr "不支持的支付方式"

#: charge/views.py:414
msgid "接口维护中"
msgstr "接口维护中"

#: chat/apps.py:7
msgid "Chat"
msgstr "聊天"

#: chat/business.py:46
msgid "At least 50coins to chat"
msgstr "至少要50枚硬币用以聊天"

#: chat/business.py:48
msgid "You have been banned chat"
msgstr "您已被禁止聊天"

#: chat/business.py:129
msgid "Message not found or permission denied"
msgstr "消息未找到或权限被拒绝"

#: chat/business.py:137
msgid "Message marked as read"
msgstr "消息已标记为已读"

#: chat/models.py:16
msgid "Sender"
msgstr "发送者"

#: chat/models.py:20
msgid "Sender Name"
msgstr "发送者姓名"

#: chat/models.py:23
msgid "Recipient"
msgstr "接收者"

#: chat/models.py:27
msgid "Recipient Name"
msgstr "接收者姓名"

#: chat/models.py:28
msgid "Subject"
msgstr "主题"

#: chat/models.py:29
msgid "Body"
msgstr "内容"

#: chat/models.py:30
msgid "Sent At"
msgstr "发送时间"

#: chat/models.py:31
msgid "Read At"
msgstr "阅读时间"

#: chat/models.py:34 chat/models.py:63
msgid "Message"
msgstr "消息"

#: chat/models.py:35
#, fuzzy
#| msgid "Message"
msgid "Messages"
msgstr "消息"

#: chat/models.py:68 chat/models.py:69
msgid "Chat Bot Message"
msgstr "聊天机器人消息"

#: chat/models.py:77
msgid "chat idle min(seconds)"
msgstr "最小聊天间隔（秒）"

#: chat/models.py:78
msgid "chat idle max(seconds)"
msgstr "最大聊天间隔（秒）"

#: chat/models.py:79 sitecfg/models.py:205
msgid "message"
msgstr "消息"

#: chat/models.py:85 chat/models.py:86
msgid "Chat Bot Config"
msgstr "聊天机器人配置"

#: crash/apps.py:7
msgid "Crash"
msgstr "崩溃"

#: crash/business.py:144
msgid "Crash game win"
msgstr "Crash Game赢得游戏"

#: crash/business.py:200 tradeup/business.py:82 tradeup/business.py:186
msgid "Invalid bet amount"
msgstr "无效对战金额"

#: crash/business.py:204
msgid "Auto crashout should between 1.01~999.99"
msgstr "自动淘汰应该介于1.01~999.99"

#: crash/business.py:213
msgid "Invalid game"
msgstr "无效游戏"

#: crash/business.py:215
msgid "Game is running, please wait on"
msgstr "游戏正在运行，请稍候"

#: crash/business.py:219
msgid "Over bet max amount"
msgstr "最高对战额"

#: crash/business.py:239
msgid "Crash game bet"
msgstr "Crash Game下注"

#: crash/business.py:256
msgid "Game is not running"
msgstr "游戏未运行"

#: crash/business.py:258
msgid "Invalid out point"
msgstr "无效点数"

#: crash/business.py:265
msgid "Invalid operation"
msgstr "无效操作"

#: crash/models.py:20 tradeup/models.py:24
msgid "hash"
msgstr "哈希值"

#: crash/models.py:21 tradeup/models.py:25
msgid "secret"
msgstr "秘密"

#: crash/models.py:22 tradeup/models.py:26
msgid "percentage"
msgstr "比例"

#: crash/models.py:23
msgid "crash point"
msgstr "崩溃点"

#: crash/models.py:24 roll/models.py:42
msgid "total amount"
msgstr "总金额"

#: crash/models.py:25 roll/models.py:41
msgid "pump amount"
msgstr "抽水金额"

#: crash/models.py:27
msgid "joinable time"
msgstr "可参加的时间"

#: crash/models.py:28
msgid "run time"
msgstr "运行时间"

#: crash/models.py:29 envelope/models.py:24
msgid "end time"
msgstr "结束时间"

#: crash/models.py:34 crash/models.py:35
msgid "Crash Game"
msgstr "Crash Game"

#: crash/models.py:43 tradeup/models.py:19
msgid "Not End"
msgstr "未结束"

#: crash/models.py:44 tradeup/models.py:20
msgid "Win"
msgstr "胜利"

#: crash/models.py:45 tradeup/models.py:21
msgid "Lose"
msgstr "失败"

#: crash/models.py:50
msgid "out point"
msgstr "输出点数"

#: crash/models.py:53 tradeup/models.py:32
msgid "win result"
msgstr "获胜结果"

#: crash/models.py:57 crash/models.py:58
msgid "Crash Bet"
msgstr "Crash对战"

#: crash/models.py:70 crash/models.py:71
msgid "Crash Pump Day"
msgstr "日Crash Pump"

#: crash/models.py:94 crash/models.py:95
msgid "Crash Pump Month"
msgstr "月Crash Pump"

#: crash/models.py:119 crash/models.py:203 roll/models.py:204
#: roll/models.py:264 tradeup/models.py:164 tradeup/models.py:248
msgid "game amount"
msgstr "游戏金额"

#: crash/models.py:120 crash/models.py:204 roll/models.py:205
#: roll/models.py:265 tradeup/models.py:165 tradeup/models.py:249
msgid "game journal"
msgstr "游戏日记"

#: crash/models.py:121 crash/models.py:205 tradeup/models.py:166
#: tradeup/models.py:250
msgid "win amount journal"
msgstr "获胜金额日记"

#: crash/models.py:122 crash/models.py:206 tradeup/models.py:167
#: tradeup/models.py:251
msgid "lose amount journal"
msgstr "失败金额日记"

#: crash/models.py:123 crash/models.py:207 roll/models.py:206
#: roll/models.py:266 tradeup/models.py:168 tradeup/models.py:252
msgid "test game amount"
msgstr "测试游戏金额"

#: crash/models.py:124 crash/models.py:208 roll/models.py:207
#: roll/models.py:267 tradeup/models.py:169 tradeup/models.py:253
msgid "admin game amount"
msgstr "管理员游戏金额"

#: crash/models.py:128 crash/models.py:129
msgid "Crash Statistics Day"
msgstr "日崩溃统计"

#: crash/models.py:212 crash/models.py:213
msgid "Crash Statistics Month"
msgstr "月崩溃统计"

#: crash/models.py:301 crash/models.py:302
msgid "Crash Win Day Rank"
msgstr "日Crash获胜排名"

#: crash/models.py:326 crash/models.py:327
msgid "Crash Win Week Rank"
msgstr "周Crash获胜排名"

#: crash/models.py:346 tradeup/models.py:391
msgid "Weeks"
msgstr "周"

#: custombox/business.py:93
msgid "Quantity must be greater than 2"
msgstr "数量必须大于2"

#: custombox/business.py:153
msgid "No Permission to Open This Box"
msgstr "没有权限开启此箱子"

#: custombox/models.py:21 custombox/models.py:22
msgid "CustomBox"
msgstr "自制箱子"

#: custombox/models.py:40 custombox/models.py:41
msgid "CustomDropItem"
msgstr "掉落饰品"

#: custombox/models.py:51 custombox/models.py:52
msgid "CustomBoxCover"
msgstr "自制箱子背景图配置"

#: custombox/models.py:62 custombox/models.py:63
msgid "CustomBoxItem"
msgstr "自制开箱饰品图片配置"

#: custombox/models.py:71 luckybox/models.py:13 steambase/models.py:44
#: withdraw/models.py:79
msgid "market name"
msgstr "市场名称"

#: custombox/models.py:74 custombox/models.py:75
msgid "CustomBoxItemInfo"
msgstr "饰品信息"

#: custombox/views.py:91 custombox/views.py:128
#, fuzzy
#| msgid "系统维护中，请稍后再试，具体恢复时间请关注网站公告。"
msgid "The Custom Box is under maintenance, please wait for a while."
msgstr "该网站正在维护中，请稍候。"

#: envelope/apps.py:7
msgid "Envelope"
msgstr "红包"

#: envelope/business.py:43
msgid "口令不存在"
msgstr "口令不存在"

#: envelope/business.py:48
msgid "该红包未开始领取或已过期"
msgstr "该红包未开始领取或已过期"

#: envelope/business.py:56
msgid "不满足条件"
msgstr "不满足条件"

#: envelope/business.py:60
msgid "该口令红包已领取"
msgstr "该口令红包已领取"

#: envelope/business.py:68
msgid "该红包已抢光"
msgstr "该红包已抢光"

#: envelope/business.py:83
msgid "口令红包"
msgstr "口令红包"

#: envelope/models.py:10
msgid "准备中"
msgstr "准备中"

#: envelope/models.py:11
msgid "未开始"
msgstr "未开始"

#: envelope/models.py:12
msgid "已开始"
msgstr "已开始"

#: envelope/models.py:13
msgid "已结束"
msgstr "已结束"

#: envelope/models.py:15 lottery/models.py:67 sitecfg/models.py:46
#: sitecfg/models.py:66 sitecfg/models.py:83 sitecfg/models.py:98
#: sitecfg/models.py:118 sitecfg/models.py:157
msgid "title"
msgstr "标题"

#: envelope/models.py:16
msgid "rule start time"
msgstr "充值限制开始时间"

#: envelope/models.py:17
msgid "rule_end_time"
msgstr "充值限制结束时间"

#: envelope/models.py:18
msgid "rule coins"
msgstr "充值金额限制"

#: envelope/models.py:20
msgid "stock"
msgstr "库存"

#: envelope/models.py:21
msgid "有限库存"
msgstr "有限库存"

#: envelope/models.py:23
msgid "start time"
msgstr "开始时间"

#: envelope/models.py:25
msgid "handsel min"
msgstr "赠送金额最小值"

#: envelope/models.py:26
msgid "handsel max"
msgstr "赠送金额最大值"

#: envelope/models.py:28 envelope/models.py:47
msgid "envelop password"
msgstr "红包口令"

#: envelope/models.py:29 lottery/models.py:47
msgid "state"
msgstr "状态"

#: envelope/models.py:30 package/models.py:28 roll/models.py:31
#: sitecfg/models.py:123
msgid "description"
msgstr "备注"

#: envelope/models.py:37 envelope/models.py:38
msgid "EnvelopeRule"
msgstr "红包规则配置"

#: envelope/models.py:46
msgid "rule"
msgstr "规则"

#: envelope/models.py:51 envelope/models.py:52
msgid "EnvelopeRecord"
msgstr "红包记录"

#: envelope/service/admin_actions.py:37
msgid "红包记录导出Excel"
msgstr "红包记录导出Excel"

#: envelope/views.py:37
msgid "系统维护中，请稍等再试"
msgstr "系统维护中，请稍等再试"

#: grab/business.py:65 grab/business.py:105 grab/business.py:181
msgid "Grab Room Not Found"
msgstr "房间未找到"

#: grab/business.py:156 grab/business.py:185
#, fuzzy
#| msgid "Invalid count"
msgid "Invalid Count"
msgstr "无效数量"

#: grab/business.py:159
#, fuzzy
#| msgid "Good Not Found"
msgid "Room Not Found"
msgstr "商品未找到"

#: grab/business.py:161
msgid "Room is not Joinable"
msgstr "房间无法加入"

#: grab/business.py:165
msgid "Card is Picked"
msgstr "卡片已被选择"

#: grab/models.py:22
#, fuzzy
#| msgid "Count"
msgid "Coupon"
msgstr "计数"

#: grab/models.py:27
#, fuzzy
#| msgid "Full"
msgid "Fulling"
msgstr "已满"

#: grab/models.py:28
#, fuzzy
#| msgid "Trading"
msgid "Handling"
msgstr "取回中"

#: grab/models.py:31
#, fuzzy
#| msgid "room uid"
msgid "room id"
msgstr "房间"

#: grab/models.py:34
#, fuzzy
#| msgid "total profit"
msgid "total position"
msgstr "总收益"

#: grab/models.py:35
msgid "user max choice"
msgstr "用户最大选择"

#: grab/models.py:36
msgid "coins to one card"
msgstr "金币兑换卡片"

#: grab/models.py:37
#, fuzzy
#| msgid "Item Price"
msgid "item price"
msgstr "饰品价格"

#: grab/models.py:38
#, fuzzy
#| msgid "state"
msgid "room state"
msgstr "状态"

#: grab/models.py:39
msgid "position last"
msgstr "最后位置"

#: grab/models.py:42 grab/models.py:43 grab/models.py:56 grab/models.py:57
#, fuzzy
#| msgid "room"
msgid "GrabRoom"
msgstr "房间"

#: grab/models.py:52 grab/models.py:64
msgid "grab position"
msgstr "抢夺位置"

#: grab/models.py:53
#, fuzzy
#| msgid "victory"
msgid "grab victory"
msgstr "胜利"

#: grab/models.py:65
#, fuzzy
#| msgid "cover image"
msgid "grab card image"
msgstr "封面图案"

#: grab/models.py:69 grab/models.py:70
#, fuzzy
#| msgid "Card"
msgid "GrabCard"
msgstr "卡"

#: grab/models.py:79
#, fuzzy
#| msgid "win amount"
msgid "join count"
msgstr "获胜金额"

#: grab/models.py:80
#, fuzzy
#| msgid "timestamp"
msgid "join timestamp"
msgstr "时间戳"

#: libs/mail_server.py:45
#, fuzzy
#| msgid "Invalid verify code"
msgid "Send verify code fail"
msgstr "无效验证码"

#: lottery/apps.py:7
msgid "Lottery"
msgstr "抽奖"

#: lottery/models.py:14 lottery/models.py:34 lottery/models.py:63
msgid "Hourly"
msgstr "每小时"

#: lottery/models.py:15 lottery/models.py:35 lottery/models.py:64
msgid "Daily"
msgstr "每日"

#: lottery/models.py:16 lottery/models.py:36 lottery/models.py:65
msgid "Weekly"
msgstr "每周"

#: lottery/models.py:18 lottery/models.py:45 lottery/models.py:70
msgid "lottery type"
msgstr "抽奖种类"

#: lottery/models.py:20
msgid "min charge"
msgstr "最小金额"

#: lottery/models.py:25 lottery/models.py:26
msgid "Lottery Setting"
msgstr "抽奖设置"

#: lottery/models.py:39
msgid "Not enough charge"
msgstr "金额不足"

#: lottery/models.py:40
msgid "Joined"
msgstr "已加入"

#: lottery/models.py:46
msgid "charge amount"
msgstr "充值金额"

#: lottery/models.py:48
msgid "win"
msgstr "获胜"

#: lottery/models.py:51
#, fuzzy
#| msgid "winner"
msgid "set winner"
msgstr "获胜者"

#: lottery/models.py:54 lottery/models.py:55
msgid "Lottery Joiner"
msgstr "抽奖参加者"

#: lottery/models.py:68
msgid "comment"
msgstr "备注"

#: lottery/models.py:71
msgid "sort"
msgstr "排序"

#: lottery/models.py:75 lottery/models.py:76
msgid "Lottery Info Setting"
msgstr "抽奖文本设置"

#: luckybox/admin.py:27
msgid "默认图片"
msgstr "默认图片"

#: luckybox/admin.py:33
msgid "自定义图片"
msgstr "自定义图片"

#: luckybox/admin.py:39
msgid "自定义点亮图片"
msgstr "自定义点亮图片"

#: luckybox/apps.py:6
msgid "LuckBox"
msgstr "拉货"

#: luckybox/business.py:237
msgid "Items Invalid"
msgstr "饰品无效"

#: luckybox/business.py:243
#, fuzzy
#| msgid "Page Not Found"
msgid "Items Not Found"
msgstr "页面未找到"

#: luckybox/business.py:245 luckybox/business.py:247
#, fuzzy
#| msgid "Items Invalid"
msgid "Percent Invalid"
msgstr "饰品无效"

#: luckybox/business.py:260
msgid "Open Lucky Box"
msgstr "拉货"

#: luckybox/models.py:14 steambase/models.py:45
msgid "market hash name"
msgstr "市场hash名称"

#: luckybox/models.py:16 steambase/models.py:59
msgid "weapon"
msgstr "武器"

#: luckybox/models.py:18 steambase/models.py:52
msgid "icon url"
msgstr "图标链接"

#: luckybox/models.py:19 steambase/models.py:54
msgid "rarity"
msgstr "稀有性"

#: luckybox/models.py:22 luckybox/models.py:23
msgid "LuckyBox Group"
msgstr "拉货分组"

#: luckybox/models.py:50 luckybox/models.py:51
msgid "LuckyBox Recommend Group"
msgstr "拉货热门分组"

#: luckybox/models.py:65 luckybox/models.py:66
msgid "LuckyBox Item"
msgstr "拉货饰品"

#: luckybox/models.py:80 luckybox/models.py:81
msgid "LuckyBoxCategory"
msgstr "拉货分类"

#: luckybox/models.py:88
msgid "luckybox item"
msgstr "拉货饰品"

#: luckybox/models.py:89
msgid "lucky origin percent"
msgstr "原始幸运百分比"

#: luckybox/models.py:90
msgid "lucky percent"
msgstr "幸运百分率"

#: luckybox/models.py:91
msgid "lucky coins"
msgstr "金额"

#: luckybox/models.py:92
msgid "lucky percentage"
msgstr "目标幸运百分比"

#: luckybox/models.py:93
msgid "lucky result"
msgstr "获胜结果"

#: luckybox/models.py:94
msgid "luckybox target item"
msgstr "拉货概率折扣类型"

#: luckybox/models.py:98 luckybox/models.py:99
msgid "LuckyBox Record"
msgstr "幸运饰品追梦记录"

#: luckybox/service/admin_actions.py:33
msgid "sync group item hash name"
msgstr "刷新幸运分组市场hash名称"

#: luckybox/views.py:155
msgid "拉货维护中"
msgstr "拉货维护中"

#: market/business.py:107
msgid "余额不足, 请充值"
msgstr "余额不足，请充值"

#: market/models.py:14
msgid "item rarity cn"
msgstr "稀有度"

#: market/models.py:15
msgid "item exterior cn"
msgstr "品质"

#: market/models.py:16
msgid "item dark gold"
msgstr "暗金"

#: market/models.py:19 market/models.py:20
msgid "MarketItem"
msgstr "商城饰品"

#: monitor/apps.py:7 monitor/views.py:25
#: templates/admin_backup/base_copy_backup.html:293
msgid "Monitor"
msgstr "监控"

#: package/admin.py:106 package/admin.py:110
msgid "cancel_send"
msgstr "取消_发送"

#: package/apps.py:7 tradeup/models.py:46
msgid "Item"
msgstr "饰品"

#: package/business.py:185
msgid "No bot online"
msgstr "没有在线机器人"

#: package/business.py:197 package/business.py:615 promotion/business.py:54
#: promotion/business.py:57 roll/business.py:72 roll/business.py:151
#: roll/business.py:185 withdraw/business.py:134 withdraw/business.py:136
#: withdraw/business.py:187
msgid "Invalid params"
msgstr "无效参数"

#: package/business.py:265 package/business.py:425
msgid "Out of stock, please content customer service on the right side."
msgstr "缺货，请在右侧联系客服。"

#: package/business.py:332
msgid "一些饰品不存在或已被出售"
msgstr "一些饰品不存在或已被出售"

#: package/business.py:345
msgid "出售饰品"
msgstr "出售饰品"

#: package/business.py:389
msgid "没有可出售的饰品"
msgstr "没有可出售的饰品"

#: package/business.py:433
msgid "Shop item"
msgstr "商城饰品"

#: package/business.py:690 package/views.py:58
msgid "物品ID不能为空"
msgstr "物品ID不能为空"

#: package/business.py:703
msgid "没有找到该饰品"
msgstr "没有找到该饰品"

#: package/models.py:19
msgid "蓝色"
msgstr "蓝色"

#: package/models.py:20
msgid "灰色"
msgstr "灰色"

#: package/models.py:21
msgid "粉色"
msgstr "粉色"

#: package/models.py:22
msgid "红色"
msgstr "红色"

#: package/models.py:23
msgid "金色"
msgstr "金色"

#: package/models.py:24
msgid "紫色"
msgstr "紫色"

#: package/models.py:25
msgid "深蓝色"
msgstr "深蓝色"

#: package/models.py:30
msgid "custom icon"
msgstr "自定义饰品图片"

#: package/models.py:32
msgid "custom rarity"
msgstr "自定义饰品稀有度颜色"

#: package/models.py:38
#, fuzzy
#| msgid "quality"
msgid "Quality"
msgstr "质量"

#: package/models.py:39
#, fuzzy
#| msgid "rarity"
msgid "Rarity"
msgstr "稀有性"

#: package/models.py:40
#, fuzzy
#| msgid "exterior"
msgid "Exterior"
msgstr "外观"

#: package/models.py:46 package/models.py:47
msgid "Item Info"
msgstr "饰品信息"

#: package/models.py:54
#, fuzzy
#| msgid "level"
msgid "Level"
msgstr "等级"

#: package/models.py:55 package/models.py:69 package/models.py:81
#: package/models.py:93
msgid "Is Show"
msgstr "是否显示"

#: package/models.py:56 package/models.py:70 package/models.py:82
#: package/models.py:94
msgid "Sort"
msgstr "排序"

#: package/models.py:61 package/models.py:62
#, fuzzy
#| msgid "WearCategory0"
msgid "Item Category"
msgstr "崭新出厂"

#: package/models.py:66
#, fuzzy
#| msgid "quality"
msgid "Quality Id"
msgstr "质量"

#: package/models.py:67
#, fuzzy
#| msgid "quality"
msgid "Quality Name"
msgstr "质量"

#: package/models.py:68
#, fuzzy
#| msgid "quality color"
msgid "Quality Color"
msgstr "质量颜色"

#: package/models.py:73 package/models.py:74
#, fuzzy
#| msgid "Item Whitelist"
msgid "Item Quality"
msgstr "饰品白名单"

#: package/models.py:78
#, fuzzy
#| msgid "rarity"
msgid "Rarity Id"
msgstr "稀有性"

#: package/models.py:79
msgid "Rarity Name"
msgstr "稀有度名称"

#: package/models.py:80
#, fuzzy
#| msgid "rarity color"
msgid "Rarity Color"
msgstr "稀有性颜色"

#: package/models.py:85 package/models.py:86
#, fuzzy
#| msgid "item rarity cn"
msgid "Item Rarity"
msgstr "稀有度"

#: package/models.py:90
#, fuzzy
#| msgid "exterior"
msgid "Exterior Id"
msgstr "外观"

#: package/models.py:91
#, fuzzy
#| msgid "exterior"
msgid "Exterior Name"
msgstr "外观"

#: package/models.py:92
#, fuzzy
#| msgid "exterior"
msgid "Exterior Color"
msgstr "外观"

#: package/models.py:97 package/models.py:98
#, fuzzy
#| msgid "exterior"
msgid "Item Exterior"
msgstr "外观"

#: package/models.py:106 promotion/models.py:25
msgid "enable custom"
msgstr "启用自定义"

#: package/models.py:107
msgid "custom price(USD)"
msgstr "自定义价格（USD）"

#: package/models.py:108
msgid "custom discount(%)"
msgstr "自定义折扣（%）"

#: package/models.py:109
msgid "waxpeer market price(USD)-0"
msgstr "waxpeer市场价格（USD）-0"

#: package/models.py:110
msgid "zbt market price(USD)-0"
msgstr "Steam市场价格（USD）-0"

#: package/models.py:111
msgid "Steam market price(USD)-0"
msgstr "Steam市场价格（USD）-0"

#: package/models.py:112
msgid "Steam normal price(USD)-10"
msgstr "Steam正常价格（USD）-10"

#: package/models.py:113
msgid "Steam sale price(USD)-11"
msgstr "Steam销售价格（USD）-11"

#: package/models.py:114
#, fuzzy
#| msgid "point price"
msgid "lock price"
msgstr "积分价格"

#: package/models.py:115
#, fuzzy
#| msgid "create time"
msgid "lock price time"
msgstr "创建时间"

#: package/models.py:120 package/models.py:121
msgid "Item Price"
msgstr "饰品价格"

#: package/models.py:163
msgid "Available"
msgstr "可用"

#: package/models.py:164
msgid "Blocked"
msgstr "被锁"

#: package/models.py:165
msgid "Gaming"
msgstr "游戏中"

#: package/models.py:166
msgid "Withdrawing"
msgstr "取回中"

#: package/models.py:167
msgid "Withdrawn"
msgstr "已取回"

#: package/models.py:168
msgid "Exchanged"
msgstr "已出售"

#: package/models.py:169
msgid "Invalid"
msgstr "无效"

#: package/models.py:176 roll/apps.py:7
msgid "Roll"
msgstr "Roll房"

#: package/models.py:177
msgid "BattleRoom"
msgstr "对战房间"

#: package/models.py:178
msgid "Giveaway"
msgstr "赠品"

#: package/models.py:184 package/models.py:311 package/models.py:345
msgid "instanceid"
msgstr "实例id"

#: package/models.py:186 package/models.py:292 package/models.py:346
msgid "bot Steamid"
msgstr "机器人Steamid"

#: package/models.py:188
msgid "part of item"
msgstr "饰品的一部分"

#: package/models.py:190
msgid "case name"
msgstr "箱子名字"

#: package/models.py:191
msgid "case cover"
msgstr "箱子背景图"

#: package/models.py:192
#, fuzzy
#| msgid "case_key"
msgid "case key"
msgstr "钥匙"

#: package/models.py:215 package/models.py:216
msgid "Package Item"
msgstr "饰品背包"

#: package/models.py:221
msgid "price rate(%)"
msgstr "价格比率（%）"

#: package/models.py:222
msgid "min price"
msgstr "最小价格"

#: package/models.py:223
msgid "max price"
msgstr "最大价格"

#: package/models.py:227 package/models.py:228
msgid "Item Price Rate Config"
msgstr "饰品价格比率配置"

#: package/models.py:252 package/models.py:253
msgid "Exchange Record"
msgstr "出售记录"

#: package/models.py:268 package/models.py:269
msgid "Shop record"
msgstr "商城记录"

#: package/models.py:274 package/models.py:389 package/models.py:418
msgid "Deposit"
msgstr "存入"

#: package/models.py:275 package/models.py:390 package/models.py:419
msgid "Withdraw"
msgstr "取回"

#: package/models.py:281
msgid "Submitted"
msgstr "已提交"

#: package/models.py:282
msgid "TradeNoUpdated"
msgstr "交易编号更新"

#: package/models.py:283 withdraw/models.py:21
msgid "Active"
msgstr "活跃的"

#: package/models.py:287 package/models.py:394 package/models.py:423
msgid "trade type"
msgstr "交易种类"

#: package/models.py:291
msgid "security code"
msgstr "安全码"

#: package/models.py:293
msgid "bot message"
msgstr "机器人消息"

#: package/models.py:294
msgid "trade time"
msgstr "交易时间"

#: package/models.py:295
msgid "review"
msgstr "审核"

#: package/models.py:302 package/models.py:303 withdraw/models.py:49
#: withdraw/models.py:50
msgid "Trade Record"
msgstr "交易记录"

#: package/models.py:308
msgid "trade record"
msgstr "交易记录"

#: package/models.py:318 package/models.py:319
msgid "Trade Item"
msgstr "交易饰品"

#: package/models.py:327
msgid "enable deposit"
msgstr "可用的余额"

#: package/models.py:328
msgid "enable withdraw"
msgstr "可取回金额"

#: package/models.py:330 roll/models.py:32
msgid "password"
msgstr "密码"

#: package/models.py:331
msgid "shared secret"
msgstr "分享的秘密"

#: package/models.py:332
msgid "identity secret"
msgstr "身份秘密"

#: package/models.py:338 package/models.py:339
msgid "Trade Bot Config"
msgstr "交易机器人配置"

#: package/models.py:347
msgid "matching"
msgstr "匹配"

#: package/models.py:354 package/models.py:355
msgid "Trade Bot Inventory"
msgstr "交易机器人库存"

#: package/models.py:367 package/models.py:368
msgid "Shop Bot Config"
msgstr "商城机器人配置"

#: package/models.py:383 package/models.py:384
msgid "BlackList"
msgstr "黑名单"

#: package/models.py:412 package/models.py:413
msgid "Item Statistics Day"
msgstr "日饰品统计"

#: package/models.py:446 package/models.py:447
msgid "Item Statistics Month"
msgstr "月饰品统计"

#: package/models.py:451
msgid "last unlock time"
msgstr "上一次解锁时间"

#: package/models.py:452
msgid "interval(min)"
msgstr "间隔（分钟）"

#: package/models.py:459 package/models.py:460
msgid "Item Unlock Time Config"
msgstr "饰品未锁定时间配置"

#: package/models.py:465
msgid "Count"
msgstr "计数"

#: package/models.py:472 package/models.py:473
msgid "Lock Items Statistics"
msgstr "锁定饰品统计"

#: package/models.py:483 package/models.py:484
msgid "Item Whitelist"
msgstr "饰品白名单"

#: package/service/admin_actions.py:42
msgid "Startup complete"
msgstr "启动完成"

#: package/service/admin_actions.py:44
msgid "Startup fail"
msgstr "启动失败"

#: package/service/admin_actions.py:47
msgid "Startup trade bot"
msgstr "启动交易机器人"

#: package/service/admin_actions.py:58
msgid "Get inventory from Steam fail"
msgstr "从Steam获取库存失败"

#: package/service/admin_actions.py:86
msgid "Review complete"
msgstr "审核完成"

#: package/service/admin_actions.py:89
msgid "Review selected trade"
msgstr "审核已选交易"

#: package/service/admin_actions.py:94
msgid "Invalidate complete"
msgstr "作废完成"

#: package/service/admin_actions.py:97
msgid "Invalidate selected item"
msgstr "作废已选物品"

#: package/service/admin_actions.py:102
msgid "Exchanged complete"
msgstr "已出售"

#: package/service/admin_actions.py:105
msgid "Exchanged selected item"
msgstr "已选物品"

#: package/service/admin_actions.py:110
#, fuzzy
#| msgid "Invalidate complete"
msgid "Available complete"
msgstr "作废完成"

#: package/service/admin_actions.py:113
msgid "Available selected item"
msgstr "物品可用"

#: package/service/admin_actions.py:150
msgid "import item to luckybox"
msgstr "导入到拉货"

#: package/service/admin_actions.py:164
#, fuzzy
#| msgid "import item to luckybox"
msgid "import item to custombox"
msgstr "导入到拉货"

#: package/service/admin_actions.py:222
msgid "背包饰品导出EXCEL"
msgstr "背包饰品导出EXCEL"

#: package/views.py:213
msgid "Deposit is under maintenance, please wait for a while."
msgstr "存入正在维护中，请稍候。"

#: package/views.py:337
msgid "饰品售出子系统正在维护，请稍后再试。"
msgstr "饰品售出子系统正在维护，请稍后再试"

#: package/views.py:363
msgid "Exchange is under maintenance, please wait for a while."
msgstr "交换正在维护中，请稍候。"

#: package/views.py:382
msgid "Shop is under maintenance, please wait for a while."
msgstr "商城正在维护中，请稍候。"

#: promotion/apps.py:7 promotion/apps.py:8
msgid "Promotion"
msgstr "推广"

#: promotion/business.py:27
msgid "Invalid promotion code"
msgstr "无效促销码"

#: promotion/business.py:31
msgid "You have been bound another account"
msgstr "您已绑定另一个账户"

#: promotion/business.py:52
msgid "Code Length Must 4 to 8 digits"
msgstr "推广码必须4至8位"

#: promotion/business.py:59
msgid "Code is already exists"
msgstr "代码已经存在"

#: promotion/business.py:69
msgid "邀请人数不足"
msgstr "邀请人数不足"

#: promotion/business.py:77
msgid "您的收益不足5B"
msgstr "您的收益不足5B"

#: promotion/models.py:20
msgid "refer code"
msgstr "邀请代码"

#: promotion/models.py:21
msgid "refer count"
msgstr "邀请数量"

#: promotion/models.py:22 promotion/models.py:40
msgid "total charge"
msgstr "总充值"

#: promotion/models.py:23
msgid "total profit"
msgstr "总收益"

#: promotion/models.py:24
msgid "profit"
msgstr "收益"

#: promotion/models.py:26
msgid "refer level"
msgstr "邀请等级"

#: promotion/models.py:27 promotion/models.py:60
msgid "profit rate(%)"
msgstr "收益率（%）"

#: promotion/models.py:30 promotion/models.py:31
msgid "User Promotion"
msgstr "用户推广信息"

#: promotion/models.py:38
msgid "refer"
msgstr "邀请"

#: promotion/models.py:41
msgid "refer profit"
msgstr "邀请收益"

#: promotion/models.py:46 promotion/models.py:47
msgid "Promotion Record"
msgstr "推广记录"

#: promotion/models.py:61
msgid "refer rate(%)"
msgstr "推广率（%）"

#: promotion/models.py:69 promotion/models.py:70
msgid "Promotion Level Config"
msgstr "推广等级配置"

#: promotion/models.py:82 promotion/models.py:83
msgid "PromotionCaseConfig"
msgstr "推广箱子配置"

#: promotion/service/admin_actions.py:37
msgid "推广记录导出Excel"
msgstr "推广记录导出Excel"

#: roll/admin.py:71 roll/models.py:86 roll/models.py:87
msgid "Roll Room Bet"
msgstr "Roll房加入"

#: roll/admin.py:114
msgid "room uid"
msgstr "房间"

#: roll/business.py:74
msgid "Invalid fee"
msgstr "无效等级"

#: roll/business.py:197
msgid "存入数量必须大于获胜人数"
msgstr "存入数量必须大于获胜人数"

#: roll/business.py:247
msgid "Invalid password"
msgstr "密码错误"

#: roll/business.py:263
msgid "充值金额小于参加条件"
msgstr "充值金额小于参加条件"

#: roll/business.py:267
msgid "Roll room bet"
msgstr "Roll房加入"

#: roll/business.py:269
msgid "Failed to update balance"
msgstr "更新余额失败"

#: roll/business.py:314
msgid "Somebody has joined"
msgstr "已经加入"

#: roll/business.py:320
msgid "Roll room cancel"
msgstr "Roll房取消"

#: roll/business.py:560 roll/business.py:675
msgid "Roll room fee"
msgstr "roll房费率"

#: roll/business.py:771
msgid "Rollroom cancel"
msgstr "Roll房取消"

#: roll/business.py:1251
msgid "发生错误"
msgstr "发生错误"

#: roll/business.py:1336
msgid "存入数量必须等于获胜人数"
msgstr "存入数量必须等于获胜人数"

#: roll/model_signals.py:61
msgid "Rollroom win"
msgstr "Roll房胜利"

#: roll/models.py:21
msgid "No charge room"
msgstr "非收费房"

#: roll/models.py:22
msgid "Charge room"
msgstr "收费房间"

#: roll/models.py:25
msgid "主播"
msgstr "主播"

#: roll/models.py:26
msgid "官方"
msgstr "官方"

#: roll/models.py:33
msgid "entrance fee"
msgstr "房费"

#: roll/models.py:34
msgid "min joiner"
msgstr "最小参加人数"

#: roll/models.py:36
msgid "max winner"
msgstr "最大获胜人数"

#: roll/models.py:37
msgid "begin time"
msgstr "开始时间"

#: roll/models.py:38
msgid "due time"
msgstr "结束时间"

#: roll/models.py:39
msgid "win time"
msgstr "开奖时间"

#: roll/models.py:40
msgid "total fee"
msgstr "总充值"

#: roll/models.py:43
msgid "total items count"
msgstr "饰品总数"

#: roll/models.py:45
msgid "deposit enable"
msgstr "启用存入限制"

#: roll/models.py:46
msgid "rollroom enable"
msgstr "Roll房启用"

#: roll/models.py:47
msgid "charge limit amount"
msgstr "充值金额限制"

#: roll/models.py:48
msgid "charge begin time"
msgstr "充值开始时间限制"

#: roll/models.py:49
msgid "charge end time"
msgstr "充值结束时间限制"

#: roll/models.py:50
msgid "official"
msgstr "官方"

#: roll/models.py:53
msgid "debug"
msgstr "调试"

#: roll/models.py:70 roll/models.py:71
msgid "Roll Room"
msgstr "roll房"

#: roll/models.py:102
#, fuzzy
#| msgid "Item Info"
msgid "Item ID"
msgstr "饰品信息"

#: roll/models.py:105 roll/models.py:106
msgid "Roll Room Item"
msgstr "Roll房饰品"

#: roll/models.py:141 roll/models.py:142
msgid "Roll Room Pump Day"
msgstr "日Crash Pump"

#: roll/models.py:171 roll/models.py:172
msgid "Roll Room Pump Month"
msgstr "月Crash Pump"

#: roll/models.py:177
msgid "join idle min(seconds)"
msgstr "最小加入间隔（秒）"

#: roll/models.py:178
msgid "join idle max(seconds)"
msgstr "最大加入间隔（秒）"

#: roll/models.py:183 roll/models.py:184
msgid "Rollroom Bot Config"
msgstr "Roll房机器人配置"

#: roll/models.py:191
#, fuzzy
#| msgid "bet"
msgid "bot"
msgstr "对战"

#: roll/models.py:195 roll/models.py:196
msgid "Roll房机器人"
msgstr "Roll房机器人"

#: roll/models.py:211 roll/models.py:212
msgid "Rollroom Statistics Day"
msgstr "Roll房日统计"

#: roll/models.py:271 roll/models.py:272
msgid "Rollroom Statistics Month"
msgstr "Roll房月统计"

#: roll/service/admin_actions.py:26
msgid "Cancel rollroom state complete"
msgstr "取消roll房完成"

#: roll/service/admin_actions.py:28
msgid "Cancel rollroom state"
msgstr "取消roll房"

#: roll/service/admin_actions.py:40
msgid "delete rollroom complete"
msgstr "删除roll房完成"

#: roll/service/admin_actions.py:42
msgid "Delete rollroom"
msgstr "删除roll房"

#: roll/views.py:38 roll/views.py:78 roll/views.py:107 roll/views.py:137
#: roll/views.py:158 roll/views.py:498
msgid "月度福利板块正在维护"
msgstr "月度福利板块正在维护"

#: roll/views.py:468
msgid "福利房间维护中，请稍后再试"
msgstr "福利房间维护中，请稍后再试"

#: sitecfg/apps.py:7 sitecfg/models.py:32 sitecfg/models.py:33
msgid "Site Config"
msgstr "站点配置"

#: sitecfg/models.py:13 sitecfg/models.py:14
msgid "Site Config Category"
msgstr "站点配置类别"

#: sitecfg/models.py:24
msgid "value"
msgstr "值"

#: sitecfg/models.py:29 sitecfg/models.py:70 sitecfg/models.py:86
#: sitecfg/models.py:100 sitecfg/models.py:122 sitecfg/models.py:147
#: sitecfg/models.py:166
msgid "order No."
msgstr "排序编号"

#: sitecfg/models.py:41
msgid "Homepage"
msgstr "主页"

#: sitecfg/models.py:52 sitecfg/models.py:53 sitecfg/models.py:61
#: sitecfg/models.py:68
#, fuzzy
#| msgid "Announce"
msgid "Announcee"
msgstr "声明"

#: sitecfg/models.py:62
msgid "Link"
msgstr "链接"

#: sitecfg/models.py:67 sitecfg/models.py:125
msgid "link"
msgstr "链接"

#: sitecfg/models.py:75 sitecfg/models.py:76
msgid "Footer"
msgstr "页脚"

#: sitecfg/models.py:105 sitecfg/models.py:106
msgid "Support"
msgstr "支持"

#: sitecfg/models.py:114
#, fuzzy
#| msgid "Ali"
msgid "All"
msgstr "支付宝"

#: sitecfg/models.py:115
msgid "PC"
msgstr "电脑"

#: sitecfg/models.py:116
msgid "Mobile"
msgstr "手机"

#: sitecfg/models.py:121
msgid "is simple"
msgstr "是否简单"

#: sitecfg/models.py:126
msgid "background class"
msgstr "背景类"

#: sitecfg/models.py:127
msgid "glow class"
msgstr "发光类"

#: sitecfg/models.py:128
msgid "primary button text"
msgstr "主按钮文本"

#: sitecfg/models.py:129
msgid "primary button link"
msgstr "主按钮链接"

#: sitecfg/models.py:130
msgid "secondary button text"
msgstr "次按钮文本"

#: sitecfg/models.py:131
msgid "secondary button link"
msgstr "次按钮链接"

#: sitecfg/models.py:138 sitecfg/models.py:139
#, fuzzy
#| msgid "winner"
msgid "Banner"
msgstr "获胜者"

#: sitecfg/models.py:158
#, fuzzy
#| msgid "title"
msgid "seo title"
msgstr "标题"

#: sitecfg/models.py:159
#, fuzzy
#| msgid "description"
msgid "seo description"
msgstr "备注"

#: sitecfg/models.py:160
#, fuzzy
#| msgid "case_key"
msgid "seo keywords"
msgstr "钥匙"

#: sitecfg/models.py:185
msgid "Keywords"
msgstr "关键词"

#: sitecfg/models.py:188
msgid "ICP"
msgstr "ICP备案"

#: sitecfg/models.py:190
#, fuzzy
#| msgid "enable"
msgid "News Enable"
msgstr "可用"

#: sitecfg/models.py:204
msgid "IP"
msgstr "IP地址"

#: sitecfg/models.py:207
#, fuzzy
#| msgid "remark"
msgid "remarks"
msgstr "备注"

#: sitecfg/service/admin_actions.py:18
msgid "set online users base count complete"
msgstr "在线用户基数设置完成"

#: sitecfg/service/admin_actions.py:20
msgid "set online users base count fail"
msgstr "在线用户基数设置失败"

#: sitecfg/service/admin_actions.py:22
msgid "set online base count"
msgstr "设置在线用户基数"

#: steambase/models.py:21
msgid "uid"
msgstr "uid"

#: steambase/models.py:37
msgid "WearCategory0"
msgstr "崭新出厂"

#: steambase/models.py:38
msgid "WearCategory1"
msgstr "略有磨损"

#: steambase/models.py:39
msgid "WearCategory2"
msgstr "久经沙场"

#: steambase/models.py:40
msgid "WearCategory3"
msgstr "破损不堪"

#: steambase/models.py:41
msgid "WearCategory4"
msgstr "战痕累累"

#: steambase/models.py:46
msgid "name(CN)"
msgstr "名字（CN）"

#: steambase/models.py:47
msgid "market name(CN)"
msgstr "市场名称（CN）"

#: steambase/models.py:48
msgid "name color"
msgstr "名字颜色"

#: steambase/models.py:50
msgid "contextid"
msgstr "contextid"

#: steambase/models.py:51
msgid "classid"
msgstr "classid"

#: steambase/models.py:53
msgid "icon url large"
msgstr "大图标链接"

#: steambase/models.py:55
msgid "rarity color"
msgstr "稀有性颜色"

#: steambase/models.py:56
msgid "quality"
msgstr "质量"

#: steambase/models.py:57
msgid "quality color"
msgstr "质量颜色"

#: steambase/models.py:60
msgid "exterior"
msgstr "外观"

#: steambase/models.py:61
msgid "item set"
msgstr "饰品设置"

#: steambase/models.py:62
msgid "slot"
msgstr "slot"

#: steambase/models.py:63
msgid "hero"
msgstr "英雄"

#: templates/404.html:5
msgid "Not Found"
msgstr "未找到"

#: templates/404.html:10
msgid "Page Not Found"
msgstr "页面未找到"

#: templates/404.html:13 templates/500.html:16
msgid "Go to Home Page"
msgstr "去往主页"

#: templates/500.html:5 templates/500.html:9
msgid "Server Exception"
msgstr "服务器异常"

#: templates/500.html:13
msgid "Page Not Found111"
msgstr "页面未找到111"

#: templates/admin/base_site.html:3
msgid "Django site admin"
msgstr "Django 站点管理"

#: templates/admin/base_site.html:6
msgid "Django administration"
msgstr "Django 管理"

#: templates/admin_backup/base_copy_backup.html:60
#: templates/admin_backup/base_jet_backup.html:60
msgid "Welcome,"
msgstr "欢迎，"

#: templates/admin_backup/base_copy_backup.html:65
#: templates/admin_backup/base_copy_backup.html:182
#: templates/admin_backup/base_jet_backup.html:65
#: templates/admin_backup/base_jet_backup.html:182
msgid "View site"
msgstr "浏览站点"

#: templates/admin_backup/base_copy_backup.html:70
#: templates/admin_backup/base_copy_backup.html:191
#: templates/admin_backup/base_jet_backup.html:70
#: templates/admin_backup/base_jet_backup.html:191
msgid "Documentation"
msgstr "文件"

#: templates/admin_backup/base_copy_backup.html:74
#: templates/admin_backup/base_jet_backup.html:74
msgid "Change password"
msgstr "更换密码"

#: templates/admin_backup/base_copy_backup.html:76
#: templates/admin_backup/base_jet_backup.html:76
msgid "Log out"
msgstr "登出"

#: templates/admin_backup/base_copy_backup.html:86
#: templates/admin_backup/base_copy_backup.html:174
#: templates/admin_backup/base_jet_backup.html:86
#: templates/admin_backup/base_jet_backup.html:174
#: templates/admin_backup/change_form.html:23
msgid "Home"
msgstr "主页"

#: templates/admin_backup/base_copy_backup.html:154
#: templates/admin_backup/base_jet_backup.html:154
msgid "back"
msgstr "返回"

#: templates/admin_backup/base_copy_backup.html:235
#: templates/admin_backup/base_jet_backup.html:235
msgid "Applications"
msgstr "应用"

#: templates/admin_backup/base_copy_backup.html:259
#: templates/admin_backup/base_jet_backup.html:259
msgid "Hide applications"
msgstr "隐藏应用"

#: templates/admin_backup/base_copy_backup.html:260
#: templates/admin_backup/base_jet_backup.html:260
msgid "Show hidden"
msgstr "显示隐藏项"

#: templates/admin_backup/base_copy_backup.html:302
#: templates/admin_backup/base_copy_backup.html:321
#: templates/admin_backup/base_jet_backup.html:286
#: templates/admin_backup/base_jet_backup.html:305
msgid "Add bookmark"
msgstr "加书签"

#: templates/admin_backup/base_copy_backup.html:307
#: templates/admin_backup/base_jet_backup.html:291
msgid "URL"
msgstr "链接"

#: templates/admin_backup/base_copy_backup.html:315
#: templates/admin_backup/base_jet_backup.html:299
msgid "Delete bookmark"
msgstr "删除书签"

#: templates/admin_backup/base_copy_backup.html:316
#: templates/admin_backup/base_jet_backup.html:300
msgid "Are you sure want to delete this bookmark?"
msgstr "您确定要删除此书签吗？"

#: templates/admin_backup/base_copy_backup.html:323
#: templates/admin_backup/base_jet_backup.html:307
msgid "bookmarks"
msgstr "书签"

#: templates/admin_backup/base_copy_backup.html:331
#: templates/admin_backup/base_copy_backup.html:338
#: templates/admin_backup/base_jet_backup.html:315
#: templates/admin_backup/base_jet_backup.html:322
msgid "Remove"
msgstr "移除"

#: templates/admin_backup/base_copy_backup.html:360
#: templates/admin_backup/base_jet_backup.html:344
msgid "Search"
msgstr "搜索"

#: templates/admin_backup/base_copy_backup.html:365
#: templates/admin_backup/base_jet_backup.html:349
msgid "Application page"
msgstr "应用页面"

#: templates/admin_backup/base_copy_backup.html:391
#: templates/admin_backup/base_jet_backup.html:375
msgid "current theme"
msgstr "当前主题"

#: templates/admin_backup/change_form.html:26
#, python-format
msgid "Add %(name)s"
msgstr "添加 %(name)s"

#: templates/admin_backup/change_form.html:38
msgid "History"
msgstr "历史"

#: templates/admin_backup/change_form.html:40
msgid "View on site"
msgstr "View on site"

#: templates/admin_backup/change_form.html:52
#: templates/admin_backup/login.html:21
msgid "Please correct the error below."
msgstr "请更正以下错误。"

#: templates/admin_backup/change_form.html:52
#: templates/admin_backup/login.html:21
msgid "Please correct the errors below."
msgstr "请更正以下错误。"

#: templates/admin_backup/login.html:37
#, python-format
msgid ""
"You are authenticated as %(username)s, but are not authorized to access this "
"page. Would you like to login to a different account?"
msgstr "您已通过身份验证 %(username)s，但无权访问此页面。您要登录其他账户吗？"

#: templates/admin_backup/login.html:79
msgid "Forgotten your password or username?"
msgstr "忘记密码或用户名？"

#: templates/admin_backup/login.html:83
msgid "Log in"
msgstr "登录"

#: templates/admin_backup/monitor.html:127
msgid "Connecting"
msgstr "连接中"

#: tradeup/apps.py:7
msgid "Tradeup"
msgstr "卖次买好"

#: tradeup/business.py:57 tradeup/business.py:100 tradeup/business.py:495
#: tradeup/business.py:532
msgid "Invalid bet items"
msgstr "无效对战饰品"

#: tradeup/business.py:110 tradeup/business.py:196 tradeup/business.py:503
#: tradeup/business.py:540
msgid "Invalid target items"
msgstr "无效目标饰品"

#: tradeup/business.py:126 tradeup/business.py:209
#, fuzzy
#| msgid "Coins Tradeup Bet amount"
msgid "Coins Tradeup game bet"
msgstr "Coins Tradeup Bet amount"

#: tradeup/business.py:132 tradeup/business.py:217 tradeup/business.py:520
#: tradeup/business.py:549
msgid "Invalid bet percentage"
msgstr "无效对战比例"

#: tradeup/business.py:202
msgid "Invalid target appid"
msgstr "无效目标appid"

#: tradeup/business.py:297
#, fuzzy
#| msgid "Coins Tradeup Bet amount"
msgid "Coins Tradeup game win"
msgstr "Coins Tradeup Bet amount"

#: tradeup/models.py:27
msgid "bet percentage"
msgstr "对战比例"

#: tradeup/models.py:28
msgid "upper"
msgstr "upper"

#: tradeup/models.py:29
msgid "origin amount"
msgstr "原始金额"

#: tradeup/models.py:30
msgid "target amount"
msgstr "目标金额"

#: tradeup/models.py:37 tradeup/models.py:38
msgid "Tradeup Game"
msgstr "Tradeup游戏"

#: tradeup/models.py:52
msgid "bet item type"
msgstr "对战饰品种类"

#: tradeup/models.py:55 tradeup/models.py:56
msgid "Tradeup Bet Item"
msgstr "Tradeup Bet Item"

#: tradeup/models.py:74 tradeup/models.py:75
msgid "Coins Tradeup Bet amount"
msgstr "Coins Tradeup Bet amount"

#: tradeup/models.py:87 tradeup/models.py:88
msgid "Tradeup Target Item"
msgstr "Tradeup Target Item"

#: tradeup/models.py:102 tradeup/models.py:103
msgid "Tradeup Inventory Item"
msgstr "Tradeup Inventory Item"

#: tradeup/models.py:115 tradeup/models.py:116
msgid "Tradeup Pump Day"
msgstr "Tradeup Pump Day"

#: tradeup/models.py:139 tradeup/models.py:140
msgid "Tradeup Pump Month"
msgstr "Tradeup Pump Month"

#: tradeup/models.py:173 tradeup/models.py:174
msgid "Tradeup Statistics Day"
msgstr "Tradeup Statistics Day"

#: tradeup/models.py:257 tradeup/models.py:258
msgid "Tradeup Statistics Month"
msgstr "Tradeup Statistics Month"

#: tradeup/models.py:346 tradeup/models.py:347
msgid "Tradeup Win Day Rank"
msgstr "Tradeup日获胜排名"

#: tradeup/models.py:371 tradeup/models.py:372
msgid "Tradeup Win Week Rank"
msgstr "Tradeup Win Week Rank"

#: tradeup/models.py:396
msgid "bet idle min(seconds)"
msgstr "最小对战间隔（秒）"

#: tradeup/models.py:397
msgid "bet idle max(seconds)"
msgstr "最大对战间隔（秒）"

#: tradeup/models.py:398
msgid "bot tradeup min chance"
msgstr "bot tradeup min chance"

#: tradeup/models.py:399
msgid "bot tradeup max chance"
msgstr "bot tradeup max chance"

#: tradeup/models.py:400
msgid "bot item price min"
msgstr "最小机器人饰品价格"

#: tradeup/models.py:401
msgid "bot item price max"
msgstr "最大机器人饰品价格"

#: tradeup/models.py:406 tradeup/models.py:407
msgid "Tradeup Bot Config"
msgstr "Tradeup Bot Config"

#: tradeup/service/admin_actions.py:21
msgid "Sync Tradeup Price complete"
msgstr "Sync Tradeup Price complete"

#: tradeup/service/admin_actions.py:23
msgid "Sync Tradeup Price state"
msgstr "Sync Tradeup Price state"

#: websocket/apps.py:7
msgid "Websocket"
msgstr "Websocket"

#: withdraw/apps.py:7
msgid "WXP取回"
msgstr "WXP取回"

#: withdraw/business.py:33
msgid "您需累计充值15$才能提取饰品"
msgstr "您需累计充值15$才能提取饰品"

#: withdraw/business.py:43
msgid "You have another unclosed trade"
msgstr "您还有另一笔未关闭的交易"

#: withdraw/business.py:46
msgid "Invalid package items"
msgstr "无效背包饰品"

#: withdraw/business.py:50
#, fuzzy
#| msgid "Minimum withdrawal amount is 1$"
msgid "Minimum withdrawal amount"
msgstr "最小取回金额为1$"

#: withdraw/business.py:168
#, fuzzy
#| msgid "point price"
msgid "Error price"
msgstr "积分价格"

#: withdraw/business.py:177
msgid "Over withdraw price"
msgstr "超过提现价格"

#: withdraw/business.py:185
msgid "Items is not allowed, please try another items"
msgstr "饰品不允许，请尝试其他饰品"

#: withdraw/business.py:236
msgid "success"
msgstr "成功"

#: withdraw/business.py:320
msgid "close trade error"
msgstr "关闭交易错误"

#: withdraw/models.py:22
msgid "Closed"
msgstr "已关闭"

#: withdraw/models.py:23
msgid "PendClose"
msgstr "等待关闭"

#: withdraw/models.py:27
msgid "waiting for user to buy more items"
msgstr "等待用户购买更多饰品"

#: withdraw/models.py:28
msgid "Proccessing the trade"
msgstr "处理交易中"

#: withdraw/models.py:29
msgid "Waiting for seller to confirm"
msgstr "等待卖家确认"

#: withdraw/models.py:30
msgid "Trade Sent"
msgstr "交易发送"

#: withdraw/models.py:31
msgid "Completed"
msgstr "已完成"

#: withdraw/models.py:32
msgid "Declined and Refunded"
msgstr "拒绝并退款"

#: withdraw/models.py:42
msgid "amount used"
msgstr "已用金额"

#: withdraw/models.py:43
msgid "drop refund"
msgstr "掉落返还"

#: withdraw/models.py:44
msgid "balance refund"
msgstr "余额返还"

#: withdraw/models.py:45
msgid "trade state"
msgstr "交易状态"

#: withdraw/models.py:57 withdraw/models.py:71
msgid "trade id"
msgstr "交易id"

#: withdraw/models.py:59
msgid "returned"
msgstr "已返回"

#: withdraw/models.py:63 withdraw/models.py:64
msgid "Withdraw Item"
msgstr "提取饰品"

#: withdraw/models.py:72
msgid "project id"
msgstr "项目id"

#: withdraw/models.py:73
msgid "waxpeer id"
msgstr "waxpeer id"

#: withdraw/models.py:74
msgid "waxpeer item id"
msgstr "waxpeer饰品id"

#: withdraw/models.py:75
msgid "waxpeer withdraw status"
msgstr "waxpeer取回状态"

#: withdraw/models.py:77
msgid "done"
msgstr "已完成"

#: withdraw/models.py:81
msgid "steam trade id"
msgstr "steam交易id"

#: withdraw/models.py:85 withdraw/models.py:86
msgid "Wxp Trade Offer"
msgstr "Wxp交易记录"

#: withdraw/models.py:107 withdraw/models.py:108
msgid "Trade Statistics Day"
msgstr "日交易统计"

#: withdraw/models.py:150 withdraw/models.py:151
msgid "Trade Statistics Month"
msgstr "月交易统计"

#~ msgid "B2Ctrade"
#~ msgstr "扎比特取回"

#~ msgid "price_expectation_a"
#~ msgstr "A概率期望价格"

#, fuzzy
#~| msgid "Case is blocked, please open other case."
#~ msgid "Case is block, please open other case."
#~ msgstr "箱子维护中，请稍后再试。"

#~ msgid "CaseKey"
#~ msgstr "钥匙专属"

#~ msgid "blue"
#~ msgstr "蓝色"

#~ msgid "purple"
#~ msgstr "紫色"

#~ msgid "red"
#~ msgstr "红色"

#~ msgid "round"
#~ msgstr "轮数"

#~ msgid "pay url"
#~ msgstr "交易链接"

#~ msgid "Timestamp"
#~ msgstr "时间戳记"

#~ msgid "limited"
#~ msgstr "未受限"

#~ msgid "win roll bet"
#~ msgstr "获胜结果"

#~ msgid "content_en"
#~ msgstr "英文介绍"

#~ msgid "content_cn"
#~ msgstr "中文介绍"

#, fuzzy
#~| msgid "Market"
#~ msgid "target"
#~ msgstr "市场"

#~ msgid "withdraw"
#~ msgstr "提取"

#, fuzzy
#~| msgid "Invalid level"
#~ msgid "Invalid Level"
#~ msgstr "无效等级"

#~ msgid "Not enough invitations"
#~ msgstr "邀请数量不足"

#~ msgid "Invalid amount"
#~ msgstr "无效金额"

#~ msgid "Diamond exchange coins"
#~ msgstr "交换Coins"

#~ msgid "Shop"
#~ msgstr "商城"

#~ msgid "Invalid shop item"
#~ msgstr "无效商城饰品"

#~ msgid "Out of stock"
#~ msgstr "缺货"

#~ msgid "diamond price"
#~ msgstr "钻石价格"

#~ msgid "active point price"
#~ msgstr "活跃积分价格"

#~ msgid "point price"
#~ msgstr "积分价格"

#~ msgid "Shop Item"
#~ msgstr "商城饰品"

#~ msgid "Active point"
#~ msgstr "活跃积分"

#~ msgid "Point"
#~ msgstr "积分"

#~ msgid "Create case room"
#~ msgstr "创建开箱房间"

#~ msgid "Case room bet"
#~ msgstr "对战"

#~ msgid "Exchange item"
#~ msgstr "出售饰品"

#~ msgid "The current trade url does not belong to your account"
#~ msgstr "当前交易链接不属于您的账户"

#~ msgid "Phone is bound by another user"
#~ msgstr "手机已被其他用户绑定"

#~ msgid "only superuse can charge in test"
#~ msgstr "只有超级用户才能在测试中充值"

#~ msgid "G2Apay"
#~ msgstr "G2Apay"

#, fuzzy
#~| msgid "Invalid items"
#~ msgid "Invalid item price"
#~ msgstr "无效饰品"

#~ msgid "buy failed, please try another item"
#~ msgstr "购买失败，请尝试其他饰品"

#~ msgid "{}"
#~ msgstr "{}"
