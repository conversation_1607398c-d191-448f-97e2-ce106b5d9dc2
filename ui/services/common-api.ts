// TypeScript接口定义
export interface ApiResponse<T = any> {
  code: number
  message: string
  body?: T
  data?: T
}

export interface StatsData {
  user_number: number
  case_number: number
  battle_number: number
  online_number: number
}

export interface CaseRecord {
  id: string
  user: string
  case_name: string
  skin_name: string
  skin_image: string
  case_image: string
  rarity: string
  value: number
  timestamp: number
}

export interface CaseItem {
  id: string
  name: string
  key: string
  case_key?: string
  image: string
  price: number
  hot_level?: number
  sell_count?: number
  is_hot?: boolean
  is_new?: boolean
  discount?: number
  updated_at: string
  // 保留多语言字段
  name_en?: string
  name_zh_hans?: string
  description?: string
  description_en?: string
  description_zh_hans?: string
  cover?: string
  tag?: string
  tag_en?: string
  tag_zh_hans?: string
  open_count?: number
}

export interface HomeData {
  todayOpened: number
  totalUsers: number
  totalOpenings: number
  onlineUsers: number
  hotCases: CaseItem[]
  newCases: CaseItem[]
  discountCases: CaseItem[]
  recentOpenings: CaseRecord[]
}

// 通用API服务类
class CommonApi {
  private baseURL = ''
  
  // 创建fetch封装，统一处理请求
  private async fetchApi<T>(endpoint: string, options: RequestInit & { query?: Record<string, any> } = {}): Promise<T> {
    const { query, ...fetchOptions } = options
    
    // 构建URL和查询参数
    let url = `${this.baseURL}${endpoint}`
    if (query) {
      const searchParams = new URLSearchParams()
      Object.entries(query).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value))
        }
      })
      if (searchParams.toString()) {
        url += `?${searchParams.toString()}`
      }
    }

    console.log(`[API] 请求: ${fetchOptions.method || 'GET'} ${url}`)

    const config: RequestInit = {
      ...fetchOptions,
      headers: {
        'Content-Type': 'application/json',
        ...fetchOptions.headers,
      },
    }

    try {
      const response = await $fetch<T>(url, config as any)
      console.log(`[API] 响应成功: ${url}`)
      return response
    } catch (error) {
      console.error(`[API] 请求失败: ${url}`, error)
      throw error
    }
  }

  // 数据转换辅助方法
  private transformCaseItems(items: any[]): CaseItem[] {
    return items.map(item => ({
      id: item.id || item.case_key || item.key,
      name: item.name || item.name_zh_hans || '',
      key: item.key || item.case_key || item.id,
      case_key: item.case_key || item.key || item.id,
      image: item.image || item.cover || '',
      price: Number(item.price) || 0,
      hot_level: Number(item.hot_level) || 0,
      sell_count: Number(item.sell_count) || Number(item.open_count) || 0,
      is_hot: Boolean(item.is_hot) || item.tag === 'HOT',
      is_new: Boolean(item.is_new) || item.tag === 'new',
      discount: Number(item.discount) || 100,
      updated_at: item.updated_at || new Date().toISOString(),
      // 多语言字段
      name_en: item.name_en,
      name_zh_hans: item.name_zh_hans,
      description: item.description,
      description_en: item.description_en,
      description_zh_hans: item.description_zh_hans,
      cover: item.cover,
      tag: item.tag,
      tag_en: item.tag_en,
      tag_zh_hans: item.tag_zh_hans,
      open_count: Number(item.open_count) || Number(item.sell_count) || 0
    }))
  }

  // 转换开箱记录
  private transformCaseRecords(records: any[]): CaseRecord[] {
    return records.map(record => ({
      id: record.id || String(Math.random()),
      user: record.user || record.username || '匿名用户',
      case_name: record.case_name || record.boxName || '',
      skin_name: record.skin_name || record.skinName || record.name || '',
      skin_image: record.skin_image || record.image || '',
      case_image: record.case_image || record.boxImage || '',
      rarity: record.rarity || 'common',
      value: Number(record.value) || Number(record.price) || 0,
      timestamp: record.timestamp || Date.now()
    }))
  }

  // 通用API方法
  common = {
    // 获取网站统计数据
    getStats: async (): Promise<StatsData> => {
      try {
        const response = await this.fetchApi<ApiResponse<StatsData>>('/api/stats/')
        return response.body || response.data || {
          user_number: 0,
          case_number: 0,
          battle_number: 0,
          online_number: 0
        }
      } catch (error) {
        console.error('获取统计数据失败:', error)
        return {
          user_number: 0,
          case_number: 0,
          battle_number: 0,
          online_number: 0
        }
      }
    },

    // 获取最近开箱记录（全局）
    getRecentOpenings: async (limit: number = 10): Promise<CaseRecord[]> => {
      try {
        const response = await this.fetchApi<ApiResponse<any>>('/api/recent/', {
          query: { limit }
        })
        
        const records = response.body?.items || response.data?.items || response.body || response.data || []
        return this.transformCaseRecords(records)
      } catch (error) {
        console.error('获取最近开箱记录失败:', error)
        return []
      }
    }
  }

  // 首页API
  home = {
    // 获取首页所有数据
    getAllData: async (): Promise<HomeData> => {
      try {
        const [stats, hotCases, newCases, discountCases, recentOpenings] = await Promise.all([
          this.common.getStats(),
          this.home.getHotCases(5),
          this.home.getNewCases(5), 
          this.home.getDiscountCases(5),
          this.common.getRecentOpenings(10)
        ])

        return {
          todayOpened: stats.case_number,
          totalUsers: stats.user_number,
          totalOpenings: stats.case_number,
          onlineUsers: stats.online_number,
          hotCases,
          newCases,
          discountCases,
          recentOpenings
        }
      } catch (error) {
        console.error('获取首页数据失败:', error)
        throw error
      }
    },

    // 获取热门箱子（用于首页）
    getHotCases: async (num: number = 5): Promise<CaseItem[]> => {
      try {
        const response = await this.fetchApi<ApiResponse<any>>('/api/box/tag/', {
          query: { q: 'hot', num }
        })
        
        const items = response.body?.items || response.data?.items || response.body || response.data || []
        return this.transformCaseItems(items)
      } catch (error) {
        console.error('获取热门箱子失败:', error)
        throw error
      }
    },

    // 获取新品箱子（用于首页）
    getNewCases: async (num: number = 5): Promise<CaseItem[]> => {
      try {
        const response = await this.fetchApi<ApiResponse<any>>('/api/box/tag/', {
          query: { q: 'new', num }
        })
        
        const items = response.body?.items || response.data?.items || response.body || response.data || []
        return this.transformCaseItems(items)
      } catch (error) {
        console.error('获取新箱子失败:', error)
        throw error
      }
    },

    // 获取折扣箱子（用于首页）
    getDiscountCases: async (num: number = 5): Promise<CaseItem[]> => {
      try {
        const response = await this.fetchApi<ApiResponse<any>>('/api/box/tag/', {
          query: { q: 'SELL', num }
        })
        
        const items = response.body?.items || response.data?.items || response.body || response.data || []
        return this.transformCaseItems(items)
      } catch (error) {
        console.error('获取折扣箱子失败:', error)
        throw error
      }
    }
  }

  // Case相关API
  cases = {
    // 获取箱子分类
    getCaseCategories: async (): Promise<any[]> => {
      try {
        const response = await this.fetchApi<ApiResponse<{ items: any[] }>>('/api/box/category/')
        return response.body?.items || response.data?.items || []
      } catch (error) {
        console.error('获取箱子分类失败:', error)
        throw error
      }
    },

    // 获取热门箱子
    getHotCases: async (num: number = 10): Promise<CaseItem[]> => {
      try {
        const response = await this.fetchApi<ApiResponse<any>>('/api/box/tag/', {
          query: { q: 'hot', num }
        })
        
        const items = response.body?.items || response.data?.items || response.body || response.data || []
        return this.transformCaseItems(items)
      } catch (error) {
        console.error('获取热门箱子失败:', error)
        throw error
      }
    },

    // 获取新箱子
    getNewCases: async (num: number = 10): Promise<CaseItem[]> => {
      try {
        const response = await this.fetchApi<ApiResponse<any>>('/api/box/tag/', {
          query: { q: 'new', num }
        })
        
        const items = response.body?.items || response.data?.items || response.body || response.data || []
        return this.transformCaseItems(items)
      } catch (error) {
        console.error('获取新箱子失败:', error)
        throw error
      }
    },

    // 获取折扣箱子
    getDiscountCases: async (num: number = 10): Promise<CaseItem[]> => {
      try {
        const response = await this.fetchApi<ApiResponse<any>>('/api/box/tag/', {
          query: { q: 'sell', num }
        })
        
        const items = response.body?.items || response.data?.items || response.body || response.data || []
        return this.transformCaseItems(items)
      } catch (error) {
        console.error('获取折扣箱子失败:', error)
        throw error
      }
    },

    // 获取所有箱子 - 返回按分类组织的数据
    getAllCases: async (): Promise<any[]> => {
      try {
        const response = await this.fetchApi<ApiResponse<any>>('/api/box/search/')
        
        // API可能返回已按分类组织的数据结构
        const data = response.body?.items || response.data?.items || response.body || response.data || []
        
        // 如果数据已经是分类结构（包含cate_id），直接返回
        if (Array.isArray(data) && data.length > 0 && data[0].cate_id) {
          return data.map(category => ({
            ...category,
            cases: category.cases ? this.transformCaseItems(category.cases) : []
          }))
        }
        
        // 如果是平铺的箱子数据，按默认分类组织
        const transformedCases = this.transformCaseItems(data)
        return [{
          cate_id: 1,
          cate_name: '所有箱子',
          cate_name_en: 'All Cases',
          cases: transformedCases
        }]
      } catch (error) {
        console.error('获取所有箱子失败:', error)
        throw error
      }
    },

    // 获取箱子详情
    getCaseDetail: async (key: string): Promise<CaseItem | null> => {
      try {
        // 使用/api/box/detail/端点，保持与代理一致
        const response = await this.fetchApi<ApiResponse<any>>('/api/box/detail/', {
          query: { key }
        })
        
        const item = response.body || response.data
        if (!item) return null
        
        const transformed = this.transformCaseItems([item])
        return transformed[0] || null
      } catch (error) {
        console.error('获取箱子详情失败:', error)
        throw error
      }
    },

    // 获取箱子皮肤
    getCaseSkins: async (key: string): Promise<any[]> => {
      try {
        // 使用/api/box/skins/端点
        const response = await this.fetchApi<ApiResponse<any>>('/api/box/skins/', {
          query: { key }
        })
        
        const items = response.body?.items || response.data?.items || response.body || response.data || []
        return items
      } catch (error) {
        console.error('获取箱子皮肤失败:', error)
        throw error
      }
    },

    // 获取箱子开箱记录
    getCaseRecords: async (key: string): Promise<CaseRecord[]> => {
      try {
        // 使用/api/box/records/端点
        const response = await this.fetchApi<ApiResponse<any>>('/api/box/records/', {
          query: { key }
        })
        
        const records = response.body?.items || response.data?.items || response.body || response.data || []
        
        // 转换记录数据格式
        return records.map((record: any) => ({
          id: record.id || String(Math.random()),
          user: record.user || record.username || '匿名用户',
          case_name: record.case_name || '',
          skin_name: record.skin_name || record.name || '',
          skin_image: record.skin_image || record.image || '',
          case_image: record.case_image || '',
          rarity: record.rarity || 'common',
          value: Number(record.value) || Number(record.price) || 0,
          timestamp: record.timestamp || Date.now()
        }))
      } catch (error) {
        console.error('获取箱子开箱记录失败:', error)
        throw error
      }
    },

    // 开箱API - 根据old项目实现
    openCase: async (caseKey: string, count: number = 1): Promise<any> => {
      try {
        console.log('开箱API调用开始:', { caseKey, count })
        
        // 参数验证
        if (!caseKey) {
          throw new Error('箱子标识不能为空')
        }
        
        if (count <= 0 || count > 10) {
          throw new Error('开箱数量必须在1-10之间')
        }
        
        // 根据old项目的实现，使用POST方法调用/api/box/open/端点
        const requestData = { case_key: caseKey, count }
        console.log('开箱请求数据:', requestData)
        
        const response = await this.fetchApi<ApiResponse<any>>('/api/box/open/', {
          method: 'POST',
          body: JSON.stringify(requestData)
        })
        
        console.log('开箱API响应:', response)
        
        // 返回开箱结果
        const result = response.body || response.data
        if (result) {
          console.log('开箱成功:', result)
          return result
        } else {
          console.warn('开箱响应为空')
          return null
        }
      } catch (error) {
        console.error('开箱API调用失败:', error)
        throw error
      }
    }
  }
}

// 导出单例实例
export const commonApi = new CommonApi()

// 保持向后兼容性
export const api = commonApi
export const ApiService = CommonApi
export { CommonApi as default }
