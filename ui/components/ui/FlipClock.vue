<template>
  <div class="flip-clock-container">
    <div class="flip-clock">
      <!-- 小时十位 -->
      <div class="time-box">
        <div class="divider-bg"></div>
        <div class="divider-line"></div>
        <div class="base">
          {{ flipHours.toString().padStart(2, '0')[0] }}
          <div class="base-b">{{ flipHours.toString().padStart(2, '0')[0] }}</div>
        </div>
        <div 
          :class="['face', { anime: flipHourTrigger }]" 
          @animationend="onFlipAnimateEnd(0)"
        >
          {{ flipHours.toString().padStart(2, '0')[0] }}
        </div>
        <div :class="['back', { anime: flipHourTrigger }]">
          {{ flipHours.toString().padStart(2, '0')[0] }}
        </div>
      </div>
      <!-- 小时个位 -->
      <div class="time-box">
        <div class="divider-bg"></div>
        <div class="divider-line"></div>
        <div class="base">
          {{ flipHours.toString().padStart(2, '0')[1] }}
          <div class="base-b">{{ flipHours.toString().padStart(2, '0')[1] }}</div>
        </div>
        <div 
          :class="['face', { anime: flipHourTrigger }]" 
          @animationend="onFlipAnimateEnd(1)"
        >
          {{ flipHours.toString().padStart(2, '0')[1] }}
        </div>
        <div :class="['back', { anime: flipHourTrigger }]">
          {{ flipHours.toString().padStart(2, '0')[1] }}
        </div>
      </div>
      <div class="time-unit">:</div>
      <!-- 分钟十位 -->
      <div class="time-box">
        <div class="divider-bg"></div>
        <div class="divider-line"></div>
        <div class="base">
          {{ flipMinutes.toString().padStart(2, '0')[0] }}
          <div class="base-b">{{ flipMinutes.toString().padStart(2, '0')[0] }}</div>
        </div>
        <div 
          :class="['face', { anime: flipMinuteTrigger }]" 
          @animationend="onFlipAnimateEnd(2)"
        >
          {{ flipMinutes.toString().padStart(2, '0')[0] }}
        </div>
        <div :class="['back', { anime: flipMinuteTrigger }]">
          {{ flipMinutes.toString().padStart(2, '0')[0] }}
        </div>
      </div>
      <!-- 分钟个位 -->
      <div class="time-box">
        <div class="divider-bg"></div>
        <div class="divider-line"></div>
        <div class="base">
          {{ flipMinutes.toString().padStart(2, '0')[1] }}
          <div class="base-b">{{ flipMinutes.toString().padStart(2, '0')[1] }}</div>
        </div>
        <div 
          :class="['face', { anime: flipMinuteTrigger }]" 
          @animationend="onFlipAnimateEnd(3)"
        >
          {{ flipMinutes.toString().padStart(2, '0')[1] }}
        </div>
        <div :class="['back', { anime: flipMinuteTrigger }]">
          {{ flipMinutes.toString().padStart(2, '0')[1] }}
        </div>
      </div>
      <div class="time-unit">:</div>
      <!-- 秒钟十位（静态显示） -->
      <div class="time-box">
        <div class="divider-bg"></div>
        <div class="divider-line"></div>
        <div class="base">
          {{ flipSeconds.toString().padStart(2, '0')[0] }}
          <div class="base-b">{{ flipSeconds.toString().padStart(2, '0')[0] }}</div>
        </div>
        <div class="face">
          {{ flipSeconds.toString().padStart(2, '0')[0] }}
        </div>
        <div class="back">
          {{ flipSeconds.toString().padStart(2, '0')[0] }}
        </div>
      </div>
      <!-- 秒钟个位（动态翻页） -->
      <div class="time-box">
        <div class="divider-bg"></div>
        <div class="divider-line"></div>
        <div class="base">
          {{ flipSeconds.toString().padStart(2, '0')[1] }}
          <div class="base-b">{{ flipSeconds.toString().padStart(2, '0')[1] }}</div>
        </div>
        <div 
          :class="['face', { anime: flipSecondTrigger }]" 
          @animationend="onFlipAnimateEnd(4)"
        >
          {{ flipSeconds.toString().padStart(2, '0')[1] }}
        </div>
        <div :class="['back', { anime: flipSecondTrigger }]">
          {{ flipSeconds.toString().padStart(2, '0')[1] }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

// 国际化
const { t } = useI18n()

// Props
interface Props {
  timezone?: string
  showSeconds?: boolean
  size?: 'small' | 'medium' | 'large'
}

const props = withDefaults(defineProps<Props>(), {
  timezone: 'Asia/Shanghai',
  showSeconds: true,
  size: 'medium'
})

// Emits
const emit = defineEmits<{
  timeUpdate: [time: Date]
  animationEnd: [index: number]
}>()

// 响应式变量
const flipHours = ref(new Date().getHours())
const flipMinutes = ref(new Date().getMinutes())
const flipSeconds = ref(new Date().getSeconds())

// 翻页动画触发器
const flipHourTrigger = ref(false)
const flipMinuteTrigger = ref(false)
const flipSecondTrigger = ref(false)

let timeInterval: ReturnType<typeof setInterval> | null = null

// 动画结束处理
const onFlipAnimateEnd = (index: number) => {
  // 动画结束后重置触发器
  if (index === 0 || index === 1) {
    flipHourTrigger.value = false
  } else if (index === 2 || index === 3) {
    flipMinuteTrigger.value = false
  } else if (index === 4) {
    flipSecondTrigger.value = false
  }
  
  emit('animationEnd', index)
}

// 更新时间
const updateTime = () => {
  const now = new Date()
  
  // 检测时间变化并触发翻页动画
  const newHours = now.getHours()
  const newMinutes = now.getMinutes()
  const newSeconds = now.getSeconds()
  
  // 检测小时变化
  if (newHours !== flipHours.value) {
    flipHourTrigger.value = true
    setTimeout(() => {
      flipHours.value = newHours
      flipHourTrigger.value = false
    }, 600) // 匹配动画时长
  }
  
  // 检测分钟变化
  if (newMinutes !== flipMinutes.value) {
    flipMinuteTrigger.value = true
    setTimeout(() => {
      flipMinutes.value = newMinutes
      flipMinuteTrigger.value = false
    }, 600) // 匹配动画时长
  }
  
  // 检测秒钟变化
  if (newSeconds !== flipSeconds.value) {
    flipSecondTrigger.value = true
    setTimeout(() => {
      flipSeconds.value = newSeconds
      flipSecondTrigger.value = false
    }, 600) // 匹配动画时长
  }
  
  emit('timeUpdate', now)
}

// 生命周期
onMounted(() => {
  // 初始化时间
  updateTime()
  
  // 启动定时器
  timeInterval = setInterval(updateTime, 1000)
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})
</script>

<style lang="scss" scoped>
.flip-clock-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 1rem 0;
  overflow: hidden;
}

.flip-clock {
  @apply flex items-center gap-1 flex-nowrap justify-center;
  font-family: 'SF Mono', 'Consolas', 'Menlo', monospace;
  
  .time-box {
    @apply relative box-border h-20 min-w-[3.5rem] text-center bg-gray-900 rounded-lg text-white;
    font-size: 1.75rem;
    perspective: 12.5rem;
    line-height: 5rem;
    box-shadow: 0 0.25rem 0.5rem rgba(0,0,0,0.3), 0 0.125rem 0.25rem rgba(0,0,0,0.2), inset 0 0.0625rem 0 rgba(255,255,255,0.1);
    border: 0.0625rem solid #333;
    will-change: transform;
    backface-visibility: hidden;
    transform-style: preserve-3d;
    
    &:before {
      content: "";
      @apply absolute bg-gray-900 w-1 h-4 top-1/2 -left-1 -mt-2 -z-10 rounded-l-sm;
      box-shadow: inset 0 0.0625rem 0 rgba(255,255,255,0.1);
    }
    &:after {
      content: "";
      @apply absolute bg-gray-900 w-1 h-4 top-1/2 -right-1 -mt-2 -z-10 rounded-r-sm;
      box-shadow: inset 0 0.0625rem 0 rgba(255,255,255,0.1);
    }
    .divider-bg {
      @apply absolute top-1/2 left-0 right-0 h-px bg-gray-700 z-10;
    }
    .divider-line {
      @apply absolute top-1/2 left-0 right-0 h-px z-20;
      background: linear-gradient(90deg, transparent, #666, transparent);
      box-shadow: 0 0 0.25rem rgba(255,255,255,0.1);
    }
    & > div {
      @apply overflow-hidden rounded-lg;
      animation-timing-function: cubic-bezier(0.4,0,0.2,1);
      animation-duration: 600ms;
      transform: rotateX(0deg);
      will-change: transform;
      backface-visibility: hidden;
      &.base {
        @apply relative;
        background: linear-gradient(180deg, #2a2a2a 0%, #1a1a1a 50%, #0a0a0a 100%);
        .base-b {
          @apply absolute left-0 bottom-0 rounded-b-lg w-full h-full;
          background: linear-gradient(180deg, #0a0a0a 0%, #1a1a1a 50%, #2a2a2a 100%);
          clip-path: polygon(0 50%, 100% 50%, 100% 100%, 0 100%);
        }
      }
      &.face {
        @apply absolute left-0 top-0 w-full h-full z-20;
        background: linear-gradient(180deg, #2a2a2a 0%, #1a1a1a 50%, #0a0a0a 100%);
        backface-visibility: hidden;
        clip-path: polygon(0 0, 100% 0, 100% 50%, 0 50%);
        box-shadow: inset 0 0.0625rem 0 rgba(255,255,255,0.1);
        will-change: transform;
        transform-origin: 50% 0%;
        &.anime {
          animation-name: animate-flip-face;
        }
      }
      &.back {
        @apply absolute left-0 top-0 w-full h-full;
        background: linear-gradient(180deg, #0a0a0a 0%, #1a1a1a 50%, #2a2a2a 100%);
        transform: rotateX(-180deg);
        backface-visibility: hidden;
        clip-path: polygon(0 50%, 100% 50%, 100% 100%, 0 100%);
        box-shadow: inset 0 -0.0625rem 0 rgba(255,255,255,0.1);
        will-change: transform;
        transform-origin: 50% 100%;
        &.anime {
          animation-name: animate-flip-back;
        }
      }
    }
  }
  .time-unit {
    @apply px-2 text-primary font-bold whitespace-nowrap;
    font-size: 1.5rem;
    line-height: 5rem;
    text-shadow: 0 0 0.625rem rgba(0,168,255,0.5), 0 0 1.25rem rgba(0,168,255,0.3);
    animation: separator-pulse 2s ease-in-out infinite;
  }
}
@keyframes animate-flip-face {
  0% { transform: rotateX(0deg); opacity: 1; }
  50% { transform: rotateX(-90deg); opacity: 0.5; }
  100% { transform: rotateX(-180deg); opacity: 0; }
}
@keyframes animate-flip-back {
  0% { transform: rotateX(180deg); opacity: 0; }
  50% { transform: rotateX(90deg); opacity: 0.5; }
  100% { transform: rotateX(0deg); opacity: 1; }
}
@keyframes separator-pulse {
  0%, 100% {
    opacity: 0.9;
    transform: scale(1);
    text-shadow: 0 0 10px rgba(0,168,255,0.5), 0 0 20px rgba(0,168,255,0.3);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.1);
    text-shadow: 0 0 15px rgba(0,168,255,0.7), 0 0 30px rgba(0,168,255,0.5), 0 0 45px rgba(0,168,255,0.3);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .flip-clock {
    @apply gap-0.5;
    
    .time-box {
      @apply h-16 min-w-[2.5rem] px-1;
      font-size: 1.25rem;
      line-height: 4rem;
      
      &:before, &:after {
        @apply w-0.5 h-3 -left-0.5 -right-0.5 -mt-1.5;
      }
    }
    
    .time-unit {
      @apply px-1;
      font-size: 1rem;
      line-height: 4rem;
    }
  }
}
</style> 