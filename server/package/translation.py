from modeltranslation.translator import register, TranslationOptions
from package.models import ItemInfo, ItemCategory, ItemQuality, ItemRarity, ItemExterior

@register(ItemInfo)
class ItemInfoTranslationOptions(TranslationOptions):
    fields = ('name',)  # 暂时移除 description 和 content 字段，等待 CKEditor5 翻译支持


@register(ItemCategory)
class ItemCategoryTranslationOptions(TranslationOptions):
    fields = ('cate_name',)

@register(ItemQuality)
class ItemQualityTranslationOptions(TranslationOptions):
    fields = ('quality_name',)

@register(ItemRarity)
class ItemRarityTranslationOptions(TranslationOptions):
    fields = ('rarity_name',)

@register(ItemExterior)
class ItemExteriorTranslationOptions(TranslationOptions):
    fields = ('exterior_name',)