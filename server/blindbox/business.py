import hashlib
import json
import logging
from pprint import pprint
from random import shuffle

from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.core.paginator import Paginator
from django.db import transaction
from django.utils.translation import gettext_lazy as _
from django_redis import get_redis_connection

from blindbox.models import BlindBox, BlindBoxType, FreeCaseConfig, BlindBoxDrop, BlindBoxRecord, BlindBoxGame
from blindbox.serializers import Blind<PERSON>oxSerializer, FreeCaseConfigSerializer, BlindBoxDropItemSerializer, \
    BlindBoxRecordSerializer
from package.interfaces import get_item_price_by_name
from package.models import ItemInfo, PackageItem
from package.serializers import ItemInfoSerializer
from steambase.enums import RespCode, CaseCategoryType, PackageState
from steambase.utils import id_generator

_logger = logging.getLogger(__name__)
_box_redis_channel_key = 'ws_channel'
USER = get_user_model()


def ws_send_box_game(data, action):
    if data:
        r = get_redis_connection('default')
        rt_msg = ['box', action, data]
        r.publish(_box_redis_channel_key, json.dumps(rt_msg))


def get_user_level(user):
    user_level = 0
    charge_level = user.charge_level.all()
    if len(charge_level) > 0:
        user_level = charge_level[0].level
    if user_level < 1:
        return 0
    else:
        return user_level


def update_user_freebox_count(user):
    user_level = get_user_level(user)
    if user_level < 1:
        return True
    if user_level == 1:
        user.extra.update_freebox_lv1_count(user, 1)
    if user_level == 2:
        user.extra.update_freebox_lv2_count(user, 1)
    if user_level == 3:
        user.extra.update_freebox_lv3_count(user, 1)
    if user_level == 4:
        user.extra.update_freebox_lv4_count(user, 1)
    if user_level == 5:
        user.extra.update_freebox_lv5_count(user, 1)
    return False


def user_open_blind_case(user, key, gid, pos):
    _logger.info('{} open case {} gameid:{} position:{}'.format(user, key, gid, pos))
    case = BlindBox.objects.filter(key=key, enable=True).first()
    if not case or not case.enable or not case.unlock:
        return RespCode.InvalidGame.value, _('Case is blocked, please open other case.')
    game_key = "blindbox:{}:{}".format(user, key)
    game = cache.get(game_key)
    if not game:
        return RespCode.NotFound.value, _('该轮游戏已结束，请更换盒子')
    if pos is None or pos not in [index for index, d in enumerate(case.blind_drops.all())]:
        return RespCode.InvalidParams.value, _('位置错误，请重新选择')
    position = {}
    for p in game['pos']:
        (key, value), = p.items()
        position[key] = value
    if position[pos]['is_opened']:
        return RespCode.InvalidParams.value, _("该位置已开启")
    win_items = []
    send_box_game_list = []

    with transaction.atomic():
        if case.case_type.category == CaseCategoryType.Free.value:
            is_opened = update_user_freebox_count(user)
            if is_opened:
                return RespCode.InvalidGame.value, _('Invalid count')
        else:
            cost = round(case.price * case.discount / 100, 2)
            user.update_balance(-cost, '开盲盒')
        drops = game['drop']
        item_data = {
            'uid': drops[pos]['uid'],
            'price': get_item_price_by_name(drops[pos]['market_hash_name'])
        }
        item_info = ItemInfo.objects.filter(market_hash_name=drops[pos]['market_hash_name']).first()
        item_data = dict(item_data, **(ItemInfoSerializer(item_info).data))

        package = PackageItem.objects.create(user=user, item_info=item_info, assetid='0',
                                             instanceid='0', state=PackageState.Available.value,
                                             amount=item_data['price'],
                                             case_name=case.name, case_cover=case.cover)
        item_data['pid'] = package.uid

        # 生成游戏记录
        box_game, state = BlindBoxGame.objects.get_or_create(user=user, gid=gid, seed=game['seed'])
        # 生成开箱记录
        record = BlindBoxRecord.objects.create(user=user, box=case, item_info=item_info, price=item_data['price'],
                                               index=pos, game=box_game)
        record_data = BlindBoxRecordSerializer(record, read_only=True, exclude=('code', 'ecode', 'secret')).data
        send_box_game_list.append(record_data)
        win_items.append(item_data)
        # 更新缓存, 如果全部开完则删除缓存
        is_opened_list = []
        for pos_data in game['pos']:
            (key, value), = pos_data.items()
            if key == pos:
                pos_data[pos].update({
                    'is_opened': True,
                    'drop': game['drop'][pos]
                })
            is_opened_list.append(value['is_opened'])
        if all(is_opened_list):
            cache.delete(game_key)
        else:
            if cache.ttl(game_key):
                cache.set(game_key, game, cache.ttl(game_key))
            else:
                cache.set(game_key, game, case.limited)
    for game_data in send_box_game_list:
        ws_send_box_game(game_data, 'new')

    return RespCode.Succeed.value, win_items


def get_blindbox_list(user, query, fields):
    case_list = cache.get('caselist')
    if not case_list:
        case_list = {
            'top': [],
            # 'free': None,
            'normal': [],
            'freegive': []
        }
        top = BlindBoxType.objects.filter(category=CaseCategoryType.Top.value).first()
        top_case = BlindBox.objects.filter(case_type=top, enable=True, **query).order_by('order')
        if top_case:
            case_list['top'] = BlindBoxSerializer(top_case, many=True, fields=fields).data

        normal_type = BlindBoxType.objects.filter(category=CaseCategoryType.Normal.value).order_by('order')
        # case_list['normal'] = CaseSerializer(normal_case, many=True, fields=fields).data
        for type in normal_type:
            normal_case = BlindBox.objects.filter(case_type=type, enable=True, **query).order_by('order')
            if normal_case:
                case_list['normal'].append({
                    'type': type.name,
                    'case': BlindBoxSerializer(normal_case, many=True, fields=fields).data
                })

        freegive_type = BlindBoxType.objects.filter(category=CaseCategoryType.FreeGive.value).order_by('order')
        for _type in freegive_type:
            freegive_case = BlindBox.objects.filter(case_type=_type, enable=True, **query).order_by('order')
            if freegive_case:
                case_list['freegive'].append({
                    'type': _type.name,
                    'case': BlindBoxSerializer(freegive_case, many=True, fields=fields).data
                })
        cache.set('caselist', case_list, 5 * 60)
    resp = {
        'case_list': case_list
    }
    return RespCode.Succeed.value, resp


def get_free_blindbox_list(user, query, fields):
    free_case_list = cache.get('freecaselist')
    if not free_case_list:
        free_case_list = []
        free_case = FreeCaseConfig.objects.filter(**query).order_by('level')
        if free_case:
            free_case_list = FreeCaseConfigSerializer(free_case, many=True, fields=fields).data
        cache.set('freecaselist', free_case_list, settings.DAY_REDIS_TIMEOUT)

    resp = {
        'free_case_list': free_case_list,
    }
    return RespCode.Succeed.value, resp


def get_blindbox_detail(user, key, query, fields):
    if not BlindBox.objects.filter(enable=True, key=key).exists():
        return RespCode.InvalidParams.value, _('Invalid case key')

    case = BlindBox.objects.filter(key=key, enable=True).first()
    case_data = BlindBoxSerializer(case, fields=fields).data

    # if case_data.get('drops', None):
    #     drops = []
    #     drops_name = []
    #     for drop in case_data['drops']:
    #         name = "".join(drop['market_name_cn'].split('(')[:-1])
    #         if not name:
    #             name = drop['market_name_cn']
    #         if name not in drops_name:
    #             drops_item = case.drops.filter(case=case, item_info__market_name_cn__startswith=name)
    #             drops_data = DropItemSerializer(drops_item, fields=('icon_url','market_name_cn','uid', 'show_chance','rarity_color', 'price', 'id'), many=True, read_only=True).data
    #             for d in drops_data:
    #                 d['price'] = get_drop_price(DropItem.objects.filter(uid=d['uid']).first())
    #             data = {
    #                 'name': name,
    #                 'drops': drops_data,
    #                 'show_chance_total': sum([item['show_chance'] for item in drops_data])
    #             }
    #             drops_name.append(name)
    #             drops.append(data)
    #     sorted(drops, key=lambda i: i['show_chance_total'], reverse=False)
    #     case_data['drops'] = drops
    #     cache.set('case:{}'.format(key), case_data, settings.DAY_REDIS_TIMEOUT)
    resp = {
        'case': case_data
    }

    return RespCode.Succeed.value, resp


def get_blindbox_current(user, key):
    # if not user:
    #     return RespCode.NotLogin.value, _('请先登录')
    if not BlindBox.objects.filter(enable=True, key=key).exists():
        return RespCode.InvalidParams.value, _('参数错误')

    case = BlindBox.objects.filter(key=key, enable=True).first()
    # 限制时间为0则不过期
    detail_key = "blindbox:{}:{}".format(user, key)
    fields = ('market_hash_name', 'market_name_cn', 'icon_url', 'icon_url_large', 'rarity_color', 'uid')
    game = cache.get(detail_key)
    if not game:
        drops = BlindBoxDropItemSerializer(case.blind_drops.all(), many=True, read_only=True, fields=fields).data
        # 随机打乱
        shuffle(drops)
        # 生成随机数种子
        seed = id_generator(8)
        # 游戏id
        gid = id_generator(12)
        game = {
            'game': gid,
            'pos': [],
            'drop': {},
            'seed': seed,
            'timestamp': 0
        }
        md5 = hashlib.md5()

        for index, drop in enumerate(drops):
            md5.update(drop['market_hash_name'].encode('utf-8'))
            base_secret = md5.hexdigest()
            secret_pre = "{}:{}:{}:{}".format(base_secret, seed, gid, index)
            md5.update(secret_pre.encode('utf-8'))
            # drop.update(secret=md5.hexdigest())
            game['pos'].append({
                    'secret': md5.hexdigest(),
                    'is_opened': False
            })
            game['drop'].update({index: drop})
        cache.set(detail_key, game, None)
    game.update(timestamp=cache.ttl(detail_key))
    game.pop('drop')
    game.pop('seed')
    # 分页
    total = len(game['pos'])
    # data_group = [game['pos'][i:i + page_size] for i in range(0, total + 1, page_size)]
    # game['pos'] = data_group[page - 1]
    resp = {
        'data': game,
        'total': total,
        # 'page': page,
        # 'limit': page_size
    }
    return RespCode.Succeed, resp


def get_blindbox_record(user, fields, page, page_size):
    # TODO: 随机码展示
    queryset = BlindBoxRecord.objects.filter(user=user).order_by('-create_time')
    paginator = Paginator(queryset, page_size)
    records = paginator.page(page)
    records_data = BlindBoxRecordSerializer(records, many=True, fields=fields).data
    resp = {
        'records': records_data,
        'total': paginator.count
    }
    return RespCode.Succeed.value, resp


def get_blindbox_recent():
    pass


def replace_current_game(user, key):
    if not BlindBox.objects.filter(enable=True, key=key).exists():
        return RespCode.InvalidParams.value, _('参数错误')
    case = BlindBox.objects.filter(key=key, enable=True).first()
    detail_key = "blindbox:{}:{}".format(user, key)
    fields = ('market_hash_name', 'market_name_cn', 'icon_url', 'icon_url_large', 'rarity_color', 'uid')
    drops = BlindBoxDropItemSerializer(case.blind_drops.all(), many=True, read_only=True, fields=fields).data
    # 随机打乱
    shuffle(drops)
    # 生成随机数种子
    seed = id_generator(8)
    # 游戏id
    gid = id_generator(12)
    game = {
        'game': gid,
        'pos': [],
        'drop': {},
        'seed': seed,
        'timestamp': 0
    }
    md5 = hashlib.md5()

    for index, drop in enumerate(drops):
        md5.update(drop['market_hash_name'].encode('utf-8'))
        base_secret = md5.hexdigest()
        secret_pre = "{}:{}:{}:{}".format(base_secret, seed, gid, index)
        md5.update(secret_pre.encode('utf-8'))
        # drop.update(secret=md5.hexdigest())
        game['pos'].append({
            index: {
                'secret': md5.hexdigest(),
                'is_opened': False
            }
        })
        game['drop'].update({index: drop})
    cache.set(detail_key, game, None)
    return RespCode.Succeed, {}