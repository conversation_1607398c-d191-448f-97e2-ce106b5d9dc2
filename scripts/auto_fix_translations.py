#!/usr/bin/env python
"""
自动修复Django模型翻译问题
"""

import os
import sys
import re
import django
from pathlib import Path

# 设置Django环境
sys.path.append('/www/wwwroot/csgoskins.com.cn/server')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')
django.setup()

# 翻译映射表
TRANSLATION_MAP = {
    # 通用字段
    'user': '用户',
    'amount': '金额',
    'price': '价格',
    'name': '名称',
    'title': '标题',
    'content': '内容',
    'description': '描述',
    'status': '状态',
    'state': '状态',
    'type': '类型',
    'category': '分类',
    'tag': '标签',
    'image': '图片',
    'url': '链接',
    'email': '邮箱',
    'phone': '电话',
    'address': '地址',
    'count': '数量',
    'level': '等级',
    'score': '分数',
    'rank': '排名',
    'order': '订单',
    'date': '日期',
    'time': '时间',
    'create_time': '创建时间',
    'update_time': '更新时间',
    'start_time': '开始时间',
    'end_time': '结束时间',
    
    # 业务相关
    'CDKey': 'CDKey',
    'CDKey info': 'CDKey信息',
    'CDKey record': 'CDKey兑换记录',
    'generate CDKey': '生成CDKey',
    'Charge Record': '充值记录',
    'PayMethod': '支付方式',
    'Trade Record': '交易记录',
    'Market Item': '市场物品',
    'User Profile': '用户资料',
    'Chat Message': '聊天消息',
    'Box Item': '箱子物品',
    'Lucky Box': '幸运箱',
    'Lottery': '抽奖',
    'Promotion': '促销活动',
    'Agent': '代理',
    'Withdraw': '提现',
    'Package': '包裹',
    'Roll Game': '滚轮游戏',
    'Crash Game': '崩盘游戏',
    'Grab Game': '抢夺游戏',
    'Blind Box': '盲盒',
    'Custom Box': '自定义箱子',
    'Envelope': '红包',
    'Trade Up': '合成',
    'Site Config': '站点配置',
    'Article': '文章',
    'Content': '内容',
    'Category': '分类',
    'Tag': '标签',

    # 更多业务模型
    'Profile': '用户资料',
    'Asset': '资产',
    'Steam': 'Steam账户',
    'Extra': '扩展信息',
    'User Balance Record': '用户余额记录',
    'User Points Record': '用户积分记录',
    'User Diamond Record': '用户钻石记录',
    'User Active Point Record': '用户活跃积分记录',
    'Auth User': '认证用户',
    'User Statistics Day': '用户日统计',
    'Phone Code Record': '手机验证码记录',
    'Steam Black List': 'Steam黑名单',
    'Email Suffix': '邮箱后缀',
    'User Promotion': '用户推广',
    'Promotion Record': '推广记录',
    'Promotion Level Config': '推广等级配置',
    'PromotionCaseConfig': '推广箱子配置',
    'Site Config Category': '站点配置分类',
    'Announcee': '公告',
    'Footer': '页脚',
    'FAQ': '常见问题',
    'Support': '客服支持',
    'Banner': '横幅',
    'Article Category': '文章分类',
    'SEO': 'SEO设置',
    'Content Category': '内容分类',
    'Content Tag': '内容标签',
    'Content Attachment': '内容附件',
    'Content View': '内容浏览',
    'Item Info': '物品信息',
    'Item Category': '物品分类',
    'Item Quality': '物品品质',
    'Item Rarity': '物品稀有度',
    'Item Exterior': '物品磨损',
    'Item Price': '物品价格',
    'Package Item': '包裹物品',
    'Item Price Rate Config': '物品价格比率配置',
    'Exchange Record': '兑换记录',
    'Shop record': '商店记录',
    'Trade Item': '交易物品',
    'Trade Bot Config': '交易机器人配置',
    'Trade Bot Inventory': '交易机器人库存',
    'Shop Bot Config': '商店机器人配置',
    'BlackList': '黑名单',
    'Item Statistics Day': '物品日统计',
    'Item Statistics Month': '物品月统计',
    'Item Unlock Time Config': '物品解锁时间配置',
    'Lock Items Statistics': '锁定物品统计',
    'Item Whitelist': '物品白名单',
    'Case Type': '箱子类型',
    'BlindBox': '盲盒',
    'BlindBoxDrop': '盲盒掉落',
    'BlindBoxGame': '盲盒游戏',
    'Blind Box Record': '盲盒记录',
    'Free Case Config': '免费箱子配置',
    'Case Bot Config': '箱子机器人配置',
    'Case Category': '箱子分类',
    'Case': '箱子',
    'Drop Item': '掉落物品',
    'Case Room': '箱子房间',
    'Case Room Round': '箱子房间轮次',
    'Case Room Bet': '箱子房间下注',
    'Case Room Item': '箱子房间物品',
    'Case Record': '箱子记录',
    'Case Statistics Day': '箱子日统计',
    'Case Statistics Month': '箱子月统计',
    'Festival Case Config': '节日箱子配置',
    'Festival Case Date': '节日箱子日期',
    'Festival Case Record': '节日箱子记录',
    'Drop Day Rank': '掉落日排行',
    'Income Day Rank': '收入日排行',
    'Room Day Rank': '房间日排行',
    'Lose Week Rank': '亏损周排行',
    'CaseKeyConfig': '箱子钥匙配置',
    'GiveawayItems': '赠送物品',
    'GrabRoom': '抢夺房间',
    'GrabBet': '抢夺下注',
    'GrabCard': '抢夺卡片',
    'grab history': '抢夺历史',
    'LuckyBox Group': '幸运箱组',
    'LuckyBox Recommend Group': '幸运箱推荐组',
    'LuckyBox Item': '幸运箱物品',
    'LuckyBoxCategory': '幸运箱分类',
    'LuckyBox Record': '幸运箱记录',
    'CustomBox': '自定义箱子',
    'CustomDropItem': '自定义掉落物品',
    'CustomBoxCover': '自定义箱子封面',
    'CustomBoxItem': '自定义箱子物品',
    'CustomBoxItemInfo': '自定义箱子物品信息',
    'MarketItem': '市场物品',
    'Tradeup Game': '合成游戏',
    'Tradeup Bet Item': '合成下注物品',
    'Coins Tradeup Bet amount': '金币合成下注金额',
    'Tradeup Target Item': '合成目标物品',
    'Tradeup Inventory Item': '合成库存物品',
    'Tradeup Pump Day': '合成抽水日统计',
    'Tradeup Pump Month': '合成抽水月统计',
    'Tradeup Statistics Day': '合成日统计',
    'Tradeup Statistics Month': '合成月统计',
    'Tradeup Win Day Rank': '合成赢取日排行',
    'Tradeup Win Week Rank': '合成赢取周排行',
    'Tradeup Bot Config': '合成机器人配置',
    'Crash Bet': '崩盘下注',
    'Crash Pump Day': '崩盘抽水日统计',
    'Crash Pump Month': '崩盘抽水月统计',
    'Crash Statistics Day': '崩盘日统计',
    'Crash Statistics Month': '崩盘月统计',
    'Crash Win Day Rank': '崩盘赢取日排行',
    'Crash Win Week Rank': '崩盘赢取周排行',
    'Charge Statistics Day': '充值日统计',
    'Charge Statistics Month': '充值月统计',
    'Charge Week Rank': '充值周排行',
    'Charge Level': '充值等级',
    'Charge Level Config': '充值等级配置',
    'Cxka goods': 'Cxka商品',
    'Cxka Generate': 'Cxka生成',
    'ChargeAmountConfig': '充值金额配置',
    'Message': '消息',
    'Chat Bot Message': '聊天机器人消息',
    'Chat Bot Config': '聊天机器人配置',
    'Lottery Setting': '抽奖设置',
    'Lottery Joiner': '抽奖参与者',
    'Lottery Info Setting': '抽奖信息设置',
    'EnvelopeRule': '红包规则',
    'EnvelopeRecord': '红包记录',
    'Withdraw Item': '提现物品',
    'Wxp Trade Offer': 'Waxpeer交易报价',
    'Trade Statistics Day': '交易日统计',
    'Trade Statistics Month': '交易月统计',
    'Roll Room': '滚轮房间',
    'Roll Room Bet': '滚轮房间下注',
    'Roll Room Item': '滚轮房间物品',
    'Roll Room Pump Day': '滚轮房间抽水日统计',
    'Roll Room Pump Month': '滚轮房间抽水月统计',
    'Roll房机器人': '滚轮房间机器人',
    'Rollroom Bot Config': '滚轮房间机器人配置',
    'Rollroom Statistics Day': '滚轮房间日统计',
    'Rollroom Statistics Month': '滚轮房间月统计',
    'B2C Market Item': 'B2C市场物品',
    'ZBT Trade Record': 'ZBT交易记录',
    'ZBTBlackList': 'ZBT黑名单',
    'B2COfficialTrade Record': 'B2C官方交易记录',
    'B2C Item list': 'B2C物品列表',
    'B2COfficial Account': 'B2C官方账户',
    'B2CTrade Statistics Day': 'B2C交易日统计',
    'B2CTrade Statistics Month': 'B2C交易月统计',
    'Agent Withdrawal Order': '代理提现订单',
    'Agent Balance Record': '代理余额记录',
    
    # 状态相关
    'active': '激活',
    'inactive': '未激活',
    'pending': '待处理',
    'approved': '已批准',
    'rejected': '已拒绝',
    'completed': '已完成',
    'cancelled': '已取消',
    'processing': '处理中',
    'success': '成功',
    'failed': '失败',
    'enabled': '启用',
    'disabled': '禁用',
    'published': '已发布',
    'draft': '草稿',
    'archived': '已归档',
    'used': '已使用',
    'unused': '未使用',
}

def fix_file_translations(file_path):
    """修复单个文件的翻译问题"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        fixes_count = 0
        
        # 修复 verbose_name = _("...") 模式
        for original, translation in TRANSLATION_MAP.items():
            # 匹配 verbose_name = _("original")
            pattern1 = rf'verbose_name\s*=\s*_\(["\']({re.escape(original)})["\']\)'
            replacement1 = f'verbose_name = "{translation}"'
            content, count1 = re.subn(pattern1, replacement1, content)
            fixes_count += count1
            
            # 匹配字段中的 verbose_name=_("original")
            pattern2 = rf'verbose_name\s*=\s*_\(["\']({re.escape(original)})["\']\)'
            replacement2 = f'verbose_name="{translation}"'
            content, count2 = re.subn(pattern2, replacement2, content)
            fixes_count += count2
        
        # 如果有修改，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return fixes_count
        
        return 0
        
    except Exception as e:
        print(f"❌ 修复文件失败 {file_path}: {e}")
        return 0

def main():
    print("🔧 开始批量修复翻译问题...")
    
    # 需要修复的文件列表 - 扩展版本
    files_to_fix = [
        'tradeup/models.py',
        'b2ctrade/models.py',
        'promotion/models.py',
        'envelope/models.py',
        'agent/models.py',
        'roll/models.py',
        'sitecfg/models.py',
        'chat/models.py',
        'charge/models.py',
        'blindbox/models.py',
        'articles/models.py',
        'authentication/models.py',
        'package/models.py',
        'withdraw/models.py',
        'box/models.py',
        'custombox/models.py',
        'lottery/models.py',
        'crash/models.py',
        'grab/models.py',
        'luckybox/models.py',
        'market/models.py',
    ]
    
    total_fixes = 0
    fixed_files = 0
    
    server_path = Path('/www/wwwroot/csgoskins.com.cn/server')
    
    for file_rel_path in files_to_fix:
        file_path = server_path / file_rel_path
        if file_path.exists():
            fixes = fix_file_translations(file_path)
            if fixes > 0:
                print(f"✅ {file_rel_path}: 修复了 {fixes} 个翻译问题")
                total_fixes += fixes
                fixed_files += 1
            else:
                print(f"⚪ {file_rel_path}: 无需修复")
        else:
            print(f"❌ {file_rel_path}: 文件不存在")
    
    print(f"\n🎉 批量修复完成!")
    print(f"📊 总计修复了 {fixed_files} 个文件中的 {total_fixes} 个翻译问题")
    
    return total_fixes

if __name__ == "__main__":
    main()
