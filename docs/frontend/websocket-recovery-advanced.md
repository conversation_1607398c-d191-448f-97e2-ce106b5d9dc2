# WebSocket重连高级容错机制

## 概述

本文档描述了前端WebSocket重连的高级容错机制，配合后端API的响应数据完整性检查，形成了完整的容错体系。

## 核心特性

- ✅ **智能重试机制**: 最多3次重试，指数退避延迟
- ✅ **请求超时控制**: 5秒超时防止长时间等待
- ✅ **数据完整性验证**: 客户端验证响应数据格式
- ✅ **优雅降级**: 重试失败时的用户友好提示
- ✅ **详细日志**: 完整的错误跟踪和调试信息

## 实现代码

### 1. 增强版WebSocket重连管理器

```javascript
class WebSocketReconnectionManager {
    constructor(websocket, animationController) {
        this.websocket = websocket;
        this.animationController = animationController;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.currentRoom = null;
        this.isReconnecting = false;
        this.recoveryConfig = {
            maxRetries: 3,
            requestTimeout: 5000,
            enableExponentialBackoff: true,
            maxBackoffDelay: 5000
        };
        
        this.setupReconnectionHandlers();
    }
    
    // ✅ 增强的动画状态恢复方法
    async recoverAnimationState(roomUid) {
        const { maxRetries } = this.recoveryConfig;
        let attempt = 0;
        
        while (attempt < maxRetries) {
            try {
                console.log(`[动画恢复] 尝试恢复房间 ${roomUid} 的动画状态 (${attempt + 1}/${maxRetries})`);
                
                // ✅ 超时控制
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), this.recoveryConfig.requestTimeout);
                
                const response = await fetch(`/api/box/battle/animation-state/?uid=${roomUid}`, {
                    signal: controller.signal,
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include' // 确保发送认证信息
                });
                
                clearTimeout(timeoutId);
                
                // ✅ HTTP状态检查
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const result = await response.json();
                
                // ✅ API响应码检查
                if (result.code !== 0) {
                    throw new Error(result.message || '获取动画状态失败');
                }
                
                // ✅ 验证响应数据完整性
                if (!this.validateAnimationStateData(result.body)) {
                    throw new Error('响应数据格式错误');
                }
                
                // ✅ 恢复动画状态
                await this.restoreAnimationFromState(result.body);
                
                console.log(`[动画恢复] 房间 ${roomUid} 动画状态恢复成功`);
                return; // 成功恢复，退出重试循环
                
            } catch (error) {
                attempt++;
                console.error(`[动画恢复] 第${attempt}次尝试失败:`, error);
                
                // ✅ 特定错误处理
                if (this.isUnrecoverableError(error)) {
                    console.error('[动画恢复] 检测到不可恢复错误，停止重试');
                    this.handleUnrecoverableError(error);
                    return;
                }
                
                if (attempt >= maxRetries) {
                    console.error('[动画恢复] 所有重试都失败了');
                    this.showRecoveryFailureDialog();
                    return;
                }
                
                // ✅ 指数退避重试
                const delay = this.calculateBackoffDelay(attempt);
                console.log(`[动画恢复] 等待 ${delay}ms 后进行第${attempt + 1}次重试`);
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
    }
    
    // ✅ 响应数据完整性验证
    validateAnimationStateData(data) {
        if (!data || typeof data !== 'object') {
            console.error('[数据验证] 响应数据不是有效对象');
            return false;
        }
        
        // 验证必需字段
        const requiredFields = ['room_uid', 'room_state', 'animation_state'];
        for (const field of requiredFields) {
            if (!(field in data)) {
                console.error(`[数据验证] 缺少必需字段: ${field}`);
                return false;
            }
        }
        
        // 验证动画状态格式
        const animationState = data.animation_state;
        if (!animationState || typeof animationState !== 'object') {
            console.error('[数据验证] animation_state字段格式错误');
            return false;
        }
        
        if (!animationState.status) {
            console.error('[数据验证] 缺少动画状态status字段');
            return false;
        }
        
        // 验证服务器时间戳
        if (!data.server_timestamp || typeof data.server_timestamp !== 'number') {
            console.error('[数据验证] 缺少或格式错误的server_timestamp字段');
            return false;
        }
        
        // 验证参与者数据
        if (!Array.isArray(data.participants)) {
            console.error('[数据验证] participants字段不是数组');
            return false;
        }
        
        console.log('[数据验证] 响应数据验证通过');
        return true;
    }
    
    // ✅ 计算指数退避延迟
    calculateBackoffDelay(attempt) {
        if (!this.recoveryConfig.enableExponentialBackoff) {
            return 1000; // 固定1秒延迟
        }
        
        const baseDelay = 1000;
        const exponentialDelay = baseDelay * Math.pow(2, attempt - 1);
        const jitteredDelay = exponentialDelay + Math.random() * 500; // 添加抖动
        
        return Math.min(jitteredDelay, this.recoveryConfig.maxBackoffDelay);
    }
    
    // ✅ 判断是否为不可恢复错误
    isUnrecoverableError(error) {
        const unrecoverableMessages = [
            'Access denied',
            'Room not found', 
            'Invalid Room UID format',
            'Authentication required'
        ];
        
        return unrecoverableMessages.some(msg => 
            error.message && error.message.includes(msg)
        );
    }
    
    // ✅ 处理不可恢复错误
    handleUnrecoverableError(error) {
        if (error.message.includes('Authentication required')) {
            this.showAuthenticationErrorDialog();
        } else if (error.message.includes('Access denied')) {
            this.showAccessDeniedDialog();
        } else if (error.message.includes('Room not found')) {
            this.showRoomNotFoundDialog();
        } else {
            this.showGenericErrorDialog(error.message);
        }
    }
    
    // ✅ 用户友好的错误对话框
    showRecoveryFailureDialog() {
        const message = '动画状态恢复失败，是否刷新页面获取最新状态？';
        if (confirm(message)) {
            window.location.reload();
        } else {
            this.showFallbackUI();
        }
    }
    
    showAuthenticationErrorDialog() {
        alert('用户认证失败，请重新登录后再试');
        // 可以重定向到登录页面
        // window.location.href = '/login';
    }
    
    showAccessDeniedDialog() {
        alert('您没有权限访问此房间的动画状态');
        this.showFallbackUI();
    }
    
    showRoomNotFoundDialog() {
        alert('房间不存在，可能已被删除或过期');
        // 可以重定向到房间列表
        // window.location.href = '/rooms';
    }
    
    showGenericErrorDialog(message) {
        alert(`发生错误：${message}\n\n请刷新页面重试`);
        this.showFallbackUI();
    }
    
    // ✅ 降级UI显示
    showFallbackUI() {
        // 显示静态的房间状态，隐藏动画元素
        const animationElements = document.querySelectorAll('.animation-container');
        animationElements.forEach(el => el.style.display = 'none');
        
        // 显示提示信息
        const fallbackMessage = document.createElement('div');
        fallbackMessage.className = 'fallback-message';
        fallbackMessage.innerHTML = `
            <div class="fallback-content">
                <h3>🔄 连接恢复中</h3>
                <p>正在尝试恢复连接，请稍候...</p>
                <button onclick="window.location.reload()">刷新页面</button>
            </div>
        `;
        document.body.appendChild(fallbackMessage);
    }
}
```

### 2. 网络状态监控

```javascript
class NetworkStatusMonitor {
    constructor(reconnectionManager) {
        this.reconnectionManager = reconnectionManager;
        this.isOnline = navigator.onLine;
        this.setupNetworkListeners();
    }
    
    setupNetworkListeners() {
        window.addEventListener('online', () => {
            console.log('[网络监控] 网络连接已恢复');
            this.isOnline = true;
            this.onNetworkRestore();
        });
        
        window.addEventListener('offline', () => {
            console.log('[网络监控] 网络连接已断开');
            this.isOnline = false;
            this.onNetworkLoss();
        });
    }
    
    onNetworkRestore() {
        // 网络恢复时，尝试重新连接WebSocket
        if (this.reconnectionManager.currentRoom) {
            setTimeout(() => {
                this.reconnectionManager.handleReconnectionSuccess();
            }, 1000); // 等待1秒确保网络稳定
        }
    }
    
    onNetworkLoss() {
        // 网络断开时，显示离线提示
        this.showOfflineIndicator();
    }
    
    showOfflineIndicator() {
        const indicator = document.createElement('div');
        indicator.id = 'offline-indicator';
        indicator.innerHTML = `
            <div class="offline-banner">
                <span>⚠️ 网络连接已断开</span>
            </div>
        `;
        document.body.appendChild(indicator);
    }
    
    hideOfflineIndicator() {
        const indicator = document.getElementById('offline-indicator');
        if (indicator) {
            indicator.remove();
        }
    }
}
```

### 3. 性能监控

```javascript
class PerformanceMonitor {
    constructor() {
        this.metrics = {
            apiResponseTimes: [],
            reconnectionTimes: [],
            errorCounts: {
                network: 0,
                timeout: 0,
                authentication: 0,
                validation: 0
            }
        };
        this.startTime = null;
    }
    
    startMeasurement(operation) {
        this.startTime = performance.now();
        console.log(`[性能监控] 开始测量: ${operation}`);
    }
    
    endMeasurement(operation, success = true) {
        if (!this.startTime) return;
        
        const duration = performance.now() - this.startTime;
        
        if (operation === 'api_call') {
            this.metrics.apiResponseTimes.push(duration);
        } else if (operation === 'reconnection') {
            this.metrics.reconnectionTimes.push(duration);
        }
        
        console.log(`[性能监控] ${operation} 耗时: ${duration.toFixed(2)}ms`);
        this.startTime = null;
        
        // 定期报告性能数据
        if (this.metrics.apiResponseTimes.length % 10 === 0) {
            this.reportPerformanceMetrics();
        }
    }
    
    recordError(errorType) {
        if (this.metrics.errorCounts[errorType] !== undefined) {
            this.metrics.errorCounts[errorType]++;
        }
    }
    
    reportPerformanceMetrics() {
        const avgApiTime = this.metrics.apiResponseTimes.reduce((a, b) => a + b, 0) / this.metrics.apiResponseTimes.length;
        
        console.log('[性能报告]', {
            averageApiResponseTime: avgApiTime.toFixed(2) + 'ms',
            errorCounts: this.metrics.errorCounts,
            totalApiCalls: this.metrics.apiResponseTimes.length
        });
    }
}
```

## 4. 完整集成示例

```javascript
// 初始化容错系统
class BattleSystemWithFaultTolerance {
    constructor() {
        this.performanceMonitor = new PerformanceMonitor();
        this.websocket = null;
        this.animationController = new AnimationSyncController();
        this.reconnectionManager = null;
        this.networkMonitor = null;
    }
    
    async initialize(roomUid) {
        try {
            // 1. 建立WebSocket连接
            this.websocket = new WebSocket('ws://localhost:8000/ws/');
            
            // 2. 创建重连管理器（使用增强版本）
            this.reconnectionManager = new WebSocketReconnectionManager(
                this.websocket, 
                this.animationController
            );
            
            // 3. 设置网络监控
            this.networkMonitor = new NetworkStatusMonitor(this.reconnectionManager);
            
            // 4. 设置当前房间
            this.reconnectionManager.setCurrentRoom({ uid: roomUid });
            
            // 5. 首次检查动画状态（带性能监控）
            this.performanceMonitor.startMeasurement('initial_state_check');
            await this.initialAnimationStateCheck(roomUid);
            this.performanceMonitor.endMeasurement('initial_state_check');
            
        } catch (error) {
            console.error('[系统初始化] 失败:', error);
            this.performanceMonitor.recordError('initialization');
            throw error;
        }
    }
    
    async initialAnimationStateCheck(roomUid) {
        try {
            this.performanceMonitor.startMeasurement('api_call');
            await this.reconnectionManager.recoverAnimationState(roomUid);
            this.performanceMonitor.endMeasurement('api_call', true);
        } catch (error) {
            this.performanceMonitor.endMeasurement('api_call', false);
            this.performanceMonitor.recordError('api_call');
            console.warn('[初始化] 动画状态检查失败:', error);
        }
    }
}

// 使用示例
const battleSystem = new BattleSystemWithFaultTolerance();
await battleSystem.initialize('room123456');
```

## 容错机制优势

### 1. **全面的错误处理** 🛡️
- HTTP错误、网络超时、认证失败等各种场景
- 不可恢复错误的智能识别和处理
- 用户友好的错误提示和降级方案

### 2. **智能重试策略** 🔄
- 指数退避算法防止服务器过载
- 随机抖动减少重试风暴
- 最大重试次数限制防止无限循环

### 3. **性能监控** 📊
- API响应时间跟踪
- 错误统计和分类
- 性能瓶颈识别

### 4. **网络适应性** 🌐
- 离线/在线状态检测
- 网络恢复后自动重连
- 网络状态的用户提示

### 5. **数据完整性保障** ✅
- 客户端数据格式验证
- 与后端API完整性检查配合
- 双重保障确保数据正确性

## 最佳实践

1. **合理的超时设置**: 5秒请求超时平衡了响应性和成功率
2. **渐进式重试**: 指数退避避免了对服务器的压力
3. **详细的日志**: 便于问题排查和性能优化
4. **优雅降级**: 即使在最坏情况下也能提供基础功能
5. **用户体验**: 清晰的状态提示和友好的错误处理

这个容错机制与后端的响应数据完整性检查形成了完整的容错体系，确保了系统在各种异常情况下的稳定性和可用性。 