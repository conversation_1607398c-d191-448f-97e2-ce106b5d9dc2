from django.urls import re_path as url

from box import views, model_signals



app_name = 'box'
urlpatterns = [
    # socket
    url(r'^connect/', views.SocketConnectView.as_view()),
    url(r'^disconnect/', views.SocketDisconnectView.as_view()),
   
    # url(r'^list/', views.GetCaseListView.as_view()),
    # url(r'^free/', views.GetFreeCaseListView.as_view()),
    # url(r'^festival/', views.GetFestivalCaseView.as_view()),    
    # url(r'^record/', views.GetCaseRecordView.as_view()),
    # url(r'^record/', views.GetCaseRecentRecordView.as_view()),
    # url(r'^recent/', views.GetCaseRecentRecordView.as_view()),    
    # url(r'^info/', views.GetBattleInfoView.as_view()),
    # url(r'^online/', views.GetJoinableRoomView.as_view()),
    # url(r'^room/create/', views.CreateCaseRoomView.as_view()),
    # url(r'^room/join/', views.JoinCaseRoomView.as_view()),    
    # url(r'^room/quicksell/', views.SellCaseRoomItemView.as_view()),
    # url(r'^room/record/', views.GetRoomRecordView.as_view()),
    # url(r'^room/history/', views.GetRoomHistoryView.as_view()),
    # url(r'^room/stat/', views.GetRoomStatistics.as_view()),
    # url(r'^rank/dropday/', views.GetDropDayRankView.as_view()),
    # url(r'^rank/incomeday/', views.GetIncomeDayRankView.as_view()),
    # url(r'^rank/roomday/', views.GetRoomDayRankView.as_view()),
    # url(r'^rank/loseweek/', views.GetLoseWeekRankView.as_view()),
    # url(r'^rank/dropdayprice/', views.GetDropDayPriceRankView.as_view()),
    # url(r'^rank/dropdaycount/', views.GetDropDayMostRankView.as_view()),
    # url(r'^rank/battledayuser/', views.GetBattleDayRankView.as_view()),
    # url(r'^battle/rankday/', views.GetBattleDayRankView.as_view()),
    # url(r'^hot/', views.GetHotBoxListView.as_view()),
    # url(r'^discount/', views.GetDiscountBoxListView.as_view()),
    # url(r'^room/list/', views.GetBattleListView.as_view()),   
    # url(r'^newbox/', views.GetNewBoxListView.as_view()), 
    # url(r'^randombox/', views.GetRandomBoxListView.as_view()),
    # url(r'^room/recordall/', views.GetRoomRecordAllView.as_view()),
    # url(r'^deleteoldcase/', views.DeleteOldCaseRecordsView.as_view()),
    # url(r'^updateboxitems/', views.UpdateBoxItemsCustomPriceView.as_view()),
    # url(r'^syncboxshowchance/', views.SyncBoxShowChanceView.as_view()),

    # 2025优化接口
    url(r'^search/', views.SearchCaseView.as_view()),
    url(r'^category/', views.GetCaseCategoryView.as_view()),
    url(r'^type/', views.GetCaseTypeView.as_view()),
    url(r'^detail/', views.GetCaseDetailView.as_view()), 
    url(r'^skins/', views.GetBoxSkinView.as_view()),
    url(r'^records/', views.GetBoxRecordView.as_view()),
    url(r'^tag/', views.GetTagBoxListView.as_view()),
    url(r'^open/', views.OpenCaseView.as_view()),
    
    # 对战
    url(r'^battle/case/', views.GetBattleCaseListView.as_view()),
    url(r'^battle/create/', views.CreateBattleCaseRoomView.as_view()),
    url(r'^battle/join/', views.JoinBattleCaseRoomView.as_view()),
    url(r'^battle/leave/', views.GetCaseRoomLeaveView.as_view()),
    url(r'^battle/quit/', views.QuitCaseRoomListView.as_view()),
    url(r'^battle/dismiss/', views.DismissCaseRoomView.as_view()),
    url(r'^battle/list/', views.GetCaseRoomListView.as_view()),
    url(r'^battle/detail/', views.GetCaseRoomDetailView.as_view()),
    url(r'^battle/self/', views.GetCaseRoomSelfView.as_view()),
    url(r'^battle/participated/', views.GetCaseRoomParticipatedView.as_view()),
    url(r'^battle/animation-config/', views.BattleAnimationConfigView.as_view()),
    url(r'^battle/animation-state/', views.BattleAnimationStateView.as_view()),
    
    # 时间同步
    url(r'^battle/time-sync/', views.BattleTimeSyncView.as_view()),

    # 对战统计和排行
    url(r'^room/stat/', views.GetRoomStatistics.as_view()),
    url(r'^battle/rankday/', views.GetBattleDayRankView.as_view()),

]
