<!-- components/battle/BattleRoundManager.vue -->
<template>
  <div class="battle-round-manager">
    <!-- 回合状态显示 -->
    <div class="round-status-section">
      <div class="status-card" :class="statusCardClass">
        <div class="status-icon">
          <Icon :name="statusIcon" class="w-8 h-8" />
        </div>
        <div class="status-content">
          <h3 class="status-title">{{ statusTitle }}</h3>
          <p class="status-description">{{ statusDescription }}</p>
        </div>
      </div>
    </div>

    <!-- 倒计时显示 -->
    <div v-if="showCountdown" class="countdown-section">
      <div class="countdown-container">
        <div class="countdown-circle">
          <svg class="countdown-svg" viewBox="0 0 100 100">
            <circle
              class="countdown-track"
              cx="50" cy="50" r="45"
              fill="none" stroke="#374151" stroke-width="8"
            />
            <circle
              class="countdown-progress"
              cx="50" cy="50" r="45"
              fill="none" stroke="url(#countdown-gradient)" stroke-width="8"
              :stroke-dasharray="circumference"
              :stroke-dashoffset="dashOffset"
              transform="rotate(-90 50 50)"
            />
            <defs>
              <linearGradient id="countdown-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" style="stop-color:#3B82F6"/>
                <stop offset="100%" style="stop-color:#EF4444"/>
              </linearGradient>
            </defs>
          </svg>
          <div class="countdown-text">
            <div class="countdown-number">{{ countdownValue }}</div>
            <div class="countdown-label">{{ t('battle.detail.seconds') }}</div>
          </div>
        </div>
        <div class="countdown-message">{{ countdownMessage }}</div>
      </div>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-buttons-section">
      <div class="buttons-container">
        <!-- 开始回合按钮 -->
        <button
          v-if="canStartRound"
          @click="handleStartRound"
          :disabled="isProcessing"
          class="action-btn primary"
        >
          <Icon name="heroicons:play" class="w-5 h-5" />
          <span>{{ t('battle.detail.start_round', { round: currentRound + 1 }) }}</span>
          <div v-if="isProcessing" class="btn-spinner"></div>
        </button>

        <!-- 下一回合按钮 -->
        <button
          v-else-if="canNextRound"
          @click="handleNextRound"
          :disabled="isProcessing"
          class="action-btn secondary"
        >
          <Icon name="heroicons:arrow-right" class="w-5 h-5" />
          <span>{{ t('battle.detail.next_round') }}</span>
        </button>

        <!-- 查看结果按钮 -->
        <button
          v-else-if="canViewResults"
          @click="handleViewResults"
          class="action-btn success"
        >
          <Icon name="heroicons:trophy" class="w-5 h-5" />
          <span>{{ t('battle.detail.view_results') }}</span>
        </button>

        <!-- 重新开始按钮 -->
        <button
          v-if="battleState === 'finished'"
          @click="handleRestart"
          class="action-btn outline"
        >
          <Icon name="heroicons:arrow-path" class="w-5 h-5" />
          <span>{{ t('battle.detail.restart') }}</span>
        </button>

        <!-- 退出对战按钮 -->
        <button
          v-if="canLeaveBattle"
          @click="handleLeaveBattle"
          class="action-btn danger"
        >
          <Icon name="heroicons:x-mark" class="w-5 h-5" />
          <span>{{ t('battle.detail.leave_battle') }}</span>
        </button>
      </div>
    </div>

    <!-- 回合历史 -->
    <div v-if="roundHistory.length > 0" class="round-history-section">
      <h4 class="history-title">{{ t('battle.detail.round_history') }}</h4>
      <div class="history-list">
        <div 
          v-for="(round, index) in roundHistory" 
          :key="index"
          class="history-item"
          :class="{ 'current': index === currentRound }"
        >
          <div class="round-number">{{ index + 1 }}</div>
          <div class="round-info">
            <div class="round-case">{{ round.caseName }}</div>
            <div class="round-winner">
              <Icon name="heroicons:trophy" class="w-3 h-3" />
              {{ round.winner }}
            </div>
          </div>
          <div class="round-value">${{ round.value.toFixed(2) }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface RoundHistoryItem {
  caseName: string
  winner: string
  value: number
  completed: boolean
}

interface Props {
  battleState: 'waiting' | 'ready' | 'running' | 'finished'
  currentRound: number
  totalRounds: number
  canStartRound: boolean
  canNextRound: boolean
  canViewResults: boolean
  canLeaveBattle: boolean
  isProcessing: boolean
  countdownValue: number
  countdownMessage: string
  roundHistory: RoundHistoryItem[]
}

interface Emits {
  (e: 'start-round'): void
  (e: 'next-round'): void
  (e: 'view-results'): void
  (e: 'restart'): void
  (e: 'leave-battle'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const { t } = useI18n()

// 计算属性
const showCountdown = computed(() => {
  return props.countdownValue > 0 && (props.battleState === 'ready' || props.battleState === 'running')
})

const statusCardClass = computed(() => ({
  'status-waiting': props.battleState === 'waiting',
  'status-ready': props.battleState === 'ready',
  'status-running': props.battleState === 'running',
  'status-finished': props.battleState === 'finished'
}))

const statusIcon = computed(() => {
  switch (props.battleState) {
    case 'waiting': return 'heroicons:clock'
    case 'ready': return 'heroicons:check-circle'
    case 'running': return 'heroicons:play'
    case 'finished': return 'heroicons:trophy'
    default: return 'heroicons:question-mark-circle'
  }
})

const statusTitle = computed(() => {
  switch (props.battleState) {
    case 'waiting': return t('battle.detail.status.waiting_title')
    case 'ready': return t('battle.detail.status.ready_title')
    case 'running': return t('battle.detail.status.running_title')
    case 'finished': return t('battle.detail.status.finished_title')
    default: return t('battle.detail.status.unknown_title')
  }
})

const statusDescription = computed(() => {
  switch (props.battleState) {
    case 'waiting': return t('battle.detail.status.waiting_desc')
    case 'ready': return t('battle.detail.status.ready_desc')
    case 'running': return t('battle.detail.status.running_desc', { round: props.currentRound + 1, total: props.totalRounds })
    case 'finished': return t('battle.detail.status.finished_desc')
    default: return t('battle.detail.status.unknown_desc')
  }
})

// 倒计时相关
const circumference = 2 * Math.PI * 45
const dashOffset = computed(() => {
  const progress = props.countdownValue / 30 // 假设最大30秒
  return circumference * (1 - progress)
})

// 事件处理
const handleStartRound = () => emit('start-round')
const handleNextRound = () => emit('next-round')
const handleViewResults = () => emit('view-results')
const handleRestart = () => emit('restart')
const handleLeaveBattle = () => emit('leave-battle')
</script>

<style lang="scss" scoped>
.battle-round-manager {
  @apply space-y-6;
}

.round-status-section {
  .status-card {
    @apply bg-gray-800/50 border border-gray-700/30 rounded-xl p-6;
    @apply flex items-center gap-4 transition-all duration-300;
    
    &.status-waiting {
      @apply border-yellow-500/30 bg-yellow-500/5;
    }
    
    &.status-ready {
      @apply border-green-500/30 bg-green-500/5;
    }
    
    &.status-running {
      @apply border-blue-500/30 bg-blue-500/5;
      animation: running-pulse 2s ease-in-out infinite;
    }
    
    &.status-finished {
      @apply border-purple-500/30 bg-purple-500/5;
    }
    
    .status-icon {
      @apply flex-shrink-0 p-3 rounded-full;
      
      .status-waiting & {
        @apply bg-yellow-500/20 text-yellow-400;
      }
      
      .status-ready & {
        @apply bg-green-500/20 text-green-400;
      }
      
      .status-running & {
        @apply bg-blue-500/20 text-blue-400;
      }
      
      .status-finished & {
        @apply bg-purple-500/20 text-purple-400;
      }
    }
    
    .status-content {
      @apply flex-1;
      
      .status-title {
        @apply text-white font-bold text-lg mb-1;
      }
      
      .status-description {
        @apply text-gray-300 text-sm;
      }
    }
  }
}

.countdown-section {
  @apply flex justify-center;
  
  .countdown-container {
    @apply text-center;
    
    .countdown-circle {
      @apply relative w-32 h-32 mx-auto mb-4;
      
      .countdown-svg {
        @apply w-full h-full;
      }
      
      .countdown-track {
        opacity: 0.2;
      }
      
      .countdown-progress {
        transition: stroke-dashoffset 1s linear;
      }
      
      .countdown-text {
        @apply absolute inset-0 flex flex-col items-center justify-center;
        
        .countdown-number {
          @apply text-3xl font-bold text-white;
        }
        
        .countdown-label {
          @apply text-xs text-gray-400 uppercase tracking-wide;
        }
      }
    }
    
    .countdown-message {
      @apply text-gray-300 text-sm;
    }
  }
}

.action-buttons-section {
  .buttons-container {
    @apply flex flex-wrap gap-3 justify-center;
    
    .action-btn {
      @apply relative inline-flex items-center gap-2 px-6 py-3 rounded-lg font-medium;
      @apply transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
      
      &.primary {
        @apply bg-primary hover:bg-primary/80 text-white;
        box-shadow: 0 4px 20px rgba(var(--color-primary-rgb), 0.3);
        
        &:hover:not(:disabled) {
          box-shadow: 0 6px 25px rgba(var(--color-primary-rgb), 0.4);
          transform: translateY(-1px);
        }
      }
      
      &.secondary {
        @apply bg-gray-700 hover:bg-gray-600 text-white;
      }
      
      &.success {
        @apply bg-green-600 hover:bg-green-500 text-white;
        box-shadow: 0 4px 20px rgba(34, 197, 94, 0.3);
      }
      
      &.danger {
        @apply bg-red-600 hover:bg-red-500 text-white;
      }
      
      &.outline {
        @apply bg-transparent border border-gray-600 hover:border-gray-500 text-gray-300 hover:text-white;
      }
      
      .btn-spinner {
        @apply absolute inset-0 flex items-center justify-center;
        @apply bg-inherit rounded-lg;
        
        &::after {
          content: '';
          @apply w-5 h-5 border-2 border-white/30 border-t-white rounded-full;
          animation: spin 1s linear infinite;
        }
      }
    }
  }
}

.round-history-section {
  .history-title {
    @apply text-white font-semibold text-lg mb-4;
  }
  
  .history-list {
    @apply space-y-2;
    
    .history-item {
      @apply flex items-center gap-4 p-3 bg-gray-800/30 border border-gray-700/30 rounded-lg;
      @apply transition-all duration-200;
      
      &.current {
        @apply border-primary/50 bg-primary/5;
      }
      
      &:hover {
        @apply border-gray-600/50;
      }
      
      .round-number {
        @apply flex-shrink-0 w-8 h-8 bg-gray-700 rounded-full;
        @apply flex items-center justify-center text-white font-bold text-sm;
        
        .current & {
          @apply bg-primary text-white;
        }
      }
      
      .round-info {
        @apply flex-1 min-w-0;
        
        .round-case {
          @apply text-white font-medium text-sm truncate;
        }
        
        .round-winner {
          @apply flex items-center gap-1 text-yellow-400 text-xs;
        }
      }
      
      .round-value {
        @apply text-primary font-bold text-sm;
      }
    }
  }
}

// 动画定义
@keyframes running-pulse {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.5);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style> 