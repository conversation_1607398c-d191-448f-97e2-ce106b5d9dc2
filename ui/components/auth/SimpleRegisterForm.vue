<template>
  <div class="p-6 bg-gray-800 rounded-lg border border-gray-600">
    <h2 class="text-xl font-semibold text-white mb-6 text-center">注册</h2>
    
    <form @submit.prevent="handleRegister" class="space-y-4">
      <!-- 邮箱 -->
      <div>
        <label class="block text-sm font-medium text-gray-300 mb-2">邮箱</label>
        <input 
          type="email" 
          v-model="email" 
          class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
          placeholder="请输入邮箱"
          required
        />
      </div>
      
      <!-- 密码 -->
      <div>
        <label class="block text-sm font-medium text-gray-300 mb-2">密码</label>
        <input 
          type="password" 
          v-model="password" 
          class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
          placeholder="请输入密码（至少8位）"
          required
        />
      </div>
      
      <!-- 确认密码 -->
      <div>
        <label class="block text-sm font-medium text-gray-300 mb-2">确认密码</label>
        <input 
          type="password" 
          v-model="confirmPassword" 
          class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
          placeholder="请再次输入密码"
          required
        />
      </div>
      
      <!-- 服务条款 -->
      <div class="flex items-center">
        <input 
          type="checkbox" 
          id="agree" 
          v-model="agreeTerms" 
          class="mr-2 rounded bg-gray-700 border-gray-600"
        />
        <label for="agree" class="text-sm text-gray-300">
          我同意
          <NuxtLink to="/terms" class="text-blue-400 hover:text-blue-300">服务条款</NuxtLink>
          和
          <NuxtLink to="/privacy" class="text-blue-400 hover:text-blue-300">隐私政策</NuxtLink>
        </label>
      </div>
      
      <!-- 提交按钮 -->
      <button 
        type="submit" 
        :disabled="isLoading || !agreeTerms"
        class="w-full py-2 px-4 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white font-medium rounded transition-colors"
      >
        {{ isLoading ? '注册中...' : '注册' }}
      </button>
      
      <!-- 错误信息 -->
      <div v-if="error" class="p-3 bg-red-500/20 border border-red-500 rounded">
        <p class="text-red-300 text-sm">{{ error }}</p>
      </div>
    </form>
    
    <!-- 链接 -->
    <div class="mt-6 text-center">
      <p class="text-gray-400 text-sm">
        已有账号？
        <NuxtLink to="/auth/login" class="text-blue-400 hover:text-blue-300">立即登录</NuxtLink>
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
const email = ref('')
const password = ref('')
const confirmPassword = ref('')
const agreeTerms = ref(false)
const isLoading = ref(false)
const error = ref('')
const userStore = useUserStore()
const router = useRouter()

const handleRegister = async () => {
  // 基本验证
  if (!email.value || !password.value || !confirmPassword.value) {
    error.value = '请填写所有必填字段'
    return
  }
  
  if (password.value.length < 8) {
    error.value = '密码至少需要8位'
    return
  }
  
  if (password.value !== confirmPassword.value) {
    error.value = '两次密码输入不一致'
    return
  }
  
  if (!agreeTerms.value) {
    error.value = '请同意服务条款和隐私政策'
    return
  }
  
  try {
    isLoading.value = true
    error.value = ''
    
    await userStore.register({
      email: email.value,
      password: password.value,
      verifyCode: '000000' // 开发模式临时验证码
    })
    
    // 注册成功后跳转
    await router.push('/')
  } catch (err: any) {
    error.value = err.message || '注册失败，请重试'
  } finally {
    isLoading.value = false
  }
}
</script> 