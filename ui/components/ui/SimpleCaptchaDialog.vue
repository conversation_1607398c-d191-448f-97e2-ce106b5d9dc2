<template>
  <div 
    v-if="show" 
    class="fixed inset-0 bg-black/70 flex items-center justify-center z-[9999] p-4"
    @click="$emit('close')"
  >
    <div 
      class="bg-white rounded-lg p-6 w-full max-w-sm shadow-2xl"
      @click.stop
    >
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-bold text-gray-900">
          输入验证码
        </h3>
        <button 
          @click="$emit('close')"
          class="text-gray-500 hover:text-gray-700"
        >
          ✕
        </button>
      </div>
      
      <div class="space-y-4">
        <div class="flex gap-3 items-center">
          <input 
            type="text" 
            v-model="captchaInput"
            class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="请输入验证码" 
            @keyup.enter="handleConfirm"
          />
          <div
            class="w-20 h-10 border border-gray-300 rounded-lg bg-gray-100 flex items-center justify-center cursor-pointer hover:bg-gray-200"
            @click="$emit('refresh')"
          >
            <img v-if="image" :src="image" alt="captcha" class="w-full h-full object-cover rounded" />
            <span v-else class="text-xs text-gray-500">刷新</span>
          </div>
        </div>
        
        <p v-if="error" class="text-sm text-red-500">
          {{ error }}
        </p>
        
        <div class="flex gap-3">
          <button 
            @click="$emit('close')"
            class="flex-1 py-2 px-4 bg-gray-300 hover:bg-gray-400 text-gray-700 rounded-lg"
          >
            取消
          </button>
          <button 
            @click="handleConfirm"
            :disabled="loading"
            class="flex-1 py-2 px-4 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg"
          >
            {{ loading ? '加载中...' : '确认' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  show: boolean
  image?: string
  error?: string
  loading?: boolean
}

interface Emits {
  (e: 'close'): void
  (e: 'confirm', value: string): void
  (e: 'refresh'): void
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  image: '',
  error: '',
  loading: false
})

const emit = defineEmits<Emits>()
const captchaInput = ref('')

const handleConfirm = () => {
  emit('confirm', captchaInput.value)
}

// 监听弹窗状态变化，清空输入
watch(() => props.show, (newValue) => {
  if (!newValue) {
    captchaInput.value = ''
  }
})
</script> 