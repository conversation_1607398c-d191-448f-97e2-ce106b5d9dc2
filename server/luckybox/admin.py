from django.contrib import admin
from django.contrib.admin.views.main import ChangeList
from django.db.models import Sum
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _
# # from jet.filters import DateRangeFilter  # 暂时禁用，等待Django 4.x兼容版本  # 暂时禁用，等待Django 4.x兼容版本

from luckybox.models import LuckyBoxItem, LuckyBoxRecord, LuckyBoxCategory, LuckyBoxGroup, LuckyRecommendGroup
from luckybox.service.admin_actions import import_to_recommend, sync_group_price
from package.models import ItemInfo
from steambase.admin import ReadOnlyAdmin

_STEAM_IMG_BASE = 'https://steamcommunity-a.akamaihd.net/economy/image/{icon_url}/'

@admin.register(LuckyBoxCategory)
class LuckyBoxCategoryAdmin(admin.ModelAdmin):
    list_display = ('category',) # "default_image", 'custom_image'

    def default_image(self, obj):
        weapons_qs = ItemInfo.objects.filter(type=obj.category)
        weapon_first = weapons_qs.first()
        icon_url = None
        if weapon_first:
            icon_url = _STEAM_IMG_BASE.format(icon_url=weapon_first.icon_url)
        return format_html('<img src="{}" width="160px"/>', icon_url)

    default_image.short_description = _("默认图片")

    def custom_image(self, obj):
        if obj.image:
            return format_html('<img src="{}" width="160px"/>', obj.image.url)
        return "-"
    custom_image.short_description = _("自定义图片")

    def custom_click_image(self, obj):
        if obj.image:
            return format_html('<img src="{}" width="160px"/>', obj.click_image.url)
        return "-"
    custom_click_image.short_description = _("自定义点亮图片")


@admin.register(LuckyBoxItem)
class LuckyBoxItemAdmin(admin.ModelAdmin):
    list_display = ('item_info', 'price')
    list_per_page = 200
    readonly_fields = ('item_info', )
    search_fields = ('item_info__market_hash_name', 'item_info__market_name_cn')


@admin.register(LuckyBoxGroup)
class LuckyBoxGroupAdmin(admin.ModelAdmin):
    list_per_page = 200
    list_display = ("market_name", "type", "weapon", "price")
    actions = [import_to_recommend, sync_group_price]

@admin.register(LuckyRecommendGroup)
class LuckyRecommendGroupAdmin(admin.ModelAdmin):
    list_per_page = 200
    pass

@admin.register(LuckyBoxRecord)
class LuckyBoxRecordAdmin(ReadOnlyAdmin):
    list_display = ("uid", 'item', 'user', 'percent', 'origin_percent','percentage', 'coins', 'win', 'update_time')
    ordering = ('-update_time',)
    search_fields = ("user__username", "item__item_info__market_hash_name", "item__item_info__market_name_cn")
    list_filter = ("win",)


