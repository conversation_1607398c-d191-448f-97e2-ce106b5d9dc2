export interface CaseItem {
  id: string
  name: string
  name_zh_hans?: string  // 中文名称
  name_en?: string       // 英文名称
  name_zh?: string       // 兼容字段
  image: string
  price: number
  original_price?: number
  discount?: number
  is_hot?: boolean
  is_new?: boolean
  description?: string
  category?: string
  rarity?: string
  created_at?: string
  updated_at?: string
  open_count?: number
  cover?: string         // 封面图片
  case_key?: string      // 箱子key
  tag?: string           // 标签
  tag_zh_hans?: string   // 中文标签
  tag_en?: string        // 英文标签
}

export interface CaseResponse {
  cases: CaseItem[]
  total: number
}

export interface HotCasesResponse {
  hot_cases: CaseItem[]
  total: number
}

export interface DiscountCasesResponse {
  discount_cases: CaseItem[]
  total: number
}

export interface NewCasesResponse {
  new_cases: CaseItem[]
  total: number
} 