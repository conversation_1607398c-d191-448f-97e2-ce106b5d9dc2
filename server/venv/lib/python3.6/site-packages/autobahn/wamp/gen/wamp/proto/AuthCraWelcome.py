# automatically generated by the FlatBuffers compiler, do not modify

# namespace: proto

import flatbuffers

class AuthCraWelcome(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAsAuthCraWelcome(cls, buf, offset):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = AuthCraWelcome()
        x.Init(buf, n + offset)
        return x

    # AuthCraWelcome
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

def AuthCraWelcomeStart(builder): builder.StartObject(0)
def AuthCraWelcomeEnd(builder): return builder.EndObject()
