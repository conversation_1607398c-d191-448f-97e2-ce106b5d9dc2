/**
 * 🎯 对战轮次动画管理器
 * 
 * 这是系统的关键功能，负责：
 * 1. 协调轮次执行状态与UI标记的完全同步
 * 2. 管理动画执行流程和时间间歇
 * 3. 确保开箱记录的准确添加和显示
 * 4. 提供流畅丝滑的用户体验
 */

import { ref, reactive, computed, watch, nextTick, onUnmounted, readonly } from 'vue'
import { useBattleStateSync } from './useBattleStateSync'
import { useBattleAudio } from './useBattleAudio'

// 🎯 轮次执行状态枚举
export enum RoundExecutionState {
  WAITING = 'waiting',          // 等待开始
  PREPARING = 'preparing',      // 准备中（显示标记但未开始动画）
  ANIMATING = 'animating',      // 动画执行中
  COMPLETING = 'completing',    // 动画完成，处理结果中
  INTERVAL = 'interval',        // 轮次间隔休息
  COMPLETED = 'completed'       // 已完成
}

// 🎯 动画事件类型
export interface AnimationEvent {
  type: 'round_start' | 'animation_start' | 'animation_complete' | 'round_complete' | 'battle_complete'
  round: number
  playerIndex?: number
  item?: any
  timestamp: number
  duration?: number
}

// 🎯 轮次执行配置
export interface RoundExecutionConfig {
  // 时间配置（毫秒）
  preparationDelay: number      // 准备阶段延迟
  animationDuration: number     // 动画执行时长  
  completionDelay: number       // 完成处理延迟
  intervalDuration: number      // 轮次间隔时长
  
  // 音效配置
  enableAudio: boolean
  audioVolume: number
  
  // 动画配置
  enableGlowEffect: boolean
  enableParticles: boolean
  enableShakeEffect: boolean
}

// 🎯 默认配置
const DEFAULT_CONFIG: RoundExecutionConfig = {
  preparationDelay: 800,      // 800ms准备时间
  animationDuration: 5000,    // 5秒动画
  completionDelay: 1200,      // 1.2秒处理时间
  intervalDuration: 2000,     // 2秒间隔
  enableAudio: true,
  audioVolume: 0.7,
  enableGlowEffect: true,
  enableParticles: true,
  enableShakeEffect: false
}

export function useBattleRoundAnimationManager() {
  const battleStateSync = useBattleStateSync()
  const battleAudio = useBattleAudio()

  // 🎯 核心状态
  const isInitialized = ref(false)
  const executionConfig = reactive<RoundExecutionConfig>({ ...DEFAULT_CONFIG })
  
  // 🎯 执行状态跟踪
  const currentExecutionState = ref<RoundExecutionState>(RoundExecutionState.WAITING)
  const executingRound = ref<number>(0)
  const executingPlayerIndex = ref<number>(-1)
  
  // 🎯 时间管理
  const stateStartTime = ref<number>(0)
  const executionTimer = ref<NodeJS.Timeout | null>(null)
  const intervalTimer = ref<NodeJS.Timeout | null>(null)
  
  // 🎯 动画记录
  const animationHistory = ref<AnimationEvent[]>([])
  const activeAnimations = ref<Map<string, AnimationEvent>>(new Map())
  
  // 🎯 UI标记状态
  const currentRoundMarker = ref<number>(0)
  const executingCaseId = ref<string | null>(null)
  
  // 🎯 事件回调队列
  const eventCallbacks = ref<Map<string, Function[]>>(new Map())

  // 🎯 计算属性 - 当前轮次状态
  const getCurrentRoundState = computed(() => {
    const round = executingRound.value
    const state = currentExecutionState.value
    
    return {
      round,
      state,
      isExecuting: state === RoundExecutionState.ANIMATING,
      isMarked: state !== RoundExecutionState.WAITING && state !== RoundExecutionState.COMPLETED,
      progress: getStateProgress(state),
      timeRemaining: getTimeRemaining(),
      nextRound: round < battleStateSync.state.totalRounds ? round + 1 : null
    }
  })

  // 🎯 初始化动画管理器
  const initialize = async (config?: Partial<RoundExecutionConfig>) => {
    if (isInitialized.value) return
    
    console.log('[🎬ROUND-ANIMATION] 初始化轮次动画管理器')
    
    // 应用配置
    if (config) {
      Object.assign(executionConfig, config)
    }
    
    // 重置状态
    resetExecutionState()
    
    // 设置事件监听
    setupEventListeners()
    
    isInitialized.value = true
    console.log('[🎬ROUND-ANIMATION] ✅ 初始化完成', { config: executionConfig })
  }

  // 🎯 开始轮次执行
  const startRoundExecution = async (round: number, caseId: string, players: any[]) => {
    if (!isInitialized.value) {
      throw new Error('轮次动画管理器未初始化')
    }
    
    // 🚨 轮次验证：防止重复执行和无效轮次
    if (currentExecutionState.value !== RoundExecutionState.WAITING) {
      console.warn('[🎬ROUND-ANIMATION] ⚠️ 轮次执行已在进行中，当前状态:', currentExecutionState.value, '当前轮次:', executingRound.value)
      return
    }
    
    // 🚨 轮次合理性检查
    if (round <= 0) {
      console.error('[🎬ROUND-ANIMATION] ❌ 无效轮次:', round)
      return
    }
    
    // 🚨 防止轮次回退
    if (executingRound.value > 0 && round <= executingRound.value) {
      console.warn('[🎬ROUND-ANIMATION] ⚠️ 轮次回退检测:', { 当前轮次: executingRound.value, 请求轮次: round })
      return
    }
    
    console.log('[🎬ROUND-ANIMATION] 🚀 开始轮次执行', { round, caseId, playerCount: players.length, 之前轮次: executingRound.value })
    
    // 更新执行状态
    executingRound.value = round
    currentRoundMarker.value = round
    executingCaseId.value = caseId
    stateStartTime.value = Date.now()
    
    // 发送轮次开始事件
    emitAnimationEvent({
      type: 'round_start',
      round,
      timestamp: Date.now()
    })
    
    // 进入准备阶段
    await transitionToState(RoundExecutionState.PREPARING)
    
    // 延迟后开始动画
    setTimeout(async () => {
      await startAnimationPhase(round, caseId, players)
    }, executionConfig.preparationDelay)
  }

  // 🎯 开始动画阶段
  const startAnimationPhase = async (round: number, caseId: string, players: any[]) => {
    console.log('[🎬ROUND-ANIMATION] 🎬 开始动画阶段', { round, caseId })
    
    // 切换到动画状态
    await transitionToState(RoundExecutionState.ANIMATING)
    
    // 更新状态同步器
    battleStateSync.setOpeningState(caseId, 0, players[0]?.user?.profile?.nickname || '')
    battleStateSync.updateBattleState({
      currentRound: round,
      isBattleStarted: true
    })
    
    // 播放开始音效
    if (executionConfig.enableAudio) {
      battleAudio.playSound('round_start', { volume: executionConfig.audioVolume })
    }
    
    // 发送动画开始事件
    emitAnimationEvent({
      type: 'animation_start',
      round,
      timestamp: Date.now(),
      duration: executionConfig.animationDuration
    })
    
    // 设置动画完成定时器
    executionTimer.value = setTimeout(async () => {
      await completeAnimationPhase(round, caseId, players)
    }, executionConfig.animationDuration)
  }

  // 🎯 完成动画阶段
  const completeAnimationPhase = async (round: number, caseId: string, players: any[]) => {
    console.log('[🎬ROUND-ANIMATION] ✅ 动画阶段完成', { round, caseId })
    
    // 清除定时器
    if (executionTimer.value) {
      clearTimeout(executionTimer.value)
      executionTimer.value = null
    }
    
    // 切换到完成处理状态
    await transitionToState(RoundExecutionState.COMPLETING)
    
    // 播放完成音效
    if (executionConfig.enableAudio) {
      battleAudio.playSound('round_complete', { volume: executionConfig.audioVolume })
    }
    
    // 处理结果延迟
    setTimeout(async () => {
      await finishRoundExecution(round, caseId)
    }, executionConfig.completionDelay)
  }

  // 🎯 结束轮次执行
  const finishRoundExecution = async (round: number, caseId: string) => {
    console.log('[🎬ROUND-ANIMATION] 🏁 结束轮次执行', { round, caseId })
    
    // 清除开箱状态
    battleStateSync.setOpeningState(null, null, '')
    
    // 标记轮次完成
    battleStateSync.completeRound(round, caseId)
    
    // 发送轮次完成事件
    emitAnimationEvent({
      type: 'round_complete',
      round,
      timestamp: Date.now()
    })
    
    // 检查是否还有更多轮次
    if (round < battleStateSync.state.totalRounds) {
      // 进入间隔状态
      await transitionToState(RoundExecutionState.INTERVAL)
      
      // 设置间隔定时器
      intervalTimer.value = setTimeout(async () => {
        await prepareNextRound(round + 1)
      }, executionConfig.intervalDuration)
    } else {
      // 对战完成
      await completeBattle()
    }
  }

  // 🎯 准备下一轮次
  const prepareNextRound = async (nextRound: number) => {
    console.log('[🎬ROUND-ANIMATION] ⏭️ 准备下一轮次', { nextRound })
    
    // 清除间隔定时器
    if (intervalTimer.value) {
      clearTimeout(intervalTimer.value)
      intervalTimer.value = null
    }
    
    // 重置到等待状态
    await transitionToState(RoundExecutionState.WAITING)
    
    // 更新轮次标记
    currentRoundMarker.value = nextRound
    executingCaseId.value = null
    
    // 等待外部触发下一轮次
    console.log('[🎬ROUND-ANIMATION] ⏸️ 等待下一轮次触发...')
  }

  // 🎯 完成对战
  const completeBattle = async () => {
    console.log('[🎬ROUND-ANIMATION] 🎉 对战完成')
    
    // 切换到完成状态
    await transitionToState(RoundExecutionState.COMPLETED)
    
    // 完成对战状态
    battleStateSync.finishBattle()
    
    // 播放对战完成音效
    if (executionConfig.enableAudio) {
      battleAudio.playSound('battle_complete', { volume: executionConfig.audioVolume })
    }
    
    // 发送对战完成事件
    emitAnimationEvent({
      type: 'battle_complete',
      round: executingRound.value,
      timestamp: Date.now()
    })
    
    // 清除所有标记
    currentRoundMarker.value = 0
    executingCaseId.value = null
  }

  // 🎯 状态转换
  const transitionToState = async (newState: RoundExecutionState) => {
    const oldState = currentExecutionState.value
    
    console.log('[🎬ROUND-ANIMATION] 🔄 状态转换', { from: oldState, to: newState })
    
    currentExecutionState.value = newState
    stateStartTime.value = Date.now()
    
    // 等待Vue响应式更新
    await nextTick()
    
    // 触发状态变化回调
    triggerCallbacks('stateChange', { oldState, newState, timestamp: Date.now() })
  }

  // 🎯 添加开箱记录
  const addOpeningRecord = (playerIndex: number, item: any, round?: number) => {
    const targetRound = round || executingRound.value
    
    console.log('[🎬ROUND-ANIMATION] 📝 添加开箱记录', { playerIndex, targetRound, item })
    
    // 获取玩家ID
    const playerId = `player_${playerIndex}` // 简化的玩家ID，实际应该使用真实UID
    
    // 添加到状态同步器
    battleStateSync.addPlayerRecord(playerId, item, targetRound - 1)
    
    // 发送动画完成事件
    emitAnimationEvent({
      type: 'animation_complete',
      round: targetRound,
      playerIndex,
      item,
      timestamp: Date.now()
    })
    
    // 触发记录添加回调
    triggerCallbacks('recordAdded', { playerIndex, item, round: targetRound })
  }

  // 🎯 强制停止执行
  const forceStop = () => {
    console.log('[🎬ROUND-ANIMATION] ⏹️ 强制停止执行')
    
    // 清除所有定时器
    if (executionTimer.value) {
      clearTimeout(executionTimer.value)
      executionTimer.value = null
    }
    if (intervalTimer.value) {
      clearTimeout(intervalTimer.value)
      intervalTimer.value = null
    }
    
    // 重置状态
    resetExecutionState()
  }

  // 🎯 重置执行状态
  const resetExecutionState = () => {
    currentExecutionState.value = RoundExecutionState.WAITING
    executingRound.value = 0
    executingPlayerIndex.value = -1
    currentRoundMarker.value = 0
    executingCaseId.value = null
    stateStartTime.value = 0
    activeAnimations.value.clear()
  }

  // 🎯 获取状态进度
  const getStateProgress = (state: RoundExecutionState): number => {
    const elapsed = Date.now() - stateStartTime.value
    
    switch (state) {
      case RoundExecutionState.PREPARING:
        return Math.min(elapsed / executionConfig.preparationDelay, 1)
      case RoundExecutionState.ANIMATING:
        return Math.min(elapsed / executionConfig.animationDuration, 1)
      case RoundExecutionState.COMPLETING:
        return Math.min(elapsed / executionConfig.completionDelay, 1)
      case RoundExecutionState.INTERVAL:
        return Math.min(elapsed / executionConfig.intervalDuration, 1)
      default:
        return 0
    }
  }

  // 🎯 获取剩余时间
  const getTimeRemaining = (): number => {
    const elapsed = Date.now() - stateStartTime.value
    const state = currentExecutionState.value
    
    switch (state) {
      case RoundExecutionState.PREPARING:
        return Math.max(executionConfig.preparationDelay - elapsed, 0)
      case RoundExecutionState.ANIMATING:
        return Math.max(executionConfig.animationDuration - elapsed, 0)
      case RoundExecutionState.COMPLETING:
        return Math.max(executionConfig.completionDelay - elapsed, 0)
      case RoundExecutionState.INTERVAL:
        return Math.max(executionConfig.intervalDuration - elapsed, 0)
      default:
        return 0
    }
  }

  // 🎯 事件发射
  const emitAnimationEvent = (event: AnimationEvent) => {
    animationHistory.value.push(event)
    
    // 限制历史记录长度
    if (animationHistory.value.length > 100) {
      animationHistory.value = animationHistory.value.slice(-50)
    }
    
    console.log('[🎬ROUND-ANIMATION] 📡 发送事件', event)
    
    // 发送到全局事件系统
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent(`battle:${event.type}`, {
        detail: event
      }))
    }
  }

  // 🎯 设置事件监听器
  const setupEventListeners = () => {
    // 监听WebSocket事件以保持同步
    if (typeof window !== 'undefined') {
      window.addEventListener('socket:round_start', handleSocketRoundStart)
      window.addEventListener('socket:opening_start', handleSocketOpeningStart)
      window.addEventListener('socket:round_result', handleSocketRoundResult)
    }
  }

  // 🎯 Socket事件处理器
  const handleSocketRoundStart = (event: any) => {
    const data = event.detail
    console.log('[🎬ROUND-ANIMATION] 🔄 Socket轮次开始', data)
    // 可以在这里同步服务器状态
  }

  const handleSocketOpeningStart = (event: any) => {
    const data = event.detail
    console.log('[🎬ROUND-ANIMATION] 🔄 Socket开箱开始', data)
    // 可以在这里同步开箱状态
  }

  const handleSocketRoundResult = (event: any) => {
    const data = event.detail
    console.log('[🎬ROUND-ANIMATION] 🔄 Socket轮次结果', data)
    // 可以在这里处理服务器返回的结果
  }

  // 🎯 注册回调
  const onEvent = (eventType: string, callback: Function) => {
    if (!eventCallbacks.value.has(eventType)) {
      eventCallbacks.value.set(eventType, [])
    }
    eventCallbacks.value.get(eventType)!.push(callback)
    
    return () => {
      const callbacks = eventCallbacks.value.get(eventType)
      if (callbacks) {
        const index = callbacks.indexOf(callback)
        if (index > -1) {
          callbacks.splice(index, 1)
        }
      }
    }
  }

  // 🎯 触发回调
  const triggerCallbacks = (eventType: string, data: any) => {
    const callbacks = eventCallbacks.value.get(eventType)
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error('[🎬ROUND-ANIMATION] ❌ 回调执行错误', error)
        }
      })
    }
  }

  // 🎯 清理资源
  const cleanup = () => {
    console.log('[🎬ROUND-ANIMATION] 🧹 清理资源')
    
    forceStop()
    
    // 移除事件监听器
    if (typeof window !== 'undefined') {
      window.removeEventListener('socket:round_start', handleSocketRoundStart)
      window.removeEventListener('socket:opening_start', handleSocketOpeningStart)
      window.removeEventListener('socket:round_result', handleSocketRoundResult)
    }
    
    // 清空回调队列
    eventCallbacks.value.clear()
    
    isInitialized.value = false
  }

  // 🎯 组件卸载时自动清理
  onUnmounted(() => {
    cleanup()
  })

  return {
    // 状态访问
    isInitialized: readonly(isInitialized),
    currentExecutionState: readonly(currentExecutionState),
    executingRound: readonly(executingRound),
    currentRoundMarker: readonly(currentRoundMarker),
    executingCaseId: readonly(executingCaseId),
    
    // 计算属性
    getCurrentRoundState,
    
    // 核心方法
    initialize,
    startRoundExecution,
    addOpeningRecord,
    forceStop,
    cleanup,
    
    // 配置管理
    updateConfig: (config: Partial<RoundExecutionConfig>) => {
      Object.assign(executionConfig, config)
    },
    getConfig: () => ({ ...executionConfig }),
    
    // 事件系统
    onEvent,
    
    // 调试工具
    getAnimationHistory: () => [...animationHistory.value],
    getDebugInfo: () => ({
      state: currentExecutionState.value,
      round: executingRound.value,
      marker: currentRoundMarker.value,
      caseId: executingCaseId.value,
      progress: getStateProgress(currentExecutionState.value),
      timeRemaining: getTimeRemaining(),
      isInitialized: isInitialized.value
    })
  }
}