# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 开发命令

**开发服务器：**
- `npm run dev` / `pnpm dev` / `yarn dev` / `bun run dev` - 启动开发服务器 http://localhost:3000

**构建命令：**
- `npm run build` / `pnpm build` / `yarn build` / `bun run build` - 生产环境构建
- `npm run preview` / `pnpm preview` / `yarn preview` / `bun run preview` - 预览生产构建
- `npm run generate` / `pnpm generate` / `yarn generate` / `bun run generate` - 生成静态网站

**其他命令：**
- `npm run postinstall` / `pnpm postinstall` - 安装后准备 Nuxt 环境

## 项目架构

这是一个基于 **Vue 3 + Nuxt 3 + TypeScript** 构建的 CS:GO 皮肤交易平台前端。核心架构模式：

### 核心技术栈
- **框架：** Nuxt 3（禁用SSR，SPA模式）
- **语言：** TypeScript 严格模式
- **状态管理：** Pinia 全局状态，VueUse 工具函数
- **UI框架：** Tailwind CSS + UnoCSS 原子化样式
- **实时通信：** Socket.IO 客户端 WebSocket 连接
- **国际化：** @nuxtjs/i18n（默认简体中文 zh-hans，英文 en）
- **动画：** GSAP、auto-animate、Three.js 复杂特效

### 目录结构
- `components/` - 按功能组织的 Vue 组件（auth/、battle/、case/、ui/ 等）
- `composables/` - 可复用组合函数，大量用于对战系统
- `pages/` - 基于文件的路由（battle/[id].vue 用于动态对战房间）
- `stores/` - Pinia 状态管理（battle.ts、socket.ts、user.ts 等）
- `services/` - API 服务层（battle-api.ts、auth-api.ts 等）
- `types/` - TypeScript 类型定义
- `locales/` - 翻译文件（zh-hans.json、en.json）
- `docs/` - 功能和API的中文文档

### 对战系统架构

对战系统是核心功能，具有复杂的实时交互：

**状态管理模式：**
- `useBattleCore()` - 主协调器组合函数
- `useBattleState()` - 集中状态管理
- `useBattleActions()` - 用户操作处理器
- `useBattleWebSocket()` - 实时通信
- `useBattleAnimations()` - 同步动画

**WebSocket 事件流：**
- Socket 事件通过 `stores/socket.ts` 处理
- 事件通过自定义浏览器事件分发到特定组合函数
- 对战房间支持实时玩家加入/离开、开箱动画
- 跨多客户端动画同步，使用时间戳协调

**关键组合函数：**
- `useBattleCore()` - 主对战控制器，初始化所有其他组合函数
- `useBattleAnimationSync()` - 处理跨客户端动画时序
- `useBattleAudio()` - 音效管理
- `useBattleReconnection()` - 连接恢复逻辑

### API 架构

**服务层模式：**
所有 API 调用通过 `services/` 中的服务类抽象：
- `battleApi` - 对战房间CRUD操作、加入/离开/创建
- `authApi` - 认证和CSRF令牌管理
- `caseApi` - 开箱和物品管理

**响应格式：**
```typescript
interface ApiResponse<T> {
  code: number
  message: string
  body: T
}
```

**CSRF 保护：**
API使用从 `/api/auth/csrf/` 获取的CSRF令牌，在 `X-CSRFToken` 头中发送。

### 实时通信

**Socket.IO 集成：**
- 插件：`plugins/socket.client.ts` - 客户端socket初始化
- 存储：`stores/socket.ts` - 消息处理和事件分发
- 事件：`['boxroom', 'boxroomdetail', 'monitor', 'stats']` 消息类型

**消息处理流程：**
1. 在 `socket.ts` 中接收原始socket消息
2. 解析为带有 action/payload 结构的类型化事件
3. 作为自定义浏览器事件分发到特定组合函数
4. 状态更新触发UI响应性

**对战房间事件：**
- `boxroom` 事件：房间创建、更新、玩家加入/离开
- `boxroomdetail` 事件：开箱动画、回合结果
- 自动房间数据同步，乐观UI更新

### 响应式设计策略

**设备优先方法：**
- 桌面优先设计，移动端适配
- 使用 VueUse `useBreakpoints()` 进行设备检测
- 基于设备类型使用 `v-if` 条件渲染
- 需要时分离移动/桌面组件变体

**断点：**
- 移动端：< 768px
- 平板：768px - 1024px  
- 桌面：> 1024px

### 国际化系统

**语言配置：**
- 默认：简体中文（zh-hans）
- 次要：英文（en）
- 策略：'no_prefix' 自动浏览器检测
- 所有用户界面文本必须使用翻译键

**翻译结构：**
```json
{
  "page.battle.title": "对战房间",
  "component.auth.login": "登录",
  "common.loading": "加载中..."
}
```

**SEO 集成：**
- 基于当前语言的动态元标签
- 所有页面组件必须使用 `useSeoMeta()`
- 支持特定语言URL

### 状态管理模式

**Pinia 存储组织：**
- `stores/battle.ts` - 对战房间、WebSocket集成、缓存
- `stores/socket.ts` - Socket连接状态和消息路由
- `stores/user.ts` - 认证和用户资料
- `stores/app.ts` - 全局应用状态和偏好

**缓存策略：**
- 对战房间列表和箱子数据5分钟缓存
- WebSocket更新时自动缓存失效
- 乐观UI更新提升用户体验

### 开发指南

**组件开发：**
- 仅使用 `<script setup>` 语法
- 必须TypeScript严格模式
- 仅组合API，禁止选项API
- 所有组件必须支持国际化
- 必须移动/桌面响应式设计

**代码组织顺序：**
1. 组件导入
2. 组合函数（useI18n、useBreakpoints等）
3. 设备检测和响应式状态
4. 工具函数
5. 常量和配置
6. TypeScript类型定义
7. SEO元数据（页面组件）

**错误处理：**
- 使用骨架屏而非加载图标
- 错误记录到控制台，避免向用户显示原始错误
- 离线/连接问题的优雅降级
- WebSocket重连使用指数退避

### 测试和调试

**演示页面：**
- 位于 `pages/demo/` 目录
- 演示页面链接在 `pages/demo/index.vue`
- 测试可使用模拟数据，生产页面不可使用
- 中文文档在 `docs/` 目录

**调试工具：**
- 控制台中的Socket连接调试消息
- 通过组合函数检查对战动画状态
- 开发模式下WebSocket消息日志

### 构建配置

**Nuxt 配置：**
- 禁用SSR（`ssr: false`）SPA模式
- 开发期间API代理到后端
- 启用TypeScript严格模式
- 基于环境的API端点配置

**资源优化：**
- WebP图片懒加载
- 大型库代码分割（ECharts、Three.js）
- 启用tree-shaking优化包大小

### 重要说明

**Socket消息格式：**
消息遵循模式：`[messageType, action, payload, socketId?]`
示例：`['boxroom', 'new', roomData]` 或 `['boxroomdetail', 'round', betData, socketId]`

**动画同步：**
对战动画需要跨客户端精确时序。使用服务器时间戳和客户端时间同步进行协调。

**CSRF 要求：**
所有POST/PUT/DELETE请求需要CSRF令牌。对战API服务自动处理。

**中文文档：**
功能文档用中文编写，存储在 `docs/` 目录。API文档使用英文命名约定。