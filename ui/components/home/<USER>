<template>
  <div class="mobile-home-content">
    <!-- 英雄轮播 - 移动端版本 -->
    <div class="mb-4">
      <HeroCarousel :slides="props.heroSlides" :autoplay="true" />
    </div>

    <!-- 实时开箱记录 -->
    <div class="mb-4">
      <LiveOpenings :key="`live-openings-mobile-${localeKey}`" />
    </div>

    <!-- 热门武器箱 -->
    <div class="bg-gray-800/50 rounded-xl p-4 mb-4">
      <div class="flex justify-between items-center mb-3">
        <h2 class="text-lg font-bold text-white flex items-center">
          <i class="iconfont icon-remen2 text-orange-500 text-base mr-2"></i>
          {{ $t('cases.hot_cases') }}
        </h2>
        <NuxtLink to="/cases" class="text-blue-400 text-sm">
          {{ $t('common.view_all') }}
        </NuxtLink>
      </div>
      
      <div v-if="props.hotCases.length > 0" class="overflow-x-auto">
        <div class="flex space-x-3 pb-2">
          <div
            v-for="caseItem in props.hotCases.slice(0, 5)"
            :key="caseItem.id"
            class="flex-shrink-0 w-32 bg-gray-700/50 rounded-xl overflow-hidden"
          >
            <img
              :src="caseItem.cover || caseItem.image"
              :alt="caseItem.name"
              class="w-full h-32 object-cover"
            />
            <div class="p-2">
              <h3 class="text-white text-xs font-medium mb-1 line-clamp-1">
                {{ caseItem.name }}
              </h3>
              <div class="flex items-center justify-between">
                <span class="text-green-400 text-xs font-bold">
                  ${{ caseItem.price.toFixed(2) }}
                </span>
                <button class="bg-blue-600 text-white text-xs px-2 py-1 rounded">
                  {{ $t('cases.open') }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div v-else class="mobile-cases-skeleton">
        <div class="flex space-x-3 pb-2">
          <div v-for="i in 5" :key="i" class="skeleton-case-mobile"></div>
        </div>
      </div>
    </div>

    <!-- 精选皮肤推荐 -->
    <div class="bg-gray-800/50 rounded-xl p-4">
      <div class="flex justify-between items-center mb-3">
        <h2 class="text-lg font-bold text-white flex items-center">
          <i class="iconfont icon-tuijian text-purple-500 text-base mr-2"></i>
          {{ $t('skins.featured_skins') }}
        </h2>
        <div class="flex items-center space-x-2">
          <button 
            @click="$emit('refreshRandomSkins')"
            class="text-blue-400 text-sm"
          >
            {{ $t('common.refresh') }}
          </button>
          <NuxtLink to="/skins" class="text-blue-400 text-sm">
            {{ $t('common.view_all') }}
          </NuxtLink>
        </div>
      </div>
      
      <div v-if="props.randomSkins.length > 0" class="grid grid-cols-2 gap-3">
        <div
          v-for="skin in props.randomSkins.slice(0, 4)"
          :key="skin.id"
          class="bg-gray-700/50 rounded-xl overflow-hidden"
        >
          <img
            :src="skin.image"
            :alt="skin.name"
            class="w-full h-20 object-cover"
            loading="lazy"
          />
          <div class="p-2">
            <h3 class="text-white text-xs font-medium mb-1 line-clamp-1">
              {{ skin.name }}
            </h3>
            <div class="flex items-center justify-between">
              <span class="text-green-400 text-xs font-bold">
                ${{ skin.price.toFixed(2) }}
              </span>
              <button class="bg-purple-600 text-white text-xs px-2 py-1 rounded">
                {{ $t('skins.view') }}
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <div v-else class="mobile-skins-skeleton">
        <div class="grid grid-cols-2 gap-3">
          <div v-for="i in 4" :key="i" class="skeleton-skin-mobile"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { BannerItem } from '~/types/banner'
import type { CaseItem } from '~/types/case'
import type { SkinItem } from '~/types/skin'

// 导入组件
import HeroCarousel from '~/components/home/<USER>'

// 组件props
interface Props {
  heroSlides: BannerItem[]
  hotCases: CaseItem[]
  randomSkins: SkinItem[]
}

const props = defineProps<Props>()

// 组件事件
const emit = defineEmits<{
  refreshRandomSkins: []
}>()

// 国际化
const { $t } = useNuxtApp()

// 获取语言状态用于强制组件重新渲染
const { locale } = useI18n()
const localeKey = computed(() => locale.value)
</script>

<style scoped lang="scss">
.mobile-home-content {
  width: 100%;
}

.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.overflow-x-auto {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.overflow-x-auto::-webkit-scrollbar {
  height: 4px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 2px;
}

/* 移动端骨架屏样式 */
.mobile-cases-skeleton, .mobile-skins-skeleton {
  .skeleton-case-mobile {
    flex-shrink: 0;
    width: 128px; // w-32
    height: 160px;
    background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
    border-radius: 0.5rem;
    position: relative;
    overflow: hidden;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.1),
        transparent
      );
      animation: skeleton-shimmer 2s infinite;
    }

    &::after {
      content: '📦';
      position: absolute;
      top: 40%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 1.5rem;
      opacity: 0.3;
      animation: skeleton-pulse 2s ease-in-out infinite;
    }
  }

  .skeleton-skin-mobile {
    width: 100%;
    height: 120px;
    background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
    border-radius: 0.5rem;
    position: relative;
    overflow: hidden;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.1),
        transparent
      );
      animation: skeleton-shimmer 2s infinite;
    }

    &::after {
      content: '🔫';
      position: absolute;
      top: 40%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 1.2rem;
      opacity: 0.3;
      animation: skeleton-pulse 2s ease-in-out infinite;
    }
  }
}

/* 动画定义 */
@keyframes skeleton-shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes skeleton-pulse {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.1;
  }
}
</style> 