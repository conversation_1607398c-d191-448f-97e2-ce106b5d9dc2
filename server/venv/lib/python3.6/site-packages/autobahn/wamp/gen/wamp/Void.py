# automatically generated by the FlatBuffers compiler, do not modify

# namespace: wamp

import flatbuffers

class Void(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAsVoid(cls, buf, offset):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = Void()
        x.Init(buf, n + offset)
        return x

    # Void
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

def VoidStart(builder): builder.StartObject(0)
def VoidEnd(builder): return builder.EndObject()
