#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复后的异步集成模块
解决Python 3.6兼容性和数据类型问题
"""

import logging
import sys
import json
import traceback
from typing import Dict, List, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class CompatibleAsyncManager:
    """兼容的异步管理器"""
    
    @staticmethod
    def handle_round_completion_compatible(room, room_round, current_round, total_rounds, bets):
        """兼容的轮次完成处理"""
        try:
            # 检查Python版本和asyncio支持
            if sys.version_info >= (3, 7):
                # Python 3.7+ 使用真正的异步
                return CompatibleAsyncManager._handle_async_modern(
                    room, room_round, current_round, total_rounds, bets
                )
            else:
                # Python 3.6 使用同步处理
                logger.info(f"Python 3.6环境，使用同步处理: room={room.short_id}")
                return CompatibleAsyncManager._handle_sync_fallback(
                    room, room_round, current_round, total_rounds, bets
                )
                
        except Exception as e:
            logger.error(f"轮次完成处理失败: room={room.short_id}, error={e}")
            # 最终降级到简单同步处理
            return CompatibleAsyncManager._handle_simple_sync(
                room, room_round, current_round, total_rounds, bets
            )
    
    @staticmethod
    def _handle_async_modern(room, room_round, current_round, total_rounds, bets):
        """现代异步处理 (Python 3.7+)"""
        import asyncio
        
        async def async_completion():
            try:
                # 准备轮次结果
                round_results = CompatibleAsyncManager._prepare_round_results(
                    room, room_round, bets
                )
                
                # 发送轮次结果消息
                await CompatibleAsyncManager._send_round_result_async(
                    room.uid, round_results, current_round, total_rounds
                )
                
                # 检查是否需要进行下一轮
                if current_round < total_rounds:
                    await CompatibleAsyncManager._schedule_next_round(
                        room, current_round + 1, total_rounds
                    )
                else:
                    await CompatibleAsyncManager._handle_battle_end_async(room)
                
                logger.info(f"异步轮次处理完成: room={room.short_id}, round={current_round}")
                
            except Exception as e:
                logger.error(f"异步处理失败: room={room.short_id}, error={e}")
                # 降级到同步
                CompatibleAsyncManager._handle_sync_fallback(
                    room, room_round, current_round, total_rounds, bets
                )
        
        # 使用现代异步API
        try:
            asyncio.create_task(async_completion())
            return True
        except Exception as e:
            logger.error(f"创建异步任务失败: {e}")
            return False
    
    @staticmethod
    def _handle_sync_fallback(room, room_round, current_round, total_rounds, bets):
        """同步降级处理"""
        try:
            # 准备轮次结果
            round_results = CompatibleAsyncManager._prepare_round_results(
                room, room_round, bets
            )
            
            # 同步发送轮次结果
            CompatibleAsyncManager._send_round_result_sync(
                room.uid, round_results, current_round, total_rounds
            )
            
            # 检查是否需要进行下一轮
            if current_round < total_rounds:
                CompatibleAsyncManager._schedule_next_round_sync(
                    room, current_round + 1, total_rounds
                )
            else:
                CompatibleAsyncManager._handle_battle_end_sync(room)
            
            logger.info(f"同步轮次处理完成: room={room.short_id}, round={current_round}")
            return True
            
        except Exception as e:
            logger.error(f"同步处理失败: room={room.short_id}, error={e}")
            return False
    
    @staticmethod
    def _handle_simple_sync(room, room_round, current_round, total_rounds, bets):
        """简单同步处理 - 最终降级方案"""
        try:
            from .models import CaseRoomRound, GameState
            
            # 检查是否还有未开启的轮次
            remaining_rounds = CaseRoomRound.objects.filter(
                room=room, 
                opened=False
            ).exclude(id=room_round.id)
            
            if remaining_rounds.exists():
                logger.info(f"还有 {remaining_rounds.count()} 轮次待处理: room={room.short_id}")
            else:
                # 结束对战
                logger.info(f"所有轮次完成，结束对战: room={room.short_id}")
                CompatibleAsyncManager._end_battle_simple(room)
            
            return True
            
        except Exception as e:
            logger.error(f"简单同步处理失败: room={room.short_id}, error={e}")
            return False
    
    @staticmethod
    def _prepare_round_results(room, room_round, bets):
        """准备轮次结果数据"""
        try:
            from .models import CaseRoomItem
            
            results = []
            for bet in bets:
                # 获取该bet在此轮次的物品
                bet_items = CaseRoomItem.objects.filter(
                    room=room, 
                    round=room_round, 
                    bet=bet
                )
                
                items_data = []
                total_value = 0
                
                for item in bet_items:
                    item_data = {
                        'item_name': item.item_info.market_hash_name,
                        'price': float(item.price) if item.price else 0.0,
                        'item_type': item.item_type,
                        'rarity': getattr(item.item_info, 'rarity', 'common')
                    }
                    items_data.append(item_data)
                    total_value += item_data['price']
                
                user_result = {
                    'user_uid': str(bet.user.uid),
                    'username': bet.user.steam.personaname if hasattr(bet.user, 'steam') else bet.user.username,
                    'items': items_data,
                    'total_value': round(total_value, 2),
                    'open_amount': float(bet.open_amount) if bet.open_amount else 0.0
                }
                results.append(user_result)
            
            return results
            
        except Exception as e:
            logger.error(f"准备轮次结果失败: room={room.uid}, error={e}")
            return []
    
    @staticmethod
    async def _send_round_result_async(room_uid, results, current_round, total_rounds):
        """异步发送轮次结果"""
        try:
            from django_redis import get_redis_connection
            
            message_data = {
                'room_uid': str(room_uid),
                'round': int(current_round),
                'total_rounds': int(total_rounds),
                'results': results,
                'timestamp': int(datetime.now().timestamp() * 1000),
                'message_type': 'round_result'
            }
            
            # 确保消息格式正确 - 修复 "expected str instance, list found" 错误
            websocket_message = ['boxroom', 'round_result', message_data]
            
            redis_client = get_redis_connection('default')
            redis_client.publish('ws_channel', json.dumps(websocket_message, ensure_ascii=False, default=str))
            
            logger.info(f"异步轮次结果发送成功: room={room_uid}, round={current_round}")
            
        except Exception as e:
            logger.error(f"异步发送轮次结果失败: room={room_uid}, error={e}")
    
    @staticmethod
    def _send_round_result_sync(room_uid, results, current_round, total_rounds):
        """同步发送轮次结果"""
        try:
            from django_redis import get_redis_connection
            
            message_data = {
                'room_uid': str(room_uid),
                'round': int(current_round),
                'total_rounds': int(total_rounds),
                'results': results,
                'timestamp': int(datetime.now().timestamp() * 1000),
                'message_type': 'round_result'
            }
            
            # 确保消息格式正确
            websocket_message = ['boxroom', 'round_result', message_data]
            
            redis_client = get_redis_connection('default')
            redis_client.publish('ws_channel', json.dumps(websocket_message, ensure_ascii=False, default=str))
            
            logger.info(f"同步轮次结果发送成功: room={room_uid}, round={current_round}")
            
        except Exception as e:
            logger.error(f"同步发送轮次结果失败: room={room_uid}, error={e}")
    
    @staticmethod
    async def _schedule_next_round(room, next_round, total_rounds):
        """异步调度下一轮"""
        try:
            import asyncio
            
            # 延迟2秒开始下一轮
            await asyncio.sleep(2.0)
            
            # 获取下一轮的回合
            from .models import CaseRoomRound
            next_round_obj = CaseRoomRound.objects.filter(
                room=room,
                opened=False
            ).order_by('create_time').first()
            
            if next_round_obj:
                # 触发下一轮处理
                from .business_room_optimized import optimized_open_room_case
                optimized_open_room_case(room, next_round_obj)
            
            logger.info(f"下一轮调度完成: room={room.short_id}, round={next_round}")
            
        except Exception as e:
            logger.error(f"调度下一轮失败: room={room.short_id}, error={e}")
    
    @staticmethod
    def _schedule_next_round_sync(room, next_round, total_rounds):
        """同步调度下一轮"""
        try:
            import time
            import threading
            
            def delayed_next_round():
                time.sleep(2.0)  # 延迟2秒
                
                try:
                    from .models import CaseRoomRound
                    next_round_obj = CaseRoomRound.objects.filter(
                        room=room,
                        opened=False
                    ).order_by('create_time').first()
                    
                    if next_round_obj:
                        from .business_room_optimized import optimized_open_room_case
                        optimized_open_room_case(room, next_round_obj)
                    
                    logger.info(f"同步下一轮处理完成: room={room.short_id}, round={next_round}")
                    
                except Exception as e:
                    logger.error(f"延迟处理下一轮失败: room={room.short_id}, error={e}")
            
            # 在新线程中延迟执行
            thread = threading.Thread(target=delayed_next_round)
            thread.daemon = True
            thread.start()
            
        except Exception as e:
            logger.error(f"同步调度下一轮失败: room={room.short_id}, error={e}")
    
    @staticmethod
    async def _handle_battle_end_async(room):
        """异步处理对战结束"""
        try:
            await CompatibleAsyncManager._end_battle_common(room)
            logger.info(f"异步对战结束处理完成: room={room.short_id}")
            
        except Exception as e:
            logger.error(f"异步对战结束失败: room={room.short_id}, error={e}")
    
    @staticmethod
    def _handle_battle_end_sync(room):
        """同步处理对战结束"""
        try:
            import asyncio
            
            # 在同步环境中运行异步代码
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # 如果事件循环正在运行，创建任务
                    asyncio.create_task(CompatibleAsyncManager._end_battle_common(room))
                else:
                    # 运行异步代码
                    loop.run_until_complete(CompatibleAsyncManager._end_battle_common(room))
            except RuntimeError:
                # 没有事件循环，直接同步处理
                CompatibleAsyncManager._end_battle_simple(room)
                
            logger.info(f"同步对战结束处理完成: room={room.short_id}")
            
        except Exception as e:
            logger.error(f"同步对战结束失败: room={room.short_id}, error={e}")
            # 最终降级
            CompatibleAsyncManager._end_battle_simple(room)
    
    @staticmethod
    async def _end_battle_common(room):
        """通用对战结束处理"""
        try:
            from .models import CaseRoomBet, GameState, CaseRoomItem, PackageItem
            from steambase.enums import PackageState, PackageSourceType
            import random
            
            # 获取所有参与者
            bets = CaseRoomBet.objects.filter(room=room).order_by('-open_amount')
            
            if bets.count() == 0:
                logger.warning(f"房间无参与者，取消对战: room={room.short_id}")
                room.state = GameState.Cancelled.value
                room.save()
                return
            
            # 重置胜利状态
            for bet in bets:
                bet.victory = 0
                bet.win_amount = bet.win_amount or 0
                bet.win_items_count = bet.win_items_count or 0
                bet.save()
            
            # 确定获胜者
            winner = bets[0]
            if len(bets) >= 2 and bets[0].open_amount == bets[1].open_amount:
                # 平局随机决定获胜者
                winner = random.choice(bets[:2])
            
            # 处理战利品分配
            lose_bets = bets.exclude(id=winner.id)
            total_win_value = 0
            
            for lose_bet in lose_bets:
                bet_items = CaseRoomItem.objects.filter(room=room, bet=lose_bet)
                for item in bet_items:
                    # 将失败者的物品给获胜者
                    PackageItem.objects.create(
                        user=winner.user,
                        item_info=item.item_info,
                        assetid='0',
                        source=PackageSourceType.BattleRoom.value,
                        instanceid='0',
                        state=PackageState.Available.value,
                        amount=item.price,
                        case_name="对战胜利"
                    )
                    
                    total_win_value += item.price
                    winner.win_items_count += 1
            
            # 更新获胜者信息
            winner.victory = 1
            winner.win_amount += total_win_value
            winner.save()
            
            # 更新房间状态
            room.state = GameState.End.value
            room.save()
            
            # 发送对战结束消息
            await CompatibleAsyncManager._send_battle_end_message(room, winner, bets)
            
            logger.info(f"对战结束: room={room.short_id}, winner={winner.user.username}, win_value={total_win_value}")
            
        except Exception as e:
            logger.error(f"对战结束处理失败: room={room.short_id}, error={e}")
            logger.error(traceback.format_exc())
    
    @staticmethod
    def _end_battle_simple(room):
        """简单对战结束处理"""
        try:
            from .models import GameState
            
            room.state = GameState.End.value
            room.save()
            
            logger.info(f"简单对战结束: room={room.short_id}")
            
        except Exception as e:
            logger.error(f"简单对战结束失败: room={room.short_id}, error={e}")
    
    @staticmethod
    async def _send_battle_end_message(room, winner, all_bets):
        """发送对战结束消息"""
        try:
            from django_redis import get_redis_connection
            
            # 准备最终结果数据
            final_results = []
            for bet in all_bets:
                final_results.append({
                    'user_uid': str(bet.user.uid),
                    'username': bet.user.username,
                    'open_amount': float(bet.open_amount or 0),
                    'win_amount': float(bet.win_amount or 0),
                    'victory': bet.victory,
                    'win_items_count': bet.win_items_count or 0
                })
            
            message_data = {
                'room_uid': str(room.uid),
                'winner': {
                    'user_uid': str(winner.user.uid),
                    'username': winner.user.username,
                    'total_win': float(winner.win_amount or 0)
                },
                'final_results': final_results,
                'timestamp': int(datetime.now().timestamp() * 1000),
                'message_type': 'battle_end'
            }
            
            websocket_message = ['boxroom', 'battle_end', message_data]
            
            redis_client = get_redis_connection('default')
            redis_client.publish('ws_channel', json.dumps(websocket_message, ensure_ascii=False, default=str))
            
            logger.info(f"对战结束消息发送成功: room={room.uid}")
            
        except Exception as e:
            logger.error(f"发送对战结束消息失败: room={room.uid}, error={e}")

# 导出集成函数
def get_integration_functions():
    """获取集成函数"""
    return {
        'handle_round_completion_async': CompatibleAsyncManager.handle_round_completion_compatible,
        'handle_round_completion_sync': CompatibleAsyncManager._handle_sync_fallback,
    }

# 向后兼容
integration_functions = get_integration_functions()
