# Generated by Django 3.2.25 on 2025-07-19 03:46

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('box', '0040_case_case_category'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='case',
            options={'ordering': ('case_type', 'sort'), 'verbose_name': '箱子', 'verbose_name_plural': 'Case'},
        ),
        migrations.AlterModelOptions(
            name='casebot',
            options={'verbose_name': '箱子机器人配置', 'verbose_name_plural': 'Case Bot Config'},
        ),
        migrations.AlterModelOptions(
            name='casecategory',
            options={'ordering': ('sort',), 'verbose_name': '箱子分类', 'verbose_name_plural': 'Case Category'},
        ),
        migrations.AlterModelOptions(
            name='casekeyconfig',
            options={'verbose_name': '箱子钥匙配置', 'verbose_name_plural': 'CaseKeyConfig'},
        ),
        migrations.AlterModelOptions(
            name='caserecord',
            options={'verbose_name': '箱子记录', 'verbose_name_plural': 'Case Record'},
        ),
        migrations.AlterModelOptions(
            name='caseroom',
            options={'verbose_name': '箱子房间', 'verbose_name_plural': 'Case Room'},
        ),
        migrations.AlterModelOptions(
            name='caseroombet',
            options={'verbose_name': '箱子房间下注', 'verbose_name_plural': 'Case Room Bet'},
        ),
        migrations.AlterModelOptions(
            name='caseroomitem',
            options={'verbose_name': '箱子房间物品', 'verbose_name_plural': 'Case Room Item'},
        ),
        migrations.AlterModelOptions(
            name='caseroomround',
            options={'verbose_name': '箱子房间轮次', 'verbose_name_plural': 'Case Room Round'},
        ),
        migrations.AlterModelOptions(
            name='casestatisticsday',
            options={'ordering': ('-create_time',), 'verbose_name': '箱子日统计', 'verbose_name_plural': 'Case Statistics Day'},
        ),
        migrations.AlterModelOptions(
            name='casestatisticsmonth',
            options={'ordering': ('-create_time',), 'verbose_name': '箱子月统计', 'verbose_name_plural': 'Case Statistics Month'},
        ),
        migrations.AlterModelOptions(
            name='casetype',
            options={'ordering': ('sort',), 'verbose_name': '箱子类型', 'verbose_name_plural': 'Case Type'},
        ),
        migrations.AlterModelOptions(
            name='dropdayrank',
            options={'verbose_name': '掉落日排行', 'verbose_name_plural': 'Drop Day Rank'},
        ),
        migrations.AlterModelOptions(
            name='dropitem',
            options={'verbose_name': '掉落物品', 'verbose_name_plural': 'Drop Item'},
        ),
        migrations.AlterModelOptions(
            name='festivalcaseconfig',
            options={'ordering': ('level',), 'verbose_name': '节日箱子配置', 'verbose_name_plural': 'Festival Case Config'},
        ),
        migrations.AlterModelOptions(
            name='festivalcasedate',
            options={'ordering': ('month', 'day'), 'verbose_name': '节日箱子日期', 'verbose_name_plural': 'Festival Case Date'},
        ),
        migrations.AlterModelOptions(
            name='festivalcaserecord',
            options={'verbose_name': '节日箱子记录', 'verbose_name_plural': 'Festival Case Record'},
        ),
        migrations.AlterModelOptions(
            name='freecaseconfig',
            options={'ordering': ('level',), 'verbose_name': '免费箱子配置', 'verbose_name_plural': 'Free Case Config'},
        ),
        migrations.AlterModelOptions(
            name='giveawayitems',
            options={'verbose_name': '赠送物品', 'verbose_name_plural': 'GiveawayItems'},
        ),
        migrations.AlterModelOptions(
            name='incomedayrank',
            options={'verbose_name': '收入日排行', 'verbose_name_plural': 'Income Day Rank'},
        ),
        migrations.AlterModelOptions(
            name='loseweekrank',
            options={'verbose_name': '亏损周排行', 'verbose_name_plural': 'Lose Week Rank'},
        ),
        migrations.AlterModelOptions(
            name='roomdayrank',
            options={'verbose_name': '房间日排行', 'verbose_name_plural': 'Room Day Rank'},
        ),
        migrations.AlterField(
            model_name='casebot',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='case_bot', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
        migrations.AlterField(
            model_name='caserecord',
            name='user',
            field=models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='case_records', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
        migrations.AlterField(
            model_name='caseroom',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='case_room', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
        migrations.AlterField(
            model_name='caseroombet',
            name='user',
            field=models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='case_room_bets', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
        migrations.AlterField(
            model_name='dropdayrank',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='drop_day_rank', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
        migrations.AlterField(
            model_name='festivalcaserecord',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='festival_record', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
        migrations.AlterField(
            model_name='incomedayrank',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='income_day_rank', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
        migrations.AlterField(
            model_name='loseweekrank',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lose_week_rank', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
        migrations.AlterField(
            model_name='roomdayrank',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='room_day_rank', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
    ]
