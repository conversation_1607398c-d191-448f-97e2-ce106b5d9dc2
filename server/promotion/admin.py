import json
import logging
import os

from django.contrib import admin, messages
from django.contrib.admin import TabularInline, StackedInline
from django.core.cache import cache
from django.utils.translation import gettext_lazy as _

from jet.filters import DateRangeFilter

from promotion.service.admin_actions import promotion_export_to_excel
from steambase.admin import ReadOnlyAdmin, ChangeOnlyAdmin

from promotion.models import UserPromotion, PromotionRecord, PromotionLevelConfig, PromotionCaseConfig

_logger = logging.getLogger(__name__)


@admin.register(UserPromotion)
class UserPromotionAdmin(ChangeOnlyAdmin):
    fields = ('user', 'code', 'count', 'profit', 'total_profit', 'total_charge', 'custom', 'level', 'profit_rate')
    readonly_fields = ('user', 'count', 'profit', 'total_profit', 'total_charge')
    list_display = ('user', 'level', 'profit_rate', 'count', 'profit', 'total_profit', 'total_charge')
    list_filter = ('level', 'custom')
    list_editable = ('level',)
    search_fields = ('user__username',)
    list_per_page = 50


@admin.register(PromotionRecord)
class PromotionRecordAdmin(ReadOnlyAdmin):
    fields = ('ref', 'user', 'profit', 'total_charge', 'active')
    list_display = ('ref', 'user', 'profit', 'total_charge', 'active')
    list_filter = ('active', ('create_time', DateRangeFilter))
    search_fields = ('ref__user__username', 'user__username')
    list_per_page = 50
    actions = [promotion_export_to_excel]


@admin.register(PromotionLevelConfig)
class PromotionLevelConfigAdmin(admin.ModelAdmin):
    fields = ('level', 'profit_rate', 'refer_rate', 'min_amount', 'max_amount')
    list_display = ('level', 'profit_rate', 'refer_rate', 'min_amount', 'max_amount')
    list_per_page = 50

    def save_model(self, request, obj, form, change):
        cache.delete('promocfg')
        if change:
            UserPromotion.objects.filter(custom=False, level=obj.level).update(profit_rate=obj.profit_rate)
        obj.save()

    def delete_model(self, request, obj):
        cache.delete('promocfg')
        obj.delete()


@admin.register(PromotionCaseConfig)
class PromotionCaseConfigAdmin(admin.ModelAdmin):
    fields = ('level', "case")