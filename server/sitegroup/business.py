import logging
from django.core.cache import cache
from django.db.models import Q
from django.utils import timezone

from steambase.enums import RespCode
from sitegroup.models import Site, SiteGroup, SiteConfig, SiteTheme
from sitegroup.serializers import SiteSerializer, SiteConfigSerializer

_logger = logging.getLogger(__name__)


def get_site_by_domain(domain):
    """根据域名获取站点信息"""
    try:
        # 缓存键
        cache_key = f'site_info_{domain}'
        cached_data = cache.get(cache_key)
        if cached_data:
            return RespCode.Succeed.value, cached_data
        
        # 查找站点
        site = Site.objects.filter(
            domain=domain, 
            is_active=True
        ).select_related('site_group', 'theme').first()
        
        if not site:
            # 返回默认站点信息
            return get_default_site_info()
        
        # 序列化站点信息
        site_data = SiteSerializer(site).data
        
        # 缓存结果
        cache.set(cache_key, site_data, timeout=3600)  # 缓存1小时
        
        return RespCode.Succeed.value, site_data
        
    except Exception as e:
        _logger.error(f"Error getting site by domain {domain}: {e}")
        return RespCode.ServerError.value, {'message': 'Server error'}


def get_default_site_info():
    """获取默认站点信息"""
    default_data = {
        'domain': 'default',
        'name': 'CSGOSKINS',
        'subtitle': '专业的CSGO皮肤交易平台',
        'seo_title': 'CSGOSKINS - 专业的CSGO皮肤交易平台',
        'seo_keywords': 'CSGO,皮肤,交易,开箱,对战',
        'seo_description': 'CSGOSKINS是专业的CSGO皮肤交易平台，提供安全可靠的开箱和交易服务',
        'features': {
            'articles': True,
            'announcements': True,
            'case_battle': True,
            'case_opening': True,
            'market': True,
            'lottery': True,
            'roll': True,
            'crash': True,
        },
        'theme': {
            'primary_color': '#007bff',
            'secondary_color': '#6c757d',
        },
        'is_active': True,
        'maintenance_mode': False,
    }
    return RespCode.Succeed.value, default_data


def check_feature_enabled(domain, feature_name):
    """检查站点功能是否启用"""
    try:
        cache_key = f'site_feature_{domain}_{feature_name}'
        cached_result = cache.get(cache_key)
        if cached_result is not None:
            return cached_result
        
        site = Site.objects.filter(domain=domain, is_active=True).first()
        if not site:
            # 默认启用所有功能
            result = True
        else:
            feature_map = {
                'articles': site.articles_enabled,
                'announcements': site.announcements_enabled,
                'case_battle': site.case_battle_enabled,
                'case_opening': site.case_opening_enabled,
                'market': site.market_enabled,
                'lottery': site.lottery_enabled,
                'roll': site.roll_enabled,
                'crash': site.crash_enabled,
            }
            result = feature_map.get(feature_name, False)
        
        # 缓存结果
        cache.set(cache_key, result, timeout=1800)  # 缓存30分钟
        return result
        
    except Exception as e:
        _logger.error(f"Error checking feature {feature_name} for domain {domain}: {e}")
        return True  # 出错时默认启用


def get_site_config(domain, config_key, default_value=None):
    """获取站点配置"""
    try:
        cache_key = f'site_config_{domain}_{config_key}'
        cached_value = cache.get(cache_key)
        if cached_value is not None:
            return cached_value
        
        site = Site.objects.filter(domain=domain, is_active=True).first()
        if not site:
            return default_value
        
        config = SiteConfig.objects.filter(
            site=site,
            config_key=config_key,
            is_active=True
        ).first()
        
        if config:
            value = config.get_typed_value()
        else:
            value = default_value
        
        # 缓存结果
        cache.set(cache_key, value, timeout=1800)  # 缓存30分钟
        return value
        
    except Exception as e:
        _logger.error(f"Error getting config {config_key} for domain {domain}: {e}")
        return default_value


def set_site_config(domain, config_key, config_value, config_type='string', description=''):
    """设置站点配置"""
    try:
        site = Site.objects.filter(domain=domain, is_active=True).first()
        if not site:
            return False
        
        config, created = SiteConfig.objects.get_or_create(
            site=site,
            config_key=config_key,
            defaults={
                'config_value': str(config_value),
                'config_type': config_type,
                'description': description,
                'is_active': True
            }
        )
        
        if not created:
            config.config_value = str(config_value)
            config.config_type = config_type
            config.save()
        
        # 清除缓存
        cache_key = f'site_config_{domain}_{config_key}'
        cache.delete(cache_key)
        
        return True
        
    except Exception as e:
        _logger.error(f"Error setting config {config_key} for domain {domain}: {e}")
        return False


def get_agent_sites(agent_user):
    """获取代理商的所有站点"""
    try:
        if not agent_user or not agent_user.is_agent:
            return RespCode.InvalidParams.value, {'message': 'Invalid agent user'}
        
        site_groups = SiteGroup.objects.filter(
            agent=agent_user,
            is_active=True
        ).prefetch_related('sites')
        
        sites_data = []
        for group in site_groups:
            for site in group.sites.filter(is_active=True):
                site_data = SiteSerializer(site).data
                site_data['group_name'] = group.name
                sites_data.append(site_data)
        
        return RespCode.Succeed.value, {'sites': sites_data}
        
    except Exception as e:
        _logger.error(f"Error getting agent sites for user {agent_user.id}: {e}")
        return RespCode.ServerError.value, {'message': 'Server error'}


def create_site_for_agent(agent_user, site_data):
    """为代理商创建新站点"""
    try:
        if not agent_user or not agent_user.is_agent:
            return RespCode.InvalidParams.value, {'message': 'Invalid agent user'}
        
        # 获取或创建站群
        site_group, created = SiteGroup.objects.get_or_create(
            agent=agent_user,
            defaults={'name': f'{agent_user.username} Sites'}
        )
        
        # 检查域名是否已存在
        if Site.objects.filter(domain=site_data['domain']).exists():
            return RespCode.BusinessError.value, {'message': 'Domain already exists'}
        
        # 创建站点
        site = Site.objects.create(
            site_group=site_group,
            domain=site_data['domain'],
            name=site_data['name'],
            subtitle=site_data.get('subtitle', ''),
            seo_title=site_data.get('seo_title', ''),
            seo_keywords=site_data.get('seo_keywords', ''),
            seo_description=site_data.get('seo_description', ''),
        )
        
        # 创建默认主题
        SiteTheme.objects.create(site=site)
        
        # 清除相关缓存
        cache.delete(f'site_info_{site.domain}')
        
        return RespCode.Succeed.value, SiteSerializer(site).data
        
    except Exception as e:
        _logger.error(f"Error creating site for agent {agent_user.id}: {e}")
        return RespCode.ServerError.value, {'message': 'Server error'}


def update_site_features(domain, features_data):
    """更新站点功能开关"""
    try:
        site = Site.objects.filter(domain=domain, is_active=True).first()
        if not site:
            return RespCode.InvalidParams.value, {'message': 'Site not found'}
        
        # 更新功能开关
        for feature, enabled in features_data.items():
            if hasattr(site, f'{feature}_enabled'):
                setattr(site, f'{feature}_enabled', enabled)
        
        site.save()
        
        # 清除相关缓存
        for feature in features_data.keys():
            cache.delete(f'site_feature_{domain}_{feature}')
        cache.delete(f'site_info_{domain}')
        
        return RespCode.Succeed.value, {'message': 'Features updated successfully'}
        
    except Exception as e:
        _logger.error(f"Error updating features for domain {domain}: {e}")
        return RespCode.ServerError.value, {'message': 'Server error'}


def get_site_analytics(domain, start_date=None, end_date=None):
    """获取站点统计数据"""
    try:
        site = Site.objects.filter(domain=domain, is_active=True).first()
        if not site:
            return RespCode.InvalidParams.value, {'message': 'Site not found'}
        
        # 构建查询
        analytics_query = site.analytics.all()
        
        if start_date:
            analytics_query = analytics_query.filter(date__gte=start_date)
        if end_date:
            analytics_query = analytics_query.filter(date__lte=end_date)
        
        analytics_data = analytics_query.order_by('-date')[:30]  # 最近30天
        
        # 计算总计
        total_stats = {
            'total_page_views': sum(a.page_views for a in analytics_data),
            'total_unique_visitors': sum(a.unique_visitors for a in analytics_data),
            'total_registrations': sum(a.new_registrations for a in analytics_data),
            'total_case_openings': sum(a.case_openings for a in analytics_data),
        }
        
        return RespCode.Succeed.value, {
            'daily_stats': [
                {
                    'date': a.date.isoformat(),
                    'page_views': a.page_views,
                    'unique_visitors': a.unique_visitors,
                    'new_registrations': a.new_registrations,
                    'case_openings': a.case_openings,
                } for a in analytics_data
            ],
            'total_stats': total_stats
        }
        
    except Exception as e:
        _logger.error(f"Error getting analytics for domain {domain}: {e}")
        return RespCode.ServerError.value, {'message': 'Server error'}


def clear_site_cache(domain):
    """清除站点相关缓存"""
    try:
        # 清除站点信息缓存
        cache.delete(f'site_info_{domain}')
        
        # 清除功能开关缓存
        features = ['articles', 'announcements', 'case_battle', 'case_opening', 
                   'market', 'lottery', 'roll', 'crash']
        for feature in features:
            cache.delete(f'site_feature_{domain}_{feature}')
        
        # 清除配置缓存（这里只能清除已知的配置键）
        # 实际使用中可能需要维护一个配置键列表
        
        _logger.info(f"Cleared cache for domain: {domain}")
        return True
        
    except Exception as e:
        _logger.error(f"Error clearing cache for domain {domain}: {e}")
        return False
