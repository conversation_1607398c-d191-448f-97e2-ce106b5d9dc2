<template>
  <div class="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-6">
    <div class="max-w-4xl mx-auto">
      <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-white mb-4">✨ 粒子特效动画</h1>
        <p class="text-gray-300">展示各种酷炫的粒子效果和动态动画</p>
      </div>

      <div class="flex justify-center mb-8 space-x-4">
        <button 
          @click="startAllEffects"
          class="px-6 py-3 bg-primary hover:bg-primary-dark text-white rounded-lg transition-all duration-300 hover:scale-105"
        >
          🚀 开始所有特效
        </button>
        <button 
          @click="stopAllEffects"
          class="px-6 py-3 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-all duration-300"
        >
          ⏹️ 停止特效
        </button>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        
        <!-- 浮动粒子 -->
        <div class="animation-card">
          <h3 class="card-title">🌟 浮动粒子</h3>
          <div class="particle-container floating-particles">
            <div 
              class="floating-particle" 
              v-for="i in 15" 
              :key="i"
              :style="{ 
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 3}s`,
                animationDuration: `${2 + Math.random() * 2}s`
              }"
            ></div>
          </div>
        </div>

        <!-- 爆炸粒子 -->
        <div class="animation-card">
          <h3 class="card-title">💥 爆炸粒子</h3>
          <div class="particle-container explosion-particles">
            <div 
              class="explosion-particle" 
              v-for="i in 20" 
              :key="i"
              :style="{ 
                '--angle': `${(i / 20) * 360}deg`,
                '--distance': `${50 + Math.random() * 50}px`,
                animationDelay: `${Math.random() * 0.5}s`
              }"
            ></div>
          </div>
        </div>

        <!-- 螺旋粒子 -->
        <div class="animation-card">
          <h3 class="card-title">🌀 螺旋粒子</h3>
          <div class="particle-container spiral-particles">
            <div 
              class="spiral-particle" 
              v-for="i in 30" 
              :key="i"
              :style="{ 
                animationDelay: `${i * 0.1}s`,
                '--particle-index': i
              }"
            ></div>
          </div>
        </div>

        <!-- 矩阵粒子 -->
        <div class="animation-card">
          <h3 class="card-title">🔢 矩阵粒子</h3>
          <div class="particle-container matrix-particles">
            <div 
              class="matrix-particle" 
              v-for="i in 50" 
              :key="i"
              :style="{ 
                left: `${(i % 10) * 10}%`,
                top: `${Math.floor(i / 10) * 10}%`,
                animationDelay: `${Math.random() * 2}s`
              }"
            ></div>
          </div>
        </div>

        <!-- 波浪粒子 -->
        <div class="animation-card">
          <h3 class="card-title">🌊 波浪粒子</h3>
          <div class="particle-container wave-particles">
            <div 
              class="wave-particle" 
              v-for="i in 25" 
              :key="i"
              :style="{ 
                left: `${(i / 25) * 100}%`,
                animationDelay: `${i * 0.1}s`
              }"
            ></div>
          </div>
        </div>

        <!-- 脉冲粒子 -->
        <div class="animation-card">
          <h3 class="card-title">💓 脉冲粒子</h3>
          <div class="particle-container pulse-particles">
            <div 
              class="pulse-particle" 
              v-for="i in 12" 
              :key="i"
              :style="{ 
                animationDelay: `${i * 0.2}s`
              }"
            ></div>
          </div>
        </div>

      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

const isActive = ref(false)

const startAllEffects = () => {
  isActive.value = true
}

const stopAllEffects = () => {
  isActive.value = false
}

onMounted(() => {
  startAllEffects()
})
</script>

<style lang="scss" scoped>
.animation-card {
  @apply bg-gray-800/40 backdrop-blur-lg rounded-lg p-6 border border-gray-700/50 transition-all duration-300;
  
  &:hover {
    @apply border-primary/50 bg-gray-800/60 transform -translate-y-1;
    box-shadow: 0 8px 32px rgba(0, 168, 255, 0.15);
  }
}

.card-title {
  @apply text-lg font-semibold text-white mb-4 pb-2 border-b border-gray-600/50;
}

.particle-container {
  @apply relative h-48 bg-gray-900/50 rounded-lg overflow-hidden;
}

// 浮动粒子
.floating-particles {
  .floating-particle {
    @apply absolute w-2 h-2 bg-primary rounded-full;
    animation: float-around 4s ease-in-out infinite;
    box-shadow: 0 0 10px rgba(0, 168, 255, 0.6);
  }
}

// 爆炸粒子
.explosion-particles {
  .explosion-particle {
    @apply absolute w-1 h-1 bg-secondary rounded-full;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    animation: explode-out 1.5s ease-out infinite;
    box-shadow: 0 0 8px rgba(255, 77, 0, 0.6);
  }
}

// 螺旋粒子
.spiral-particles {
  .spiral-particle {
    @apply absolute w-1 h-1 bg-primary rounded-full;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    animation: spiral-out 3s linear infinite;
    box-shadow: 0 0 6px rgba(0, 168, 255, 0.5);
  }
}

// 矩阵粒子
.matrix-particles {
  .matrix-particle {
    @apply absolute w-1 h-1 bg-green-400 rounded-full;
    animation: matrix-fall 2s linear infinite;
    box-shadow: 0 0 5px rgba(34, 197, 94, 0.6);
  }
}

// 波浪粒子
.wave-particles {
  .wave-particle {
    @apply absolute w-2 h-2 bg-primary rounded-full;
    bottom: 0;
    animation: wave-bounce 2s ease-in-out infinite;
    box-shadow: 0 0 8px rgba(0, 168, 255, 0.5);
  }
}

// 脉冲粒子
.pulse-particles {
  .pulse-particle {
    @apply absolute w-3 h-3 bg-secondary rounded-full;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    animation: pulse-expand 2s ease-in-out infinite;
    box-shadow: 0 0 15px rgba(255, 77, 0, 0.6);
  }
}

@keyframes float-around {
  0%, 100% {
    transform: translateY(0) translateX(0);
    opacity: 0.7;
  }
  25% {
    transform: translateY(-20px) translateX(10px);
    opacity: 1;
  }
  50% {
    transform: translateY(-10px) translateX(-15px);
    opacity: 0.8;
  }
  75% {
    transform: translateY(-30px) translateX(5px);
    opacity: 1;
  }
}

@keyframes explode-out {
  0% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) translateX(calc(cos(var(--angle)) * var(--distance))) translateY(calc(sin(var(--angle)) * var(--distance))) scale(0);
    opacity: 0;
  }
}

@keyframes spiral-out {
  0% {
    transform: translate(-50%, -50%) rotate(0deg) translateX(0);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg) translateX(100px);
    opacity: 0;
  }
}

@keyframes matrix-fall {
  0% {
    transform: translateY(-100%);
    opacity: 1;
  }
  100% {
    transform: translateY(100vh);
    opacity: 0;
  }
}

@keyframes wave-bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-60px);
  }
}

@keyframes pulse-expand {
  0%, 100% {
    transform: translate(-50%, -50%) scale(0.5);
    opacity: 0.7;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 1;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .particle-container {
    @apply h-32;
  }
  
  .floating-particle,
  .wave-particle {
    @apply w-1 h-1;
  }
  
  .pulse-particle {
    @apply w-2 h-2;
  }
}
</style> 