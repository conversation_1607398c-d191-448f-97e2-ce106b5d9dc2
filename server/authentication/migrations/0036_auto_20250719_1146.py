# Generated by Django 3.2.25 on 2025-07-19 03:46

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('authentication', '0035_auto_20250718_1647'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='authuser',
            options={'verbose_name': '认证用户', 'verbose_name_plural': 'Auth User'},
        ),
        migrations.AlterModelOptions(
            name='emailsuffix',
            options={'verbose_name': '邮箱后缀', 'verbose_name_plural': 'Email Suffix'},
        ),
        migrations.AlterModelOptions(
            name='phonecoderecord',
            options={'verbose_name': '手机验证码记录', 'verbose_name_plural': 'Phone Code Record'},
        ),
        migrations.AlterModelOptions(
            name='steamblacklist',
            options={'verbose_name': 'Steam黑名单', 'verbose_name_plural': 'Steam Black List'},
        ),
        migrations.AlterModelOptions(
            name='useractivepointrecord',
            options={'verbose_name': '用户活跃积分记录', 'verbose_name_plural': 'User Active Point Record'},
        ),
        migrations.AlterModelOptions(
            name='userasset',
            options={'verbose_name': '资产', 'verbose_name_plural': 'Asset'},
        ),
        migrations.AlterModelOptions(
            name='userbalancerecord',
            options={'verbose_name': '用户余额记录', 'verbose_name_plural': 'User Balance Record'},
        ),
        migrations.AlterModelOptions(
            name='userdiamondrecord',
            options={'verbose_name': '用户钻石记录', 'verbose_name_plural': 'User Diamond Record'},
        ),
        migrations.AlterModelOptions(
            name='userextra',
            options={'verbose_name': '扩展信息', 'verbose_name_plural': 'Extra'},
        ),
        migrations.AlterModelOptions(
            name='userpointsrecord',
            options={'verbose_name': '用户积分记录', 'verbose_name_plural': 'User Points Record'},
        ),
        migrations.AlterModelOptions(
            name='userprofile',
            options={'verbose_name': '用户资料', 'verbose_name_plural': 'Profile'},
        ),
        migrations.AlterModelOptions(
            name='userstatisticsday',
            options={'ordering': ('-create_time',), 'verbose_name': '用户日统计', 'verbose_name_plural': 'User Statistics Day'},
        ),
        migrations.AlterModelOptions(
            name='usersteam',
            options={'verbose_name': 'Steam账户', 'verbose_name_plural': 'Steam'},
        ),
        migrations.AlterField(
            model_name='useractivepointrecord',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='active_point_records', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
        migrations.AlterField(
            model_name='userbalancerecord',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='balance_records', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
        migrations.AlterField(
            model_name='userdiamondrecord',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='diamond_records', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
        migrations.AlterField(
            model_name='userpointsrecord',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='points_records', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
    ]
