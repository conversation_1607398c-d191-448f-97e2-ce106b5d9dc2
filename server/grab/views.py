import logging

from django.utils.translation import gettext_lazy as _

from rest_framework.permissions import AllowAny
from rest_framework.views import APIView

from steambase.enums import RespCode
from steambase.utils import reformat_resp, current_user
from grab.business import (
    get_grab_room_list, get_grab_user, get_grab_room_detail, get_grab_room_position,
    get_grab_dynamic, get_grab_user_history, join_grab_room, pick_grab_room_card
)

_logger = logging.getLogger(__name__)


class GetGrabRoomListView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            user = current_user(request)
            state = request.query_params.get('state', None)
            coins = request.query_params.get('coins', None)
            sort = request.query_params.get('sort', None)
            symbol = request.query_params.get('s', None)
            join = request.query_params.get('join', None)
            code, resp = get_grab_room_list(user, state, coins, sort, symbol, join)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetGrabUserView(APIView):

    def get(self, request):
        try:
            user = current_user(request)
            code, resp = get_grab_user(user)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetGrabRoomDetailView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            user = current_user(request)
            rid = request.query_params.get("rid", None)
            code, resp = get_grab_room_detail(user, rid)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetRoomPositionListView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            user = current_user(request)
            rid = request.query_params.get('rid', None)
            code, resp = get_grab_room_position(user, rid)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetGrabDynamicView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            user = current_user(request)
            code, resp = get_grab_dynamic()
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetGrabUserHistoryView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            user = current_user(request)
            code, resp = get_grab_user_history(user)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class JoinGrabRoomView(APIView):

    def post(self, request):
        try:
            user = current_user(request)
            rid = request.data.get('rid', None)
            nums = request.data.get('nums', [])
            code, resp = join_grab_room(user, nums, rid)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class PickGrabCardView(APIView):

    def get(self, request):
        try:
            user = current_user(request)
            count = request.query_params.get("count", 1)
            rid = request.query_params.get("rid", None)
            code, resp = pick_grab_room_card(user, rid, int(count))
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')