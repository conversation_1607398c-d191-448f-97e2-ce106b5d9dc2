<template>
  <div class="csgo-loader">
    <div class="csgo-loader-inner"></div>
    <div class="csgo-loader-text">{{ text || $t('common.loading') }}</div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  text?: string
}

const props = withDefaults(defineProps<Props>(), {
  text: ''
})
</script>

<style lang="scss" scoped>
/* CSGO风格加载器 */
.csgo-loader {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.csgo-loader-inner {
  width: 3rem;
  height: 3rem;
  border: 2px solid rgba(var(--color-primary-rgb), 0.2);
  border-top: 2px solid var(--color-primary);
  border-radius: 50%;
  animation: csgo-spin 1s linear infinite;
  position: relative;
}

.csgo-loader-inner::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0.5rem;
  height: 0.5rem;
  background: var(--color-primary);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 0 10px var(--color-primary);
  animation: csgo-pulse 0.8s ease-in-out infinite alternate;
}

.csgo-loader-text {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 500;
  letter-spacing: 0.05em;
  animation: text-fade 1.5s ease-in-out infinite;
}

@keyframes csgo-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes csgo-pulse {
  0% {
    opacity: 0.5;
    transform: translate(-50%, -50%) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.2);
  }
}

@keyframes text-fade {
  0%, 100% { opacity: 0.4; }
  50% { opacity: 0.8; }
}

/* 移动端适配 */
@media (max-width: 640px) {
  .csgo-loader-inner {
    width: 2.5rem;
    height: 2.5rem;
  }

  .csgo-loader-text {
    font-size: 0.75rem;
  }
}
</style>
