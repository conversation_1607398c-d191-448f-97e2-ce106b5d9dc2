import demoTranslations from '~/locales/demo-case-opening.json'

/**
 * Composable for demo case opening translations
 * Provides easy access to demo-specific translations
 */
export const useDemoCaseOpeningTranslations = () => {
  const { locale } = useI18n()
  
  const getTranslation = (key: string, params?: Record<string, any>) => {
    const currentLocale = locale.value.includes('zh') ? 'zh-hans' : 'en'
    const translation = demoTranslations[currentLocale as keyof typeof demoTranslations]
    
    if (!translation) {
      console.warn(`Translation not found for locale: ${currentLocale}`)
      return key
    }
    
    // Navigate through nested keys
    const keys = key.split('.')
    let value: any = translation
    
    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k]
      } else {
        console.warn(`Translation key not found: ${key} for locale: ${currentLocale}`)
        return key
      }
    }
    
    // Handle parameter substitution
    if (typeof value === 'string' && params) {
      return value.replace(/\{(\w+)\}/g, (match, paramKey) => {
        return params[paramKey] || match
      })
    }
    
    return value
  }
  
  const t = (key: string, params?: Record<string, any>) => {
    return getTranslation(key, params)
  }
  
  return {
    t,
    getTranslation
  }
}
