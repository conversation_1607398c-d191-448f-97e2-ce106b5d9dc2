# Generated by Django 3.2.25 on 2025-07-18 08:28

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('package', '0022_auto_20250718_1459'),
    ]

    operations = [
        migrations.AddField(
            model_name='iteminfo',
            name='item_category',
            field=models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='item_info', to='package.itemcategory', verbose_name='Category'),
        ),
        migrations.AddField(
            model_name='iteminfo',
            name='item_quality',
            field=models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='item_info', to='package.itemquality', verbose_name='Quality'),
        ),
        migrations.AddField(
            model_name='iteminfo',
            name='item_rarity',
            field=models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='item_info', to='package.itemrarity', verbose_name='Rarity'),
        ),
        migrations.AddField(
            model_name='iteminfo',
            name='item_exterior',
            field=models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='item_info', to='package.itemexterior', verbose_name='Exterior'),
        ),
    ]
