# -*- coding: utf-8 -*-
# Generated by Django 1.11.13 on 2021-04-14 03:12
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import steambase.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODELL),
    ]

    operations = [
        migrations.CreateModel(
            name='EnvelopeRecord',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('password', models.Char<PERSON>ield(blank=True, default=None, max_length=64, null=True, verbose_name='envelop password')),
                ('handsel', models.FloatField(default=0, verbose_name='handsel')),
            ],
            options={
                'verbose_name': 'EnvelopeRecord',
                'verbose_name_plural': 'EnvelopeRecord',
            },
        ),
        migrations.CreateModel(
            name='EnvelopeRule',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('title', models.CharField(max_length=128, verbose_name='title')),
                ('rule_start_time', models.DateTimeField(default=None, verbose_name='rule start time')),
                ('rule_end_time', models.DateTimeField(default=None, verbose_name='rule_end_time')),
                ('rule_coins', models.FloatField(default=0, verbose_name='rule coins')),
                ('stock', models.SmallIntegerField(default=0, verbose_name='stock')),
                ('limited', models.BooleanField(default=True, verbose_name='limited')),
                ('start_time', models.DateTimeField(default=None, verbose_name='start time')),
                ('end_time', models.DateTimeField(default=None, verbose_name='end time')),
                ('handsel_min', models.FloatField(default=0, verbose_name='handsel min')),
                ('handsel_max', models.FloatField(default=0, verbose_name='handsel max')),
                ('password', models.CharField(blank=True, default=None, max_length=64, null=True, verbose_name='envelop password')),
                ('state', models.SmallIntegerField(choices=[(0, '准备中'), (1, '未开始'), (2, '已开始')], default=0, verbose_name='state')),
            ],
            options={
                'verbose_name': 'EnvelopeRule',
                'verbose_name_plural': 'EnvelopeRule',
            },
        ),
        migrations.AddField(
            model_name='enveloperecord',
            name='rule',
            field=models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='envelop_rule', to='envelope.EnvelopeRule', verbose_name='rule'),
        ),
        migrations.AddField(
            model_name='enveloperecord',
            name='user',
            field=models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='envelop_record', to=settings.AUTH_USER_MODELL, verbose_name='user'),
        ),
    ]
