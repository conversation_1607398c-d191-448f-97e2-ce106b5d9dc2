# 🤝 贡献指南

感谢你对CSGO皮肤交易平台项目的关注！我们欢迎所有形式的贡献，包括但不限于代码、文档、问题反馈、功能建议等。

## 📋 目录

- [贡献方式](#贡献方式)
- [开发环境搭建](#开发环境搭建)
- [代码规范](#代码规范)
- [提交流程](#提交流程)
- [问题反馈](#问题反馈)
- [功能建议](#功能建议)

## 🎯 贡献方式

### 代码贡献
- 🐛 **Bug修复** - 修复已知问题
- ✨ **新功能** - 添加新的功能特性
- 🎨 **UI改进** - 改善用户界面和体验
- ⚡ **性能优化** - 提升系统性能
- 🔧 **重构** - 代码结构优化

### 文档贡献
- 📝 **文档完善** - 补充或改进现有文档
- 🌍 **翻译** - 多语言文档翻译
- 📖 **教程** - 编写使用教程和示例
- ❓ **FAQ** - 补充常见问题解答

### 其他贡献
- 🧪 **测试** - 编写和改进测试用例
- 🔍 **代码审查** - 参与代码审查
- 💬 **社区支持** - 帮助其他用户解决问题
- 📢 **推广** - 分享和推广项目

## 🛠️ 开发环境搭建

### 前置要求
- Node.js 16+
- Python 3.8+
- Docker 20.10+
- Git

### 克隆项目
```bash
git clone https://github.com/your-repo/csgoskins.com.cn.git
cd csgoskins.com.cn
```

### 后端环境
```bash
cd server
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
cp .env.example .env
# 编辑 .env 文件配置数据库等
python manage.py migrate
python manage.py runserver
```

### 前端环境
```bash
cd ui
npm install
cp .env.example .env
# 编辑 .env 文件配置API地址等
npm run dev
```

### Docker环境
```bash
docker-compose up -d
```

## 📝 代码规范

### 前端规范 (Vue3/TypeScript)

#### 命名规范
```typescript
// 组件命名 - PascalCase
export default defineComponent({
  name: 'CaseOpeningCard'
})

// 文件命名 - kebab-case
case-opening-card.vue
use-battle-state.ts

// 变量命名 - camelCase
const userBalance = ref(0)
const isLoading = ref(false)

// 常量命名 - UPPER_SNAKE_CASE
const API_ENDPOINTS = {
  CASES: '/api/cases/',
  BATTLE: '/api/battle/'
}
```

#### 组件结构
```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup lang="ts">
// 1. 导入
import { ref, computed, onMounted } from 'vue'

// 2. 类型定义
interface Props {
  id: number
  name: string
}

// 3. Props和Emits
const props = defineProps<Props>()
const emit = defineEmits<{
  change: [value: string]
}>()

// 4. 响应式数据
const data = ref('')

// 5. 计算属性
const computed = computed(() => {})

// 6. 方法
const method = () => {}

// 7. 生命周期
onMounted(() => {})
</script>

<style scoped>
/* 样式 */
</style>
```

### 后端规范 (Django/Python)

#### 命名规范
```python
# 类命名 - PascalCase
class CaseOpeningView(APIView):
    pass

# 函数命名 - snake_case
def open_case(case_id: int) -> dict:
    pass

# 变量命名 - snake_case
user_balance = 0
is_authenticated = False

# 常量命名 - UPPER_SNAKE_CASE
API_VERSION = 'v1'
DEFAULT_PAGE_SIZE = 20
```

#### 代码结构
```python
# models.py
class Case(models.Model):
    """箱子模型"""
    name = models.CharField("名称", max_length=100)
    price = models.DecimalField("价格", max_digits=10, decimal_places=2)
    
    class Meta:
        verbose_name = "箱子"
        verbose_name_plural = "箱子"

# views.py
class CaseListView(ListAPIView):
    """箱子列表视图"""
    queryset = Case.objects.all()
    serializer_class = CaseSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """自定义查询集"""
        return super().get_queryset().filter(is_active=True)

# serializers.py
class CaseSerializer(serializers.ModelSerializer):
    """箱子序列化器"""
    
    class Meta:
        model = Case
        fields = ['id', 'name', 'price', 'image']
```

### Git提交规范

#### 提交信息格式
```
<type>(<scope>): <subject>

<body>

<footer>
```

#### 类型说明
- `feat`: 新功能
- `fix`: Bug修复
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

#### 示例
```bash
feat(battle): add real-time battle animation

- Implement WebSocket connection for battle rooms
- Add GSAP animations for case opening
- Update battle state management

Closes #123
```

## 🔄 提交流程

### 1. Fork项目
在GitHub上Fork项目到你的账户

### 2. 创建分支
```bash
git checkout -b feature/amazing-feature
# 或
git checkout -b fix/bug-description
```

### 3. 开发和测试
```bash
# 前端测试
cd ui
npm run test
npm run lint

# 后端测试
cd server
python manage.py test
flake8 .
```

### 4. 提交代码
```bash
git add .
git commit -m "feat: add amazing feature"
git push origin feature/amazing-feature
```

### 5. 创建Pull Request
在GitHub上创建Pull Request，描述你的更改

### 6. 代码审查
等待维护者审查你的代码，根据反馈进行修改

### 7. 合并
审查通过后，代码将被合并到主分支

## 🐛 问题反馈

### 报告Bug
在报告Bug时，请提供以下信息：

1. **环境信息**
   - 操作系统
   - 浏览器版本
   - Node.js/Python版本

2. **重现步骤**
   - 详细的操作步骤
   - 预期结果
   - 实际结果

3. **错误信息**
   - 错误截图
   - 控制台日志
   - 服务器日志

### Bug报告模板
```markdown
## Bug描述
简要描述遇到的问题

## 重现步骤
1. 打开页面
2. 点击按钮
3. 查看结果

## 预期行为
描述你期望发生的情况

## 实际行为
描述实际发生的情况

## 环境信息
- OS: [e.g. Windows 10]
- Browser: [e.g. Chrome 91]
- Version: [e.g. 1.0.0]

## 附加信息
其他相关信息或截图
```

## 💡 功能建议

### 提出建议
我们欢迎任何功能建议！请在GitHub Issues中提出，并使用以下模板：

```markdown
## 功能描述
简要描述建议的功能

## 使用场景
描述什么情况下会用到这个功能

## 解决方案
描述你认为的实现方案

## 替代方案
描述其他可能的实现方案

## 附加信息
其他相关信息
```

## 📚 开发资源

### 文档链接
- [项目文档](docs/README.md)
- [API文档](docs/api/README.md)
- [前端开发指南](docs/frontend/development-guide.md)
- [后端开发指南](docs/backend/README.md)

### 学习资源
- [Vue 3 官方文档](https://vuejs.org/)
- [Nuxt 3 官方文档](https://nuxt.com/)
- [Django 官方文档](https://docs.djangoproject.com/)
- [Django REST Framework](https://www.django-rest-framework.org/)

### 工具推荐
- **IDE**: VS Code, PyCharm
- **Git客户端**: SourceTree, GitKraken
- **API测试**: Postman, Insomnia
- **数据库工具**: MySQL Workbench, Redis Desktop Manager

## 🏆 贡献者

感谢所有为项目做出贡献的开发者！

<!-- 这里可以添加贡献者列表 -->

## 📞 联系我们

如果你有任何问题或建议，可以通过以下方式联系我们：

- **GitHub Issues**: [提交问题](https://github.com/your-repo/issues)
- **邮箱**: <EMAIL>
- **Discord**: [加入社区](https://discord.gg/your-invite)
- **QQ群**: 123456789

## 📄 许可证

本项目采用 MIT 许可证。详情请查看 [LICENSE](LICENSE) 文件。

---

🙏 **感谢你对项目的贡献！每一个贡献都让项目变得更好。**

⭐ **如果你觉得项目有用，请给我们一个星标！**
