import os,sys
import django
sys.path.append('./')
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "steambase.settings")
django.setup()

import requests
from package.models import ItemInfo


url = 'http://*************/get_steam_items/?skip={}&limit={}'

def get_data(page, page_size):
    data = None
    try:
        _url = url.format(page, page_size)
        resp = requests.get(_url, timeout=60)
        data = resp.json()
        if not data:
            return None
        else:
            data = data.get('items', [])
            return data
    except:
        return None

def sync_iteminfo(items):
    for item in items:
        hash_name = item.get('market_hash_name', '')
        market_name_cn = item.get('market_name', '')
        name_cn = item.get('name', '')
        icon_url = item.get('icon_url', '')
        appid = item.get('appid', '')
        _item = ItemInfo.objects.filter(market_hash_name=hash_name).first()
        if _item:
            # continue
            ItemInfo.objects.filter(market_hash_name=hash_name).update(market_name_cn=market_name_cn, name_cn=name_cn)
        else:
            ItemInfo.objects.create(appid=appid, market_hash_name=hash_name,
                                   name=hash_name, market_name=hash_name, icon_url=icon_url, icon_url_large=icon_url,
                                   market_name_cn=name_cn, name_cn=name_cn
                                   )
            #print('create hash_name: ', hash_name)


def main():
    limit = 100
    skip = 0
    for i in range(0, 161):
        items = get_data(skip+1, limit)
        skip = skip+limit
        if items:
            sync_iteminfo(items)


if __name__ == "__main__":
    # data = get_data(1, 100)
    # print(data)
    main()