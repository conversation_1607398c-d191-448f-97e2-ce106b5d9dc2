<!-- pages/demo/vertical-battle.vue -->
<template>
  <div class="vertical-battle-demo">
    <!-- 页面标题 -->
    <div class="demo-header">
      <h1>竖向老虎机对战测试</h1>
      <div class="demo-controls">
        <button @click="startBattle" :disabled="isRunning" class="start-btn">
          {{ isRunning ? '对战进行中...' : '开始对战' }}
        </button>
        <button @click="resetBattle" class="reset-btn">重置</button>
      </div>
    </div>

    <!-- 对战区域 -->
    <div class="battle-arena">
      <!-- 玩家槽位 -->
      <div class="players-container">
        <div 
          v-for="(player, index) in players" 
          :key="index"
          class="player-slot"
          :class="{ 'winner': player.isWinner, 'spinning': player.isSpinning }"
        >
          <!-- 玩家信息 -->
          <div class="player-info">
            <div class="player-avatar">
              <img :src="`/demo/avatar${index + 1}.png`" :alt="player.name">
              <div class="player-status" :class="{ 'active': player.isSpinning }"></div>
            </div>
            <div class="player-details">
              <div class="player-name">{{ player.name }}</div>
              <div class="player-score">${{ player.score.toFixed(2) }}</div>
            </div>
          </div>

          <!-- 竖向老虎机 -->
          <div class="vertical-slot-machine">
            <!-- 中奖指示线 -->
            <div class="winner-line">
              <div class="line-left"></div>
              <div class="line-arrow">→</div>
              <div class="line-right"></div>
            </div>

            <!-- 滚轮容器 -->
            <div class="reel-container">
              <div 
                class="reel-strip" 
                :style="{ transform: `translateY(${player.reelPosition}px)` }"
              >
                <div 
                  v-for="(item, itemIndex) in player.items" 
                  :key="itemIndex"
                  class="reel-item"
                  :class="{ 'winner-item': itemIndex === player.winnerIndex && !player.isSpinning }"
                >
                  <div class="item-image-container">
                    <img :src="item.image" :alt="item.name" class="item-image">
                    <div class="item-rarity-glow" :style="{ backgroundColor: item.rarity + '30' }"></div>
                  </div>
                  <div class="item-info">
                    <div class="item-name">{{ item.name }}</div>
                    <div class="item-price">${{ item.price.toFixed(2) }}</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 获胜物品展示 -->
            <div v-if="player.winnerItem && !player.isSpinning" class="winner-showcase">
              <div class="winner-glow"></div>
              <div class="winner-particles"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 对战状态 -->
      <div class="battle-status">
        <div class="status-text">{{ battleStatus }}</div>
        <div v-if="winner" class="winner-announcement">
          🏆 {{ winner.name }} 获胜！
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { gsap } from 'gsap'

// 响应式数据
const isRunning = ref(false)
const battleStatus = ref('准备开始')

// 玩家数据
const players = ref([
  {
    name: '玩家1',
    score: 0,
    isSpinning: false,
    isWinner: false,
    reelPosition: 0,
    winnerIndex: -1,
    winnerItem: null,
    items: []
  },
  {
    name: '玩家2', 
    score: 0,
    isSpinning: false,
    isWinner: false,
    reelPosition: 0,
    winnerIndex: -1,
    winnerItem: null,
    items: []
  },
  {
    name: '玩家3',
    score: 0,
    isSpinning: false,
    isWinner: false,
    reelPosition: 0,
    winnerIndex: -1,
    winnerItem: null,
    items: []
  },
  {
    name: '玩家4',
    score: 0,
    isSpinning: false,
    isWinner: false,
    reelPosition: 0,
    winnerIndex: -1,
    winnerItem: null,
    items: []
  }
])

// 计算获胜者
const winner = computed(() => {
  return players.value.find(p => p.isWinner)
})

// 生成模拟物品数据
const generateItems = () => {
  const itemNames = [
    'AK-47 红线', 'AWP 龙狙', 'M4A4 咆哮', 'Glock 水元素',
    'USP-S 杀戮确认', 'Desert Eagle 印花', 'AK-47 火蛇',
    'M4A1-S 金属丝', 'AWP 二西莫夫', 'Karambit 虎牙',
    'Butterfly 刀 渐变', 'Bayonet 大马士革', 'Flip 刀 多普勒',
    'Huntsman 刀 深红之网', 'Shadow 刀 虎牙', 'Talon 刀 渐变',
    'Ursus 刀 伽马多普勒', 'Stiletto 刀 大马士革', 'Navaja 刀 虎牙',
    'Nomad 刀 深红之网'
  ]
  
  const rarities = [
    '#B0C3D9', // 消费级 - 灰色
    '#5E98D9', // 工业级 - 浅蓝
    '#4B69FF', // 军规级 - 蓝色
    '#8847FF', // 受限 - 紫色
    '#D32CE6', // 保密 - 粉色
    '#EB4B4B', // 隐秘 - 红色
    '#E4AE33'  // 极其罕见 - 金色
  ]

  const items = []
  for (let i = 0; i < 20; i++) {
    const rarity = rarities[Math.floor(Math.random() * rarities.length)]
    const price = rarity === '#E4AE33' ? Math.random() * 1000 + 500 : // 金色
                  rarity === '#EB4B4B' ? Math.random() * 500 + 100 : // 红色
                  rarity === '#D32CE6' ? Math.random() * 200 + 50 : // 粉色
                  rarity === '#8847FF' ? Math.random() * 100 + 20 : // 紫色
                  rarity === '#4B69FF' ? Math.random() * 50 + 10 : // 蓝色
                  rarity === '#5E98D9' ? Math.random() * 20 + 5 : // 浅蓝
                  Math.random() * 10 + 1 // 灰色

    items.push({
      id: i,
      name: itemNames[Math.floor(Math.random() * itemNames.length)],
      image: `/demo/item${(i % 12) + 1}.png`,
      price: price,
      rarity: rarity
    })
  }
  return items
}

// 开始对战
const startBattle = async () => {
  if (isRunning.value) return
  
  isRunning.value = true
  battleStatus.value = '对战开始！'
  
  // 重置所有玩家状态
  players.value.forEach(player => {
    player.isSpinning = true
    player.isWinner = false
    player.reelPosition = 0
    player.winnerIndex = -1
    player.winnerItem = null
    player.items = generateItems()
  })

  // 开始滚动动画
  await startSpinAnimation()
  
  // 确定获胜者
  determineWinner()
  
  isRunning.value = false
  battleStatus.value = '对战结束'
}

// 滚动动画
const startSpinAnimation = () => {
  return new Promise((resolve) => {
    const timeline = gsap.timeline()
    
    players.value.forEach((player, playerIndex) => {
      const itemHeight = 80 // 每个物品的高度
      const totalItems = player.items.length
      
      // 随机选择获胜物品
      const winnerIndex = Math.floor(Math.random() * totalItems)
      player.winnerIndex = winnerIndex
      player.winnerItem = player.items[winnerIndex]
      
      // 计算最终位置（让获胜物品停在中奖线上）
      const finalPosition = -(winnerIndex * itemHeight) + (itemHeight * 2)
      
      // 第一阶段：快速滚动
      timeline.to(player, {
        reelPosition: -(totalItems * itemHeight * 2), // 滚动两圈
        duration: 2 + playerIndex * 0.2, // 错开时间
        ease: "power2.in"
      }, playerIndex * 0.1)
      
      // 第二阶段：减速停止
      timeline.to(player, {
        reelPosition: finalPosition,
        duration: 1.5 + playerIndex * 0.2,
        ease: "power4.out",
        onComplete: () => {
          player.isSpinning = false
          player.score += player.winnerItem.price
          
          // 检查是否所有玩家都完成
          if (players.value.every(p => !p.isSpinning)) {
            resolve()
          }
        }
      })
    })
  })
}

// 确定获胜者
const determineWinner = () => {
  let maxScore = 0
  let winnerPlayer = null
  
  players.value.forEach(player => {
    if (player.score > maxScore) {
      maxScore = player.score
      winnerPlayer = player
    }
  })
  
  if (winnerPlayer) {
    winnerPlayer.isWinner = true
  }
}

// 重置对战
const resetBattle = () => {
  if (isRunning.value) return
  
  players.value.forEach(player => {
    player.score = 0
    player.isSpinning = false
    player.isWinner = false
    player.reelPosition = 0
    player.winnerIndex = -1
    player.winnerItem = null
    player.items = generateItems()
  })
  
  battleStatus.value = '准备开始'
}

// 初始化
onMounted(() => {
  resetBattle()
})
</script>

<style lang="scss" scoped>
.vertical-battle-demo {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f1419 100%);
  padding: 20px;
  color: white;
}

.demo-header {
  text-align: center;
  margin-bottom: 30px;
  
  h1 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: #fff;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
  }
  
  .demo-controls {
    display: flex;
    gap: 15px;
    justify-content: center;
    
    button {
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &.start-btn {
        background: linear-gradient(45deg, #4CAF50, #45a049);
        color: white;
        
        &:hover:not(:disabled) {
          transform: translateY(-2px);
          box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        }
        
        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      }
      
      &.reset-btn {
        background: linear-gradient(45deg, #f44336, #da190b);
        color: white;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 5px 15px rgba(244, 67, 54, 0.4);
        }
      }
    }
  }
}

.battle-arena {
  max-width: 1400px;
  margin: 0 auto;
}

.players-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.player-slot {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  padding: 20px;
  transition: all 0.3s ease;
  
  &.spinning {
    border-color: #4CAF50;
    box-shadow: 0 0 30px rgba(76, 175, 80, 0.5);
  }
  
  &.winner {
    border-color: #FFD700;
    box-shadow: 0 0 40px rgba(255, 215, 0, 0.6);
    animation: winner-glow 2s ease-in-out infinite;
  }
}

@keyframes winner-glow {
  0%, 100% { box-shadow: 0 0 40px rgba(255, 215, 0, 0.6); }
  50% { box-shadow: 0 0 60px rgba(255, 215, 0, 0.8); }
}

.player-info {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
  
  .player-avatar {
    position: relative;
    
    img {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      border: 3px solid #4CAF50;
    }
    
    .player-status {
      position: absolute;
      top: -2px;
      right: -2px;
      width: 16px;
      height: 16px;
      border-radius: 50%;
      background: #666;
      border: 2px solid #000;
      
      &.active {
        background: #4CAF50;
        animation: pulse 1s ease-in-out infinite;
      }
    }
  }
  
  .player-details {
    .player-name {
      font-size: 1.2rem;
      font-weight: bold;
      margin-bottom: 5px;
    }
    
    .player-score {
      font-size: 1.1rem;
      color: #4CAF50;
      font-weight: bold;
    }
  }
}

.vertical-slot-machine {
  position: relative;
  height: 400px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  overflow: hidden;
  border: 2px solid rgba(255, 255, 255, 0.1);
  
  .winner-line {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    z-index: 10;
    display: flex;
    align-items: center;
    height: 3px;
    
    .line-left, .line-right {
      flex: 1;
      height: 100%;
      background: linear-gradient(90deg, transparent, #FFD700);
    }
    
    .line-right {
      background: linear-gradient(90deg, #FFD700, transparent);
    }
    
    .line-arrow {
      padding: 0 10px;
      color: #FFD700;
      font-weight: bold;
      font-size: 1.2rem;
      text-shadow: 0 0 10px rgba(255, 215, 0, 0.8);
      animation: arrow-pulse 1.5s ease-in-out infinite;
    }
  }
  
  .reel-container {
    height: 100%;
    overflow: hidden;
    
    .reel-strip {
      transition: transform 0.1s ease-out;
      
      .reel-item {
        display: flex;
        align-items: center;
        gap: 12px;
        height: 80px;
        padding: 10px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;
        
        &.winner-item {
          background: rgba(255, 215, 0, 0.2);
          border-color: #FFD700;
          animation: winner-item-glow 2s ease-in-out infinite;
        }
        
        .item-image-container {
          position: relative;
          width: 60px;
          height: 60px;
          
          .item-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 8px;
            border: 2px solid rgba(255, 255, 255, 0.3);
          }
          
          .item-rarity-glow {
            position: absolute;
            inset: -2px;
            border-radius: 10px;
            opacity: 0.5;
            pointer-events: none;
          }
        }
        
        .item-info {
          flex: 1;
          
          .item-name {
            font-size: 0.9rem;
            font-weight: bold;
            margin-bottom: 4px;
            color: white;
          }
          
          .item-price {
            font-size: 0.8rem;
            color: #4CAF50;
            font-weight: bold;
          }
        }
      }
    }
  }
  
  .winner-showcase {
    position: absolute;
    inset: 0;
    pointer-events: none;
    
    .winner-glow {
      position: absolute;
      inset: 5px;
      border: 2px solid #FFD700;
      border-radius: 8px;
      box-shadow: 
        inset 0 0 20px rgba(255, 215, 0, 0.3),
        0 0 30px rgba(255, 215, 0, 0.5);
      animation: winner-glow-pulse 2s ease-in-out infinite;
    }
    
    .winner-particles {
      position: absolute;
      inset: 0;
      background: radial-gradient(circle at 50% 50%, rgba(255, 215, 0, 0.1) 0%, transparent 70%);
      animation: particles-float 3s ease-in-out infinite;
    }
  }
}

.battle-status {
  text-align: center;
  
  .status-text {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: #4CAF50;
  }
  
  .winner-announcement {
    font-size: 2rem;
    font-weight: bold;
    color: #FFD700;
    text-shadow: 0 0 20px rgba(255, 215, 0, 0.8);
    animation: winner-announce 1s ease-in-out infinite;
  }
}

// 动画定义
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes arrow-pulse {
  0%, 100% { opacity: 0.7; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.2); }
}

@keyframes winner-item-glow {
  0%, 100% { box-shadow: 0 0 15px rgba(255, 215, 0, 0.3); }
  50% { box-shadow: 0 0 25px rgba(255, 215, 0, 0.6); }
}

@keyframes winner-glow-pulse {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

@keyframes particles-float {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 0.6; transform: scale(1.05); }
}

@keyframes winner-announce {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

// 响应式设计
@media (max-width: 768px) {
  .players-container {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .player-slot {
    padding: 15px;
  }
  
  .vertical-slot-machine {
    height: 300px;
  }
  
  .demo-header h1 {
    font-size: 2rem;
  }
}
</style> 