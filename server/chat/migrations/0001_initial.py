# -*- coding: utf-8 -*-
# Generated by Django 1.11.13 on 2021-04-14 02:09
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODELL),
    ]

    operations = [
        migrations.CreateModel(
            name='ChatBot',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('chat_idle_min', models.IntegerField(default=0, verbose_name='chat idle min(seconds)')),
                ('chat_idle_max', models.IntegerField(default=0, verbose_name='chat idle max(seconds)')),
                ('remark', models.CharField(max_length=128, verbose_name='remark')),
                ('enable', models.BooleanField(default=True, verbose_name='enable')),
            ],
            options={
                'verbose_name': 'Chat Bot Config',
                'verbose_name_plural': 'Chat Bot Config',
            },
        ),
        migrations.CreateModel(
            name='ChatBotMessage',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('message', models.TextField(verbose_name='Message')),
                ('remark', models.CharField(max_length=128, verbose_name='remark')),
                ('enable', models.BooleanField(default=True, verbose_name='enable')),
            ],
            options={
                'verbose_name': 'Chat Bot Message',
                'verbose_name_plural': 'Chat Bot Message',
            },
        ),
        migrations.CreateModel(
            name='Message',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('message', models.TextField(verbose_name='Message')),
                ('timestamp', models.DateTimeField(db_index=True, default=django.utils.timezone.now, verbose_name='Timestamp')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='messages', to=settings.AUTH_USER_MODELL, verbose_name='user')),
            ],
        ),
        migrations.AddField(
            model_name='chatbot',
            name='message',
            field=models.ManyToManyField(blank=True, default=None, related_name='chat_bot', to='chat.ChatBotMessage', verbose_name='message'),
        ),
        migrations.AddField(
            model_name='chatbot',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='chat_bot', to=settings.AUTH_USER_MODELL, verbose_name='user'),
        ),
    ]
