{"version": 3, "file": "styles.css", "mappings": "AAMA,uCAEC,iBAWD,CATC,qDAIC,8BAA+B,CAF/B,MAAO,CAKP,mBAAoB,CANpB,iBAAkB,CAElB,OAKD,CAKA,wCACC,YACD,CAQD,iCACC,iBACD,CC7BC,8BACC,uCCOA,yCDLA,CACD,CCOA,qDACC,WAmBD,CDvBA,4BACC,qDCMC,6CDJD,CACD,CAZA,8BACC,qDCsBC,iBAAkB,CAMlB,eD1BD,CACD,C;AEJD,qDACC,YACD,C;ACAA,WAGC,sBACD,CCPA,MACC,gBAAiB,CACjB,4CAA+C,CAC/C,kBACD,CCDA,oDAEC,yBACD,CCNA,MAEC,+DAAoE,CAEpE,gEAAqE,CAErE,qEAA0E,CAC1E,oCAAqC,CACrC,sCAAuC,CACvC,kCAA2C,CAC3C,qDAAsD,CACtD,+BAA4C,CAC5C,yDAA0D,CAE1D,4BAA6B,CAC7B,6BAA8B,CAC9B,2FAAgG,CAChG,6FACD,CAEA,2CACC,qDAAsD,CAGtD,0CAA2C,CAD3C,eAAgB,CAEhB,gBAAiB,CACjB,mCAmDD,CAjDC,6DACC,4CAsCD,CApCC,+DAGC,kBAAmB,CAFnB,cAAe,CACf,YAAa,CAGb,qBAAsB,CACtB,4CAA6C,CAF7C,WAAY,CAGZ,qFACD,CAEA,mFASC,qCAAsC,CAFtC,cAAe,CANf,wCAAyC,CAIzC,eAAiB,CAHjB,kDAAmD,CAMnD,kBAAmB,CAFnB,gBAAiB,CAHjB,gBAAiB,CACjB,wBAOD,CAEA,sEAEC,cAAe,CADf,aAAc,CAGd,uCAAwC,CADxC,qCAED,CAGC,qEACC,mBAAqB,CACrB,SACD,CAIF,mEACC,wBACD,CAEA,mEACC,2BAA4B,CAC5B,8CACD,CC5ED,MAEC,qEAA0E,CAE1E,uEAA4E,CAE5E,2EAAgF,CAChF,0CAA2C,CAC3C,4CAA6C,CAC7C,wCAAiD,CACjD,2DAA4D,CAC5D,qCAAkD,CAClD,+DACD,CAEA,iDACC,2DAA4D,CAG5D,gDAAiD,CADjD,eAAgB,CAEhB,gBAAiB,CACjB,mCA0BD,CAxBC,yEACC,kDAAmD,CACnD,iGAYD,CAVC,qGAQC,2CAA4C,CAP5C,aAAc,CAEd,8CAA+C,CAE/C,eAAiB,CADjB,wDAAyD,CAEzD,kBAAmB,CAJnB,aAAc,CAKd,wBAED,CAGD,yEACC,wBACD,CAEA,yEACC,2BAA4B,CAC5B,oDACD,CC9CD,MACC,kCAAmD,CACnD,+BAAoD,CACpD,8BAAkD,CAClD,8BAAuD,CACvD,6BAAmD,CACnD,yBAA+C,CAC/C,8BAAsD,CACtD,oCAA4D,CAC5D,6BAAkD,CAIlD,mDAA4D,CAC5D,qEAA+E,CAC/E,qCAA4D,CAC5D,qDAA8D,CAC9D,gDAAyD,CACzD,yCAAqD,CACrD,sCAAsD,CACtD,4CAA0D,CAC1D,sCAAsD,CAItD,gDAAuD,CACvD,kDAAiE,CACjE,mDAAkE,CAClE,yDAA8D,CAE9D,uCAA6D,CAC7D,6CAAoE,CACpE,8CAAoE,CACpE,gDAAiE,CACjE,kCAAyD,CAGzD,+DAAsE,CACtE,iDAAsE,CACtE,kDAAsE,CACtE,oDAAoE,CACpE,6DAAsE,CAEtE,8BAAoD,CACpD,gCAAqD,CAErD,+CAA8D,CAC9D,qDAAiE,CACjE,+EAAqF,CACrF,oDAAuE,CACvE,yEAA8E,CAC9E,oDAAgE,CAIhE,oEAA2E,CAC3E,4DAAoE,CAIpE,wDAAiE,CACjE,4DAAmE,CAInE,2DAAoE,CACpE,mDAA6D,CAC7D,wDAAgE,CAChE,+CAA0D,CAC1D,4CAA2D,CAC3D,4DAAoE,CACpE,sCAAsD,CAItD,0DAAmE,CACnE,uFAA6F,CAC7F,oEAA2E,CAC3E,0EAA+E,CAC/E,8DAAsE,CAItE,2DAAoE,CACpE,mDAA6D,CAI7D,6DAAsE,CACtE,qDAA+D,CAI/D,uDAAgE,CAChE,uDAAiE,CAIjE,0CAAyD,CAIzD,wCAA2D,CAI3D,+BAAoD,CACpD,uDAAmE,CACnE,kDAAgE,CAIhE,oCAAyD,CAIzD,yBAAgD,CChHhD,wBAAyB,CCAzB,0CAA2C,CAK3C,gGAAiG,CAKjG,4GAA6G,CAK7G,sGAAuG,CAKvG,sDAAuD,CCvBvD,wBAAyB,CACzB,6BAA8B,CAC9B,wDAA6D,CAE7D,yBAA0B,CAC1B,2BAA4B,CAC5B,yBAA0B,CAC1B,wBAAyB,CACzB,0BAA2B,CCJ3B,kCJgHD,CI1GA,2EAYC,sBAAuB,CADvB,QAAS,CART,qBAAsB,CAEtB,WAAY,CAIZ,QAAS,CACT,SAAU,CAJV,eAAgB,CAOhB,oBAAqB,CAErB,eAAgB,CADhB,qBAAsB,CAVtB,UAAW,CAcX,oBACD,CAKA,8DAGC,wBAAyB,CAEzB,0BAA2B,CAG3B,WAAY,CACZ,UAAW,CALX,iGAAkG,CAElG,eAAgB,CAChB,kBAGD,CAGC,qDACC,gBACD,CAEA,mDAEC,sBACD,CAEA,qDACC,oBACD,CAEA,mLAGC,WACD,CAEA,iNAGC,cACD,CAEA,qDAEC,yBAAoC,CADpC,YAED,CAEA,qEAGC,QAAQ,CADR,SAED,CAMD,8BAEC,gBACD,CCxFA,MACC,sBAAuB,CCAvB,gEAAiE,CAKjE,0DAA2D,CAK3D,wEAAyE,CCbzE,uBAA8B,CAC9B,mDAA2D,CAC3D,4CAAkD,CAClD,oDAA4D,CAC5D,mDAA2D,CAC3D,kDAA2D,CAC3D,yDFFD,C;AGLA,MAEC,8DACD,CAEA,qBACC,YAAa,CACb,iBAAkB,CAElB,yBAyCD,CAtCE,+GAEC,UAAW,CACX,iBACD,CAEA,wDACC,6CACD,CAEA,uDACC,uDACD,CAIA,4CACC,6CACD,CAEA,2CACC,uDACD,CAIA,4CACC,6CACD,CAEA,2CACC,uDACD,CAGD,8CACC,aACD,CC9CD,MACC,6BAA8B,CAC9B,6BAA8B,CAC9B,8BAA+B,CAC/B,iCAAkC,CAClC,oEACD,CAEA,qBAMC,2CAA4C,CAC5C,wEAAyE,CCZzE,eAAgB,CDShB,eA0LD,CCjMC,iFAEC,qCAED,CDDD,qBEPC,oCFqMD,CApLE,+GAIC,kBAAmB,CADnB,QAAS,CADT,OAGD,CAIA,uFAEC,mHACD,CAEA,4CACC,6EAA8E,CAC9E,kDACD,CAEA,2CACC,iFAAkF,CAClF,gFACD,CAIA,uFAEC,mHACD,CAEA,4CACC,iEAAkE,CAClE,uDAAwD,CACxD,qDACD,CAEA,2CACC,iFAAkF,CAClF,mFACD,CAIA,uFAEC,mHACD,CAEA,4CACC,6EAA8E,CAC9E,oDACD,CAEA,2CACC,iFAAkF,CAClF,kFACD,CAIA,uFAEC,mHACD,CAEA,4CACC,6EAA8E,CAC9E,mDACD,CAEA,2CACC,iFAAkF,CAClF,iFACD,CAIA,yGAEC,QAAS,CACT,uDAA0D,CAC1D,2CACD,CAIA,2GAEC,+CAAkD,CAClD,2CACD,CAIA,2GAEC,gDAAmD,CACnD,2CACD,CAIA,yGAIC,8CAAiD,CAFjD,QAAS,CACT,uDAED,CAIA,2GAGC,8CAAiD,CADjD,+CAED,CAIA,2GAGC,8CAAiD,CADjD,gDAED,CAIA,6GAIC,8CAAiD,CADjD,uDAA0D,CAD1D,SAGD,CAIA,6GAIC,8CAAiD,CAFjD,QAAS,CACT,sDAED,CAIA,6GAGC,uDAA0D,CAD1D,SAAU,CAEV,2CACD,CAIA,6GAEC,QAAS,CACT,sDAAyD,CACzD,2CACD,CAIA,yGAGC,sDAAyD,CADzD,6CAAgD,CAEhD,OACD,CAIA,yGAEC,4CAA+C,CAC/C,sDAAyD,CACzD,OACD,C;AGrMF,gCCEC,qBAAsB,CACtB,wBAAyB,CACzB,oBAAqB,CACrB,gBAAgB,CDFhB,sCAAyC,CEFzC,6BAA8B,CAC9B,6BAA8B,CAC9B,iCAAkC,CAClC,6BAA8B,CAC9B,6BAA8B,CAC9B,8DAA+D,CAkB/D,eAAgB,CAhBhB,kCFJD,CEMC,kDAGC,kCAAmC,CAFnC,cAAe,CACf,eAED,CAEA,wEAEC,oBAAqB,CAErB,eAAgB,CADhB,wCAAyC,CAFzC,wBAID,CAMA,uCACC,YACD,C;AC/BD,YACC,qBACD,CCFA,MACC,0EAA6E,CAC7E,iCACD,CAEA,YAKC,kCAAmC,CAHnC,0BAA2B,CAD3B,yBA2BD,CAlBC,0BAFA,cAKA,CAMC,sEACC,aAMD,CAJC,+CAEC,iBACD,C;AC9BH,2BAEC,aAAc,CADd,iBAAkB,CAElB,YACD,CAEA,6BACC,oBACD,C;ACRA,MACC,sCACD,CAEA,kDACC,gDACD,C;ACNA,oBAIC,kBAAmB,CAHnB,YAAa,CACb,kBAAmB,CACnB,gBAAiB,CAEjB,6BAKD,CAHC,8CACC,WACD,CCPD,MACC,4BACD,CAEA,oBAIC,mDAAoD,CAFpD,mCAAoC,CACpC,wCAAyC,CAFzC,uDAiCD,CCvCC,uCDaE,qCCXF,CAFA,uCDiBE,oCCfF,CDmBA,4CACC,wBAAyB,CACzB,eACD,CAGA,mDACC,8DACD,CAGA,yDACC,8DACD,CAEA,oCACC,yCACD,C;AEpCD,6BAMC,kBAAmB,CADnB,mBAAoB,CADpB,iBAAkB,CTHlB,qBAAsB,CACtB,wBAAyB,CACzB,oBAAqB,CACrB,gBS0BD,CDhCC,iDCWC,oBDTD,CAFA,iDCeC,qBDbD,CCgBA,iEACC,YACD,CAGC,yGACC,oBACD,CAID,iFACC,sBACD,CCzBD,6BA6BC,uBAAwB,CC7BxB,oDAAqC,CDuBrC,4BAA6B,CbxB7B,eAAgB,CaOhB,cAAe,CAcf,iBAAkB,CAHlB,aAAc,CAJd,4CAA6C,CAD7C,2CAA4C,CAJ5C,8BAA+B,CAC/B,iBAAkB,CAiBlB,4DAA8D,CAnB9D,qBAAsB,CAFtB,kBAwID,CC1IE,6EACC,0DACD,CAEA,+EACC,2DACD,CAID,qDACC,6DACD,CddA,wIAEC,qCAED,Ca0BA,uCA/BD,6BAgCE,eA6GF,CA5GC,CAEA,oFEpCA,2BAA2B,CdF3B,2CAA8B,CcC9B,YFyCA,CAIC,kJAEC,aACD,CAGD,iEAIC,aAAc,CACd,cAAe,CAHf,iBAAkB,CAClB,mBAAoB,CAMpB,qBASD,CFzEA,qFEmEE,eFjEF,CAFA,qFEuEE,gBFrEF,CEyEA,yEACC,aAAc,CAUd,UACD,CFvFA,6FE+EE,mCF7EF,CAFA,6FEmFE,oCFjFF,CEyFC,oIZxFD,oDY4FC,CAOA,gLGnGD,kCHqGC,CAEA,iGACC,UACD,CAGD,qEACC,yDAYD,CF5HA,2HEqHG,qCFnHH,CAFA,2HEyHG,oCFvHH,CE8HC,mHACC,WACD,CAID,yCChIA,+CAAqC,CDmIpC,qCACD,CCjIC,yFACC,qDACD,CAEA,2FACC,sDACD,CAID,iEACC,wDACD,CDuHA,2DACC,iCACD,CAEA,+DACC,mCACD,CAID,2CChJC,mDAAqC,CDoJrC,wCACD,CClJE,2FACC,yDACD,CAEA,6FACC,0DACD,CAID,mEACC,4DACD,CDwID,uCAEC,eACD,C;AIhKC,qCACC,YAAa,CACb,wBAAyB,CCDzB,+BDED,CCAC,yCACC,mCACD,C;ACNF,sBAKC,QAAS,CACT,MAAO,CAJP,wBAAyB,CAEzB,cAAe,CAGf,OAAQ,CACR,KAAM,CAPN,gBAcD,CALC,qDAEC,cAAe,CACf,eAAgB,CAFhB,mBAGD,CAGD,cACC,wBAAyB,CAEzB,iBAAkB,CADlB,iBAiBD,CAdC,kCACC,aACD,CAIC,gFACC,WACD,CAGD,mEACC,kBACD,CC/BD,MACC,mDAA2D,CAC3D,uDAA8D,CAC9D,2BAA4B,CAC5B,2BAA4B,CAC5B,4DAA6D,CAC7D,gEACD,CAEA,sBACC,+BAAgC,CAChC,oDAAqD,CACrD,0BACD,CAEA,cpBbC,eAAgB,CoBiBhB,6CAA8C,CAE9C,4CAA6C,CAG7C,4CAA6C,CAF7C,sCAAuC,CACvC,oCAAqC,CAErC,2BAKD,CpB1BC,mEAEC,qCAED,CoBOD,cnBfC,oCmB8BD,CAHC,kCACC,iEACD,CAGD,yBACC,eACD,CAEA,6BACC,GACC,sBACD,CAEA,GACC,oDACD,CACD,C;AChDA,cAIC,iBAMD,CAJC,2DAEC,yBACD,CCLC,8DAOC,wCAAsB,CtBLxB,eAAgB,CsBKd,sBAcD,CtBjBD,mKAEC,qCAAsC,CsBJpC,2BAA4B,CAC5B,4BtBKH,CsBCE,8FACC,uBACD,CAEA,2EACC,QAAS,CACT,mDACD,CAEA,0EACC,QACD,CAMH,yCAEC,0CAA2C,CtB1B3C,esBoCD,CtBlCC,yHAEC,qCAAsC,CsByBtC,wBAAyB,CACzB,yBtBxBD,CsB2BA,0DACC,wCACD,C;ACnCD,MACC,0CACD,CAEA,yDvBJC,euBWD,CvBTC,yJAEC,qCAED,CuBCA,oERPA,2BAA2B,CdF3B,qCAA8B,CcC9B,YQWA,CAGD,+BAGC,4BAA6B,CAF7B,aAAc,CACd,oCA6BD,CA1BC,wCACC,eACD,CAEA,wCACC,gBACD,CAGA,4CACC,kCACD,CAGA,2CAKC,qCACD,CAGA,sDACC,kDACD,CAKA,gEACC,oDACD,CAIA,gEACC,iDACD,C;AC9DD,aACC,aACD,CAEA,mBACC,YACD,CCNA,aACC,eACD,C;ACDC,qDAEC,cAAe,CACf,KAAM,CAFN,yBAGD,CAEA,kEAEC,iBAAkB,CADlB,QAED,CCPA,qDAIC,wBAAyB,CACzB,yBAA0B,CAF1B,sBAAuB,C1BFxB,oC0BKA,C;ACTD,MACC,4BACD,CAEA,gBACC,oBAAqB,CACrB,iBA2ED,CAzEC,oCACC,mBAAoB,CACpB,2BACD,CAGA,+CACC,UACD,CAEA,oCACC,YAAa,CAEb,sCAAuC,CAEvC,iBAAkB,CAHlB,yBA4DD,CAvDC,+DACC,oBACD,CAEA,mSAKC,WACD,CAEA,mSAUC,WAAY,CADZ,QAED,CAEA,oHAEC,MACD,CAEA,oHAEC,OACD,CAEA,kHAGC,QAAS,CACT,0BACD,CAEA,sHAGC,QAAS,CACT,0BACD,CAEA,sHAGC,QAAS,CACT,0BACD,CAQF,mCACC,mCACD,CCpFA,MACC,sDACD,CAEA,gBAEC,iBA2ED,CAzEC,oCACC,mCACD,CAGC,8CAIC,sCAAuC,CAHvC,gCAID,CAIA,8CACC,+BAAgC,CAGhC,oCACD,CAGD,gDb/BA,kCaiCA,CAIE,mFAEC,oCACD,CAIA,mFAEC,qCACD,CAID,iEAEC,eAAgB,CAChB,sBAAuB,CAFvB,SAGD,CAGA,6Eb1DD,kCa4DC,CAGA,qDACC,2BAA4B,CAC5B,4BACD,CAEA,sGACC,UACD,CAGA,yHAEC,eAKD,CAHC,qI5B7EF,2C4B+EE,CAKH,uBAIC,oDAAqD,CACrD,sDAAuD,C7BvFvD,eAAgB,C6BwFhB,QAAS,CAGT,cAuBD,C7BhHC,qFAEC,qCAED,C6B4ED,uB5BpFC,oC4BoHD,CAnBC,6CACC,wBACD,CAEA,6CACC,yBACD,CAEA,6CACC,2BACD,CAEA,6CACC,4BACD,CAEA,6BACC,YACD,C;ACrHD,YAGC,YAAa,CACb,qBAAsB,C3BFtB,qBAAsB,CACtB,wBAAyB,CACzB,oBAAqB,CACrB,gB2BaD,CAZC,2DAEC,aACD,CAKA,kCACC,iBAAkB,CAClB,2BACD,CCdD,YAIC,0CAA2C,C/BH3C,eAAgB,C+BEhB,oBAAqB,CAIrB,iCACD,C/BLC,+DAEC,qCAED,C+BGD,kBACC,cAAe,CAGf,cA4DD,CA1DC,uDAIC,eAAgB,CAFhB,gBAAiB,CADjB,iEAAoE,CAEpE,UAwCD,CpB/DA,iEoB2BE,epBzBF,CAFA,iEoB+BE,gBpB7BF,CoBgCC,yEAEC,qEACD,CAEA,8DACC,eACD,CAEA,6DACC,oDAAqD,CACrD,yCAaD,CAXC,oEACC,eACD,CAEA,qFACC,0DACD,CAEA,qFACC,4CACD,CAGD,+EACC,uDACD,CAMA,mDACC,0CAA2C,CAC3C,aAMD,CAJC,2EACC,uDAAwD,CACxD,aACD,CAKH,yBACC,oCAiBD,CAdC,qCACC,aACD,CAGA,0CACC,gDACD,CAEA,mCACC,cAAe,CACf,eAAiB,CACjB,0DACD,CAGD,uBAGC,sCAAuC,CAFvC,UAAW,CAKX,gCAAiC,CAJjC,UAKD,C;AC5GA,mBAEC,iBAKD,CAHC,iDACC,qCACD,CCJD,MACC,gDAAyD,CACzD,4CACD,CAQE,oIAGC,gCAAiC,CADjC,6BAED,CAEA,oIAGC,+BAAgC,CADhC,4BAED,CAGD,0CAGC,eAiBD,CAfC,oDAGC,+BAAgC,CADhC,4BAED,CAEA,oDAGC,gCAAiC,CADjC,6BAED,CAEA,8CACC,mCACD,CAKD,sDAEC,qBAAwB,CADxB,kBAED,CAIA,uCjCnDA,eiC6DA,CjC3DA,qHAEC,qCAED,CiC+CE,qKACC,2BACD,CAEA,mKACC,4BACD,CAUD,0KACC,wDACD,CAIA,8JAKC,0DAA2D,CAJ3D,UAAW,CAGX,WAAY,CAFZ,iBAAkB,CAClB,SAGD,CAGA,sIACC,iEACD,CAGC,kLACC,SACD,CAIA,kLACC,UACD,C;ACnGD,4HACC,aACD,CCCF,MAEC,8CAA+C,CAE/C,0DAAgE,CAChE,2HAIC,CACD,0FACD,CAOC,0QAEC,sBAAuB,CADvB,aAED,CxB3BA,0DwBgCE,4CxB9BF,CAFA,0DwBqCE,2CxBnCF,CwBuCA,iDAkBC,uDAAwD,CAFxD,4BAA6B,CnCpD9B,eAAgB,CmCmDf,iFAAsF,CAEtF,0CA2BD,CnC9EA,yIAEC,qCAED,CWXA,2DwB8CE,gBxB5CF,CAFA,2DwBmDE,iBxBjDF,CwB2DC,2EAOC,yDAA0D,CnC/D5D,eAAgB,CmC8Dd,gDAAiD,CAIjD,uBAA0B,CAL1B,+CAUD,CnCrED,6LAEC,qCAAsC,CmCsDpC,8CnCpDH,CmC8DE,uCAZD,2EAaE,eAEF,CADC,CAGD,uDACC,6DAKD,CAHC,iFACC,qDACD,CAIF,6DnBpFA,kCmBsFA,CAGA,oCACC,wBAAyB,CAEzB,eAAgB,CADhB,YAQD,CALC,uDACC,iGAAmG,CAEnG,4BAA6B,CAD7B,kBAED,CAKA,uDACC,sDAkBD,CAhBC,6DACC,4DACD,CxB/GF,2FwBsHI,2DxBpHJ,CAFA,2FwB0HI,oExBxHJ,C;AyBHD,MACC,oCACD,CAEA,4CAGC,8CAA+C,CAD/C,iBAQD,CAJE,6DACC,qCACD,CCZF,oCACC,QACD,C;ACAA,6CtCIC,esCqBD,CtCnBC,iIAEC,qCAAsC,CsCJtC,wBtCMD,CsCAC,mFtCND,esCYC,CtCVD,6MAEC,qCAAsC,CsCKpC,2BAA4B,CAC5B,4BAA6B,CAF7B,wBtCFH,CsCQC,kFtCdD,esCmBC,CtCjBD,2MAEC,qCAAsC,CsCYpC,wBAAyB,CACzB,yBtCXH,C;AuCVD,wBAGC,eAAgB,CAFhB,gBAAiB,CACjB,UAsCD,C5BzCC,kC4BOC,e5BLD,CAFA,kC4BWC,gB5BTD,CAFA,iE4BgBE,oC5BdF,CAFA,iE4BoBE,qC5BlBF,C4BsBA,2DACC,mBAAoB,CAEpB,WAAY,CADZ,UAUD,C5BpCA,qE4B8BE,oC5B5BF,CAFA,qE4BkCE,mC5BhCF,C4BoCA,yDACC,WACD,CCvCD,kCACC,iEAiCD,CA/BC,0EAEC,0CAA2C,CAC3C,0BACD,C7BRA,oF6BYE,oC7BVF,CAFA,oF6BgBE,qC7BdF,C6BuBA,6OAIC,uDAKD,CAHC,qTACC,0BACD,C;AChCF,eAKC,kBAAmB,CAFnB,YAAa,CACb,oBAAqB,CtCFrB,qBAAsB,CACtB,wBAAyB,CACzB,oBAAqB,CACrB,gBsC6CD,CA3CC,kCAGC,kBAAmB,CAFnB,YAAa,CACb,kBAAmB,CAEnB,WAED,CAEA,yCACC,oBAWD,CAJC,yGAEC,YACD,CAGD,uCACC,eACD,CAEA,sDACC,gBACD,CAEA,sDACC,qBACD,CAEA,sDACC,gBACD,CAGC,yFACC,YACD,CC/CF,eAGC,6CAA8C,CAE9C,+CAAgD,C1CFhD,eAAgB,C0CChB,iCAuGD,C1CtGC,qEAEC,qCAED,C0CFA,yCAIC,yCAA0C,CAH1C,0BAA2B,CAU3B,qCAAsC,CADtC,kCAAmC,CAPnC,aAAc,CADd,SAUD,CAEA,uCACC,QACD,CAGC,gEAEC,oCACD,CAIA,kEACC,YACD,CAGD,gHAIC,qCAAsC,CADtC,kCAED,CAEA,mCAEC,SAaD,CAVC,0DAQC,eAAgB,CAHhB,QAAS,CAHT,UAOD,CAGD,kCAEC,SAWD,CATC,uDAEC,QAMD,CAHC,yFACC,eACD,CASD,kFACC,mCACD,CAMA,wEACC,cACD,CAEA,iFACC,aAAc,CACd,UACD,CAGD,qBACC,YACD,CAEA,qCACC,QACD,CAaA,+FACC,cACD,CAEA,iJAEC,mCACD,CAEA,qHACC,aACD,CAIC,6JAEC,2BAA4B,CAD5B,wBAED,CAGA,2JAEC,4BAA6B,CAD7B,yBAED,CASD,8RACC,mCACD,CAWA,qHACC,cACD,CAIC,6JAEC,4BAA6B,CAD7B,yBAED,CAGA,2JAEC,2BAA4B,CAD5B,wBAED,CASD,8RACC,oCACD,C;ACjMA,mFACC,mBAAoB,CACpB,2BACD,CCIA,iDACC,UAuBD,CArBC,mEACC,WAAY,CACZ,eAAgB,CAChB,sBACD,CAEA,+E5BbD,kC4BeC,CAGC,qFACC,oCACD,CAIA,qFACC,qCACD,CAOF,6EAEC,gBAAiB,CADjB,wDAsBD,CAnBC,+FAEC,iBAAkB,CAMlB,gBAAiB,CAPjB,WAQD,CAEA,mFACC,2BAA4B,CAC5B,4BACD,CAEA,sFACC,YACD,CAMD,mFACC,eA+BD,CA7BC,qHACC,mCAuBD,CjC3FD,+HiC0EG,sCAAuC,CAGvC,6CAAgD,CANhD,wBjCrEH,CAFA,+HiCmFG,+BAAgC,CAMhC,4CAA+C,CAH/C,oCAAqC,CALrC,uBjC/EH,CiC2FC,iI5B1FD,kC4B4FC,C;AChGF,MACC,iCAAkC,CAClC,+CACD,CAEA,mDAEC,2CAA4C,CAC5C,iBAAkB,CAFlB,yBAkDD,CA9CC,gLAEC,WACD,CAEA,gLAGC,WAAY,CADZ,QAED,CAEA,gLAEC,MACD,CAEA,gLAEC,OACD,CAEA,gLAEC,kEACD,CAEA,wFACC,KACD,CAEA,wFACC,QACD,CAEA,gLAEC,mEACD,CAEA,wFACC,KACD,CAEA,wFACC,QACD,CCnDD,MACC,uCACD,CAEA,mDAIC,oDAAqD,CACrD,sDAAuD,C9CPvD,eAAgB,C8CQhB,QAAS,CACT,kBAAmB,CACnB,iDA0BD,C9ClCC,6IAEC,qCAED,C8CJD,mD7CJC,oC6CsCD,CAvBC,gLAEC,wBACD,CAEA,gLAEC,yBACD,CAEA,gLAEC,2BACD,CAEA,gLAEC,4BACD,CAEA,yDACC,YACD,CAKC,uFAEC,wBAAyB,CACzB,eACD,CAIA,yJ/BhDD,2BAA2B,CdF3B,2CAA8B,CcC9B,YAAa,C+BoDX,iBAAkB,CAClB,SAID,C;AC9DF,sBACC,aAAc,CCCd,iBAAkB,CDAlB,iBACD,CCCC,kDACC,cACD,C;ACAA,iEACC,eAoBD,CAlBC,0LAGC,8BACD,CAEA,uFAEC,kCASD,CtCzBD,iGsCmBG,qCtCjBH,CAFA,iGsCuBG,oCtCrBH,C;AuCHD,gBAIC,0CAA2C,CAG3C,+CAAgD,CANhD,YAAa,CACb,cAAe,CAIf,2BAA4B,CAH5B,0BAA2B,CAE3B,+BAAgC,CAGhC,UACD,C;ACLC,+CACC,aAAc,CAEd,eAAgB,CADhB,iBAAkB,CAElB,2BAwBD,CAtBC,mDAGC,QAAS,CAFT,iBAAkB,CAClB,OAAQ,CAER,qCACD,CAEA,qFAGC,kBAAoB,CADpB,gDAAoD,CAGpD,0BACD,CAEA,oFAEC,mDAAuD,CACvD,mBAAqB,CAErB,yBACD,CAUA,mLACC,UAAW,CACX,aAAc,CAGd,QAAS,CAFT,iBAAkB,CAClB,OAAQ,CAER,qCACD,CAMD,2EACC,YAAa,CAEb,MAAO,CADP,iBAAkB,CAElB,OACD,CAOA,iFACC,gDAAqD,CACrD,iDACD,CAKA,wHAEC,aAAc,CADd,qDAED,CAKA,uHACC,wDAA6D,CAC7D,aACD,CAoBD,mOACC,YACD,CC3GA,MACC,wCAAyC,CACzC,wEAAyE,CACzE,8EAA+E,CAC/E,2FAA4F,CAC5F,wDAAyD,CACzD,uDAAwD,CACxD,yEACD,CAgBC,+CAGC,oDAAqD,CACrD,mBAAoB,CAFpB,+CAAgD,CAVjD,SAAU,CACV,mBAAoB,CAYnB,uMAAyM,CAJzM,8CAwED,CAhEC,uCATD,+CAUE,eA+DF,CA9DC,CAEA,mDAEC,UAAW,CAGX,cAAe,CAFf,8BAA+B,CAC/B,6BAA8B,CAH9B,UAwBD,CAlBC,uCAPD,mDAQE,eAiBF,CAhBC,CAEA,qDACC,mBAAoB,CACpB,mBAAoB,CAEpB,SAAU,CACV,qDAAsD,CACtD,kBAAmB,CACnB,oBAAqB,CACrB,qBACD,CAEA,wDACC,kBACD,CAGD,qDAIC,6DA4BD,CAtBE,kEACC,oDACD,CAEA,8DACC,wDACD,CAGD,uCAQE,qLACC,cACD,CAEF,CAQD,6EA5FD,SAAU,CACV,mBA6FC,CAOD,gGACC,0DACD,CAOA,uKAEC,2DAQD,CANC,mLAIC,uEAAkF,CADlF,mBAAoB,CADpB,2DAA4D,CAD5D,0DAID,CAQA,6JAEC,yBACD,CAKA,0FAvID,SAAU,CACV,mBAwIC,CAOD,8GACC,gBACD,CAKA,mDAGC,mFAAoF,CAOpF,oCAAqC,CARrC,UAAW,CAOX,oCAAwC,CARxC,mBAUD,CASC,yKACC,iDACD,CAMA,uOA9KD,SAAU,CACV,mBA+KC,CAoBA,6yBACC,SACD,CASF,uHACC,aAAc,CACd,iBACD,CAaG,iRA/NF,SAAU,CACV,mBAgOE,CASF,mGA1OA,SAAU,CACV,mBA2OA,CAMD,kIACC,qEAKD,CAHC,wIACC,WACD,CAGD,4CACC,GACC,oBACD,CACA,OACC,mBACD,CACD,CAEA,gDACC,OACC,mBACD,CACA,OACC,mBACD,CACD,CAEA,8CACC,GACC,6HACD,CACA,IACC,6HACD,CACA,GACC,+HACD,CACD,CAEA,kDACC,GACC,SACD,CACA,IACC,SACD,CACA,IACC,SACD,CACA,IACC,SACD,CACA,GACC,SACD,CACD,C;ACxTA,MACC,+CAAgD,CAChD,6CAAsD,CACtD,uCAAgD,CAEhD,kDAAmD,CACnD,gCAAiC,CACjC,kEACD,CAOA,8DAEC,iBAqBD,CAnBC,4EACC,iBAOD,CALC,qFAGC,aACD,CASD,iLACC,kBACD,CAGD,kBACC,qDAAsD,CAEtD,qDAAsD,CACtD,6CAA8C,CAF9C,0CAA2C,CAI3C,aAAc,CADd,kCAAmC,CAGnC,uCAAwC,CACxC,4CAA6C,CAF7C,iCAsCD,CAlCC,8NAKC,iBACD,CAEA,0CAEC,qCAAsC,CADtC,oCAED,CAEA,2CAEC,sCAAuC,CADvC,oCAED,CAEA,8CACC,uCAAwC,CACxC,sCACD,CAEA,6CACC,uCAAwC,CACxC,qCACD,CAGA,8CAEC,QAAS,CADT,6CAAgD,CAEhD,yBACD,CChFD,MACC,iCAAkC,CAClC,kCAAmC,CACnC,4CAA6C,CAC7C,wCAAyC,CAEzC,wCAAiD,CACjD,sCAAkD,CAClD,2EAA4E,CAC5E,yEACD,CAEA,eAGC,yBAA0B,CAD1B,mBAAoB,CADpB,gDAAiD,CAGjD,6GAcD,CAZC,uCAND,eAOE,eAWF,CAVC,CAEA,0EAEC,6EACD,CAEA,qBACC,iDACD,CAGD,gCACC,4BAoBD,CAhBC,yGrDrCA,qCqDoDA,CxE/CA,4BACC,yGwEmCC,iExEjCD,CACD,CwEwCC,+HvC/CD,2BAA2B,CAD3B,YuCkDC,CAKD,4EAKC,4BAA6B,CAa7B,iEAAkE,CAhBlE,qBAAsB,CAoBtB,mDAAoD,CAhBpD,SAAU,CALV,WAAY,CAsBZ,KAAM,CAFN,2BAA4B,CAT5B,6SAwCD,CA3BC,uCAzBD,4EA0BE,eA0BF,CAzBC,CAEA,qFAIC,oDAAqD,CADrD,yCAA0C,CAD1C,wCAeD,CAVC,kHACC,SAAU,CAGV,+DAKD,CAHC,uCAND,kHAOE,eAEF,CADC,CAKF,wHACC,SACD,CAID,kFAEC,oDAAqD,CADrD,SAED,CAKC,oMAEC,6CAA8C,CAD9C,SAOD,CAHC,gRACC,SACD,CAOH,qFACC,SAAU,CACV,oDACD,CAGA,gDAEC,eAkBD,CAhBC,yEAOC,iCACD,CAGC,gOAEC,gDACD,CAOF,2CACC,yBACD,CAGA,wGACC,SAAU,CACV,iBACD,CAKC,wIAEC,mDAQD,CALE,ghBAEC,gDACD,CAKH,yKAOC,yDACD,C;AC1MA,oCAEC,kBAAmB,CADnB,YAAa,CAEb,sBACD,CAKA,6CACC,sBACD,CCXA,oCACC,6CAA8C,CAC9C,sDAAuD,CACvD,iCAgBD,CAbC,sCAGC,qCAAsC,CAFtC,oCAAqC,CACrC,kCAED,CAGA,iEAIC,mCAAoC,CAHpC,uCAID,CAMA,2DACC,eACD,C;AC1BD,mBACC,iBAAkB,CAGlB,mCACD,CAEA,uBACC,iBACD,CAEA,mCACC,SACD,CAEA,oCACC,SACD,CCfA,MACC,6CAA8C,CAC9C,2CACD,CAGA,uBAKC,2CAA4C,CAC5C,6CAA8C,CAC9C,qCAAsC,CzDXtC,oCAA8B,CyDc9B,WAAY,CAPZ,eAAgB,CAMhB,UAED,CAEA,mCACC,0DAA2D,CAC3D,uDACD,CAEA,oCACC,kEAAqE,CACrE,+DACD,CACA,oCACC,kEAAqE,CACrE,+DACD,CAGA,yIAGC,4CACD,C;ACxCA,4BAEC,iBACD,CAEA,wBACC,YAAa,CAMb,MAAO,CAFP,mBAAoB,CAHpB,iBAAkB,CAMlB,KACD,CAGC,2EACC,aACD,CAGD,gCAIC,kBAAmB,CAHnB,iBAcD,CATC,4IAEC,kBACD,CAEA,4IAEC,kBACD,CCpCD,MACC,sBAAuB,CAGvB,yDAAiE,CACjE,6BACD,CAEA,wBACC,yCACD,CAEA,gCAGC,uCAAwC,CACxC,gDAA6D,CAC7D,6CAA8C,CAH9C,6BAA8B,CAD9B,4BAyBD,CAnBC,oEAEC,6BAA8B,CAD9B,4BAED,CAEA,qEAEC,8BAA+B,CAD/B,4BAED,CAEA,wEACC,+BAAgC,CAChC,8BACD,CAEA,uEACC,+BAAgC,CAChC,6BACD,C;ACjCA,8DACC,cAAe,CAEf,mBAAoB,CADpB,iBAOD,CAJC,mEACC,iBAAkB,CAClB,OACD,CAWA,qJACC,YACD,CAIF,kCAEC,mBAAoB,CADpB,iBAED,CC9BA,MACC,yCAA0C,CAC1C,yCAA0C,CAC1C,6DACD,CAOE,mEAIC,gDAAiD,CADjD,sDAAuD,CAFvD,2DAA8D,CAI9D,gBAAiB,CAHjB,wDAqBD,CAfC,yEAWC,sFAAuF,CAEvF,kBAAmB,CADnB,qKAA0K,CAX1K,UAAW,CAIX,aAAc,CAFd,QAAS,CAIT,QAAS,CADT,iBAAkB,CAElB,wDAA2D,CAE3D,0BAA2B,CAR3B,OAYD,CAOF,kEACC,gGACD,CAKA,gDACC,OAAS,CACT,sBACD,CAGD,kCAGC,gDAAiD,CADjD,sDAAuD,CADvD,QAAS,CAGT,eAwBD,CAtBC,yCAMC,kBAAmB,CALnB,UAAW,CAIX,QAAS,CAHT,iBAAkB,CAClB,uDAA0D,CAC1D,OAiBD,CnDjFA,mDmDwEE,sFAAuF,CADvF,+JAAoK,CAFpK,SnDnEF,CAFA,mDmD+EE,sFAAuF,CADvF,+JAAmK,CAFnK,UnD1EF,C;AoDAD,MACC,8CAA+C,CAC/C,+CAAgD,CAChD,mDAA8D,CAC9D,4DAAyE,CACzE,uDACD,CAEA,wEAOC,4BAA6B,CAJ7B,yDAA0D,CAD1D,uDAAwD,CAExD,aAAc,CAHd,+BAAgC,CAIhC,gBAgFD,CA5EC,8EhDdA,2BAA2B,CdF3B,2CAA8B,CcC9B,YgDkBA,CAEA,0EACC,kBACD,CAGA,kFACC,YACD,CAEA,2EAEC,eAAgB,CADhB,eAED,CAEA,2EAEC,aAAc,CADd,eAED,CAEA,8SAIC,YACD,CAEA,2EAIC,kBAAmB,CADnB,qEAAsE,CAFtE,YAAa,CACb,6BAiBD,CAbC,4JACC,wEAAyE,CACzE,cACD,CAEA,8EACC,mBACD,CAEA,8EACC,mBAAoB,CACpB,gBACD,CAGD,yJAEC,oEAAqE,CAIrE,iBAAkB,CALlB,oBAAqB,CAOrB,cAAe,CAHf,aAAc,CAFd,YAAa,CAIb,iBAAkB,CAHlB,qBAKD,CAEA,6EACC,qBACD,CAEA,4EAEC,yEAA4E,CAC5E,YAAa,CAFb,eAOD,CAHC,gFACC,eACD,C;AC9FF,iBACC,kCAAuC,CAEvC,iBAAkB,CADlB,aAED,CAEA,0CACC,kCACD,C;ACRA,uBAWC,0BAAsC,CADtC,iBAAkB,CAFlB,aAAc,CACd,cAAe,CAPf,eAAgB,CAIhB,kBAAmB,CADnB,mBAOD,CAEA,gCACC,aAAc,CACd,2BACD,C;ACjBA,0CACC,cACD,CAEA,0CACC,cACD,CAEA,0CACC,cACD,CAEA,+BACC,eACD,CCZC,2EACC,SACD,CAEA,uEACC,cACD,C;ACND,MACC,sCACD,CCHC,oCDMA,MACC,sCACD,CCNA,CDUA,gDACC,2CACD,C;AEZA,mCAMC,WAAY,CALZ,UAAW,CAEX,iBAAkB,CAClB,UAAW,CACX,QAAS,CAHT,OAAQ,CAKR,SACD,CAEA,yCACC,YACD,CDdA,oCCoBE,wCAMC,WAAY,CALZ,UAAW,CAEX,iBAAkB,CAClB,UAAW,CACX,QAAS,CAHT,OAAQ,CAKR,SACD,CAEA,8CACC,YACD,CD9BF,CEAD,qDACC,kDACD,CAEA,uBACC,+BAoED,CAlEC,6BAEC,YACD,CASC,uGACC,sCACD,CFvBD,oCEMD,uBAqBE,SAAU,CACV,oCA+CF,CA7CE,8CACC,wDAYD,CAVC,4HAEC,WAAY,CACZ,UACD,CAGA,4EACC,kBACD,CAKA,0DACC,kDACD,CAGD,iGAIC,eAAgB,CADhB,kCAAmC,CADnC,kCAmBD,CAfC,yHACC,gDACD,C5D5DF,0O4DmEG,a5DjEH,C4DmEG,+IACC,kDACD,CFrEH,C;AGAD,MACC,6BACD,CAEA,YACC,mCAyED,CAvEC,kCACC,kCACD,CAEA,kBACC,YACD,CAEA,8DAEC,cAAe,CACf,OACD,CAEA,4BACC,cAWD,CARE,6DACC,4CACD,CAEA,mEACC,UACD,CHjCF,oCGwCE,yEAGE,mBAAoB,CADpB,qBAAsB,CAEtB,SAcF,CAXC,6EAEE,wDAEF,CAEA,8FAEE,sBAEF,CAGD,kFAEE,sCAEF,CH9DF,CGwEE,uHACC,aACD,C;AC7EH,iBAIC,sBAAuB,CAHvB,YAAa,CACb,kBAAmB,CACnB,6BAA8B,CAE9B,4DAqBD,CAnBC,gDACC,mCACD,CAEA,mDACC,sCACD,CAEA,0CACC,gBAKD,CAHC,6DACC,8CACD,CAGD,kCACC,SACD,C;ACxBA,mEACC,YAAa,CACb,iBACD,CAEA,uCACC,aAAc,CACd,iBACD,CCND,MACC,kEAAsE,CACtE,gFAAiF,CACjF,oEAAqE,CACrE,8EAAiF,CACjF,yEACD,CAEA,0B3ELC,e2EuHD,C3ErHC,2FAEC,qCAED,C2EEA,mEACC,UAwCD,CAtCC,gFAkBC,yDAA0D,CAG1D,eAAmB,CADnB,kBAAoB,CAOpB,cAAe,CAFf,eAAgB,CANhB,2CAA8C,CAH9C,mBAAoB,CAQpB,sBAAuB,CAvBvB,KAAQ,CA4BR,+JAQD,ChErDD,0FgEoBG,MAAS,CAGT,6DAA+D,CAF/D,oBhEnBH,CAFA,0FgE2BG,OAAU,CAEV,sEAA0E,CAD1E,uBhE1BH,CgEgDE,uCAlCD,gFAmCE,eAEF,CADC,CASD,mKACC,gCACD,CAGD,yDACC,mCAAoC,CACpC,kCAAmC,CAInC,kBAKD,CAHC,6FACC,gCACD,CAID,4OAEC,yCACD,CAIA,uWAaC,sBAAuB,CAFvB,iEAAkE,CAGlE,SACD,ChEtGA,2XgE0FE,+HhExFF,CAFA,2XgE8FE,wIhE5FF,CgEyGA,8FACC,sBACD,CAGA,yIACC,SACD,CAGA,kMACC,8HACD,CAEA,2DACC,WACD,C;ACxHD,MACC,qBAAsB,CAGtB,2CACD,CAEA,aAGC,2CAA4C,CAC5C,6CAA8C,C5ET9C,eAAgB,C4EchB,4CAA6C,CAH7C,+BAAgC,CADhC,6DAA8D,CAO9D,4DAkCD,C5EjDC,iEAEC,qCAED,C4EaA,uCAdD,aAeE,eA+BF,CA9BC,CAEA,mB7DvBA,2BAA2B,CdF3B,2CAA8B,CcC9B,Y6D2BA,CAEA,uBAEC,oDAAqD,CADrD,sDAAuD,CAEvD,yCAMD,CAJC,6B3EnCD,oD2EsCC,CAGD,sBAEC,sCAAuC,CADvC,+CAUD,CAPC,uCAJD,sBAKE,cAMF,CALC,CAEA,4B3EjDD,iD2EmDC,CAIF,0BACC,IACC,0BACD,CAEA,IACC,yBACD,CAEA,IACC,0BACD,CAEA,IACC,yBACD,CACD,C;AC1EC,8CACC,yBACD,C;ACHD,wBAEC,yDAA0D,CAD1D,WAOD,CAJC,yDACC,YAAa,CACb,mCACD,CCLD,MACC,2CACD,CAEA,wBACC,qBAgBD,CAdC,yDACC,uCAAwC,CACxC,kCAWD,CATC,oJAEC,sBAAuB,CACvB,cACD,CAEA,sFACC,0BACD,CAKD,uCACC,aAAc,CACd,UASD,CpEpCA,iDoE8BE,epE5BF,CAFA,iDoEkCE,gBpEhCF,CoEoCA,4CASC,oDACD,CATC,8DACC,gDACD,CAEA,6DACC,mDACD,CAMD,iDACC,oDAAqD,CACrD,+BACD,CAEA,+BACC,YACD,C;AC1DA,sEACC,YACD,CCHD,MACC,yDACD,CAGC,iCAGC,eAAgB,CAChB,aAAc,CAFd,eAAiB,CADjB,UAmBD,CAdC,uCACC,sBACD,CAEA,wIACC,sBAAuB,CACvB,wBAAyB,CACzB,eACD,CAEA,0CACC,qCAAsC,CACtC,sCACD,CAGD,6CACC,gFACD,CAGC,mEACC,wBACD,C;ACjCD,mBAEC,UAAW,CADX,aAAc,CAOd,gBAAkB,CAGlB,cAAe,CARf,iBA2BD,CAjBC,uBAEC,aAAc,CAad,WAAY,CAVZ,aAAc,CAGd,cAAe,CAGf,cAKD,CAGD,0BAYC,sBAAuB,CANvB,mBAAoB,CAGpB,cAoBD,CAdC,kCACC,YACD,CAGA,gEAGC,WAAY,CACZ,aAAc,CAGd,cACD,CAUD,gEASC,eAAgB,CARhB,oBAAqB,CACrB,qBAAsB,CAQtB,sBAAuB,CAFvB,kBAGD,CAKA,+BACC,SASD,CAHC,kDACC,SACD,CAMD,sCACC,SAkBD,CAZC,yDACC,SAUD,CAHC,qEACC,YACD,CAMF,0CACC,WACD,CAMC,0FACC,cACD,C;ACrIF,MACC,2CAAoD,CACpD,kCAA8C,CAC9C,oDACD,CAGA,8BAKC,yDAA0D,CAH1D,mBAAoB,CAEpB,wCAAyC,CAHzC,qBAAsB,CAMtB,eAAgB,CAChB,mBAAoB,CAFpB,YAAa,CAHb,qBAYD,CAJC,8BAXD,8BAYE,sBAAuB,CACvB,WAEF,CADC,CrGdA,4BACC,qEqGmBA,iDrGjBA,CACD,CqGmBA,uCALD,qEAME,cAEF,CADC,CAGD,sCACC,GACC,qEACD,CAEA,GACC,yDACD,CACD,C;AC7CA,MACC,8BAA+B,CAC/B,qEACD,CAQE,iGAEC,oDACD,CAIA,qFAEC,UACD,CAEA,oCACC,WAAY,CACZ,yCAA0C,CAC1C,aACD,CAEA,0CACC,UAAW,CACX,0CACD,CAEA,2CACC,WAAY,CACZ,yCACD,CAEA,iDAEC,gBAAiB,CADjB,cAED,CAEA,gDACC,aAAc,CACd,iBACD,CAGD,sCACC,gBAAiB,CACjB,iBACD,CAEA,oCACC,UAAW,CACX,0CACD,CAEA,qCACC,WAAY,CACZ,yCACD,CAGA,+HAGC,YACD,CAGC,mGAGC,kDAAmD,CADnD,+CAED,CAEA,iDACC,iDACD,CAEA,kDACC,gDACD,CAUC,0lBAGC,qDAKD,CAHC,8nBACC,YACD,CAKD,oVAGC,2DACD,C;AC9GH,sBACC,mDAMD,CAHC,wCACC,yFACD,CAOD,4BACC,8CACD,CAGA,sCAEC,gDAAiD,CADjD,WAAY,CAEZ,iBAAkB,CAClB,oCACD,C;ACpBC,+CACC,YAAa,CACb,qBAOD,CAJE,kFACC,WACD,CAIF,uCACC,YACD,CCdD,MACC,2BAA4B,CAE5B,kGAAqG,CAErG,2IACD,ClBRC,oCkBWA,MACC,2BACD,ClBXA,CkBeD,yBAEC,gBAAiB,CADjB,gCAiCD,ClBnDC,oCkBsBC,mEAEE,QAEF,ClBxBD,CkB2BA,uDACC,gDAoBD,CAjBC,0FAKC,2FAA8F,CAJ9F,aAKD,CAEA,wEAEC,eAAgB,CADhB,uDAOD,CAJC,0FACC,eAAgB,CAChB,sBACD,C;AChDF,+CACC,iBAAkB,CAClB,eAAgB,CAChB,2BAOD,CAJE,mEACC,aACD,CCPH,MACC,+BAAgC,CAChC,oCAAqC,CACrC,2DACD,CpBLC,oCoBQA,MACC,+BACD,CpBRA,CoBWD,sBACC,oCA6BD,CA3BC,8CACC,eAAgB,CAChB,sBACD,CAEA,+CACC,wDAcD,CAXE,mEAEC,yCAA0C,CAD1C,wCAED,CAEA,4EACC,eAAgB,CAChB,sBAAuB,CACvB,kBACD,CAIF,gDAGC,iBAAkB,CAFlB,gEAAqE,CACrE,iBAED,C;ACzCD,MACC,gCACD,CrBHC,oCqBMA,MACC,gCACD,CrBNA,CqBSD,uBACC,qCACD,C;ACdA,6BACC,oBAKD,CAHC,+CACC,eACD,CCHD,MACC,0DACD,CAGC,kDACC,gBACD,CAGD,wCAEC,kCAAmC,CACnC,cAAe,CACf,sBAAuB,CAHvB,kCA+CD,CA1CC,6DAKC,+BAAgC,CAJhC,sBAKD,CAEA,mLAIC,eACD,CAEA,+CACC,eACD,CAEA,4FAEC,yBACD,CAEA,wFAEC,wCAAyC,CADzC,uCAYD,CjFtDA,kGiF+CE,mCAAoC,CADpC,mCjF5CF,CAFA,kGiFmDE,kCAAmC,CACnC,oCjFlDF,CiFsDA,sDACC,0CACD,C;AC3DD,8CACC,aACD,C;ACFA,eACC,uBAiBD,CAfC,kBACC,2BAaD,CAXC,qBACC,2BASD,CAPC,wBACC,2BAKD,CAHC,2BACC,2BACD,CAMJ,eACC,oBAaD,CAXC,kBACC,sBASD,CAJE,6CACC,sBACD,C;AC7BH,MACC,yCACD,C1BHC,oC0BMA,MACC,yCACD,C1BNA,C0BUA,mDACC,8CACD,C;ACdD,8BACC,WACD,CAEA,iCAQC,qBAAsB,CADtB,aAAc,CANd,cAkBD,CATC,qCAEC,UACD,CAEA,4CAEC,aACD,CAQC,sHACC,cACD,CAIF,oFACC,uCACD,CAEA,oFACC,sCACD,CAEA,oEACC,SACD,C;AC/CA,kBACC,YACD,CCCA,MACC,8BAA+B,CAK/B,wCACD,CAEA,kBACC,YAAa,CACb,WACD,CAEA,wBACC,8BAkED,CpH3EC,4BACC,wBoHgBA,QAAS,CAJT,qCAAsC,CAEtC,yCAA0C,CAD1C,wCAAyC,CAEzC,SAAU,CAJV,oCpHTA,CoHgBA,8HAIC,QACD,CAEA,+DACC,gDACD,CAEA,8BACC,8FACD,CAEA,gGAEC,iGACD,CpHjCD,CAZA,8BACC,wBoHqDA,YAAa,CAEb,gBAAiB,CADjB,eAAgB,CAEhB,iCAAkC,CAJlC,WpHlDA,CoHwDA,0CACC,oBACD,CpHzDD,CoH4DA,uCAhDD,wBAiDE,eAkBF,CAjBC,CAEA,oCACC,YAAa,CACb,gBACD,CAEA,oCAEC,2CAA4C,CAD5C,YAED,CAGC,0CACC,aACD,CAIF,yBACC,oCACD,C;ACxFA,wBACC,iBAKD,CAHC,qCACC,eACD,CAGD,yBACC,YAAa,CACb,kBAAmB,CACnB,gBAAiB,CACjB,6BAA8B,CAC9B,kCAAmC,CACnC,WAcD,CAZC,mDACC,iBACD,CAEA,2CACC,WACD,CAEA,qDAEC,sCAAuC,CADvC,kCAED,C;ACtBC,oLAGC,kBAAmB,CADnB,YAMD,CzFZD,wMyFUG,0BzFRH,CyFeC,iFACC,YAAa,CACb,kBAAmB,CACnB,4BAMD,CAJC,oMAEC,MACD,CCrBD,oLAEC,UACD,CAEA,0FAEC,2BAA4B,CAC5B,4BAA6B,CAF7B,qEAiBD,CAbC,sGACC,gDACD,C1FhBF,gH0FoBI,uC1FlBJ,CAFA,gH0FwBI,sC1FtBJ,C0F2BC,6EACC,iBACD,CAKA,oEACC,WAoBD,CAlBC,sFACC,YAAa,CACb,eAeD,CAbC,wGACC,iEACD,CAEA,iGACC,iEACD,CAEA,yNAGC,WAAY,CADZ,UAED,CAIF,iFACC,iBACD,C;ACzDD,uBACC,cACD,CAEA,wBACC,eACD,CAEA,sBACC,eACD,CAEA,uBACC,eACD,C;AClBA,0CAGC,kBAAmB,CAFnB,YAAa,CACb,qBAcD,CAXC,sEAEC,cAAe,CAEf,iBAMD,CAJC,wGAEC,aAAc,CADd,eAED,CAWD,6kBACC,YACD,CAYF,2LACC,mBACD,CC1CA,MACC,0CAA2C,CAE3C,mDAA4D,CAC5D,2EACD,CAEA,mBACC,aA+FD,CA7FC,0CAEC,0CAA2C,CAD3C,0CA6BD,CA1BC,uEAIC,uBAA2B,CAC3B,qBAAsB,CAHtB,kDAAmD,CACnD,qCAAsC,CAFtC,qDAUD,CAJC,gFAEC,WAAY,CADZ,UAED,CAGD,4EACC,sDAAuD,CAGvD,iBAAkB,CADlB,iBAAkB,CAElB,sBAAuB,CAHvB,kBAUD,CALC,kFACC,4DAA6D,CAC7D,cAAe,CACf,yBACD,CAIF,wDAEC,gBAAiB,CADjB,eAED,CAEA,4UAIC,wDACD,CAEA,2EACC,kBAaD,CAXC,wGACC,wDACD,CAEA,6GACC,UAKD,CAHC,mHACC,UACD,CAIF,4EACC,2DAcD,CAZC,yGACC,wDACD,CAGA,8GACC,aAKD,CAHC,oHACC,UACD,CAIF,6EAEC,iDAaD,CAXC,0GACC,wDACD,CAEA,+GACC,aAKD,CAHC,qHACC,UACD,yiQ;AClGH,kBAEC,sBAAuB,CADvB,YAAa,CAEb,kBAAmB,CACnB,gBAAiB,CACjB,WA0BD,CAxBC,yCACC,oBAAqB,CACrB,UACD,CAEA,4BACC,YACD,CAEA,4BACC,UACD,CpCnBA,oCoCCD,kBAqBE,cAUF,CARE,yCACC,eACD,CAEA,6BACC,cACD,CpC5BD,C;AqCHD,mBAGC,UAAW,CASX,aAAc,CAJd,aAAe,CAQf,cACD,C;ACjBA,MACC,gEACD,CAWE,8QAGC,2DAA4D,CAC5D,8CAA+C,CAC/C,mBACD,C;ACnBF,oCACC,YAAa,CACb,kBAAmB,CACnB,cACD,CCJA,MACC,uCAAwC,CACxC,0CAA2C,CAC3C,yCAA0C,CAC1C,yCACD,CAEA,oCAGC,yFAA0F,CAD1F,oJAED,CAEA,mFAEC,iBACD,CAEA,uCAIC,4CAA6C,CAC7C,iBAAkB,CAFlB,iDAAkD,CADlD,qDAAsD,CADtD,mDAAoD,CAKpD,YAAa,CACb,eAcD,CAZC,uCATD,uCAUE,eAWF,CAVC,CAEA,6CACC,eACD,CAEA,6CAEC,6CAA8C,CAD9C,yCAED,C;ACtCD,MACC,wDACD,CAGC,0IAKC,gBAAiB,CAFjB,uBAAwB,CACxB,aAAc,CAFd,iBAiCD,CA3BC,sJAGC,yDAA0D,CAK1D,QAAS,CAPT,UAAW,CAKX,MAAO,CAJP,mBAAoB,CAEpB,iBAAkB,CAGlB,OAAQ,CAFR,KAID,CAEA,wTAEC,4BACD,CAMA,gKACC,aAKD,CAHC,0NACC,YACD,C;ACnCF,+BACC,gBACD,CAEA,+BACC,eACD,CAEA,4CACC,aAMD,CAJC,kDAEC,WAAY,CADZ,UAED,CAGD,uFAKC,gBACD,CAEA,6FAQC,yBAAkC,CALlC,wBAAyB,CACzB,gBA+BD,CAlBG,0OAEC,0BAA+B,CAD/B,eAED,CAEA,odASC,wBAAiC,CANjC,aAAc,CACd,YAMD,CAiBJ,aACC,+BACC,WACD,CACD,CAEA,+CAKC,oBAAqB,CAMrB,UACD,C;AC/FA,mBAEC,YAAa,CACb,0BAA2B,CAF3B,UAgCD,CA5BC,0CAEC,WAAY,CADZ,cAED,CAEA,sCACC,cAMD,CAHC,kFACC,YACD,CAGD,8CAEC,YAWD,CATC,kFAEC,eAAgB,CADhB,iBAOD,CAJC,0IAEC,aAAc,CADd,iBAED,CrG7BF,+CsGME,4BAA6B,CAD7B,yBtGHF,CAFA,+CsGWE,2BAA4B,CAD5B,wBtGRF,CsGcC,2CACC,SACD,CAIA,wEACC,SA0CD,CtGjED,kFsG2BG,2BAA4B,CAD5B,wBtGxBH,CsG2BG,8FACC,iCACD,CtG/BH,kFsGoCG,4BAA6B,CAD7B,yBtGjCH,CsGoCG,8FACC,kCACD,CAGD,oFACC,oDACD,CAEA,4GAKC,6CAA8C,CjH/CjD,eAAgB,CiH8Cb,WAAY,CADZ,UAcD,CjHzDF,+PAEC,qCAED,CiH2CG,oKAKC,cAA6B,CAC7B,iBAAkB,CAHlB,WAAY,CADZ,QAAS,CADT,QAAS,CAMT,uBAAwB,CACxB,oBAAqB,CAJrB,QAKD,CAKH,oDAIC,2BAA4B,CAC5B,4BAA6B,CAH7B,qEAAwE,CADxE,UA0BD,CApBC,gEACC,oDACD,CtG7ED,8DsGgFE,yBtG9EF,CAFA,8DsGoFE,wBtGlFF,CsGqFC,gEACC,uCAMD,CtG9FD,0EsG4FG,sCAAuC,CADvC,ctGzFH,C;AuGDC,wHAEC,sBACD,CCAA,mCACC,2CACD,CAGD,2BAEC,cAAe,CADf,UAED,CAEA,2CACC,kCAKD,CAHC,wEACC,0BACD,C;ACdA,wHACC,cACD,CAEA,8DAEC,kBAAmB,CADnB,cAgBD,CAbC,qFAGC,kBAAmB,CAFnB,YAAa,CACb,6BAMD,CAEA,sMACC,WACD,CAKD,mFACC,WACD,CAGD,4CAEC,iBAoBD,CAlBC,8EAGC,2DAAgE,CADhE,QAAS,CADT,iBAAkB,CAGlB,8BAA+B,CAG/B,SAUD,CAPC,oFACC,UAAW,CAGX,QAAS,CAFT,iBAAkB,CAClB,wDAA6D,CAE7D,0BACD,CCtDH,MACC,0CAA2C,CAC3C,2CACD,CAMI,2FACC,kCAAmC,CACnC,iBACD,CAGD,8KAIC,cAAe,CADf,cAAe,CADf,UAGD,CAGD,8DACC,SAcD,CAZC,yMAEC,QACD,CAEA,iGACC,mBAAoB,CACpB,oBAAqB,CACrB,wCAAyC,CACzC,6CAA8C,CAC9C,gCACD,CAIF,4CACC,sCA6BD,CA3BC,8EAgBC,iEAAkE,CAblE,qCAAsC,CrH3CxC,eAAgB,CqH4Cd,qCAAsC,CAEtC,oDAAqD,CADrD,wDAAyD,CAEzD,iBAcD,CrH3DD,mMAEC,qCAED,CqH4CE,oFACC,2EAA4E,CAE5E,kBAAmB,CADnB,kJAED,CAIA,uCAlBD,8EAmBE,cAEF,CADC,CAID,6GACC,YACD,CAIF,oDACC,GACC,SACD,CAEA,GACC,SACD,CACD,C;AClFE,mFAGC,sBAAuB,CADvB,YAAa,CADb,cAOD,CAHC,qHACC,gBACD,CCTH,6BACC,WAmBD,CAhBE,mFACC,mBAAoB,CACpB,SAYD,CAVC,kGACC,eAAgB,CAGhB,qCAKD,CAHC,uHACC,UACD,C;ACdF,6FACC,cAiBD,CAdE,0HAEC,cACD,CAEA,yHAEC,cACD,CAEA,uHACC,WACD,CClBJ,kCACC,WAkBD,CAfE,2FACC,mBAAoB,CACpB,SAAU,CACV,SACD,CAGC,4GACC,eAAgB,CAGhB,qCACD,C;AChBH,MACC,oCAA+C,CAC/C,mCAA+C,CAC/C,kCAA8C,CAC9C,kCAA8C,CAC9C,8BAAwC,CACxC,gCACD,CAGC,2BACC,kDACD,CAFA,0BACC,iDACD,CAFA,yBACC,gDACD,CAFA,yBACC,gDACD,CAIA,qBAIC,4BAA6B,CAH7B,iCAID,CALA,uBAIC,4BAA6B,CAH7B,mCAID,C;ACrBD,MACC,kCACD,CA4EA,uBACC,eAwBD,CAtBC,0BAEC,iBAAkB,CADlB,iBAMD,CAHC,qCACC,cACD,CAIA,+CAtFD,uBAAwB,CAQxB,QAAS,CAPT,oBAAqB,CAGrB,yCAA0C,CAO1C,UAAW,CAGX,aAAc,CAFd,kBAAmB,CAVnB,iBAAkB,CAWlB,OAAQ,CARR,qBAAsB,CAFtB,wCAqFC,CAvED,wDACC,MAAO,CAGP,iBAAkB,CAFlB,cAAe,CACf,WAED,CAEA,sDAOC,qBAAiC,CACjC,iBAAkB,CALlB,qBAAsB,CACtB,UAAW,CAHX,aAAc,CAKd,WAAY,CAJZ,iBAAkB,CAOlB,sCAAwC,CAJxC,UASD,CAHC,uCAXD,sDAYE,eAEF,CADC,CAGD,qDAaC,wBAAyB,CADzB,kBAAmB,CAEnB,sGAA+G,CAX/G,sBAAuB,CAEvB,UAAW,CAJX,aAAc,CAUd,mDAAwD,CAHxD,+CAAoD,CAJpD,mBAAoB,CAFpB,iBAAkB,CAOlB,gDAAqD,CAMrD,uBAAwB,CALxB,kDAMD,CAGC,+DACC,kBAA8B,CAC9B,oBACD,CAEA,8DACC,iBACD,CAwBA,wEACC,qBACD,CAEA,mGACC,iBACD,CAYD,kKAEC,cAKD,CAHC,4LACC,mCACD,CAMD,+FAxHA,uBAAwB,CAQxB,QAAS,CAPT,oBAAqB,CAGrB,yCAA0C,CAO1C,UAAW,CAGX,aAAc,CAFd,kBAAmB,CAVnB,iBAAkB,CAWlB,OAAQ,CARR,qBAAsB,CAFtB,wCAuHA,CAzGA,wGACC,MAAO,CAGP,iBAAkB,CAFlB,cAAe,CACf,WAED,CAEA,sGAOC,qBAAiC,CACjC,iBAAkB,CALlB,qBAAsB,CACtB,UAAW,CAHX,aAAc,CAKd,WAAY,CAJZ,iBAAkB,CAOlB,sCAAwC,CAJxC,UASD,CAHC,uCAXD,sGAYE,eAEF,CADC,CAGD,qGAaC,wBAAyB,CADzB,kBAAmB,CAEnB,sGAA+G,CAX/G,sBAAuB,CAEvB,UAAW,CAJX,aAAc,CAUd,mDAAwD,CAHxD,+CAAoD,CAJpD,mBAAoB,CAFpB,iBAAkB,CAOlB,gDAAqD,CAMrD,uBAAwB,CALxB,kDAMD,CAGC,+GACC,kBAA8B,CAC9B,oBACD,CAEA,8GACC,iBACD,CA2DA,uHACC,iBACD,C;ACpIF,gBAGC,4BAAiC,CACjC,wBAAiC,CACjC,iBAAkB,CAHlB,aAAwB,CAOxB,aAAc,CAMd,iBAAkB,CAGlB,eAAgB,CAjBhB,WAAY,CAUZ,UAAW,CAHX,eAAgB,CAIhB,oBAaD,CALC,qBACC,gBAAiB,CAEjB,eAAgB,CADhB,SAED,CAGD,4BACC,iBAMD,CAJC,iDACC,2BAA4B,CAC5B,iBACD,CCjCD,MACC,8CACD,CAEA,iDAGC,sDAAuD,CAMvD,UAAuB,CAHvB,+BAAgC,CADhC,cAAe,CAEf,gBAAiB,CACjB,uDAAwD,CANxD,UAAW,CADX,QAAS,CAST,kBACD,CAEA,+CAEC,gBAAiB,CAEjB,iBAAkB,CADlB,eAED,C;ACpBC,yDACC,+BASD,CAPC,2DACC,cAKD,CAHC,6DACC,qCACD,CASD,wFACC,oCACD,CAGA,mFACC,gDAWD,CARE,+GACC,UAKD,CAHC,iHACC,qCACD,CAMJ,8EACC,cAAe,CACf,UACD,CAEA,uEACC,sBAAuB,CAGvB,6CAAgD,CAFhD,cAAe,CACf,eAQD,CALC,2JAGC,eAAgB,CADhB,wBAAyB,CADzB,eAGD,C;ACtDF,wBACC,YACD,CCFA,MACC,gCACD,CAEA,wBAGC,mCAAoC,CAFpC,oCAAwC,CAGxC,+BAAgC,CAFhC,gCA4BD,CAxBC,mCAiBC,sBAAuB,CAPvB,QAAS,CANT,SAmBD,CAJC,+EAhBA,uCAAwC,CADxC,sCAoBA,C;AC5BF,wBAEC,eAAgB,CADhB,iBAED,CAEA,+DAIC,4BAA6B,CAG7B,qBAAsB,CADtB,oCAAqC,CADrC,sCAAuC,CAFvC,QAAS,CADT,+BAAgC,CAMhC,oBACD,CAEA,8BACC,4BAA6B,CAE7B,aAAc,CADd,iBAED,CAEA,iCASC,wCAAyC,CjI7BzC,eAAgB,CiI2BhB,qBAAsB,CAJtB,WAAY,CAEZ,YAAa,CACb,eAAgB,CALhB,iBAAkB,CAGlB,WAAY,CAFZ,UAkBD,CjItCC,yGAEC,qCAAsC,CiI4BtC,wBAAyB,CACzB,yBjI3BD,CiI8BA,uDlHpCA,2BAA2B,CdF3B,qCAA8B,CcC9B,YkHwCA,C;AC7CD,MACC,kDACD,CAEA,6BAEC,gDAAiD,CADjD,kCAAmC,CAKnC,iDAAkD,CAHlD,+BAAgC,CAEhC,oEAgCD,CA7BC,kEACC,wGACD,CAEA,oCAOC,eAA4B,CAG5B,iEAAkE,CAClE,qCAAsC,CAPtC,0CAA2C,CAS3C,+BAAgC,CADhC,kCAAmC,CAVnC,iBAAkB,CADlB,eAAmB,CAKnB,+BAAgC,CAGhC,yIAA0I,CAN1I,iBAAkB,CAElB,KAAM,CAGN,0GAMD,CAGA,2HACC,gBACD,CAEA,yDAEC,mBACD,CAGD,gCACC,eACD,CAEA,iCACC,oBACD,C;AChDC,qEAEC,iBACD,CAGA,uGAIC,MAAO,CAFP,iBAAkB,CAClB,KAED,CCRC,yFACC,oBAMD,CAJC,uCAHD,yFAKE,cAAe,CADf,SAGF,CADC,CAKF,uGAIC,gDAAiD,CAFjD,UAAW,CAGX,oBAAuB,CAFvB,OAGD,CAGD,kBACC,GAAO,SAAY,CACnB,GAAO,SAAY,CACpB,C;AC3BA,+BAUC,iBAAkB,CATlB,aAAc,CACd,iBAAkB,CAOlB,sCAAwC,CADxC,oCAAsC,CAGtC,SAMD,CAJC,qCACC,UAAW,CACX,iBACD,CChBD,MACC,iCAA8C,CAC9C,+CAA4D,CAG5D,8BAA+B,CAC/B,gCAAiC,CACjC,4DACD,CAEA,+BAWC,sBAA4B,CAN5B,0BAAgC,CADhC,qCAAuC,CADvC,wEAA0E,CAD1E,uDAAwD,CAMxD,oDAAuD,CAWvD,oFAAuF,CAlBvF,SAAU,CAgBV,eAAgB,CAChB,mFAqCD,CAjCC,qCAgBC,mBAAsB,CADtB,sBAAyB,CAEzB,4BAA6B,CAH7B,4CAA6C,CAF7C,sFAAuF,CADvF,oFAAqF,CASrF,qBAAsB,CAdtB,QAAS,CAJT,QAAS,CAGT,SAAU,CADV,OAAQ,CAKR,mCAAoC,CACpC,yBAA0B,CAH1B,OAcD,CAEA,uCA7CD,+BA8CE,sBASF,CAPE,qCACC,cAAe,CAGf,YAAc,CAFd,SAAU,CACV,UAED,CACD,CAGD,wCACC,GACC,SACD,CAEA,GACC,SACD,CACD,CAEA,wCACC,GACC,SACD,CAEA,GACC,SACD,CACD,CAEA,yCACC,GAGC,QAAS,CAFT,SAAU,CACV,OAED,CACA,IAEC,QAAS,CADT,UAED,CACA,GAGC,YAAc,CAFd,SAAU,CACV,UAED,CACD,C;ACtGA,kCAGC,kBAAmB,CADnB,YAAa,CAEb,sBAAuB,CAEvB,MAAO,CALP,iBAAkB,CAIlB,KAOD,CAJC,yCACC,UAAW,CACX,iBACD,CCXD,MACC,4CAAqD,CACrD,wCAAyC,CACzC,8CACD,CAEA,iCAGC,QAAS,CADT,UAgBD,CAbC,8CACC,sGACD,CAEA,qCAOC,4DACD,CAGD,kCAEC,WAAY,CADZ,UAWD,CARC,yCAMC,yDAA0D,CAH1D,iBAAkB,CAElB,kCAAmC,CADnC,8DAA+D,CAF/D,+CAAgD,CADhD,8CAMD,CAGD,wCACC,GACC,uBACD,CACD,C;AC5CA,MACC,oEAAqE,CACrE,mCAAoC,CAIpC,iGACD,CAEA,qCACC,kBACD,CAEA,4CAIC,wBACD,CAEA,oEAGC,iBACD,CAEA,wDAGC,QAAS,CAGT,iBAAkB,CALlB,iBAAkB,CAGlB,oDAAqD,CAFrD,KAAM,CAKN,gBAAiB,CAFjB,0CAA2C,CAG3C,2BACD,CAQA,qJACC,YACD,CAEA,8HAEC,8DAA+D,CAC/D,WACD,CAEA,iEACC,mDAAoD,CACpD,WACD,C;ACrDG,oHACC,kCACD,CAKA,gRAGC,qCACD,CAIF,yCACC,aAAc,CACd,QAiCD,CA7BE,yDAGC,2BACD,CAEA,sFACC,WAAa,CACb,kBAKD,CAHC,4FACC,SACD,CAMD,4GACC,SAAU,CACV,iBACD,CAEA,sEAGC,2BACD,CAMF,2EAIC,eAAgB,CADhB,YAED,CAEA,iFAEC,gBACD,CCnED,MACC,qDAAsD,CACtD,oDAAqD,CACrD,oDAAqD,CACrD,+CAAgD,CAChD,8CAED,CAIE,+CAKC,sBAAuB,CAHvB,WAAY,CADZ,UAoBD,CAbC,+NASC,wBAAyB,CACzB,cAAe,CACf,YACD,CAGD,2DACC,iBAAkB,CAElB,aAAc,CADd,cAAe,CAGf,eA0CD,CAvCC,mTAIC,cACD,CAGA,iEACC,4BACD,CAIA,2OASC,wBAAyB,CAEzB,cAAe,CACf,8DAA+D,CAC/D,mBAKD,CAHC,iPACC,8CACD,CAID,qFAEC,aAAc,CADd,sBAED,CAKA,2EACC,yFAiCD,CA9BE,iSAEC,uBAAwB,CAExB,SACD,CAEA,iJAGC,6BAA8B,CAD9B,kBAAoB,CADpB,iEAOD,CAHC,uJACC,6BACD,CAMA,sSACC,6BACD,CAGD,+GAEC,4DAA6D,CAD7D,0DAED,CAKD,gHACC,6EAA8E,CAE9E,oEAAqE,CAGrE,SACD,CAMC,4JACC,KACD,CAOA,2JACC,QACD,C;AChJL,MACC,8CAAuD,CACvD,qCAAiD,CACjD,uDACD,CAGA,8BAMC,4DAA6D,CAJ7D,gBAAiB,CAGjB,2CAA4C,CAJ5C,qBAAsB,CAOtB,eAAgB,CAChB,mBAAoB,CAFpB,YAAa,CAHb,iBAAkB,CADlB,qBAaD,C9JxBC,8BACC,8B8JoBA,sBAAuB,CACvB,W9JnBA,CACD,CAIA,4B8JqBC,qEACC,iDACD,C9JnBD,C8JsBA,gEASC,eAAgB,CARhB,oBAAqB,CACrB,qBAAsB,CAQtB,sBAAuB,CAFvB,kBAGD,CAGD,sCACC,GACC,wEACD,CAEA,GACC,4DACD,CACD,C;ACzDA,MACC,kCACD,CAEA,gBACC,4CAA6C,CAM7C,iBAAkB,CAJlB,eAAgB,CAMhB,2BAQD,CAJC,+BAEC,aAAc,CADd,eAED,CAGD,2CACC,sCACD,C;ACzBA,MACC,+CAAwD,CACxD,+BACD,CAEA,qBACC,6CAA8C,CAC9C,kCACD,C;ACRA,MACC,0BACD,CAEA,kCACC,YAAa,CACb,gEAAiE,CACjE,qBAgBD,CAdC,yDACC,YAAa,CAEb,qBAAsB,CADtB,6BAWD,CARC,yFAEC,oBAAqB,CAErB,kBAAmB,CAHnB,YAAa,CAKb,eAAgB,CADhB,WAAY,CAFZ,0BAID,CCrBF,MACC,mCAAoC,CACpC,mCAAoC,CACpC,gDAA2D,CAC3D,sDAAiE,CACjE,kDACD,CAEA,kCAEC,kCAAmC,CADnC,+BAmFD,CAhFC,yDACC,0EAA2E,CAC3E,2EAA4E,CAI5E,0CAA2C,CAF3C,SAAU,CACV,wCA0ED,CAtEC,qEACC,4CACD,CAEA,2EAOC,aAAc,CANd,WAAY,CACZ,gBAAiB,CAGjB,eAAgB,CADhB,kCAAmC,CAEnC,sBAAuB,CAHvB,UAKD,CAEA,yFAMC,0CAA2C,CAC3C,gDAAiD,CAJjD,UAAW,CADX,eAAgB,CAGhB,gCAAiC,CAJjC,UAOD,CAEA,qEACC,6EAaD,CAVC,iFACC,0DACD,CAEA,qGAGC,4CAA6C,CAC7C,mBAAoB,CAHpB,UAID,CAGD,+DACC,wCAUD,CARC,iFACC,+CAAgD,CAChD,SACD,CAEA,qEACC,8CACD,CAIA,uFACC,wDACD,CAEA,6FACC,8DACD,CAGD,6FACC,4DAKD,CAHC,6HACC,SACD,C;ACxFF,0DACC,gCACD,CAGC,sEACC,YACD,C;ACRF,MACC,iCACD,CAEA,mBAGC,2CAA4C,CAD5C,eAAgB,CADhB,+BAGD,C;ACRA,iGACC,iBACD,C;ACDA,yCAEC,iBACD,CAEA,eAGC,kBAA2B,CAC3B,QAAS,CAFT,UAAW,CADX,aAID,C;ACPE,+FACC,aAAc,CACd,iBACD,CCPF,MAEC,sCAAuC,CACvC,oEACD,CAME,+FAUC,+BAAqC,CACrC,wDAA+3B,CAG/3B,uBAA2B,CAD3B,2BAA4B,CAD5B,oBAAqB,CAGrB,kBAAmB,CAdnB,UAAW,CAsBX,oGAAuG,CAFvG,eAAgB,CAbhB,sCAAwC,CADxC,oCAAsC,CAetC,mGAED,yyB;ACjCF,0BAMC,iBAAkB,CAOlB,iBAAkB,CATlB,gBAAkB,CAMlB,cAAe,CALf,iBAwDD,CA5CC,iCACC,iBAAkB,CAGlB,SACD,CAKA,2DAEC,YAAa,CACb,qBAAsB,CAFtB,iBAGD,CAEA,mDAGC,YAAa,CADb,eAAgB,CADhB,iBAGD,CAEA,2DAOC,wBAAyB,CACzB,kBAAmB,CAFnB,aAAc,CAHd,WAAY,CADZ,iBAAkB,CADlB,UAQD,CAEA,+DAQC,kBAAmB,CAHnB,QAAS,CAET,YAAa,CAEb,sBAAuB,CAPvB,MAAO,CADP,iBAAkB,CAGlB,OAAQ,CADR,KAOD,CC7DD,MACC,kEAAqE,CACrE,kCAAmC,CACnC,2CAA4C,CAC5C,yFAA0F,CAE1F,0EAA2E,CAC3E,6CACD,CAGA,0BAEC,gDAAiD,CADjD,kCA0ID,CAvIC,+DACC,iGACD,CAGA,mCACC,eACD,CAEA,mCACC,gBACD,CAIA,iCAIC,eAA4B,CAG5B,iEAAkE,CAClE,qCAAsC,CAPtC,mCAAoC,CASpC,+BAAgC,CADhC,kCAAmC,CANnC,+BAAgC,CAGhC,kIAAmI,CAJnI,yDAA4D,CAG5D,0GAMD,CAEA,0CACC,SAAU,CACV,gCACD,CAGA,iIACC,gBACD,CAEA,uFAEC,sDAAuD,CADvD,KAED,CAEA,6FAGC,uCAAwC,CADxC,sDAAuD,CADvD,KAGD,CAEA,wFAEC,sDAAuD,CADvD,KAED,CAIA,2DACC,kCACD,CAGA,2DAEC,gCAAiC,CADjC,8BAcD,CAXC,kGACC,iCACD,CAEA,oGACC,mCACD,CAEA,wFACC,kCACD,CAGD,oEACC,+BAAgC,CAChC,UACD,CAGA,kDACC,qBAAsB,CActB,aAAc,CAPd,qBAAsB,CAGtB,kCAAmC,CATnC,yCAA0C,CAG1C,WAAY,CACZ,kCAAmC,CAFnC,WAAY,CAKZ,UAAW,CAKX,eAAgB,CAJhB,oBAAqB,CAPrB,wCAsBD,CARC,4DACC,0DAA2D,CAC3D,gDAAiD,CAGjD,kEAAmE,CACnE,SACD,CAID,mDACC,kDAAmD,CACnD,wCAMD,CAHC,2FACC,mBACD,CAGD,2DAEC,gDAAiD,CADjD,qBAOD,CAJC,6DACC,gBAAiB,CACjB,iBACD,CAGD,+DACC,gDACD,C;ACrJD,sBACC,cAKD,CAHC,gDACC,YACD,CCFD,MACC,kCACD,CAEA,sBAEC,iBAAkB,CADlB,eAqCD,CAlCC,gDACC,sFAAyF,CACzF,yDAA0D,CAC1D,mCACD,CAEA,+CAQC,QAAS,CAHT,eAAgB,CAHhB,yCAA0C,CAE1C,6CAA8C,CAD9C,4CAA6C,CAG7C,SAAU,CACV,8BAA+B,CAN/B,wCA0BD,CAjBC,uCAVD,+CAWE,eAgBF,CAfC,CAEA,8IAGC,QAAS,CACT,iGACD,CAGA,iEACC,8CAA+C,CAE/C,iBAAkB,CADlB,UAED,C;AC3CF,sBCIC,gDAAiD,CDHjD,YAAa,CACb,6BAA8B,CCC9B,uDDAD,CCGC,wBAEC,mCAAoC,CADpC,wBAED,CAEA,+CACC,eAAgB,CAEhB,eAAgB,CADhB,sBAED,CAEA,+CACC,UACD,C;ACZA,kDAKC,YAAa,CAFb,WAAY,CADZ,cAAe,CADf,WAAY,CAKZ,iBAAoB,CACpB,cAAiB,CACjB,yBAA0B,CAC1B,gCAkED,CAhEC,2EACC,iBAAwB,CACxB,wDAoDD,CAlDC,kGACC,sCAAuC,CACvC,UAgCD,CA9BC,oIAcC,iEAAkE,CAblE,qCAAsC,CACtC,qCAAsC,CAEtC,oDAAqD,CADrD,wDAAyD,CAEzD,iBAcD,CAXC,0IACC,2EAA4E,CAE5E,kBAAmB,CADnB,kJAED,CAIA,uCAhBD,oIAiBE,cAEF,CADC,CAID,mKACC,YACD,CAEA,4GACC,kCAAmC,CACnC,iBACD,CAGD,wFACC,aAAc,CACd,UAWD,CARE,2GACC,4CACD,CAEA,oGACC,UACD,CAKH,qEACC,iBAAwB,CACxB,gBACD,CAEA,qEACC,iBACD,CC5ED,+CACC,eAAgB,CAEhB,eAAgB,CADhB,sBAED,CAEA,sEAEC,gBAAiB,CAEjB,iBAAkB,CADlB,eAED,CzFfA,oCyFED,qCAgBE,eAOF,CALE,6DAEC,eAAgB,CADhB,sBAED,CzFrBD,C;A0FDD,MACC,qCACD,CAsCC,0FAzBA,2BAA4B,CAC5B,gBA0BA,CAfA,yIACC,qDACD,CpJ9BA,oGoJMA,wDAAsf,CACtf,2BpJLA,CAFA,oGoJWA,wiBAAyiB,CACziB,wCpJVA,CoJ2CA,wFA7BA,2BAA4B,CAC5B,gBA8BA,CAnBA,uIACC,qDACD,CpJ9BA,kGoJMA,wDAAsf,CACtf,2BpJLA,CAFA,kGoJWA,siBAAyiB,CACziB,wCpJVA,CoJ+CA,6FAjCA,2BAA4B,CAC5B,gBAkCA,CAvBA,4IACC,qDACD,CpJ9BA,uGoJMA,wDAAsf,CACtf,2BpJLA,CAFA,uGoJWA,2iBAAyiB,CACziB,wCpJVA,CoJmDA,0FArCA,2BAA4B,CAC5B,gBAsCA,CA3BA,yIACC,qDACD,CpJ9BA,oGoJMA,wDAAsf,CACtf,2BpJLA,CAFA,oGoJWA,wiBAAyiB,CACziB,wCpJVA,CoJuDA,mHAzCA,2BAA4B,CAC5B,gBA0CA,CA/BA,kKACC,qDACD,CpJ9BA,6HoJMA,wDAAsf,CACtf,2BpJLA,CAFA,6HoJWA,oiBAAyiB,CACziB,wCpJVA,CoJ2DA,yFA7CA,2BAA4B,CAC5B,gBA8CA,CAnCA,wIACC,qDACD,CpJ9BA,mGoJMA,wDAAsf,CACtf,2BpJLA,CAFA,mGoJWA,uiBAAyiB,CACziB,wCpJVA,CoJ+DA,qFAjDA,2BAA4B,CAC5B,gBAkDA,CAvCA,oIACC,qDACD,CpJ9BA,+FoJMA,wDAAsf,CACtf,2BpJLA,CAFA,+FoJWA,miBAAyiB,CACziB,wCpJVA,CoJmEA,qFArDA,2BAA4B,CAC5B,gBAsDA,CA3CA,oIACC,qDACD,CpJ9BA,+FoJMA,wDAAsf,CACtf,2BpJLA,CAFA,+FoJWA,miBAAyiB,CACziB,wCpJVA,CoJuEA,qFAzDA,2BAA4B,CAC5B,gBA0DA,CA/CA,oIACC,qDACD,CpJ9BA,+FoJMA,wDAAsf,CACtf,2BpJLA,CAFA,+FoJWA,miBAAyiB,CACziB,wCpJVA,CoJ2EA,qFA7DA,2BAA4B,CAC5B,gBA8DA,CAnDA,oIACC,qDACD,CpJ9BA,+FoJMA,wDAAsf,CACtf,2BpJLA,CAFA,+FoJWA,miBAAyiB,CACziB,wCpJVA,CoJ+EA,qFAjEA,2BAA4B,CAC5B,gBAkEA,CAvDA,oIACC,qDACD,CpJ9BA,+FoJMA,yDAAsf,CACtf,2BpJLA,CAFA,+FoJWA,miBAAyiB,CACziB,wCpJVA,CoJmFA,qFArEA,2BAA4B,CAC5B,gBAsEA,CA3DA,oIACC,qDACD,CpJ9BA,+FoJMA,yDAAsf,CACtf,2BpJLA,CAFA,+FoJWA,miBAAyiB,CACziB,wCpJVA,CoJuFA,yFAzEA,2BAA4B,CAC5B,gBA0EA,CA/DA,wIACC,qDACD,CpJ9BA,mGoJMA,yDAAsf,CACtf,2BpJLA,CAFA,mGoJWA,uiBAAyiB,CACziB,wCpJVA,CoJ2FA,uFA7EA,2BAA4B,CAC5B,gBA8EA,CAnEA,sIACC,qDACD,CpJ9BA,iGoJMA,yDAAsf,CACtf,2BpJLA,CAFA,iGoJWA,qiBAAyiB,CACziB,wCpJVA,CoJ+FA,sFAjFA,2BAA4B,CAC5B,gBAkFA,CAvEA,qIACC,qDACD,CpJ9BA,gGoJMA,yDAAsf,CACtf,2BpJLA,CAFA,gGoJWA,oiBAAyiB,CACziB,wCpJVA,CoJmGA,sFArFA,2BAA4B,CAC5B,gBAsFA,CA3EA,qIACC,qDACD,CpJ9BA,gGoJMA,yDAAsf,CACtf,2BpJLA,CAFA,gGoJWA,oiBAAyiB,CACziB,wCpJVA,CoJuGA,qFAzFA,2BAA4B,CAC5B,gBA0FA,CA/EA,oIACC,qDACD,CpJ9BA,+FoJMA,yDAAsf,CACtf,2BpJLA,CAFA,+FoJWA,miBAAyiB,CACziB,wCpJVA,CoJ2GA,qFA7FA,2BAA4B,CAC5B,gBA8FA,CAnFA,oIACC,qDACD,CpJ9BA,+FoJMA,yDAAsf,CACtf,2BpJLA,CAFA,+FoJWA,miBAAyiB,CACziB,wCpJVA,CoJ+GA,oFAjGA,2BAA4B,CAC5B,gBAkGA,CAvFA,mIACC,qDACD,CpJ9BA,8FoJMA,yDAAsf,CACtf,2BpJLA,CAFA,8FoJWA,kiBAAyiB,CACziB,wCpJVA,CoJmHA,0FArGA,2BAA4B,CAC5B,gBAsGA,CA3FA,yIACC,qDACD,CpJ9BA,oGoJMA,yDAAsf,CACtf,2BpJLA,CAFA,oGoJWA,wiBAAyiB,CACziB,wCpJVA,CoJuHA,+HAzGA,2BAA4B,CAC5B,gBA0GA,CA/FA,8KACC,qDACD,CpJ9BA,yIoJMA,yDAAsf,CACtf,2BpJLA,CAFA,yIoJWA,2iBAAyiB,CACziB,wCpJVA,goU;AqJHD,6BACC,cAUD,CARC,+HACC,YAMD,CAHC,yOACC,iBACD,CCNF,6BACC,WAoHD,CA9GC,mCACC,YACD,CAGA,+HAMC,qBAAsB,CADtB,kBAAmB,CAHnB,aAAc,CACd,kBAAmB,CACnB,cAAe,CAKf,QAAS,CADT,+BA4BD,CAzBC,qJACC,aACD,CAGC,2JACC,sCACD,CAIA,2JACC,uCACD,CAGD,6KACC,aAMD,CAJC,iMAEC,cAAe,CADf,UAED,CAMF,+DAEC,sBAqCD,CAnCC,wFACC,uBACD,CAEA,wFACC,wBACD,CAEA,mFAYC,iCAAkC,CAXlC,OAAQ,CACR,0BAWD,CtJ9ED,6FsJsEG,gCtJpEH,CAFA,6FsJ0EG,+BtJxEH,CsJ8EC,yFACC,aAAc,CACd,sCASD,CtJ3FD,mGsJqFG,atJnFH,CAFA,mGsJyFG,ctJvFH,CsJ6FA,gEACC,cAAe,CACf,wBAAyB,CACzB,2CAWD,CATC,gFACC,eAOD,CAJC,kGACC,oCAAqC,CACrC,qCACD,CAIF,8CAMC,kBAAmB,CAJnB,YAAa,CACb,kBAAmB,CACnB,gBAAiB,CACjB,6BAA8B,CAJ9B,UAMD,C5FtHA,oC4F0HA,6BAIC,cAAe,CAHf,WA+DD,CAzDC,6DACC,cA4BD,CA1BC,oFACC,aAAc,CAEd,wCAAyC,CADzC,UAED,CAEA,wEACC,iBAkBD,CAhBC,sFACC,aAcD,CtJ5JH,gGsJiJK,atJ/IL,CAFA,gGsJqJK,ctJnJL,CsJsJI,wGAEC,iBAAkB,CADlB,UAED,CAMH,4FAEC,aAAc,CADd,cAuBD,CApBC,uGACC,iBAkBD,CAhBC,qHACC,aASD,CtJlLH,+HsJ4KK,atJ1KL,CAFA,+HsJgLK,ctJ9KL,CsJkLG,yHAEC,iBAAkB,CADlB,UAED,C5FrLH,C;A6FHD,gBACC,+CAAgD,CAChD,0BACD,CAEA,yBACC,kBACD,C;ACVA,qBACI,SACJ,CAEA,gCACI,oBACJ,CAEA,0BACI,SACJ,CAEA,mCACI,gBACJ,CAEA,OAEI,kBAAmB,CADnB,aAEJ,CAEA,wBACI,kBACJ,CAEA,qBACI,iBAAkB,CAClB,oBACJ,CACA,eACI,YAAa,CACb,eACJ,CAEA,sBACI,iBACJ,CAEA,cACI,iBAAkB,CAClB,UACJ,CACA,qBACI,UACJ,C", "sources": ["webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-engine/theme/placeholder.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-ui/theme/mixins/_mediacolors.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-engine/placeholder.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-engine/theme/renderer.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-ui/theme/globals/_hidden.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-ui/theme/globals/_zindex.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-ui/theme/globals/_transition.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-ui/theme/globals/_poweredby.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-ui/theme/globals/_evaluationbadge.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-ui/globals/_colors.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-ui/globals/_disabled.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-ui/globals/_focus.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-ui/globals/_fonts.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-ui/globals/_reset.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-ui/globals/_rounded.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-ui/globals/_shadow.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-ui/globals/_spacing.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-ui/theme/components/panel/balloonpanel.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-ui/components/panel/balloonpanel.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/mixins/_rounded.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/mixins/_shadow.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-ui/theme/components/tooltip/tooltip.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-ui/theme/mixins/_unselectable.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-ui/components/tooltip/tooltip.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-ui/theme/components/icon/icon.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-ui/components/icon/icon.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-ui/theme/components/arialiveannouncer/arialiveannouncer.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-ui/components/menubar/menubarmenulistitem.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-ui/theme/components/formheader/formheader.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-ui/components/formheader/formheader.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-ui/theme/mixins/_dir.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-ui/theme/components/button/button.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-ui/components/button/button.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-ui/mixins/_button.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/mixins/_focus.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/mixins/_disabled.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-ui/theme/components/dialog/dialogactions.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-ui/components/dialog/dialogactions.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-ui/theme/components/dialog/dialog.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-ui/components/dialog/dialog.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-editor-classic/theme/classiceditor.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-editor-classic/classiceditor.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-ui/components/editorui/editorui.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-ui/theme/components/label/label.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-ui/components/label/label.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-ui/theme/components/panel/stickypanel.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-ui/components/panel/stickypanel.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-ui/theme/components/dropdown/dropdown.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-ui/components/dropdown/dropdown.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-ui/theme/components/list/list.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-ui/components/list/list.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-ui/theme/components/dropdown/splitbutton.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-ui/components/dropdown/splitbutton.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-ui/theme/components/button/switchbutton.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-ui/components/button/switchbutton.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-ui/theme/components/dropdown/toolbardropdown.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-ui/components/dropdown/toolbardropdown.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-ui/components/dropdown/listdropdown.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-ui/theme/components/button/listitembutton.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-ui/components/button/listitembutton.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-ui/theme/components/toolbar/toolbar.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-ui/components/toolbar/toolbar.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-ui/theme/components/menubar/menubarmenubutton.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-ui/components/menubar/menubarmenubutton.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-ui/theme/components/menubar/menubarmenupanel.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-ui/components/menubar/menubarmenupanel.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-ui/theme/components/menubar/menubarmenu.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-ui/components/menubar/menubarmenu.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-ui/components/menubar/menubarmenulistitembutton.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-ui/components/menubar/menubar.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-widget/theme/widgettypearound.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-widget/widgettypearound.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-widget/theme/widget.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-widget/widget.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-ui/theme/components/panel/balloonrotator.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-ui/components/panel/balloonrotator.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-ui/theme/components/panel/fakepanel.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-ui/components/panel/fakepanel.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-widget/theme/widgetresize.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-widget/widgetresize.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-clipboard/theme/clipboard.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-clipboard/clipboard.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-ui/components/editorui/accessibilityhelp.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-basic-styles/theme/code.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-block-quote/theme/blockquote.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-heading/theme/heading.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-heading/heading.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-image/textalternativeform.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-ui/theme/mixins/_rwd.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-ui/theme/components/responsive-form/responsiveform.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-ui/components/responsive-form/responsiveform.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-ui/theme/components/form/form.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-ui/theme/components/formrow/formrow.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-ui/theme/components/labeledfield/labeledfieldview.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-ui/components/labeledfield/labeledfieldview.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-ui/components/input/input.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-image/theme/imageplaceholder.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-image/theme/imageinsert.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-image/imageinsert.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-ui/theme/components/collapsible/collapsible.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-ui/components/collapsible/collapsible.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-image/theme/image.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-image/theme/imagecaption.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-image/theme/imagestyle.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-link/link.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-link/theme/linkform.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-link/linkform.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-link/theme/linkprovideritems.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-link/linkprovideritems.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-link/linkproperties.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-link/theme/linktoolbar.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-link/linktoolbar.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-list/theme/documentlist.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-list/theme/list.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-image/imagecustomresizeform.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-image/theme/imageresize.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-ui/theme/components/colorgrid/colorgrid.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-ui/components/colorgrid/colorgrid.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-ui/theme/components/colorpicker/colorpicker.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-ui/theme/components/colorselector/colorselector.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-ui/components/colorselector/colorselector.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-font/theme/fontsize.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-media-embed/theme/mediaembedediting.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-media-embed/mediaembedediting.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-media-embed/theme/mediaform.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-media-embed/theme/mediaembed.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-table/tableediting.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-table/theme/inserttable.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-table/inserttable.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-table/tableselection.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-table/theme/table.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-table/theme/colorinput.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-table/colorinput.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-table/theme/formrow.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-table/formrow.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-table/theme/tableform.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-table/tableform.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-table/theme/tableproperties.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-table/tableproperties.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-table/theme/tablecellproperties.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-table/tablecellproperties.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-highlight/theme/highlight.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-list/theme/todolist.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-code-block/theme/codeblock.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-code-block/codeblock.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-list/listproperties.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-list/theme/liststyles.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-list/liststyles.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-source-editing/theme/sourceediting.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-html-support/theme/datafilter.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-image/theme/imageuploadprogress.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-image/imageuploadprogress.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-image/theme/imageuploadicon.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-image/imageuploadicon.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-image/theme/imageuploadloader.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-image/imageuploadloader.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-table/theme/tablecolumnresize.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-table/theme/tablelayout.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-table/tablelayout.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-table/theme/tablecaption.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-mention/theme/mentionui.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-mention/mention.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-style/theme/stylegrid.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-style/stylegrid.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-style/stylegroup.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-style/stylepanel.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-style/style.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-horizontal-line/theme/horizontalline.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-link/theme/linkimage.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-link/linkimage.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-html-embed/theme/htmlembed.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-html-embed/htmlembed.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-special-characters/theme/charactergrid.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-special-characters/charactergrid.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-special-characters/theme/characterinfo.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-special-characters/characterinfo.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-special-characters/theme/specialcharacters.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-special-characters/specialcharacters.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-show-blocks/theme/showblocks.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-find-and-replace/theme/findandreplaceform.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-theme-lark/theme/ckeditor5-find-and-replace/findandreplaceform.css", "webpack://django_ckeditor_5/./node_modules/@ckeditor/ckeditor5-find-and-replace/theme/findandreplace.css", "webpack://django_ckeditor_5/./static/django_ckeditor_5/src/override-django.css"], "sourcesContent": ["/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n/* See ckeditor/ckeditor5#936. */\n.ck.ck-placeholder,\n.ck .ck-placeholder {\n\tposition: relative;\n\n\t&::before {\n\t\tposition: absolute;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tcontent: attr(data-placeholder);\n\n\t\t/* See ckeditor/ckeditor5#469. */\n\t\tpointer-events: none;\n\t}\n}\n\n/* See ckeditor/ckeditor5#1987. */\n.ck.ck-read-only .ck-placeholder {\n\t&::before {\n\t\tdisplay: none;\n\t}\n}\n\n/*\n * Rules for the `ck-placeholder` are loaded before the rules for `ck-reset_all` in the base CKEditor 5 DLL build.\n * This fix overwrites the incorrectly set `position: static` from `ck-reset_all`.\n * See https://github.com/ckeditor/ckeditor5/issues/11418.\n */\n.ck.ck-reset_all .ck-placeholder {\n\tposition: relative;\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@define-mixin ck-media-forced-colors {\n\t@media (forced-colors: active) {\n\t\t& {\n\t\t\t@mixin-content;\n\t\t}\n\t}\n}\n\n@define-mixin ck-media-default-colors {\n\t@media (forced-colors: none) {\n\t\t& {\n\t\t\t@mixin-content;\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"@ckeditor/ckeditor5-ui/theme/mixins/_mediacolors.css\";\n\n/* See ckeditor/ckeditor5#936. */\n.ck.ck-placeholder, .ck .ck-placeholder {\n\t@mixin ck-media-forced-colors {\n\t\t/*\n\t\t * This is needed for Edge on Windows to use the right color for the placeholder content (::before).\n\t\t * See https://github.com/ckeditor/ckeditor5/issues/14907.\n\t\t */\n\t\tforced-color-adjust: preserve-parent-color;\n\t}\n\n\t&::before {\n\t\tcursor: text;\n\n\t\t@mixin ck-media-default-colors {\n\t\t\tcolor: var(--ck-color-engine-placeholder-text);\n\t\t}\n\n\t\t@mixin ck-media-forced-colors {\n\t\t\t/*\n\t\t\t * In the high contrast mode there is no telling between regular and placeholder text. Using\n\t\t\t * italic text to address that issue. See https://github.com/ckeditor/ckeditor5/issues/14907.\n\t\t\t */\n\t\t\tfont-style: italic;\n\n\t\t\t/*\n\t\t\t * Without this margin, the caret will not show up and blink when the user puts the selection\n\t\t\t * in the placeholder (Edge on Windows). See https://github.com/ckeditor/ckeditor5/issues/14907.\n\t\t\t */\n\t\t\tmargin-left: 1px;\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n/* Elements marked by the Renderer as hidden should be invisible in the editor. */\n.ck.ck-editor__editable span[data-ck-unsafe-element] {\n\tdisplay: none;\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n/**\n * A class which hides an element in DOM.\n */\n.ck-hidden {\n\t/* Override selector specificity. Otherwise, all elements with some display\n\tstyle defined will override this one, which is not a desired result. */\n\tdisplay: none !important;\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n:root {\n\t--ck-z-default: 1;\n\t--ck-z-panel: calc( var(--ck-z-default) + 999 );\n\t--ck-z-dialog: 9999;\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n/**\n * A class that disables all transitions of the element and its children.\n */\n.ck-transitions-disabled,\n.ck-transitions-disabled * {\n\ttransition: none !important;\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n:root {\n\t/* where 7.5(px) is the font size of the powered by label and 13(px) is the base font size. */\n\t--ck-powered-by-font-size: calc(var(--ck-font-size-base) * 7.5 / 13);\n\t/* where 10(px) is the line height of the powered by label and 13(px) is the base font size. */\n\t--ck-powered-by-line-height: calc(var(--ck-font-size-base) * 10 / 13);\n\t/* where -0.2(px) is the letter spacing of the powered by label and 13(px) is the base font size. */\n\t--ck-powered-by-letter-spacing: calc(var(--ck-font-size-base) * -0.2 / 13);\n\t--ck-powered-by-padding-vertical: 2px;\n\t--ck-powered-by-padding-horizontal: 4px;\n\t--ck-powered-by-text-color: hsl(0, 0%, 31%);\n\t--ck-powered-by-border-radius: var(--ck-border-radius);\n\t--ck-powered-by-background: hsl(0, 0%, 100%);\n\t--ck-powered-by-border-color: var(--ck-color-focus-border);\n\n\t--ck-powered-by-svg-width: 53;\n\t--ck-powered-by-svg-height: 10;\n\t--ck-powered-by-icon-width: calc(var(--ck-font-size-base) * var(--ck-powered-by-svg-width) / 13);\n\t--ck-powered-by-icon-height: calc(var(--ck-font-size-base) * var(--ck-powered-by-svg-height) / 13);\n}\n\n.ck.ck-balloon-panel.ck-powered-by-balloon {\n\t--ck-border-radius: var(--ck-powered-by-border-radius);\n\n\tbox-shadow: none;\n\tbackground: var(--ck-powered-by-background);\n\tmin-height: unset;\n\tz-index: calc( var(--ck-z-panel) - 1 );\n\n\t& .ck.ck-powered-by {\n\t\tline-height: var(--ck-powered-by-line-height);\n\n\t\t& a {\n\t\t\tcursor: pointer;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\topacity: .66;\n\t\t\tfilter: grayscale(80%);\n\t\t\tline-height: var(--ck-powered-by-line-height);\n\t\t\tpadding: var(--ck-powered-by-padding-vertical) var(--ck-powered-by-padding-horizontal);\n\t\t}\n\n\t\t& .ck-powered-by__label {\n\t\t\tfont-size: var(--ck-powered-by-font-size);\n\t\t\tletter-spacing: var(--ck-powered-by-letter-spacing);\n\t\t\tpadding-left: 2px;\n\t\t\ttext-transform: uppercase;\n\t\t\tfont-weight: bold;\n\t\t\tmargin-right: 4px;\n\t\t\tcursor: pointer;\n\t\t\tline-height: normal;\n\t\t\tcolor: var(--ck-powered-by-text-color);\n\n\t\t}\n\n\t\t& .ck-icon {\n\t\t\tdisplay: block;\n\t\t\tcursor: pointer;\n\t\t\twidth: var(--ck-powered-by-icon-width);\n\t\t\theight: var(--ck-powered-by-icon-height);\n\t\t}\n\n\t\t&:hover {\n\t\t\t& a {\n\t\t\t\tfilter: grayscale(0%);\n\t\t\t\topacity: 1;\n\t\t\t}\n\t\t}\n\t}\n\n\t&[class*=\"position_inside\"] {\n\t\tborder-color: transparent;\n\t}\n\n\t&[class*=\"position_border\"] {\n\t\tborder: var(--ck-focus-ring);\n\t\tborder-color: var(--ck-powered-by-border-color);\n\t}\n}\n\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n:root {\n\t/* where 7.5(px) is the font size of the evaluation badge label and 13(px) is the base font size. */\n\t--ck-evaluation-badge-font-size: calc(var(--ck-font-size-base) * 7.5 / 13);\n\t/* where 7.5(px) is the line height of the evaluation badge label and 13(px) is the base font size. */\n\t--ck-evaluation-badge-line-height: calc(var(--ck-font-size-base) * 7.5 / 13);\n\t/* where -0.2(px) is the letter spacing of the evaluation badge label and 13(px) is the base font size. */\n\t--ck-evaluation-badge-letter-spacing: calc(var(--ck-font-size-base) * -0.2 / 13);\n\t--ck-evaluation-badge-padding-vertical: 2px;\n\t--ck-evaluation-badge-padding-horizontal: 4px;\n\t--ck-evaluation-badge-text-color: hsl(0, 0%, 31%);\n\t--ck-evaluation-badge-border-radius: var(--ck-border-radius);\n\t--ck-evaluation-badge-background: hsl(0, 0%, 100%);\n\t--ck-evaluation-badge-border-color: var(--ck-color-focus-border);\n}\n\n.ck.ck-balloon-panel.ck-evaluation-badge-balloon {\n\t--ck-border-radius: var(--ck-evaluation-badge-border-radius);\n\n\tbox-shadow: none;\n\tbackground: var(--ck-evaluation-badge-background);\n\tmin-height: unset;\n\tz-index: calc( var(--ck-z-panel) - 1 );\n\n\t& .ck.ck-evaluation-badge {\n\t\tline-height: var(--ck-evaluation-badge-line-height);\n\t\tpadding: var(--ck-evaluation-badge-padding-vertical) var(--ck-evaluation-badge-padding-horizontal);\n\n\t\t& .ck-evaluation-badge__label {\n\t\t\tdisplay: block;\n\t\t\tpadding: 0 2px;\n\t\t\tfont-size: var(--ck-evaluation-badge-font-size);\n\t\t\tletter-spacing: var(--ck-evaluation-badge-letter-spacing);\n\t\t\tfont-weight: bold;\n\t\t\tline-height: normal;\n\t\t\ttext-transform: uppercase;\n\t\t\tcolor: var(--ck-evaluation-badge-text-color);\n\t\t}\n\t}\n\n\t&[class*=\"position_inside\"] {\n\t\tborder-color: transparent;\n\t}\n\n\t&[class*=\"position_border\"] {\n\t\tborder: var(--ck-focus-ring);\n\t\tborder-color: var(--ck-evaluation-badge-border-color);\n\t}\n}\n\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n:root {\n\t--ck-color-base-foreground: \t\t\t\t\t\t\t\thsl(0, 0%, 98%);\n\t--ck-color-base-background: \t\t\t\t\t\t\t\thsl(0, 0%, 100%);\n\t--ck-color-base-border: \t\t\t\t\t\t\t\t\thsl(220, 6%, 81%);\n\t--ck-color-base-action: \t\t\t\t\t\t\t\t\thsl(104, 50.2%, 42.5%);\n\t--ck-color-base-focus: \t\t\t\t\t\t\t\t\t\thsl(209, 92%, 70%);\n\t--ck-color-base-text: \t\t\t\t\t\t\t\t\t\thsl(0, 0%, 20%);\n\t--ck-color-base-active: \t\t\t\t\t\t\t\t\thsl(218.1, 100%, 58%);\n\t--ck-color-base-active-focus:\t\t\t\t\t\t\t\thsl(218.2, 100%, 52.5%);\n\t--ck-color-base-error:\t\t\t\t\t\t\t\t\t\thsl(15, 100%, 43%);\n\n\t/* -- Generic colors ------------------------------------------------------------------------ */\n\n\t--ck-color-focus-border-coordinates: \t\t\t\t\t\t218, 81.8%, 56.9%;\n\t--ck-color-focus-border: \t\t\t\t\t\t\t\t\thsl(var(--ck-color-focus-border-coordinates));\n\t--ck-color-focus-outer-shadow:\t\t\t\t\t\t\t\thsl(212.4, 89.3%, 89%);\n\t--ck-color-focus-disabled-shadow:\t\t\t\t\t\t\thsla(209, 90%, 72%,.3);\n\t--ck-color-focus-error-shadow:\t\t\t\t\t\t\t\thsla(9,100%,56%,.3);\n\t--ck-color-text: \t\t\t\t\t\t\t\t\t\t\tvar(--ck-color-base-text);\n\t--ck-color-shadow-drop: \t\t\t\t\t\t\t\t\thsla(0, 0%, 0%, 0.15);\n\t--ck-color-shadow-drop-active:\t\t\t\t\t\t\t\thsla(0, 0%, 0%, 0.2);\n\t--ck-color-shadow-inner: \t\t\t\t\t\t\t\t\thsla(0, 0%, 0%, 0.1);\n\n\t/* -- Buttons ------------------------------------------------------------------------------- */\n\n\t--ck-color-button-default-background: \t\t\t\t\t\ttransparent;\n\t--ck-color-button-default-hover-background: \t\t\t\thsl(0, 0%, 94.1%);\n\t--ck-color-button-default-active-background: \t\t\t\thsl(0, 0%, 94.1%);\n\t--ck-color-button-default-disabled-background: \t\t\t\ttransparent;\n\n\t--ck-color-button-on-background: \t\t\t\t\t\t\thsl(212, 100%, 97.1%);\n\t--ck-color-button-on-hover-background: \t\t\t\t\t\thsl(211.7, 100%, 92.9%);\n\t--ck-color-button-on-active-background: \t\t\t\t\thsl(211.7, 100%, 92.9%);\n\t--ck-color-button-on-disabled-background: \t\t\t\t\thsl(211, 15%, 95%);\n\t--ck-color-button-on-color:\t\t\t\t\t\t\t\t\thsl(218.1, 100%, 58%);\n\n\n\t--ck-color-button-action-background: \t\t\t\t\t\tvar(--ck-color-base-action);\n\t--ck-color-button-action-hover-background: \t\t\t\t\thsl(104, 53.2%, 40.2%);\n\t--ck-color-button-action-active-background: \t\t\t\thsl(104, 53.2%, 40.2%);\n\t--ck-color-button-action-disabled-background: \t\t\t\thsl(104, 44%, 58%);\n\t--ck-color-button-action-text: \t\t\t\t\t\t\t\tvar(--ck-color-base-background);\n\n\t--ck-color-button-save: \t\t\t\t\t\t\t\t\thsl(120, 100%, 27%);\n\t--ck-color-button-cancel: \t\t\t\t\t\t\t\t\thsl(15, 100%, 43%);\n\n\t--ck-color-switch-button-off-background:\t\t\t\t\thsl(0, 0%, 57.6%);\n\t--ck-color-switch-button-off-hover-background:\t\t\t\thsl(0, 0%, 49%);\n\t--ck-color-switch-button-on-background:\t\t\t\t\t\tvar(--ck-color-button-action-background);\n\t--ck-color-switch-button-on-hover-background:\t\t\t\thsl(104, 53.2%, 40.2%);\n\t--ck-color-switch-button-inner-background:\t\t\t\t\tvar(--ck-color-base-background);\n\t--ck-color-switch-button-inner-shadow:\t\t\t\t\t\thsla(0, 0%, 0%, 0.1);\n\n\t/* -- Dropdown ------------------------------------------------------------------------------ */\n\n\t--ck-color-dropdown-panel-background: \t\t\t\t\t\tvar(--ck-color-base-background);\n\t--ck-color-dropdown-panel-border: \t\t\t\t\t\t\tvar(--ck-color-base-border);\n\n\t/* -- Dialog -------------------------------------------------------------------------------- */\n\n\t--ck-color-dialog-background: \t\t\t\t\t\t\t\tvar(--ck-custom-background);\n\t--ck-color-dialog-form-header-border: \t\t\t\t\t\tvar(--ck-custom-border);\n\n\t/* -- Input --------------------------------------------------------------------------------- */\n\n\t--ck-color-input-background: \t\t\t\t\t\t\t\tvar(--ck-color-base-background);\n\t--ck-color-input-border: \t\t\t\t\t\t\t\t\tvar(--ck-color-base-border);\n\t--ck-color-input-error-border:\t\t\t\t\t\t\t\tvar(--ck-color-base-error);\n\t--ck-color-input-text: \t\t\t\t\t\t\t\t\t\tvar(--ck-color-base-text);\n\t--ck-color-input-disabled-background: \t\t\t\t\t\thsl(0, 0%, 95%);\n\t--ck-color-input-disabled-border: \t\t\t\t\t\t\tvar(--ck-color-base-border);\n\t--ck-color-input-disabled-text: \t\t\t\t\t\t\thsl(0, 0%, 46%);\n\n\t/* -- List ---------------------------------------------------------------------------------- */\n\n\t--ck-color-list-background: \t\t\t\t\t\t\t\tvar(--ck-color-base-background);\n\t--ck-color-list-button-hover-background: \t\t\t\t\tvar(--ck-color-button-default-hover-background);\n\t--ck-color-list-button-on-background: \t\t\t\t\t\tvar(--ck-color-button-on-color);\n\t--ck-color-list-button-on-background-focus: \t\t\t\tvar(--ck-color-button-on-color);\n\t--ck-color-list-button-on-text:\t\t\t\t\t\t\t\tvar(--ck-color-base-background);\n\n\t/* -- Panel --------------------------------------------------------------------------------- */\n\n\t--ck-color-panel-background: \t\t\t\t\t\t\t\tvar(--ck-color-base-background);\n\t--ck-color-panel-border: \t\t\t\t\t\t\t\t\tvar(--ck-color-base-border);\n\n\t/* -- Toolbar ------------------------------------------------------------------------------- */\n\n\t--ck-color-toolbar-background: \t\t\t\t\t\t\t\tvar(--ck-color-base-background);\n\t--ck-color-toolbar-border: \t\t\t\t\t\t\t\t\tvar(--ck-color-base-border);\n\n\t/* -- Tooltip ------------------------------------------------------------------------------- */\n\n\t--ck-color-tooltip-background: \t\t\t\t\t\t\t\tvar(--ck-color-base-text);\n\t--ck-color-tooltip-text: \t\t\t\t\t\t\t\t\tvar(--ck-color-base-background);\n\n\t/* -- Engine -------------------------------------------------------------------------------- */\n\n\t--ck-color-engine-placeholder-text: \t\t\t\t\t\thsl(0, 0%, 44%);\n\n\t/* -- Upload -------------------------------------------------------------------------------- */\n\n\t--ck-color-upload-bar-background:\t\t \t\t\t\t\thsl(209, 92%, 70%);\n\n\t/* -- Link -------------------------------------------------------------------------------- */\n\n\t--ck-color-link-default:\t\t\t\t\t\t\t\t\thsl(240, 100%, 47%);\n\t--ck-color-link-selected-background:\t\t\t\t\t\thsla(201, 100%, 56%, 0.1);\n\t--ck-color-link-fake-selection:\t\t\t\t\t\t\t\thsla(201, 100%, 56%, 0.3);\n\n\t/* -- Search result highlight ---------------------------------------------------------------- */\n\n\t--ck-color-highlight-background:\t\t\t\t\t\t\thsl(60, 100%, 50%);\n\n\t/* -- Generic colors ------------------------------------------------------------------------- */\n\n\t--ck-color-light-red:\t\t\t\t\t\t\t\t\t\thsl(0, 100%, 90%);\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n:root {\n\t/**\n\t * An opacity value of disabled UI item.\n\t */\n\t--ck-disabled-opacity: .5;\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n:root {\n\t/**\n\t * The geometry of the of focused element's outer shadow.\n\t */\n\t--ck-focus-outer-shadow-geometry: 0 0 0 3px;\n\n\t/**\n\t * A visual style of focused element's outer shadow.\n\t */\n\t--ck-focus-outer-shadow: var(--ck-focus-outer-shadow-geometry) var(--ck-color-focus-outer-shadow);\n\n\t/**\n\t * A visual style of focused element's outer shadow (when disabled).\n\t */\n\t--ck-focus-disabled-outer-shadow: var(--ck-focus-outer-shadow-geometry) var(--ck-color-focus-disabled-shadow);\n\n\t/**\n\t * A visual style of focused element's outer shadow (when has errors).\n\t */\n\t--ck-focus-error-outer-shadow: var(--ck-focus-outer-shadow-geometry) var(--ck-color-focus-error-shadow);\n\n\t/**\n\t * A visual style of focused element's border or outline.\n\t */\n\t--ck-focus-ring: 1px solid var(--ck-color-focus-border);\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n:root {\n\t--ck-font-size-base: 13px;\n\t--ck-line-height-base: 1.84615;\n\t--ck-font-face: Helvetica, Arial, Tahoma, Verdana, Sans-Serif;\n\n\t--ck-font-size-tiny: 0.7em;\n\t--ck-font-size-small: 0.75em;\n\t--ck-font-size-normal: 1em;\n\t--ck-font-size-big: 1.4em;\n\t--ck-font-size-large: 1.8em;\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n:root {\n\t/* This is super-important. This is **manually** adjusted so a button without an icon\n\tis never smaller than a button with icon, additionally making sure that text-less buttons\n\tare perfect squares. The value is also shared by other components which should stay \"in-line\"\n\twith buttons. */\n\t--ck-ui-component-min-height: 2.3em;\n}\n\n/**\n * Resets an element, ignoring its children.\n */\n.ck.ck-reset,\n.ck.ck-reset_all,\n.ck-reset_all *:not(.ck-reset_all-excluded *) {\n\tbox-sizing: border-box;\n\twidth: auto;\n\theight: auto;\n\tposition: static;\n\n\t/* Do not include inheritable rules here. */\n\tmargin: 0;\n\tpadding: 0;\n\tborder: 0;\n\tbackground: transparent;\n\ttext-decoration: none;\n\tvertical-align: middle;\n\ttransition: none;\n\n\t/* https://github.com/ckeditor/ckeditor5-theme-lark/issues/105 */\n\tword-wrap: break-word;\n}\n\n/**\n * Resets an element AND its children.\n */\n.ck.ck-reset_all,\n.ck-reset_all *:not(.ck-reset_all-excluded *) {\n\t/* These are rule inherited by all children elements. */\n\tborder-collapse: collapse;\n\tfont: normal normal normal var(--ck-font-size-base)/var(--ck-line-height-base) var(--ck-font-face);\n\tcolor: var(--ck-color-text);\n\ttext-align: left;\n\twhite-space: nowrap;\n\tcursor: auto;\n\tfloat: none;\n}\n\n.ck-reset_all {\n\t& .ck-rtl *:not(.ck-reset_all-excluded *) {\n\t\ttext-align: right;\n\t}\n\n\t& iframe:not(.ck-reset_all-excluded *) {\n\t\t/* For IE */\n\t\tvertical-align: inherit;\n\t}\n\n\t& textarea:not(.ck-reset_all-excluded *) {\n\t\twhite-space: pre-wrap;\n\t}\n\n\t& textarea:not(.ck-reset_all-excluded *),\n\t& input[type=\"text\"]:not(.ck-reset_all-excluded *),\n\t& input[type=\"password\"]:not(.ck-reset_all-excluded *) {\n\t\tcursor: text;\n\t}\n\n\t& textarea[disabled]:not(.ck-reset_all-excluded *),\n\t& input[type=\"text\"][disabled]:not(.ck-reset_all-excluded *),\n\t& input[type=\"password\"][disabled]:not(.ck-reset_all-excluded *) {\n\t\tcursor: default;\n\t}\n\n\t& fieldset:not(.ck-reset_all-excluded *) {\n\t\tpadding: 10px;\n\t\tborder: 2px groove hsl(255, 7%, 88%);\n\t}\n\n\t& button:not(.ck-reset_all-excluded *)::-moz-focus-inner {\n\t\t/* See http://stackoverflow.com/questions/5517744/remove-extra-button-spacing-padding-in-firefox */\n\t\tpadding: 0;\n\t\tborder: 0\n\t}\n}\n\n/**\n * Default UI rules for RTL languages.\n */\n.ck[dir=\"rtl\"],\n.ck[dir=\"rtl\"] .ck {\n\ttext-align: right;\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n/**\n * Default border-radius value.\n */\n:root{\n\t--ck-border-radius: 2px;\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n:root {\n\t/**\n\t * A visual style of element's inner shadow (i.e. input).\n\t */\n\t--ck-inner-shadow: 2px 2px 3px var(--ck-color-shadow-inner) inset;\n\n\t/**\n\t * A visual style of element's drop shadow (i.e. panel).\n\t */\n\t--ck-drop-shadow: 0 1px 2px 1px var(--ck-color-shadow-drop);\n\n\t/**\n\t * A visual style of element's active shadow (i.e. comment or suggestion).\n\t */\n\t--ck-drop-shadow-active: 0 3px 6px 1px var(--ck-color-shadow-drop-active);\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n:root {\n\t--ck-spacing-unit: \t\t\t\t\t\t0.6em;\n\t--ck-spacing-large: \t\t\t\t\tcalc(var(--ck-spacing-unit) * 1.5);\n\t--ck-spacing-standard: \t\t\t\t\tvar(--ck-spacing-unit);\n\t--ck-spacing-medium: \t\t\t\t\tcalc(var(--ck-spacing-unit) * 0.8);\n\t--ck-spacing-small: \t\t\t\t\tcalc(var(--ck-spacing-unit) * 0.5);\n\t--ck-spacing-tiny: \t\t\t\t\t\tcalc(var(--ck-spacing-unit) * 0.3);\n\t--ck-spacing-extra-tiny: \t\t\t\tcalc(var(--ck-spacing-unit) * 0.16);\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n:root {\n\t/* Make sure the balloon arrow does not float over its children. */\n\t--ck-balloon-panel-arrow-z-index: calc(var(--ck-z-default) - 3);\n}\n\n.ck.ck-balloon-panel {\n\tdisplay: none;\n\tposition: absolute;\n\n\tz-index: var(--ck-z-panel);\n\n\t&.ck-balloon-panel_with-arrow {\n\t\t&::before,\n\t\t&::after {\n\t\t\tcontent: \"\";\n\t\t\tposition: absolute;\n\t\t}\n\n\t\t&::before {\n\t\t\tz-index: var(--ck-balloon-panel-arrow-z-index);\n\t\t}\n\n\t\t&::after {\n\t\t\tz-index: calc(var(--ck-balloon-panel-arrow-z-index) + 1);\n\t\t}\n\t}\n\n\t&[class*=\"arrow_n\"] {\n\t\t&::before {\n\t\t\tz-index: var(--ck-balloon-panel-arrow-z-index);\n\t\t}\n\n\t\t&::after {\n\t\t\tz-index: calc(var(--ck-balloon-panel-arrow-z-index) + 1);\n\t\t}\n\t}\n\n\t&[class*=\"arrow_s\"] {\n\t\t&::before {\n\t\t\tz-index: var(--ck-balloon-panel-arrow-z-index);\n\t\t}\n\n\t\t&::after {\n\t\t\tz-index: calc(var(--ck-balloon-panel-arrow-z-index) + 1);\n\t\t}\n\t}\n\n\t&.ck-balloon-panel_visible {\n\t\tdisplay: block;\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"../../../mixins/_rounded.css\";\n@import \"../../../mixins/_shadow.css\";\n\n:root {\n\t--ck-balloon-border-width: 1px;\n\t--ck-balloon-arrow-offset: 2px;\n\t--ck-balloon-arrow-height: 10px;\n\t--ck-balloon-arrow-half-width: 8px;\n\t--ck-balloon-arrow-drop-shadow: 0 2px 2px var(--ck-color-shadow-drop);\n}\n\n.ck.ck-balloon-panel {\n\t@mixin ck-rounded-corners;\n\t@mixin ck-drop-shadow;\n\n\tmin-height: 15px;\n\n\tbackground: var(--ck-color-panel-background);\n\tborder: var(--ck-balloon-border-width) solid var(--ck-color-panel-border);\n\n\t&.ck-balloon-panel_with-arrow {\n\t\t&::before,\n\t\t&::after {\n\t\t\twidth: 0;\n\t\t\theight: 0;\n\t\t\tborder-style: solid;\n\t\t}\n\t}\n\n\t&[class*=\"arrow_n\"] {\n\t\t&::before,\n\t\t&::after {\n\t\t\tborder-width: 0 var(--ck-balloon-arrow-half-width) var(--ck-balloon-arrow-height) var(--ck-balloon-arrow-half-width);\n\t\t}\n\n\t\t&::before {\n\t\t\tborder-color: transparent transparent var(--ck-color-panel-border) transparent;\n\t\t\tmargin-top: calc( -1 * var(--ck-balloon-border-width) );\n\t\t}\n\n\t\t&::after {\n\t\t\tborder-color: transparent transparent var(--ck-color-panel-background) transparent;\n\t\t\tmargin-top: calc( var(--ck-balloon-arrow-offset) - var(--ck-balloon-border-width) );\n\t\t}\n\t}\n\n\t&[class*=\"arrow_s\"] {\n\t\t&::before,\n\t\t&::after {\n\t\t\tborder-width: var(--ck-balloon-arrow-height) var(--ck-balloon-arrow-half-width) 0 var(--ck-balloon-arrow-half-width);\n\t\t}\n\n\t\t&::before {\n\t\t\tborder-color: var(--ck-color-panel-border) transparent transparent;\n\t\t\tfilter: drop-shadow(var(--ck-balloon-arrow-drop-shadow));\n\t\t\tmargin-bottom: calc( -1 * var(--ck-balloon-border-width) );\n\t\t}\n\n\t\t&::after {\n\t\t\tborder-color: var(--ck-color-panel-background) transparent transparent transparent;\n\t\t\tmargin-bottom: calc( var(--ck-balloon-arrow-offset) - var(--ck-balloon-border-width) );\n\t\t}\n\t}\n\n\t&[class*=\"arrow_e\"] {\n\t\t&::before,\n\t\t&::after {\n\t\t\tborder-width: var(--ck-balloon-arrow-half-width) 0 var(--ck-balloon-arrow-half-width) var(--ck-balloon-arrow-height);\n\t\t}\n\n\t\t&::before {\n\t\t\tborder-color: transparent transparent transparent var(--ck-color-panel-border);\n\t\t\tmargin-right: calc( -1 * var(--ck-balloon-border-width) );\n\t\t}\n\n\t\t&::after {\n\t\t\tborder-color: transparent transparent transparent var(--ck-color-panel-background);\n\t\t\tmargin-right: calc( var(--ck-balloon-arrow-offset) - var(--ck-balloon-border-width) );\n\t\t}\n\t}\n\n\t&[class*=\"arrow_w\"] {\n\t\t&::before,\n\t\t&::after {\n\t\t\tborder-width: var(--ck-balloon-arrow-half-width) var(--ck-balloon-arrow-height) var(--ck-balloon-arrow-half-width) 0;\n\t\t}\n\n\t\t&::before {\n\t\t\tborder-color: transparent var(--ck-color-panel-border) transparent transparent;\n\t\t\tmargin-left: calc( -1 * var(--ck-balloon-border-width) );\n\t\t}\n\n\t\t&::after {\n\t\t\tborder-color: transparent var(--ck-color-panel-background) transparent transparent;\n\t\t\tmargin-left: calc( var(--ck-balloon-arrow-offset) - var(--ck-balloon-border-width) );\n\t\t}\n\t}\n\n\t&.ck-balloon-panel_arrow_n {\n\t\t&::before,\n\t\t&::after {\n\t\t\tleft: 50%;\n\t\t\tmargin-left: calc(-1 * var(--ck-balloon-arrow-half-width));\n\t\t\ttop: calc(-1 * var(--ck-balloon-arrow-height));\n\t\t}\n\t}\n\n\t&.ck-balloon-panel_arrow_nw {\n\t\t&::before,\n\t\t&::after {\n\t\t\tleft: calc(2 * var(--ck-balloon-arrow-half-width));\n\t\t\ttop: calc(-1 * var(--ck-balloon-arrow-height));\n\t\t}\n\t}\n\n\t&.ck-balloon-panel_arrow_ne {\n\t\t&::before,\n\t\t&::after {\n\t\t\tright: calc(2 * var(--ck-balloon-arrow-half-width));\n\t\t\ttop: calc(-1 * var(--ck-balloon-arrow-height));\n\t\t}\n\t}\n\n\t&.ck-balloon-panel_arrow_s {\n\t\t&::before,\n\t\t&::after {\n\t\t\tleft: 50%;\n\t\t\tmargin-left: calc(-1 * var(--ck-balloon-arrow-half-width));\n\t\t\tbottom: calc(-1 * var(--ck-balloon-arrow-height));\n\t\t}\n\t}\n\n\t&.ck-balloon-panel_arrow_sw {\n\t\t&::before,\n\t\t&::after {\n\t\t\tleft: calc(2 * var(--ck-balloon-arrow-half-width));\n\t\t\tbottom: calc(-1 * var(--ck-balloon-arrow-height));\n\t\t}\n\t}\n\n\t&.ck-balloon-panel_arrow_se {\n\t\t&::before,\n\t\t&::after {\n\t\t\tright: calc(2 * var(--ck-balloon-arrow-half-width));\n\t\t\tbottom: calc(-1 * var(--ck-balloon-arrow-height));\n\t\t}\n\t}\n\n\t&.ck-balloon-panel_arrow_sme {\n\t\t&::before,\n\t\t&::after {\n\t\t\tright: 25%;\n\t\t\tmargin-right: calc(2 * var(--ck-balloon-arrow-half-width));\n\t\t\tbottom: calc(-1 * var(--ck-balloon-arrow-height));\n\t\t}\n\t}\n\n\t&.ck-balloon-panel_arrow_smw {\n\t\t&::before,\n\t\t&::after {\n\t\t\tleft: 25%;\n\t\t\tmargin-left: calc(2 * var(--ck-balloon-arrow-half-width));\n\t\t\tbottom: calc(-1 * var(--ck-balloon-arrow-height));\n\t\t}\n\t}\n\n\t&.ck-balloon-panel_arrow_nme {\n\t\t&::before,\n\t\t&::after {\n\t\t\tright: 25%;\n\t\t\tmargin-right: calc(2 * var(--ck-balloon-arrow-half-width));\n\t\t\ttop: calc(-1 * var(--ck-balloon-arrow-height));\n\t\t}\n\t}\n\n\t&.ck-balloon-panel_arrow_nmw {\n\t\t&::before,\n\t\t&::after {\n\t\t\tleft: 25%;\n\t\t\tmargin-left: calc(2 * var(--ck-balloon-arrow-half-width));\n\t\t\ttop: calc(-1 * var(--ck-balloon-arrow-height));\n\t\t}\n\t}\n\n\t&.ck-balloon-panel_arrow_e {\n\t\t&::before,\n\t\t&::after {\n\t\t\tright: calc(-1 * var(--ck-balloon-arrow-height));\n\t\t\tmargin-top: calc(-1 * var(--ck-balloon-arrow-half-width));\n\t\t\ttop: 50%;\n\t\t}\n\t}\n\n\t&.ck-balloon-panel_arrow_w {\n\t\t&::before,\n\t\t&::after {\n\t\t\tleft: calc(-1 * var(--ck-balloon-arrow-height));\n\t\t\tmargin-top: calc(-1 * var(--ck-balloon-arrow-half-width));\n\t\t\ttop: 50%;\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n/**\n * Implements rounded corner interface for .ck-rounded-corners class.\n *\n * @see $ck-border-radius\n */\n@define-mixin ck-rounded-corners {\n\tborder-radius: 0;\n\n\t.ck-rounded-corners &,\n\t&.ck-rounded-corners {\n\t\tborder-radius: var(--ck-border-radius);\n\t\t@mixin-content;\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n/**\n * A helper to combine multiple shadows.\n */\n@define-mixin ck-box-shadow $shadowA, $shadowB: 0 0 {\n\tbox-shadow: $shadowA, $shadowB;\n}\n\n/**\n * Gives an element a drop shadow so it looks like a floating panel.\n */\n@define-mixin ck-drop-shadow {\n\t@mixin ck-box-shadow var(--ck-drop-shadow);\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"../../mixins/_unselectable.css\";\n\n.ck.ck-balloon-panel.ck-tooltip {\n\t@mixin ck-unselectable;\n\n\tz-index: calc( var(--ck-z-dialog) + 100 );\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n/**\n * Makes element unselectable.\n */\n@define-mixin ck-unselectable {\n\t-moz-user-select: none;\n\t-webkit-user-select: none;\n\t-ms-user-select: none;\n\tuser-select: none\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"../../../mixins/_rounded.css\";\n\n.ck.ck-balloon-panel.ck-tooltip {\n\t--ck-balloon-border-width: 0px;\n\t--ck-balloon-arrow-offset: 0px;\n\t--ck-balloon-arrow-half-width: 4px;\n\t--ck-balloon-arrow-height: 4px;\n\t--ck-tooltip-text-padding: 4px;\n\t--ck-color-panel-background: var(--ck-color-tooltip-background);\n\n\tpadding: 0 var(--ck-spacing-medium);\n\n\t& .ck-tooltip__text {\n\t\tfont-size: .9em;\n\t\tline-height: 1.5;\n\t\tcolor: var(--ck-color-tooltip-text);\n\t}\n\n\t&.ck-tooltip_multi-line .ck-tooltip__text {\n\t\twhite-space: break-spaces;\n\t\tdisplay: inline-block;\n\t\tpadding: var(--ck-tooltip-text-padding) 0;\n\t\tmax-width: 200px;\n\t}\n\n\t/* Reset balloon panel styles */\n\tbox-shadow: none;\n\n\t/* Hide the default shadow of the .ck-balloon-panel tip */\n\t&::before {\n\t\tdisplay: none;\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck.ck-icon {\n\tvertical-align: middle;\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n:root {\n\t--ck-icon-size: calc(var(--ck-line-height-base) * var(--ck-font-size-normal));\n\t--ck-icon-font-size: .8333350694em;\n}\n\n.ck.ck-icon {\n\twidth: var(--ck-icon-size);\n\theight: var(--ck-icon-size);\n\n\t/* Multiplied by the height of the line in \"px\" should give SVG \"viewport\" dimensions */\n\tfont-size: var(--ck-icon-font-size);\n\n\t/* Inherit cursor style (#5). */\n\tcursor: inherit;\n\n\t& * {\n\t\t/* Inherit cursor style (#5). */\n\t\tcursor: inherit;\n\t}\n\n\t/* Allows dynamic coloring of an icon by inheriting its color from the parent. */\n\t&.ck-icon_inherit-color {\n\t\tcolor: inherit;\n\n\t\t& * {\n\t\t\tcolor: inherit;\n\n\t\t\t&:not([fill]) {\n\t\t\t\t/* Needed by FF. */\n\t\t\t\tfill: currentColor;\n\t\t\t}\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck.ck-aria-live-announcer {\n\tposition: absolute;\n\tleft: -10000px;\n\ttop: -10000px;\n}\n\n.ck.ck-aria-live-region-list {\n\tlist-style-type: none;\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n:root {\n\t--ck-menu-bar-menu-item-min-width: 18em;\n}\n\n.ck.ck-menu-bar__menu .ck.ck-menu-bar__menu__item {\n\tmin-width: var(--ck-menu-bar-menu-item-min-width);\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck.ck-form__header {\n\tdisplay: flex;\n\tflex-direction: row;\n\tflex-wrap: nowrap;\n\talign-items: center;\n\tjustify-content: space-between;\n\n\t& h2.ck-form__header__label {\n\t\tflex-grow: 1;\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"@ckeditor/ckeditor5-ui/theme/mixins/_dir.css\";\n\n:root {\n\t--ck-form-header-height: 44px;\n}\n\n.ck.ck-form__header {\n\tpadding: var(--ck-spacing-small) var(--ck-spacing-large);\n\theight: var(--ck-form-header-height);\n\tline-height: var(--ck-form-header-height);\n\tborder-bottom: 1px solid var(--ck-color-base-border);\n\n\t& > .ck-icon {\n\t\t@mixin ck-dir ltr {\n\t\t\tmargin-right: var(--ck-spacing-medium);\n\t\t}\n\n\t\t@mixin ck-dir rtl {\n\t\t\tmargin-left: var(--ck-spacing-medium);\n\t\t}\n\t}\n\n\t& .ck-form__header__label {\n\t\t--ck-font-size-base: 15px;\n\t\tfont-weight: bold;\n\t}\n\n\t/* Padding when back button is hidden */\n\t&:has(.ck-button-back.ck-hidden) {\n\t\tpadding-inline: var(--ck-spacing-large) var(--ck-spacing-large);\n\t}\n\n\t/* Padding when back button is visible */\n\t&:has(.ck-button-back:not(.ck-hidden)) {\n\t\tpadding-inline: var(--ck-spacing-small) var(--ck-spacing-small);\n\t}\n\n\t& > .ck-button-back {\n\t\tmargin-inline-end: var(--ck-spacing-small);\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@define-mixin ck-dir $direction {\n\t[dir=\"$(direction)\"] & {\n\t\t@mixin-content;\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"../../mixins/_unselectable.css\";\n@import \"../../mixins/_dir.css\";\n\n.ck.ck-button,\na.ck.ck-button {\n\t@mixin ck-unselectable;\n\n\tposition: relative;\n\tdisplay: inline-flex;\n\talign-items: center;\n\n\t@mixin ck-dir ltr {\n\t\tjustify-content: left;\n\t}\n\n\t@mixin ck-dir rtl {\n\t\tjustify-content: right;\n\t}\n\n\t& .ck-button__label {\n\t\tdisplay: none;\n\t}\n\n\t&.ck-button_with-text {\n\t\t& .ck-button__label {\n\t\t\tdisplay: inline-block;\n\t\t}\n\t}\n\n\t/* Center the icon horizontally in a button without text. */\n\t&:not(.ck-button_with-text)  {\n\t\tjustify-content: center;\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"../../../mixins/_focus.css\";\n@import \"../../../mixins/_shadow.css\";\n@import \"../../../mixins/_disabled.css\";\n@import \"../../../mixins/_rounded.css\";\n@import \"../../mixins/_button.css\";\n@import \"@ckeditor/ckeditor5-ui/theme/mixins/_dir.css\";\n\n.ck.ck-button,\na.ck.ck-button {\n\t@mixin ck-button-colors --ck-color-button-default;\n\t@mixin ck-rounded-corners;\n\n\twhite-space: nowrap;\n\tcursor: default;\n\tvertical-align: middle;\n\tpadding: var(--ck-spacing-tiny);\n\ttext-align: center;\n\n\t/* A very important piece of styling. Go to variable declaration to learn more. */\n\tmin-width: var(--ck-ui-component-min-height);\n\tmin-height: var(--ck-ui-component-min-height);\n\n\t/* Normalize the height of the line. Removing this will break consistent height\n\tamong text and text-less buttons (with icons). */\n\tline-height: 1;\n\n\t/* Enable font size inheritance, which allows fluid UI scaling. */\n\tfont-size: inherit;\n\n\t/* Avoid flickering when the foucs border shows up. */\n\tborder: 1px solid transparent;\n\n\t/* Apply some smooth transition to the box-shadow and border. */\n\ttransition: box-shadow .2s ease-in-out, border .2s ease-in-out;\n\n\t/* https://github.com/ckeditor/ckeditor5-theme-lark/issues/189 */\n\t-webkit-appearance: none;\n\n\t@media (prefers-reduced-motion: reduce) {\n\t\ttransition: none;\n\t}\n\n\t&:active,\n\t&:focus {\n\t\t@mixin ck-focus-ring;\n\t\t@mixin ck-box-shadow var(--ck-focus-outer-shadow);\n\t}\n\n\t/* Allow icon coloring using the text \"color\" property. */\n\t& .ck-button__icon {\n\t\t& use,\n\t\t& use * {\n\t\t\tcolor: inherit;\n\t\t}\n\t}\n\n\t& .ck-button__label {\n\t\t/* Enable font size inheritance, which allows fluid UI scaling. */\n\t\tfont-size: inherit;\n\t\tfont-weight: inherit;\n\t\tcolor: inherit;\n\t\tcursor: inherit;\n\n\t\t/* Must be consistent with .ck-icon's vertical align. Otherwise, buttons with and\n\t\twithout labels (but with icons) have different sizes in Chrome */\n\t\tvertical-align: middle;\n\n\t\t@mixin ck-dir ltr {\n\t\t\ttext-align: left;\n\t\t}\n\n\t\t@mixin ck-dir rtl {\n\t\t\ttext-align: right;\n\t\t}\n\t}\n\n\t& .ck-button__keystroke {\n\t\tcolor: inherit;\n\n\t\t@mixin ck-dir ltr {\n\t\t\tmargin-left: var(--ck-spacing-large);\n\t\t}\n\n\t\t@mixin ck-dir rtl {\n\t\t\tmargin-right: var(--ck-spacing-large);\n\t\t}\n\n\t\topacity: .5;\n\t}\n\n\t/* https://github.com/ckeditor/ckeditor5-theme-lark/issues/70 */\n\t&.ck-disabled {\n\t\t&:active,\n\t\t&:focus {\n\t\t\t/* The disabled button should have a slightly less visible shadow when focused. */\n\t\t\t@mixin ck-box-shadow var(--ck-focus-disabled-outer-shadow);\n\t\t}\n\n\t\t& .ck-button__icon {\n\t\t\t@mixin ck-disabled;\n\t\t}\n\n\t\t/* https://github.com/ckeditor/ckeditor5-theme-lark/issues/98 */\n\t\t& .ck-button__label {\n\t\t\t@mixin ck-disabled;\n\t\t}\n\n\t\t& .ck-button__keystroke {\n\t\t\topacity: .3;\n\t\t}\n\t}\n\n\t&.ck-button_with-text {\n\t\tpadding: var(--ck-spacing-tiny) var(--ck-spacing-standard);\n\n\t\t/* stylelint-disable-next-line no-descending-specificity */\n\t\t& .ck-button__icon {\n\t\t\t@mixin ck-dir ltr {\n\t\t\t\tmargin-right: var(--ck-spacing-medium);\n\t\t\t}\n\n\t\t\t@mixin ck-dir rtl {\n\t\t\t\tmargin-left: var(--ck-spacing-medium);\n\t\t\t}\n\t\t}\n\t}\n\n\t&.ck-button_with-keystroke {\n\t\t/* stylelint-disable-next-line no-descending-specificity */\n\t\t& .ck-button__label {\n\t\t\tflex-grow: 1;\n\t\t}\n\t}\n\n\t/* A style of the button which is currently on, e.g. its feature is active. */\n\t&.ck-on {\n\t\t@mixin ck-button-colors --ck-color-button-on;\n\n\t\tcolor: var(--ck-color-button-on-color);\n\t}\n\n\t&.ck-button-save {\n\t\tcolor: var(--ck-color-button-save);\n\t}\n\n\t&.ck-button-cancel {\n\t\tcolor: var(--ck-color-button-cancel);\n\t}\n}\n\n/* A style of the button which handles the primary action. */\n.ck.ck-button-action,\na.ck.ck-button-action {\n\t@mixin ck-button-colors --ck-color-button-action;\n\n\tcolor: var(--ck-color-button-action-text);\n}\n\n.ck.ck-button-bold,\na.ck.ck-button-bold {\n\tfont-weight: bold;\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n/**\n * Implements a button of given background color.\n *\n * @param {String} $background - Background color of the button.\n * @param {String} $border - Border color of the button.\n */\n@define-mixin ck-button-colors $prefix {\n\tbackground: var($(prefix)-background);\n\n\t&:not(.ck-disabled) {\n\t\t&:hover {\n\t\t\tbackground: var($(prefix)-hover-background);\n\t\t}\n\n\t\t&:active {\n\t\t\tbackground: var($(prefix)-active-background);\n\t\t}\n\t}\n\n\t/* https://github.com/ckeditor/ckeditor5-theme-lark/issues/98 */\n\t&.ck-disabled {\n\t\tbackground: var($(prefix)-disabled-background);\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n/**\n * A visual style of focused element's border.\n */\n@define-mixin ck-focus-ring {\n\t/* Disable native outline. */\n\toutline: none;\n\tborder: var(--ck-focus-ring)\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n/**\n * A class which indicates that an element holding it is disabled.\n */\n@define-mixin ck-disabled {\n\topacity: var(--ck-disabled-opacity);\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck.ck-dialog {\n\t& .ck.ck-dialog__actions {\n\t\tdisplay: flex;\n\t\tjustify-content: flex-end;\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck.ck-dialog {\n\t& .ck.ck-dialog__actions {\n\t\tpadding: var(--ck-spacing-large);\n\n\t\t& > * + * {\n\t\t\tmargin-left: var(--ck-spacing-large);\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck.ck-dialog-overlay {\n\tuser-select: none;\n\toverscroll-behavior: none;\n\n\tposition: fixed;\n\tbottom: 0;\n\tleft: 0;\n\tright: 0;\n\ttop: 0;\n\n\t&.ck-dialog-overlay__transparent {\n\t\tpointer-events: none;\n\t\tanimation: none;\n\t\tbackground: none;\n\t}\n}\n\n.ck.ck-dialog {\n\toverscroll-behavior: none;\n\twidth: fit-content;\n\tposition: absolute;\n\n\t& .ck.ck-form__header  {\n\t\tflex-shrink: 0;\n\t}\n\n\t/* Modals should not be draggable. */\n\t&:not(.ck-dialog_modal) {\n\t\t& .ck.ck-form__header .ck-form__header__label {\n\t\t\tcursor: grab;\n\t\t}\n\t}\n\n\t.ck.ck-dialog-overlay.ck-dialog-overlay__transparent & {\n\t\tpointer-events: all;\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"../../../mixins/_rounded.css\";\n@import \"../../../mixins/_shadow.css\";\n@import \"@ckeditor/ckeditor5-ui/theme/mixins/_rwd.css\";\n\n:root {\n\t--ck-dialog-overlay-background-color: hsla( 0, 0%, 0%, .5 );\n\t--ck-dialog-drop-shadow: 0px 0px 6px 2px hsl(0deg 0% 0% / 15%);\n\t--ck-dialog-max-width: 100vw;\n\t--ck-dialog-max-height: 90vh;\n\t--ck-color-dialog-background: var(--ck-color-base-background);\n\t--ck-color-dialog-form-header-border: var(--ck-color-base-border);\n}\n\n.ck.ck-dialog-overlay {\n\tanimation: ck-dialog-fade-in .3s;\n\tbackground: var(--ck-dialog-overlay-background-color);\n\tz-index: var(--ck-z-dialog);\n}\n\n.ck.ck-dialog {\n\t@mixin ck-rounded-corners;\n\t@mixin ck-drop-shadow;\n\n\t--ck-drop-shadow: var(--ck-dialog-drop-shadow);\n\n\tbackground: var(--ck-color-dialog-background);\n\tmax-height: var(--ck-dialog-max-height);\n\tmax-width: var(--ck-dialog-max-width);\n\tborder: 1px solid var(--ck-color-base-border);\n\toverscroll-behavior: contain;\n\n\t& .ck.ck-form__header {\n\t\tborder-bottom: 1px solid var(--ck-color-dialog-form-header-border);\n\t}\n}\n\n.ck-dialog-scroll-locked {\n\toverflow: hidden;\n}\n\n@keyframes ck-dialog-fade-in {\n\t0% {\n\t\tbackground: hsla( 0, 0%, 0%, 0 );\n\t}\n\n\t100% {\n\t\tbackground: var(--ck-dialog-overlay-background-color);\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck.ck-editor {\n\t/* All the elements within `.ck-editor` are positioned relatively to it.\n\t If any element needs to be positioned with respect to the <body>, etc.,\n\t it must land outside of the `.ck-editor` in DOM. */\n\tposition: relative;\n\n\t& .ck-editor__top .ck-sticky-panel .ck-toolbar {\n\t\t/* https://github.com/ckeditor/ckeditor5-editor-classic/issues/62 */\n\t\tz-index: var(--ck-z-panel);\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"../mixins/_rounded.css\";\n\n.ck.ck-editor__top {\n\t& .ck-sticky-panel {\n\t\t& .ck-sticky-panel__content {\n\t\t\t@mixin ck-rounded-corners {\n\t\t\t\tborder-bottom-left-radius: 0;\n\t\t\t\tborder-bottom-right-radius: 0;\n\t\t\t}\n\n\t\t\tborder: 1px solid var(--ck-color-base-border);\n\t\t\tborder-bottom-width: 0;\n\n\t\t\t&.ck-sticky-panel__content_sticky {\n\t\t\t\tborder-bottom-width: 1px;\n\t\t\t}\n\n\t\t\t& .ck-menu-bar {\n\t\t\t\tborder: 0;\n\t\t\t\tborder-bottom: 1px solid var(--ck-color-base-border);\n\t\t\t}\n\n\t\t\t& .ck-toolbar {\n\t\t\t\tborder: 0;\n\t\t\t}\n\t\t}\n\t}\n}\n\n/* Note: Use ck-editor__main to make sure these styles don't apply to other editor types */\n.ck.ck-editor__main > .ck-editor__editable {\n\t/* https://github.com/ckeditor/ckeditor5-theme-lark/issues/113 */\n\tbackground: var(--ck-color-base-background);\n\n\t@mixin ck-rounded-corners {\n\t\tborder-top-left-radius: 0;\n\t\tborder-top-right-radius: 0;\n\t}\n\n\t&:not(.ck-focused) {\n\t\tborder-color: var(--ck-color-base-border);\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"../../../mixins/_rounded.css\";\n@import \"../../../mixins/_disabled.css\";\n@import \"../../../mixins/_shadow.css\";\n@import \"../../../mixins/_focus.css\";\n@import \"../../mixins/_button.css\";\n\n:root {\n\t--ck-color-editable-blur-selection: hsl(0, 0%, 85%);\n}\n\n.ck.ck-editor__editable:not(.ck-editor__nested-editable) {\n\t@mixin ck-rounded-corners;\n\n\t&.ck-focused {\n\t\t@mixin ck-focus-ring;\n\t\t@mixin ck-box-shadow var(--ck-inner-shadow);\n\t}\n}\n\n.ck.ck-editor__editable_inline {\n\toverflow: auto;\n\tpadding: 0 var(--ck-spacing-standard);\n\tborder: 1px solid transparent;\n\n\t&[dir=\"ltr\"] {\n\t\ttext-align: left;\n\t}\n\n\t&[dir=\"rtl\"] {\n\t\ttext-align: right;\n\t}\n\n\t/* https://github.com/ckeditor/ckeditor5-theme-lark/issues/116 */\n\t& > *:first-child {\n\t\tmargin-top: var(--ck-spacing-large);\n\t}\n\n\t/* https://github.com/ckeditor/ckeditor5/issues/847 */\n\t& > *:last-child {\n\t\t/*\n\t\t * This value should match with the default margins of the block elements (like .media or .image)\n\t\t * to avoid a content jumping when the fake selection container shows up (See https://github.com/ckeditor/ckeditor5/issues/9825).\n\t\t */\n\t\tmargin-bottom: var(--ck-spacing-large);\n\t}\n\n\t/* https://github.com/ckeditor/ckeditor5/issues/6517 */\n\t&.ck-blurred ::selection {\n\t\tbackground: var(--ck-color-editable-blur-selection);\n\t}\n}\n\n/* https://github.com/ckeditor/ckeditor5-theme-lark/issues/111 */\n.ck.ck-balloon-panel.ck-toolbar-container[class*=\"arrow_n\"] {\n\t&::after {\n\t\tborder-bottom-color: var(--ck-color-panel-background);\n\t}\n}\n\n.ck.ck-balloon-panel.ck-toolbar-container[class*=\"arrow_s\"] {\n\t&::after {\n\t\tborder-top-color: var(--ck-color-panel-background);\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck.ck-label {\n\tdisplay: block;\n}\n\n.ck.ck-voice-label {\n\tdisplay: none;\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck.ck-label {\n\tfont-weight: bold;\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck.ck-sticky-panel {\n\t& .ck-sticky-panel__content_sticky {\n\t\tz-index: var(--ck-z-panel); /* #315 */\n\t\tposition: fixed;\n\t\ttop: 0;\n\t}\n\n\t& .ck-sticky-panel__content_sticky_bottom-limit {\n\t\ttop: auto;\n\t\tposition: absolute;\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"../../../mixins/_shadow.css\";\n\n.ck.ck-sticky-panel {\n\t& .ck-sticky-panel__content_sticky {\n\t\t@mixin ck-drop-shadow;\n\n\t\tborder-width: 0 1px 1px;\n\t\tborder-top-left-radius: 0;\n\t\tborder-top-right-radius: 0;\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n:root {\n\t--ck-dropdown-max-width: 75vw;\n}\n\n.ck.ck-dropdown {\n\tdisplay: inline-block;\n\tposition: relative;\n\n\t& .ck-dropdown__arrow {\n\t\tpointer-events: none;\n\t\tz-index: var(--ck-z-default);\n\t}\n\n\t/* Dropdown button should span horizontally, e.g. in vertical toolbars */\n\t& .ck-button.ck-dropdown__button {\n\t\twidth: 100%;\n\t}\n\n\t& .ck-dropdown__panel {\n\t\tdisplay: none;\n\t\tz-index: var(--ck-z-panel);\n\t\tmax-width: var(--ck-dropdown-max-width);\n\n\t\tposition: absolute;\n\n\t\t&.ck-dropdown__panel-visible {\n\t\t\tdisplay: inline-block;\n\t\t}\n\n\t\t&.ck-dropdown__panel_ne,\n\t\t&.ck-dropdown__panel_nw,\n\t\t&.ck-dropdown__panel_n,\n\t\t&.ck-dropdown__panel_nmw,\n\t\t&.ck-dropdown__panel_nme {\n\t\t\tbottom: 100%;\n\t\t}\n\n\t\t&.ck-dropdown__panel_se,\n\t\t&.ck-dropdown__panel_sw,\n\t\t&.ck-dropdown__panel_smw,\n\t\t&.ck-dropdown__panel_sme,\n\t\t&.ck-dropdown__panel_s {\n\t\t\t/*\n\t\t\t * Using transform: translate3d( 0, 100%, 0 ) causes blurry dropdown on Chrome 67-78+ on non-retina displays.\n\t\t\t * See https://github.com/ckeditor/ckeditor5/issues/1053.\n\t\t\t */\n\t\t\ttop: 100%;\n\t\t\tbottom: auto;\n\t\t}\n\n\t\t&.ck-dropdown__panel_ne,\n\t\t&.ck-dropdown__panel_se {\n\t\t\tleft: 0px;\n\t\t}\n\n\t\t&.ck-dropdown__panel_nw,\n\t\t&.ck-dropdown__panel_sw {\n\t\t\tright: 0px;\n\t\t}\n\n\t\t&.ck-dropdown__panel_s,\n\t\t&.ck-dropdown__panel_n {\n\t\t\t/* Positioning panels relative to the center of the button */\n\t\t\tleft: 50%;\n\t\t\ttransform: translateX(-50%);\n\t\t}\n\n\t\t&.ck-dropdown__panel_nmw,\n\t\t&.ck-dropdown__panel_smw {\n\t\t\t/* Positioning panels relative to the middle-west of the button */\n\t\t\tleft: 75%;\n\t\t\ttransform: translateX(-75%);\n\t\t}\n\n\t\t&.ck-dropdown__panel_nme,\n\t\t&.ck-dropdown__panel_sme {\n\t\t\t/* Positioning panels relative to the middle-east of the button */\n\t\t\tleft: 25%;\n\t\t\ttransform: translateX(-25%);\n\t\t}\n\t}\n}\n\n/*\n * Toolbar dropdown panels should be always above the UI (eg. other dropdown panels) from the editor's content.\n * See https://github.com/ckeditor/ckeditor5/issues/7874\n */\n.ck.ck-toolbar .ck-dropdown__panel {\n\tz-index: calc( var(--ck-z-panel) + 1 );\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"../../../mixins/_rounded.css\";\n@import \"../../../mixins/_disabled.css\";\n@import \"../../../mixins/_shadow.css\";\n@import \"@ckeditor/ckeditor5-ui/theme/mixins/_dir.css\";\n\n:root {\n\t--ck-dropdown-arrow-size: calc(0.5 * var(--ck-icon-size));\n}\n\n.ck.ck-dropdown {\n\t/* Enable font size inheritance, which allows fluid UI scaling. */\n\tfont-size: inherit;\n\n\t& .ck-dropdown__arrow {\n\t\twidth: var(--ck-dropdown-arrow-size);\n\t}\n\n\t@mixin ck-dir ltr {\n\t\t& .ck-dropdown__arrow {\n\t\t\tright: var(--ck-spacing-standard);\n\n\t\t\t/* A space to accommodate the triangle. */\n\t\t\tmargin-left: var(--ck-spacing-standard);\n\t\t}\n\t}\n\n\t@mixin ck-dir rtl {\n\t\t& .ck-dropdown__arrow {\n\t\t\tleft: var(--ck-spacing-standard);\n\n\t\t\t/* A space to accommodate the triangle. */\n\t\t\tmargin-right: var(--ck-spacing-small);\n\t\t}\n\t}\n\n\t&.ck-disabled .ck-dropdown__arrow {\n\t\t@mixin ck-disabled;\n\t}\n\n\t& .ck-button.ck-dropdown__button {\n\t\t@mixin ck-dir ltr {\n\t\t\t&:not(.ck-button_with-text) {\n\t\t\t\t/* Make sure dropdowns with just an icon have the right inner spacing */\n\t\t\t\tpadding-left: var(--ck-spacing-small);\n\t\t\t}\n\t\t}\n\n\t\t@mixin ck-dir rtl {\n\t\t\t&:not(.ck-button_with-text) {\n\t\t\t\t/* Make sure dropdowns with just an icon have the right inner spacing */\n\t\t\t\tpadding-right: var(--ck-spacing-small);\n\t\t\t}\n\t\t}\n\n\t\t/* #23 */\n\t\t& .ck-button__label {\n\t\t\twidth: 7em;\n\t\t\toverflow: hidden;\n\t\t\ttext-overflow: ellipsis;\n\t\t}\n\n\t\t/* https://github.com/ckeditor/ckeditor5-theme-lark/issues/70 */\n\t\t&.ck-disabled .ck-button__label {\n\t\t\t@mixin ck-disabled;\n\t\t}\n\n\t\t/* https://github.com/ckeditor/ckeditor5/issues/816 */\n\t\t&.ck-on {\n\t\t\tborder-bottom-left-radius: 0;\n\t\t\tborder-bottom-right-radius: 0;\n\t\t}\n\n\t\t&.ck-dropdown__button_label-width_auto .ck-button__label {\n\t\t\twidth: auto;\n\t\t}\n\n\t\t/* https://github.com/ckeditor/ckeditor5/issues/8699 */\n\t\t&.ck-off:active,\n\t\t&.ck-on:active {\n\t\t\tbox-shadow: none;\n\n\t\t\t&:focus {\n\t\t\t\t@mixin ck-box-shadow var(--ck-focus-outer-shadow);\n\t\t\t}\n\t\t}\n\t}\n}\n\n.ck.ck-dropdown__panel {\n\t@mixin ck-rounded-corners;\n\t@mixin ck-drop-shadow;\n\n\tbackground: var(--ck-color-dropdown-panel-background);\n\tborder: 1px solid var(--ck-color-dropdown-panel-border);\n\tbottom: 0;\n\n\t/* Make sure the panel is at least as wide as the drop-down's button. */\n\tmin-width: 100%;\n\n\t/* Disabled corner border radius to be consistent with the .dropdown__button\n\thttps://github.com/ckeditor/ckeditor5/issues/816 */\n\t&.ck-dropdown__panel_se {\n\t\tborder-top-left-radius: 0;\n\t}\n\n\t&.ck-dropdown__panel_sw {\n\t\tborder-top-right-radius: 0;\n\t}\n\n\t&.ck-dropdown__panel_ne {\n\t\tborder-bottom-left-radius: 0;\n\t}\n\n\t&.ck-dropdown__panel_nw {\n\t\tborder-bottom-right-radius: 0;\n\t}\n\n\t&:focus {\n\t\toutline: none;\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"../../mixins/_unselectable.css\";\n\n.ck.ck-list {\n\t@mixin ck-unselectable;\n\n\tdisplay: flex;\n\tflex-direction: column;\n\n\t& .ck-list__item,\n\t& .ck-list__separator {\n\t\tdisplay: block;\n\t}\n\n\t/* Make sure that whatever child of the list item gets focus, it remains on the\n\ttop. Thanks to that, styles like box-shadow, outline, etc. are not masked by\n\tadjacent list items. */\n\t& .ck-list__item > *:focus {\n\t\tposition: relative;\n\t\tz-index: var(--ck-z-default);\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"../../../mixins/_disabled.css\";\n@import \"../../../mixins/_rounded.css\";\n@import \"../../../mixins/_shadow.css\";\n@import \"@ckeditor/ckeditor5-ui/theme/mixins/_dir.css\";\n\n.ck.ck-list {\n\t@mixin ck-rounded-corners;\n\n\tlist-style-type: none;\n\tbackground: var(--ck-color-list-background);\n\n\t/* A spacing at the beginning and end of the list */\n\tpadding: var(--ck-spacing-small) 0;\n}\n\n.ck.ck-list__item {\n\tcursor: default;\n\n\t/* Almost as wide as menu bar items. */\n\tmin-width: 15em;\n\n\t& > .ck-button:not(.ck-list-item-button) {\n\t\tpadding: var(--ck-spacing-tiny) calc(2 * var(--ck-spacing-standard));\n\t\tmin-height: unset;\n\t\twidth: 100%;\n\t\tborder-radius: 0;\n\n\t\t@mixin ck-dir ltr {\n\t\t\ttext-align: left;\n\t\t}\n\n\t\t@mixin ck-dir rtl {\n\t\t\ttext-align: right;\n\t\t}\n\n\t\t& .ck-button__label {\n\t\t\t/* https://github.com/ckeditor/ckeditor5-heading/issues/63 */\n\t\t\tline-height: calc(var(--ck-line-height-base) * var(--ck-font-size-base));\n\t\t}\n\n\t\t&:active {\n\t\t\tbox-shadow: none;\n\t\t}\n\n\t\t&.ck-on {\n\t\t\tbackground: var(--ck-color-list-button-on-background);\n\t\t\tcolor: var(--ck-color-list-button-on-text);\n\n\t\t\t&:active {\n\t\t\t\tbox-shadow: none;\n\t\t\t}\n\n\t\t\t&:hover:not(.ck-disabled) {\n\t\t\t\tbackground: var(--ck-color-list-button-on-background-focus);\n\t\t\t}\n\n\t\t\t&:focus:not(.ck-disabled) {\n\t\t\t\tborder-color: var(--ck-color-base-background);\n\t\t\t}\n\t\t}\n\n\t\t&:hover:not(.ck-disabled) {\n\t\t\tbackground: var(--ck-color-list-button-hover-background);\n\t\t}\n\t}\n\n\t/* It's unnecessary to change the background/text of a switch toggle; it has different ways\n\tof conveying its state (like the switcher) */\n\t& > .ck-button.ck-switchbutton {\n\t\t&.ck-on {\n\t\t\tbackground: var(--ck-color-list-background);\n\t\t\tcolor: inherit;\n\n\t\t\t&:hover:not(.ck-disabled) {\n\t\t\t\tbackground: var(--ck-color-list-button-hover-background);\n\t\t\t\tcolor: inherit;\n\t\t\t}\n\t\t}\n\t}\n}\n\n.ck-list .ck-list__group {\n\tpadding-top: var(--ck-spacing-medium);\n\n\t/* Lists come with an inner vertical padding. Don't duplicate it. */\n\t&:first-child {\n\t\tpadding-top: 0;\n\t}\n\n\t/* The group should have a border when it's not the first item. */\n\t*:not(.ck-hidden) ~ & {\n\t\tborder-top: 1px solid var(--ck-color-base-border);\n\t}\n\n\t& > .ck-label {\n\t\tfont-size: 11px;\n\t\tfont-weight: bold;\n\t\tpadding: var(--ck-spacing-medium) var(--ck-spacing-large) 0;\n\t}\n}\n\n.ck.ck-list__separator {\n\theight: 1px;\n\twidth: 100%;\n\tbackground: var(--ck-color-base-border);\n\n\t/* Give the separator some air */\n\tmargin: var(--ck-spacing-small) 0;\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck.ck-splitbutton {\n\t/* Enable font size inheritance, which allows fluid UI scaling. */\n\tfont-size: inherit;\n\n\t& .ck-splitbutton__action:focus {\n\t\tz-index: calc(var(--ck-z-default) + 1);\n\t}\n}\n\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"../../../mixins/_rounded.css\";\n\n:root {\n\t--ck-color-split-button-hover-background: hsl(0, 0%, 92%);\n\t--ck-color-split-button-hover-border: hsl(0, 0%, 70%);\n}\n\n.ck.ck-splitbutton {\n\t/*\n\t * Note: ck-rounded and ck-dir mixins don't go together (because they both use @nest).\n\t */\n\t&:hover > .ck-splitbutton__action,\n\t&.ck-splitbutton_open > .ck-splitbutton__action {\n\t\t[dir=\"ltr\"] & {\n\t\t\t/* Don't round the action button on the right side */\n\t\t\tborder-top-right-radius: unset;\n\t\t\tborder-bottom-right-radius: unset;\n\t\t}\n\n\t\t[dir=\"rtl\"] & {\n\t\t\t/* Don't round the action button on the left side */\n\t\t\tborder-top-left-radius: unset;\n\t\t\tborder-bottom-left-radius: unset;\n\t\t}\n\t}\n\n\t& > .ck-splitbutton__arrow {\n\t\t/* It's a text-less button and since the icon is positioned absolutely in such situation,\n\t\tit must get some arbitrary min-width. */\n\t\tmin-width: unset;\n\n\t\t[dir=\"ltr\"] & {\n\t\t\t/* Don't round the arrow button on the left side */\n\t\t\tborder-top-left-radius: unset;\n\t\t\tborder-bottom-left-radius: unset;\n\t\t}\n\n\t\t[dir=\"rtl\"] & {\n\t\t\t/* Don't round the arrow button on the right side */\n\t\t\tborder-top-right-radius: unset;\n\t\t\tborder-bottom-right-radius: unset;\n\t\t}\n\n\t\t& svg {\n\t\t\twidth: var(--ck-dropdown-arrow-size);\n\t\t}\n\t}\n\n\t/* Make sure the divider stretches 100% height of the button\n\thttps://github.com/ckeditor/ckeditor5/issues/10936 */\n\t& > .ck-splitbutton__arrow:not(:focus) {\n\t\tborder-top-width: 0px;\n\t\tborder-bottom-width: 0px;\n\t}\n\n\t/* Don't round the bottom left and right corners of the buttons when \"open\"\n\thttps://github.com/ckeditor/ckeditor5/issues/816 */\n\t&.ck-splitbutton_open {\n\t\t@mixin ck-rounded-corners {\n\t\t\t& > .ck-splitbutton__action {\n\t\t\t\tborder-bottom-left-radius: 0;\n\t\t\t}\n\n\t\t\t& > .ck-splitbutton__arrow {\n\t\t\t\tborder-bottom-right-radius: 0;\n\t\t\t}\n\t\t}\n\t}\n\n\t/* When the split button is \"open\" (the arrow is on) or being hovered, it should get some styling\n\tas a whole. The background of both buttons should stand out and there should be a visual\n\tseparation between both buttons. */\n\t&.ck-splitbutton_open,\n\t&:hover {\n\t\t/* When the split button hovered as a whole, not as individual buttons. */\n\t\t& > .ck-button:not(.ck-on):not(.ck-disabled):not(:hover) {\n\t\t\tbackground: var(--ck-color-split-button-hover-background);\n\t\t}\n\n\t\t/* Splitbutton separator needs to be set with the ::after pseudoselector\n\t\tto display properly the borders on focus */\n\t\t& > .ck-splitbutton__arrow:not(.ck-disabled)::after {\n\t\t\tcontent: '';\n\t\t\tposition: absolute;\n\t\t\twidth: 1px;\n\t\t\theight: 100%;\n\t\t\tbackground-color: var(--ck-color-split-button-hover-border);\n\t\t}\n\n\t\t/* Make sure the divider between the buttons looks fine when the button is focused */\n\t\t& > .ck-splitbutton__arrow:focus::after {\n\t\t\t--ck-color-split-button-hover-border: var(--ck-color-focus-border);\n\t\t}\n\n\t\t[dir=\"ltr\"] & {\n\t\t\t& > .ck-splitbutton__arrow:not(.ck-disabled)::after {\n\t\t\t\tleft: -1px;\n\t\t\t}\n\t\t}\n\n\t\t[dir=\"rtl\"] & {\n\t\t\t& > .ck-splitbutton__arrow:not(.ck-disabled)::after {\n\t\t\t\tright: -1px;\n\t\t\t}\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck.ck-button.ck-switchbutton {\n\t& .ck-button__toggle {\n\t\tdisplay: block;\n\n\t\t& .ck-button__toggle__inner {\n\t\t\tdisplay: block;\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"../../../mixins/_rounded.css\";\n@import \"../../../mixins/_disabled.css\";\n@import \"@ckeditor/ckeditor5-ui/theme/mixins/_dir.css\";\n\n/* Note: To avoid rendering issues (aliasing) but to preserve the responsive nature\nof the component, floating–point numbers have been used which, for the default font size\n(see: --ck-font-size-base), will generate simple integers. */\n:root {\n\t/* 34px at 13px font-size */\n\t--ck-switch-button-toggle-width: 2.6153846154em;\n\t/* 14px at 13px font-size */\n\t--ck-switch-button-toggle-inner-size: calc(1.0769230769em + 1px);\n\t--ck-switch-button-translation: calc(\n\t\tvar(--ck-switch-button-toggle-width) -\n\t\tvar(--ck-switch-button-toggle-inner-size) -\n\t\t2px /* Border */\n\t);\n\t--ck-switch-button-inner-hover-shadow: 0 0 0 5px var(--ck-color-switch-button-inner-shadow);\n}\n\n.ck.ck-button.ck-switchbutton {\n\t/* Unlike a regular button, the switch button text color and background should never change.\n\t * Changing toggle switch (background, outline) is enough to carry the information about the\n\t * state of the entire component (https://github.com/ckeditor/ckeditor5/issues/12519)\n\t */\n\t&, &:hover, &:focus, &:active, &.ck-on:hover, &.ck-on:focus, &.ck-on:active {\n\t\tcolor: inherit;\n\t\tbackground: transparent;\n\t}\n\n\t& .ck-button__label {\n\t\t@mixin ck-dir ltr {\n\t\t\t/* Separate the label from the switch */\n\t\t\tmargin-right: calc(2 * var(--ck-spacing-large));\n\t\t}\n\n\t\t@mixin ck-dir rtl {\n\t\t\t/* Separate the label from the switch */\n\t\t\tmargin-left: calc(2 * var(--ck-spacing-large));\n\t\t}\n\t}\n\n\t& .ck-button__toggle {\n\t\t@mixin ck-rounded-corners;\n\n\t\t@mixin ck-dir ltr {\n\t\t\t/* Make sure the toggle is always to the right as far as possible. */\n\t\t\tmargin-left: auto;\n\t\t}\n\n\t\t@mixin ck-dir rtl {\n\t\t\t/* Make sure the toggle is always to the left as far as possible. */\n\t\t\tmargin-right: auto;\n\t\t}\n\n\t\t/* Apply some smooth transition to the box-shadow and border. */\n\t\t/* Gently animate the background color of the toggle switch */\n\t\ttransition: background 400ms ease, box-shadow .2s ease-in-out, outline .2s ease-in-out;\n\t\tborder: 1px solid transparent;\n\t\twidth: var(--ck-switch-button-toggle-width);\n\t\tbackground: var(--ck-color-switch-button-off-background);\n\n\t\t& .ck-button__toggle__inner {\n\t\t\t@mixin ck-rounded-corners {\n\t\t\t\tborder-radius: calc(.5 * var(--ck-border-radius));\n\t\t\t}\n\n\t\t\twidth: var(--ck-switch-button-toggle-inner-size);\n\t\t\theight: var(--ck-switch-button-toggle-inner-size);\n\t\t\tbackground: var(--ck-color-switch-button-inner-background);\n\n\t\t\t/* Gently animate the inner part of the toggle switch */\n\t\t\ttransition: all 300ms ease;\n\n\t\t\t@media (prefers-reduced-motion: reduce) {\n\t\t\t\ttransition: none;\n\t\t\t}\n\t\t}\n\n\t\t&:hover {\n\t\t\tbackground: var(--ck-color-switch-button-off-hover-background);\n\n\t\t\t& .ck-button__toggle__inner {\n\t\t\t\tbox-shadow: var(--ck-switch-button-inner-hover-shadow);\n\t\t\t}\n\t\t}\n\t}\n\n\t&.ck-disabled .ck-button__toggle {\n\t\t@mixin ck-disabled;\n\t}\n\n\t/* Overriding default .ck-button:focus styles + an outline around the toogle */\n\t&:focus {\n\t\tborder-color: transparent;\n\t\toutline: none;\n\t\tbox-shadow: none;\n\n\t\t& .ck-button__toggle {\n\t\t\tbox-shadow: 0 0 0 1px var(--ck-color-base-background), 0 0 0 5px var(--ck-color-focus-outer-shadow);\n\t\t\toutline-offset: 1px;\n\t\t\toutline: var(--ck-focus-ring);\n\t\t}\n\t}\n\n\t/* stylelint-disable-next-line no-descending-specificity */\n\t&.ck-on {\n\t\t& .ck-button__toggle {\n\t\t\tbackground: var(--ck-color-switch-button-on-background);\n\n\t\t\t&:hover {\n\t\t\t\tbackground: var(--ck-color-switch-button-on-hover-background);\n\t\t\t}\n\n\t\t\t& .ck-button__toggle__inner {\n\t\t\t\t/*\n\t\t\t\t* Move the toggle switch to the right. It will be animated.\n\t\t\t\t*/\n\t\t\t\t@mixin ck-dir ltr {\n\t\t\t\t\ttransform: translateX( var( --ck-switch-button-translation ) );\n\t\t\t\t}\n\n\t\t\t\t@mixin ck-dir rtl {\n\t\t\t\t\ttransform: translateX( calc( -1 * var( --ck-switch-button-translation ) ) );\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n:root {\n\t--ck-toolbar-dropdown-max-width: 60vw;\n}\n\n.ck.ck-toolbar-dropdown > .ck-dropdown__panel {\n\t/* https://github.com/ckeditor/ckeditor5/issues/5586 */\n\twidth: max-content;\n\tmax-width: var(--ck-toolbar-dropdown-max-width);\n\n\t& .ck-button {\n\t\t&:focus {\n\t\t\tz-index: calc(var(--ck-z-default) + 1);\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck.ck-toolbar-dropdown .ck-toolbar {\n\tborder: 0;\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"../../../mixins/_rounded.css\";\n\n.ck.ck-dropdown > .ck-dropdown__panel > .ck-list {\n\t/* Disabled radius of top-left border to be consistent with .dropdown__button\n\thttps://github.com/ckeditor/ckeditor5/issues/816 */\n\t@mixin ck-rounded-corners {\n\t\tborder-top-left-radius: 0;\n\t}\n\n\t/* Make sure the button belonging to the first/last child of the list goes well with the\n\tborder radius of the entire panel. */\n\t& .ck-list__item {\n\t\t&:first-child > .ck-button {\n\t\t\t@mixin ck-rounded-corners {\n\t\t\t\tborder-top-left-radius: 0;\n\t\t\t\tborder-bottom-left-radius: 0;\n\t\t\t\tborder-bottom-right-radius: 0;\n\t\t\t}\n\t\t}\n\n\t\t&:last-child > .ck-button {\n\t\t\t@mixin ck-rounded-corners {\n\t\t\t\tborder-top-left-radius: 0;\n\t\t\t\tborder-top-right-radius: 0;\n\t\t\t}\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"../../mixins/_dir.css\";\n\n.ck.ck-list-item-button {\n\tmin-height: unset;\n\twidth: 100%;\n\tborder-radius: 0;\n\n\t@mixin ck-dir ltr {\n\t\ttext-align: left;\n\t}\n\n\t@mixin ck-dir rtl {\n\t\ttext-align: right;\n\t}\n\n\t&.ck-list-item-button_toggleable {\n\t\t@mixin ck-dir ltr {\n\t\t\tpadding-left: var(--ck-spacing-small);\n\t\t}\n\n\t\t@mixin ck-dir rtl {\n\t\t\tpadding-right: var(--ck-spacing-small);\n\t\t}\n\t}\n\n\t& .ck-list-item-button__check-holder {\n\t\tdisplay: inline-flex;\n\t\twidth: .9em;\n\t\theight: .9em;\n\n\t\t@mixin ck-dir ltr {\n\t\t\tmargin-right: var(--ck-spacing-small);\n\t\t}\n\n\t\t@mixin ck-dir rtl {\n\t\t\tmargin-left: var(--ck-spacing-small);\n\t\t}\n\t}\n\n\t& .ck-list-item-button__check-icon {\n\t\theight: 100%;\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"@ckeditor/ckeditor5-ui/theme/mixins/_dir.css\";\n\n.ck.ck-button.ck-list-item-button {\n\tpadding: var(--ck-spacing-tiny) calc(2 * var(--ck-spacing-standard));\n\n\t&,\n\t&.ck-on {\n\t\tbackground: var(--ck-color-list-background);\n\t\tcolor: var(--ck-color-text);\n\t}\n\n\t&:has(.ck-list-item-button__check-holder) {\n\t\t@mixin ck-dir ltr {\n\t\t\tpadding-left: var(--ck-spacing-small);\n\t\t}\n\n\t\t@mixin ck-dir rtl {\n\t\t\tpadding-right: var(--ck-spacing-small);\n\t\t}\n\t}\n\n\t/*\n\t * `.ck-on` class and background styling is overridden for `ck-button` in many places.\n\t * This is a workaround to make sure that the background is not overridden and uses similar\n\t * selector specificity as the other overrides.\n\t */\n\t&:hover:not(.ck-disabled),\n\t&.ck-button.ck-on:hover,\n\t&.ck-on:not(.ck-list-item-button_toggleable),\n\t&.ck-on:hover {\n\t\tbackground: var(--ck-color-list-button-hover-background);\n\n\t\t&:not(.ck-disabled) {\n\t\t\tcolor: var(--ck-color-text);\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"../../mixins/_unselectable.css\";\n\n.ck.ck-toolbar {\n\t@mixin ck-unselectable;\n\n\tdisplay: flex;\n\tflex-flow: row nowrap;\n\talign-items: center;\n\n\t& > .ck-toolbar__items {\n\t\tdisplay: flex;\n\t\tflex-flow: row wrap;\n\t\talign-items: center;\n\t\tflex-grow: 1;\n\n\t}\n\n\t& .ck.ck-toolbar__separator {\n\t\tdisplay: inline-block;\n\n\t\t/*\n\t\t * A leading or trailing separator makes no sense (separates from nothing on one side).\n\t\t * For instance, it can happen when toolbar items (also separators) are getting grouped one by one and\n\t\t * moved to another toolbar in the dropdown.\n\t\t */\n\t\t&:first-child,\n\t\t&:last-child {\n\t\t\tdisplay: none;\n\t\t}\n\t}\n\n\t& .ck-toolbar__line-break {\n\t\tflex-basis: 100%;\n\t}\n\n\t&.ck-toolbar_grouping > .ck-toolbar__items {\n\t\tflex-wrap: nowrap;\n\t}\n\n\t&.ck-toolbar_vertical > .ck-toolbar__items {\n\t\tflex-direction: column;\n\t}\n\n\t&.ck-toolbar_floating > .ck-toolbar__items {\n\t\tflex-wrap: nowrap;\n\t}\n\n\t& > .ck.ck-toolbar__grouped-dropdown {\n\t\t& > .ck-dropdown__button .ck-dropdown__arrow {\n\t\t\tdisplay: none;\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"../../../mixins/_rounded.css\";\n@import \"@ckeditor/ckeditor5-ui/theme/mixins/_dir.css\";\n\n.ck.ck-toolbar {\n\t@mixin ck-rounded-corners;\n\n\tbackground: var(--ck-color-toolbar-background);\n\tpadding: 0 var(--ck-spacing-small);\n\tborder: 1px solid var(--ck-color-toolbar-border);\n\n\t& .ck.ck-toolbar__separator {\n\t\theight: var(--ck-icon-size);\n\t\twidth: 1px;\n\t\tmin-width: 1px;\n\t\tbackground: var(--ck-color-toolbar-border);\n\n\t\t/*\n\t\t * These margins make the separators look better in balloon toolbars (when aligned with the \"tip\").\n\t\t * See https://github.com/ckeditor/ckeditor5/issues/7493.\n\t\t */\n\t\tmargin-top: var(--ck-spacing-small);\n\t\tmargin-bottom: var(--ck-spacing-small);\n\t}\n\n\t& .ck-toolbar__line-break {\n\t\theight: 0;\n\t}\n\n\t& > .ck-toolbar__items {\n\t\t& > *:not(.ck-toolbar__line-break) {\n\t\t\t/* (#11) Separate toolbar items. */\n\t\t\tmargin-right: var(--ck-spacing-small);\n\t\t}\n\n\t\t/* Don't display a separator after an empty items container, for instance,\n\t\twhen all items were grouped */\n\t\t&:empty + .ck.ck-toolbar__separator {\n\t\t\tdisplay: none;\n\t\t}\n\t}\n\n\t& > .ck-toolbar__items > *:not(.ck-toolbar__line-break),\n\t& > .ck.ck-toolbar__grouped-dropdown {\n\t\t/* Make sure items wrapped to the next line have v-spacing */\n\t\tmargin-top: var(--ck-spacing-small);\n\t\tmargin-bottom: var(--ck-spacing-small);\n\t}\n\n\t&.ck-toolbar_vertical {\n\t\t/* Items in a vertical toolbar span the entire width. */\n\t\tpadding: 0;\n\n\t\t/* Specificity matters here. See https://github.com/ckeditor/ckeditor5-theme-lark/issues/168. */\n\t\t& > .ck-toolbar__items > .ck {\n\t\t\t/* Items in a vertical toolbar should span the horizontal space. */\n\t\t\twidth: 100%;\n\n\t\t\t/* Items in a vertical toolbar should have no margin. */\n\t\t\tmargin: 0;\n\n\t\t\t/* Items in a vertical toolbar span the entire width so rounded corners are pointless. */\n\t\t\tborder-radius: 0;\n\t\t}\n\t}\n\n\t&.ck-toolbar_compact {\n\t\t/* No spacing around items. */\n\t\tpadding: 0;\n\n\t\t& > .ck-toolbar__items > * {\n\t\t\t/* Compact toolbar items have no spacing between them. */\n\t\t\tmargin: 0;\n\n\t\t\t/* \"Middle\" children should have no rounded corners. */\n\t\t\t&:not(:first-child):not(:last-child) {\n\t\t\t\tborder-radius: 0;\n\t\t\t}\n\t\t}\n\t}\n\n\t& > .ck.ck-toolbar__grouped-dropdown {\n\t\t/*\n\t\t * Dropdown button has asymmetric padding to fit the arrow.\n\t\t * This button has no arrow so let's revert that padding back to normal.\n\t\t */\n\t\t& > .ck.ck-button.ck-dropdown__button {\n\t\t\tpadding-left: var(--ck-spacing-tiny);\n\t\t}\n\t}\n\n\t/* A drop-down containing the nested toolbar with configured items. */\n\t& .ck-toolbar__nested-toolbar-dropdown {\n\t\t/* Prevent empty space in the panel when the dropdown label is visible and long but the toolbar has few items. */\n\t\t& > .ck-dropdown__panel {\n\t\t\tmin-width: auto;\n\t\t}\n\n\t\t& > .ck-button > .ck-button__label {\n\t\t\tmax-width: 7em;\n\t\t\twidth: auto;\n\t\t}\n\t}\n\n\t&:focus {\n\t\toutline: none;\n\t}\n\n\t.ck-toolbar-container & {\n\t\tborder: 0;\n\t}\n}\n\n/* stylelint-disable */\n\n/*\n * Styles for RTL toolbars.\n *\n * Note: In some cases (e.g. a decoupled editor), the toolbar has its own \"dir\"\n * because its parent is not controlled by the editor framework.\n */\n[dir=\"rtl\"] .ck.ck-toolbar,\n.ck.ck-toolbar[dir=\"rtl\"] {\n\t& > .ck-toolbar__items > .ck {\n\t\tmargin-right: 0;\n\t}\n\n\t&:not(.ck-toolbar_compact) > .ck-toolbar__items > .ck {\n\t\t/* (#11) Separate toolbar items. */\n\t\tmargin-left: var(--ck-spacing-small);\n\t}\n\n\t& > .ck-toolbar__items > .ck:last-child {\n\t\tmargin-left: 0;\n\t}\n\n\t&.ck-toolbar_compact > .ck-toolbar__items > .ck {\n\t\t/* No rounded corners on the right side of the first child. */\n\t\t&:first-child {\n\t\t\tborder-top-left-radius: 0;\n\t\t\tborder-bottom-left-radius: 0;\n\t\t}\n\n\t\t/* No rounded corners on the left side of the last child. */\n\t\t&:last-child {\n\t\t\tborder-top-right-radius: 0;\n\t\t\tborder-bottom-right-radius: 0;\n\t\t}\n\t}\n\n\t/* Separate the the separator form the grouping dropdown when some items are grouped. */\n\t& > .ck.ck-toolbar__separator {\n\t\tmargin-left: var(--ck-spacing-small);\n\t}\n\n\t/* Some spacing between the items and the separator before the grouped items dropdown. */\n\t&.ck-toolbar_grouping > .ck-toolbar__items:not(:empty):not(:only-child) {\n\t\tmargin-left: var(--ck-spacing-small);\n\t}\n}\n\n/*\n * Styles for LTR toolbars.\n *\n * Note: In some cases (e.g. a decoupled editor), the toolbar has its own \"dir\"\n * because its parent is not controlled by the editor framework.\n */\n[dir=\"ltr\"] .ck.ck-toolbar,\n.ck.ck-toolbar[dir=\"ltr\"] {\n\t& > .ck-toolbar__items > .ck:last-child {\n\t\tmargin-right: 0;\n\t}\n\n\t&.ck-toolbar_compact > .ck-toolbar__items > .ck {\n\t\t/* No rounded corners on the right side of the first child. */\n\t\t&:first-child {\n\t\t\tborder-top-right-radius: 0;\n\t\t\tborder-bottom-right-radius: 0;\n\t\t}\n\n\t\t/* No rounded corners on the left side of the last child. */\n\t\t&:last-child {\n\t\t\tborder-top-left-radius: 0;\n\t\t\tborder-bottom-left-radius: 0;\n\t\t}\n\t}\n\n\t/* Separate the the separator form the grouping dropdown when some items are grouped. */\n\t& > .ck.ck-toolbar__separator {\n\t\tmargin-right: var(--ck-spacing-small);\n\t}\n\n\t/* Some spacing between the items and the separator before the grouped items dropdown. */\n\t&.ck-toolbar_grouping > .ck-toolbar__items:not(:empty):not(:only-child) {\n\t\tmargin-right: var(--ck-spacing-small);\n\t}\n}\n\n/* stylelint-enable */\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck.ck-menu-bar__menu  {\n\t& > .ck-menu-bar__menu__button > .ck-menu-bar__menu__button__arrow {\n\t\tpointer-events: none;\n\t\tz-index: var(--ck-z-default);\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"../../../mixins/_disabled.css\";\n@import \"../../mixins/_button.css\";\n@import \"@ckeditor/ckeditor5-ui/theme/mixins/_dir.css\";\n\n.ck.ck-menu-bar__menu {\n\t/*\n\t * All menu buttons.\n\t */\n\t& > .ck-menu-bar__menu__button {\n\t\twidth: 100%;\n\n\t\t& > .ck-button__label {\n\t\t\tflex-grow: 1;\n\t\t\toverflow: hidden;\n\t\t\ttext-overflow: ellipsis;\n\t\t}\n\n\t\t&.ck-disabled > .ck-button__label {\n\t\t\t@mixin ck-disabled;\n\t\t}\n\n\t\t@mixin ck-dir ltr {\n\t\t\t&:not(.ck-button_with-text) {\n\t\t\t\tpadding-left: var(--ck-spacing-small);\n\t\t\t}\n\t\t}\n\n\t\t@mixin ck-dir rtl {\n\t\t\t&:not(.ck-button_with-text) {\n\t\t\t\tpadding-right: var(--ck-spacing-small);\n\t\t\t}\n\t\t}\n\t}\n\n\t/*\n\t * Top-level menu buttons only.\n\t */\n\t&.ck-menu-bar__menu_top-level > .ck-menu-bar__menu__button {\n\t\tpadding: var(--ck-spacing-small) var(--ck-spacing-medium);\n\t\tmin-height: unset;\n\n\t\t& .ck-button__label {\n\t\t\twidth: unset;\n\t\t\tline-height: unset;\n\n\t\t\t/*\n\t\t\t * Top-level buttons don't use ellipsis and overflow: hidden clips descenders.\n\t\t\t *  See https://github.com/ckeditor/ckeditor5/issues/17422.\n\t\t\t */\n\t\t\toverflow: visible;\n\t\t}\n\n\t\t&.ck-on {\n\t\t\tborder-bottom-left-radius: 0;\n\t\t\tborder-bottom-right-radius: 0;\n\t\t}\n\n\t\t& .ck-icon {\n\t\t\tdisplay: none;\n\t\t}\n\t}\n\n\t/*\n\t * Sub-menu buttons.\n\t */\n\t&:not(.ck-menu-bar__menu_top-level) .ck-menu-bar__menu__button {\n\t\tborder-radius: 0;\n\n\t\t& > .ck-menu-bar__menu__button__arrow {\n\t\t\twidth: var(--ck-dropdown-arrow-size);\n\n\t\t\t@mixin ck-dir ltr {\n\t\t\t\ttransform: rotate(-90deg);\n\n\t\t\t\t/* A space to accommodate the triangle. */\n\t\t\t\tmargin-left: var(--ck-spacing-standard);\n\n\t\t\t\t/* Nudge the arrow gently to the right because its center of gravity is to the left */\n\t\t\t\tmargin-right: calc(-1 * var(--ck-spacing-small));\n\t\t\t}\n\n\t\t\t@mixin ck-dir rtl {\n\t\t\t\ttransform: rotate(90deg);\n\n\t\t\t\tleft: var(--ck-spacing-standard);\n\n\t\t\t\t/* A space to accommodate the triangle. */\n\t\t\t\tmargin-right: var(--ck-spacing-small);\n\n\t\t\t\t/* Nudge the arrow gently to the left because its center of gravity is to the right (after rotation). */\n\t\t\t\tmargin-left: calc(-1 * var(--ck-spacing-small));\n\t\t\t}\n\t\t}\n\n\t\t&.ck-disabled > .ck-menu-bar__menu__button__arrow {\n\t\t\t@mixin ck-disabled;\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n:root {\n\t--ck-menu-bar-menu-max-width: 75vw;\n\t--ck-menu-bar-nested-menu-horizontal-offset: 5px;\n}\n\n.ck.ck-menu-bar__menu > .ck.ck-menu-bar__menu__panel {\n\tz-index: var(--ck-z-panel);\n\tmax-width: var(--ck-menu-bar-menu-max-width);\n\tposition: absolute;\n\n\t&.ck-menu-bar__menu__panel_position_ne,\n\t&.ck-menu-bar__menu__panel_position_nw {\n\t\tbottom: 100%;\n\t}\n\n\t&.ck-menu-bar__menu__panel_position_se,\n\t&.ck-menu-bar__menu__panel_position_sw {\n\t\ttop: 100%;\n\t\tbottom: auto;\n\t}\n\n\t&.ck-menu-bar__menu__panel_position_ne,\n\t&.ck-menu-bar__menu__panel_position_se {\n\t\tleft: 0px;\n\t}\n\n\t&.ck-menu-bar__menu__panel_position_nw,\n\t&.ck-menu-bar__menu__panel_position_sw {\n\t\tright: 0px;\n\t}\n\n\t&.ck-menu-bar__menu__panel_position_es,\n\t&.ck-menu-bar__menu__panel_position_en {\n\t\tleft: calc( 100% - var(--ck-menu-bar-nested-menu-horizontal-offset) );\n\t}\n\n\t&.ck-menu-bar__menu__panel_position_es {\n\t\ttop: 0px;\n\t}\n\n\t&.ck-menu-bar__menu__panel_position_en {\n\t\tbottom: 0px;\n\t}\n\n\t&.ck-menu-bar__menu__panel_position_ws,\n\t&.ck-menu-bar__menu__panel_position_wn {\n\t\tright: calc( 100% - var(--ck-menu-bar-nested-menu-horizontal-offset) );\n\t}\n\n\t&.ck-menu-bar__menu__panel_position_ws {\n\t\ttop: 0px;\n\t}\n\n\t&.ck-menu-bar__menu__panel_position_wn {\n\t\tbottom: 0px;\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"../../../mixins/_rounded.css\";\n@import \"../../../mixins/_shadow.css\";\n@import \"../../../mixins/_focus.css\";\n\n:root {\n\t--ck-menu-bar-menu-panel-max-width: 75vw;\n}\n\n.ck.ck-menu-bar__menu > .ck.ck-menu-bar__menu__panel {\n\t@mixin ck-rounded-corners;\n\t@mixin ck-drop-shadow;\n\n\tbackground: var(--ck-color-dropdown-panel-background);\n\tborder: 1px solid var(--ck-color-dropdown-panel-border);\n\tbottom: 0;\n\theight: fit-content;\n\tmax-width: var(--ck-menu-bar-menu-panel-max-width);\n\n\t/* Corner border radius consistent with the button. */\n\t&.ck-menu-bar__menu__panel_position_es,\n\t&.ck-menu-bar__menu__panel_position_se {\n\t\tborder-top-left-radius: 0;\n\t}\n\n\t&.ck-menu-bar__menu__panel_position_ws,\n\t&.ck-menu-bar__menu__panel_position_sw {\n\t\tborder-top-right-radius: 0;\n\t}\n\n\t&.ck-menu-bar__menu__panel_position_en,\n\t&.ck-menu-bar__menu__panel_position_ne {\n\t\tborder-bottom-left-radius: 0;\n\t}\n\n\t&.ck-menu-bar__menu__panel_position_wn,\n\t&.ck-menu-bar__menu__panel_position_nw {\n\t\tborder-bottom-right-radius: 0;\n\t}\n\n\t&:focus {\n\t\toutline: none;\n\t}\n}\n\n.ck.ck-menu-bar {\n\t& .ck-list-item-button {\n\t\t&:focus,\n\t\t&:active {\n\t\t\tborder-color: transparent;\n\t\t\tbox-shadow: none;\n\t\t}\n\t}\n\n\t&.ck-menu-bar_focus-border-enabled .ck-list-item-button {\n\t\t&:focus,\n\t\t&:active {\n\t\t\t/* Fix truncated shadows due to rendering order. */\n\t\t\tposition: relative;\n\t\t\tz-index: 2;\n\n\t\t\t@mixin ck-focus-ring;\n\t\t\t@mixin ck-box-shadow var(--ck-focus-outer-shadow);\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck.ck-menu-bar__menu {\n\tdisplay: block;\n\tposition: relative;\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck.ck-menu-bar__menu {\n\t/* Enable font size inheritance, which allows fluid UI scaling. */\n\tfont-size: inherit;\n\n\t&.ck-menu-bar__menu_top-level {\n\t\tmax-width: 100%;\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"@ckeditor/ckeditor5-ui/theme/mixins/_dir.css\";\n\n.ck.ck-menu-bar__menu {\n\t/*\n\t * List item buttons.\n\t */\n\t& .ck-button.ck-menu-bar__menu__item__button {\n\t\tborder-radius: 0;\n\n\t\t& > .ck-spinner-container,\n\t\t& > .ck-spinner-container .ck-spinner {\n\t\t\t/* These styles correspond to .ck-icon so that the spinner seamlessly replaces the icon. */\n\t\t\t--ck-toolbar-spinner-size: 20px;\n\t\t}\n\n\t\t& > .ck-spinner-container {\n\t\t\t/* This ensures margins corresponding to the .ck-icon. */\n\t\t\tfont-size: var(--ck-icon-font-size);\n\n\t\t\t@mixin ck-dir ltr {\n\t\t\t\tmargin-right: var(--ck-spacing-medium);\n\t\t\t}\n\n\t\t\t@mixin ck-dir rtl {\n\t\t\t\tmargin-left: var(--ck-spacing-medium);\n\t\t\t}\n\t\t}\n\t}\n}\n\n\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck.ck-menu-bar {\n\tdisplay: flex;\n\tflex-wrap: wrap;\n\tjustify-content: flex-start;\n\tbackground: var(--ck-color-base-background);\n\tpadding: var(--ck-spacing-small);\n\tgap: var(--ck-spacing-small);\n\tborder: 1px solid var(--ck-color-toolbar-border);\n\twidth: 100%;\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck .ck-widget {\n\t/*\n\t * Styles of the type around buttons\n\t */\n\t& .ck-widget__type-around__button {\n\t\tdisplay: block;\n\t\tposition: absolute;\n\t\toverflow: hidden;\n\t\tz-index: var(--ck-z-default);\n\n\t\t& svg {\n\t\t\tposition: absolute;\n\t\t\ttop: 50%;\n\t\t\tleft: 50%;\n\t\t\tz-index: calc(var(--ck-z-default) + 2);\n\t\t}\n\n\t\t&.ck-widget__type-around__button_before {\n\t\t\t/* Place it in the middle of the outline */\n\t\t\ttop: calc(-0.5 * var(--ck-widget-outline-thickness));\n\t\t\tleft: min(10%, 30px);\n\n\t\t\ttransform: translateY(-50%);\n\t\t}\n\n\t\t&.ck-widget__type-around__button_after {\n\t\t\t/* Place it in the middle of the outline */\n\t\t\tbottom: calc(-0.5 * var(--ck-widget-outline-thickness));\n\t\t\tright: min(10%, 30px);\n\n\t\t\ttransform: translateY(50%);\n\t\t}\n\t}\n\n\t/*\n\t * Styles for the buttons when:\n\t * - the widget is selected,\n\t * - or the button is being hovered (regardless of the widget state).\n\t */\n\t&.ck-widget_selected > .ck-widget__type-around > .ck-widget__type-around__button,\n\t& > .ck-widget__type-around > .ck-widget__type-around__button:hover {\n\t\t&::after {\n\t\t\tcontent: \"\";\n\t\t\tdisplay: block;\n\t\t\tposition: absolute;\n\t\t\ttop: 1px;\n\t\t\tleft: 1px;\n\t\t\tz-index: calc(var(--ck-z-default) + 1);\n\t\t}\n\t}\n\n\t/*\n\t * Styles for the horizontal \"fake caret\" which is displayed when the user navigates using the keyboard.\n\t */\n\t& > .ck-widget__type-around > .ck-widget__type-around__fake-caret {\n\t\tdisplay: none;\n\t\tposition: absolute;\n\t\tleft: 0;\n\t\tright: 0;\n\t}\n\n\t/*\n\t * When the widget is hovered the \"fake caret\" would normally be narrower than the\n\t * extra outline displayed around the widget. Let's extend the \"fake caret\" to match\n\t * the full width of the widget.\n\t */\n\t&:hover > .ck-widget__type-around > .ck-widget__type-around__fake-caret {\n\t\tleft: calc( -1 * var(--ck-widget-outline-thickness) );\n\t\tright: calc( -1 * var(--ck-widget-outline-thickness) );\n\t}\n\n\t/*\n\t * Styles for the horizontal \"fake caret\" when it should be displayed before the widget (backward keyboard navigation).\n\t */\n\t&.ck-widget_type-around_show-fake-caret_before > .ck-widget__type-around > .ck-widget__type-around__fake-caret {\n\t\ttop: calc( -1 * var(--ck-widget-outline-thickness) - 1px );\n\t\tdisplay: block;\n\t}\n\n\t/*\n\t * Styles for the horizontal \"fake caret\" when it should be displayed after the widget (forward keyboard navigation).\n\t */\n\t&.ck-widget_type-around_show-fake-caret_after > .ck-widget__type-around > .ck-widget__type-around__fake-caret {\n\t\tbottom: calc( -1 * var(--ck-widget-outline-thickness) - 1px );\n\t\tdisplay: block;\n\t}\n}\n\n/*\n * Integration with the read-only mode of the editor.\n */\n.ck.ck-editor__editable.ck-read-only .ck-widget__type-around {\n\tdisplay: none;\n}\n\n/*\n * Integration with the restricted editing mode (feature) of the editor.\n */\n.ck.ck-editor__editable.ck-restricted-editing_mode_restricted .ck-widget__type-around {\n\tdisplay: none;\n}\n\n/*\n * Integration with the #isEnabled property of the WidgetTypeAround plugin.\n */\n.ck.ck-editor__editable.ck-widget__type-around_disabled .ck-widget__type-around {\n\tdisplay: none;\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n:root {\n\t--ck-widget-type-around-button-size: 20px;\n\t--ck-color-widget-type-around-button-active: var(--ck-color-focus-border);\n\t--ck-color-widget-type-around-button-hover: var(--ck-color-widget-hover-border);\n\t--ck-color-widget-type-around-button-blurred-editable: var(--ck-color-widget-blurred-border);\n\t--ck-color-widget-type-around-button-radar-start-alpha: 0;\n\t--ck-color-widget-type-around-button-radar-end-alpha: .3;\n\t--ck-color-widget-type-around-button-icon: var(--ck-color-base-background);\n}\n\n@define-mixin ck-widget-type-around-button-visible {\n\topacity: 1;\n\tpointer-events: auto;\n}\n\n@define-mixin ck-widget-type-around-button-hidden {\n\topacity: 0;\n\tpointer-events: none;\n}\n\n.ck .ck-widget {\n\t/*\n\t * Styles of the type around buttons\n\t */\n\t& .ck-widget__type-around__button {\n\t\twidth: var(--ck-widget-type-around-button-size);\n\t\theight: var(--ck-widget-type-around-button-size);\n\t\tbackground: var(--ck-color-widget-type-around-button);\n\t\tborder-radius: 100px;\n\t\ttransition: opacity var(--ck-widget-handler-animation-duration) var(--ck-widget-handler-animation-curve), background var(--ck-widget-handler-animation-duration) var(--ck-widget-handler-animation-curve);\n\n\t\t@mixin ck-widget-type-around-button-hidden;\n\n\t\t@media (prefers-reduced-motion: reduce) {\n\t\t\ttransition: none;\n\t\t}\n\n\t\t& svg {\n\t\t\twidth: 10px;\n\t\t\theight: 8px;\n\t\t\ttransform: translate(-50%,-50%);\n\t\t\ttransition: transform .5s ease;\n\t\t\tmargin-top: 1px;\n\n\t\t\t@media (prefers-reduced-motion: reduce) {\n\t\t\t\ttransition: none;\n\t\t\t}\n\n\t\t\t& * {\n\t\t\t\tstroke-dasharray: 10;\n\t\t\t\tstroke-dashoffset: 0;\n\n\t\t\t\tfill: none;\n\t\t\t\tstroke: var(--ck-color-widget-type-around-button-icon);\n\t\t\t\tstroke-width: 1.5px;\n\t\t\t\tstroke-linecap: round;\n\t\t\t\tstroke-linejoin: round;\n\t\t\t}\n\n\t\t\t& line {\n\t\t\t\tstroke-dasharray: 7;\n\t\t\t}\n\t\t}\n\n\t\t&:hover {\n\t\t\t/*\n\t\t\t * Display the \"sonar\" around the button when hovered.\n\t\t\t */\n\t\t\tanimation: ck-widget-type-around-button-sonar 1s ease infinite;\n\n\t\t\t/*\n\t\t\t * Animate active button's icon.\n\t\t\t */\n\t\t\t& svg {\n\t\t\t\t& polyline {\n\t\t\t\t\tanimation: ck-widget-type-around-arrow-dash 2s linear;\n\t\t\t\t}\n\n\t\t\t\t& line {\n\t\t\t\t\tanimation: ck-widget-type-around-arrow-tip-dash 2s linear;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t@media (prefers-reduced-motion: reduce) {\n\t\t\t\tanimation: none;\n\n\t\t\t\t& svg {\n\t\t\t\t\t& polyline {\n\t\t\t\t\t\tanimation: none;\n\t\t\t\t\t}\n\n\t\t\t\t\t& line {\n\t\t\t\t\t\tanimation: none;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t/*\n\t * Show type around buttons when the widget is being hovered.\n\t */\n\t&:hover {\n\t\t& > .ck-widget__type-around > .ck-widget__type-around__button {\n\t\t\t@mixin ck-widget-type-around-button-visible;\n\t\t}\n\t}\n\n\t/*\n\t * Styles for the buttons when the widget is NOT selected (but the buttons are visible\n\t * and still can be hovered).\n\t */\n\t&:not(.ck-widget_selected) > .ck-widget__type-around > .ck-widget__type-around__button {\n\t\tbackground: var(--ck-color-widget-type-around-button-hover);\n\t}\n\n\t/*\n\t * Styles for the buttons when:\n\t * - the widget is selected,\n\t * - or the button is being hovered (regardless of the widget state).\n\t */\n\t&.ck-widget_selected > .ck-widget__type-around > .ck-widget__type-around__button,\n\t& > .ck-widget__type-around > .ck-widget__type-around__button:hover {\n\t\tbackground: var(--ck-color-widget-type-around-button-active);\n\n\t\t&::after {\n\t\t\twidth: calc(var(--ck-widget-type-around-button-size) - 2px);\n\t\t\theight: calc(var(--ck-widget-type-around-button-size) - 2px);\n\t\t\tborder-radius: 100px;\n\t\t\tbackground: linear-gradient(135deg, hsla(0,0%,100%,0) 0%, hsla(0,0%,100%,.3) 100%);\n\t\t}\n\t}\n\n\t&.ck-widget_selected {\n\t\t/*\n\t\t* Styles of the widget when the \"fake caret\" is blinking (e.g. upon keyboard navigation).\n\t\t* Despite the widget being physically selected in the model, its outline should disappear.\n\t\t*/\n\t\t&.ck-widget_type-around_show-fake-caret_before,\n\t\t&.ck-widget_type-around_show-fake-caret_after {\n\t\t\toutline-color: transparent;\n\t\t}\n\n\t\t/*\n\t\t* Show type around buttons when the widget gets selected.\n\t\t*/\n\t\t& > .ck-widget__type-around > .ck-widget__type-around__button {\n\t\t\t@mixin ck-widget-type-around-button-visible;\n\t\t}\n\t}\n\n\t/*\n\t * Styles for the \"before\" button when the widget has a selection handle. Because some space\n\t * is consumed by the handle, the button must be moved slightly to the right to let it breathe.\n\t */\n\t&.ck-widget_with-selection-handle > .ck-widget__type-around > .ck-widget__type-around__button_before {\n\t\tmargin-left: 20px;\n\t}\n\n\t/*\n\t * Styles for the horizontal \"fake caret\" which is displayed when the user navigates using the keyboard.\n\t */\n\t& .ck-widget__type-around__fake-caret {\n\t\tpointer-events: none;\n\t\theight: 1px;\n\t\tanimation: ck-widget-type-around-fake-caret-pulse linear 1s infinite normal forwards;\n\n\t\t/*\n\t\t * The semi-transparent-outline+background combo improves the contrast\n\t\t * when the background underneath the fake caret is dark.\n\t\t */\n\t\toutline: solid 1px hsla(0, 0%, 100%, .5);\n\t\tbackground: var(--ck-color-base-text);\n\t}\n\n\t&.ck-widget_type-around_show-fake-caret_before,\n\t&.ck-widget_type-around_show-fake-caret_after {\n\t\t/*\n\t\t * When the \"fake caret\" is visible we simulate that the widget is not selected\n\t\t * (despite being physically selected), so the outline color should be for the\n\t\t * unselected widget.\n\t\t */\n\t\t&.ck-widget_selected:hover {\n\t\t\toutline-color: var(--ck-color-widget-hover-border);\n\t\t}\n\n\t\t/*\n\t\t * Styles of the type around buttons when the \"fake caret\" is blinking (e.g. upon keyboard navigation).\n\t\t * In this state, the type around buttons would collide with the fake carets so they should disappear.\n\t\t */\n\t\t& > .ck-widget__type-around > .ck-widget__type-around__button {\n\t\t\t@mixin ck-widget-type-around-button-hidden;\n\t\t}\n\n\t\t/*\n\t\t * Fake horizontal caret integration with the selection handle. When the caret is visible, simply\n\t\t * hide the handle because it intersects with the caret (and does not make much sense anyway).\n\t\t */\n\t\t&.ck-widget_with-selection-handle {\n\t\t\t&.ck-widget_selected,\n\t\t\t&.ck-widget_selected:hover {\n\t\t\t\t& > .ck-widget__selection-handle {\n\t\t\t\t\topacity: 0\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t/*\n\t\t * Fake horizontal caret integration with the resize UI. When the caret is visible, simply\n\t\t * hide the resize UI because it creates too much noise. It can be visible when the user\n\t\t * hovers the widget, though.\n\t\t */\n\t\t&.ck-widget_selected.ck-widget_with-resizer > .ck-widget__resizer {\n\t\t\topacity: 0\n\t\t}\n\t}\n}\n\n/*\n * Styles for the \"before\" button when the widget has a selection handle in an RTL environment.\n * The selection handler is aligned to the right side of the widget so there is no need to create\n * additional space for it next to the \"before\" button.\n */\n.ck[dir=\"rtl\"] .ck-widget.ck-widget_with-selection-handle .ck-widget__type-around > .ck-widget__type-around__button_before {\n\tmargin-left: 0;\n\tmargin-right: 20px;\n}\n\n/*\n * Hide type around buttons when the widget is selected as a child of a selected\n * nested editable (e.g. mulit-cell table selection).\n *\n * See https://github.com/ckeditor/ckeditor5/issues/7263.\n */\n.ck-editor__nested-editable.ck-editor__editable_selected {\n\t/* stylelint-disable-next-line no-descending-specificity */\n\t& .ck-widget {\n\t\t&.ck-widget_selected,\n\t\t&:hover {\n\t\t\t& > .ck-widget__type-around > .ck-widget__type-around__button {\n\t\t\t\t@mixin ck-widget-type-around-button-hidden;\n\t\t\t}\n\t\t}\n\t}\n}\n\n/*\n * Hide type around buttons of all widget parents when the widget is being hovered.\n */\n.ck .ck-widget:has( .ck-widget.table:hover ) {\n\t& > .ck-widget__type-around > .ck-widget__type-around__button {\n\t\t@mixin ck-widget-type-around-button-hidden;\n\t}\n}\n\n/*\n * Styles for the buttons when the widget is selected but the user clicked outside of the editor (blurred the editor).\n */\n.ck-editor__editable.ck-blurred .ck-widget.ck-widget_selected > .ck-widget__type-around > .ck-widget__type-around__button:not(:hover) {\n\tbackground: var(--ck-color-widget-type-around-button-blurred-editable);\n\n\t& svg * {\n\t\tstroke: hsl(0,0%,60%);\n\t}\n}\n\n@keyframes ck-widget-type-around-arrow-dash {\n\t0% {\n\t\tstroke-dashoffset: 10;\n\t}\n\t20%, 100% {\n\t\tstroke-dashoffset: 0;\n\t}\n}\n\n@keyframes ck-widget-type-around-arrow-tip-dash {\n\t0%, 20% {\n\t\tstroke-dashoffset: 7;\n\t}\n\t40%, 100% {\n\t\tstroke-dashoffset: 0;\n\t}\n}\n\n@keyframes ck-widget-type-around-button-sonar {\n\t0% {\n\t\tbox-shadow: 0 0 0 0 hsla(var(--ck-color-focus-border-coordinates), var(--ck-color-widget-type-around-button-radar-start-alpha));\n\t}\n\t50% {\n\t\tbox-shadow: 0 0 0 5px hsla(var(--ck-color-focus-border-coordinates), var(--ck-color-widget-type-around-button-radar-end-alpha));\n\t}\n\t100% {\n\t\tbox-shadow: 0 0 0 5px hsla(var(--ck-color-focus-border-coordinates), var(--ck-color-widget-type-around-button-radar-start-alpha));\n\t}\n}\n\n@keyframes ck-widget-type-around-fake-caret-pulse {\n\t0% {\n\t\topacity: 1;\n\t}\n\t49% {\n\t\topacity: 1;\n\t}\n\t50% {\n\t\topacity: 0;\n\t}\n\t99% {\n\t\topacity: 0;\n\t}\n\t100% {\n\t\topacity: 1;\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n:root {\n\t--ck-color-resizer: var(--ck-color-focus-border);\n\t--ck-color-resizer-tooltip-background: hsl(0, 0%, 15%);\n\t--ck-color-resizer-tooltip-text: hsl(0, 0%, 95%);\n\n\t--ck-resizer-border-radius: var(--ck-border-radius);\n\t--ck-resizer-tooltip-offset: 10px;\n\t--ck-resizer-tooltip-height: calc(var(--ck-spacing-small) * 2 + 10px);\n}\n\n.ck .ck-widget {\n\t/* This is neccessary for type around UI to be positioned properly. */\n\tposition: relative;\n}\n\n.ck .ck-widget.ck-widget_with-selection-handle {\n\t/* Make the widget wrapper a relative positioning container for the drag handle. */\n\tposition: relative;\n\n\t& .ck-widget__selection-handle {\n\t\tposition: absolute;\n\n\t\t& .ck-icon {\n\t\t\t/* Make sure the icon in not a subject to font-size or line-height to avoid\n\t\t\tunnecessary spacing around it. */\n\t\t\tdisplay: block;\n\t\t}\n\t}\n\n\t/* Show the selection handle on mouse hover over the widget, but not for nested widgets. */\n\t&:hover > .ck-widget__selection-handle {\n\t\tvisibility: visible;\n\t}\n\n\t/* Show the selection handle when the widget is selected, but not for nested widgets. */\n\t&.ck-widget_selected > .ck-widget__selection-handle {\n\t\tvisibility: visible;\n\t}\n}\n\n.ck .ck-size-view {\n\tbackground: var(--ck-color-resizer-tooltip-background);\n\tcolor: var(--ck-color-resizer-tooltip-text);\n\tborder: 1px solid var(--ck-color-resizer-tooltip-text);\n\tborder-radius: var(--ck-resizer-border-radius);\n\tfont-size: var(--ck-font-size-tiny);\n\tdisplay: block;\n\tpadding: 0 var(--ck-spacing-small);\n\theight: var(--ck-resizer-tooltip-height);\n\tline-height: var(--ck-resizer-tooltip-height);\n\n\t&.ck-orientation-top-left,\n\t&.ck-orientation-top-right,\n\t&.ck-orientation-bottom-right,\n\t&.ck-orientation-bottom-left,\n\t&.ck-orientation-above-center {\n\t\tposition: absolute;\n\t}\n\n\t&.ck-orientation-top-left {\n\t\ttop: var(--ck-resizer-tooltip-offset);\n\t\tleft: var(--ck-resizer-tooltip-offset);\n\t}\n\n\t&.ck-orientation-top-right {\n\t\ttop: var(--ck-resizer-tooltip-offset);\n\t\tright: var(--ck-resizer-tooltip-offset);\n\t}\n\n\t&.ck-orientation-bottom-right {\n\t\tbottom: var(--ck-resizer-tooltip-offset);\n\t\tright: var(--ck-resizer-tooltip-offset);\n\t}\n\n\t&.ck-orientation-bottom-left {\n\t\tbottom: var(--ck-resizer-tooltip-offset);\n\t\tleft: var(--ck-resizer-tooltip-offset);\n\t}\n\n\t/* Class applied if the widget is too small to contain the size label */\n\t&.ck-orientation-above-center {\n\t\ttop: calc(var(--ck-resizer-tooltip-height) * -1);\n\t\tleft: 50%;\n\t\ttransform: translate(-50%);\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"../mixins/_focus.css\";\n@import \"../mixins/_shadow.css\";\n@import \"@ckeditor/ckeditor5-ui/theme/mixins/_mediacolors.css\";\n\n:root {\n\t--ck-widget-outline-thickness: 3px;\n\t--ck-widget-handler-icon-size: 16px;\n\t--ck-widget-handler-animation-duration: 200ms;\n\t--ck-widget-handler-animation-curve: ease;\n\n\t--ck-color-widget-blurred-border: hsl(0, 0%, 87%);\n\t--ck-color-widget-hover-border: hsl(43, 100%, 62%);\n\t--ck-color-widget-editable-focus-background: var(--ck-color-base-background);\n\t--ck-color-widget-drag-handler-icon-color: var(--ck-color-base-background);\n}\n\n.ck .ck-widget {\n\toutline-width: var(--ck-widget-outline-thickness);\n\toutline-style: solid;\n\toutline-color: transparent;\n\ttransition: outline-color var(--ck-widget-handler-animation-duration) var(--ck-widget-handler-animation-curve);\n\n\t@media (prefers-reduced-motion: reduce) {\n\t\ttransition: none;\n\t}\n\n\t&.ck-widget_selected,\n\t&.ck-widget_selected:hover {\n\t\toutline: var(--ck-widget-outline-thickness) solid var(--ck-color-focus-border);\n\t}\n\n\t&:hover {\n\t\toutline-color: var(--ck-color-widget-hover-border);\n\t}\n}\n\n.ck .ck-editor__nested-editable {\n\tborder: 1px solid transparent;\n\n\t/* The :focus style is applied before .ck-editor__nested-editable_focused class is rendered in the view.\n\tThese styles show a different border for a blink of an eye, so `:focus` need to have same styles applied. */\n\t&.ck-editor__nested-editable_focused,\n\t&:focus {\n\t\t@mixin ck-box-shadow var(--ck-inner-shadow);\n\t\t@mixin ck-media-default-colors {\n\t\t\tbackground-color: var(--ck-color-widget-editable-focus-background);\n\t\t}\n\n\t\t/**\n\t\t * Focus border should not be applied to table cells because it overrides the default table cell border color.\n\t\t * In other words - in some scenarios, the part of the table cell border has focus color style, which is not expected behavior\n\t\t * because it should be the same as the table cell border color.\n\t\t */\n\t\t&:not(td, th) {\n\t\t\t@mixin ck-focus-ring;\n\t\t}\n\t}\n}\n\n.ck .ck-widget.ck-widget_with-selection-handle {\n\t& .ck-widget__selection-handle {\n\t\tpadding: 4px;\n\t\tbox-sizing: border-box;\n\n\t\t/* Background and opacity will be animated as the handler shows up or the widget gets selected. */\n\t\tbackground-color: transparent;\n\t\topacity: 0;\n\n\t\t/* Transition:\n\t\t   * background-color for the .ck-widget_selected state change,\n\t\t   * visibility for hiding the handler,\n\t\t   * opacity for the proper look of the icon when the handler disappears. */\n\t\ttransition:\n\t\t\tbackground-color var(--ck-widget-handler-animation-duration) var(--ck-widget-handler-animation-curve),\n\t\t\tvisibility var(--ck-widget-handler-animation-duration) var(--ck-widget-handler-animation-curve),\n\t\t\topacity var(--ck-widget-handler-animation-duration) var(--ck-widget-handler-animation-curve);\n\n\t\t/* Make only top corners round. */\n\t\tborder-radius: var(--ck-border-radius) var(--ck-border-radius) 0 0;\n\n\t\t/* Place the drag handler outside the widget wrapper. */\n\t\ttransform: translateY(-100%);\n\t\tleft: calc(0px - var(--ck-widget-outline-thickness));\n\t\ttop: 0;\n\n\t\t@media (prefers-reduced-motion: reduce) {\n\t\t\ttransition: none;\n\t\t}\n\n\t\t& .ck-icon {\n\t\t\t/* Make sure the dimensions of the icon are independent of the fon-size of the content. */\n\t\t\twidth: var(--ck-widget-handler-icon-size);\n\t\t\theight: var(--ck-widget-handler-icon-size);\n\t\t\tcolor: var(--ck-color-widget-drag-handler-icon-color);\n\n\t\t\t/* The \"selected\" part of the icon is invisible by default */\n\t\t\t& .ck-icon__selected-indicator {\n\t\t\t\topacity: 0;\n\n\t\t\t\t/* Note: The animation is longer on purpose. Simply feels better. */\n\t\t\t\ttransition: opacity 300ms var(--ck-widget-handler-animation-curve);\n\n\t\t\t\t@media (prefers-reduced-motion: reduce) {\n\t\t\t\t\ttransition: none;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t/* Advertise using the look of the icon that once clicked the handler, the widget will be selected. */\n\t\t&:hover .ck-icon .ck-icon__selected-indicator {\n\t\t\topacity: 1;\n\t\t}\n\t}\n\n\t/* Show the selection handler on mouse hover over the widget, but not for nested widgets. */\n\t&:hover > .ck-widget__selection-handle {\n\t\topacity: 1;\n\t\tbackground-color: var(--ck-color-widget-hover-border);\n\t}\n\n\t/* Show the selection handler when the widget is selected, but not for nested widgets. */\n\t&.ck-widget_selected,\n\t&.ck-widget_selected:hover {\n\t\t& > .ck-widget__selection-handle {\n\t\t\topacity: 1;\n\t\t\tbackground-color: var(--ck-color-focus-border);\n\n\t\t\t/* When the widget is selected, notify the user using the proper look of the icon. */\n\t\t\t& .ck-icon .ck-icon__selected-indicator {\n\t\t\t\topacity: 1;\n\t\t\t}\n\t\t}\n\t}\n}\n\n/* In a RTL environment, align the selection handler to the right side of the widget */\n/* stylelint-disable-next-line no-descending-specificity */\n.ck[dir=\"rtl\"] .ck-widget.ck-widget_with-selection-handle .ck-widget__selection-handle {\n\tleft: auto;\n\tright: calc(0px - var(--ck-widget-outline-thickness));\n}\n\n/* https://github.com/ckeditor/ckeditor5/issues/6415 */\n.ck.ck-editor__editable.ck-read-only .ck-widget {\n\t/* Prevent the :hover outline from showing up because of the used outline-color transition. */\n\ttransition: none;\n\n\t&:not(.ck-widget_selected) {\n\t\t/* Disable visual effects of hover/active widget when CKEditor is in readOnly mode.\n\t\t * See: https://github.com/ckeditor/ckeditor5/issues/1261\n\t\t *\n\t\t * Leave the unit because this custom property is used in calc() by other features.\n\t\t * See: https://github.com/ckeditor/ckeditor5/issues/6775\n\t\t */\n\t\t--ck-widget-outline-thickness: 0px;\n\t}\n\n\t&.ck-widget_with-selection-handle {\n\t\t& .ck-widget__selection-handle,\n\t\t& .ck-widget__selection-handle:hover {\n\t\t\tbackground: var(--ck-color-widget-blurred-border);\n\t\t}\n\t}\n}\n\n/*\n* Hide the outline of all widget parents when the widget is being hovered.\n*/\n.ck .ck-widget:has( .ck-widget.table:hover ) {\n\toutline-color: transparent;\n}\n\n/* Hide the selection handle on mouse hover over the widget of all the widget parents. */\n.ck .ck-widget.ck-widget_with-selection-handle:has( .ck-widget.table:hover ) > .ck-widget__selection-handle {\n\topacity: 0;\n\tvisibility: hidden;\n}\n\n/* Style the widget when it's selected but the editable it belongs to lost focus. */\n/* stylelint-disable-next-line no-descending-specificity */\n.ck.ck-editor__editable.ck-blurred .ck-widget {\n\t&.ck-widget_selected,\n\t&.ck-widget_selected:hover {\n\t\toutline-color: var(--ck-color-widget-blurred-border);\n\n\t\t&.ck-widget_with-selection-handle {\n\t\t\t& > .ck-widget__selection-handle,\n\t\t\t& > .ck-widget__selection-handle:hover {\n\t\t\t\tbackground: var(--ck-color-widget-blurred-border);\n\t\t\t}\n\t\t}\n\t}\n}\n\n.ck.ck-editor__editable > .ck-widget.ck-widget_with-selection-handle:first-child,\n.ck.ck-editor__editable blockquote > .ck-widget.ck-widget_with-selection-handle:first-child {\n\t/* Do not crop selection handler if a widget is a first-child in the blockquote or in the root editable.\n\tIn fact, anything with overflow: hidden.\n\thttps://github.com/ckeditor/ckeditor5-block-quote/issues/28\n\thttps://github.com/ckeditor/ckeditor5-widget/issues/44\n\thttps://github.com/ckeditor/ckeditor5-widget/issues/66 */\n\tmargin-top: calc(1em + var(--ck-widget-handler-icon-size));\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck .ck-balloon-rotator__navigation {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n/* Buttons inside a toolbar should be centered when rotator bar is wider.\n * See: https://github.com/ckeditor/ckeditor5-ui/issues/495\n */\n.ck .ck-balloon-rotator__content .ck-toolbar {\n\tjustify-content: center;\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck .ck-balloon-rotator__navigation {\n\tbackground: var(--ck-color-toolbar-background);\n\tborder-bottom: 1px solid var(--ck-color-toolbar-border);\n\tpadding: 0 var(--ck-spacing-small);\n\n\t/* Let's keep similar appearance to `ck-toolbar`. */\n\t& > * {\n\t\tmargin-right: var(--ck-spacing-small);\n\t\tmargin-top: var(--ck-spacing-small);\n\t\tmargin-bottom: var(--ck-spacing-small);\n\t}\n\n\t/* Gives counter more breath than buttons. */\n\t& .ck-balloon-rotator__counter {\n\t\tmargin-right: var(--ck-spacing-standard);\n\n\t\t/* We need to use smaller margin because of previous button's right margin. */\n\t\tmargin-left: var(--ck-spacing-small);\n\t}\n}\n\n.ck .ck-balloon-rotator__content {\n\n\t/* Disable default annotation shadow inside rotator with fake panels. */\n\t& .ck.ck-annotation-wrapper {\n\t\tbox-shadow: none;\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck .ck-fake-panel {\n\tposition: absolute;\n\n\t/* Fake panels should be placed under main balloon content. */\n\tz-index: calc(var(--ck-z-panel) - 1);\n}\n\n.ck .ck-fake-panel div {\n\tposition: absolute;\n}\n\n.ck .ck-fake-panel div:nth-child( 1 ) {\n\tz-index: 2;\n}\n\n.ck .ck-fake-panel div:nth-child( 2 ) {\n\tz-index: 1;\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"../../../mixins/_shadow.css\";\n\n:root {\n\t--ck-balloon-fake-panel-offset-horizontal: 6px;\n\t--ck-balloon-fake-panel-offset-vertical: 6px;\n}\n\n/* Let's use `.ck-balloon-panel` appearance. See: balloonpanel.css. */\n.ck .ck-fake-panel div {\n\t@mixin ck-drop-shadow;\n\n\tmin-height: 15px;\n\n\tbackground: var(--ck-color-panel-background);\n\tborder: 1px solid var(--ck-color-panel-border);\n\tborder-radius: var(--ck-border-radius);\n\n\twidth: 100%;\n\theight: 100%;\n}\n\n.ck .ck-fake-panel div:nth-child( 1 ) {\n\tmargin-left: var(--ck-balloon-fake-panel-offset-horizontal);\n\tmargin-top: var(--ck-balloon-fake-panel-offset-vertical);\n}\n\n.ck .ck-fake-panel div:nth-child( 2 ) {\n\tmargin-left: calc(var(--ck-balloon-fake-panel-offset-horizontal) * 2);\n\tmargin-top: calc(var(--ck-balloon-fake-panel-offset-vertical) * 2);\n}\n.ck .ck-fake-panel div:nth-child( 3 ) {\n\tmargin-left: calc(var(--ck-balloon-fake-panel-offset-horizontal) * 3);\n\tmargin-top: calc(var(--ck-balloon-fake-panel-offset-vertical) * 3);\n}\n\n/* If balloon is positioned above element, we need to move fake panel to the top. */\n.ck .ck-balloon-panel_arrow_s + .ck-fake-panel,\n.ck .ck-balloon-panel_arrow_se + .ck-fake-panel,\n.ck .ck-balloon-panel_arrow_sw + .ck-fake-panel {\n\t--ck-balloon-fake-panel-offset-vertical: -6px;\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck .ck-widget_with-resizer {\n\t/* Make the widget wrapper a relative positioning container for the drag handle. */\n\tposition: relative;\n}\n\n.ck .ck-widget__resizer {\n\tdisplay: none;\n\tposition: absolute;\n\n\t/* The wrapper itself should not interfere with the pointer device, only the handles should. */\n\tpointer-events: none;\n\n\tleft: 0;\n\ttop: 0;\n}\n\n.ck-focused .ck-widget_with-resizer.ck-widget_selected {\n\t& > .ck-widget__resizer {\n\t\tdisplay: block;\n\t}\n}\n\n.ck .ck-widget__resizer__handle {\n\tposition: absolute;\n\n\t/* Resizers are the only UI elements that should interfere with a pointer device. */\n\tpointer-events: all;\n\n\t&.ck-widget__resizer__handle-top-left,\n\t&.ck-widget__resizer__handle-bottom-right {\n\t\tcursor: nwse-resize;\n\t}\n\n\t&.ck-widget__resizer__handle-top-right,\n\t&.ck-widget__resizer__handle-bottom-left {\n\t\tcursor: nesw-resize;\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n:root {\n\t--ck-resizer-size: 10px;\n\n\t/* Set the resizer with a 50% offset. */\n\t--ck-resizer-offset: calc( ( var(--ck-resizer-size) / -2 ) - 2px);\n\t--ck-resizer-border-width: 1px;\n}\n\n.ck .ck-widget__resizer {\n\toutline: 1px solid var(--ck-color-resizer);\n}\n\n.ck .ck-widget__resizer__handle {\n\twidth: var(--ck-resizer-size);\n\theight: var(--ck-resizer-size);\n\tbackground: var(--ck-color-focus-border);\n\tborder: var(--ck-resizer-border-width) solid hsl(0, 0%, 100%);\n\tborder-radius: var(--ck-resizer-border-radius);\n\n\t&.ck-widget__resizer__handle-top-left {\n\t\ttop: var(--ck-resizer-offset);\n\t\tleft: var(--ck-resizer-offset);\n\t}\n\n\t&.ck-widget__resizer__handle-top-right {\n\t\ttop: var(--ck-resizer-offset);\n\t\tright: var(--ck-resizer-offset);\n\t}\n\n\t&.ck-widget__resizer__handle-bottom-right {\n\t\tbottom: var(--ck-resizer-offset);\n\t\tright: var(--ck-resizer-offset);\n\t}\n\n\t&.ck-widget__resizer__handle-bottom-left {\n\t\tbottom: var(--ck-resizer-offset);\n\t\tleft: var(--ck-resizer-offset);\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck.ck-editor__editable {\n\t/*\n\t * Vertical drop target (in text).\n\t */\n\t& .ck.ck-clipboard-drop-target-position {\n\t\tdisplay: inline;\n\t\tposition: relative;\n\t\tpointer-events: none;\n\n\t\t& span {\n\t\t\tposition: absolute;\n\t\t\twidth: 0;\n\t\t}\n\t}\n\n\t/*\n\t * Styles of the widget being dragged (its preview).\n\t */\n\t& .ck-widget:-webkit-drag {\n\t\t& > .ck-widget__selection-handle {\n\t\t\tdisplay: none;\n\t\t}\n\n\t\t& > .ck-widget__type-around {\n\t\t\tdisplay: none;\n\t\t}\n\t}\n}\n\n.ck.ck-clipboard-drop-target-line {\n\tposition: absolute;\n\tpointer-events: none;\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"@ckeditor/ckeditor5-ui/theme/mixins/_dir.css\";\n\n:root {\n\t--ck-clipboard-drop-target-dot-width: 12px;\n\t--ck-clipboard-drop-target-dot-height: 8px;\n\t--ck-clipboard-drop-target-color: var(--ck-color-focus-border);\n}\n\n.ck.ck-editor__editable {\n\t/*\n\t * Vertical drop target (in text).\n\t */\n\t& .ck.ck-clipboard-drop-target-position {\n\t\t& span {\n\t\t\tbottom: calc(-.5 * var(--ck-clipboard-drop-target-dot-height));\n\t\t\ttop: calc(-.5 * var(--ck-clipboard-drop-target-dot-height));\n\t\t\tborder: 1px solid var(--ck-clipboard-drop-target-color);\n\t\t\tbackground: var(--ck-clipboard-drop-target-color);\n\t\t\tmargin-left: -1px;\n\n\t\t\t/* The triangle above the marker */\n\t\t\t&::after {\n\t\t\t\tcontent: '';\n\t\t\t\twidth: 0;\n\t\t\t\theight: 0;\n\n\t\t\t\tdisplay: block;\n\t\t\t\tposition: absolute;\n\t\t\t\tleft: 50%;\n\t\t\t\ttop: calc(-.5 * var(--ck-clipboard-drop-target-dot-height));\n\n\t\t\t\ttransform: translateX(-50%);\n\t\t\t\tborder-color: var(--ck-clipboard-drop-target-color) transparent transparent transparent;\n\t\t\t\tborder-width: calc(var(--ck-clipboard-drop-target-dot-height)) calc(.5 * var(--ck-clipboard-drop-target-dot-width)) 0 calc(.5 * var(--ck-clipboard-drop-target-dot-width));\n\t\t\t\tborder-style: solid;\n\t\t\t}\n\t\t}\n\t}\n\n\t/*\n\t * Styles of the widget that it a drop target.\n\t */\n\t& .ck-widget.ck-clipboard-drop-target-range {\n\t\toutline: var(--ck-widget-outline-thickness) solid var(--ck-clipboard-drop-target-color) !important;\n\t}\n\n\t/*\n\t * Styles of the widget being dragged (its preview).\n\t */\n\t& .ck-widget:-webkit-drag {\n\t\tzoom: 0.6;\n\t\toutline: none !important;\n\t}\n}\n\n.ck.ck-clipboard-drop-target-line {\n\theight: 0;\n\tborder: 1px solid var(--ck-clipboard-drop-target-color);\n\tbackground: var(--ck-clipboard-drop-target-color);\n\tmargin-top: -1px;\n\n\t&::before {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\ttop: calc(-.5 * var(--ck-clipboard-drop-target-dot-width));\n\t\twidth: 0;\n\t\theight: 0;\n\t\tborder-style: solid;\n\n\t\t@mixin ck-dir ltr {\n\t\t\tleft: -1px;\n\n\t\t\tborder-width: calc(.5 * var(--ck-clipboard-drop-target-dot-width)) 0 calc(.5 * var(--ck-clipboard-drop-target-dot-width)) var(--ck-clipboard-drop-target-dot-height);\n\t\t\tborder-color: transparent transparent transparent var(--ck-clipboard-drop-target-color);\n\t\t}\n\n\t\t@mixin ck-dir rtl {\n\t\t\tright: -1px;\n\n\t\t\tborder-width:calc(.5 * var(--ck-clipboard-drop-target-dot-width)) var(--ck-clipboard-drop-target-dot-height) calc(.5 * var(--ck-clipboard-drop-target-dot-width)) 0;\n\t\t\tborder-color: transparent var(--ck-clipboard-drop-target-color) transparent transparent;\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"../../../mixins/_focus.css\";\n@import \"../../../mixins/_shadow.css\";\n\n:root {\n\t--ck-accessibility-help-dialog-max-width: 600px;\n\t--ck-accessibility-help-dialog-max-height: 400px;\n\t--ck-accessibility-help-dialog-border-color: hsl(220, 6%, 81%);\n\t--ck-accessibility-help-dialog-code-background-color: hsl(0deg 0% 92.94%);\n\t--ck-accessibility-help-dialog-kbd-shadow-color: hsl(0deg 0% 61%);\n}\n\n.ck.ck-accessibility-help-dialog .ck-accessibility-help-dialog__content {\n\tpadding: var(--ck-spacing-large);\n\tmax-width: var(--ck-accessibility-help-dialog-max-width);\n\tmax-height: var(--ck-accessibility-help-dialog-max-height);\n\toverflow: auto;\n\tuser-select: text;\n\n\tborder: 1px solid transparent;\n\n\t&:focus {\n\t\t@mixin ck-focus-ring;\n\t\t@mixin ck-box-shadow var(--ck-focus-outer-shadow);\n\t}\n\n\t* {\n\t\twhite-space: normal;\n\t}\n\n\t/* Hide the main label of the content container. */\n\t& .ck-label {\n\t\tdisplay: none;\n\t}\n\n\t& h3 {\n\t\tfont-weight: bold;\n\t\tfont-size: 1.2em;\n\t}\n\n\t& h4 {\n\t\tfont-weight: bold;\n\t\tfont-size: 1em;\n\t}\n\n\t& p,\n\t& h3,\n\t& h4,\n\t& table {\n\t\tmargin: 1em 0;\n\t}\n\n\t& dl {\n\t\tdisplay: grid;\n\t\tgrid-template-columns: 2fr 1fr;\n\t\tborder-top: 1px solid var(--ck-accessibility-help-dialog-border-color);\n\t\tborder-bottom: none;\n\n\t\t& dt, & dd {\n\t\t\tborder-bottom: 1px solid var(--ck-accessibility-help-dialog-border-color);\n\t\t\tpadding: .4em 0;\n\t\t}\n\n\t\t& dt {\n\t\t\tgrid-column-start: 1;\n\t\t}\n\n\t\t& dd {\n\t\t\tgrid-column-start: 2;\n\t\t\ttext-align: right;\n\t\t}\n\t}\n\n\t& kbd, & code {\n\t\tdisplay: inline-block;\n\t\tbackground: var(--ck-accessibility-help-dialog-code-background-color);\n\t\tpadding: .4em;\n\t\tvertical-align: middle;\n\t\tline-height: 1;\n\t\tborder-radius: 2px;\n\t\ttext-align: center;\n\t\tfont-size: .9em;\n\t}\n\n\t& code {\n\t\tfont-family: monospace;\n\t}\n\n\t& kbd {\n\t\tmin-width: 1.8em;\n\t\tbox-shadow: 0px 1px 1px var(--ck-accessibility-help-dialog-kbd-shadow-color);\n\t\tmargin: 0 1px;\n\n\t\t& + kbd {\n\t\t\tmargin-left: 2px;\n\t\t}\n\t}\n}\n\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck-content code {\n\tbackground-color: hsla(0, 0%, 78%, 0.3);\n\tpadding: .15em;\n\tborder-radius: 2px;\n}\n\n.ck.ck-editor__editable .ck-code_selected  {\n\tbackground-color: hsla(0, 0%, 78%, 0.5);\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck-content blockquote {\n\t/* See #12 */\n\toverflow: hidden;\n\n\t/* https://github.com/ckeditor/ckeditor5-block-quote/issues/15 */\n\tpadding-right: 1.5em;\n\tpadding-left: 1.5em;\n\n\tmargin-left: 0;\n\tmargin-right: 0;\n\tfont-style: italic;\n\tborder-left: solid 5px hsl(0, 0%, 80%);\n}\n\n.ck-content[dir=\"rtl\"] blockquote {\n\tborder-left: 0;\n\tborder-right: solid 5px hsl(0, 0%, 80%);\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck.ck-heading_heading1 .ck-button__label {\n\tfont-size: 20px;\n}\n\n.ck.ck-heading_heading2 .ck-button__label {\n\tfont-size: 17px;\n}\n\n.ck.ck-heading_heading3 .ck-button__label {\n\tfont-size: 14px;\n}\n\n.ck[class*=\"ck-heading_heading\"] {\n\tfont-weight: bold;\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n/* Resize dropdown's button label. */\n.ck.ck-dropdown.ck-heading-dropdown {\n\t& .ck-dropdown__button .ck-button__label {\n\t\twidth: 8em;\n\t}\n\n\t& .ck-dropdown__panel .ck-list__item {\n\t\tmin-width: 18em;\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"@ckeditor/ckeditor5-ui/theme/mixins/_rwd.css\";\n\n:root {\n\t--ck-text-alternative-form-width: 340px;\n}\n\n@mixin ck-media-phone {\n\t:root {\n\t\t--ck-text-alternative-form-width: 300px;\n\t}\n}\n\n.ck.ck-text-alternative-form {\n\t&.ck-responsive-form {\n\t\twidth: var(--ck-text-alternative-form-width);\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@define-mixin ck-media-phone {\n\t@media screen and (max-width: 600px) {\n\t\t@mixin-content;\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"@ckeditor/ckeditor5-ui/theme/mixins/_rwd.css\";\n\n.ck-vertical-form .ck-button {\n\t&::after {\n\t\tcontent: \"\";\n\t\twidth: 0;\n\t\tposition: absolute;\n\t\tright: -1px;\n\t\ttop: -1px;\n\t\tbottom: -1px;\n\t\tz-index: 1;\n\t}\n\n\t&:focus::after {\n\t\tdisplay: none;\n\t}\n}\n\n.ck.ck-responsive-form {\n\t@mixin ck-media-phone {\n\t\t& .ck-button {\n\t\t\t&::after {\n\t\t\t\tcontent: \"\";\n\t\t\t\twidth: 0;\n\t\t\t\tposition: absolute;\n\t\t\t\tright: -1px;\n\t\t\t\ttop: -1px;\n\t\t\t\tbottom: -1px;\n\t\t\t\tz-index: 1;\n\t\t\t}\n\n\t\t\t&:focus::after {\n\t\t\t\tdisplay: none;\n\t\t\t}\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"@ckeditor/ckeditor5-ui/theme/mixins/_rwd.css\";\n@import \"@ckeditor/ckeditor5-ui/theme/mixins/_dir.css\";\n\n.ck-vertical-form > .ck-button:nth-last-child(2)::after {\n\tborder-right: 1px solid var(--ck-color-base-border);\n}\n\n.ck.ck-responsive-form {\n\tpadding: var(--ck-spacing-large);\n\n\t&:focus {\n\t\t/* See: https://github.com/ckeditor/ckeditor5/issues/4773 */\n\t\toutline: none;\n\t}\n\n\t@mixin ck-dir ltr {\n\t\t& > :not(:first-child) {\n\t\t\tmargin-left: var(--ck-spacing-standard);\n\t\t}\n\t}\n\n\t@mixin ck-dir rtl {\n\t\t& > :not(:last-child) {\n\t\t\tmargin-left: var(--ck-spacing-standard);\n\t\t}\n\t}\n\n\t@mixin ck-media-phone {\n\t\tpadding: 0;\n\t\twidth: calc(.8 * var(--ck-input-width));\n\n\t\t& .ck-labeled-field-view {\n\t\t\tmargin: var(--ck-spacing-large) var(--ck-spacing-large) 0;\n\n\t\t\t& .ck-input-text,\n\t\t\t& .ck-input-number {\n\t\t\t\tmin-width: 0;\n\t\t\t\twidth: 100%;\n\t\t\t}\n\n\t\t\t/* Let the long error messages wrap in the narrow form. */\n\t\t\t& .ck-labeled-field-view__error {\n\t\t\t\twhite-space: normal;\n\t\t\t}\n\t\t}\n\n\t\t/* Styles for two last buttons in the form (save&cancel, edit&unlink, etc.). */\n\t\t& > .ck-button:nth-last-child(2) {\n\t\t\t&::after {\n\t\t\t\tborder-right: 1px solid var(--ck-color-base-border);\n\t\t\t}\n\t\t}\n\n\t\t& > .ck-button:nth-last-child(1),\n\t\t& > .ck-button:nth-last-child(2) {\n\t\t\tpadding: var(--ck-spacing-standard);\n\t\t\tmargin-top: var(--ck-spacing-large);\n\t\t\tborder-radius: 0;\n\n\t\t\t&:not(:focus) {\n\t\t\t\tborder-top: 1px solid var(--ck-color-base-border);\n\t\t\t}\n\n\t\t\t@mixin ck-dir ltr {\n\t\t\t\tmargin-left: 0;\n\t\t\t}\n\n\t\t\t@mixin ck-dir rtl {\n\t\t\t\tmargin-left: 0;\n\n\t\t\t\t&:last-of-type {\n\t\t\t\t\tborder-right: 1px solid var(--ck-color-base-border);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"@ckeditor/ckeditor5-ui/theme/mixins/_rwd.css\";\n@import \"@ckeditor/ckeditor5-ui/theme/mixins/_dir.css\";\n\n:root {\n\t--ck-form-default-width: 340px;\n}\n\n.ck.ck-form {\n\tpadding: 0 0 var(--ck-spacing-large);\n\n\t&.ck-form_default-width {\n\t\twidth: var(--ck-form-default-width);\n\t}\n\n\t&:focus {\n\t\toutline: none;\n\t}\n\n\t& .ck.ck-input-text,\n\t& .ck.ck-input-number {\n\t\tmin-width: 100%;\n\t\twidth: 0;\n\t}\n\n\t& .ck.ck-dropdown {\n\t\tmin-width: 100%;\n\n\t\t& .ck-dropdown__button {\n\t\t\t&:not(:focus) {\n\t\t\t\tborder: 1px solid var(--ck-color-base-border);\n\t\t\t}\n\n\t\t\t& .ck-button__label {\n\t\t\t\twidth: 100%;\n\t\t\t}\n\t\t}\n\t}\n\n\t/* Default `ck-responsive-form` customization when it lies next to `ck-form` class */\n\t&.ck-responsive-form {\n\t\t& .ck.ck-form__row {\n\t\t\t&.ck-form__row_with-submit {\n\t\t\t\t@mixin ck-media-phone {\n\t\t\t\t\tflex-direction: column;\n\t\t\t\t\talign-items: stretch;\n\t\t\t\t\tpadding: 0;\n\t\t\t\t}\n\n\t\t\t\t> .ck {\n\t\t\t\t\t@mixin ck-media-phone {\n\t\t\t\t\t\tmargin: var(--ck-spacing-large) var(--ck-spacing-large) 0;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.ck-button_with-text {\n\t\t\t\t\t@mixin ck-media-phone {\n\t\t\t\t\t\tjustify-content: center;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&.ck-form__row_large-bottom-padding {\n\t\t\t\t@mixin ck-media-phone {\n\t\t\t\t\tpadding-bottom: var(--ck-spacing-large);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t@mixin ck-dir ltr {\n\t\t\t& > :not(:first-child) {\n\t\t\t\tmargin-left: 0;\n\t\t\t}\n\t\t}\n\n\t\t@mixin ck-dir rtl {\n\t\t\t& > :not(:last-child) {\n\t\t\t\tmargin-left: 0;\n\t\t\t}\n\t\t}\n\t}\n\t/* End of `ck-responsive-form` customization */\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck.ck-form__row {\n\tdisplay: flex;\n\tflex-direction: row;\n\tjustify-content: space-between;\n\talign-items: flex-start;\n\tpadding: var(--ck-spacing-standard) var(--ck-spacing-large) 0;\n\n\t&.ck-form__row_large-top-padding {\n\t\tpadding-top: var(--ck-spacing-large);\n\t}\n\n\t&.ck-form__row_large-bottom-padding {\n\t\tpadding-bottom: var(--ck-spacing-large);\n\t}\n\n\t&.ck-form__row_with-submit {\n\t\tflex-wrap: nowrap;\n\n\t\t& > *:not(:first-child) {\n\t\t\tmargin-inline-start: var(--ck-spacing-standard);\n\t\t}\n\t}\n\n\t& > .ck.ck-form__row {\n\t\tpadding: 0;\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck.ck-labeled-field-view {\n\t& > .ck.ck-labeled-field-view__input-wrapper {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t}\n\n\t& .ck.ck-label {\n\t\tdisplay: block;\n\t\tposition: absolute;\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"@ckeditor/ckeditor5-ui/theme/mixins/_dir.css\";\n@import \"../../../mixins/_rounded.css\";\n\n:root {\n\t--ck-labeled-field-view-transition: .1s cubic-bezier(0, 0, 0.24, 0.95);\n\t--ck-labeled-field-empty-unfocused-max-width: 100% - 2 * var(--ck-spacing-medium);\n\t--ck-labeled-field-label-default-position-x: var(--ck-spacing-medium);\n\t--ck-labeled-field-label-default-position-y: calc(0.6 * var(--ck-font-size-base));\n\t--ck-color-labeled-field-label-background: var(--ck-color-base-background);\n}\n\n.ck.ck-labeled-field-view {\n\t@mixin ck-rounded-corners;\n\n\t& > .ck.ck-labeled-field-view__input-wrapper {\n\t\twidth: 100%;\n\n\t\t& > .ck.ck-label {\n\t\t\ttop: 0px;\n\n\t\t\t@mixin ck-dir ltr {\n\t\t\t\tleft: 0px;\n\t\t\t\ttransform-origin: 0 0;\n\t\t\t\t/* By default, display the label scaled down above the field. */\n\t\t\t\ttransform: translate(var(--ck-spacing-medium), -6px) scale(.75);\n\t\t\t}\n\n\t\t\t@mixin ck-dir rtl {\n\t\t\t\tright: 0px;\n\t\t\t\ttransform-origin: 100% 0;\n\t\t\t\ttransform: translate(calc(-1 * var(--ck-spacing-medium)), -6px) scale(.75);\n\t\t\t}\n\n\t\t\tpointer-events: none;\n\n\t\t\tbackground: var(--ck-color-labeled-field-label-background);\n\t\t\tpadding: 0 calc(.5 * var(--ck-font-size-tiny));\n\t\t\tline-height: initial;\n\t\t\tfont-weight: normal;\n\n\t\t\t/* Prevent overflow when the label is longer than the input */\n\t\t\ttext-overflow: ellipsis;\n\t\t\toverflow: hidden;\n\n\t\t\tmax-width: 100%;\n\n\t\t\ttransition:\n\t\t\t\ttransform var(--ck-labeled-field-view-transition),\n\t\t\t\tpadding var(--ck-labeled-field-view-transition),\n\t\t\t\tbackground var(--ck-labeled-field-view-transition);\n\n\t\t\t@media (prefers-reduced-motion: reduce) {\n\t\t\t\ttransition: none;\n\t\t\t}\n\t\t}\n\t}\n\n\t&.ck-error {\n\t\t& > .ck.ck-labeled-field-view__input-wrapper > .ck.ck-label {\n\t\t\tcolor: var(--ck-color-base-error);\n\t\t}\n\n\t\t& .ck-input:not([readonly]) + .ck.ck-label {\n\t\t\tcolor: var(--ck-color-base-error);\n\t\t}\n\t}\n\n\t& .ck-labeled-field-view__status {\n\t\tfont-size: var(--ck-font-size-small);\n\t\tmargin-top: var(--ck-spacing-small);\n\n\t\t/* Let the info wrap to the next line to avoid stretching the layout horizontally.\n\t\tThe status could be very long. */\n\t\twhite-space: normal;\n\n\t\t&.ck-labeled-field-view__status_error {\n\t\t\tcolor: var(--ck-color-base-error);\n\t\t}\n\t}\n\n\t/* Disabled fields and fields that have no focus should fade out. */\n\t&.ck-disabled > .ck.ck-labeled-field-view__input-wrapper > .ck.ck-label,\n\t&.ck-labeled-field-view_empty:not(.ck-labeled-field-view_focused) > .ck.ck-labeled-field-view__input-wrapper > .ck.ck-label {\n\t\tcolor: var(--ck-color-input-disabled-text);\n\t}\n\n\t/* Fields that are disabled or not focused and without a placeholder should have full-sized labels. */\n\t/* stylelint-disable-next-line no-descending-specificity */\n\t&.ck-disabled.ck-labeled-field-view_empty:not(.ck-labeled-field-view_placeholder) > .ck.ck-labeled-field-view__input-wrapper > .ck.ck-label,\n\t&.ck-labeled-field-view_empty:not(.ck-labeled-field-view_focused):not(.ck-labeled-field-view_placeholder):not(.ck-error) > .ck.ck-labeled-field-view__input-wrapper > .ck.ck-label {\n\t\t@mixin ck-dir ltr {\n\t\t\ttransform: translate(var(--ck-labeled-field-label-default-position-x), var(--ck-labeled-field-label-default-position-y)) scale(1);\n\t\t}\n\n\t\t@mixin ck-dir rtl {\n\t\t\ttransform: translate(calc(-1 * var(--ck-labeled-field-label-default-position-x)), var(--ck-labeled-field-label-default-position-y)) scale(1);\n\t\t}\n\n\t\t/* Compensate for the default translate position. */\n\t\tmax-width: calc(var(--ck-labeled-field-empty-unfocused-max-width));\n\n\t\tbackground: transparent;\n\t\tpadding: 0;\n\t}\n\n\t/*------ DropdownView integration ----------------------------------------------------------------------------------- */\n\n\t/* Make sure dropdown' background color in any of dropdown's state does not collide with labeled field. */\n\t& > .ck.ck-labeled-field-view__input-wrapper > .ck-dropdown > .ck.ck-button {\n\t\tbackground: transparent;\n\t}\n\n\t/* When the dropdown is \"empty\", the labeled field label replaces its label. */\n\t&.ck-labeled-field-view_empty > .ck.ck-labeled-field-view__input-wrapper > .ck-dropdown > .ck-button > .ck-button__label {\n\t\topacity: 0;\n\t}\n\n\t/* Make sure the label of the empty, unfocused input does not cover the dropdown arrow. */\n\t&.ck-labeled-field-view_empty:not(.ck-labeled-field-view_focused):not(.ck-labeled-field-view_placeholder) > .ck.ck-labeled-field-view__input-wrapper > .ck-dropdown + .ck-label {\n\t\tmax-width: calc(var(--ck-labeled-field-empty-unfocused-max-width) - var(--ck-dropdown-arrow-size) - var(--ck-spacing-standard));\n\t}\n\n\t&.ck-labeled-field-view_full-width {\n\t\tflex-grow: 1;\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"../../../mixins/_rounded.css\";\n@import \"../../../mixins/_focus.css\";\n@import \"../../../mixins/_shadow.css\";\n\n:root {\n\t--ck-input-width: 18em;\n\n\t/* Backward compatibility. */\n\t--ck-input-text-width: var(--ck-input-width);\n}\n\n.ck.ck-input {\n\t@mixin ck-rounded-corners;\n\n\tbackground: var(--ck-color-input-background);\n\tborder: 1px solid var(--ck-color-input-border);\n\tpadding: var(--ck-spacing-extra-tiny) var(--ck-spacing-medium);\n\tmin-width: var(--ck-input-width);\n\n\t/* This is important to stay of the same height as surrounding buttons */\n\tmin-height: var(--ck-ui-component-min-height);\n\n\t/* Apply some smooth transition to the box-shadow and border. */\n\ttransition: box-shadow .1s ease-in-out, border .1s ease-in-out;\n\n\t@media (prefers-reduced-motion: reduce) {\n\t\ttransition: none;\n\t}\n\n\t&:focus {\n\t\t@mixin ck-focus-ring;\n\t\t@mixin ck-box-shadow var(--ck-focus-outer-shadow);\n\t}\n\n\t&[readonly] {\n\t\tborder: 1px solid var(--ck-color-input-disabled-border);\n\t\tbackground: var(--ck-color-input-disabled-background);\n\t\tcolor: var(--ck-color-input-disabled-text);\n\n\t\t&:focus {\n\t\t\t/* The read-only input should have a slightly less visible shadow when focused. */\n\t\t\t@mixin ck-box-shadow var(--ck-focus-disabled-outer-shadow);\n\t\t}\n\t}\n\n\t&.ck-error {\n\t\tborder-color: var(--ck-color-input-error-border);\n\t\tanimation: ck-input-shake .3s ease both;\n\n\t\t@media (prefers-reduced-motion: reduce) {\n\t\t\tanimation: none;\n\t\t}\n\n\t\t&:focus {\n\t\t\t@mixin ck-box-shadow var(--ck-focus-error-outer-shadow);\n\t\t}\n\t}\n}\n\n@keyframes ck-input-shake {\n\t20% {\n\t\ttransform: translateX(-2px);\n\t}\n\n\t40% {\n\t\ttransform: translateX(2px);\n\t}\n\n\t60% {\n\t\ttransform: translateX(-1px);\n\t}\n\n\t80% {\n\t\ttransform: translateX(1px);\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck.ck-editor__editable {\n\t& img.image_placeholder {\n\t\tbackground-size: 100% 100%;\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck.ck-image-insert-url {\n\twidth: 400px;\n\tpadding: var(--ck-spacing-large) var(--ck-spacing-large) 0;\n\n\t& .ck-image-insert-url__action-row {\n\t\tdisplay: grid;\n\t\tgrid-template-columns: repeat(2, 1fr);\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"@ckeditor/ckeditor5-ui/theme/mixins/_dir.css\";\n\n:root {\n\t--ck-image-insert-insert-by-url-width: 250px;\n}\n\n.ck.ck-image-insert-url {\n\t--ck-input-width: 100%;\n\n\t& .ck-image-insert-url__action-row {\n\t\tgrid-column-gap: var(--ck-spacing-large);\n\t\tmargin-top: var(--ck-spacing-large);\n\n\t\t& .ck-button-save,\n\t\t& .ck-button-cancel {\n\t\t\tjustify-content: center;\n\t\t\tmin-width: auto;\n\t\t}\n\n\t\t& .ck-button .ck-button__label {\n\t\t\tcolor: var(--ck-color-text);\n\t\t}\n\t}\n}\n\n.ck.ck-image-insert-form {\n\t& > .ck.ck-button {\n\t\tdisplay: block;\n\t\twidth: 100%;\n\n\t\t@mixin ck-dir ltr {\n\t\t\ttext-align: left;\n\t\t}\n\n\t\t@mixin ck-dir rtl {\n\t\t\ttext-align: right;\n\t\t}\n\t}\n\n\t& > .ck.ck-collapsible {\n\t\t&:not(:first-child) {\n\t\t\tborder-top: 1px solid var(--ck-color-base-border);\n\t\t}\n\n\t\t&:not(:last-child) {\n\t\t\tborder-bottom: 1px solid var(--ck-color-base-border);\n\t\t}\n\n\t\tmin-width: var(--ck-image-insert-insert-by-url-width);\n\t}\n\n\t/* This is the case when there are no other integrations configured than insert by URL */\n\t& > .ck.ck-image-insert-url {\n\t\tmin-width: var(--ck-image-insert-insert-by-url-width);\n\t\tpadding: var(--ck-spacing-large);\n\t}\n\n\t&:focus {\n\t\toutline: none;\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck.ck-collapsible.ck-collapsible_collapsed {\n\t& > .ck-collapsible__children {\n\t\tdisplay: none;\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n:root {\n\t--ck-collapsible-arrow-size: calc(0.5 * var(--ck-icon-size));\n}\n\n.ck.ck-collapsible {\n\t& > .ck.ck-button {\n\t\twidth: 100%;\n\t\tfont-weight: bold;\n\t\tborder-radius: 0;\n\t\tcolor: inherit;\n\n\t\t&:focus {\n\t\t\tbackground: transparent;\n\t\t}\n\n\t\t&:active, &:not(:focus), &:hover:not(:focus) {\n\t\t\tbackground: transparent;\n\t\t\tborder-color: transparent;\n\t\t\tbox-shadow: none;\n\t\t}\n\n\t\t& > .ck-icon {\n\t\t\tmargin-right: var(--ck-spacing-medium);\n\t\t\twidth: var(--ck-collapsible-arrow-size);\n\t\t}\n\t}\n\n\t& > .ck-collapsible__children {\n\t\tpadding: var(--ck-spacing-medium) var(--ck-spacing-large) var(--ck-spacing-large);\n\t}\n\n\t&.ck-collapsible_collapsed {\n\t\t& > .ck.ck-button .ck-icon {\n\t\t\ttransform: rotate(-90deg);\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck-content {\n\t& .image {\n\t\tdisplay: table;\n\t\tclear: both;\n\t\ttext-align: center;\n\n\t\t/* Make sure there is some space between the content and the image. Center image by default. */\n\t\t/* The first value should be equal to --ck-spacing-large variable if used in the editor context\n\t \tto avoid the content jumping (See https://github.com/ckeditor/ckeditor5/issues/9825). */\n\t\tmargin: 0.9em auto;\n\n\t\t/* Make sure the caption will be displayed properly (See: https://github.com/ckeditor/ckeditor5/issues/1870). */\n\t\tmin-width: 50px;\n\n\t\t& img {\n\t\t\t/* Prevent unnecessary margins caused by line-height (see #44). */\n\t\t\tdisplay: block;\n\n\t\t\t/* Center the image if its width is smaller than the content's width. */\n\t\t\tmargin: 0 auto;\n\n\t\t\t/* Make sure the image never exceeds the size of the parent container (ckeditor/ckeditor5-ui#67). */\n\t\t\tmax-width: 100%;\n\n\t\t\t/* Make sure the image is never smaller than the parent container (See: https://github.com/ckeditor/ckeditor5/issues/9300). */\n\t\t\tmin-width: 100%;\n\n\t\t\t/* Keep proportions of the block image if the height is set and the image is wider than the editor width.\n\t\t\tSee https://github.com/ckeditor/ckeditor5/issues/14542. */\n\t\t\theight: auto;\n\t\t}\n\t}\n\n\t& .image-inline {\n\t\t/*\n\t\t * Normally, the .image-inline would have \"display: inline-block\" and \"img { width: 100% }\" (to follow the wrapper while resizing).\n\t\t * Unfortunately, together with \"srcset\", it gets automatically stretched up to the width of the editing root.\n\t\t * This strange behavior does not happen with inline-flex.\n\t\t */\n\t\tdisplay: inline-flex;\n\n\t\t/* While being resized, don't allow the image to exceed the width of the editing root. */\n\t\tmax-width: 100%;\n\n\t\t/* This is required by Safari to resize images in a sensible way. Without this, the browser breaks the ratio. */\n\t\talign-items: flex-start;\n\n\t\t/* When the picture is present it must act as a flex container to let the img resize properly */\n\t\t& picture {\n\t\t\tdisplay: flex;\n\t\t}\n\n\t\t/* When the picture is present, it must act like a resizable img. */\n\t\t& picture,\n\t\t& img {\n\t\t\t/* This is necessary for the img to span the entire .image-inline wrapper and to resize properly. */\n\t\t\tflex-grow: 1;\n\t\t\tflex-shrink: 1;\n\n\t\t\t/* Prevents overflowing the editing root boundaries when an inline image is very wide. */\n\t\t\tmax-width: 100%;\n\t\t}\n\t}\n}\n\n.ck.ck-editor__editable {\n\t/*\n\t * Inhertit the content styles padding of the <figcaption> in case the integration overrides `text-align: center`\n\t * of `.image` (e.g. to the left/right). This ensures the placeholder stays at the padding just like the native\n\t * caret does, and not at the edge of <figcaption>.\n\t */\n\t& .image > figcaption.ck-placeholder::before {\n\t\tpadding-left: inherit;\n\t\tpadding-right: inherit;\n\n\t\t/*\n\t\t * Make sure the image caption placeholder doesn't overflow the placeholder area.\n\t\t * See https://github.com/ckeditor/ckeditor5/issues/9162.\n\t\t */\n\t\twhite-space: nowrap;\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t}\n\n\t/*\n\t * See https://github.com/ckeditor/ckeditor5/issues/15115.\n\t */\n\t& .image {\n\t\tz-index: 1;\n\n\t\t/*\n\t\t * Make sure the selected image always stays on top of its siblings.\n\t\t * See https://github.com/ckeditor/ckeditor5/issues/9108.\n\t\t */\n\t\t&.ck-widget_selected {\n\t\t\tz-index: 2;\n\t\t}\n\t}\n\n\t/*\n\t * See https://github.com/ckeditor/ckeditor5/issues/15115.\n\t */\n\t& .image-inline {\n\t\tz-index: 1;\n\n\t\t/*\n\t\t * Make sure the selected inline image always stays on top of its siblings.\n\t\t * See https://github.com/ckeditor/ckeditor5/issues/9108.\n\t\t */\n\t\t&.ck-widget_selected {\n\t\t\tz-index: 2;\n\n\t\t\t/*\n\t\t\t * Make sure the native browser selection style is not displayed.\n\t\t\t * Inline image widgets have their own styles for the selected state and\n\t\t\t * leaving this up to the browser is asking for a visual collision.\n\t\t\t */\n\t\t\t& ::selection {\n\t\t\t\tdisplay: none;\n\t\t\t}\n\t\t}\n\t}\n\n\t/* Keep proportions of the inline image if the height is set and the image is wider than the editor width.\n\tSee https://github.com/ckeditor/ckeditor5/issues/14542. */\n\t& .image-inline img {\n\t\theight: auto;\n\t}\n\n\t/* The inline image nested in the table should have its original size if not resized.\n\tSee https://github.com/ckeditor/ckeditor5/issues/9117. */\n\t& td,\n\t& th {\n\t\t& .image-inline img {\n\t\t\tmax-width: none;\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"@ckeditor/ckeditor5-ui/theme/mixins/_mediacolors.css\";\n\n:root {\n\t--ck-color-image-caption-background: hsl(0, 0%, 97%);\n\t--ck-color-image-caption-text: hsl(0, 0%, 20%);\n\t--ck-color-image-caption-highlighted-background: hsl(52deg 100% 50%);\n}\n\n/* Content styles */\n.ck-content .image > figcaption {\n\tdisplay: table-caption;\n\tcaption-side: bottom;\n\tword-break: break-word;\n\tcolor: var(--ck-color-image-caption-text);\n\tbackground-color: var(--ck-color-image-caption-background);\n\tpadding: .6em;\n\tfont-size: .75em;\n\toutline-offset: -1px;\n\n\t/* Improve placeholder rendering in high-constrast mode (https://github.com/ckeditor/ckeditor5/issues/14907). */\n\t@media (forced-colors: active) {\n\t\tbackground-color: unset;\n\t\tcolor: unset;\n\t}\n}\n\n/* Editing styles */\n.ck.ck-editor__editable .image > figcaption.image__caption_highlighted {\n\t@mixin ck-media-default-colors {\n\t\tanimation: ck-image-caption-highlight .6s ease-out;\n\t}\n\n\t@media (prefers-reduced-motion: reduce) {\n\t\tanimation: none;\n\t}\n}\n\n@keyframes ck-image-caption-highlight {\n\t0% {\n\t\tbackground-color: var(--ck-color-image-caption-highlighted-background);\n\t}\n\n\t100% {\n\t\tbackground-color: var(--ck-color-image-caption-background);\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n:root {\n\t--ck-image-style-spacing: 1.5em;\n\t--ck-inline-image-style-spacing: calc(var(--ck-image-style-spacing) / 2);\n}\n\n.ck-content {\n\t/* See: https://github.com/ckeditor/ckeditor5/issues/16317 */\n\t& .image {\n\t\t/* Provides a minimal side margin for the left and right aligned images, so that the user has a visual feedback\n\t\tconfirming successful application of the style if image width exceeds the editor's size.\n\t\tSee https://github.com/ckeditor/ckeditor5/issues/9342 */\n\t\t&.image-style-block-align-left,\n\t\t&.image-style-block-align-right {\n\t\t\tmax-width: calc(100% - var(--ck-image-style-spacing));\n\t\t}\n\n\t\t/* Allows displaying multiple floating images in the same line.\n\t\tSee https://github.com/ckeditor/ckeditor5/issues/9183#issuecomment-804988132 */\n\t\t&.image-style-align-left,\n\t\t&.image-style-align-right {\n\t\t\tclear: none;\n\t\t}\n\n\t\t&.image-style-side {\n\t\t\tfloat: right;\n\t\t\tmargin-left: var(--ck-image-style-spacing);\n\t\t\tmax-width: 50%;\n\t\t}\n\n\t\t&.image-style-align-left {\n\t\t\tfloat: left;\n\t\t\tmargin-right: var(--ck-image-style-spacing);\n\t\t}\n\n\t\t&.image-style-align-right {\n\t\t\tfloat: right;\n\t\t\tmargin-left: var(--ck-image-style-spacing);\n\t\t}\n\n\t\t&.image-style-block-align-right {\n\t\t\tmargin-right: 0;\n\t\t\tmargin-left: auto;\n\t\t}\n\n\t\t&.image-style-block-align-left {\n\t\t\tmargin-left: 0;\n\t\t\tmargin-right: auto;\n\t\t}\n\t}\n\n\t& .image-style-align-center {\n\t\tmargin-left: auto;\n\t\tmargin-right: auto;\n\t}\n\n\t& .image-style-align-left {\n\t\tfloat: left;\n\t\tmargin-right: var(--ck-image-style-spacing);\n\t}\n\n\t& .image-style-align-right {\n\t\tfloat: right;\n\t\tmargin-left: var(--ck-image-style-spacing);\n\t}\n\n\t/* Simulates margin collapsing with the preceding paragraph, which does not work for the floating elements. */\n\t& p + .image.image-style-align-left,\n\t& p + .image.image-style-align-right,\n\t& p + .image.image-style-side {\n\t\tmargin-top: 0;\n\t}\n\n\t& .image-inline {\n\t\t&.image-style-align-left,\n\t\t&.image-style-align-right {\n\t\t\tmargin-top: var(--ck-inline-image-style-spacing);\n\t\t\tmargin-bottom: var(--ck-inline-image-style-spacing);\n\t\t}\n\n\t\t&.image-style-align-left {\n\t\t\tmargin-right: var(--ck-inline-image-style-spacing);\n\t\t}\n\n\t\t&.image-style-align-right {\n\t\t\tmargin-left: var(--ck-inline-image-style-spacing);\n\t\t}\n\t}\n}\n\n.ck.ck-splitbutton {\n\t/* The button should display as a regular drop-down if the action button\n\tis forced to fire the same action as the arrow button. */\n\t&.ck-splitbutton_flatten {\n\t\t&:hover,\n\t\t&.ck-splitbutton_open {\n\t\t\t& > .ck-splitbutton__action:not(.ck-disabled),\n\t\t\t& > .ck-splitbutton__arrow:not(.ck-disabled),\n\t\t\t& > .ck-splitbutton__arrow:not(.ck-disabled):not(:hover) {\n\t\t\t\tbackground-color: var(--ck-color-button-on-background);\n\n\t\t\t\t&::after {\n\t\t\t\t\tdisplay: none;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t&.ck-splitbutton_open:hover {\n\t\t\t& > .ck-splitbutton__action:not(.ck-disabled),\n\t\t\t& > .ck-splitbutton__arrow:not(.ck-disabled),\n\t\t\t& > .ck-splitbutton__arrow:not(.ck-disabled):not(:hover) {\n\t\t\t\tbackground-color: var(--ck-color-button-on-hover-background);\n\t\t\t}\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n/* Class added to span element surrounding currently selected link. */\n.ck .ck-link_selected {\n\tbackground: var(--ck-color-link-selected-background);\n\n\t/* Give linked inline images some outline to let the user know they are also part of the link. */\n\t& span.image-inline {\n\t\toutline: var(--ck-widget-outline-thickness) solid var(--ck-color-link-selected-background);\n\t}\n}\n\n/*\n * Classes used by the \"fake visual selection\" displayed in the content when an input\n * in the link UI has focus (the browser does not render the native selection in this state).\n */\n.ck .ck-fake-link-selection {\n\tbackground: var(--ck-color-link-fake-selection);\n}\n\n/* A collapsed fake visual selection. */\n.ck .ck-fake-link-selection_collapsed {\n\theight: 100%;\n\tborder-right: 1px solid var(--ck-color-base-text);\n\tmargin-right: -1px;\n\toutline: solid 1px hsla(0, 0%, 100%, .5);\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"@ckeditor/ckeditor5-ui/theme/mixins/_rwd.css\";\n\n/* Generic class that wraps each link balloon view. */\n.ck.ck-link-form {\n\t& .ck-link-form__providers-list {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\n\t\t& .ck-link__button {\n\t\t\t& > .ck-button__label {\n\t\t\t\tflex-grow: 1;\n\t\t\t}\n\t\t}\n\t}\n\n\t& .ck-link__items:empty {\n\t\tdisplay: none;\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"@ckeditor/ckeditor5-ui/theme/mixins/_dir.css\";\n@import \"@ckeditor/ckeditor5-ui/theme/mixins/_rwd.css\";\n\n:root {\n\t--ck-link-panel-width: 340px;\n\t/* The height of the text inside the link providers list button. */\n\t--ck-link-provider-list-item-text-height: calc(var(--ck-line-height-base) * var(--ck-font-size-base));\n\t/* The height of the link providers list item contained paddings. */\n\t--ck-link-provider-list-item-height: calc(var(--ck-link-provider-list-item-text-height) + var(--ck-spacing-small) + var(--ck-spacing-small));\n}\n\n@mixin ck-media-phone {\n\t:root {\n\t\t--ck-link-panel-width: 300px;\n\t}\n}\n\n/* Generic class that wraps each link balloon view. */\n.ck.ck-form.ck-link-form {\n\twidth: var(--ck-link-panel-width);\n\tpadding-bottom: 0;\n\n\t&.ck-responsive-form {\n\t\t& .ck-labeled-field-view {\n\t\t\t@mixin ck-media-phone {\n\t\t\t\tmargin: 0;\n\t\t\t}\n\t\t}\n\t}\n\n\t& .ck-link-form__providers-list {\n\t\tborder-top: 1px solid var(--ck-color-base-border);\n\n\t\t/* The list should be scrollable when there are more than 4 items. */\n\t\t&:has(.ck-list__item:nth-child(n + 5)) {\n\t\t\toverflow: auto;\n\t\t\t/* Scroll should appear when there are more than 5 item on the list.\n\t\t\t* var(--ck-spacing-large) - is a form padding.\n\t\t\t* 1px - is a border width. */\n\t\t\tmax-height: calc(var(--ck-link-provider-list-item-height) * 4 + var(--ck-spacing-large) + 1px);\n\t\t}\n\n\t\t& .ck-link__button {\n\t\t\tpadding: var(--ck-spacing-small) var(--ck-spacing-large);\n\t\t\tborder-radius: 0;\n\n\t\t\t& > .ck-button__label {\n\t\t\t\toverflow: hidden;\n\t\t\t\ttext-overflow: ellipsis;\n\t\t\t}\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck.ck-link-providers {\n\t& > .ck-link-providers__list {\n\t\toverflow-x: hidden;\n\t\toverflow-y: auto;\n\t\toverscroll-behavior: contain;\n\n\t\t& .ck-button {\n\t\t\t& > .ck-icon {\n\t\t\t\tflex-shrink: 0;\n\t\t\t}\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"@ckeditor/ckeditor5-ui/theme/mixins/_rwd.css\";\n\n:root {\n\t--ck-link-providers-width: 340px;\n\t--ck-link-list-view-max-height: 240px;\n\t--ck-link-list-view-icon-size: calc( var(--ck-icon-size) * 0.8); /* 0.8 = 16/20 cause default the icon size is 20px */\n}\n\n@mixin ck-media-phone {\n\t:root {\n\t\t--ck-link-providers-width: 300px;\n\t}\n}\n\n.ck.ck-link-providers {\n\twidth: var(--ck-link-providers-width);\n\n\t& .ck-form__header__label {\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t}\n\n\t& > .ck-link-providers__list {\n\t\tmax-height: min( var(--ck-link-list-view-max-height), 40vh );\n\n\t\t& .ck-button {\n\t\t\t& > .ck-icon {\n\t\t\t\twidth: var(--ck-link-list-view-icon-size);\n\t\t\t\theight: var(--ck-link-list-view-icon-size);\n\t\t\t}\n\n\t\t\t& > .ck-button__label {\n\t\t\t\toverflow: hidden;\n\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\twhite-space: nowrap;\n\t\t\t}\n\t\t}\n\t}\n\n\t& .ck-link__empty-list-info {\n\t\tpadding: calc( 2 * var(--ck-spacing-large) ) var(--ck-spacing-medium);\n\t\ttext-align: center;\n\t\tfont-style: italic;\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"@ckeditor/ckeditor5-ui/theme/mixins/_rwd.css\";\n\n:root {\n\t--ck-link-properties-width: 340px;\n}\n\n@mixin ck-media-phone {\n\t:root {\n\t\t--ck-link-properties-width: 300px;\n\t}\n}\n\n.ck.ck-link-properties {\n\twidth: var(--ck-link-properties-width);\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck.ck-link-toolbar__preview {\n\tdisplay: inline-block;\n\n\t& .ck-button__label {\n\t\toverflow: hidden;\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"@ckeditor/ckeditor5-ui/theme/mixins/_dir.css\";\n\n:root {\n\t--ck-link-bookmark-icon-size: calc( var(--ck-icon-size) * 0.7); /* 0.7 = 14/20 cause default the icon size is 20px */\n}\n\n.ck.ck-toolbar.ck-link-toolbar {\n\t& > .ck-toolbar__items {\n\t\tflex-wrap: nowrap;\n\t}\n}\n\na.ck.ck-button.ck-link-toolbar__preview {\n\tpadding: 0 var(--ck-spacing-medium);\n\tcolor: var(--ck-color-link-default);\n\tcursor: pointer;\n\tjustify-content: center;\n\n\t& .ck.ck-button__label {\n\t\ttext-overflow: ellipsis;\n\n\t\t/* Match the box model of the link editor form's input so the balloon\n\t\tdoes not change width when moving between actions and the form. */\n\t\tmax-width: var(--ck-input-width);\n\t}\n\n\t&,\n\t&:hover,\n\t&:focus,\n\t&:active {\n\t\tbackground: none;\n\t}\n\n\t&:active {\n\t\tbox-shadow: none;\n\t}\n\n\t&:hover,\n\t&:focus {\n\t\ttext-decoration: underline;\n\t}\n\n\t&.ck-button_with-text .ck.ck-icon.ck-button__icon {\n\t\twidth: var(--ck-link-bookmark-icon-size);\n\t\theight: var(--ck-link-bookmark-icon-size);\n\n\t\t@mixin ck-dir ltr {\n\t\t\tmargin-right: var(--ck-spacing-tiny);\n\t\t\tmargin-left: var(--ck-spacing-small);\n\t\t}\n\n\t\t@mixin ck-dir rtl {\n\t\t\tmargin-left: var(--ck-spacing-tiny);\n\t\t\tmargin-right: var(--ck-spacing-small);\n\t\t}\n\t}\n\n\t&:has( .ck-icon ) {\n\t\tpadding-left: var(--ck-spacing-extra-tiny );\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck-editor__editable .ck-list-bogus-paragraph {\n\tdisplay: block;\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck-content ol {\n\tlist-style-type: decimal;\n\n\t& ol {\n\t\tlist-style-type: lower-latin;\n\n\t\t& ol {\n\t\t\tlist-style-type: lower-roman;\n\n\t\t\t& ol {\n\t\t\t\tlist-style-type: upper-latin;\n\n\t\t\t\t& ol {\n\t\t\t\t\tlist-style-type: upper-roman;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n.ck-content ul {\n\tlist-style-type: disc;\n\n\t& ul {\n\t\tlist-style-type: circle;\n\n\t\t& ul {\n\t\t\tlist-style-type: square;\n\n\t\t\t& ul {\n\t\t\t\tlist-style-type: square;\n\t\t\t}\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"@ckeditor/ckeditor5-ui/theme/mixins/_rwd.css\";\n\n:root {\n\t--ck-image-custom-resize-form-width: 340px;\n}\n\n@mixin ck-media-phone {\n\t:root {\n\t\t--ck-image-custom-resize-form-width: 300px;\n\t}\n}\n\n.ck.ck-image-custom-resize-form {\n\t&.ck-responsive-form {\n\t\twidth: var(--ck-image-custom-resize-form-width);\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n/* Preserve aspect ratio of the resized image after introducing image height attribute. */\n.ck-content img.image_resized {\n\theight: auto;\n}\n\n.ck-content .image.image_resized {\n\tmax-width: 100%;\n\t/*\n\tThe `<figure>` element for resized images must not use `display:table` as browsers do not support `max-width` for it well.\n\tSee https://stackoverflow.com/questions/4019604/chrome-safari-ignoring-max-width-in-table/14420691#14420691 for more.\n\tFortunately, since we control the width, there is no risk that the image will look bad.\n\t*/\n\tdisplay: block;\n\tbox-sizing: border-box;\n\n\t& img {\n\t\t/* For resized images it is the `<figure>` element that determines the image width. */\n\t\twidth: 100%;\n\t}\n\n\t& > figcaption {\n\t\t/* The `<figure>` element uses `display:block`, so `<figcaption>` also has to. */\n\t\tdisplay: block;\n\t}\n}\n\n.ck.ck-editor__editable {\n\t/* The resized inline image nested in the table should respect its parent size.\n\tSee https://github.com/ckeditor/ckeditor5/issues/9117. */\n\t& td,\n\t& th {\n\t\t& .image-inline.image_resized img {\n\t\t\tmax-width: 100%;\n\t\t}\n\t}\n}\n\n[dir=\"ltr\"] .ck.ck-button.ck-button_with-text.ck-resize-image-button .ck-button__icon {\n\tmargin-right: var(--ck-spacing-standard);\n}\n\n[dir=\"rtl\"] .ck.ck-button.ck-button_with-text.ck-resize-image-button .ck-button__icon {\n\tmargin-left: var(--ck-spacing-standard);\n}\n\n.ck.ck-dropdown .ck-button.ck-resize-image-button .ck-button__label {\n\twidth: 4em;\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck.ck-color-grid {\n\tdisplay: grid;\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"../../../mixins/_rounded.css\";\n@import \"@ckeditor/ckeditor5-ui/theme/mixins/_mediacolors.css\";\n\n:root {\n\t--ck-color-grid-tile-size: 24px;\n\n\t/* Not using global colors here because these may change but some colors in a pallette\n\t * require special treatment. For instance, this ensures no matter what the UI text color is,\n\t * the check icon will look good on the black color tile. */\n\t--ck-color-color-grid-check-icon: hsl(212, 81%, 46%);\n}\n\n.ck.ck-color-grid {\n\tgrid-gap: 5px;\n\tpadding: 8px;\n}\n\n.ck.ck-color-grid__tile {\n\ttransition: .2s ease box-shadow;\n\n\t@mixin ck-media-default-colors {\n\t\twidth: var(--ck-color-grid-tile-size);\n\t\theight: var(--ck-color-grid-tile-size);\n\t\tmin-width: var(--ck-color-grid-tile-size);\n\t\tmin-height: var(--ck-color-grid-tile-size);\n\t\tpadding: 0;\n\t\tborder: 0;\n\n\t\t&.ck-on,\n\t\t&:focus:not( .ck-disabled ),\n\t\t&:hover:not( .ck-disabled ) {\n\t\t\t/* Disable the default .ck-button's border ring. */\n\t\t\tborder: 0;\n\t\t}\n\n\t\t&.ck-color-selector__color-tile_bordered {\n\t\t\tbox-shadow: 0 0 0 1px var(--ck-color-base-border);\n\t\t}\n\n\t\t&.ck-on {\n\t\t\tbox-shadow: inset 0 0 0 1px var(--ck-color-base-background), 0 0 0 2px var(--ck-color-base-text);\n\t\t}\n\n\t\t&:focus:not( .ck-disabled ),\n\t\t&:hover:not( .ck-disabled ) {\n\t\t\tbox-shadow: inset 0 0 0 1px var(--ck-color-base-background), 0 0 0 2px var(--ck-color-focus-border);\n\t\t}\n\t}\n\n\t/*\n\t * In high contrast mode, the colors are replaced with text labels.\n\t * See https://github.com/ckeditor/ckeditor5/issues/14907.\n\t */\n\t@mixin ck-media-forced-colors {\n\t\twidth: unset;\n\t\theight: unset;\n\t\tmin-width: unset;\n\t\tmin-height: unset;\n\t\tpadding: 0 var(--ck-spacing-small);\n\n\t\t& .ck-button__label {\n\t\t\tdisplay: inline-block;\n\t\t}\n\t}\n\n\t@media (prefers-reduced-motion: reduce) {\n\t\ttransition: none;\n\t}\n\n\t&.ck-disabled {\n\t\tcursor: unset;\n\t\ttransition: unset;\n\t}\n\n\t& .ck.ck-icon {\n\t\tdisplay: none;\n\t\tcolor: var(--ck-color-color-grid-check-icon);\n\t}\n\n\t&.ck-on {\n\t\t& .ck.ck-icon {\n\t\t\tdisplay: block;\n\t\t}\n\t}\n}\n\n.ck.ck-color-grid__label {\n\tpadding: 0 var(--ck-spacing-standard);\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.color-picker-hex-input {\n\twidth: max-content;\n\n\t& .ck.ck-input {\n\t\tmin-width: unset;\n\t}\n}\n\n.ck.ck-color-picker__row {\n\tdisplay: flex;\n\tflex-direction: row;\n\tflex-wrap: nowrap;\n\tjustify-content: space-between;\n\tmargin: var(--ck-spacing-large) 0 0;\n\twidth: unset;\n\n\t& .ck.ck-labeled-field-view {\n\t\tpadding-top: unset;\n\t}\n\n\t& .ck.ck-input-text {\n\t\twidth: unset;\n\t}\n\n\t& .ck-color-picker__hash-view {\n\t\tpadding-top: var(--ck-spacing-tiny);\n\t\tpadding-right: var(--ck-spacing-medium);\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"@ckeditor/ckeditor5-ui/theme/mixins/_dir.css\";\n\n.ck.ck-color-selector {\n\t/* View fragment with color grids. */\n\t& .ck-color-grids-fragment {\n\t\t& .ck-button.ck-color-selector__remove-color,\n\t\t& .ck-button.ck-color-selector__color-picker {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\n\t\t\t@mixin ck-dir rtl {\n\t\t\t\tjustify-content: flex-start;\n\t\t\t}\n\t\t}\n\t}\n\n\t/* View fragment with a color picker. */\n\t& .ck-color-picker-fragment {\n\t\t& .ck.ck-color-selector_action-bar {\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: row;\n\t\t\tjustify-content: space-around;\n\n\t\t\t& .ck-button-save,\n\t\t\t& .ck-button-cancel {\n\t\t\t\tflex: 1\n\t\t\t}\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"@ckeditor/ckeditor5-ui/theme/mixins/_dir.css\";\n\n.ck.ck-color-selector {\n\t/* View fragment with color grids. */\n\t& .ck-color-grids-fragment {\n\t\t& .ck-button.ck-color-selector__remove-color,\n\t\t& .ck-button.ck-color-selector__color-picker {\n\t\t\twidth: 100%;\n\t\t}\n\n\t\t& .ck-button.ck-color-selector__color-picker {\n\t\t\tpadding: calc(var(--ck-spacing-standard) / 2) var(--ck-spacing-standard);\n\t\t\tborder-bottom-left-radius: 0;\n\t\t\tborder-bottom-right-radius: 0;\n\n\t\t\t&:not(:focus) {\n\t\t\t\tborder-top: 1px solid var(--ck-color-base-border);\n\t\t\t}\n\n\t\t\t& .ck.ck-icon {\n\t\t\t\t@mixin ck-dir ltr {\n\t\t\t\t\tmargin-right: var(--ck-spacing-standard);\n\t\t\t\t}\n\n\t\t\t\t@mixin ck-dir rtl {\n\t\t\t\t\tmargin-left: var(--ck-spacing-standard);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t& label.ck.ck-color-grid__label {\n\t\t\tfont-weight: unset;\n\t\t}\n\t}\n\n\t/* View fragment with a color picker. */\n\t& .ck-color-picker-fragment {\n\t\t& .ck.ck-color-picker {\n\t\t\tpadding: 8px;\n\n\t\t\t& .hex-color-picker {\n\t\t\t\theight: 100px;\n\t\t\t\tmin-width: 180px;\n\n\t\t\t\t&::part(saturation) {\n\t\t\t\t\tborder-radius: var(--ck-border-radius) var(--ck-border-radius) 0 0;\n\t\t\t\t}\n\n\t\t\t\t&::part(hue) {\n\t\t\t\t\tborder-radius: 0 0 var(--ck-border-radius) var(--ck-border-radius);\n\t\t\t\t}\n\n\t\t\t\t&::part(saturation-pointer),\n\t\t\t\t&::part(hue-pointer) {\n\t\t\t\t\twidth: 15px;\n\t\t\t\t\theight: 15px;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t& .ck.ck-color-selector_action-bar {\n\t\t\tpadding: 0 8px 8px;\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n/* The values should be synchronized with the \"FONT_SIZE_PRESET_UNITS\" object in the \"/src/fontsize/utils.js\" file. */\n\n/* Styles should be prefixed with the `.ck-content` class.\nSee https://github.com/ckeditor/ckeditor5/issues/6636 */\n.ck-content {\n\t& .text-tiny {\n\t\tfont-size: .7em;\n\t}\n\n\t& .text-small {\n\t\tfont-size: .85em;\n\t}\n\n\t& .text-big {\n\t\tfont-size: 1.4em;\n\t}\n\n\t& .text-huge {\n\t\tfont-size: 1.8em;\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck-media__wrapper {\n\t& .ck-media__placeholder {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\n\t\t& .ck-media__placeholder__url {\n\t\t\t/* Otherwise the URL will overflow when the content is very narrow. */\n\t\t\tmax-width: 100%;\n\n\t\t\tposition: relative;\n\n\t\t\t& .ck-media__placeholder__url__text {\n\t\t\t\toverflow: hidden;\n\t\t\t\tdisplay: block;\n\t\t\t}\n\t\t}\n\t}\n\n\t&[data-oembed-url*=\"twitter.com\"],\n\t&[data-oembed-url*=\"google.com/maps\"],\n\t&[data-oembed-url*=\"goo.gl/maps\"],\n\t&[data-oembed-url*=\"maps.google.com\"],\n\t&[data-oembed-url*=\"maps.app.goo.gl\"],\n\t&[data-oembed-url*=\"facebook.com\"],\n\t&[data-oembed-url*=\"instagram.com\"] {\n\t\t& .ck-media__placeholder__icon * {\n\t\t\tdisplay: none;\n\t\t}\n\t}\n}\n\n/* Disable all mouse interaction as long as the editor is not read–only.\n   https://github.com/ckeditor/ckeditor5-media-embed/issues/58 */\n.ck-editor__editable:not(.ck-read-only) .ck-media__wrapper > *:not(.ck-media__placeholder) {\n\tpointer-events: none;\n}\n\n/* Disable all mouse interaction when the widget is not selected (e.g. to avoid opening links by accident).\n   https://github.com/ckeditor/ckeditor5-media-embed/issues/18 */\n.ck-editor__editable:not(.ck-read-only) .ck-widget:not(.ck-widget_selected) .ck-media__placeholder {\n\tpointer-events: none;\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n:root {\n\t--ck-media-embed-placeholder-icon-size: 3em;\n\n\t--ck-color-media-embed-placeholder-url-text: hsl(0, 0%, 46%);\n\t--ck-color-media-embed-placeholder-url-text-hover: var(--ck-color-base-text);\n}\n\n.ck-media__wrapper {\n\tmargin: 0 auto;\n\n\t& .ck-media__placeholder {\n\t\tpadding: calc( 3 * var(--ck-spacing-standard) );\n\t\tbackground: var(--ck-color-base-foreground);\n\n\t\t& .ck-media__placeholder__icon {\n\t\t\tmin-width: var(--ck-media-embed-placeholder-icon-size);\n\t\t\theight: var(--ck-media-embed-placeholder-icon-size);\n\t\t\tmargin-bottom: var(--ck-spacing-large);\n\t\t\tbackground-position: center;\n\t\t\tbackground-size: cover;\n\n\t\t\t& .ck-icon {\n\t\t\t\twidth: 100%;\n\t\t\t\theight: 100%;\n\t\t\t}\n\t\t}\n\n\t\t& .ck-media__placeholder__url__text {\n\t\t\tcolor: var(--ck-color-media-embed-placeholder-url-text);\n\t\t\twhite-space: nowrap;\n\t\t\ttext-align: center;\n\t\t\tfont-style: italic;\n\t\t\ttext-overflow: ellipsis;\n\n\t\t\t&:hover {\n\t\t\t\tcolor: var(--ck-color-media-embed-placeholder-url-text-hover);\n\t\t\t\tcursor: pointer;\n\t\t\t\ttext-decoration: underline;\n\t\t\t}\n\t\t}\n\t}\n\n\t&[data-oembed-url*=\"open.spotify.com\"] {\n\t\tmax-width: 300px;\n\t\tmax-height: 380px;\n\t}\n\n\t&[data-oembed-url*=\"google.com/maps\"] .ck-media__placeholder__icon,\n\t&[data-oembed-url*=\"goo.gl/maps\"] .ck-media__placeholder__icon,\n\t&[data-oembed-url*=\"maps.google.com\"] .ck-media__placeholder__icon,\n\t&[data-oembed-url*=\"maps.app.goo.gl\"] .ck-media__placeholder__icon {\n\t\tbackground-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNTAuMzc4IiBoZWlnaHQ9IjI1NC4xNjciIHZpZXdCb3g9IjAgMCA2Ni4yNDYgNjcuMjQ4Ij48ZyB0cmFuc2Zvcm09InRyYW5zbGF0ZSgtMTcyLjUzMSAtMjE4LjQ1NSkgc2NhbGUoLjk4MDEyKSI+PHJlY3Qgcnk9IjUuMjM4IiByeD0iNS4yMzgiIHk9IjIzMS4zOTkiIHg9IjE3Ni4wMzEiIGhlaWdodD0iNjAuMDk5IiB3aWR0aD0iNjAuMDk5IiBmaWxsPSIjMzRhNjY4IiBwYWludC1vcmRlcj0ibWFya2VycyBzdHJva2UgZmlsbCIvPjxwYXRoIGQ9Ik0yMDYuNDc3IDI2MC45bC0yOC45ODcgMjguOTg3YTUuMjE4IDUuMjE4IDAgMCAwIDMuNzggMS42MWg0OS42MjFjMS42OTQgMCAzLjE5LS43OTggNC4xNDYtMi4wMzd6IiBmaWxsPSIjNWM4OGM1Ii8+PHBhdGggZD0iTTIyNi43NDIgMjIyLjk4OGMtOS4yNjYgMC0xNi43NzcgNy4xNy0xNi43NzcgMTYuMDE0LjAwNyAyLjc2Mi42NjMgNS40NzQgMi4wOTMgNy44NzUuNDMuNzAzLjgzIDEuNDA4IDEuMTkgMi4xMDcuMzMzLjUwMi42NSAxLjAwNS45NSAxLjUwOC4zNDMuNDc3LjY3My45NTcuOTg4IDEuNDQgMS4zMSAxLjc2OSAyLjUgMy41MDIgMy42MzcgNS4xNjguNzkzIDEuMjc1IDEuNjgzIDIuNjQgMi40NjYgMy45OSAyLjM2MyA0LjA5NCA0LjAwNyA4LjA5MiA0LjYgMTMuOTE0di4wMTJjLjE4Mi40MTIuNTE2LjY2Ni44NzkuNjY3LjQwMy0uMDAxLjc2OC0uMzE0LjkzLS43OTkuNjAzLTUuNzU2IDIuMjM4LTkuNzI5IDQuNTg1LTEzLjc5NC43ODItMS4zNSAxLjY3My0yLjcxNSAyLjQ2NS0zLjk5IDEuMTM3LTEuNjY2IDIuMzI4LTMuNCAzLjYzOC01LjE2OS4zMTUtLjQ4Mi42NDUtLjk2Mi45ODgtMS40MzkuMy0uNTAzLjYxNy0xLjAwNi45NS0xLjUwOC4zNTktLjcuNzYtMS40MDQgMS4xOS0yLjEwNyAxLjQyNi0yLjQwMiAyLTUuMTE0IDIuMDA0LTcuODc1IDAtOC44NDQtNy41MTEtMTYuMDE0LTE2Ljc3Ni0xNi4wMTR6IiBmaWxsPSIjZGQ0YjNlIiBwYWludC1vcmRlcj0ibWFya2VycyBzdHJva2UgZmlsbCIvPjxlbGxpcHNlIHJ5PSI1LjU2NCIgcng9IjUuODI4IiBjeT0iMjM5LjAwMiIgY3g9IjIyNi43NDIiIGZpbGw9IiM4MDJkMjciIHBhaW50LW9yZGVyPSJtYXJrZXJzIHN0cm9rZSBmaWxsIi8+PHBhdGggZD0iTTE5MC4zMDEgMjM3LjI4M2MtNC42NyAwLTguNDU3IDMuODUzLTguNDU3IDguNjA2czMuNzg2IDguNjA3IDguNDU3IDguNjA3YzMuMDQzIDAgNC44MDYtLjk1OCA2LjMzNy0yLjUxNiAxLjUzLTEuNTU3IDIuMDg3LTMuOTEzIDIuMDg3LTYuMjkgMC0uMzYyLS4wMjMtLjcyMi0uMDY0LTEuMDc5aC04LjI1N3YzLjA0M2g0Ljg1Yy0uMTk3Ljc1OS0uNTMxIDEuNDUtMS4wNTggMS45ODYtLjk0Mi45NTgtMi4wMjggMS41NDgtMy45MDEgMS41NDgtMi44NzYgMC01LjIwOC0yLjM3Mi01LjIwOC01LjI5OSAwLTIuOTI2IDIuMzMyLTUuMjk5IDUuMjA4LTUuMjk5IDEuMzk5IDAgMi42MTguNDA3IDMuNTg0IDEuMjkzbDIuMzgxLTIuMzhjMC0uMDAyLS4wMDMtLjAwNC0uMDA0LS4wMDUtMS41ODgtMS41MjQtMy42Mi0yLjIxNS01Ljk1NS0yLjIxNXptNC40MyA1LjY2bC4wMDMuMDA2di0uMDAzeiIgZmlsbD0iI2ZmZiIgcGFpbnQtb3JkZXI9Im1hcmtlcnMgc3Ryb2tlIGZpbGwiLz48cGF0aCBkPSJNMjE1LjE4NCAyNTEuOTI5bC03Ljk4IDcuOTc5IDI4LjQ3NyAyOC40NzVjLjI4Ny0uNjQ5LjQ0OS0xLjM2Ni40NDktMi4xMjN2LTMxLjE2NWMtLjQ2OS42NzUtLjkzNCAxLjM0OS0xLjM4MiAyLjAwNS0uNzkyIDEuMjc1LTEuNjgyIDIuNjQtMi40NjUgMy45OS0yLjM0NyA0LjA2NS0zLjk4MiA4LjAzOC00LjU4NSAxMy43OTQtLjE2Mi40ODUtLjUyNy43OTgtLjkzLjc5OS0uMzYzLS4wMDEtLjY5Ny0uMjU1LS44NzktLjY2N3YtLjAxMmMtLjU5My01LjgyMi0yLjIzNy05LjgyLTQuNi0xMy45MTQtLjc4My0xLjM1LTEuNjczLTIuNzE1LTIuNDY2LTMuOTktMS4xMzctMS42NjYtMi4zMjctMy40LTMuNjM3LTUuMTY5bC0uMDAyLS4wMDN6IiBmaWxsPSIjYzNjM2MzIi8+PHBhdGggZD0iTTIxMi45ODMgMjQ4LjQ5NWwtMzYuOTUyIDM2Ljk1M3YuODEyYTUuMjI3IDUuMjI3IDAgMCAwIDUuMjM4IDUuMjM4aDEuMDE1bDM1LjY2Ni0zNS42NjZhMTM2LjI3NSAxMzYuMjc1IDAgMCAwLTIuNzY0LTMuOSAzNy41NzUgMzcuNTc1IDAgMCAwLS45ODktMS40NGMtLjI5OS0uNTAzLS42MTYtMS4wMDYtLjk1LTEuNTA4LS4wODMtLjE2Mi0uMTc2LS4zMjYtLjI2NC0uNDg5eiIgZmlsbD0iI2ZkZGM0ZiIgcGFpbnQtb3JkZXI9Im1hcmtlcnMgc3Ryb2tlIGZpbGwiLz48cGF0aCBkPSJNMjExLjk5OCAyNjEuMDgzbC02LjE1MiA2LjE1MSAyNC4yNjQgMjQuMjY0aC43ODFhNS4yMjcgNS4yMjcgMCAwIDAgNS4yMzktNS4yMzh2LTEuMDQ1eiIgZmlsbD0iI2ZmZiIgcGFpbnQtb3JkZXI9Im1hcmtlcnMgc3Ryb2tlIGZpbGwiLz48L2c+PC9zdmc+);\n\t}\n\n\t&[data-oembed-url*=\"facebook.com\"] .ck-media__placeholder {\n\t\tbackground: hsl(220, 46%, 48%);\n\n\t\t& .ck-media__placeholder__icon {\n\t\t\tbackground-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz48c3ZnIHdpZHRoPSIxMDI0cHgiIGhlaWdodD0iMTAyNHB4IiB2aWV3Qm94PSIwIDAgMTAyNCAxMDI0IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPiAgICAgICAgPHRpdGxlPkZpbGwgMTwvdGl0bGU+ICAgIDxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPiAgICA8ZGVmcz48L2RlZnM+ICAgIDxnIGlkPSJQYWdlLTEiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPiAgICAgICAgPGcgaWQ9ImZMb2dvX1doaXRlIiBmaWxsPSIjRkZGRkZFIj4gICAgICAgICAgICA8cGF0aCBkPSJNOTY3LjQ4NCwwIEw1Ni41MTcsMCBDMjUuMzA0LDAgMCwyNS4zMDQgMCw1Ni41MTcgTDAsOTY3LjQ4MyBDMCw5OTguNjk0IDI1LjI5NywxMDI0IDU2LjUyMiwxMDI0IEw1NDcsMTAyNCBMNTQ3LDYyOCBMNDE0LDYyOCBMNDE0LDQ3MyBMNTQ3LDQ3MyBMNTQ3LDM1OS4wMjkgQzU0NywyMjYuNzY3IDYyNy43NzMsMTU0Ljc0NyA3NDUuNzU2LDE1NC43NDcgQzgwMi4yNjksMTU0Ljc0NyA4NTAuODQyLDE1OC45NTUgODY1LDE2MC44MzYgTDg2NSwyOTkgTDc4My4zODQsMjk5LjAzNyBDNzE5LjM5MSwyOTkuMDM3IDcwNywzMjkuNTI5IDcwNywzNzQuMjczIEw3MDcsNDczIEw4NjAuNDg3LDQ3MyBMODQwLjUwMSw2MjggTDcwNyw2MjggTDcwNywxMDI0IEw5NjcuNDg0LDEwMjQgQzk5OC42OTcsMTAyNCAxMDI0LDk5OC42OTcgMTAyNCw5NjcuNDg0IEwxMDI0LDU2LjUxNSBDMTAyNCwyNS4zMDMgOTk4LjY5NywwIDk2Ny40ODQsMCIgaWQ9IkZpbGwtMSI+PC9wYXRoPiAgICAgICAgPC9nPiAgICA8L2c+PC9zdmc+);\n\t\t}\n\n\t\t& .ck-media__placeholder__url__text {\n\t\t\tcolor: hsl(220, 100%, 90%);\n\n\t\t\t&:hover {\n\t\t\t\tcolor: hsl(0, 0%, 100%);\n\t\t\t}\n\t\t}\n\t}\n\n\t&[data-oembed-url*=\"instagram.com\"] .ck-media__placeholder {\n\t\tbackground: linear-gradient(-135deg,hsl(246, 100%, 39%),hsl(302, 100%, 36%),hsl(0, 100%, 48%));\n\n\t\t& .ck-media__placeholder__icon {\n\t\t\tbackground-image: url(data:image/svg+xml;base64,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);\n\t\t}\n\n\t\t/* stylelint-disable-next-line no-descending-specificity */\n\t\t& .ck-media__placeholder__url__text {\n\t\t\tcolor: hsl(302, 100%, 94%);\n\n\t\t\t&:hover {\n\t\t\t\tcolor: hsl(0, 0%, 100%);\n\t\t\t}\n\t\t}\n\t}\n\n\t&[data-oembed-url*=\"twitter.com\"] .ck.ck-media__placeholder {\n\t\t/* Use gradient to contrast with focused widget (ckeditor/ckeditor5-media-embed#22). */\n\t\tbackground: linear-gradient( to right, hsl(201, 85%, 70%), hsl(201, 85%, 35%) );\n\n\t\t& .ck-media__placeholder__icon {\n\t\t\tbackground-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz48c3ZnIHZlcnNpb249IjEuMSIgaWQ9IldoaXRlIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB4PSIwcHgiIHk9IjBweCIgdmlld0JveD0iMCAwIDQwMCA0MDAiIHN0eWxlPSJlbmFibGUtYmFja2dyb3VuZDpuZXcgMCAwIDQwMCA0MDA7IiB4bWw6c3BhY2U9InByZXNlcnZlIj48c3R5bGUgdHlwZT0idGV4dC9jc3MiPi5zdDB7ZmlsbDojRkZGRkZGO308L3N0eWxlPjxwYXRoIGNsYXNzPSJzdDAiIGQ9Ik00MDAsMjAwYzAsMTEwLjUtODkuNSwyMDAtMjAwLDIwMFMwLDMxMC41LDAsMjAwUzg5LjUsMCwyMDAsMFM0MDAsODkuNSw0MDAsMjAweiBNMTYzLjQsMzA1LjVjODguNywwLDEzNy4yLTczLjUsMTM3LjItMTM3LjJjMC0yLjEsMC00LjItMC4xLTYuMmM5LjQtNi44LDE3LjYtMTUuMywyNC4xLTI1Yy04LjYsMy44LTE3LjksNi40LTI3LjcsNy42YzEwLTYsMTcuNi0xNS40LDIxLjItMjYuN2MtOS4zLDUuNS0xOS42LDkuNS0zMC42LDExLjdjLTguOC05LjQtMjEuMy0xNS4yLTM1LjItMTUuMmMtMjYuNiwwLTQ4LjIsMjEuNi00OC4yLDQ4LjJjMCwzLjgsMC40LDcuNSwxLjMsMTFjLTQwLjEtMi03NS42LTIxLjItOTkuNC01MC40Yy00LjEsNy4xLTYuNSwxNS40LTYuNSwyNC4yYzAsMTYuNyw4LjUsMzEuNSwyMS41LDQwLjFjLTcuOS0wLjItMTUuMy0yLjQtMjEuOC02YzAsMC4yLDAsMC40LDAsMC42YzAsMjMuNCwxNi42LDQyLjgsMzguNyw0Ny4zYy00LDEuMS04LjMsMS43LTEyLjcsMS43Yy0zLjEsMC02LjEtMC4zLTkuMS0wLjljNi4xLDE5LjIsMjMuOSwzMy4xLDQ1LDMzLjVjLTE2LjUsMTIuOS0zNy4zLDIwLjYtNTkuOSwyMC42Yy0zLjksMC03LjctMC4yLTExLjUtMC43QzExMC44LDI5Ny41LDEzNi4yLDMwNS41LDE2My40LDMwNS41Ii8+PC9zdmc+);\n\t\t}\n\n\t\t& .ck-media__placeholder__url__text {\n\t\t\tcolor: hsl(201, 100%, 86%);\n\n\t\t\t&:hover {\n\t\t\t\tcolor: hsl(0, 0%, 100%);\n\t\t\t}\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"@ckeditor/ckeditor5-ui/theme/mixins/_rwd.css\";\n\n.ck.ck-media-form {\n\tdisplay: flex;\n\talign-items: flex-start;\n\tflex-direction: row;\n\tflex-wrap: nowrap;\n\twidth: 400px;\n\n\t& .ck-labeled-field-view {\n\t\tdisplay: inline-block;\n\t\twidth: 100%;\n\t}\n\n\t& .ck-label {\n\t\tdisplay: none;\n\t}\n\n\t& .ck-input {\n\t\twidth: 100%;\n\t}\n\n\t@mixin ck-media-phone {\n\t\tflex-wrap: wrap;\n\n\t\t& .ck-labeled-field-view {\n\t\t\tflex-basis: 100%;\n\t\t}\n\n\t\t& .ck-button {\n\t\t\tflex-basis: 50%;\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck-content .media {\n\t/* Don't allow floated content overlap the media.\n\thttps://github.com/ckeditor/ckeditor5-media-embed/issues/53 */\n\tclear: both;\n\n\t/* Make sure there is some space between the content and the media. */\n\t/* The first value should be equal to --ck-spacing-large variable if used in the editor context\n\tto avoid the content jumping (See https://github.com/ckeditor/ckeditor5/issues/9825). */\n\tmargin: 0.9em 0;\n\n\t/* Make sure media is not overriden with Bootstrap default `flex` value.\n\tSee: https://github.com/ckeditor/ckeditor5/issues/1373. */\n\tdisplay: block;\n\n\t/* Give the media some minimal width in the content to prevent them\n\tfrom being \"squashed\" in tight spaces, e.g. in table cells (#44) */\n\tmin-width: 15em;\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n:root {\n\t--ck-color-selector-focused-cell-background: hsla(212, 90%, 80%, .3);\n}\n\n.ck-widget.table {\n\t& td,\n\t& th {\n\t\t/**\n\t\t * While setting outline is fine, the border should not be modified here\n\t\t * because it overrides the default table cell border color which is not expected.\n\t\t * So do not use `@mixin ck-focus-ring;` here, or any other border styles.\n\t\t * See more: https://github.com/ckeditor/ckeditor5/issues/16979\n\t\t */\n\t\t&.ck-editor__nested-editable.ck-editor__nested-editable_focused,\n\t\t&.ck-editor__nested-editable:focus {\n\t\t\t/* A very slight background to highlight the focused cell */\n\t\t\tbackground: var(--ck-color-selector-focused-cell-background);\n\t\t\toutline: 1px solid var(--ck-color-focus-border);\n\t\t\toutline-offset: -1px; /* progressive enhancement - no IE support */\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck .ck-insert-table-dropdown__grid {\n\tdisplay: flex;\n\tflex-direction: row;\n\tflex-wrap: wrap;\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n:root {\n\t--ck-insert-table-dropdown-padding: 10px;\n\t--ck-insert-table-dropdown-box-height: 11px;\n\t--ck-insert-table-dropdown-box-width: 12px;\n\t--ck-insert-table-dropdown-box-margin: 1px;\n}\n\n.ck .ck-insert-table-dropdown__grid {\n\t/* The width of a container should match 10 items in a row so there will be a 10x10 grid. */\n\twidth: calc(var(--ck-insert-table-dropdown-box-width) * 10 + var(--ck-insert-table-dropdown-box-margin) * 20 + var(--ck-insert-table-dropdown-padding) * 2);\n\tpadding: var(--ck-insert-table-dropdown-padding) var(--ck-insert-table-dropdown-padding) 0;\n}\n\n.ck .ck-insert-table-dropdown__label,\n.ck[dir=rtl] .ck-insert-table-dropdown__label {\n\ttext-align: center;\n}\n\n.ck .ck-insert-table-dropdown-grid-box {\n\tmin-width: var(--ck-insert-table-dropdown-box-width);\n\tmin-height: var(--ck-insert-table-dropdown-box-height);\n\tmargin: var(--ck-insert-table-dropdown-box-margin);\n\tborder: 1px solid var(--ck-color-base-border);\n\tborder-radius: 1px;\n\toutline: none;\n\ttransition: none;\n\n\t@media (prefers-reduced-motion: reduce) {\n\t\ttransition: none;\n\t}\n\n\t&:focus {\n\t\tbox-shadow: none;\n\t}\n\n\t&.ck-on {\n\t\tborder-color: var(--ck-color-focus-border);\n\t\tbackground: var(--ck-color-focus-outer-shadow);\n\t}\n}\n\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n:root {\n\t--ck-table-selected-cell-background: hsla(208, 90%, 80%, .3);\n}\n\n.ck.ck-editor__editable .table table {\n\t& td.ck-editor__editable_selected,\n\t& th.ck-editor__editable_selected {\n\t\tposition: relative;\n\t\tcaret-color: transparent;\n\t\toutline: unset;\n\t\tbox-shadow: unset;\n\n\t\t/* https://github.com/ckeditor/ckeditor5/issues/6446 */\n\t\t&:after {\n\t\t\tcontent: '';\n\t\t\tpointer-events: none;\n\t\t\tbackground-color: var(--ck-table-selected-cell-background);\n\t\t\tposition: absolute;\n\t\t\ttop: 0;\n\t\t\tleft: 0;\n\t\t\tright: 0;\n\t\t\tbottom: 0;\n\t\t}\n\n\t\t& ::selection,\n\t\t&:focus {\n\t\t\tbackground-color: transparent;\n\t\t}\n\n\t\t/*\n\t\t * To reduce the amount of noise, all widgets in the table selection have no outline and no selection handle.\n\t\t * See https://github.com/ckeditor/ckeditor5/issues/9491.\n\t\t */\n\t\t& .ck-widget {\n\t\t\toutline: unset;\n\n\t\t\t& > .ck-widget__selection-handle {\n\t\t\t\tdisplay: none;\n\t\t\t}\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck-content {\n\t/* Text alignment of the table header should match the editor settings and override the native browser styling,\n\twhen content is available outside the editor. See https://github.com/ckeditor/ckeditor5/issues/6638 */\n\t&[dir=\"rtl\"] .table th {\n\t\ttext-align: right;\n\t}\n\n\t&[dir=\"ltr\"] .table th {\n\t\ttext-align: left;\n\t}\n\n\t& figure.table:not(.layout-table) {\n\t\tdisplay: table;\n\n\t\t& > table {\n\t\t\twidth: 100%;\n\t\t\theight: 100%;\n\t\t}\n\t}\n\n\t& figure.table:not(.layout-table),\n\t& table.table:not(.layout-table) {\n\t\t/* Give the table widget some air and center it horizontally */\n\t\t/* The first value should be equal to --ck-spacing-large variable if used in the editor context\n\t\tto avoid the content jumping (See https://github.com/ckeditor/ckeditor5/issues/9825). */\n\t\tmargin: 0.9em auto;\n\t}\n\n\t& table.table:not(.layout-table),\n\t& figure.table:not(.layout-table) > table {\n\t\t/* The table cells should have slight borders */\n\t\tborder-collapse: collapse;\n\t\tborder-spacing: 0;\n\n\t\t/* The outer border of the table should be slightly darker than the inner lines.\n\t\tAlso see https://github.com/ckeditor/ckeditor5-table/issues/50. */\n\t\tborder: 1px double hsl(0, 0%, 70%);\n\n\t\t& > thead,\n\t\t& > tbody {\n\t\t\t/* The linter is disabled here because linter is confused when resolving the `table.table:not(.layout-table)`\n\t\t\tand `figure.table:not(.layout-table) > table` selectors combined with below selectors.\n\t\t\tThere is no need to split it into two large structures with same code just to make linter happy. */\n\t\t\t/* stylelint-disable no-descending-specificity */\n\t\t\t& > tr {\n\t\t\t\t& > th {\n\t\t\t\t\tfont-weight: bold;\n\t\t\t\t\tbackground: hsla(0, 0%, 0%, 5%);\n\t\t\t\t}\n\n\t\t\t\t& > td,\n\t\t\t\t& > th {\n\t\t\t\t\t/* stylelint-enable no-descending-specificity */\n\t\t\t\t\tmin-width: 2em;\n\t\t\t\t\tpadding: .4em;\n\n\t\t\t\t\t/* The border is inherited from .ck-editor__nested-editable styles, so theoretically it's not necessary here.\n\t\t\t\t\tHowever, the border is a content style, so it should use .ck-content (so it works outside the editor).\n\t\t\t\t\tHence, the duplication. See https://github.com/ckeditor/ckeditor5/issues/6314 */\n\t\t\t\t\tborder: 1px solid hsl(0, 0%, 75%);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n/**\n * Expanding the table to the full height of the parent container is necessary because tables\n * are rendered inside <figure> elements, which is kinda buggy in table height calculation.\n * While setting `height: 100%` fixes the issue in the editing mode described here:\n * https://github.com/ckeditor/ckeditor5/issues/6186\n *\n * it's causing another issue with the table height in the print preview mode here:\n * https://github.com/ckeditor/ckeditor5/issues/16856\n *\n * For now, resetting the height to `initial` in the print mode works as a workaround.\n */\n@media print {\n\t.ck-content figure.table > table {\n\t\theight: initial;\n\t}\n}\n\n.ck-editor__editable .ck-table-bogus-paragraph {\n\t/*\n\t * Use display:inline-block to force Chrome/Safari to limit text mutations to this element.\n\t * See https://github.com/ckeditor/ckeditor5/issues/6062.\n\t */\n\tdisplay: inline-block;\n\n\t/*\n\t * Inline HTML elements nested in the span should always be dimensioned in relation to the whole cell width.\n\t * See https://github.com/ckeditor/ckeditor5/issues/9117.\n\t */\n\twidth: 100%;\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck.ck-input-color {\n\twidth: 100%;\n\tdisplay: flex;\n\tflex-direction: row-reverse;\n\n\t& > input.ck.ck-input-text {\n\t\tmin-width: auto;\n\t\tflex-grow: 1;\n\t}\n\n\t& > div.ck.ck-dropdown {\n\t\tmin-width: auto;\n\n\t\t/* This dropdown has no arrow but a color preview instead. */\n\t\t& > .ck-input-color__button .ck-dropdown__arrow {\n\t\t\tdisplay: none;\n\t\t}\n\t}\n\n\t& .ck.ck-input-color__button {\n\t\t/* Resolving issue with misaligned buttons on Safari (see #10589) */\n\t\tdisplay: flex;\n\n\t\t& .ck.ck-input-color__button__preview {\n\t\t\tposition: relative;\n\t\t\toverflow: hidden;\n\n\t\t\t& > .ck.ck-input-color__button__preview__no-color-indicator {\n\t\t\t\tposition: absolute;\n\t\t\t\tdisplay: block;\n\t\t\t}\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"@ckeditor/ckeditor5-ui/theme/mixins/_dir.css\";\n@import \"../mixins/_rounded.css\";\n\n.ck.ck-input-color {\n\t& > .ck.ck-input-text {\n\t\t@mixin ck-dir ltr {\n\t\t\tborder-top-right-radius: 0;\n\t\t\tborder-bottom-right-radius: 0;\n\t\t}\n\n\t\t@mixin ck-dir rtl {\n\t\t\tborder-top-left-radius: 0;\n\t\t\tborder-bottom-left-radius: 0;\n\t\t}\n\n\t\t/* Make sure the focused input is always on top of the dropdown button so its\n\t\t   outline and border are never cropped (also when the input is read-only). */\n\t\t&:focus {\n\t\t\tz-index: 0;\n\t\t}\n\t}\n\n\t& > .ck.ck-dropdown {\n\t\t& > .ck.ck-button.ck-input-color__button {\n\t\t\tpadding: 0;\n\n\t\t\t@mixin ck-dir ltr {\n\t\t\t\tborder-top-left-radius: 0;\n\t\t\t\tborder-bottom-left-radius: 0;\n\n\t\t\t\t&:not(:focus) {\n\t\t\t\t\tborder-left: 1px solid transparent;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t@mixin ck-dir rtl {\n\t\t\t\tborder-top-right-radius: 0;\n\t\t\t\tborder-bottom-right-radius: 0;\n\n\t\t\t\t&:not(:focus) {\n\t\t\t\t\tborder-right: 1px solid transparent;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&.ck-disabled {\n\t\t\t\tbackground: var(--ck-color-input-disabled-background);\n\t\t\t}\n\n\t\t\t& > .ck.ck-input-color__button__preview {\n\t\t\t\t@mixin ck-rounded-corners;\n\n\t\t\t\twidth: 20px;\n\t\t\t\theight: 20px;\n\t\t\t\tborder: 1px solid var(--ck-color-input-border);\n\n\t\t\t\t& > .ck.ck-input-color__button__preview__no-color-indicator {\n\t\t\t\t\ttop: -30%;\n\t\t\t\t\tleft: 50%;\n\t\t\t\t\theight: 150%;\n\t\t\t\t\twidth: 8%;\n\t\t\t\t\tbackground: hsl(0, 100%, 50%);\n\t\t\t\t\tborder-radius: 2px;\n\t\t\t\t\ttransform: rotate(45deg);\n\t\t\t\t\ttransform-origin: 50%;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t& .ck.ck-input-color__remove-color {\n\t\twidth: 100%;\n\t\tpadding: calc(var(--ck-spacing-standard) / 2) var(--ck-spacing-standard);\n\n\t\tborder-bottom-left-radius: 0;\n\t\tborder-bottom-right-radius: 0;\n\n\t\t&:not(:focus) {\n\t\t\tborder-bottom: 1px solid var(--ck-color-input-border);\n\t\t}\n\n\t\t@mixin ck-dir ltr {\n\t\t\tborder-top-right-radius: 0;\n\t\t}\n\n\t\t@mixin ck-dir rtl {\n\t\t\tborder-top-left-radius: 0;\n\t\t}\n\n\t\t& .ck.ck-icon {\n\t\t\tmargin-right: var(--ck-spacing-standard);\n\n\t\t\t@mixin ck-dir rtl {\n\t\t\t\tmargin-right: 0;\n\t\t\t\tmargin-left: var(--ck-spacing-standard);\n\t\t\t}\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck.ck-form__row {\n\t&.ck-table-form__action-row {\n\t\t& .ck-button-save,\n\t\t& .ck-button-cancel {\n\t\t\tjustify-content: center;\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"@ckeditor/ckeditor5-ui/theme/mixins/_dir.css\";\n\n.ck.ck-form__row {\n\t/* Ignore labels that work as fieldset legends */\n\t& > *:not(.ck-label) {\n\t\t& + * {\n\t\t\tmargin-inline-start: var(--ck-spacing-large);\n\t\t}\n\t}\n\n\t& > .ck-label {\n\t\twidth: 100%;\n\t\tmin-width: 100%;\n\t}\n\n\t&.ck-table-form__action-row {\n\t\tmargin-top: var(--ck-spacing-large);\n\n\t\t& .ck-button .ck-button__label {\n\t\t\tcolor: var(--ck-color-text);\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck.ck-table-form {\n\t& .ck-form__row {\n\t\t&.ck-table-form__border-row {\n\t\t\tflex-wrap: wrap;\n\t\t}\n\n\t\t&.ck-table-form__background-row {\n\t\t\tflex-wrap: wrap;\n\t\t}\n\n\t\t&.ck-table-form__dimensions-row {\n\t\t\tflex-wrap: wrap;\n\t\t\talign-items: center;\n\n\t\t\t& .ck-labeled-field-view {\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-direction: column-reverse;\n\t\t\t\talign-items: center;\n\n\t\t\t\t& .ck.ck-dropdown {\n\t\t\t\t\tflex-grow: 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t& .ck-table-form__dimension-operator {\n\t\t\t\tflex-grow: 0;\n\t\t\t}\n\t\t}\n\n\t\t/* Ignore labels that work as fieldset legends */\n\t\t/* Fallback for table dimension operator */\n\t\t& > *:not(.ck-label, .ck-table-form__dimension-operator) {\n\t\t\tflex-grow: 1;\n\t\t}\n\t}\n\n\t& .ck.ck-labeled-field-view {\n\t\t/* Allow absolute positioning of the status (error) balloons. */\n\t\tposition: relative;\n\n\t\t& .ck.ck-labeled-field-view__status {\n\t\t\tposition: absolute;\n\t\t\tleft: 50%;\n\t\t\tbottom: calc( -1 * var(--ck-table-properties-error-arrow-size) );\n\t\t\ttransform: translate(-50%,100%);\n\n\t\t\t/* Make sure the balloon status stays on top of other form elements. */\n\t\t\tz-index: 1;\n\n\t\t\t/* The arrow pointing towards the field. */\n\t\t\t&::after {\n\t\t\t\tcontent: \"\";\n\t\t\t\tposition: absolute;\n\t\t\t\ttop: calc( -1 * var(--ck-table-properties-error-arrow-size) );\n\t\t\t\tleft: 50%;\n\t\t\t\ttransform: translateX( -50% );\n\t\t\t}\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"../mixins/_rounded.css\";\n\n:root {\n\t--ck-table-properties-error-arrow-size: 6px;\n\t--ck-table-properties-min-error-width: 150px;\n}\n\n.ck.ck-table-form {\n\t& .ck-form__row {\n\t\t&.ck-table-form__border-row {\n\t\t\t& .ck-labeled-field-view {\n\t\t\t\t& > .ck-label {\n\t\t\t\t\tfont-size: var(--ck-font-size-tiny);\n\t\t\t\t\ttext-align: center;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t& .ck-table-form__border-style,\n\t\t\t& .ck-table-form__border-width {\n\t\t\t\twidth: 80px;\n\t\t\t\tmin-width: 80px;\n\t\t\t\tmax-width: 80px;\n\t\t\t}\n\t\t}\n\n\t\t&.ck-table-form__dimensions-row {\n\t\t\tpadding: 0;\n\n\t\t\t& .ck-table-form__dimensions-row__width,\n\t\t\t& .ck-table-form__dimensions-row__height {\n\t\t\t\tmargin: 0\n\t\t\t}\n\n\t\t\t& .ck-table-form__dimension-operator {\n\t\t\t\talign-self: flex-end;\n\t\t\t\tdisplay: inline-block;\n\t\t\t\theight: var(--ck-ui-component-min-height);\n\t\t\t\tline-height: var(--ck-ui-component-min-height);\n\t\t\t\tmargin: 0 var(--ck-spacing-small);\n\t\t\t}\n\t\t}\n\t}\n\n\t& .ck.ck-labeled-field-view {\n\t\tpadding-top: var(--ck-spacing-standard);\n\n\t\t& .ck.ck-labeled-field-view__status {\n\t\t\t@mixin ck-rounded-corners;\n\n\t\t\tbackground: var(--ck-color-base-error);\n\t\t\tcolor: var(--ck-color-base-background);\n\t\t\tpadding: var(--ck-spacing-small) var(--ck-spacing-medium);\n\t\t\tmin-width: var(--ck-table-properties-min-error-width);\n\t\t\ttext-align: center;\n\n\t\t\t/* The arrow pointing towards the field. */\n\t\t\t&::after {\n\t\t\t\tborder-color: transparent transparent var(--ck-color-base-error) transparent;\n\t\t\t\tborder-width: 0 var(--ck-table-properties-error-arrow-size) var(--ck-table-properties-error-arrow-size) var(--ck-table-properties-error-arrow-size);\n\t\t\t\tborder-style: solid;\n\t\t\t}\n\n\t\t\tanimation: ck-table-form-labeled-view-status-appear .15s ease both;\n\n\t\t\t@media (prefers-reduced-motion: reduce) {\n\t\t\t\tanimation: none;\n\t\t\t}\n\t\t}\n\n\t\t/* Hide the error balloon when the field is blurred. Makes the experience much more clear. */\n\t\t& .ck-input.ck-error:not(:focus) + .ck.ck-labeled-field-view__status {\n\t\t\tdisplay: none;\n\t\t}\n\t}\n}\n\n@keyframes ck-table-form-labeled-view-status-appear {\n\t0% {\n\t\topacity: 0;\n\t}\n\n\t100% {\n\t\topacity: 1;\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck.ck-table-properties-form {\n\t& .ck-form__row {\n\t\t&.ck-table-properties-form__alignment-row {\n\t\t\tflex-wrap: wrap;\n\t\t\tflex-basis: 0;\n\t\t\talign-content: baseline;\n\n\t\t\t& .ck.ck-toolbar .ck-toolbar__items {\n\t\t\t\tflex-wrap: nowrap;\n\t\t\t}\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck.ck-table-properties-form {\n\twidth: 320px;\n\n\t& .ck-form__row {\n\t\t&.ck-table-properties-form__alignment-row {\n\t\t\talign-self: flex-end;\n\t\t\tpadding: 0;\n\n\t\t\t& .ck.ck-toolbar {\n\t\t\t\tbackground: none;\n\n\t\t\t\t/* Compensate for missing input label that would push the margin (toolbar has no inputs). */\n\t\t\t\tmargin-top: var(--ck-spacing-standard);\n\n\t\t\t\t& .ck-toolbar__items > * {\n\t\t\t\t\twidth: 40px;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck.ck-table-cell-properties-form {\n\t& .ck-form__row {\n\t\t&.ck-table-cell-properties-form__alignment-row {\n\t\t\tflex-wrap: wrap;\n\n\t\t\t& .ck.ck-toolbar {\n\t\t\t\t&:first-of-type {\n\t\t\t\t\t/* 4 buttons out of 7 (h-alignment + v-alignment) = 0.57 */\n\t\t\t\t\tflex-grow: 0.57;\n\t\t\t\t}\n\n\t\t\t\t&:last-of-type {\n\t\t\t\t\t/* 3 buttons out of 7 (h-alignment + v-alignment) = 0.43 */\n\t\t\t\t\tflex-grow: 0.43;\n\t\t\t\t}\n\n\t\t\t\t& .ck-button {\n\t\t\t\t\tflex-grow: 1;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck.ck-table-cell-properties-form {\n\twidth: 320px;\n\n\t& .ck-form__row {\n\t\t&.ck-table-cell-properties-form__padding-row {\n\t\t\talign-self: flex-end;\n\t\t\tpadding: 0;\n\t\t\twidth: 25%;\n\t\t}\n\n\t\t&.ck-table-cell-properties-form__alignment-row {\n\t\t\t& .ck.ck-toolbar {\n\t\t\t\tbackground: none;\n\n\t\t\t\t/* Compensate for missing input label that would push the margin (toolbar has no inputs). */\n\t\t\t\tmargin-top: var(--ck-spacing-standard);\n\t\t\t}\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n:root {\n\t--ck-highlight-marker-yellow: hsl(60, 97%, 73%);\n\t--ck-highlight-marker-green: hsl(120, 93%, 68%);\n\t--ck-highlight-marker-pink: hsl(345, 96%, 73%);\n\t--ck-highlight-marker-blue: hsl(201, 97%, 72%);\n\t--ck-highlight-pen-red: hsl(0, 85%, 49%);\n\t--ck-highlight-pen-green: hsl(112, 100%, 27%);\n}\n\n@define-mixin highlight-marker-color $color {\n\t.ck-content .marker-$color {\n\t\tbackground-color: var(--ck-highlight-marker-$color);\n\t}\n}\n\n@define-mixin highlight-pen-color $color {\n\t.ck-content .pen-$color {\n\t\tcolor: var(--ck-highlight-pen-$color);\n\n\t\t/* Override default yellow background of `<mark>` from user agent stylesheet */\n\t\tbackground-color: transparent;\n\t}\n}\n\n@mixin highlight-marker-color yellow;\n@mixin highlight-marker-color green;\n@mixin highlight-marker-color pink;\n@mixin highlight-marker-color blue;\n\n@mixin highlight-pen-color red;\n@mixin highlight-pen-color green;\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n:root {\n\t--ck-todo-list-checkmark-size: 16px;\n}\n\n@define-mixin todo-list-checkbox {\n\t-webkit-appearance: none;\n\tdisplay: inline-block;\n\tposition: relative;\n\twidth: var(--ck-todo-list-checkmark-size);\n\theight: var(--ck-todo-list-checkmark-size);\n\tvertical-align: middle;\n\n\t/* Needed on iOS */\n\tborder: 0;\n\n\t/* LTR styles */\n\tleft: -25px;\n\tmargin-right: -15px;\n\tright: 0;\n\tmargin-left: 0;\n\n\t/* RTL styles */\n\t[dir=rtl]& {\n\t\tleft: 0;\n\t\tmargin-right: 0;\n\t\tright: -25px;\n\t\tmargin-left: -15px;\n\t}\n\n\t&::before {\n\t\tdisplay: block;\n\t\tposition: absolute;\n\t\tbox-sizing: border-box;\n\t\tcontent: '';\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tborder: 1px solid hsl(0, 0%, 20%);\n\t\tborder-radius: 2px;\n\t\ttransition: 250ms ease-in-out box-shadow;\n\n\t\t@media (prefers-reduced-motion: reduce) {\n\t\t\ttransition: none;\n\t\t}\n\t}\n\n\t&::after {\n\t\tdisplay: block;\n\t\tposition: absolute;\n\t\tbox-sizing: content-box;\n\t\tpointer-events: none;\n\t\tcontent: '';\n\n\t\t/* Calculate tick position, size and border-width proportional to the checkmark size. */\n\t\tleft: calc( var(--ck-todo-list-checkmark-size) / 3 );\n\t\ttop: calc( var(--ck-todo-list-checkmark-size) / 5.3 );\n\t\twidth: calc( var(--ck-todo-list-checkmark-size) / 5.3 );\n\t\theight: calc( var(--ck-todo-list-checkmark-size) / 2.6 );\n\t\tborder-style: solid;\n\t\tborder-color: transparent;\n\t\tborder-width: 0 calc( var(--ck-todo-list-checkmark-size) / 8 ) calc( var(--ck-todo-list-checkmark-size) / 8 ) 0;\n\t\ttransform: rotate(45deg);\n\t}\n\n\t&[checked] {\n\t\t&::before {\n\t\t\tbackground: hsl(126, 64%, 41%);\n\t\t\tborder-color: hsl(126, 64%, 41%);\n\t\t}\n\n\t\t&::after {\n\t\t\tborder-color: hsl(0, 0%, 100%);\n\t\t}\n\t}\n}\n\n/*\n * To-do list content styles.\n */\n.ck-content .todo-list {\n\tlist-style: none;\n\n\t& li {\n\t\tposition: relative;\n\t\tmargin-bottom: 5px;\n\n\t\t& .todo-list {\n\t\t\tmargin-top: 5px;\n\t\t}\n\t}\n\n\t& .todo-list__label {\n\t\t& > input {\n\t\t\t@mixin todo-list-checkbox;\n\t\t}\n\n\t\t& .todo-list__label__description {\n\t\t\tvertical-align: middle;\n\t\t}\n\n\t\t&.todo-list__label_without-description input[type=checkbox] {\n\t\t\tposition: absolute;\n\t\t}\n\t}\n}\n\n/*\n * To-do list editing view styles.\n */\n.ck-editor__editable.ck-content .todo-list .todo-list__label {\n\t/*\n\t * To-do list should be interactive only during the editing\n\t * (https://github.com/ckeditor/ckeditor5/issues/2090).\n\t */\n\t& > input,\n\t& > span[contenteditable=false] > input {\n\t\tcursor: pointer;\n\n\t\t&:hover::before {\n\t\t\tbox-shadow: 0 0 0 5px hsla(0, 0%, 0%, 0.1);\n\t\t}\n\t}\n\n\t/*\n\t * Document Lists - editing view has an additional span around checkbox.\n\t */\n\t& > span[contenteditable=false] > input {\n\t\t@mixin todo-list-checkbox;\n\t}\n\n\t&.todo-list__label_without-description {\n\t\t& input[type=checkbox] {\n\t\t\tposition: absolute;\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck-content pre {\n\tpadding: 1em;\n\tcolor: hsl(0, 0%, 20.8%);\n\tbackground: hsla(0, 0%, 78%, 0.3);\n\tborder: 1px solid hsl(0, 0%, 77%);\n\tborder-radius: 2px;\n\n\t/* Code block are language direction–agnostic. */\n\ttext-align: left;\n\tdirection: ltr;\n\n\ttab-size: 4;\n\twhite-space: pre-wrap;\n\n\t/* Don't inherit the style, e.g. when in a block quote. */\n\tfont-style: normal;\n\n\t/* Don't let the code be squashed e.g. when in a table cell. */\n\tmin-width: 200px;\n\n\t& code {\n\t\tbackground: unset;\n\t\tpadding: 0;\n\t\tborder-radius: 0;\n\t}\n}\n\n.ck.ck-editor__editable pre {\n\tposition: relative;\n\n\t&[data-language]::after {\n\t\tcontent: attr(data-language);\n\t\tposition: absolute;\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n:root {\n\t--ck-color-code-block-label-background: hsl(0, 0%, 46%);\n}\n\n.ck.ck-editor__editable pre[data-language]::after {\n\ttop: -1px;\n\tright: 10px;\n\tbackground: var(--ck-color-code-block-label-background);\n\n\tfont-size: 10px;\n\tfont-family: var(--ck-font-face);\n\tline-height: 16px;\n\tpadding: var(--ck-spacing-tiny) var(--ck-spacing-medium);\n\tcolor: hsl(0, 0%, 100%);\n\twhite-space: nowrap;\n}\n\n.ck.ck-code-block-dropdown .ck-dropdown__panel {\n\t/* There could be dozens of languages available. Use scroll to prevent a 10e6px dropdown. */\n\tmax-height: 250px;\n\toverflow-y: auto;\n\toverflow-x: hidden;\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck.ck-list-properties {\n\t/* When there are no list styles and there is no collapsible. */\n\t&.ck-list-properties_without-styles {\n\t\tpadding: var(--ck-spacing-large);\n\n\t\t& > * {\n\t\t\tmin-width: 14em;\n\n\t\t\t& + * {\n\t\t\t\tmargin-top: var(--ck-spacing-standard);\n\t\t\t}\n\t\t}\n\t}\n\n\t/*\n\t * When the numbered list property fields (start at, reversed) should be displayed,\n\t * more horizontal space is needed. Reconfigure the style grid to create that space.\n\t */\n\t&.ck-list-properties_with-numbered-properties {\n\t\t& > .ck-list-styles-list {\n\t\t\tgrid-template-columns: repeat( 4, auto );\n\t\t}\n\n\t\t/* When list styles are rendered and property fields are in a collapsible. */\n\t\t& > .ck-collapsible {\n\t\t\tborder-top: 1px solid var(--ck-color-base-border);\n\n\t\t\t& > .ck-collapsible__children {\n\t\t\t\t& > * {\n\t\t\t\t\twidth: 100%;\n\n\t\t\t\t\t& + * {\n\t\t\t\t\t\tmargin-top: var(--ck-spacing-standard);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t& .ck.ck-numbered-list-properties__start-index .ck-input {\n\t\tmin-width: auto;\n\t\twidth: 100%;\n\t}\n\n\t& .ck.ck-numbered-list-properties__reversed-order {\n\t\tbackground: transparent;\n\t\tpadding-left: 0;\n\t\tpadding-right: 0;\n\t\tmargin-bottom: calc(-1 * var(--ck-spacing-tiny));\n\n\t\t&:active, &:hover {\n\t\t\tbox-shadow: none;\n\t\t\tborder-color: transparent;\n\t\t\tbackground: none;\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck.ck-list-styles-list {\n\tdisplay: grid;\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n:root {\n\t--ck-list-style-button-size: 44px;\n}\n\n.ck.ck-list-styles-list {\n\tgrid-template-columns: repeat( 3, auto );\n\trow-gap: var(--ck-spacing-medium);\n\tcolumn-gap: var(--ck-spacing-medium);\n\tpadding: var(--ck-spacing-large);\n\n\t& .ck-button {\n\t\t/* Make the button look like a thumbnail (the icon \"takes it all\"). */\n\t\twidth: var(--ck-list-style-button-size);\n\t\theight: var(--ck-list-style-button-size);\n\t\tpadding: 0;\n\n\t\t/*\n\t\t * Buttons are aligned by the grid so disable default button margins to not collide with the\n\t\t * gaps in the grid.\n\t\t */\n\t\tmargin: 0;\n\n\t\t/*\n\t\t * Make sure the button border (which is displayed on focus, BTW) does not steal pixels\n\t\t * from the button dimensions and, as a result, decrease the size of the icon\n\t\t * (which becomes blurry as it scales down).\n\t\t */\n\t\tbox-sizing: content-box;\n\n\t\t& .ck-icon {\n\t\t\twidth: var(--ck-list-style-button-size);\n\t\t\theight: var(--ck-list-style-button-size);\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"@ckeditor/ckeditor5-theme-lark/theme/mixins/_rounded.css\";\n@import \"@ckeditor/ckeditor5-theme-lark/theme/mixins/_focus.css\";\n@import \"@ckeditor/ckeditor5-theme-lark/theme/mixins/_shadow.css\";\n\n.ck-source-editing-area {\n\tposition: relative;\n\toverflow: hidden;\n}\n\n.ck-source-editing-area::after,\n.ck-source-editing-area textarea {\n\tpadding: var(--ck-spacing-large);\n\tmargin: 0;\n\tborder: 1px solid transparent;\n\tline-height: var(--ck-line-height-base);\n\tfont-size: var(--ck-font-size-normal);\n\tfont-family: monospace;\n\twhite-space: pre-wrap;\n}\n\n.ck-source-editing-area::after {\n\tcontent: attr(data-value) \" \";\n\tvisibility: hidden;\n\tdisplay: block;\n}\n\n.ck-source-editing-area textarea {\n\tposition: absolute;\n\twidth: 100%;\n\theight: 100%;\n\tresize: none;\n\toutline: none;\n\toverflow: hidden;\n\tbox-sizing: border-box;\n\n\tborder-color: var(--ck-color-base-border);\n\n\t@mixin ck-rounded-corners {\n\t\tborder-top-left-radius: 0;\n\t\tborder-top-right-radius: 0;\n\t}\n\n\t&:not([readonly]):focus {\n\t\t@mixin ck-focus-ring;\n\t\t@mixin ck-box-shadow var(--ck-inner-shadow);\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n:root {\n\t--ck-html-object-embed-unfocused-outline-width: 1px;\n}\n\n.ck-widget.html-object-embed {\n\tfont-size: var(--ck-font-size-base);\n\tbackground-color: var(--ck-color-base-foreground);\n\tpadding: var(--ck-spacing-small);\n\t/* Leave space for label */\n\tpadding-top: calc(var(--ck-font-size-tiny) + var(--ck-spacing-large));\n\tmin-width: calc(76px + var(--ck-spacing-standard));\n\n\t&:not(.ck-widget_selected):not(:hover) {\n\t\toutline: var(--ck-html-object-embed-unfocused-outline-width) dashed var(--ck-color-widget-blurred-border);\n\t}\n\n\t&::before {\n\t\tfont-weight: normal;\n\t\tfont-style: normal;\n\t\tposition: absolute;\n\t\tcontent: attr(data-html-object-embed-label);\n\t\ttop: 0;\n\t\tleft: var(--ck-spacing-standard);\n\t\tbackground: hsl(0deg 0% 60%);\n\t\ttransition: background var(--ck-widget-handler-animation-duration) var(--ck-widget-handler-animation-curve);\n\t\tpadding: calc(var(--ck-spacing-tiny) + var(--ck-html-object-embed-unfocused-outline-width)) var(--ck-spacing-small) var(--ck-spacing-tiny);\n\t\tborder-radius: 0 0 var(--ck-border-radius) var(--ck-border-radius);\n\t\tcolor: var(--ck-color-base-background);\n\t\tfont-size: var(--ck-font-size-tiny);\n\t\tfont-family: var(--ck-font-face);\n\t}\n\n\t/* Make space for label. */\n\t& .ck-widget__type-around .ck-widget__type-around__button.ck-widget__type-around__button_before {\n\t\tmargin-left: 50px;\n\t}\n\n\t& .html-object-embed__content {\n\t\t/* Disable user interaction with embed content */\n\t\tpointer-events: none;\n\t}\n}\n\ndiv.ck-widget.html-object-embed {\n\tmargin: 1em auto;\n}\n\nspan.ck-widget.html-object-embed {\n\tdisplay: inline-block;\n}\n\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck.ck-editor__editable {\n\t& .image,\n\t& .image-inline {\n\t\tposition: relative;\n\t}\n\n\t/* Upload progress bar. */\n\t& .image .ck-progress-bar,\n\t& .image-inline .ck-progress-bar {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck.ck-editor__editable {\n\t& .image,\n\t& .image-inline {\n\t\t/* Showing animation. */\n\t\t&.ck-appear {\n\t\t\tanimation: fadeIn 700ms;\n\n\t\t\t@media (prefers-reduced-motion: reduce) {\n\t\t\t\topacity: 1;\n\t\t\t\tanimation: none;\n\t\t\t}\n\t\t}\n\t}\n\n\t/* Upload progress bar. */\n\t& .image .ck-progress-bar,\n\t& .image-inline .ck-progress-bar {\n\t\theight: 2px;\n\t\twidth: 0;\n\t\tbackground: var(--ck-color-upload-bar-background);\n\t\ttransition: width 100ms;\n\t}\n}\n\n@keyframes fadeIn {\n\tfrom { opacity: 0; }\n\tto   { opacity: 1; }\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck-image-upload-complete-icon {\n\tdisplay: block;\n\tposition: absolute;\n\n\t/*\n\t * Smaller images should have the icon closer to the border.\n\t * Match the icon position with the linked image indicator brought by the link image feature.\n\t */\n\ttop: min(var(--ck-spacing-medium), 6%);\n\tright: min(var(--ck-spacing-medium), 6%);\n\tborder-radius: 50%;\n\tz-index: 1;\n\n\t&::after {\n\t\tcontent: \"\";\n\t\tposition: absolute;\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n:root {\n\t--ck-color-image-upload-icon: hsl(0, 0%, 100%);\n\t--ck-color-image-upload-icon-background: hsl(120, 100%, 27%);\n\n\t/* Match the icon size with the linked image indicator brought by the link image feature. */\n\t--ck-image-upload-icon-size: 20;\n\t--ck-image-upload-icon-width: 2px;\n\t--ck-image-upload-icon-is-visible: clamp(0px, 100% - 50px, 1px);\n}\n\n.ck-image-upload-complete-icon {\n\topacity: 0;\n\tbackground: var(--ck-color-image-upload-icon-background);\n\tanimation-name: ck-upload-complete-icon-show, ck-upload-complete-icon-hide;\n\tanimation-fill-mode: forwards, forwards;\n\tanimation-duration: 500ms, 500ms;\n\n\t/* To make animation scalable. */\n\tfont-size: calc(1px * var(--ck-image-upload-icon-size));\n\n\t/* <PERSON><PERSON> completed upload icon after 3 seconds. */\n\tanimation-delay: 0ms, 3000ms;\n\n\t/*\n\t * Use CSS math to simulate container queries.\n\t * https://css-tricks.com/the-raven-technique-one-step-closer-to-container-queries/#what-about-showing-and-hiding-things\n\t */\n\toverflow: hidden;\n\twidth: calc(var(--ck-image-upload-icon-is-visible) * var(--ck-image-upload-icon-size));\n\theight: calc(var(--ck-image-upload-icon-is-visible) * var(--ck-image-upload-icon-size));\n\n\t/* This is check icon element made from border-width mixed with animations. */\n\t&::after {\n\t\t/* Because of border transformation we need to \"hard code\" left position. */\n\t\tleft: 25%;\n\n\t\ttop: 50%;\n\t\topacity: 0;\n\t\theight: 0;\n\t\twidth: 0;\n\n\t\ttransform: scaleX(-1) rotate(135deg);\n\t\ttransform-origin: left top;\n\t\tborder-top: var(--ck-image-upload-icon-width) solid var(--ck-color-image-upload-icon);\n\t\tborder-right: var(--ck-image-upload-icon-width) solid var(--ck-color-image-upload-icon);\n\n\t\tanimation-name: ck-upload-complete-icon-check;\n\t\tanimation-duration: 500ms;\n\t\tanimation-delay: 500ms;\n\t\tanimation-fill-mode: forwards;\n\n\t\t/* #1095. While reset is not providing proper box-sizing for pseudoelements, we need to handle it. */\n\t\tbox-sizing: border-box;\n\t}\n\n\t@media (prefers-reduced-motion: reduce) {\n\t\tanimation-duration: 0ms;\n\n\t\t&::after {\n\t\t\tanimation: none;\n\t\t\topacity: 1;\n\t\t\twidth: 0.3em;\n\t\t\theight: 0.45em;\n\t\t}\n\t}\n}\n\n@keyframes ck-upload-complete-icon-show {\n\tfrom {\n\t\topacity: 0;\n\t}\n\n\tto {\n\t\topacity: 1;\n\t}\n}\n\n@keyframes ck-upload-complete-icon-hide {\n\tfrom {\n\t\topacity: 1;\n\t}\n\n\tto {\n\t\topacity: 0;\n\t}\n}\n\n@keyframes ck-upload-complete-icon-check {\n\t0% {\n\t\topacity: 1;\n\t\twidth: 0;\n\t\theight: 0;\n\t}\n\t33% {\n\t\twidth: 0.3em;\n\t\theight: 0;\n\t}\n\t100% {\n\t\topacity: 1;\n\t\twidth: 0.3em;\n\t\theight: 0.45em;\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck .ck-upload-placeholder-loader {\n\tposition: absolute;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\ttop: 0;\n\tleft: 0;\n\n\t&::before {\n\t\tcontent: '';\n\t\tposition: relative;\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n:root {\n\t--ck-color-upload-placeholder-loader: hsl(0, 0%, 70%);\n\t--ck-upload-placeholder-loader-size: 32px;\n\t--ck-upload-placeholder-image-aspect-ratio: 2.8;\n}\n\n.ck .ck-image-upload-placeholder {\n\t/* We need to control the full width of the SVG gray background. */\n\twidth: 100%;\n\tmargin: 0;\n\n\t&.image-inline {\n\t\twidth: calc( 2 * var(--ck-upload-placeholder-loader-size) * var(--ck-upload-placeholder-image-aspect-ratio) );\n\t}\n\n\t& img {\n\t\t/*\n\t\t * This is an arbitrary aspect for a 1x1 px GIF to display to the user. Not too tall, not too short.\n\t\t * There's nothing special about this number except that it should make the image placeholder look like\n\t\t * a real image during this short period after the upload started and before the image was read from the\n\t\t * file system (and a rich preview was loaded).\n\t\t */\n\t\taspect-ratio: var(--ck-upload-placeholder-image-aspect-ratio);\n\t}\n}\n\n.ck .ck-upload-placeholder-loader {\n\twidth: 100%;\n\theight: 100%;\n\n\t&::before {\n\t\twidth: var(--ck-upload-placeholder-loader-size);\n\t\theight: var(--ck-upload-placeholder-loader-size);\n\t\tborder-radius: 50%;\n\t\tborder-top: 3px solid var(--ck-color-upload-placeholder-loader);\n\t\tborder-right: 2px solid transparent;\n\t\tanimation: ck-upload-placeholder-loader 1s linear infinite;\n\t}\n}\n\n@keyframes ck-upload-placeholder-loader {\n\tto {\n\t\ttransform: rotate( 360deg );\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n:root {\n\t--ck-color-selector-column-resizer-hover: var(--ck-color-base-active);\n\t--ck-table-column-resizer-width: 7px;\n\n\t/* The offset used for absolute positioning of the resizer element, so that it is placed exactly above the cell border.\n\t   The value is: minus half the width of the resizer decreased additionaly by the half the width of the border (0.5px). */\n\t--ck-table-column-resizer-position-offset: calc(var(--ck-table-column-resizer-width) * -0.5 - 0.5px);\n}\n\n.ck-content .table .ck-table-resized {\n\ttable-layout: fixed;\n}\n\n.ck-content .table td,\n.ck-content .table th {\n\t/* To prevent text overflowing beyond its cell when columns are resized by resize handler\n\t(https://github.com/ckeditor/ckeditor5/pull/14379#issuecomment-**********). */\n\toverflow-wrap: break-word;\n}\n\n.ck.ck-editor__editable .table td,\n.ck.ck-editor__editable .table th {\n\t/* The resizer element is placed inside each cell, so it must be positioned relatively to the cell. */\n\tposition: relative;\n}\n\n.ck.ck-editor__editable .table .ck-table-column-resizer {\n\tposition: absolute;\n\ttop: 0;\n\tbottom: 0;\n\tright: var(--ck-table-column-resizer-position-offset);\n\twidth: var(--ck-table-column-resizer-width);\n\tcursor: col-resize;\n\tuser-select: none;\n\tz-index: var(--ck-z-default);\n}\n\n.ck.ck-editor__editable.ck-column-resize_disabled .table .ck-table-column-resizer {\n\tdisplay: none;\n}\n\n/* The resizer elements, which are extended to an extremely high height, break the drag & drop feature in Chrome. To make it work again,\n   all resizers must be hidden while the table is dragged. */\n.ck.ck-editor__editable .table[draggable] .ck-table-column-resizer {\n\tdisplay: none;\n}\n\n.ck.ck-editor__editable .table .ck-table-column-resizer:hover,\n.ck.ck-editor__editable .table .ck-table-column-resizer__active {\n\tbackground-color: var(--ck-color-selector-column-resizer-hover);\n\topacity: 0.25;\n}\n\n.ck.ck-editor__editable[dir=rtl] .table .ck-table-column-resizer {\n\tleft: var(--ck-table-column-resizer-position-offset);\n\tright: unset;\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck-editor__editable {\n\t&.ck-editor__editable_inline {\n\t\t& > .ck-widget.ck-widget_with-selection-handle.layout-table {\n\t\t\t&:first-child {\n\t\t\t\tmargin-top: var(--ck-spacing-large);\n\t\t\t}\n\t\t\t/*\n\t\t\t* This value should match with the default margins of the block elements (like .media, .image or .table)\n\t\t\t* to avoid a content jumping when the fake selection container shows up (See https://github.com/ckeditor/ckeditor5/issues/9825).\n\t\t\t*/\n\t\t\t&:last-child,\n\t\t\t/* Fallback for hidden fake selection div */\n\t\t\t&:nth-last-child(2):has( + .ck-fake-selection-container) {\n\t\t\t\tmargin-bottom: var(--ck-spacing-large);\n\t\t\t}\n\t\t}\n\t}\n\n\t& .table.layout-table {\n\t\tdisplay: table;\n\t\tmargin: 0;\n\n\t\t/* Widget type around overrides. */\n\t\t&.ck-widget {\n\t\t\t&:hover {\n\t\t\t\t/* To prevent the widget outline from being cut off at the bottom\n\t\t\t\twhen the next cell or table has a background color, for example. */\n\t\t\t\tz-index: var(--ck-z-default);\n\t\t\t}\n\n\t\t\t&:hover > .ck-widget__selection-handle {\n\t\t\t\topacity: 0.75;\n\t\t\t\tvisibility: visible;\n\n\t\t\t\t&:hover {\n\t\t\t\t\topacity: 1;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t/* Because of setting the opacity to 0.75 to the selection handle for the layout table,\n\t\t\tit overrides the similar selector in widget (specificity is higher),\n\t\t\tthats why we must override it here also. */\n\t\t\t&:has( .ck-widget.table:hover ) > .ck-widget__selection-handle {\n\t\t\t\topacity: 0;\n\t\t\t\tvisibility: hidden;\n\t\t\t}\n\n\t\t\t&.ck-widget_selected {\n\t\t\t\t/* To prevent the widget outline from being cut off at the bottom\n\t\t\t\twhen the next cell or table has a background color, for example. */\n\t\t\t\tz-index: var(--ck-z-default);\n\t\t\t}\n\t\t}\n\t}\n}\n\n.ck-content {\n\t& table.table.layout-table,\n\t& figure.table.layout-table {\n\t\t/* Do not reserve space above and below the layout table. */\n\t\tmargin-top: 0;\n\t\tmargin-bottom: 0;\n\t}\n\n\t& table.table.layout-table,\n\t& figure.table.layout-table > table {\n\t\tborder-spacing: 0;\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n:root {\n\t--ck-table-layout-widget-type-around-button-size: 16px;\n\t--ck-table-layout-widget-type-around-icon-width: 10px;\n\t--ck-table-layout-widget-type-around-icon-height: 8px;\n\t--ck-table-layout-widget-handler-icon-size: 10px;\n\t--ck-table-layout-default-border-color: hsl(0, 0%, 83%);\n\n}\n\n.ck-editor__editable {\n\t& .table.layout-table {\n\t\t& > table {\n\t\t\twidth: 100%;\n\t\t\theight: 100%;\n\n\t\t\t/* Resetting `border-collapse` property to the user agent styles. */\n\t\t\tborder-collapse: revert;\n\n\t\t\t/* The default table layout style in the editing view when the border is unset. */\n\t\t\t&:not(\n\t\t\t[style*=\"border:\"],\n\t\t\t[style*=\"border-top\"],\n\t\t\t[style*=\"border-bottom\"],\n\t\t\t[style*=\"border-left\"],\n\t\t\t[style*=\"border-right\"],\n\t\t\t[style*=\"border-width\"],\n\t\t\t[style*=\"border-style\"],\n\t\t\t[style*=\"border-color\"]) {\n\t\t\t\tborder-color: transparent;\n\t\t\t\tborder-width: 0;\n\t\t\t\toutline: none;\n\t\t\t}\n\t\t}\n\n\t\t& > table > tbody > tr > td {\n\t\t\tbox-shadow: revert;\n\t\t\tpadding: revert;\n\t\t\tmin-width: 2em;\n\t\t\t/* To make the caret visible. */\n\t\t\ttext-indent: 1px;\n\n\t\t\t/* Resets the `min-width` style attribute that was added by the content table style. */\n\t\t\t&[style^=\"width:\"],\n\t\t\t&[style*=\" width:\"],\n\t\t\t&[style*=\";width:\"],\n\t\t\t&[style*=\"'width:\"] {\n\t\t\t\tmin-width: auto;\n\t\t\t}\n\n\t\t\t/* Remove the default background color from the focused table cell. */\n\t\t\t&:focus {\n\t\t\t\tbackground-color: transparent;\n\t\t\t}\n\n\t\t\t/* The default table layout cell style in the editing view when the border is unset.\n\t\t\tBut it need to be more strict cause of style attributes like `border-collapse`, `border-spacing`, etc. */\n\t\t\t&:not(\n\t\t\t[style*=\"border:\"],\n\t\t\t[style*=\"border-top\"],\n\t\t\t[style*=\"border-bottom\"],\n\t\t\t[style*=\"border-left\"],\n\t\t\t[style*=\"border-right\"],\n\t\t\t[style*=\"border-width\"],\n\t\t\t[style*=\"border-style\"],\n\t\t\t[style*=\"border-color\"]) {\n\t\t\t\tborder-color: transparent;\n\t\t\t\t/* Resetting `border-width` property to the user agent styles. */\n\t\t\t\tborder-width: 0;\n\t\t\t\toutline: var(--ck-table-layout-default-border-color) 1px dashed;\n\t\t\t\toutline-offset: -1px;\n\n\t\t\t\t&:focus {\n\t\t\t\t\toutline: var(--ck-color-focus-border) 1px solid;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t/* Reset the `width` and `text-indent` of the bogus paragraph because <td> has 1px text indent. */\n\t\t\t& > .ck-table-bogus-paragraph {\n\t\t\t\twidth: calc(100% - 1px);\n\t\t\t\ttext-indent: 0;\n\t\t\t}\n\t\t}\n\n\t\t/* Widget type around overrides. */\n\t\t&.ck-widget {\n\t\t\t& > .ck-widget__type-around {\n\t\t\t\t--ck-widget-type-around-button-size: var(--ck-table-layout-widget-type-around-button-size);\n\n\t\t\t\t& > .ck-widget__type-around__button {\n\t\t\t\t\t&.ck-widget__type-around__button_before,\n\t\t\t\t\t&.ck-widget__type-around__button_after {\n\t\t\t\t\t\ttransform: translateY(0);\n\t\t\t\t\t\t/* Same value as inline-image widget because it is after the inline-image in HTML structure */\n\t\t\t\t\t\tz-index: 2;\n\t\t\t\t\t}\n\n\t\t\t\t\t&.ck-widget__type-around__button_before {\n\t\t\t\t\t\tmargin-left: var(--ck-table-layout-widget-type-around-button-size);\n\t\t\t\t\t\tleft: min(10%, 30px);\n\t\t\t\t\t\tborder-radius: 0 0 100px 100px;\n\n\t\t\t\t\t\t&::after {\n\t\t\t\t\t\t\tborder-radius: 0 0 100px 100px;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t&.ck-widget__type-around__button_after {\n\t\t\t\t\t\tborder-radius: 100px 100px 0 0;\n\n\t\t\t\t\t\t&::after {\n\t\t\t\t\t\t\tborder-radius: 100px 100px 0 0;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t& svg {\n\t\t\t\t\t\twidth: var(--ck-table-layout-widget-type-around-icon-width);\n\t\t\t\t\t\theight: var(--ck-table-layout-widget-type-around-icon-height);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&.ck-widget_with-selection-handle {\n\t\t\t\t& > .ck-widget__selection-handle {\n\t\t\t\t\t--ck-widget-handler-icon-size: var(--ck-table-layout-widget-handler-icon-size);\n\n\t\t\t\t\ttransform: translateY(calc(0px - var(--ck-widget-outline-thickness)));\n\t\t\t\t\t/* Value increased by 1 as in selected inline-image widget\n\t\t\t\t\tbecause it is before the inline-image in HTML structure. */\n\t\t\t\t\tz-index: 3;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t/* Show fake caret on border not above. */\n\t\t\t&.ck-widget_type-around_show-fake-caret_before {\n\t\t\t\t& > .ck-widget__type-around {\n\t\t\t\t\t& > .ck-widget__type-around__fake-caret {\n\t\t\t\t\t\ttop: 0;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t/* Show fake caret on border not below. */\n\t\t\t&.ck-widget_type-around_show-fake-caret_after {\n\t\t\t\t& > .ck-widget__type-around {\n\t\t\t\t\t& > .ck-widget__type-around__fake-caret {\n\t\t\t\t\t\tbottom: 0;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"@ckeditor/ckeditor5-ui/theme/mixins/_mediacolors.css\";\n\n:root {\n\t--ck-color-selector-caption-background: hsl(0, 0%, 97%);\n\t--ck-color-selector-caption-text: hsl(0, 0%, 20%);\n\t--ck-color-selector-caption-highlighted-background: hsl(52deg 100% 50%);\n}\n\n/* Content styles */\n.ck-content .table > figcaption {\n\tdisplay: table-caption;\n\tcaption-side: top;\n\tword-break: break-word;\n\ttext-align: center;\n\tcolor: var(--ck-color-selector-caption-text);\n\tbackground-color: var(--ck-color-selector-caption-background);\n\tpadding: .6em;\n\tfont-size: .75em;\n\toutline-offset: -1px;\n\n\t/* Improve placeholder rendering in high-constrast mode (https://github.com/ckeditor/ckeditor5/issues/14907). */\n\t@mixin ck-media-forced-colors {\n\t\tbackground-color: unset;\n\t\tcolor: unset;\n\t}\n}\n\n/* Editing styles */\n.ck.ck-editor__editable .table > figcaption {\n\t@mixin ck-media-default-colors {\n\t\t&.table__caption_highlighted {\n\t\t\tanimation: ck-table-caption-highlight .6s ease-out;\n\t\t}\n\t}\n\n\t&.ck-placeholder::before {\n\t\tpadding-left: inherit;\n\t\tpadding-right: inherit;\n\n\t\t/*\n\t\t * Make sure the table caption placeholder doesn't overflow the placeholder area.\n\t\t * See https://github.com/ckeditor/ckeditor5/issues/9162.\n\t\t */\n\t\twhite-space: nowrap;\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t}\n}\n\n@keyframes ck-table-caption-highlight {\n\t0% {\n\t\tbackground-color: var(--ck-color-selector-caption-highlighted-background);\n\t}\n\n\t100% {\n\t\tbackground-color: var(--ck-color-selector-caption-background);\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n:root {\n\t--ck-mention-list-max-height: 300px;\n}\n\n.ck.ck-mentions {\n\tmax-height: var(--ck-mention-list-max-height);\n\n\toverflow-y: auto;\n\n\t/* Prevent unnecessary horizontal scrollbar in Safari\n\thttps://github.com/ckeditor/ckeditor5-mention/issues/41 */\n\toverflow-x: hidden;\n\n\toverscroll-behavior: contain;\n\n\t/* Prevent unnecessary vertical scrollbar in Safari\n\thttps://github.com/ckeditor/ckeditor5-mention/issues/41 */\n\t& > .ck-list__item {\n\t\toverflow: hidden;\n\t\tflex-shrink: 0;\n\t}\n}\n\ndiv.ck.ck-balloon-panel.ck-mention-balloon {\n\tz-index: calc( var( --ck-z-dialog ) + 1 );\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n:root {\n\t--ck-color-mention-background: hsla(341, 100%, 30%, 0.1);\n\t--ck-color-mention-text: hsl(341, 100%, 30%);\n}\n\n.ck-content .mention {\n\tbackground: var(--ck-color-mention-background);\n\tcolor: var(--ck-color-mention-text);\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n:root {\n\t--ck-style-panel-columns: 3;\n}\n\n.ck.ck-style-panel .ck-style-grid {\n\tdisplay: grid;\n\tgrid-template-columns: repeat(var(--ck-style-panel-columns),auto);\n\tjustify-content: start;\n\n\t& .ck-style-grid__button {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\tflex-direction: column;\n\n\t\t& .ck-style-grid__button__preview {\n\t\t\tdisplay: flex;\n\t\t\talign-content: center;\n\t\t\tjustify-content: flex-start;\n\t\t\talign-items: center;\n\t\t\tflex-grow: 1;\n\t\t\tflex-basis: 100%;\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n:root {\n\t--ck-style-panel-button-width: 120px;\n\t--ck-style-panel-button-height: 80px;\n\t--ck-style-panel-button-label-background: hsl(0, 0%, 94.1%);\n\t--ck-style-panel-button-hover-label-background: hsl(0, 0%, 92.1%);\n\t--ck-style-panel-button-hover-border-color: hsl(0, 0%, 70%);\n}\n\n.ck.ck-style-panel .ck-style-grid {\n\trow-gap: var(--ck-spacing-large);\n\tcolumn-gap: var(--ck-spacing-large);\n\n\t& .ck-style-grid__button {\n\t\t--ck-color-button-default-hover-background: var(--ck-color-base-background);\n\t\t--ck-color-button-default-active-background: var(--ck-color-base-background);\n\n\t\tpadding: 0;\n\t\twidth: var(--ck-style-panel-button-width);\n\t\theight: var(--ck-style-panel-button-height);\n\n\t\t/* Let default .ck-button :focus styles apply */\n\t\t&:not(:focus) {\n\t\t\tborder: 1px solid var(--ck-color-base-border);\n\t\t}\n\n\t\t& .ck-button__label {\n\t\t\theight: 22px;\n\t\t\tline-height: 22px;\n\t\t\twidth: 100%;\n\t\t\tpadding: 0 var(--ck-spacing-medium);\n\t\t\toverflow: hidden;\n\t\t\ttext-overflow: ellipsis;\n\t\t\tflex-shrink: 0;\n\t\t}\n\n\t\t& .ck-style-grid__button__preview {\n\t\t\twidth: 100%;\n\t\t\toverflow: hidden;\n\t\t\topacity: .9;\n\n\t\t\tpadding: var(--ck-spacing-medium);\n\t\t\tbackground: var(--ck-color-base-background);\n\t\t\tborder: 2px solid var(--ck-color-base-background);\n\t\t}\n\n\t\t&.ck-disabled {\n\t\t\t--ck-color-button-default-disabled-background: var(--ck-color-base-foreground);\n\n\t\t\t/* Let default .ck-button :focus styles apply */\n\t\t\t&:not(:focus) {\n\t\t\t\tborder-color: var(--ck-style-panel-button-label-background);\n\t\t\t}\n\n\t\t\t& .ck-style-grid__button__preview {\n\t\t\t\topacity: .4;\n\n\t\t\t\tborder-color: var(--ck-color-base-foreground);\n\t\t\t\tfilter: saturate(.3);\n\t\t\t}\n\t\t}\n\n\t\t&.ck-on {\n\t\t\tborder-color: var(--ck-color-base-active);\n\n\t\t\t& .ck-button__label {\n\t\t\t\tbox-shadow: 0 -1px 0 var(--ck-color-base-active);\n\t\t\t\tz-index: 1; /* Stay on top of the preview with the shadow. */\n\t\t\t}\n\n\t\t\t&:hover {\n\t\t\t\tborder-color: var(--ck-color-base-active-focus);\n\t\t\t}\n\t\t}\n\n\t\t&:not(.ck-on) {\n\t\t\t& .ck-button__label {\n\t\t\t\tbackground: var(--ck-style-panel-button-label-background);\n\t\t\t}\n\n\t\t\t&:hover .ck-button__label {\n\t\t\t\tbackground: var(--ck-style-panel-button-hover-label-background);\n\t\t\t}\n\t\t}\n\n\t\t&:hover:not(.ck-disabled):not(.ck-on) {\n\t\t\tborder-color: var(--ck-style-panel-button-hover-border-color);\n\n\t\t\t& .ck-style-grid__button__preview {\n\t\t\t\topacity: 1;\n\t\t\t}\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck.ck-style-panel .ck-style-panel__style-group {\n\t& > .ck-label {\n\t\tmargin: var(--ck-spacing-large) 0;\n\t}\n\n\t&:first-child {\n\t\t& > .ck-label {\n\t\t\tmargin-top: 0;\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n:root {\n\t--ck-style-panel-max-height: 470px;\n}\n\n.ck.ck-style-panel {\n\tpadding: var(--ck-spacing-large);\n\toverflow-y: auto;\n\tmax-height: var(--ck-style-panel-max-height);\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck.ck-dropdown.ck-style-dropdown.ck-style-dropdown_multiple-active > .ck-button > .ck-button__label {\n\tfont-style: italic;\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n\n.ck-editor__editable .ck-horizontal-line {\n\t/* Necessary to render properly next to floated objects, e.g. side image case. */\n\tdisplay: flow-root;\n}\n\n.ck-content hr {\n\tmargin: 15px 0;\n\theight: 4px;\n\tbackground: hsl(0, 0%, 87%);\n\tborder: 0;\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck.ck-editor__editable {\n\t/* Linked image indicator */\n\t& figure.image > a,\n\t& a span.image-inline {\n\t\t&::after {\n\t\t\tdisplay: block;\n\t\t\tposition: absolute;\n\t\t}\n\t}\n}\n\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n:root {\n\t/* Match the icon size with the upload indicator brought by the image upload feature. */\n\t--ck-link-image-indicator-icon-size: 20;\n\t--ck-link-image-indicator-icon-is-visible: clamp(0px, 100% - 50px, 1px);\n}\n\n.ck.ck-editor__editable {\n\t/* Linked image indicator */\n\t& figure.image > a,\n\t& a span.image-inline {\n\t\t&::after {\n\t\t\tcontent: \"\";\n\n\t\t\t/*\n\t\t\t * Smaller images should have the icon closer to the border.\n\t\t\t * Match the icon position with the upload indicator brought by the image upload feature.\n\t\t\t */\n\t\t\ttop: min(var(--ck-spacing-medium), 6%);\n\t\t\tright: min(var(--ck-spacing-medium), 6%);\n\n\t\t\tbackground-color: hsla(0, 0%, 0%, .4);\n\t\t\tbackground-image: url(\"data:image/svg+xml;base64,PHN2ZyB2aWV3Qm94PSIwIDAgMjAgMjAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZmlsbD0iI2ZmZiIgZD0ibTExLjA3NyAxNSAuOTkxLTEuNDE2YS43NS43NSAwIDEgMSAxLjIyOS44NmwtMS4xNDggMS42NGEuNzQ4Ljc0OCAwIDAgMS0uMjE3LjIwNiA1LjI1MSA1LjI1MSAwIDAgMS04LjUwMy01Ljk1NS43NDEuNzQxIDAgMCAxIC4xMi0uMjc0bDEuMTQ3LTEuNjM5YS43NS43NSAwIDEgMSAxLjIyOC44Nkw0LjkzMyAxMC43bC4wMDYuMDAzYTMuNzUgMy43NSAwIDAgMCA2LjEzMiA0LjI5NGwuMDA2LjAwNHptNS40OTQtNS4zMzVhLjc0OC43NDggMCAwIDEtLjEyLjI3NGwtMS4xNDcgMS42MzlhLjc1Ljc1IDAgMSAxLTEuMjI4LS44NmwuODYtMS4yM2EzLjc1IDMuNzUgMCAwIDAtNi4xNDQtNC4zMDFsLS44NiAxLjIyOWEuNzUuNzUgMCAwIDEtMS4yMjktLjg2bDEuMTQ4LTEuNjRhLjc0OC43NDggMCAwIDEgLjIxNy0uMjA2IDUuMjUxIDUuMjUxIDAgMCAxIDguNTAzIDUuOTU1em0tNC41NjMtMi41MzJhLjc1Ljc1IDAgMCAxIC4xODQgMS4wNDVsLTMuMTU1IDQuNTA1YS43NS43NSAwIDEgMS0xLjIyOS0uODZsMy4xNTUtNC41MDZhLjc1Ljc1IDAgMCAxIDEuMDQ1LS4xODR6Ii8+PC9zdmc+\");\n\t\t\tbackground-size: 14px;\n\t\t\tbackground-repeat: no-repeat;\n\t\t\tbackground-position: center;\n\t\t\tborder-radius: 100%;\n\n\t\t\t/*\n\t\t\t* Use CSS math to simulate container queries.\n\t\t\t* https://css-tricks.com/the-raven-technique-one-step-closer-to-container-queries/#what-about-showing-and-hiding-things\n\t\t\t*/\n\t\t\toverflow: hidden;\n\t\t\twidth: calc(var(--ck-link-image-indicator-icon-is-visible) * var(--ck-link-image-indicator-icon-size));\n\t\t\theight: calc(var(--ck-link-image-indicator-icon-is-visible) * var(--ck-link-image-indicator-icon-size));\n\t\t}\n\t}\n}\n\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n/* The feature container. */\n.ck-widget.raw-html-embed {\n\t/* Give the embed some air. */\n\t/* The first value should be equal to --ck-spacing-large variable if used in the editor context\n\tto avoid the content jumping (See https://github.com/ckeditor/ckeditor5/issues/9825). */\n\tmargin: 0.9em auto;\n\tposition: relative;\n\tdisplay: flow-root;\n\n\t/* Give the html embed some minimal width in the content to prevent them\n\tfrom being \"squashed\" in tight spaces, e.g. in table cells (https://github.com/ckeditor/ckeditor5/issues/8331) */\n\tmin-width: 15em;\n\n\t/* Don't inherit the style, e.g. when in a block quote. */\n\tfont-style: normal;\n\n\t/* ----- Emebed label in the upper left corner ----------------------------------------------- */\n\n\t&::before {\n\t\tposition: absolute;\n\n\t\t/* Make sure the content does not cover the label. */\n\t\tz-index: 1;\n\t}\n\n\t/* ----- Emebed internals --------------------------------------------------------------------- */\n\n\t/* The switch mode button wrapper. */\n\t& .raw-html-embed__buttons-wrapper {\n\t\tposition: absolute;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\n\t& .raw-html-embed__preview {\n\t\tposition: relative;\n\t\toverflow: hidden;\n\t\tdisplay: flex;\n\t}\n\n\t& .raw-html-embed__preview-content {\n\t\twidth: 100%;\n\t\tposition: relative;\n\t\tmargin: auto;\n\n\t\t/* Gives spacing to the small renderable elements, so they always cover the placeholder. */\n\t\tdisplay: table;\n\t\tborder-collapse: separate;\n\t\tborder-spacing: 7px;\n\t}\n\n\t& .raw-html-embed__preview-placeholder {\n\t\tposition: absolute;\n\t\tleft: 0;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n:root {\n\t--ck-html-embed-content-width: calc(100% - 1.5 * var(--ck-icon-size));\n\t--ck-html-embed-source-height: 10em;\n\t--ck-html-embed-unfocused-outline-width: 1px;\n\t--ck-html-embed-content-min-height: calc(var(--ck-icon-size) + var(--ck-spacing-standard));\n\n\t--ck-html-embed-source-disabled-background: var(--ck-color-base-foreground);\n\t--ck-html-embed-source-disabled-color: hsl(0deg 0% 45%);\n}\n\n/* The feature container. */\n.ck-widget.raw-html-embed {\n\tfont-size: var(--ck-font-size-base);\n\tbackground-color: var(--ck-color-base-foreground);\n\n\t&:not(.ck-widget_selected):not(:hover) {\n\t\toutline: var(--ck-html-embed-unfocused-outline-width) dashed var(--ck-color-widget-blurred-border);\n\t}\n\n\t/* HTML embed widget itself should respect UI language direction */\n\t&[dir=\"ltr\"] {\n\t\ttext-align: left;\n\t}\n\n\t&[dir=\"rtl\"] {\n\t\ttext-align: right;\n\t}\n\n\t/* ----- Embed label in the upper left corner ----------------------------------------------- */\n\n\t&::before {\n\t\tcontent: attr(data-html-embed-label);\n\t\ttop: calc(-1 * var(--ck-html-embed-unfocused-outline-width));\n\t\tleft: var(--ck-spacing-standard);\n\t\tbackground: hsl(0deg 0% 60%);\n\t\ttransition: background var(--ck-widget-handler-animation-duration) var(--ck-widget-handler-animation-curve);\n\t\tpadding: calc(var(--ck-spacing-tiny) + var(--ck-html-embed-unfocused-outline-width)) var(--ck-spacing-small) var(--ck-spacing-tiny);\n\t\tborder-radius: 0 0 var(--ck-border-radius) var(--ck-border-radius);\n\t\tcolor: var(--ck-color-base-background);\n\t\tfont-size: var(--ck-font-size-tiny);\n\t\tfont-family: var(--ck-font-face);\n\t}\n\n\t&[dir=\"rtl\"]::before {\n\t\tleft: auto;\n\t\tright: var(--ck-spacing-standard);\n\t}\n\n\t/* Make space for label but it only collides in LTR languages */\n\t&[dir=\"ltr\"] .ck-widget__type-around .ck-widget__type-around__button.ck-widget__type-around__button_before {\n\t\tmargin-left: 50px;\n\t}\n\n\t.ck.ck-editor__editable.ck-blurred &.ck-widget_selected::before {\n\t\ttop: 0px;\n\t\tpadding: var(--ck-spacing-tiny) var(--ck-spacing-small);\n\t}\n\n\t.ck.ck-editor__editable:not(.ck-blurred) &.ck-widget_selected::before {\n\t\ttop: 0;\n\t\tpadding: var(--ck-spacing-tiny) var(--ck-spacing-small);\n\t\tbackground: var(--ck-color-focus-border);\n\t}\n\n\t.ck.ck-editor__editable &:not(.ck-widget_selected):hover::before {\n\t\ttop: 0px;\n\t\tpadding: var(--ck-spacing-tiny) var(--ck-spacing-small);\n\t}\n\n\t/* ----- Emebed internals --------------------------------------------------------------------- */\n\n\t& .raw-html-embed__content-wrapper {\n\t\tpadding: var(--ck-spacing-standard);\n\t}\n\n\t/* The switch mode button wrapper. */\n\t& .raw-html-embed__buttons-wrapper {\n\t\ttop: var(--ck-spacing-standard);\n\t\tright: var(--ck-spacing-standard);\n\n\t\t& .ck-button.raw-html-embed__save-button {\n\t\t\tcolor: var(--ck-color-button-save);\n\t\t}\n\n\t\t& .ck-button.raw-html-embed__cancel-button {\n\t\t\tcolor: var(--ck-color-button-cancel);\n\t\t}\n\n\t\t& .ck-button:not(:first-child) {\n\t\t\tmargin-top: var(--ck-spacing-small);\n\t\t}\n\t}\n\n\t&[dir=\"rtl\"] .raw-html-embed__buttons-wrapper {\n\t\tleft: var(--ck-spacing-standard);\n\t\tright: auto;\n\t}\n\n\t/* The edit source element. */\n\t& .raw-html-embed__source {\n\t\tbox-sizing: border-box;\n\t\theight: var(--ck-html-embed-source-height);\n\t\twidth: var(--ck-html-embed-content-width);\n\t\tresize: none;\n\t\tmin-width: 0;\n\t\tpadding: var(--ck-spacing-standard);\n\n\t\tfont-family: monospace;\n\t\ttab-size: 4;\n\t\twhite-space: pre-wrap;\n\t\tfont-size: var(--ck-font-size-base); /* Safari needs this. */\n\n\t\t/* HTML code is direction–agnostic. */\n\t\ttext-align: left;\n\t\tdirection: ltr;\n\n\t\t&[disabled] {\n\t\t\tbackground: var(--ck-html-embed-source-disabled-background);\n\t\t\tcolor: var(--ck-html-embed-source-disabled-color);\n\n\t\t\t/* Safari needs this for the proper text color in disabled input (https://github.com/ckeditor/ckeditor5/issues/8320). */\n\t\t\t-webkit-text-fill-color: var(--ck-html-embed-source-disabled-color);\n\t\t\topacity: 1;\n\t\t}\n\t}\n\n\t/* The preview data container. */\n\t& .raw-html-embed__preview {\n\t\tmin-height: var(--ck-html-embed-content-min-height);\n\t\twidth: var(--ck-html-embed-content-width);\n\n\t\t/* Disable all mouse interaction as long as the editor is not read–only. */\n\t\t.ck-editor__editable:not(.ck-read-only) & {\n\t\t\tpointer-events: none;\n\t\t}\n\t}\n\n\t& .raw-html-embed__preview-content {\n\t\tbox-sizing: border-box;\n\t\tbackground-color: var(--ck-color-base-foreground);\n\n\t\t& > * {\n\t\t\tmargin-left: auto;\n\t\t\tmargin-right: auto;\n\t\t}\n\t}\n\n\t& .raw-html-embed__preview-placeholder {\n\t\tcolor: var(--ck-html-embed-source-disabled-color)\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck.ck-character-grid {\n\tmax-width: 100%;\n\n\t& .ck-character-grid__tiles {\n\t\tdisplay: grid;\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"@ckeditor/ckeditor5-ui/theme/mixins/_rwd.css\";\n@import \"../mixins/_rounded.css\";\n\n:root {\n\t--ck-character-grid-tile-size: 24px;\n}\n\n.ck.ck-character-grid {\n\toverflow-y: auto;\n\toverflow-x: hidden;\n\n\t& .ck-character-grid__tiles {\n\t\tgrid-template-columns: repeat(auto-fill, minmax(var(--ck-character-grid-tile-size), 1fr));\n\t\tmargin: var(--ck-spacing-standard) var(--ck-spacing-large);\n\t\tgrid-gap: var(--ck-spacing-standard);\n\t}\n\n\t& .ck-character-grid__tile {\n\t\twidth: var(--ck-character-grid-tile-size);\n\t\theight: var(--ck-character-grid-tile-size);\n\t\tmin-width: var(--ck-character-grid-tile-size);\n\t\tmin-height: var(--ck-character-grid-tile-size);\n\t\tfont-size: 1.5em;\n\t\tpadding: 0;\n\t\ttransition: .2s ease box-shadow;\n\t\tborder: 0;\n\n\t\t@media (prefers-reduced-motion: reduce) {\n\t\t\ttransition: none;\n\t\t}\n\n\t\t&:focus:not( .ck-disabled ),\n\t\t&:hover:not( .ck-disabled ) {\n\t\t\t/* Disable the default .ck-button's border ring. */\n\t\t\tborder: 0;\n\t\t\tbox-shadow: inset 0 0 0 1px var(--ck-color-base-background), 0 0 0 2px var(--ck-color-focus-border);\n\t\t}\n\n\t\t/* Make sure the glyph is rendered in the center of the button */\n\t\t& .ck-button__label {\n\t\t\tline-height: var(--ck-character-grid-tile-size);\n\t\t\twidth: 100%;\n\t\t\ttext-align: center;\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck.ck-character-info {\n\tdisplay: flex;\n\tjustify-content: space-between;\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"@ckeditor/ckeditor5-ui/theme/mixins/_rwd.css\";\n\n.ck.ck-character-info {\n\tpadding: var(--ck-spacing-small) var(--ck-spacing-large);\n\tborder-top: 1px solid var(--ck-color-base-border);\n\n\t& > * {\n\t\ttext-transform: uppercase;\n\t\tfont-size: var(--ck-font-size-small);\n\t}\n\n\t& .ck-character-info__name {\n\t\tmax-width: 280px;\n\t\ttext-overflow: ellipsis;\n\t\toverflow: hidden;\n\t}\n\n\t& .ck-character-info__code {\n\t\topacity: .6;\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n/*\n * Note: This file should contain the wireframe styles only. But since there are no such styles,\n * it acts as a message to the builder telling that it should look for the corresponding styles\n * **in the theme** when compiling the editor.\n */\n\n.ck.ck-special-characters {\n\t& > .ck-dialog__content > div {\n\t\twidth: 350px;\n\t\tmax-width: 100%;\n\t\theight: 100%;\n\n\t\tdisplay: grid;\n\t\tgrid-column-gap: 0px;\n\t\tgrid-row-gap: 0px;\n\t\tgrid-template-columns: 1fr;\n\t\tgrid-template-rows: auto 1fr auto;\n\n\t\t& > .ck-character-categories {\n\t\t\tgrid-area: 1 / 1 / 2 / 2;\n\t\t\tpadding: var(--ck-spacing-medium) var(--ck-spacing-large);\n\n\t\t\t& > .ck-labeled-field-view {\n\t\t\t\tpadding-top: var(--ck-spacing-standard);\n\t\t\t\twidth: 100%;\n\n\t\t\t\t& .ck.ck-labeled-field-view__status {\n\t\t\t\t\tbackground: var(--ck-color-base-error);\n\t\t\t\t\tcolor: var(--ck-color-base-background);\n\t\t\t\t\tpadding: var(--ck-spacing-small) var(--ck-spacing-medium);\n\t\t\t\t\tmin-width: var(--ck-table-properties-min-error-width);\n\t\t\t\t\ttext-align: center;\n\n\t\t\t\t\t/* The arrow pointing towards the field. */\n\t\t\t\t\t&::after {\n\t\t\t\t\t\tborder-color: transparent transparent var(--ck-color-base-error) transparent;\n\t\t\t\t\t\tborder-width: 0 var(--ck-table-properties-error-arrow-size) var(--ck-table-properties-error-arrow-size) var(--ck-table-properties-error-arrow-size);\n\t\t\t\t\t\tborder-style: solid;\n\t\t\t\t\t}\n\n\t\t\t\t\tanimation: ck-table-form-labeled-view-status-appear .15s ease both;\n\n\t\t\t\t\t@media (prefers-reduced-motion: reduce) {\n\t\t\t\t\t\tanimation: none;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t/* Hide the error balloon when the field is blurred. Makes the experience much more clear. */\n\t\t\t\t& .ck-input.ck-error:not(:focus) + .ck.ck-labeled-field-view__status {\n\t\t\t\t\tdisplay: none;\n\t\t\t\t}\n\n\t\t\t\t& > .ck-label {\n\t\t\t\t\tfont-size: var(--ck-font-size-tiny);\n\t\t\t\t\ttext-align: center;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.ck-dropdown {\n\t\t\t\tdisplay: block;\n\t\t\t\twidth: 100%;\n\n\t\t\t\t& > button {\n\t\t\t\t\t&:not(:focus) {\n\t\t\t\t\t\tborder: 1px solid var(--ck-color-base-border);\n\t\t\t\t\t}\n\n\t\t\t\t\t& > span {\n\t\t\t\t\t\twidth: 100%;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t& > .ck-character-grid {\n\t\t\tgrid-area: 2 / 1 / 3 / 2;\n\t\t\tmax-height: 200px;\n\t\t}\n\n\t\t& > .ck-character-info {\n\t\t\tgrid-area: 3 / 1 / 4 / 2;\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"@ckeditor/ckeditor5-ui/theme/mixins/_dir.css\";\n@import \"@ckeditor/ckeditor5-ui/theme/mixins/_rwd.css\";\n\n.ck.ck-special-characters-navigation {\n\n\t& > .ck-label {\n\t\tmax-width: 160px;\n\t\ttext-overflow: ellipsis;\n\t\toverflow: hidden;\n\t}\n\n\t& > .ck-dropdown .ck-dropdown__panel {\n\t\t/* There could be dozens of categories available. Use scroll to prevent a 10e6px dropdown. */\n\t\tmax-height: 250px;\n\t\toverflow-y: auto;\n\t\toverflow-x: hidden;\n\t}\n\n\t@mixin ck-media-phone {\n\t\tmax-width: 190px;\n\n\t\t& > .ck-form__header__label {\n\t\t\ttext-overflow: ellipsis;\n\t\t\toverflow: hidden;\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"@ckeditor/ckeditor5-ui/theme/mixins/_dir.css\";\n\n:root {\n\t--ck-show-blocks-border-color: hsl(0, 0%, 46%);\n}\n\n@define-mixin block-name-background-ltr $text {\n\tbackground-image: url(\"data:image/svg+xml;utf8,<svg width='120' height='12' xmlns='http://www.w3.org/2000/svg' ><text style='paint-order:stroke fill; clip-path: inset(-3px)' stroke='%23EAEAEA' stroke-width='13' dominant-baseline='middle' fill='black' x='3' y='7' font-size='9px' font-family='Consolas, %22Lucida Console%22, %22Lucida Sans Typewriter%22, %22DejaVu Sans Mono%22, %22Bitstream Vera Sans Mono%22, %22Liberation Mono%22, Monaco, %22Courier New%22, Courier, monospace'>$(text)</text></svg>\");\n\tbackground-position: 1px 1px;\n}\n\n@define-mixin block-name-background-rtl $text {\n\tbackground-image: url(\"data:image/svg+xml;utf8,<svg width='120' height='12' xmlns='http://www.w3.org/2000/svg' ><text style='paint-order:stroke fill; clip-path: inset(-3px); transform:translate(-2px, 0)' stroke='%23EAEAEA' stroke-width='13' dominant-baseline='middle' fill='black' x='100%' text-anchor='end' y='7' font-size='9px' font-family='Consolas, %22Lucida Console%22, %22Lucida Sans Typewriter%22, %22DejaVu Sans Mono%22, %22Bitstream Vera Sans Mono%22, %22Liberation Mono%22, Monaco, %22Courier New%22, Courier, monospace'>$(text)</text></svg>\");\n\tbackground-position: calc(100% - 1px) 1px;\n}\n\n@define-mixin block-name-background $text {\n\tbackground-repeat: no-repeat;\n\tpadding-top: 15px;\n\n\t/* Fix for Multi-root editor\n\thttps://github.com/ckeditor/ckeditor5/issues/15969 */\n\t[dir=ltr] & {\n\t\t@mixin block-name-background-ltr $text;\n\t}\n\t[dir=rtl] & {\n\t\t@mixin block-name-background-rtl $text;\n\t}\n\n\t&:not(.ck-widget_selected):not(.ck-widget:hover) {\n\t\toutline: 1px dashed var(--ck-show-blocks-border-color);\n\t}\n\n\t@mixin ck-dir ltr {\n\t\t@mixin block-name-background-ltr $text;\n\t}\n\t@mixin ck-dir rtl {\n\t\t@mixin block-name-background-rtl $text;\n\t}\n}\n\n.ck.ck-editor__editable.ck-editor__editable_inline.ck-show-blocks:not(.ck-widget) {\n\t& address {\n\t\t@mixin block-name-background ADDRESS;\n\t}\n\n\t& aside {\n\t\t@mixin block-name-background ASIDE;\n\t}\n\n\t& blockquote {\n\t\t@mixin block-name-background BLOCKQUOTE;\n\t}\n\n\t& details {\n\t\t@mixin block-name-background DETAILS;\n\t}\n\n\t& div:not(.ck-widget, .ck-widget *) {\n\t\t@mixin block-name-background DIV;\n\t}\n\n\t& footer {\n\t\t@mixin block-name-background FOOTER;\n\t}\n\n\t& h1 {\n\t\t@mixin block-name-background H1;\n\t}\n\n\t& h2 {\n\t\t@mixin block-name-background H2;\n\t}\n\n\t& h3 {\n\t\t@mixin block-name-background H3;\n\t}\n\n\t& h4 {\n\t\t@mixin block-name-background H4;\n\t}\n\n\t& h5 {\n\t\t@mixin block-name-background H5;\n\t}\n\n\t& h6 {\n\t\t@mixin block-name-background H6;\n\t}\n\n\t& header {\n\t\t@mixin block-name-background HEADER;\n\t}\n\n\t& main {\n\t\t@mixin block-name-background MAIN;\n\t}\n\n\t& nav {\n\t\t@mixin block-name-background NAV;\n\t}\n\n\t& pre {\n\t\t@mixin block-name-background PRE;\n\t}\n\n\t& ol {\n\t\t@mixin block-name-background OL;\n\t}\n\n\t& ul {\n\t\t@mixin block-name-background UL;\n\t}\n\n\t& p {\n\t\t@mixin block-name-background P;\n\t}\n\n\t& section {\n\t\t@mixin block-name-background SECTION;\n\t}\n\n\t& :where(figure.image, figure.table) figcaption {\n\t\t@mixin block-name-background FIGCAPTION;\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck.ck-find-and-replace-form {\n\tmax-width: 100%;\n\n\t& .ck-find-and-replace-form__inputs, .ck-find-and-replace-form__actions {\n\t\tdisplay: flex;\n\n\t\t/* The inputs area styles */\n\t\t&.ck-find-and-replace-form__inputs .ck-results-counter {\n\t\t\tposition: absolute;\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n@import \"@ckeditor/ckeditor5-ui/theme/mixins/_rwd.css\";\n@import \"@ckeditor/ckeditor5-ui/theme/mixins/_dir.css\";\n\n.ck.ck-find-and-replace-form {\n\twidth: 400px;\n\n\t/*\n\t * The <form> needs tabindex=\"-1\" for proper Esc handling after being clicked\n\t * but the side effect is that this creates a nasty focus outline in some browsers.\n\t */\n\t&:focus {\n\t\toutline: none;\n\t}\n\n\t/* Generic styles for the form inputs and actions. */\n\t& .ck-find-and-replace-form__inputs,\n\t& .ck-find-and-replace-form__actions {\n\t\tflex: 1 1 auto;\n\t\tflex-direction: row;\n\t\tflex-wrap: wrap;\n\t\talign-items: center;\n\t\talign-content: stretch;\n\n\t\tpadding: var(--ck-spacing-large);\n\t\tmargin: 0;\n\n\t\t& > .ck-button {\n\t\t\tflex: 0 0 auto;\n\t\t}\n\n\t\t@mixin ck-dir ltr {\n\t\t\t& > * + * {\n\t\t\t\tmargin-left: var(--ck-spacing-standard);\n\t\t\t}\n\t\t}\n\n\t\t@mixin ck-dir rtl {\n\t\t\t& > * + * {\n\t\t\t\tmargin-right: var(--ck-spacing-standard);\n\t\t\t}\n\t\t}\n\n\t\t& .ck-labeled-field-view {\n\t\t\tflex: 1 1 auto;\n\n\t\t\t& .ck-input {\n\t\t\t\twidth: 100%;\n\t\t\t\tmin-width: 50px;\n\t\t\t}\n\t\t}\n\n\t}\n\n\t/* Styles specific for inputs area. */\n\t& .ck-find-and-replace-form__inputs {\n\t\t/* To display all controls in line when there's an error under the input */\n\t\talign-items: flex-start;\n\n\t\t& > .ck-button-prev > .ck-icon {\n\t\t\ttransform: rotate(90deg);\n\t\t}\n\n\t\t& > .ck-button-next > .ck-icon {\n\t\t\ttransform: rotate(-90deg);\n\t\t}\n\n\t\t& .ck-results-counter {\n\t\t\ttop: 50%;\n\t\t\ttransform: translateY(-50%);\n\n\t\t\t@mixin ck-dir ltr {\n\t\t\t\tright: var(--ck-spacing-standard);\n\t\t\t}\n\n\t\t\t@mixin ck-dir rtl {\n\t\t\t\tleft: var(--ck-spacing-standard);\n\t\t\t}\n\n\t\t\tcolor: var(--ck-color-base-border);\n\t\t}\n\n\t\t& > .ck-labeled-field-replace {\n\t\t\tflex: 0 0 100%;\n\t\t\tpadding-top: var(--ck-spacing-standard);\n\n\t\t\t@mixin ck-dir ltr {\n\t\t\t\tmargin-left: 0;\n\t\t\t}\n\n\t\t\t@mixin ck-dir rtl {\n\t\t\t\tmargin-right: 0;\n\t\t\t}\n\t\t}\n\t}\n\n\t/* Styles specific for actions area. */\n\t& .ck-find-and-replace-form__actions {\n\t\tflex-wrap: wrap;\n\t\tjustify-content: flex-end;\n\t\tmargin-top: calc( -1 * var(--ck-spacing-large) );\n\n\t\t& > .ck-button-find {\n\t\t\tfont-weight: bold;\n\n\t\t\t/* Beef the find button up a little. It's the main action button in the form */\n\t\t\t& .ck-button__label {\n\t\t\t\tpadding-left: var(--ck-spacing-large);\n\t\t\t\tpadding-right: var(--ck-spacing-large);\n\t\t\t}\n\t\t}\n\t}\n\n\t& .ck-switchbutton {\n\t\twidth: 100%;\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\tflex-wrap: nowrap;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t}\n}\n\n@mixin ck-media-phone {\n\t.ck.ck-find-and-replace-form {\n\t\twidth: 300px;\n\n\t\t/* Don't let the form overflow from the dialog (https://github.com/cksource/ckeditor5-commercial/issues/5913) */\n\t\tmax-width: 100%;\n\n\t\t/* Styles specific for inputs area. */\n\t\t&.ck-find-and-replace-form__input {\n\t\t\tflex-wrap: wrap;\n\n\t\t\t& .ck-labeled-field-view {\n\t\t\t\tflex: 1 0 auto;\n\t\t\t\twidth: 100%;\n\t\t\t\tmargin-bottom: var(--ck-spacing-standard);\n\t\t\t}\n\n\t\t\t& > .ck-button {\n\t\t\t\ttext-align: center;\n\n\t\t\t\t&:first-of-type {\n\t\t\t\t\tflex: 1 1 auto;\n\n\t\t\t\t\t@mixin ck-dir ltr {\n\t\t\t\t\t\tmargin-left: 0;\n\t\t\t\t\t}\n\n\t\t\t\t\t@mixin ck-dir rtl {\n\t\t\t\t\t\tmargin-right: 0;\n\t\t\t\t\t}\n\n\t\t\t\t\t& .ck-button__label {\n\t\t\t\t\t\twidth: 100%;\n\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t/* Styles specific for actions area. */\n\t\t&.ck-find-and-replace-form__actions > :not(.ck-labeled-field-view) {\n\t\t\tflex-wrap: wrap;\n\t\t\tflex: 1 1 auto;\n\n\t\t\t& > .ck-button {\n\t\t\t\ttext-align: center;\n\n\t\t\t\t&:first-of-type {\n\t\t\t\t\tflex: 1 1 auto;\n\n\t\t\t\t\t@mixin ck-dir ltr {\n\t\t\t\t\t\tmargin-left: 0;\n\t\t\t\t\t}\n\n\t\t\t\t\t@mixin ck-dir rtl {\n\t\t\t\t\t\tmargin-right: 0;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t& .ck-button__label {\n\t\t\t\t\twidth: 100%;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n", "/*\n * Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options\n */\n\n.ck-find-result {\n\tbackground: var(--ck-color-highlight-background);\n\tcolor: var(--ck-color-text);\n}\n\n.ck-find-result_selected {\n\tbackground: hsl(29, 100%, 60%);\n}\n", "/** Todo list **/\n\n.ck .todo-list input {\n    padding: 0;\n}\n\n.ck .todo-list__checkmark:after {\n    height: 6px !important;\n}\n\n.ck .todo-list__checkmark {\n    padding: 0;\n}\n\n.ck.ck-content.ck-editor__editable {\n    min-height: 300px;\n}\n\n.ck h2 {\n    color: inherit;\n    background: inherit;\n}\n\nform .aligned .ck ul li {\n    list-style: inherit;\n}\n\nform .aligned .ck ul {\n    margin-left: 1.5em;\n    padding-left: inherit;\n}\n.ck-word-count{\n    display: flex;\n    margin-top: 10px;\n}\n\n.ck-word-count__words{\n    margin-right: 10px;\n}\n\n.ck.ck-editor {\n    position: relative;\n    width: 100%;\n}\n.ck-editor-container{\n    width: 100%;\n}"], "names": [], "sourceRoot": ""}