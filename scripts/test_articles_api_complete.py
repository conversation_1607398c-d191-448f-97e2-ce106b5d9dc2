#!/usr/bin/env python
"""
完整的Articles API测试脚本
测试所有API接口的功能
"""

import os
import sys
import django
import requests
import json
from datetime import datetime

# 设置Django环境
sys.path.append('/www/wwwroot/csgoskins.com.cn/server')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')
django.setup()

from articles.models import ContentCategory, ContentTag, Content

# API基础URL
BASE_URL = "http://localhost:9001"

def test_api_endpoint(endpoint, description):
    """测试API接口"""
    print(f"\n📡 测试 {description}...")
    try:
        response = requests.get(f"{BASE_URL}{endpoint}", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 成功: {endpoint}")
            if isinstance(data, dict) and 'count' in data:
                print(f"   📊 总数: {data['count']}")
                if 'results' in data:
                    print(f"   📄 返回: {len(data['results'])} 条记录")
            elif isinstance(data, list):
                print(f"   📄 返回: {len(data)} 条记录")
            return True
        else:
            print(f"❌ 失败: {endpoint} - HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 错误: {endpoint} - {str(e)}")
        return False

def main():
    print("🧪 开始完整的Articles API测试...")
    print(f"🕒 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试数据库数据
    print("\n🗄️  检查数据库数据...")
    categories = ContentCategory.objects.count()
    tags = ContentTag.objects.count()
    contents = Content.objects.count()
    articles = Content.objects.filter(content_type='article').count()
    announcements = Content.objects.filter(content_type='announcement').count()
    
    print(f"   📁 分类数量: {categories}")
    print(f"   🏷️  标签数量: {tags}")
    print(f"   📝 总内容数量: {contents}")
    print(f"   📄 文章数量: {articles}")
    print(f"   📢 公告数量: {announcements}")
    
    # 测试API接口
    test_results = []
    
    # 基础接口测试
    test_results.append(test_api_endpoint("/health/", "健康检查"))
    test_results.append(test_api_endpoint("/api/articles/categories/", "分类列表"))
    test_results.append(test_api_endpoint("/api/articles/tags/", "标签列表"))
    
    # 内容接口测试
    test_results.append(test_api_endpoint("/api/articles/contents/", "内容列表"))
    test_results.append(test_api_endpoint("/api/articles/contents/?page_size=5", "内容列表(分页)"))
    test_results.append(test_api_endpoint("/api/articles/contents/?content_type=article", "文章列表"))
    test_results.append(test_api_endpoint("/api/articles/contents/?content_type=announcement", "公告列表"))
    
    # 搜索和过滤测试
    test_results.append(test_api_endpoint("/api/articles/contents/?search=CSGO", "搜索功能"))
    test_results.append(test_api_endpoint("/api/articles/contents/?is_featured=true", "推荐内容"))
    test_results.append(test_api_endpoint("/api/articles/contents/?is_pinned=true", "置顶内容"))
    
    # 专门接口测试
    test_results.append(test_api_endpoint("/api/articles/announcements/", "公告专门接口"))
    test_results.append(test_api_endpoint("/api/articles/announcements/homepage/", "首页公告"))
    test_results.append(test_api_endpoint("/api/articles/featured/", "推荐内容专门接口"))
    
    # 分类和标签接口测试
    if categories > 0:
        category = ContentCategory.objects.first()
        test_results.append(test_api_endpoint(f"/api/articles/category/{category.slug}/", f"按分类获取({category.name})"))
    
    if tags > 0:
        tag = ContentTag.objects.first()
        test_results.append(test_api_endpoint(f"/api/articles/tag/{tag.slug}/", f"按标签获取({tag.name})"))
    
    # 内容详情测试
    if contents > 0:
        content = Content.objects.first()
        test_results.append(test_api_endpoint(f"/api/articles/contents/{content.slug}/", f"内容详情({content.title})"))
    
    # 统计结果
    success_count = sum(test_results)
    total_count = len(test_results)
    success_rate = (success_count / total_count * 100) if total_count > 0 else 0
    
    print(f"\n📊 测试结果统计:")
    print(f"   ✅ 成功: {success_count}/{total_count}")
    print(f"   📈 成功率: {success_rate:.1f}%")
    
    if success_rate == 100:
        print("\n🎉 所有API接口测试通过！Articles App完全正常运行！")
    elif success_rate >= 80:
        print("\n✅ 大部分API接口正常，Articles App基本可用！")
    else:
        print("\n⚠️  部分API接口存在问题，需要进一步检查。")
    
    print(f"\n🔗 API文档地址: {BASE_URL}/admin/")
    print(f"🌐 服务器地址: {BASE_URL}")

if __name__ == "__main__":
    main()
