#!/usr/bin/env python3
"""
CKEditor 4 到 CKEditor 5 迁移脚本

这个脚本将帮助迁移项目中的 CKEditor 4 字段到 CKEditor 5，
解决安全警告问题。

使用方法:
    python scripts/migrate_to_ckeditor5.py --dry-run  # 预览模式
    python scripts/migrate_to_ckeditor5.py --execute  # 执行迁移
"""

import os
import sys
import re
import argparse
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CKEditorMigrator:
    """CKEditor 迁移工具"""
    
    def __init__(self, dry_run=True):
        self.dry_run = dry_run
        self.changes = []
        
    def find_ckeditor_files(self):
        """查找使用 CKEditor 的文件"""
        files_to_check = []
        
        # 查找 Python 文件
        for pattern in ['**/*.py']:
            for file_path in Path('.').glob(pattern):
                if 'venv' in str(file_path) or '__pycache__' in str(file_path):
                    continue
                files_to_check.append(file_path)
                
        return files_to_check
    
    def analyze_file(self, file_path):
        """分析文件中的 CKEditor 使用情况"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            changes = []
            
            # 检查导入语句
            if 'from ckeditor_uploader.fields import RichTextUploadingField' in content:
                changes.append({
                    'type': 'import',
                    'old': 'from ckeditor_uploader.fields import RichTextUploadingField',
                    'new': 'from django_ckeditor_5.fields import CKEditor5Field',
                    'file': file_path
                })
            
            if 'from ckeditor.fields import RichTextField' in content:
                changes.append({
                    'type': 'import',
                    'old': 'from ckeditor.fields import RichTextField',
                    'new': 'from django_ckeditor_5.fields import CKEditor5Field',
                    'file': file_path
                })
            
            # 检查字段使用
            field_patterns = [
                (r'RichTextUploadingField\((.*?)\)', 'CKEditor5Field(\\1)'),
                (r'RichTextField\((.*?)\)', 'CKEditor5Field(\\1)')
            ]
            
            for old_pattern, new_pattern in field_patterns:
                matches = re.finditer(old_pattern, content, re.DOTALL)
                for match in matches:
                    changes.append({
                        'type': 'field',
                        'old': match.group(0),
                        'new': re.sub(old_pattern, new_pattern, match.group(0)),
                        'file': file_path,
                        'line': content[:match.start()].count('\n') + 1
                    })
            
            return changes
            
        except Exception as e:
            logger.error(f"分析文件 {file_path} 时出错: {e}")
            return []
    
    def apply_changes(self, file_path, changes):
        """应用更改到文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 按行号倒序排序，避免位置偏移
            file_changes = [c for c in changes if c['file'] == file_path]
            
            for change in file_changes:
                content = content.replace(change['old'], change['new'])
            
            if not self.dry_run:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                logger.info(f"已更新文件: {file_path}")
            else:
                logger.info(f"[预览] 将更新文件: {file_path}")
                
        except Exception as e:
            logger.error(f"更新文件 {file_path} 时出错: {e}")
    
    def update_settings(self):
        """更新设置文件"""
        settings_file = Path('steambase/base_settings.py')
        
        try:
            with open(settings_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 更新 INSTALLED_APPS
            old_apps = """    'ckeditor',
    'ckeditor_uploader',
    # 'django_ckeditor_5',"""
            
            new_apps = """    # 'ckeditor',  # 已迁移到 CKEditor 5
    # 'ckeditor_uploader',  # 已迁移到 CKEditor 5
    'django_ckeditor_5',"""
            
            content = content.replace(old_apps, new_apps)
            
            if not self.dry_run:
                with open(settings_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                logger.info(f"已更新设置文件: {settings_file}")
            else:
                logger.info(f"[预览] 将更新设置文件: {settings_file}")
                
        except Exception as e:
            logger.error(f"更新设置文件时出错: {e}")
    
    def update_urls(self):
        """更新 URL 配置"""
        urls_file = Path('steambase/urls.py')
        
        try:
            with open(urls_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 更新 URL 配置
            old_url = "url(r'^ckeditor/', include('ckeditor_uploader.urls')),"
            new_url = "# url(r'^ckeditor/', include('ckeditor_uploader.urls')),  # 已迁移到 CKEditor 5"
            
            content = content.replace(old_url, new_url)
            
            # 启用 CKEditor 5 URL
            old_ckeditor5_url = "# url(r'^ckeditor5/', include('django_ckeditor_5.urls')),"
            new_ckeditor5_url = "url(r'^ckeditor5/', include('django_ckeditor_5.urls')),"
            
            content = content.replace(old_ckeditor5_url, new_ckeditor5_url)
            
            if not self.dry_run:
                with open(urls_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                logger.info(f"已更新 URL 配置: {urls_file}")
            else:
                logger.info(f"[预览] 将更新 URL 配置: {urls_file}")
                
        except Exception as e:
            logger.error(f"更新 URL 配置时出错: {e}")
    
    def run_migration(self):
        """执行完整迁移"""
        logger.info("开始 CKEditor 4 到 CKEditor 5 迁移...")
        
        # 1. 查找所有相关文件
        files = self.find_ckeditor_files()
        logger.info(f"找到 {len(files)} 个文件需要检查")
        
        # 2. 分析每个文件
        all_changes = []
        for file_path in files:
            changes = self.analyze_file(file_path)
            all_changes.extend(changes)
        
        if not all_changes:
            logger.info("没有找到需要迁移的 CKEditor 使用")
            return
        
        # 3. 显示将要进行的更改
        logger.info(f"找到 {len(all_changes)} 个需要更改的地方:")
        for change in all_changes:
            logger.info(f"  {change['file']}: {change['old']} -> {change['new']}")
        
        # 4. 应用更改
        if not self.dry_run:
            # 按文件分组应用更改
            files_to_update = set(change['file'] for change in all_changes)
            for file_path in files_to_update:
                self.apply_changes(file_path, all_changes)
        
        # 5. 更新设置和 URL
        self.update_settings()
        self.update_urls()
        
        # 6. 提示后续步骤
        if not self.dry_run:
            logger.info("\n迁移完成！请执行以下步骤:")
            logger.info("1. 运行数据库迁移: python manage.py makemigrations")
            logger.info("2. 应用迁移: python manage.py migrate")
            logger.info("3. 收集静态文件: python manage.py collectstatic")
            logger.info("4. 重启服务器测试功能")
        else:
            logger.info("\n这是预览模式，没有实际修改文件")
            logger.info("使用 --execute 参数执行实际迁移")

def main():
    parser = argparse.ArgumentParser(description='CKEditor 4 到 CKEditor 5 迁移工具')
    parser.add_argument('--dry-run', action='store_true', default=True,
                       help='预览模式，不实际修改文件 (默认)')
    parser.add_argument('--execute', action='store_true',
                       help='执行实际迁移')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='详细输出')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 如果指定了 --execute，则不是 dry_run
    dry_run = not args.execute
    
    try:
        migrator = CKEditorMigrator(dry_run=dry_run)
        migrator.run_migration()
    except Exception as e:
        logger.error(f"迁移失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
