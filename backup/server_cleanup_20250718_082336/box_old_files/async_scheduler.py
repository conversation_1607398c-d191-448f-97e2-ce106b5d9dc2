#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
异步对战任务调度器
负责管理异步对战流程，定期处理消息队列和状态更新
"""

import asyncio
import logging
import time
from datetime import datetime
from django.core.cache import cache
from django.conf import settings

logger = logging.getLogger(__name__)

class AsyncBattleScheduler:
    """异步对战调度器"""
    
    def __init__(self):
        self.running = False
        self.task = None
        
    async def start(self):
        """启动调度器"""
        if self.running:
            logger.warning("调度器已经在运行")
            return
            
        self.running = True
        # Python 3.6兼容性：使用ensure_future代替create_task
        if hasattr(asyncio, 'create_task'):
            self.task = asyncio.create_task(self._scheduler_loop())
        else:
            self.task = asyncio.ensure_future(self._scheduler_loop())
        logger.info("异步对战调度器已启动")
        
    async def stop(self):
        """停止调度器"""
        if not self.running:
            return
            
        self.running = False
        if self.task:
            self.task.cancel()
            try:
                await self.task
            except asyncio.CancelledError:
                pass
        logger.info("异步对战调度器已停止")
        
    async def _scheduler_loop(self):
        """调度器主循环"""
        try:
            while self.running:
                await self._process_all_battles()
                await asyncio.sleep(0.5)  # 每0.5秒处理一次
        except asyncio.CancelledError:
            logger.info("调度器任务被取消")
        except Exception as e:
            logger.error(f"调度器主循环出错: {e}")
            
    async def _process_all_battles(self):
        """处理所有活跃对战的消息队列"""
        try:
            from .async_battle_progression import AsyncBattleMessageProcessor
            await AsyncBattleMessageProcessor.process_all_battles()
        except Exception as e:
            logger.error(f"处理对战消息队列失败: {e}")

# 全局调度器实例
_scheduler = None

async def start_async_battle_scheduler():
    """启动全局异步对战调度器"""
    global _scheduler
    if _scheduler is None:
        _scheduler = AsyncBattleScheduler()
    await _scheduler.start()

async def stop_async_battle_scheduler():
    """停止全局异步对战调度器"""
    global _scheduler
    if _scheduler:
        await _scheduler.stop()

def get_scheduler():
    """获取调度器实例"""
    global _scheduler
    return _scheduler
