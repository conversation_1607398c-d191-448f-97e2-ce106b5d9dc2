import logging
import random
import time
import json

from django.core.paginator import <PERSON><PERSON><PERSON>, EmptyPage, PageNotAnInteger
from django.db import connection, transaction
from django.db.models import Sum
from django.utils.translation import gettext_lazy as _

from steambase.enums import RespCode, PackageState, TradeRecordState, WxpWithdrawStatus
from steambase.utils import ParamException, pid_generator, parse_tradeurl, parse_usd_to_wxp_price, parse_wxp_price_to_usd, pid_generator
from steambase.utils import is_connection_usable, aware_timestamp_to_timezone
from steambase.redis_con import get_redis

from withdraw.models import TradeRecord, WithdrawItem, WxpTradeOffer
from package.service.item import get_item_price
from package.models import PackageItem, ItemInfo
from package.serializers import ItemInfoSerializer
from withdraw.waxpeer.wxpapi import WxpApi
from withdraw.waxpeer.service import return_trade_record_items

from withdraw.serializers import TradeRecordSerializer, WxpTradeOfferSerializer
from authentication.models import AuthUser


_logger = logging.getLogger(__name__)

wxp_buy_list_cache = 'wxp_buy_list'

def withdraw_items_from_package(user, uids):
    if user.asset.total_charge_balance < 15:
        return RespCode.BusinessError.value, _('您需累计充值15$才能提取饰品')
    if user.extra.ban_withdraw:
        return RespCode.NoPermission.value, _('无权执行此操作')
    trade_url = user.asset.tradeurl
    if not trade_url:
        return RespCode.InvalidParams.value, _('请先输入您的交易链接')
    records = TradeRecord.objects.filter(user=user, state__in=[TradeRecordState.Initialed.value, 
                                                     TradeRecordState.Active.value,
                                                     TradeRecordState.PendClose.value]).count()
    if records > 0:
        return RespCode.InvalidParams.value, _('You have another unclosed trade')
    packages = PackageItem.objects.filter(uid__in=uids, user=user, state=PackageState.Available.value)
    if len(packages) != len(uids):
        return RespCode.InvalidParams.value, _('Invalid package items')
    total_amount = packages.aggregate(Sum('amount')).get('amount__sum') or 0
    total_amount = round(total_amount, 2)
    if total_amount < 3:
        return RespCode.BusinessError.value, _('Minimum withdrawal amount')

    try:
        with transaction.atomic():
            packages = PackageItem.update_state(uids, PackageState.Available.value, PackageState.Withdrawing.value, user)
            trade_data = {
                'user': user,
                'trade_url': trade_url,
                'steam_id': user.username,
                'amount': total_amount,
                'state': TradeRecordState.Active.value
            }
            trade = TradeRecord.objects.create(**trade_data)

            for item in packages:
                WithdrawItem.objects.create(trade=trade, package=item)
            
            resp = {'id': trade.id}
            _logger.info('user:{} withdraw amount: {}'.format(user, total_amount))
            return RespCode.Succeed.value, resp
    except ParamException as e:
        return RespCode.InvalidParams.value, str(e)


def wxp_data_filter(data):
    def _f(x):
        if int(x.get('count', 0)) >10:
            return x
    res = list(filter(_f, data))
    return res


def items_data_format(data):
    res = []
    for item in data:
        info = ItemInfo.objects.filter(market_hash_name=item.get('name')).first()
        if info:
            info_data = ItemInfoSerializer(info, exclude=('id', 'slot', 'quality', 'quality_color', 'item_set',
                                                           'hero', 'name_cn', 'market_name')).data
            info_data['min'] = parse_wxp_price_to_usd(item.get('min', 0))
            info_data['max'] = parse_wxp_price_to_usd(item.get('max', 0))
            info_data['avg'] = parse_wxp_price_to_usd(item.get('avg', 0))
            info_data['count'] = item.get('count', 0)
            info_data['suggest'] = get_item_price(info.market_hash_name)
            res.append(info_data)
    return res


def get_wxp_sell_list(user, min_price, max_price, search):
    if user.extra.ban_withdraw:
        return RespCode.NoPermission.value, _('无权执行此操作')
    trade = TradeRecord.objects.filter(user=user, state__in=[TradeRecordState.Initialed.value, 
                                                     TradeRecordState.Active.value,
                                                     TradeRecordState.PendClose.value]).first()
    if not trade:
        return RespCode.BusinessError.value, _('无权执行此操作')
    amount = trade.amount
    if min_price is None:
        min_price = int(amount - 10)
        if min_price < 0:
            min_price = 0
    if max_price is None:
        max_price = int(amount + 1)
    min_price = parse_usd_to_wxp_price(min_price)
    max_price = parse_usd_to_wxp_price(max_price)
    wxpapi = WxpApi()
    data = wxpapi.get_sell_list(min_price, max_price, search) or []
    if len(data) > 50:
        data = data[:50]
    data = wxp_data_filter(data)
    data = items_data_format(data)
    resp = data
    return RespCode.Succeed.value, resp


def get_wxp_items(user, search, skip, limit, order_by):
    if user.extra.ban_withdraw:
        return RespCode.NoPermission.value, _('无权执行此操作')
    trade = TradeRecord.objects.filter(user=user, state__in=[TradeRecordState.Initialed.value, 
                                                     TradeRecordState.Active.value,
                                                     TradeRecordState.PendClose.value]).first()
    if not trade:
        return RespCode.BusinessError.value, _('无权执行此操作')
    if search is None:
        return RespCode.InvalidParams.value, _('Invalid params')
    if order_by not in ['asc', 'desc']:
        return RespCode.InvalidParams.value, _('Invalid params')

    wxpapi = WxpApi()
    data = wxpapi.get_sell_detail_list(search, skip, limit, order_by) or []
    for item in data:
        item['wxp_price'] = item['price']
        item['price'] = parse_wxp_price_to_usd(item['price'])
        del item['steam_price']
        del item['best_deals']
    iteminfo = items_data_format([{'name':search}])
    resp = {
        'item': iteminfo,
        'offers': data
    }

    return RespCode.Succeed.value, resp


def buy_wxp_items(user, items):
    if user.extra.ban_withdraw:
        return RespCode.NoPermission.value, _('无权执行此操作')
    trade_url = user.asset.tradeurl
    if not trade_url:
        return RespCode.InvalidParams.value, _('请先输入您的交易链接')
    with transaction.atomic():
        trade = TradeRecord.objects.filter(user=user, state=TradeRecordState.Active.value).first()
        if not trade:
            # return RespCode.BusinessError.value, _('无权执行此操作')
            raise ParamException(_('Invalid trade'))
        wxp_amount = sum([i.get('wxp_price', 0) for i in items])
        if wxp_amount < 0:
            # return RespCode.InvalidParams.value, _('Invalid params')
            raise ParamException(_('Error price'))
        total_amount = parse_wxp_price_to_usd(wxp_amount)

        # old_amount_used = trade.wxp_trades.exclude(status=WxpWithdrawStatus.Declined.value).aggregate(Sum('price')).get('price__sum') or 0
        old_amount_used = trade.amount_used
        # print('total_amount', 'trade amount', 'old amount used')
        # print(total_amount, trade.amount, old_amount_used)
        if total_amount > trade.amount - old_amount_used:
            # return RespCode.InvalidParams.value, _('Invalid params')
            raise ParamException(_('Over withdraw price'))
        amount_used = round(old_amount_used + total_amount, 2)
        trade.amount_used = amount_used
        trade.save()
    
        for item in items:
            iteminfo = ItemInfo.objects.filter(market_hash_name=item.get('name', '')).first()
            if not iteminfo:
                raise ParamException(_('Items is not allowed, please try another items'))
            if item.get('price', 0) < 0:
                raise ParamException(_('Invalid params'))
    
    project_id = pid_generator(trade.id)
    partner, token = parse_tradeurl(user.asset.tradeurl)
    wxpapi = WxpApi()

    try:
        for item in items:
            iteminfo = ItemInfo.objects.filter(market_hash_name=item.get('name', '')).first()
            item_id = item.get('id', '')
            # price = parse_usd_to_wxp_price(price)
            wxp_price = item.get('wxp_price', 0)

            conn = get_redis()
            cache_buy = dict(user=user.username, trade_id=trade.id, project_id=project_id, item_id=item_id, wxp_price=wxp_price,
                             token=token, partner=partner, iteminfo_id=iteminfo.id)
            conn.lpush(wxp_buy_list_cache, json.dumps(cache_buy))
            # data, wxp_resp = wxpapi.buy_item(project_id, item_id, wxp_price, token, partner)
            # trade = TradeRecord.objects.filter(user=user, state=TradeRecordState.Active.value).first()
            # if data is None:
            #     _logger.error('wxp buy error: {}'.format(wxp_resp))
            #     old_amount_used = trade.wxp_trades.aggregate(Sum('price')).get('price__sum') or 0
            #     amount_used = round(old_amount_used, 2)
            #     trade.amount_used = amount_used
            #     trade.save()
            #     return RespCode.BusinessError.value, _('buy failed, please try another item')
            # if data.get('success', None) == True:
            #     wxp_id = data.get('id', '')
            #     buy_price = data.get('price', 0)
            #     buy_price = parse_wxp_price_to_usd(buy_price)
            #     offer_data = {
            #         'trade': trade,
            #         'project_id': project_id,
            #         'wxp_id': wxp_id,
            #         'wxp_item_id': item_id,
            #         'price': buy_price,
            #         'market_name': iteminfo.market_hash_name,
            #         'item_info': iteminfo
            #     }

            #     WxpTradeOffer.objects.create(**offer_data)
            #     # old_amount_used = trade.wxp_trades.aggregate(Sum('price')).get('price__sum') or 0
            #     # amount_used = round(old_amount_used, 2)
            #     # trade.amount_used = amount_used
            #     # trade.save()

            # if data.get('success', None) == False:
            #     msg = data.get('msg', '')
            #     return RespCode.BusinessError.value, _('{}'.format(msg))
        return RespCode.Succeed.value, _('success')
    except ParamException as e:
        return RespCode.InvalidParams.value, str(e)


def get_wxp_match(user, skip, limit, order_by):
    if user.extra.ban_withdraw:
        return RespCode.NoPermission.value, _('无权执行此操作')
    trade = TradeRecord.objects.filter(user=user, state__in=[TradeRecordState.Initialed.value, 
                                                     TradeRecordState.Active.value,
                                                     TradeRecordState.PendClose.value]).first()
    if not trade:
        return RespCode.BusinessError.value, _('无权执行此操作')
    # name_list = [i for i. in trade.withdraw_items]
    name_list = []
    items = trade.withdraw_items.all()
    for item in items:
        name_list.append(item.package.item_info.market_hash_name)
    wxpapi = WxpApi()
    data = []
    for name in name_list:
        item_dict = {}
        wxp_data = wxpapi.get_sell_detail_list(name, skip, limit, order_by) or []
        if len(wxp_data) > 0:
            item_dict.update(wxp_data[0])
            iteminfo = ItemInfo.objects.filter(market_hash_name=wxp_data[0].get('name')).first()
            item_dict['market_name_cn'] = iteminfo.market_name_cn
            item_dict['wxp_price'] = wxp_data[0]['price']
            item_dict['price'] = parse_wxp_price_to_usd(wxp_data[0]['price'])
            data.append(item_dict)
        # for item in data:
        #     item['wxp_price'] = item['price']
        #     item['price'] = parse_wxp_price_to_usd(item['price'])
        #     del item['steam_price']
        #     del item['best_deals']
    resp = {
        'items': data
    }

    return RespCode.Succeed.value, resp


def get_withdraw_history(user):
    records = TradeRecord.objects.filter(user=user)
    resp = TradeRecordSerializer(records, many=True, fields=('id', 'trade_url', 'amount', 'amount_used', 'create_time', 
                                                             'drop_refund', 'balance_refund', 'state')).data
    return RespCode.Succeed.value, resp


def get_trade_detail(user, _id):
    if not _id:
        return RespCode.InvalidParams, _("Invalid Params")
    trade = TradeRecord.objects.filter(user=user, id=_id).first()
    resp = TradeRecordSerializer(trade, fields=('id', 'trade_url', 'amount', 'amount_used', 'wxp_offers',
                                                             'drop_refund', 'balance_refund', 'state', 'withdraw_items')).data
    return RespCode.Succeed.value, resp


def check_wxp_offer(user, _id):
    trade = TradeRecord.objects.filter(user=user, id=_id).first()
    data = WxpTradeOfferSerializer(trade.wxp_trades, many=True).data
    resp = data
    return RespCode.Succeed.value, resp
    

def close_tradeoffer(user, _id):
    try:
        with transaction.atomic():
            trade = TradeRecord.objects.filter(user=user, id=_id).first()
            if trade.state == TradeRecordState.Active.value:
                wxp_trades = trade.wxp_trades.all()
                pend_count = trade.wxp_trades.filter(done=False).count()
                if pend_count == 0:
                    return_trade_record_items(trade)
                    trade.state = TradeRecordState.Closed.value
                    trade.save()
                else:
                    trade.state = TradeRecordState.PendClose.value
                    trade.save()
                return RespCode.Succeed.value, {}
            else:
                return RespCode.InvalidParams.value, _("Invalid Params")
    except Exception as e:
        _logger.error('close tradeoffer by id:{} , reason: {}'.format(_id, e))
        return RespCode.BusinessError.value, _('close trade error')


def wxp_buy_worker():
    while True:
        r = get_redis()
        try:
            if not is_connection_usable():
                connection.close()
            # items = r.lrange(wxp_buy_list_cache, 0, -1)
            # print('wxp buy worker items:', items)
            item = r.rpop(wxp_buy_list_cache)
            # for item in items:
            if item:
                wxpapi = WxpApi()
                print('wxp buy item:', item)
                _logger.info('wxp buy data: {}'.format(item))
                # r.lrem(wxp_buy_list_cache, item, num=1)
                data = json.loads(item)
                username = data.get('user')
                trade_id = data.get('trade_id')
                project_id = data.get('project_id')
                item_id = data.get('item_id')
                wxp_price = data.get('wxp_price')
                token = data.get('token')
                partner = data.get('partner')
                iteminfo_id = data.get('iteminfo_id')
                print('data:',data)

                iteminfo = ItemInfo.objects.filter(id=iteminfo_id).first()
                print('iteminfo', iteminfo)
                offer_data = {
                        'project_id': project_id,
                        'wxp_item_id': item_id,
                        'market_name': iteminfo.market_hash_name,
                        'item_info': iteminfo
                }
                print('wxp buy iteminfo', iteminfo)
                _logger.info('wxp buy iteminfo: {}'.format(iteminfo))

                if iteminfo:
                    try:
                        wxp_data, wxp_resp = wxpapi.buy_item(project_id, item_id, wxp_price, token, partner)
                        print('data, resp', wxp_data, wxp_resp)
                    except Exception as e:
                        wxp_data, wxp_resp = None, ''
                        _logger.info('wxp buy item error: {}'.format(e))
                else:
                    wxp_data, wxp_resp = None, ''
                user = AuthUser.objects.filter(username=username).first()
                print('wxp buy return data', wxp_data, wxp_resp)
                _logger.info('wxp state : {}'.format(wxp_data))
                with transaction.atomic():
                    trade = TradeRecord.objects.filter(user=user, state=TradeRecordState.Active.value).first()
                    refund_flag = False
                    if wxp_data is None or wxp_data is 0:
                        refund_flag = True
                    else:
                        if wxp_data.get('success', None) == True:
                            wxp_id = wxp_data.get('id', '')
                            buy_price = wxp_data.get('price', 0)
                            buy_price = parse_wxp_price_to_usd(buy_price)
                            offer_data.update({'wxp_id': wxp_id, 'price': buy_price, 'trade': trade})
                            WxpTradeOffer.objects.create(**offer_data)
                        else:
                            refund_flag = True
                    print('refund_flag', refund_flag)
                    if refund_flag:
                        refund_price = parse_wxp_price_to_usd(wxp_price)
                        old_amount_used = trade.amount_used
                        amount_used = round(old_amount_used - refund_price, 2)
                        if amount_used<0:
                            amount_used = 0
                        trade.amount_used = amount_used
                        trade.save()
                        offer_data.update({'trade': trade, 'done':True, 'price':refund_price,
                                           'status': WxpWithdrawStatus.Declined.value, 'reason':wxp_resp})
                        WxpTradeOffer.objects.create(**offer_data)

                    # old_amount_used = trade.wxp_trades.aggregate(Sum('price')).get('price__sum') or 0
                    # amount_used = round(old_amount_used, 2)
                    # trade.amount_used = amount_used
                    # trade.save()
        except BaseException as e:
            _logger.error('wxp buy error {}'.format(e))
        finally:
            time.sleep(1)