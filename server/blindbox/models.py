from django.db import models, transaction
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from steambase.enums import CaseCategoryType, BlindBoxCategory
from steambase.models import ModelBase, USER_MODEL
from package.models import ItemInfo


class BlindBoxType(models.Model):
    CASE_CATEGORY_TYPE = (
        (1, _('Normal')),
        (2, _('Top')),
        (3, _('Free')),
        (4, _('Festival')),
        (5, _('FreeGive')),
    )
    name = models.CharField(_('name'), max_length=128)
    category = models.SmallIntegerField(_('category'), choices=CASE_CATEGORY_TYPE)
    order = models.IntegerField(_('order'), default=0)

    class Meta:
        ordering = ('category', 'order')
        verbose_name = _('Case Type')
        verbose_name_plural = _('Case Type')

    def __str__(self):
        return self.name


class BlindBox(models.Model):
    name = models.Char<PERSON>ield(_('name'), max_length=128)
    key = models.CharField(_('key'), max_length=128, unique=True)
    price = models.FloatField(_('price'), default=0)
    discount = models.SmallIntegerField(_('discount(%)'), default=100)
    cover = models.ImageField(_('cover image'), upload_to='cases', default=None, null=True, blank=True)
    item = models.ImageField(_('item image'), upload_to='cases', default=None, null=True, blank=True)
    case_type = models.ForeignKey(BlindBoxType, verbose_name=_('blindbox type'), related_name='blindbox')
    order = models.IntegerField(_('order'), default=0)
    unlock = models.BooleanField(_('unlock'), default=True)
    enable = models.BooleanField(_('enable'), default=True)
    limited = models.IntegerField(_('limit timestamp'), default=0)
    limited_time = models.IntegerField(_('limited times'), default=0)

    class Meta:
        ordering = ('case_type', 'order')
        verbose_name = _('BlindBox')
        verbose_name_plural = _('BlindBox')

    def __str__(self):
        return self.name


class BlindBoxDrop(ModelBase):
    item_info = models.ForeignKey(ItemInfo, verbose_name=_('item info'), related_name='blind_box_drops', default=None, null=True, blank=True)
    box = models.ForeignKey(BlindBox, verbose_name=_('case'), related_name='blind_drops')
    show_chance = models.FloatField(_('show chance'), default=0)
    drop_chance_a = models.FloatField(_('drop chance a'), default=0)
    drop_chance_b = models.FloatField(_('drop chance b'), default=0)
    drop_chance_c = models.FloatField(_('drop chance c'), default=0)
    drop_chance_d = models.FloatField(_('drop chance d'), default=0)
    drop_chance_e = models.FloatField(_('drop chance e'), default=0)
    coins = models.FloatField(_('diamond'), default=0)
    custom_enable = models.BooleanField(_("custom enable"), default=False)
    custom_price = models.FloatField(_("custom price"), default=0, null=True, blank=True)

    class Meta:
        verbose_name = _('BlindBoxDrop')
        verbose_name_plural = _('BlindBoxDrop')


class BlindBoxGame(ModelBase):
    user = models.ForeignKey(USER_MODEL, verbose_name=_("user"), related_name='blind_game', )
    gid = models.CharField(_('game id'), max_length=56)
    seed = models.CharField(_('random seed'), max_length=56)

    class Meta:
        verbose_name = _('BlindBoxGame')
        verbose_name_plural = _('BlindBoxGame')

    def __str__(self):
        return self.gid


class BlindBoxRecord(ModelBase):
    CASE_RECORD_TYPE = (
        (BlindBoxCategory.Normal.value, _('Normal')),
        (BlindBoxCategory.Free.value, _('Free')),
    )
    user = models.ForeignKey(USER_MODEL, verbose_name=_("user"), related_name='blind_records')
    item_info = models.ForeignKey(ItemInfo, verbose_name=_("item info"), related_name='blind_records')
    box = models.ForeignKey(BlindBox, verbose_name=_('case'), related_name='records')
    price = models.FloatField(_('price'), default=0)
    source = models.SmallIntegerField(_("type"), default=BlindBoxCategory.Normal.value, choices=CASE_RECORD_TYPE)
    index = models.IntegerField(_('index'), default=0)
    game = models.ForeignKey(BlindBoxGame, verbose_name=_("game"), related_name='records_game', default=None, null=True, blank=True)

    class Meta:
        verbose_name = _('Blind Box Record')
        verbose_name_plural = _('Blind Box Record')

    def __str__(self):
        return self.uid





class FreeCaseConfig(models.Model):
    level = models.IntegerField(_('level'), default=0)
    min_amount = models.FloatField(_('min point'), default=0)
    max_amount = models.FloatField(_('max point'), default=0)
    case = models.ForeignKey(BlindBox, verbose_name=_('case'), related_name='free_case')

    class Meta:
        ordering = ('level',)
        verbose_name = _('Free Case Config')
        verbose_name_plural = _('Free Case Config')

    def __str__(self):
        return str(self.level)


class CaseBot(models.Model):
    user = models.OneToOneField(USER_MODEL, verbose_name=_("user"), related_name='blindbox_bot')
    open_idle_min = models.IntegerField(_('open idle min(seconds)'), default=0)
    open_idle_max = models.IntegerField(_('open idle max(seconds)'), default=0)
    case = models.ManyToManyField(BlindBox, verbose_name=_('case'), related_name='blindbox_bot')
    remark = models.CharField(_('remark'), max_length=128)
    enable = models.BooleanField(_('enable'), default=True)

    class Meta:
        verbose_name = _('Case Bot Config')
        verbose_name_plural = _('Case Bot Config')

    def __str__(self):
        return self.remark
