import logging

from django.contrib import messages
from django.http.response import HttpResponse
from django.shortcuts import redirect, reverse
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.views.decorators.csrf import csrf_exempt

from rest_framework.views import APIView
from rest_framework.permissions import AllowAny
from rest_framework.parsers import <PERSON><PERSON>ars<PERSON>, JSONParser
from rest_framework.response import Response

from steambase.enums import RespCode, PayType, ChargeState
from steambase.utils import reformat_resp, current_user
from sitecfg.interfaces import get_maintenance, get_maintenance_charge
from chat.business import send_chat_message, get_message, mark_message_as_read, get_message_detail


_logger = logging.getLogger(__name__)


class SendMessageView(APIView):

    def post(self, request, format=None):
        try:
            m = get_maintenance()
            if m:
                return reformat_resp(RespCode.Maintenance.value, {},
                                     _('系统维护中，请稍后再试，具体恢复时间请关注网站公告。'))
            user = current_user(request)
            message = request.data.get('comment', None)
            code, resp = send_chat_message(user, message)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, {}, 'Success')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')

class GetMessageView(APIView):
    def get(self, request, format=None):
        try:
            user = current_user(request)  # 获取当前用户
            fields = ('id', 'subject', 'body', 'sent_at', 'sender_name', 'read_at')
            page = int(request.GET.get('page', 1))
            page_size = int(request.GET.get('page_size', 10))

            # 调用获取消息的函数
            code, response_data = get_message(user, fields, page, page_size)
            return reformat_resp(code, response_data, 'Success')  # 直接返回 reformat_resp 的结果
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, 'Exception')

class MarkMessageAsReadView(APIView):
    def post(self, request, message_id, format=None):
        try:
            user = current_user(request)  # 获取当前用户

            # 调用业务函数
            code, data, message = mark_message_as_read(user, message_id)
            return Response(reformat_resp(code, data, message))
        
        except Exception as e:
            _logger.exception(e)
            return Response(reformat_resp(RespCode.Exception.value, {}, 'Exception'))
        
class GetMessageDetailView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            user = current_user(request)  # 获取当前用户
            message_id = request.query_params.get('id', None)  # 获取消息ID
            if not message_id:
                return reformat_resp(RespCode.InvalidParams.value, {}, 'Message ID is required')

            code, resp = get_message_detail(user, message_id)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, 'Exception')