unneeded-not:10:unneeded_not:Consider changing "not not bool_var" to "bool_var"
unneeded-not:12:unneeded_not:Consider changing "not someint == 1" to "someint != 1"
unneeded-not:14:unneeded_not:Consider changing "not someint != 1" to "someint == 1"
unneeded-not:16:unneeded_not:Consider changing "not someint < 1" to "someint >= 1"
unneeded-not:18:unneeded_not:Consider changing "not someint > 1" to "someint <= 1"
unneeded-not:20:unneeded_not:Consider changing "not someint <= 1" to "someint > 1"
unneeded-not:22:unneeded_not:Consider changing "not someint >= 1" to "someint < 1"
unneeded-not:24:unneeded_not:Consider changing "not not someint" to "someint"
unneeded-not:26:unneeded_not:Consider changing "not bool_var == True" to "bool_var != True"
unneeded-not:28:unneeded_not:Consider changing "not bool_var == False" to "bool_var != False"
unneeded-not:30:unneeded_not:Consider changing "not bool_var != True" to "bool_var == True"
unneeded-not:32:unneeded_not:Consider changing "not True == True" to "True != True"
unneeded-not:34:unneeded_not:Consider changing "not 2 in [3, 4]" to "2 not in [3, 4]"
unneeded-not:36:unneeded_not:Consider changing "not someint is 'test'" to "someint is not 'test'"
