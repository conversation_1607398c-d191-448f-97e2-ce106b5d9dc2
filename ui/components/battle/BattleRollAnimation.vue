<!-- components/battle/BattleRollAnimation.vue -->
<template>
  <div class="battle-roll-animation relative">
    <!-- 滚轮容器 -->
    <div class="roll-container bg-gray-900/80 rounded-xl border border-gray-800/30 p-6 relative overflow-hidden">
      <!-- 装饰性光效 -->
      <div class="absolute top-0 left-0 w-16 h-16 rounded-full blur-2xl bg-primary/10 animate-pulse"></div>
      <div class="absolute bottom-0 right-0 w-16 h-16 rounded-full blur-2xl bg-secondary/10 animate-pulse"></div>
      
      <!-- 滚轮指示线 -->
      <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-20">
        <div class="w-1 h-16 bg-primary shadow-glow animate-pulse"></div>
      </div>
      
      <!-- 滚轮轨道 -->
      <div class="roll-track relative h-24 overflow-hidden rounded-lg bg-gray-800/50 border border-gray-700/30">
        <div 
          ref="rollTrack"
          class="roll-items-container flex transition-transform duration-3000 ease-out"
          :style="{ transform: `translateX(${translateX}px)` }"
        >
          <div
            v-for="(item, index) in rollItems"
            :key="`${item.id}-${index}`"
            class="roll-item flex-shrink-0 w-20 h-20 mx-1 relative"
          >
            <!-- 稀有度边框 -->
            <div 
              class="absolute inset-0 rounded-lg border-2 transition-all duration-300"
              :class="getRarityBorderClass(item.rarity)"
              :style="{ 
                borderColor: item.rarity_color || '#6b7280',
                boxShadow: isWinningItem(index) ? `0 0 20px ${item.rarity_color || '#6b7280'}` : 'none'
              }"
            ></div>
            
            <!-- 物品图片 -->
            <div class="absolute inset-1 bg-gray-800/60 rounded-md flex items-center justify-center">
              <img
                :src="item.image"
                :alt="item.name"
                class="w-12 h-12 object-contain transition-transform duration-300"
                :class="{ 'scale-110': isWinningItem(index) }"
                @error="handleImageError"
              >
            </div>
            
            <!-- 获胜高亮效果 -->
            <div
              v-if="isWinningItem(index) && hasWinner"
              class="absolute inset-0 bg-gradient-to-t from-yellow-400/30 to-transparent rounded-lg animate-pulse"
            ></div>
          </div>
        </div>
      </div>
      
      <!-- 开箱状态显示 -->
      <div class="mt-4 text-center">
        <div v-if="isRolling" class="text-primary font-medium animate-pulse">
          <i class="fa fa-sync-alt animate-spin mr-2"></i>
          {{ t('battle.rolling') }}
        </div>
        <div v-else-if="hasWinner && winningItem" class="text-yellow-400 font-bold">
          <i class="fa fa-trophy mr-2"></i>
          {{ getItemDisplayName(winningItem) }} - ${{ winningItem.price?.toFixed(2) }}
        </div>
        <div v-else class="text-white/60">
          {{ t('battle.ready_to_roll') }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import type { CaseItem } from '~/types/case'

// Props & Emits
interface Props {
  items: CaseItem[]
  autoStart?: boolean
  duration?: number
  winnerItem?: CaseItem | null
}

const props = withDefaults(defineProps<Props>(), {
  autoStart: false,
  duration: 3000,
  winnerItem: null
})

const emit = defineEmits<{
  animationComplete: [item: CaseItem]
  animationStart: []
}>()

// 组合函数
const { t } = useI18n()

// 响应式状态
const rollTrack = ref<HTMLElement>()
const translateX = ref(0)
const isRolling = ref(false)
const hasWinner = ref(false)
const winningItem = ref<CaseItem | null>(null)
const rollItems = ref<CaseItem[]>([])

// 计算属性
const itemWidth = 88 // 20 * 4 + 8 (w-20 + mx-1 * 2)
const visibleItems = 12
const totalItems = computed(() => rollItems.value.length)
const winnerIndex = computed(() => {
  if (!hasWinner.value || !winningItem.value) return -1
  return Math.floor(totalItems.value / 2) // 中间位置
})

// 方法
const generateRollItems = (): CaseItem[] => {
  if (!props.items.length) return []
  
  const items: CaseItem[] = []
  
  // 前置填充项
  for (let i = 0; i < visibleItems; i++) {
    items.push(props.items[Math.floor(Math.random() * props.items.length)])
  }
  
  // 中间获胜物品
  if (props.winnerItem) {
    items.push(props.winnerItem)
  } else {
    items.push(props.items[Math.floor(Math.random() * props.items.length)])
  }
  
  // 后置填充项
  for (let i = 0; i < visibleItems; i++) {
    items.push(props.items[Math.floor(Math.random() * props.items.length)])
  }
  
  return items
}

const isWinningItem = (index: number): boolean => {
  return hasWinner.value && index === winnerIndex.value
}

const getRarityBorderClass = (rarity: string): string => {
  const rarityClasses: Record<string, string> = {
    common: 'border-gray-400',
    uncommon: 'border-green-400',
    rare: 'border-blue-400',
    epic: 'border-purple-400',
    legendary: 'border-orange-400',
    mythic: 'border-red-400'
  }
  return rarityClasses[rarity] || 'border-gray-400'
}

const getItemDisplayName = (item: CaseItem): string => {
  if (!item?.name) return 'Unknown Item'
  
  // 移除StatTrak前缀
  let name = item.name.replace('StatTrak™ ', '')
  
  // 长度限制
  if (name.length > 20) {
    name = name.substring(0, 17) + '...'
  }
  
  return name
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/images/default-item.png'
}

const startRoll = async (): Promise<void> => {
  if (isRolling.value) return
  
  isRolling.value = true
  hasWinner.value = false
  winningItem.value = null
  
  // 生成滚动项目
  rollItems.value = generateRollItems()
  
  emit('animationStart')
  
  await nextTick()
  
  // 计算最终位置（居中显示获胜物品）
  const containerWidth = rollTrack.value?.clientWidth || 0
  const targetIndex = Math.floor(totalItems.value / 2)
  const finalPosition = -(targetIndex * itemWidth - containerWidth / 2 + itemWidth / 2)
  
  // 开始滚动动画
  translateX.value = finalPosition
  
  // 动画完成后处理
  setTimeout(() => {
    isRolling.value = false
    hasWinner.value = true
    winningItem.value = rollItems.value[targetIndex]
    
    if (winningItem.value) {
      emit('animationComplete', winningItem.value)
    }
  }, props.duration)
}

const reset = (): void => {
  translateX.value = 0
  isRolling.value = false
  hasWinner.value = false
  winningItem.value = null
  rollItems.value = []
}

// 生命周期
onMounted(() => {
  if (props.autoStart && props.items.length > 0) {
    startRoll()
  }
})

// 暴露方法
defineExpose({
  startRoll,
  reset,
  isRolling: computed(() => isRolling.value)
})
</script>

<style lang="scss" scoped>
.battle-roll-animation {
  .roll-track {
    position: relative;
    
    &::before,
    &::after {
      content: '';
      position: absolute;
      top: 0;
      bottom: 0;
      width: 30px;
      z-index: 10;
      pointer-events: none;
    }
    
    &::before {
      left: 0;
      background: linear-gradient(to right, rgba(17, 24, 39, 0.8), transparent);
    }
    
    &::after {
      right: 0;
      background: linear-gradient(to left, rgba(17, 24, 39, 0.8), transparent);
    }
  }
  
  .roll-items-container {
    will-change: transform;
  }
  
  .roll-item {
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
    }
  }
}

// 光效动画
.shadow-glow {
  box-shadow: 0 0 20px rgba(var(--color-primary-rgb), 0.3);
}

@keyframes glow-pulse {
  0%, 100% {
    box-shadow: 0 0 10px rgba(var(--color-primary-rgb), 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(var(--color-primary-rgb), 0.6);
  }
}

.animate-glow-pulse {
  animation: glow-pulse 2s ease-in-out infinite;
}
</style>
