/**
 * 对战实时同步组合函数
 * 专门处理对战详情页的WebSocket实时动画同步
 */

import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useSocketRoomManager, socketRooms, socketEvents } from '~/utils/socket-manager'

export interface BattleRealtimeState {
  isConnected: boolean
  isReconnecting: boolean
  reconnectAttempts: number
  lastError: string | null
  animationState: 'idle' | 'countdown' | 'opening' | 'revealing' | 'completed'
  currentAnimationId: string | null
  animationProgress: number
  participantStates: Map<string, 'waiting' | 'animating' | 'completed'>
  timeSync: {
    clockOffset: number
    networkDelay: number
    lastSyncTime: number
    syncQuality: 'good' | 'poor' | 'bad'
  }
}

export interface BattleAnimationEvent {
  type: 'room_update' | 'battle_start' | 'round_start' | 'opening_start' | 'animation_progress' | 'round_result' | 'battle_end' | 'room_cancel'
  data: any
  timestamp: number
  battleId: string
  roundNumber?: number
  participantId?: string
}

export function useBattleRealtimeSync(battleId: string) {
  const socketRoomManager = useSocketRoomManager()
  
  // 响应式状态
  const state = ref<BattleRealtimeState>({
    isConnected: false,
    isReconnecting: false,
    reconnectAttempts: 0,
    lastError: null,
    animationState: 'idle',
    currentAnimationId: null,
    animationProgress: 0,
    participantStates: new Map(),
    timeSync: {
      clockOffset: 0,
      networkDelay: 0,
      lastSyncTime: 0,
      syncQuality: 'good'
    }
  })

  // 事件回调
  const callbacks = ref<{
    onRoomUpdate?: (data: any) => void
    onBattleStart?: (data: any) => void
    onRoundStart?: (data: any) => void
    onOpeningStart?: (data: any) => void
    onAnimationProgress?: (data: any) => void
    onRoundResult?: (data: any) => void
    onBattleEnd?: (data: any) => void
    onRoomCancel?: (data: any) => void
  }>({})

  // 计算属性
  const isReady = computed(() => state.value.isConnected && !state.value.isReconnecting)
  const canStartAnimation = computed(() => isReady.value && state.value.animationState === 'idle')

  // WebSocket事件处理
  const handleBattleUpdate = (event: Event) => {
    try {
      const customEvent = event as CustomEvent
      const { action, data } = customEvent.detail
      
      console.log(`[BattleRealtimeSync] 收到对战更新 - ${battleId}:`, action, data)
      
      if (data && data.uid === battleId) {
        const animationEvent: BattleAnimationEvent = {
          type: 'room_update',
          data,
          timestamp: Date.now(),
          battleId
        }
        
        processAnimationEvent(animationEvent)
        callbacks.value.onRoomUpdate?.(data)
      }
    } catch (error) {
      console.error('[BattleRealtimeSync] 处理对战更新失败:', error)
      state.value.lastError = error instanceof Error ? error.message : '处理对战更新失败'
    }
  }

  const handleBattleStart = (event: Event) => {
    try {
      const customEvent = event as CustomEvent
      const data = customEvent.detail?.data
      
      console.log(`[BattleRealtimeSync] 收到对战开始 - ${battleId}:`, data)
      
      if (data && data.uid === battleId) {
        const animationEvent: BattleAnimationEvent = {
          type: 'battle_start',
          data,
          timestamp: Date.now(),
          battleId
        }
        
        processAnimationEvent(animationEvent)
        callbacks.value.onBattleStart?.(data)
      }
    } catch (error) {
      console.error('[BattleRealtimeSync] 处理对战开始失败:', error)
      state.value.lastError = error instanceof Error ? error.message : '处理对战开始失败'
    }
  }

  const handleBattleEnd = (event: Event) => {
    try {
      const customEvent = event as CustomEvent
      const data = customEvent.detail?.data
      
      console.log(`[BattleRealtimeSync] 收到对战结束 - ${battleId}:`, data)
      
      if (data && data.uid === battleId) {
        const animationEvent: BattleAnimationEvent = {
          type: 'battle_end',
          data,
          timestamp: Date.now(),
          battleId
        }
        
        processAnimationEvent(animationEvent)
        callbacks.value.onBattleEnd?.(data)
      }
    } catch (error) {
      console.error('[BattleRealtimeSync] 处理对战结束失败:', error)
      state.value.lastError = error instanceof Error ? error.message : '处理对战结束失败'
    }
  }

  // 处理动画事件
  const processAnimationEvent = (event: BattleAnimationEvent) => {
    switch (event.type) {
      case 'battle_start':
        state.value.animationState = 'countdown'
        state.value.currentAnimationId = `battle_${event.battleId}_start`
        break
        
      case 'round_start':
        state.value.animationState = 'opening'
        state.value.currentAnimationId = `round_${event.roundNumber}_start`
        break
        
      case 'opening_start':
        state.value.animationState = 'opening'
        if (event.participantId) {
          state.value.participantStates.set(event.participantId, 'animating')
        }
        break
        
      case 'animation_progress':
        state.value.animationProgress = event.data.progress || 0
        break
        
      case 'round_result':
        state.value.animationState = 'revealing'
        if (event.participantId) {
          state.value.participantStates.set(event.participantId, 'completed')
        }
        break
        
      case 'battle_end':
        state.value.animationState = 'completed'
        state.value.currentAnimationId = null
        state.value.participantStates.clear()
        break
        
      case 'room_cancel':
        state.value.animationState = 'idle'
        state.value.currentAnimationId = null
        state.value.participantStates.clear()
        break
    }
    
    // 更新时间同步
    updateTimeSync(event.timestamp)
  }

  // 更新时间同步
  const updateTimeSync = (serverTimestamp: number) => {
    const now = Date.now()
    const networkDelay = (now - serverTimestamp) / 2
    const clockOffset = serverTimestamp - now + networkDelay
    
    state.value.timeSync = {
      clockOffset,
      networkDelay,
      lastSyncTime: now,
      syncQuality: networkDelay < 100 ? 'good' : networkDelay < 300 ? 'poor' : 'bad'
    }
  }

  // 初始化WebSocket连接
  const initialize = async () => {
    try {
      console.log(`[BattleRealtimeSync] 初始化实时同步 - ${battleId}`)
      
      // 加入对战房间
      await socketRoomManager.joinRoom(socketRooms.battle(battleId))
      
      // 监听对战事件
      socketRoomManager.addEventListener(socketEvents.battle.update, handleBattleUpdate)
      socketRoomManager.addEventListener(socketEvents.battle.start, handleBattleStart)
      socketRoomManager.addEventListener(socketEvents.battle.end, handleBattleEnd)
      
      state.value.isConnected = true
      state.value.lastError = null
      
      console.log(`[BattleRealtimeSync] 实时同步初始化完成 - ${battleId}`)
    } catch (error) {
      console.error('[BattleRealtimeSync] 初始化失败:', error)
      state.value.lastError = error instanceof Error ? error.message : '初始化失败'
      state.value.isConnected = false
    }
  }

  // 清理资源
  const cleanup = () => {
    console.log(`[BattleRealtimeSync] 清理资源 - ${battleId}`)
    
    // 移除事件监听器
    socketRoomManager.removeEventListener(socketEvents.battle.update, handleBattleUpdate)
    socketRoomManager.removeEventListener(socketEvents.battle.start, handleBattleStart)
    socketRoomManager.removeEventListener(socketEvents.battle.end, handleBattleEnd)
    
    // 清理状态
    state.value.isConnected = false
    state.value.animationState = 'idle'
    state.value.currentAnimationId = null
    state.value.participantStates.clear()
  }

  // 注册事件回调
  const onRoomUpdate = (callback: (data: any) => void) => {
    callbacks.value.onRoomUpdate = callback
  }

  const onBattleStart = (callback: (data: any) => void) => {
    callbacks.value.onBattleStart = callback
  }

  const onRoundStart = (callback: (data: any) => void) => {
    callbacks.value.onRoundStart = callback
  }

  const onOpeningStart = (callback: (data: any) => void) => {
    callbacks.value.onOpeningStart = callback
  }

  const onAnimationProgress = (callback: (data: any) => void) => {
    callbacks.value.onAnimationProgress = callback
  }

  const onRoundResult = (callback: (data: any) => void) => {
    callbacks.value.onRoundResult = callback
  }

  const onBattleEnd = (callback: (data: any) => void) => {
    callbacks.value.onBattleEnd = callback
  }

  const onRoomCancel = (callback: (data: any) => void) => {
    callbacks.value.onRoomCancel = callback
  }

  // 生命周期管理
  onMounted(() => {
    initialize()
  })

  onUnmounted(() => {
    cleanup()
  })

  return {
    // 状态
    state: readonly(state),
    isReady,
    canStartAnimation,
    
    // 事件注册
    onRoomUpdate,
    onBattleStart,
    onRoundStart,
    onOpeningStart,
    onAnimationProgress,
    onRoundResult,
    onBattleEnd,
    onRoomCancel,
    
    // 方法
    initialize,
    cleanup
  }
}
