<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-4">
    <div class="max-w-7xl mx-auto">
      <!-- 页面标题 -->
      <div class="text-center mb-8">
        <h1 class="title-i18n text-4xl font-bold text-white mb-4">对战组件职责分离演示</h1>
        <p class="text-gray-400 max-w-2xl mx-auto">
          展示BattleAnimation组件专注于动画效果，BattlePlayerList组件负责玩家信息和开箱记录
        </p>
      </div>

      <!-- 控制面板 -->
      <div class="bg-slate-800/60 backdrop-blur-xl border border-white/20 rounded-2xl p-6">
        <div class="flex items-center gap-4 mb-6">
          <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
            <Icon name="material-symbols:settings" class="w-5 h-5 text-white" />
          </div>
          <h2 class="title-i18n text-2xl font-bold text-white">控制面板</h2>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <!-- 玩家数量控制 -->
          <div class="space-y-2">
            <label class="font-i18n text-sm font-medium text-white">{{ $t('battle.player_count') }}</label>
            <div class="flex gap-2">
              <button 
                v-for="count in [2, 3, 4]" 
                :key="count"
                @click="setPlayerCount(count)"
                class="flex-1 px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200"
                :class="currentPlayerCount === count 
                  ? 'bg-cyan-500 text-white shadow-lg' 
                  : 'bg-slate-700/50 text-white/60 hover:bg-slate-700/70'"
              >
                {{ count }}
              </button>
            </div>
          </div>

          <!-- 轮次控制 -->
          <div class="space-y-2">
            <label class="font-i18n text-sm font-medium text-white">{{ $t('battle.round_count') }}</label>
            <div class="flex gap-2">
              <button 
                v-for="round in [3, 5, 7]" 
                :key="round"
                @click="setTotalRounds(round)"
                class="flex-1 px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200"
                :class="totalRounds === round 
                  ? 'bg-cyan-500 text-white shadow-lg' 
                  : 'bg-slate-700/50 text-white/60 hover:bg-slate-700/70'"
              >
                {{ round }}
              </button>
            </div>
          </div>

          <!-- 动画控制 -->
          <div class="space-y-2">
            <label class="font-i18n text-sm font-medium text-white">{{ $t('battle.animation_control') }}</label>
            <div class="flex gap-2">
              <button 
                @click="startAnimation"
                :disabled="isAnimating"
                class="flex-1 px-3 py-2 text-sm font-medium bg-green-500 hover:bg-green-600 disabled:bg-slate-600 disabled:cursor-not-allowed text-white rounded-lg transition-all duration-200"
              >
                {{ $t('battle.start_animation') }}
              </button>
              <button 
                @click="resetAnimation"
                class="flex-1 px-3 py-2 text-sm font-medium bg-red-500 hover:bg-red-600 text-white rounded-lg transition-all duration-200"
              >
                {{ $t('battle.reset_animation') }}
              </button>
            </div>
          </div>

          <!-- 音效控制 -->
          <div class="space-y-2">
            <label class="font-i18n text-sm font-medium text-white">{{ $t('battle.audio_control') }}</label>
            <div class="flex gap-2">
              <button 
                @click="toggleAudio"
                class="flex-1 px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200"
                :class="audioEnabled 
                  ? 'bg-yellow-500 text-white' 
                  : 'bg-slate-700/50 text-white/60'"
              >
                <Icon :name="audioEnabled ? 'material-symbols:volume-up' : 'material-symbols:volume-off'" class="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>

        <!-- 状态信息 -->
        <div class="mt-6 p-4 bg-slate-700/30 rounded-lg border border-white/10">
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div class="text-cyan-400 text-lg font-bold">{{ currentPlayerCount }}</div>
              <div class="text-gray-400 text-xs">{{ $t('battle.players') }}</div>
            </div>
            <div>
              <div class="text-green-400 text-lg font-bold">{{ currentRound }}</div>
              <div class="text-gray-400 text-xs">{{ $t('battle.current_round') }}</div>
            </div>
            <div>
              <div class="text-yellow-400 text-lg font-bold">{{ totalRounds }}</div>
              <div class="text-gray-400 text-xs">{{ $t('battle.total_rounds') }}</div>
            </div>
            <div>
              <div class="text-purple-400 text-lg font-bold">{{ Math.round((currentRound / totalRounds) * 100) }}%</div>
              <div class="text-gray-400 text-xs">{{ $t('battle.progress') }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="space-y-6">
        <!-- 动画区域 - 放在玩家列表之上 -->
        <div class="bg-slate-800/60 backdrop-blur-xl border border-white/20 rounded-2xl p-6">
          <div class="flex items-center gap-4 mb-6">
            <div class="w-10 h-10 bg-gradient-to-r from-red-500 to-pink-500 rounded-xl flex items-center justify-center">
              <Icon name="material-symbols:casino" class="w-5 h-5 text-white" />
            </div>
            <h2 class="title-i18n text-2xl font-bold text-white">对战开箱动画</h2>
            <div class="flex items-center gap-2 px-3 py-1 bg-red-500/20 border border-red-400/30 rounded-lg">
              <div class="w-2 h-2 bg-red-400 rounded-full animate-pulse"></div>
              <span class="font-i18n text-sm font-medium text-red-400">{{ $t('battle.live') }}</span>
            </div>
          </div>

          <BattleAnimation
            ref="battleAnimationRef"
            :players="players"
            :current-round="currentRound"
            :total-rounds="totalRounds"
            :is-animating="isAnimating"
            @animation-complete="handleAnimationComplete"
            @animation-start="handleAnimationStart"
            @round-complete="handleRoundComplete"
          />
        </div>

        <!-- 玩家列表和开箱记录 -->
        <div class="bg-slate-800/60 backdrop-blur-xl border border-white/20 rounded-2xl p-6">
          <div class="flex items-center gap-4 mb-6">
            <div class="w-10 h-10 bg-gradient-to-r from-green-500 to-teal-500 rounded-xl flex items-center justify-center">
              <Icon name="material-symbols:group" class="w-5 h-5 text-white" />
            </div>
            <h2 class="title-i18n text-2xl font-bold text-white">玩家开箱结果</h2>
            <div class="text-sm text-white/60">
              {{ currentPlayerCount }}/{{ maxPlayers }} {{ $t('battle.players') }}
            </div>
          </div>

          <!-- 玩家列表容器 -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <BattlePlayerList 
              :players="players" 
              :is-battle-finished="isBattleFinished"
            />
          </div>
        </div>

        <!-- 状态控制 -->
        <div class="mt-8 flex justify-center">
          <button 
            @click="isBattleFinished = !isBattleFinished"
            class="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            {{ isBattleFinished ? '隐藏结果' : '显示结果' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 国际化
const { t, locale } = useI18n()

// 响应式数据
const currentRound = ref(1)
const totalRounds = ref(3)
const isAnimating = ref(false)
const completedPlayers = ref(0)
const battleAnimationRef = ref(null)
const playerCount = ref(3) // 默认3个玩家
const audioEnabled = ref(true)

// 模拟玩家数据
const players = ref([
  // 获胜者 - 显示所有饰品信息（包括历史记录）
  {
    id: 1,
    username: 'winner_user',
    nickname: '获胜者',
    avatar: '/demo/avatar1.png',
    isWinner: true,
    currentRoundResult: {
      id: 1,
      name: 'AK-47 | Fire Serpent',
      name_zh_hans: 'AK-47 | 火蛇',
      image: '/demo/item1.png',
      price: 1250.00,
      rarity_color: '#ffd700',
      rarity_name_zh_hans: '保密',
      condition_name_zh_hans: '崭新出厂',
      is_stat_trak: true,
      round: 3
    },
    openingHistory: [
      {
        id: 2,
        name: 'M4A4 | Howl',
        name_zh_hans: 'M4A4 | 咆哮',
        image: '/demo/item2.png',
        price: 890.00,
        rarity_color: '#ffd700',
        rarity_name_zh_hans: '保密',
        condition_name_zh_hans: '略有磨损',
        is_stat_trak: false,
        round: 1
      },
      {
        id: 3,
        name: 'AWP | Dragon Lore',
        name_zh_hans: 'AWP | 龙狙',
        image: '/demo/item3.png',
        price: 2100.00,
        rarity_color: '#ffd700',
        rarity_name_zh_hans: '保密',
        condition_name_zh_hans: '久经沙场',
        is_stat_trak: true,
        round: 2
      }
    ]
  },
  // 失败者1 - 只显示一件系统赠送饰品
  {
    id: 2,
    username: 'loser_user1',
    nickname: '失败者1',
    avatar: '/demo/avatar2.png',
    isWinner: false,
    currentRoundResult: {
      id: 4,
      name: 'Glock-18 | Fade',
      name_zh_hans: 'Glock-18 | 渐变',
      image: '/demo/item5.png',
      price: 320.00,
      rarity_color: '#ffd700',
      rarity_name_zh_hans: '保密',
      condition_name_zh_hans: '崭新出厂',
      is_stat_trak: false,
      round: 3
    },
    openingHistory: [
      {
        id: 5,
        name: 'MAC-10 | 二西莫夫',
        name_zh_hans: 'MAC-10 | 二西莫夫',
        image: '/demo/item6.png',
        price: 2.34,
        rarity_color: '#4b69ff',
        rarity_name_zh_hans: '军规级',
        condition_name_zh_hans: '略有磨损',
        is_stat_trak: false,
        round: 1
      }
    ]
  },
  // 失败者2 - 只显示一件系统赠送饰品
  {
    id: 3,
    username: 'loser_user2',
    nickname: '失败者2',
    avatar: '/demo/avatar3.png',
    isWinner: false,
    currentRoundResult: {
      id: 6,
      name: 'Desert Eagle | Blaze',
      name_zh_hans: 'Desert Eagle | 烈焰',
      image: '/demo/item7.png',
      price: 45.50,
      rarity_color: '#ff6b35',
      rarity_name_zh_hans: '受限',
      condition_name_zh_hans: '久经沙场',
      is_stat_trak: false,
      round: 3
    },
    openingHistory: []
  },
  // 失败者3 - 只显示一件系统赠送饰品
  {
    id: 4,
    username: 'loser_user3',
    nickname: '失败者3',
    avatar: '/demo/avatar4.png',
    isWinner: false,
    currentRoundResult: {
      id: 7,
      name: 'USP-S | Kill Confirmed',
      name_zh_hans: 'USP-S | 击杀确认',
      image: '/demo/item8.png',
      price: 12.80,
      rarity_color: '#4b69ff',
      rarity_name_zh_hans: '军规级',
      condition_name_zh_hans: '破损不堪',
      is_stat_trak: false,
      round: 3
    },
    openingHistory: []
  }
])

// 对战状态控制
const isBattleFinished = ref(false)

// 生成演示物品数据
function generateDemoItems() {
  const items = []
  for (let i = 1; i <= 20; i++) {
    items.push({
      id: i,
      name: `演示物品${i}`,
      image: `/demo/item${(i % 12) + 1}.png`,
      price: Math.random() * 100 + 1,
      rarity_color: ['#6b7280', '#10b981', '#3b82f6', '#8b5cf6', '#f59e0b', '#ef4444'][Math.floor(Math.random() * 6)],
      rarity_name_zh_hans: ['普通', '罕见', '稀有', '史诗', '传说', '神话'][Math.floor(Math.random() * 6)],
      is_stat_trak: Math.random() > 0.7
    })
  }
  return items
}

// 计算属性
const totalValue = computed(() => {
  let total = 0
  players.value.forEach(player => {
    if (player.currentRoundResult?.price) {
      total += Number(player.currentRoundResult.price)
    }
    if (player.openingHistory) {
      player.openingHistory.forEach(record => {
        if (record.price) {
          total += Number(record.price)
        }
      })
    }
  })
  return total
})

// 方法
const startAnimation = () => {
  if (isAnimating.value) return
  
  isAnimating.value = true
  currentRound.value = 1
  
  // 模拟动画开始
  setTimeout(() => {
    isAnimating.value = false
    currentRound.value = totalRounds.value
  }, 5000)
}

const resetAnimation = () => {
  isAnimating.value = false
  currentRound.value = 1
}

const setPlayerCount = (count) => {
  // 根据数量生成对应的玩家数据
  const playerTemplates = [
    {
      id: 1,
      username: 'winner_user',
      nickname: '获胜者',
      avatar: '/demo/avatar1.png',
      isWinner: true,
      currentRoundResult: {
        id: 1,
        name: 'AK-47 | Fire Serpent',
        name_zh_hans: 'AK-47 | 火蛇',
        image: '/demo/item1.png',
        price: 1250.00,
        rarity_color: '#ffd700',
        rarity_name_zh_hans: '保密',
        condition_name_zh_hans: '崭新出厂',
        is_stat_trak: true,
        round: 3
      },
      openingHistory: [
        {
          id: 2,
          name: 'M4A4 | Howl',
          name_zh_hans: 'M4A4 | 咆哮',
          image: '/demo/item2.png',
          price: 890.00,
          rarity_color: '#ffd700',
          rarity_name_zh_hans: '保密',
          condition_name_zh_hans: '略有磨损',
          is_stat_trak: false,
          round: 1
        },
        {
          id: 3,
          name: 'AWP | Dragon Lore',
          name_zh_hans: 'AWP | 龙狙',
          image: '/demo/item3.png',
          price: 2100.00,
          rarity_color: '#ffd700',
          rarity_name_zh_hans: '保密',
          condition_name_zh_hans: '久经沙场',
          is_stat_trak: true,
          round: 2
        }
      ]
    },
    {
      id: 2,
      username: 'loser_user1',
      nickname: '失败者1',
      avatar: '/demo/avatar2.png',
      isWinner: false,
      currentRoundResult: {
        id: 4,
        name: 'Glock-18 | Fade',
        name_zh_hans: 'Glock-18 | 渐变',
        image: '/demo/item5.png',
        price: 320.00,
        rarity_color: '#ffd700',
        rarity_name_zh_hans: '保密',
        condition_name_zh_hans: '崭新出厂',
        is_stat_trak: false,
        round: 3
      },
      openingHistory: [
        {
          id: 5,
          name: 'MAC-10 | 二西莫夫',
          name_zh_hans: 'MAC-10 | 二西莫夫',
          image: '/demo/item6.png',
          price: 2.34,
          rarity_color: '#4b69ff',
          rarity_name_zh_hans: '军规级',
          condition_name_zh_hans: '略有磨损',
          is_stat_trak: false,
          round: 1
        }
      ]
    },
    {
      id: 3,
      username: 'loser_user2',
      nickname: '失败者2',
      avatar: '/demo/avatar3.png',
      isWinner: false,
      currentRoundResult: {
        id: 6,
        name: 'Desert Eagle | Blaze',
        name_zh_hans: 'Desert Eagle | 烈焰',
        image: '/demo/item7.png',
        price: 45.50,
        rarity_color: '#ff6b35',
        rarity_name_zh_hans: '受限',
        condition_name_zh_hans: '久经沙场',
        is_stat_trak: false,
        round: 3
      },
      openingHistory: []
    },
    {
      id: 4,
      username: 'loser_user3',
      nickname: '失败者3',
      avatar: '/demo/avatar4.png',
      isWinner: false,
      currentRoundResult: {
        id: 7,
        name: 'USP-S | Kill Confirmed',
        name_zh_hans: 'USP-S | 击杀确认',
        image: '/demo/item8.png',
        price: 12.80,
        rarity_color: '#4b69ff',
        rarity_name_zh_hans: '军规级',
        condition_name_zh_hans: '破损不堪',
        is_stat_trak: false,
        round: 3
      },
      openingHistory: []
    }
  ]
  
  players.value = playerTemplates.slice(0, count)
}

const setTotalRounds = (rounds) => {
  totalRounds.value = rounds
}

const toggleAudio = () => {
  audioEnabled.value = !audioEnabled.value
}

const handleAnimationComplete = () => {
  console.log('动画完成')
}

const handleAnimationStart = () => {
  console.log('动画开始')
}

const handleRoundComplete = () => {
  console.log('轮次完成')
}

// 更新玩家数量
const updatePlayerCount = () => {
  const count = parseInt(playerCount.value)
  const newPlayers = []
  
  for (let i = 0; i < count; i++) {
    newPlayers.push({
      id: i + 1,
      nickname: `玩家${i + 1}`,
      avatar: `/demo/avatar${(i % 3) + 1}.png`,
      isHost: i === 0,
      isWinner: false,
      currentRoundResult: null,
      openingHistory: [],
      animationData: {
        selectedCase: {
          key: `case${i + 1}`,
          name: ['普通箱子', '稀有箱子', '史诗箱子', '传说箱子'][i],
          image: `/demo/case${(i % 4) + 1}.png`
        },
        caseItems: generateDemoItems()
      }
    })
  }
  
  players.value = newPlayers
  resetBattle() // 重置对战状态
}

// SEO 配置
useSeoMeta({
  title: () => t('demo.battle_separated.title'),
  description: () => t('demo.battle_separated.description')
})
</script> 