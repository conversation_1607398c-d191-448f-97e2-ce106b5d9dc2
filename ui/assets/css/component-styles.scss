// 组件样式文件 - 遵循 Vue3 + Nuxt3 + TypeScript 开发规范
// 使用 SCSS 特性和 Tailwind CSS 工具类的组合

// ===================
// 基础样式重置和全局设置
// ===================

html {
  color-scheme: dark;
  font-size: 16px;
}

body {
  background-color: var(--color-background);
  color: var(--color-text);
  font-family: var(--font-primary, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 选择文本高亮
::selection {
  background-color: var(--color-primary-700);
  color: var(--color-text);
}

// ===================
// 自定义滚动条样式
// ===================

::-webkit-scrollbar {
  width: 0.5rem;
  height: 0.5rem;
}

::-webkit-scrollbar-track {
  background-color: var(--color-background-lighter);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb {
  background-color: var(--color-primary-700);
  border-radius: var(--radius-full);
  transition: background-color var(--transition-fast) ease;
  
  &:hover {
    background-color: var(--color-primary-600);
  }
}

// ===================
// 通用工具类
// ===================

// 渐变背景工具类
.bg-gradient-primary {
  background: var(--gradient-primary);
}

.bg-gradient-secondary {
  background: var(--gradient-secondary);
}

.bg-gradient-case {
  background: var(--gradient-case);
}

// 阴影工具类
.shadow-neon-primary {
  box-shadow: var(--neon-primary);
}

.shadow-neon-secondary {
  box-shadow: var(--neon-secondary);
}

.shadow-custom-sm {
  box-shadow: var(--shadow-sm);
}

.shadow-custom-md {
  box-shadow: var(--shadow-md);
}

.shadow-custom-lg {
  box-shadow: var(--shadow-lg);
}

// ===================
// CSGO 稀有度样式
// ===================

@mixin rarity-glow($color) {
  box-shadow: 0 0 0.3125rem rgba($color, 0.7);
  border: 1px solid rgba($color, 0.5);
}

.rarity-common {
  @include rarity-glow(#B0C3D9);
}

.rarity-uncommon {
  @include rarity-glow(#5E98D9);
}

.rarity-rare {
  @include rarity-glow(#4B69FF);
}

.rarity-mythical {
  @include rarity-glow(#8847FF);
}

.rarity-legendary {
  @include rarity-glow(#D32CE6);
}

.rarity-ancient {
  @include rarity-glow(#EB4B4B);
}

.rarity-immortal {
  @include rarity-glow(#E4AE33);
}

.rarity-arcana {
  @include rarity-glow(#ADE55C);
}

// ===================
// 动画和交互效果
// ===================

// 悬停效果
.hover-scale {
  transform: scale(1);
  transition: transform var(--transition-fast) var(--ease-out);
  
  &:hover {
    transform: scale(1.05);
  }
}

.hover-glow {
  transition: box-shadow var(--transition-fast) ease;
  
  &:hover {
    box-shadow: var(--neon-primary);
  }
}

// 点击反馈
.click-feedback {
  transition: transform var(--transition-fast) ease;
  
  &:active {
    transform: scale(0.98);
  }
}

// ===================
// 卡片组件样式
// ===================

.card-base {
  background: var(--color-background-lighter);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  transition: all var(--transition-normal) ease;
  
  &:hover {
    border-color: var(--color-border-hover);
    box-shadow: var(--shadow-md);
  }
}

.card-glass {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
}

// ===================
// 按钮样式
// ===================

.btn-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-md);
  font-weight: 600;
  text-decoration: none;
  transition: all var(--transition-fast) ease;
  cursor: pointer;
  border: none;
  outline: none;
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
  }
}

.btn-primary {
  @extend .btn-base;
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-sm);
  
  &:hover:not(:disabled) {
    box-shadow: var(--shadow-md), var(--neon-primary);
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0);
  }
}

.btn-secondary {
  @extend .btn-base;
  background: var(--gradient-secondary);
  color: white;
  
  &:hover:not(:disabled) {
    box-shadow: var(--shadow-md), var(--neon-secondary);
  }
}

.btn-ghost {
  @extend .btn-base;
  background: transparent;
  color: var(--color-text);
  border: 1px solid var(--color-border);
  
  &:hover:not(:disabled) {
    border-color: var(--color-primary);
    color: var(--color-primary);
    box-shadow: var(--shadow-sm);
  }
}

// ===================
// 响应式工具类
// ===================

// 移动优先响应式显示/隐藏
.mobile-only {
  @media (min-width: 768px) {
    display: none !important;
  }
}

.desktop-only {
  @media (max-width: 767px) {
    display: none !important;
  }
}

// ===================
// 无障碍和用户体验
// ===================

// 聚焦样式
.focus-visible {
  &:focus-visible {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
  }
}

// 减少动画（尊重用户偏好）
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

// ===================
// 组件样式结束
// ===================
