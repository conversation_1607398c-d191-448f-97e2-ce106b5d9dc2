<template>
  <div class="min-h-screen relative">
    <!-- 背景层 -->
    <div class="absolute inset-0 overflow-hidden">
      <div class="absolute inset-0 bg-gradient-to-b from-background-darker via-background to-background-dark z-0" />
      <div class="particle-container absolute inset-0 z-0" />
      <div class="absolute top-0 left-0 w-full h-full overflow-hidden opacity-15 z-0">
        <div class="light-beam light-beam-1" />
        <div class="light-beam light-beam-3" />
        <div class="light-beam light-beam-2" />
      </div>
      <div class="absolute inset-0 bg-grid-pattern opacity-[0.04] z-0" />
    </div>

    <!-- PC版内容 -->
    <div class="container mx-auto px-4 py-6 relative z-10" v-if="!isMobile">
      <div class="max-w-6xl mx-auto">
        <!-- 页面头部 -->
        <div class="page-header mb-12">
          <h1 class="text-4xl font-bold text-white mb-4">{{ $t('support.title') }}</h1>
          <p class="text-gray-300 text-lg">{{ $t('support.description') }}</p>
        </div>

        <!-- 联系方式 -->
        <div class="contact-methods mb-12">
          <h2 class="text-2xl font-semibold text-white mb-6">{{ $t('support.contact.title') }}</h2>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div
              v-for="method in contactMethods"
              :key="method.id"
              class="contact-method bg-white/5 backdrop-blur-md border border-white/10 rounded-2xl p-6 text-center hover:bg-white/10 transition-all duration-300"
            >
              <i :class="`${method.icon} w-12 h-12 text-primary mx-auto mb-4`" />
              <h3 class="text-xl font-semibold text-white mb-2">{{ $t(`support.contact.${method.id}.title`) }}</h3>
              <p class="text-gray-300 mb-4">{{ $t(`support.contact.${method.id}.description`) }}</p>
              <a
                :href="method.link"
                class="inline-flex items-center space-x-2 text-primary hover:text-primary-light transition-colors duration-200"
              >
                <span>{{ method.value }}</span>
                <i class="i-heroicons-arrow-right w-4 h-4" />
              </a>
            </div>
          </div>
        </div>

        <!-- 工单表单 -->
        <div class="ticket-form mb-12">
          <h2 class="text-2xl font-semibold text-white mb-6">{{ $t('support.ticket.title') }}</h2>
          <div class="bg-white/5 backdrop-blur-md border border-white/10 rounded-2xl p-8">
            <form @submit.prevent="submitTicket" class="space-y-6">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label class="block text-white font-medium mb-2">{{ $t('support.ticket.name') }}</label>
                  <input
                    v-model="ticketForm.name"
                    type="text"
                    required
                    class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-transparent"
                    :placeholder="$t('support.ticket.name_placeholder')"
                  />
                </div>
                <div>
                  <label class="block text-white font-medium mb-2">{{ $t('support.ticket.email') }}</label>
                  <input
                    v-model="ticketForm.email"
                    type="email"
                    required
                    class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-transparent"
                    :placeholder="$t('support.ticket.email_placeholder')"
                  />
                </div>
              </div>
              <div>
                <label class="block text-white font-medium mb-2">{{ $t('support.ticket.subject') }}</label>
                <input
                  v-model="ticketForm.subject"
                  type="text"
                  required
                  class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-transparent"
                  :placeholder="$t('support.ticket.subject_placeholder')"
                />
              </div>
              <div>
                <label class="block text-white font-medium mb-2">{{ $t('support.ticket.category') }}</label>
                <select
                  v-model="ticketForm.category"
                  required
                  class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-transparent"
                >
                  <option value="">{{ $t('support.ticket.select_category') }}</option>
                  <option
                    v-for="category in ticketCategories"
                    :key="category.id"
                    :value="category.id"
                  >
                    {{ $t(`support.ticket.categories.${category.id}`) }}
                  </option>
                </select>
              </div>
              <div>
                <label class="block text-white font-medium mb-2">{{ $t('support.ticket.message') }}</label>
                <textarea
                  v-model="ticketForm.message"
                  required
                  rows="6"
                  class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-transparent resize-none"
                  :placeholder="$t('support.ticket.message_placeholder')"
                ></textarea>
              </div>
              <div class="flex justify-end">
                <button
                  type="submit"
                  :disabled="isSubmitting"
                  class="bg-primary hover:bg-primary-dark text-white font-semibold py-3 px-8 rounded-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span v-if="!isSubmitting">{{ $t('support.ticket.submit') }}</span>
                  <span v-else class="flex items-center space-x-2">
                    <i class="i-heroicons-arrow-path w-5 h-5 animate-spin" />
                    <span>{{ $t('support.ticket.submitting') }}</span>
                  </span>
                </button>
              </div>
            </form>
          </div>
        </div>

        <!-- FAQ快捷入口 -->
        <div class="faq-section">
          <h2 class="text-2xl font-semibold text-white mb-6">{{ $t('support.faq.title') }}</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div
              v-for="faq in quickFaqs"
              :key="faq.id"
              class="faq-card bg-white/5 backdrop-blur-md border border-white/10 rounded-2xl p-6 hover:bg-white/10 transition-all duration-300 cursor-pointer"
              @click="goToFaq(faq.id)"
            >
              <i :class="`${faq.icon} w-8 h-8 text-primary mb-4`" />
              <h3 class="text-lg font-semibold text-white mb-2">{{ $t(`support.faq.${faq.id}.title`) }}</h3>
              <p class="text-gray-300 text-sm">{{ $t(`support.faq.${faq.id}.description`) }}</p>
            </div>
          </div>
          <div class="text-center mt-8">
            <NuxtLink
              to="/faq"
              class="inline-flex items-center space-x-2 bg-primary hover:bg-primary-dark text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300"
            >
              <span>{{ $t('support.faq.view_all') }}</span>
              <i class="i-heroicons-arrow-right w-5 h-5" />
            </NuxtLink>
          </div>
        </div>
      </div>
    </div>

    <!-- 移动版内容 -->
    <div class="container mx-auto px-4 py-4 relative z-10" v-else>
      <div class="max-w-4xl mx-auto">
        <!-- 页面头部 -->
        <div class="page-header mb-8">
          <h1 class="text-2xl font-bold text-white mb-3">{{ $t('support.title') }}</h1>
          <p class="text-gray-300 text-sm">{{ $t('support.description') }}</p>
        </div>

        <!-- 联系方式 -->
        <div class="contact-methods mb-8">
          <h2 class="text-xl font-semibold text-white mb-4">{{ $t('support.contact.title') }}</h2>
          <div class="space-y-4">
            <div
              v-for="method in contactMethods"
              :key="method.id"
              class="contact-method bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-4 text-center"
            >
              <i :class="`${method.icon} w-8 h-8 text-primary mx-auto mb-3`" />
              <h3 class="text-lg font-semibold text-white mb-2">{{ $t(`support.contact.${method.id}.title`) }}</h3>
              <p class="text-gray-300 text-sm mb-3">{{ $t(`support.contact.${method.id}.description`) }}</p>
              <a
                :href="method.link"
                class="inline-flex items-center space-x-2 text-primary hover:text-primary-light transition-colors duration-200 text-sm"
              >
                <span>{{ method.value }}</span>
                <i class="i-heroicons-arrow-right w-4 h-4" />
              </a>
            </div>
          </div>
        </div>

        <!-- 工单表单 -->
        <div class="ticket-form mb-8">
          <h2 class="text-xl font-semibold text-white mb-4">{{ $t('support.ticket.title') }}</h2>
          <div class="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-6">
            <form @submit.prevent="submitTicket" class="space-y-4">
              <div>
                <label class="block text-white font-medium mb-2 text-sm">{{ $t('support.ticket.name') }}</label>
                <input
                  v-model="ticketForm.name"
                  type="text"
                  required
                  class="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-transparent text-sm"
                  :placeholder="$t('support.ticket.name_placeholder')"
                />
              </div>
              <div>
                <label class="block text-white font-medium mb-2 text-sm">{{ $t('support.ticket.email') }}</label>
                <input
                  v-model="ticketForm.email"
                  type="email"
                  required
                  class="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-transparent text-sm"
                  :placeholder="$t('support.ticket.email_placeholder')"
                />
              </div>
              <div>
                <label class="block text-white font-medium mb-2 text-sm">{{ $t('support.ticket.subject') }}</label>
                <input
                  v-model="ticketForm.subject"
                  type="text"
                  required
                  class="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-transparent text-sm"
                  :placeholder="$t('support.ticket.subject_placeholder')"
                />
              </div>
              <div>
                <label class="block text-white font-medium mb-2 text-sm">{{ $t('support.ticket.category') }}</label>
                <select
                  v-model="ticketForm.category"
                  required
                  class="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-transparent text-sm"
                >
                  <option value="">{{ $t('support.ticket.select_category') }}</option>
                  <option
                    v-for="category in ticketCategories"
                    :key="category.id"
                    :value="category.id"
                  >
                    {{ $t(`support.ticket.categories.${category.id}`) }}
                  </option>
                </select>
              </div>
              <div>
                <label class="block text-white font-medium mb-2 text-sm">{{ $t('support.ticket.message') }}</label>
                <textarea
                  v-model="ticketForm.message"
                  required
                  rows="4"
                  class="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-transparent resize-none text-sm"
                  :placeholder="$t('support.ticket.message_placeholder')"
                ></textarea>
              </div>
              <div class="flex justify-end">
                <button
                  type="submit"
                  :disabled="isSubmitting"
                  class="w-full bg-primary hover:bg-primary-dark text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span v-if="!isSubmitting">{{ $t('support.ticket.submit') }}</span>
                  <span v-else class="flex items-center justify-center space-x-2">
                    <i class="i-heroicons-arrow-path w-4 h-4 animate-spin" />
                    <span>{{ $t('support.ticket.submitting') }}</span>
                  </span>
                </button>
              </div>
            </form>
          </div>
        </div>

        <!-- FAQ快捷入口 -->
        <div class="faq-section">
          <h2 class="text-xl font-semibold text-white mb-4">{{ $t('support.faq.title') }}</h2>
          <div class="space-y-3">
            <div
              v-for="faq in quickFaqs"
              :key="faq.id"
              class="faq-card bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-4 cursor-pointer"
              @click="goToFaq(faq.id)"
            >
              <div class="flex items-center space-x-3">
                <i :class="`${faq.icon} w-6 h-6 text-primary`" />
                <div>
                  <h3 class="text-white font-medium text-sm">{{ $t(`support.faq.${faq.id}.title`) }}</h3>
                  <p class="text-gray-300 text-xs">{{ $t(`support.faq.${faq.id}.description`) }}</p>
                </div>
              </div>
            </div>
          </div>
          <div class="text-center mt-6">
            <NuxtLink
              to="/faq"
              class="inline-flex items-center space-x-2 bg-primary hover:bg-primary-dark text-white font-semibold py-2 px-4 rounded-lg transition-all duration-300 text-sm"
            >
              <span>{{ $t('support.faq.view_all') }}</span>
              <i class="i-heroicons-arrow-right w-4 h-4" />
            </NuxtLink>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 国际化
const { t } = useI18n()

// 设备检测 - 使用响应式状态
const isMobile = ref(false)

// 在客户端检测设备类型
onMounted(() => {
  const checkMobile = () => {
    isMobile.value = window.innerWidth < 768
  }
  
  checkMobile()
  window.addEventListener('resize', checkMobile)
  
  onUnmounted(() => {
    window.removeEventListener('resize', checkMobile)
  })
})

// SEO配置
useSeoMeta({
  title: () => t('support.seo.title'),
  description: () => t('support.seo.description'),
  keywords: () => t('support.seo.keywords'),
  ogTitle: () => t('support.seo.title'),
  ogDescription: () => t('support.seo.description'),
  twitterTitle: () => t('support.seo.title'),
  twitterDescription: () => t('support.seo.description')
})

// 响应式数据
const isSubmitting = ref(false)
const ticketForm = ref({
  name: '',
  email: '',
  subject: '',
  category: '',
  message: ''
})

// 联系方式
const contactMethods = [
  {
    id: 'email',
    icon: 'i-heroicons-envelope',
    value: '<EMAIL>',
    link: 'mailto:<EMAIL>'
  },
  {
    id: 'live_chat',
    icon: 'i-heroicons-chat-bubble-left-right',
    value: 'Live Chat',
    link: '#'
  },
  {
    id: 'discord',
    icon: 'i-simple-icons-discord',
    value: 'Discord Server',
    link: 'https://discord.gg/csgoskins'
  }
]

// 工单分类
const ticketCategories = [
  { id: 'technical', name: 'support.ticket.categories.technical' },
  { id: 'billing', name: 'support.ticket.categories.billing' },
  { id: 'account', name: 'support.ticket.categories.account' },
  { id: 'gameplay', name: 'support.ticket.categories.gameplay' },
  { id: 'other', name: 'support.ticket.categories.other' }
]

// 快捷FAQ
const quickFaqs = [
  { id: 'how_to_recharge', icon: 'i-heroicons-credit-card' },
  { id: 'how_to_open_case', icon: 'i-heroicons-gift' },
  { id: 'account_security', icon: 'i-heroicons-shield-check' },
  { id: 'payment_methods', icon: 'i-heroicons-currency-dollar' },
  { id: 'withdrawal_process', icon: 'i-heroicons-arrow-down-tray' },
  { id: 'technical_issues', icon: 'i-heroicons-wrench-screwdriver' }
]

// 方法
const submitTicket = async () => {
  isSubmitting.value = true
  
  try {
    // 模拟提交工单
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 重置表单
    ticketForm.value = {
      name: '',
      email: '',
      subject: '',
      category: '',
      message: ''
    }
    
    // 显示成功消息
    console.log('工单提交成功')
  } catch (error) {
    console.error('工单提交失败:', error)
  } finally {
    isSubmitting.value = false
  }
}

const goToFaq = (faqId: string) => {
  // 跳转到FAQ页面并定位到特定问题
  navigateTo(`/faq#${faqId}`)
}
</script>

<style scoped>
/* 粒子效果 */
.particle-container {
  background-image: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
}

/* 动态光线效果 */
.light-beam {
  position: absolute;
  width: 2px;
  height: 100%;
  background: linear-gradient(to bottom, transparent, var(--color-primary), transparent);
  animation: lightBeam 8s infinite linear;
}

.light-beam-1 {
  left: 10%;
  animation-delay: 0s;
}

.light-beam-2 {
  left: 50%;
  animation-delay: 2.5s;
}

.light-beam-3 {
  left: 90%;
  animation-delay: 5s;
}

@keyframes lightBeam {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(100%);
    opacity: 0;
  }
}

/* 网格背景 */
.bg-grid-pattern {
  background-image: linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
}

/* 页面头部样式 */
.page-header {
  text-align: center;
  position: relative;
}

.page-header::after {
  content: '';
  position: absolute;
  bottom: -1rem;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
  border-radius: 2px;
}

/* 联系方式卡片样式 */
.contact-method {
  transition: all 0.3s ease;
}

.contact-method:hover {
  transform: translateY(-4px);
}

/* FAQ卡片样式 */
.faq-card {
  transition: all 0.3s ease;
}

.faq-card:hover {
  transform: translateY(-2px);
}

/* 表单样式 */
input, select, textarea {
  transition: all 0.2s ease;
}

input:focus, select:focus, textarea:focus {
  box-shadow: 0 0 0 3px rgba(var(--color-primary-rgb), 0.1);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .page-header h1 {
    font-size: 1.75rem;
  }
  
  .contact-method {
    padding: 1rem;
  }
  
  .faq-card {
    padding: 1rem;
  }
}
</style> 