#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
开箱对战系统监控诊断脚本
实时检测系统状态和性能指标
"""

import os
import sys
import time
import json
import logging
from datetime import datetime, timedelta
from collections import defaultdict

# Django环境设置
sys.path.insert(0, '/www/wwwroot/csgoskins.com.cn/server')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')

try:
    import django
    django.setup()
    
    from django.core.cache import cache
    from django.db import connection
    from django.utils import timezone
    from django.db.models import Count, Sum, Avg
    from box.models import CaseRoom, CaseRoomBet, CaseRoomRound, CaseRoomItem
    from steambase.enums import GameState
except ImportError as e:
    print(f"Django导入失败: {e}")
    sys.exit(1)

# 安全导入配置文件
try:
    from box.battle_config import BattleSystemConfig, BattleSystemMetrics
except ImportError:
    print("警告: 无法导入自定义配置，使用默认配置")
    
    class BattleSystemConfig:
        def __init__(self):
            self.CACHE_KEY_PREFIX = "battle:room:"
            self.DEDUP_KEY_PREFIX = "dedup:msg:"
            
        def validate_config(self):
            return []
    
    class BattleSystemMetrics:
        def __init__(self):
            pass
            
        def get_counter(self, key):
            return 0
            
        def get_average_timing(self, key):
            return 0.0

logger = logging.getLogger(__name__)

class BattleSystemMonitor:
    """对战系统监控器"""
    
    def __init__(self):
        self.start_time = datetime.now()
        self.metrics = BattleSystemMetrics()
        self.config = BattleSystemConfig()
    
    def run_full_diagnosis(self):
        """运行完整诊断"""
        print("=" * 80)
        print("🔍 开箱对战系统诊断报告")
        print("=" * 80)
        print(f"诊断时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # 1. 系统状态检查
        self._check_system_status()
        
        # 2. 数据库状态检查
        self._check_database_status()
        
        # 3. 缓存状态检查
        self._check_cache_status()
        
        # 4. 性能指标检查
        self._check_performance_metrics()
        
        # 5. 业务逻辑检查
        self._check_business_logic()
        
        # 6. 错误分析
        self._analyze_errors()
        
        # 7. 优化建议
        self._provide_recommendations()
        
        print("=" * 80)
        print("✅ 诊断完成")
        print("=" * 80)
    
    def _check_system_status(self):
        """检查系统状态"""
        print("📊 系统状态检查")
        print("-" * 40)
        
        # Python版本检查
        python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
        print(f"Python版本: {python_version}")
        
        if sys.version_info < (3, 7):
            print("⚠️  Python版本过低，建议升级到3.7+以获得更好的异步支持")
        else:
            print("✅ Python版本支持异步功能")
        
        # 异步支持检查
        try:
            import asyncio
            if hasattr(asyncio, 'create_task'):
                print("✅ 异步功能完全支持")
            else:
                print("⚠️  异步功能部分支持（兼容模式）")
        except ImportError:
            print("❌ 异步功能不可用")
        
        # 配置验证
        config_errors = self.config.validate_config()
        if config_errors:
            print("❌ 配置错误:")
            for error in config_errors:
                print(f"   - {error}")
        else:
            print("✅ 配置验证通过")
        
        print()
    
    def _check_database_status(self):
        """检查数据库状态"""
        print("🗄️  数据库状态检查")
        print("-" * 40)
        
        try:
            # 连接状态
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                print("✅ 数据库连接正常")
            
            # 房间统计
            total_rooms = CaseRoom.objects.count()
            active_rooms = CaseRoom.objects.filter(
                state__in=[GameState.Joinable.value, GameState.Running.value]
            ).count()
            
            print(f"总房间数: {total_rooms}")
            print(f"活跃房间数: {active_rooms}")
            
            if active_rooms > 100:
                print(f"⚠️  活跃房间数量较多 ({active_rooms})，可能影响性能")
            
            # 状态分布
            state_distribution = CaseRoom.objects.values('state').annotate(
                count=Count('id')
            ).order_by('state')
            
            print("房间状态分布:")
            for item in state_distribution:
                state_name = self._get_state_name(item['state'])
                print(f"  {state_name}: {item['count']}")
            
            # 数据一致性检查
            self._check_data_consistency()
            
        except Exception as e:
            print(f"❌ 数据库检查失败: {e}")
        
        print()
    
    def _check_cache_status(self):
        """检查缓存状态"""
        print("💾 缓存状态检查")
        print("-" * 40)
        
        try:
            # Redis连接测试
            from django_redis import get_redis_connection
            redis_client = get_redis_connection('default')
            redis_client.ping()
            print("✅ Redis连接正常")
            
            # 缓存使用情况
            info = redis_client.info('memory')
            used_memory = info.get('used_memory_human', 'Unknown')
            print(f"Redis内存使用: {used_memory}")
            
            # 检查对战相关缓存
            battle_keys = redis_client.keys(f"{self.config.CACHE_KEY_PREFIX}*")
            dedup_keys = redis_client.keys(f"{self.config.DEDUP_KEY_PREFIX}*")
            
            print(f"对战缓存键数量: {len(battle_keys)}")
            print(f"消息去重键数量: {len(dedup_keys)}")
            
            if len(dedup_keys) > 1000:
                print("⚠️  消息去重键过多，可能需要清理")
            
        except Exception as e:
            print(f"❌ 缓存检查失败: {e}")
        
        print()
    
    def _check_performance_metrics(self):
        """检查性能指标"""
        print("⚡ 性能指标检查")
        print("-" * 40)
        
        # 获取各种指标
        metrics = [
            'rooms_created_today',
            'rounds_processed_today',
            'messages_sent_today',
            'errors_count_today',
            'async_fallbacks_today'
        ]
        
        for metric in metrics:
            value = self.metrics.get_counter(metric)
            print(f"{metric}: {value}")
        
        # 平均执行时间
        timing_metrics = [
            'room_creation_time',
            'round_processing_time',
            'message_sending_time'
        ]
        
        print("\n平均执行时间:")
        for metric in timing_metrics:
            avg_time = self.metrics.get_average_timing(metric)
            print(f"{metric}: {avg_time:.3f}s")
            
            if avg_time > 5.0:
                print(f"⚠️  {metric} 执行时间过长")
        
        print()
    
    def _check_business_logic(self):
        """检查业务逻辑"""
        print("🎮 业务逻辑检查")
        print("-" * 40)
        
        # 检查卡住的房间
        stuck_rooms = CaseRoom.objects.filter(
            state=GameState.Running.value,
            update_time__lt=timezone.now() - timedelta(minutes=10)
        )
        
        if stuck_rooms.exists():
            print(f"❌ 发现 {stuck_rooms.count()} 个可能卡住的房间:")
            for room in stuck_rooms[:5]:  # 只显示前5个
                print(f"   - 房间 {room.short_id}, 最后更新: {room.update_time}")
        else:
            print("✅ 没有发现卡住的房间")
        
        # 检查数据不一致
        inconsistent_bets = CaseRoomBet.objects.filter(
            open_amount__isnull=True,
            room__state=GameState.End.value
        )
        
        if inconsistent_bets.exists():
            print(f"⚠️  发现 {inconsistent_bets.count()} 个数据不一致的投注记录")
        else:
            print("✅ 投注数据一致性正常")
        
        # 检查轮次状态
        orphaned_rounds = CaseRoomRound.objects.filter(
            opened=False,
            room__state=GameState.End.value
        )
        
        if orphaned_rounds.exists():
            print(f"⚠️  发现 {orphaned_rounds.count()} 个孤立的轮次记录")
        else:
            print("✅ 轮次状态正常")
        
        print()
    
    def _check_data_consistency(self):
        """检查数据一致性"""
        print("数据一致性检查:")
        
        try:
            # 检查房间与投注的一致性
            rooms_with_mismatched_bets = []
            
            running_rooms = CaseRoom.objects.filter(state=GameState.Running.value)[:10]  # 限制查询数量
            
            for room in running_rooms:
                expected_bets = room.max_joiner
                actual_bets = CaseRoomBet.objects.filter(room=room).count()
                
                if actual_bets != expected_bets:
                    rooms_with_mismatched_bets.append((room.short_id, expected_bets, actual_bets))
            
            if rooms_with_mismatched_bets:
                print("  ⚠️  投注数量不匹配的房间:")
                for room_id, expected, actual in rooms_with_mismatched_bets[:3]:
                    print(f"     房间 {room_id}: 期望 {expected}, 实际 {actual}")
            else:
                print("  ✅ 房间投注数量一致")
            
            # 检查轮次数据一致性
            rooms_with_incomplete_rounds = CaseRoom.objects.filter(
                state=GameState.End.value,
                rounds__opened=False
            ).distinct()[:5]
            
            if rooms_with_incomplete_rounds.exists():
                print(f"  ⚠️  发现 {rooms_with_incomplete_rounds.count()} 个有未完成轮次的已结束房间")
            else:
                print("  ✅ 轮次数据一致")
                
        except Exception as e:
            print(f"  ❌ 数据一致性检查失败: {e}")
            import traceback
            print(f"  详细错误: {traceback.format_exc()}")
    
    def _analyze_errors(self):
        """分析错误"""
        print("🔍 错误分析")
        print("-" * 40)
        
        # 从日志或缓存中获取最近的错误
        error_patterns = [
            'module \'asyncio\' has no attribute \'create_task\'',
            'expected str instance, list found',
            'sequence item 2: expected str instance, list found',
            '轮次验证失败',
            '异步轮次推进失败'
        ]
        
        print("常见错误模式检查:")
        for pattern in error_patterns:
            # 这里可以检查日志文件或错误计数
            print(f"  - {pattern}: 需要检查日志文件")
        
        # 检查最近的异步降级情况
        fallback_count = self.metrics.get_counter('async_fallbacks_today')
        if fallback_count > 0:
            print(f"⚠️  今日异步降级次数: {fallback_count}")
            print("   建议检查异步系统稳定性")
        
        print()
    
    def _provide_recommendations(self):
        """提供优化建议"""
        print("💡 优化建议")
        print("-" * 40)
        
        recommendations = []
        
        # Python版本建议
        if sys.version_info < (3, 7):
            recommendations.append("升级Python到3.7+版本以获得更好的异步支持")
        
        # 活跃房间数量建议
        active_rooms = CaseRoom.objects.filter(
            state__in=[GameState.Joinable.value, GameState.Running.value]
        ).count()
        
        if active_rooms > 50:
            recommendations.append("考虑增加房间清理机制，减少活跃房间数量")
        
        # 缓存使用建议
        try:
            from django_redis import get_redis_connection
            redis_client = get_redis_connection('default')
            dedup_keys = redis_client.keys(f"{self.config.DEDUP_KEY_PREFIX}*")
            
            if len(dedup_keys) > 500:
                recommendations.append("定期清理过期的消息去重键")
        except:
            pass
        
        # 性能建议
        avg_round_time = self.metrics.get_average_timing('round_processing_time')
        if avg_round_time > 3.0:
            recommendations.append("优化轮次处理逻辑，减少处理时间")
        
        if not recommendations:
            print("✅ 系统运行良好，暂无优化建议")
        else:
            for i, rec in enumerate(recommendations, 1):
                print(f"{i}. {rec}")
        
        print()
    
    def _get_state_name(self, state_value):
        """获取状态名称"""
        state_names = {
            GameState.Initial.value: "初始化",
            GameState.Joinable.value: "可加入",
            GameState.Joining.value: "加入中",
            GameState.Full.value: "已满",
            GameState.Running.value: "运行中",
            GameState.End.value: "已结束",
            GameState.Cancelled.value: "已取消"
        }
        return state_names.get(state_value, f"未知({state_value})")
    
    def monitor_realtime(self, duration_seconds=300):
        """实时监控"""
        print(f"🔄 开始实时监控 (持续{duration_seconds}秒)")
        print("-" * 40)
        
        start_time = time.time()
        last_check = start_time
        
        while time.time() - start_time < duration_seconds:
            current_time = time.time()
            
            if current_time - last_check >= 30:  # 每30秒检查一次
                # 获取当前状态
                active_rooms = CaseRoom.objects.filter(
                    state__in=[GameState.Joinable.value, GameState.Running.value]
                ).count()
                
                running_rooms = CaseRoom.objects.filter(
                    state=GameState.Running.value
                ).count()
                
                print(f"[{datetime.now().strftime('%H:%M:%S')}] "
                      f"活跃房间: {active_rooms}, 运行中: {running_rooms}")
                
                last_check = current_time
            
            time.sleep(1)
        
        print("实时监控结束")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='开箱对战系统监控诊断')
    parser.add_argument('--mode', choices=['diagnosis', 'monitor'], 
                       default='diagnosis', help='运行模式')
    parser.add_argument('--duration', type=int, default=300,
                       help='监控持续时间（秒）')
    
    args = parser.parse_args()
    
    monitor = BattleSystemMonitor()
    
    if args.mode == 'diagnosis':
        monitor.run_full_diagnosis()
    elif args.mode == 'monitor':
        monitor.monitor_realtime(args.duration)

if __name__ == '__main__':
    main()
