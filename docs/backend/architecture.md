# 项目架构文档

## 系统架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端客户端     │    │     Nginx        │    │   负载均衡器    │
│   (Browser)     │◄──►│   (反向代理)      │◄──►│  (可选)         │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────────┐
│                        Docker 容器集群                          │
│  ┌──────────────┐  ┌──────────────┐  ┌──────────────┐          │
│  │    Django    │  │   thworker   │  │   Node.js    │          │
│  │   (Web App)  │  │   (Worker)   │  │ (WebSocket)  │          │
│  │   gunicorn   │  │              │  │   Socket.IO  │          │
│  └──────────────┘  └──────────────┘  └──────────────┘          │
└─────────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     MySQL       │    │     Redis       │    │   阿里云OSS     │
│   (主数据库)     │    │ (缓存+消息队列)  │    │   (文件存储)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 技术栈详解

### 后端技术栈

#### Django 框架 (v1.11)
- **核心框架**: Django 1.11.29 (LTS版本)
- **API框架**: Django REST Framework 3.9.1
- **数据库ORM**: Django ORM
- **认证系统**: Django Authentication + Steam OAuth
- **国际化**: django-modeltranslation (支持中英文)

#### 数据库与缓存
- **主数据库**: MySQL 
- **缓存系统**: Redis (django-redis)
- **会话存储**: Redis
- **异步任务**: Redis + thworker (自定义线程池框架)

#### 第三方服务集成
- **文件存储**: 阿里云OSS (oss2)
- **支付系统**: 
  - 支付宝 (python-alipay-sdk)
  - 微信支付
  - 多个第三方支付网关
- **短信服务**: 阿里云短信 (aliyun-python-sdk-dysmsapi)
- **Steam API**: Steam Web API集成

### 前端技术栈
- **架构**: 待分析 (基于ui目录结构)
- **实时通信**: Socket.IO客户端
- **UI组件**: 需要进一步确认

### 中间件与工具
- **Web服务器**: Gunicorn
- **反向代理**: Nginx
- **容器化**: Docker + Docker Compose
- **任务调度**: thworker + schedule (Python定时任务库)
- **日志管理**: Python logging + 文件日志

## 应用模块架构

### 核心业务模块

```
steambase/                 # 项目核心配置
├── settings.py            # Django配置
├── urls.py               # 主路由
├── middleware.py         # 自定义中间件
└── redis_con.py          # Redis连接管理

authentication/           # 用户认证模块
├── models.py            # 用户模型
├── views.py             # 认证视图
├── business.py          # 业务逻辑
├── serializers.py       # 序列化器
└── service/             # 邮件、短信服务

box/                     # 开箱系统
├── models.py            # 箱子、物品模型
├── views.py             # 开箱接口
├── business.py          # 开箱逻辑
├── business_room.py     # 房间对战逻辑
└── tasks.py             # 异步任务

b2ctrade/               # 交易系统
├── models.py           # 交易模型
├── views.py            # 交易接口
├── business.py         # 交易逻辑
└── service/            # 第三方交易服务

charge/                 # 充值系统
├── models.py           # 充值记录模型
├── views.py            # 支付接口
├── business.py         # 支付逻辑
└── service/            # 支付服务集成

package/                # 背包系统
├── models.py           # 物品模型
├── views.py            # 背包接口
├── business.py         # 背包逻辑
└── service/            # 物品价格服务

[其他模块...]
```

### 游戏模块
- **crash/**: 崩盘游戏
- **roll/**: Roll点游戏
- **grab/**: 抢夺游戏
- **lottery/**: 彩票系统
- **luckybox/**: 幸运盒子
- **custombox/**: 自定义箱子
- **blindbox/**: 盲盒系统
- **tradeup/**: 以旧换新

### 支撑模块
- **chat/**: 聊天系统
- **websocket/**: WebSocket通信
- **promotion/**: 推广系统
- **agent/**: 代理商系统
- **sitecfg/**: 网站配置
- **monitor/**: 监控系统

## 数据流架构

### 请求处理流程
```
客户端请求
    │
    ▼
Nginx (反向代理)
    │
    ▼
Django应用 (Gunicorn)
    │
    ├─── 认证中间件
    ├─── 权限检查
    ├─── 业务逻辑处理
    ├─── 数据库操作
    └─── 缓存操作
    │
    ▼
返回响应
```

### 实时通信流程
```
客户端WebSocket连接
    │
    ▼
Node.js Socket.IO服务器
    │
    ├─── 连接认证
    ├─── 频道订阅
    └─── Redis发布/订阅
    │
    ▼
Django业务逻辑
    │
    ▼
Redis消息发布
    │
    ▼
Socket.IO广播给客户端
```

### 异步任务流程
```
业务触发
    │
    ▼
Celery任务队列 (Redis)
    │
    ▼
Celery Worker处理
    │
    ├─── 数据库操作
    ├─── 第三方API调用
    └─── 结果存储
    │
    ▼
任务完成
```

## 部署架构

### Docker容器编排
```yaml
services:
  web:        # Django Web应用
  worker:     # Celery Worker
  websocket:  # Node.js WebSocket服务
```

### 网络架构
- **host网络模式**: 所有容器使用宿主机网络
- **端口分配**:
  - Django: 8000 (内部)
  - WebSocket: 4000
  - Redis: 6379
  - MySQL: 3306

### 数据持久化
- **日志目录**: `./logs/` 映射到容器内部
- **应用代码**: `./server/` 映射到容器内部
- **数据库**: 外部MySQL服务
- **文件存储**: 阿里云OSS

## 安全架构

### 认证与授权
- **Django Session**: 基于Redis的会话管理
- **CSRF保护**: Django内置CSRF中间件
- **Steam OAuth**: 第三方认证集成
- **权限控制**: Django权限系统

### 数据安全
- **数据库连接**: 加密连接
- **敏感信息**: 环境变量存储
- **文件上传**: 阿里云OSS安全存储
- **API安全**: RESTful API权限控制

### 网络安全
- **反向代理**: Nginx安全配置
- **HTTPS**: SSL/TLS加密
- **防火墙**: 服务器级别防护
- **限流**: 可配置的访问限制

## 扩展性设计

### 水平扩展
- **Web层**: 多个Django实例负载均衡
- **Worker层**: 多个Celery Worker实例
- **数据层**: Redis集群、MySQL读写分离

### 模块化设计
- **松耦合**: 各模块独立开发部署
- **服务化**: 支持微服务架构演进
- **插件化**: 新功能模块易于集成

### 性能优化
- **缓存策略**: 多层缓存设计
- **数据库优化**: 索引优化、查询优化
- **静态资源**: CDN加速
- **异步处理**: 耗时操作异步化
