from django.conf import settings
from django.core.cache import cache
from django.utils import timezone as dt
from rest_framework import serializers

from steambase.serializers import CustomFieldsSerializer
from steambase.utils import aware_datetime_to_timestamp, get_serializer_cache_key
from sitecfg.models import Announce<PERSON>, Support, Footer, Banner, Article


class AnnounceeSerializer(CustomFieldsSerializer):
    update_ts = serializers.SerializerMethodField()

    class Meta:
        model = Announcee
        fields = '__all__'

    def get_update_ts(self, obj):
        return aware_datetime_to_timestamp(obj.update_time)


class SupportSerializer(CustomFieldsSerializer):
    class Meta:
        model = Support
        fields = '__all__'


class SupportCacheSerializer(CustomFieldsSerializer):
    class Meta:
        model = Support
        fields = '__all__'

    def to_representation(self, instance):
        key = get_serializer_cache_key(instance, self.__class__)
        cached = cache.get(key)
        if cached:
            return cached

        result = super(SupportCacheSerializer, self).to_representation(instance)
        cache.set(key, result, settings.HOUR_REDIS_TIMEOUT * 2)
        return result


class FooterSerializer(CustomFieldsSerializer):
    class Meta:
        model = Footer
        fields = '__all__'


class BannerSerializer(CustomFieldsSerializer):
    image = serializers.SerializerMethodField()
    
    class Meta:
        model = Banner
        fields = '__all__'

    def get_image(self, obj):
        # 直接返回存储的URL
        if obj.image:
            if obj.image.startswith('http'):
                return obj.image
            else:
                # OSS拼接 settings.ALIYUN_OSS_ENDPOINT
                return f'https://{settings.ALIYUN_OSS_BUCKET_NAME}.{settings.ALIYUN_OSS_ENDPOINT}/{obj.image}'
        return None

   
    
   

    
class ArticleSerializer(CustomFieldsSerializer):
    class Meta:
        model = Article
        fields = '__all__'



