import logging

from django.utils.translation import gettext_lazy as _
from rest_framework.response import Response

from rest_framework.permissions import AllowAny
from rest_framework.views import APIView

from steambase.enums import RespCode
from steambase.utils import reformat_resp, current_user
from sitecfg.interfaces import get_maintenance, get_maintenance_withdraw
from b2ctrade.business import get_b2cmarket, user_withdraw_items, check_withdraw, check_trade, user_cancel_items, \
    zbt_trade_reocrd, user_batch_withdraw_zbt_item, zbt_trade_reocrd_newapi
from b2ctrade.business import update_zbt_items_price, user_withdraw_zbt_item, user_cancel_zbt_item, receive_zbt_state, user_withdraw_zbt, get_user_steam_info


_logger = logging.getLogger(__name__)


class GetMarketInventoryView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:

            user = current_user(request)
            fields = ('uid', 'price', 'item_info')
            name = request.query_params.get('name', '')
            appid = request.query_params.get('appid', '730')
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            order = request.query_params.get('order', '-price')
            code, resp = get_b2cmarket(user, appid, fields, name, page, page_size, order)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class UserWithdrawZBTItemView(APIView):

    def post(self, request):
        try:
            user = current_user(request)

            if get_maintenance_withdraw():
                return reformat_resp(RespCode.Maintenance.value, {},
                                     _('Withdraw is under maintenance, please wait for a while.'))
            if user.is_staff or user.extra.ban_withdraw or user.is_agent:
                return reformat_resp(RespCode.NoPermission.value, {}, _('No permission'))
            item_id = request.data.get('itemId', '')
            code, resp = user_withdraw_zbt_item(user, item_id)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class UserBatchWithdrawItemsView(APIView):

    def post(self, request):
        try:
            user = current_user(request)
            if get_maintenance():
                if user and user.is_superuser:
                    pass
                else:
                    return reformat_resp(RespCode.Maintenance.value, {},
                                         _('系统维护中，请稍后再试，具体恢复时间请关注网站公告。'))
            if get_maintenance_withdraw():
                return reformat_resp(RespCode.Maintenance.value, {},
                                     _('Withdraw is under maintenance, please wait for a while.'))
            item_id = request.data.get('items', [])
            code, resp = user_batch_withdraw_zbt_item(user, item_id)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')



class UserCancelZBTItemView(APIView):

    def post(self, request):
        try:
            user = current_user(request)

            if get_maintenance_withdraw():
                return reformat_resp(RespCode.Maintenance.value, {},
                                     _('Withdraw is under maintenance, please wait for a while.'))
            uid = request.data.get('uid', '')
            code, resp = user_cancel_zbt_item(user, uid)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class UserWithdrawItemsView(APIView):

    def post(self, request):
        try:
            user = current_user(request)

            if get_maintenance_withdraw():
                return reformat_resp(RespCode.Maintenance.value, {},
                                     _('Withdraw is under maintenance, please wait for a while.'))
            uid = request.data.get('uid', '')
            code, resp = user_withdraw_items(user, uid)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class UserCancelItemsView(APIView):

    def post(self, request):
        try:
            user = current_user(request)

            if get_maintenance_withdraw():
                return reformat_resp(RespCode.Maintenance.value, {},
                                     _('Withdraw is under maintenance, please wait for a while.'))
            uid = request.data.get('uid', '')
            code, resp = user_cancel_items(user, uid)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class CheckWithdraw(APIView):

    def get(self, request):
        try:
            user = current_user(request)
            query = {
                'appid': request.query_params.get('appid', None)
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            fields = ('uid', 'item_info', 'create_time', 'amount', 'state', 'zbt_state', 'trade_no')
            code, resp = check_withdraw(user, query, fields)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class CheckTrade(APIView):

    def post(self, request):
        try:
            user = current_user(request)
            uid = request.data.get('uid', '')
            fields = ('item_info', 'create_time', 'amount', 'state', 'zbt_state', 'tradeoffers_url', 'cancel_able', 'trade_no')
            code, resp = check_trade(user, uid, fields)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')
    

class UpdateB2cItemsView(APIView):

    def get(self, request):
        code, resp = update_zbt_items_price()
        if code == RespCode.Succeed.value:
            return reformat_resp(code, resp, 'Succeed')
        else:
            return reformat_resp(code, {}, resp)


class ReceiveZBTradeState(APIView):

    permission_classes = [AllowAny]

    def post(self, request):
        try:
            # print('receive============', request.data)
            code, resp = receive_zbt_state(request.data)
            if code == RespCode.Succeed.value:
                return Response('success')
                # return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')
    

class GetTradeRecordView(APIView):

    def get(self, request):
        try:
            user = current_user(request)
            query = {
                # 'appid': request.query_params.get('appid', None)
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            fields = ('uid', 'item_info', 'create_time', 'update_time', 'amount', 'state')
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            code, resp = zbt_trade_reocrd(user, query, fields, page, page_size)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')
    
class GetZBTTradeRecordView(APIView):


    def get(self, request):
        try:
            user = current_user(request)
            query = {
                'appid': request.query_params.get('appid', None)
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            fields = ('uid', 'item_info', 'create_time', 'update_time', 'amount', 'state', 'zbt_state', 'trade_no', 'trade_url', 'accept_time', 'zbt_orderid', 'zbt_error', 'out_trade_no')
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            code, resp = zbt_trade_reocrd_newapi(user, query, fields, page, page_size)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')
    
class UserWithdrawZBTView(APIView):

    def post(self, request):
        try:
            user = current_user(request)
            if get_maintenance_withdraw():
                return reformat_resp(RespCode.Maintenance.value, {},
                                     _('取回接口维护，稍后再试，恢复时间请参考网站公告。'))
            if user.is_staff or user.extra.ban_withdraw:
                return reformat_resp(RespCode.NoPermission.value, {}, _('取回权限被限制，请联系在线客服。'))
            uid = request.data.get('uid', '')
            code, resp = user_withdraw_zbt(user, uid)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')
    
class GetSteamInfoView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        """
        处理 GET 请求，获取用户 Steam 信息。
        """
        try:
            #登录判断
            user = current_user(request)
            if not user:
                return reformat_resp(RespCode.BadRequest.value, {}, '请先登录')

            trade_url = request.query_params.get('trade_url', '')
            if not trade_url:
                return reformat_resp(RespCode.BadRequest.value, {}, 'Trade URL is required')

            steam_info = get_user_steam_info(trade_url)
            if not steam_info:
                return reformat_resp(RespCode.BadRequest.value, {}, 'Failed to fetch Steam info')

            return reformat_resp(RespCode.Succeed.value, steam_info, 'Succeed')
        except Exception as e:
            _logger.exception("Error while fetching Steam info: %s", e)
            return reformat_resp(RespCode.Exception.value, {}, 'Exception')
       
            
