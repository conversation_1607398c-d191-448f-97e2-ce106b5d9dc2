#!/usr/bin/env python3
"""
CKEditor 安全警告修复脚本

这个脚本将帮助解决 CKEditor 4.22.1 的安全警告问题，
通过升级到 CKEditor 4 LTS 版本。

使用方法:
    python scripts/fix_ckeditor_security_warning.py
"""

import os
import sys
import shutil
import requests
import zipfile
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CKEditorSecurityFixer:
    """CKEditor 安全问题修复工具"""
    
    def __init__(self):
        self.static_dir = Path('static/ckeditor')
        self.ckeditor_dir = self.static_dir / 'ckeditor'
        
    def backup_current_ckeditor(self):
        """备份当前的 CKEditor"""
        if self.ckeditor_dir.exists():
            backup_dir = Path(f'../backup/ckeditor_backup_{int(time.time())}')
            backup_dir.mkdir(parents=True, exist_ok=True)
            shutil.copytree(self.ckeditor_dir, backup_dir / 'ckeditor')
            logger.info(f"已备份当前 CKEditor 到: {backup_dir}")
            return backup_dir
        return None
    
    def download_ckeditor_lts(self):
        """下载 CKEditor 4 LTS 版本"""
        # CKEditor 4 LTS 下载链接
        lts_url = "https://download.ckeditor.com/CKEditor/CKEditor-4.24.0-LTS/ckeditor_4.24.0-lts_full.zip"
        
        logger.info("开始下载 CKEditor 4 LTS...")
        
        try:
            response = requests.get(lts_url, stream=True)
            response.raise_for_status()
            
            zip_path = Path('/tmp/ckeditor_lts.zip')
            with open(zip_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            logger.info("CKEditor 4 LTS 下载完成")
            return zip_path
            
        except Exception as e:
            logger.error(f"下载失败: {e}")
            return None
    
    def extract_and_install_lts(self, zip_path):
        """解压并安装 CKEditor 4 LTS"""
        try:
            # 创建临时目录
            temp_dir = Path('/tmp/ckeditor_lts_extract')
            if temp_dir.exists():
                shutil.rmtree(temp_dir)
            temp_dir.mkdir()
            
            # 解压文件
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(temp_dir)
            
            # 查找 ckeditor 目录
            ckeditor_source = None
            for item in temp_dir.iterdir():
                if item.is_dir() and 'ckeditor' in item.name.lower():
                    ckeditor_source = item
                    break
            
            if not ckeditor_source:
                logger.error("在解压文件中找不到 ckeditor 目录")
                return False
            
            # 备份现有的 CKEditor
            if self.ckeditor_dir.exists():
                backup_dir = self.ckeditor_dir.parent / 'ckeditor_old'
                if backup_dir.exists():
                    shutil.rmtree(backup_dir)
                shutil.move(self.ckeditor_dir, backup_dir)
                logger.info("已备份旧版本 CKEditor")
            
            # 安装新版本
            self.ckeditor_dir.parent.mkdir(parents=True, exist_ok=True)
            shutil.copytree(ckeditor_source, self.ckeditor_dir)
            
            logger.info("CKEditor 4 LTS 安装完成")
            
            # 清理临时文件
            shutil.rmtree(temp_dir)
            zip_path.unlink()
            
            return True
            
        except Exception as e:
            logger.error(f"安装失败: {e}")
            return False
    
    def update_settings(self):
        """更新 Django 设置"""
        settings_file = Path('steambase/base_settings.py')
        
        try:
            with open(settings_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 添加 CKEditor LTS 配置注释
            lts_comment = """
# CKEditor 4 LTS 版本已安装 - 解决安全警告
# 版本: 4.24.0 LTS
# 更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
            
            if "CKEditor 4 LTS 版本已安装" not in content:
                # 在 CKEDITOR_CONFIGS 前添加注释
                content = content.replace(
                    "# CKEditor 4 LTS 配置 (解决安全警告)",
                    lts_comment + "# CKEditor 4 LTS 配置 (解决安全警告)"
                )
                
                with open(settings_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                logger.info("已更新 Django 设置文件")
            
        except Exception as e:
            logger.error(f"更新设置文件失败: {e}")
    
    def verify_installation(self):
        """验证安装"""
        # 检查关键文件是否存在
        key_files = [
            self.ckeditor_dir / 'ckeditor.js',
            self.ckeditor_dir / 'config.js',
            self.ckeditor_dir / 'contents.css',
        ]
        
        for file_path in key_files:
            if not file_path.exists():
                logger.error(f"关键文件缺失: {file_path}")
                return False
        
        # 检查版本信息
        try:
            ckeditor_js = self.ckeditor_dir / 'ckeditor.js'
            with open(ckeditor_js, 'r', encoding='utf-8') as f:
                content = f.read()
                if '4.24.0' in content or 'LTS' in content:
                    logger.info("✅ CKEditor 4 LTS 版本验证成功")
                    return True
                else:
                    logger.warning("⚠️ 无法确认 CKEditor 版本")
                    return True  # 继续，可能版本检查方式不同
        except Exception as e:
            logger.error(f"版本验证失败: {e}")
            return False
    
    def run_fix(self):
        """执行完整的修复流程"""
        logger.info("开始修复 CKEditor 安全警告...")
        
        # 1. 备份当前版本
        backup_dir = self.backup_current_ckeditor()
        
        # 2. 下载 CKEditor 4 LTS
        zip_path = self.download_ckeditor_lts()
        if not zip_path:
            logger.error("下载失败，修复中止")
            return False
        
        # 3. 安装新版本
        if not self.extract_and_install_lts(zip_path):
            logger.error("安装失败，修复中止")
            return False
        
        # 4. 更新设置
        self.update_settings()
        
        # 5. 验证安装
        if not self.verify_installation():
            logger.error("验证失败，请检查安装")
            return False
        
        logger.info("🎉 CKEditor 安全警告修复完成！")
        logger.info("请执行以下步骤完成修复:")
        logger.info("1. 重启 Django 服务器")
        logger.info("2. 运行 python manage.py collectstatic")
        logger.info("3. 检查管理后台的编辑器功能")
        
        return True

def main():
    import time
    import datetime
    
    try:
        fixer = CKEditorSecurityFixer()
        success = fixer.run_fix()
        
        if success:
            logger.info("修复成功完成！")
        else:
            logger.error("修复失败，请检查日志")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"修复过程中出现错误: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
