# CKEditor 迁移和修复脚本

本目录包含与CKEditor相关的脚本，主要用于处理CKEditor的安全更新和版本迁移。

## 脚本说明

### 1. backup_before_ckeditor_migration.py
- **功能**: 在CKEditor迁移前创建备份
- **用途**: 确保迁移过程中数据安全

### 2. fix_ckeditor_security_warning.py
- **功能**: 修复CKEditor安全警告
- **用途**: 处理已知的安全漏洞

### 3. migrate_to_ckeditor5.py
- **功能**: 从CKEditor 4迁移到CKEditor 5
- **用途**: 版本升级迁移

### 4. simple_ckeditor_fix.py
- **功能**: 简单的CKEditor修复
- **用途**: 快速修复常见问题

## 使用注意事项

1. 运行任何迁移脚本前，请先执行备份脚本
2. 建议在测试环境中先验证脚本效果
3. 生产环境使用前请仔细阅读脚本内容

## 相关文档

- [CKEditor安全修复文档](../../docs/CKEDITOR_SECURITY_FIX.md)
