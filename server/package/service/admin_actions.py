import json
import os
from datetime import datetime

from django.contrib import admin, messages
from django.http import HttpResponse
from django.utils.translation import gettext_lazy as _
from openpyxl import Workbook

from custombox.models import CustomBoxItemInfo
# from luckybox.models import LuckyBoxItem
from luckybox.models import LuckyBoxGroup, LuckyBoxItem
from market.models import MarketItem
from package.service.item import get_item_price, get_item_property
from steambase.enums import PackageState, RespCode
from package.business import get_user_inventory
from package.models import TradeBot, TradeBotInventory, PackageItem, TradeRecord, TradeItem, ItemInfo, ItemPrice


def startup_trade_bot(modeladmin, request, queryset):
    bots = queryset.filter(enable=True)
    acceptFroms = ['*****************']
    botConfigs = []
    for bot in bots:
        bot_info = {
            'steamid': bot.steamid,
            'accountName': bot.account,
            'password': bot.password,
            'twoFactorCode': bot.shared_secret,
            'confirmChecker': bot.identity_secret,
            'allowOffer': True,
            'allowGiftOnly': False,
            'acceptOfferFrom': acceptFroms
        }
        botConfigs.append(bot_info)
    with open('/www/steambots/bot_config.js', 'w') as f:
        f.write('var botConfigs = ')
        f.write(json.dumps(botConfigs))
        f.write(';module.exports = botConfigs;')
    result = os.system('sudo pm2 restart steambot')
    if result == 0:
        messages.success(request, _('Startup complete'))
    else:
        messages.warning(request, _('Startup fail'))


startup_trade_bot.short_description = _("Startup trade bot")


def sync_trade_bot_inventory(modeladmin, request, queryset):
    bots = queryset.filter(enable=True, enable_withdraw=True)
    for bot in bots:
        steamid = bot.steamid
        TradeBotInventory.objects.filter(bot_steamid=steamid).delete()
        appid = '570'
        code, inventory = get_user_inventory(steamid, appid, complete=True)
        if code != RespCode.Succeed.value:
            messages.error(request, _('Get inventory from Steam fail'))
            return
        for item in inventory.get('items', []):
            item_info = ItemInfo.objects.filter(market_hash_name=item.get('market_hash_name', None)).first()
            if item_info:
                assetid = item.get('assetid', None)
                instanceid = item.get('instanceid', None)
                matching = True if PackageItem.objects.filter(assetid=assetid,
                                                              state__in=[
                                                                  PackageState.Initialed.value,
                                                                  PackageState.Available.value,
                                                                  PackageState.Blocked.value,
                                                                  PackageState.Gaming.value,
                                                                  PackageState.Withdrawing.value
                                                              ]) else False
                TradeBotInventory.objects.create(item_info=item_info, assetid=assetid, instanceid=instanceid,
                                                 bot_steamid=steamid, matching=matching)
    messages.success(request, _('Sync complete'))


sync_trade_bot_inventory.short_description = _('Sync trade bot inventory')


def review_trade(modeladmin, request, queryset):
    records = queryset.all()
    for r in records:
        r.review = True
        r.save()
    messages.success(request, _('Review complete'))


review_trade.short_description = _('Review selected trade')


def invalidate_package(modeladmin, request, queryset):
    queryset.filter(state=PackageState.Available.value).update(state=PackageState.Invalid.value, user=None)
    messages.success(request, _('Invalidate complete'))


invalidate_package.short_description = _('Invalidate selected item')


def exchanged_package(modeladmin, request, queryset):
    queryset.update(state=PackageState.Exchanged.value, user=None)
    messages.success(request, _('Exchanged complete'))


exchanged_package.short_description = _('Exchanged selected item')


def available_package(modeladmin, request, queryset):
    queryset.filter(state=PackageState.Withdrawing.value).update(state=PackageState.Available.value)
    messages.success(request, _('Available complete'))


available_package.short_description = _('Available selected item')


def import_to_luckybox(modeladmin, request, queryset):
    for qs in queryset:
        name = "".join(qs.market_name_cn.split('(')[:-1]) if qs.market_name_cn != 'NULL' else None
        if not name:
            name = "".join(qs.market_name_cn) if qs.market_name_cn != 'NULL' else None
        hash_name = "".join(qs.market_hash_name.split('(')[:-1]) if qs.market_name_cn != 'NULL' else None
        if not hash_name:
            hash_name = "".join(qs.market_hash_name) if qs.market_name_cn != 'NULL' else None
        # print(hash_name)
        # 添加分组信息
        group = LuckyBoxGroup.objects.filter(market_name=name).first()
        if not group:
            group = LuckyBoxGroup.objects.create(
                market_name=name,
                market_hash_name=hash_name,
                type=qs.type,
                weapon=qs.weapon,
                icon_url=qs.icon_url,
                rarity_color=qs.rarity_color
            )
        price = get_item_price(qs.market_hash_name)
        group.set_price(price)
        # 添加饰品信息
        item = LuckyBoxItem.objects.filter(item_info=qs).first()
        if not item:
            LuckyBoxItem.objects.create(
                item_info=qs,
                group=group,
                price=price
            )

    messages.success(request, "导入成功")


import_to_luckybox.short_description = _('import item to luckybox')


def import_to_custombox(modeladmin, request, queryset):
    for qs in queryset:
        custom_box_item = CustomBoxItemInfo.objects.filter(item_info=qs)
        name = qs.market_name_cn if qs.market_name_cn != 'NULL' else qs.market_hash_name

        if not custom_box_item:
            CustomBoxItemInfo.objects.create(item_info=qs, market_name=name)
        else:
            CustomBoxItemInfo.objects.filter(item_info=qs).update(market_name=name)


import_to_custombox.short_description = _('import item to custombox')


def import_to_market(modeladmin, request, queryset):
    for qs in queryset:
        market_item = MarketItem.objects.filter(item_info=qs)
        market_item_property = get_item_property(qs)

        if not market_item:
            MarketItem.objects.create(item_info=qs, **market_item_property)
        else:
            MarketItem.objects.filter(item_info=qs).update(**market_item_property)


def packageitem_export_to_excel(modeladmin, request, queryset):
    name = "PackageItem-{}".format(datetime.now().strftime("%Y%m%d-%H%M%S"))  # 用于定义文件名, 格式为: app名.模型类名
    field_names = modeladmin.list_display
    field_names_trans = {
        "uid": "uid",
        "item_info": "饰品信息",
        "price": '价格',
        "amount": '金额',
        "user": '用户',
        "state": '状态',
    }
    response = HttpResponse(content_type='application/msexcel')  # 定义响应内容类型
    response['Content-Disposition'] = f'attachment; filename={name}.xlsx'  # 定义响应数据格式
    wb = Workbook()  # 新建Workbook
    ws = wb.active  # 使用当前活动的Sheet表
    ws.append([field_names_trans.get(field, "") for field in field_names])  # 将模型字段名作为标题写入第一行

    # 遍历选中queryset
    for obj in queryset:
        set_attr = {
            'price': get_item_price(obj.item_info.market_hash_name),
        }
        # # 设置自定义字段
        [setattr(obj, key, value) for key, value in set_attr.items()]
        data = []
        for field in field_names:
            if field == 'state':
                state_map = {
                    0: "初始化",
                    1: "可用",
                    2: "已锁定",
                    3: "游戏中",
                    4: "取回中",
                    5: "已取回",
                    6: "已售出",
                    7: "无效",
                }
                data.append(state_map[int(getattr(obj, field))])
            else:
                data.append(str(getattr(obj, field)))
        row = ws.append(data)
    wb.save(response)  # 将数据存入响应内容
    return response

packageitem_export_to_excel.short_description = _("背包饰品导出EXCEL")