# Generated by Django 3.2.25 on 2025-07-19 04:09

import ckeditor_uploader.fields
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('articles', '0002_auto_20250719_1146'),
    ]

    operations = [
        migrations.AlterField(
            model_name='content',
            name='author',
            field=models.ForeignKey(blank=True, limit_choices_to={'is_active': True, 'is_agent': True}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='authored_contents', to=settings.AUTH_USER_MODEL, verbose_name='Author'),
        ),
        migrations.AlterField(
            model_name='content',
            name='content',
            field=ckeditor_uploader.fields.RichTextUploadingField(blank=True, null=True, verbose_name='Content'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='content',
            name='editor',
            field=models.ForeignKey(blank=True, limit_choices_to={'is_active': True, 'is_agent': True}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='edited_contents', to=settings.AUTH_USER_MODEL, verbose_name='Last Editor'),
        ),
    ]
