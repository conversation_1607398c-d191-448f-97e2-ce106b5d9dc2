<template>
  <div class="battle-header">
    <!-- 🎯 对战基本信息区域 -->
    <div class="battle-info">
      <!-- 对战ID和状态 -->
      <div class="battle-id-section">
        <div class="battle-id">
          <span class="id-label">{{ t('battle.detail.battle_id') }}:</span>
          <span class="id-value">{{ battleData?.short_id || 'Loading...' }}</span>
          <button @click="handleCopyId" class="copy-btn">
            <Icon name="heroicons:clipboard-document" class="w-4 h-4" />
          </button>
        </div>
        <div class="battle-status">
          <span 
            class="status-badge"
            :class="getStatusClass(battleData?.state)"
          >
            {{ getStatusText(battleData?.state) }}
          </span>
        </div>
      </div>

      

      <!-- 时间信息 -->
      <div class="time-info">
        <div class="time-item">
          <i class="i-heroicons-clock w-4 h-4" />
          <span class="time-label">{{ t('battle.detail.time') }}</span>
          <span class="time-value">{{ formatTime(battleData.create_time) }}</span>
        </div>
      </div>
    </div>

    

   
  </div>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'

// 🎯 国际化设置
const { t } = useI18n()

// 🎯 Props 定义
interface BattleHeaderProps {
  battleData: {
    short_id: string
    state: number
    user: {
      uid?: string
      nickname: string
      avatar: string
    }
    create_time: string
    joiner_count: number
    max_joiner: number
    bets: Array<{
      uid: string
      user: {
        uid: string
        profile: {
          nickname: string
          avatar: string
        }
      }
    }>
  }
  isUserJoined: boolean
  isUserCreator: boolean
  isLoading?: boolean
}

const props = withDefaults(defineProps<BattleHeaderProps>(), {
  isLoading: false
})

// 🎯 Events 定义
interface BattleHeaderEmits {
  join: () => void
  leave: () => void
  dismiss: () => void
  copyId: () => void
  share: () => void
}

const emit = defineEmits<BattleHeaderEmits>()

// 🎯 计算属性
const canJoin = () => {
  if (!props.battleData) return false
  return props.battleData.state === 2 && props.battleData.joiner_count < props.battleData.max_joiner
}

const isBattleWaiting = computed(() => {
  if (!props.battleData) return false
  return props.battleData.state === 2 && props.battleData.joiner_count < props.battleData.max_joiner
})

const isBattleStarted = computed(() => {
  if (!props.battleData) return false
  return props.battleData.state === 2
})

// 🎯 按钮显示逻辑 - 确保只有一个按钮显示
const shouldShowJoinButton = computed(() => {
  return !props.isUserJoined && canJoin()
})

const shouldShowDismissButton = computed(() => {
  return props.isUserCreator && isBattleWaiting.value
})

const shouldShowLeaveButton = computed(() => {
  return props.isUserJoined && !props.isUserCreator && isBattleWaiting.value
})

// 🎯 调试信息
const debugInfo = computed(() => {
  return {
    state: props.battleData?.state,
    isUserJoined: props.isUserJoined,
    isUserCreator: props.isUserCreator,
    isBattleWaiting: isBattleWaiting.value,
    canJoin: canJoin(),
    joinerCount: props.battleData?.joiner_count,
    maxJoiner: props.battleData?.max_joiner,
    betsLength: props.battleData?.bets.length,
    bets: props.battleData?.bets.map(bet => ({
      uid: bet.user?.uid,
      nickname: bet.user?.profile?.nickname
    })),
    creatorUid: props.battleData?.user?.uid,
    creatorNickname: props.battleData?.user?.nickname || '未知',
    // 添加新的按钮显示逻辑
    shouldShowJoinButton: shouldShowJoinButton.value,
    shouldShowDismissButton: shouldShowDismissButton.value,
    shouldShowLeaveButton: shouldShowLeaveButton.value
  }
})

// 🎯 开发环境调试
if (process.env.NODE_ENV === 'development') {
  watch(debugInfo, (info) => {
    // console.log('[🎰BATTLE-HEADER] 按钮显示调试信息:', info)
    
    // 详细分析按钮显示逻辑
    const showJoinButton = shouldShowJoinButton.value
    const showDismissButton = shouldShowDismissButton.value
    const showLeaveButton = shouldShowLeaveButton.value
    
    // console.log('[🎰BATTLE-HEADER] 按钮显示分析:', {
    //   showJoinButton,
    //   showDismissButton,
    //   showLeaveButton,
    //   reason: {
    //     join: `!${info.isUserJoined} && ${info.canJoin} = ${showJoinButton}`,
    //     dismiss: `${info.isUserCreator} && ${info.isBattleWaiting} = ${showDismissButton}`,
    //     leave: `${info.isUserJoined} && !${info.isUserCreator} && ${info.isBattleWaiting} = ${showLeaveButton}`
    //   }
    // })
  }, { immediate: true })
}

const getEmptySlots = () => {
  if (!props.battleData) return 0
  return Math.max(0, props.battleData.max_joiner - (props.battleData.bets?.length || 0))
}

// 🎯 状态处理
const getStatusClass = (state: number | undefined) => {
  if (!state) return 'status-waiting'
  
  switch (state) {
    case 1:
    case 2:
    case 3:
    case 4:
      return 'status-waiting'
    case 5:
      return 'status-in-progress'
    case 11:
      return 'status-finished'
    default:
      return 'status-waiting'
  }
}

const getStatusText = (state: number | undefined) => {
  if (!state) return t('battle.detail.status.waiting')
  
  switch (state) {
    case 1:
    case 2:
    case 3:
    case 4:
      return t('battle.detail.status.waiting')
    case 5:
      return t('battle.detail.status.in_progress')
    case 11:
      return t('battle.detail.status.finished')
    default:
      return t('battle.detail.status.waiting')
  }
}

// 🎯 工具函数
const formatTime = (timeString: string) => {
  try {
    const date = new Date(timeString)
    return date.toLocaleString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch {
    return timeString
  }
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/demo/avatar1.png'
}

const handleCopyId = () => {
  if (props.battleData?.short_id) {
    navigator.clipboard.writeText(props.battleData.short_id)
    // 可以添加复制成功的提示
  }
}
</script>

<style lang="scss" scoped>
// 🎯 对战头部容器
.battle-header {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: var(--radius-lg, 0.75rem);
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  gap: 1.5rem;

  @media (min-width: 768px) {
    flex-direction: row;
    align-items: flex-start;
    justify-content: space-between;
  }
}

// 🎯 对战基本信息区域
.battle-info {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  flex: 1;

  @media (min-width: 768px) {
    flex-direction: row;
    align-items: center;
    gap: 2rem;
  }
}

// 🎯 对战ID和状态区域
.battle-id-section {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;

  @media (min-width: 768px) {
    flex-direction: row;
    align-items: center;
    gap: 1rem;
  }
}

.battle-id {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(0, 0, 0, 0.2);
  padding: 0.5rem 0.75rem;
  border-radius: var(--radius-md, 0.375rem);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5px);
}

.id-label {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

.id-value {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  letter-spacing: 0.05em;
}

.copy-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: var(--radius-sm, 0.25rem);
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.9);
    transform: scale(1.05);
  }
}

.battle-status {
  display: flex;
  align-items: center;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-full, 9999px);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;

  &.status-waiting {
    background: rgba(59, 130, 246, 0.15);
    color: #60a5fa;
    border: 1px solid rgba(59, 130, 246, 0.3);
  }

  &.status-preparing {
    background: rgba(245, 158, 11, 0.15);
    color: #fbbf24;
    border: 1px solid rgba(245, 158, 11, 0.3);
  }

  &.status-battle {
    background: rgba(34, 197, 94, 0.15);
    color: #4ade80;
    border: 1px solid rgba(34, 197, 94, 0.3);
  }

  &.status-calculating {
    background: rgba(168, 85, 247, 0.15);
    color: #a78bfa;
    border: 1px solid rgba(168, 85, 247, 0.3);
  }

  &.status-finished {
    background: rgba(107, 114, 128, 0.15);
    color: #9ca3af;
    border: 1px solid rgba(107, 114, 128, 0.3);
  }

  &.status-unknown {
    background: rgba(239, 68, 68, 0.15);
    color: #f87171;
    border: 1px solid rgba(239, 68, 68, 0.3);
  }
}

.creator-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.creator-avatar {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-img {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.creator-badge {
  position: absolute;
  bottom: -2px;
  right: -2px;
  background: #eab308;
  color: #ffffff;
  border-radius: 50%;
  width: 1.25rem;
  height: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid #ffffff;
}

.creator-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.creator-name {
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.875rem;
}

.creator-label {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
}

.time-info {
  display: flex;
  align-items: center;
}

.time-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.875rem;
}

.time-label {
  font-weight: 500;
}

.time-value {
  color: rgba(255, 255, 255, 0.9);
}

// 🎯 参与者信息区域
.participants-info {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  min-width: 200px;
}

.participants-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.participants-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
}

.participants-count {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
  background: rgba(0, 0, 0, 0.2);
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm, 0.25rem);
}

.participants-list {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.participant-item {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;

  &.creator .participant-avatar {
    border-color: #eab308;
  }

  &.empty .empty-avatar {
    background: rgba(255, 255, 255, 0.1);
    border: 2px dashed rgba(255, 255, 255, 0.3);
    color: #9ca3af;
  }
}

.participant-avatar {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.2);
  transition: all 0.2s ease;

  &:hover {
    transform: scale(1.1);
    border-color: rgba(255, 255, 255, 0.4);
  }
}

.participant-badge {
  position: absolute;
  bottom: -2px;
  right: -2px;
  background: #eab308;
  color: #ffffff;
  border-radius: 50%;
  width: 1rem;
  height: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #ffffff;
}

.empty-avatar {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 🎯 操作按钮区域
.battle-actions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  align-items: stretch;

  @media (min-width: 768px) {
    flex-direction: row;
    align-items: center;
    gap: 1rem;
  }
}

.primary-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.secondary-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: var(--radius-md, 0.375rem);
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
  }

  &.primary {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: #ffffff;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);

    &:hover:not(:disabled) {
      background: linear-gradient(135deg, #2563eb, #1d4ed8);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
    }
  }

  &.secondary {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.2);

    &:hover:not(:disabled) {
      background: rgba(255, 255, 255, 0.15);
      transform: translateY(-1px);
    }
  }

  &.danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: #ffffff;
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);

    &:hover:not(:disabled) {
      background: linear-gradient(135deg, #dc2626, #b91c1c);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
    }
  }

  &.icon {
    width: 2.5rem;
    height: 2.5rem;
    padding: 0;
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.2);

    &:hover {
      background: rgba(255, 255, 255, 0.15);
      color: rgba(255, 255, 255, 0.9);
      transform: scale(1.05);
    }
  }
}
</style>