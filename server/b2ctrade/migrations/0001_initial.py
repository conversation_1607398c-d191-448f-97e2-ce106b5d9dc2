# -*- coding: utf-8 -*-
# Generated by Django 1.11.13 on 2021-04-14 02:09
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import steambase.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODELL),
        ('package', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='B2CItemlist',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('enable', models.BooleanField(default=True, verbose_name='enable')),
                ('count', models.IntegerField(default=0, verbose_name='count')),
                ('item_info', models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='b2c_item', to='package.ItemInfo', verbose_name='item info')),
            ],
            options={
                'verbose_name': 'B2C Item list',
                'verbose_name_plural': 'B2C Item list',
                'ordering': ('item_info__market_hash_name',),
            },
        ),
        migrations.CreateModel(
            name='B2CMarketItem',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('price', models.FloatField(default=0, verbose_name='price')),
                ('zbt_price', models.FloatField(default=0.0, verbose_name='zbt price')),
                ('count', models.IntegerField(default=0, verbose_name='count')),
                ('unlimited', models.BooleanField(default=True, verbose_name='unlimited')),
                ('enable', models.BooleanField(default=True, verbose_name='enable')),
                ('create_time', models.DateTimeField(auto_now_add=True, null=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, null=True, verbose_name='update time')),
                ('item_info', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='b2c_market_item', to='package.ItemInfo', verbose_name='item info')),
            ],
            options={
                'verbose_name': 'B2C Market Item',
                'verbose_name_plural': 'B2C Market Item',
            },
        ),
        migrations.CreateModel(
            name='B2COfficialAccount',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('remark', models.CharField(max_length=128, verbose_name='remark')),
                ('enable', models.BooleanField(default=True, verbose_name='enable')),
                ('customid', models.CharField(default=None, max_length=128, verbose_name='customid')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='b2c_official_account', to=settings.AUTH_USER_MODELL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'B2COfficial Account',
                'verbose_name_plural': 'B2COfficial Account',
            },
        ),
        migrations.CreateModel(
            name='B2CTradeRecord',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('seller_tradeurl', models.CharField(blank=True, default=None, max_length=256, null=True, verbose_name='trade url')),
                ('buyer_tradeurl', models.CharField(blank=True, default=None, max_length=256, null=True, verbose_name='trade url')),
                ('amount', models.FloatField(default=0.0, verbose_name='amount')),
                ('state', models.SmallIntegerField(choices=[(0, 'Initialed'), (1, 'Accepted'), (2, 'Cancelled'), (11, 'WaitForBuy'), (12, 'RequestBuy'), (13, 'WaitUnlock'), (14, 'TradeReady'), (21, 'BuyerCancelled')], default=0, verbose_name='trade status')),
                ('accept_time', models.DateTimeField(blank=True, default=None, null=True, verbose_name='accept time')),
                ('appid', models.CharField(blank=True, choices=[('570', 'Dota2'), ('730', 'CSGO'), ('578080', 'PUBG'), ('433850', 'H1Z1')], default=None, max_length=64, null=True, verbose_name='appid')),
                ('expire_time', models.DateTimeField(blank=True, default=None, null=True, verbose_name='expire time')),
                ('assetid', models.CharField(blank=True, default=None, max_length=64, null=True, verbose_name='assetid')),
                ('item_type', models.SmallIntegerField(choices=[(1, 'Purchase'), (2, 'Stocks')], default=2, verbose_name='item type')),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='b2c', to=settings.AUTH_USER_MODELL, verbose_name='account')),
                ('buyer', models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='b2c_buy', to=settings.AUTH_USER_MODELL, verbose_name='buyer')),
                ('item_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='package.ItemInfo', verbose_name='item info')),
            ],
            options={
                'verbose_name': 'B2COfficialTrade Record',
                'verbose_name_plural': 'B2COfficialTrade Record',
                'ordering': ('-create_time',),
            },
        ),
        migrations.CreateModel(
            name='B2CTradeStatisticsDay',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('date', models.DateField(default=django.utils.timezone.now, verbose_name='date')),
                ('amount', models.FloatField(default=0.0, verbose_name='amount')),
                ('test_amount', models.FloatField(default=0.0, verbose_name='test amount')),
                ('appid', models.CharField(blank=True, choices=[('570', 'Dota2'), ('730', 'CSGO'), ('578080', 'PUBG'), ('433850', 'H1Z1')], default=None, max_length=64, null=True, verbose_name='appid')),
            ],
            options={
                'verbose_name': 'B2CTrade Statistics Day',
                'verbose_name_plural': 'B2CTrade Statistics Day',
                'ordering': ('-create_time',),
            },
        ),
        migrations.CreateModel(
            name='B2CTradeStatisticsMonth',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('date', models.DateField(default=django.utils.timezone.now, verbose_name='date')),
                ('amount', models.FloatField(default=0.0, verbose_name='amount')),
                ('test_amount', models.FloatField(default=0.0, verbose_name='test amount')),
                ('appid', models.CharField(choices=[('570', 'Dota2'), ('730', 'CSGO'), ('578080', 'PUBG'), ('433850', 'H1Z1')], max_length=64, verbose_name='appid')),
            ],
            options={
                'verbose_name': 'B2CTrade Statistics Month',
                'verbose_name_plural': 'B2CTrade Statistics Month',
                'ordering': ('-create_time',),
            },
        ),
        migrations.CreateModel(
            name='ZBTBlackList',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('nickname', models.CharField(default=None, max_length=128, verbose_name='seller nickname')),
            ],
            options={
                'verbose_name': 'ZBTBlackList',
                'verbose_name_plural': 'ZBTBlackList',
            },
        ),
        migrations.CreateModel(
            name='ZBTradeRecord',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('trade_url', models.CharField(max_length=256, verbose_name='trade url')),
                ('amount', models.FloatField(default=0.0, verbose_name='amount')),
                ('buy_price', models.FloatField(default=0.0, verbose_name='buy price')),
                ('state', models.SmallIntegerField(choices=[(0, 'Initialed'), (1, 'Accepted'), (2, 'Cancelled'), (15, 'Trading'), (16, 'Cancelling'), (22, 'PriceCancelled'), (23, 'OutOfStock'), (24, 'ZBTCancelled')], default=0, verbose_name='trade status')),
                ('zbt_state', models.SmallIntegerField(choices=[(0, 'WaitForPay'), (1, 'WaitForSend'), (3, 'WaitForTrade'), (4, 'Receive'), (10, 'Accepted'), (11, 'Cancelled')], default=0, verbose_name='zbt trade status')),
                ('accept_time', models.DateTimeField(blank=True, default=None, null=True, verbose_name='accept time')),
                ('appid', models.CharField(blank=True, choices=[('570', 'Dota2'), ('730', 'CSGO'), ('578080', 'PUBG'), ('433850', 'H1Z1')], default=None, max_length=64, null=True, verbose_name='appid')),
                ('assetid', models.CharField(blank=True, default=None, max_length=64, null=True, verbose_name='assetid')),
                ('zbt_orderid', models.CharField(blank=True, default=None, max_length=64, null=True, verbose_name='zbt order id')),
                ('zbt_error', models.CharField(blank=True, default=None, max_length=256, null=True, verbose_name='zbt error')),
                ('trade_no', models.CharField(blank=True, default=None, max_length=32, null=True, verbose_name='trade No.')),
                ('out_trade_no', models.CharField(blank=True, default=None, max_length=64, null=True, verbose_name='out trade no')),
                ('trade_source', models.SmallIntegerField(choices=[(0, 'Market'), (1, 'Package')], default=0, verbose_name='trade source')),
                ('item_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='package.ItemInfo', verbose_name='item info')),
                ('package', models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='zbtrade_records', to='package.PackageItem', verbose_name='package item')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='zbt_user', to=settings.AUTH_USER_MODELL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'ZBT Trade Record',
                'verbose_name_plural': 'ZBT Trade Record',
                'ordering': ('-create_time',),
            },
        ),
    ]
