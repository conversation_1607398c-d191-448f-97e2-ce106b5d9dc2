from django.contrib import admin
from django.contrib.admin import TabularInline
from django.core.urlresolvers import reverse
from django.utils.safestring import mark_safe
from django.utils.translation import gettext_lazy as _

from jet.filters import DateRangeFilter

from steambase.admin import ReadOnlyAdmin
from chat.models import Message, ChatBot, ChatBotMessage



@admin.register(Message)
class MessageAdmin(admin.ModelAdmin):
    list_display = ('id', 'sender_name', 'recipient_name', 'subject', 'body', 'sent_at', 'read_at')
    search_fields = ('sender_name', 'recipient_name','subject', 'body')
    list_per_page = 50

  




# @admin.register(ChatBot)
# class ChatBotAdmin(admin.ModelAdmin):
#     list_display = ('remark', 'user', 'chat_idle_min', 'chat_idle_max', 'enable')
#     list_editable = ('chat_idle_min', 'chat_idle_max', 'enable')
#     list_filter = ('enable',)
#     search_fields = ('remark', 'user__personaname')
#     list_per_page = 50


# @admin.register(ChatBotMessage)
# class ChatBotMessageAdmin(admin.ModelAdmin):
#     list_display = ('remark', 'enable')
#     list_editable = ('enable',)
#     list_filter = ('enable',)
#     search_fields = ('remark', 'message')
#     list_per_page = 50
