// 🎯 统一WebSocket消息处理协调器 - 消除重复处理和事件冲突
// 所有WebSocket消息都通过这个协调器统一处理

import { ref, computed } from 'vue'
import { useBattleStateCoordinator } from './useBattleStateCoordinator'
import { useSocketStore } from '~/stores/socket'

// 🎯 消息处理状态
interface MessageProcessingState {
  isProcessing: boolean
  processedMessages: Set<string>
  lastProcessedTime: number
  messageStats: {
    totalMessages: number
    duplicateMessages: number
    errorMessages: number
    lastSequence: number
  }
}

// 🎯 WebSocket消息协调器类
class WebSocketMessageCoordinator {
  private coordinator = useBattleStateCoordinator()
  private socketStore = useSocketStore()
  private battleId: string
  
  private state = ref<MessageProcessingState>({
    isProcessing: false,
    processedMessages: new Set<string>(),
    lastProcessedTime: 0,
    messageStats: {
      totalMessages: 0,
      duplicateMessages: 0,
      errorMessages: 0,
      lastSequence: 0
    }
  })

  private messageQueue: Array<{
    messageType: string
    action: string
    data: any
    socketId?: string
    timestamp: number
  }> = []

  // 事件监听器引用，用于cleanup
  private eventListeners: Record<string, any> = {}

  constructor(battleId: string) {
    this.battleId = battleId
    this.setupSocketListeners()
  }

  // 🎯 设置Socket监听器（使用全局插件）
  private setupSocketListeners() {
    console.log('[🔄WS-COORDINATOR] 设置WebSocket监听器（复用全局Socket插件）')
    
    // 🎯 监听全局插件派发的socket消息事件
    this.setupGlobalEventListeners()
    
    // 如果Socket已经连接，立即加入房间
    if (this.socketStore.socket && this.socketStore.isConnected) {
      console.log('[🔄WS-COORDINATOR] Socket已连接，立即加入房间')
      this.joinRoomChannel(this.battleId)
    } else {
      // 等待Socket连接完成
      console.log('[🔄WS-COORDINATOR] Socket未连接，等待连接完成...')
      
      const handleSocketConnected = () => {
        console.log('[🔄WS-COORDINATOR] Socket连接完成，加入房间')
        this.joinRoomChannel(this.battleId)
        
        // 移除事件监听器
        window.removeEventListener('socket:connected', handleSocketConnected)
      }
      
      window.addEventListener('socket:connected', handleSocketConnected)
      
      // 设置超时机制
      setTimeout(() => {
        if (!this.socketStore.socket || !this.socketStore.isConnected) {
          console.error('[🔄WS-COORDINATOR] Socket连接超时')
          window.removeEventListener('socket:connected', handleSocketConnected)
        }
      }, 5000)
    }
  }

  // 🎯 设置全局事件监听器（复用全局Socket插件）
  private setupGlobalEventListeners() {
    console.log('[🔄WS-COORDINATOR] 设置全局事件监听器')

    // 🎯 监听全局插件派发的socket消息事件 - 这是主要的消息来源
    const handleSocketMessage = (event: CustomEvent) => {
      const { messageType, action, data, socketId, timestamp } = event.detail
      console.log(`[🔄WS-COORDINATOR] 📨 收到全局Socket消息 [${messageType}, ${action}]`, { data, socketId })
      
      // 构造标准消息格式
      const wrappedMessage = [messageType, action, data, socketId]
      this.handleIncomingMessage(wrappedMessage)
    }

    // 监听全局socket消息事件
    window.addEventListener('socket:message', handleSocketMessage as EventListener)

    // 🎯 监听连接状态变化
    const handleSocketConnected = () => {
      console.log('[🔄WS-COORDINATOR] WebSocket连接状态变化：已连接')
      this.updateConnectionState(true)
    }

    const handleSocketDisconnected = () => {
      console.log('[🔄WS-COORDINATOR] WebSocket连接状态变化：已断开')
      this.updateConnectionState(false)
    }

    window.addEventListener('socket:connected', handleSocketConnected)
    window.addEventListener('socket:disconnected', handleSocketDisconnected)

    // 保存事件监听器引用，用于cleanup
    this.eventListeners = {
      'socket:message': handleSocketMessage,
      'socket:connected': handleSocketConnected,
      'socket:disconnected': handleSocketDisconnected
    }

    console.log('[🔄WS-COORDINATOR] ✅ 全局事件监听器设置完成 - 主要监听socket:message事件')
  }

  // 🎯 处理入站消息（统一入口）
  private async handleIncomingMessage(rawData: any) {
    try {
      // 解析消息格式：[messageType, action, data, socketId?]
      if (!Array.isArray(rawData) || rawData.length < 3) {
        console.warn('[🔄WS-COORDINATOR] 无效的消息格式:', rawData)
        this.state.value.messageStats.errorMessages++
        return
      }

      const [messageType, action, data, socketId] = rawData
      const timestamp = Date.now()

      // 🎯 生成消息唯一标识符
      const messageId = this.generateMessageId(messageType, action, data, timestamp)

      // 🎯 去重检查
      if (this.state.value.processedMessages.has(messageId)) {
        console.log('[🔄WS-COORDINATOR] 🔄 重复消息已忽略:', messageId)
        this.state.value.messageStats.duplicateMessages++
        return
      }

      // 🎯 消息验证
      if (!this.isValidMessage(messageType, action, data)) {
        console.warn('[🔄WS-COORDINATOR] 消息验证失败:', { messageType, action })
        this.state.value.messageStats.errorMessages++
        return
      }

      // 🎯 添加到处理队列
      this.messageQueue.push({
        messageType,
        action,
        data,
        socketId,
        timestamp
      })

      // 🎯 标记为已处理（防止重复）
      this.state.value.processedMessages.add(messageId)
      this.state.value.messageStats.totalMessages++

      // 🎯 更新序列号
      if (data?.message_sequence) {
        this.state.value.messageStats.lastSequence = Math.max(
          this.state.value.messageStats.lastSequence,
          data.message_sequence
        )
      }

      // 🎯 处理消息队列
      if (!this.state.value.isProcessing) {
        await this.processMessageQueue()
      }

    } catch (error) {
      console.error('[🔄WS-COORDINATOR] 处理入站消息失败:', error)
      this.state.value.messageStats.errorMessages++
    }
  }

  // 🎯 处理消息队列
  private async processMessageQueue() {
    this.state.value.isProcessing = true

    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift()!
      await this.processMessage(message)
    }

    this.state.value.isProcessing = false
    this.state.value.lastProcessedTime = Date.now()
  }

  // 🎯 处理单个消息
  private async processMessage(message: {
    messageType: string
    action: string
    data: any
    socketId?: string
    timestamp: number
  }) {
    const { messageType, action, data, timestamp } = message

    console.log(`[🔄WS-COORDINATOR] 处理消息: ${messageType}.${action}`)

    try {
      // 🎯 过滤与当前对战相关的消息
      if (!this.isRelevantMessage(messageType, action, data)) {
        console.log('[🔄WS-COORDINATOR] 消息与当前对战无关，跳过')
        return
      }

      // 🎯 通过状态协调器处理消息
      await this.coordinator.updateState({
        type: 'websocket_message',
        data: {
          messageType,
          action,
          data
        },
        timestamp,
        source: `websocket_${messageType}_${action}`
      })

      // 🎯 特殊处理：触发legacy事件以保持兼容性
      this.triggerLegacyEvents(messageType, action, data)

    } catch (error) {
      console.error('[🔄WS-COORDINATOR] 处理消息失败:', error)
      this.state.value.messageStats.errorMessages++
    }
  }

  // 🎯 生成消息唯一标识符
  private generateMessageId(messageType: string, action: string, data: any, timestamp: number): string {
    // 基于消息内容生成唯一ID
    const contentHash = JSON.stringify({
      messageType,
      action,
      sequence: data?.message_sequence,
      round: data?.round,
      animationId: data?.animation_id
    })
    
    return `${messageType}_${action}_${btoa(contentHash).slice(0, 8)}_${timestamp}`
  }

  // 🎯 消息验证
  private isValidMessage(messageType: string, action: string, data: any): boolean {
    // 基础验证
    if (!messageType || !action) return false

    // 特定消息类型验证
    switch (messageType) {
      case 'boxroom':
        return ['new', 'update', 'start', 'cancel'].includes(action)
      
      case 'boxroomdetail':
        return ['round_start', 'opening_start', 'animation_progress', 'round_result', 'battle_end', 'round', 'end'].includes(action)
      
      case 'box':
        return ['new', 'details'].includes(action)
      
      default:
        return true // 允许其他类型的消息通过
    }
  }

  // 🎯 检查消息是否与当前对战相关
  private isRelevantMessage(messageType: string, action: string, data: any): boolean {
    // boxroomdetail消息始终相关（通过房间频道发送）
    if (messageType === 'boxroomdetail') return true

    // boxroom消息需要检查房间ID
    if (messageType === 'boxroom') {
      return data?.uid === this.battleId || 
             data?.short_id === this.battleId ||
             this.checkRoomIdMatch(data)
    }

    // 其他消息类型暂时允许通过
    return true
  }

  // 🎯 检查房间ID匹配
  private checkRoomIdMatch(data: any): boolean {
    // TODO: 实现更精确的房间ID匹配逻辑
    return false
  }

  // 🎯 触发legacy事件（保持向后兼容）
  private triggerLegacyEvents(messageType: string, action: string, data: any) {
    // 只触发必要的legacy事件，避免重复处理
    const legacyEventMap: Record<string, string> = {
      'boxroomdetail.round_start': 'socket:round_start',
      'boxroomdetail.opening_start': 'socket:opening_start',
      'boxroomdetail.round_result': 'socket:round_result',
      'boxroomdetail.battle_end': 'socket:battle_end'
    }

    const eventKey = `${messageType}.${action}`
    const legacyEventName = legacyEventMap[eventKey]

    if (legacyEventName) {
      console.log(`[🔄WS-COORDINATOR] 触发legacy事件: ${legacyEventName}`)
      window.dispatchEvent(new CustomEvent(legacyEventName, {
        detail: data
      }))
    }
  }

  // 🎯 更新连接状态
  private updateConnectionState(isConnected: boolean) {
    this.coordinator.updateState({
      type: 'websocket_message',
      data: {
        messageType: 'connection',
        action: isConnected ? 'connected' : 'disconnected',
        data: { isConnected }
      },
      timestamp: Date.now(),
      source: 'websocket_connection'
    })
  }

  // 🎯 获取统计信息
  getMessageStats() {
    return {
      ...this.state.value.messageStats,
      latestActivity: new Date(this.state.value.lastProcessedTime).toLocaleTimeString(),
      isProcessing: this.state.value.isProcessing,
      queueLength: this.messageQueue.length,
      processedMessagesCount: this.state.value.processedMessages.size
    }
  }

  // 🎯 验证状态一致性
  validateStateConsistency(): boolean {
    try {
      const coordinatorState = this.coordinator.getState()
      
      // 检查基本一致性
      const hasValidBattleData = coordinatorState.battleData !== null
      const hasValidRounds = coordinatorState.currentRound > 0 && coordinatorState.totalRounds > 0
      const roundsInRange = coordinatorState.currentRound <= coordinatorState.totalRounds
      
      return hasValidBattleData && hasValidRounds && roundsInRange
    } catch (error) {
      console.error('[🔄WS-COORDINATOR] 状态一致性检查失败:', error)
      return false
    }
  }

  // 🎯 清理方法
  cleanup() {
    console.log('[🔄WS-COORDINATOR] 清理WebSocket协调器')
    
    // 清理全局事件监听器
    Object.keys(this.eventListeners).forEach(eventName => {
      window.removeEventListener(eventName, this.eventListeners[eventName])
    })
    
    // 清空事件监听器引用
    this.eventListeners = {}

    // 清理状态
    this.messageQueue.length = 0
    this.state.value.processedMessages.clear()
    this.state.value.isProcessing = false
    
    console.log('[🔄WS-COORDINATOR] ✅ 清理完成')
  }

  // 🎯 手动发送消息
  sendMessage(eventName: string, data: any) {
    if (this.socketStore.socket && this.socketStore.isConnected) {
      console.log(`[🔄WS-COORDINATOR] 发送消息: ${eventName}`, data)
      this.socketStore.socket.emit(eventName, data)
    } else {
      console.warn('[🔄WS-COORDINATOR] 无法发送消息，Socket未连接')
    }
  }

  // 🎯 加入房间频道
  joinRoomChannel(roomUid: string) {
    this.sendMessage('join', roomUid)
  }

  // 🎯 请求对战数据
  requestBattleData() {
    this.sendMessage('request_battle_data', { battle_id: this.battleId })
  }
}

// 🎯 全局WebSocket协调器实例
let globalWebSocketCoordinator: WebSocketMessageCoordinator | null = null

export const useBattleWebSocketCoordinator = (battleId: string) => {
  if (!globalWebSocketCoordinator || globalWebSocketCoordinator['battleId'] !== battleId) {
    // 清理旧的协调器
    if (globalWebSocketCoordinator) {
      globalWebSocketCoordinator.cleanup()
    }
    
    globalWebSocketCoordinator = new WebSocketMessageCoordinator(battleId)
  }
  
  return {
    // 方法
    getMessageStats: () => globalWebSocketCoordinator!.getMessageStats(),
    validateStateConsistency: () => globalWebSocketCoordinator!.validateStateConsistency(),
    sendMessage: (eventName: string, data: any) => globalWebSocketCoordinator!.sendMessage(eventName, data),
    joinRoomChannel: (roomUid: string) => globalWebSocketCoordinator!.joinRoomChannel(roomUid),
    requestBattleData: () => globalWebSocketCoordinator!.requestBattleData(),
    cleanup: () => globalWebSocketCoordinator!.cleanup()
  }
} 