<template>
  <div class="auth-page min-h-screen flex flex-col justify-center items-center py-12 px-4 relative">
    <!-- 背景装饰元素 -->
    <div class="fixed inset-0 -z-10">
      <div class="absolute top-0 -left-32 w-96 h-96 rounded-full blur-3xl bg-blue-500/5 animate-pulse-slow"></div>
      <div class="absolute bottom-0 -right-32 w-96 h-96 rounded-full blur-3xl bg-purple-500/5 animate-pulse-slow"></div>
      <div class="absolute inset-0 bg-grid opacity-10"></div>
    </div>

    <!-- 登录表单 -->
    <div class="auth-form p-8 rounded-xl shadow-2xl w-full max-w-md mx-auto bg-gray-800/40 backdrop-blur-md border border-gray-700/30">
      <h1 class="text-3xl font-bold mb-8 text-center text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-400">
        {{ $t('auth.login.title') }}
      </h1>
      
      <!-- 登录表单 -->
      <form @submit.prevent="handleSubmit" method="post" class="space-y-6">
        <!-- 邮箱输入 -->
        <div class="form-group">
          <label for="email" class="block mb-2 text-sm font-medium text-white/90">
            {{ $t('auth.email') }}
          </label>
          <div class="relative">
            <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50">
              📧
            </div>
            <input 
              type="email" 
              id="email" 
              v-model="formData.email" 
              :class="[
                'w-full pl-10 pr-4 py-3 rounded-lg bg-gray-700/50 border transition-all duration-300 text-white placeholder-gray-400',
                'focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50',
                emailError ? 'border-red-500 ring-2 ring-red-500/20' : 'border-gray-600/50 hover:border-gray-500/50'
              ]"
              :placeholder="$t('auth.email_placeholder')"
              @blur="validateEmail"
              @input="clearEmailError"
              autocomplete="email"
            />
          </div>
          <transition name="error">
            <p v-if="emailError" class="mt-2 text-sm text-red-400 flex items-center">
              <span class="mr-1">⚠️</span>
              {{ emailError }}
            </p>
          </transition>
        </div>
        
        <!-- 密码输入 -->
        <div class="form-group">
          <label for="password" class="block mb-2 text-sm font-medium text-white/90">
            {{ $t('auth.password') }}
          </label>
          <div class="relative">
            <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50">
              🔒
            </div>
            <input 
              :type="showPassword ? 'text' : 'password'" 
              id="password" 
              v-model="formData.password" 
              :class="[
                'w-full pl-10 pr-12 py-3 rounded-lg bg-gray-700/50 border transition-all duration-300 text-white placeholder-gray-400',
                'focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50',
                passwordError ? 'border-red-500 ring-2 ring-red-500/20' : 'border-gray-600/50 hover:border-gray-500/50'
              ]"
              :placeholder="$t('auth.password_placeholder')"
              @blur="validatePassword"
              @input="clearPasswordError"
              autocomplete="current-password"
            />
            <button 
              type="button" 
              class="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/50 hover:text-white transition-colors"
              @click="togglePasswordVisibility"
            >
              {{ showPassword ? '🙈' : '👁️' }}
            </button>
          </div>
          <transition name="error">
            <p v-if="passwordError" class="mt-2 text-sm text-red-400 flex items-center">
              <span class="mr-1">⚠️</span>
              {{ passwordError }}
            </p>
          </transition>
        </div>
        
        <!-- 验证码 -->
        <div class="form-group">
          <label for="captcha" class="block mb-2 text-sm font-medium text-white/90">
            {{ $t('auth.captcha') }}
          </label>
          <div class="flex gap-3">
            <div class="relative flex-1">
              <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50">
                🛡️
              </div>
              <input 
                type="text" 
                id="captcha" 
                v-model="formData.captcha" 
                :class="[
                  'w-full pl-10 pr-4 py-3 rounded-lg bg-gray-700/50 border transition-all duration-300 text-white placeholder-gray-400',
                  'focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50',
                  captchaError ? 'border-red-500 ring-2 ring-red-500/20' : 'border-gray-600/50 hover:border-gray-500/50'
                ]"
                :placeholder="$t('auth.captcha_placeholder')"
                @blur="validateCaptcha"
                @input="clearCaptchaError"
                maxlength="4"
              />
            </div>
            <div 
              class="w-28 h-12 overflow-hidden rounded-lg border border-gray-600/50 cursor-pointer flex items-center justify-center bg-gray-700/50 hover:bg-gray-600/50 transition-all duration-300 relative group"
              @click="refreshCaptcha"
              :title="$t('auth.refresh_captcha')"
            >
              <div v-if="isLoadingCaptcha" class="absolute inset-0 flex items-center justify-center bg-gray-700/80 rounded-lg">
                <div class="animate-spin w-5 h-5 border-2 border-blue-400 border-t-transparent rounded-full"></div>
              </div>
              <img 
                v-if="captcha?.captcha && !isLoadingCaptcha" 
                :src="captcha.captcha" 
                :alt="$t('auth.captcha')" 
                class="w-full h-full object-cover group-hover:opacity-80 transition-opacity" 
              />
              <div v-if="!captcha?.captcha && !isLoadingCaptcha" class="text-xs text-blue-400 animate-pulse text-center">
                点击获取
              </div>
              <!-- 调试信息 -->
              <div v-if="isDev" class="absolute -bottom-8 left-0 text-xs text-gray-400">
                状态: {{ isLoadingCaptcha ? '加载中' : (captcha?.captcha ? '已加载' : '未加载') }}
              </div>
            </div>
          </div>
          <transition name="error">
            <p v-if="captchaError" class="mt-2 text-sm text-red-400 flex items-center">
              <span class="mr-1">⚠️</span>
              {{ captchaError }}
            </p>
          </transition>
        </div>
        
        <!-- 记住登录状态 & 忘记密码 -->
        <div class="flex justify-between items-center">
          <div class="flex items-center">
            <div class="relative inline-flex items-center cursor-pointer" @click="rememberMe = !rememberMe">
              <input 
                type="checkbox" 
                id="remember" 
                v-model="rememberMe" 
                class="sr-only"
              />
              <div class="toggle-bg transition-all duration-300 border-2 h-6 w-11 rounded-full" 
                   :class="rememberMe ? 'bg-blue-500/20 border-blue-500/50' : 'bg-gray-700 border-gray-600'"></div>
              <div class="dot absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition-all duration-300 shadow-md"
                   :class="{ 'transform translate-x-5': rememberMe }"></div>
            </div>
            <label for="remember" class="ml-3 text-sm text-white/70 cursor-pointer hover:text-white/90 transition-colors">
              {{ $t('auth.remember_me') }}
            </label>
          </div>
          <NuxtLink 
            :to="localePath('/auth/forgot-password')" 
            class="text-sm text-blue-400 hover:text-blue-300 transition-colors hover:underline"
          >
            {{ $t('auth.forgot_password') }}
          </NuxtLink>
        </div>
        
        <!-- 提交按钮 -->
        <button 
          type="submit" 
          class="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-500 hover:to-purple-500 text-white font-medium py-3 px-4 rounded-lg transition-all duration-300 flex items-center justify-center transform hover:-translate-y-0.5 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
          :disabled="isLoading || !isFormValid"
        >
          <div v-if="isLoading" class="loading-spinner mr-2"></div>
          {{ isLoading ? $t('auth.logging_in') : $t('auth.login.title') }}
        </button>
        
        <!-- 错误信息 -->
        <transition name="error">
          <div v-if="formError" class="p-4 bg-red-500/20 border border-red-500/50 rounded-lg">
            <p class="text-sm text-red-300 text-center flex items-center justify-center">
              <span class="mr-2">❌</span>
              {{ formError }}
            </p>
          </div>
        </transition>
        
        <!-- 注册链接 -->
        <div class="text-center text-sm text-white/70">
          {{ $t('auth.no_account') }}？
          <NuxtLink 
            :to="localePath('/auth/register')" 
            class="text-blue-400 hover:text-blue-300 transition-colors font-medium hover:underline ml-1"
          >
            {{ $t('auth.register_now') }}
          </NuxtLink>
        </div>
        
        <!-- 第三方登录选项 -->
        <div class="mt-8">
          <div class="relative mb-6">
            <div class="absolute inset-0 flex items-center">
              <div class="w-full border-t border-gray-600/50"></div>
            </div>
            <div class="relative flex justify-center">
              <span class="px-4 bg-gray-800/40 text-sm text-white/50">
                {{ $t('auth.or_continue_with') }}
              </span>
            </div>
          </div>
          
          <div class="flex justify-center space-x-4">
            <button 
              type="button" 
              class="flex items-center justify-center p-3 rounded-lg bg-gray-700/70 hover:bg-gray-600/70 border border-gray-600/50 hover:border-blue-500/30 transition-all duration-300 transform hover:-translate-y-0.5 shadow-md group"
              @click="handleThirdPartyLogin('steam')"
              :title="$t('auth.login_with', { provider: 'Steam' })"
            >
              <span class="text-lg group-hover:scale-110 transition-transform">🎮</span>
            </button>
            
            <button 
              type="button" 
              class="flex items-center justify-center p-3 rounded-lg bg-gray-700/70 hover:bg-gray-600/70 border border-gray-600/50 hover:border-blue-500/30 transition-all duration-300 transform hover:-translate-y-0.5 shadow-md group"
              @click="handleThirdPartyLogin('google')"
              :title="$t('auth.login_with', { provider: 'Google' })"
            >
              <span class="text-lg group-hover:scale-110 transition-transform">🚀</span>
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAuth } from '~/composables/useAuth'

// 认证相关
const {
  captcha,
  isLoadingCaptcha,
  isLoading,
  authError,
  getCaptcha,
  login
} = useAuth()

// 多语言和路由
const { t } = useI18n()
const localePath = useLocalePath()

// 表单数据
const formData = reactive({
  email: '',
  password: '',
  captcha: ''
})

// UI状态
const showPassword = ref(false)
const rememberMe = ref(false)

// 错误状态
const emailError = ref('')
const passwordError = ref('')
const captchaError = ref('')
const formError = ref('')

const userStore = useUserStore()
const router = useRouter()

// 开发模式检测
const isDev = computed(() => process.dev)

// 表单验证计算属性
const isFormValid = computed(() => {
  return formData.email && 
         formData.password && 
         formData.captcha && 
         !emailError.value && 
         !passwordError.value && 
         !captchaError.value
})

// 验证邮箱
const validateEmail = () => {
  if (!formData.email) {
    emailError.value = t('auth.email_required')
    return false
  }
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(formData.email)) {
    emailError.value = t('auth.email_invalid')
    return false
  }
  
  emailError.value = ''
  return true
}

// 验证密码
const validatePassword = () => {
  if (!formData.password) {
    passwordError.value = t('auth.password_required')
    return false
  }
  
  if (formData.password.length < 6) {
    passwordError.value = t('auth.password_too_short')
    return false
  }
  
  passwordError.value = ''
  return true
}

// 验证验证码
const validateCaptcha = () => {
  if (!formData.captcha) {
    captchaError.value = t('auth.captcha_required')
    return false
  }
  
  captchaError.value = ''
  return true
}

// 清除错误信息
const clearEmailError = () => { emailError.value = '' }
const clearPasswordError = () => { passwordError.value = '' }
const clearCaptchaError = () => { captchaError.value = '' }

// 切换密码可见性
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value
}

// 刷新验证码
const refreshCaptcha = async () => {
  try {
    await getCaptcha()
    formData.captcha = '' // 清空验证码输入
    captchaError.value = ''
  } catch (error) {
    console.error('刷新验证码失败:', error)
  }
}

// 表单提交
const handleSubmit = async () => {
  // 清除之前的错误
  formError.value = ''
  
  // 验证表单
  const isEmailValid = validateEmail()
  const isPasswordValid = validatePassword()
  const isCaptchaValid = validateCaptcha()
  
  if (!isEmailValid || !isPasswordValid || !isCaptchaValid) {
    return
  }
  
  try {
    const success = await login(formData.email, formData.password, formData.captcha)
    
    if (success) {
      // 登录成功，跳转到首页
      await router.push(localePath('/'))
    } else {
      // 登录失败，显示错误信息
      formError.value = authError.value || '登录失败，请重试'
    }
  } catch (error: any) {
    console.error('登录出错:', error)
    formError.value = error.message || '登录失败，请重试'
  }
}

// 第三方登录
const handleThirdPartyLogin = (provider: string) => {
  if (provider === 'steam') {
    // 跳转到Steam登录页面
    window.location.href = '/api/auth/steam/'
  } else if (provider === 'google') {
    // 这里可以集成Google登录
    formError.value = 'Google登录功能即将推出'
  }
}

// 页面加载时获取验证码
onMounted(async () => {
  await refreshCaptcha()
})

// SEO 和元数据设置
useSeoMeta({
  title: computed(() => t('auth.login.title')),
  description: computed(() => t('auth.login.description')),
  keywords: computed(() => t('auth.login.keywords')),
})

useHead({
  title: computed(() => t('auth.login.title')),
  link: [
    {
      rel: 'canonical',
      href: `${useRuntimeConfig().public.siteUrl}${localePath('/auth/login')}`
    }
  ]
})

// 路由保护：已登录用户重定向到首页
definePageMeta({
  middleware: 'guest'
})
</script>

<style lang="scss" scoped>
.auth-page {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.95) 100%);
  backdrop-filter: blur(10px);
}

/* 背景网格 */
.bg-grid {
  background-image: 
    linear-gradient(to right, rgba(255, 255, 255, 0.03) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* 慢脉冲动画 */
.animate-pulse-slow {
  animation: pulse-slow 8s ease-in-out infinite;
}

@keyframes pulse-slow {
  0%, 100% { opacity: 0.4; }
  50% { opacity: 0.7; }
}

/* 加载动画 */
.loading-spinner {
  display: inline-block;
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 切换按钮样式 */
.toggle-bg {
  transition: all 0.3s ease;
}

.dot {
  transition: all 0.3s ease;
}

/* 错误信息过渡动画 */
.error-enter-active, .error-leave-active {
  transition: all 0.3s ease;
}

.error-enter-from, .error-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* 表单组优化 */
.form-group {
  position: relative;
}

.form-group input:focus + .absolute {
  color: rgba(59, 130, 246, 0.8);
}

/* 渐变背景优化 */
.auth-form {
  background: 
    linear-gradient(135deg, rgba(31, 41, 55, 0.8) 0%, rgba(17, 24, 39, 0.8) 100%),
    radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(147, 51, 234, 0.1) 0%, transparent 50%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 按钮悬停效果 */
button[type="submit"]:not(:disabled):hover {
  box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.3);
}

/* 第三方登录按钮悬停效果 */
.group:hover {
  box-shadow: 0 4px 15px -3px rgba(59, 130, 246, 0.2);
}
</style> 