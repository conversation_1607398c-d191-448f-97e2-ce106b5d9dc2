# Sitecfg模块API分析文档

## 概述

Sitecfg模块是网站配置管理系统，负责网站的全局配置、公告管理、内容管理、SEO设置、IP访问控制等功能。该模块是整个系统的配置中心，为其他模块提供配置参数支持。

## 现有API接口

### 1. 网站配置接口

#### GET /api/sitecfg/cfg/ - 获取网站配置

**功能**: 获取网站的基础配置信息。

**权限**: AllowAny

**响应格式**:
```json
{
    "status": 200,
    "data": {
        "roll_join_min_charge": 1
    },
    "msg": "Succeed"
}
```

### 2. 公告管理接口

#### GET /api/sitecfg/announce/list/ - 获取公告列表

**功能**: 获取网站公告列表，支持分页。

**请求参数**:
- `page`: 页码，默认为1
- `pageSize`: 每页大小，默认为10  
- `order`: 排序方式，默认为'-id'

**响应格式**:
```json
{
    "status": 200,
    "data": {
        "items": [
            {
                "id": 1,
                "title": "公告标题",
                "title_en": "Announcement Title",
                "title_zh_hans": "公告标题",
                "content": "<p>公告内容...</p>",
                "content_en": "<p>Announcement content...</p>",
                "content_zh_hans": "<p>公告内容...</p>",
                "create_time": "2023-12-01T10:00:00Z",
                "update_time": "2023-12-01T10:00:00Z"
            }
        ],
        "total": 50,
        "page": 1,
        "limit": 10
    },
    "msg": "Succeed"
}
```

#### GET /api/sitecfg/announce/detail/ - 获取公告详情

**功能**: 获取指定公告的详细信息。

**请求参数**:
- `id`: 公告ID

### 3. 页面内容接口

#### GET /api/sitecfg/banner/ - 获取横幅信息

**功能**: 获取网站横幅广告信息。

**响应格式**:
```json
{
    "status": 200,
    "data": {
        "items": [
            {
                "title": "横幅标题",
                "title_en": "Banner Title",
                "title_zh_hans": "横幅标题",
                "link": "https://example.com",
                "image": "/media/banner/image.jpg",
                "type": 0,
                "is_simple": true,
                "description": "横幅描述",
                "description_en": "Banner description",
                "description_zh_hans": "横幅描述",
                "background_class": "bg-primary",
                "glow_class": "glow-effect",
                "primary_button_text": "主按钮",
                "primary_button_text_en": "Primary Button",
                "primary_button_text_zh_hans": "主按钮",
                "primary_button_link": "/action",
                "secondary_button_text": "次按钮",
                "secondary_button_text_en": "Secondary Button", 
                "secondary_button_text_zh_hans": "次按钮",
                "secondary_button_link": "/secondary",
                "order": 1
            }
        ]
    },
    "msg": "Succeed"
}
```

#### GET /api/sitecfg/footer/ - 获取页脚信息

**功能**: 获取网站页脚链接和信息。

#### GET /api/sitecfg/faq/ - 获取FAQ信息

**功能**: 获取常见问题解答。

**请求参数**:
- `uid`: FAQ的UID（可选，不传则返回所有FAQ）

**响应格式**:
```json
{
    "status": 200,
    "data": {
        "items": [
            {
                "uid": "faq-001",
                "title": "如何充值？",
                "title_en": "How to recharge?",
                "title_zh_hans": "如何充值？",
                "content": "<p>充值步骤...</p>",
                "content_en": "<p>Recharge steps...</p>",
                "content_zh_hans": "<p>充值步骤...</p>"
            }
        ]
    },
    "msg": "Succeed"
}
```

#### GET /api/sitecfg/support/ - 获取客服支持信息

**功能**: 获取客服支持相关信息。

**请求参数**:
- `uid`: 支持信息的UID（可选）

### 4. 文章内容接口

#### GET /api/sitecfg/article/ - 获取文章详情

**功能**: 获取指定文章的详细内容。

**请求参数**:
- `tag`: 文章标签

**响应格式**:
```json
{
    "status": 200,
    "data": {
        "item": {
            "id": 1,
            "title": "文章标题",
            "seo_title": "SEO标题",
            "seo_description": "SEO描述",
            "seo_keywords": "SEO关键词",
            "tag": "article-tag",
            "content": "<p>文章内容...</p>",
            "color": "#FF0000",
            "image": "/media/article/image.jpg",
            "category": {...}
        },
        "next": {
            "id": 2,
            "title": "下一篇文章标题"
        },
        "pre": {
            "id": 0,
            "title": "上一篇文章标题"
        }
    },
    "msg": "Succeed"
}
```

#### GET /api/sitecfg/articlelist/ - 获取文章列表（已弃用）

#### GET /api/sitecfg/articleweapon/ - 获取武器文章

**功能**: 获取武器相关的文章分类和列表。

**响应格式**:
```json
{
    "status": 200,
    "data": {
        "items_data": [
            {
                "id": 1,
                "name": "步枪",
                "name_en": "CSGO_Type_Rifle",
                "articles": [
                    {
                        "id": 1,
                        "title": "AK-47系列",
                        "tag": "AK-47",
                        "number": 15
                    }
                ]
            }
        ]
    },
    "msg": "Succeed"
}
```

#### GET /api/sitecfg/articleteam/ - 获取战队文章

**功能**: 获取电竞战队相关文章。

#### GET /api/sitecfg/articleplayer/ - 获取选手文章

**功能**: 获取职业选手相关文章。

#### GET /api/sitecfg/articlematch/ - 获取赛事文章

**功能**: 获取电竞赛事相关文章。

### 5. SEO和访问控制接口

#### GET /api/sitecfg/seo/ - 获取SEO信息

**功能**: 获取指定域名的SEO配置信息。

**请求参数**:
- `url`: 域名URL

**响应格式**:
```json
{
    "status": 200,
    "data": {
        "url": "www.csgoskins.com.cn",
        "name": "CSGOSKINS开箱网",
        "subtitle": "CSGO开箱网中国站",
        "title": "CSGO开箱网站,CS2开箱网站",
        "keywords": "CSGO开箱网站,CS2开箱网站,CSGOSKINS开箱网",
        "description": "CSGOSKINS开箱网致力于打造中国良心CSGO开箱网站...",
        "icp": "备案号",
        "news_enable": false
    },
    "msg": "Succeed"
}
```

#### GET /api/sitecfg/checkip/ - IP访问检查

**功能**: 检查当前IP是否在黑名单中。

**响应格式**:
```json
{
    "status": 200,
    "data": true,    // true表示允许访问，false表示被封禁
    "msg": "***********"  // 当前IP地址
}
```

## 配置管理系统

### SiteConfig配置项

Sitecfg模块通过interfaces.py提供了丰富的配置接口，主要包括：

#### 1. 业务配置
- `roll_join_min_charge_limit`: Roll房间参与最低充值限制
- `withdraw_count_limit`: 每日提现次数限制
- `withdraw_price_limit`: 提现金额限制
- `box_room_expire`: 箱子房间过期时间
- `charge_rate`: 充值汇率

#### 2. 维护模式配置
- `maintenance`: 全站维护
- `maintenance_box`: 开箱维护
- `maintenance_roll`: Roll房间维护
- `maintenance_withdraw`: 提现维护
- `maintenance_charge`: 充值维护
- `maintenance_deposit`: 存入维护

#### 3. 游戏相关配置
- `box_chance_chosen`: 开箱概率方案选择
- `luckybox_rate`: 幸运箱子爆率
- `custombox_rate`: 自定义箱子爆率
- `tradeup_bet_percentage_max`: 合成最大成功率

#### 4. 用户权限配置
- `enable_recharge_bot`: 启用充值机器人
- `enable_recharge_limit`: 启用每日充值限制
- `daily_recharge_limit`: 每日充值限制金额
- `access_control_ip`: 启用IP访问控制
- `access_control_email_suffix`: 启用邮箱后缀限制

#### 5. 统计数据配置
- `users_base_count`: 用户基数
- `online_users_base_count`: 在线用户基数
- `open_base_count`: 开箱基数

## 存在的问题和缺陷

### 1. API设计问题

**问题**:
- 所有接口都使用`AllowAny`权限，缺少权限控制
- 接口命名不够RESTful，如`/articleweapon/`
- 响应格式不统一，有些用`items`，有些用`items_data`
- 缺少配置修改接口，只能查询不能更新

**建议**:
```python
# 改进的URL设计
urlpatterns = [
    # 配置管理
    url(r'^configs/$', views.ConfigListView.as_view()),           # GET配置列表
    url(r'^configs/(?P<key>\w+)/$', views.ConfigDetailView.as_view()),  # GET/PUT配置详情
    
    # 公告管理
    url(r'^announcements/$', views.AnnouncementListView.as_view()),      # GET列表, POST创建
    url(r'^announcements/(?P<id>\d+)/$', views.AnnouncementDetailView.as_view()),  # GET详情
    
    # 内容管理
    url(r'^articles/$', views.ArticleListView.as_view()),
    url(r'^articles/(?P<tag>\w+)/$', views.ArticleDetailView.as_view()),
    url(r'^articles/categories/(?P<category>\w+)/$', views.ArticleByCategoryView.as_view()),
]
```

### 2. 权限和安全问题

**问题**:
- 所有接口都允许匿名访问，存在信息泄露风险
- 配置项没有修改权限控制
- IP检查接口暴露了服务器获取用户IP的方式
- 缺少配置变更审计日志

**建议**:
```python
class ConfigDetailView(APIView):
    def get_permissions(self):
        if self.request.method == 'GET':
            return [AllowAny()]
        else:
            return [IsAdminUser()]  # 只有管理员可以修改配置
    
    def put(self, request, key):
        # 记录配置变更日志
        old_value = get_string_from_site_config(key)
        new_value = request.data.get('value')
        
        ConfigChangeLog.objects.create(
            key=key,
            old_value=old_value,
            new_value=new_value,
            changed_by=request.user,
            timestamp=timezone.now()
        )
```

### 3. 缓存策略问题

**问题**:
- 缓存使用不一致，有些接口有缓存，有些没有
- 缓存失效策略不完善
- 配置更新后缓存没有及时清理

**建议**:
```python
class CacheManager:
    @staticmethod
    def get_config_cache_key(key):
        return f'sitecfg_config_{key}'
    
    @staticmethod
    def invalidate_config_cache(key):
        cache_key = CacheManager.get_config_cache_key(key)
        cache.delete(cache_key)
        
        # 清理相关缓存
        related_caches = [
            'banner', 'seo_*', 'article_weapon'
        ]
        for pattern in related_caches:
            cache.delete_pattern(pattern)

def update_site_config(key, value):
    """更新配置并清理缓存"""
    config = SiteConfig.objects.get(key=key)
    config.value = value
    config.save()
    
    # 清理相关缓存
    CacheManager.invalidate_config_cache(key)
```

### 4. 数据模型问题

**问题**:
- 配置项缺少数据类型和验证
- 多语言支持不完整，字段命名混乱
- 缺少配置项的分组和层级管理

**建议**:
```python
class SiteConfig(models.Model):
    CONFIG_TYPES = (
        ('string', '字符串'),
        ('integer', '整数'),
        ('float', '浮点数'),
        ('boolean', '布尔值'),
        ('json', 'JSON'),
    )
    
    category = models.ForeignKey(SiteConfigCategory)
    key = models.CharField(max_length=64, unique=True)
    value = models.TextField()  # 改为TextField支持更长内容
    data_type = models.CharField(max_length=20, choices=CONFIG_TYPES, default='string')
    validation_rule = models.TextField(null=True, blank=True)  # 验证规则
    description = models.TextField()  # 配置项说明
    enable = models.BooleanField(default=True)
    order = models.IntegerField(default=0)
    
    def clean_value(self):
        """根据数据类型验证和清理值"""
        if self.data_type == 'integer':
            return int(self.value)
        elif self.data_type == 'float':
            return float(self.value)
        elif self.data_type == 'boolean':
            return self.value.lower() in ('true', '1', 'yes')
        elif self.data_type == 'json':
            return json.loads(self.value)
        return self.value
```

### 5. 业务逻辑问题

**问题**:
- 文章分类查询逻辑复杂，效率低下
- 配置更新没有生效通知机制
- 缺少配置项的依赖关系管理

**建议**:
```python
# 优化文章查询
def get_articles_by_category(category_names, page=1, page_size=10):
    """优化的文章分类查询"""
    # 使用缓存
    cache_key = f'articles_by_category_{hash(tuple(category_names))}_{page}_{page_size}'
    cached_result = cache.get(cache_key)
    if cached_result:
        return cached_result
    
    # 优化查询
    categories = ArticleCategory.objects.filter(
        name_en__in=category_names,
        enable=True
    ).prefetch_related('articles')
    
    result = []
    for category in categories:
        articles = category.articles.filter(enable=True).order_by('sort_order')
        paginator = Paginator(articles, page_size)
        page_articles = paginator.page(page)
        
        result.append({
            'category': category,
            'articles': page_articles,
            'total': paginator.count
        })
    
    cache.set(cache_key, result, 60*60)  # 缓存1小时
    return result

# 配置变更通知
class ConfigChangeNotifier:
    @staticmethod
    def notify_config_change(key, old_value, new_value):
        """配置变更通知"""
        # WebSocket通知
        ws_send_msg('admin_config_changes', {
            'key': key,
            'old_value': old_value,
            'new_value': new_value,
            'timestamp': timezone.now().isoformat()
        })
        
        # 如果是关键配置，发送邮件通知
        critical_configs = ['maintenance', 'charge_rate', 'withdraw_count_limit']
        if key in critical_configs:
            send_admin_notification_email(key, old_value, new_value)
```

### 6. 国际化问题

**问题**:
- 多语言字段命名不规范（`title_en`, `title_zh_hans`）
- 缺少动态语言切换支持
- 默认语言处理逻辑复杂

**建议**:
```python
# 改进的多语言模型设计
class MultilingualModel(models.Model):
    class Meta:
        abstract = True
    
    def get_localized_field(self, field_name, language=None):
        """获取本地化字段值"""
        if not language:
            language = get_current_language()
        
        localized_field = f"{field_name}_{language}"
        if hasattr(self, localized_field):
            value = getattr(self, localized_field)
            if value:
                return value
        
        # 回退到默认字段
        return getattr(self, field_name, '')

class Announce(MultilingualModel):
    title = models.CharField(max_length=128)
    title_en = models.CharField(max_length=128, null=True, blank=True)
    title_zh_hans = models.CharField(max_length=128, null=True, blank=True)
    
    @property
    def localized_title(self):
        return self.get_localized_field('title')
```

## 改进建议

### 1. API重构建议

**统一的RESTful API设计**:
```python
# sitecfg/urls.py
urlpatterns = [
    # 配置管理
    url(r'^configs/$', views.ConfigListView.as_view()),
    url(r'^configs/categories/$', views.ConfigCategoryListView.as_view()),
    url(r'^configs/(?P<key>\w+)/$', views.ConfigDetailView.as_view()),
    
    # 内容管理
    url(r'^announcements/$', views.AnnouncementListView.as_view()),
    url(r'^announcements/(?P<id>\d+)/$', views.AnnouncementDetailView.as_view()),
    
    url(r'^articles/$', views.ArticleListView.as_view()),
    url(r'^articles/(?P<tag>\w+)/$', views.ArticleDetailView.as_view()),
    url(r'^articles/categories/(?P<category>\w+)/$', views.ArticleByCategoryView.as_view()),
    
    # 页面元素
    url(r'^banners/$', views.BannerListView.as_view()),
    url(r'^footer/$', views.FooterView.as_view()),
    url(r'^seo/$', views.SEOView.as_view()),
    
    # 支持和帮助
    url(r'^faq/$', views.FAQListView.as_view()),
    url(r'^support/$', views.SupportListView.as_view()),
    
    # 系统功能
    url(r'^ip-check/$', views.IPCheckView.as_view()),
    url(r'^maintenance/$', views.MaintenanceStatusView.as_view()),
]
```

### 2. 配置管理系统重构

**配置管理中心**:
```python
class ConfigManager:
    """配置管理中心"""
    
    @staticmethod
    def get_config(key, default=None, data_type='string'):
        """获取配置值"""
        config = SiteConfig.objects.filter(key=key, enable=True).first()
        if not config:
            return default
        
        try:
            if data_type == 'int':
                return int(config.value)
            elif data_type == 'float':
                return float(config.value)
            elif data_type == 'bool':
                return config.value.lower() in ('true', '1', 'yes')
            elif data_type == 'json':
                return json.loads(config.value)
            return config.value
        except (ValueError, json.JSONDecodeError):
            return default
    
    @staticmethod
    def set_config(key, value, user=None):
        """设置配置值"""
        config, created = SiteConfig.objects.get_or_create(key=key)
        old_value = config.value
        config.value = str(value)
        config.save()
        
        # 记录变更日志
        if user:
            ConfigChangeLog.objects.create(
                key=key,
                old_value=old_value,
                new_value=str(value),
                changed_by=user
            )
        
        # 清理缓存
        CacheManager.invalidate_config_cache(key)
        
        # 发送通知
        ConfigChangeNotifier.notify_config_change(key, old_value, value)

# 配置变更日志模型
class ConfigChangeLog(models.Model):
    key = models.CharField(max_length=64)
    old_value = models.TextField()
    new_value = models.TextField()
    changed_by = models.ForeignKey(USER_MODEL)
    timestamp = models.DateTimeField(auto_now_add=True)
    ip_address = models.GenericIPAddressField()
```

### 3. 缓存策略优化

**统一的缓存管理**:
```python
class SitecfgCacheManager:
    CACHE_PREFIXES = {
        'config': 'sitecfg_config_',
        'banner': 'sitecfg_banner',
        'seo': 'sitecfg_seo_',
        'article': 'sitecfg_article_',
        'announcement': 'sitecfg_announcement_',
    }
    
    @classmethod
    def get_cache_key(cls, cache_type, identifier=''):
        prefix = cls.CACHE_PREFIXES.get(cache_type, 'sitecfg_')
        return f"{prefix}{identifier}"
    
    @classmethod
    def get_cached(cls, cache_type, identifier='', default=None):
        cache_key = cls.get_cache_key(cache_type, identifier)
        return cache.get(cache_key, default)
    
    @classmethod
    def set_cached(cls, cache_type, data, identifier='', timeout=3600):
        cache_key = cls.get_cache_key(cache_type, identifier)
        cache.set(cache_key, data, timeout)
    
    @classmethod
    def invalidate_cache(cls, cache_type, identifier=''):
        if identifier:
            cache_key = cls.get_cache_key(cache_type, identifier)
            cache.delete(cache_key)
        else:
            # 删除整个类型的缓存
            pattern = cls.CACHE_PREFIXES.get(cache_type, 'sitecfg_') + '*'
            cache.delete_pattern(pattern)
```

### 4. WebSocket通知集成

**实时配置更新通知**:
```python
def send_config_update_notification(config_key, old_value, new_value):
    """发送配置更新的实时通知"""
    notification = {
        'type': 'config_update',
        'config_key': config_key,
        'old_value': old_value,
        'new_value': new_value,
        'timestamp': timezone.now().isoformat()
    }
    
    # 发送给管理员
    ws_send_msg('admin_notifications', notification)
    
    # 如果是影响用户的配置，也发送给用户
    user_affecting_configs = ['maintenance', 'charge_rate', 'withdraw_count_limit']
    if config_key in user_affecting_configs:
        ws_send_msg('user_notifications', {
            'type': 'system_update',
            'message': f'系统配置已更新: {config_key}',
            'timestamp': timezone.now().isoformat()
        })
```

### 5. 管理界面API

**后台管理专用API**:
```python
class AdminConfigView(APIView):
    permission_classes = [IsAdminUser]
    
    def get(self, request):
        """获取所有配置项"""
        configs = SiteConfig.objects.all().select_related('category')
        data = []
        
        for config in configs:
            data.append({
                'key': config.key,
                'value': config.value,
                'data_type': config.data_type,
                'category': config.category.name if config.category else None,
                'description': config.description,
                'enable': config.enable,
                'last_modified': config.update_time
            })
        
        return Response(data)
    
    def put(self, request, key):
        """更新配置项"""
        config = get_object_or_404(SiteConfig, key=key)
        
        old_value = config.value
        new_value = request.data.get('value')
        
        # 验证配置值
        if not self.validate_config_value(config, new_value):
            return Response(
                {'error': '配置值格式不正确'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 更新配置
        ConfigManager.set_config(key, new_value, request.user)
        
        return Response({'message': '配置更新成功'})
```

## 总结

Sitecfg模块是整个系统的配置中心，负责网站的全局设置和内容管理。虽然功能相对完整，但在API设计、权限控制、缓存策略、数据模型等方面还有很大改进空间。建议：

1. **短期改进**: 统一API响应格式、添加权限控制、优化缓存策略
2. **中期重构**: 重新设计配置管理系统、完善多语言支持、添加管理界面
3. **长期优化**: 实现配置变更实时通知、添加配置依赖管理、完善审计系统

通过这些改进，可以将Sitecfg模块打造成一个更加安全、高效、易用的配置管理中心。
