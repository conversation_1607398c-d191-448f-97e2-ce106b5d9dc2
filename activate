#!/bin/bash
# 激活项目环境
PROJECT_ROOT="/www/wwwroot/csgoskins.com.cn"
cd "$PROJECT_ROOT/server"
source venv/bin/activate

echo "✅ 项目环境已激活"
echo "📍 项目根目录: $PROJECT_ROOT"
echo "📁 当前目录: $(pwd)"
echo "🐍 Python版本: $(python --version)"
echo "📦 虚拟环境: $VIRTUAL_ENV"
echo ""
echo "💡 可用命令:"
echo "  ./python    - 使用项目Python"
echo "  ./pip       - 使用项目pip"
echo "  ./manage    - Django管理命令"
echo ""

# 如果有参数，执行命令
if [ $# -gt 0 ]; then
    echo "🚀 执行命令: $@"
    exec "$@"
else
    # 启动新的bash会话
    exec bash
fi
