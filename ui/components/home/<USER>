<template>
  <div 
    class="hero-carousel relative overflow-hidden w-full"
    :key="renderKey"
    @mouseenter="pauseAutoplay"
    @mouseleave="resumeAutoplay"
  >
    <!-- 调试信息 (开发环境) -->
    <div v-if="$dev" class="absolute top-4 left-4 z-50 bg-black/80 text-white p-2 rounded text-sm">
      <div>API Slides: {{ props.slides.length }}</div>
      <div>Localized Slides: {{ localizedSlides.length }}</div>
      <div>Active Index: {{ activeIndex }}</div>
    </div>
    
    <!-- 轮播内容容器 -->
    <div class="carousel-container relative w-full">
      <div
        v-for="(slide, index) in localizedSlides"
        :key="`slide-${index}-${renderKey}`"
        class="carousel-slide w-full h-full transition-all duration-500 absolute inset-0"
        :class="{
          'z-20 opacity-100 visible': index === activeIndex,
          'opacity-0 invisible': index !== activeIndex
        }"
      >
        <!-- 背景效果层 -->
        <div class="absolute inset-0">
          <!-- 主背景色 -->
          <div 
            class="absolute inset-0" 
            :style="slide.background_class ? '' : 'background: linear-gradient(135deg, #1a202c, #2d3748, #1a202c)'"
          :class="slide.background_class || ''"
          ></div>
          
          <!-- 动态装饰元素 -->
          <div class="absolute top-0 -left-32 w-64 h-64 rounded-full blur-3xl bg-primary/10 animate-pulse"></div>
          <div class="absolute bottom-0 -right-32 w-64 h-64 rounded-full blur-3xl bg-secondary/10 animate-pulse" style="animation-delay: 1s;"></div>
          
          <!-- 噪点纹理 -->
          <div class="absolute inset-0 bg-noise opacity-30 mix-blend-soft-light"></div>
        </div>
        
        <!-- 内容区域 -->
        <div class="w-full h-full relative z-10">
          <!-- 简单图片模式 -->
          <div v-if="slide.is_simple" class="w-full h-full flex items-center justify-center">
            <div class="banner-image-wrapper relative w-full max-w-7xl">
              <NuxtImg 
                :src="slide.image" 
                :alt="slide.title || $t('common.banner_image')"
                class="simple-banner-image w-full h-full object-cover rounded-xl"
                :loading="index === 0 ? 'eager' : 'lazy'"
                format="webp"
                quality="85"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 90vw, 80vw"
              />
              
              <!-- 标题遮罩 -->
              <div 
                v-if="slide.title" 
                class="absolute bottom-0 left-0 right-0 p-6 flex justify-center"
                style="background: linear-gradient(to top, rgba(0,0,0,0.8), rgba(0,0,0,0.4), transparent)"
              >
                <span class="px-6 py-2 bg-black/60 backdrop-blur-sm text-white rounded-full text-lg font-semibold">
                  {{ slide.title }}
                </span>
              </div>
            </div>
          </div>
          
          <!-- 复杂内容模式 -->
          <div v-else class="content-slide flex items-center h-full relative overflow-hidden">
            <!-- 背景层 - 不使用图片作为背景 -->
            <div class="absolute inset-0 z-0">
              <!-- 纯色背景 -->
              <div 
                class="absolute inset-0" 
                :class="slide.background_class || 'bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900'"
              ></div>
              
              <!-- 动态光效背景 -->
              <div class="absolute inset-0 hero-bg-effects">
                <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-primary/20 rounded-full blur-3xl animate-pulse-slow"></div>
                <div class="absolute bottom-1/3 right-1/4 w-80 h-80 bg-secondary/15 rounded-full blur-3xl animate-pulse-slow" style="animation-delay: 2s;"></div>
                <div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-radial from-primary/10 to-transparent rounded-full animate-spin-very-slow"></div>
              </div>
              <!-- 噪点纹理 -->
              <div class="absolute inset-0 bg-noise opacity-30 mix-blend-soft-light"></div>
            </div>
            
            <!-- 内容布局 -->
            <div class="container mx-auto px-6 md:px-8 lg:px-12 relative z-10">
              <div class="grid grid-cols-1 lg:grid-cols-12 gap-8 lg:gap-16 items-center min-h-[32rem] lg:min-h-[36rem]">
                <!-- 文本内容区域 -->
                <div class="lg:col-span-7 text-center lg:text-left order-2 lg:order-1 space-y-6 lg:space-y-8">
                  <!-- 标签/分类 -->
                  <!-- <div v-if="slide.background_class" class="inline-flex items-center gap-2 px-4 py-2 bg-primary/20 backdrop-blur-sm border border-primary/30 rounded-full text-sm font-medium text-primary-200">
                    <Icon name="ph:star-fill" class="w-4 h-4" />
                    <span>{{ $t('common.featured') }}</span>
                  </div> -->
                  
                  <!-- 主标题 -->
                  <h1 class="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold leading-tight tracking-tight">
                    <span class="hero-title-gradient block">
                      {{ slide.title }}
                    </span>
                  </h1>
                  
                  <!-- 副标题/描述 -->
                  <div v-if="slide.description" class="space-y-4">
                    <p class="text-lg md:text-xl lg:text-2xl text-white/90 leading-relaxed max-w-2xl font-light">
                      {{ slide.description }}
                    </p>
                    
                    <!-- 特色点 -->
                    <!-- <div v-if="slide.secondary_button_text" class="flex flex-wrap gap-4 justify-center lg:justify-start">
                      <div class="flex items-center gap-2 text-sm text-white/70 bg-white/5 backdrop-blur-sm px-3 py-1.5 rounded-full border border-white/10">
                        <Icon name="ph:check-circle" class="w-4 h-4 text-primary" />
                        <span>{{ $t('common.premium_content') }}</span>
                      </div>
                      <div class="flex items-center gap-2 text-sm text-white/70 bg-white/5 backdrop-blur-sm px-3 py-1.5 rounded-full border border-white/10">
                        <Icon name="ph:check-circle" class="w-4 h-4 text-primary" />
                        <span>{{ $t('common.high_quality') }}</span>
                      </div>
                    </div> -->
                  </div>
                  
                  <!-- 行动按钮组 -->
                  <div class="flex flex-col sm:flex-row gap-4 lg:gap-6 justify-center lg:justify-start pt-4">
                    <!-- 主要按钮 -->
                    <NuxtLink 
                      v-if="slide.primary_button_text && slide.primary_button_link"
                      :to="slide.primary_button_link" 
                      class="hero-btn-primary group"
                    >
                      <span class="relative z-10">{{ slide.primary_button_text }}</span>
                      <Icon 
                        name="ph:arrow-right" 
                        class="w-5 h-5 ml-2 transition-all duration-300 group-hover:translate-x-1 group-hover:scale-110 relative z-10" 
                      />
                      <div class="absolute inset-0 bg-gradient-to-r from-blue-600 to-blue-500 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </NuxtLink>
                    
                    <!-- 次要按钮 -->
                    <NuxtLink 
                      v-if="slide.secondary_button_text && slide.secondary_button_link"
                      :to="slide.secondary_button_link" 
                      class="hero-btn-secondary group"
                    >
                      <span class="relative z-10">{{ slide.secondary_button_text }}</span>
                      <Icon 
                        name="ph:play-circle" 
                        class="w-5 h-5 ml-2 transition-all duration-300 group-hover:scale-110 relative z-10" 
                      />
                    </NuxtLink>
                  </div>
                  
                  <!-- 统计信息/社会证明 -->
                  <!-- <div v-if="slide.primary_button_text && slide.secondary_button_text" class="flex flex-wrap gap-8 justify-center lg:justify-start pt-8 border-t border-white/10">
                    <div class="text-center lg:text-left">
                      <div class="text-2xl lg:text-3xl font-bold text-primary">{{ $t('common.trusted_by') }}</div>
                      <div class="text-sm text-white/60 mt-1">{{ $t('common.thousands_users') }}</div>
                    </div>
                    <div class="text-center lg:text-left">
                      <div class="text-2xl lg:text-3xl font-bold text-secondary">{{ $t('common.premium') }}</div>
                      <div class="text-sm text-white/60 mt-1">{{ $t('common.quality_guaranteed') }}</div>
                    </div>
                  </div> -->
                </div>
                
                <!-- 箱子图片展示区域 -->
                <div class="lg:col-span-5 flex justify-center lg:justify-end order-1 lg:order-2">
                  <div class="relative w-80 h-80 sm:w-96 sm:h-96 lg:w-[28rem] lg:h-[28rem]">
                    <!-- 箱子图片容器 -->
                    <div class="case-image-container relative w-full h-full">
                      <!-- 背景光晕效果 -->
                      <div class="absolute inset-0 case-glow-effects">
                        <div 
                          class="absolute inset-0 rounded-full blur-3xl opacity-60 animate-pulse-slow"
                          :class="slide.glow_class || 'bg-gradient-radial from-primary/40 via-primary/20 to-transparent'"
                        ></div>
                        <div class="absolute inset-4 rounded-full blur-2xl bg-gradient-radial from-secondary/30 to-transparent opacity-40 animate-pulse-slow" style="animation-delay: 1.5s;"></div>
                      </div>
                      
                      <!-- 圆形旋转边框 -->
                      <div class="absolute inset-0 case-border-rings">
                        <!-- 外层圆形边框 - 主要旋转环 -->
                        <div class="absolute inset-0 rounded-full border-4 border-transparent bg-gradient-to-r from-primary via-secondary to-primary p-1 animate-spin-slow">
                          <div class="w-full h-full rounded-full bg-gray-900"></div>
                        </div>
                        
                        <!-- 中层圆形边框 -->
                        <div class="absolute inset-6 rounded-full border-2 border-dashed border-white/20 animate-reverse-spin"></div>
                        
                        <!-- 内层圆形边框 -->
                        <div class="absolute inset-12 rounded-full border border-primary/30 animate-spin-slow" style="animation-duration: 30s;"></div>
                        <div class="absolute inset-16 rounded-full border border-dotted border-secondary/20 animate-reverse-spin" style="animation-duration: 25s;"></div>
                      </div>
                      
                      <!-- 箱子图片 -->
                      <div class="absolute inset-12 lg:inset-16 flex items-center justify-center case-image-wrapper">
                        <div class="relative w-full h-full">
                          <NuxtImg 
                            v-if="slide.image"
                            :src="slide.image" 
                            :alt="slide.title || $t('common.case_image')"
                            class="w-full h-full object-contain drop-shadow-2xl animate-float filter brightness-110 contrast-110"
                            :loading="index === 0 ? 'eager' : 'lazy'"
                            format="webp"
                            quality="95"
                            :placeholder="[40, 40, 75, 5]"
                          />
                          <!-- 图片反光效果 -->
                          <div class="absolute inset-0 bg-gradient-to-tr from-transparent via-white/5 to-transparent opacity-60 pointer-events-none rounded-full"></div>
                        </div>
                      </div>
                      
                      <!-- 粒子装饰点 -->
                      <div class="absolute inset-0 case-particles pointer-events-none">
                        <div class="absolute -top-6 -right-6 w-12 h-12 bg-gradient-radial from-secondary/60 to-transparent rounded-full blur-md animate-ping-slow"></div>
                        <div class="absolute -bottom-8 left-12 w-8 h-8 bg-gradient-radial from-primary/50 to-transparent rounded-full blur-sm animate-ping-slow" style="animation-delay: 1s;"></div>
                        <div class="absolute top-12 -left-6 w-6 h-6 bg-gradient-radial from-green-400/40 to-transparent rounded-full blur-sm animate-ping-slow" style="animation-delay: 2s;"></div>
                        <div class="absolute top-1/3 right-4 w-4 h-4 bg-gradient-radial from-blue-400/40 to-transparent rounded-full blur-sm animate-ping-slow" style="animation-delay: 3s;"></div>
                        <div class="absolute bottom-1/4 left-1/4 w-3 h-3 bg-gradient-radial from-purple-400/40 to-transparent rounded-full blur-sm animate-ping-slow" style="animation-delay: 4s;"></div>
                      </div>
                      
                      <!-- 圆形射线效果 -->
                      <div class="absolute inset-0 case-rays overflow-hidden rounded-full opacity-30">
                        <div class="absolute top-1/2 left-1/2 w-px h-full bg-gradient-to-b from-transparent via-primary/60 to-transparent transform -translate-x-1/2 -translate-y-1/2 rotate-0 animate-spin-slow origin-center"></div>
                        <div class="absolute top-1/2 left-1/2 w-px h-full bg-gradient-to-b from-transparent via-secondary/40 to-transparent transform -translate-x-1/2 -translate-y-1/2 rotate-45 animate-spin-slow origin-center" style="animation-delay: 1s;"></div>
                        <div class="absolute top-1/2 left-1/2 w-px h-full bg-gradient-to-b from-transparent via-primary/40 to-transparent transform -translate-x-1/2 -translate-y-1/2 rotate-90 animate-spin-slow origin-center" style="animation-delay: 2s;"></div>
                        <div class="absolute top-1/2 left-1/2 w-px h-full bg-gradient-to-b from-transparent via-secondary/30 to-transparent transform -translate-x-1/2 -translate-y-1/2 rotate-135 animate-spin-slow origin-center" style="animation-delay: 3s;"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 指示器 -->
    <div class="absolute bottom-6 left-1/2 transform -translate-x-1/2 z-30">
      <div class="flex items-center gap-3 px-4 py-2 bg-black/70 backdrop-blur-md rounded-full border border-white/20">
        <button
          v-for="(_, index) in localizedSlides"
          :key="`indicator-${index}`"
          @click="setActiveSlide(index)"
          class="carousel-indicator transition-all duration-300 relative overflow-hidden"
          :class="{
            'w-8 h-2 bg-primary shadow-lg shadow-primary/50': index === activeIndex,
            'w-6 h-2 bg-white/40 hover:bg-white/60': index !== activeIndex
          }"
          :aria-label="$t('carousel.slide_indicator', { index: index + 1 })"
        >
          <!-- 进度条动画 -->
          <div 
            v-if="index === activeIndex && autoplay && !isPaused" 
            class="absolute inset-0 bg-white/30 animate-progress"
          ></div>
        </button>
      </div>
    </div>
    
    <!-- 导航箭头 -->
    <button
      @click="prev"
      class="carousel-arrow carousel-arrow-left group"
      :aria-label="$t('carousel.previous_slide')"
    >
      <Icon name="ph:caret-left" class="w-6 h-6 transition-transform group-hover:-translate-x-0.5" />
    </button>
    
    <button
      @click="next"
      class="carousel-arrow carousel-arrow-right group"
      :aria-label="$t('carousel.next_slide')"
    >
      <Icon name="ph:caret-right" class="w-6 h-6 transition-transform group-hover:translate-x-0.5" />
    </button>
    
    <!-- 底部渐变遮罩 -->
    <div class="absolute bottom-0 left-0 right-0 h-16 pointer-events-none" style="background: linear-gradient(to top, rgba(13, 15, 18, 0.8), transparent)"></div>
  </div>
</template>

<script setup lang="ts">
import type { BannerItem } from '~/types/banner'

// 定义组件属性
interface Props {
  slides: BannerItem[]
  autoplay?: boolean
  interval?: number
  height?: string
}

const props = withDefaults(defineProps<Props>(), {
  autoplay: true,
  interval: 5000,
  height: 'auto'
})

// 使用国际化
const { t, locale } = useI18n()

// 响应式状态
const activeIndex = ref(0)
const renderKey = ref(0)
const isPaused = ref(false)
let autoplayTimer: NodeJS.Timeout | null = null

// 本地化处理函数
const formatLocalizedItem = (item: any) => {
  if (!item) return null
  
  const currentLocale = locale.value === 'zh-hans' ? 'zh_hans' : locale.value
  const itemWithI18n = item as any
  
  // 获取本地化字段
  const localizedFields: any = {}
  
  // 处理标题
  if (currentLocale === 'zh_hans') {
    localizedFields.title = itemWithI18n.title_zh_hans || itemWithI18n.title_zh || item.title || ''
    localizedFields.description = itemWithI18n.description_zh_hans || itemWithI18n.description_zh || item.description || ''
    localizedFields.primary_button_text = itemWithI18n.primary_button_text_zh_hans || itemWithI18n.primary_button_text_zh || item.primary_button_text || ''
    localizedFields.secondary_button_text = itemWithI18n.secondary_button_text_zh_hans || itemWithI18n.secondary_button_text_zh || item.secondary_button_text || ''
  } else if (currentLocale === 'en') {
    localizedFields.title = itemWithI18n.title_en || item.title || ''
    localizedFields.description = itemWithI18n.description_en || item.description || ''
    localizedFields.primary_button_text = itemWithI18n.primary_button_text_en || item.primary_button_text || ''
    localizedFields.secondary_button_text = itemWithI18n.secondary_button_text_en || item.secondary_button_text || ''
  }
  
  return {
    ...item,
    ...localizedFields
  }
}

// 本地化轮播数据
const localizedSlides = computed(() => {
  return props.slides.map(slide => formatLocalizedItem(slide))
})

// 轮播控制方法
const next = () => {
  activeIndex.value = (activeIndex.value + 1) % props.slides.length
  resetTimer()
}

const prev = () => {
  activeIndex.value = activeIndex.value === 0 ? props.slides.length - 1 : activeIndex.value - 1
  resetTimer()
}

const setActiveSlide = (index: number) => {
  activeIndex.value = index
  resetTimer()
}

// 自动播放控制
const startAutoplay = () => {
  if (props.autoplay && props.slides.length > 1 && !isPaused.value) {
    autoplayTimer = setInterval(next, props.interval)
  }
}

const stopAutoplay = () => {
  if (autoplayTimer) {
    clearInterval(autoplayTimer)
    autoplayTimer = null
  }
}

const resetTimer = () => {
  stopAutoplay()
  startAutoplay()
}

const pauseAutoplay = () => {
  isPaused.value = true
  stopAutoplay()
}

const resumeAutoplay = () => {
  isPaused.value = false
  startAutoplay()
}

// 语言变更处理
const forceRerender = () => {
  renderKey.value++
}

// 键盘导航
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'ArrowLeft') {
    prev()
  } else if (event.key === 'ArrowRight') {
    next()
  }
}

// 生命周期管理
onMounted(() => {
  startAutoplay()
  
  // 监听语言变更
  if (process.client) {
    window.addEventListener('languageChanged', forceRerender)
    document.addEventListener('keydown', handleKeydown)
  }
})

onUnmounted(() => {
  stopAutoplay()
  
  if (process.client) {
    window.removeEventListener('languageChanged', forceRerender)
    document.removeEventListener('keydown', handleKeydown)
  }
})

// 监听语言变化
watch(locale, () => {
  forceRerender()
})

// SEO Meta
useSeoMeta({
  title: () => localizedSlides.value[activeIndex.value]?.title || t('common.hero_carousel'),
  description: () => localizedSlides.value[activeIndex.value]?.description || t('common.hero_carousel_desc')
})
</script>

<style lang="scss" scoped>
/* 轮播容器 */
.hero-carousel {
  min-height: 500px;
  aspect-ratio: 21/9;
  border-radius: var(--radius-lg);
  background: var(--gradient-dark);
  box-shadow: var(--shadow-xl);
  
  @media (max-width: 768px) {
    aspect-ratio: 16/9;
    min-height: 400px;
  }
  
  &:hover .carousel-arrow {
    opacity: 1;
    visibility: visible;
  }
}

.carousel-container {
  height: 100%;
  border-radius: inherit;
  overflow: hidden;
}

/* 轮播项 */
.carousel-slide {
  border-radius: inherit;
}

/* 简单模式样式 */
.banner-image-wrapper {
  height: 100%;
  border-radius: inherit;
  overflow: hidden;
}

.simple-banner-image {
  transition: transform var(--transition-slow) ease-out;
}

.hero-carousel:hover .simple-banner-image {
  transform: scale(1.02);
}

/* 内容模式样式 */
.content-slide {
  min-height: 500px;
  border-radius: inherit;
  
  @media (max-width: 768px) {
    min-height: 400px;
  }
}

/* 按钮样式 */
.hero-btn-primary {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 2rem;
  font-weight: 600;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  
  background: var(--gradient-primary);
  box-shadow: var(--neon-primary);
  color: white;
  text-decoration: none;
  
  &:hover {
    background: linear-gradient(135deg, var(--color-primary-dark), var(--color-primary-600));
    box-shadow: var(--neon-primary), var(--shadow-lg);
    transform: translateY(-2px);
  }
  
  &:active {
    transform: translateY(0);
  }
}

.hero-btn-secondary {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 2rem;
  background-color: transparent;
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  font-weight: 600;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(8px);
  text-decoration: none;
  
  &:hover {
    border-color: rgba(255, 255, 255, 0.5);
    background-color: rgba(255, 255, 255, 0.1);
  }
}

/* 导航箭头 */
.carousel-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 3rem;
  height: 3rem;
  backdrop-filter: blur(8px);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 30;
  opacity: 0;
  visibility: hidden;
  
  background-color: rgba(0, 0, 0, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.2);
  
  &:hover {
    background-color: rgba(0, 168, 255, 0.8);
    border-color: rgba(0, 168, 255, 0.5);
    box-shadow: var(--neon-primary);
  }
  
  &.carousel-arrow-left {
    left: 1rem;
    
    @media (min-width: 1024px) {
      left: 2rem;
    }
  }
  
  &.carousel-arrow-right {
    right: 1rem;
    
    @media (min-width: 1024px) {
      right: 2rem;
    }
  }
}

/* 指示器 */
.carousel-indicator {
  border-radius: 9999px;
  
  &:hover {
    transform: scaleY(1.2);
  }
}

/* 文本渐变 */
.hero-title-gradient {
  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  
  /* 添加文字动画效果 */
  animation: gradient-shift 8s ease-in-out infinite;
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* 边框渐变 */
.border-gradient-primary {
  border-image: linear-gradient(135deg, var(--color-primary), var(--color-secondary)) 1;
}

/* 径向渐变 */
.bg-gradient-radial {
  background: radial-gradient(circle, var(--tw-gradient-from), var(--tw-gradient-to));
}

/* 背景效果 */
.hero-bg-effects {
  pointer-events: none;
}

/* 视觉容器 */
.case-image-container {
  transform-style: preserve-3d;
  perspective: 1000px;
}

/* 光晕效果 */
.case-glow-effects {
  filter: blur(0);
  will-change: transform;
}

/* 装饰环系统 */
.case-border-rings {
  pointer-events: none;
  transform-style: preserve-3d;
}

/* 图片包装器 */
.case-image-wrapper {
  transform-style: preserve-3d;
  will-change: transform;
}

/* 粒子装饰 */
.case-particles {
  transform-style: preserve-3d;
}

/* 射线效果 */
.case-rays {
  mix-blend-mode: screen;
  transform-style: preserve-3d;
}

/* 噪点纹理 */
.bg-noise {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.9' numOctaves='1' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
  background-size: 200px;
}

/* 增强动画 */
@keyframes float {
  0%, 100% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(-10px) scale(1.02);
  }
}

@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes spin-very-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes reverse-spin {
  from {
    transform: rotate(360deg);
  }
  to {
    transform: rotate(0deg);
  }
}

@keyframes ping-slow {
  0% {
    transform: scale(0.8);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.4;
  }
  100% {
    transform: scale(0.8);
    opacity: 0.8;
  }
}

@keyframes pulse-slow {
  0%, 100% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes progress {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

/* 动画类 */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-spin-slow {
  animation: spin-slow 20s linear infinite;
}

.animate-spin-very-slow {
  animation: spin-very-slow 40s linear infinite;
}

.animate-reverse-spin {
  animation: reverse-spin 15s linear infinite;
}

.animate-ping-slow {
  animation: ping-slow 3s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse-slow 4s ease-in-out infinite;
}

.animate-progress {
  animation: progress v-bind(interval + 'ms') linear;
}
</style> 