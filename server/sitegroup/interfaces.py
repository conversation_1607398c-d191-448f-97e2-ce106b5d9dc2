"""
站群系统接口模块
提供给其他应用调用的接口函数
"""

import logging
from django.core.cache import cache

from sitegroup.models import Site
from sitegroup.business import get_site_by_domain, check_feature_enabled, get_site_config

_logger = logging.getLogger(__name__)


def get_current_site(request):
    """
    根据请求获取当前站点信息
    
    Args:
        request: Django请求对象
        
    Returns:
        Site对象或None
    """
    try:
        domain = request.get_host()
        site = Site.objects.filter(domain=domain, is_active=True).first()
        return site
    except Exception as e:
        _logger.error(f"Error getting current site: {e}")
        return None


def is_feature_enabled(request, feature_name):
    """
    检查当前站点是否启用指定功能
    
    Args:
        request: Django请求对象
        feature_name: 功能名称
        
    Returns:
        bool: 功能是否启用
    """
    try:
        domain = request.get_host()
        return check_feature_enabled(domain, feature_name)
    except Exception as e:
        _logger.error(f"Error checking feature {feature_name}: {e}")
        return True  # 出错时默认启用


def get_site_setting(request, setting_key, default_value=None):
    """
    获取当前站点的配置设置
    
    Args:
        request: Django请求对象
        setting_key: 配置键名
        default_value: 默认值
        
    Returns:
        配置值
    """
    try:
        domain = request.get_host()
        return get_site_config(domain, setting_key, default_value)
    except Exception as e:
        _logger.error(f"Error getting site setting {setting_key}: {e}")
        return default_value


def get_site_contents_for_domain(domain, content_type=None):
    """
    获取指定域名的内容列表
    
    Args:
        domain: 域名
        content_type: 内容类型（可选）
        
    Returns:
        QuerySet: 内容查询集
    """
    try:
        from articles.models import Content
        
        # 获取站点
        site = Site.objects.filter(domain=domain, is_active=True).first()
        
        if not site:
            # 如果站点不存在，返回全局内容
            contents = Content.objects.filter(
                target_sites__isnull=True,
                status='published'
            )
        else:
            # 获取指定站点的内容和全局内容
            site_specific = Content.objects.filter(
                target_sites=site,
                status='published'
            )
            
            global_contents = Content.objects.filter(
                target_sites__isnull=True,
                status='published'
            )
            
            # 合并并去重
            contents = (site_specific | global_contents).distinct()
        
        # 按内容类型过滤
        if content_type:
            contents = contents.filter(content_type=content_type)
        
        return contents.order_by('-is_pinned', '-priority', '-publish_date')
        
    except Exception as e:
        _logger.error(f"Error getting site contents for domain {domain}: {e}")
        from articles.models import Content
        return Content.objects.none()


def get_site_announcements(request):
    """
    获取当前站点的公告列表
    
    Args:
        request: Django请求对象
        
    Returns:
        QuerySet: 公告查询集
    """
    try:
        domain = request.get_host()
        
        # 检查公告功能是否启用
        if not check_feature_enabled(domain, 'announcements'):
            from articles.models import Content
            return Content.objects.none()
        
        return get_site_contents_for_domain(domain, 'announcement')
        
    except Exception as e:
        _logger.error(f"Error getting site announcements: {e}")
        from articles.models import Content
        return Content.objects.none()


def get_site_articles(request):
    """
    获取当前站点的文章列表
    
    Args:
        request: Django请求对象
        
    Returns:
        QuerySet: 文章查询集
    """
    try:
        domain = request.get_host()
        
        # 检查文章功能是否启用
        if not check_feature_enabled(domain, 'articles'):
            from articles.models import Content
            return Content.objects.none()
        
        return get_site_contents_for_domain(domain, 'article')
        
    except Exception as e:
        _logger.error(f"Error getting site articles: {e}")
        from articles.models import Content
        return Content.objects.none()


def register_user_to_site(user, request):
    """
    将用户注册到当前站点
    
    Args:
        user: 用户对象
        request: Django请求对象
        
    Returns:
        bool: 是否成功
    """
    try:
        domain = request.get_host()
        site = Site.objects.filter(domain=domain, is_active=True).first()
        
        if site:
            user.domain = domain
            user.registered_site = site
            user.save(update_fields=['domain', 'registered_site'])
            
            _logger.info(f"User {user.username} registered to site {domain}")
            return True
        else:
            # 如果站点不存在，只记录域名
            user.domain = domain
            user.save(update_fields=['domain'])
            
            _logger.warning(f"Site {domain} not found, only recorded domain for user {user.username}")
            return False
            
    except Exception as e:
        _logger.error(f"Error registering user to site: {e}")
        return False


def get_site_theme_config(request):
    """
    获取当前站点的主题配置
    
    Args:
        request: Django请求对象
        
    Returns:
        dict: 主题配置字典
    """
    try:
        site = get_current_site(request)
        
        if site and hasattr(site, 'theme'):
            return {
                'primary_color': site.primary_color,
                'secondary_color': site.secondary_color,
                'header_background': site.theme.header_background,
                'header_text_color': site.theme.header_text_color,
                'footer_background': site.theme.footer_background,
                'footer_text_color': site.theme.footer_text_color,
                'font_family': site.theme.font_family,
                'font_size_base': site.theme.font_size_base,
                'container_width': site.theme.container_width,
                'border_radius': site.theme.border_radius,
                'custom_css': site.custom_css,
            }
        else:
            # 返回默认主题配置
            return {
                'primary_color': '#007bff',
                'secondary_color': '#6c757d',
                'header_background': '#ffffff',
                'header_text_color': '#000000',
                'footer_background': '#f8f9fa',
                'footer_text_color': '#6c757d',
                'font_family': 'Arial, sans-serif',
                'font_size_base': 14,
                'container_width': '1200px',
                'border_radius': 4,
                'custom_css': '',
            }
            
    except Exception as e:
        _logger.error(f"Error getting site theme config: {e}")
        return {}


def is_site_in_maintenance(request):
    """
    检查当前站点是否处于维护模式
    
    Args:
        request: Django请求对象
        
    Returns:
        tuple: (是否维护模式, 维护消息)
    """
    try:
        site = get_current_site(request)
        
        if site:
            return site.maintenance_mode, site.maintenance_message or '网站正在维护中，请稍后访问'
        else:
            return False, ''
            
    except Exception as e:
        _logger.error(f"Error checking maintenance mode: {e}")
        return False, ''


def get_site_seo_info(request):
    """
    获取当前站点的SEO信息
    
    Args:
        request: Django请求对象
        
    Returns:
        dict: SEO信息字典
    """
    try:
        site = get_current_site(request)
        
        if site:
            return {
                'title': site.seo_title or site.name,
                'keywords': site.seo_keywords or '',
                'description': site.seo_description or '',
                'site_name': site.name,
                'subtitle': site.subtitle or '',
                'icp_number': site.icp_number or '',
                'logo_url': site.logo_url or '',
                'favicon_url': site.favicon_url or '',
            }
        else:
            # 返回默认SEO信息
            return {
                'title': 'CSGOSKINS - 专业的CSGO皮肤交易平台',
                'keywords': 'CSGO,皮肤,交易,开箱,对战',
                'description': 'CSGOSKINS是专业的CSGO皮肤交易平台，提供安全可靠的开箱和交易服务',
                'site_name': 'CSGOSKINS',
                'subtitle': '专业的CSGO皮肤交易平台',
                'icp_number': '',
                'logo_url': '',
                'favicon_url': '',
            }
            
    except Exception as e:
        _logger.error(f"Error getting site SEO info: {e}")
        return {}


# 向后兼容的函数别名
def get_seo_by_domain(domain):
    """向后兼容sitecfg.business中的函数"""
    try:
        site = Site.objects.filter(domain=domain, is_active=True).first()
        
        if site:
            return {
                'url': site.domain,
                'name': site.name,
                'subtitle': site.subtitle or '',
                'title': site.seo_title or site.name,
                'keywords': site.seo_keywords or '',
                'description': site.seo_description or '',
                'enable': site.is_active,
                'icp': site.icp_number or '',
                'news_enable': site.articles_enabled,
                'agent': site.site_group.agent if site.site_group else None
            }
        else:
            # 返回默认数据
            return {
                'url': domain,
                'name': 'CSGOSKINS',
                'subtitle': '专业的CSGO皮肤交易平台',
                'title': 'CSGOSKINS - 专业的CSGO皮肤交易平台',
                'keywords': 'CSGO,皮肤,交易,开箱,对战',
                'description': 'CSGOSKINS是专业的CSGO皮肤交易平台，提供安全可靠的开箱和交易服务',
                'enable': True,
                'icp': '',
                'news_enable': True,
                'agent': None
            }
            
    except Exception as e:
        _logger.error(f"Error in get_seo_by_domain: {e}")
        return {}
