<template>
  <div class="min-h-screen relative">
    <!-- 背景装饰 -->
    <div class="fixed inset-0 -z-10">
      <div class="absolute top-0 -left-32 w-64 h-64 rounded-full blur-3xl bg-primary/10 animate-pulse-slow"></div>
      <div class="absolute bottom-0 -right-32 w-64 h-64 rounded-full blur-3xl bg-secondary/10 animate-pulse-slow"></div>
      <div class="absolute inset-0 bg-grid-pattern opacity-20"></div>
      <div class="absolute inset-0 bg-noise opacity-40 mix-blend-soft-light"></div>
    </div>

    <!-- PC端内容 -->
    <div class="container mx-auto px-4 py-8 relative z-10" v-if="!isMobile">
      <!-- 页面头部 -->
      <div class="page-header text-center mb-12">
        <h1 class="text-4xl font-bold mb-4">
          <span class="text-gradient">{{ $t('inventory.title') }}</span>
        </h1>
        <p class="text-gray-300 max-w-2xl mx-auto">{{ $t('inventory.description') }}</p>
        
        <!-- 筛选和搜索 -->
        <div class="filter-section mt-8 flex justify-center items-center space-x-4">
          <div class="search-box">
            <i class="i-heroicons-magnifying-glass text-white/50"></i>
            <input 
              v-model="searchQuery" 
              type="text" 
              :placeholder="$t('inventory.search_placeholder')"
              class="search-input"
            />
          </div>
          <select v-model="currentCategory" class="category-select">
            <option value="all">{{ $t('inventory.categories.all') }}</option>
            <option value="rifles">{{ $t('inventory.categories.rifles') }}</option>
            <option value="pistols">{{ $t('inventory.categories.pistols') }}</option>
            <option value="knives">{{ $t('inventory.categories.knives') }}</option>
            <option value="gloves">{{ $t('inventory.categories.gloves') }}</option>
          </select>
          <select v-model="currentRarity" class="rarity-select">
            <option value="all">{{ $t('inventory.rarities.all') }}</option>
            <option value="common">{{ $t('inventory.rarities.common') }}</option>
            <option value="rare">{{ $t('inventory.rarities.rare') }}</option>
            <option value="epic">{{ $t('inventory.rarities.epic') }}</option>
            <option value="legendary">{{ $t('inventory.rarities.legendary') }}</option>
          </select>
        </div>
      </div>

      <!-- 统计信息 -->
      <div class="stats-section mb-8">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="i-heroicons-squares-2x2 text-2xl text-primary"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ totalItems }}</div>
              <div class="stat-label">{{ $t('inventory.total_items') }}</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <i class="i-heroicons-star text-2xl text-yellow-400"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ rareItems }}</div>
              <div class="stat-label">{{ $t('inventory.rare_items') }}</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <i class="i-heroicons-trophy text-2xl text-orange-400"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ legendaryItems }}</div>
              <div class="stat-label">{{ $t('inventory.legendary_items') }}</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <i class="i-heroicons-currency-dollar text-2xl text-green-400"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">${{ totalValue.toFixed(2) }}</div>
              <div class="stat-label">{{ $t('inventory.total_value') }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 物品网格 -->
      <div class="inventory-content">
        <div class="inventory-grid grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
          <div
            v-for="item in filteredItems"
            :key="item.id"
            class="inventory-item"
            :class="item.rarityClass"
            @click="selectItem(item)"
          >
            <div class="item-image">
              <img :src="item.image" :alt="item.name" />
              <div class="item-rarity-badge" :class="item.rarityClass">
                {{ item.rarity }}
              </div>
              <div class="item-actions">
                <button class="action-btn" @click.stop="sellItem(item)">
                  <i class="i-heroicons-currency-dollar"></i>
                </button>
                <button class="action-btn" @click.stop="tradeItem(item)">
                  <i class="i-heroicons-arrow-path"></i>
                </button>
              </div>
            </div>
            <div class="item-info">
              <div class="item-name">{{ item.name }}</div>
              <div class="item-category">{{ item.category }}</div>
              <div class="item-value" v-if="item.value">
                ${{ item.value.toFixed(2) }}
              </div>
            </div>
          </div>
        </div>
        
        <!-- 空状态 -->
        <div v-if="filteredItems.length === 0" class="empty-inventory text-center py-16">
          <div class="empty-icon mb-6">
            <i class="i-heroicons-squares-2x2 text-6xl text-gray-500"></i>
          </div>
          <div class="empty-text text-gray-400 text-lg mb-6">
            {{ $t('inventory.no_items') }}
          </div>
          <button class="btn-primary">{{ $t('inventory.start_opening') }}</button>
        </div>
      </div>
    </div>

    <!-- 移动端内容 -->
    <div class="container mx-auto px-4 py-4 relative z-10" v-else>
      <!-- 页面头部 -->
      <div class="page-header mb-6">
        <h1 class="text-2xl font-bold mb-2">{{ $t('inventory.title') }}</h1>
        <p class="text-gray-300 text-sm">{{ $t('inventory.description') }}</p>
        
        <!-- 筛选和搜索 -->
        <div class="filter-section mt-4 space-y-3">
          <div class="search-box">
            <i class="i-heroicons-magnifying-glass text-white/50"></i>
            <input 
              v-model="searchQuery" 
              type="text" 
              :placeholder="$t('inventory.search_placeholder')"
              class="search-input"
            />
          </div>
          <div class="filter-buttons flex space-x-2 overflow-x-auto pb-2">
            <button 
              v-for="category in categories"
              :key="category.value"
              @click="currentCategory = category.value"
              class="filter-btn-mobile whitespace-nowrap"
              :class="{ 'active': currentCategory === category.value }"
            >
              {{ $t(category.label) }}
            </button>
          </div>
          <div class="filter-buttons flex space-x-2 overflow-x-auto pb-2">
            <button 
              v-for="rarity in rarities"
              :key="rarity.value"
              @click="currentRarity = rarity.value"
              class="filter-btn-mobile whitespace-nowrap"
              :class="{ 'active': currentRarity === rarity.value }"
            >
              {{ $t(rarity.label) }}
            </button>
          </div>
        </div>
      </div>

      <!-- 统计信息 -->
      <div class="stats-section mb-6">
        <div class="grid grid-cols-2 gap-3">
          <div class="stat-card-mobile">
            <div class="stat-icon">
              <i class="i-heroicons-squares-2x2 text-lg text-primary"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ totalItems }}</div>
              <div class="stat-label">{{ $t('inventory.total_items') }}</div>
            </div>
          </div>
          <div class="stat-card-mobile">
            <div class="stat-icon">
              <i class="i-heroicons-currency-dollar text-lg text-green-400"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">${{ totalValue.toFixed(2) }}</div>
              <div class="stat-label">{{ $t('inventory.total_value') }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 物品列表 -->
      <div class="inventory-content">
        <div class="inventory-list space-y-3">
          <div
            v-for="item in filteredItems"
            :key="item.id"
            class="inventory-item-mobile"
            :class="item.rarityClass"
            @click="selectItem(item)"
          >
            <div class="item-image">
              <img :src="item.image" :alt="item.name" />
              <div class="item-rarity-badge" :class="item.rarityClass">
                {{ item.rarity }}
              </div>
            </div>
            <div class="item-info">
              <div class="item-name">{{ item.name }}</div>
              <div class="item-category">{{ item.category }}</div>
              <div class="item-value" v-if="item.value">
                ${{ item.value.toFixed(2) }}
              </div>
            </div>
            <div class="item-actions">
              <button class="action-btn-mobile" @click.stop="sellItem(item)">
                <i class="i-heroicons-currency-dollar"></i>
              </button>
              <button class="action-btn-mobile" @click.stop="tradeItem(item)">
                <i class="i-heroicons-arrow-path"></i>
              </button>
            </div>
          </div>
        </div>
        
        <!-- 空状态 -->
        <div v-if="filteredItems.length === 0" class="empty-inventory text-center py-12">
          <div class="empty-icon mb-4">
            <i class="i-heroicons-squares-2x2 text-4xl text-gray-500"></i>
          </div>
          <div class="empty-text text-gray-400 text-base mb-4">
            {{ $t('inventory.no_items') }}
          </div>
          <button class="btn-primary">{{ $t('inventory.start_opening') }}</button>
        </div>
      </div>
    </div>

    <!-- 物品详情模态框 -->
    <div v-if="selectedItem" class="item-modal fixed inset-0 z-50 flex items-center justify-center">
      <div class="modal-overlay absolute inset-0 bg-black/80 backdrop-blur-sm" @click="selectedItem = null"></div>
      <div class="modal-content bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-6 max-w-md w-full mx-4">
        <div class="modal-header flex items-center justify-between mb-4">
          <h3 class="text-white font-semibold">{{ selectedItem.name }}</h3>
          <button @click="selectedItem = null" class="close-btn">
            <i class="i-heroicons-x-mark w-5 h-5"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="item-preview mb-4">
            <img :src="selectedItem.image" :alt="selectedItem.name" class="w-full rounded-lg" />
          </div>
          <div class="item-details space-y-2">
            <div class="detail-item">
              <span class="detail-label">{{ $t('inventory.category') }}:</span>
              <span class="detail-value">{{ selectedItem.category }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">{{ $t('inventory.rarity') }}:</span>
              <span class="detail-value" :class="selectedItem.rarityClass">{{ selectedItem.rarity }}</span>
            </div>
            <div class="detail-item" v-if="selectedItem.value">
              <span class="detail-label">{{ $t('inventory.value') }}:</span>
              <span class="detail-value text-green-400">${{ selectedItem.value.toFixed(2) }}</span>
            </div>
          </div>
          <div class="modal-actions mt-6 flex space-x-3">
            <button class="btn-secondary flex-1" @click="sellItem(selectedItem)">
              {{ $t('inventory.sell') }}
            </button>
            <button class="btn-primary flex-1" @click="tradeItem(selectedItem)">
              {{ $t('inventory.trade') }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 国际化
const { t } = useI18n()

// 设备检测 - 使用响应式状态
const isMobile = ref(false)

// 在客户端检测设备类型
onMounted(() => {
  const checkMobile = () => {
    isMobile.value = window.innerWidth < 768
  }
  
  checkMobile()
  window.addEventListener('resize', checkMobile)
  
  onUnmounted(() => {
    window.removeEventListener('resize', checkMobile)
  })
})

// SEO配置
useSeoMeta({
  title: '物品库存 - CSGO开箱网站',
  description: '管理您的物品库存，查看收集的皮肤和武器',
  keywords: '物品库存,皮肤收集,武器库存,CSGO开箱',
  ogTitle: '物品库存 - CSGO开箱网站',
  ogDescription: '管理您的物品库存，查看收集的皮肤和武器',
  twitterTitle: '物品库存 - CSGO开箱网站',
  twitterDescription: '管理您的物品库存，查看收集的皮肤和武器'
})

// 搜索和筛选
const searchQuery = ref('')
const currentCategory = ref('all')
const currentRarity = ref('all')

// 分类选项
const categories = [
  { label: 'inventory.categories.all', value: 'all' },
  { label: 'inventory.categories.rifles', value: 'rifles' },
  { label: 'inventory.categories.pistols', value: 'pistols' },
  { label: 'inventory.categories.knives', value: 'knives' },
  { label: 'inventory.categories.gloves', value: 'gloves' }
]

// 稀有度选项
const rarities = [
  { label: 'inventory.rarities.all', value: 'all' },
  { label: 'inventory.rarities.common', value: 'common' },
  { label: 'inventory.rarities.rare', value: 'rare' },
  { label: 'inventory.rarities.epic', value: 'epic' },
  { label: 'inventory.rarities.legendary', value: 'legendary' }
]

// 选中的物品
const selectedItem = ref(null)

// 模拟物品数据
const items = ref([
  {
    id: 1,
    name: 'AK-47 | 血腥运动',
    image: '/demo/item1.png',
    category: '步枪',
    rarity: '稀有',
    rarityClass: 'rarity-rare',
    value: 25.50
  },
  {
    id: 2,
    name: 'M4A4 | 龙王',
    image: '/demo/item2.png',
    category: '步枪',
    rarity: '史诗',
    rarityClass: 'rarity-epic',
    value: 45.80
  },
  {
    id: 3,
    name: 'AWP | 龙王',
    image: '/demo/item3.png',
    category: '步枪',
    rarity: '传说',
    rarityClass: 'rarity-legendary',
    value: 125.00
  },
  {
    id: 4,
    name: 'Desert Eagle | 龙王',
    image: '/demo/item4.png',
    category: '手枪',
    rarity: '稀有',
    rarityClass: 'rarity-rare',
    value: 18.90
  },
  {
    id: 5,
    name: 'USP-S | 龙王',
    image: '/demo/item5.png',
    category: '手枪',
    rarity: '普通',
    rarityClass: 'rarity-common',
    value: 5.20
  },
  {
    id: 6,
    name: 'Glock-18 | 龙王',
    image: '/demo/item6.png',
    category: '手枪',
    rarity: '普通',
    rarityClass: 'rarity-common',
    value: 3.80
  },
  {
    id: 7,
    name: 'Karambit | 龙王',
    image: '/demo/item7.png',
    category: '刀具',
    rarity: '传说',
    rarityClass: 'rarity-legendary',
    value: 350.00
  },
  {
    id: 8,
    name: 'M9 Bayonet | 龙王',
    image: '/demo/item8.png',
    category: '刀具',
    rarity: '史诗',
    rarityClass: 'rarity-epic',
    value: 180.00
  }
])

// 计算属性
const filteredItems = computed(() => {
  let filtered = items.value
  
  // 按搜索词筛选
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(item => 
      item.name.toLowerCase().includes(query) ||
      item.category.toLowerCase().includes(query)
    )
  }
  
  // 按分类筛选
  if (currentCategory.value !== 'all') {
    filtered = filtered.filter(item => {
      switch (currentCategory.value) {
        case 'rifles': return item.category === '步枪'
        case 'pistols': return item.category === '手枪'
        case 'knives': return item.category === '刀具'
        case 'gloves': return item.category === '手套'
        default: return true
      }
    })
  }
  
  // 按稀有度筛选
  if (currentRarity.value !== 'all') {
    filtered = filtered.filter(item => {
      switch (currentRarity.value) {
        case 'common': return item.rarity === '普通'
        case 'rare': return item.rarity === '稀有'
        case 'epic': return item.rarity === '史诗'
        case 'legendary': return item.rarity === '传说'
        default: return true
      }
    })
  }
  
  return filtered
})

const totalItems = computed(() => items.value.length)

const rareItems = computed(() => 
  items.value.filter(item => item.rarity === '稀有').length
)

const legendaryItems = computed(() => 
  items.value.filter(item => item.rarity === '传说').length
)

const totalValue = computed(() => 
  items.value.reduce((sum, item) => sum + (item.value || 0), 0)
)

// 方法
const selectItem = (item: any) => {
  selectedItem.value = item
}

const sellItem = (item: any) => {
  console.log('出售物品:', item.name)
  // TODO: 实现出售功能
}

const tradeItem = (item: any) => {
  console.log('交易物品:', item.name)
  // TODO: 实现交易功能
}
</script>

<style scoped>
/* 页面头部样式 */
.page-header {
  text-align: center;
  position: relative;
}

.page-header::after {
  content: '';
  position: absolute;
  bottom: -1rem;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
  border-radius: 2px;
}

/* 搜索框样式 */
.search-box {
  @apply relative flex items-center;
}

.search-box i {
  @apply absolute left-3 text-sm;
}

.search-input {
  @apply pl-10 pr-4 py-2 rounded-xl bg-white/5 border border-white/10;
  @apply text-white placeholder-white/50;
  @apply focus:outline-none focus:border-primary focus:bg-white/10;
  @apply transition-all duration-300;
  min-width: 250px;
}

/* 筛选选择器样式 */
.category-select, .rarity-select {
  @apply px-4 py-2 rounded-xl bg-white/5 border border-white/10;
  @apply text-white focus:outline-none focus:border-primary;
  @apply transition-all duration-300;
}

/* 筛选按钮样式 */
.filter-btn-mobile {
  @apply px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300;
  @apply bg-white/5 border border-white/10 text-white/70;
  @apply hover:bg-white/10 hover:border-white/20 hover:text-white;
}

.filter-btn-mobile.active {
  @apply bg-primary/20 border-primary text-primary;
  @apply shadow-lg shadow-primary/25;
}

/* 统计卡片样式 */
.stat-card {
  @apply bg-white/5 backdrop-blur-md border border-white/10 rounded-2xl p-6;
  @apply hover:border-white/20 transition-all duration-300;
  @apply flex items-center space-x-4;
}

.stat-icon {
  @apply flex-shrink-0;
}

.stat-content {
  @apply flex-1;
}

.stat-value {
  @apply text-2xl font-bold text-white mb-1;
}

.stat-label {
  @apply text-white/60 text-sm;
}

.stat-card-mobile {
  @apply bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-4;
  @apply hover:border-white/20 transition-all duration-300;
  @apply flex items-center space-x-3;
}

.stat-card-mobile .stat-value {
  @apply text-lg font-bold text-white mb-1;
}

.stat-card-mobile .stat-label {
  @apply text-white/60 text-xs;
}

/* 物品网格样式 */
.inventory-item {
  @apply bg-white/5 backdrop-blur-md border border-white/10 rounded-xl overflow-hidden;
  @apply hover:border-white/20 transition-all duration-300;
  @apply hover:-translate-y-1 transform cursor-pointer;
}

.inventory-item.rarity-rare {
  @apply border-blue-500/30 hover:border-blue-500/50;
}

.inventory-item.rarity-epic {
  @apply border-purple-500/30 hover:border-purple-500/50;
}

.inventory-item.rarity-legendary {
  @apply border-orange-500/30 hover:border-orange-500/50;
}

.item-image {
  @apply relative w-full h-32 overflow-hidden;
}

.item-image img {
  @apply w-full h-full object-cover;
}

.item-rarity-badge {
  @apply absolute top-2 right-2 px-2 py-1 rounded-full text-xs font-medium;
}

.item-rarity-badge.rarity-rare {
  @apply bg-blue-500/20 text-blue-400 border border-blue-500/30;
}

.item-rarity-badge.rarity-epic {
  @apply bg-purple-500/20 text-purple-400 border border-purple-500/30;
}

.item-rarity-badge.rarity-legendary {
  @apply bg-orange-500/20 text-orange-400 border border-orange-500/30;
}

.item-rarity-badge.rarity-common {
  @apply bg-gray-500/20 text-gray-400 border border-gray-500/30;
}

.item-actions {
  @apply absolute inset-0 bg-black/50 opacity-0 hover:opacity-100;
  @apply flex items-center justify-center space-x-2;
  @apply transition-opacity duration-300;
}

.action-btn {
  @apply w-8 h-8 rounded-full bg-white/20 border border-white/30;
  @apply text-white hover:bg-white/30 transition-all duration-300;
  @apply flex items-center justify-center;
}

.item-info {
  @apply p-3;
}

.item-name {
  @apply text-white font-medium text-sm mb-1 truncate;
}

.item-category {
  @apply text-white/60 text-xs mb-1;
}

.item-value {
  @apply text-green-400 font-semibold text-xs;
}

/* 移动端物品样式 */
.inventory-item-mobile {
  @apply bg-white/5 backdrop-blur-md border border-white/10 rounded-xl overflow-hidden;
  @apply hover:border-white/20 transition-all duration-300;
  @apply flex items-center cursor-pointer;
}

.inventory-item-mobile.rarity-rare {
  @apply border-blue-500/30 hover:border-blue-500/50;
}

.inventory-item-mobile.rarity-epic {
  @apply border-purple-500/30 hover:border-purple-500/50;
}

.inventory-item-mobile.rarity-legendary {
  @apply border-orange-500/30 hover:border-orange-500/50;
}

.inventory-item-mobile .item-image {
  @apply w-16 h-16 flex-shrink-0;
}

.inventory-item-mobile .item-info {
  @apply p-3 flex-1;
}

.inventory-item-mobile .item-name {
  @apply text-white font-medium text-sm mb-1;
}

.inventory-item-mobile .item-category {
  @apply text-white/60 text-xs mb-1;
}

.inventory-item-mobile .item-value {
  @apply text-green-400 font-semibold text-xs;
}

.inventory-item-mobile .item-actions {
  @apply flex space-x-1 p-2;
}

.action-btn-mobile {
  @apply w-6 h-6 rounded bg-white/20 border border-white/30;
  @apply text-white hover:bg-white/30 transition-all duration-300;
  @apply flex items-center justify-center text-xs;
}

/* 模态框样式 */
.item-modal {
  @apply fixed inset-0 z-50 flex items-center justify-center;
}

.modal-overlay {
  @apply absolute inset-0 bg-black/80 backdrop-blur-sm;
}

.modal-content {
  @apply bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-6;
  @apply max-w-md w-full mx-4;
}

.modal-header {
  @apply flex items-center justify-between mb-4;
}

.close-btn {
  @apply w-8 h-8 rounded-full bg-white/20 border border-white/30;
  @apply text-white hover:bg-white/30 transition-all duration-300;
  @apply flex items-center justify-center;
}

.item-preview {
  @apply mb-4;
}

.item-preview img {
  @apply w-full rounded-lg;
}

.detail-item {
  @apply flex justify-between items-center;
}

.detail-label {
  @apply text-white/60 text-sm;
}

.detail-value {
  @apply text-white font-medium text-sm;
}

.modal-actions {
  @apply mt-6 flex space-x-3;
}

/* 空状态样式 */
.empty-inventory {
  @apply text-center py-16;
}

.empty-icon {
  @apply mb-6;
}

.empty-text {
  @apply text-gray-400 text-lg mb-6;
}

/* 按钮样式 */
.btn-primary {
  @apply bg-primary hover:bg-primary-dark text-white font-semibold py-3 px-6 rounded-xl;
  @apply transition-all duration-300 shadow-lg shadow-primary/25;
}

.btn-secondary {
  @apply px-4 py-2 rounded-xl text-sm font-medium;
  @apply bg-white/5 border border-white/10 text-white/70;
  @apply hover:bg-white/10 hover:border-white/20 hover:text-white;
  @apply transition-all duration-300;
}

/* 渐变文本 */
.text-gradient {
  @apply text-transparent bg-clip-text bg-gradient-to-r from-primary to-secondary;
}

/* 网格背景 */
.bg-grid-pattern {
  background-image: linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
}

/* 噪声背景 */
.bg-noise {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
}

/* 动画 */
.animate-pulse-slow {
  animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style> 