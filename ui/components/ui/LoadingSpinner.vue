<template>
  <div class="loading-spinner" :class="containerClass">
    <!-- CSGO霓虹风格 -->
    <div v-if="type === 'csgo'" class="csgo-loader">
      <div class="csgo-loader-inner"></div>
      <div v-if="showText" class="csgo-loader-text">{{ text || $t('common.loading') }}</div>
    </div>

    <!-- 三点跳跃风格 -->
    <div v-else-if="type === 'dots'" class="case-loader">
      <div class="case-loader-dot"></div>
      <div class="case-loader-dot"></div>
      <div class="case-loader-dot"></div>
    </div>

    <!-- 波纹脉冲风格 -->
    <div v-else-if="type === 'pulse'" class="pulse-loader">
      <div class="pulse-ring"></div>
      <div class="pulse-ring"></div>
      <div class="pulse-ring"></div>
      <div class="pulse-core"></div>
    </div>

    <!-- 极简旋转风格 -->
    <div v-else-if="type === 'simple'" class="simple-loader">
      <div class="simple-spinner"></div>
    </div>

    <!-- 星光闪烁风格 -->
    <div v-else-if="type === 'star'" class="star-loader">
      <div class="star-container">
        <div class="star" v-for="i in 6" :key="i"></div>
      </div>
    </div>

    <!-- 新增：骨架shimmer风格（来自old/v2） -->
    <div v-else-if="type === 'skeleton'" class="skeleton-loader">
      <div class="skeleton-bar"></div>
      <div class="skeleton-bar short"></div>
      <div class="skeleton-bar medium"></div>
      <div v-if="showText" class="skeleton-loader-text">{{ text || $t('common.loading') }}</div>
    </div>

    <!-- 新增：牛顿摆风格 -->
    <div v-else-if="type === 'newton'" class="newton-loader">
      <div class="newton-ball first"></div>
      <div class="newton-ball"></div>
      <div class="newton-ball"></div>
      <div class="newton-ball"></div>
      <div class="newton-ball last"></div>
    </div>

    <!-- 新增：电子风暴风格 -->
    <div v-else-if="type === 'electric'" class="electric-loader">
      <div class="electric-ring">
        <div class="electric-spark"></div>
        <div class="electric-spark"></div>
        <div class="electric-spark"></div>
      </div>
      <div v-if="showText" class="electric-text">{{ text || $t('common.loading') }}</div>
    </div>

    <!-- 新增：液体波浪风格 -->
    <div v-else-if="type === 'wave'" class="wave-loader">
      <div class="wave-container">
        <div class="wave"></div>
        <div class="wave"></div>
      </div>
      <div v-if="showText" class="wave-text">{{ text || $t('common.loading') }}</div>
    </div>

    <!-- 新增：魔方旋转风格 -->
    <div v-else-if="type === 'cube'" class="cube-loader">
      <div class="cube-face cube-front"></div>
      <div class="cube-face cube-back"></div>
      <div class="cube-face cube-right"></div>
      <div class="cube-face cube-left"></div>
      <div class="cube-face cube-top"></div>
      <div class="cube-face cube-bottom"></div>
    </div>

    <!-- 新增：彩虹圆环风格 -->
    <div v-else-if="type === 'rainbow'" class="rainbow-loader">
      <div class="rainbow-ring"></div>
      <div v-if="showText" class="rainbow-text">{{ text || $t('common.loading') }}</div>
    </div>

    <!-- 新增：DNA螺旋风格 -->
    <div v-else-if="type === 'dna'" class="dna-loader">
      <div class="dna-strand">
        <div class="dna-base"></div>
        <div class="dna-base"></div>
        <div class="dna-base"></div>
        <div class="dna-base"></div>
      </div>
    </div>

    <!-- 新增：心跳脉搏风格 -->
    <div v-else-if="type === 'heartbeat'" class="heartbeat-loader">
      <div class="heartbeat-line">
        <div class="pulse-wave"></div>
      </div>
      <div v-if="showText" class="heartbeat-text">{{ text || $t('common.loading') }}</div>
    </div>

    <!-- 默认CSGO风格 -->
    <div v-else class="csgo-loader">
      <div class="csgo-loader-inner"></div>
      <div v-if="showText" class="csgo-loader-text">{{ text || $t('common.loading') }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// 组件属性
interface Props {
  type?: 'csgo' | 'dots' | 'pulse' | 'simple' | 'star' | 'skeleton' | 'newton' | 'electric' | 'wave' | 'cube' | 'rainbow' | 'dna' | 'heartbeat'
  size?: 'small' | 'medium' | 'large'
  showText?: boolean
  text?: string
  containerClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  type: 'csgo',
  size: 'medium',
  showText: true,
  text: '',
  containerClass: ''
})

// 国际化
const { t } = useI18n()

// 计算属性
const containerClass = computed(() => {
  const classes = ['loading-spinner-container']
  
  // 尺寸类
  switch (props.size) {
    case 'small':
      classes.push('size-small')
      break
    case 'large':
      classes.push('size-large')
      break
    default:
      classes.push('size-medium')
  }
  
  // 自定义类
  if (props.containerClass) {
    classes.push(props.containerClass)
  }
  
  return classes
})
</script>

<style lang="scss" scoped>
/* Loading组件基础样式 */
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

.loading-spinner-container {
  /* 基础尺寸变量 */
  --size-small: 1.5rem;
  --size-medium: 3rem;
  --size-large: 4.5rem;
  
  /* 动画速度变量 */
  --speed-fast: 0.8s;
  --speed-normal: 1.2s;
  --speed-slow: 2s;
}

/* 尺寸类 */
.size-small {
  --current-size: var(--size-small);
  font-size: 0.75rem;
}

.size-medium {
  --current-size: var(--size-medium);
  font-size: 0.875rem;
}

.size-large {
  --current-size: var(--size-large);
  font-size: 1rem;
}

/* ===== 1. CSGO霓虹风格 ===== */
.csgo-loader {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
}

.csgo-loader-inner {
  width: var(--current-size);
  height: var(--current-size);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-top: 2px solid var(--color-primary);
  border-radius: 50%;
  animation: csgo-spin var(--speed-normal) linear infinite;
  position: relative;
}

.csgo-loader-inner::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0.5rem;
  height: 0.5rem;
  background: var(--color-primary);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 0 10px var(--color-primary);
  animation: csgo-pulse var(--speed-fast) ease-in-out infinite alternate;
}

.csgo-loader-text {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 500;
  letter-spacing: 0.05em;
  animation: text-fade 1.5s ease-in-out infinite;
}

@keyframes csgo-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes csgo-pulse {
  0% {
    opacity: 0.5;
    transform: translate(-50%, -50%) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.2);
  }
}

@keyframes text-fade {
  0%, 100% { opacity: 0.4; }
  50% { opacity: 0.8; }
}

/* ===== 2. 三点跳跃风格 ===== */
.case-loader {
  display: flex;
  gap: 0.375rem;
  align-items: center;
}

.case-loader-dot {
  width: calc(var(--current-size) * 0.2);
  height: calc(var(--current-size) * 0.2);
  background: var(--color-primary);
  border-radius: 50%;
  animation: case-loading-dots 1.5s ease-in-out infinite;
  box-shadow: 0 0 8px rgba(var(--color-primary-rgb), 0.4);
}

.case-loader-dot:nth-child(1) { animation-delay: 0s; }
.case-loader-dot:nth-child(2) { animation-delay: 0.2s; }
.case-loader-dot:nth-child(3) { animation-delay: 0.4s; }

@keyframes case-loading-dots {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* ===== 3. 波纹脉冲风格 ===== */
.pulse-loader {
  position: relative;
  width: var(--current-size);
  height: var(--current-size);
  display: flex;
  align-items: center;
  justify-content: center;
}

.pulse-ring {
  position: absolute;
  border: 2px solid var(--color-primary);
  border-radius: 50%;
  animation: pulse-expand 2s ease-out infinite;
}

.pulse-ring:nth-child(1) {
  width: 100%;
  height: 100%;
  animation-delay: 0s;
}

.pulse-ring:nth-child(2) {
  width: 80%;
  height: 80%;
  animation-delay: 0.5s;
}

.pulse-ring:nth-child(3) {
  width: 60%;
  height: 60%;
  animation-delay: 1s;
}

.pulse-core {
  width: 30%;
  height: 30%;
  background: var(--color-primary);
  border-radius: 50%;
  animation: pulse-core 1s ease-in-out infinite alternate;
}

@keyframes pulse-expand {
  0% {
    transform: scale(0.1);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

@keyframes pulse-core {
  0% { opacity: 0.8; }
  100% { opacity: 1; }
}

/* ===== 4. 极简旋转风格 ===== */
.simple-loader {
  width: var(--current-size);
  height: var(--current-size);
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid var(--color-primary);
  border-radius: 50%;
  animation: simple-spin var(--speed-normal) linear infinite;
}

.simple-spinner {
  width: var(--current-size);
  height: var(--current-size);
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid var(--color-primary);
  border-radius: 50%;
  animation: simple-spin var(--speed-normal) linear infinite;
}

.simple-text {
  font-size: var(--current-size);
  color: rgba(255, 255, 255, 0.7);
  font-weight: 400;
}

@keyframes simple-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ===== 5. 星光闪烁风格 ===== */
.star-loader {
  position: relative;
  width: var(--current-size);
  height: var(--current-size);
}

.star-container {
  position: relative;
  width: var(--current-size);
  height: var(--current-size);
}

.star {
  position: absolute;
  width: 4px;
  height: 4px;
  background: var(--color-primary);
  border-radius: 50%;
  box-shadow: 0 0 6px var(--color-primary);
  animation: star-sparkle 1.5s ease-in-out infinite;
}

.star:nth-child(1) {
  top: 0;
  left: 50%;
  animation-delay: 0s;
}

.star:nth-child(2) {
  top: 25%;
  right: 10%;
  animation-delay: 0.3s;
}

.star:nth-child(3) {
  top: 50%;
  right: 0;
  animation-delay: 0.6s;
}

.star:nth-child(4) {
  bottom: 25%;
  right: 10%;
  animation-delay: 0.9s;
}

.star:nth-child(5) {
  bottom: 0;
  left: 50%;
  animation-delay: 1.2s;
}

.star:nth-child(6) {
  top: 50%;
  left: 0;
  animation-delay: 1.5s;
}

.star-text {
  font-size: var(--current-size);
  color: rgba(255, 255, 255, 0.6);
  font-weight: 500;
  text-align: center;
}

@keyframes star-sparkle {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.2) rotate(180deg);
    opacity: 1;
    filter: brightness(1.5);
  }
}

/* ===== 6. 骨架shimmer风格（来自old/v2） ===== */
.skeleton-loader {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: calc(var(--current-size) * 2);
}

.skeleton-bar {
  height: calc(var(--current-size) * 0.2);
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(var(--color-primary-rgb), 0.15) 50%,
    rgba(255, 255, 255, 0.1) 100%
  );
  background-size: 200% 100%;
  border-radius: 0.25rem;
  animation: skeleton-shimmer var(--speed-slow) ease-in-out infinite;
}

.skeleton-bar.short {
  width: 60%;
}

.skeleton-bar.medium {
  width: 80%;
}

.skeleton-loader-text {
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.75em;
  margin-top: 0.5rem;
}

@keyframes skeleton-shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* ===== 7. 牛顿摆风格 ===== */
.newton-loader {
  display: flex;
  align-items: flex-end;
  gap: 2px;
  height: var(--current-size);
}

.newton-ball {
  width: calc(var(--current-size) * 0.15);
  height: calc(var(--current-size) * 0.15);
  background: var(--color-primary);
  border-radius: 50%;
  position: relative;
}

.newton-ball::before {
  content: "";
  position: absolute;
  top: calc(-1 * var(--current-size) * 0.8);
  left: 50%;
  width: 1px;
  height: calc(var(--current-size) * 0.8);
  background: rgba(255, 255, 255, 0.3);
  transform: translateX(-50%);
}

.newton-ball.first {
  animation: newton-left 1.5s ease-in-out infinite;
  transform-origin: 50% calc(-1 * var(--current-size) * 0.8);
}

.newton-ball.last {
  animation: newton-right 1.5s ease-in-out infinite;
  transform-origin: 50% calc(-1 * var(--current-size) * 0.8);
  animation-delay: 0.75s;
}

@keyframes newton-left {
  0%, 50% { transform: rotate(0deg); }
  25% { transform: rotate(-30deg); }
}

@keyframes newton-right {
  0%, 50% { transform: rotate(0deg); }
  25% { transform: rotate(30deg); }
}

/* ===== 8. 电子风暴风格 ===== */
.electric-loader {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
}

.electric-ring {
  position: relative;
  width: var(--current-size);
  height: var(--current-size);
  border: 2px solid transparent;
  border-radius: 50%;
  background: linear-gradient(45deg, transparent, var(--color-primary), transparent);
  animation: electric-rotate var(--speed-normal) linear infinite;
}

.electric-spark {
  position: absolute;
  width: 4px;
  height: 4px;
  background: var(--color-primary);
  border-radius: 50%;
  box-shadow: 0 0 10px var(--color-primary);
}

.electric-spark:nth-child(1) {
  top: -2px;
  left: 50%;
  animation: electric-spark1 1s ease-in-out infinite;
}

.electric-spark:nth-child(2) {
  right: -2px;
  top: 50%;
  animation: electric-spark2 1s ease-in-out infinite;
  animation-delay: 0.33s;
}

.electric-spark:nth-child(3) {
  bottom: -2px;
  left: 50%;
  animation: electric-spark3 1s ease-in-out infinite;
  animation-delay: 0.66s;
}

.electric-text {
  color: var(--color-primary);
  font-weight: 600;
  animation: electric-glow 1.5s ease-in-out infinite;
}

@keyframes electric-rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes electric-spark1 {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.3; transform: scale(1.5); }
}

@keyframes electric-spark2 {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.3; transform: scale(1.5); }
}

@keyframes electric-spark3 {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.3; transform: scale(1.5); }
}

@keyframes electric-glow {
  0%, 100% { text-shadow: 0 0 5px var(--color-primary); }
  50% { text-shadow: 0 0 20px var(--color-primary), 0 0 30px var(--color-primary); }
}

/* ===== 9. 液体波浪风格 ===== */
.wave-loader {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
}

.wave-container {
  position: relative;
  width: var(--current-size);
  height: var(--current-size);
  border-radius: 50%;
  background: rgba(var(--color-primary-rgb), 0.1);
  overflow: hidden;
}

.wave {
  position: absolute;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(var(--color-primary-rgb), 0.3) 50%,
    transparent 70%
  );
  animation: wave-flow 2s linear infinite;
}

.wave:nth-child(2) {
  animation-delay: 1s;
  opacity: 0.7;
}

.wave-text {
  color: var(--color-primary);
  font-weight: 500;
}

@keyframes wave-flow {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

/* ===== 10. 魔方旋转风格 ===== */
.cube-loader {
  position: relative;
  width: var(--current-size);
  height: var(--current-size);
  transform-style: preserve-3d;
  animation: cube-rotate 2s linear infinite;
}

.cube-face {
  position: absolute;
  width: 100%;
  height: 100%;
  background: rgba(var(--color-primary-rgb), 0.8);
  border: 1px solid var(--color-primary);
}

.cube-front  { transform: rotateY(0deg) translateZ(calc(var(--current-size) / 2)); }
.cube-back   { transform: rotateY(180deg) translateZ(calc(var(--current-size) / 2)); }
.cube-right  { transform: rotateY(90deg) translateZ(calc(var(--current-size) / 2)); }
.cube-left   { transform: rotateY(-90deg) translateZ(calc(var(--current-size) / 2)); }
.cube-top    { transform: rotateX(90deg) translateZ(calc(var(--current-size) / 2)); }
.cube-bottom { transform: rotateX(-90deg) translateZ(calc(var(--current-size) / 2)); }

@keyframes cube-rotate {
  0% { transform: rotateX(0deg) rotateY(0deg); }
  100% { transform: rotateX(360deg) rotateY(360deg); }
}

/* ===== 11. 彩虹圆环风格 ===== */
.rainbow-loader {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
}

.rainbow-ring {
  width: var(--current-size);
  height: var(--current-size);
  border-radius: 50%;
  background: conic-gradient(
    from 0deg,
    #ff0000,
    #ff8000,
    #ffff00,
    #80ff00,
    #00ff00,
    #00ff80,
    #00ffff,
    #0080ff,
    #0000ff,
    #8000ff,
    #ff00ff,
    #ff0080,
    #ff0000
  );
  animation: rainbow-spin var(--speed-normal) linear infinite;
  position: relative;
}

.rainbow-ring::before {
  content: "";
  position: absolute;
  top: 10%;
  left: 10%;
  right: 10%;
  bottom: 10%;
  background: #0d0f12;
  border-radius: 50%;
}

.rainbow-text {
  background: linear-gradient(45deg, #ff0000, #ff8000, #ffff00, #00ff00, #00ffff, #0000ff, #ff00ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 600;
  animation: rainbow-text 2s ease-in-out infinite;
}

@keyframes rainbow-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes rainbow-text {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; transform: scale(1.05); }
}

/* ===== 12. DNA螺旋风格 ===== */
.dna-loader {
  position: relative;
  width: var(--current-size);
  height: calc(var(--current-size) * 1.5);
}

.dna-strand {
  position: relative;
  width: 100%;
  height: 100%;
  animation: dna-rotate 2s linear infinite;
}

.dna-base {
  position: absolute;
  width: 6px;
  height: 6px;
  background: var(--color-primary);
  border-radius: 50%;
  left: 50%;
  transform: translateX(-50%);
}

.dna-base:nth-child(1) {
  top: 20%;
  animation: dna-pulse1 2s ease-in-out infinite;
}

.dna-base:nth-child(2) {
  top: 40%;
  animation: dna-pulse2 2s ease-in-out infinite;
}

.dna-base:nth-child(3) {
  top: 60%;
  animation: dna-pulse3 2s ease-in-out infinite;
}

.dna-base:nth-child(4) {
  top: 80%;
  animation: dna-pulse4 2s ease-in-out infinite;
}

@keyframes dna-rotate {
  0% { transform: rotateY(0deg); }
  100% { transform: rotateY(360deg); }
}

@keyframes dna-pulse1 {
  0%, 100% { transform: translateX(-50%) scale(1); }
  25% { transform: translateX(-80%) scale(1.2); }
  75% { transform: translateX(-20%) scale(0.8); }
}

@keyframes dna-pulse2 {
  0%, 100% { transform: translateX(-50%) scale(1); }
  25% { transform: translateX(-20%) scale(1.2); }
  75% { transform: translateX(-80%) scale(0.8); }
}

@keyframes dna-pulse3 {
  0%, 100% { transform: translateX(-50%) scale(1); }
  25% { transform: translateX(-80%) scale(1.2); }
  75% { transform: translateX(-20%) scale(0.8); }
}

@keyframes dna-pulse4 {
  0%, 100% { transform: translateX(-50%) scale(1); }
  25% { transform: translateX(-20%) scale(1.2); }
  75% { transform: translateX(-80%) scale(0.8); }
}

/* ===== 13. 心跳脉搏风格 ===== */
.heartbeat-loader {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
}

.heartbeat-line {
  position: relative;
  width: calc(var(--current-size) * 2);
  height: calc(var(--current-size) * 0.5);
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.pulse-wave {
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  height: 2px;
  background: var(--color-primary);
  transform: translateY(-50%);
  animation: heartbeat-pulse 1.5s ease-in-out infinite;
}

.pulse-wave::before {
  content: "";
  position: absolute;
  width: 20px;
  height: 20px;
  background: var(--color-primary);
  border-radius: 50%;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  box-shadow: 0 0 10px var(--color-primary);
  animation: heartbeat-dot 1.5s ease-in-out infinite;
}

.heartbeat-text {
  color: var(--color-primary);
  font-weight: 500;
  animation: heartbeat-text 1.5s ease-in-out infinite;
}

@keyframes heartbeat-pulse {
  0% { transform: translateY(-50%) scaleX(0); opacity: 0; }
  50% { transform: translateY(-50%) scaleX(1); opacity: 1; }
  100% { transform: translateY(-50%) scaleX(0); opacity: 0; }
}

@keyframes heartbeat-dot {
  0% { left: 0; }
  100% { left: calc(100% - 20px); }
}

@keyframes heartbeat-text {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 1; transform: scale(1.05); }
}

/* ===== 移动端适配 ===== */
@media (max-width: 768px) {
  .loading-spinner {
    gap: 0.5rem;
  }
  
  .size-small {
    --current-size: 1rem;
    font-size: 0.625rem;
  }
  
  .size-medium {
    --current-size: 2rem;
    font-size: 0.75rem;
  }
  
  .size-large {
    --current-size: 3rem;
    font-size: 0.875rem;
  }
  
  .case-loader {
    gap: 0.25rem;
  }
  
  .newton-loader {
    gap: 1px;
  }
}

/* ===== 硬件加速优化 ===== */
.csgo-loader-inner,
.simple-loader,
.star,
.electric-ring,
.cube-loader,
.rainbow-ring,
.dna-strand {
  will-change: transform;
}

.pulse-ring,
.wave,
.pulse-wave {
  will-change: transform, opacity;
}
</style> 