# Tests 测试脚本

本目录包含各种测试和验证脚本，用于确保系统功能正常运行。

## 脚本列表

### API测试

#### test_battle_api_i18n.py
- **功能**：测试对战API的国际化字段支持
- **测试范围**：
  - 箱子列表API的国际化字段（name、name_en、name_zh_hans等）
  - 房间详情API的国际化字段
  - 房间列表API的国际化字段
  - 饰品序列化器的国际化字段
- **运行方式**：
  ```bash
  cd /www/wwwroot/csgoskins.com.cn/server
  python ../scripts/tests/test_battle_api_i18n.py
  ```
- **测试结果**：输出详细的测试报告，包括每个API接口的字段验证结果

## 测试分类

### 1. 功能测试
- API接口功能验证
- 数据完整性检查
- 业务逻辑正确性验证

### 2. 性能测试
- API响应时间测试
- 数据库查询性能测试
- 并发处理能力测试

### 3. 集成测试
- 前后端接口对接测试
- 第三方服务集成测试
- 系统组件协同测试

### 4. 回归测试
- 修复后功能验证
- 版本升级后兼容性测试
- 配置变更后影响测试

## 测试执行规范

### 测试环境

1. **开发环境测试**
   ```bash
   export DJANGO_SETTINGS_MODULE="steambase.settings.dev"
   python ../scripts/tests/test_battle_api_i18n.py
   ```

2. **测试环境验证**
   ```bash
   export DJANGO_SETTINGS_MODULE="steambase.settings.test"
   python ../scripts/tests/test_battle_api_i18n.py
   ```

3. **预生产环境验证**
   ```bash
   export DJANGO_SETTINGS_MODULE="steambase.settings.staging"
   python ../scripts/tests/test_battle_api_i18n.py
   ```

### 测试数据准备

1. **测试数据库**
   ```bash
   # 创建测试数据
   python manage.py migrate --database=test
   python manage.py loaddata test_fixtures.json
   ```

2. **模拟数据**
   ```bash
   # 创建模拟用户和房间数据
   python manage.py create_test_data
   ```

3. **清理测试数据**
   ```bash
   # 测试完成后清理
   python manage.py flush --database=test
   ```

## 测试报告

### 自动化报告

测试脚本会自动生成报告，包含：

1. **测试概要**
   - 总测试数量
   - 通过/失败统计
   - 执行时间

2. **详细结果**
   - 每个测试用例的结果
   - 失败原因分析
   - 性能数据

3. **建议和修复**
   - 发现的问题
   - 修复建议
   - 优化建议

### 报告格式示例

```
========================================
API国际化字段测试报告
========================================
测试时间: 2025-06-28 14:30:00
测试环境: development
测试版本: v2.1.0

测试概要:
- 总测试数: 12
- 通过数: 12
- 失败数: 0
- 成功率: 100%

详细结果:
✓ 箱子列表API - 国际化字段检查 (0.15s)
✓ 房间详情API - 国际化字段检查 (0.23s)
✓ 房间列表API - 国际化字段检查 (0.18s)
...

性能分析:
- 平均响应时间: 0.18s
- 最慢接口: 房间详情API (0.23s)
- 数据库查询次数: 45

建议:
- 所有测试通过，API国际化支持正常
- 建议定期执行此测试以确保持续性
```

## 测试开发规范

### 新增测试脚本

1. **命名规范**
   - 使用 `test_` 前缀
   - 功能描述清晰
   - 使用下划线分隔

2. **文件结构**
   ```python
   #!/usr/bin/env python
   # -*- coding: utf-8 -*-
   """
   测试脚本：[测试功能描述]
   
   测试范围：
   - [测试项目1]
   - [测试项目2]
   
   作者：[作者名]
   创建时间：[创建日期]
   """
   
   import unittest
   import django
   import os
   import sys
   
   # Django环境设置
   sys.path.append('./')
   os.environ.setdefault("DJANGO_SETTINGS_MODULE", "steambase.settings")
   django.setup()
   
   class TestClassName(unittest.TestCase):
       def setUp(self):
           """测试前准备"""
           pass
       
       def tearDown(self):
           """测试后清理"""
           pass
       
       def test_function_name(self):
           """测试具体功能"""
           pass
   
   if __name__ == '__main__':
       unittest.main()
   ```

3. **测试原则**
   - 独立性：每个测试独立运行
   - 可重复：测试结果一致
   - 明确性：测试目的明确
   - 快速性：执行时间合理

### 测试用例设计

1. **正常流程测试**
   - 验证基本功能正常
   - 检查返回数据格式
   - 确认业务逻辑正确

2. **边界条件测试**
   - 空数据处理
   - 最大/最小值处理
   - 异常参数处理

3. **错误处理测试**
   - 无效输入处理
   - 网络异常处理
   - 数据库异常处理

4. **性能测试**
   - 响应时间测试
   - 并发处理测试
   - 资源使用测试

## 持续集成

### 自动化测试

1. **Git钩子集成**
   ```bash
   # 提交前自动测试
   #!/bin/bash
   cd /www/wwwroot/csgoskins.com.cn/server
   python ../scripts/tests/test_battle_api_i18n.py
   ```

2. **定时测试**
   ```bash
   # 每日自动测试 (crontab)
   0 2 * * * cd /www/wwwroot/csgoskins.com.cn/server && python ../scripts/tests/test_battle_api_i18n.py > /tmp/daily_test.log 2>&1
   ```

3. **部署后验证**
   ```bash
   # 部署后自动验证
   python ../scripts/tests/test_battle_api_i18n.py --production
   ```

### 测试报告集成

1. **邮件通知**
   - 测试失败时发送邮件
   - 定期发送测试报告

2. **日志记录**
   - 测试结果记录到日志
   - 性能数据持续监控

3. **告警机制**
   - 测试失败触发告警
   - 性能下降预警

## 常见问题

### 测试环境问题

1. **Django设置错误**
   - 检查DJANGO_SETTINGS_MODULE设置
   - 确认数据库配置正确

2. **数据库连接失败**
   - 检查数据库服务状态
   - 确认连接配置正确

3. **模块导入错误**
   - 检查Python路径设置
   - 确认依赖包安装完整

### 测试数据问题

1. **测试数据不一致**
   - 重新初始化测试数据
   - 检查数据迁移状态

2. **测试数据污染**
   - 使用独立测试数据库
   - 测试后清理数据

3. **数据权限问题**
   - 检查数据库用户权限
   - 确认测试数据访问权限

## 最佳实践

1. **测试数据管理**
   - 使用fixture文件
   - 保持测试数据最小化
   - 测试后及时清理

2. **测试执行效率**
   - 合理使用测试数据库
   - 避免不必要的网络请求
   - 优化测试用例设计

3. **测试维护**
   - 定期更新测试用例
   - 删除过时的测试
   - 保持测试文档更新

最后更新：2025-06-28
