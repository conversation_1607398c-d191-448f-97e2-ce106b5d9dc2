import json
import logging
import time

from django.conf import settings
from django.core.cache import cache
from django.contrib import messages
from django.db import transaction, connection
from django.db.models import Count
from django.http import HttpResponse
from django.utils import timezone
from datetime import timezone as tz
from django.utils.translation import gettext_lazy as _

from datetime import datetime as dt, datetime
from datetime import timed<PERSON><PERSON>

from openpyxl import Workbook

from libs.steamapi import SteamApi
from steambase.enums import B2CTradeState, B2CItemType, ZBTradeState
from steambase.redis_con import get_redis
from steambase.utils import pid_generator, steam3id_to_steamid64, parse_tradeurl
from b2ctrade.models import B2CTradeRecord, B2CItemlist, B2COfficialAccount
from b2ctrade.serializers import B2CTradeRecordSerializer, B2CItemlistSerializer
from package.business import get_user_inventory
from package.models import ItemInfo, ItemPrice, PackageItem, PackageState
from package.service.item import get_item_price
from b2ctrade.service.trade import get_zbt_item_price
# from sitecfg.interfaces import get_dota2_usd_balance_exchange_rate, get_csgo_usd_balance_exchange_rate
from sitecfg.interfaces import get_zbt_buy_max_rate

from b2ctrade.zbtservice.zbtapi import ZbtApi

_logger = logging.getLogger(__name__)


def b2ctrade_accept(modeladmin, request, queryset):
    try:
        with transaction.atomic():
            for item in queryset:
                item.state = B2CTradeState.Accepted.value
                item.zbt_state = ZBTradeState.Accepted.value
                item.save()
    except Exception as e:
        _logger.exception(e)

    messages.success(request, _('b2c trade accept complete'))


b2ctrade_accept.short_description = _('b2c trade accept')

# 取消订单状态，修改用户背包饰品的状态 
def b2ctrade_cancel(modeladmin, request, queryset):
    try:
        with transaction.atomic():
            for trade in queryset:
                trade.state = B2CTradeState.Cancelled.value
                trade.save()
                
                 # 通过 package_id 获取 PackageItem 实例
                if trade.package_id:  # 确保 trade 有 package_id 属性
                    try:
                        package_item = PackageItem.objects.get(id=trade.package_id)
                        package_item.state = PackageState.Available.value  # 或其他适当的状态
                        package_item.save()
                    except PackageItem.DoesNotExist:
                        _logger.warning(f"PackageItem with id {trade.package_id} does not exist.")
                    
                    
    except Exception as e:
        _logger.exception(e)
        messages.error(request, _('An error occurred during the process.'))
    else:
        messages.success(request, _('b2c trade cancel complete'))


b2ctrade_cancel.short_description = _('b2c trade cancel')


def zbt_buy(modeladmin, request, queryset):
    try:
        with transaction.atomic():
            app_key = settings.ZBT_APP_KEY
            for item in queryset:
                if item.state == B2CTradeState.WaitForBuy.value:
                    user = item.user
                    trade_url = item.trade_url
                    appid = item.item_info.appid
                    hash_name = item.item_info.market_hash_name
                    zbtapi = ZbtApi(app_key=app_key, appid=appid)
                    zbt_price, price, quantity = get_zbt_item_price(hash_name, appid)
                    price = item.item_price.price
                    if zbt_price:
                        max_price = round(price + price * get_zbt_buy_max_rate(), 2)
                        #max_price = round(zbt_price + zbt_price * get_zbt_buy_max_rate(), 2)
                        #max_price = round(zbt_price + zbt_price * 0.1, 2)
                        trade_uid = item.out_trade_no

                        data = zbtapi.buy_item(market_hash_name=hash_name, max_price=max_price,
                                               trade_url=trade_url, trade_uid=trade_uid)
                    # print('withdraw', data)
                    if not data or not zbt_price:
                        _logger.info('Withdraw busy item: {}.'.format(hash_name))
                        messages.warning(request, _('扎比特购买失败请稍后重试.'))
                        return

                    _data = data.get('data', 0)
                    buy_price = _data.get('buyPrice', 0)
                    order_id = _data.get('orderId', '')
                    if data.get('success', False):
                        item.zbt_orderid = order_id
                        item.buy_price = buy_price
                        item.state = B2CTradeState.Trading.value
                        item.zbt_state = ZBTradeState.WaitForSend.value
                        messages.success(request, _('zbt_buy complete'))
                    else:
                        error_code = str(data.get('errorCode', ''))
                        error_msg = data.get('errorMsg', '')
                        error_data = data.get('errorData', '')
                        zbt_error = ''.join([error_code, error_msg, error_data])
                        item.zbt_error = zbt_error
                        messages.warning(request, _('扎比特接口错误：{}'.format(zbt_error)))
                    item.save()
    except Exception as e:
        _logger.exception(e)


zbt_buy.short_description = _('zbt_buy')


def sync_b2c_market_price(modeladmin, request, queryset):
    try:
        with transaction.atomic():
            items = ItemInfo.objects.all()
            for item in items:
                appid = item.appid
                hash_name = item.market_hash_name
                zbtapi = ZbtApi(app_key=settings.ZBT_APP_KEY, appid=appid)
                price_data = zbtapi.get_item_list_price_data(keyword_list=[hash_name])
                if not price_data:
                    return
                price_dict = price_data.get(hash_name, {})
                zbt_price, price, quantity = 0, 0, 0
                if price_dict:
                    zbt_price = price_dict.get('price', 0)
                    quantity = price_dict.get('quantity', 0)
                    if appid == '570':
                        # price = zbt_price * get_dota2_usd_balance_exchange_rate() + (zbt_price * get_dota2_b2cmarket_withdraw_rate())
                        price = zbt_price
                    elif appid == '730':
                        # price = zbt_price * get_csgo_usd_balance_exchange_rate() + (zbt_price * get_csgo_b2cmarket_withdraw_rate())
                        price = zbt_price

                    item.item_price.price = round(price, 2)
                    item.item_price.save()

    except Exception as e:
        _logger.exception(e)

    messages.success(request, _('Sync b2c market price complete'))


sync_b2c_market_price.short_description = _('Sync b2c market price')


def sync_b2c_trade_bot(modeladmin, request, queryset):
    try:
        accounts = queryset.filter(enable=True)
        appid = '730'
        for account in accounts:
            user = account.user
            steamid = user.username
            records = B2CTradeRecord.objects.filter(account=user,
                                                    state__in=[B2CTradeState.WaitForBuy.value,
                                                               B2CTradeState.RequestBuy.value,
                                                               B2CTradeState.WaitUnlock.value,
                                                               B2CTradeState.RequestBuy.value,
                                                               B2CTradeState.TradeReady.value])
            record_asssetids = [i.assetid for i in records]
            steam_api = SteamApi(api_key=settings.API_KEY, steamid=user.username)
            rginventory = steam_api.rginventory(account.customid, appid='730', contextid='2')
            if not rginventory:
                continue
            inve = rginventory.get('rgInventory', None)
            # assertids = [i for i in inve]
            desc = rginventory.get('rgDescriptions', None)
            # print(len(record_asssetids), len(assertids))
            for assetid in inve:
                if assetid in record_asssetids:
                    continue
                item = inve[assetid]
                cls_insid = ''.join([item.get('classid', ''), '_', item.get('instanceid', '')])
                item_desc = desc.get(cls_insid, {})
                if item_desc.get('marketable', 0) == 1:
                    expire = item_desc.get('cache_expiration', '')
                else:
                    continue
                if expire:
                    expire = dt.strptime(expire, '%Y-%m-%dT%H:%M:%SZ')
                else:
                    expire = dt.now()
                item = ItemInfo.objects.filter(market_hash_name=item_desc.get('market_hash_name', '')).first()
                if item:
                    price = get_item_price(item_desc.get('market_hash_name', ''), appid=appid)
                    B2CTradeRecord.objects.create(account=user, state=B2CTradeState.WaitForBuy.value,
                                                  item_info=item, seller_tradeurl=user.asset.tradeurl,
                                                  assetid=assetid, amount=price,
                                                  expire_time=expire)
    except Exception as e:
        _logger.exception('{}'.format(e))
    messages.success(request, _('Sync complete'))


sync_b2c_trade_bot.short_description = _('Sync trade bot inventory')


def sync_b2c_trade_list(modeladmin, request, queryset):
    try:
        items = B2CItemlist.objects.filter(enable=True)

        account = B2COfficialAccount.objects.filter(enable=True).first()
        if not account:
            messages.error(request, _('Sync failed not search b2c account'))
            return
        appid = '730'
        user = account.user
        steamid = user.username
        # print(steamid, 'steamid')
        for item in items:
            hash_name = item.item_info.market_hash_name
            count = B2CTradeRecord.objects.filter(item_type=B2CItemType.Purchase.value,
                                                  item_info__market_hash_name=hash_name,
                                                  state=B2CTradeState.WaitForBuy.value).count()
            # print(count, item.count)
            if count < item.count:
                for i in range(item.count - count):
                    price = get_item_price(hash_name, appid=appid)
                    if price > 0:
                        B2CTradeRecord.objects.create(account=user, state=B2CTradeState.WaitForBuy.value,
                                                      item_info=item.item_info, seller_tradeurl=user.asset.tradeurl,
                                                      amount=price, item_type=B2CItemType.Purchase.value)
            elif count > item.count:
                for i in range(count - item.count):
                    record = B2CTradeRecord.objects.filter(account=user, state=B2CTradeState.WaitForBuy.value,
                                                           item_info=item.item_info,
                                                           item_type=B2CItemType.Purchase.value).first()
                    record.state = B2CTradeState.Cancelled.value
                    record.save()
    except Exception as e:
        _logger.exception('{}'.format(e))
    messages.success(request, _('Sync complete'))


sync_b2c_trade_bot.short_description = _('Sync trade bot inventory')


def get_steamid(obj):
    if obj.zbt_state in [ZBTradeState.Accepted.value]:
        if obj.user.asset.tradeurl:
            partner, token = parse_tradeurl(obj.user.asset.tradeurl)
            try:
                partner = int(partner)
            except ValueError:
                return "error"
            return steam3id_to_steamid64(int(partner))
        else:
            return '-'


def zbtrecord_export_to_excel(modeladmin, request, queryset):
    name = "ChargeStatistics-{}".format(datetime.now().strftime("%Y%m%d-%H%M%S"))  # 用于定义文件名, 格式为: app名.模型类名
    field_names = modeladmin.list_display
    field_names_trans = {
        "user": "用户",
        "steamid": "steamid",
        "market_hash_name": "市场名称",
        "market_name_cn": "市场名称（中文）",
        "amount": "金额",
        "buy_price": "购买价格",
        "state": "状态",
        "zbt_state": "扎比特交易状态",
        "create_time": "创建时间",
        "accept_time": "接受时间",
        "zbt_orderid": "扎比特订单号"
    }
    response = HttpResponse(content_type='application/msexcel')  # 定义响应内容类型
    response['Content-Disposition'] = f'attachment; filename={name}.xlsx'  # 定义响应数据格式
    wb = Workbook()  # 新建Workbook
    ws = wb.active  # 使用当前活动的Sheet表
    ws.append([field_names_trans.get(field, "") for field in field_names])  # 将模型字段名作为标题写入第一行

    # 遍历选中queryset
    for obj in queryset:
        set_attr = {
            'market_hash_name': obj.item_info.market_hash_name,
            'market_name_cn': obj.item_info.market_name_cn,
            'steamid': get_steamid(obj)
        }
        # # 设置自定义字段
        [setattr(obj, key, value) for key, value in set_attr.items()]
        data = []
        for field in field_names:
            if field == 'state':
                state_map = {
                    B2CTradeState.Initialed.value: "初始化",
                    B2CTradeState.Accepted.value: "已接受",
                    B2CTradeState.Cancelled.value: "已取消",
                    B2CTradeState.Trading.value: "交易中",
                    B2CTradeState.Cancelling.value: "正在取消",
                    B2CTradeState.PriceCancelled: "价格过高取消",
                    B2CTradeState.OutOfStock.value: "库存不足",
                    B2CTradeState.ZBTCancelled.value: "扎比特取消",
                }
                data.append(state_map[int(getattr(obj, field))])
            elif field == 'zbt_state':
                zbt_state_map = {
                    ZBTradeState.WaitForPay.value: "等待购买",
                    ZBTradeState.WaitForSend.value: "等待发送",
                    ZBTradeState.WaitForTrade.value: "等待交易",
                    ZBTradeState.Receive.value: "接受",
                    ZBTradeState.Accepted.value: "已接受",
                    ZBTradeState.Cancelled.value: "已取消"
                }
                data.append(zbt_state_map[int(getattr(obj, field))])
            elif field == 'create_time' or field == 'accept_time':
                if getattr(obj, field):
                    data.append(getattr(obj, field).astimezone(tz(timedelta(hours=8))).strftime("%Y-%m-%d %H:%M:%S"))
                else:
                    data.append(None)
            else:
                data.append(str(getattr(obj, field)))
        row = ws.append(data)
    wb.save(response)  # 将数据存入响应内容
    return response


zbtrecord_export_to_excel.short_description = _('扎比特取回记录导出Excel')
