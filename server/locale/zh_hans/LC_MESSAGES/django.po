# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#: b2ctrade/business.py:80
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-19 13:58+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: agent/business.py:489
#, python-brace-format
msgid "用户 {user.username} 提取饰品金额 {amount}，手续费 {fee}"
msgstr ""

#: agent/business.py:521
msgid "用户充值"
msgstr ""

#: agent/business.py:611
#, python-brace-format
msgid "代理商提现 {amount}"
msgstr ""

#: agent/models.py:10 agent/models.py:119 agent/models.py:133
msgid "User"
msgstr ""

#: agent/models.py:11
msgid "Agent Name"
msgstr ""

#: agent/models.py:12
msgid "Agent Phone"
msgstr ""

#: agent/models.py:13
msgid "Agent Email"
msgstr ""

#: agent/models.py:14
msgid "Agent Wechat"
msgstr ""

#: agent/models.py:15
msgid "Agent QQ"
msgstr ""

#: agent/models.py:16
msgid "Agent Alipay"
msgstr ""

#: agent/models.py:17
msgid "Agent Wechatpay"
msgstr ""

#: agent/models.py:18
msgid "Agent Bankcard"
msgstr ""

#: agent/models.py:19
msgid "Agent Bank"
msgstr ""

#: agent/models.py:20
msgid "Agent Bankname"
msgstr ""

#: agent/models.py:21 roll/models.py:93
msgid "Balance"
msgstr ""

#: agent/models.py:22 agent/models.py:115 agent/models.py:131
msgid "Enable"
msgstr ""

#: agent/models.py:23 agent/models.py:75 agent/models.py:101
#: agent/models.py:116
msgid "Create Time"
msgstr ""

#: agent/models.py:24 agent/models.py:76 agent/models.py:102
#: agent/models.py:117
msgid "Update Time"
msgstr ""

#: agent/models.py:25 agent/models.py:74 agent/models.py:100
msgid "Remark"
msgstr ""

#: agent/models.py:31
msgid "Agents"
msgstr ""

#: agent/models.py:63 tradeup/models.py:47
msgid "Amount"
msgstr ""

#: agent/models.py:65
msgid "Init"
msgstr ""

#: agent/models.py:66
msgid "Success"
msgstr ""

#: agent/models.py:67 charge/models.py:19
msgid "Failed"
msgstr ""

#: agent/models.py:68 articles/admin.py:109 articles/models.py:156
msgid "Status"
msgstr ""

#: agent/models.py:70
msgid "Alipay"
msgstr ""

#: agent/models.py:71
msgid "Wechatpay"
msgstr ""

#: agent/models.py:72
msgid "Bankcard"
msgstr ""

#: agent/models.py:73
msgid "Pay Type"
msgstr ""

#: agent/models.py:81
msgid "Agent Withdrawal Orders"
msgstr ""

#: agent/models.py:89
msgid "Balance Changed"
msgstr ""

#: agent/models.py:90
msgid "Balance Before"
msgstr ""

#: agent/models.py:91
msgid "Balance After"
msgstr ""

#: agent/models.py:93
msgid "User Recharge"
msgstr ""

#: agent/models.py:94
msgid "User Withdrawal"
msgstr ""

#: agent/models.py:95
msgid "Agent Withdrawal Order"
msgstr ""

#: agent/models.py:96
msgid "Other"
msgstr ""

#: agent/models.py:97
msgid "Type"
msgstr ""

#: agent/models.py:98
msgid "Out Trade No"
msgstr ""

#: agent/models.py:107
msgid "Agent Balance Records"
msgstr ""

#: agent/models.py:113 articles/models.py:139 sitecfg/models.py:187
#: templates/admin_backup/base_copy_backup.html:305
#: templates/admin_backup/base_jet_backup.html:289
msgid "Title"
msgstr ""

#: agent/models.py:124
msgid "Articles"
msgstr ""

#: agent/models.py:130 sitecfg/models.py:185
msgid "Name"
msgstr ""

#: agent/models.py:132
msgid "Site"
msgstr ""

#: agent/models.py:138
msgid "Article Categories"
msgstr ""

#: agent/views.py:67 agent/views.py:84 agent/views.py:103 agent/views.py:120
#: agent/views.py:137 agent/views.py:163 agent/views.py:210 agent/views.py:306
#: agent/views.py:320 agent/views.py:342 agent/views.py:357 agent/views.py:379
#: authentication/business.py:876 blindbox/views.py:39 blindbox/views.py:59
#: blindbox/views.py:78 blindbox/views.py:98 blindbox/views.py:119
#: blindbox/views.py:139 blindbox/views.py:163 blindbox/views.py:183
#: box/views.py:52 box/views.py:565 crash/views.py:135 crash/views.py:160
#: crash/views.py:185 crash/views.py:203 tradeup/views.py:70
#: tradeup/views.py:92 tradeup/views.py:115 tradeup/views.py:162
#: tradeup/views.py:188 tradeup/views.py:213 tradeup/views.py:238
#: tradeup/views.py:261 tradeup/views.py:283 tradeup/views.py:306
#: websocket/views.py:29
msgid "Exception"
msgstr ""

#: articles/admin.py:20 articles/admin.py:39
msgid "Content Count"
msgstr ""

#: articles/admin.py:35
msgid "Tag Preview"
msgstr ""

#: articles/admin.py:64
msgid "Basic Information"
msgstr ""

#: articles/admin.py:67
msgid "Classification"
msgstr ""

#: articles/admin.py:70
msgid "Publishing"
msgstr ""

#: articles/admin.py:73
msgid "Author & Editor"
msgstr ""

#: articles/admin.py:75
msgid "Only agent users can be selected as authors and editors"
msgstr ""

#: articles/admin.py:77
msgid "Media"
msgstr ""

#: articles/admin.py:81 sitecfg/models.py:199
msgid "SEO"
msgstr ""

#: articles/admin.py:85
msgid "Display Settings"
msgstr ""

#: articles/admin.py:89
msgid "Style Settings"
msgstr ""

#: articles/admin.py:123 articles/models.py:157
msgid "Priority"
msgstr ""

#: articles/admin.py:150
msgid "Mark selected contents as published"
msgstr ""

#: articles/admin.py:155
msgid "Mark selected contents as draft"
msgstr ""

#: articles/admin.py:160
msgid "Mark selected contents as featured"
msgstr ""

#: articles/admin.py:165
msgid "Remove featured from selected contents"
msgstr ""

#: articles/admin.py:181 articles/models.py:248
msgid "File Size"
msgstr "文件大小"

#: articles/apps.py:8
msgid "Articles & Announcements Management"
msgstr ""

#: articles/models.py:70 articles/models.py:91 articles/models.py:135
#: articles/models.py:242
msgid "Created At"
msgstr "创建时间"

#: articles/models.py:71 articles/models.py:92 articles/models.py:136
#: articles/models.py:243
msgid "Updated At"
msgstr "更新时间"

#: articles/models.py:72
msgid "Category Name"
msgstr "分类"

#: articles/models.py:73 articles/models.py:94 articles/models.py:140
msgid "Slug"
msgstr ""

#: articles/models.py:74 articles/models.py:250 sitecfg/models.py:189
msgid "Description"
msgstr ""

#: articles/models.py:75 package/models.py:57
msgid "Icon"
msgstr ""

#: articles/models.py:76 articles/models.py:95
msgid "Color"
msgstr ""

#: articles/models.py:76 articles/models.py:95
msgid "Hex color code"
msgstr ""

#: articles/models.py:77 articles/models.py:183
msgid "Sort Order"
msgstr ""

#: articles/models.py:78 articles/models.py:96
msgid "Is Active"
msgstr ""

#: articles/models.py:82
msgid "Content Categories"
msgstr ""

#: articles/models.py:93
msgid "Tag Name"
msgstr ""

#: articles/models.py:100
msgid "Content Tags"
msgstr ""

#: articles/models.py:111
msgid "Article"
msgstr ""

#: articles/models.py:112
msgid "Announcement"
msgstr ""

#: articles/models.py:113
msgid "News"
msgstr ""

#: articles/models.py:114
msgid "Guide"
msgstr ""

#: articles/models.py:115 sitecfg/models.py:93
msgid "FAQ"
msgstr ""

#: articles/models.py:116
msgid "Policy"
msgstr ""

#: articles/models.py:117
msgid "Notice"
msgstr ""

#: articles/models.py:121
msgid "Draft"
msgstr ""

#: articles/models.py:122
msgid "Published"
msgstr ""

#: articles/models.py:123
msgid "Archived"
msgstr ""

#: articles/models.py:124
msgid "Scheduled"
msgstr ""

#: articles/models.py:128
msgid "Low"
msgstr ""

#: articles/models.py:129 blindbox/models.py:12 blindbox/models.py:87
msgid "Normal"
msgstr ""

#: articles/models.py:130
msgid "High"
msgstr ""

#: articles/models.py:131
msgid "Urgent"
msgstr ""

#: articles/models.py:141 sitecfg/models.py:186
msgid "Subtitle"
msgstr ""

#: articles/models.py:142
msgid "Excerpt"
msgstr ""

#: articles/models.py:143
msgid "Brief description or summary"
msgstr ""

#: articles/models.py:144 sitecfg/models.py:62
msgid "Content"
msgstr ""

#: articles/models.py:147
msgid "Content Type"
msgstr ""

#: articles/models.py:152
msgid "Tags"
msgstr ""

#: articles/models.py:158
msgid "Is Featured"
msgstr ""

#: articles/models.py:159
msgid "Is Pinned"
msgstr ""

#: articles/models.py:162
msgid "Publish Date"
msgstr ""

#: articles/models.py:163
msgid "Expire Date"
msgstr ""

#: articles/models.py:166
msgid "Author"
msgstr ""

#: articles/models.py:169
msgid "Last Editor"
msgstr ""

#: articles/models.py:174
msgid "Featured Image"
msgstr ""

#: articles/models.py:175
msgid "Thumbnail"
msgstr ""

#: articles/models.py:178
msgid "SEO Title"
msgstr ""

#: articles/models.py:179
msgid "SEO Description"
msgstr ""

#: articles/models.py:180
msgid "SEO Keywords"
msgstr ""

#: articles/models.py:184
msgid "View Count"
msgstr ""

#: articles/models.py:185
msgid "Allow Comments"
msgstr ""

#: articles/models.py:188
msgid "Custom CSS Class"
msgstr ""

#: articles/models.py:189
msgid "Background Color"
msgstr ""

#: articles/models.py:190
msgid "Text Color"
msgstr ""

#: articles/models.py:197
msgid "Contents"
msgstr ""

#: articles/models.py:222
msgid "Editor must be an active agent user"
msgstr ""

#: articles/models.py:246
msgid "File Name"
msgstr ""

#: articles/models.py:247
msgid "File Path"
msgstr ""

#: articles/models.py:248
msgid "Size in bytes"
msgstr ""

#: articles/models.py:249
msgid "File Type"
msgstr ""

#: articles/models.py:251
msgid "Download Count"
msgstr ""

#: articles/models.py:255
msgid "Content Attachments"
msgstr ""

#: articles/models.py:265
msgid "IP Address"
msgstr ""

#: articles/models.py:266
msgid "User Agent"
msgstr ""

#: articles/models.py:267
msgid "Viewed At"
msgstr ""

#: articles/models.py:271
msgid "Content Views"
msgstr ""

#: authentication/admin.py:82
msgid "Permissions"
msgstr ""

#: authentication/admin.py:83
msgid "Important dates"
msgstr ""

#: authentication/admin.py:126
msgid "余额"
msgstr ""

#: authentication/admin.py:131
msgid "总充值余额"
msgstr ""

#: authentication/admin.py:135
msgid "昵称"
msgstr ""

#: authentication/admin.py:152
msgid "box_chance_type"
msgstr ""

#: authentication/apps.py:7
msgid "Account"
msgstr ""

#: authentication/business.py:92 authentication/business.py:100
#: package/business.py:227
msgid "Invalid trade url"
msgstr ""

#: authentication/business.py:106
msgid "User asset not found"
msgstr ""

#: authentication/business.py:108
msgid "No changes were detected in the URL you provided."
msgstr ""

#: authentication/business.py:140
msgid "Invalid trade URL format"
msgstr ""

#: authentication/business.py:151
msgid "This Steam link is already in use. Each link can bind only one account."
msgstr ""

#: authentication/business.py:156
msgid "Failed to update. An unknown error occurred."
msgstr ""

#: authentication/business.py:160
msgid ""
"Update failed. The system detected a security issue with this operation. "
"Please ensure your actions comply with security policies."
msgstr ""

#: authentication/business.py:186 authentication/business.py:215
#: authentication/business.py:242 authentication/business.py:275
msgid "Oops! Wrong CAPTCHA. Try again."
msgstr ""

#: authentication/business.py:219
msgid ""
"This phone number has already been bound to a different user. Please check "
"and retry."
msgstr ""

#: authentication/business.py:279
msgid "Oops! This email is already taken."
msgstr ""

#: authentication/business.py:295
msgid "Exceeded verification code sending limit. Please try again later."
msgstr ""

#: authentication/business.py:307
msgid ""
"Failed to send verification code. Please ensure your network connection is "
"stable and try again."
msgstr ""

#: authentication/business.py:339
msgid "Username must be 2–10 characters. Please adjust and try again.\""
msgstr ""

#: authentication/business.py:342
msgid ""
"Error: Username cannot contain a phone number. Please try another username."
msgstr ""

#: authentication/business.py:345 authentication/business.py:492
#: authentication/business.py:626 authentication/business.py:891
msgid "API request failed due to missing parameters."
msgstr ""

#: authentication/business.py:347 authentication/business.py:496
#: authentication/business.py:628 authentication/business.py:898
msgid "Error: Password must be 8 to 16 characters."
msgstr ""

#: authentication/business.py:353
msgid ""
"Security alert: High registration activity detected from this IP. Please try "
"again later."
msgstr ""

#: authentication/business.py:360 authentication/business.py:648
msgid "Too many verification code attempts. Please try again later."
msgstr ""

#: authentication/business.py:367
msgid "Verification failed: Code expired."
msgstr ""

#: authentication/business.py:371 authentication/business.py:512
#: authentication/business.py:659 authentication/business.py:920
#: authentication/business.py:1198 authentication/business.py:1344
msgid "Verification failed: Incorrect code."
msgstr ""

#: authentication/business.py:375
msgid "The token does not match. Please try again."
msgstr ""

#: authentication/business.py:377
msgid ""
"Token or verification code validation failed. Please ensure you have entered "
"the correct information and try again."
msgstr ""

#: authentication/business.py:379
msgid "Verification failed: Token or code has expired."
msgstr ""

#: authentication/business.py:388
msgid "Error: Phone number already in use. Please try another one."
msgstr ""

#: authentication/business.py:429
msgid "Invalid phone number."
msgstr ""

#: authentication/business.py:431
msgid "You must enter a password to proceed."
msgstr ""

#: authentication/business.py:436
msgid ""
"System interception: We have detected bulk registration attempts or "
"malicious activity. Please comply with our terms of service and avoid such "
"behavior."
msgstr ""

#: authentication/business.py:458 authentication/business.py:871
msgid "Login successful"
msgstr ""

#: authentication/business.py:461 authentication/business.py:873
msgid "Invalid username or password. Please try again."
msgstr ""

#: authentication/business.py:464 authentication/business.py:860
#: authentication/business.py:903 authentication/business.py:950
msgid "This account does not exist."
msgstr ""

#: authentication/business.py:503 authentication/business.py:655
#: authentication/business.py:911 authentication/business.py:1189
msgid "Verification code is expired. Please try again with a new one."
msgstr ""

#: authentication/business.py:519
msgid "该账号不存在"
msgstr ""

#: authentication/business.py:525 authentication/business.py:928
msgid "Password reset successful"
msgstr ""

#: authentication/business.py:632
msgid "Username length cannot exceed 10 characters."
msgstr ""

#: authentication/business.py:641
msgid ""
"High registration activity detected from this IP. Please try again later."
msgstr ""

#: authentication/business.py:666 authentication/business.py:766
msgid "Email already registered"
msgstr ""

#: authentication/business.py:754 authentication/business.py:1031
#: authentication/business.py:1240
msgid "Invalid verification type"
msgstr ""

#: authentication/business.py:761
msgid "Email service not configured"
msgstr ""

#: authentication/business.py:768 authentication/business.py:1069
msgid "User not found"
msgstr ""

#: authentication/business.py:792 authentication/business.py:1062
msgid "Please wait 5 minutes before requesting another code."
msgstr ""

#: authentication/business.py:807
msgid "Failed to send verification code"
msgstr ""

#: authentication/business.py:809
msgid "Email service not available"
msgstr ""

#: authentication/business.py:814 authentication/business.py:1099
msgid "System error while sending verification code."
msgstr ""

#: authentication/business.py:837
msgid "CAPTCHA cannot be empty"
msgstr ""

#: authentication/business.py:839
msgid "Missing CAPTCHA identifier"
msgstr ""

#: authentication/business.py:841
msgid "Invalid email address"
msgstr ""

#: authentication/business.py:843
msgid "Password cannot be empty"
msgstr ""

#: authentication/business.py:894 authentication/business.py:1248
msgid "Invalid email format"
msgstr ""

#: authentication/business.py:952
msgid "You are not allowed to change your nickname"
msgstr ""

#: authentication/business.py:954
msgid "Username must be 2–16 characters. Please adjust and try again."
msgstr ""

#: authentication/business.py:964
msgid "Please log in first"
msgstr ""

#: authentication/business.py:966
msgid "You are not allowed to change your avatar"
msgstr ""

#: authentication/business.py:968
msgid "Image data is empty"
msgstr ""

#: authentication/business.py:970
msgid "Only jpeg, jpg, and png files are supported"
msgstr ""

#: authentication/business.py:976
msgid "User profile does not exist"
msgstr ""

#: authentication/business.py:990
msgid "Error occurred while uploading avatar"
msgstr ""

#: authentication/business.py:1035
msgid "Token is required"
msgstr ""

#: authentication/business.py:1040
msgid "Invalid or expired token"
msgstr ""

#: authentication/business.py:1042
msgid "Token already used"
msgstr ""

#: authentication/business.py:1048
msgid "Token validation failed"
msgstr ""

#: authentication/business.py:1057
msgid "SMS service not configured"
msgstr ""

#: authentication/business.py:1067
msgid "Phone number already registered"
msgstr ""

#: authentication/business.py:1096
msgid "Failed to send verification code."
msgstr ""

#: authentication/business.py:1154 authentication/business.py:1171
msgid "User does not exist"
msgstr ""

#: authentication/business.py:1159
msgid "Congratulations on passing the exam"
msgstr ""

#: authentication/business.py:1159
msgid "Unfortunately, you did not pass the exam. Please retake it."
msgstr ""

#: authentication/business.py:1174 authentication/business.py:1215
#: authentication/business.py:1316
msgid "An error occurred"
msgstr ""

#: authentication/business.py:1182
msgid "This email does not exist"
msgstr ""

#: authentication/business.py:1242
msgid "Contact information cannot be empty"
msgstr ""

#: authentication/business.py:1246
msgid "Invalid phone number format"
msgstr ""

#: authentication/business.py:1258
msgid "Invalid client"
msgstr ""

#: authentication/business.py:1264
msgid "Website not allowed"
msgstr ""

#: authentication/business.py:1271
msgid "Under maintenance"
msgstr ""

#: authentication/business.py:1279
msgid "Exceeded daily token limit"
msgstr ""

#: authentication/business.py:1291
msgid "Too many requests from the same IP for different contact methods"
msgstr ""

#: authentication/business.py:1312
msgid "Token generated successfully"
msgstr ""

#: authentication/business.py:1335
msgid "Verification code parameters are incomplete"
msgstr ""

#: authentication/business.py:1340
msgid "Verification code expired. Please refresh."
msgstr ""

#: authentication/business.py:1348
msgid "Verification successful"
msgstr ""

#: authentication/models.py:28
msgid "nick name"
msgstr ""

#: authentication/models.py:29
msgid "avatar"
msgstr ""

#: authentication/models.py:34
msgid "Profile"
msgstr ""

#: authentication/models.py:42
msgid "Steam trade url"
msgstr ""

#: authentication/models.py:43 roll/models.py:98
msgid "balance"
msgstr ""

#: authentication/models.py:44
msgid "points"
msgstr ""

#: authentication/models.py:45 package/models.py:238
msgid "Diamond"
msgstr ""

#: authentication/models.py:46
msgid "active point"
msgstr ""

#: authentication/models.py:47
msgid "total charge balance"
msgstr ""

#: authentication/models.py:49
msgid "daily charge limit"
msgstr ""

#: authentication/models.py:51
msgid "total withdraw balance"
msgstr ""

#: authentication/models.py:55
msgid "Asset"
msgstr ""

#: authentication/models.py:63 package/models.py:324
msgid "Steamid"
msgstr ""

#: authentication/models.py:64
msgid "Steam name"
msgstr ""

#: authentication/models.py:65
msgid "profile url"
msgstr ""

#: authentication/models.py:66
msgid "small avatar"
msgstr ""

#: authentication/models.py:67
msgid "medium avatar"
msgstr ""

#: authentication/models.py:68
msgid "big avatar"
msgstr ""

#: authentication/models.py:69
msgid "Steam level"
msgstr ""

#: authentication/models.py:70
msgid "own games count"
msgstr ""

#: authentication/models.py:71
msgid "Dota2 playtime"
msgstr ""

#: authentication/models.py:75
msgid "Steam"
msgstr ""

#: authentication/models.py:83
msgid "Box chance A"
msgstr ""

#: authentication/models.py:84
msgid "Box chance B"
msgstr ""

#: authentication/models.py:85
msgid "Box chance C"
msgstr ""

#: authentication/models.py:86
msgid "Box chance D"
msgstr ""

#: authentication/models.py:87
msgid "Box chance E"
msgstr ""

#: authentication/models.py:90
msgid "Lucky Box Rate Type A"
msgstr ""

#: authentication/models.py:91
msgid "Lucky Box Rate Type B"
msgstr ""

#: authentication/models.py:92
msgid "Lucky Box Rate Type C"
msgstr ""

#: authentication/models.py:93
msgid "Lucky Box Rate Type D"
msgstr ""

#: authentication/models.py:94
msgid "Lucky Box Rate Type E"
msgstr ""

#: authentication/models.py:97
msgid "box chance type"
msgstr ""

#: authentication/models.py:98
msgid "box free count"
msgstr ""

#: authentication/models.py:99
msgid "box free last"
msgstr ""

#: authentication/models.py:100
msgid "box free level 0 count"
msgstr ""

#: authentication/models.py:101
msgid "box free level 0 last"
msgstr ""

#: authentication/models.py:102
msgid "ban chat"
msgstr ""

#: authentication/models.py:103 package/models.py:374
msgid "ban deposit"
msgstr ""

#: authentication/models.py:104 package/models.py:375
msgid "ban withdraw"
msgstr ""

#: authentication/models.py:105 package/models.py:376
msgid "ban exchange"
msgstr ""

#: authentication/models.py:106 package/models.py:377
msgid "ban shop"
msgstr ""

#: authentication/models.py:107
msgid "ban roll room"
msgstr ""

#: authentication/models.py:108
msgid "ban create roll room"
msgstr ""

#: authentication/models.py:109
msgid "ban charge room"
msgstr ""

#: authentication/models.py:110
msgid "box free give count"
msgstr ""

#: authentication/models.py:111
msgid "freebox lv1 count"
msgstr ""

#: authentication/models.py:112
msgid "freebox lv2 count"
msgstr ""

#: authentication/models.py:113
msgid "freebox lv3 count"
msgstr ""

#: authentication/models.py:114
msgid "freebox lv4 count"
msgstr ""

#: authentication/models.py:115
msgid "freebox lv5 count"
msgstr ""

#: authentication/models.py:116
msgid "freebox lv6 count"
msgstr ""

#: authentication/models.py:117
msgid "freebox lv7 count"
msgstr ""

#: authentication/models.py:118
msgid "freebox lv8 count"
msgstr ""

#: authentication/models.py:119
msgid "freebox lv9 count"
msgstr ""

#: authentication/models.py:120
msgid "freebox lv10 count"
msgstr ""

#: authentication/models.py:121
msgid "freebox lv1 limit"
msgstr ""

#: authentication/models.py:122
msgid "freebox lv2 limit"
msgstr ""

#: authentication/models.py:123
msgid "freebox lv3 limit"
msgstr ""

#: authentication/models.py:124
msgid "freebox lv4 limit"
msgstr ""

#: authentication/models.py:125
msgid "freebox lv5 limit"
msgstr ""

#: authentication/models.py:126
msgid "freebox lv6 limit"
msgstr ""

#: authentication/models.py:127
msgid "freebox lv7 limit"
msgstr ""

#: authentication/models.py:128
msgid "freebox lv8 limit"
msgstr ""

#: authentication/models.py:129
msgid "freebox lv9 limit"
msgstr ""

#: authentication/models.py:130
msgid "freebox lv10 limit"
msgstr ""

#: authentication/models.py:131
msgid "luckybox rate type"
msgstr ""

#: authentication/models.py:132
msgid "box promotion level"
msgstr ""

#: authentication/models.py:134
msgid "ban withdraw reason"
msgstr ""

#: authentication/models.py:135
msgid "ban deposit reason"
msgstr ""

#: authentication/models.py:137
msgid "ban battle"
msgstr ""

#: authentication/models.py:139
msgid "ban battle reason"
msgstr ""

#: authentication/models.py:141
msgid "ban rename"
msgstr ""

#: authentication/models.py:143
msgid "locked box chance"
msgstr ""

#: authentication/models.py:145
msgid "exam passed"
msgstr ""

#: authentication/models.py:146
msgid "exam time"
msgstr ""

#: authentication/models.py:148
msgid "ban avatar"
msgstr ""

#: authentication/models.py:151
msgid "profit limit"
msgstr ""

#: authentication/models.py:152
msgid "loss limit"
msgstr ""

#: authentication/models.py:158
msgid "Extra"
msgstr ""

#: authentication/models.py:168 authentication/models.py:170
#: authentication/models.py:178 authentication/models.py:180
#: authentication/models.py:188 authentication/models.py:190
#: authentication/models.py:198 authentication/models.py:200
#: authentication/models.py:208 authentication/models.py:210
#: authentication/models.py:218 authentication/models.py:220
#: authentication/models.py:228 authentication/models.py:230
#: authentication/models.py:238 authentication/models.py:240
#: authentication/models.py:248 authentication/models.py:250
#: authentication/models.py:258 authentication/models.py:260
#: authentication/models.py:268
msgid "Invalid count change"
msgstr ""

#: authentication/models.py:271
msgid "Opening limit exceeded for this level case."
msgstr ""

#: authentication/models.py:277 authentication/models.py:292
#: authentication/models.py:307 authentication/models.py:322
msgid "changed"
msgstr ""

#: authentication/models.py:278 authentication/models.py:293
#: authentication/models.py:308 authentication/models.py:323
msgid "change before"
msgstr ""

#: authentication/models.py:279 authentication/models.py:294
#: authentication/models.py:309 authentication/models.py:324
msgid "change after"
msgstr ""

#: authentication/models.py:280 authentication/models.py:295
#: authentication/models.py:310 authentication/models.py:325
#: authentication/models.py:699 withdraw/models.py:78
msgid "reason"
msgstr ""

#: authentication/models.py:284
msgid "User Balance Record"
msgstr ""

#: authentication/models.py:299
msgid "User Points Record"
msgstr ""

#: authentication/models.py:314
msgid "User Diamond Record"
msgstr ""

#: authentication/models.py:329
msgid "User Active Point Record"
msgstr ""

#: authentication/models.py:370
msgid "user uid"
msgstr ""

#: authentication/models.py:371
msgid "user name"
msgstr ""

#: authentication/models.py:372
msgid "email address"
msgstr ""

#: authentication/models.py:373 authentication/models.py:681
msgid "phone"
msgstr ""

#: authentication/models.py:374
msgid "date joined"
msgstr ""

#: authentication/models.py:375 sitecfg/models.py:184
msgid "Domain"
msgstr ""

#: authentication/models.py:376
msgid "registered ip"
msgstr ""

#: authentication/models.py:377
msgid "login ip"
msgstr ""

#: authentication/models.py:378
msgid "login domain"
msgstr ""

#: authentication/models.py:379
msgid "login time"
msgstr ""

#: authentication/models.py:382
msgid "note"
msgstr ""

#: authentication/models.py:383
msgid "is vip"
msgstr ""

#: authentication/models.py:390
msgid "staff status"
msgstr ""

#: authentication/models.py:392
msgid "Designates whether the user can log into this admin site."
msgstr ""

#: authentication/models.py:394
msgid "agent status"
msgstr ""

#: authentication/models.py:396 promotion/models.py:42
msgid "active"
msgstr ""

#: authentication/models.py:399
msgid ""
"Designates whether this user should be treated as active. Unselect this "
"instead of deleting accounts."
msgstr ""

#: authentication/models.py:404
msgid "用户锁定原因"
msgstr ""

#: authentication/models.py:475
msgid "Invalid balance change"
msgstr ""

#: authentication/models.py:518
msgid "Invalid points change"
msgstr ""

#: authentication/models.py:540
msgid "Invalid diamond change"
msgstr ""

#: authentication/models.py:557
msgid "Invalid active point change"
msgstr ""

#: authentication/models.py:573 authentication/models.py:584
#: authentication/models.py:638
msgid "No enough count"
msgstr ""

#: authentication/models.py:618
msgid "Invalid total charge balance change"
msgstr ""

#: authentication/models.py:644
msgid "Auth User"
msgstr ""

#: authentication/models.py:657 b2ctrade/models.py:211 b2ctrade/models.py:254
#: box/models.py:273 box/models.py:297 box/models.py:387 box/models.py:402
#: box/models.py:418 charge/models.py:64 charge/models.py:103
#: crash/models.py:65 crash/models.py:89 crash/models.py:118
#: crash/models.py:202 crash/models.py:297 package/models.py:392
#: package/models.py:421 roll/models.py:122 roll/models.py:146
#: roll/models.py:203 roll/models.py:263 tradeup/models.py:110
#: tradeup/models.py:134 tradeup/models.py:163 tradeup/models.py:247
#: tradeup/models.py:342 withdraw/models.py:100 withdraw/models.py:143
msgid "date"
msgstr ""

#: authentication/models.py:658 b2ctrade/models.py:23 b2ctrade/models.py:174
#: charge/models.py:277 tradeup/models.py:97
msgid "count"
msgstr ""

#: authentication/models.py:676
msgid "User Statistics Day"
msgstr ""

#: authentication/models.py:684
msgid "code"
msgstr ""

#: authentication/models.py:685
msgid "ip"
msgstr ""

#: authentication/models.py:686
msgid "domain"
msgstr ""

#: authentication/models.py:694
msgid "Phone Code Record"
msgstr ""

#: authentication/models.py:698 withdraw/models.py:39
msgid "steam id"
msgstr ""

#: authentication/models.py:700 authentication/models.py:712
#: b2ctrade/models.py:25 b2ctrade/models.py:173 b2ctrade/models.py:191
#: blindbox/models.py:41 blindbox/models.py:130 box/models.py:18
#: box/models.py:32 box/models.py:61 box/models.py:262 box/models.py:360
#: box/models.py:470 charge/models.py:248 charge/models.py:326
#: chat/models.py:65 chat/models.py:82 lottery/models.py:22
#: lottery/models.py:72 package/models.py:326 package/models.py:348
#: package/models.py:361 package/models.py:373 package/models.py:479
#: roll/models.py:180 sitecfg/models.py:25 sitecfg/models.py:74
#: sitecfg/models.py:88 sitecfg/models.py:104 sitecfg/models.py:123
#: sitecfg/models.py:190 sitecfg/models.py:209 tradeup/models.py:99
#: tradeup/models.py:403
msgid "enable"
msgstr ""

#: authentication/models.py:707
msgid "Steam Black List"
msgstr ""

#: authentication/models.py:711
msgid "suffix"
msgstr ""

#: authentication/models.py:718
msgid "Email Suffix"
msgstr ""

#: authentication/pipeline.py:85 authentication/pipeline.py:88
#: authentication/pipeline.py:93
msgid "This Steam account is already in bind."
msgstr ""

#: authentication/service/admin_actions.py:39
msgid "Send fail, no admin message settings"
msgstr ""

#: authentication/service/admin_actions.py:42
msgid "Send message request complete"
msgstr ""

#: authentication/service/admin_actions.py:44
msgid "Admin send sms message"
msgstr ""

#: authentication/service/admin_actions.py:54
msgid "User Chance Type Change To A"
msgstr ""

#: authentication/service/admin_actions.py:56
msgid "User Chance Type Change To B"
msgstr ""

#: authentication/service/admin_actions.py:59
msgid "Change User Chance A B"
msgstr ""

#: authentication/service/admin_actions.py:94
msgid "用户导出EXCEL"
msgstr ""

#: authentication/service/admin_actions.py:121
msgid "余额记录导出EXCEL"
msgstr ""

#: b2ctrade/apps.py:7
msgid "ZBT取回"
msgstr ""

#: b2ctrade/business.py:83 b2ctrade/business.py:1137
msgid "系统接口维护中，请稍后再试"
msgstr ""

#: b2ctrade/business.py:101
msgid "提现数量超过最大限制"
msgstr ""

#: b2ctrade/business.py:104 b2ctrade/business.py:1160
msgid "提取饰品需要至少充值{}金币"
msgstr ""

#: b2ctrade/business.py:109 b2ctrade/business.py:1212
msgid "饰品不存在或不可提取"
msgstr ""

#: b2ctrade/business.py:111
msgid "This skin can only be sold."
msgstr ""

#: b2ctrade/business.py:115 b2ctrade/business.py:1220
msgid "请提取{}金币以上的饰品"
msgstr ""

#: b2ctrade/business.py:157 b2ctrade/business.py:218 b2ctrade/business.py:454
#: package/business.py:177 package/business.py:222 package/business.py:324
#: package/business.py:380 package/business.py:421 roll/business.py:65
#: roll/business.py:245 withdraw/business.py:35 withdraw/business.py:100
#: withdraw/business.py:105 withdraw/business.py:127 withdraw/business.py:132
#: withdraw/business.py:156 withdraw/business.py:243 withdraw/business.py:248
msgid "Access denied: You are not authorized to perform this action."
msgstr ""

#: b2ctrade/business.py:160 b2ctrade/business.py:221 b2ctrade/business.py:457
#: b2ctrade/business.py:1133 box/business.py:279 charge/views.py:358
#: envelope/views.py:42 roll/views.py:477
msgid "Please set a real and valid Steam link."
msgstr ""

#: b2ctrade/business.py:168
msgid "取回数量超过限制"
msgstr ""

#: b2ctrade/business.py:170
msgid "提取饰品需要至少充值{}$"
msgstr ""

#: b2ctrade/business.py:172
msgid "Please select a weapon skin to extract."
msgstr ""

#: b2ctrade/business.py:228
msgid "Over withdarw max limit at sametime"
msgstr ""

#: b2ctrade/business.py:235
msgid "Can not withdraw your deposit item"
msgstr ""

#: b2ctrade/business.py:256
msgid "Withdraw busy please try later."
msgstr ""

#: b2ctrade/business.py:310 b2ctrade/business.py:464 b2ctrade/business.py:500
#: b2ctrade/business.py:502 withdraw/business.py:164
msgid "Invalid trade"
msgstr ""

#: b2ctrade/business.py:312
msgid "Cancel must waite 20 minutes later."
msgstr ""

#: b2ctrade/business.py:466
msgid "Already withdraw by other users."
msgstr ""

#: b2ctrade/business.py:468 luckybox/business.py:262 roll/business.py:188
msgid "Not enough balance"
msgstr ""

#: b2ctrade/business.py:470
msgid "Not enough available balance"
msgstr ""

#: b2ctrade/business.py:484
msgid "B2C trade buy"
msgstr ""

#: b2ctrade/business.py:507
msgid "B2C trade cancel"
msgstr ""

#: b2ctrade/business.py:1155
msgid "Over withdraw max limit at sametime"
msgstr ""

#: b2ctrade/business.py:1215
msgid "此饰品只能出售"
msgstr ""

#: b2ctrade/models.py:20 b2ctrade/models.py:74 b2ctrade/models.py:137
#: b2ctrade/models.py:171 blindbox/models.py:55 blindbox/models.py:91
#: box/models.py:107 box/models.py:215 box/models.py:243 box/models.py:389
#: box/models.py:404 box/models.py:469 custombox/models.py:29
#: custombox/models.py:85 lottery/models.py:19 lottery/models.py:49
#: package/models.py:104 package/models.py:182 package/models.py:241
#: package/models.py:258 package/models.py:307 package/models.py:343
#: package/models.py:464 package/models.py:477 tradeup/models.py:83
#: tradeup/models.py:95 withdraw/models.py:80
msgid "item info"
msgstr ""

#: b2ctrade/models.py:21 blindbox/models.py:34 blindbox/models.py:93
#: box/models.py:48 box/models.py:156 box/models.py:218 box/models.py:245
#: charge/models.py:223 custombox/models.py:16 custombox/models.py:87
#: luckybox/models.py:17 luckybox/models.py:62 package/admin.py:81
#: package/models.py:105 package/models.py:244 package/models.py:261
#: package/models.py:312 roll/models.py:97 tradeup/models.py:51
#: tradeup/models.py:84 tradeup/models.py:96 withdraw/models.py:76
msgid "price"
msgstr ""

#: b2ctrade/models.py:22
msgid "zbt price"
msgstr ""

#: b2ctrade/models.py:24 tradeup/models.py:98
msgid "unlimited"
msgstr ""

#: b2ctrade/models.py:26 steambase/models.py:22
msgid "create time"
msgstr ""

#: b2ctrade/models.py:27 package/models.py:27 package/models.py:103
#: steambase/models.py:23
msgid "update time"
msgstr ""

#: b2ctrade/models.py:31
msgid "B2C Market Item"
msgstr ""

#: b2ctrade/models.py:39 b2ctrade/models.py:110 b2ctrade/models.py:205
#: b2ctrade/models.py:249 steambase/models.py:31 tradeup/models.py:13
#: tradeup/models.py:64 withdraw/models.py:13 withdraw/models.py:94
#: withdraw/models.py:138
msgid "Dota2"
msgstr ""

#: b2ctrade/models.py:40 b2ctrade/models.py:111 b2ctrade/models.py:206
#: b2ctrade/models.py:250 steambase/models.py:32 tradeup/models.py:14
#: tradeup/models.py:65 withdraw/models.py:14 withdraw/models.py:95
#: withdraw/models.py:139
msgid "CSGO"
msgstr ""

#: b2ctrade/models.py:41 b2ctrade/models.py:112 b2ctrade/models.py:207
#: b2ctrade/models.py:251 steambase/models.py:33 tradeup/models.py:15
#: tradeup/models.py:66 withdraw/models.py:15 withdraw/models.py:96
#: withdraw/models.py:140
msgid "PUBG"
msgstr ""

#: b2ctrade/models.py:42 b2ctrade/models.py:113 b2ctrade/models.py:208
#: b2ctrade/models.py:252 steambase/models.py:34 tradeup/models.py:16
#: tradeup/models.py:67 withdraw/models.py:16 withdraw/models.py:97
#: withdraw/models.py:141
msgid "H1Z1"
msgstr ""

#: b2ctrade/models.py:45 b2ctrade/models.py:116 charge/models.py:16
#: package/models.py:162 package/models.py:278 withdraw/models.py:20
msgid "Initialed"
msgstr ""

#: b2ctrade/models.py:46 b2ctrade/models.py:59 b2ctrade/models.py:117
#: package/models.py:279
msgid "Accepted"
msgstr ""

#: b2ctrade/models.py:47 b2ctrade/models.py:60 b2ctrade/models.py:118
#: box/models.py:146 crash/models.py:18 package/models.py:280 roll/models.py:18
msgid "Cancelled"
msgstr ""

#: b2ctrade/models.py:48
msgid "Trading"
msgstr ""

#: b2ctrade/models.py:49
msgid "Cancelling"
msgstr ""

#: b2ctrade/models.py:50
msgid "PriceCancelled"
msgstr ""

#: b2ctrade/models.py:51
msgid "OutOfStock"
msgstr ""

#: b2ctrade/models.py:52
msgid "ZBTCancelled"
msgstr ""

#: b2ctrade/models.py:55
msgid "WaitForPay"
msgstr ""

#: b2ctrade/models.py:56
msgid "WaitForSend"
msgstr ""

#: b2ctrade/models.py:57
msgid "WaitForTrade"
msgstr ""

#: b2ctrade/models.py:58
msgid "Receive"
msgstr ""

#: b2ctrade/models.py:63
msgid "Market"
msgstr ""

#: b2ctrade/models.py:64
msgid "Package"
msgstr ""

#: b2ctrade/models.py:67 b2ctrade/models.py:131 b2ctrade/models.py:132
#: package/models.py:286 package/models.py:325 withdraw/models.py:38
msgid "trade url"
msgstr ""

#: b2ctrade/models.py:68 b2ctrade/models.py:133 b2ctrade/models.py:212
#: b2ctrade/models.py:255 box/models.py:274 box/models.py:298 box/models.py:388
#: box/models.py:403 box/models.py:419 box/models.py:434 charge/models.py:65
#: charge/models.py:104 charge/models.py:154 charge/models.py:194
#: charge/models.py:276 charge/models.py:291 crash/models.py:49
#: crash/models.py:66 crash/models.py:90 crash/models.py:298
#: crash/models.py:323 package/models.py:189 package/models.py:288
#: package/models.py:393 package/models.py:422 roll/models.py:123
#: roll/models.py:147 tradeup/models.py:70 tradeup/models.py:111
#: tradeup/models.py:135 tradeup/models.py:343 tradeup/models.py:368
#: withdraw/models.py:40 withdraw/models.py:101 withdraw/models.py:144
msgid "amount"
msgstr ""

#: b2ctrade/models.py:69
msgid "buy price"
msgstr ""

#: b2ctrade/models.py:70 b2ctrade/models.py:134 package/models.py:289
msgid "trade status"
msgstr ""

#: b2ctrade/models.py:71
msgid "zbt trade status"
msgstr ""

#: b2ctrade/models.py:72 b2ctrade/models.py:135
msgid "accept time"
msgstr ""

#: b2ctrade/models.py:73 b2ctrade/models.py:136 b2ctrade/models.py:214
#: b2ctrade/models.py:257 steambase/models.py:49 tradeup/models.py:33
#: tradeup/models.py:71 withdraw/models.py:41 withdraw/models.py:103
#: withdraw/models.py:146
msgid "appid"
msgstr ""

#: b2ctrade/models.py:75 b2ctrade/models.py:139 package/models.py:183
#: package/models.py:310 package/models.py:344
msgid "assetid"
msgstr ""

#: b2ctrade/models.py:76
msgid "zbt order id"
msgstr ""

#: b2ctrade/models.py:79 package/models.py:290
msgid "trade No."
msgstr ""

#: b2ctrade/models.py:80
msgid "out trade no"
msgstr ""

#: b2ctrade/models.py:81
msgid "trade source"
msgstr ""

#: b2ctrade/models.py:82 package/models.py:242 package/models.py:259
#: package/models.py:309 tradeup/models.py:50 withdraw/models.py:58
msgid "package item"
msgstr ""

#: b2ctrade/models.py:86
msgid "is extract"
msgstr ""

#: b2ctrade/models.py:89
msgid "error msg"
msgstr ""

#: b2ctrade/models.py:94
msgid "ZBT Trade Record"
msgstr ""

#: b2ctrade/models.py:101
msgid "seller nickname"
msgstr ""

#: b2ctrade/models.py:105
msgid "ZBTBlackList"
msgstr ""

#: b2ctrade/models.py:119
msgid "WaitForBuy"
msgstr ""

#: b2ctrade/models.py:120
msgid "RequestBuy"
msgstr ""

#: b2ctrade/models.py:121
msgid "WaitUnlock"
msgstr ""

#: b2ctrade/models.py:122
msgid "TradeReady"
msgstr ""

#: b2ctrade/models.py:123
msgid "BuyerCancelled"
msgstr ""

#: b2ctrade/models.py:126
msgid "Purchase"
msgstr ""

#: b2ctrade/models.py:127
msgid "Stocks"
msgstr ""

#: b2ctrade/models.py:129 package/models.py:329 package/models.py:359
msgid "account"
msgstr ""

#: b2ctrade/models.py:130
msgid "buyer"
msgstr ""

#: b2ctrade/models.py:138
msgid "expire time"
msgstr ""

#: b2ctrade/models.py:140 box/models.py:116 box/models.py:217 roll/models.py:99
msgid "item type"
msgstr ""

#: b2ctrade/models.py:145
msgid "B2COfficialTrade Record"
msgstr ""

#: b2ctrade/models.py:163 package/models.py:205
msgid "Invalid items"
msgstr ""

#: b2ctrade/models.py:179
msgid "B2C Item list"
msgstr ""

#: b2ctrade/models.py:189 blindbox/models.py:129 box/models.py:261
#: box/models.py:471 chat/models.py:64 chat/models.py:81 package/models.py:323
#: package/models.py:360 roll/models.py:179 sitecfg/models.py:26
#: sitecfg/models.py:65 sitecfg/models.py:100 tradeup/models.py:402
msgid "remark"
msgstr ""

#: b2ctrade/models.py:192
msgid "customid"
msgstr ""

#: b2ctrade/models.py:197
msgid "B2COfficial Account"
msgstr ""

#: b2ctrade/models.py:213 b2ctrade/models.py:256 withdraw/models.py:102
#: withdraw/models.py:145
msgid "test amount"
msgstr ""

#: b2ctrade/models.py:219
msgid "B2CTrade Statistics Day"
msgstr ""

#: b2ctrade/models.py:262
msgid "B2CTrade Statistics Month"
msgstr ""

#: b2ctrade/models.py:294 box/models.py:316 charge/models.py:147
#: crash/models.py:114 crash/models.py:292 package/models.py:439
#: roll/models.py:164 roll/models.py:328 tradeup/models.py:159
#: tradeup/models.py:337 withdraw/models.py:185
msgid "Month"
msgstr ""

#: b2ctrade/service/admin_actions.py:48
msgid "b2c trade accept complete"
msgstr ""

#: b2ctrade/service/admin_actions.py:51
msgid "b2c trade accept"
msgstr ""

#: b2ctrade/service/admin_actions.py:73
msgid "An error occurred during the process."
msgstr ""

#: b2ctrade/service/admin_actions.py:75
msgid "b2c trade cancel complete"
msgstr ""

#: b2ctrade/service/admin_actions.py:78
msgid "b2c trade cancel"
msgstr ""

#: b2ctrade/service/admin_actions.py:105
msgid "扎比特购买失败请稍后重试."
msgstr ""

#: b2ctrade/service/admin_actions.py:116
msgid "zbt_buy complete"
msgstr ""

#: b2ctrade/service/admin_actions.py:123
msgid "扎比特接口错误：{}"
msgstr ""

#: b2ctrade/service/admin_actions.py:129
msgid "zbt_buy"
msgstr ""

#: b2ctrade/service/admin_actions.py:161
msgid "Sync b2c market price complete"
msgstr ""

#: b2ctrade/service/admin_actions.py:164
msgid "Sync b2c market price"
msgstr ""

#: b2ctrade/service/admin_actions.py:212 b2ctrade/service/admin_actions.py:252
#: package/service/admin_actions.py:75
msgid "Sync complete"
msgstr ""

#: b2ctrade/service/admin_actions.py:215 b2ctrade/service/admin_actions.py:255
#: package/service/admin_actions.py:78
msgid "Sync trade bot inventory"
msgstr ""

#: b2ctrade/service/admin_actions.py:224
msgid "Sync failed not search b2c account"
msgstr ""

#: b2ctrade/service/admin_actions.py:338
msgid "扎比特取回记录导出Excel"
msgstr ""

#: b2ctrade/views.py:51 b2ctrade/views.py:78 b2ctrade/views.py:99
#: b2ctrade/views.py:119 b2ctrade/views.py:139 package/views.py:234
#: withdraw/views.py:31 withdraw/views.py:56
msgid "Withdraw is under maintenance, please wait for a while."
msgstr ""

#: b2ctrade/views.py:53
msgid "No permission"
msgstr ""

#: b2ctrade/views.py:75 chat/views.py:31
msgid "系统维护中，请稍后再试，具体恢复时间请关注网站公告。"
msgstr ""

#: b2ctrade/views.py:272
msgid "取回接口维护，稍后再试，恢复时间请参考网站公告。"
msgstr ""

#: b2ctrade/views.py:274
msgid "取回权限被限制，请联系在线客服。"
msgstr ""

#: blindbox/business.py:68 box/business.py:313 custombox/business.py:168
msgid "Case is blocked, please open other case."
msgstr ""

#: blindbox/business.py:72
msgid "该轮游戏已结束，请更换盒子"
msgstr ""

#: blindbox/business.py:74
msgid "位置错误，请重新选择"
msgstr ""

#: blindbox/business.py:80
msgid "该位置已开启"
msgstr ""

#: blindbox/business.py:88
msgid "Invalid count"
msgstr ""

#: blindbox/business.py:193 box/business.py:599
msgid "Invalid case key"
msgstr ""

#: blindbox/business.py:231 blindbox/business.py:302 envelope/business.py:36
#: market/business.py:100
msgid "参数错误"
msgstr ""

#: blindbox/models.py:13
msgid "Top"
msgstr ""

#: blindbox/models.py:14 blindbox/models.py:88 box/models.py:208
msgid "Free"
msgstr ""

#: blindbox/models.py:15
msgid "Festival"
msgstr ""

#: blindbox/models.py:16
msgid "FreeGive"
msgstr ""

#: blindbox/models.py:18 blindbox/models.py:32 box/models.py:44
#: charge/models.py:323 custombox/models.py:14 luckybox/models.py:77
#: roll/models.py:30 sitecfg/models.py:10 steambase/models.py:43
msgid "name"
msgstr ""

#: blindbox/models.py:19 luckybox/models.py:73
msgid "category"
msgstr ""

#: blindbox/models.py:20 blindbox/models.py:39 box/models.py:19
#: box/models.py:33 box/models.py:57 envelope/models.py:31
msgid "order"
msgstr ""

#: blindbox/models.py:25 box/models.py:26
msgid "Case Type"
msgstr ""

#: blindbox/models.py:33 box/models.py:47 custombox/models.py:15
msgid "key"
msgstr ""

#: blindbox/models.py:35 box/models.py:49
msgid "discount(%)"
msgstr ""

#: blindbox/models.py:36 box/models.py:52 custombox/models.py:17
#: custombox/models.py:48
msgid "cover image"
msgstr ""

#: blindbox/models.py:37 box/models.py:53 custombox/models.py:18
#: custombox/models.py:59
msgid "item image"
msgstr ""

#: blindbox/models.py:38
msgid "blindbox type"
msgstr ""

#: blindbox/models.py:40 charge/models.py:327
msgid "unlock"
msgstr ""

#: blindbox/models.py:42
msgid "limit timestamp"
msgstr ""

#: blindbox/models.py:43
msgid "limited times"
msgstr ""

#: blindbox/models.py:48
msgid "BlindBox"
msgstr ""

#: blindbox/models.py:56 blindbox/models.py:92 blindbox/models.py:113
#: blindbox/models.py:128 box/models.py:109 box/models.py:171 box/models.py:242
#: box/models.py:260 box/models.py:331 box/models.py:346 box/models.py:373
#: box/models.py:406 box/models.py:457 custombox/models.py:31
#: custombox/models.py:84 promotion/models.py:78
msgid "case"
msgstr ""

#: blindbox/models.py:57 custombox/models.py:32
msgid "show chance"
msgstr ""

#: blindbox/models.py:58 box/models.py:111 custombox/models.py:33
msgid "drop chance a"
msgstr ""

#: blindbox/models.py:59 box/models.py:112 custombox/models.py:34
msgid "drop chance b"
msgstr ""

#: blindbox/models.py:60 box/models.py:113 custombox/models.py:35
msgid "drop chance c"
msgstr ""

#: blindbox/models.py:61 box/models.py:114 custombox/models.py:36
msgid "drop chance d"
msgstr ""

#: blindbox/models.py:62 box/models.py:115 custombox/models.py:37
msgid "drop chance e"
msgstr ""

#: blindbox/models.py:63 box/models.py:117
msgid "diamond"
msgstr ""

#: blindbox/models.py:64
msgid "custom enable"
msgstr ""

#: blindbox/models.py:65
msgid "custom price"
msgstr ""

#: blindbox/models.py:69
msgid "BlindBoxDrop"
msgstr ""

#: blindbox/models.py:74
msgid "game id"
msgstr ""

#: blindbox/models.py:75
msgid "random seed"
msgstr ""

#: blindbox/models.py:79
msgid "BlindBoxGame"
msgstr ""

#: blindbox/models.py:94 box/models.py:158 box/models.py:246
#: charge/models.py:329 luckybox/models.py:15 roll/models.py:29
#: sitecfg/models.py:66 sitecfg/models.py:122 steambase/models.py:58
msgid "type"
msgstr ""

#: blindbox/models.py:95
msgid "index"
msgstr ""

#: blindbox/models.py:96 crash/models.py:48 tradeup/models.py:49
#: tradeup/models.py:69 tradeup/models.py:82
msgid "game"
msgstr ""

#: blindbox/models.py:100
msgid "Blind Box Record"
msgstr ""

#: blindbox/models.py:110 box/models.py:328 box/models.py:343
#: charge/models.py:195 charge/models.py:207 charge/models.py:260
#: package/models.py:220 promotion/models.py:59
msgid "level"
msgstr ""

#: blindbox/models.py:111 box/models.py:329
msgid "min point"
msgstr ""

#: blindbox/models.py:112 box/models.py:330
msgid "max point"
msgstr ""

#: blindbox/models.py:118 box/models.py:336
msgid "Free Case Config"
msgstr ""

#: blindbox/models.py:126 box/models.py:258
msgid "open idle min(seconds)"
msgstr ""

#: blindbox/models.py:127 box/models.py:259
msgid "open idle max(seconds)"
msgstr ""

#: blindbox/models.py:134 box/models.py:266
msgid "Case Bot Config"
msgstr ""

#: blindbox/views.py:26 box/business.py:264 crash/views.py:42
#: tradeup/views.py:58 tradeup/views.py:80 tradeup/views.py:102
#: tradeup/views.py:150 tradeup/views.py:248 tradeup/views.py:271
#: tradeup/views.py:293
msgid "Current game is under maintenance, please wait for a while."
msgstr ""

#: blindbox/views.py:32 blindbox/views.py:50 blindbox/views.py:71
#: blindbox/views.py:91 blindbox/views.py:112 blindbox/views.py:132
#: blindbox/views.py:156 blindbox/views.py:176 box/views.py:45
#: box/views.py:1220 box/views.py:1241 box/views.py:1264 charge/models.py:18
#: crash/views.py:130 crash/views.py:155 crash/views.py:180 crash/views.py:198
#: tradeup/views.py:65 tradeup/views.py:87 tradeup/views.py:110
#: tradeup/views.py:157 tradeup/views.py:183 tradeup/views.py:208
#: tradeup/views.py:233 tradeup/views.py:256 tradeup/views.py:278
#: tradeup/views.py:301
msgid "Succeed"
msgstr ""

#: box/admin.py:83
msgid "期望价格"
msgstr ""

#: box/admin.py:94
msgid "price_expectation_b"
msgstr ""

#: box/admin.py:105
msgid "price_expectation_c"
msgstr ""

#: box/admin.py:116
msgid "price_expectation_d"
msgstr ""

#: box/admin.py:127
msgid "price_expectation_e"
msgstr ""

#: box/apps.py:7
msgid "Box"
msgstr ""

#: box/business.py:149
msgid "Invalid level"
msgstr ""

#: box/business.py:155
msgid "Already opened case today"
msgstr ""

#: box/business.py:158
msgid "Invalid case"
msgstr ""

#: box/business.py:160
msgid "Invalid level, please open correct level case."
msgstr ""

#: box/business.py:271
msgid "This weapon case is under maintenance. Please try again later."
msgstr ""

#: box/business.py:275
msgid "箱子正在维护中，请稍后再试2"
msgstr ""

#: box/business.py:289 box/business_room.py:209 box/business_room.py:295
msgid "余额不足"
msgstr ""

#: box/business.py:294
msgid "箱子没有配置掉落物品"
msgstr ""

#: box/business.py:408
msgid "开箱过程中发生错误，请重试"
msgstr ""

#: box/business_room.py:167 box/business_room.py:273
msgid "对战功能需要至少一件饰品有效提取才能自动开通，请先提取后再试。"
msgstr ""

#: box/business_room.py:177 box/business_room.py:284 box/business_room.py:374
#: roll/business.py:87
msgid "Over max room count"
msgstr ""

#: box/business_room.py:183
msgid "Invalid cases count"
msgstr ""

#: box/business_room.py:188
msgid "Invalid joiners count"
msgstr ""

#: box/business_room.py:199
msgid "Case is block, please choice other case."
msgstr ""

#: box/business_room.py:204
msgid "Invalid cases"
msgstr ""

#: box/business_room.py:211
msgid "对战具有一定的残酷性，禁止超过账户余额的一半金额创建房间。"
msgstr ""

#: box/business_room.py:243
msgid "创建对战"
msgstr ""

#: box/business_room.py:297
msgid "对战具有一定的残酷性，禁止超过账户余额的一半金额参加对战。"
msgstr ""

#: box/business_room.py:309 box/business_room.py:378 box/business_room.py:424
#: box/business_room.py:459 roll/business.py:167 roll/business.py:195
#: roll/business.py:240 roll/business.py:275 roll/business.py:312
#: roll/business.py:1205 roll/business.py:1334 roll/models.py:60
msgid "Invalid room"
msgstr ""

#: box/business_room.py:313 box/business_room.py:381 roll/business.py:249
msgid "Already joined"
msgstr ""

#: box/business_room.py:321 box/business_room.py:386
msgid "对战"
msgstr ""

#: box/business_room.py:359
msgid "Invalid room type"
msgstr ""

#: box/business_room.py:384
msgid "Invalid team"
msgstr ""

#: box/business_room.py:427
msgid "Invalid bet"
msgstr ""

#: box/business_room.py:431
msgid "房主不能退出房间，请使用解散功能"
msgstr ""

#: box/business_room.py:434
msgid "Case room bet quit"
msgstr ""

#: box/business_room.py:463
msgid "只有房主可以解散房间"
msgstr ""

#: box/business_room.py:473
msgid "Case room dismissed by owner"
msgstr ""

#: box/business_room.py:800 box/business_room.py:809
msgid "Case chance invalid."
msgstr ""

#: box/business_room.py:1254 box/business_room.py:1349
msgid "Case drops invalid."
msgstr ""

#: box/business_room.py:1405
msgid "Case room cancel"
msgstr ""

#: box/models.py:16
msgid "Type Id"
msgstr ""

#: box/models.py:17
msgid "Type Name"
msgstr ""

#: box/models.py:30 package/models.py:51
msgid "Cate Id"
msgstr ""

#: box/models.py:31 package/models.py:52
msgid "Cate Name"
msgstr ""

#: box/models.py:34 luckybox/models.py:76
msgid "icon"
msgstr ""

#: box/models.py:41
msgid "Case Category"
msgstr ""

#: box/models.py:50
msgid "present"
msgstr ""

#: box/models.py:54
msgid "back image"
msgstr ""

#: box/models.py:55
msgid "case type"
msgstr ""

#: box/models.py:56
msgid "case category"
msgstr ""

#: box/models.py:59 envelope/models.py:34
msgid "is show"
msgstr ""

#: box/models.py:62
msgid "enable room"
msgstr ""

#: box/models.py:64
msgid "superuser enable"
msgstr ""

#: box/models.py:67
msgid "tag"
msgstr ""

#: box/models.py:68
msgid "标签颜色"
msgstr ""

#: box/models.py:69
msgid "open count"
msgstr ""

#: box/models.py:70
msgid "case cost"
msgstr ""

#: box/models.py:71
msgid "up sill"
msgstr ""

#: box/models.py:72
msgid "down sill"
msgstr ""

#: box/models.py:73
msgid "dyntic"
msgstr ""

#: box/models.py:74
msgid "algorithm_enable"
msgstr ""

#: box/models.py:75 lottery/models.py:69 package/models.py:29
#: sitecfg/models.py:87 sitecfg/models.py:102
msgid "content"
msgstr ""

#: box/models.py:77
msgid "SEO标题"
msgstr ""

#: box/models.py:78
msgid "SEO关键词"
msgstr ""

#: box/models.py:79
msgid "SEO描述"
msgstr ""

#: box/models.py:87 box/models.py:235 package/models.py:172
msgid "Case"
msgstr ""

#: box/models.py:99
msgid "Base by count and chance"
msgstr ""

#: box/models.py:100
msgid "Base by count"
msgstr ""

#: box/models.py:101
msgid "Base by chance"
msgstr ""

#: box/models.py:104 box/models.py:207 roll/models.py:92
msgid "Assets"
msgstr ""

#: box/models.py:110
msgid "开箱概率"
msgstr ""

#: box/models.py:122
msgid "up sill drop"
msgstr ""

#: box/models.py:123
msgid "down sill drop"
msgstr ""

#: box/models.py:126
msgid "Drop Item"
msgstr ""

#: box/models.py:140 crash/models.py:12 roll/models.py:12
msgid "Initial"
msgstr ""

#: box/models.py:141 crash/models.py:13 grab/models.py:26 roll/models.py:13
msgid "Joinable"
msgstr ""

#: box/models.py:142 crash/models.py:14 roll/models.py:14
msgid "Joining"
msgstr ""

#: box/models.py:143 crash/models.py:15 lottery/models.py:41 roll/models.py:15
msgid "Full"
msgstr ""

#: box/models.py:144 crash/models.py:16 roll/models.py:16
msgid "Running"
msgstr ""

#: box/models.py:145 crash/models.py:17 grab/models.py:29 lottery/models.py:42
#: roll/models.py:17
msgid "End"
msgstr ""

#: box/models.py:149 box/models.py:236
msgid "Battle"
msgstr ""

#: box/models.py:150 box/models.py:237
msgid "Equality"
msgstr ""

#: box/models.py:151 box/models.py:238
msgid "TeamBattle"
msgstr ""

#: box/models.py:153
msgid "short id"
msgstr ""

#: box/models.py:155 lottery/models.py:21 roll/models.py:35
msgid "max joiner"
msgstr ""

#: box/models.py:157 charge/models.py:32 crash/models.py:30
#: package/models.py:185 package/models.py:187 roll/models.py:44
msgid "status"
msgstr ""

#: box/models.py:159
msgid "private"
msgstr ""

#: box/models.py:163
msgid "Case Room"
msgstr ""

#: box/models.py:170 box/models.py:188 box/models.py:210 box/models.py:420
#: roll/models.py:76 roll/models.py:95 roll/models.py:192
msgid "room"
msgstr ""

#: box/models.py:172 box/models.py:374
msgid "opened"
msgstr ""

#: box/models.py:176
msgid "Case Room Round"
msgstr ""

#: box/models.py:191
msgid "open amount"
msgstr ""

#: box/models.py:192 crash/models.py:26 crash/models.py:52 roll/models.py:77
#: tradeup/models.py:31
msgid "win amount"
msgstr ""

#: box/models.py:193 roll/models.py:78
msgid "win items count"
msgstr ""

#: box/models.py:194 roll/models.py:79
msgid "victory"
msgstr ""

#: box/models.py:195
msgid "bet team"
msgstr ""

#: box/models.py:199
msgid "Case Room Bet"
msgstr ""

#: box/models.py:211
msgid "bet"
msgstr ""

#: box/models.py:213
msgid "winner"
msgstr ""

#: box/models.py:219
msgid "part price of item"
msgstr ""

#: box/models.py:220
msgid "item need to split"
msgstr ""

#: box/models.py:224
msgid "Case Room Item"
msgstr ""

#: box/models.py:250 custombox/models.py:91
msgid "Case Record"
msgstr ""

#: box/models.py:293
msgid "Case Statistics Day"
msgstr ""

#: box/models.py:324
msgid "Case Statistics Month"
msgstr ""

#: box/models.py:344
msgid "min charge total amount"
msgstr ""

#: box/models.py:345
msgid "max charge total amount"
msgstr ""

#: box/models.py:351
msgid "Festival Case Config"
msgstr ""

#: box/models.py:358
msgid "month"
msgstr ""

#: box/models.py:359
msgid "day"
msgstr ""

#: box/models.py:365
msgid "Festival Case Date"
msgstr ""

#: box/models.py:375
msgid "expired time"
msgstr ""

#: box/models.py:379
msgid "Festival Case Record"
msgstr ""

#: box/models.py:394
msgid "Drop Day Rank"
msgstr ""

#: box/models.py:410
msgid "Income Day Rank"
msgstr ""

#: box/models.py:424
msgid "Room Day Rank"
msgstr ""

#: box/models.py:432 charge/models.py:152 crash/models.py:321
#: tradeup/models.py:366
msgid "year"
msgstr ""

#: box/models.py:433 charge/models.py:153 crash/models.py:322
#: tradeup/models.py:367
msgid "week"
msgstr ""

#: box/models.py:438
msgid "Lose Week Rank"
msgstr ""

#: box/models.py:458
msgid "case_key"
msgstr ""

#: box/models.py:462
msgid "CaseKeyConfig"
msgstr ""

#: box/models.py:475
msgid "GiveawayItems"
msgstr ""

#: box/service/admin_actions.py:48
msgid "导出EXCEL"
msgstr ""

#: box/service/admin_actions.py:100
msgid "箱子掉落导出EXCEL"
msgstr ""

#: box/views.py:963 package/views.py:72 package/views.py:649
msgid "成功"
msgstr ""

#: box/views.py:965 package/views.py:651
msgid "搜索失败"
msgstr ""

#: box/views.py:969 package/views.py:78 package/views.py:655
msgid "服务器异常"
msgstr ""

#: box/views.py:1138
msgid "room_uid required"
msgstr ""

#: box/views.py:1146
msgid "Room not found"
msgstr ""

#: box/views.py:1152
msgid "Access denied"
msgstr ""

#: box/views.py:1224 box/views.py:1245 box/views.py:1267
msgid "Internal error"
msgstr ""

#: charge/apps.py:7
msgid "charge"
msgstr ""

#: charge/business.py:83
msgid "Invalid charge amount"
msgstr ""

#: charge/business.py:85
msgid "充值金额必须为整数"
msgstr ""

#: charge/business.py:87 charge/business.py:158 custombox/business.py:41
#: custombox/business.py:142 grab/business.py:56 grab/business.py:132
#: grab/business.py:154 grab/business.py:178 luckybox/business.py:235
#: market/business.py:98
msgid "Please Login First"
msgstr ""

#: charge/business.py:89
msgid "Invalid pay type"
msgstr ""

#: charge/business.py:154 charge/business.py:160 charge/business.py:208
#: charge/views.py:142 luckybox/business.py:97 luckybox/business.py:160
#: luckybox/business.py:162 withdraw/business.py:287 withdraw/business.py:317
msgid "Invalid Params"
msgstr ""

#: charge/business.py:156
msgid "count must be int"
msgstr ""

#: charge/business.py:163
msgid "Good Not Found"
msgstr ""

#: charge/business.py:172
msgid "Charge System Error, Please Try Again Later"
msgstr ""

#: charge/business.py:840 charge/business.py:885 charge/business.py:930
#: charge/business.py:975 charge/business.py:1019 charge/business.py:1741
#: charge/business.py:1887 charge/business.py:1966
msgid "充值通道维护"
msgstr ""

#: charge/business.py:852 charge/business.py:896 charge/business.py:941
#: charge/business.py:986 charge/business.py:1030 charge/business.py:1752
#: charge/business.py:1977
msgid "防沉迷系统提示：您今日充值总额已超出每日充值限制，请明日再试。"
msgstr ""

#: charge/business.py:855 charge/business.py:899 charge/business.py:944
#: charge/business.py:989 charge/business.py:1033 charge/business.py:1175
#: charge/business.py:1755 charge/business.py:1980
msgid "Invalid Coins"
msgstr ""

#: charge/business.py:871 charge/business.py:915 charge/business.py:960
#: charge/business.py:1005 charge/business.py:1049 charge/business.py:1771
#: charge/business.py:1926 charge/business.py:1996
msgid "支付请求失败"
msgstr ""

#: charge/business.py:1189
msgid "Charge Api is Busy"
msgstr ""

#: charge/business.py:1232 charge/business.py:1268 charge/business.py:1305
#: charge/business.py:1367 charge/business.py:1420 charge/business.py:1577
#: envelope/business.py:34
msgid "请先登录"
msgstr ""

#: charge/business.py:1235 charge/business.py:1271 charge/business.py:1308
#: charge/business.py:1370 charge/business.py:1423 charge/business.py:1580
msgid "金额错误"
msgstr ""

#: charge/business.py:1240 charge/business.py:1277 charge/business.py:1349
#: charge/business.py:1402 charge/business.py:1447 charge/business.py:1601
msgid "invalid params"
msgstr ""

#: charge/business.py:1248 charge/business.py:1285 charge/business.py:1354
#: charge/business.py:1407 charge/business.py:1452 charge/business.py:1609
msgid "charge system api error"
msgstr ""

#: charge/business.py:1548
msgid "Please login first"
msgstr ""

#: charge/business.py:1552
msgid "CDKey is invalid"
msgstr ""

#: charge/business.py:1558
msgid "CDKey top-up"
msgstr ""

#: charge/business.py:1716
msgid "user not found"
msgstr ""

#: charge/business.py:1732
msgid "pay method not found"
msgstr ""

#: charge/business.py:1896
msgid "防沉迷系统提示：您今日充值总额已超出每日充值限制，请明日再试"
msgstr ""

#: charge/business.py:1900 charge/views.py:374
msgid "无效的充值金额"
msgstr ""

#: charge/models.py:17
msgid "Actived"
msgstr ""

#: charge/models.py:20
msgid "Canceled"
msgstr ""

#: charge/models.py:23 charge/models.py:318
msgid "WeChat"
msgstr ""

#: charge/models.py:24 charge/models.py:319
msgid "Ali"
msgstr ""

#: charge/models.py:25 charge/models.py:320
msgid "Union Pay"
msgstr ""

#: charge/models.py:26 charge/models.py:321
msgid "Other Pay"
msgstr ""

#: charge/models.py:29
msgid "out trade No"
msgstr ""

#: charge/models.py:31 grab/models.py:33 package/models.py:245
msgid "currency"
msgstr ""

#: charge/models.py:33
msgid "pay type"
msgstr ""

#: charge/models.py:34
msgid "pay amount"
msgstr ""

#: charge/models.py:35
msgid "pay time"
msgstr ""

#: charge/models.py:36
msgid "nonce"
msgstr ""

#: charge/models.py:37
msgid "timestamp"
msgstr ""

#: charge/models.py:38
msgid "client ip"
msgstr ""

#: charge/models.py:99
msgid "Charge Statistics Day"
msgstr ""

#: charge/models.py:109
msgid "Charge Statistics Month"
msgstr ""

#: charge/models.py:158
msgid "Charge Week Rank"
msgstr ""

#: charge/models.py:200
msgid "Charge Level"
msgstr ""

#: charge/models.py:208 charge/models.py:261 promotion/models.py:64
msgid "min amount"
msgstr ""

#: charge/models.py:209 charge/models.py:262 promotion/models.py:65
msgid "max amount"
msgstr ""

#: charge/models.py:214
msgid "Charge Level Config"
msgstr ""

#: charge/models.py:221
msgid "Cxka good id"
msgstr ""

#: charge/models.py:222
msgid "Cxka good name"
msgstr ""

#: charge/models.py:228
msgid "Cxka goods"
msgstr ""

#: charge/models.py:235
msgid "generate count"
msgstr ""

#: charge/models.py:239
msgid "Cxka Generate"
msgstr ""

#: charge/models.py:246 grab/models.py:23 package/models.py:237
msgid "Coins"
msgstr ""

#: charge/models.py:247 charge/models.py:324
msgid "sort order"
msgstr ""

#: charge/models.py:249 luckybox/models.py:74 sitecfg/models.py:127
msgid "image"
msgstr ""

#: charge/models.py:253
msgid "ChargeAmountConfig"
msgstr ""

#: charge/models.py:263 envelope/models.py:48
msgid "handsel"
msgstr ""

#: charge/models.py:267 charge/models.py:268
msgid "充值赠送金额配置"
msgstr ""

#: charge/models.py:290
msgid "CDKey"
msgstr ""

#: charge/models.py:301
msgid "used"
msgstr ""

#: charge/models.py:302
msgid "unused"
msgstr ""

#: charge/models.py:328
msgid "suggest"
msgstr ""

#: charge/models.py:330
msgid "color"
msgstr ""

#: charge/models.py:333
msgid "daily limit"
msgstr ""

#: charge/models.py:335
msgid "single limit"
msgstr ""

#: charge/models.py:342
msgid "rate"
msgstr ""

#: charge/service/admin_actions.py:34
msgid "startup_export_txt"
msgstr ""

#: charge/service/admin_actions.py:96
msgid "充值记录导出为Excel"
msgstr ""

#: charge/service/admin_actions.py:129
msgid "充值等级导出为Excel"
msgstr ""

#: charge/service/admin_actions.py:160
msgid "充值日统计导出为Excel"
msgstr ""

#: charge/service/admin_actions.py:187
msgid "用户等级重置"
msgstr ""

#: charge/service/admin_actions.py:200
msgid "Generate CDKey"
msgstr ""

#: charge/views.py:47 charge/views.py:72 charge/views.py:146
msgid "Charge is under maintenance, please wait for a while."
msgstr ""

#: charge/views.py:248 charge/views.py:363 charge/views.py:501
#: charge/views.py:749 charge/views.py:792 charge/views.py:815
msgid "充值渠道维护，具体恢复时间请查看网站公告。"
msgstr ""

#: charge/views.py:356
msgid "用户不存在"
msgstr ""

#: charge/views.py:360
msgid "请进入会员中心先通过规则考试后再充值"
msgstr ""

#: charge/views.py:410
msgid "不支持的支付方式"
msgstr ""

#: charge/views.py:414
msgid "接口维护中"
msgstr ""

#: chat/apps.py:7
msgid "Chat"
msgstr ""

#: chat/business.py:46
msgid "At least 50coins to chat"
msgstr ""

#: chat/business.py:48
msgid "You have been banned chat"
msgstr ""

#: chat/business.py:129
msgid "Message not found or permission denied"
msgstr ""

#: chat/business.py:137
msgid "Message marked as read"
msgstr ""

#: chat/models.py:16
msgid "Sender"
msgstr ""

#: chat/models.py:20
msgid "Sender Name"
msgstr ""

#: chat/models.py:23
msgid "Recipient"
msgstr ""

#: chat/models.py:27
msgid "Recipient Name"
msgstr ""

#: chat/models.py:28
msgid "Subject"
msgstr ""

#: chat/models.py:29
msgid "Body"
msgstr ""

#: chat/models.py:30
msgid "Sent At"
msgstr ""

#: chat/models.py:31
msgid "Read At"
msgstr ""

#: chat/models.py:35
msgid "Messages"
msgstr ""

#: chat/models.py:63
msgid "Message"
msgstr ""

#: chat/models.py:69
msgid "Chat Bot Message"
msgstr ""

#: chat/models.py:77
msgid "chat idle min(seconds)"
msgstr ""

#: chat/models.py:78
msgid "chat idle max(seconds)"
msgstr ""

#: chat/models.py:79 sitecfg/models.py:208
msgid "message"
msgstr ""

#: chat/models.py:86
msgid "Chat Bot Config"
msgstr ""

#: crash/apps.py:7
msgid "Crash"
msgstr ""

#: crash/business.py:144
msgid "Crash game win"
msgstr ""

#: crash/business.py:200 tradeup/business.py:82 tradeup/business.py:186
msgid "Invalid bet amount"
msgstr ""

#: crash/business.py:204
msgid "Auto crashout should between 1.01~999.99"
msgstr ""

#: crash/business.py:213
msgid "Invalid game"
msgstr ""

#: crash/business.py:215
msgid "Game is running, please wait on"
msgstr ""

#: crash/business.py:219
msgid "Over bet max amount"
msgstr ""

#: crash/business.py:239
msgid "Crash game bet"
msgstr ""

#: crash/business.py:256
msgid "Game is not running"
msgstr ""

#: crash/business.py:258
msgid "Invalid out point"
msgstr ""

#: crash/business.py:265
msgid "Invalid operation"
msgstr ""

#: crash/models.py:20 tradeup/models.py:24
msgid "hash"
msgstr ""

#: crash/models.py:21 tradeup/models.py:25
msgid "secret"
msgstr ""

#: crash/models.py:22 tradeup/models.py:26
msgid "percentage"
msgstr ""

#: crash/models.py:23
msgid "crash point"
msgstr ""

#: crash/models.py:24 roll/models.py:42
msgid "total amount"
msgstr ""

#: crash/models.py:25 roll/models.py:41
msgid "pump amount"
msgstr ""

#: crash/models.py:27
msgid "joinable time"
msgstr ""

#: crash/models.py:28
msgid "run time"
msgstr ""

#: crash/models.py:29 envelope/models.py:24
msgid "end time"
msgstr ""

#: crash/models.py:35
msgid "Crash Game"
msgstr ""

#: crash/models.py:43 tradeup/models.py:19
msgid "Not End"
msgstr ""

#: crash/models.py:44 tradeup/models.py:20
msgid "Win"
msgstr ""

#: crash/models.py:45 tradeup/models.py:21
msgid "Lose"
msgstr ""

#: crash/models.py:50
msgid "out point"
msgstr ""

#: crash/models.py:53 tradeup/models.py:32
msgid "win result"
msgstr ""

#: crash/models.py:58
msgid "Crash Bet"
msgstr ""

#: crash/models.py:71
msgid "Crash Pump Day"
msgstr ""

#: crash/models.py:95
msgid "Crash Pump Month"
msgstr ""

#: crash/models.py:119 crash/models.py:203 roll/models.py:204
#: roll/models.py:264 tradeup/models.py:164 tradeup/models.py:248
msgid "game amount"
msgstr ""

#: crash/models.py:120 crash/models.py:204 roll/models.py:205
#: roll/models.py:265 tradeup/models.py:165 tradeup/models.py:249
msgid "game journal"
msgstr ""

#: crash/models.py:121 crash/models.py:205 tradeup/models.py:166
#: tradeup/models.py:250
msgid "win amount journal"
msgstr ""

#: crash/models.py:122 crash/models.py:206 tradeup/models.py:167
#: tradeup/models.py:251
msgid "lose amount journal"
msgstr ""

#: crash/models.py:123 crash/models.py:207 roll/models.py:206
#: roll/models.py:266 tradeup/models.py:168 tradeup/models.py:252
msgid "test game amount"
msgstr ""

#: crash/models.py:124 crash/models.py:208 roll/models.py:207
#: roll/models.py:267 tradeup/models.py:169 tradeup/models.py:253
msgid "admin game amount"
msgstr ""

#: crash/models.py:129
msgid "Crash Statistics Day"
msgstr ""

#: crash/models.py:213
msgid "Crash Statistics Month"
msgstr ""

#: crash/models.py:302
msgid "Crash Win Day Rank"
msgstr ""

#: crash/models.py:327
msgid "Crash Win Week Rank"
msgstr ""

#: crash/models.py:346 tradeup/models.py:391
msgid "Weeks"
msgstr ""

#: custombox/business.py:93
msgid "Quantity must be greater than 2"
msgstr ""

#: custombox/business.py:153
msgid "No Permission to Open This Box"
msgstr ""

#: custombox/models.py:22
msgid "CustomBox"
msgstr ""

#: custombox/models.py:41
msgid "CustomDropItem"
msgstr ""

#: custombox/models.py:52
msgid "CustomBoxCover"
msgstr ""

#: custombox/models.py:63
msgid "CustomBoxItem"
msgstr ""

#: custombox/models.py:71 luckybox/models.py:13 steambase/models.py:44
#: withdraw/models.py:79
msgid "market name"
msgstr ""

#: custombox/models.py:75
msgid "CustomBoxItemInfo"
msgstr ""

#: custombox/views.py:91 custombox/views.py:128
msgid "The Custom Box is under maintenance, please wait for a while."
msgstr ""

#: envelope/apps.py:7
msgid "Envelope"
msgstr ""

#: envelope/business.py:43
msgid "口令不存在"
msgstr ""

#: envelope/business.py:48
msgid "该红包未开始领取或已过期"
msgstr ""

#: envelope/business.py:56
msgid "不满足条件"
msgstr ""

#: envelope/business.py:60
msgid "该口令红包已领取"
msgstr ""

#: envelope/business.py:68
msgid "该红包已抢光"
msgstr ""

#: envelope/business.py:83
msgid "口令红包"
msgstr ""

#: envelope/models.py:10
msgid "准备中"
msgstr ""

#: envelope/models.py:11
msgid "未开始"
msgstr ""

#: envelope/models.py:12
msgid "已开始"
msgstr ""

#: envelope/models.py:13
msgid "已结束"
msgstr ""

#: envelope/models.py:15 lottery/models.py:67 sitecfg/models.py:67
#: sitecfg/models.py:86 sitecfg/models.py:101 sitecfg/models.py:121
msgid "title"
msgstr ""

#: envelope/models.py:16
msgid "rule start time"
msgstr ""

#: envelope/models.py:17
msgid "rule_end_time"
msgstr ""

#: envelope/models.py:18
msgid "rule coins"
msgstr ""

#: envelope/models.py:20
msgid "stock"
msgstr ""

#: envelope/models.py:21
msgid "有限库存"
msgstr ""

#: envelope/models.py:23
msgid "start time"
msgstr ""

#: envelope/models.py:25
msgid "handsel min"
msgstr ""

#: envelope/models.py:26
msgid "handsel max"
msgstr ""

#: envelope/models.py:28 envelope/models.py:47
msgid "envelop password"
msgstr ""

#: envelope/models.py:29 lottery/models.py:47
msgid "state"
msgstr ""

#: envelope/models.py:30 package/models.py:28 roll/models.py:31
#: sitecfg/models.py:126
msgid "description"
msgstr ""

#: envelope/models.py:38
msgid "EnvelopeRule"
msgstr ""

#: envelope/models.py:46
msgid "rule"
msgstr ""

#: envelope/models.py:52
msgid "EnvelopeRecord"
msgstr ""

#: envelope/service/admin_actions.py:37
msgid "红包记录导出Excel"
msgstr ""

#: envelope/views.py:37
msgid "系统维护中，请稍等再试"
msgstr ""

#: grab/business.py:65 grab/business.py:105 grab/business.py:181
msgid "Grab Room Not Found"
msgstr ""

#: grab/business.py:156 grab/business.py:185
msgid "Invalid Count"
msgstr ""

#: grab/business.py:159
msgid "Room Not Found"
msgstr ""

#: grab/business.py:161
msgid "Room is not Joinable"
msgstr ""

#: grab/business.py:165
msgid "Card is Picked"
msgstr ""

#: grab/models.py:22
msgid "Coupon"
msgstr ""

#: grab/models.py:27
msgid "Fulling"
msgstr ""

#: grab/models.py:28
msgid "Handling"
msgstr ""

#: grab/models.py:31
msgid "room id"
msgstr ""

#: grab/models.py:34
msgid "total position"
msgstr ""

#: grab/models.py:35
msgid "user max choice"
msgstr ""

#: grab/models.py:36
msgid "coins to one card"
msgstr ""

#: grab/models.py:37
msgid "item price"
msgstr ""

#: grab/models.py:38
msgid "room state"
msgstr ""

#: grab/models.py:39
msgid "position last"
msgstr ""

#: grab/models.py:43 grab/models.py:57
msgid "GrabRoom"
msgstr ""

#: grab/models.py:52 grab/models.py:64
msgid "grab position"
msgstr ""

#: grab/models.py:53
msgid "grab victory"
msgstr ""

#: grab/models.py:65
msgid "grab card image"
msgstr ""

#: grab/models.py:70
msgid "GrabCard"
msgstr ""

#: grab/models.py:79
msgid "join count"
msgstr ""

#: grab/models.py:80
msgid "join timestamp"
msgstr ""

#: libs/mail_server.py:45
msgid "Send verify code fail"
msgstr ""

#: lottery/apps.py:7
msgid "Lottery"
msgstr ""

#: lottery/models.py:14 lottery/models.py:34 lottery/models.py:63
msgid "Hourly"
msgstr ""

#: lottery/models.py:15 lottery/models.py:35 lottery/models.py:64
msgid "Daily"
msgstr ""

#: lottery/models.py:16 lottery/models.py:36 lottery/models.py:65
msgid "Weekly"
msgstr ""

#: lottery/models.py:18 lottery/models.py:45 lottery/models.py:70
msgid "lottery type"
msgstr ""

#: lottery/models.py:20
msgid "min charge"
msgstr ""

#: lottery/models.py:26
msgid "Lottery Setting"
msgstr ""

#: lottery/models.py:39
msgid "Not enough charge"
msgstr ""

#: lottery/models.py:40
msgid "Joined"
msgstr ""

#: lottery/models.py:46
msgid "charge amount"
msgstr ""

#: lottery/models.py:48
msgid "win"
msgstr ""

#: lottery/models.py:51
msgid "set winner"
msgstr ""

#: lottery/models.py:55
msgid "Lottery Joiner"
msgstr ""

#: lottery/models.py:68
msgid "comment"
msgstr ""

#: lottery/models.py:71
msgid "sort"
msgstr ""

#: lottery/models.py:76
msgid "Lottery Info Setting"
msgstr ""

#: luckybox/admin.py:27
msgid "默认图片"
msgstr ""

#: luckybox/admin.py:33
msgid "自定义图片"
msgstr ""

#: luckybox/admin.py:39
msgid "自定义点亮图片"
msgstr ""

#: luckybox/apps.py:6
msgid "LuckBox"
msgstr ""

#: luckybox/business.py:237
msgid "Items Invalid"
msgstr ""

#: luckybox/business.py:243
msgid "Items Not Found"
msgstr ""

#: luckybox/business.py:245 luckybox/business.py:247
msgid "Percent Invalid"
msgstr ""

#: luckybox/business.py:260
msgid "Open Lucky Box"
msgstr ""

#: luckybox/models.py:14 steambase/models.py:45
msgid "market hash name"
msgstr ""

#: luckybox/models.py:16 steambase/models.py:59
msgid "weapon"
msgstr ""

#: luckybox/models.py:18 steambase/models.py:52
msgid "icon url"
msgstr ""

#: luckybox/models.py:19 steambase/models.py:54
msgid "rarity"
msgstr ""

#: luckybox/models.py:23
msgid "LuckyBox Group"
msgstr ""

#: luckybox/models.py:51
msgid "LuckyBox Recommend Group"
msgstr ""

#: luckybox/models.py:66
msgid "LuckyBox Item"
msgstr ""

#: luckybox/models.py:81
msgid "LuckyBoxCategory"
msgstr ""

#: luckybox/models.py:88
msgid "luckybox item"
msgstr ""

#: luckybox/models.py:89
msgid "lucky origin percent"
msgstr ""

#: luckybox/models.py:90
msgid "lucky percent"
msgstr ""

#: luckybox/models.py:91
msgid "lucky coins"
msgstr ""

#: luckybox/models.py:92
msgid "lucky percentage"
msgstr ""

#: luckybox/models.py:93
msgid "lucky result"
msgstr ""

#: luckybox/models.py:94
msgid "luckybox target item"
msgstr ""

#: luckybox/models.py:99
msgid "LuckyBox Record"
msgstr ""

#: luckybox/service/admin_actions.py:33
msgid "sync group item hash name"
msgstr ""

#: luckybox/views.py:155
msgid "拉货维护中"
msgstr ""

#: market/business.py:107
msgid "余额不足, 请充值"
msgstr ""

#: market/models.py:14
msgid "item rarity cn"
msgstr ""

#: market/models.py:15
msgid "item exterior cn"
msgstr ""

#: market/models.py:16
msgid "item dark gold"
msgstr ""

#: market/models.py:20
msgid "MarketItem"
msgstr ""

#: monitor/apps.py:7 monitor/views.py:25
#: templates/admin_backup/base_copy_backup.html:293
msgid "Monitor"
msgstr ""

#: package/admin.py:106 package/admin.py:110
msgid "cancel_send"
msgstr ""

#: package/apps.py:7 tradeup/models.py:46
msgid "Item"
msgstr ""

#: package/business.py:182 withdraw/business.py:38 withdraw/business.py:159
msgid "请先输入您的交易链接"
msgstr ""

#: package/business.py:185
msgid "No bot online"
msgstr ""

#: package/business.py:197 package/business.py:615 promotion/business.py:54
#: promotion/business.py:57 roll/business.py:72 roll/business.py:151
#: roll/business.py:185 withdraw/business.py:134 withdraw/business.py:136
#: withdraw/business.py:187
msgid "Invalid params"
msgstr ""

#: package/business.py:265 package/business.py:425
msgid "Out of stock, please content customer service on the right side."
msgstr ""

#: package/business.py:332
msgid "一些饰品不存在或已被出售"
msgstr ""

#: package/business.py:345
msgid "出售饰品"
msgstr ""

#: package/business.py:389
msgid "没有可出售的饰品"
msgstr ""

#: package/business.py:433
msgid "Shop item"
msgstr ""

#: package/business.py:690 package/views.py:58
msgid "物品ID不能为空"
msgstr ""

#: package/business.py:703
msgid "没有找到该饰品"
msgstr ""

#: package/models.py:19
msgid "蓝色"
msgstr ""

#: package/models.py:20
msgid "灰色"
msgstr ""

#: package/models.py:21
msgid "粉色"
msgstr ""

#: package/models.py:22
msgid "红色"
msgstr ""

#: package/models.py:23
msgid "金色"
msgstr ""

#: package/models.py:24
msgid "紫色"
msgstr ""

#: package/models.py:25
msgid "深蓝色"
msgstr ""

#: package/models.py:30
msgid "custom icon"
msgstr ""

#: package/models.py:32
msgid "custom rarity"
msgstr ""

#: package/models.py:38
msgid "Quality"
msgstr ""

#: package/models.py:39
msgid "Rarity"
msgstr ""

#: package/models.py:40
msgid "Exterior"
msgstr ""

#: package/models.py:47
msgid "Item Info"
msgstr ""

#: package/models.py:54
msgid "Level"
msgstr ""

#: package/models.py:55 package/models.py:69 package/models.py:81
#: package/models.py:93
msgid "Is Show"
msgstr ""

#: package/models.py:56 package/models.py:70 package/models.py:82
#: package/models.py:94
msgid "Sort"
msgstr ""

#: package/models.py:62
msgid "Item Category"
msgstr ""

#: package/models.py:66
msgid "Quality Id"
msgstr ""

#: package/models.py:67
msgid "Quality Name"
msgstr ""

#: package/models.py:68
msgid "Quality Color"
msgstr ""

#: package/models.py:74
msgid "Item Quality"
msgstr ""

#: package/models.py:78
msgid "Rarity Id"
msgstr ""

#: package/models.py:79
msgid "Rarity Name"
msgstr ""

#: package/models.py:80
msgid "Rarity Color"
msgstr ""

#: package/models.py:86
msgid "Item Rarity"
msgstr ""

#: package/models.py:90
msgid "Exterior Id"
msgstr ""

#: package/models.py:91
msgid "Exterior Name"
msgstr ""

#: package/models.py:92
msgid "Exterior Color"
msgstr ""

#: package/models.py:98
msgid "Item Exterior"
msgstr ""

#: package/models.py:106 promotion/models.py:25
msgid "enable custom"
msgstr ""

#: package/models.py:107
msgid "custom price(USD)"
msgstr ""

#: package/models.py:108
msgid "custom discount(%)"
msgstr ""

#: package/models.py:109
msgid "waxpeer market price(USD)-0"
msgstr ""

#: package/models.py:110
msgid "zbt market price(USD)-0"
msgstr ""

#: package/models.py:111
msgid "Steam market price(USD)-0"
msgstr ""

#: package/models.py:112
msgid "Steam normal price(USD)-10"
msgstr ""

#: package/models.py:113
msgid "Steam sale price(USD)-11"
msgstr ""

#: package/models.py:114
msgid "lock price"
msgstr ""

#: package/models.py:115
msgid "lock price time"
msgstr ""

#: package/models.py:121
msgid "Item Price"
msgstr ""

#: package/models.py:163
msgid "Available"
msgstr ""

#: package/models.py:164
msgid "Blocked"
msgstr ""

#: package/models.py:165
msgid "Gaming"
msgstr ""

#: package/models.py:166
msgid "Withdrawing"
msgstr ""

#: package/models.py:167
msgid "Withdrawn"
msgstr ""

#: package/models.py:168
msgid "Exchanged"
msgstr ""

#: package/models.py:169
msgid "Invalid"
msgstr ""

#: package/models.py:176 roll/apps.py:7
msgid "Roll"
msgstr ""

#: package/models.py:177
msgid "BattleRoom"
msgstr ""

#: package/models.py:178
msgid "Giveaway"
msgstr ""

#: package/models.py:184 package/models.py:311 package/models.py:345
msgid "instanceid"
msgstr ""

#: package/models.py:186 package/models.py:292 package/models.py:346
msgid "bot Steamid"
msgstr ""

#: package/models.py:188
msgid "part of item"
msgstr ""

#: package/models.py:190
msgid "case name"
msgstr ""

#: package/models.py:191
msgid "case cover"
msgstr ""

#: package/models.py:192
msgid "case key"
msgstr ""

#: package/models.py:216
msgid "Package Item"
msgstr ""

#: package/models.py:221
msgid "price rate(%)"
msgstr ""

#: package/models.py:222
msgid "min price"
msgstr ""

#: package/models.py:223
msgid "max price"
msgstr ""

#: package/models.py:228
msgid "Item Price Rate Config"
msgstr ""

#: package/models.py:253
msgid "Exchange Record"
msgstr ""

#: package/models.py:269
msgid "Shop record"
msgstr ""

#: package/models.py:274 package/models.py:389 package/models.py:418
msgid "Deposit"
msgstr ""

#: package/models.py:275 package/models.py:390 package/models.py:419
msgid "Withdraw"
msgstr ""

#: package/models.py:281
msgid "Submitted"
msgstr ""

#: package/models.py:282
msgid "TradeNoUpdated"
msgstr ""

#: package/models.py:283 withdraw/models.py:21
msgid "Active"
msgstr ""

#: package/models.py:287 package/models.py:394 package/models.py:423
msgid "trade type"
msgstr ""

#: package/models.py:291
msgid "security code"
msgstr ""

#: package/models.py:293
msgid "bot message"
msgstr ""

#: package/models.py:294
msgid "trade time"
msgstr ""

#: package/models.py:295
msgid "review"
msgstr ""

#: package/models.py:303 withdraw/models.py:50
msgid "Trade Record"
msgstr ""

#: package/models.py:308
msgid "trade record"
msgstr ""

#: package/models.py:319
msgid "Trade Item"
msgstr ""

#: package/models.py:327
msgid "enable deposit"
msgstr ""

#: package/models.py:328
msgid "enable withdraw"
msgstr ""

#: package/models.py:330 roll/models.py:32
msgid "password"
msgstr ""

#: package/models.py:331
msgid "shared secret"
msgstr ""

#: package/models.py:332
msgid "identity secret"
msgstr ""

#: package/models.py:339
msgid "Trade Bot Config"
msgstr ""

#: package/models.py:347
msgid "matching"
msgstr ""

#: package/models.py:355
msgid "Trade Bot Inventory"
msgstr ""

#: package/models.py:368
msgid "Shop Bot Config"
msgstr ""

#: package/models.py:384
msgid "BlackList"
msgstr ""

#: package/models.py:413
msgid "Item Statistics Day"
msgstr ""

#: package/models.py:447
msgid "Item Statistics Month"
msgstr ""

#: package/models.py:451
msgid "last unlock time"
msgstr ""

#: package/models.py:452
msgid "interval(min)"
msgstr ""

#: package/models.py:460
msgid "Item Unlock Time Config"
msgstr ""

#: package/models.py:465
msgid "Count"
msgstr ""

#: package/models.py:473
msgid "Lock Items Statistics"
msgstr ""

#: package/models.py:484
msgid "Item Whitelist"
msgstr ""

#: package/service/admin_actions.py:42
msgid "Startup complete"
msgstr ""

#: package/service/admin_actions.py:44
msgid "Startup fail"
msgstr ""

#: package/service/admin_actions.py:47
msgid "Startup trade bot"
msgstr ""

#: package/service/admin_actions.py:58
msgid "Get inventory from Steam fail"
msgstr ""

#: package/service/admin_actions.py:86
msgid "Review complete"
msgstr ""

#: package/service/admin_actions.py:89
msgid "Review selected trade"
msgstr ""

#: package/service/admin_actions.py:94
msgid "Invalidate complete"
msgstr ""

#: package/service/admin_actions.py:97
msgid "Invalidate selected item"
msgstr ""

#: package/service/admin_actions.py:102
msgid "Exchanged complete"
msgstr ""

#: package/service/admin_actions.py:105
msgid "Exchanged selected item"
msgstr ""

#: package/service/admin_actions.py:110
msgid "Available complete"
msgstr ""

#: package/service/admin_actions.py:113
msgid "Available selected item"
msgstr ""

#: package/service/admin_actions.py:150
msgid "import item to luckybox"
msgstr ""

#: package/service/admin_actions.py:164
msgid "import item to custombox"
msgstr ""

#: package/service/admin_actions.py:222
msgid "背包饰品导出EXCEL"
msgstr ""

#: package/views.py:213
msgid "Deposit is under maintenance, please wait for a while."
msgstr ""

#: package/views.py:337
msgid "饰品售出子系统正在维护，请稍后再试。"
msgstr ""

#: package/views.py:363
msgid "Exchange is under maintenance, please wait for a while."
msgstr ""

#: package/views.py:382
msgid "Shop is under maintenance, please wait for a while."
msgstr ""

#: promotion/apps.py:7 promotion/apps.py:8
msgid "Promotion"
msgstr ""

#: promotion/business.py:27
msgid "Invalid promotion code"
msgstr ""

#: promotion/business.py:31
msgid "You have been bound another account"
msgstr ""

#: promotion/business.py:52
msgid "Code Length Must 4 to 8 digits"
msgstr ""

#: promotion/business.py:59
msgid "Code is already exists"
msgstr ""

#: promotion/business.py:69
msgid "邀请人数不足"
msgstr ""

#: promotion/business.py:77
msgid "您的收益不足5B"
msgstr ""

#: promotion/models.py:20
msgid "refer code"
msgstr ""

#: promotion/models.py:21
msgid "refer count"
msgstr ""

#: promotion/models.py:22 promotion/models.py:40
msgid "total charge"
msgstr ""

#: promotion/models.py:23
msgid "total profit"
msgstr ""

#: promotion/models.py:24
msgid "profit"
msgstr ""

#: promotion/models.py:26
msgid "refer level"
msgstr ""

#: promotion/models.py:27 promotion/models.py:60
msgid "profit rate(%)"
msgstr ""

#: promotion/models.py:31
msgid "User Promotion"
msgstr ""

#: promotion/models.py:38
msgid "refer"
msgstr ""

#: promotion/models.py:41
msgid "refer profit"
msgstr ""

#: promotion/models.py:47
msgid "Promotion Record"
msgstr ""

#: promotion/models.py:61
msgid "refer rate(%)"
msgstr ""

#: promotion/models.py:70
msgid "Promotion Level Config"
msgstr ""

#: promotion/models.py:83
msgid "PromotionCaseConfig"
msgstr ""

#: promotion/service/admin_actions.py:37
msgid "推广记录导出Excel"
msgstr ""

#: roll/admin.py:71 roll/models.py:87
msgid "Roll Room Bet"
msgstr ""

#: roll/admin.py:114
msgid "room uid"
msgstr ""

#: roll/business.py:74
msgid "Invalid fee"
msgstr ""

#: roll/business.py:197
msgid "存入数量必须大于获胜人数"
msgstr ""

#: roll/business.py:247
msgid "Invalid password"
msgstr ""

#: roll/business.py:263
msgid "充值金额小于参加条件"
msgstr ""

#: roll/business.py:267
msgid "Roll room bet"
msgstr ""

#: roll/business.py:269
msgid "Failed to update balance"
msgstr ""

#: roll/business.py:314
msgid "Somebody has joined"
msgstr ""

#: roll/business.py:320
msgid "Roll room cancel"
msgstr ""

#: roll/business.py:560 roll/business.py:675
msgid "Roll room fee"
msgstr ""

#: roll/business.py:771
msgid "Rollroom cancel"
msgstr ""

#: roll/business.py:1251
msgid "发生错误"
msgstr ""

#: roll/business.py:1336
msgid "存入数量必须等于获胜人数"
msgstr ""

#: roll/model_signals.py:61
msgid "Rollroom win"
msgstr ""

#: roll/models.py:21
msgid "No charge room"
msgstr ""

#: roll/models.py:22
msgid "Charge room"
msgstr ""

#: roll/models.py:25
msgid "主播"
msgstr ""

#: roll/models.py:26
msgid "官方"
msgstr ""

#: roll/models.py:33
msgid "entrance fee"
msgstr ""

#: roll/models.py:34
msgid "min joiner"
msgstr ""

#: roll/models.py:36
msgid "max winner"
msgstr ""

#: roll/models.py:37
msgid "begin time"
msgstr ""

#: roll/models.py:38
msgid "due time"
msgstr ""

#: roll/models.py:39
msgid "win time"
msgstr ""

#: roll/models.py:40
msgid "total fee"
msgstr ""

#: roll/models.py:43
msgid "total items count"
msgstr ""

#: roll/models.py:45
msgid "deposit enable"
msgstr ""

#: roll/models.py:46
msgid "rollroom enable"
msgstr ""

#: roll/models.py:47
msgid "charge limit amount"
msgstr ""

#: roll/models.py:48
msgid "charge begin time"
msgstr ""

#: roll/models.py:49
msgid "charge end time"
msgstr ""

#: roll/models.py:50
msgid "official"
msgstr ""

#: roll/models.py:53
msgid "debug"
msgstr ""

#: roll/models.py:71
msgid "Roll Room"
msgstr ""

#: roll/models.py:102
msgid "Item ID"
msgstr ""

#: roll/models.py:106
msgid "Roll Room Item"
msgstr ""

#: roll/models.py:142
msgid "Roll Room Pump Day"
msgstr ""

#: roll/models.py:172
msgid "Roll Room Pump Month"
msgstr ""

#: roll/models.py:177
msgid "join idle min(seconds)"
msgstr ""

#: roll/models.py:178
msgid "join idle max(seconds)"
msgstr ""

#: roll/models.py:184
msgid "Rollroom Bot Config"
msgstr ""

#: roll/models.py:191
msgid "bot"
msgstr ""

#: roll/models.py:196
msgid "Roll房机器人"
msgstr ""

#: roll/models.py:212
msgid "Rollroom Statistics Day"
msgstr ""

#: roll/models.py:272
msgid "Rollroom Statistics Month"
msgstr ""

#: roll/service/admin_actions.py:26
msgid "Cancel rollroom state complete"
msgstr ""

#: roll/service/admin_actions.py:28
msgid "Cancel rollroom state"
msgstr ""

#: roll/service/admin_actions.py:40
msgid "delete rollroom complete"
msgstr ""

#: roll/service/admin_actions.py:42
msgid "Delete rollroom"
msgstr ""

#: roll/views.py:38 roll/views.py:78 roll/views.py:107 roll/views.py:137
#: roll/views.py:158 roll/views.py:498
msgid "月度福利板块正在维护"
msgstr ""

#: roll/views.py:468
msgid "福利房间维护中，请稍后再试"
msgstr ""

#: sitecfg/apps.py:7 sitecfg/models.py:33
msgid "Site Config"
msgstr ""

#: sitecfg/models.py:14
msgid "Site Config Category"
msgstr ""

#: sitecfg/models.py:24
msgid "value"
msgstr ""

#: sitecfg/models.py:29 sitecfg/models.py:73 sitecfg/models.py:89
#: sitecfg/models.py:103 sitecfg/models.py:125
msgid "order No."
msgstr ""

#: sitecfg/models.py:63
msgid "Link"
msgstr ""

#: sitecfg/models.py:68 sitecfg/models.py:128
msgid "link"
msgstr ""

#: sitecfg/models.py:71
msgid "Content ID"
msgstr ""

#: sitecfg/models.py:72
msgid "Reference to articles.Content model"
msgstr ""

#: sitecfg/models.py:79
msgid "Footer"
msgstr ""

#: sitecfg/models.py:109
msgid "Support"
msgstr ""

#: sitecfg/models.py:117
msgid "All"
msgstr ""

#: sitecfg/models.py:118
msgid "PC"
msgstr ""

#: sitecfg/models.py:119
msgid "Mobile"
msgstr ""

#: sitecfg/models.py:124
msgid "is simple"
msgstr ""

#: sitecfg/models.py:129
msgid "background class"
msgstr ""

#: sitecfg/models.py:130
msgid "glow class"
msgstr ""

#: sitecfg/models.py:131
msgid "primary button text"
msgstr ""

#: sitecfg/models.py:132
msgid "primary button link"
msgstr ""

#: sitecfg/models.py:133
msgid "secondary button text"
msgstr ""

#: sitecfg/models.py:134
msgid "secondary button link"
msgstr ""

#: sitecfg/models.py:142
msgid "Banner"
msgstr ""

#: sitecfg/models.py:188
msgid "Keywords"
msgstr ""

#: sitecfg/models.py:191
msgid "ICP"
msgstr ""

#: sitecfg/models.py:193
msgid "News Enable"
msgstr ""

#: sitecfg/models.py:207
msgid "IP"
msgstr ""

#: sitecfg/models.py:210
msgid "remarks"
msgstr ""

#: sitecfg/service/admin_actions.py:18
msgid "set online users base count complete"
msgstr ""

#: sitecfg/service/admin_actions.py:20
msgid "set online users base count fail"
msgstr ""

#: sitecfg/service/admin_actions.py:22
msgid "set online base count"
msgstr ""

#: steambase/models.py:21
msgid "uid"
msgstr ""

#: steambase/models.py:37
msgid "WearCategory0"
msgstr ""

#: steambase/models.py:38
msgid "WearCategory1"
msgstr ""

#: steambase/models.py:39
msgid "WearCategory2"
msgstr ""

#: steambase/models.py:40
msgid "WearCategory3"
msgstr ""

#: steambase/models.py:41
msgid "WearCategory4"
msgstr ""

#: steambase/models.py:46
msgid "name(CN)"
msgstr ""

#: steambase/models.py:47
msgid "market name(CN)"
msgstr ""

#: steambase/models.py:48
msgid "name color"
msgstr ""

#: steambase/models.py:50
msgid "contextid"
msgstr ""

#: steambase/models.py:51
msgid "classid"
msgstr ""

#: steambase/models.py:53
msgid "icon url large"
msgstr ""

#: steambase/models.py:55
msgid "rarity color"
msgstr ""

#: steambase/models.py:56
msgid "quality"
msgstr ""

#: steambase/models.py:57
msgid "quality color"
msgstr ""

#: steambase/models.py:60
msgid "exterior"
msgstr ""

#: steambase/models.py:61
msgid "item set"
msgstr ""

#: steambase/models.py:62
msgid "slot"
msgstr ""

#: steambase/models.py:63
msgid "hero"
msgstr ""

#: templates/404.html:5
msgid "Not Found"
msgstr ""

#: templates/404.html:10
msgid "Page Not Found"
msgstr ""

#: templates/404.html:13 templates/500.html:16
msgid "Go to Home Page"
msgstr ""

#: templates/500.html:5 templates/500.html:9
msgid "Server Exception"
msgstr ""

#: templates/500.html:13
msgid "Page Not Found111"
msgstr ""

#: templates/admin/base_site.html:3
msgid "Django site admin"
msgstr ""

#: templates/admin/base_site.html:6
msgid "Django administration"
msgstr ""

#: templates/admin_backup/base_copy_backup.html:60
#: templates/admin_backup/base_jet_backup.html:60
msgid "Welcome,"
msgstr ""

#: templates/admin_backup/base_copy_backup.html:65
#: templates/admin_backup/base_copy_backup.html:182
#: templates/admin_backup/base_jet_backup.html:65
#: templates/admin_backup/base_jet_backup.html:182
msgid "View site"
msgstr ""

#: templates/admin_backup/base_copy_backup.html:70
#: templates/admin_backup/base_copy_backup.html:191
#: templates/admin_backup/base_jet_backup.html:70
#: templates/admin_backup/base_jet_backup.html:191
msgid "Documentation"
msgstr ""

#: templates/admin_backup/base_copy_backup.html:74
#: templates/admin_backup/base_jet_backup.html:74
msgid "Change password"
msgstr ""

#: templates/admin_backup/base_copy_backup.html:76
#: templates/admin_backup/base_jet_backup.html:76
msgid "Log out"
msgstr ""

#: templates/admin_backup/base_copy_backup.html:86
#: templates/admin_backup/base_copy_backup.html:174
#: templates/admin_backup/base_jet_backup.html:86
#: templates/admin_backup/base_jet_backup.html:174
#: templates/admin_backup/change_form.html:23
msgid "Home"
msgstr ""

#: templates/admin_backup/base_copy_backup.html:154
#: templates/admin_backup/base_jet_backup.html:154
msgid "back"
msgstr ""

#: templates/admin_backup/base_copy_backup.html:235
#: templates/admin_backup/base_jet_backup.html:235
msgid "Applications"
msgstr ""

#: templates/admin_backup/base_copy_backup.html:259
#: templates/admin_backup/base_jet_backup.html:259
msgid "Hide applications"
msgstr ""

#: templates/admin_backup/base_copy_backup.html:260
#: templates/admin_backup/base_jet_backup.html:260
msgid "Show hidden"
msgstr ""

#: templates/admin_backup/base_copy_backup.html:302
#: templates/admin_backup/base_copy_backup.html:321
#: templates/admin_backup/base_jet_backup.html:286
#: templates/admin_backup/base_jet_backup.html:305
msgid "Add bookmark"
msgstr ""

#: templates/admin_backup/base_copy_backup.html:307
#: templates/admin_backup/base_jet_backup.html:291
msgid "URL"
msgstr ""

#: templates/admin_backup/base_copy_backup.html:315
#: templates/admin_backup/base_jet_backup.html:299
msgid "Delete bookmark"
msgstr ""

#: templates/admin_backup/base_copy_backup.html:316
#: templates/admin_backup/base_jet_backup.html:300
msgid "Are you sure want to delete this bookmark?"
msgstr ""

#: templates/admin_backup/base_copy_backup.html:323
#: templates/admin_backup/base_jet_backup.html:307
msgid "bookmarks"
msgstr ""

#: templates/admin_backup/base_copy_backup.html:331
#: templates/admin_backup/base_copy_backup.html:338
#: templates/admin_backup/base_jet_backup.html:315
#: templates/admin_backup/base_jet_backup.html:322
msgid "Remove"
msgstr ""

#: templates/admin_backup/base_copy_backup.html:360
#: templates/admin_backup/base_jet_backup.html:344
msgid "Search"
msgstr ""

#: templates/admin_backup/base_copy_backup.html:365
#: templates/admin_backup/base_jet_backup.html:349
msgid "Application page"
msgstr ""

#: templates/admin_backup/base_copy_backup.html:391
#: templates/admin_backup/base_jet_backup.html:375
msgid "current theme"
msgstr ""

#: templates/admin_backup/change_form.html:26
#, python-format
msgid "Add %(name)s"
msgstr ""

#: templates/admin_backup/change_form.html:38
msgid "History"
msgstr ""

#: templates/admin_backup/change_form.html:40
msgid "View on site"
msgstr ""

#: templates/admin_backup/change_form.html:52
#: templates/admin_backup/login.html:21
msgid "Please correct the error below."
msgstr ""

#: templates/admin_backup/change_form.html:52
#: templates/admin_backup/login.html:21
msgid "Please correct the errors below."
msgstr ""

#: templates/admin_backup/login.html:37
#, python-format
msgid ""
"You are authenticated as %(username)s, but are not authorized to access this "
"page. Would you like to login to a different account?"
msgstr ""

#: templates/admin_backup/login.html:79
msgid "Forgotten your password or username?"
msgstr ""

#: templates/admin_backup/login.html:83
msgid "Log in"
msgstr ""

#: templates/admin_backup/monitor.html:127
msgid "Connecting"
msgstr ""

#: tradeup/apps.py:7
msgid "Tradeup"
msgstr ""

#: tradeup/business.py:57 tradeup/business.py:100 tradeup/business.py:495
#: tradeup/business.py:532
msgid "Invalid bet items"
msgstr ""

#: tradeup/business.py:110 tradeup/business.py:196 tradeup/business.py:503
#: tradeup/business.py:540
msgid "Invalid target items"
msgstr ""

#: tradeup/business.py:126 tradeup/business.py:209
msgid "Coins Tradeup game bet"
msgstr ""

#: tradeup/business.py:132 tradeup/business.py:217 tradeup/business.py:520
#: tradeup/business.py:549
msgid "Invalid bet percentage"
msgstr ""

#: tradeup/business.py:202
msgid "Invalid target appid"
msgstr ""

#: tradeup/business.py:297
msgid "Coins Tradeup game win"
msgstr ""

#: tradeup/models.py:27
msgid "bet percentage"
msgstr ""

#: tradeup/models.py:28
msgid "upper"
msgstr ""

#: tradeup/models.py:29
msgid "origin amount"
msgstr ""

#: tradeup/models.py:30
msgid "target amount"
msgstr ""

#: tradeup/models.py:38
msgid "Tradeup Game"
msgstr ""

#: tradeup/models.py:52
msgid "bet item type"
msgstr ""

#: tradeup/models.py:56
msgid "Tradeup Bet Item"
msgstr ""

#: tradeup/models.py:75
msgid "Coins Tradeup Bet amount"
msgstr ""

#: tradeup/models.py:88
msgid "Tradeup Target Item"
msgstr ""

#: tradeup/models.py:103
msgid "Tradeup Inventory Item"
msgstr ""

#: tradeup/models.py:116
msgid "Tradeup Pump Day"
msgstr ""

#: tradeup/models.py:140
msgid "Tradeup Pump Month"
msgstr ""

#: tradeup/models.py:174
msgid "Tradeup Statistics Day"
msgstr ""

#: tradeup/models.py:258
msgid "Tradeup Statistics Month"
msgstr ""

#: tradeup/models.py:347
msgid "Tradeup Win Day Rank"
msgstr ""

#: tradeup/models.py:372
msgid "Tradeup Win Week Rank"
msgstr ""

#: tradeup/models.py:396
msgid "bet idle min(seconds)"
msgstr ""

#: tradeup/models.py:397
msgid "bet idle max(seconds)"
msgstr ""

#: tradeup/models.py:398
msgid "bot tradeup min chance"
msgstr ""

#: tradeup/models.py:399
msgid "bot tradeup max chance"
msgstr ""

#: tradeup/models.py:400
msgid "bot item price min"
msgstr ""

#: tradeup/models.py:401
msgid "bot item price max"
msgstr ""

#: tradeup/models.py:407
msgid "Tradeup Bot Config"
msgstr ""

#: tradeup/service/admin_actions.py:21
msgid "Sync Tradeup Price complete"
msgstr ""

#: tradeup/service/admin_actions.py:23
msgid "Sync Tradeup Price state"
msgstr ""

#: websocket/apps.py:7
msgid "Websocket"
msgstr ""

#: withdraw/apps.py:7
msgid "WXP取回"
msgstr ""

#: withdraw/business.py:33
msgid "您需累计充值15$才能提取饰品"
msgstr ""

#: withdraw/business.py:43
msgid "You have another unclosed trade"
msgstr ""

#: withdraw/business.py:46
msgid "Invalid package items"
msgstr ""

#: withdraw/business.py:50
msgid "Minimum withdrawal amount"
msgstr ""

#: withdraw/business.py:168
msgid "Error price"
msgstr ""

#: withdraw/business.py:177
msgid "Over withdraw price"
msgstr ""

#: withdraw/business.py:185
msgid "Items is not allowed, please try another items"
msgstr ""

#: withdraw/business.py:236
msgid "success"
msgstr ""

#: withdraw/business.py:320
msgid "close trade error"
msgstr ""

#: withdraw/models.py:22
msgid "Closed"
msgstr ""

#: withdraw/models.py:23
msgid "PendClose"
msgstr ""

#: withdraw/models.py:27
msgid "waiting for user to buy more items"
msgstr ""

#: withdraw/models.py:28
msgid "Proccessing the trade"
msgstr ""

#: withdraw/models.py:29
msgid "Waiting for seller to confirm"
msgstr ""

#: withdraw/models.py:30
msgid "Trade Sent"
msgstr ""

#: withdraw/models.py:31
msgid "Completed"
msgstr ""

#: withdraw/models.py:32
msgid "Declined and Refunded"
msgstr ""

#: withdraw/models.py:42
msgid "amount used"
msgstr ""

#: withdraw/models.py:43
msgid "drop refund"
msgstr ""

#: withdraw/models.py:44
msgid "balance refund"
msgstr ""

#: withdraw/models.py:45
msgid "trade state"
msgstr ""

#: withdraw/models.py:57 withdraw/models.py:71
msgid "trade id"
msgstr ""

#: withdraw/models.py:59
msgid "returned"
msgstr ""

#: withdraw/models.py:64
msgid "Withdraw Item"
msgstr ""

#: withdraw/models.py:72
msgid "project id"
msgstr ""

#: withdraw/models.py:73
msgid "waxpeer id"
msgstr ""

#: withdraw/models.py:74
msgid "waxpeer item id"
msgstr ""

#: withdraw/models.py:75
msgid "waxpeer withdraw status"
msgstr ""

#: withdraw/models.py:77
msgid "done"
msgstr ""

#: withdraw/models.py:81
msgid "steam trade id"
msgstr ""

#: withdraw/models.py:86
msgid "Wxp Trade Offer"
msgstr ""

#: withdraw/models.py:108
msgid "Trade Statistics Day"
msgstr ""

#: withdraw/models.py:151
msgid "Trade Statistics Month"
msgstr ""
