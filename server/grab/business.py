import json
import logging
import threading
import time
from datetime import datetime

from django.conf import settings
from django.core.cache import cache
from django.db import transaction
from django.db.models import Count
from django.utils.translation import gettext_lazy as _

from authentication.serializers import AuthUserSerializer
from grab.models import GrabRoom, GrabBet, GrabCard, GrabHistory
from grab.serializers import GrabRoomSerializer, GrabBetSerializer, GrabHistorySerializer, GrabCardSerializer
from package.models import PackageItem
from package.service.item import get_item_price
from steambase.enums import GrabRoomStatus, RespCode, PackageState, PackageSourceType, GrabCurrencyType

_logger = logging.getLogger(__name__)
_grab_room_key = "grab_room:{}"


def get_grab_room_list(user, state, coins, sort, symbol, join):
    query = {}
    if state:
        if int(state) == 0:
            query['state'] = GrabRoomStatus.End.value
        if int(state) == 1:
            query['state'] = GrabRoomStatus.Joinable.value
    if coins:
        # 金币还是抢卡券
        if int(coins) == 0:
            query['curreny'] = GrabCurrencyType.Coupon.value
        else:
            query['curreny'] = GrabCurrencyType.Coins.value
            query['coins'] = int(coins)
    if join:
        query['user'] = user
    rooms = GrabRoom.objects.filter(**query).order_by()
    if sort:
        if sort == "price":
            order_string = "price" if int(symbol) else "-price"
            rooms = rooms.order_by(order_string)
        if sort == "card":
            order_string = "-position_last" if int(symbol) else "position_last"
            rooms = rooms.order_by(order_string)

    fields = ("rid", "uid", "currency", "item_info", "position", "coins", "price", "position_last")
    room_data = GrabRoomSerializer(rooms, many=True, fields=fields).data
    return RespCode.Succeed.value, room_data


def get_grab_user(user):
    if not user:
        return RespCode.NotLogin.value, _("Please Login First")
    fields = ()
    user_data = AuthUserSerializer(user, fields=fields).data
    return RespCode.Succeed.value, user_data


def get_grab_room_detail(user, rid):
    grab_room = GrabRoom.objects.filter(rid=rid).first()
    if not grab_room:
        return RespCode.NotFound.value, _("Grab Room Not Found")
    fields = ("item_info", "uid", "price", "curreny", "position", "coins", "state", "position_last", "update_time")
    room_data = GrabRoomSerializer(grab_room, fields=fields).data
    room_max = grab_room.user_max_choice
    if not user:
        room_data['room_max'] = room_max
    else:
        grab_user_count = GrabBet.objects.filter(room=grab_room, user=user).count()
        room_data['room_max'] = room_max - grab_user_count
    if grab_room.state == GrabRoomStatus.End.value:
        # 中奖公示
        notice = {}
        history = GrabHistory.objects.filter(room=grab_room).all()
        total_timestamp = 0
        for his in history:
            total_timestamp += int(his.timestamp)
        remainder = total_timestamp % grab_room.position
        result = remainder + 1
        fields = ("user", "create_time", "timestamp", "count")
        users = GrabHistorySerializer(history, many=True, fields=fields).data
        card = GrabCard.objects.filter(position=result).first()
        notice.update(timestamp=total_timestamp, result=result, remainder=remainder, user=users, image=card.image.url)
        room_data.update(notice=notice)
        # 中奖者信息
        winner = {}
        winner_bet = GrabBet.objects.filter(room=grab_room, position=result).first()
        winner_user = winner_bet.user
        user_info = AuthUserSerializer(winner_user, fields=('uid', 'steam', 'nick_name')).data
        winner_owner_card = GrabBet.objects.filter(room=grab_room, user=winner_user)
        winner_count = winner_owner_card.count()
        cards = GrabCard.objects.filter(position__in=[card.position for card in winner_owner_card])
        cards_data = GrabCardSerializer(cards, many=True, read_only=True, fields=('position', 'image')).data
        winner.update(user=user_info, count=winner_count, cards=cards_data)
        room_data.update(winner=winner)
    return RespCode.Succeed.value, room_data


def get_grab_room_position(user, rid):
    grab_room = GrabRoom.objects.filter(rid=rid).first()
    if not grab_room:
        return RespCode.NotFound.value, _("Grab Room Not Found")
    card = cache.get(_grab_room_key.format(grab_room.uid))
    if not card:
        resp = []
        for position in range(1, grab_room.position + 1):
            card = GrabCard.objects.filter(position=position).first()
            bet = GrabBet.objects.filter(room=grab_room, position=position)
            resp.append({
                position: {
                    "number": "{:04d}".format(card.position),
                    "image": card.image.url,
                    "user": AuthUserSerializer(
                        bet.user, read_only=True, fields=('uid', 'steam', 'nick_name')).data
                }
            })
        cache.set(_grab_room_key.format(grab_room.uid), json.dumps(resp), timeout=settings.DAY_REDIS_TIMEOUT)
    else:
        resp = json.loads(card)
    return RespCode.Succeed.value, resp


def get_grab_dynamic():
    pass


def get_grab_user_history(user):
    if not user:
        return RespCode.NotLogin.value, _("Please Login First")
    history = GrabHistory.objects.filter(user=user).all()
    room_data = []
    for his in history:
        fields = ("rid", "item_info", "state", "position")
        data = GrabRoomSerializer(his.room, fields=fields).data
        join_count = his.count
        if his.room.state == GrabRoomStatus.End.value:
            endtime = his.room.update_time
            bet = GrabBet.objects.filter(room=his.room, victory=1).first()
            if bet.user == user:
                data.update(win=True)
            else:
                data.update(win=False)
            data.update(endtime=endtime)
        data.update(join_count=join_count, join_time=his.create_time)
        room_data.append(data)
    return RespCode.Succeed.value, room_data


def join_grab_room(user, nums, rid):
    if not user:
        return RespCode.NotLogin.value, _("Please Login First")
    if not nums:
        return RespCode.InvalidParams.value, _("Invalid Count")
    grab_room = GrabRoom.objects.filter(rid=rid).first()
    if not grab_room:
        return RespCode.NotFound.value, _("Room Not Found")
    if grab_room.state != GrabRoomStatus.Joinable.value:
        return RespCode.Exception.value, _("Room is not Joinable")
    with transaction.atomic():
        grab_bets = GrabBet.objects.select_for_update().filter(position__in=list(map(int, nums)), user=None, room=grab_room).all()
        if not grab_bets:
            return RespCode.NoPermission.value, _("Card is Picked")
        for bet in grab_bets:
            bet.user = user
            bet.save()
        GrabHistory.objects.create(
            user=user, room=grab_room, count=len(nums),
            timestamp=str(int(time.time() * 1000))
        )
    return RespCode.Succeed.value, {}


def pick_grab_room_card(user, rid, count):
    if not user:
        return RespCode.NotLogin.value, _("Please Login First")
    grab_room = GrabRoom.objects.filter(rid=rid).first()
    if not grab_room:
        return RespCode.NotFound.value, _("Grab Room Not Found")
    grab_user_count = GrabBet.objects.filter(room=grab_room, user=user).count()
    grab_room_max = grab_room.user_max_choice
    if count < 0 or count > grab_room_max or count > grab_room_max - grab_user_count:
        return RespCode.InvalidParams.value, _("Invalid Count")
    spare_card = GrabBet.objects.filter(room=grab_room, user=None).all().order_by("?")[:count]
    fields = ("uid", "position", "image")
    resp = GrabBetSerializer(spare_card, many=True, fields=fields).data
    return RespCode.Succeed.value, resp


def check_room_state():
    while True:
        try:
            grab_rooms = GrabRoom.objects.filter(state__in=[GrabRoomStatus.Joinable.value,]).all()
            for grab in grab_rooms:
                full = GrabBet.objects.filter(room=grab, user__isnull=True).count()
                if full == 0:
                    ready_to_run_room(grab.uid)
        except Exception as E:
            _logger.error(E)
        else:
            time.sleep(10)


def check_run_grab_room():
    while True:
        try:
            rooms = GrabRoom.objects.filter(state__in=[GrabRoomStatus.Fulling.value,]).all()
            for room in rooms:
                run_grab_room(room.uid)
        except Exception as e:
            _logger.exception(e)
        finally:
            time.sleep(10)


def setup_check_room_state_worker():
    th = threading.Thread(target=check_room_state)
    th.start()


def setup_check_run_grab_room_worker():
    th = threading.Thread(target=check_run_grab_room)
    th.start()


def ready_to_run_room(uid):
    try:
        _logger.info('Ready To Run case room: {}'.format(uid))
        with transaction.atomic():
            room = GrabRoom.objects.select_for_update().filter(uid=uid).first()
            if not room:
                return
            room.state = GrabRoomStatus.Fulling.value
            room.save()
    except Exception as e:
        _logger.exception(e)


def run_grab_room(uid):
    _logger.info('Run Grab Room: {}'.format(uid))
    with transaction.atomic():
        room = GrabRoom.objects.select_for_update().filter(uid=uid).first()
        if not room:
            return
        room.state = GrabRoomStatus.Handling.value
        room.save()

        bets = GrabBet.objects.filter(room=room).only("update_time").order_by("-update_time")[:50]
        total_time = 0
        for bet in bets:
            update_time = bet.update_time
            timestamp = int(update_time.timestamp() * 1000)
            total_time += timestamp
        position_result = total_time % room.position + 1
        win_bet = GrabBet.objects.filter(room=room, position=position_result).first()
        win_bet.victory = True
        win_bet.save()

        win_user = win_bet.user
        PackageItem.objects.create(
            user=win_user, item_info=room.item_info,
            assetid='0', instanceid='0',
            state=PackageState.Available.value,
            source=PackageSourceType.Grab.value,
            amount=get_item_price(room.item_info.market_hash_name)
        )
        room.state = GrabRoomStatus.End.value
        room.save()



