<template>
  <NuxtLink :to="`/skins/${skinItem.id}`" class="skin-card-link group">
    <div
      class="skin-card relative overflow-hidden transition-all duration-300 group-hover:scale-[1.02] group-hover:-translate-y-1"
    >
      <!-- 主卡片容器 - 统一圆角 -->
      <div
        class="relative bg-gradient-to-br from-gray-800/90 to-gray-900/90 rounded-xl border border-gray-700/30 backdrop-blur-md overflow-hidden h-full"
      >
        <!-- 背景装饰网格 -->
        <div class="absolute inset-0 bg-grid-pattern opacity-5"></div>

        <!-- 悬停时的渐变装饰 -->
        <div
          class="absolute -inset-px bg-gradient-to-r from-primary/20 via-transparent to-secondary/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"
        ></div>

        <!-- StatTrak™ 角标 - 统一为ST显示 -->
        <div v-if="isStatTrak" class="absolute left-2 top-2 z-20">
          <div
            class="bg-orange-500/90 rounded-md px-1.5 py-0.5 text-xs font-bold text-white shadow-lg backdrop-blur-sm border border-orange-400/50 flex items-center gap-1"
          >
            <span class="text-yellow-300 text-[10px]">⚡</span>
            <span class="text-[10px]">ST</span>
          </div>
        </div>

        <!-- 外观标签 - 右上角 -->
        <div v-if="getExteriorName" class="absolute right-2 top-2 z-20">
          <div
            class="px-1.5 py-0.5 text-[10px] font-semibold text-white shadow-lg backdrop-blur-sm rounded-md bg-gray-800/80 border border-gray-600/50 transition-all duration-300 group-hover:border-primary/50"
          >
            {{ getExteriorName }}
          </div>
        </div>

        <!-- 图片容器 - 调整比例，增加上边距避免角标重叠 -->
        <div
          class="relative aspect-[4/3] flex justify-center items-center p-3 pt-8"
        >
          <!-- 背景光效 -->
          <div
            class="absolute inset-0 bg-gradient-to-br from-primary/5 to-secondary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
          ></div>

          <!-- 皮肤图片 -->
          <div class="relative w-full h-full flex items-center justify-center">
            <img
              v-if="skinItem?.image"
              :src="skinItem.image"
              :alt="getSkinFullName"
              class="max-w-full max-h-full object-contain transition-all duration-500 ease-out group-hover:scale-110 drop-shadow-lg filter"
              @error="handleSkinImageError"
            />
            <div
              v-else
              class="w-full h-full flex items-center justify-center bg-gray-700/30 rounded-lg"
            >
              <div class="text-center">
                <div class="text-2xl mb-1">🔫</div>
                <span class="text-gray-400 text-[10px]">{{
                  $t("skins.image_error")
                }}</span>
              </div>
            </div>
          </div>

          <!-- 高光效果 -->
          <div
            class="absolute inset-0 bg-gradient-to-tr from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-700 ease-in-out transform -rotate-12 blur-sm"
          ></div>

          <!-- 稀有度发光边框 -->
          <div
            class="absolute inset-0 rounded-lg opacity-0 group-hover:opacity-60 transition-opacity duration-300"
            :style="{ boxShadow: getRarityGlow }"
          ></div>
        </div>

        <!-- 稀有度指示条 - 底部全宽，使用API颜色 -->
        <div
          class="absolute bottom-0 left-0 right-0 h-1 opacity-80"
          :style="{ backgroundColor: getRarityColor }"
        ></div>

        <!-- 内容部分 - 紧凑布局 -->
        <div
          class="relative p-3 space-y-2 bg-gradient-to-t from-gray-900/50 to-transparent"
        >
          <!-- 皮肤名称 -->
          <div class="text-center">
            <h3
              class="text-xs font-semibold text-white group-hover:text-primary transition-colors duration-300 leading-tight line-clamp-1"
              :title="getSkinFullName"
            >
              {{ getSkinName }}
            </h3>
            <!-- 武器名称 -->
            <p class="text-[10px] text-gray-400 mt-0.5 truncate">
              {{ getWeaponName }}
            </p>
          </div>

          <!-- 底部信息栏 -->
          <div
            class="flex justify-between items-center pt-1.5 border-t border-gray-700/30"
          >
            <!-- 价格 -->
            <div class="flex items-center text-emerald-400">
              <div
                class="w-2 h-2 mr-1 bg-emerald-400/20 rounded-full flex items-center justify-center"
              >
                <div class="w-1 h-1 bg-emerald-400 rounded-full"></div>
              </div>
              <span class="text-xs font-bold"> ${{ formatPrice }} </span>
            </div>

            <!-- 稀有度标识 -->
            <div v-if="show_rarityName" class="flex items-center">
              <div
                class="w-2 h-2 rounded-full border border-white/20"
                :style="{ backgroundColor: getRarityColor }"
              ></div>
              <span
              
                class="text-[10px] text-gray-400 ml-1 font-medium truncate max-w-[60px]"
              >
                {{ getRarityName }}
              </span>
            </div>
          </div>
        </div>

        <!-- 悬停时的粒子效果装饰 -->
        <div
          class="absolute top-3 right-3 w-1.5 h-1.5 bg-primary/60 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-300 animate-pulse"
        ></div>
        <div
          class="absolute bottom-4 left-3 w-1 h-1 bg-secondary/60 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-500 animate-pulse"
          style="animation-delay: 0.2s"
        ></div>
        <div
          class="absolute top-1/2 left-4 w-0.5 h-0.5 bg-primary/40 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-700 animate-pulse"
          style="animation-delay: 0.4s"
        ></div>
      </div>
    </div>
  </NuxtLink>
</template>

<script setup lang="ts">
import type { SkinItem } from "~/types/skin";

// Props
const props = defineProps<{
  skinItem: SkinItem;
  show_rarityName?: boolean; // 是否显示稀有度名称
  isLocalized?: boolean;
}>();

// 组合函数
const { t, locale } = useI18n();

// 监听语言变化，确保组件响应
const localeKey = computed(() => locale.value);

// 计算属性 - 响应式处理皮肤数据
const isStatTrak = computed(() => {
  return (
    props.skinItem?.isStatTrak ||
    props.skinItem?.name?.includes("StatTrak™") ||
    false
  );
});

// 获取完整的皮肤名称 - 响应式
const getSkinFullName = computed((): string => {
  if (!props.skinItem) return t("skins.unknown");

  const currentLocale = locale.value === "zh-hans" ? "zh_hans" : locale.value;
  const itemWithI18n = props.skinItem as any;

  // 获取本地化名称
  let localizedName = props.skinItem.name || t("skins.unknown");

  if (currentLocale === "zh_hans") {
    localizedName =
      itemWithI18n.name_zh_hans ||
      itemWithI18n.name_zh ||
      props.skinItem.name ||
      t("skins.unknown");
  } else if (currentLocale === "en") {
    localizedName =
      itemWithI18n.name_en || props.skinItem.name || t("skins.unknown");
  }

  return localizedName;
});

// 分离武器名称和皮肤名称 - 响应式
const getSkinName = computed((): string => {
  // 如果已经有分离的skin字段，优先使用（已经处理过多语言的）
  if (props.skinItem.skin) {
    return props.skinItem.skin;
  }

  // 否则从完整名称中分离
  const fullName = getSkinFullName.value;
  const parts = fullName.split(" | ");
  if (parts.length < 2) return fullName;

  const skinPart = parts[1].trim();
  return skinPart.split(" (")[0].trim();
});

// 获取武器名称 - 响应式
const getWeaponName = computed((): string => {
  // 如果已经有分离的weapon字段，优先使用
  if (props.skinItem.weapon) {
    return props.skinItem.weapon;
  }

  // 否则从完整名称中分离
  const fullName = getSkinFullName.value;
  const parts = fullName.split(" | ");
  return parts[0].trim();
});

// 获取磨损信息 - 响应式
const getExterior = computed((): string => {
  // 如果已经有分离的exterior字段，优先使用
  if (props.skinItem.exterior) {
    return props.skinItem.exterior;
  }

  // 否则从完整名称中提取
  const fullName = getSkinFullName.value;
  const match = fullName.match(/\(([^)]+)\)$/);
  return match ? match[1] : "";
});

// 价格格式化 - 响应式
const formatPrice = computed((): string => {
  // 尝试多种可能的价格字段
  const skinItemAny = props.skinItem as any;
  let price = 0;

  // 优先级：item_price.price > price > 0
  if (skinItemAny?.item_price?.price) {
    price = skinItemAny.item_price.price;
  } else if (props.skinItem?.price) {
    price = props.skinItem.price;
  } else if (skinItemAny?.price) {
    price = skinItemAny.price;
  }

  if (!price || price === 0) return "0.00";
  return price.toFixed(2);
});

// 外观名称 - 响应式，支持多语言
const getExteriorName = computed((): string => {
  if (!props.skinItem?.exterior) return "";

  // 外观缩写映射
  const exteriorMap: Record<string, string> = {
    "Factory New": "FN",
    "Minimal Wear": "MW",
    "Field-Tested": "FT",
    "Well-Worn": "WW",
    "Battle-Scarred": "BS",
    崭新出厂: "FN",
    略有磨损: "MW",
    久经沙场: "FT",
    破损不堪: "WW",
    战痕累累: "BS",
  };

  return exteriorMap[props.skinItem.exterior] || props.skinItem.exterior;
});

// 稀有度颜色 - 优先使用API返回的颜色
const getRarityColor = computed((): string => {
  const skinItemAny = props.skinItem as any;

  // 优先级：API返回的稀有度颜色 > 备用主题颜色
  if (skinItemAny?.item_rarity?.rarity_color) {
    return skinItemAny.item_rarity.rarity_color;
  }

  if (skinItemAny?.rarity_info?.color) {
    return skinItemAny.rarity_info.color;
  }

  if (skinItemAny?.rarity?.color) {
    return skinItemAny.rarity.color;
  }

  if (props.skinItem?.rarityColor) {
    return props.skinItem.rarityColor;
  }

  // 备用方案：使用主题变量
  return getRarityThemeColor(props.skinItem?.rarity);
});

// 稀有度发光效果
const getRarityGlow = computed((): string => {
  const color = getRarityColor.value;
  if (!color || color === "var(--rarity-common)") return "none";

  // 使用稀有度颜色创建发光效果
  return `0 0 15px ${color}40, 0 0 30px ${color}20`;
});

// 稀有度名称 - 直接使用API返回的多语言数据
const getRarityName = computed((): string => {
  const skinItemAny = props.skinItem as any;

  // 如果有稀有度对象且包含多语言数据
  if (skinItemAny?.item_rarity || skinItemAny?.rarity_info) {
    const rarityInfo = skinItemAny.item_rarity || skinItemAny.rarity_info;
    const currentLocale = locale.value === "zh-hans" ? "zh_hans" : locale.value;

    // 优先使用API返回的本地化稀有度名称
    if (currentLocale === "zh_hans") {
      return (
        rarityInfo.rarity_name_zh_hans ||
        rarityInfo.rarity_name_zh ||
        rarityInfo.rarity_name ||
        rarityInfo.name_zh_hans ||
        rarityInfo.name_zh ||
        rarityInfo.name ||
        "Unknown"
      );
    } else {
      return (
        rarityInfo.rarity_name_en ||
        rarityInfo.rarity_name ||
        rarityInfo.name_en ||
        rarityInfo.name ||
        "Unknown"
      );
    }
  }

  // 如果有直接的稀有度字符串
  if (props.skinItem?.rarity) {
    return props.skinItem.rarity;
  }

  // 备用方案
  return "Unknown";
});

// 稀有度主题颜色（备用）
const getRarityThemeColor = (rarity?: string): string => {
  if (!rarity) return "#6b7280"; // gray-500

  const rarityLower = rarity.toLowerCase();
  const colorMap: Record<string, string> = {
    "consumer grade": "#6b7280", // gray-500
    common: "#6b7280",
    "industrial grade": "#22c55e", // green-500
    uncommon: "#22c55e",
    "mil-spec": "#3b82f6", // blue-500
    "mil-spec grade": "#3b82f6",
    restricted: "#8b5cf6", // violet-500
    classified: "#ec4899", // pink-500
    covert: "#ef4444", // red-500
    contraband: "#f59e0b", // amber-500
  };

  return colorMap[rarityLower] || "#6b7280";
};

// 图片加载错误处理（使用统一工具函数）
const handleSkinImageError = (event: Event) => {
  const target = event.target as HTMLImageElement;
  if (!target) return;

  // 避免无限循环
  if (target.src.includes("placehold.co")) return;

  target.src = "https://placehold.co/128x96/374151/ffffff/png?text=🔫";
  target.alt = t("skins.image_error", "饰品图片加载失败");
  target.title = t("skins.image_error", "饰品图片加载失败");
};
</script>

<style lang="scss" scoped>
.skin-card-link {
  display: block;

  .skin-card {
    height: 100%;

    // 使用整站主题变量
    &:hover {
      box-shadow: var(--shadow-lg);
    }
  }
}

// 网格背景图案
.bg-grid-pattern {
  background-image: linear-gradient(
      rgba(255, 255, 255, 0.03) 1px,
      transparent 1px
    ),
    linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
  background-size: 20px 20px;
}

// 硬件加速优化
.skin-card {
  transform: translateZ(0);
  will-change: transform;
}

// 文本截断
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

// 响应式优化
@media (max-width: 768px) {
  .skin-card {
    .absolute.left-2.top-2,
    .absolute.right-2.top-2 {
      left: 0.25rem;
      right: 0.25rem;
      top: 0.25rem;
    }
  }
}
</style>
