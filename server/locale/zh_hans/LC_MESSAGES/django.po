# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-04 09:55+0800\n"
"PO-Revision-Date: 2020-09-18 19:09+0800\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: zh\n"
"X-Generator: Poedit 2.2.3\n"

#: .\authentication\admin.py:82
msgid "Permissions"
msgstr "许可"

#: .\authentication\admin.py:83
msgid "Important dates"
msgstr "重要日期"

#: .\authentication\admin.py:124 .\authentication\models.py:39
#: .\roll\models.py:98
msgid "balance"
msgstr "余额"

#: .\authentication\admin.py:141
msgid "box_chance_type"
msgstr "开箱概率类型"

#: .\authentication\apps.py:7
msgid "Account"
msgstr "账户"

#: .\authentication\business.py:56 .\package\business.py:194
msgid "Invalid trade url"
msgstr "无效交易链接"

#: .\authentication\business.py:78
msgid "交易链接只能保存一次"
msgstr ""

#: .\authentication\business.py:83 .\authentication\business.py:99
#: .\authentication\business.py:170 .\authentication\business.py:332
#: .\authentication\business.py:385
msgid "验证码错误"
msgstr ""

#: .\authentication\business.py:92
msgid "该手机已绑定其他用户"
msgstr ""

#: .\authentication\business.py:109
msgid "超过验证码发送限制"
msgstr ""

#: .\authentication\business.py:121 .\authentication\business.py:441
msgid "验证码发送失败请重试"
msgstr ""

#: .\authentication\business.py:137 .\authentication\business.py:299
#: .\blindbox\business.py:231 .\blindbox\business.py:302
#: .\envelope\business.py:35 .\market\business.py:100
msgid "参数错误"
msgstr ""

#: .\authentication\business.py:140 .\authentication\business.py:423
msgid "禁止注册"
msgstr ""

#: .\authentication\business.py:142 .\authentication\business.py:189
#: .\authentication\business.py:301
msgid "密码过长"
msgstr ""

#: .\authentication\business.py:144 .\authentication\business.py:191
#: .\authentication\business.py:303
msgid "密码过短"
msgstr ""

#: .\authentication\business.py:168
msgid "手机号已存在"
msgstr ""

#: .\authentication\business.py:176 .\authentication\business.py:187
#: .\authentication\business.py:206 .\authentication\business.py:363
#: .\authentication\business.py:374
msgid "该账号不存在"
msgstr ""

#: .\authentication\business.py:181 .\authentication\business.py:368
msgid "账号或密码错误"
msgstr ""

#: .\authentication\business.py:209
msgid "用户名长度必须大于2小于16"
msgstr ""

#: .\authentication\business.py:330
msgid "邮箱已存在"
msgstr ""

#: .\authentication\business.py:337 .\authentication\business.py:340
msgid "Email地址不合法"
msgstr ""

#: .\authentication\business.py:351
msgid "邮件服务未开通"
msgstr ""

#: .\authentication\business.py:357
msgid "发送邮件失败，请稍后再试"
msgstr ""

#: .\authentication\business.py:396 .\charge\business.py:622
#: .\envelope\business.py:33
msgid "请先登录"
msgstr ""

#: .\authentication\business.py:400
msgid "仅支持jpeg,jpg,png格式文件"
msgstr ""

#: .\authentication\business.py:420
msgid "短信服务未开通"
msgstr ""

#: .\authentication\business.py:427
msgid "操作频繁，稍后再试"
msgstr ""

#: .\authentication\models.py:24
msgid "nick name"
msgstr "昵称"

#: .\authentication\models.py:25
msgid "avatar"
msgstr "头像"

#: .\authentication\models.py:29 .\authentication\models.py:30
msgid "Profile"
msgstr "个人资料"

#: .\authentication\models.py:38
msgid "Steam trade url"
msgstr "Steam交易链接"

#: .\authentication\models.py:40
msgid "points"
msgstr "积分"

#: .\authentication\models.py:41 .\package\models.py:170
msgid "Diamond"
msgstr "钻石"

#: .\authentication\models.py:42
msgid "active point"
msgstr "活跃积分"

#: .\authentication\models.py:43
msgid "total charge balance"
msgstr "总充值余额"

#: .\authentication\models.py:46 .\authentication\models.py:47
msgid "Asset"
msgstr "资产"

#: .\authentication\models.py:55 .\package\models.py:256
msgid "Steamid"
msgstr "Steamid"

#: .\authentication\models.py:56
msgid "Steam name"
msgstr "Steam名字"

#: .\authentication\models.py:57
msgid "profile url"
msgstr "个人资料链接"

#: .\authentication\models.py:58
msgid "small avatar"
msgstr "小头像"

#: .\authentication\models.py:59
msgid "medium avatar"
msgstr "中头像"

#: .\authentication\models.py:60
msgid "big avatar"
msgstr "大头像"

#: .\authentication\models.py:61
msgid "Steam level"
msgstr "Steam等级"

#: .\authentication\models.py:62
msgid "own games count"
msgstr "拥有游戏数量"

#: .\authentication\models.py:63
msgid "Dota2 playtime"
msgstr "Dota2游戏时间"

#: .\authentication\models.py:66 .\authentication\models.py:67
msgid "Steam"
msgstr "Steam"

#: .\authentication\models.py:75
msgid "Box chance A"
msgstr "开箱概率A"

#: .\authentication\models.py:76
msgid "Box chance B"
msgstr "开箱概率B"

#: .\authentication\models.py:77
msgid "Box chance C"
msgstr "开箱概率C"

#: .\authentication\models.py:78
msgid "Box chance D"
msgstr "开箱概率D"

#: .\authentication\models.py:79
msgid "Box chance E"
msgstr "开箱概率E"

#: .\authentication\models.py:82
msgid "Lucky Box Rate Type A"
msgstr "拉货概率折扣A"

#: .\authentication\models.py:83
msgid "Lucky Box Rate Type B"
msgstr "拉货概率折扣B"

#: .\authentication\models.py:84
msgid "Lucky Box Rate Type C"
msgstr "拉货概率折扣C"

#: .\authentication\models.py:85
msgid "Lucky Box Rate Type D"
msgstr "拉货概率折扣D"

#: .\authentication\models.py:86
msgid "Lucky Box Rate Type E"
msgstr "拉货概率折扣E"

#: .\authentication\models.py:89
msgid "box chance type"
msgstr "开箱概率种类"

#: .\authentication\models.py:90
msgid "box free count"
msgstr "box free count"

#: .\authentication\models.py:91
msgid "box free last"
msgstr "box free last"

#: .\authentication\models.py:92
msgid "box free level 0 count"
msgstr "box free level 0 count"

#: .\authentication\models.py:93
msgid "box free level 0 last"
msgstr "box free level 0 last"

#: .\authentication\models.py:94
msgid "ban chat"
msgstr "禁止聊天"

#: .\authentication\models.py:95 .\package\models.py:306
msgid "ban deposit"
msgstr "禁止存入"

#: .\authentication\models.py:96 .\package\models.py:307
msgid "ban withdraw"
msgstr "禁止取回"

#: .\authentication\models.py:97 .\package\models.py:308
msgid "ban exchange"
msgstr "禁止交换"

#: .\authentication\models.py:98 .\package\models.py:309
msgid "ban shop"
msgstr "禁止开店"

#: .\authentication\models.py:99
msgid "ban roll room"
msgstr "禁止roll房"

#: .\authentication\models.py:100
msgid "ban create roll room"
msgstr "禁止创建roll房"

#: .\authentication\models.py:101
msgid "ban charge room"
msgstr "禁止收费房间"

#: .\authentication\models.py:102
msgid "box free give count"
msgstr "白给箱剩余次数"

#: .\authentication\models.py:103
msgid "freebox lv1 count"
msgstr "免费等级1箱子次数"

#: .\authentication\models.py:104
msgid "freebox lv2 count"
msgstr "免费等级2箱子次数"

#: .\authentication\models.py:105
msgid "freebox lv3 count"
msgstr "免费等级3箱子次数"

#: .\authentication\models.py:106
msgid "freebox lv4 count"
msgstr "免费等级4箱子次数"

#: .\authentication\models.py:107
msgid "freebox lv5 count"
msgstr "免费等级5箱子次数"

#: .\authentication\models.py:108
msgid "freebox lv6 count"
msgstr "免费等级6箱子次数"

#: .\authentication\models.py:109
msgid "freebox lv7 count"
msgstr "免费等级7箱子次数"

#: .\authentication\models.py:110
msgid "freebox lv8 count"
msgstr "免费等级8箱子次数"

#: .\authentication\models.py:111
msgid "freebox lv9 count"
msgstr "免费等级9箱子次数"

#: .\authentication\models.py:112
msgid "freebox lv10 count"
msgstr "免费等级10箱子次数"

#: .\authentication\models.py:113
msgid "freebox lv1 limit"
msgstr "免费等级1箱子次数限制"

#: .\authentication\models.py:114
msgid "freebox lv2 limit"
msgstr "免费等级2箱子次数限制"

#: .\authentication\models.py:115
msgid "freebox lv3 limit"
msgstr "免费等级3箱子次数限制"

#: .\authentication\models.py:116
msgid "freebox lv4 limit"
msgstr "免费等级4箱子次数限制"

#: .\authentication\models.py:117
msgid "freebox lv5 limit"
msgstr "免费等级5箱子次数限制"

#: .\authentication\models.py:118
msgid "freebox lv6 limit"
msgstr "免费等级6箱子次数限制"

#: .\authentication\models.py:119
msgid "freebox lv7 limit"
msgstr "免费等级7箱子次数限制"

#: .\authentication\models.py:120
msgid "freebox lv8 limit"
msgstr "免费等级8箱子次数限制"

#: .\authentication\models.py:121
msgid "freebox lv9 limit"
msgstr "免费等级9箱子次数限制"

#: .\authentication\models.py:122
msgid "freebox lv10 limit"
msgstr "免费等级10箱子次数限制"

#: .\authentication\models.py:123
msgid "luckybox rate type"
msgstr "拉货概率折扣类型"

#: .\authentication\models.py:124
#, fuzzy
#| msgid "Promotion Level Config"
msgid "box promotion level"
msgstr "推广等级配置"

#: .\authentication\models.py:127 .\authentication\models.py:128
msgid "Extra"
msgstr "其他"

#: .\authentication\models.py:137 .\authentication\models.py:139
#: .\authentication\models.py:147 .\authentication\models.py:149
#: .\authentication\models.py:157 .\authentication\models.py:159
#: .\authentication\models.py:167 .\authentication\models.py:169
#: .\authentication\models.py:177 .\authentication\models.py:179
#: .\authentication\models.py:187 .\authentication\models.py:189
#: .\authentication\models.py:197 .\authentication\models.py:199
#: .\authentication\models.py:207 .\authentication\models.py:209
#: .\authentication\models.py:217 .\authentication\models.py:219
#: .\authentication\models.py:227 .\authentication\models.py:229
#: .\authentication\models.py:237
#, fuzzy
#| msgid "Invalid points change"
msgid "Invalid count change"
msgstr "无效积分变更"

#: .\authentication\models.py:240
msgid "该等级箱子开启超过限制"
msgstr ""

#: .\authentication\models.py:245 .\authentication\models.py:260
#: .\authentication\models.py:275 .\authentication\models.py:290
#: .\b2ctrade\models.py:66 .\b2ctrade\models.py:183 .\blindbox\models.py:73
#: .\blindbox\models.py:90 .\blindbox\models.py:125 .\box\models.py:136
#: .\box\models.py:171 .\box\models.py:224 .\box\models.py:241
#: .\box\models.py:356 .\box\models.py:370 .\box\models.py:385
#: .\box\models.py:401 .\box\models.py:415 .\charge\models.py:22
#: .\charge\models.py:102 .\charge\models.py:128 .\charge\models.py:222
#: .\chat\models.py:10 .\chat\models.py:39 .\crash\models.py:47
#: .\crash\models.py:296 .\crash\models.py:320 .\custombox\models.py:12
#: .\custombox\models.py:82 .\envelope\models.py:42 .\lottery\models.py:42
#: .\package\models.py:112 .\package\models.py:172 .\package\models.py:189
#: .\package\models.py:217 .\package\models.py:304 .\promotion\models.py:19
#: .\promotion\models.py:39 .\roll\models.py:28 .\roll\models.py:72
#: .\roll\models.py:167 .\tradeup\models.py:23 .\tradeup\models.py:341
#: .\tradeup\models.py:365 .\tradeup\models.py:395 .\withdraw\models.py:37
msgid "user"
msgstr "用户"

#: .\authentication\models.py:246 .\authentication\models.py:261
#: .\authentication\models.py:276 .\authentication\models.py:291
msgid "changed"
msgstr "已变更"

#: .\authentication\models.py:247 .\authentication\models.py:262
#: .\authentication\models.py:277 .\authentication\models.py:292
msgid "change before"
msgstr "变更以前"

#: .\authentication\models.py:248 .\authentication\models.py:263
#: .\authentication\models.py:278 .\authentication\models.py:293
msgid "change after"
msgstr "变更之后"

#: .\authentication\models.py:249 .\authentication\models.py:264
#: .\authentication\models.py:279 .\authentication\models.py:294
#: .\withdraw\models.py:78
msgid "reason"
msgstr "原因"

#: .\authentication\models.py:252 .\authentication\models.py:253
msgid "User Balance Record"
msgstr "用户余额记录"

#: .\authentication\models.py:267 .\authentication\models.py:268
msgid "User Points Record"
msgstr "用户积分记录"

#: .\authentication\models.py:282 .\authentication\models.py:283
msgid "User Diamond Record"
msgstr "用户钻石记录"

#: .\authentication\models.py:297 .\authentication\models.py:298
msgid "User Active Point Record"
msgstr "用户活跃积分记录"

#: .\authentication\models.py:339
msgid "user uid"
msgstr "用户uid"

#: .\authentication\models.py:340
msgid "user name"
msgstr "用户名字"

#: .\authentication\models.py:341
msgid "email address"
msgstr "邮箱地址"

#: .\authentication\models.py:342
msgid "phone"
msgstr "电话"

#: .\authentication\models.py:343
msgid "date joined"
msgstr "加入时间"

#: .\authentication\models.py:345
msgid "staff status"
msgstr "用户状态"

#: .\authentication\models.py:347
msgid "Designates whether the user can log into this admin site."
msgstr "指定用户是否可以登录此管理站点。"

#: .\authentication\models.py:350 .\promotion\models.py:42
msgid "active"
msgstr "活跃的"

#: .\authentication\models.py:353
msgid ""
"Designates whether this user should be treated as active. Unselect this "
"instead of deleting accounts."
msgstr "指定是否应将此用户视为活动用户。取消选择它而不是删除账户。"

#: .\authentication\models.py:367 .\authentication\models.py:460
msgid "Invalid balance change"
msgstr "余额不足，请先充值"

#: .\authentication\models.py:382
msgid "Invalid points change"
msgstr "无效积分变更"

#: .\authentication\models.py:404
msgid "Invalid diamond change"
msgstr "无效钻石变更"

#: .\authentication\models.py:421
msgid "Invalid active point change"
msgstr "无效活跃积分变更"

#: .\authentication\models.py:437 .\authentication\models.py:448
#: .\authentication\models.py:469
msgid "No enough count"
msgstr "没有足够的数量"

#: .\authentication\models.py:474 .\authentication\models.py:475
msgid "Auth User"
msgstr "注册用户"

#: .\authentication\models.py:488 .\b2ctrade\models.py:204
#: .\b2ctrade\models.py:247 .\box\models.py:257 .\box\models.py:281
#: .\box\models.py:371 .\box\models.py:386 .\box\models.py:402
#: .\charge\models.py:46 .\charge\models.py:70 .\crash\models.py:65
#: .\crash\models.py:89 .\crash\models.py:118 .\crash\models.py:202
#: .\crash\models.py:297 .\package\models.py:324 .\package\models.py:353
#: .\roll\models.py:113 .\roll\models.py:137 .\roll\models.py:194
#: .\roll\models.py:254 .\tradeup\models.py:110 .\tradeup\models.py:134
#: .\tradeup\models.py:163 .\tradeup\models.py:247 .\tradeup\models.py:342
#: .\withdraw\models.py:100 .\withdraw\models.py:143
msgid "date"
msgstr "日期"

#: .\authentication\models.py:489 .\b2ctrade\models.py:23
#: .\b2ctrade\models.py:167 .\charge\models.py:210 .\tradeup\models.py:97
msgid "count"
msgstr "数量"

#: .\authentication\models.py:506 .\authentication\models.py:507
msgid "User Statistics Day"
msgstr "日用户统计"

#: .\authentication\pipeline.py:85 .\authentication\pipeline.py:88
#: .\authentication\pipeline.py:93
msgid "This Steam account is already in bind."
msgstr "该Steam账户已绑定。"

#: .\authentication\service\admin_actions.py:39
msgid "Send fail, no admin message settings"
msgstr "发送失败，无管理员消息设置"

#: .\authentication\service\admin_actions.py:42
msgid "Send message request complete"
msgstr "发送消息请求完成"

#: .\authentication\service\admin_actions.py:44
msgid "Admin send sms message"
msgstr "管理员发送短信"

#: .\authentication\service\admin_actions.py:54
msgid "User Chance Type Change To A"
msgstr "B概率用户设置A概率成功"

#: .\authentication\service\admin_actions.py:56
msgid "User Chance Type Change To B"
msgstr "A概率用户设置B概率成功"

#: .\authentication\service\admin_actions.py:59
msgid "Change User Chance A B"
msgstr "A、B概率转换"

#: .\authentication\service\admin_actions.py:94
msgid "用户导出EXCEL"
msgstr ""

#: .\authentication\service\admin_actions.py:121
msgid "余额记录导出EXCEL"
msgstr ""

#: .\b2ctrade\apps.py:7
msgid "B2Ctrade"
msgstr "扎比特取回"

#: .\b2ctrade\business.py:70 .\b2ctrade\business.py:159
#: .\b2ctrade\business.py:220 .\b2ctrade\business.py:435
#: .\package\business.py:144 .\package\business.py:189
#: .\package\business.py:290 .\package\business.py:324
#: .\package\business.py:359 .\roll\business.py:56 .\roll\business.py:224
#: .\withdraw\business.py:35 .\withdraw\business.py:100
#: .\withdraw\business.py:105 .\withdraw\business.py:127
#: .\withdraw\business.py:132 .\withdraw\business.py:156
#: .\withdraw\business.py:243 .\withdraw\business.py:248
msgid "无权执行此操作"
msgstr "无权执行此操作"

#: .\b2ctrade\business.py:73 .\b2ctrade\business.py:162
#: .\b2ctrade\business.py:223 .\b2ctrade\business.py:438
#: .\package\business.py:149 .\withdraw\business.py:38
#: .\withdraw\business.py:159
msgid "请先输入您的交易链接"
msgstr "请先输入您的交易链接"

#: .\b2ctrade\business.py:81 .\b2ctrade\business.py:230
msgid "Over withdarw max limit at sametime"
msgstr "超过同时取回最大订单限制"

#: .\b2ctrade\business.py:83 .\b2ctrade\business.py:172
msgid "提取饰品需要至少充值{}$"
msgstr ""

#: .\b2ctrade\business.py:87
msgid "此物品只能出售"
msgstr ""

#: .\b2ctrade\business.py:92
msgid "请提取{}$以上的物品"
msgstr ""

#: .\b2ctrade\business.py:170
msgid "取回数量超过限制"
msgstr ""

#: .\b2ctrade\business.py:174
msgid "请选择提取饰品"
msgstr ""

#: .\b2ctrade\business.py:237
msgid "Can not withdraw your deposit item"
msgstr "不能提取该物品"

#: .\b2ctrade\business.py:258
msgid "Withdraw busy please try later."
msgstr "取回繁忙请稍后再试"

#: .\b2ctrade\business.py:312 .\b2ctrade\business.py:445
#: .\b2ctrade\business.py:481 .\b2ctrade\business.py:483
#: .\withdraw\business.py:164
msgid "Invalid trade"
msgstr "无效交易链接"

#: .\b2ctrade\business.py:314
msgid "Cancel must waite 20 minutes later."
msgstr "取消必须等待20分钟后"

#: .\b2ctrade\business.py:447
msgid "Already withdraw by other users."
msgstr "已经被其他用户取回"

#: .\b2ctrade\business.py:449 .\luckybox\business.py:217
#: .\roll\business.py:177
msgid "Not enough balance"
msgstr "余额不足"

#: .\b2ctrade\business.py:451
msgid "Not enough available balance"
msgstr "余额不足"

#: .\b2ctrade\business.py:465
msgid "B2C trade buy"
msgstr "扎比特购买"

#: .\b2ctrade\business.py:488
#, fuzzy
#| msgid "bot tradeup min chance"
msgid "B2C trade cancel"
msgstr "bot tradeup min chance"

#: .\b2ctrade\models.py:20 .\b2ctrade\models.py:74 .\b2ctrade\models.py:130
#: .\b2ctrade\models.py:164 .\blindbox\models.py:55 .\blindbox\models.py:91
#: .\box\models.py:90 .\box\models.py:199 .\box\models.py:227
#: .\box\models.py:373 .\box\models.py:388 .\custombox\models.py:29
#: .\custombox\models.py:85 .\lottery\models.py:17 .\lottery\models.py:47
#: .\package\models.py:41 .\package\models.py:114 .\package\models.py:173
#: .\package\models.py:190 .\package\models.py:239 .\package\models.py:275
#: .\package\models.py:396 .\package\models.py:409 .\tradeup\models.py:83
#: .\tradeup\models.py:95 .\withdraw\models.py:80
msgid "item info"
msgstr "饰品信息"

#: .\b2ctrade\models.py:21 .\blindbox\models.py:34 .\blindbox\models.py:93
#: .\box\models.py:45 .\box\models.py:138 .\box\models.py:202
#: .\box\models.py:229 .\charge\models.py:158 .\custombox\models.py:16
#: .\custombox\models.py:87 .\luckybox\models.py:17 .\luckybox\models.py:62
#: .\package\admin.py:81 .\package\models.py:42 .\package\models.py:176
#: .\package\models.py:193 .\package\models.py:244 .\roll\models.py:97
#: .\tradeup\models.py:51 .\tradeup\models.py:84 .\tradeup\models.py:96
#: .\withdraw\models.py:76
msgid "price"
msgstr "价格"

#: .\b2ctrade\models.py:22
msgid "zbt price"
msgstr "扎比特价格"

#: .\b2ctrade\models.py:24 .\tradeup\models.py:98
msgid "unlimited"
msgstr "未受限"

#: .\b2ctrade\models.py:25 .\b2ctrade\models.py:166 .\b2ctrade\models.py:184
#: .\blindbox\models.py:41 .\blindbox\models.py:130 .\box\models.py:55
#: .\box\models.py:246 .\box\models.py:344 .\chat\models.py:28
#: .\chat\models.py:45 .\lottery\models.py:20 .\lottery\models.py:69
#: .\package\models.py:258 .\package\models.py:280 .\package\models.py:293
#: .\package\models.py:305 .\package\models.py:411 .\roll\models.py:171
#: .\sitecfg\models.py:25 .\sitecfg\models.py:44 .\sitecfg\models.py:67
#: .\sitecfg\models.py:82 .\sitecfg\models.py:99 .\sitecfg\models.py:116
#: .\tradeup\models.py:99 .\tradeup\models.py:403
msgid "enable"
msgstr "可用"

#: .\b2ctrade\models.py:26 .\steambase\models.py:22
msgid "create time"
msgstr "创建时间"

#: .\b2ctrade\models.py:27 .\package\models.py:26 .\package\models.py:40
#: .\steambase\models.py:23
msgid "update time"
msgstr "更新时间"

#: .\b2ctrade\models.py:30 .\b2ctrade\models.py:31
msgid "B2C Market Item"
msgstr "市场物品"

#: .\b2ctrade\models.py:39 .\b2ctrade\models.py:103 .\b2ctrade\models.py:198
#: .\b2ctrade\models.py:242 .\steambase\models.py:31 .\tradeup\models.py:13
#: .\tradeup\models.py:64 .\withdraw\models.py:13 .\withdraw\models.py:94
#: .\withdraw\models.py:138
msgid "Dota2"
msgstr "Dota2"

#: .\b2ctrade\models.py:40 .\b2ctrade\models.py:104 .\b2ctrade\models.py:199
#: .\b2ctrade\models.py:243 .\steambase\models.py:32 .\tradeup\models.py:14
#: .\tradeup\models.py:65 .\withdraw\models.py:14 .\withdraw\models.py:95
#: .\withdraw\models.py:139
msgid "CSGO"
msgstr "CSGO"

#: .\b2ctrade\models.py:41 .\b2ctrade\models.py:105 .\b2ctrade\models.py:200
#: .\b2ctrade\models.py:244 .\steambase\models.py:33 .\tradeup\models.py:15
#: .\tradeup\models.py:66 .\withdraw\models.py:15 .\withdraw\models.py:96
#: .\withdraw\models.py:140
msgid "PUBG"
msgstr "PUBG"

#: .\b2ctrade\models.py:42 .\b2ctrade\models.py:106 .\b2ctrade\models.py:201
#: .\b2ctrade\models.py:245 .\steambase\models.py:34 .\tradeup\models.py:16
#: .\tradeup\models.py:67 .\withdraw\models.py:16 .\withdraw\models.py:97
#: .\withdraw\models.py:141
msgid "H1Z1"
msgstr "H1Z1"

#: .\b2ctrade\models.py:45 .\b2ctrade\models.py:109 .\charge\models.py:12
#: .\package\models.py:95 .\package\models.py:210 .\withdraw\models.py:20
msgid "Initialed"
msgstr "已初始化"

#: .\b2ctrade\models.py:46 .\b2ctrade\models.py:59 .\b2ctrade\models.py:110
#: .\package\models.py:211
msgid "Accepted"
msgstr "已接受"

#: .\b2ctrade\models.py:47 .\b2ctrade\models.py:60 .\b2ctrade\models.py:111
#: .\box\models.py:128 .\crash\models.py:18 .\package\models.py:212
#: .\roll\models.py:18
msgid "Cancelled"
msgstr "已取消"

#: .\b2ctrade\models.py:48
msgid "Trading"
msgstr "取回中"

#: .\b2ctrade\models.py:49
msgid "Cancelling"
msgstr "取消中"

#: .\b2ctrade\models.py:50
msgid "PriceCancelled"
msgstr "溢价取消"

#: .\b2ctrade\models.py:51
msgid "OutOfStock"
msgstr "缺货"

#: .\b2ctrade\models.py:52
msgid "ZBTCancelled"
msgstr "卖家已取消"

#: .\b2ctrade\models.py:55
msgid "WaitForPay"
msgstr "等待购买"

#: .\b2ctrade\models.py:56
msgid "WaitForSend"
msgstr "等待发送"

#: .\b2ctrade\models.py:57
msgid "WaitForTrade"
msgstr "等待交易"

#: .\b2ctrade\models.py:58
msgid "Receive"
msgstr "接受"

#: .\b2ctrade\models.py:63
msgid "Market"
msgstr "市场"

#: .\b2ctrade\models.py:64
msgid "Package"
msgstr "饰品背包"

#: .\b2ctrade\models.py:67 .\b2ctrade\models.py:124 .\b2ctrade\models.py:125
#: .\package\models.py:218 .\package\models.py:257 .\withdraw\models.py:38
msgid "trade url"
msgstr "交易链接"

#: .\b2ctrade\models.py:68 .\b2ctrade\models.py:126 .\b2ctrade\models.py:205
#: .\b2ctrade\models.py:248 .\box\models.py:258 .\box\models.py:282
#: .\box\models.py:372 .\box\models.py:387 .\box\models.py:403
#: .\box\models.py:418 .\charge\models.py:24 .\charge\models.py:47
#: .\charge\models.py:71 .\charge\models.py:105 .\charge\models.py:129
#: .\charge\models.py:209 .\charge\models.py:224 .\charge\models.py:238
#: .\crash\models.py:49 .\crash\models.py:66 .\crash\models.py:90
#: .\crash\models.py:298 .\crash\models.py:323 .\package\models.py:121
#: .\package\models.py:220 .\package\models.py:325 .\package\models.py:354
#: .\roll\models.py:114 .\roll\models.py:138 .\tradeup\models.py:70
#: .\tradeup\models.py:111 .\tradeup\models.py:135 .\tradeup\models.py:343
#: .\tradeup\models.py:368 .\withdraw\models.py:40 .\withdraw\models.py:101
#: .\withdraw\models.py:144
msgid "amount"
msgstr "金额"

#: .\b2ctrade\models.py:69
msgid "buy price"
msgstr "购买价格"

#: .\b2ctrade\models.py:70 .\b2ctrade\models.py:127 .\package\models.py:221
msgid "trade status"
msgstr "交易状态"

#: .\b2ctrade\models.py:71
msgid "zbt trade status"
msgstr "扎比特交易状态"

#: .\b2ctrade\models.py:72 .\b2ctrade\models.py:128
msgid "accept time"
msgstr "接受时间"

#: .\b2ctrade\models.py:73 .\b2ctrade\models.py:129 .\b2ctrade\models.py:207
#: .\b2ctrade\models.py:250 .\steambase\models.py:49 .\tradeup\models.py:33
#: .\tradeup\models.py:71 .\withdraw\models.py:41 .\withdraw\models.py:103
#: .\withdraw\models.py:146
msgid "appid"
msgstr "appid"

#: .\b2ctrade\models.py:75 .\b2ctrade\models.py:132 .\package\models.py:115
#: .\package\models.py:242 .\package\models.py:276
msgid "assetid"
msgstr "assetid"

#: .\b2ctrade\models.py:76
msgid "zbt order id"
msgstr "扎比特订单号"

#: .\b2ctrade\models.py:77
msgid "zbt error"
msgstr "扎比特错误"

#: .\b2ctrade\models.py:78 .\package\models.py:222
msgid "trade No."
msgstr "交易编号"

#: .\b2ctrade\models.py:79
msgid "out trade no"
msgstr "交易编号"

#: .\b2ctrade\models.py:80
msgid "trade source"
msgstr "来源"

#: .\b2ctrade\models.py:81 .\package\models.py:174 .\package\models.py:191
#: .\package\models.py:241 .\roll\models.py:95 .\tradeup\models.py:50
#: .\withdraw\models.py:58
msgid "package item"
msgstr "饰品背包"

#: .\b2ctrade\models.py:86 .\b2ctrade\models.py:87
msgid "ZBT Trade Record"
msgstr "交易记录"

#: .\b2ctrade\models.py:94
msgid "seller nickname"
msgstr "昵称"

#: .\b2ctrade\models.py:97 .\b2ctrade\models.py:98
msgid "ZBTBlackList"
msgstr "黑名单"

#: .\b2ctrade\models.py:112
msgid "WaitForBuy"
msgstr "等待购买"

#: .\b2ctrade\models.py:113
msgid "RequestBuy"
msgstr "请求购买"

#: .\b2ctrade\models.py:114
msgid "WaitUnlock"
msgstr "等待解锁"

#: .\b2ctrade\models.py:115
msgid "TradeReady"
msgstr "准备交易"

#: .\b2ctrade\models.py:116
msgid "BuyerCancelled"
msgstr "买家取消"

#: .\b2ctrade\models.py:119
msgid "Purchase"
msgstr "购买"

#: .\b2ctrade\models.py:120
msgid "Stocks"
msgstr "库存"

#: .\b2ctrade\models.py:122 .\package\models.py:261 .\package\models.py:291
msgid "account"
msgstr "账户"

#: .\b2ctrade\models.py:123
msgid "buyer"
msgstr "买家"

#: .\b2ctrade\models.py:131
msgid "expire time"
msgstr "过期时间"

#: .\b2ctrade\models.py:133 .\box\models.py:99 .\box\models.py:201
#: .\roll\models.py:99
msgid "item type"
msgstr "饰品种类"

#: .\b2ctrade\models.py:137 .\b2ctrade\models.py:138
msgid "B2COfficialTrade Record"
msgstr "交易记录"

#: .\b2ctrade\models.py:156 .\package\models.py:137
msgid "Invalid items"
msgstr "无效饰品"

#: .\b2ctrade\models.py:171 .\b2ctrade\models.py:172
#, fuzzy
#| msgid "Item Whitelist"
msgid "B2C Item list"
msgstr "饰品白名单"

#: .\b2ctrade\models.py:182 .\blindbox\models.py:129 .\box\models.py:245
#: .\chat\models.py:27 .\chat\models.py:44 .\package\models.py:255
#: .\package\models.py:292 .\roll\models.py:170 .\sitecfg\models.py:26
#: .\sitecfg\models.py:42 .\sitecfg\models.py:60 .\sitecfg\models.py:94
#: .\tradeup\models.py:402
msgid "remark"
msgstr "备注"

#: .\b2ctrade\models.py:185
msgid "customid"
msgstr "自定义ID"

#: .\b2ctrade\models.py:189 .\b2ctrade\models.py:190
msgid "B2COfficial Account"
msgstr ""

#: .\b2ctrade\models.py:206 .\b2ctrade\models.py:249 .\withdraw\models.py:102
#: .\withdraw\models.py:145
msgid "test amount"
msgstr "测试金额"

#: .\b2ctrade\models.py:211 .\b2ctrade\models.py:212
msgid "B2CTrade Statistics Day"
msgstr "日交易统计"

#: .\b2ctrade\models.py:254 .\b2ctrade\models.py:255
msgid "B2CTrade Statistics Month"
msgstr "月交易统计"

#: .\b2ctrade\models.py:287 .\box\models.py:300 .\charge\models.py:98
#: .\crash\models.py:114 .\crash\models.py:292 .\package\models.py:371
#: .\roll\models.py:155 .\roll\models.py:319 .\tradeup\models.py:159
#: .\tradeup\models.py:337 .\withdraw\models.py:185
msgid "Month"
msgstr "月"

#: .\b2ctrade\service\admin_actions.py:48
msgid "b2c trade accept complete"
msgstr "订单修改完成"

#: .\b2ctrade\service\admin_actions.py:51
msgid "b2c trade accept"
msgstr "订单修改完成"

#: .\b2ctrade\service\admin_actions.py:64
msgid "b2c trade cancel complete"
msgstr "订单修改取消成功"

#: .\b2ctrade\service\admin_actions.py:67
msgid "b2c trade cancel"
msgstr "订单修改取消"

#: .\b2ctrade\service\admin_actions.py:91
msgid "扎比特购买失败请稍后重试."
msgstr ""

#: .\b2ctrade\service\admin_actions.py:102
msgid "zbt_buy complete"
msgstr "扎比特购买完成"

#: .\b2ctrade\service\admin_actions.py:109
msgid "扎比特接口错误：{}"
msgstr ""

#: .\b2ctrade\service\admin_actions.py:115
msgid "zbt_buy"
msgstr "扎比特购买"

#: .\b2ctrade\service\admin_actions.py:147
msgid "Sync b2c market price complete"
msgstr "同步市场价格完成"

#: .\b2ctrade\service\admin_actions.py:150
msgid "Sync b2c market price"
msgstr "Steam市场价格（USD）-0"

#: .\b2ctrade\service\admin_actions.py:198
#: .\b2ctrade\service\admin_actions.py:238
#: .\package\service\admin_actions.py:75
msgid "Sync complete"
msgstr "同步完成"

#: .\b2ctrade\service\admin_actions.py:201
#: .\b2ctrade\service\admin_actions.py:241
#: .\package\service\admin_actions.py:78
msgid "Sync trade bot inventory"
msgstr "同步交易机器人库存"

#: .\b2ctrade\service\admin_actions.py:210
msgid "Sync failed not search b2c account"
msgstr ""

#: .\b2ctrade\service\admin_actions.py:324
msgid "扎比特取回记录导出Excel"
msgstr ""

#: .\b2ctrade\views.py:51 .\b2ctrade\views.py:76 .\b2ctrade\views.py:97
#: .\b2ctrade\views.py:117 .\b2ctrade\views.py:137 .\package\views.py:115
#: .\withdraw\views.py:31 .\withdraw\views.py:56
msgid "Withdraw is under maintenance, please wait for a while."
msgstr "取回正在维护中，请稍候。"

#: .\b2ctrade\views.py:73 .\chat\views.py:31
msgid "系统维护中，请稍后再试，具体恢复时间请关注网站公告。"
msgstr "该网站正在维护中，请稍候。"

#: .\blindbox\business.py:68 .\box\business.py:235 .\box\business.py:300
#: .\custombox\business.py:168
msgid "Case is blocked, please open other case."
msgstr "箱子维护中，请稍后再试。"

#: .\blindbox\business.py:72
msgid "该轮游戏已结束，请更换盒子"
msgstr ""

#: .\blindbox\business.py:74
msgid "位置错误，请重新选择"
msgstr ""

#: .\blindbox\business.py:80
msgid "该位置已开启"
msgstr ""

#: .\blindbox\business.py:88 .\box\business.py:244 .\box\business.py:247
#: .\box\business.py:256 .\box\business.py:265 .\box\business.py:273
msgid "Invalid count"
msgstr "无效数量"

#: .\blindbox\business.py:193 .\box\business.py:448
msgid "Invalid case key"
msgstr "无效钥匙"

#: .\blindbox\models.py:12 .\blindbox\models.py:87 .\box\models.py:15
#: .\sitecfg\models.py:39
msgid "Normal"
msgstr "正常"

#: .\blindbox\models.py:13
msgid "Top"
msgstr "顶级"

#: .\blindbox\models.py:14 .\blindbox\models.py:88 .\box\models.py:17
#: .\box\models.py:190
msgid "Free"
msgstr "免费"

#: .\blindbox\models.py:15
msgid "Festival"
msgstr "节日"

#: .\blindbox\models.py:16 .\box\models.py:19
msgid "FreeGive"
msgstr "白给 "

#: .\blindbox\models.py:18 .\blindbox\models.py:32 .\box\models.py:23
#: .\box\models.py:43 .\custombox\models.py:14 .\roll\models.py:30
#: .\sitecfg\models.py:10 .\steambase\models.py:43
msgid "name"
msgstr "名字"

#: .\blindbox\models.py:19 .\box\models.py:24 .\luckybox\models.py:73
#: .\sitecfg\models.py:21
msgid "category"
msgstr "类别"

#: .\blindbox\models.py:20 .\blindbox\models.py:39 .\box\models.py:25
#: .\box\models.py:53 .\envelope\models.py:31
msgid "order"
msgstr "排序"

#: .\blindbox\models.py:24 .\blindbox\models.py:25 .\box\models.py:30
#: .\box\models.py:31
msgid "Case Type"
msgstr "箱子种类"

#: .\blindbox\models.py:33 .\box\models.py:44 .\custombox\models.py:15
msgid "key"
msgstr "钥匙"

#: .\blindbox\models.py:35 .\box\models.py:46
msgid "discount(%)"
msgstr "折扣（%）"

#: .\blindbox\models.py:36 .\box\models.py:49 .\custombox\models.py:17
#: .\custombox\models.py:48
msgid "cover image"
msgstr "封面图案"

#: .\blindbox\models.py:37 .\box\models.py:50 .\custombox\models.py:18
#: .\custombox\models.py:59
msgid "item image"
msgstr "饰品图案"

#: .\blindbox\models.py:38
#, fuzzy
#| msgid "Invalid room type"
msgid "blindbox type"
msgstr "无效房间种类"

#: .\blindbox\models.py:40 .\box\models.py:54
msgid "unlock"
msgstr "未锁定"

#: .\blindbox\models.py:42
#, fuzzy
#| msgid "timestamp"
msgid "limit timestamp"
msgstr "时间戳"

#: .\blindbox\models.py:43
#, fuzzy
#| msgid "end time"
msgid "limited times"
msgstr "结束时间"

#: .\blindbox\models.py:47 .\blindbox\models.py:48
msgid "BlindBox"
msgstr ""

#: .\blindbox\models.py:56 .\blindbox\models.py:92 .\blindbox\models.py:113
#: .\blindbox\models.py:128 .\box\models.py:92 .\box\models.py:153
#: .\box\models.py:226 .\box\models.py:244 .\box\models.py:315
#: .\box\models.py:330 .\box\models.py:357 .\box\models.py:390
#: .\box\models.py:441 .\custombox\models.py:31 .\custombox\models.py:84
#: .\promotion\models.py:78
msgid "case"
msgstr "箱子"

#: .\blindbox\models.py:57 .\box\models.py:93 .\custombox\models.py:32
msgid "show chance"
msgstr "显示概率"

#: .\blindbox\models.py:58 .\box\models.py:94 .\custombox\models.py:33
msgid "drop chance a"
msgstr "掉落概率a"

#: .\blindbox\models.py:59 .\box\models.py:95 .\custombox\models.py:34
msgid "drop chance b"
msgstr "掉落概率b"

#: .\blindbox\models.py:60 .\box\models.py:96 .\custombox\models.py:35
msgid "drop chance c"
msgstr "掉落概率c"

#: .\blindbox\models.py:61 .\box\models.py:97 .\custombox\models.py:36
msgid "drop chance d"
msgstr "掉落概率d"

#: .\blindbox\models.py:62 .\box\models.py:98 .\custombox\models.py:37
msgid "drop chance e"
msgstr "掉落概率e"

#: .\blindbox\models.py:63 .\box\models.py:100
msgid "diamond"
msgstr "钻石"

#: .\blindbox\models.py:64 .\box\models.py:102
msgid "custom enable"
msgstr "启用自定义价格"

#: .\blindbox\models.py:65 .\box\models.py:103
msgid "custom price"
msgstr "自定义价格"

#: .\blindbox\models.py:68 .\blindbox\models.py:69
msgid "BlindBoxDrop"
msgstr ""

#: .\blindbox\models.py:74
#, fuzzy
#| msgid "game"
msgid "game id"
msgstr "游戏"

#: .\blindbox\models.py:75
msgid "random seed"
msgstr ""

#: .\blindbox\models.py:78 .\blindbox\models.py:79
msgid "BlindBoxGame"
msgstr ""

#: .\blindbox\models.py:94 .\box\models.py:140 .\box\models.py:230
#: .\luckybox\models.py:15 .\roll\models.py:29 .\sitecfg\models.py:41
#: .\sitecfg\models.py:61 .\sitecfg\models.py:80 .\sitecfg\models.py:96
#: .\steambase\models.py:58
msgid "type"
msgstr "种类"

#: .\blindbox\models.py:95
msgid "index"
msgstr ""

#: .\blindbox\models.py:96 .\crash\models.py:48 .\tradeup\models.py:49
#: .\tradeup\models.py:69 .\tradeup\models.py:82
msgid "game"
msgstr "游戏"

#: .\blindbox\models.py:99 .\blindbox\models.py:100
msgid "Blind Box Record"
msgstr ""

#: .\blindbox\models.py:110 .\box\models.py:312 .\box\models.py:327
#: .\charge\models.py:130 .\charge\models.py:142 .\charge\models.py:193
#: .\package\models.py:152 .\promotion\models.py:59
msgid "level"
msgstr "等级"

#: .\blindbox\models.py:111 .\box\models.py:313
msgid "min point"
msgstr "最小积分"

#: .\blindbox\models.py:112 .\box\models.py:314
msgid "max point"
msgstr "最大积分"

#: .\blindbox\models.py:117 .\blindbox\models.py:118 .\box\models.py:319
#: .\box\models.py:320
msgid "Free Case Config"
msgstr "免费箱子配置"

#: .\blindbox\models.py:126 .\box\models.py:242
msgid "open idle min(seconds)"
msgstr "最小打开间隔（秒）"

#: .\blindbox\models.py:127 .\box\models.py:243
msgid "open idle max(seconds)"
msgstr "最大打开间隔（秒）"

#: .\blindbox\models.py:133 .\blindbox\models.py:134 .\box\models.py:249
#: .\box\models.py:250
msgid "Case Bot Config"
msgstr "箱子机器人配置"

#: .\blindbox\views.py:26 .\box\views.py:34 .\crash\views.py:42
#: .\roll\views.py:24 .\roll\views.py:60 .\roll\views.py:89
#: .\roll\views.py:111 .\roll\views.py:132 .\tradeup\views.py:58
#: .\tradeup\views.py:80 .\tradeup\views.py:102 .\tradeup\views.py:150
#: .\tradeup\views.py:248 .\tradeup\views.py:271 .\tradeup\views.py:293
msgid "Current game is under maintenance, please wait for a while."
msgstr "当前游戏正在维护中，请稍候。"

#: .\blindbox\views.py:32 .\blindbox\views.py:50 .\blindbox\views.py:71
#: .\blindbox\views.py:91 .\blindbox\views.py:112 .\blindbox\views.py:132
#: .\blindbox\views.py:156 .\blindbox\views.py:176 .\box\views.py:40
#: .\charge\models.py:14 .\crash\views.py:130 .\crash\views.py:155
#: .\crash\views.py:180 .\crash\views.py:198 .\tradeup\views.py:65
#: .\tradeup\views.py:87 .\tradeup\views.py:110 .\tradeup\views.py:157
#: .\tradeup\views.py:183 .\tradeup\views.py:208 .\tradeup\views.py:233
#: .\tradeup\views.py:256 .\tradeup\views.py:278 .\tradeup\views.py:301
msgid "Succeed"
msgstr "成功"

#: .\blindbox\views.py:39 .\blindbox\views.py:59 .\blindbox\views.py:78
#: .\blindbox\views.py:98 .\blindbox\views.py:119 .\blindbox\views.py:139
#: .\blindbox\views.py:163 .\blindbox\views.py:183 .\box\views.py:47
#: .\box\views.py:423 .\crash\views.py:135 .\crash\views.py:160
#: .\crash\views.py:185 .\crash\views.py:203 .\tradeup\views.py:70
#: .\tradeup\views.py:92 .\tradeup\views.py:115 .\tradeup\views.py:162
#: .\tradeup\views.py:188 .\tradeup\views.py:213 .\tradeup\views.py:238
#: .\tradeup\views.py:261 .\tradeup\views.py:283 .\tradeup\views.py:306
#: .\websocket\views.py:29
msgid "Exception"
msgstr "异常"

#: .\box\admin.py:70
msgid "price_expectation_a"
msgstr "A概率期望价格"

#: .\box\admin.py:81
msgid "price_expectation_b"
msgstr "B概率期望价格"

#: .\box\admin.py:92
msgid "price_expectation_c"
msgstr "C概率期望价格"

#: .\box\admin.py:103
msgid "price_expectation_d"
msgstr "D概率期望价格"

#: .\box\admin.py:114
msgid "price_expectation_e"
msgstr "E概率期望价格"

#: .\box\apps.py:7
msgid "Box"
msgstr "箱子"

#: .\box\business.py:129
msgid "Invalid level"
msgstr "无效等级"

#: .\box\business.py:135
msgid "Already opened case today"
msgstr "今天已经开箱"

#: .\box\business.py:138
msgid "Invalid case"
msgstr "无效箱子"

#: .\box\business.py:140
msgid "Invalid level, please open correct level case."
msgstr "无效等级，请打开正确等级的箱子。"

#: .\box\business.py:233
#, fuzzy
#| msgid "Case is blocked, please open other case."
msgid "Case is block, please open other case."
msgstr "箱子维护中，请稍后再试。"

#: .\box\business.py:250
msgid "该等级箱子开启次数超过限制"
msgstr ""

#: .\box\business.py:259
msgid "白给次数已用完"
msgstr ""

#: .\box\business.py:267
msgid "推广等级不足"
msgstr ""

#: .\box\business.py:270
msgid "该等级箱子已开启"
msgstr ""

#: .\box\business.py:279
msgid "未找到对应钥匙，请更换箱子"
msgstr ""

#: .\box\business_room.py:115 .\box\business_room.py:202
#: .\box\business_room.py:238 .\box\business_room.py:315 .\roll\business.py:78
msgid "Over max room count"
msgstr "您还有未结束的对战，每人限制同时开启6个对战"

#: .\box\business_room.py:118 .\box\business_room.py:240
msgid "Invalid cases count"
msgstr "箱子数量每次限制4个"

#: .\box\business_room.py:120
msgid "Invalid joiners count"
msgstr "参加者每次最多4人"

#: .\box\business_room.py:130 .\box\business_room.py:251
#, fuzzy
#| msgid "Case is blocked, please open other case."
msgid "Case is block, please choice other case."
msgstr "箱子维护中，请稍后再试。"

#: .\box\business_room.py:135 .\box\business_room.py:256
msgid "Invalid cases"
msgstr "箱子正在维护"

#: .\box\business_room.py:147 .\box\business_room.py:269
msgid "创建对战"
msgstr ""

#: .\box\business_room.py:206 .\box\business_room.py:319
#: .\box\business_room.py:347 .\roll\business.py:156 .\roll\business.py:184
#: .\roll\business.py:226 .\roll\business.py:293 .\roll\models.py:57
msgid "Invalid room"
msgstr "无效房间"

#: .\box\business_room.py:209 .\box\business_room.py:322
#: .\roll\business.py:232
msgid "Already joined"
msgstr "已经加入"

#: .\box\business_room.py:211 .\box\business_room.py:327
msgid "对战"
msgstr ""

#: .\box\business_room.py:242 .\box\business_room.py:325
msgid "Invalid team"
msgstr "无效团队"

#: .\box\business_room.py:300
msgid "Invalid room type"
msgstr "无效房间种类"

#: .\box\business_room.py:350
msgid "Invalid  bet"
msgstr "无效对战"

#: .\box\business_room.py:355 .\box\business_room.py:361
msgid "Case room bet quit"
msgstr "对战退出"

#: .\box\business_room.py:490 .\box\business_room.py:496
msgid "Case chance invalid."
msgstr "无效开箱概率。"

#: .\box\business_room.py:568 .\box\business_room.py:759
#: .\box\business_room.py:853
msgid "Case drops invalid."
msgstr "无效开箱掉落。"

#: .\box\business_room.py:908
msgid "Case room cancel"
msgstr "开箱房间取消"

#: .\box\models.py:20 .\promotion\apps.py:7 .\promotion\apps.py:8
msgid "Promotion"
msgstr "推广"

#: .\box\models.py:21
msgid "CaseKey"
msgstr "钥匙专属"

#: .\box\models.py:26 .\charge\models.py:182 .\luckybox\models.py:74
#: .\sitecfg\models.py:100 .\sitecfg\models.py:114
msgid "image"
msgstr "图片"

#: .\box\models.py:39
msgid "blue"
msgstr "蓝色"

#: .\box\models.py:40
msgid "purple"
msgstr "紫色"

#: .\box\models.py:41
msgid "red"
msgstr "红色"

#: .\box\models.py:47
msgid "present"
msgstr "礼物"

#: .\box\models.py:51
msgid "back image"
msgstr "背景图"

#: .\box\models.py:52
msgid "case type"
msgstr "箱子种类"

#: .\box\models.py:56
msgid "enable room"
msgstr "可用房间"

#: .\box\models.py:57
msgid "superuser enable case"
msgstr "管理员可开"

#: .\box\models.py:58
msgid "tag"
msgstr "标签"

#: .\box\models.py:59
msgid "tag color"
msgstr "标签颜色"

#: .\box\models.py:60
msgid "open count"
msgstr "打开数量"

#: .\box\models.py:61
msgid "case cost"
msgstr "箱子成本价"

#: .\box\models.py:62
msgid "up sill"
msgstr "上限阈值"

#: .\box\models.py:63
msgid "down sill"
msgstr "下限阈值"

#: .\box\models.py:64
msgid "dyntic"
msgstr "浮动值"

#: .\box\models.py:65
msgid "algorithm_enable"
msgstr "使用新算法"

#: .\box\models.py:69 .\box\models.py:70 .\box\models.py:219
#: .\package\models.py:105
msgid "Case"
msgstr "箱子"

#: .\box\models.py:82
msgid "Base by count and chance"
msgstr "基于次数及概率"

#: .\box\models.py:83
msgid "Base by count"
msgstr "基于次数"

#: .\box\models.py:84
msgid "Base by chance"
msgstr "基于概率"

#: .\box\models.py:87 .\box\models.py:189 .\roll\models.py:89
msgid "Assets"
msgstr "资产"

#: .\box\models.py:104
msgid "up sill drop"
msgstr "上限掉落"

#: .\box\models.py:105
msgid "down sill drop"
msgstr "下限掉落"

#: .\box\models.py:107 .\box\models.py:108
msgid "Drop Item"
msgstr "掉落饰品"

#: .\box\models.py:122 .\crash\models.py:12 .\roll\models.py:12
msgid "Initial"
msgstr "初始"

#: .\box\models.py:123 .\crash\models.py:13 .\grab\models.py:26
#: .\roll\models.py:13
msgid "Joinable"
msgstr "可加入"

#: .\box\models.py:124 .\crash\models.py:14 .\roll\models.py:14
msgid "Joining"
msgstr "正在加入"

#: .\box\models.py:125 .\crash\models.py:15 .\lottery\models.py:39
#: .\roll\models.py:15
msgid "Full"
msgstr "已满"

#: .\box\models.py:126 .\crash\models.py:16 .\roll\models.py:16
msgid "Running"
msgstr "正在跑"

#: .\box\models.py:127 .\crash\models.py:17 .\grab\models.py:29
#: .\lottery\models.py:40 .\roll\models.py:17
msgid "End"
msgstr "结束"

#: .\box\models.py:131 .\box\models.py:220
msgid "Battle"
msgstr "对战"

#: .\box\models.py:132 .\box\models.py:221
msgid "Equality"
msgstr "平等"

#: .\box\models.py:133 .\box\models.py:222
msgid "TeamBattle"
msgstr "团队对战"

#: .\box\models.py:135
msgid "short id"
msgstr "短id"

#: .\box\models.py:137 .\lottery\models.py:19 .\roll\models.py:35
msgid "max joiner"
msgstr "最大参加者"

#: .\box\models.py:139 .\charge\models.py:26 .\crash\models.py:30
#: .\package\models.py:117 .\package\models.py:119 .\roll\models.py:44
msgid "status"
msgstr "状态"

#: .\box\models.py:141
msgid "private"
msgstr "私人的"

#: .\box\models.py:144 .\box\models.py:145
msgid "Case Room"
msgstr "开箱房间"

#: .\box\models.py:152 .\box\models.py:170 .\box\models.py:192
#: .\box\models.py:404 .\roll\models.py:73 .\roll\models.py:92
#: .\roll\models.py:183
msgid "room"
msgstr "房间"

#: .\box\models.py:154 .\box\models.py:358
msgid "opened"
msgstr "已打开"

#: .\box\models.py:157 .\box\models.py:158
msgid "Case Room Round"
msgstr "开箱房间轮数"

#: .\box\models.py:173
msgid "open amount"
msgstr "打开金额"

#: .\box\models.py:174 .\crash\models.py:26 .\crash\models.py:52
#: .\roll\models.py:74 .\tradeup\models.py:31
msgid "win amount"
msgstr "获胜金额"

#: .\box\models.py:175 .\roll\models.py:75
msgid "win items count"
msgstr "获胜饰品计数"

#: .\box\models.py:176 .\roll\models.py:76
msgid "victory"
msgstr "胜利"

#: .\box\models.py:177
msgid "bet team"
msgstr "对战团队"

#: .\box\models.py:180 .\box\models.py:181
msgid "Case Room Bet"
msgstr "对战"

#: .\box\models.py:193
msgid "round"
msgstr "轮数"

#: .\box\models.py:195
msgid "bet"
msgstr "对战"

#: .\box\models.py:197
msgid "winner"
msgstr "获胜者"

#: .\box\models.py:203
msgid "part price of item"
msgstr "饰品的部分价格"

#: .\box\models.py:204
msgid "item need to split"
msgstr "需要拆分的饰品"

#: .\box\models.py:207 .\box\models.py:208
msgid "Case Room Item"
msgstr "开箱房间饰品"

#: .\box\models.py:233 .\box\models.py:234 .\custombox\models.py:90
#: .\custombox\models.py:91
msgid "Case Record"
msgstr "箱子记录"

#: .\box\models.py:276 .\box\models.py:277
msgid "Case Statistics Day"
msgstr "日箱子统计"

#: .\box\models.py:307 .\box\models.py:308
msgid "Case Statistics Month"
msgstr "月箱子统计"

#: .\box\models.py:328
msgid "min charge total amount"
msgstr "最低充值总额"

#: .\box\models.py:329
msgid "max charge total amount"
msgstr "最高充值总额"

#: .\box\models.py:334 .\box\models.py:335
msgid "Festival Case Config"
msgstr "节日箱子配置"

#: .\box\models.py:342
msgid "month"
msgstr "月"

#: .\box\models.py:343
msgid "day"
msgstr "日"

#: .\box\models.py:348 .\box\models.py:349
msgid "Festival Case Date"
msgstr "节日箱子日期"

#: .\box\models.py:359
msgid "expired time"
msgstr "过期时间"

#: .\box\models.py:362 .\box\models.py:363
msgid "Festival Case Record"
msgstr "节日箱子记录"

#: .\box\models.py:377 .\box\models.py:378
msgid "Drop Day Rank"
msgstr "日掉落排名"

#: .\box\models.py:393 .\box\models.py:394
msgid "Income Day Rank"
msgstr "日收入排名"

#: .\box\models.py:407 .\box\models.py:408
msgid "Room Day Rank"
msgstr "日房间排名"

#: .\box\models.py:416 .\charge\models.py:103 .\crash\models.py:321
#: .\tradeup\models.py:366
msgid "year"
msgstr "年"

#: .\box\models.py:417 .\charge\models.py:104 .\crash\models.py:322
#: .\tradeup\models.py:367
msgid "week"
msgstr "周"

#: .\box\models.py:421 .\box\models.py:422
msgid "Lose Week Rank"
msgstr "周失败排名"

#: .\box\models.py:442
msgid "case_key"
msgstr "钥匙"

#: .\box\models.py:445 .\box\models.py:446
msgid "CaseKeyConfig"
msgstr "钥匙专属箱子配置"

#: .\box\service\admin_actions.py:48
msgid "导出EXCEL"
msgstr ""

#: .\box\service\admin_actions.py:100
msgid "箱子掉落导出EXCEL"
msgstr ""

#: .\charge\apps.py:7
msgid "charge"
msgstr "充值"

#: .\charge\business.py:50
msgid "Invalid charge amount"
msgstr "无效充值金额"

#: .\charge\business.py:52
msgid "充值金额必须为整数"
msgstr ""

#: .\charge\business.py:114 .\charge\business.py:120 .\charge\business.py:168
#: .\charge\views.py:135 .\luckybox\business.py:95 .\luckybox\business.py:158
#: .\luckybox\business.py:160 .\withdraw\business.py:287
#: .\withdraw\business.py:317
msgid "Invalid Params"
msgstr "无效参数"

#: .\charge\business.py:116
msgid "count must be int"
msgstr "数量必须为整数"

#: .\charge\business.py:118 .\custombox\business.py:41
#: .\custombox\business.py:142 .\grab\business.py:56 .\grab\business.py:132
#: .\grab\business.py:154 .\grab\business.py:178 .\luckybox\business.py:190
#: .\market\business.py:98
msgid "Please Login First"
msgstr "请先登录"

#: .\charge\business.py:123
msgid "Good Not Found"
msgstr "商品未找到"

#: .\charge\business.py:132
msgid "Charge System Error, Please Try Again Later"
msgstr "充值系统错误，请稍后再试"

#: .\charge\business.py:510 .\charge\business.py:565
#, fuzzy
#| msgid "Invalid items"
msgid "Invalid Coins"
msgstr "无效饰品"

#: .\charge\business.py:579
#, fuzzy
#| msgid "Charge Statistics Day"
msgid "Charge Api is Busy"
msgstr "日充值统计"

#: .\charge\business.py:625
msgid "金额错误"
msgstr ""

#: .\charge\business.py:630
#, fuzzy
#| msgid "Invalid params"
msgid "invalid params"
msgstr "无效参数"

#: .\charge\business.py:637 .\charge\business.py:645
msgid "charge system api error"
msgstr ""

#: .\charge\business.py:701
msgid "Please login first"
msgstr "请先登录"

#: .\charge\business.py:705
msgid "CDKey is invalid"
msgstr "该CDKey无效。"

#: .\charge\business.py:711
msgid "CDKey top-up"
msgstr "CDKey充值"

#: .\charge\models.py:13
msgid "Actived"
msgstr "活跃的"

#: .\charge\models.py:15
msgid "Failed"
msgstr "失败"

#: .\charge\models.py:16
msgid "Canceled"
msgstr "已取消"

#: .\charge\models.py:19
msgid "WeChat"
msgstr "微信"

#: .\charge\models.py:20
msgid "Ali"
msgstr "支付宝"

#: .\charge\models.py:21
msgid "Union"
msgstr "银联"




#: .\charge\models.py:24
msgid "out trade No"
msgstr "交易编号"

#: .\charge\models.py:25 .\grab\models.py:33 .\package\models.py:177
msgid "currency"
msgstr "货币"

#: .\charge\models.py:27
msgid "pay type"
msgstr "支付种类"

#: .\charge\models.py:28
msgid "pay amount"
msgstr "支付金额"

#: .\charge\models.py:29
msgid "pay time"
msgstr "支付时间"

#: .\charge\models.py:30
msgid "nonce"
msgstr ""

#: .\charge\models.py:31
msgid "timestamp"
msgstr "时间戳"

#: .\charge\models.py:32
msgid "client ip"
msgstr "客户端IP"

#: .\charge\models.py:33
msgid "qr code"
msgstr "二维码"

#: .\charge\models.py:34
msgid "pay url"
msgstr "交易链接"

#: .\charge\models.py:38 .\charge\models.py:39
msgid "Charge Record"
msgstr "充值记录"

#: .\charge\models.py:65 .\charge\models.py:66
msgid "Charge Statistics Day"
msgstr "日充值统计"

#: .\charge\models.py:75 .\charge\models.py:76
msgid "Charge Statistics Month"
msgstr "月充值统计"

#: .\charge\models.py:108 .\charge\models.py:109
msgid "Charge Week Rank"
msgstr "周充值排名"

#: .\charge\models.py:134 .\charge\models.py:135
msgid "Charge Level"
msgstr "充值等级"

#: .\charge\models.py:143 .\charge\models.py:194 .\promotion\models.py:64
msgid "min amount"
msgstr "最小金额"

#: .\charge\models.py:144 .\charge\models.py:195 .\promotion\models.py:65
msgid "max amount"
msgstr "最大金额"

#: .\charge\models.py:148 .\charge\models.py:149
msgid "Charge Level Config"
msgstr "充值等级配置"

#: .\charge\models.py:156
msgid "Cxka good id"
msgstr "商品ID"

#: .\charge\models.py:157
msgid "Cxka good name"
msgstr "商品名称"

#: .\charge\models.py:162 .\charge\models.py:163
msgid "Cxka goods"
msgstr "充值卡"

#: .\charge\models.py:170
msgid "generate count"
msgstr "生成数量"

#: .\charge\models.py:173 .\charge\models.py:174
msgid "Cxka Generate"
msgstr "充值卡生成"

#: .\charge\models.py:181 .\grab\models.py:23 .\package\models.py:169
msgid "Coins"
msgstr "金币"

#: .\charge\models.py:185 .\charge\models.py:186
msgid "ChargeAmountConfig"
msgstr "充值金额配置"

#: .\charge\models.py:196 .\envelope\models.py:45
msgid "handsel"
msgstr "金额"

#: .\charge\models.py:200 .\charge\models.py:201
msgid "充值赠送金额配置"
msgstr ""

#: .\charge\models.py:213 .\charge\models.py:214
msgid "generate CDKey"
msgstr "CDKey生成"

#: .\charge\models.py:223 .\charge\models.py:237
msgid "CDKey"
msgstr "CDK"

#: .\charge\models.py:227 .\charge\models.py:228
msgid "CDKey record"
msgstr "CDKey记录"

#: .\charge\models.py:234
msgid "used"
msgstr "已使用"

#: .\charge\models.py:235
msgid "unused"
msgstr "未使用"

#: .\charge\models.py:239
msgid "Key state"
msgstr "CDKey状态"

#: .\charge\models.py:242 .\charge\models.py:243
msgid "CDKey info"
msgstr "CDKey信息"

#: .\charge\service\admin_actions.py:34
msgid "startup_export_txt"
msgstr "生成充值卡并导出"

#: .\charge\service\admin_actions.py:94
msgid "充值记录导出为Excel"
msgstr ""

#: .\charge\service\admin_actions.py:127
msgid "充值等级导出为Excel"
msgstr ""

#: .\charge\service\admin_actions.py:158
msgid "充值日统计导出为Excel"
msgstr ""

#: .\charge\service\admin_actions.py:185
msgid "用户等级重置"
msgstr ""

#: .\charge\service\admin_actions.py:198
msgid "Generate CDKey"
msgstr "CDKey生成"

#: .\charge\views.py:41 .\charge\views.py:65 .\charge\views.py:139
msgid "Charge is under maintenance, please wait for a while."
msgstr "充值正在维护中，请稍候。"

#: .\charge\views.py:295 .\charge\views.py:343 .\charge\views.py:386
#, fuzzy
#| msgid "系统维护中，请稍后再试，具体恢复时间请关注网站公告。"
msgid "充值渠道维护，具体恢复时间请查看网站公告。"
msgstr "该网站正在维护中，请稍候。"

#: .\chat\apps.py:7
msgid "Chat"
msgstr "聊天"

#: .\chat\business.py:41
msgid "At least 50coins to chat"
msgstr "至少要50枚硬币用以聊天"

#: .\chat\business.py:43
msgid "You have been banned chat"
msgstr "您已被禁止聊天"

#: .\chat\models.py:11 .\chat\models.py:26
msgid "Message"
msgstr "消息"

#: .\chat\models.py:12
msgid "Timestamp"
msgstr "时间戳记"

#: .\chat\models.py:31 .\chat\models.py:32
msgid "Chat Bot Message"
msgstr "聊天机器人消息"

#: .\chat\models.py:40
msgid "chat idle min(seconds)"
msgstr "最小聊天间隔（秒）"

#: .\chat\models.py:41
msgid "chat idle max(seconds)"
msgstr "最大聊天间隔（秒）"

#: .\chat\models.py:42
msgid "message"
msgstr "消息"

#: .\chat\models.py:48 .\chat\models.py:49
msgid "Chat Bot Config"
msgstr "聊天机器人配置"

#: .\crash\apps.py:7
msgid "Crash"
msgstr "崩溃"

#: .\crash\business.py:144
msgid "Crash game win"
msgstr "Crash Game赢得游戏"

#: .\crash\business.py:200 .\tradeup\business.py:82 .\tradeup\business.py:186
msgid "Invalid bet amount"
msgstr "无效对战金额"

#: .\crash\business.py:204
msgid "Auto crashout should between 1.01~999.99"
msgstr "自动淘汰应该介于1.01~999.99"

#: .\crash\business.py:213
msgid "Invalid game"
msgstr "无效游戏"

#: .\crash\business.py:215
msgid "Game is running, please wait on"
msgstr "游戏正在运行，请稍候"

#: .\crash\business.py:219
msgid "Over bet max amount"
msgstr "最高对战额"

#: .\crash\business.py:239
msgid "Crash game bet"
msgstr "Crash Game下注"

#: .\crash\business.py:256
msgid "Game is not running"
msgstr "游戏未运行"

#: .\crash\business.py:258
msgid "Invalid out point"
msgstr "无效点数"

#: .\crash\business.py:265
msgid "Invalid operation"
msgstr "无效操作"

#: .\crash\models.py:20 .\tradeup\models.py:24
msgid "hash"
msgstr ""

#: .\crash\models.py:21 .\tradeup\models.py:25
msgid "secret"
msgstr "秘密"

#: .\crash\models.py:22 .\tradeup\models.py:26
msgid "percentage"
msgstr "比例"

#: .\crash\models.py:23
msgid "crash point"
msgstr "崩溃点"

#: .\crash\models.py:24 .\roll\models.py:42
msgid "total amount"
msgstr "总金额"

#: .\crash\models.py:25 .\roll\models.py:41
msgid "pump amount"
msgstr "抽水金额"

#: .\crash\models.py:27
msgid "joinable time"
msgstr "可参加的时间"

#: .\crash\models.py:28
msgid "run time"
msgstr "运行时间"

#: .\crash\models.py:29 .\envelope\models.py:24
msgid "end time"
msgstr "结束时间"

#: .\crash\models.py:34 .\crash\models.py:35
msgid "Crash Game"
msgstr "Crash Game"

#: .\crash\models.py:43 .\tradeup\models.py:19
msgid "Not End"
msgstr "未结束"

#: .\crash\models.py:44 .\tradeup\models.py:20
msgid "Win"
msgstr "胜利"

#: .\crash\models.py:45 .\tradeup\models.py:21
msgid "Lose"
msgstr "失败"

#: .\crash\models.py:50
msgid "out point"
msgstr "输出点数"

#: .\crash\models.py:53 .\tradeup\models.py:32
msgid "win result"
msgstr "获胜结果"

#: .\crash\models.py:57 .\crash\models.py:58
msgid "Crash Bet"
msgstr "Crash对战"

#: .\crash\models.py:70 .\crash\models.py:71
msgid "Crash Pump Day"
msgstr "日Crash Pump"

#: .\crash\models.py:94 .\crash\models.py:95
msgid "Crash Pump Month"
msgstr "月Crash Pump"

#: .\crash\models.py:119 .\crash\models.py:203 .\roll\models.py:195
#: .\roll\models.py:255 .\tradeup\models.py:164 .\tradeup\models.py:248
msgid "game amount"
msgstr "游戏金额"

#: .\crash\models.py:120 .\crash\models.py:204 .\roll\models.py:196
#: .\roll\models.py:256 .\tradeup\models.py:165 .\tradeup\models.py:249
msgid "game journal"
msgstr "游戏日记"

#: .\crash\models.py:121 .\crash\models.py:205 .\tradeup\models.py:166
#: .\tradeup\models.py:250
msgid "win amount journal"
msgstr "获胜金额日记"

#: .\crash\models.py:122 .\crash\models.py:206 .\tradeup\models.py:167
#: .\tradeup\models.py:251
msgid "lose amount journal"
msgstr "失败金额日记"

#: .\crash\models.py:123 .\crash\models.py:207 .\roll\models.py:197
#: .\roll\models.py:257 .\tradeup\models.py:168 .\tradeup\models.py:252
msgid "test game amount"
msgstr "测试游戏金额"

#: .\crash\models.py:124 .\crash\models.py:208 .\roll\models.py:198
#: .\roll\models.py:258 .\tradeup\models.py:169 .\tradeup\models.py:253
msgid "admin game amount"
msgstr "管理员游戏金额"

#: .\crash\models.py:128 .\crash\models.py:129
msgid "Crash Statistics Day"
msgstr "日崩溃统计"

#: .\crash\models.py:212 .\crash\models.py:213
msgid "Crash Statistics Month"
msgstr "月崩溃统计"

#: .\crash\models.py:301 .\crash\models.py:302
msgid "Crash Win Day Rank"
msgstr "日Crash获胜排名"

#: .\crash\models.py:326 .\crash\models.py:327
msgid "Crash Win Week Rank"
msgstr "周Crash获胜排名"

#: .\crash\models.py:346 .\tradeup\models.py:391
msgid "Weeks"
msgstr "周"

#: .\custombox\business.py:93
msgid "Quantity must be greater than 2"
msgstr ""

#: .\custombox\business.py:153
msgid "No Permission to Open This Box"
msgstr "没有权限开启此箱子"

#: .\custombox\models.py:21 .\custombox\models.py:22
msgid "CustomBox"
msgstr "自制箱子"

#: .\custombox\models.py:40 .\custombox\models.py:41
msgid "CustomDropItem"
msgstr "掉落饰品"

#: .\custombox\models.py:51 .\custombox\models.py:52
msgid "CustomBoxCover"
msgstr "自制箱子背景图配置"

#: .\custombox\models.py:62 .\custombox\models.py:63
msgid "CustomBoxItem"
msgstr "自制开箱饰品图片配置"

#: .\custombox\models.py:71 .\luckybox\models.py:13 .\steambase\models.py:44
#: .\withdraw\models.py:79
msgid "market name"
msgstr "市场名称"

#: .\custombox\models.py:74 .\custombox\models.py:75
msgid "CustomBoxItemInfo"
msgstr "饰品信息"

#: .\custombox\views.py:91 .\custombox\views.py:128
#, fuzzy
#| msgid "系统维护中，请稍后再试，具体恢复时间请关注网站公告。"
msgid "The Custom Box is under maintenance, please wait for a while."
msgstr "该网站正在维护中，请稍候。"

#: .\envelope\apps.py:7
msgid "Envelope"
msgstr "红包"

#: .\envelope\business.py:41
msgid "口令不存在"
msgstr ""

#: .\envelope\business.py:47
msgid "该红包未开始领取"
msgstr ""

#: .\envelope\business.py:57
msgid "不满足条件"
msgstr ""

#: .\envelope\business.py:60
msgid "该口令红包已领取"
msgstr ""

#: .\envelope\business.py:68
msgid "该红包已抢光"
msgstr ""

#: .\envelope\business.py:73
msgid "口令红包"
msgstr ""

#: .\envelope\models.py:10
msgid "准备中"
msgstr ""

#: .\envelope\models.py:11
msgid "未开始"
msgstr ""

#: .\envelope\models.py:12
msgid "已开始"
msgstr ""

#: .\envelope\models.py:13
msgid "已结束"
msgstr ""

#: .\envelope\models.py:15 .\lottery\models.py:65 .\sitecfg\models.py:62
#: .\sitecfg\models.py:79 .\sitecfg\models.py:95 .\sitecfg\models.py:112
msgid "title"
msgstr "标题"

#: .\envelope\models.py:16
msgid "rule start time"
msgstr "充值限制开始时间"

#: .\envelope\models.py:17
msgid "rule_end_time"
msgstr "充值限制结束时间"

#: .\envelope\models.py:18
msgid "rule coins"
msgstr "充值金额限制"

#: .\envelope\models.py:20
msgid "stock"
msgstr "库存"

#: .\envelope\models.py:21
msgid "limited"
msgstr "未受限"

#: .\envelope\models.py:23
msgid "start time"
msgstr "开始时间"

#: .\envelope\models.py:25
msgid "handsel min"
msgstr "赠送金额最小值"

#: .\envelope\models.py:26
msgid "handsel max"
msgstr "赠送金额最大值"

#: .\envelope\models.py:28 .\envelope\models.py:44
msgid "envelop password"
msgstr "红包口令"

#: .\envelope\models.py:29 .\lottery\models.py:45
msgid "state"
msgstr "状态"

#: .\envelope\models.py:30 .\roll\models.py:31
msgid "description"
msgstr "备注"

#: .\envelope\models.py:34 .\envelope\models.py:35
msgid "EnvelopeRule"
msgstr "红包规则配置"

#: .\envelope\models.py:43
msgid "rule"
msgstr "规则"

#: .\envelope\models.py:48 .\envelope\models.py:49
msgid "EnvelopeRecord"
msgstr "红包记录"

#: .\envelope\service\admin_actions.py:37
msgid "红包记录导出Excel"
msgstr ""

#: .\envelope\views.py:37
msgid "红包功能维护中，请稍等片刻"
msgstr ""

#: .\grab\business.py:65 .\grab\business.py:105 .\grab\business.py:181
msgid "Grab Room Not Found"
msgstr "房间未找到"

#: .\grab\business.py:156 .\grab\business.py:185
#, fuzzy
#| msgid "Invalid count"
msgid "Invalid Count"
msgstr "无效数量"

#: .\grab\business.py:159
#, fuzzy
#| msgid "Good Not Found"
msgid "Room Not Found"
msgstr "商品未找到"

#: .\grab\business.py:161
msgid "Room is not Joinable"
msgstr ""

#: .\grab\business.py:165
msgid "Card is Picked"
msgstr "卡片已被选择"

#: .\grab\models.py:22
#, fuzzy
#| msgid "Count"
msgid "Coupon"
msgstr "计数"

#: .\grab\models.py:27
#, fuzzy
#| msgid "Full"
msgid "Fulling"
msgstr "已满"

#: .\grab\models.py:28
#, fuzzy
#| msgid "Trading"
msgid "Handling"
msgstr "取回中"

#: .\grab\models.py:31
#, fuzzy
#| msgid "room uid"
msgid "room id"
msgstr "房间"

#: .\grab\models.py:34
#, fuzzy
#| msgid "total profit"
msgid "total position"
msgstr "总收益"

#: .\grab\models.py:35
msgid "user max choice"
msgstr ""

#: .\grab\models.py:36
msgid "coins to one card"
msgstr ""

#: .\grab\models.py:37
#, fuzzy
#| msgid "Item Price"
msgid "item price"
msgstr "饰品价格"

#: .\grab\models.py:38
#, fuzzy
#| msgid "state"
msgid "room state"
msgstr "状态"

#: .\grab\models.py:39
msgid "position last"
msgstr ""

#: .\grab\models.py:42 .\grab\models.py:43 .\grab\models.py:56
#: .\grab\models.py:57
#, fuzzy
#| msgid "room"
msgid "GrabRoom"
msgstr "房间"

#: .\grab\models.py:52 .\grab\models.py:64
msgid "grab position"
msgstr ""

#: .\grab\models.py:53
#, fuzzy
#| msgid "victory"
msgid "grab victory"
msgstr "胜利"

#: .\grab\models.py:65
#, fuzzy
#| msgid "cover image"
msgid "grab card image"
msgstr "封面图案"

#: .\grab\models.py:69 .\grab\models.py:70
#, fuzzy
#| msgid "Card"
msgid "GrabCard"
msgstr "卡"

#: .\grab\models.py:79
#, fuzzy
#| msgid "win amount"
msgid "join count"
msgstr "获胜金额"

#: .\grab\models.py:80
#, fuzzy
#| msgid "timestamp"
msgid "join timestamp"
msgstr "时间戳"

#: .\libs\mail_server.py:45
#, fuzzy
#| msgid "Invalid verify code"
msgid "Send verify code fail"
msgstr "无效验证码"

#: .\lottery\apps.py:7
msgid "Lottery"
msgstr "抽奖"

#: .\lottery\models.py:12 .\lottery\models.py:32 .\lottery\models.py:61
msgid "Hourly"
msgstr "每小时"

#: .\lottery\models.py:13 .\lottery\models.py:33 .\lottery\models.py:62
msgid "Daily"
msgstr "每日"

#: .\lottery\models.py:14 .\lottery\models.py:34 .\lottery\models.py:63
msgid "Weekly"
msgstr "每周"

#: .\lottery\models.py:16 .\lottery\models.py:43 .\lottery\models.py:67
msgid "lottery type"
msgstr "抽奖种类"

#: .\lottery\models.py:18
msgid "min charge"
msgstr "最小金额"

#: .\lottery\models.py:23 .\lottery\models.py:24
msgid "Lottery Setting"
msgstr "抽奖设置"

#: .\lottery\models.py:37
msgid "Not enough charge"
msgstr "金额不足"

#: .\lottery\models.py:38
msgid "Joined"
msgstr "已加入"

#: .\lottery\models.py:44
msgid "charge amount"
msgstr "充值金额"

#: .\lottery\models.py:46
msgid "win"
msgstr "获胜"

#: .\lottery\models.py:49
#, fuzzy
#| msgid "winner"
msgid "set winner"
msgstr "获胜者"

#: .\lottery\models.py:52 .\lottery\models.py:53
msgid "Lottery Joiner"
msgstr "抽奖参加者"

#: .\lottery\models.py:66
msgid "comment"
msgstr "备注"

#: .\lottery\models.py:68 .\sitecfg\models.py:66 .\sitecfg\models.py:83
#: .\sitecfg\models.py:98 .\sitecfg\models.py:115
msgid "order No."
msgstr "排序编号"

#: .\lottery\models.py:72 .\lottery\models.py:73
msgid "Lottery Info Setting"
msgstr "抽奖文本设置"

#: .\luckybox\admin.py:27
msgid "默认图片"
msgstr ""

#: .\luckybox\admin.py:33
msgid "自定义图片"
msgstr ""

#: .\luckybox\admin.py:39
msgid "自定义点亮图片"
msgstr ""

#: .\luckybox\apps.py:6
msgid "LuckBox"
msgstr "拉货"

#: .\luckybox\business.py:192
msgid "Items Invalid"
msgstr "饰品无效"

#: .\luckybox\business.py:198
#, fuzzy
#| msgid "Page Not Found"
msgid "Items Not Found"
msgstr "页面未找到"

#: .\luckybox\business.py:200 .\luckybox\business.py:202
#, fuzzy
#| msgid "Items Invalid"
msgid "Percent Invalid"
msgstr "饰品无效"

#: .\luckybox\business.py:215
msgid "Open Lucky Box"
msgstr "拉货"

#: .\luckybox\models.py:14 .\steambase\models.py:45
msgid "market hash name"
msgstr "市场hash名称"

#: .\luckybox\models.py:16 .\steambase\models.py:59
msgid "weapon"
msgstr "武器"

#: .\luckybox\models.py:18 .\steambase\models.py:52
msgid "icon url"
msgstr "图标链接"

#: .\luckybox\models.py:19 .\steambase\models.py:54
msgid "rarity"
msgstr "稀有性"

#: .\luckybox\models.py:22 .\luckybox\models.py:23
msgid "LuckyBox Group"
msgstr "拉货分组"

#: .\luckybox\models.py:50 .\luckybox\models.py:51
msgid "LuckyBox Recommend Group"
msgstr "拉货热门分组"

#: .\luckybox\models.py:65 .\luckybox\models.py:66
msgid "LuckyBox Item"
msgstr "拉货饰品"

#: .\luckybox\models.py:78 .\luckybox\models.py:79
msgid "LuckyBoxCategory"
msgstr "拉货分类"

#: .\luckybox\models.py:87
msgid "luckybox item"
msgstr "拉货饰品"

#: .\luckybox\models.py:89
msgid "lucky origin percent"
msgstr "原始幸运百分比"

#: .\luckybox\models.py:90
msgid "lucky percent"
msgstr "幸运百分率"

#: .\luckybox\models.py:91
msgid "lucky coins"
msgstr "金额"

#: .\luckybox\models.py:92
msgid "lucky percentage"
msgstr "目标幸运百分比"

#: .\luckybox\models.py:93
msgid "lucky result"
msgstr "获胜结果"

#: .\luckybox\models.py:94
msgid "luckybox target item"
msgstr "拉货概率折扣类型"

#: .\luckybox\models.py:98 .\luckybox\models.py:99
msgid "LuckyBox Record"
msgstr "幸运饰品追梦记录"

#: .\luckybox\service\admin_actions.py:31
msgid "sync group item hash name"
msgstr "刷新幸运分组市场hash名称"

#: .\luckybox\views.py:155
msgid "幸运箱子维护中，请稍等片刻"
msgstr ""

#: .\market\business.py:107
msgid "余额不足, 请充值"
msgstr ""

#: .\market\models.py:14
msgid "item rarity cn"
msgstr "稀有度"

#: .\market\models.py:15
msgid "item exterior cn"
msgstr "品质"

#: .\market\models.py:16
msgid "item dark gold"
msgstr "暗金"

#: .\market\models.py:19 .\market\models.py:20
msgid "MarketItem"
msgstr "商城饰品"

#: .\monitor\apps.py:7 .\monitor\views.py:25 .\templates\admin\base
#: copy.html:293
msgid "Monitor"
msgstr "监控"

#: .\package\admin.py:106 .\package\admin.py:110
msgid "cancel_send"
msgstr "取消_发送"

#: .\package\apps.py:7 .\tradeup\models.py:46
msgid "Item"
msgstr "饰品"

#: .\package\business.py:152
msgid "No bot online"
msgstr "没有在线机器人"

#: .\package\business.py:164 .\package\business.py:500
#: .\promotion\business.py:54 .\promotion\business.py:57 .\roll\business.py:63
#: .\roll\business.py:140 .\roll\business.py:174 .\withdraw\business.py:134
#: .\withdraw\business.py:136 .\withdraw\business.py:187
msgid "Invalid params"
msgstr "无效参数"

#: .\package\business.py:232 .\package\business.py:363
msgid "Out of stock, please content customer service on the right side."
msgstr "缺货，请在右侧联系客服。"

#: .\package\business.py:303
msgid "出售饰品"
msgstr ""

#: .\package\business.py:371
msgid "Shop item"
msgstr "商城饰品"

#: .\package\models.py:18
msgid "蓝色"
msgstr ""

#: .\package\models.py:19
msgid "灰色"
msgstr ""

#: .\package\models.py:20
msgid "粉色"
msgstr ""

#: .\package\models.py:21
msgid "红色"
msgstr ""

#: .\package\models.py:22
msgid "金色"
msgstr ""

#: .\package\models.py:23
msgid "紫色"
msgstr ""

#: .\package\models.py:24
msgid "深蓝色"
msgstr ""

#: .\package\models.py:27
msgid "custom icon"
msgstr "自定义饰品图片"

#: .\package\models.py:29
msgid "custom rarity"
msgstr "自定义饰品稀有度颜色"

#: .\package\models.py:35 .\package\models.py:36
msgid "Item Info"
msgstr "饰品信息"

#: .\package\models.py:43 .\promotion\models.py:25
msgid "enable custom"
msgstr "启用自定义"

#: .\package\models.py:44
msgid "custom price(USD)"
msgstr "自定义价格（USD）"

#: .\package\models.py:45
msgid "custom discount(%)"
msgstr "自定义折扣（%）"

#: .\package\models.py:46
msgid "waxpeer market price(USD)-0"
msgstr "waxpeer市场价格（USD）-0"

#: .\package\models.py:47
msgid "zbt market price(USD)-0"
msgstr "Steam市场价格（USD）-0"

#: .\package\models.py:48
msgid "Steam market price(USD)-0"
msgstr "Steam市场价格（USD）-0"

#: .\package\models.py:49
msgid "Steam normal price(USD)-10"
msgstr "Steam正常价格（USD）-10"

#: .\package\models.py:50
msgid "Steam sale price(USD)-11"
msgstr "Steam销售价格（USD）-11"

#: .\package\models.py:53 .\package\models.py:54
msgid "Item Price"
msgstr "饰品价格"

#: .\package\models.py:96
msgid "Available"
msgstr "可用"

#: .\package\models.py:97
msgid "Blocked"
msgstr "被锁"

#: .\package\models.py:98
msgid "Gaming"
msgstr "游戏中"

#: .\package\models.py:99
msgid "Withdrawing"
msgstr "取回中"

#: .\package\models.py:100
msgid "Withdrawn"
msgstr "已取回"

#: .\package\models.py:101
msgid "Exchanged"
msgstr "已出售"

#: .\package\models.py:102
msgid "Invalid"
msgstr "无效"

#: .\package\models.py:106
msgid "LuckyBox"
msgstr ""

#: .\package\models.py:109 .\roll\apps.py:7
msgid "Roll"
msgstr "Roll房"

#: .\package\models.py:110
msgid "BattleRoom"
msgstr "对战房间"

#: .\package\models.py:116 .\package\models.py:243 .\package\models.py:277
msgid "instanceid"
msgstr "实例id"

#: .\package\models.py:118 .\package\models.py:224 .\package\models.py:278
msgid "bot Steamid"
msgstr "机器人Steamid"

#: .\package\models.py:120
msgid "part of item"
msgstr "饰品的一部分"

#: .\package\models.py:122
msgid "case name"
msgstr "箱子名字"

#: .\package\models.py:123
msgid "case cover"
msgstr "箱子背景图"

#: .\package\models.py:124
#, fuzzy
#| msgid "case_key"
msgid "case key"
msgstr "钥匙"

#: .\package\models.py:147 .\package\models.py:148
msgid "Package Item"
msgstr "饰品背包"

#: .\package\models.py:153
msgid "price rate(%)"
msgstr "价格比率（%）"

#: .\package\models.py:154
msgid "min price"
msgstr "最小价格"

#: .\package\models.py:155
msgid "max price"
msgstr "最大价格"

#: .\package\models.py:159 .\package\models.py:160
msgid "Item Price Rate Config"
msgstr "饰品价格比率配置"

#: .\package\models.py:184 .\package\models.py:185
msgid "Exchange Record"
msgstr "出售记录"

#: .\package\models.py:200 .\package\models.py:201
msgid "Shop record"
msgstr "商城记录"

#: .\package\models.py:206 .\package\models.py:321 .\package\models.py:350
msgid "Deposit"
msgstr "存入"

#: .\package\models.py:207 .\package\models.py:322 .\package\models.py:351
msgid "Withdraw"
msgstr "取回"

#: .\package\models.py:213
msgid "Submitted"
msgstr "已提交"

#: .\package\models.py:214
msgid "TradeNoUpdated"
msgstr "交易编号更新"

#: .\package\models.py:215 .\withdraw\models.py:21
msgid "Active"
msgstr "活跃的"

#: .\package\models.py:219 .\package\models.py:326 .\package\models.py:355
msgid "trade type"
msgstr "交易种类"

#: .\package\models.py:223
msgid "security code"
msgstr "安全码"

#: .\package\models.py:225
msgid "bot message"
msgstr "机器人消息"

#: .\package\models.py:226
msgid "trade time"
msgstr "交易时间"

#: .\package\models.py:227
msgid "review"
msgstr "审核"

#: .\package\models.py:234 .\package\models.py:235 .\withdraw\models.py:49
#: .\withdraw\models.py:50
msgid "Trade Record"
msgstr "交易记录"

#: .\package\models.py:240
msgid "trade record"
msgstr "交易记录"

#: .\package\models.py:250 .\package\models.py:251
msgid "Trade Item"
msgstr "交易饰品"

#: .\package\models.py:259
msgid "enable deposit"
msgstr "可用的余额"

#: .\package\models.py:260
msgid "enable withdraw"
msgstr "可取回金额"

#: .\package\models.py:262 .\roll\models.py:32
msgid "password"
msgstr "密码"

#: .\package\models.py:263
msgid "shared secret"
msgstr "分享的秘密"

#: .\package\models.py:264
msgid "identity secret"
msgstr "身份秘密"

#: .\package\models.py:270 .\package\models.py:271
msgid "Trade Bot Config"
msgstr "交易机器人配置"

#: .\package\models.py:279
msgid "matching"
msgstr "匹配"

#: .\package\models.py:286 .\package\models.py:287
msgid "Trade Bot Inventory"
msgstr "交易机器人库存"

#: .\package\models.py:299 .\package\models.py:300
msgid "Shop Bot Config"
msgstr "商城机器人配置"

#: .\package\models.py:315 .\package\models.py:316
msgid "BlackList"
msgstr "黑名单"

#: .\package\models.py:344 .\package\models.py:345
msgid "Item Statistics Day"
msgstr "日饰品统计"

#: .\package\models.py:378 .\package\models.py:379
msgid "Item Statistics Month"
msgstr "月饰品统计"

#: .\package\models.py:383
msgid "last unlock time"
msgstr "上一次解锁时间"

#: .\package\models.py:384
msgid "interval(min)"
msgstr "间隔（分钟）"

#: .\package\models.py:391 .\package\models.py:392
msgid "Item Unlock Time Config"
msgstr "饰品未锁定时间配置"

#: .\package\models.py:397
msgid "Count"
msgstr "计数"

#: .\package\models.py:404 .\package\models.py:405
msgid "Lock Items Statistics"
msgstr "锁定饰品统计"

#: .\package\models.py:415 .\package\models.py:416
msgid "Item Whitelist"
msgstr "饰品白名单"

#: .\package\service\admin_actions.py:42
msgid "Startup complete"
msgstr "启动完成"

#: .\package\service\admin_actions.py:44
msgid "Startup fail"
msgstr "启动失败"

#: .\package\service\admin_actions.py:47
msgid "Startup trade bot"
msgstr "启动交易机器人"

#: .\package\service\admin_actions.py:58
msgid "Get inventory from Steam fail"
msgstr "从Steam获取库存失败"

#: .\package\service\admin_actions.py:86
msgid "Review complete"
msgstr "审核完成"

#: .\package\service\admin_actions.py:89
msgid "Review selected trade"
msgstr "审核已选交易"

#: .\package\service\admin_actions.py:94
msgid "Invalidate complete"
msgstr "作废完成"

#: .\package\service\admin_actions.py:97
msgid "Invalidate selected item"
msgstr "作废已选物品"

#: .\package\service\admin_actions.py:102
msgid "Exchanged complete"
msgstr "已出售"

#: .\package\service\admin_actions.py:105
msgid "Exchanged selected item"
msgstr "已选物品"

#: .\package\service\admin_actions.py:110
#, fuzzy
#| msgid "Invalidate complete"
msgid "Available complete"
msgstr "作废完成"

#: .\package\service\admin_actions.py:113
msgid "Available selected item"
msgstr "物品可用"

#: .\package\service\admin_actions.py:146
msgid "import item to luckybox"
msgstr "导入到拉货"

#: .\package\service\admin_actions.py:160
#, fuzzy
#| msgid "import item to luckybox"
msgid "import item to custombox"
msgstr "导入到拉货"

#: .\package\service\admin_actions.py:218
msgid "背包饰品导出EXCEL"
msgstr ""

#: .\package\views.py:94
msgid "Deposit is under maintenance, please wait for a while."
msgstr "存入正在维护中，请稍候。"

#: .\package\views.py:189 .\package\views.py:209
msgid "Exchange is under maintenance, please wait for a while."
msgstr "交换正在维护中，请稍候。"

#: .\package\views.py:228
msgid "Shop is under maintenance, please wait for a while."
msgstr "商城正在维护中，请稍候。"

#: .\promotion\business.py:27
msgid "Invalid promotion code"
msgstr "无效促销码"

#: .\promotion\business.py:31
msgid "You have been bound another account"
msgstr "您已绑定另一个账户"

#: .\promotion\business.py:52
msgid "Code Length Must 4 to 8 digits"
msgstr "推广码必须4至8位"

#: .\promotion\business.py:59
msgid "Code is already exists"
msgstr "代码已经存在"

#: .\promotion\business.py:69
msgid "邀请人数不足"
msgstr ""

#: .\promotion\business.py:77
msgid "您的收益不足5B"
msgstr ""

#: .\promotion\models.py:20
msgid "refer code"
msgstr "邀请代码"

#: .\promotion\models.py:21
msgid "refer count"
msgstr "邀请数量"

#: .\promotion\models.py:22 .\promotion\models.py:40
msgid "total charge"
msgstr "总充值"

#: .\promotion\models.py:23
msgid "total profit"
msgstr "总收益"

#: .\promotion\models.py:24
msgid "profit"
msgstr "收益"

#: .\promotion\models.py:26
msgid "refer level"
msgstr "邀请等级"

#: .\promotion\models.py:27 .\promotion\models.py:60
msgid "profit rate(%)"
msgstr "收益率（%）"

#: .\promotion\models.py:30 .\promotion\models.py:31
msgid "User Promotion"
msgstr "用户推广信息"

#: .\promotion\models.py:38
msgid "refer"
msgstr "邀请"

#: .\promotion\models.py:41
msgid "refer profit"
msgstr "邀请收益"

#: .\promotion\models.py:46 .\promotion\models.py:47
msgid "Promotion Record"
msgstr "推广记录"

#: .\promotion\models.py:61
msgid "refer rate(%)"
msgstr "推广率（%）"

#: .\promotion\models.py:69 .\promotion\models.py:70
msgid "Promotion Level Config"
msgstr "推广等级配置"

#: .\promotion\models.py:82 .\promotion\models.py:83
msgid "PromotionCaseConfig"
msgstr "推广箱子配置"

#: .\promotion\service\admin_actions.py:37
msgid "推广记录导出Excel"
msgstr ""

#: .\roll\admin.py:62 .\roll\models.py:83 .\roll\models.py:84
msgid "Roll Room Bet"
msgstr "Roll房加入"

#: .\roll\admin.py:105
msgid "room uid"
msgstr "房间"

#: .\roll\business.py:65
msgid "Invalid fee"
msgstr "无效等级"

#: .\roll\business.py:186
msgid "存入数量必须大于获胜人数"
msgstr ""

#: .\roll\business.py:228
msgid "Invalid password"
msgstr "密码错误"

#: .\roll\business.py:243 .\roll\business.py:249 .\roll\business.py:255
msgid "限定时间内充值金额小于参加条件"
msgstr ""

#: .\roll\business.py:260
msgid "充值金额小于参加条件"
msgstr ""

#: .\roll\business.py:263
msgid "Roll room bet"
msgstr "Roll房加入"

#: .\roll\business.py:295
msgid "Somebody has joined"
msgstr "已经加入"

#: .\roll\business.py:301
msgid "Roll room cancel"
msgstr "Roll房取消"

#: .\roll\business.py:394
msgid "页码错误"
msgstr ""

#: .\roll\business.py:487 .\roll\business.py:632
msgid "Roll room fee"
msgstr "roll房费率"

#: .\roll\business.py:728
msgid "Rollroom cancel"
msgstr "Roll房取消"

#: .\roll\model_signals.py:43
msgid "Rollroom win"
msgstr "Roll房胜利"

#: .\roll\models.py:21
msgid "No charge room"
msgstr "非收费房"

#: .\roll\models.py:22
msgid "Charge room"
msgstr "收费房间"

#: .\roll\models.py:25
msgid "主播"
msgstr ""

#: .\roll\models.py:26
msgid "官方"
msgstr ""

#: .\roll\models.py:33
msgid "entrance fee"
msgstr "房费"

#: .\roll\models.py:34
msgid "min joiner"
msgstr "最小参加人数"

#: .\roll\models.py:36
msgid "max winner"
msgstr "最大获胜人数"

#: .\roll\models.py:37
msgid "begin time"
msgstr "开始时间"

#: .\roll\models.py:38
msgid "due time"
msgstr "结束时间"

#: .\roll\models.py:39
msgid "win time"
msgstr "开奖时间"

#: .\roll\models.py:40
msgid "total fee"
msgstr "总充值"

#: .\roll\models.py:43
msgid "total items count"
msgstr "饰品总数"

#: .\roll\models.py:45
msgid "deposit enable"
msgstr "启用存入限制"

#: .\roll\models.py:46
msgid "rollroom enable"
msgstr "Roll房启用"

#: .\roll\models.py:47
msgid "charge limit amount"
msgstr "充值金额限制"

#: .\roll\models.py:48
msgid "charge begin time"
msgstr "充值开始时间限制"

#: .\roll\models.py:49
msgid "charge end time"
msgstr "充值结束时间限制"

#: .\roll\models.py:50
msgid "official"
msgstr ""

#: .\roll\models.py:67 .\roll\models.py:68
msgid "Roll Room"
msgstr "roll房"

#: .\roll\models.py:90
msgid "Balance"
msgstr "余额"

#: .\roll\models.py:93
msgid "win roll bet"
msgstr "获胜结果"

#: .\roll\models.py:102 .\roll\models.py:103
msgid "Roll Room Item"
msgstr "Roll房饰品"

#: .\roll\models.py:132 .\roll\models.py:133
msgid "Roll Room Pump Day"
msgstr "日Crash Pump"

#: .\roll\models.py:162 .\roll\models.py:163
msgid "Roll Room Pump Month"
msgstr "月Crash Pump"

#: .\roll\models.py:168
msgid "join idle min(seconds)"
msgstr "最小加入间隔（秒）"

#: .\roll\models.py:169
msgid "join idle max(seconds)"
msgstr "最大加入间隔（秒）"

#: .\roll\models.py:174 .\roll\models.py:175
msgid "Rollroom Bot Config"
msgstr "Roll房机器人配置"

#: .\roll\models.py:182
#, fuzzy
#| msgid "bet"
msgid "bot"
msgstr "对战"

#: .\roll\models.py:186 .\roll\models.py:187
msgid "Roll房机器人"
msgstr ""

#: .\roll\models.py:202 .\roll\models.py:203
msgid "Rollroom Statistics Day"
msgstr "Roll房日统计"

#: .\roll\models.py:262 .\roll\models.py:263
msgid "Rollroom Statistics Month"
msgstr "Roll房月统计"

#: .\roll\service\admin_actions.py:26
msgid "Cancel rollroom state complete"
msgstr "取消roll房完成"

#: .\roll\service\admin_actions.py:28
msgid "Cancel rollroom state"
msgstr "取消roll房"

#: .\roll\service\admin_actions.py:40
msgid "delete rollroom complete"
msgstr "删除roll房完成"

#: .\roll\service\admin_actions.py:42
msgid "Delete rollroom"
msgstr "删除roll房"

#: .\sitecfg\apps.py:7 .\sitecfg\models.py:29 .\sitecfg\models.py:30
msgid "Site Config"
msgstr "站点配置"

#: .\sitecfg\models.py:13 .\sitecfg\models.py:14
msgid "Site Config Category"
msgstr "站点配置类别"

#: .\sitecfg\models.py:24
msgid "value"
msgstr "值"

#: .\sitecfg\models.py:38
msgid "Homepage"
msgstr "主页"

#: .\sitecfg\models.py:43 .\sitecfg\models.py:81 .\sitecfg\models.py:97
msgid "content"
msgstr "描述内容"

#: .\package\models.py:29
msgid "content_en"
msgstr "英文介绍"

#: .\package\models.py:30
msgid "content_cn"
msgstr "中文介绍"

#: .\sitecfg\models.py:48 .\sitecfg\models.py:49 .\sitecfg\models.py:57
#: .\sitecfg\models.py:64
msgid "Announce"
msgstr "声明"

#: .\sitecfg\models.py:58
msgid "Link"
msgstr "链接"

#: .\sitecfg\models.py:63
msgid "link"
msgstr "链接"

#: .\sitecfg\models.py:71 .\sitecfg\models.py:72
msgid "Footer"
msgstr "页脚"

#: .\sitecfg\models.py:86 .\sitecfg\models.py:87
msgid "FAQ"
msgstr "FAQ"

#: .\sitecfg\models.py:104 .\sitecfg\models.py:105
msgid "Support"
msgstr "支持"

#: .\sitecfg\models.py:113
#, fuzzy
#| msgid "Market"
msgid "target"
msgstr "市场"

#: .\sitecfg\models.py:120 .\sitecfg\models.py:121
#, fuzzy
#| msgid "winner"
msgid "Banner"
msgstr "获胜者"

#: .\sitecfg\service\admin_actions.py:18
msgid "set online users base count complete"
msgstr "在线用户基数设置完成"

#: .\sitecfg\service\admin_actions.py:20
msgid "set online users base count fail"
msgstr "在线用户基数设置失败"

#: .\sitecfg\service\admin_actions.py:22
msgid "set online base count"
msgstr "设置在线用户基数"

#: .\steambase\models.py:21
msgid "uid"
msgstr "uid"

#: .\steambase\models.py:37
msgid "WearCategory0"
msgstr "崭新出厂"

#: .\steambase\models.py:38
msgid "WearCategory1"
msgstr "略有磨损"

#: .\steambase\models.py:39
msgid "WearCategory2"
msgstr "久经沙场"

#: .\steambase\models.py:40
msgid "WearCategory3"
msgstr "破损不堪"

#: .\steambase\models.py:41
msgid "WearCategory4"
msgstr "战痕累累"

#: .\steambase\models.py:46
msgid "name(CN)"
msgstr "名字（CN）"

#: .\steambase\models.py:47
msgid "market name(CN)"
msgstr "市场名称（CN）"

#: .\steambase\models.py:48
msgid "name color"
msgstr "名字颜色"

#: .\steambase\models.py:50
msgid "contextid"
msgstr "contextid"

#: .\steambase\models.py:51
msgid "classid"
msgstr "classid"

#: .\steambase\models.py:53
msgid "icon url large"
msgstr "大图标链接"

#: .\steambase\models.py:55
msgid "rarity color"
msgstr "稀有性颜色"

#: .\steambase\models.py:56
msgid "quality"
msgstr "质量"

#: .\steambase\models.py:57
msgid "quality color"
msgstr "质量颜色"

#: .\steambase\models.py:60
msgid "exterior"
msgstr "外观"

#: .\steambase\models.py:61
msgid "item set"
msgstr "饰品设置"

#: .\steambase\models.py:62
msgid "slot"
msgstr "slot"

#: .\steambase\models.py:63
msgid "hero"
msgstr "英雄"

#: .\templates\404.html:5
msgid "Not Found"
msgstr "未找到"

#: .\templates\404.html:10
msgid "Page Not Found"
msgstr "页面未找到"

#: .\templates\404.html:13 .\templates\500.html:16
msgid "Go to Home Page"
msgstr "去往主页"

#: .\templates\500.html:5 .\templates\500.html:9
msgid "Server Exception"
msgstr "服务器异常"

#: .\templates\500.html:13
msgid "Page Not Found111"
msgstr "页面未找到111"

#: .\templates\admin\base copy.html:60 .\templates\admin\base.html:60
msgid "Welcome,"
msgstr "欢迎，"

#: .\templates\admin\base copy.html:65 copy.html:182
#: .\templates\admin\base.html:65 .\templates\admin\base.html:182
msgid "View site"
msgstr "浏览站点"

#: .\templates\admin\base copy.html:70 copy.html:191
#: .\templates\admin\base.html:70 .\templates\admin\base.html:191
msgid "Documentation"
msgstr "文件"

#: .\templates\admin\base copy.html:74 .\templates\admin\base.html:74
msgid "Change password"
msgstr "更换密码"

#: .\templates\admin\base copy.html:76 .\templates\admin\base.html:76
msgid "Log out"
msgstr "登出"

#: .\templates\admin\base copy.html:86 copy.html:174
#: .\templates\admin\base.html:86 .\templates\admin\base.html:174
#: .\templates\admin\change_form.html:23
msgid "Home"
msgstr "主页"

#: .\templates\admin\base copy.html:154 .\templates\admin\base.html:154
msgid "back"
msgstr "返回"

#: .\templates\admin\base copy.html:235 .\templates\admin\base.html:235
msgid "Applications"
msgstr "应用"

#: .\templates\admin\base copy.html:259 .\templates\admin\base.html:259
msgid "Hide applications"
msgstr "隐藏应用"

#: .\templates\admin\base copy.html:260 .\templates\admin\base.html:260
msgid "Show hidden"
msgstr "显示隐藏项"

#: .\templates\admin\base copy.html:302 copy.html:321
#: .\templates\admin\base.html:286 .\templates\admin\base.html:305
msgid "Add bookmark"
msgstr "加书签"

#: .\templates\admin\base copy.html:305 .\templates\admin\base.html:289
msgid "Title"
msgstr "标题"

#: .\templates\admin\base copy.html:307 .\templates\admin\base.html:291
msgid "URL"
msgstr "链接"

#: .\templates\admin\base copy.html:315 .\templates\admin\base.html:299
msgid "Delete bookmark"
msgstr "删除书签"

#: .\templates\admin\base copy.html:316 .\templates\admin\base.html:300
msgid "Are you sure want to delete this bookmark?"
msgstr "您确定要删除此书签吗？"

#: .\templates\admin\base copy.html:323 .\templates\admin\base.html:307
msgid "bookmarks"
msgstr "书签"

#: .\templates\admin\base copy.html:331 copy.html:338
#: .\templates\admin\base.html:315 .\templates\admin\base.html:322
msgid "Remove"
msgstr "移除"

#: .\templates\admin\base copy.html:360 .\templates\admin\base.html:344
msgid "Search"
msgstr "搜索"

#: .\templates\admin\base copy.html:365 .\templates\admin\base.html:349
msgid "Application page"
msgstr "应用页面"

#: .\templates\admin\base copy.html:391 .\templates\admin\base.html:375
msgid "current theme"
msgstr "当前主题"

#: .\templates\admin\change_form.html:26
#, python-format
msgid "Add %(name)s"
msgstr "添加 %(name)s"

#: .\templates\admin\change_form.html:38
msgid "History"
msgstr "历史"

#: .\templates\admin\change_form.html:40
msgid "View on site"
msgstr "View on site"

#: .\templates\admin\change_form.html:52 .\templates\admin\login.html:21
msgid "Please correct the error below."
msgstr "请更正以下错误。"

#: .\templates\admin\change_form.html:52 .\templates\admin\login.html:21
msgid "Please correct the errors below."
msgstr "请更正以下错误。"

#: .\templates\admin\login.html:37
#, python-format
msgid ""
"You are authenticated as %(username)s, but are not authorized to access this "
"page. Would you like to login to a different account?"
msgstr "您已通过身份验证 %(username)s，但无权访问此页面。您要登录其他账户吗？"

#: .\templates\admin\login.html:79
msgid "Forgotten your password or username?"
msgstr ""

#: .\templates\admin\login.html:83
msgid "Log in"
msgstr "登录"

#: .\templates\admin\monitor.html:127
msgid "Connecting"
msgstr "连接中"

#: .\tradeup\apps.py:7
msgid "Tradeup"
msgstr "卖次买好"

#: .\tradeup\business.py:57 .\tradeup\business.py:100
#: .\tradeup\business.py:495 .\tradeup\business.py:532
msgid "Invalid bet items"
msgstr "无效对战饰品"

#: .\tradeup\business.py:110 .\tradeup\business.py:196
#: .\tradeup\business.py:503 .\tradeup\business.py:540
msgid "Invalid target items"
msgstr "无效目标饰品"

#: .\tradeup\business.py:126 .\tradeup\business.py:209
#, fuzzy
#| msgid "Coins Tradeup Bet amount"
msgid "Coins Tradeup game bet"
msgstr "Coins Tradeup Bet amount"

#: .\tradeup\business.py:132 .\tradeup\business.py:217
#: .\tradeup\business.py:520 .\tradeup\business.py:549
msgid "Invalid bet percentage"
msgstr "无效对战比例"

#: .\tradeup\business.py:202
msgid "Invalid target appid"
msgstr "无效目标appid"

#: .\tradeup\business.py:297
#, fuzzy
#| msgid "Coins Tradeup Bet amount"
msgid "Coins Tradeup game win"
msgstr "Coins Tradeup Bet amount"

#: .\tradeup\models.py:27
msgid "bet percentage"
msgstr "对战比例"

#: .\tradeup\models.py:28
msgid "upper"
msgstr "upper"

#: .\tradeup\models.py:29
msgid "origin amount"
msgstr "原始金额"

#: .\tradeup\models.py:30
msgid "target amount"
msgstr "目标金额"

#: .\tradeup\models.py:37 .\tradeup\models.py:38
msgid "Tradeup Game"
msgstr "Tradeup游戏"

#: .\tradeup\models.py:47
msgid "Amount"
msgstr "金额"

#: .\tradeup\models.py:52
msgid "bet item type"
msgstr "对战饰品种类"

#: .\tradeup\models.py:55 .\tradeup\models.py:56
msgid "Tradeup Bet Item"
msgstr "Tradeup Bet Item"

#: .\tradeup\models.py:74 .\tradeup\models.py:75
msgid "Coins Tradeup Bet amount"
msgstr "Coins Tradeup Bet amount"

#: .\tradeup\models.py:87 .\tradeup\models.py:88
msgid "Tradeup Target Item"
msgstr "Tradeup Target Item"

#: .\tradeup\models.py:102 .\tradeup\models.py:103
msgid "Tradeup Inventory Item"
msgstr "Tradeup Inventory Item"

#: .\tradeup\models.py:115 .\tradeup\models.py:116
msgid "Tradeup Pump Day"
msgstr "Tradeup Pump Day"

#: .\tradeup\models.py:139 .\tradeup\models.py:140
msgid "Tradeup Pump Month"
msgstr "Tradeup Pump Month"

#: .\tradeup\models.py:173 .\tradeup\models.py:174
msgid "Tradeup Statistics Day"
msgstr "Tradeup Statistics Day"

#: .\tradeup\models.py:257 .\tradeup\models.py:258
msgid "Tradeup Statistics Month"
msgstr "Tradeup Statistics Month"

#: .\tradeup\models.py:346 .\tradeup\models.py:347
msgid "Tradeup Win Day Rank"
msgstr ""

#: .\tradeup\models.py:371 .\tradeup\models.py:372
msgid "Tradeup Win Week Rank"
msgstr "Tradeup Win Week Rank"

#: .\tradeup\models.py:396
msgid "bet idle min(seconds)"
msgstr "最小对战间隔（秒）"

#: .\tradeup\models.py:397
msgid "bet idle max(seconds)"
msgstr "最大对战间隔（秒）"

#: .\tradeup\models.py:398
msgid "bot tradeup min chance"
msgstr "bot tradeup min chance"

#: .\tradeup\models.py:399
msgid "bot tradeup max chance"
msgstr "bot tradeup max chance"

#: .\tradeup\models.py:400
msgid "bot item price min"
msgstr "最小机器人饰品价格"

#: .\tradeup\models.py:401
msgid "bot item price max"
msgstr "最大机器人饰品价格"

#: .\tradeup\models.py:406 .\tradeup\models.py:407
msgid "Tradeup Bot Config"
msgstr "Tradeup Bot Config"

#: .\tradeup\service\admin_actions.py:21
msgid "Sync Tradeup Price complete"
msgstr "Sync Tradeup Price complete"

#: .\tradeup\service\admin_actions.py:23
msgid "Sync Tradeup Price state"
msgstr "Sync Tradeup Price state"

#: .\websocket\apps.py:7
msgid "Websocket"
msgstr "Websocket"

#: .\withdraw\apps.py:7
msgid "withdraw"
msgstr "提取"

#: .\withdraw\business.py:33
msgid "您需累计充值15$才能提取饰品"
msgstr ""

#: .\withdraw\business.py:43
msgid "You have another unclosed trade"
msgstr "您还有另一笔未关闭的交易"

#: .\withdraw\business.py:46
msgid "Invalid package items"
msgstr "无效背包饰品"

#: .\withdraw\business.py:50
#, fuzzy
#| msgid "Minimum withdrawal amount is 1$"
msgid "Minimum withdrawal amount"
msgstr "最小取回金额为1$"

#: .\withdraw\business.py:168
#, fuzzy
#| msgid "point price"
msgid "Error price"
msgstr "积分价格"

#: .\withdraw\business.py:177
msgid "Over withdraw price"
msgstr ""

#: .\withdraw\business.py:185
msgid "Items is not allowed, please try another items"
msgstr "饰品不允许，请尝试其他饰品"

#: .\withdraw\business.py:236
msgid "success"
msgstr ""

#: .\withdraw\business.py:320
msgid "close trade error"
msgstr "关闭交易错误"

#: .\withdraw\models.py:22
msgid "Closed"
msgstr "已关闭"

#: .\withdraw\models.py:23
msgid "PendClose"
msgstr "等待关闭"

#: .\withdraw\models.py:27
msgid "waiting for user to buy more items"
msgstr "等待用户购买更多饰品"

#: .\withdraw\models.py:28
msgid "Proccessing the trade"
msgstr "处理交易中"

#: .\withdraw\models.py:29
msgid "Waiting for seller to confirm"
msgstr "等待卖家确认"

#: .\withdraw\models.py:30
msgid "Trade Sent"
msgstr "交易发送"

#: .\withdraw\models.py:31
msgid "Completed"
msgstr "已完成"

#: .\withdraw\models.py:32
msgid "Declined and Refunded"
msgstr "拒绝并退款"

#: .\withdraw\models.py:39
msgid "steam id"
msgstr "steam id"

#: .\withdraw\models.py:42
msgid "amount used"
msgstr "已用金额"

#: .\withdraw\models.py:43
msgid "drop refund"
msgstr "掉落返还"

#: .\withdraw\models.py:44
msgid "balance refund"
msgstr "余额返还"

#: .\withdraw\models.py:45
msgid "trade state"
msgstr "交易状态"

#: .\withdraw\models.py:57 .\withdraw\models.py:71
msgid "trade id"
msgstr "交易id"

#: .\withdraw\models.py:59
msgid "returned"
msgstr "已返回"

#: .\withdraw\models.py:63 .\withdraw\models.py:64
msgid "Withdraw Item"
msgstr "提取饰品"

#: .\withdraw\models.py:72
msgid "project id"
msgstr "项目id"

#: .\withdraw\models.py:73
msgid "waxpeer id"
msgstr "waxpeer id"

#: .\withdraw\models.py:74
msgid "waxpeer item id"
msgstr "waxpeer饰品id"

#: .\withdraw\models.py:75
msgid "waxpeer withdraw status"
msgstr "waxpeer取回状态"

#: .\withdraw\models.py:77
msgid "done"
msgstr "已完成"

#: .\withdraw\models.py:81
msgid "steam trade id"
msgstr "steam交易id"

#: .\withdraw\models.py:85 .\withdraw\models.py:86
msgid "Wxp Trade Offer"
msgstr "Wxp交易记录"

#: .\withdraw\models.py:107 .\withdraw\models.py:108
msgid "Trade Statistics Day"
msgstr "日交易统计"

#: .\withdraw\models.py:150 .\withdraw\models.py:151
msgid "Trade Statistics Month"
msgstr "月交易统计"

#, fuzzy
#~| msgid "Invalid level"
#~ msgid "Invalid Level"
#~ msgstr "无效等级"

#~ msgid "Not enough invitations"
#~ msgstr "邀请数量不足"

#~ msgid "Invalid amount"
#~ msgstr "无效金额"

#~ msgid "Diamond exchange coins"
#~ msgstr "交换Coins"

#~ msgid "Shop"
#~ msgstr "商城"

#~ msgid "Invalid shop item"
#~ msgstr "无效商城饰品"

#~ msgid "Out of stock"
#~ msgstr "缺货"

#~ msgid "Invalid shop pay type"
#~ msgstr "无效商城支付种类"

#~ msgid "diamond price"
#~ msgstr "钻石价格"

#~ msgid "active point price"
#~ msgstr "活跃积分价格"

#~ msgid "point price"
#~ msgstr "积分价格"

#~ msgid "Shop Item"
#~ msgstr "商城饰品"

#~ msgid "Active point"
#~ msgstr "活跃积分"

#~ msgid "Point"
#~ msgstr "积分"

#~ msgid "Create case room"
#~ msgstr "创建开箱房间"

#~ msgid "Case room bet"
#~ msgstr "对战"

#~ msgid "Exchange item"
#~ msgstr "出售饰品"

#~ msgid "The current trade url does not belong to your account"
#~ msgstr "当前交易链接不属于您的账户"

#~ msgid "Phone is bound by another user"
#~ msgstr "手机已被其他用户绑定"

#~ msgid "only superuse can charge in test"
#~ msgstr "只有超级用户才能在测试中充值"

#~ msgid "G2Apay"
#~ msgstr "G2Apay"

#, fuzzy
#~| msgid "Invalid items"
#~ msgid "Invalid item price"
#~ msgstr "无效饰品"

#~ msgid "buy failed, please try another item"
#~ msgstr "购买失败，请尝试其他饰品"

#~ msgid "{}"
#~ msgstr "{}"
