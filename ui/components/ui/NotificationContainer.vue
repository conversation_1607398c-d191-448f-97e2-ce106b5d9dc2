<template>
  <Teleport to="body">
    <div class="fixed top-4 right-4 z-[9999] max-w-sm space-y-3">
      <TransitionGroup
        name="notification"
        tag="div"
      >
        <div
          v-for="notification in notifications"
          :key="notification.id"
          class="bg-gray-800/90 backdrop-blur-md rounded-lg border p-4 shadow-2xl min-w-80"
          :class="getNotificationClass(notification.type)"
        >
          <div class="flex items-start gap-3">
            <!-- 图标 -->
            <div class="flex-shrink-0 mt-0.5">
              <i
                :class="getIconClass(notification.type)"
                class="text-lg"
              />
            </div>
            
            <!-- 内容 -->
            <div class="flex-1 min-w-0">
              <h4 class="font-medium text-white mb-1">
                {{ notification.title }}
              </h4>
              <p
                v-if="notification.message"
                class="text-sm text-gray-300"
              >
                {{ notification.message }}
              </p>
            </div>
            
            <!-- 关闭按钮 -->
            <button
              @click="removeNotification(notification.id)"
              class="flex-shrink-0 text-gray-400 hover:text-white transition-colors"
            >
              <i class="fa fa-times" />
            </button>
          </div>
          
          <!-- 进度条（如果有持续时间） -->
          <div
            v-if="notification.duration && !notification.persistent"
            class="mt-3 h-1 bg-gray-700 rounded-full overflow-hidden"
          >
            <div
              class="h-full transition-all ease-linear"
              :class="getProgressClass(notification.type)"
              :style="{
                width: '100%',
                animation: `shrink ${notification.duration}ms linear forwards`
              }"
            />
          </div>
        </div>
      </TransitionGroup>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
const { notifications, removeNotification } = useNotification()

// 获取通知样式类
const getNotificationClass = (type: string) => {
  const classes = {
    success: 'border-emerald-500/30',
    error: 'border-red-500/30',
    warning: 'border-orange-500/30',
    info: 'border-blue-500/30'
  }
  return classes[type as keyof typeof classes] || classes.info
}

// 获取图标类
const getIconClass = (type: string) => {
  const icons = {
    success: 'fa fa-check-circle text-emerald-400',
    error: 'fa fa-exclamation-circle text-red-400',
    warning: 'fa fa-exclamation-triangle text-orange-400',
    info: 'fa fa-info-circle text-blue-400'
  }
  return icons[type as keyof typeof icons] || icons.info
}

// 获取进度条样式类
const getProgressClass = (type: string) => {
  const classes = {
    success: 'bg-emerald-500',
    error: 'bg-red-500',
    warning: 'bg-orange-500',
    info: 'bg-blue-500'
  }
  return classes[type as keyof typeof classes] || classes.info
}
</script>

<style scoped>
/* 通知动画 */
.notification-enter-active,
.notification-leave-active {
  transition: all 0.3s ease;
}

.notification-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.notification-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

.notification-move {
  transition: transform 0.3s ease;
}

/* 进度条动画 */
@keyframes shrink {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}
</style>
