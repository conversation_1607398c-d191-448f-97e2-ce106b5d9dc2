import logging
from django.shortcuts import render

from rest_framework.views import APIView
from rest_framework.permissions import AllowAny
from django.utils.translation import gettext_lazy as _

from blindbox.business import user_open_blind_case, get_blindbox_list, get_free_blindbox_list, get_blindbox_detail, \
    get_blindbox_record, get_blindbox_recent, get_blindbox_current, replace_current_game
from sitecfg.interfaces import get_maintenance_box
from steambase.utils import reformat_resp, current_user, ParamException, current_sock
from steambase.enums import RespCode


_logger = logging.getLogger(__name__)


class BlindBoxOpenView(APIView):

    def post(self, request):
        try:
            user = current_user(request)

            if get_maintenance_box():
                return reformat_resp(RespCode.Maintenance.value, {},
                                     _('Current game is under maintenance, please wait for a while.'))
            case_key = request.data.get('case_key', None)
            gid = request.data.get('game', None)
            pos = request.data.get('pos', None)
            code, resp = user_open_blind_case(user, case_key, gid, int(pos))
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, _('Succeed'))
            else:
                return reformat_resp(code, {}, resp)
        except ParamException as pe:
            return reformat_resp(RespCode.InvalidParams.value, {}, str(pe))
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, _('Exception'))


class ReplaceBlindBoxView(APIView):

    def post(self, request):
        try:
            user = current_user(request)
            key = request.data.get('case_key', None)
            code, resp = replace_current_game(user, key)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, _('Succeed'))
            else:
                return reformat_resp(code, {}, resp)

        except ParamException as pe:
            return reformat_resp(RespCode.InvalidParams.value, {}, str(pe))

        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, _('Exception'))

class GetBlindBoxListView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            user = current_user(request)
            query = {}
            fields = ('key', 'name', 'price', 'discount', 'cover', 'item', 'case_type_category',)
            code, resp = get_blindbox_list(user, query, fields)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, _('Succeed'))
            else:
                return reformat_resp(code, {}, resp)
        except ParamException as pe:
            return reformat_resp(RespCode.InvalidParams.value, {}, str(pe))
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, _('Exception'))


class GetFreeBlindBoxListView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            user = current_user(request)
            query = {}
            fields = ('level', 'min_amount', 'max_amount', 'case')
            code, resp = get_free_blindbox_list(user, query, fields)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, _('Succeed'))
            else:
                return reformat_resp(code, {}, resp)
        except ParamException as pe:
            return reformat_resp(RespCode.InvalidParams.value, {}, str(pe))
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, _('Exception'))


class GetBlindBoxCurrentGameView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            user = current_user(request)
            key = request.query_params.get('case_key', None)
            # page = request.query_params.get('page', 1)
            # page_size = request.query_params.get('pageSize', 18)
            code, resp = get_blindbox_current(user, key)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, _('Succeed'))
            else:
                return reformat_resp(code, {}, resp)
        except ParamException as pe:
            return reformat_resp(RespCode.InvalidParams.value, {}, str(pe))
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, _('Exception'))

class GetBlindBoxDetailView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            user = current_user(request)
            key = request.query_params.get('case_key', None)
            query = {}
            fields = ('key', 'name', 'price', 'discount', 'cover', 'blind_drops', 'unlock', 'limited', 'limited_time', 'case_type_category')
            code, resp = get_blindbox_detail(user, key, query, fields)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, _('Succeed'))
            else:
                return reformat_resp(code, {}, resp)
        except ParamException as pe:
            return reformat_resp(RespCode.InvalidParams.value, {}, str(pe))
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, _('Exception'))


class GetBlindBoxRecordView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            user = current_user(request)
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            fields = (
                'uid', 'box', 'case_name', 'market_name', 'market_name_cn', 'icon_url', 'price', 'cost', 'create_time', 'code', 'ecode', "secret"
            )

            code, resp = get_blindbox_record(user, fields, page, page_size)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, _('Succeed'))
            else:
                return reformat_resp(code, {}, resp)
        except ParamException as pe:
            return reformat_resp(RespCode.InvalidParams.value, {}, str(pe))
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, _('Exception'))


class GetBlindBoxRecentRecordView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            user = current_user(request)
            query = {}
            fields = ()
            code, resp = get_blindbox_recent(user, query, fields)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, _('Succeed'))
            else:
                return reformat_resp(code, {}, resp)
        except ParamException as pe:
            return reformat_resp(RespCode.InvalidParams.value, {}, str(pe))
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, _('Exception'))