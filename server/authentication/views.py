import logging
import time
import secrets

from django.contrib.auth.views import Logout<PERSON>iew as logout
from django.shortcuts import render, redirect
from functools import lru_cache

from django.core.exceptions import ValidationError
import re
import ipaddress
from django.http import JsonResponse
from django.core.cache import cache
from django.middleware.csrf import get_token
from django.views.decorators.csrf import ensure_csrf_cookie
from django.utils.decorators import method_decorator

from django.views import View
from rest_framework.views import APIView
from rest_framework.permissions import AllowAny
from rest_framework.authentication import SessionAuthentication, BasicAuthentication
from rest_framework.response import Response


from libs.mail_server import send_mail
from steambase.enums import RespCode
from steambase.utils import reformat_resp, current_user, ParamException


from authentication.business import get_user_info, set_user_tradeurl, set_user_phone, set_avatar, reset_pwd_by_email, login_by_email, register_by_email, get_user_balance_record
from authentication.business import login_by_phone, send_sms_code, send_email_code, get_captcha, generate_token, validate_phone_number, verify_captcha
from authentication.business import register_by_phone, reset_pwd_by_phone, set_personaname, set_user_email, check_user_exam, set_user_exam

from sitecfg.models import SEO, AccessControl
from sitecfg.interfaces import get_domain_verify
from authentication.tasks import delete_old_balance_records, delete_old_log_entries, update_staff_balance, delete_old_user_active_point_records
from authentication.models import EmailSuffix

#2025
from authentication.business import request_reset_email

_logger = logging.getLogger(__name__)


class IndexView(View):
    def get(self, request):
        return render(request, 'pages/steam/index.html')


class LogoutView(View):
    def get(self, request):
        logout(request)
        return redirect('/')


class AdminLogoutView(View):
    def get(self, request):
        logout(request)
        return redirect('admin:login')


class CheckLoginView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            is_login = current_user(request) is not None
            return reformat_resp(RespCode.Succeed.value, is_login, 'Succeed')
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetUserInfoView(APIView):
    def get(self, request):
        try:
            user = current_user(request)
            fields = ('uid', 'phone', 'email', 'nickname', 'profile', 'steam', 'asset', 'extra', 'promo', 'level', 'is_agent', 'exam_passed', 'is_active', 'is_vip', 'login_time', 'login_ip', 'date_joined')
            code, resp = get_user_info(user, fields)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')

class GetUserBalanceRecordView(APIView):
    def get(self, request):
        try:
            user = current_user(request)
            
            
            
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            
            fields = ('uid', 'create_time', 'update_time', 'balance_changed', 'balance_before', 'balance_after', 'reason')
            code, resp = get_user_balance_record(user, fields, page, page_size)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')
        

class SetUserTradeurlView(APIView):
    def post(self, request):
        try:
            user = current_user(request)
            tradeurl = request.data.get('tradeUrl', None)
            print(tradeurl)
            code, resp = set_user_tradeurl(user, tradeurl)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class SetUserPhoneView(APIView):
    def post(self, request):
        try:
            user = current_user(request)
            phone = request.data.get('phone', None)
            verify_code = request.data.get('verify_code', None)
            code, resp = set_user_phone(user, phone, verify_code)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')

class SetUserEmailView(APIView):
    def post(self, request):
        try:
            user = current_user(request)
            email = request.data.get('email', None)
            verify_code = request.data.get('verify_code', None)
            code, resp = set_user_email(user, email, verify_code)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class SetNameView(APIView):
    def post(self, request):
        try:
            user = current_user(request)
            personaname = request.data.get('name', None)
            code, resp = set_personaname(user, personaname)
            return reformat_resp(code, resp, 'Succeed' if code == RespCode.Succeed.value else resp)
        except ParamException as pe:
            return reformat_resp(RespCode.InvalidParams.value, {}, str(pe))
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, '服务器内部错误')


class SetAvatar(APIView):
    def post(self, request):
        try:
            user = current_user(request)
            image = request.data.get('data')
            code, resp = set_avatar(user, image)
            return reformat_resp(
                code,
                {'avatar_url': resp} if code == RespCode.Succeed.value else {},
                'Succeed' if code == RespCode.Succeed.value else resp
            )
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, '服务器内部错误')


class CsrfExemptSessionAuthentication(SessionAuthentication):

    def enforce_csrf(self, request):
        return


class GetCsrftokenView(APIView):
    permission_classes = [AllowAny]
    
    @method_decorator(ensure_csrf_cookie)
    def get(self, request):
        csrf_token = get_token(request)
        return reformat_resp(RespCode.Succeed.value, {'csrf_token': csrf_token}, 'Succeed')


def validate_email(email):
    """
    验证邮箱地址是否合法。
    :param email: 要验证的邮箱地址
    :return: 如果邮箱地址有效，返回True；否则返回False。
    """
    # 定义邮箱地址的正则表达式规则
    email_regex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    
    # 使用正则表达式匹配邮箱地址
    if re.match(email_regex, email):
        return True
    else:
        return False



class RegisterByPhoneView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        try:
            phone = request.data.get('phone', None)
            name = request.data.get('name', None)
            
            verify_code = request.data.get('verify_code', None)
            password = request.data.get('password', None)
            ref_code = request.data.get('ref_code', None)
            token = request.data.get('token', None)  # 新增获取token

            

            if not token:
                return reformat_resp(RespCode.BadRequest.value, {}, 'Token is required')

            code, resp = register_by_phone(request, phone, name, verify_code, password, ref_code, token)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class LoginByPhoneView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        try:
            phone = request.data.get('phone', None)
            password = request.data.get('password', None)                
            
            code, resp = login_by_phone(request, phone, password)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class ResetPasswordByPhoneView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        try:
            phone = request.data.get('phone', None)
            verify_code = request.data.get('verify_code', None)
            password = request.data.get('password', None)
            
            if not phone:
                return reformat_resp(RespCode.InvalidParams.value, {}, "手机号不能为空")
            if not validate_phone_number(phone):
                return reformat_resp(RespCode.InvalidParams.value, {}, "手机号格式不正确")

            code, resp = reset_pwd_by_phone(phone, password, verify_code)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class RegisterByEmailView(APIView):
    permission_classes = [AllowAny]
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)

    def post(self, request):
        try:
            email = request.data.get('email', None)
            name = request.data.get('name', None)
            verify_code = request.data.get('verify_code', None)
            password = request.data.get('password', None)
            ref_code = request.data.get('ref_code', None)

            code, resp = register_by_email(request, email, name, verify_code, password, ref_code)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')







class LoginByEmailView(APIView):
    permission_classes = [AllowAny]
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)

    def post(self, request):
        try:            
            email = request.data.get('email', None)
            password = request.data.get('password', None)
            captcha = request.data.get('captcha', None)
            uuid = request.data.get('uuid', None)           

            code, resp = login_by_email(request, email, password, captcha, uuid)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class ResetPasswordByEmailView(APIView):
    permission_classes = [AllowAny]
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)

    def post(self, request):
        try:
            email = request.data.get('email', None)
            verify_code = request.data.get('verify_code', None)
            password = request.data.get('newPassword', None)
            code, resp = reset_pwd_by_email(email, password, verify_code)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')
    
class CheckUserExamView(APIView):
    #permission_classes = [AllowAny]
    def get(self, request):
        try:
            user = current_user(request)
            code, resp = check_user_exam(user)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')
    
class SetUserExamView(APIView):
    #permission_classes = [AllowAny]
    def post(self, request):
        try:
            user = current_user(request)
            age = int(request.data.get('age', 0))
            price = int(request.data.get('price', 0))
            opencase_principle = int(request.data.get('opencase_principle', 0))
            opencase_value = int(request.data.get('opencase_value', 0))
            opencase_animation = int(request.data.get('opencase_animation', 0))
            opencase_battle = int(request.data.get('opencase_battle', 0))
            
            if age == 2 and price == 3 and opencase_principle == 3 and opencase_value == 2 and opencase_animation == 2 and opencase_battle == 1:
                exam_passed = True
            else:
                exam_passed = False

                
            code, resp = set_user_exam(user, exam_passed)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')

def get_ip(req):
    ip = req.META['REMOTE_ADDR']
    # forwarded proxy fix for webfaction
    if (not ip or ip == '127.0.0.1') and req.META.get('HTTP_X_FORWARDED_FOR', ''):
        ip = req.META['HTTP_X_FORWARDED_FOR']
    return ip

class GetCaptchaView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            user = current_user(request)
            code, resp = get_captcha()
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetTokenView(APIView):
    permission_classes = [AllowAny]
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)

    def post(self, request):
        try:
            # 1. 获取请求参数            
            type = request.data.get('type')  # 'sms' or 'email'
            contact = request.data.get('contact')  # phone number or email address
            # 验证码参数
            captcha = request.data.get('code', None)
            uuid = request.data.get('uuid', None)

            # 2. 调用业务层生成token（包含验证码校验逻辑）
            code, resp, message = generate_token(request, type, contact, captcha, uuid)
            
            # 3. 返回结果
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, message)

        except Exception as e:
            _logger.exception(f'生成令牌失败: {str(e)}')
            return reformat_resp(RespCode.Exception.value, {}, '服务器内部错误')

        
class SendSmsCodeView(APIView):
    permission_classes = [AllowAny]
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)

    def post(self, request):
        try:
            phone = request.data.get('phone')
            type = int(request.data.get('type', 0))
            token = request.data.get('token')          
           
            # 调用业务层统一处理短信发送及所有安全校验逻辑
            code_status, resp = send_sms_code(request, phone, type, token)

            return reformat_resp(code_status, resp, 'Succeed' if code_status == RespCode.Succeed.value else resp)

        except Exception as e:
            _logger.exception(f'Error sending verification code: {str(e)}')
            return reformat_resp(RespCode.Exception.value, {}, 'Exception')

class SendEmailCodeView(APIView):
    permission_classes = [AllowAny]
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)

    def post(self, request):
        try:
            email = request.data.get('email', '')
            type = int(request.data.get('type', 0)) # 1 注册 2 找回密码
            token = request.data.get('token', '')            

            # 调用业务层统一处理验证码发送及校验逻辑
            code, resp = send_email_code(request, email, type, token=token)
            return reformat_resp(code, resp, 'Succeed' if code == RespCode.Succeed.value else resp)

        except Exception as e:
            _logger.exception(f'Error sending email verification code: {str(e)}')
            return reformat_resp(RespCode.Exception.value, {}, 'Exception')
        

# 测试删除历史记录方法

class DeleteOldBalanceRecordsView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        """
        触发删除旧余额记录的任务并返回执行结果。
        """
        user = current_user(request)
        try:
            # 调用删除方法
            #if user.is_superuser:
            result = delete_old_balance_records()
            
            if result:
                return Response({'code': 200, 'message': result}, status=200)
            else:
                return Response({'code': 200, 'message': 'No old records found'}, status=200)
            
        except Exception as e:
            _logger.exception(e)
            return Response({'code': 500, 'message': 'Exception occurred'}, status=500)
        
class DeleteOldLogView(APIView):
    permission_classes = [AllowAny]
    def get(self, request):
        """
        触发删除旧登录记录的任务并返回执行结果。
        """
        user = current_user(request)
        try:
            # 调用删除方法
            #if user.is_superuser:
            result = delete_old_log_entries(100, 30)
            
            if result:
                return Response({'code': 200, 'message': result}, status=200)
            else:
                return Response({'code': 200, 'message': 'No old records found'}, status=200)
            
        except Exception as e:
            _logger.exception(e)
            return Response({'code': 500, 'message': 'Exception occurred'}, status=500)
        
class UpdateStaffBalanceView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        """
        触发更新员工余额的任务并返回执行结果。
        """
        user = current_user(request)
            
        try:
            # 调用更新方法
            # if user.is_superuser:
            result = update_staff_balance()
            
            if result:
                return Response({'code': 200, 'message': result}, status=200)
            else:
                return Response({'code': 200, 'message': 'No staff found'}, status=200)
            
        except Exception as e:
            _logger.exception(e)
            return Response({'code': 500, 'message': 'Exception occurred'}, status=500)
       
class DeleteOldUserActivePointRecordsView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        """
        触发删除旧用户活跃度记录的任务并返回执行结果。
        """
        user = current_user(request)
        try:
            # 调用删除方法
            #if user.is_superuser:
            result = delete_old_user_active_point_records()
            
            if result:
                return Response({'code': 200, 'message': result}, status=200)
            else:
                return Response({'code': 200, 'message': 'No old records found'}, status=200)
            
        except Exception as e:
            _logger.exception(e)
            return Response({'code': 500, 'message': 'Exception occurred'}, status=500)
        
# 2025

class RequestResetEmailView(APIView):
    permission_classes = [AllowAny]
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)

    def post(self, request):
        try:
            # 仅提取参数，校验逻辑放在 business 层
            email = request.data.get('email')
            verify_code = request.data.get('code')
            print(email, verify_code)

            status_code, resp = request_reset_email(email, verify_code)

            return reformat_resp(
                status_code,
                resp if status_code == RespCode.Succeed.value else {},
                'Succeed' if status_code == RespCode.Succeed.value else resp
            )

        except Exception as e:
            _logger.exception(f'Error requesting reset email: {str(e)}')
            return reformat_resp(RespCode.Exception.value, {}, '服务器内部错误')
            
