#!/usr/bin/env python
"""
将sitecfg中的Article和Announcee数据迁移到新的articles app
"""
import os
import sys
import django
from django.utils import timezone
from django.utils.text import slugify
import uuid

# 添加项目路径
sys.path.append('/www/wwwroot/csgoskins.com.cn/server')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')

# 初始化Django
django.setup()

from django.db import connection

def migrate_sitecfg_data():
    """迁移sitecfg中的数据到articles app"""
    
    print("🔄 开始迁移sitecfg数据到articles app...")
    
    # 首先创建默认分类
    create_default_categories()
    
    # 迁移Article数据
    migrate_articles()
    
    # 迁移Announcee数据
    migrate_announcements()
    
    print("✅ 数据迁移完成！")

def create_default_categories():
    """创建默认分类"""
    print("📁 创建默认分类...")
    
    categories = [
        {
            'name': '系统公告',
            'slug': 'system-announcements',
            'description': '系统相关的重要公告',
            'icon': 'fas fa-bullhorn',
            'color': '#dc3545'
        },
        {
            'name': '游戏资讯',
            'slug': 'game-news',
            'description': '游戏相关的最新资讯',
            'icon': 'fas fa-gamepad',
            'color': '#007bff'
        },
        {
            'name': '活动公告',
            'slug': 'event-announcements',
            'description': '各类活动和促销信息',
            'icon': 'fas fa-calendar-alt',
            'color': '#28a745'
        },
        {
            'name': '帮助文档',
            'slug': 'help-docs',
            'description': '使用帮助和常见问题',
            'icon': 'fas fa-question-circle',
            'color': '#ffc107'
        }
    ]
    
    with connection.cursor() as cursor:
        for cat in categories:
            cursor.execute("""
                INSERT IGNORE INTO articles_contentcategory 
                (name, slug, description, icon, color, sort_order, is_active, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, [
                cat['name'], cat['slug'], cat['description'], cat['icon'], cat['color'],
                0, True, timezone.now(), timezone.now()
            ])
            print(f"  ✓ 创建分类: {cat['name']}")

def migrate_articles():
    """迁移Article数据"""
    print("📄 迁移Article数据...")
    
    with connection.cursor() as cursor:
        # 检查是否存在sitecfg_article表
        cursor.execute("SHOW TABLES LIKE 'sitecfg_article'")
        if not cursor.fetchone():
            print("  ⚠️  sitecfg_article表不存在，跳过Article迁移")
            return
        
        # 获取Article数据
        cursor.execute("""
            SELECT id, title, content, create_time, update_time, enable
            FROM sitecfg_article
            ORDER BY create_time DESC
        """)
        
        articles = cursor.fetchall()
        
        # 获取默认分类ID
        cursor.execute("SELECT id FROM articles_contentcategory WHERE slug = 'help-docs' LIMIT 1")
        default_category_id = cursor.fetchone()
        default_category_id = default_category_id[0] if default_category_id else None
        
        migrated_count = 0
        for article in articles:
            article_id, title, content, create_time, update_time, enable = article
            
            # 生成slug
            slug = slugify(title)[:240] + '-' + str(uuid.uuid4())[:8]
            
            # 生成摘要
            excerpt = (content[:200] + '...') if content and len(content) > 200 else content
            
            # 插入到新表
            cursor.execute("""
                INSERT IGNORE INTO articles_content 
                (title, slug, excerpt, content, content_type, status, priority, 
                 is_featured, is_pinned, publish_date, category_id, sort_order, 
                 view_count, allow_comments, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, [
                title, slug, excerpt, content, 'article',
                'published' if enable else 'draft', 'normal',
                False, False, create_time or timezone.now(), default_category_id, 0,
                0, True, create_time or timezone.now(), update_time or timezone.now()
            ])
            migrated_count += 1
        
        print(f"  ✓ 迁移了 {migrated_count} 篇文章")

def migrate_announcements():
    """迁移Announce数据"""
    print("📢 迁移Announce数据...")

    with connection.cursor() as cursor:
        # 检查是否存在sitecfg_announce表
        cursor.execute("SHOW TABLES LIKE 'sitecfg_announce'")
        if not cursor.fetchone():
            print("  ⚠️  sitecfg_announce表不存在，跳过Announce迁移")
            return

        # 获取Announce数据
        cursor.execute("""
            SELECT id, title, content, create_time, update_time, enable
            FROM sitecfg_announce
            ORDER BY create_time DESC
        """)
        
        announcements = cursor.fetchall()
        
        # 获取默认分类ID
        cursor.execute("SELECT id FROM articles_contentcategory WHERE slug = 'system-announcements' LIMIT 1")
        default_category_id = cursor.fetchone()
        default_category_id = default_category_id[0] if default_category_id else None
        
        migrated_count = 0
        for announcement in announcements:
            ann_id, title, content, create_time, update_time, enable = announcement
            
            # 生成slug
            slug = slugify(title)[:240] + '-' + str(uuid.uuid4())[:8]
            
            # 生成摘要
            excerpt = (content[:200] + '...') if content and len(content) > 200 else content
            
            # 插入到新表
            cursor.execute("""
                INSERT IGNORE INTO articles_content 
                (title, slug, excerpt, content, content_type, status, priority, 
                 is_featured, is_pinned, publish_date, category_id, sort_order, 
                 view_count, allow_comments, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, [
                title, slug, excerpt, content, 'announcement',
                'published' if enable else 'draft', 'high',
                True, True, create_time or timezone.now(), default_category_id, 0,
                0, True, create_time or timezone.now(), update_time or timezone.now()
            ])
            migrated_count += 1
        
        print(f"  ✓ 迁移了 {migrated_count} 条公告")

if __name__ == '__main__':
    migrate_sitecfg_data()
