# 核心框架 - 升级到Django 3.2 LTS
Django>=3.2.0,<3.3.0
djangorestframework>=3.12.0
gunicorn>=20.1.0

# Django扩展 - 升级到兼容Django 3.2的版本
django-admin-rangefilter>=0.8.0
django-celery-beat>=2.2.0
django-ckeditor>=6.0.0
django-ckeditor-5>=0.1.8
aenum>=3.1.0
captcha>=0.4.0
xmltodict>=0.12.0
django-environ>=0.8.0
django-js-asset>=2.0.0
django-modeltranslation>=0.17.0
django-redis>=5.2.0
django-tabular-export>=1.2.0
djangorestframework-xml>=2.0.0

# 安全相关 - 升级版本
cryptography>=3.4.8
cffi>=1.15.0
PyJWT>=2.4.0
python3-openid>=3.2.0
social-auth-app-django>=5.0.0
social-auth-core>=4.3.0

# 阿里云SDK - 保持兼容
aliyun-python-sdk-core
aliyun-python-sdk-core-v3
aliyun-python-sdk-dysmsapi
aliyun-python-sdk-kms
oss2>=2.15.0
python-alipay-sdk

# 数据库和缓存 - 升级版本
PyMySQL>=1.0.0
redis>=4.0.0,<5.0.0
SQLAlchemy>=1.4.0

# 异步任务 - 升级版本
apscheduler>=3.9.0
celery>=5.2.0
flower>=1.2.0
gevent>=21.0.0
greenlet>=1.1.0
schedule>=1.2.0

# AWS相关
boto3>=1.20.0
botocore>=1.23.0

# WebSocket支持 - 升级到Django 3.2兼容版本
channels>=3.0.0,<4.0.0
channels-redis>=3.4.0
daphne>=3.0.0

# 数据处理
numpy>=1.21.0
pandas>=1.3.0
Pillow>=8.3.0
python-dateutil>=2.8.0
pytz>=2021.3
requests>=2.26.0
urllib3>=1.26.0

# 开发和调试
ipython>=7.25.0
django-debug-toolbar>=3.2.0

# 其他工具
beautifulsoup4>=4.10.0
lxml>=4.6.0
openpyxl>=3.0.0
xlrd>=2.0.0
xlwt>=1.3.0

# 图像处理
opencv-python>=4.5.0
qrcode>=7.3.0

# 网络请求
httpx>=0.24.0
aiohttp>=3.8.0

# 序列化
marshmallow>=3.17.0
django-ckeditor>=6.0.0
