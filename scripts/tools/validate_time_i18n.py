#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间处理和国际化配置验证脚本
验证项目的时间配置和多语言支持是否符合规范
"""

import os
import sys
import re
from pathlib import Path

def check_django_time_settings():
    """检查Django时间配置"""
    print("🕐 检查Django时间配置...")
    
    settings_file = Path(__file__).parent.parent.parent / "server/steambase/settings.py"
    
    if not settings_file.exists():
        print("❌ settings.py文件不存在")
        return False
    
    with open(settings_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    checks = [
        ("TIME_ZONE = 'Asia/Shanghai'", "时区设置"),
        ("USE_TZ = True", "时区感知"),
        ("USE_I18N = True", "国际化支持"),
        ("CELERY_TIMEZONE = 'Asia/Shanghai'", "Celery时区")
    ]
    
    all_good = True
    for pattern, description in checks:
        if pattern in content:
            print(f"✅ {description}: 已配置")
        else:
            print(f"❌ {description}: 未找到配置 - {pattern}")
            all_good = False
    
    return all_good

def check_i18n_settings():
    """检查国际化配置"""
    print("\n🌍 检查国际化配置...")
    
    settings_file = Path(__file__).parent.parent.parent / "server/steambase/base_settings.py"
    
    if not settings_file.exists():
        print("❌ base_settings.py文件不存在")
        return False
    
    with open(settings_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    checks = [
        ("MODELTRANSLATION_LANGUAGES = ('en', 'zh-hans')", "模型翻译语言"),
        ("MODELTRANSLATION_DEFAULT_LANGUAGE = 'zh-hans'", "默认语言"),
        ("USE_I18N = True", "国际化启用"),
        ("USE_L10N = True", "本地化启用")
    ]
    
    all_good = True
    for pattern, description in checks:
        if pattern in content:
            print(f"✅ {description}: 已配置")
        else:
            print(f"❌ {description}: 未找到配置")
            all_good = False
    
    return all_good

def check_model_translations():
    """检查模型翻译使用"""
    print("\n📝 检查模型翻译使用...")
    
    server_dir = Path(__file__).parent.parent.parent / "server"
    issues_found = []
    good_examples = []
    
    # 检查所有应用的models.py文件
    for app_dir in server_dir.iterdir():
        if app_dir.is_dir() and (app_dir / "models.py").exists():
            models_file = app_dir / "models.py"
            
            with open(models_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否使用了翻译函数
            if 'from django.utils.translation import' in content:
                if 'verbose_name = _(' in content:
                    good_examples.append(f"{app_dir.name}/models.py")
                else:
                    # 检查是否有直接的中文字符串
                    if re.search(r'verbose_name = ["\'][^"\']*[\u4e00-\u9fff]', content):
                        issues_found.append(f"{app_dir.name}/models.py - 使用直接中文而非翻译函数")
    
    if good_examples:
        print("✅ 正确使用翻译函数的模型:")
        for example in good_examples[:5]:  # 只显示前5个
            print(f"   - {example}")
    
    if issues_found:
        print("⚠️ 发现问题:")
        for issue in issues_found:
            print(f"   - {issue}")
        return False
    else:
        print("✅ 模型翻译使用规范")
        return True

def check_api_time_fields():
    """检查API时间字段规范"""
    print("\n⏰ 检查API时间字段规范...")
    
    server_dir = Path(__file__).parent.parent.parent / "server"
    good_examples = []
    issues_found = []
    
    # 检查序列化器文件
    for app_dir in server_dir.iterdir():
        if app_dir.is_dir() and (app_dir / "serializers.py").exists():
            serializers_file = app_dir / "serializers.py"
            
            with open(serializers_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查时间字段格式
            if 'DateTimeField(format=' in content:
                if '"%Y-%m-%d %H:%M:%S"' in content:
                    good_examples.append(f"{app_dir.name}/serializers.py")
                else:
                    issues_found.append(f"{app_dir.name}/serializers.py - 时间格式不标准")
            
            # 检查时间戳字段
            if 'aware_datetime_to_timestamp' in content:
                good_examples.append(f"{app_dir.name}/serializers.py (时间戳)")
    
    if good_examples:
        print("✅ 正确的时间字段配置:")
        unique_examples = list(set(good_examples))[:5]
        for example in unique_examples:
            print(f"   - {example}")

    if issues_found:
        print("⚠️ 发现问题:")
        for issue in issues_found:
            print(f"   - {issue}")
        return False
    else:
        print("✅ API时间字段配置规范")
        return True

def check_frontend_i18n():
    """检查前端国际化配置"""
    print("\n🎨 检查前端国际化配置...")
    
    ui_dir = Path(__file__).parent.parent.parent / "ui"
    
    # 检查语言文件
    locales_dir = ui_dir / "locales"
    required_locales = ["zh-hans.json", "en.json"]
    
    all_good = True
    for locale_file in required_locales:
        locale_path = locales_dir / locale_file
        if locale_path.exists():
            print(f"✅ 语言文件存在: {locale_file}")
        else:
            print(f"❌ 语言文件缺失: {locale_file}")
            all_good = False
    
    # 检查语言切换组件
    language_switcher = ui_dir / "components/common/LanguageSwitcher.vue"
    if language_switcher.exists():
        print("✅ 语言切换组件存在")
    else:
        print("❌ 语言切换组件缺失")
        all_good = False
    
    # 检查语言管理可组合函数
    use_language = ui_dir / "composables/useLanguage.ts"
    if use_language.exists():
        print("✅ 语言管理可组合函数存在")
    else:
        print("❌ 语言管理可组合函数缺失")
        all_good = False
    
    return all_good

def check_time_utils():
    """检查时间工具函数"""
    print("\n🔧 检查时间工具函数...")
    
    utils_file = Path(__file__).parent.parent.parent / "server/steambase/utils.py"
    
    if not utils_file.exists():
        print("❌ utils.py文件不存在")
        return False
    
    with open(utils_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    required_functions = [
        ("aware_datetime_to_timestamp", "时间戳转换函数"),
        ("today_begin", "今日开始时间函数")
    ]
    
    all_good = True
    for func_name, description in required_functions:
        if f"def {func_name}" in content:
            print(f"✅ {description}: 已实现")
        else:
            print(f"❌ {description}: 未找到")
            all_good = False
    
    return all_good

def generate_recommendations():
    """生成改进建议"""
    print("\n💡 改进建议:")
    
    recommendations = [
        "1. 确保所有模型的verbose_name使用_()翻译函数",
        "2. API响应同时提供格式化时间和时间戳",
        "3. 前端时间显示统一使用北京时间",
        "4. 所有用户可见文本支持中英文切换",
        "5. 定期检查翻译文件的完整性",
        "6. 在API文档中明确时间格式规范",
        "7. 为新开发者提供时间和国际化的开发指南"
    ]
    
    for rec in recommendations:
        print(f"   {rec}")

def main():
    """主函数"""
    print("🌍 时间处理和国际化配置验证工具")
    print("=" * 60)
    
    # 执行各项检查
    checks = [
        ("Django时间配置", check_django_time_settings),
        ("国际化配置", check_i18n_settings),
        ("模型翻译", check_model_translations),
        ("API时间字段", check_api_time_fields),
        ("前端国际化", check_frontend_i18n),
        ("时间工具函数", check_time_utils)
    ]
    
    all_passed = True
    results = []
    
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
            if not result:
                all_passed = False
        except Exception as e:
            print(f"❌ {check_name}检查失败: {e}")
            results.append((check_name, False))
            all_passed = False
    
    # 输出总结
    print("\n" + "=" * 60)
    print("📊 检查结果总结:")
    
    for check_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {check_name}: {status}")
    
    # 生成建议
    generate_recommendations()
    
    if all_passed:
        print("\n🎉 所有检查都通过了！时间和国际化配置符合规范。")
        return 0
    else:
        print("\n⚠️ 发现问题，请根据上述提示进行修复。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
