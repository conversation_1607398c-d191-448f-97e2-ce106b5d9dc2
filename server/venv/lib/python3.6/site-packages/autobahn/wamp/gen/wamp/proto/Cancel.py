# automatically generated by the FlatBuffers compiler, do not modify

# namespace: proto

import flatbuffers

class Cancel(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAsCancel(cls, buf, offset):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = Cancel()
        x.Init(buf, n + offset)
        return x

    # Cancel
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # Cancel
    def Request(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint64Flags, o + self._tab.Pos)
        return 0

    # Cancel
    def Mode(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint8Flags, o + self._tab.Pos)
        return 0

def CancelStart(builder): builder.StartObject(2)
def CancelAddRequest(builder, request): builder.PrependUint64Slot(0, request, 0)
def CancelAddMode(builder, mode): builder.PrependUint8Slot(1, mode, 0)
def CancelEnd(builder): return builder.EndObject()
