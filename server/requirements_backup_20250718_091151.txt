# 核心框架
Django>=4.2.0,<5.0.0
djangorestframework>=3.14.0
gunicorn>=20.1.0

# Django扩展
django-admin-rangefilter>=0.10.0
django-celery-beat==2.5.0
django-ckeditor>=6.0.0
django-dynamic-raw-id>=4.0.0
django-environ>=0.10.0
# django-jet==1.0.8  # 暂时禁用，不兼容Django 4.x
django-js-asset>=2.0.0
django-modeltranslation>=0.18.0
django-redis>=5.2.0
django-tabular-export>=1.2.0
djangorestframework-xml

# 安全相关
cryptography==3.4.8
cffi>=1.12.0
django-secure==1.0.2
django-session-security==2.6.6
PyJWT==1.6.4
python3-openid==3.1.0
social-auth-app-django==2.1.0
social-auth-core==1.7.0

# 阿里云SDK
aliyun-python-sdk-core
aliyun-python-sdk-core-v3
aliyun-python-sdk-dysmsapi
aliyun-python-sdk-kms
oss2>=2.9.0
python-alipay-sdk

# 数据库和缓存
PyMySQL==0.8.1
redis>=3.5.0,<4.0.0
SQLAlchemy>=1.3.24

# 异步任务
apscheduler
celery==5.3.6
flower>=2.0.0
gevent==1.4.0
greenlet==0.4.15
schedule==0.5.0

# AWS相关
boto3==1.9.161
botocore==1.12.161
s3transfer==0.2.1

# 工具库
aenum==2.1.2
asn1crypto==0.24.0
astroid==1.6.3
captcha==0.3
certifi==2018.4.16
chardet==3.0.4
colorama==0.3.9
defusedxml==0.5.0
docutils==0.14
idna==2.6
isort==4.3.4
jmespath==0.9.3
lazy-object-proxy==1.3.1
mccabe==0.6.1
numpy==1.14.3
oauthlib==2.1.0
optionaldict==0.1.1
Pillow>=8.1.1
pyasn1==0.4.5
pycparser==2.19
pycryptodome==3.7.3
pycryptodomex==3.7.2
pylint==1.8.4
python-dateutil>=2.8.2
pytz==2018.4
requests>=2.20.0
requests-oauthlib==1.0.0
rsa==4.0
simplejson==3.16.0
six==1.11.0
urllib3>=1.24.2
wincertstore==0.2
wrapt==1.10.11
XlsxWriter==1.3.7
xmltodict==0.11.0
openpyxl
channels>=4.0.0
channels-redis>=4.0.0

# Django 4.x 新增依赖
asgiref>=3.6.0
daphne>=4.0.0