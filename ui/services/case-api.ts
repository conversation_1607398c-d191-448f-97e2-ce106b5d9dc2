// services/case-api.ts
import { authApi } from './auth-api'

// 箱子API响应接口
interface CaseApiResponse<T = any> {
  code: number
  message: string
  body?: {
    items: T
    total?: number
    page?: number
    limit?: number
  }
}

// 开箱返回的原始物品数据接口
interface OpenCaseRawItem {
  uid: string
  item_type: number
  id: number
  item_price: {
    update_time: string
    price: number
  }
  item_category: any
  item_quality: {
    quality_id: number
    quality_name: string
    quality_name_en: string
    quality_name_zh_hans: string
    quality_color: string
  }
  item_rarity: {
    rarity_id: number
    rarity_name: string
    rarity_name_en: string
    rarity_name_zh_hans: string
    rarity_color: string
  }
  item_exterior: {
    exterior_id: number
    exterior_name: string
    exterior_name_en: string
    exterior_name_zh_hans: string
    exterior_color: string
  }
  image: string
  name: string
  name_en: string
  name_zh_hans: string
  pid: string
}

// 处理后的开箱物品数据接口
export interface OpenCaseProcessedItem {
  uid: string
  id: number
  itemType: number
  name: string
  nameEn: string
  nameZhHans: string
  image: string
  price: number
  priceUpdateTime: string
  quality: {
    id: number
    name: string
    nameEn: string
    nameZhHans: string
    color: string
  }
  rarity: {
    id: number
    name: string
    nameEn: string
    nameZhHans: string
    color: string
  }
  exterior: {
    id: number
    name: string
    nameEn: string
    nameZhHans: string
    color: string
  }
  category: any
  pid: string
}

// 箱子详细信息接口
export interface CaseDetailItem {
  id: string
  case_key: string
  name: string
  name_en?: string
  name_zh_hans?: string
  cover: string
  price: number
  discount?: number
  tag?: string
  tag_en?: string
  tag_zh_hans?: string
  open_count?: number
  description?: string
  description_en?: string
  description_zh_hans?: string
  created_at?: string
  updated_at?: string
}

// API查询参数
interface CaseQueryParams {
  q?: string  // 查询标签: 'HOT', 'new', 'SELL'
  num?: number  // 返回数量限制
  page?: number
  limit?: number
}

class CaseApi {
  private apiBase = '/api'

  /**
   * 获取热门箱子
   * @param params 查询参数
   * @returns Promise<CaseDetailItem[]>
   */
  async getHotCases(params: CaseQueryParams = {}): Promise<{ data: CaseDetailItem[], success: boolean, message: string }> {
    try {
      const queryParams = {
        q: 'HOT',
        num: 10,
        ...params
      }

      // console.log('[Case API] 调用热门箱子API，参数:', queryParams)

      const response = await $fetch<CaseApiResponse<CaseDetailItem[]>>(`${this.apiBase}/box/tag/`, {
        params: queryParams
      })

      console.log('[Case API] 热门箱子API响应:', { code: response.code, bodyLength: Array.isArray(response.body) ? response.body.length : response.body?.items?.length })

      if (response.code === 0) {
        // 后端修正：get_case_popular_list 现在直接返回数组，不再包装在items字段中
        const cases = Array.isArray(response.body) ? response.body : (response.body?.items || [])
        // console.log('[Case API] 热门箱子解析成功，数量:', cases.length)
        return {
          data: cases,
          success: true,
          message: '获取热门箱子成功'
        }
      } else {
        //console.warn('[Case API] 热门箱子API返回错误:', response.message)
        return {
          data: [],
          success: false,
          message: response.message || '获取热门箱子失败'
        }
      }
    } catch (error: any) {
      // console.error('[Case API] 获取热门箱子失败:', error)
      return {
        data: [],
        success: false,
        message: error.message || '网络请求失败'
      }
    }
  }

  /**
   * 获取新品箱子
   * @param params 查询参数
   * @returns Promise<CaseDetailItem[]>
   */
  async getNewCases(params: CaseQueryParams = {}): Promise<{ data: CaseDetailItem[], success: boolean, message: string }> {
    try {
      const queryParams = {
        q: 'new',
        num: 10,
        ...params
      }

      const response = await $fetch<CaseApiResponse<CaseDetailItem[]>>(`${this.apiBase}/box/tag/`, {
        params: queryParams
      })

      if (response.code === 0) {
        // 后端修正：get_case_popular_list 现在直接返回数组，不再包装在items字段中
        const cases = Array.isArray(response.body) ? response.body : (response.body?.items || [])
        return {
          data: cases,
          success: true,
          message: '获取新品箱子成功'
        }
      } else {
        return {
          data: [],
          success: false,
          message: response.message || '获取新品箱子失败'
        }
      }
    } catch (error: any) {
      console.error('[Case API] 获取新品箱子失败:', error)
      return {
        data: [],
        success: false,
        message: error.message || '网络请求失败'
      }
    }
  }

  /**
   * 获取折扣箱子
   * @param params 查询参数
   * @returns Promise<CaseDetailItem[]>
   */
  async getDiscountCases(params: CaseQueryParams = {}): Promise<{ data: CaseDetailItem[], success: boolean, message: string }> {
    try {
      const queryParams = {
        q: 'SELL',
        num: 10,
        ...params
      }

      const response = await $fetch<CaseApiResponse<CaseDetailItem[]>>(`${this.apiBase}/box/tag/`, {
        params: queryParams
      })

      if (response.code === 0) {
        // 后端修正：get_case_popular_list 现在直接返回数组，不再包装在items字段中
        const cases = Array.isArray(response.body) ? response.body : (response.body?.items || [])
        return {
          data: cases,
          success: true,
          message: '获取折扣箱子成功'
        }
      } else {
        return {
          data: [],
          success: false,
          message: response.message || '获取折扣箱子失败'
        }
      }
    } catch (error: any) {
      console.error('[Case API] 获取折扣箱子失败:', error)
      return {
        data: [],
        success: false,
        message: error.message || '网络请求失败'
      }
    }
  }

  /**
   * 获取所有箱子
   * @param params 查询参数
   * @returns Promise<CaseDetailItem[]>
   */
  async getAllCases(params: CaseQueryParams = {}): Promise<{ data: CaseDetailItem[], success: boolean, message: string }> {
    try {
      const response = await $fetch<CaseApiResponse<CaseDetailItem[]>>(`${this.apiBase}/box/search/`, {
        params
      })

      if (response.code === 0) {
        // 后端修正：search_case 现在直接返回数组，不再包装在items字段中
        const cases = Array.isArray(response.body) ? response.body : (response.body?.items || [])
        return {
          data: cases,
          success: true,
          message: '获取箱子列表成功'
        }
      } else {
        return {
          data: [],
          success: false,
          message: response.message || '获取箱子列表失败'
        }
      }
    } catch (error: any) {
      console.error('[Case API] 获取箱子列表失败:', error)
      return {
        data: [],
        success: false,
        message: error.message || '网络请求失败'
      }
    }
  }

  /**
   * 获取箱子详情
   * @param caseKey 箱子key
   * @returns Promise<CaseDetailItem | null>
   */
  async getCaseDetail(caseKey: string): Promise<{ data: CaseDetailItem | null, success: boolean, message: string }> {
    try {
      // 后端修正：get_case_detail 现在直接返回箱子详情对象，不再包装在 items 字段中
      const response = await $fetch<{code: number, message: string, body?: CaseDetailItem}>(`${this.apiBase}/box/detail/`, {
        params: { key: caseKey }
      })

      if (response.code === 0) {
        // 后端修正后，响应格式从 {code: 0, body: {items: {}}} 改为 {code: 0, body: {}}
        const caseDetail = response.body || null
        return {
          data: caseDetail,
          success: true,
          message: '获取箱子详情成功'
        }
      } else {
        return {
          data: null,
          success: false,
          message: response.message || '获取箱子详情失败'
        }
      }
    } catch (error: any) {
      console.error('[Case API] 获取箱子详情失败:', error)
      return {
        data: null,
        success: false,
        message: error.message || '网络请求失败'
      }
    }
  }

  /**
   * 获取箱子皮肤
   * @param caseKey 箱子key
   * @returns Promise<皮肤数据[]>
   */
  async getCaseSkins(caseKey: string): Promise<CaseApiResponse<any[]>> {
    try {
      // console.log('[Case API] 获取箱子皮肤:', caseKey)
      
      const requestUrl = `/api/box/skins/?key=${encodeURIComponent(caseKey)}`
      
      const response = await fetch(requestUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
        credentials: 'include'
      })

      if (!response.ok) {
        throw new Error(`HTTP错误: ${response.status}`)
      }

      const result = await response.json()

      if (result.code === 0) {
        return {
          code: 0,
          message: result.message || '获取成功',
          body: {
            items: result.body?.items || result.body || []
          }
        }
      } else {
        return {
          code: result.code || -1,
          message: result.message || '获取箱子皮肤失败'
        }
      }
    } catch (error: any) {
      console.error('[Case API] 获取箱子皮肤失败:', error)
      return {
        code: -1,
        message: error.message || '网络请求失败'
      }
    }
  }

  /**
   * 获取箱子开箱记录
   * @param caseKey 箱子key
   * @returns Promise<开箱记录数据[]>
   */
  async getCaseRecords(caseKey: string): Promise<CaseApiResponse<any[]>> {
    try {
      console.log('[Case API] 获取箱子开箱记录:', caseKey)
      
      const requestUrl = `/api/box/records/?key=${encodeURIComponent(caseKey)}`
      
      const response = await fetch(requestUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
        credentials: 'include'
      })

      if (!response.ok) {
        throw new Error(`HTTP错误: ${response.status}`)
      }

      const result = await response.json()

      if (result.code === 0) {
        return {
          code: 0,
          message: result.message || '获取成功',
          body: {
            items: result.body?.items || result.body || []
          }
        }
      } else {
        return {
          code: result.code || -1,
          message: result.message || '获取开箱记录失败'
        }
      }
    } catch (error: any) {
      console.error('[Case API] 获取开箱记录失败:', error)
      return {
        code: -1,
        message: error.message || '网络请求失败'
      }
    }
  }

  /**
   * 开箱API - 使用XMLHttpRequest和认证信息
   * @param caseKey 箱子key
   * @param count 开箱数量，默认为1
   * @returns Promise<开箱结果>
   */
  async openCase(caseKey: string, count: number = 1): Promise<{ data: OpenCaseProcessedItem[] | null, success: boolean, message: string }> {
    return new Promise(async (resolve, reject) => {
      try {
        // 参数验证
        if (!caseKey) {
          resolve({
            data: null,
            success: false,
            message: '箱子标识不能为空'
          })
          return
        }
        
        if (count <= 0 || count > 10) {
          resolve({
            data: null,
            success: false,
            message: '开箱数量必须在1-10之间'
          })
          return
        }

        console.log('[Case API] 开箱请求:', { caseKey, count })

        // 获取CSRF token
        const csrfToken = await authApi.getCsrfToken()
        console.log('[Case API] 使用CSRF令牌:', csrfToken ? '已设置' : '未设置')

        // 获取用户token
        const userToken = process.client ? 
          (localStorage.getItem('token') || sessionStorage.getItem('token')) : null
        console.log('[Case API] 使用用户Token:', userToken ? '已设置' : '未设置')

        const xhr = new XMLHttpRequest()
        xhr.open('POST', `${this.apiBase}/box/open/`, true)
        xhr.setRequestHeader('Content-Type', 'application/json')
        xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest')
        xhr.setRequestHeader('Accept', 'application/json')
        xhr.withCredentials = true

        // 设置CSRF头部
        if (csrfToken) {
          xhr.setRequestHeader('X-CSRFToken', csrfToken)
          xhr.setRequestHeader('X-CSRF-TOKEN', csrfToken)
          xhr.setRequestHeader('CSRF-Token', csrfToken)
        }

        // 设置用户认证头部
        if (userToken) {
          xhr.setRequestHeader('Authorization', `Bearer ${userToken}`)
          xhr.setRequestHeader('X-TOKEN', userToken)
        }

        xhr.onreadystatechange = function() {
          if (xhr.readyState === 4) {
            console.log('[Case API] 开箱响应状态码:', xhr.status)
            console.log('[Case API] 开箱响应头:', xhr.getAllResponseHeaders())

            if (xhr.status === 200) {
              try {
                const response = JSON.parse(xhr.responseText)
                console.log('[Case API] 开箱响应数据:', response)

                if (response && response.code === 0) {
                  // 后端修正：开箱API现在直接返回物品数组，不再包装在items字段中
                  const items = Array.isArray(response.body) ? response.body : (response.body?.items || [])
                  console.log('[Case API] 解析到的物品数量:', items.length)
                  
                  // 转换数据格式以适配前端组件
                  const processedItems = items.map((item: any) => ({
                    uid: item.uid,
                    id: item.id,
                    itemType: item.item_type,
                    name: item.name || item.name_zh_hans || item.name_en,
                    nameEn: item.name_en,
                    nameZhHans: item.name_zh_hans,
                    image: item.image,
                    price: item.item_price?.price || 0,
                    priceUpdateTime: item.item_price?.update_time,
                    quality: {
                      id: item.item_quality?.quality_id,
                      name: item.item_quality?.quality_name || item.item_quality?.quality_name_zh_hans,
                      nameEn: item.item_quality?.quality_name_en,
                      nameZhHans: item.item_quality?.quality_name_zh_hans,
                      color: item.item_quality?.quality_color
                    },
                    rarity: {
                      id: item.item_rarity?.rarity_id,
                      name: item.item_rarity?.rarity_name || item.item_rarity?.rarity_name_zh_hans,
                      nameEn: item.item_rarity?.rarity_name_en,
                      nameZhHans: item.item_rarity?.rarity_name_zh_hans,
                      color: item.item_rarity?.rarity_color
                    },
                    exterior: {
                      id: item.item_exterior?.exterior_id,
                      name: item.item_exterior?.exterior_name || item.item_exterior?.exterior_name_zh_hans,
                      nameEn: item.item_exterior?.exterior_name_en,
                      nameZhHans: item.item_exterior?.exterior_name_zh_hans,
                      color: item.item_exterior?.exterior_color
                    },
                    category: item.item_category,
                    pid: item.pid
                  }))

                  console.log('[Case API] 处理后的物品数据:', processedItems)

                  resolve({
                    data: processedItems,
                    success: true,
                    message: response.message || '开箱成功'
                  })
                } else {
                  resolve({
                    data: null,
                    success: false,
                    message: response.message || '开箱失败'
                  })
                }
              } catch (e) {
                console.error('[Case API] 解析开箱响应失败:', e)
                resolve({
                  data: null,
                  success: false,
                  message: '解析响应失败'
                })
              }
            } else if (xhr.status === 403) {
              console.error('[Case API] 403 Forbidden - 认证失败')
              resolve({
                data: null,
                success: false,
                message: '认证失败，请重新登录'
              })
            } else if (xhr.status === 401) {
              console.error('[Case API] 401 Unauthorized - 需要登录')
              resolve({
                data: null,
                success: false,
                message: '请先登录后再开箱'
              })
            } else {
              console.error('[Case API] 开箱请求失败, 状态码:', xhr.status)
              resolve({
                data: null,
                success: false,
                message: `请求失败 (${xhr.status})`
              })
            }
          }
        }

        xhr.onerror = function() {
          console.error('[Case API] 开箱请求网络错误')
          resolve({
            data: null,
            success: false,
            message: '网络连接失败'
          })
        }

        // 准备请求数据 - 使用JSON格式而不是表单编码
        const requestData: any = {
          case_key: caseKey,
          count: count  // 确保count是整数而不是字符串
        }

        // CSRF token也加入JSON数据
        if (csrfToken) {
          requestData.csrfmiddlewaretoken = csrfToken
        }

        console.log('[Case API] 发送开箱请求参数:', {
          case_key: caseKey,
          count,
          hasCsrf: !!csrfToken,
          hasToken: !!userToken,
          requestType: 'JSON'
        })

        xhr.send(JSON.stringify(requestData))

      } catch (error: any) {
        console.error('[Case API] 开箱准备失败:', error)
        resolve({
          data: null,
          success: false,
          message: error.message || '请求准备失败'
        })
      }
    })
  }
}

// 导出单例实例
export const caseApi = new CaseApi()

// 保持向后兼容性
export const CaseApiService = CaseApi
export { CaseApi as default }

export type { CaseQueryParams, CaseApiResponse, OpenCaseRawItem }