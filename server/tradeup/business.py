import hashlib
import logging
import json
import numpy
import time

from django.db import transaction
from django.core.paginator import Paginator
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from django_redis import get_redis_connection

from steambase.enums import RespCode, PackageState, WinResult, PackageSourceType, AppType, TradeupBetItemType
from steambase.utils import ParamException, secret_generator, current_week, current_year, is_connection_usable
from sitecfg.interfaces import get_tradeup_bet_percentage_max, get_tradeup_bet_rate
from sitecfg.interfaces import get_tradeup_bet_amount_min, get_tradeup_bet_amount_max, get_tradeup_targets_max_limit
from package.models import PackageItem, ItemInfo, ItemWhitelist
from package.interfaces import get_item_price_by_name
from package.service.item import get_item_price, get_item_price_by_package
from package.serializers import PackageItemSerializer
from tradeup.models import TradeupGame, TradeupBetItem, TradeupTargetItem, TradeupInventory, TradeupWinDayRank, TradeupBot
from tradeup.models import TradeupWinWeekRank, TradeupPumpDay, TradeupPumpMonth, TradeupStatisticsDay, TradeupStatisticsMonth
from tradeup.models import TradeupCoinsBetItem
from tradeup.serializers import TradeupGameSerializer, TradeupInventorySerializer, TradeupWinDayRankSerializer
from tradeup.serializers import TradeupWinWeekRankSerializer


_logger = logging.getLogger(__name__)
_ws_channel_key = 'ws_channel'


def ws_send_tradeup_game(data, action):
    if data:
        r = get_redis_connection('default')
        msg = ['tradeup', action, data]
        r.publish(_ws_channel_key, json.dumps(msg))


def create_new_game_hash():
    percentage = numpy.random.uniform(0, 100)
    secret = secret_generator(size=10)
    data = '{0:.13f}:{1}'.format(percentage, secret)
    md5_data = hashlib.md5(data.encode('utf-8'))
    hash_key = md5_data.hexdigest()
    ret = {
        'percentage': percentage,
        'secret': secret,
        'hash': hash_key
    }
    return ret


def match_tradeup_inventory(user, bet_items, bet_amount, times):
    if len(bet_items) <= 0 and (bet_amount < get_tradeup_bet_amount_min() or (
        bet_amount > user.asset.balance) or bet_amount > get_tradeup_bet_amount_max()):
        return RespCode.InvalidParams.value, _('Invalid bet items')

    try:
        with transaction.atomic():
            total_bet_amount = 0
            if len(bet_items) > 0:
                packages = PackageItem.update_state(bet_items, PackageState.Available.value, PackageState.Gaming.value, user)
                packages_bet_amount = sum([get_item_price_by_package(p) for p in packages])
                total_bet_amount += packages_bet_amount
            total_bet_amount += bet_amount
            target_amount = total_bet_amount * times
            inventory = TradeupInventory.objects.filter(
                price__gte=target_amount, enable=True).exclude(
                unlimited=False, count__lte=0
            ).order_by('price').first()
            if len(bet_items) > 0:
                PackageItem.update_state(bet_items, PackageState.Gaming.value, PackageState.Available.value, user)
            inv_data = TradeupInventorySerializer(inventory).data
        return RespCode.Succeed.value, inv_data
    except ParamException as e:
        return RespCode.InvalidParams.value, str(e)


def match_coins_tradeup_inventory(user, bet_amount, appid, times):
    if not bet_amount:
        return RespCode.InvalidParams.value, _('Invalid bet amount')

    try:
        with transaction.atomic():
            target_amount = bet_amount * times
            inventory = TradeupInventory.objects.filter(
                price__gte=target_amount, item_info__appid=appid, enable=True).exclude(
                unlimited=False, count__lte=0
            ).order_by('price').first()
            inv_data = TradeupInventorySerializer(inventory).data
        return RespCode.Succeed.value, inv_data
    except ParamException as e:
        return RespCode.InvalidParams.value, str(e)


def bet_tradeup_game(user, bet_items, bet_amount, target_items, upper):
    if len(bet_items) <= 0 and (bet_amount < get_tradeup_bet_amount_min() or (
        bet_amount > user.asset.balance) or bet_amount > get_tradeup_bet_amount_max()):
        return RespCode.InvalidParams.value, _('Invalid bet items')

    try:
        game_data = None
        with transaction.atomic():
            targets = TradeupInventory.objects.select_for_update().filter(
                uid__in=target_items, enable=True).exclude(
                unlimited=False, count__lte=0
            )
            if len(targets) <= 0 or len(targets) > get_tradeup_targets_max_limit():
                raise ParamException(_('Invalid target items'))
            for target in targets:
                if not target.unlimited:
                    target.count -= 1
                    target.save()
            total_bet_amount = 0
            packages = []
            if len(bet_items) > 0:
                is_bot = TradeupBot.objects.filter(user=user, enable=True).first()
                if not is_bot:
                    packages = PackageItem.update_state(bet_items, PackageState.Available.value, PackageState.Gaming.value, user)
                else:
                    packages = PackageItem.objects.filter(uid__in=bet_items)
                packages_bet_amount = sum([get_item_price_by_package(p) for p in packages])
                total_bet_amount += packages_bet_amount
            if bet_amount > 0:
                user.update_balance(-bet_amount, _('Coins Tradeup game bet'))
                total_bet_amount += bet_amount
            target_amount = round(sum([get_item_price_by_name(i.item_info.market_hash_name) for i in targets]), 2)
            bet_percentage = total_bet_amount / target_amount * 100
            bet_percentage *= get_tradeup_bet_rate()
            if bet_percentage < 0.01:
                raise ParamException(_('Invalid bet percentage'))
            elif bet_percentage > get_tradeup_bet_percentage_max():
                bet_percentage = get_tradeup_bet_percentage_max()
            data = create_new_game_hash()
            game = TradeupGame.objects.select_for_update().create(user=user,
                                                                    bet_percentage=bet_percentage,
                                                                    upper=upper,
                                                                    bet_amount=total_bet_amount,
                                                                    target_amount=target_amount,
                                                                    appid=AppType.CSGO.value,
                                                                    **data)
            _logger.info('User {} create tradeup game {}'.format(user, game.uid))
            if len(bet_items) > 0:
                for package in packages:
                    price = get_item_price_by_package(package)
                    item_data = {
                        'game': game,
                        'package': package,
                        'price': price,
                        'type': TradeupBetItemType.Item.value
                    }
                    TradeupBetItem.objects.create(**item_data)
            if bet_amount > 0:
                item_data = {
                    'game': game,
                    'price': bet_amount,
                    'type': TradeupBetItemType.Amount.value
                }
                TradeupBetItem.objects.create(**item_data)

            for target in targets:
                TradeupTargetItem.objects.create(game=game, item_info=target.item_info, price=target.price)

            win_items = calc_tradeup_game_result(game, user)
            game_data = TradeupGameSerializer(game).data
            win_items_data = []
            for item in win_items:
                fields = ('uid', 'market_name', 'market_hash_name', 'icon_url', 'price', 'part', 'amount', 'state',
                      'source')
                _data = PackageItemSerializer(item, fields=fields).data
                win_items_data.append(_data)
            tradeup_game_data = game_data
            tradeup_game_data['win_items'] = win_items_data

        if len(win_items)> 0:
            ws_send_tradeup_game(game_data, 'new')
        return RespCode.Succeed.value, tradeup_game_data
    except ParamException as e:
        return RespCode.InvalidParams.value, str(e)


def bet_coins_tradeup_game(user, bet_amount, target_item, upper):
    if not bet_amount or bet_amount < get_tradeup_bet_amount_min() or (
        bet_amount > user.asset.balance) or bet_amount > get_tradeup_bet_amount_max():
        return RespCode.InvalidParams.value, _('Invalid bet amount')

    try:
        game_data = None
        with transaction.atomic():
            target = TradeupInventory.objects.select_for_update().filter(
                uid=target_item, enable=True).exclude(
                unlimited=False, count__lte=0
            ).first()
            if not target:
                raise ParamException(_('Invalid target items'))
            if not target.unlimited:
                target.count -= 1
                target.save()
            appid = target.item_info.appid
            if appid not in [AppType.CSGO.value, AppType.Dota2.value]:
                raise ParamException(_('Invalid target appid'))

            # is_bot = TradeupBot.objects.filter(user=user, enable=True).first()
            # if not is_bot:
            #     packages = PackageItem.update_state(bet_items, PackageState.Available.value, PackageState.Gaming.value, user)
            # else:
            #     packages = PackageItem.objects.filter(uid=bet_items)
            user.update_balance(-bet_amount, _('Coins Tradeup game bet'))
            target_amount = target.price
            bet_percentage = bet_amount / target_amount * 100
            # if 'ceebgo.com' in (user.steam.personaname).lower():
            #     bet_percentage *= get_tradeup_bet_ceebgo_rate()
            # else:
            bet_percentage *= get_tradeup_bet_rate()
            if bet_percentage < 0.01:
                raise ParamException(_('Invalid bet percentage'))
            elif bet_percentage > get_tradeup_bet_percentage_max():
                bet_percentage = get_tradeup_bet_percentage_max()
            data = create_new_game_hash()
            game = TradeupGame.objects.select_for_update().create(user=user,
                                                                    bet_percentage=bet_percentage,
                                                                    upper=upper,
                                                                    bet_amount=bet_amount,
                                                                    target_amount=target_amount,
                                                                    appid=appid,
                                                                    **data)
            _logger.info('User {} create coins tradeup game {}'.format(user, game.uid))

            item_data = {
                'game': game,
                'amount': bet_amount,
                'appid': appid
            }
            TradeupCoinsBetItem.objects.create(**item_data)
            TradeupTargetItem.objects.create(game=game, item_info=target.item_info, price=target.price)

            win = calc_coins_tradeup_game_result(game, user)
            game_data = TradeupGameSerializer(game).data

        if win:
            ws_send_tradeup_game(game_data, 'new')
        return RespCode.Succeed.value, game_data
    except ParamException as e:
        return RespCode.InvalidParams.value, str(e)


def calc_tradeup_game_result(game, user):
    bet_items = [i.package.uid for i in game.bet_items.filter(type=TradeupBetItemType.Item.value)]
    target_items = game.target_items.all()
    is_bot = TradeupBot.objects.filter(user=game.user, enable=True).first()
    if not is_bot:
        PackageItem.objects.filter(user=game.user, uid__in=bet_items).update(user=None)

    if game.upper:
        win = game.percentage > 100 - game.bet_percentage
    else:
        win = game.percentage < game.bet_percentage

    win_items = []
    if win:
        game.win_result = WinResult.Win.value
        game.win_amount = game.target_amount - game.bet_amount
        if not is_bot:
            for target_item in target_items:
                item = PackageItem.objects.create(user=game.user, item_info=target_item.item_info,
                                       state=PackageState.Available.value,
                                       source=PackageSourceType.Tradeup.value, amount=target_item.item_info.item_price.price)
                win_items.append(item)
                _logger.info('user: {} get item:{} from market'.format(game.user, target_item.item_info))
        update_tradeup_rank(game.user, game.win_amount)
    else:
        game.win_result = WinResult.Lose.value

    game.save()
    if not is_bot:
        update_tradeup_statistics(game)
    return win_items


def calc_coins_tradeup_game_result(game, user):
    bet_amount = game.bet_amount
    target_item = game.target_items.first()
    is_bot = TradeupBot.objects.filter(user=game.user, enable=True).first()
    # if not is_bot:
        # PackageItem.objects.filter(user=game.user, uid__in=bet_items).update(user=None)

    if game.upper:
        win = game.percentage > 100 - game.bet_percentage
    else:
        win = game.percentage < game.bet_percentage

    if win:
        game.win_result = WinResult.Win.value
        game.win_amount = round(game.target_amount - game.bet_amount, 2)
        if not is_bot:
            user.update_balance(game.target_amount, _('Coins Tradeup game win'))
            # if game.win_amount > 0:
                # user.update_available_balance(game.win_amount, 'Coins Tradeup game win')
            _logger.info('user: {} get coins:{} from coins tradeup'.format(game.user, target_item.item_info))
        update_tradeup_rank(game.user, game.win_amount)
    else:
        game.win_result = WinResult.Lose.value

    game.save()
    if not is_bot:
        update_tradeup_statistics(game)
    return win


def check_tradeup_bot():
    while True:
        try:
            if not is_connection_usable():
                connection.close()

            bots = TradeupBot.objects.filter(enable=True)
            for bot in bots:
                last_bet = TradeupGame.objects.filter(user=bot.user).order_by('-create_time').first()
                if last_bet:
                    last_bet_past = (timezone.now() - last_bet.create_time).seconds
                    if last_bet_past < bot.bet_idle_min:
                        continue
                    elif last_bet_past > bot.bet_idle_max:
                        bet = True
                    else:
                        bet_probability = 1 / (bot.bet_idle_max - last_bet_past + 1)
                        percentage = numpy.random.uniform(0, 1)
                        bet = bet_probability > percentage
                else:
                    bet = True
                if bet:
                    #bet_items = TradeupBotInventory.objects.all()
                    bet_items = PackageItem.objects.filter(item_info__item_price__price__gt=bot.price_min, 
                                                           item_info__item_price__price__lt=bot.price_max)
                    if len(bet_items) == 0:
                        break
                    bet_item = numpy.random.choice(bet_items)
                    bet_amount = get_item_price_by_name(bet_item.item_info.market_hash_name, appid=bet_item.item_info.appid)

                    chance_min = bot.chance_min
                    if chance_min != 0:
                        target_amount_min = bet_amount / chance_min * 100
                    else:
                        target_amount_min = bet_amount * 100
                    chance_max = bot.chance_max
                    target_amount_max = bet_amount / chance_max * 100
                    target_items = TradeupInventory.objects.filter(price__gte=target_amount_max, price__lte=target_amount_min)
                    if len(target_items) == 0:
                        continue
                    target_item = numpy.random.choice(target_items)
                    target_amount = target_item.price
                    bet_percentage = bet_amount / target_amount * 100
                    upper = numpy.random.choice([True, False])
                    bet_tradeup_game(bot.user, bet_item.uid, target_item.uid, upper)
                    break
        except Exception as e:
            _logger.exception(e)
        finally:
            time.sleep(1)


def update_tradeup_statistics(game):
    # pump = game.pump_amount
    # if pump > 0:
    #     with transaction.atomic():
    #         TradeupPumpDay.update_amount(pump)
    #         TradeupPumpMonth.update_amount(pump)

    if game.win_result == WinResult.Lose.value:
        income = game.bet_amount
    if game.win_result == WinResult.Win.value:
        income = game.bet_amount - game.target_amount

    if not game.user.is_superuser:
        TradeupStatisticsDay.update_journal(game.bet_amount)
        TradeupStatisticsMonth.update_journal(game.bet_amount)

        if game.win_result == WinResult.Lose.value:
            TradeupStatisticsDay.update_lose_amount(game.bet_amount)
            TradeupStatisticsMonth.update_lose_amount(game.bet_amount)
        if game.win_result == WinResult.Win.value:
            TradeupStatisticsDay.update_win_amount(game.target_amount)
            TradeupStatisticsMonth.update_win_amount(game.target_amount)
        if income:
            TradeupStatisticsDay.update_amount(income)
            TradeupStatisticsMonth.update_amount(income)
    
    if game.user.is_superuser:
        TradeupStatisticsDay.update_test_amount(income)
        TradeupStatisticsMonth.update_test_amount(income)


def update_tradeup_rank(user, amount):
    if amount > 0:
        with transaction.atomic():
            TradeupWinDayRank.update_amount(user, amount)
            TradeupWinWeekRank.update_amount(user, amount)


def get_tradeup_game_inventory(query, fields, page, page_size, name, rarity, max_price, min_price, order):
    queryset = TradeupInventory.objects.filter(
        enable=True, **query).exclude(
        unlimited=False, count__lte=0
    ).order_by(order)

    for item in queryset:
        # item_info = ItemInfo.objects.get(id=item.item_info_id)
        # item.price = get_item_price(item_info.market_hash_name)
        if (timezone.now() - item.update_time).seconds > 1800:
        # if True:
            item.price = get_item_price(item.item_info.market_hash_name)
            item.save()

    if max_price:
        queryset = queryset.filter(item_info__market_name__contains=name,
                                   item_info__rarity__contains=rarity, price__range=(min_price, max_price)).order_by(order)
    else:
        queryset = queryset.filter(item_info__market_name__contains=name,
                                   item_info__rarity__contains=rarity, price__gt=min_price).order_by(order)
    paginator = Paginator(queryset, page_size)
    items = paginator.page(page)
    items_data = TradeupInventorySerializer(items, many=True, fields=fields).data
    resp = {
        'items': items_data,
        'total': paginator.count,
        'page': page,
        'limit': page_size
    }
    return RespCode.Succeed.value, resp


def get_tradeup_bet(user, query, fields, page, page_size):
    queryset = TradeupGame.objects.filter(user=user, **query).order_by('-create_time')
    paginator = Paginator(queryset, page_size)
    items = paginator.page(page)
    items_data = TradeupGameSerializer(items, many=True, fields=fields).data
    resp = {
        'items': items_data,
        'total': paginator.count,
        'page': page,
        'limit': page_size
    }
    return RespCode.Succeed.value, resp


def get_tradeup_game_history(query, fields, page, page_size):
    queryset = TradeupGame.objects.filter(**query).order_by('-create_time')
    paginator = Paginator(queryset, page_size)
    items = paginator.page(page)
    items_data = TradeupGameSerializer(items, many=True, fields=fields).data
    resp = {
        'items': items_data,
        'total': paginator.count,
        'page': page,
        'limit': page_size
    }
    return RespCode.Succeed.value, resp


def get_tradeup_day_rank(query, fields, page, page_size, count):
    today = timezone.localdate()
    queryset = TradeupWinDayRank.objects.filter(date=today, **query).order_by('-amount')[:count]
    paginator = Paginator(queryset, page_size)
    items = paginator.page(page)
    items_data = TradeupWinDayRankSerializer(items, many=True, fields=fields).data
    resp = {
        'items': items_data,
        'total': paginator.count,
        'page': page,
        'limit': page_size
    }
    return RespCode.Succeed.value, resp


def get_tradeup_week_rank(query, fields, page, page_size, count):
    year = current_year()
    week = current_week()
    queryset = TradeupWinWeekRank.objects.filter(year=year, week=week, **query).order_by('-amount')[:count]
    paginator = Paginator(queryset, page_size)
    items = paginator.page(page)
    items_data = TradeupWinWeekRankSerializer(items, many=True, fields=fields).data
    resp = {
        'items': items_data,
        'total': paginator.count,
        'page': page,
        'limit': page_size
    }
    return RespCode.Succeed.value, resp


def bet_tradeup_percentage(user, bet_items, bet_amount, target_items, upper):
    if len(bet_items) <= 0 and (bet_amount < get_tradeup_bet_amount_min() or (
        bet_amount > user.asset.balance) or bet_amount > get_tradeup_bet_amount_max()):
        return RespCode.InvalidParams.value, _('Invalid bet items')

    try:
        targets = TradeupInventory.objects.filter(
            uid__in=target_items, enable=True).exclude(
            unlimited=False, count__lte=0
        )
        if len(targets) <= 0 or len(targets) > get_tradeup_targets_max_limit():
            raise ParamException(_('Invalid target items'))
        for target in targets:
            if not target.unlimited:
                target.count -= 1
                target.save()

        total_bet_amount = 0
        if len(bet_items) > 0:
            packages = PackageItem.objects.filter(uid__in=bet_items)
            packages_bet_amount = sum([get_item_price_by_package(p) for p in packages])
            total_bet_amount += packages_bet_amount
        if bet_amount > 0:
            total_bet_amount += bet_amount
        target_amount = sum([get_item_price_by_name(i.item_info.market_hash_name) for i in targets])
        bet_percentage = total_bet_amount / target_amount * 100
        bet_percentage *= get_tradeup_bet_rate()
        if bet_percentage < 0.01:
            raise ParamException(_('Invalid bet percentage'))
        elif bet_percentage > get_tradeup_bet_percentage_max():
            bet_percentage = get_tradeup_bet_percentage_max()
        if upper:
            bet_percentage = 100 - bet_percentage
        return RespCode.Succeed.value, {'percentage': bet_percentage}
    except ParamException as e:
        return RespCode.InvalidParams.value, str(e)


def bet_coins_tradeup_percentage(user, bet_amount, target_items, upper):
    if not bet_amount:
        return RespCode.InvalidParams.value, _('Invalid bet items')

    try:
        targets = TradeupInventory.objects.filter(
            uid__in=target_items, enable=True).exclude(
            unlimited=False, count__lte=0
        ).first()
        if not target:
            raise ParamException(_('Invalid target items'))

        target_amount = target.price
        bet_percentage = bet_amount / target_amount * 100
        # if 'ceebgo.com' in (user.steam.personaname).lower():
        #     bet_percentage *= get_tradeup_bet_ceebgo_rate()
        # else:
        bet_percentage *= get_tradeup_bet_rate()
        if bet_percentage < 0.01:
            raise ParamException(_('Invalid bet percentage'))
        elif bet_percentage > get_tradeup_bet_percentage_max():
            bet_percentage = get_tradeup_bet_percentage_max()
        if upper:
            bet_percentage = 100 - bet_percentage
        return RespCode.Succeed.value, {'percentage': bet_percentage}
    except ParamException as e:
        return RespCode.InvalidParams.value, str(e)


def verify_tradeup(percentage, bet_percentage, upper, secret):
    percentage = float(percentage)
    bet_percentage = float(bet_percentage)
    data = '{0:.13f}:{1}'.format(percentage, secret)
    md5_data = hashlib.md5(data.encode('utf-8'))
    hash_key = md5_data.hexdigest()
    if upper:
        win = percentage > 100 - bet_percentage
    else:
        win = percentage < bet_percentage
    if win:
        win_result = WinResult.Win.value
    else:
        win_result = WinResult.Lose.value
    resp = {
        'hash_key': hash_key,
        'win_result': win_result
    }
    return RespCode.Succeed.value, resp
    