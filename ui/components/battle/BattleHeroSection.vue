<template>
  <div class="hero-section mb-8">
    <div class="relative bg-gradient-to-r from-gray-800/40 via-gray-700/40 to-gray-800/40 backdrop-blur-xl border border-gray-600/30 rounded-2xl p-8 overflow-hidden">
      <!-- 装饰性背景 -->
      <div class="absolute inset-0 bg-gradient-to-r from-blue-500/5 via-purple-500/5 to-blue-500/5"></div>
      <div class="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-blue-400/50 to-transparent"></div>
      
      <div class="relative z-10">
        <!-- 标题和创建按钮行 -->
        <div class="flex items-center justify-between mb-6">
          <div class="flex-1">
            <h1 class="text-4xl md:text-5xl font-bold mb-2 bg-gradient-to-r from-blue-400 via-white to-blue-400 bg-clip-text text-transparent">
              {{ t('battle.title') }}
            </h1>
            <p class="text-xl text-white/70 max-w-2xl">
              {{ t('battle.subtitle') }}
            </p>
          </div>
          
          <!-- 创建对战按钮 -->
          <button
            @click="$emit('create-battle')"
            class="create-battle-btn group relative px-8 py-4 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 rounded-xl font-semibold text-white transition-all duration-300 transform hover:scale-105 hover:shadow-2xl overflow-hidden ml-8"
          >
            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
            <div class="relative flex items-center gap-3">
              <i class="fas fa-plus text-lg"></i>
              <span class="text-lg">{{ t('battle.create_battle') }}</span>
            </div>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const { t } = useI18n()

defineEmits<{
  'create-battle': []
}>()
</script> 