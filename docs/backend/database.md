# 数据库设计文档

## 概述

本项目使用MySQL作为主数据库，采用Django ORM进行数据建模。数据库设计遵循第三范式，支持多语言国际化。

## 数据库配置

### 连接配置
```python
# settings.py
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'csgoskins',
        'HOST': '127.0.0.1',
        'PORT': 3306,
        'USER': 'csgoskins',
        'PASSWORD': 'your_password',
        'OPTIONS': {
            'charset': 'utf8mb4',
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
        }
    }
}
```

### 字符集配置
```sql
-- 数据库字符集
CREATE DATABASE csgoskins 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;
```

## 核心数据模型

### 1. 用户认证模块 (authentication)

#### AuthUser - 用户基础信息
```python
class AuthUser(AbstractUser):
    # 基础信息
    phone = models.CharField(max_length=20, blank=True, null=True)
    avatar = models.URLField(blank=True, null=True)
    real_name = models.CharField(max_length=50, blank=True, null=True)
    
    # Steam相关
    steam_id = models.CharField(max_length=20, unique=True, null=True)
    steam_trade_url = models.URLField(blank=True, null=True)
    
    # 余额和等级
    balance = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    frozen_balance = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    level = models.IntegerField(default=1)
    experience = models.IntegerField(default=0)
    
    # 状态字段
    is_verified = models.BooleanField(default=False)
    is_banned = models.BooleanField(default=False)
    ban_reason = models.TextField(blank=True, null=True)
    last_login_ip = models.GenericIPAddressField(null=True, blank=True)
    
    # 时间字段
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
```

#### UserBalanceRecord - 余额变动记录
```python
class UserBalanceRecord(models.Model):
    user = models.ForeignKey(AuthUser, on_delete=models.CASCADE)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    balance_before = models.DecimalField(max_digits=10, decimal_places=2)
    balance_after = models.DecimalField(max_digits=10, decimal_places=2)
    type = models.CharField(max_length=20, choices=BALANCE_TYPE_CHOICES)
    description = models.CharField(max_length=200)
    related_id = models.IntegerField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
```

### 2. 物品系统 (package)

#### ItemInfo - 物品基础信息
```python
class ItemInfo(models.Model):
    # 基础信息
    name = models.CharField(max_length=200)
    name_en = models.CharField(max_length=200, blank=True)
    market_hash_name = models.CharField(max_length=200, unique=True)
    
    # 分类信息
    category = models.CharField(max_length=50)
    subcategory = models.CharField(max_length=50, blank=True)
    quality = models.CharField(max_length=50, blank=True)  # 品质
    rarity = models.CharField(max_length=50, blank=True)   # 稀有度
    exterior = models.CharField(max_length=50, blank=True) # 外观
    
    # 价格信息
    price = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    market_price = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    
    # 图片和描述
    image_url = models.URLField(blank=True)
    description = models.TextField(blank=True)
    
    # 状态
    is_active = models.BooleanField(default=True)
    is_tradable = models.BooleanField(default=True)
    
    # 统计信息
    total_dropped = models.IntegerField(default=0)
    total_traded = models.IntegerField(default=0)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
```

#### PackageItem - 用户拥有的物品
```python
class PackageItem(models.Model):
    user = models.ForeignKey(AuthUser, on_delete=models.CASCADE)
    item_info = models.ForeignKey(ItemInfo, on_delete=models.CASCADE)
    
    # 物品状态
    state = models.CharField(max_length=20, choices=PACKAGE_STATE_CHOICES)
    source = models.CharField(max_length=20, choices=PACKAGE_SOURCE_CHOICES)
    
    # 价格信息
    acquired_price = models.DecimalField(max_digits=10, decimal_places=2)
    current_price = models.DecimalField(max_digits=10, decimal_places=2)
    
    # 时间信息
    acquired_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['user', 'state']),
            models.Index(fields=['item_info', 'state']),
        ]
```

### 3. 开箱系统 (box)

#### CaseInfo - 箱子信息
```python
class CaseInfo(models.Model):
    # 基础信息
    name = models.CharField(max_length=200)
    name_en = models.CharField(max_length=200, blank=True)
    description = models.TextField(blank=True)
    
    # 价格和分类
    price = models.DecimalField(max_digits=10, decimal_places=2)
    category = models.CharField(max_length=50)
    tags = models.ManyToManyField('CaseTag', blank=True)
    
    # 图片
    image_url = models.URLField(blank=True)
    banner_image = models.URLField(blank=True)
    
    # 状态和配置
    is_active = models.BooleanField(default=True)
    is_free = models.BooleanField(default=False)
    is_featured = models.BooleanField(default=False)
    discount_rate = models.DecimalField(max_digits=3, decimal_places=2, default=1.0)
    
    # 开箱限制
    max_open_per_user = models.IntegerField(default=0)  # 0表示无限制
    require_level = models.IntegerField(default=1)
    
    # 统计信息
    total_opened = models.IntegerField(default=0)
    total_value_dropped = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    
    # 排序权重
    sort_order = models.IntegerField(default=0)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
```

#### CaseItem - 箱子包含的物品
```python
class CaseItem(models.Model):
    case_info = models.ForeignKey(CaseInfo, on_delete=models.CASCADE)
    item_info = models.ForeignKey(ItemInfo, on_delete=models.CASCADE)
    
    # 掉落概率
    probability = models.DecimalField(max_digits=8, decimal_places=6)
    show_probability = models.DecimalField(max_digits=5, decimal_places=2)  # 显示给用户的概率
    
    # 权重配置
    weight = models.IntegerField(default=1)
    is_jackpot = models.BooleanField(default=False)  # 是否为大奖
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ('case_info', 'item_info')
        indexes = [
            models.Index(fields=['case_info', 'probability']),
        ]
```

#### CaseRecord - 开箱记录
```python
class CaseRecord(models.Model):
    user = models.ForeignKey(AuthUser, on_delete=models.CASCADE)
    case_info = models.ForeignKey(CaseInfo, on_delete=models.CASCADE)
    item_info = models.ForeignKey(ItemInfo, on_delete=models.CASCADE)
    
    # 开箱结果
    acquired_price = models.DecimalField(max_digits=10, decimal_places=2)
    case_price = models.DecimalField(max_digits=10, decimal_places=2)
    profit = models.DecimalField(max_digits=10, decimal_places=2)  # 盈亏
    
    # 记录类型
    record_type = models.CharField(max_length=20, choices=CASE_RECORD_TYPE_CHOICES)
    
    # 房间信息 (用于对战模式)
    room_id = models.IntegerField(null=True, blank=True)
    round_number = models.IntegerField(default=1)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['case_info', 'created_at']),
            models.Index(fields=['room_id', 'round_number']),
        ]
```

### 4. 对战系统 (box - 房间相关)

#### CaseRoom - 对战房间
```python
class CaseRoom(models.Model):
    # 房间基础信息
    name = models.CharField(max_length=100, blank=True)
    case_info = models.ForeignKey(CaseInfo, on_delete=models.CASCADE)
    creator = models.ForeignKey(AuthUser, on_delete=models.CASCADE)
    
    # 房间配置
    room_type = models.CharField(max_length=10, choices=ROOM_TYPE_CHOICES)
    max_players = models.IntegerField(default=2)
    rounds = models.IntegerField(default=1)
    
    # 房间状态
    state = models.CharField(max_length=20, choices=GAME_STATE_CHOICES)
    current_round = models.IntegerField(default=1)
    
    # 时间配置
    countdown_seconds = models.IntegerField(default=30)
    round_duration = models.IntegerField(default=10)
    
    # 房间设置
    is_private = models.BooleanField(default=False)
    password = models.CharField(max_length=20, blank=True)
    auto_start = models.BooleanField(default=True)
    
    # 奖励配置
    winner_gets_all = models.BooleanField(default=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    started_at = models.DateTimeField(null=True, blank=True)
    ended_at = models.DateTimeField(null=True, blank=True)
```

#### CaseRoomPlayer - 房间玩家
```python
class CaseRoomPlayer(models.Model):
    room = models.ForeignKey(CaseRoom, on_delete=models.CASCADE)
    user = models.ForeignKey(AuthUser, on_delete=models.CASCADE)
    
    # 玩家状态
    position = models.IntegerField()  # 玩家位置
    is_ready = models.BooleanField(default=False)
    is_winner = models.BooleanField(default=False)
    
    # 统计信息
    total_value = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    rounds_won = models.IntegerField(default=0)
    
    joined_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ('room', 'user')
        indexes = [
            models.Index(fields=['room', 'position']),
        ]
```

### 5. 充值系统 (charge)

#### ChargeRecord - 充值记录
```python
class ChargeRecord(models.Model):
    user = models.ForeignKey(AuthUser, on_delete=models.CASCADE)
    
    # 订单信息
    order_id = models.CharField(max_length=50, unique=True)
    trade_no = models.CharField(max_length=100, blank=True)  # 第三方交易号
    
    # 金额信息
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    actual_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    bonus_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    
    # 支付信息
    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHOD_CHOICES)
    gateway = models.CharField(max_length=50, blank=True)  # 支付网关
    
    # 状态
    state = models.CharField(max_length=20, choices=CHARGE_STATE_CHOICES)
    
    # 时间
    created_at = models.DateTimeField(auto_now_add=True)
    paid_at = models.DateTimeField(null=True, blank=True)
    
    # 备注
    remark = models.TextField(blank=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['user', 'state']),
            models.Index(fields=['order_id']),
            models.Index(fields=['created_at']),
        ]
```

### 6. 交易系统 (b2ctrade)

#### TradeRecord - 交易记录
```python
class TradeRecord(models.Model):
    user = models.ForeignKey(AuthUser, on_delete=models.CASCADE)
    
    # 交易基础信息
    trade_id = models.CharField(max_length=50, unique=True)
    trade_type = models.CharField(max_length=20, choices=TRADE_TYPE_CHOICES)
    
    # Steam交易信息
    steam_trade_offer_id = models.CharField(max_length=50, blank=True)
    steam_trade_url = models.URLField()
    
    # 状态
    state = models.CharField(max_length=20, choices=B2C_TRADE_STATE_CHOICES)
    
    # 物品信息
    total_items = models.IntegerField(default=0)
    total_value = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    
    # 时间
    created_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    # 错误信息
    error_message = models.TextField(blank=True)
    retry_count = models.IntegerField(default=0)
```

#### TradeItem - 交易物品明细
```python
class TradeItem(models.Model):
    trade_record = models.ForeignKey(TradeRecord, on_delete=models.CASCADE)
    package_item = models.ForeignKey(PackageItem, on_delete=models.CASCADE)
    
    # 物品信息快照
    item_name = models.CharField(max_length=200)
    item_price = models.DecimalField(max_digits=10, decimal_places=2)
    
    # 状态
    is_sent = models.BooleanField(default=False)
    sent_at = models.DateTimeField(null=True, blank=True)
```

### 7. 游戏模块

#### Crash游戏 (crash)
```python
class CrashGame(models.Model):
    # 游戏信息
    game_id = models.CharField(max_length=50, unique=True)
    multiplier = models.DecimalField(max_digits=8, decimal_places=2)
    hash_value = models.CharField(max_length=64)  # 游戏结果哈希
    
    # 状态
    state = models.CharField(max_length=20, choices=GAME_STATE_CHOICES)
    
    # 时间
    created_at = models.DateTimeField(auto_now_add=True)
    started_at = models.DateTimeField(null=True, blank=True)
    crashed_at = models.DateTimeField(null=True, blank=True)

class CrashBet(models.Model):
    game = models.ForeignKey(CrashGame, on_delete=models.CASCADE)
    user = models.ForeignKey(AuthUser, on_delete=models.CASCADE)
    
    # 下注信息
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    auto_cashout = models.DecimalField(max_digits=8, decimal_places=2, null=True)
    
    # 结果
    cashout_multiplier = models.DecimalField(max_digits=8, decimal_places=2, null=True)
    payout = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    
    # 状态
    is_cashed_out = models.BooleanField(default=False)
    cashed_out_at = models.DateTimeField(null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
```

#### Roll游戏 (roll)
```python
class RollRoom(models.Model):
    creator = models.ForeignKey(AuthUser, on_delete=models.CASCADE)
    
    # 房间配置
    name = models.CharField(max_length=100, blank=True)
    min_value = models.DecimalField(max_digits=10, decimal_places=2)
    max_value = models.DecimalField(max_digits=10, decimal_places=2, null=True)
    max_players = models.IntegerField(default=10)
    
    # 房间状态
    state = models.CharField(max_length=20, choices=GAME_STATE_CHOICES)
    total_value = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    
    # 获胜者
    winner = models.ForeignKey(AuthUser, on_delete=models.SET_NULL, null=True, related_name='won_rolls')
    winning_ticket = models.IntegerField(null=True, blank=True)
    
    # 时间配置
    countdown_seconds = models.IntegerField(default=60)
    
    created_at = models.DateTimeField(auto_now_add=True)
    started_at = models.DateTimeField(null=True, blank=True)
    ended_at = models.DateTimeField(null=True, blank=True)

class RollBet(models.Model):
    room = models.ForeignKey(RollRoom, on_delete=models.CASCADE)
    user = models.ForeignKey(AuthUser, on_delete=models.CASCADE)
    package_item = models.ForeignKey(PackageItem, on_delete=models.CASCADE)
    
    # 下注信息
    value = models.DecimalField(max_digits=10, decimal_places=2)
    ticket_start = models.IntegerField()
    ticket_end = models.IntegerField()
    
    created_at = models.DateTimeField(auto_now_add=True)
```

### 8. 聊天系统 (chat)

#### Message - 聊天消息
```python
class Message(models.Model):
    user = models.ForeignKey(AuthUser, on_delete=models.CASCADE)
    
    # 消息内容
    content = models.TextField()
    message_type = models.CharField(max_length=20, choices=MESSAGE_TYPE_CHOICES)
    
    # 频道信息
    channel = models.CharField(max_length=50, default='global')
    room_id = models.IntegerField(null=True, blank=True)
    
    # 状态
    is_deleted = models.BooleanField(default=False)
    is_system = models.BooleanField(default=False)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['channel', 'created_at']),
            models.Index(fields=['room_id', 'created_at']),
        ]
```

### 9. 网站配置 (sitecfg)

#### SiteConfig - 网站配置
```python
class SiteConfig(models.Model):
    key = models.CharField(max_length=100, unique=True)
    value = models.TextField()
    description = models.CharField(max_length=200, blank=True)
    category = models.CharField(max_length=50, default='general')
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
```

## 索引设计

### 重要索引
```sql
-- 用户相关索引
CREATE INDEX idx_authuser_steam_id ON authentication_authuser(steam_id);
CREATE INDEX idx_authuser_phone ON authentication_authuser(phone);
CREATE INDEX idx_balance_record_user_time ON authentication_userbalancerecord(user_id, created_at);

-- 物品相关索引
CREATE INDEX idx_package_item_user_state ON package_packageitem(user_id, state);
CREATE INDEX idx_package_item_state_time ON package_packageitem(state, acquired_at);
CREATE INDEX idx_item_info_category ON package_iteminfo(category, is_active);

-- 开箱相关索引
CREATE INDEX idx_case_record_user_time ON box_caserecord(user_id, created_at);
CREATE INDEX idx_case_record_case_time ON box_caserecord(case_info_id, created_at);
CREATE INDEX idx_case_room_state ON box_caseroom(state, created_at);

-- 交易相关索引
CREATE INDEX idx_trade_record_user_state ON b2ctrade_traderecord(user_id, state);
CREATE INDEX idx_trade_record_time ON b2ctrade_traderecord(created_at);
```

### 复合索引
```sql
-- 用户物品查询优化
CREATE INDEX idx_package_user_category_state ON package_packageitem(user_id, category, state);

-- 开箱记录查询优化
CREATE INDEX idx_case_record_comprehensive ON box_caserecord(user_id, case_info_id, created_at);

-- 交易状态查询优化
CREATE INDEX idx_trade_state_time ON b2ctrade_traderecord(state, created_at);
```

## 数据库优化

### 分区策略
```sql
-- 按时间分区开箱记录表
ALTER TABLE box_caserecord PARTITION BY RANGE (YEAR(created_at)) (
    PARTITION p2023 VALUES LESS THAN (2024),
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- 按时间分区余额记录表
ALTER TABLE authentication_userbalancerecord PARTITION BY RANGE (YEAR(created_at)) (
    PARTITION p2023 VALUES LESS THAN (2024),
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

### 性能优化配置
```sql
-- 优化InnoDB配置
SET GLOBAL innodb_buffer_pool_size = 2147483648;  -- 2GB
SET GLOBAL innodb_log_file_size = 268435456;      -- 256MB
SET GLOBAL innodb_flush_log_at_trx_commit = 2;

-- 查询缓存配置
SET GLOBAL query_cache_type = ON;
SET GLOBAL query_cache_size = 268435456;  -- 256MB

-- 连接配置
SET GLOBAL max_connections = 200;
SET GLOBAL wait_timeout = 300;
```

## 数据归档

### 归档策略
```python
# management/commands/archive_old_data.py
from django.core.management.base import BaseCommand
from datetime import datetime, timedelta

class Command(BaseCommand):
    def handle(self, *args, **options):
        # 归档3个月前的开箱记录
        cutoff_date = timezone.now() - timedelta(days=90)
        
        old_records = CaseRecord.objects.filter(
            created_at__lt=cutoff_date
        )
        
        # 移动到归档表
        for record in old_records.iterator():
            CaseRecordArchive.objects.create(
                # 复制字段
            )
        
        # 删除原记录
        old_records.delete()
```

### 定期清理
```python
# 清理临时数据
def cleanup_temp_data():
    # 清理过期的验证码
    ExpiredCode.objects.filter(
        created_at__lt=timezone.now() - timedelta(minutes=10)
    ).delete()
    
    # 清理过期的会话数据
    Session.objects.filter(
        expire_date__lt=timezone.now()
    ).delete()
    
    # 清理旧的日志记录
    LogEntry.objects.filter(
        created_at__lt=timezone.now() - timedelta(days=30)
    ).delete()
```

## 数据库监控

### 性能监控查询
```sql
-- 慢查询监控
SELECT 
    query_time,
    lock_time,
    rows_sent,
    rows_examined,
    sql_text
FROM mysql.slow_log 
WHERE start_time > NOW() - INTERVAL 1 DAY
ORDER BY query_time DESC;

-- 表大小监控
SELECT 
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.TABLES 
WHERE table_schema = 'csgoskins'
ORDER BY (data_length + index_length) DESC;

-- 索引使用情况
SELECT 
    object_schema,
    object_name,
    index_name,
    count_read,
    count_write,
    count_fetch,
    count_insert,
    count_update,
    count_delete
FROM performance_schema.table_io_waits_summary_by_index_usage
WHERE object_schema = 'csgoskins';
```

### 备份策略
```bash
#!/bin/bash
# 数据库备份脚本

DATE=$(date +"%Y%m%d_%H%M%S")
BACKUP_DIR="/backup/mysql"

# 全量备份
mysqldump --single-transaction --routines --triggers \
  -h localhost -u backup_user -p'backup_password' \
  csgoskins > $BACKUP_DIR/full_backup_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/full_backup_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "*.gz" -mtime +7 -delete
```

这份数据库设计文档详细描述了项目的数据模型、索引设计、性能优化和维护策略，为开发者提供了完整的数据库架构参考。
