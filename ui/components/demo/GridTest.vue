<template>
  <div class="grid-test-container p-6">
    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 lg:grid-cols-4 xl:grid-cols-4 gap-5">
      <div class="grid-item">
        <div class="bg-gray-800/50 rounded-xl p-4 border border-gray-700/30">
          <h3 class="text-white font-semibold mb-2">Grid Item 1</h3>
          <p class="text-gray-300 text-sm">This is a grid item using CSS Grid instead of Element Plus.</p>
        </div>
      </div>
      <div class="grid-item">
        <div class="bg-gray-800/50 rounded-xl p-4 border border-gray-700/30">
          <h3 class="text-white font-semibold mb-2">Grid Item 2</h3>
          <p class="text-gray-300 text-sm">This is a grid item using CSS Grid instead of Element Plus.</p>
        </div>
      </div>
      <div class="grid-item">
        <div class="bg-gray-800/50 rounded-xl p-4 border border-gray-700/30">
          <h3 class="text-white font-semibold mb-2">Grid Item 3</h3>
          <p class="text-gray-300 text-sm">This is a grid item using CSS Grid instead of Element Plus.</p>
        </div>
      </div>
      <div class="grid-item">
        <div class="bg-gray-800/50 rounded-xl p-4 border border-gray-700/30">
          <h3 class="text-white font-semibold mb-2">Grid Item 4</h3>
          <p class="text-gray-300 text-sm">This is a grid item using CSS Grid instead of Element Plus.</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 使用原生CSS Grid布局，无需Element Plus
</script>

<style scoped>
.grid-test-container {
  max-width: 1200px;
  margin: 0 auto;
}

.grid-item {
  transition: all 0.3s ease;
}

.grid-item:hover {
  transform: translateY(-2px);
}
</style> 