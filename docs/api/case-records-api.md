# Case Records API 文档

## 概述

本文档描述了开箱记录相关的API接口，包括个人开箱记录查询、全站开箱记录获取、箱子开箱记录等功能。

**重要说明**：
- ✅ **完整国际化支持**：所有名称字段都提供多语言版本
- ✅ **WebSocket实时更新**：支持通过WebSocket获取实时开箱记录
- ✅ **缓存机制**：开箱记录使用Redis缓存，提升响应速度
- ✅ **分页支持**：支持分页查询，避免数据量过大

## 基础信息

- **基础URL**: `/api/box/`
- **WebSocket频道**: `ws_channel`
- **Content-Type**: `application/json`
- **认证方式**: Session认证（部分接口支持匿名访问）
- **响应格式**: JSON

## 响应状态码

| 状态码 | 含义 | 说明 |
|--------|------|------|
| 0 | Succeed | 请求成功 |
| 100 | NotLogin | 未登录 |
| 101 | BusinessError | 业务错误 |
| 102 | InvalidParams | 参数无效 |
| 401 | Maintenance | 系统维护 |
| 500 | Exception | 服务器内部错误 |

## 通用响应格式

```json
{
    "code": 0,
    "body": {},
    "message": "Succeed"
}
```

---

## 1. 个人开箱记录接口

### 1.1 获取个人开箱记录

**接口**: `GET /api/box/records/`

**描述**: 获取当前用户的开箱记录

**权限**: 需要登录

**请求参数**:
- `case_type`: 箱子类型ID（可选）
- `page`: 页码，默认1
- `pageSize`: 每页数量，默认10

**响应示例**:
```json
{
    "code": 0,
    "body": {
        "records": [
            {
                "uid": "record123456",
                "case": {
                    "case_key": "ak47_redline_case",
                    "name": "AK-47 红线箱子",
                    "name_en": "AK-47 Redline Case",
                    "name_zh_hans": "AK-47 红线箱子"
                },
                "case_name": "AK-47 红线箱子",
                "market_name": "AK-47 | 红线 (久经沙场)",
                "market_name_cn": "AK-47 | 红线 (久经沙场)",
                "icon_url": "https://steamcdn-a.akamaihd.net/apps/730/icons/econ/default_generated/weapon_ak47_cu_ak47_cobra_light_large.png",
                "price": 125.50,
                "cost": 10.50,
                "create_time": "2023-12-01T10:30:00Z"
            }
        ],
        "total": 156
    },
    "message": "Succeed"
}
```

---

## 2. 全站开箱记录接口

### 2.1 获取全站开箱记录

**接口**: `GET /api/monitor/case_records/`

**描述**: 获取全站最近的开箱记录（用于首页显示）

**权限**: 允许匿名访问

**请求参数**:
- `num`: 返回记录数量，默认30（最大100）

**响应示例**:
```json
{
    "code": 0,
    "body": [
        {
            "user_info": {
                "uid": "user123",
                "profile": {
                    "nickname": "玩家昵称",
                    "avatar": "https://example.com/avatar.jpg"
                }
            },
            "item_info": {
                "id": 12345,
                "name": "AK-47 | 红线 (久经沙场)",
                "name_en": "AK-47 | Redline (Field-Tested)",
                "name_zh_hans": "AK-47 | 红线 (久经沙场)",
                "image": "https://steamcdn-a.akamaihd.net/apps/730/icons/econ/default_generated/weapon_ak47_cu_ak47_cobra_light_large.png",
                "item_price": {
                    "price": 125.50,
                    "update_time": "2023-12-01T10:30:00Z"
                },
                "item_category": {
                    "cate_id": 1,
                    "cate_name": "步枪",
                    "cate_name_en": "Rifle",
                    "cate_name_zh_hans": "步枪",
                    "icon": "rifle_icon.png"
                },
                "item_quality": {
                    "quality_id": 4,
                    "quality_name": "久经沙场",
                    "quality_name_en": "Field-Tested",
                    "quality_name_zh_hans": "久经沙场",
                    "quality_color": "#8847ff"
                },
                "item_rarity": {
                    "rarity_id": 5,
                    "rarity_name": "保密",
                    "rarity_name_en": "Classified",
                    "rarity_name_zh_hans": "保密",
                    "rarity_color": "#d32ce6"
                },
                "item_exterior": {
                    "exterior_id": 4,
                    "exterior_name": "久经沙场",
                    "exterior_name_en": "Field-Tested",
                    "exterior_name_zh_hans": "久经沙场",
                    "exterior_color": "#8847ff"
                }
            },
            "case_info": {
                "id": 1,
                "case_key": "ak47_redline_case",
                "name": "AK-47 红线箱子",
                "name_en": "AK-47 Redline Case",
                "name_zh_hans": "AK-47 红线箱子",
                "cover": "https://example.com/case_cover.jpg",
                "price": 10.50,
                "open_count": 12346
            },
            "cost": 10.50,
            "create_time": "2023-12-01T10:30:00Z"
        }
    ],
    "message": "Succeed"
}
```

---

## 3. 箱子开箱记录接口

### 3.1 获取指定箱子的开箱记录

**接口**: `GET /api/box/box_records/`

**描述**: 获取指定箱子的最近开箱记录

**权限**: 允许匿名访问

**请求参数**:
- `key`: 箱子key（必需）
- `num`: 返回记录数量，默认30（最大30）

**响应示例**:
```json
{
    "code": 0,
    "body": {
        "items": [
            {
                "user_info": {
                    "uid": "user123",
                    "profile": {
                        "nickname": "玩家昵称",
                        "avatar": "https://example.com/avatar.jpg"
                    }
                },
                "item_info": {
                    "id": 12345,
                    "name": "AK-47 | 红线 (久经沙场)",
                    "name_en": "AK-47 | Redline (Field-Tested)",
                    "name_zh_hans": "AK-47 | 红线 (久经沙场)",
                    "image": "https://steamcdn-a.akamaihd.net/apps/730/icons/econ/default_generated/weapon_ak47_cu_ak47_cobra_light_large.png",
                    "item_price": {
                        "price": 125.50,
                        "update_time": "2023-12-01T10:30:00Z"
                    },
                    "item_rarity": {
                        "rarity_id": 5,
                        "rarity_name": "保密",
                        "rarity_name_en": "Classified",
                        "rarity_name_zh_hans": "保密",
                        "rarity_color": "#d32ce6"
                    }
                },
                "case_info": {
                    "name": "AK-47 红线箱子",
                    "cover": "https://example.com/case_cover.jpg"
                },
                "cost": 10.50,
                "create_time": "2023-12-01T10:30:00Z"
            }
        ]
    },
    "message": "Succeed"
}
```

---

## 4. WebSocket实时获取

### 4.1 通过WebSocket获取开箱记录

**WebSocket请求**: `socket.emit('case_records')`

**响应消息**: `["case_records", "update", caseRecordsData]`

**数据格式**: 与HTTP接口相同，但通过WebSocket实时推送

**使用场景**:
- 首页实时显示开箱记录
- 箱子详情页面实时更新开箱记录
- 管理员监控面板实时数据

### 4.2 获取监控统计数据

**WebSocket请求**: `socket.emit('monitor')`

**响应消息**: `["monitor", "update", monitorData]`

**数据内容**:
- 用户总数、在线人数、开箱总数、对战总数
- 最近活动数据和趋势信息

### 4.3 获取在线人数

**WebSocket请求**: `socket.emit('online_number')`

**响应消息**: `["online_number", "update", number]`

**数据内容**: 当前在线用户数

---

## 5. 缓存机制

### 5.1 缓存策略

- **开箱记录缓存**: 缓存5分钟，减少数据库查询压力
- **增量更新**: 只缓存新增的开箱记录，避免重复数据
- **自动清理**: 定期清理过期缓存，释放内存

### 5.2 缓存键命名

- `case_records_{num}`: 全站开箱记录缓存
- `box_record_{case_key}_{num}`: 指定箱子开箱记录缓存
- `last_case_record_time`: 最后查询时间戳缓存

---

## 6. 国际化字段支持

### 6.1 支持的国际化字段

所有名称相关字段都提供以下版本：
- `name` - 默认名称（通常是中文）
- `name_en` - 英文名称
- `name_zh_hans` - 简体中文名称

### 6.2 涵盖范围

**箱子名称**：
- 开箱记录中的箱子名称
- 箱子详情中的名称

**饰品名称**：
- 开箱记录中的饰品名称
- 饰品详情中的名称

**分类名称**：
- 饰品分类（Category）
- 饰品品质（Quality）
- 饰品稀有度（Rarity）
- 饰品外观（Exterior）

---

## 7. 使用示例

### 7.1 获取个人开箱记录

```javascript
// 获取个人开箱记录
const response = await fetch('/api/box/records/?page=1&pageSize=10');
const data = await response.json();

if (data.code === 0) {
    const records = data.body.records;
    // 处理开箱记录数据
}
```

### 7.2 获取全站开箱记录

```javascript
// 获取全站开箱记录
const response = await fetch('/api/monitor/case_records/?num=30');
const data = await response.json();

if (data.code === 0) {
    const records = data.body;
    // 处理开箱记录数据
}
```

### 7.3 通过WebSocket获取实时数据

```javascript
// 连接WebSocket
const socket = io('ws://domain/ws/');

// 获取开箱记录
socket.emit('case_records');

// 监听开箱记录更新
socket.on('case_records', (data) => {
    const [channel, action, records] = data;
    if (action === 'update') {
        // 处理开箱记录数据
        updateCaseRecords(records);
    }
});

// 获取监控数据
socket.emit('monitor');

// 监听监控数据更新
socket.on('monitor', (data) => {
    const [channel, action, monitorData] = data;
    if (action === 'update') {
        // 处理监控数据
        updateMonitorData(monitorData);
    }
});
```

---

## 8. 注意事项

1. **数据限制**: 单次查询最多返回100条记录
2. **缓存更新**: 开箱记录缓存每5分钟更新一次
3. **实时性**: WebSocket数据比HTTP接口更实时
4. **国际化**: 所有名称字段都支持多语言显示
5. **权限控制**: 个人开箱记录需要登录，全站记录允许匿名访问
6. **性能优化**: 使用缓存机制提升响应速度
7. **错误处理**: 需要处理网络错误和数据格式错误
8. **分页支持**: 个人开箱记录支持分页查询

---

## 9. 更新日志

- **2024-12-29**: ✅ 补充完整的case-records API文档
- **2024-12-29**: ✅ 添加WebSocket实时获取功能说明
- **2024-12-29**: ✅ 完善国际化字段支持说明
- **2024-12-29**: ✅ 添加缓存机制和性能优化说明
- **2024-12-28**: 初始文档版本

最后更新时间: 2024-12-29 