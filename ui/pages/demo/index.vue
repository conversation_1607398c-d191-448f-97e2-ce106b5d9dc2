<template>
  <div class="min-h-screen bg-gradient-to-br from-gray-900 to-gray-800 p-6">
    <div class="max-w-6xl mx-auto">
      <!-- 页面标题 -->
      <div class="text-center mb-12">
        <h1 class="text-5xl font-bold text-white mb-4">🎮 CSGO开发演示中心</h1>
        <p class="text-gray-300 text-xl">展示各种组件、动画效果和功能演示</p>
        <div class="mt-6 flex justify-center gap-4">
          <NuxtLink to="/" class="nav-button">
            <i class="fas fa-home mr-2"></i>
            返回首页
          </NuxtLink>
          <div class="nav-button cursor-default">
            <i class="fas fa-chart-bar mr-2"></i>
            共 {{ totalDemoCount }} 个演示
          </div>
        </div>
      </div>

      <!-- 演示分类 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
        <!-- 计算动画演示 -->
        <div class="demo-section">
          <h2 class="section-title">🧮 计算动画</h2>
          <div class="demo-grid">
            <NuxtLink to="/demo/number-counter" class="demo-card">
              <div class="demo-icon">🔢</div>
              <h3 class="demo-title">数字滚动动画</h3>
              <p class="demo-description">
                酷炫的数字计数和滚动动画效果，包含价格、百分比、矩阵数字雨等多种动画
              </p>
              <div class="demo-tags">
                <span class="demo-tag">数字滚动</span>
                <span class="demo-tag">价格动画</span>
                <span class="demo-tag">矩阵数字雨</span>
                <span class="demo-tag">粒子计数</span>
              </div>
            </NuxtLink>

            <NuxtLink to="/demo/progress-animations" class="demo-card">
              <div class="demo-icon">📊</div>
              <h3 class="demo-title">进度展示大全</h3>
              <p class="demo-description">
                展示各种酷炫的进度条动画效果和可复用的进度组件，包含渐变、脉冲、环形、波浪、粒子、计算进度等特效
              </p>
              <div class="demo-tags">
                <span class="demo-tag">渐变进度</span>
                <span class="demo-tag">脉冲效果</span>
                <span class="demo-tag">环形进度</span>
                <span class="demo-tag">波浪动画</span>
                <span class="demo-tag">粒子效果</span>
                <span class="demo-tag">计算进度组件</span>
              </div>
            </NuxtLink>

            <NuxtLink to="/demo/particle-effects" class="demo-card">
              <div class="demo-icon">✨</div>
              <h3 class="demo-title">粒子特效动画</h3>
              <p class="demo-description">
                展示各种酷炫的粒子效果和动态动画，包含浮动、爆炸、螺旋、矩阵等特效
              </p>
              <div class="demo-tags">
                <span class="demo-tag">浮动粒子</span>
                <span class="demo-tag">爆炸特效</span>
                <span class="demo-tag">螺旋动画</span>
                <span class="demo-tag">矩阵粒子</span>
              </div>
            </NuxtLink>

            <NuxtLink to="/demo/loading-animations" class="demo-card">
              <div class="demo-icon">⏳</div>
              <h3 class="demo-title">加载动画大全</h3>
              <p class="demo-description">
                20+种酷炫的加载和等待动画效果，包含CSGO霓虹、三点跳跃、波纹脉冲、旋转、骨架屏、魔方、彩虹等多种风格
              </p>
              <div class="demo-tags">
                <span class="demo-tag">CSGO霓虹</span>
                <span class="demo-tag">三点跳跃</span>
                <span class="demo-tag">波纹脉冲</span>
                <span class="demo-tag">旋转加载</span>
                <span class="demo-tag">骨架屏</span>
                <span class="demo-tag">魔方旋转</span>
                <span class="demo-tag">彩虹圆环</span>
                <span class="demo-tag">心跳脉搏</span>
              </div>
            </NuxtLink>

            <NuxtLink to="/demo/time-display" class="demo-card">
              <div class="demo-icon">⏰</div>
              <h3 class="demo-title">时间展示大全</h3>
              <p class="demo-description">
                展示各种时间格式、动态效果和国际化时间显示方案，包含实时时钟、倒计时、多时区、模拟时钟等
              </p>
              <div class="demo-tags">
                <span class="demo-tag">实时时钟</span>
                <span class="demo-tag">倒计时器</span>
                <span class="demo-tag">多时区</span>
                <span class="demo-tag">模拟时钟</span>
                <span class="demo-tag">相对时间</span>
                <span class="demo-tag">国际化</span>
                <span class="demo-tag">动态效果</span>
              </div>
            </NuxtLink>
          </div>
        </div>

        <!-- 对战动画演示 -->
        <div class="demo-section">
          <h2 class="section-title">⚔️ 对战动画</h2>
          <div class="demo-grid">
            <NuxtLink to="/demo/battle-waiting-ui" class="demo-card">
              <div class="demo-icon">🎨</div>
              <h3 class="demo-title">等待状态UI优化 ⭐</h3>
              <p class="demo-description">
                现代化等待状态UI设计，包含毛玻璃背景、浮动粒子、动态时钟、CSGO主题装饰等效果
              </p>
              <div class="demo-tags">
                <span class="demo-tag">毛玻璃效果</span>
                <span class="demo-tag">浮动粒子</span>
                <span class="demo-tag">动态时钟</span>
                <span class="demo-tag">CSGO主题</span>
                <span class="demo-tag">现代化设计</span>
              </div>
            </NuxtLink>

            <NuxtLink to="/demo/vertical-battle" class="demo-card">
              <div class="demo-icon">📱</div>
              <h3 class="demo-title">竖向对战布局</h3>
              <p class="demo-description">
                垂直老虎机布局测试，适合移动端的紧凑设计
              </p>
              <div class="demo-tags">
                <span class="demo-tag">垂直布局</span>
                <span class="demo-tag">移动优化</span>
                <span class="demo-tag">老虎机</span>
              </div>
            </NuxtLink>

            <!-- 对战演示版本 -->
            <NuxtLink to="/demo/battle-demo-new" class="demo-card">
              <div class="demo-icon">🔥</div>
              <h3 class="demo-title">新版对战演示</h3>
              <p class="demo-description">
                最新版本的对战系统演示，包含完整的对战流程和动画效果
              </p>
              <div class="demo-tags">
                <span class="demo-tag">新版本</span>
                <span class="demo-tag">完整流程</span>
                <span class="demo-tag">动画效果</span>
              </div>
            </NuxtLink>

            <NuxtLink to="/demo/battle-demo-old" class="demo-card">
              <div class="demo-icon">🎮</div>
              <h3 class="demo-title">旧版对战演示</h3>
              <p class="demo-description">
                旧版对战系统演示，用于对比和兼容性测试
              </p>
              <div class="demo-tags">
                <span class="demo-tag">旧版本</span>
                <span class="demo-tag">兼容测试</span>
                <span class="demo-tag">对比参考</span>
              </div>
            </NuxtLink>

            <NuxtLink to="/demo/battle-separated-demo" class="demo-card">
              <div class="demo-icon">�</div>
              <h3 class="demo-title">分离对战演示</h3>
              <p class="demo-description">
                分离式对战演示，将不同功能模块独立展示
              </p>
              <div class="demo-tags">
                <span class="demo-tag">模块分离</span>
                <span class="demo-tag">独立展示</span>
                <span class="demo-tag">功能拆分</span>
              </div>
            </NuxtLink>

            <NuxtLink to="/demo/slot-machine" class="demo-card">
              <div class="demo-icon">🎰</div>
              <h3 class="demo-title">老虎机开箱演示</h3>
              <p class="demo-description">
                酷炫的老虎机滚轮式开箱演示，真实的赌场老虎机体验，支持多滚轮同步动画
              </p>
              <div class="demo-tags">
                <span class="demo-tag">老虎机滚轮</span>
                <span class="demo-tag">滚动动画</span>
                <span class="demo-tag">赌场风格</span>
                <span class="demo-tag">多滚轮</span>
              </div>
            </NuxtLink>
          </div>
        </div>

        <!-- 组件测试 -->
        <div class="demo-section">
          <h2 class="section-title">🧩 组件测试</h2>
          <div class="demo-grid">
            <NuxtLink to="/demo/ui-demo" class="demo-card">
              <div class="demo-icon">🧩</div>
              <h3 class="demo-title">原生UI组件演示</h3>
              <p class="demo-description">
                展示项目中所有原生UI组件的样式和交互效果，包含按钮、输入框、消息提示、对话框等多种类型
              </p>
              <div class="demo-tags">
                <span class="demo-tag">按钮</span>
                <span class="demo-tag">输入框</span>
                <span class="demo-tag">消息提示</span>
                <span class="demo-tag">对话框</span>
              </div>
            </NuxtLink>

            <NuxtLink to="/demo/modal-showcase" class="demo-card">
              <div class="demo-icon">🪟</div>
              <h3 class="demo-title">弹出框样式展示</h3>
              <p class="demo-description">
                展示项目中所有弹出框组件的样式和交互效果，包含对战加入反馈、验证码对话框等多种类型
              </p>
              <div class="demo-tags">
                <span class="demo-tag">模态框</span>
                <span class="demo-tag">验证码</span>
                <span class="demo-tag">UI组件</span>
                <span class="demo-tag">交互效果</span>
              </div>
            </NuxtLink>

            <NuxtLink to="/demo/typography" class="demo-card">
              <div class="demo-icon">🎨</div>
              <h3 class="demo-title">字体系统展示</h3>
              <p class="demo-description">
                完整的字体库展示与使用指南，包含国际化字体、专用字体族、响应式大小和多语言混排测试
              </p>
              <div class="demo-tags">
                <span class="demo-tag">字体系统</span>
                <span class="demo-tag">国际化</span>
                <span class="demo-tag">响应式</span>
                <span class="demo-tag">多语言</span>
              </div>
            </NuxtLink>

            <NuxtLink to="/demo/battle-winner-modal-test" class="demo-card">
              <div class="demo-icon">🏆</div>
              <h3 class="demo-title">胜利者弹窗测试</h3>
              <p class="demo-description">
                测试BattleWinnerModal组件的庆祝特效、数据展示和交互功能，包含富数据和简单数据测试
              </p>
              <div class="demo-tags">
                <span class="demo-tag">庆祝特效</span>
                <span class="demo-tag">粒子动画</span>
                <span class="demo-tag">数据展示</span>
                <span class="demo-tag">弹窗组件</span>
              </div>
            </NuxtLink>

            <!-- 音效测试页面 -->
            <NuxtLink to="/demo/audio-demo" class="demo-card">
              <div class="demo-icon">🎵</div>
              <h3 class="demo-title">音效测试页面</h3>
              <p class="demo-description">
                测试对战演示页面的所有音效功能，包含AudioController组件演示
              </p>
              <div class="demo-tags">
                <span class="demo-tag">音效</span>
                <span class="demo-tag">测试</span>
                <span class="demo-tag">组件</span>
              </div>
            </NuxtLink>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 页面元数据
useHead({
  title: "演示中心 - CSGO Skins",
  meta: [
    {
      name: "description",
      content: "CSGO开发演示中心，展示各种组件、动画效果和功能演示",
    },
  ],
});

// 计算演示总数
const totalDemoCount = computed(() => {
  // 计算动画演示: 5个 (新增时间展示)
  // 对战动画演示: 9个
  // 调试工具: 6个
  // 组件测试: 5个 (删除了面包屑演示)
  return 25;
});
</script>

<style lang="scss" scoped>
.demo-section {
  @apply bg-gray-800/20 backdrop-blur-lg rounded-lg p-8 border border-gray-700/30;
}

.section-title {
  @apply text-2xl font-bold text-white mb-6 pb-2 border-b border-gray-600/50;
}

.demo-grid {
  @apply grid gap-4;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.demo-card {
  @apply block bg-gray-800/40 backdrop-blur-lg rounded-lg p-6 border border-gray-700/50 transition-all duration-300;

  &:hover {
    @apply border-primary/50 bg-gray-800/60 transform -translate-y-1;
    box-shadow: 0 8px 32px rgba(var(--color-primary-rgb), 0.15);
  }

  &--placeholder {
    @apply opacity-60 cursor-not-allowed;

    &:hover {
      @apply transform-none border-gray-700/50 bg-gray-800/40;
      box-shadow: none;
    }
  }

  &--info {
    @apply border-blue-500/30 bg-blue-900/20;

    &:hover {
      @apply border-blue-400/50 bg-blue-900/30;
      box-shadow: 0 8px 32px rgba(59, 130, 246, 0.15);
    }
  }
}

.demo-icon {
  @apply text-4xl mb-4 block;
}

.demo-title {
  @apply text-lg font-semibold text-white mb-2;
}

.demo-description {
  @apply text-sm text-gray-300 mb-4 leading-relaxed;
}

.demo-tags {
  @apply flex flex-wrap gap-2;
}

.demo-tag {
  @apply inline-block bg-primary/20 text-primary text-xs px-2 py-1 rounded-full border border-primary/30;
}

.nav-button {
  @apply inline-flex items-center px-4 py-2 bg-gray-700/50 hover:bg-gray-600/50 text-white rounded-lg transition-colors border border-gray-600/50 hover:border-gray-500/50;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .demo-grid {
    grid-template-columns: 1fr;
  }

  .demo-card {
    @apply p-4;
  }

  .demo-icon {
    @apply text-3xl mb-3;
  }
}
</style>
