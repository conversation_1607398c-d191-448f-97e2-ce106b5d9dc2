# -*- coding: utf-8 -*-
# Generated by Django 1.11.29 on 2025-05-21 15:28
from __future__ import unicode_literals

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('authentication', '0031_auto_20250104_1355'),
    ]

    operations = [
        migrations.AddField(
            model_name='authuser',
            name='login_domain',
            field=models.CharField(blank=True, default=None, max_length=300, null=True, verbose_name='login domain'),
        ),
        migrations.AddField(
            model_name='authuser',
            name='login_time',
            field=models.DateTimeField(default=django.utils.timezone.now, verbose_name='login time'),
        ),
        migrations.AddField(
            model_name='phonecoderecord',
            name='domain',
            field=models.Char<PERSON>ield(blank=True, default=None, max_length=128, null=True, verbose_name='domain'),
        ),
    ]
