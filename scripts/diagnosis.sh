#!/bin/bash

# CSGO皮肤交易平台 - 快速诊断脚本
# 使用方法: ./diagnosis.sh

echo "=============================================="
echo "🔍 CSGO皮肤交易平台 - 系统诊断工具"
echo "=============================================="

# 检查Docker是否安装
echo "📦 检查Docker状态..."
if command -v docker &> /dev/null; then
    echo "✅ Docker已安装: $(docker --version)"
    if command -v docker-compose &> /dev/null; then
        echo "✅ Docker Compose已安装: $(docker-compose --version)"
    else
        echo "❌ Docker Compose未安装"
    fi
else
    echo "❌ Docker未安装"
fi

echo ""

# 检查容器状态
echo "🐳 检查容器状态..."
if docker-compose ps &> /dev/null; then
    docker-compose ps
else
    echo "❌ 无法获取容器状态，请确保在项目根目录运行"
fi

echo ""

# 检查端口占用
echo "🔌 检查端口占用情况..."
ports=(8000 3001 3306 6379 80 443)
for port in "${ports[@]}"; do
    if lsof -i :$port &> /dev/null; then
        process=$(lsof -i :$port | tail -n 1 | awk '{print $1}')
        echo "✅ 端口 $port 被占用 ($process)"
    else
        echo "⚪ 端口 $port 空闲"
    fi
done

echo ""

# 检查系统资源
echo "💻 检查系统资源..."
echo "📊 内存使用:"
free -h | grep -E "Mem|Swap"
echo "💾 磁盘使用:"
df -h | grep -E "/$|/var|/tmp" | head -3

echo ""

# 检查环境文件
echo "⚙️ 检查配置文件..."
if [ -f "server/.env" ]; then
    echo "✅ 环境文件存在: server/.env"
    # 检查关键配置项
    echo "🔑 关键配置项:"
    grep -E "SECRET_KEY|DATABASE|REDIS|STEAM_API_KEY" server/.env | sed 's/=.*/=***/' || echo "❓ 未找到关键配置项"
else
    echo "❌ 环境文件不存在: server/.env"
    echo "💡 提示: 运行 'cp server/example.env server/.env' 创建环境文件"
fi

echo ""

# 检查数据库连接
echo "🗄️ 检查数据库连接..."
if docker-compose exec -T db mysql -u root -p$(grep DATABASE_PASSWORD server/.env 2>/dev/null | cut -d'=' -f2) -e "SELECT 1;" &> /dev/null; then
    echo "✅ 数据库连接正常"
else
    echo "❌ 数据库连接失败"
fi

# 检查Redis连接
echo "🔴 检查Redis连接..."
if docker-compose exec -T redis redis-cli ping &> /dev/null; then
    echo "✅ Redis连接正常"
else
    echo "❌ Redis连接失败"
fi

echo ""

# 检查日志文件
echo "📝 检查最近的错误日志..."
if [ -d "logs" ]; then
    echo "🔍 最近的错误日志:"
    find logs -name "*.log" -exec tail -n 5 {} \; 2>/dev/null | grep -i error | head -5
    if [ $? -ne 0 ]; then
        echo "✅ 未发现明显错误"
    fi
else
    echo "❓ 日志目录不存在"
fi

echo ""

# 检查API可用性
echo "🌐 检查API可用性..."
if curl -s http://localhost:8000/admin/ > /dev/null; then
    echo "✅ Django管理后台可访问"
else
    echo "❌ Django管理后台不可访问"
fi

if curl -s http://localhost:8000/api/ > /dev/null; then
    echo "✅ API接口可访问"
else
    echo "❌ API接口不可访问"
fi

echo ""

# 检查WebSocket服务
echo "🔌 检查WebSocket服务..."
if curl -s http://localhost:3001/ > /dev/null; then
    echo "✅ WebSocket服务运行中"
else
    echo "❌ WebSocket服务不可访问"
fi

echo ""

# 提供快速修复建议
echo "=============================================="
echo "🛠️ 快速修复建议"
echo "=============================================="
echo "如果发现问题，请尝试以下操作："
echo ""
echo "1. 重启所有服务:"
echo "   docker-compose down && docker-compose up -d"
echo ""
echo "2. 重新构建容器:"
echo "   docker-compose up --build"
echo ""
echo "3. 查看详细日志:"
echo "   docker-compose logs -f"
echo ""
echo "4. 检查端口冲突:"
echo "   sudo netstat -tulpn | grep -E ':8000|:3001|:3306|:6379'"
echo ""
echo "5. 清理Docker资源:"
echo "   docker system prune -a"
echo ""
echo "6. 查看完整文档:"
echo "   访问 docs/README.md 或 docs/quickstart.md"
echo ""
echo "=============================================="
echo "✅ 诊断完成！"
echo "=============================================="
