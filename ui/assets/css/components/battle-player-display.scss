// 对战玩家显示容器
.battle-player-display {
  width: 100%;
}

// 玩家网格布局
.players-grid {
  display: grid;
  gap: 1.5rem;
  margin-bottom: 2rem;

  // 响应式网格
  &[data-players="2"] {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }

  &[data-players="3"] {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }

  &[data-players="4"] {
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
  }

  // 桌面端固定列数
  @media (min-width: 1024px) {
    &[data-players="2"] {
      grid-template-columns: repeat(2, 1fr);
    }

    &[data-players="3"] {
      grid-template-columns: repeat(3, 1fr);
    }

    &[data-players="4"] {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  // 大屏幕优化
  @media (min-width: 1280px) {
    &[data-players="4"] {
      grid-template-columns: repeat(4, 1fr);
    }
  }
}

// 🎨 简化的等待状态容器
.waiting-state-container {
  position: relative;
  min-height: 200px;
  margin-top: 2rem;
  border-radius: var(--radius-lg, 0.75rem);
  overflow: hidden;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

// 🎨 简化的主要内容区域
.waiting-content {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 1.5rem;
  text-align: center;
  min-height: 200px;
}

// 🎨 简化的状态图标容器
.status-icon-container {
  margin-bottom: 1rem;
  
  .status-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 50%;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    
    &:hover {
      background: rgba(59, 130, 246, 0.15);
      border-color: rgba(59, 130, 246, 0.3);
      transform: scale(1.05);
    }
  }
}

// 🎨 简化的等待文本区域
.waiting-text {
  margin-bottom: 1.5rem;
  
  .waiting-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
    margin: 0;
  }
}

// 🎨 简化的玩家计数
.player-count-simple {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  
  .count-text {
    font-size: 1.25rem;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .count-progress {
    position: relative;
    width: 120px;
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-full, 9999px);
    overflow: hidden;
    
    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #3b82f6, #1d4ed8);
      border-radius: var(--radius-full, 9999px);
      transition: width 0.5s ease;
      box-shadow: 0 0 8px rgba(59, 130, 246, 0.3);
    }
  }
}

// 🎨 简化的响应式适配
@media (max-width: 768px) {
  .waiting-state-container {
    min-height: 180px;
  }
  
  .waiting-content {
    padding: 1.5rem 1rem;
  }
  
  .status-icon-container .status-icon {
    width: 40px;
    height: 40px;
  }
  
  .waiting-title {
    font-size: 1rem;
  }
  
  .player-count-simple {
    .count-text {
      font-size: 1.125rem;
    }
    
    .count-progress {
      width: 100px;
      height: 5px;
    }
  }
}

// 操作按钮区域
.battle-actions {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg, 0.75rem);
  flex-wrap: wrap;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 0.75rem;
  }
}

// 操作按钮基础样式
.start-battle-btn,
.join-battle-btn,
.view-result-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--radius-md, 0.5rem);
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
  min-width: 140px;
  justify-content: center;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    transition: left 0.5s ease;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);

    &::before {
      left: 100%;
    }
  }

  &:active {
    transform: translateY(0);
  }
}

// 开始对战按钮
.start-battle-btn {
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  color: white;
  box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);

  &:hover {
    box-shadow: 0 4px 16px rgba(255, 107, 53, 0.4);
  }
}

// 加入对战按钮
.join-battle-btn {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);

  &:hover {
    box-shadow: 0 4px 16px rgba(16, 185, 129, 0.4);
  }
}

// 查看结果按钮
.view-result-btn {
  background: linear-gradient(135deg, #8e44ad, #9b59b6);
  color: white;
  box-shadow: 0 2px 8px rgba(142, 68, 173, 0.3);

  &:hover {
    box-shadow: 0 4px 16px rgba(142, 68, 173, 0.4);
  }
}

// 对战进行中提示
.battle-in-progress {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: rgba(255, 165, 0, 0.1);
  border: 1px solid rgba(255, 165, 0, 0.3);
  border-radius: var(--radius-md, 0.5rem);
  font-size: 0.875rem;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
} 