import logging

from django.contrib import messages
from django.http.response import HttpResponse
from django.shortcuts import redirect, reverse
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.views.decorators.csrf import csrf_exempt

from rest_framework.views import APIView
from rest_framework.permissions import AllowAny
from rest_framework.parsers import <PERSON><PERSON>ars<PERSON>, JSONParser
from rest_framework.response import Response

from steambase.enums import RespCode
from steambase.utils import reformat_resp, current_user
from sitecfg.interfaces import get_maintenance
from lottery.business import get_lottery_setting, get_lottery_winner, get_lottery_info


_logger = logging.getLogger(__name__)


class GetLotterySettingView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:

            user = current_user(request)
            code, resp = get_lottery_setting(user)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetLotteryInfoView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:

            code, resp = get_lottery_info()
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetLotteryWinnerView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            user = current_user(request)
            query = {
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]

            #fields = ('uid', 'market_name', 'market_hash_name', 'market_name_cn', 'icon_url','nick_name' )
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            order = request.query_params.get('order', '-create_time')

            lottery_type = request.query_params.get('lotteryType', None)
            code, resp = get_lottery_winner(lottery_type, user, query, page, page_size, order)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


