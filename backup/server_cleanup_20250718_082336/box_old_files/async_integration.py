#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
异步轮次推进集成脚本
将异步推进系统集成到现有对战逻辑中
"""

import logging
import asyncio
from typing import Dict, List

logger = logging.getLogger(__name__)

def integrate_async_progression_to_open_room_case():
    """
    将异步推进系统集成到 open_room_case 函数中
    这个函数用于修改现有的开箱逻辑，使其支持异步推进
    """
    
    # 原始开箱完成处理函数
    def handle_round_completion_async(room, room_round, current_round, total_rounds, bets):
        """异步处理轮次完成"""
        
        async def async_completion():
            try:
                from .async_battle_progression import AsyncBattleProgressionManager
                
                progression_manager = AsyncBattleProgressionManager(room.uid)
                
                # 准备轮次结果数据
                round_results = prepare_round_results(room, room_round, bets)
                
                # 完成当前轮次
                success = await progression_manager.complete_round(round_results)
                if not success:
                    logger.error(f"轮次完成失败: room={room.short_id}")
                    return
                
                # 处理消息队列
                await progression_manager.process_message_queue()
                
                # 检查对战状态
                state = await progression_manager.get_battle_state()
                if not state:
                    logger.error(f"无法获取对战状态: room={room.short_id}")
                    return
                
                # 判断是否需要结束对战
                if state.get('battle_state') == 'battle_ending':
                    await handle_battle_end_async(progression_manager, room)
                
                logger.info(f"轮次 {current_round} 异步处理完成: room={room.short_id}")
                
            except Exception as e:
                logger.error(f"异步轮次完成处理失败: room={room.short_id}, error={e}")
                # 降级到同步处理
                handle_round_completion_sync(room, room_round, current_round, total_rounds, bets)
        
        # 创建异步任务
        asyncio.create_task(async_completion())
    
    async def handle_battle_end_async(progression_manager, room):
        """异步处理对战结束"""
        try:
            from .models import CaseRoomBet, GameState, CaseRoomItem, PackageItem
            from steambase.enums import PackageState, PackageSourceType
            from .utils import safe_image_url
            
            bets = CaseRoomBet.objects.filter(room=room).order_by('-open_amount')
            
            if bets.count() == 0:
                logger.warning(f'Room {room.short_id} has no participants, cancelling room')
                room.state = GameState.Cancelled.value
                room.save()
                return
            
            # 重置所有参与者的胜利状态
            for b in bets:
                b.victory = 0
                if b.win_amount is None:
                    b.win_amount = 0
                if b.win_items_count is None:
                    b.win_items_count = 0
                b.save()

            # 确定获胜者
            winner = bets[0]
            if len(bets) >= 2 and bets[0].open_amount == bets[1].open_amount:
                import random
                win_index = random.choice([0, 1])
                winner = bets[win_index]
            
            # 更新房间状态
            room.state = GameState.End.value
            room.save()
            
            # 更新获胜者状态
            winner.victory = 1
            
            # 处理战利品分配
            pids = []
            lose_bets = bets.exclude(id=winner.id)
            for bet in lose_bets:
                bet_items = CaseRoomItem.objects.filter(room=room, bet=bet)
                for item in bet_items:
                    winner.win_amount += item.price
                    winner.win_items_count += 1
                    package = PackageItem.objects.create(
                        user=winner.user,
                        item_info=item.item_info,
                        assetid='0',
                        source=PackageSourceType.BattleRoom.value,
                        instanceid='0',
                        state=PackageState.Available.value,
                        amount=item.price,
                        case_name="对战"
                    )
                    pids.append(package.uid)
            
            winner.save()
            
            # 准备结束消息数据
            winner_data = {
                'user': {
                    'uid': winner.user.uid,
                    'profile': {
                        'nickname': winner.user.profile.nickname if hasattr(winner.user, 'profile') and winner.user.profile else winner.user.username,
                        'avatar': safe_image_url(winner.user.profile.avatar) if hasattr(winner.user, 'profile') and winner.user.profile else ''
                    }
                },
                'total_amount': float(winner.win_amount or 0),
                'victory': 1
            }
            
            final_results = []
            for bet in bets:
                final_results.append({
                    'user': {
                        'uid': bet.user.uid,
                        'profile': {
                            'nickname': bet.user.profile.nickname if hasattr(bet.user, 'profile') and bet.user.profile else bet.user.username,
                            'avatar': safe_image_url(bet.user.profile.avatar) if hasattr(bet.user, 'profile') and bet.user.profile else ''
                        }
                    },
                    'open_amount': float(bet.open_amount or 0),
                    'win_amount': float(bet.win_amount or 0),
                    'victory': bet.victory,
                    'total_items': bet.win_items_count or 0
                })
            
            # 使用异步推进系统结束对战
            await progression_manager.end_battle(winner_data, final_results)
            await progression_manager.process_message_queue()
            
            # 清理异步状态
            await progression_manager.cleanup()
            
            logger.info(f'对战异步结束: room={room.short_id}, winner={winner.user.username}')
            
        except Exception as e:
            logger.error(f'异步对战结束处理失败: room={room.short_id}, error={e}')
    
    def handle_round_completion_sync(room, room_round, current_round, total_rounds, bets):
        """同步处理轮次完成（降级逻辑）"""
        try:
            from .business_room import ws_send_round_result
            from .models import CaseRoomRound, CaseRoomItem
            
            # 准备轮次结果数据
            results = []
            for bet in bets:
                bet_items = CaseRoomItem.objects.filter(room=room, round=room_round, bet=bet)
                items_data = []
                for item in bet_items:
                    items_data.append({
                        'item_info': item.item_info,
                        'price': float(item.price) if item.price else 0.0
                    })
                
                results.append({
                    'user': bet.user.uid,
                    'items': items_data,
                    'total_amount': float(bet.open_amount) if bet.open_amount else 0.0
                })
            
            # 发送轮次结果
            ws_send_round_result(room.uid, results, current_round)
            
            # 检查是否还有轮次
            remaining_rounds = CaseRoomRound.objects.filter(room=room, opened=False)
            if remaining_rounds.exists():
                # 继续下一轮
                next_round = remaining_rounds.first()
                from .business_room import open_room_case
                open_room_case(room, next_round)
            else:
                # 对战结束
                handle_battle_end_sync(room)
                
        except Exception as e:
            logger.error(f'同步轮次完成处理失败: room={room.short_id}, error={e}')
    
    def handle_battle_end_sync(room):
        """同步处理对战结束（降级逻辑）"""
        try:
            from .business_room import ws_send_battle_end
            from .models import CaseRoomBet, GameState
            
            bets = CaseRoomBet.objects.filter(room=room).order_by('-open_amount')
            
            if bets.count() == 0:
                room.state = GameState.Cancelled.value
                room.save()
                return
            
            # 简化的对战结束处理
            winner = bets[0]
            winner.victory = 1
            winner.save()
            
            room.state = GameState.End.value
            room.save()
            
            # 发送对战结束消息
            winner_data = {'user': {'uid': winner.user.uid}, 'total_amount': 0, 'victory': 1}
            final_results = [{'user': {'uid': bet.user.uid}, 'victory': bet.victory} for bet in bets]
            
            ws_send_battle_end(room.uid, winner_data, final_results, 1)
            
            logger.info(f'对战同步结束: room={room.short_id}')
            
        except Exception as e:
            logger.error(f'同步对战结束处理失败: room={room.short_id}, error={e}')
    
    def prepare_round_results(room, room_round, bets):
        """准备轮次结果数据"""
        from .models import CaseRoomItem
        
        round_results = []
        for bet in bets:
            bet_items = CaseRoomItem.objects.filter(room=room, round=room_round, bet=bet)
            items_data = []
            for item in bet_items:
                items_data.append({
                    'item_info': item.item_info,
                    'price': float(item.price) if item.price else 0.0,
                    'item_type': item.item_type
                })
            
            round_results.append({
                'user': {
                    'uid': bet.user.uid,
                    'nickname': bet.user.profile.nickname if hasattr(bet.user, 'profile') and bet.user.profile else bet.user.username
                },
                'items': items_data,
                'total_amount': float(bet.open_amount) if bet.open_amount else 0.0
            })
        
        return round_results
    
    # 返回集成函数
    return {
        'handle_round_completion_async': handle_round_completion_async,
        'handle_round_completion_sync': handle_round_completion_sync,
        'handle_battle_end_async': handle_battle_end_async,
        'handle_battle_end_sync': handle_battle_end_sync,
        'prepare_round_results': prepare_round_results
    }

# 导出集成函数
integration_functions = integrate_async_progression_to_open_room_case()
