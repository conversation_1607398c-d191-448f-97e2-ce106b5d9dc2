from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta

from articles.models import ContentCategory, ContentTag, Content
from agent.models import Agent

User = get_user_model()


class Command(BaseCommand):
    help = '创建示例文章数据'

    def add_arguments(self, parser):
        parser.add_argument(
            '--count',
            type=int,
            default=10,
            help='要创建的文章数量'
        )

    def handle(self, *args, **options):
        count = options['count']
        
        self.stdout.write('开始创建示例文章数据...')
        
        # 创建分类
        categories = self.create_categories()
        
        # 创建标签
        tags = self.create_tags()
        
        # 获取agent用户
        agent_users = self.get_agent_users()
        
        if not agent_users:
            self.stdout.write(
                self.style.WARNING('没有找到agent用户，请先创建agent用户')
            )
            return
        
        # 创建文章
        self.create_articles(count, categories, tags, agent_users)
        
        self.stdout.write(
            self.style.SUCCESS(f'成功创建了 {count} 篇示例文章')
        )

    def create_categories(self):
        """创建示例分类"""
        categories_data = [
            {'name': '游戏攻略', 'slug': 'game-guide', 'color': '#007bff', 'icon': 'fas fa-gamepad'},
            {'name': '新闻公告', 'slug': 'news', 'color': '#28a745', 'icon': 'fas fa-newspaper'},
            {'name': '活动信息', 'slug': 'events', 'color': '#ffc107', 'icon': 'fas fa-calendar'},
            {'name': '系统更新', 'slug': 'updates', 'color': '#dc3545', 'icon': 'fas fa-sync'},
            {'name': '帮助文档', 'slug': 'help', 'color': '#6f42c1', 'icon': 'fas fa-question-circle'},
        ]
        
        categories = []
        for i, cat_data in enumerate(categories_data):
            category, created = ContentCategory.objects.get_or_create(
                slug=cat_data['slug'],
                defaults={
                    'name': cat_data['name'],
                    'color': cat_data['color'],
                    'icon': cat_data['icon'],
                    'sort_order': i,
                    'description': f'{cat_data["name"]}相关内容'
                }
            )
            categories.append(category)
            if created:
                self.stdout.write(f'创建分类: {category.name}')
        
        return categories

    def create_tags(self):
        """创建示例标签"""
        tags_data = [
            {'name': 'CS:GO', 'slug': 'csgo', 'color': '#ff6b35'},
            {'name': '皮肤', 'slug': 'skins', 'color': '#f7931e'},
            {'name': '开箱', 'slug': 'case-opening', 'color': '#00a8cc'},
            {'name': '交易', 'slug': 'trading', 'color': '#6a994e'},
            {'name': '新手', 'slug': 'beginner', 'color': '#bc4749'},
            {'name': '高级', 'slug': 'advanced', 'color': '#2d3436'},
            {'name': '热门', 'slug': 'hot', 'color': '#e17055'},
            {'name': '推荐', 'slug': 'recommended', 'color': '#00b894'},
        ]
        
        tags = []
        for tag_data in tags_data:
            tag, created = ContentTag.objects.get_or_create(
                slug=tag_data['slug'],
                defaults={
                    'name': tag_data['name'],
                    'color': tag_data['color']
                }
            )
            tags.append(tag)
            if created:
                self.stdout.write(f'创建标签: {tag.name}')
        
        return tags

    def get_agent_users(self):
        """获取agent用户"""
        agent_users = User.objects.filter(is_agent=True, is_active=True)
        if agent_users.exists():
            self.stdout.write(f'找到 {agent_users.count()} 个agent用户')
            return list(agent_users)
        return []

    def create_articles(self, count, categories, tags, agent_users):
        """创建示例文章"""
        import random
        from django.utils.text import slugify
        
        content_types = ['article', 'announcement', 'news', 'guide']
        statuses = ['published', 'draft']
        priorities = ['normal', 'high', 'low']
        
        sample_contents = [
            {
                'title': 'CS:GO皮肤交易完全指南',
                'content': '''
                <h2>什么是CS:GO皮肤交易？</h2>
                <p>CS:GO皮肤交易是指玩家之间交换游戏内装饰性物品的过程。这些皮肤不会影响游戏性能，但可以让你的武器看起来更酷。</p>
                
                <h3>交易的基本步骤：</h3>
                <ol>
                    <li><strong>了解市场价格</strong> - 在Steam市场或第三方网站查看皮肤的当前价格</li>
                    <li><strong>选择交易平台</strong> - 可以使用Steam交易、第三方网站或我们的平台</li>
                    <li><strong>验证物品真实性</strong> - 检查皮肤的磨损度、花纹等属性</li>
                    <li><strong>安全交易</strong> - 使用可信的交易方式，避免诈骗</li>
                </ol>
                
                <h3>交易注意事项：</h3>
                <ul>
                    <li>始终通过官方渠道进行交易</li>
                    <li>不要相信过于优惠的交易</li>
                    <li>保护好你的Steam账户信息</li>
                    <li>了解Steam交易限制和冷却时间</li>
                </ul>
                
                <p>记住，安全交易是最重要的。如果你是新手，建议先从小额交易开始练习。</p>
                ''',
                'excerpt': '详细介绍CS:GO皮肤交易的基本知识和注意事项，帮助新手安全地进行皮肤交易。'
            },
            {
                'title': '开箱技巧：如何提高获得稀有皮肤的几率',
                'content': '''
                <h2>开箱基础知识</h2>
                <p>虽然开箱本质上是随机的，但了解一些技巧可以帮助你做出更明智的决定。</p>
                
                <h3>选择合适的箱子：</h3>
                <ul>
                    <li><strong>新箱子</strong> - 通常包含最新的皮肤设计</li>
                    <li><strong>经典箱子</strong> - 包含一些经典和稀有的皮肤</li>
                    <li><strong>特殊箱子</strong> - 限时活动箱子，可能有独特奖励</li>
                </ul>
                
                <h3>开箱策略：</h3>
                <ol>
                    <li>设定预算并严格遵守</li>
                    <li>了解箱子内容和概率</li>
                    <li>考虑直接购买想要的皮肤</li>
                    <li>不要追求损失</li>
                </ol>
                
                <blockquote>
                    <p><strong>重要提醒：</strong>开箱是一种娱乐活动，请理性消费，不要超出自己的经济承受能力。</p>
                </blockquote>
                ''',
                'excerpt': '分享开箱的基本策略和技巧，帮助玩家理性地享受开箱乐趣。'
            },
            {
                'title': '网站新功能上线公告',
                'content': '''
                <h2>新功能介绍</h2>
                <p>我们很高兴地宣布，网站新增了以下功能：</p>
                
                <h3>智能推荐系统</h3>
                <p>基于你的交易历史和偏好，为你推荐合适的皮肤和交易机会。</p>

                <h3>价格趋势分析</h3>
                <p>查看皮肤价格的历史走势，帮助你做出更好的交易决策。</p>

                <h3>实时通知</h3>
                <p>当你关注的皮肤价格发生变化时，我们会及时通知你。</p>

                <h3>社区论坛</h3>
                <p>与其他玩家交流经验，分享交易心得。</p>
                
                <p>这些功能将在未来几天内逐步向所有用户开放。感谢大家的支持！</p>
                ''',
                'excerpt': '介绍网站最新上线的功能，包括智能推荐、价格分析等实用工具。'
            }
        ]
        
        for i in range(count):
            # 随机选择内容模板
            content_template = random.choice(sample_contents)
            
            # 生成标题
            title = f"{content_template['title']} - 第{i+1}期"
            
            # 随机选择属性
            content_type = random.choice(content_types)
            status = random.choice(statuses)
            priority = random.choice(priorities)
            category = random.choice(categories)
            author = random.choice(agent_users)
            
            # 创建文章
            article = Content.objects.create(
                title=title,
                subtitle=f"这是{title}的副标题",
                excerpt=content_template['excerpt'],
                content=content_template['content'],
                content_type=content_type,
                status=status,
                priority=priority,
                category=category,
                author=author,
                editor=author,
                is_featured=random.choice([True, False]),
                is_pinned=random.choice([True, False]) if i < 3 else False,
                publish_date=timezone.now() - timedelta(days=random.randint(0, 30)),
                allow_comments=True,
                sort_order=i
            )
            
            # 随机添加标签
            article_tags = random.sample(tags, random.randint(1, 4))
            article.tags.set(article_tags)
            
            self.stdout.write(f'创建文章: {article.title}')
