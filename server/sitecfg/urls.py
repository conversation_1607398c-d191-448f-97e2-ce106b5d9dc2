from django.conf.urls import url

# from sitecfg import model_signals
from sitecfg import views


app_name = 'sitecfg'
urlpatterns = [
    
    
    url('^footer/', views.GetSiteFooterView.as_view()),
    
    url('^cfg/', views.GetSiteCFGView.as_view()),
    url('^banner/', views.GetSiteBannerView.as_view()),
    url('^article/', views.GetArticleDetailView.as_view()),
    url('^articlelist/', views.GetArticleListView.as_view()),#弃用
    url('^articleweapon/', views.GetArticleWeaponView.as_view()),#饰品
    url('^articleteam/', views.GetArticleTeamView.as_view()),#战队
    url('^articleplayer/', views.GetArticlePlayerView.as_view()),#职业选手
    url('^articlematch/', views.GetArticleMatchView.as_view()),#赛事

    url('^seo/', views.GetSEOView.as_view()),
    url('^checkip/', views.CheckIPView.as_view()),

    # 2025
    url('^announce/list/', views.GetSiteAnnounceeListView.as_view()),
    url('^announce/detail/', views.GetSiteAnnounceeView.as_view()),
    url('^faq/', views.GetSiteFAQView.as_view()),
    url('^support/', views.GetSiteSupportView.as_view()),
]
