# 文章系统使用说明

## 📖 概述

文章系统是一个功能完整的内容管理系统，支持多种内容类型，专为agent用户设计。

## 🎯 主要特性

### ✅ 已实现的功能

1. **富文本编辑器**
   - 使用CKEditor进行内容编辑
   - 支持图片上传、表格、链接等
   - 所见即所得的编辑体验

2. **Agent用户权限**
   - 只有agent用户可以作为作者和编辑者
   - 自动记录创建者和最后编辑者
   - 权限验证和限制

3. **内容分类管理**
   - 支持多级分类
   - 分类颜色和图标
   - 分类排序

4. **标签系统**
   - 多标签支持
   - 标签颜色自定义
   - 标签统计

5. **发布管理**
   - 草稿/发布状态
   - 定时发布
   - 内容过期设置

6. **SEO优化**
   - 自定义SEO标题、描述、关键词
   - URL友好的slug
   - 结构化数据

## 🚀 使用方法

### 1. 管理后台使用

访问Django管理后台：`/admin/`

#### 创建内容分类
1. 进入 "内容分类" 管理页面
2. 点击 "新增内容分类"
3. 填写分类信息：
   - 名称：分类显示名称
   - Slug：URL友好的标识符
   - 描述：分类说明
   - 图标：FontAwesome图标类名
   - 颜色：十六进制颜色代码
   - 排序：数字越小越靠前

#### 创建内容标签
1. 进入 "内容标签" 管理页面
2. 点击 "新增内容标签"
3. 填写标签信息：
   - 名称：标签显示名称
   - Slug：URL友好的标识符
   - 颜色：标签显示颜色

#### 创建文章内容
1. 进入 "内容" 管理页面
2. 点击 "新增内容"
3. 填写基本信息：
   - 标题：文章标题
   - 副标题：可选的副标题
   - 摘要：文章简介
   - 内容：使用富文本编辑器编写正文

4. 设置分类和标签：
   - 内容类型：选择文章类型（文章、公告、新闻等）
   - 分类：选择所属分类
   - 标签：选择相关标签

5. 发布设置：
   - 状态：草稿/发布/归档/定时发布
   - 优先级：低/普通/高/紧急
   - 发布时间：内容发布时间
   - 过期时间：可选的过期时间

6. 作者和编辑者：
   - 系统会自动设置当前agent用户为作者
   - 每次编辑都会更新编辑者信息

### 2. 命令行工具

#### 创建示例数据
```bash
# 激活虚拟环境
source venv/bin/activate

# 创建示例文章数据
python manage.py create_sample_articles --count 10
```

### 3. API使用

#### 获取文章列表
```http
GET /api/articles/contents/
```

#### 获取文章详情
```http
GET /api/articles/contents/{id}/
```

#### 创建文章（需要agent权限）
```http
POST /api/articles/contents/
Content-Type: application/json

{
    "title": "文章标题",
    "content": "<p>文章内容</p>",
    "content_type": "article",
    "status": "published"
}
```

## 🔧 技术实现

### 模型结构

1. **ContentCategory** - 内容分类
2. **ContentTag** - 内容标签  
3. **Content** - 主要内容模型
4. **ContentAttachment** - 内容附件
5. **ContentView** - 浏览记录

### 关键特性

1. **Agent用户限制**
   ```python
   # 模型中的限制
   author = models.ForeignKey(
       User, 
       limit_choices_to={'is_agent': True, 'is_active': True}
   )
   ```

2. **富文本字段**
   ```python
   content = RichTextUploadingField(_("Content"), blank=True, null=True)
   ```

3. **自定义管理器**
   ```python
   # 支持链式查询
   Content.objects.published().by_agent(user).featured()
   ```

## 📝 最佳实践

### 1. 内容创建
- 使用有意义的标题和摘要
- 合理使用标签，不要过多
- 设置合适的发布时间
- 添加SEO信息提高搜索排名

### 2. 分类管理
- 保持分类结构简单清晰
- 使用一致的命名规范
- 定期整理和优化分类

### 3. 权限管理
- 确保只有合适的用户被设为agent
- 定期检查agent用户权限
- 记录重要的编辑操作

## 🛠️ 扩展功能

### 计划中的功能
1. **评论系统** - 用户评论和回复
2. **内容审核** - 发布前审核流程
3. **版本控制** - 内容修改历史
4. **批量操作** - 批量导入/导出
5. **统计分析** - 阅读量、用户行为分析

### 自定义扩展
可以通过以下方式扩展系统：

1. **自定义字段**
   ```python
   # 在Content模型中添加新字段
   custom_field = models.CharField(max_length=255, blank=True)
   ```

2. **自定义内容类型**
   ```python
   # 在CONTENT_TYPE_CHOICES中添加新类型
   ('custom_type', _('Custom Type')),
   ```

3. **自定义序列化器**
   ```python
   # 创建特定用途的序列化器
   class CustomContentSerializer(ContentDetailSerializer):
       # 自定义字段和方法
   ```

## 🔍 故障排除

### 常见问题

1. **富文本编辑器不显示**
   - 检查CKEditor静态文件是否正确加载
   - 运行 `python manage.py collectstatic`

2. **Agent用户无法选择**
   - 确保用户的`is_agent`字段为True
   - 检查用户的`is_active`状态

3. **图片上传失败**
   - 检查MEDIA_ROOT和MEDIA_URL设置
   - 确保上传目录有写权限

### 调试命令
```bash
# 检查系统状态
python manage.py check

# 查看数据库迁移状态
python manage.py showmigrations articles

# 创建超级用户
python manage.py createsuperuser
```

## 📞 支持

如果遇到问题或需要帮助，请：
1. 查看Django日志文件
2. 检查数据库连接
3. 确认所有依赖包已正确安装
4. 联系技术支持团队
