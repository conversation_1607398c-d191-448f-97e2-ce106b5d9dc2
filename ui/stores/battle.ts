import { defineStore } from 'pinia'
import { battleApi, type AvailableCase, type BattleRoom, GameState } from '~/services/battle-api'

// Battle Store状态接口
export interface BattleState {
  // 房间数据
  rooms: BattleRoom[]
  currentRoom: BattleRoom | null
  
  // 箱子数据
  availableCases: AvailableCase[]
  selectedCases: string[]
  
  // 加载状态
  loading: {
    rooms: boolean
    cases: boolean
    joining: boolean
    creating: boolean
    currentRoom: boolean
  }
  
  // 错误状态
  errors: {
    rooms: string | null
    cases: string | null
    joining: string | null
    creating: string | null
    currentRoom: string | null
  }
  
  // 缓存时间戳
  cacheTimestamps: {
    rooms: number
    cases: number
  }
  
  // 筛选器
  filters: {
    search: string
    states: string[]
    sortBy: 'create_time' | 'price_asc' | 'price_desc' | 'joiner_count'
  }
}

export const useBattleStore = defineStore('battle', {
  state: (): BattleState => ({
    // 房间数据
    rooms: [],
    currentRoom: null,
    
    // 箱子数据
    availableCases: [],
    selectedCases: [],
    
    // 加载状态
    loading: {
      rooms: false,
      cases: false,
      joining: false,
      creating: false,
      currentRoom: false
    },
    
    // 错误状态
    errors: {
      rooms: null,
      cases: null,
      joining: null,
      creating: null,
      currentRoom: null
    },
    
    // 缓存时间戳
    cacheTimestamps: {
      rooms: 0,
      cases: 0
    },
    
    // 筛选器
    filters: {
      search: '',
      states: ['2', '4', '5'], // 默认显示可加入、已满员、进行中的房间
      sortBy: 'create_time'
    }
  }),

  getters: {
    // 获取筛选后的房间列表
    filteredRooms: (state) => {
      let filtered = [...state.rooms]
      
      // 搜索筛选
      if (state.filters.search) {
        const searchTerm = state.filters.search.toLowerCase()
        filtered = filtered.filter(room => 
          room.user.profile.nickname.toLowerCase().includes(searchTerm) ||
          room.short_id.toLowerCase().includes(searchTerm)
        )
      }
      
      // 状态筛选
      if (state.filters.states.length > 0) {
        filtered = filtered.filter(room => 
          state.filters.states.includes(String(room.state))
        )
      }
      
      // 排序
      filtered.sort((a, b) => {
        switch (state.filters.sortBy) {
          case 'price_asc':
            return a.price - b.price
          case 'price_desc':
            return b.price - a.price
          case 'joiner_count':
            return b.joiner_count - a.joiner_count
          case 'create_time':
          default:
            return new Date(b.create_time).getTime() - new Date(a.create_time).getTime()
        }
      })
      
      return filtered
    },
    
    // 获取筛选后的箱子列表
    filteredCases: (state) => {
      let filtered = [...state.availableCases]
      
      // 搜索筛选
      if (state.filters.search) {
        const searchTerm = state.filters.search.toLowerCase()
        filtered = filtered.filter(caseItem => 
          caseItem.name.toLowerCase().includes(searchTerm) ||
          (caseItem.name_en && caseItem.name_en.toLowerCase().includes(searchTerm)) ||
          (caseItem.name_zh_hans && caseItem.name_zh_hans.toLowerCase().includes(searchTerm))
        )
      }
      
      return filtered
    },
    
    // 获取选中箱子的总价值
    selectedCasesTotalValue: (state) => {
      return state.selectedCases.reduce((total, caseKey) => {
        const caseItem = state.availableCases.find(c => c.case_key === caseKey)
        return total + (caseItem?.price || 0)
      }, 0)
    },
    
    // 检查缓存是否有效（5分钟）
    isCacheValid: (state) => (type: 'rooms' | 'cases') => {
      const timestamp = state.cacheTimestamps[type]
      return timestamp > 0 && (Date.now() - timestamp) < 5 * 60 * 1000
    }
  },

  actions: {
    // 获取房间列表
    async fetchRooms(force = false) {
      if (!force && this.isCacheValid('rooms') && this.rooms.length > 0) {
        // console.log('[BattleStore] 使用缓存的房间列表')
        return this.rooms
      }

      try {
        this.loading.rooms = true
        this.errors.rooms = null

        // console.log('[BattleStore] 获取房间列表')
        const response = await battleApi.getRoomList({
          state_list: this.filters.states,
          page: 1,
          page_size: 50
        })
        
        if (response.success) {
          this.rooms = response.data
          this.cacheTimestamps.rooms = Date.now()
          // console.log(`[BattleStore] 房间列表获取成功: ${this.rooms.length}个房间`)
          return this.rooms
        } else {
          throw new Error(response.message || '获取房间列表失败')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '获取房间列表失败'
        this.errors.rooms = errorMessage
        // console.error('[BattleStore] 获取房间列表失败:', error)
        return []
      } finally {
        this.loading.rooms = false
      }
    },

    // 获取箱子列表
    async fetchCases(force = false) {
      if (!force && this.isCacheValid('cases') && this.availableCases.length > 0) {
        // console.log('[BattleStore] 使用缓存的箱子列表')
        return this.availableCases
      }

      try {
        this.loading.cases = true
        this.errors.cases = null

        // console.log('[BattleStore] 获取对战箱子列表')
        const response = await battleApi.getBattleCases()
        
        if (response.success) {
          this.availableCases = response.data
          this.cacheTimestamps.cases = Date.now()
          // console.log(`[BattleStore] 箱子列表获取成功: ${this.availableCases.length}个箱子`)
          return this.availableCases
        } else {
          throw new Error(response.message || '获取箱子列表失败')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '获取箱子列表失败'
        this.errors.cases = errorMessage
        // console.error('[BattleStore] 获取箱子列表失败:', error)
        return []
      } finally {
        this.loading.cases = false
      }
    },

    // 获取房间详情
    async fetchRoomDetail(roomUid: string, force = false) {
      if (!force && this.currentRoom?.uid === roomUid) {
        // console.log('[BattleStore] 使用缓存的房间详情')
        return this.currentRoom
      }

      try {
        this.loading.currentRoom = true
        this.errors.currentRoom = null

        // console.log(`[BattleStore] 获取房间详情: ${roomUid}`)
        const response = await battleApi.getRoomDetail(roomUid)
        
        if (response.success && response.data) {
          this.currentRoom = response.data
          // console.log(`[BattleStore] 房间详情获取成功: ${roomUid}`)
          return this.currentRoom
        } else {
          throw new Error(response.message || '获取房间详情失败')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '获取房间详情失败'
        this.errors.currentRoom = errorMessage
        // console.error('[BattleStore] 获取房间详情失败:', error)
        return null
      } finally {
        this.loading.currentRoom = false
      }
    },

    // 加入房间
    async joinRoom(roomUid: string) {
      try {
        this.loading.joining = true
        this.errors.joining = null

        // console.log(`[BattleStore] 加入房间: ${roomUid}`)
        const response = await battleApi.joinRoom(roomUid)
        
        if (response.success) {
          // console.log(`[BattleStore] 成功加入房间: ${roomUid}`)
          
          // 增量更新：更新房间的玩家数量
          const roomIndex = this.rooms.findIndex(r => r.uid === roomUid)
          if (roomIndex !== -1) {
            this.rooms[roomIndex] = { 
              ...this.rooms[roomIndex], 
              joiner_count: this.rooms[roomIndex].joiner_count + 1,
              update_time: new Date().toISOString()
            }
          }
          
          // 如果有返回的房间数据，使用它来更新
          if (response.data) {
            this.handleSocketRoomUpdate(response.data)
          }
          
          return { success: true, data: response.data }
        } else {
          throw new Error(response.message || '加入房间失败')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '加入房间失败'
        this.errors.joining = errorMessage
        // console.error('[BattleStore] 加入房间失败:', error)
        return { success: false, message: errorMessage }
      } finally {
        this.loading.joining = false
      }
    },

    // 创建房间
    async createRoom(casesKey: string[], maxJoiner: number, isPrivate = false) {
      try {
        this.loading.creating = true
        this.errors.creating = null

        // console.log('[BattleStore] 创建房间:', { casesKey, maxJoiner, isPrivate })
        const response = await battleApi.createBattle({
          cases_key: casesKey,
          max_joiner: maxJoiner,
          private: isPrivate ? 1 : 0
        })
        
        if (response.success) {
          // console.log('[BattleStore] 房间创建成功')
          
          // 增量更新：将新创建的房间添加到列表开头
          if (response.data) {
            this.handleSocketNewRoom(response.data)
          }
          
          return { success: true, data: response.data }
        } else {
          throw new Error(response.message || '创建房间失败')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '创建房间失败'
        this.errors.creating = errorMessage
        // console.error('[BattleStore] 创建房间失败:', error)
        return { success: false, message: errorMessage }
      } finally {
        this.loading.creating = false
      }
    },

    // 离开房间
    async leaveRoom(roomUid: string) {
      try {
        // console.log(`[BattleStore] 离开房间: ${roomUid}`)
        const response = await battleApi.leaveRoom(roomUid)
        
        if (response.success) {
          // console.log(`[BattleStore] 成功离开房间: ${roomUid}`)
          
          // 清除当前房间信息
          if (this.currentRoom?.uid === roomUid) {
            this.currentRoom = null
          }
          
          // 增量更新：从房间列表中移除该房间或更新其状态
          const roomIndex = this.rooms.findIndex(r => r.uid === roomUid)
          if (roomIndex !== -1) {
            // 如果房间还有其他玩家，更新玩家数量；否则移除房间
            const room = this.rooms[roomIndex]
            if (room.joiner_count > 1) {
              // 减少玩家数量
              this.rooms[roomIndex] = { 
                ...room, 
                joiner_count: room.joiner_count - 1,
                update_time: new Date().toISOString()
              }
            } else {
              // 移除房间
              this.rooms.splice(roomIndex, 1)
            }
          }
          
          return { success: true }
        } else {
          throw new Error(response.message || '离开房间失败')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '离开房间失败'
        // console.error('[BattleStore] 离开房间失败:', error)
        return { success: false, message: errorMessage }
      }
    },

    // Socket消息处理：新房间
    handleSocketNewRoom(roomData: any) {
      try {
        // console.log('[🎰BATTLE-SOCKET] 🆕 新房间创建:', {
        //   uid: roomData?.uid,
        //   shortId: roomData?.short_id,
        //   creator: roomData?.user?.profile?.nickname,
        //   state: roomData?.state,
        //   maxJoiner: roomData?.max_joiner,
        //   price: roomData?.price,
        //   casesCount: roomData?.rounds?.length
        // })
        
        // 检查是否已存在
        const existingRoom = this.rooms.find(r => r.uid === roomData.uid)
        if (existingRoom) {
          // console.log('[🎰BATTLE-SOCKET] ⚠️ 房间已存在，跳过添加:', roomData.uid)
          return
        }
        
        // 为所有现有房间添加"被挤压"标记
        this.rooms.forEach(room => {
          (room as any)._isPushed = true
        })
        
        // 添加新房间标记用于动画
        const newRoom = {
          ...roomData,
          _isNewRoom: true, // 临时标记，用于触发新房间动画
          _animationTimestamp: Date.now(),
          _insertIndex: 0 // 插入位置标记
        } as any
        
        // 添加到房间列表开头（新房间优先显示）
        this.rooms.unshift(newRoom)
        
        // 限制房间数量，保留最新的100个
        if (this.rooms.length > 100) {
          this.rooms = this.rooms.slice(0, 100)
        }
        
        // console.log(`[🎰BATTLE-SOCKET] ✅ Socket添加新房间: ${roomData.uid}, 当前房间总数: ${this.rooms.length}`)
        
        // 使用 Promise 和延迟来替代 setTimeout，避免全局污染问题
        this.scheduleAnimationCleanup(roomData.uid, 1500)
        
        // 触发自定义事件，通知页面有新房间
        if (typeof window !== 'undefined') {
          window.dispatchEvent(new CustomEvent('battle-room-added', {
            detail: { room: newRoom, type: 'new', insertIndex: 0 }
          }))
        }
      } catch (error) {
        console.error('[🎰BATTLE-SOCKET] ❌ 处理Socket新房间失败:', error)
      }
    },

    // Socket消息处理：房间更新
    handleSocketRoomUpdate(roomData: any) {
      try {
        // console.log('[🎰BATTLE-SOCKET] 🔄 房间状态更新:', {
        //   uid: roomData?.uid,
        //   shortId: roomData?.short_id,
        //   state: roomData?.state,
        //   joinerCount: roomData?.joiner_count,
        //   maxJoiner: roomData?.max_joiner,
        //   roundCurrent: roomData?.round_count_current,
        //   updateTime: roomData?.update_time
        // })
        
        // 获取更新前的房间状态
        const roomIndex = this.rooms.findIndex(r => r.uid === roomData.uid)
        const oldRoom = roomIndex !== -1 ? this.rooms[roomIndex] : null
        
        // 检查满员状态变化（基于后端状态，不手动计算）
        const isNewlyFull = oldRoom && oldRoom.state !== GameState.Full && roomData.state === GameState.Full
        
        // 更新房间列表中的房间
        if (roomIndex !== -1) {
          // 检查人数变化
          const hadUserJoined = oldRoom && roomData.joiner_count > oldRoom.joiner_count
          const hadUserLeft = oldRoom && roomData.joiner_count < oldRoom.joiner_count
          
          // 确定更新类型
          let updateType = 'state'
          if (isNewlyFull) {
            updateType = 'full'
          } else if (hadUserJoined) {
            updateType = 'joined'
          } else if (hadUserLeft) {
            updateType = 'left'
          }
          
          // 添加更新动画标记
          const updatedRoom = { 
            ...this.rooms[roomIndex], 
            ...roomData,
            _isUpdated: true,
            _updateType: updateType,
            _updateTimestamp: Date.now(),
            // 用户变化时添加特殊标记
            _hasNewJoiner: hadUserJoined,
            _hasUserLeft: hadUserLeft,
            _joinerDiff: hadUserJoined ? (roomData.joiner_count - oldRoom.joiner_count) : 
                        hadUserLeft ? (oldRoom.joiner_count - roomData.joiner_count) : 0
          } as any
          
          this.rooms[roomIndex] = updatedRoom
          
          // 根据更新类型设置不同的动画持续时间
          const animationDuration = (hadUserJoined || hadUserLeft) ? 1500 : 1000 // 人数变化动画持续更久
          this.scheduleUpdateAnimationCleanup(roomData.uid, animationDuration)
          
          // 输出详细的人数变化日志
          if (hadUserJoined) {
            console.log(`[🎰BATTLE-SOCKET] 👥 新用户加入房间: ${roomData.uid} (${oldRoom.joiner_count} → ${roomData.joiner_count})`)
          } else if (hadUserLeft) {
            console.log(`[🎰BATTLE-SOCKET] 👋 用户离开房间: ${roomData.uid} (${oldRoom.joiner_count} → ${roomData.joiner_count})`)
            console.log(`[🎰BATTLE-SOCKET] 📊 用户离开详情:`, {
              房间ID: roomData.uid,
              原人数: oldRoom.joiner_count,
              现人数: roomData.joiner_count,
              离开人数: oldRoom.joiner_count - roomData.joiner_count,
              更新类型: updateType,
              动画标记: '_hasUserLeft=true'
            })
          }
          
          // console.log(`[🎰BATTLE-SOCKET] 📝 更新房间列表: ${roomData.uid}`)
        }
        
        // 更新当前房间
        if (this.currentRoom?.uid === roomData.uid) {
          this.currentRoom = { ...this.currentRoom, ...roomData }
          // console.log(`[🎰BATTLE-SOCKET] 🎯 更新当前房间: ${roomData.uid}`)
        }
        
        // 触发自定义事件，通知页面房间更新
        if (typeof window !== 'undefined') {
          window.dispatchEvent(new CustomEvent('battle-room-updated', {
            detail: { 
              room: roomData, 
              type: 'update',
              isNewlyFull,
              updateType: isNewlyFull ? 'full' : 'general'
            }
          }))
        }
      } catch (error) {
        console.error('[🎰BATTLE-SOCKET] ❌ 处理Socket房间更新失败:', error)
      }
    },

    // Socket消息处理：对战开始
    handleSocketRoomStart(roomData: any) {
      try {
        // console.log('[🎰BATTLE-SOCKET] 🚀 对战开始:', {
        //   uid: roomData?.uid,
        //   shortId: roomData?.short_id,
        //   state: roomData?.state,
        //   joinerCount: roomData?.joiner_count
        // })
        
        // 更新房间列表中的房间
        const roomIndex = this.rooms.findIndex(r => r.uid === roomData.uid)
        if (roomIndex !== -1) {
          this.rooms[roomIndex] = { 
            ...this.rooms[roomIndex], 
            ...roomData,
            state: GameState.Running,
            _isUpdated: true,
            _updateType: 'start',
            _updateTimestamp: Date.now()
          } as any
          
          this.scheduleUpdateAnimationCleanup(roomData.uid, 1000)
        }
        
        // 更新当前房间
        if (this.currentRoom?.uid === roomData.uid) {
          this.currentRoom = { ...this.currentRoom, ...roomData, state: GameState.Running }
        }
        
        // 触发自定义事件，通知页面对战开始
        if (typeof window !== 'undefined') {
          window.dispatchEvent(new CustomEvent('battle-room-started', {
            detail: { room: roomData, type: 'start' }
          }))
        }
      } catch (error) {
        console.error('[🎰BATTLE-SOCKET] ❌ 处理Socket对战开始失败:', error)
      }
    },

    // Socket消息处理：房间取消
    handleSocketRoomCancel(roomData: any) {
      try {
        console.log('[🎰BATTLE-SOCKET] ❌ 房间取消:', {
          uid: roomData?.uid,
          shortId: roomData?.short_id,
          state: roomData?.state,
          cancelReason: roomData?.cancel_reason
        })
        
        console.log(`[🎰BATTLE-SOCKET] 🗑️ 房主解散房间: ${roomData.uid}`)
        console.log(`[🎰BATTLE-SOCKET] 📊 房间解散详情:`, {
          房间ID: roomData.uid,
          房间状态: roomData.state,
          取消原因: roomData.cancel_reason || '房主主动解散',
          解散时间: roomData.cancel_time || new Date().toISOString(),
          处理方式: '添加闪烁动画后移除'
        })
        
        // 先添加闪烁动画，然后移除房间
        this.scheduleRoomRemoval(roomData.uid, roomData)
        
        // 触发自定义事件，通知页面房间取消
        if (typeof window !== 'undefined') {
          window.dispatchEvent(new CustomEvent('battle-room-cancelled', {
            detail: { room: roomData, type: 'cancel' }
          }))
        }
        
        // 如果是当前房间，清除
        if (this.currentRoom?.uid === roomData.uid) {
          this.currentRoom = null
          console.log(`[🎰BATTLE-SOCKET] 🏠 当前房间已清除: ${roomData.uid}`)
        }
      } catch (error) {
        console.error('[🎰BATTLE-SOCKET] ❌ 处理Socket房间取消失败:', error)
      }
    },

    // Socket消息处理：回合开启 (boxroomdetail round)
    handleSocketRoundStart(betData: any[], socketId?: string) {
      try {
        // console.log('[🎰BATTLE-SOCKET] 🎯 回合开启:', {
        //   playersCount: betData?.length,
        //   socketId,
        //   firstPlayer: betData?.[0]?.user?.username
        // })
        
        // 触发自定义事件，通知详情页面回合开始
        if (typeof window !== 'undefined') {
          window.dispatchEvent(new CustomEvent('battle-round-started', {
            detail: { 
              betData, 
              socketId, 
              type: 'round' 
            }
          }))
        }
      } catch (error) {
        console.error('[🎰BATTLE-SOCKET] ❌ 处理Socket回合开启失败:', error)
      }
    },

    // Socket消息处理：对战结束 (boxroomdetail end)
    handleSocketBattleEnd(betData: any[], socketId?: string) {
      try {
        // console.log('[🎰BATTLE-SOCKET] 🏆 对战结束:', {
        //   playersCount: betData?.length,
        //   socketId,
        //   winner: betData?.find(p => p.victory)?.user?.username
        // })
        
        // 查找获胜者
        const winner = betData?.find(player => player.victory)
        
        // 触发自定义事件，通知详情页面对战结束
        if (typeof window !== 'undefined') {
          window.dispatchEvent(new CustomEvent('battle-ended', {
            detail: { 
              betData, 
              socketId, 
              winner,
              type: 'end' 
            }
          }))
        }
      } catch (error) {
        console.error('[🎰BATTLE-SOCKET] ❌ 处理Socket对战结束失败:', error)
      }
    },

    // Socket消息处理：房间结束
    handleSocketRoomEnd(roomData: any) {
      try {
        // console.log('[🎰BATTLE-SOCKET] 🏁 房间结束:', {
        //   uid: roomData?.uid,
        //   shortId: roomData?.short_id,
        //   state: roomData?.state,
        //   winner: roomData?.winner?.profile?.nickname
        // })
        
        // 先添加闪烁动画，然后移除房间
        this.scheduleRoomRemoval(roomData.uid, roomData)
        
        // 触发自定义事件，通知页面房间结束
        if (typeof window !== 'undefined') {
          window.dispatchEvent(new CustomEvent('battle-room-ended', {
            detail: { room: roomData, type: 'ended' }
          }))
        }
        
        // 如果是当前房间，标记为已结束但保留显示
        if (this.currentRoom?.uid === roomData.uid) {
          this.currentRoom = { ...this.currentRoom, ...roomData, state: GameState.End }
          // console.log('[🎰BATTLE-SOCKET] 当前房间已结束，保留结果显示')
        }
      } catch (error) {
        console.error('[🎰BATTLE-SOCKET] ❌ 处理Socket房间结束失败:', error)
      }
    },

    // Socket消息处理：移除房间
    handleSocketRoomRemove(roomUid: string) {
      try {
        // console.log('[BattleStore] Socket移除房间:', roomUid)
        
        // 先添加闪烁动画，然后移除房间
        this.scheduleRoomRemoval(roomUid)
        
        // 如果是当前房间，清除
        if (this.currentRoom?.uid === roomUid) {
          this.currentRoom = null
          // console.log('[BattleStore] 清除当前房间')
        }
      } catch (error) {
        // console.error('[BattleStore] 处理Socket房间移除失败:', error)
      }
    },

    // 安排房间移除：先闪烁动画，再移除
    async scheduleRoomRemoval(roomUid: string, roomData?: any) {
      try {
        const roomIndex = this.rooms.findIndex(r => r.uid === roomUid)
        if (roomIndex === -1) return
        
        // 添加移除闪烁动画标记
        const roomToRemove = {
          ...this.rooms[roomIndex],
          ...(roomData || {}),
          _isRemoving: true,
          _removeTimestamp: Date.now()
        } as any
        
        this.rooms[roomIndex] = roomToRemove
        // console.log(`[🎰BATTLE-SOCKET] 🔥 开始房间移除动画: ${roomUid}`)
        
        // 2秒后实际移除房间
        await new Promise(resolve => {
          const timer = (globalThis as any).setTimeout || ((globalThis as any).window?.setTimeout)
          if (typeof timer === 'function') {
            timer(() => {
              const currentIndex = this.rooms.findIndex(r => r.uid === roomUid)
              if (currentIndex !== -1) {
                this.rooms.splice(currentIndex, 1)
                // console.log(`[🎰BATTLE-SOCKET] ✅ 房间已移除: ${roomUid}`)
              }
              resolve(undefined)
            }, 2000) // 2秒闪烁动画后移除
          } else {
            // 如果 setTimeout 不可用，立即移除
            const currentIndex = this.rooms.findIndex(r => r.uid === roomUid)
            if (currentIndex !== -1) {
              this.rooms.splice(currentIndex, 1)
            }
            resolve(undefined)
          }
        })
      } catch (error) {
        // console.error('[🎰BATTLE-SOCKET] ❌ 安排房间移除失败:', error)
      }
    },

    // 增量更新房间数据（避免重复代码）
    updateRoomIncrementally(roomUid: string, updates: Partial<BattleRoom>) {
      try {
        // 更新房间列表中的房间
        const roomIndex = this.rooms.findIndex(r => r.uid === roomUid)
        if (roomIndex !== -1) {
          this.rooms[roomIndex] = { ...this.rooms[roomIndex], ...updates }
          // console.log(`[BattleStore] 增量更新房间: ${roomUid}`, updates)
        }
        
        // 更新当前房间
        if (this.currentRoom?.uid === roomUid) {
          this.currentRoom = { ...this.currentRoom, ...updates }
          // console.log(`[BattleStore] 增量更新当前房间: ${roomUid}`)
        }
      } catch (error) {
        // console.error('[BattleStore] 增量更新房间失败:', error)
      }
    },

    // 设置筛选器
    setFilters(filters: Partial<BattleState['filters']>) {
      this.filters = { ...this.filters, ...filters }
      // console.log('[BattleStore] 更新筛选器:', this.filters)
    },

    // 设置搜索查询
    setSearchQuery(query: string) {
      this.filters.search = query
      // console.log('[BattleStore] 更新搜索查询:', query)
    },

    // 切换状态筛选
    toggleStateFilter(state: GameState) {
      const stateStr = String(state)
      const index = this.filters.states.indexOf(stateStr)
      if (index > -1) {
        this.filters.states.splice(index, 1)
      } else {
        this.filters.states.push(stateStr)
      }
      // console.log('[BattleStore] 切换状态筛选:', this.filters.states)
    },

    // 初始化对战数据
    async initializeBattleData() {
      // console.log('[BattleStore] 初始化对战数据')
      
      // 并行获取房间列表和箱子列表
      await Promise.all([
        this.fetchRooms(),
        this.fetchCases()
      ])
      
      // console.log('[BattleStore] 对战数据初始化完成')
    },

    // 设置选中的箱子
    setSelectedCases(casesKey: string[]) {
      this.selectedCases = casesKey
      // console.log('[BattleStore] 更新选中箱子:', casesKey)
    },

    // 添加选中箱子
    addSelectedCase(caseKey: string) {
      if (!this.selectedCases.includes(caseKey)) {
        this.selectedCases.push(caseKey)
        // console.log('[BattleStore] 添加选中箱子:', caseKey)
      }
    },

    // 移除选中箱子
    removeSelectedCase(caseKey: string) {
      const index = this.selectedCases.indexOf(caseKey)
      if (index !== -1) {
        this.selectedCases.splice(index, 1)
        // console.log('[BattleStore] 移除选中箱子:', caseKey)
      }
    },

    // 清除选中箱子
    clearSelectedCases() {
      this.selectedCases = []
      // console.log('[BattleStore] 清除所有选中箱子')
    },

    // 设置Socket监听器
    setupSocketListeners() {
      if (typeof window === 'undefined') return

      // console.log('[🎰BATTLE-STORE] 设置Socket监听器')
      
      // 监听新对战房间 - 对应Socket消息 ["boxroom", "new", data]
      window.addEventListener('socket-battle-new', (event: any) => {
        // console.log('[🎰BATTLE-STORE] 收到socket-battle-new事件:', event.detail)
        try {
          this.handleSocketNewRoom(event.detail?.data || event.detail)
        } catch (error) {
          // console.error('[🎰BATTLE-STORE] 处理新房间事件失败:', error)
        }
      })
      
      // 监听对战房间更新 - 对应Socket消息 ["boxroom", "update", data]  
      window.addEventListener('socket-battle-update', (event: any) => {
        // console.log('[🎰BATTLE-STORE] 收到socket-battle-update事件:', event.detail)
        try {
          this.handleSocketRoomUpdate(event.detail?.data || event.detail)
        } catch (error) {
          // console.error('[🎰BATTLE-STORE] 处理房间更新事件失败:', error)
        }
      })

      // 监听对战开始 - 对应Socket消息 ["boxroom", "start", data]
      window.addEventListener('socket-battle-start', (event: any) => {
        // console.log('[🎰BATTLE-STORE] 收到socket-battle-start事件:', event.detail)
        try {
          this.handleSocketRoomStart(event.detail?.data || event.detail)
        } catch (error) {
          console.error('[🎰BATTLE-STORE] 处理对战开始事件失败:', error)
        }
      })

      // 监听房间取消 - 对应Socket消息 ["boxroom", "cancel", data]
      window.addEventListener('socket-battle-cancel', (event: any) => {
        // console.log('[🎰BATTLE-STORE] 收到socket-battle-cancel事件:', event.detail)
        try {
          this.handleSocketRoomCancel(event.detail?.data || event.detail)
        } catch (error) {
          console.error('[🎰BATTLE-STORE] 处理房间取消事件失败:', error)
        }
      })
      
      // 监听对战房间结束
      window.addEventListener('socket-battle-end', (event: any) => {
        // console.log('[🎰BATTLE-STORE] 收到socket-battle-end事件:', event.detail)
        try {
          this.handleSocketRoomEnd(event.detail?.data || event.detail)
        } catch (error) {
          console.error('[🎰BATTLE-STORE] 处理房间结束事件失败:', error)
        }
      })

      // 监听回合开启 - 对应Socket消息 ["boxroomdetail", "round", betData, socketId]
      window.addEventListener('socket-battle-round', (event: any) => {
        // console.log('[🎰BATTLE-STORE] 收到socket-battle-round事件:', event.detail)
        try {
          const { betData, socketId } = event.detail || {}
          this.handleSocketRoundStart(betData, socketId)
        } catch (error) {
          console.error('[🎰BATTLE-STORE] 处理回合开启事件失败:', error)
        }
      })

      // 监听对战详情结束 - 对应Socket消息 ["boxroomdetail", "end", betData, socketId]
      window.addEventListener('socket-battle-detail-end', (event: any) => {
        // console.log('[🎰BATTLE-STORE] 收到socket-battle-detail-end事件:', event.detail)
        try {
          const { betData, socketId } = event.detail || {}
          this.handleSocketBattleEnd(betData, socketId)
        } catch (error) {
          console.error('[🎰BATTLE-STORE] 处理对战详情结束事件失败:', error)
        }
      })
      
      // 监听对战房间移除
      window.addEventListener('socket-battle-remove', (event: any) => {
        // console.log('[🎰BATTLE-STORE] 收到socket-battle-remove事件:', event.detail)
        try {
          const roomUid = event.detail?.uid || event.detail?.data?.uid || event.detail
          if (roomUid) {
            this.handleSocketRoomRemove(roomUid)
          } else {
            console.warn('[🎰BATTLE-STORE] socket-battle-remove事件缺少房间UID:', event.detail)
          }
        } catch (error) {
          console.error('[🎰BATTLE-STORE] 处理房间移除事件失败:', error)
        }
      })

      // 【新增】监听socket连接状态，连接成功后请求初始数据
      window.addEventListener('socket:connected', async () => {
        // console.log('[🎰BATTLE-STORE] Socket连接成功')
        // 不需要强制刷新，Socket会自动推送最新数据
        // 只有在没有数据时才获取初始数据
        try {
          if (this.rooms.length === 0) {
            await this.fetchRooms(false) // 仅在无数据时获取
          }
        } catch (error) {
          console.error('[🎰BATTLE-STORE] Socket连接后获取初始数据失败:', error)
        }
      })

      // 【新增】监听socket断开，清理状态
      window.addEventListener('socket:disconnected', () => {
        // console.log('[🎰BATTLE-STORE] Socket连接断开')
        // 可以选择清理数据或显示离线状态
      })

      // console.log('[🎰BATTLE-STORE] ✅ Socket监听器设置完成')
    },

    // 清除缓存
    clearCache() {
      this.rooms = []
      this.availableCases = []
      this.currentRoom = null
      this.cacheTimestamps.rooms = 0
      this.cacheTimestamps.cases = 0
      // console.log('[BattleStore] 清除所有缓存')
    },

    // 重置错误状态
    clearErrors() {
      this.errors = {
        rooms: null,
        cases: null,
        joining: null,
        creating: null,
        currentRoom: null
      }
    },

    // 使用 Promise 来安排动画清理，避免 setTimeout 污染问题
    async scheduleAnimationCleanup(roomUid: string, delay: number) {
      try {
        // 使用 Promise 和 setTimeout 的安全版本
        await new Promise(resolve => {
          const timer = (globalThis as any).setTimeout || ((globalThis as any).window?.setTimeout)
          if (typeof timer === 'function') {
            timer(resolve, delay)
          } else {
            // 如果 setTimeout 不可用，立即执行清理
            resolve(undefined)
          }
        })
        
        // 清理新房间动画标记
        const room = this.rooms.find(r => r.uid === roomUid)
        if (room && (room as any)._isNewRoom) {
          delete (room as any)._isNewRoom
          delete (room as any)._animationTimestamp
          delete (room as any)._insertIndex
          // console.log(`[🎰BATTLE-SOCKET] 🎭 移除新房间动画标记: ${roomUid}`)
        }
        
        // 移除所有房间的挤压标记
        this.rooms.forEach(room => {
          delete (room as any)._isPushed
        })
        // console.log('[🎰BATTLE-SOCKET] 🎭 移除所有房间挤压标记')
      } catch (error) {
        console.error('[🎰BATTLE-SOCKET] ❌ 动画清理失败:', error)
      }
    },

    // 安排更新动画清理
    async scheduleUpdateAnimationCleanup(roomUid: string, delay: number) {
      try {
        // 使用 Promise 和 setTimeout 的安全版本
        await new Promise(resolve => {
          const timer = (globalThis as any).setTimeout || ((globalThis as any).window?.setTimeout)
          if (typeof timer === 'function') {
            timer(resolve, delay)
          } else {
            // 如果 setTimeout 不可用，立即执行清理
            resolve(undefined)
          }
        })
        
        // 清理更新动画标记
        const room = this.rooms.find(r => r.uid === roomUid)
        if (room) {
          delete (room as any)._isUpdated
          delete (room as any)._updateType
          delete (room as any)._updateTimestamp
          // console.log(`[🎰BATTLE-SOCKET] 🎭 移除房间更新动画标记: ${roomUid}`)
        }
      } catch (error) {
        console.error('[🎰BATTLE-SOCKET] ❌ 更新动画清理失败:', error)
      }
    }
  }
})

// 向后兼容的别名导出
export const useBattleDetailStore = useBattleStore 