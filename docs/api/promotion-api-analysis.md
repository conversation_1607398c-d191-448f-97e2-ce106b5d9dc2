# Promotion 推广活动模块分析文档

## 模块概述

Promotion模块是平台的推广活动系统，实现了多层级的推荐返利机制。用户可以生成和分享推荐码，邀请其他用户注册充值，根据推荐人数和充值金额获得相应的推广收益和特权箱子。该模块是平台用户增长和留存的重要运营工具。

## 目录结构

```
promotion/
├── __init__.py
├── admin.py                 # Django管理后台配置
├── apps.py                  # 应用配置
├── business.py              # 核心业务逻辑
├── models.py                # 数据模型定义
├── serializers.py           # API序列化器
├── views.py                 # API视图
├── urls.py                  # URL路由配置
├── interfaces.py            # 接口定义
├── model_signals.py         # 数据库信号处理
├── service/                 # 服务层（空）
└── migrations/              # 数据库迁移文件
```

## 数据模型分析

### UserPromotion - 用户推广信息
```python
class UserPromotion(models.Model):
    user = models.OneToOneField(USER_MODEL)              # 关联用户
    code = models.CharField(max_length=32)               # 推荐码
    count = models.IntegerField(default=0)               # 推荐人数
    total_charge = models.FloatField(default=0.0)        # 推荐总充值
    total_profit = models.FloatField(default=0.0)        # 累计收益
    profit = models.FloatField(default=0.0)              # 可提现收益
    custom = models.BooleanField(default=False)          # 自定义推荐码
    level = models.IntegerField(default=0)               # 推广等级
    profit_rate = models.FloatField(default=0)           # 收益比例
```

**特点：**
- 每个用户一个推广账户，自动生成6位随机推荐码
- 支持自定义推荐码（4-8位字母数字）
- 分级返利机制，等级越高收益越高
- 收益需要满足条件才能提现

### PromotionRecord - 推广记录
```python
class PromotionRecord(ModelBase):
    ref = models.ForeignKey(UserPromotion)               # 推荐人
    user = models.OneToOneField(USER_MODEL)              # 被推荐人
    total_charge = models.FloatField(default=0.0)        # 累计充值
    profit = models.FloatField(default=0.0)              # 产生收益
    active = models.BooleanField(default=False)          # 激活状态
```

**特点：**
- 记录推荐关系，一对一关系防止重复绑定
- 跟踪被推荐人的充值和产生的收益
- 激活状态控制是否计入推荐收益

### PromotionLevelConfig - 推广等级配置
```python
class PromotionLevelConfig(models.Model):
    level = models.IntegerField(default=0)               # 等级
    profit_rate = models.FloatField(default=0)           # 收益比例
    refer_rate = models.FloatField(default=0)            # 推荐比例
    min_amount = models.FloatField(default=0)            # 最小充值金额
    max_amount = models.FloatField(default=0)            # 最大充值金额
```

**特点：**
- 基于充值金额的分级体系
- 不同等级享受不同的收益比例
- 支持推荐比例配置

### PromotionCaseConfig - 推广箱子配置
```python
class PromotionCaseConfig(models.Model):
    level = models.ForeignKey(PromotionLevelConfig)      # 关联等级
    case = models.ForeignKey(Case)                       # 关联箱子
```

**特点：**
- 不同推广等级解锁不同的特权箱子
- 提供除返利外的额外激励

## API 接口分析

### 1. 获取推广信息
**路径：** `GET /api/promotion/code/`

**功能：** 获取当前用户的推广账户信息

**响应格式：**
```json
{
    "code": "ABC123",
    "count": 5,
    "total_charge": 500.0,
    "total_profit": 50.0,
    "profit": 25.0,
    "level": 2,
    "profit_rate": 10.0
}
```

### 2. 获取推广记录
**路径：** `GET /api/promotion/record/`

**功能：** 获取用户的推广记录列表

**参数：**
- `page`: 页码
- `pageSize`: 每页数量

**响应格式：**
```json
{
    "records": [
        {
            "user": {
                "username": "user123",
                "avatar": "..."
            },
            "profit": 10.0
        }
    ],
    "total": 10
}
```

### 3. 设置推荐码
**路径：** `POST /api/promotion/setcode/`

**功能：** 设置自定义推荐码

**参数：**
```json
{
    "code": "MYCODE"
}
```

**限制：**
- 4-8位字母数字组合
- 不能与已有推荐码重复
- 转换为大写

### 4. 绑定推荐人
**路径：** `POST /api/promotion/bind/`

**功能：** 绑定推荐人关系

**参数：**
```json
{
    "ref_code": "ABC123"
}
```

**业务规则：**
- 每个用户只能绑定一次
- 不能绑定自己的推荐码
- 推荐码必须存在

### 5. 推荐预设置
**路径：** `POST /api/promotion/refer/`

**功能：** 用于未登录用户的推荐码预设置

**参数：**
```json
{
    "code": "ABC123"
}
```

**机制：**
- 未登录用户访问推荐链接时，推荐码存储在session中
- 用户注册登录后自动绑定推荐关系

### 6. 提取收益
**路径：** `POST /api/promotion/pick/`

**功能：** 提取推广收益到账户余额

**限制条件：**
- 邀请人数至少5人
- 收益按配置的最小单位提取

### 7. 推广配置
**路径：** `GET /api/promotion/config/`

**功能：** 获取推广等级配置信息

**响应格式：**
```json
{
    "config": [
        {
            "level": 1,
            "profit_rate": 5.0,
            "refer_rate": 10.0,
            "min_amount": 0,
            "max_amount": 100
        }
    ]
}
```

### 8. 推广箱子
**路径：** `GET /api/promotion/box/`

**功能：** 获取推广等级对应的特权箱子

**响应格式：**
```json
[
    {
        "level": {
            "level": 1,
            "profit_rate": 5.0
        },
        "case": {
            "key": "case_001",
            "name": "新手礼包"
        },
        "status": true
    }
]
```

## 核心业务逻辑分析

### 1. 推荐关系建立
```python
def create_promotion(user, ref_code):
    """建立推荐关系"""
    with transaction.atomic():
        # 查找推荐人
        refer = UserPromotion.objects.select_for_update().filter(code=ref_code).first()
        if not refer or refer.user == user:
            return "无效的推荐码"
        
        # 检查是否已绑定
        last_ref = PromotionRecord.objects.filter(user=user).first()
        if last_ref:
            return "您已绑定其他推荐人"
        
        # 创建推荐记录
        PromotionRecord.objects.create(ref=refer, user=user)
        
        # 更新推荐人统计
        refer.count += 1
        refer.save()
        
        return "绑定成功"
```

### 2. 收益计算与分配
```python
def calculate_promotion_profit(promoter, invitee_charge_amount):
    """计算推广收益"""
    # 获取推荐人等级配置
    level_config = PromotionLevelConfig.objects.filter(
        min_amount__lte=promoter.total_charge,
        max_amount__gte=promoter.total_charge
    ).first()
    
    if not level_config:
        return 0
    
    # 计算收益
    profit = invitee_charge_amount * level_config.profit_rate / 100
    
    # 更新推荐人收益
    promoter.profit += profit
    promoter.total_profit += profit
    promoter.save()
    
    # 更新推荐记录
    record = PromotionRecord.objects.filter(
        ref=promoter, 
        user=invitee
    ).first()
    if record:
        record.total_charge += invitee_charge_amount
        record.profit += profit
        record.active = True  # 激活推荐记录
        record.save()
    
    return profit
```

### 3. 等级升级机制
```python
def update_promotion_level(promoter):
    """更新推广等级"""
    # 根据总充值金额确定等级
    level_config = PromotionLevelConfig.objects.filter(
        min_amount__lte=promoter.total_charge,
        max_amount__gte=promoter.total_charge
    ).order_by('-level').first()
    
    if level_config and level_config.level > promoter.level:
        promoter.level = level_config.level
        promoter.profit_rate = level_config.profit_rate
        promoter.save()
        
        # 发送升级通知
        send_level_upgrade_notification(promoter.user, level_config)
```

### 4. 收益提取机制
```python
def user_pick_profit(user):
    """提取推广收益"""
    user_pro = UserPromotion.objects.filter(user=user).first()
    
    # 检查邀请人数
    record_count = PromotionRecord.objects.filter(
        ref=user_pro, 
        total_charge__gt=0
    ).count()
    if record_count < 5:
        return "邀请人数不足"
    
    with transaction.atomic():
        refer = UserPromotion.objects.select_for_update().filter(user=user).first()
        
        # 计算可提取金额（按最小单位）
        pick_unit = settings.PROMOTION_PROFIT_PICK_UNIT
        active_profit = refer.profit - refer.profit % pick_unit
        
        if pick_unit <= 0.01:
            active_profit = refer.profit
        
        if active_profit == 0:
            return "收益不足"
        
        # 扣除收益，增加余额
        refer.profit -= active_profit
        refer.user.update_balance(active_profit, '推广收益')
        refer.save()
        
        return "提取成功"
```

## 存在的问题与风险

### 1. 业务逻辑缺陷

**问题描述：**
- 缺少推荐码有效期管理
- 缺少反作弊机制
- 等级配置可能重叠导致计算错误
- 收益计算时机不明确

**影响：**
- 可能被恶意刷量
- 计算结果不准确
- 财务风险

### 2. 数据一致性问题

**问题描述：**
- 推广统计数据可能不一致
- 缺少收益变动日志
- 没有收益审核机制
- 提取记录缺失

**影响：**
- 数据准确性问题
- 难以追溯问题
- 用户纠纷处理困难

### 3. 性能问题

**问题描述：**
- 推广记录查询没有优化
- 等级配置缓存时间过长
- 收益计算可能频繁数据库操作
- 缺少必要索引

**影响：**
- 查询响应慢
- 数据库压力大
- 用户体验差

### 4. 功能不完整

**问题描述：**
- 缺少推广数据统计面板
- 缺少推广活动配置
- 缺少推广素材管理
- 缺少推广效果分析

**影响：**
- 运营工具不足
- 推广效果难以评估
- 用户推广积极性不高

## 改进建议

### 1. 数据模型增强

#### 推广活动配置
```python
class PromotionCampaign(ModelBase):
    """推广活动"""
    name = models.CharField(max_length=128)                    # 活动名称
    description = models.TextField(blank=True)                 # 活动描述
    start_time = models.DateTimeField()                        # 开始时间
    end_time = models.DateTimeField()                          # 结束时间
    is_active = models.BooleanField(default=True)             # 是否启用
    
    # 奖励配置
    base_reward = models.FloatField(default=0)                # 基础奖励
    milestone_rewards = models.JSONField(default=dict)        # 里程碑奖励
    
    # 限制条件
    max_participants = models.IntegerField(default=0)          # 最大参与人数
    min_invitee_charge = models.FloatField(default=0)         # 被邀请人最小充值
    
    class Meta:
        verbose_name = '推广活动'

class PromotionProfitLog(ModelBase):
    """推广收益日志"""
    user_promotion = models.ForeignKey(UserPromotion, on_delete=models.CASCADE)
    invitee = models.ForeignKey(USER_MODEL, on_delete=models.CASCADE)
    profit_type = models.CharField(max_length=50)             # 收益类型
    amount = models.FloatField()                              # 收益金额
    source_amount = models.FloatField()                       # 源金额
    profit_rate = models.FloatField()                         # 收益比例
    campaign = models.ForeignKey(PromotionCampaign, null=True, on_delete=models.SET_NULL)
    
    class Meta:
        verbose_name = '推广收益日志'

class PromotionWithdrawRecord(ModelBase):
    """推广提现记录"""
    user_promotion = models.ForeignKey(UserPromotion, on_delete=models.CASCADE)
    amount = models.FloatField()                              # 提现金额
    status = models.CharField(max_length=20, default='pending')  # 状态
    review_note = models.TextField(blank=True)                # 审核备注
    processed_at = models.DateTimeField(null=True)           # 处理时间
    
    class Meta:
        verbose_name = '推广提现记录'
```

#### 反作弊检测
```python
class PromotionAntiFraud(ModelBase):
    """反作弊检测"""
    user = models.ForeignKey(USER_MODEL, on_delete=models.CASCADE)
    risk_type = models.CharField(max_length=50)               # 风险类型
    risk_level = models.IntegerField(default=0)               # 风险等级
    detail = models.JSONField(default=dict)                   # 详细信息
    status = models.CharField(max_length=20, default='pending')  # 状态
    
    class Meta:
        verbose_name = '推广反作弊'
```

### 2. 业务逻辑重构

#### 智能等级计算
```python
class PromotionLevelCalculator:
    def __init__(self):
        self.cache_timeout = 3600  # 1小时缓存
    
    def calculate_user_level(self, user_promotion):
        """计算用户推广等级"""
        # 计算条件
        conditions = {
            'invitee_count': self.get_valid_invitee_count(user_promotion),
            'total_charge': user_promotion.total_charge,
            'active_invitees': self.get_active_invitee_count(user_promotion),
            'avg_invitee_charge': self.get_avg_invitee_charge(user_promotion)
        }
        
        # 匹配等级配置
        level_configs = PromotionLevelConfig.objects.all().order_by('-level')
        
        for config in level_configs:
            if self.check_level_requirements(conditions, config):
                return config
        
        return None
    
    def check_level_requirements(self, conditions, config):
        """检查等级要求"""
        # 邀请人数要求
        if hasattr(config, 'min_invitee_count'):
            if conditions['invitee_count'] < config.min_invitee_count:
                return False
        
        # 充值金额要求
        if conditions['total_charge'] < config.min_amount:
            return False
        
        if config.max_amount > 0 and conditions['total_charge'] > config.max_amount:
            return False
        
        # 活跃邀请人数要求
        if hasattr(config, 'min_active_invitees'):
            if conditions['active_invitees'] < config.min_active_invitees:
                return False
        
        return True
```

#### 动态收益计算
```python
class PromotionProfitCalculator:
    def __init__(self):
        self.base_rates = {
            'charge': 0.05,      # 充值返利5%
            'consumption': 0.02,  # 消费返利2%
            'recharge': 0.03     # 复充返利3%
        }
    
    def calculate_profit(self, promoter, invitee, action_type, amount):
        """计算推广收益"""
        # 获取推荐人等级
        level_config = self.get_promoter_level(promoter)
        if not level_config:
            return 0
        
        # 获取基础费率
        base_rate = self.base_rates.get(action_type, 0)
        
        # 应用等级倍数
        final_rate = base_rate * level_config.profit_rate / 100
        
        # 应用活动加成
        campaign_bonus = self.get_campaign_bonus(promoter, action_type)
        final_rate *= (1 + campaign_bonus)
        
        # 计算收益
        profit = amount * final_rate
        
        # 记录收益日志
        self.log_profit(promoter, invitee, action_type, amount, profit, final_rate)
        
        return profit
    
    def get_campaign_bonus(self, promoter, action_type):
        """获取活动加成"""
        active_campaigns = PromotionCampaign.objects.filter(
            is_active=True,
            start_time__lte=timezone.now(),
            end_time__gte=timezone.now()
        )
        
        total_bonus = 0
        for campaign in active_campaigns:
            bonus_config = campaign.bonus_config.get(action_type, {})
            if self.check_campaign_eligibility(promoter, campaign):
                total_bonus += bonus_config.get('rate_bonus', 0)
        
        return min(total_bonus, 1.0)  # 最大100%加成
```

#### 反作弊检测系统
```python
class PromotionFraudDetector:
    def __init__(self):
        self.risk_rules = [
            self.check_ip_clustering,
            self.check_device_fingerprint,
            self.check_charge_pattern,
            self.check_behavior_similarity
        ]
    
    def analyze_promotion_risk(self, promoter, invitee):
        """分析推广风险"""
        risk_score = 0
        risk_details = []
        
        for rule in self.risk_rules:
            score, detail = rule(promoter, invitee)
            risk_score += score
            if detail:
                risk_details.append(detail)
        
        # 记录风险评估
        if risk_score > 50:  # 高风险阈值
            PromotionAntiFraud.objects.create(
                user=promoter.user,
                risk_type='high_risk_promotion',
                risk_level=risk_score,
                detail={'rules': risk_details, 'invitee': invitee.id}
            )
        
        return risk_score, risk_details
    
    def check_ip_clustering(self, promoter, invitee):
        """检查IP聚集性"""
        # 检查推荐人和被推荐人是否使用相同IP
        promoter_ips = self.get_user_recent_ips(promoter.user)
        invitee_ips = self.get_user_recent_ips(invitee)
        
        common_ips = set(promoter_ips) & set(invitee_ips)
        if common_ips:
            return 30, f"共同IP地址: {list(common_ips)}"
        
        return 0, None
    
    def check_charge_pattern(self, promoter, invitee):
        """检查充值模式"""
        # 检查被推荐人充值时间和金额是否异常
        recent_charges = self.get_user_recent_charges(invitee)
        
        if len(recent_charges) == 1:  # 只充值一次就不活跃
            charge_amount = recent_charges[0]['amount']
            if charge_amount < 10:  # 小额充值
                return 20, "小额单次充值后不活跃"
        
        return 0, None
```

### 3. 数据统计与分析

#### 推广效果分析
```python
class PromotionAnalytics:
    def __init__(self):
        self.redis_client = redis.Redis()
    
    def generate_promotion_report(self, user, start_date, end_date):
        """生成推广报告"""
        user_promotion = UserPromotion.objects.get(user=user)
        
        # 基础统计
        report = {
            'period': {'start': start_date, 'end': end_date},
            'invitee_stats': self.get_invitee_stats(user_promotion, start_date, end_date),
            'profit_stats': self.get_profit_stats(user_promotion, start_date, end_date),
            'level_progress': self.get_level_progress(user_promotion),
            'ranking': self.get_user_ranking(user_promotion)
        }
        
        return report
    
    def get_invitee_stats(self, user_promotion, start_date, end_date):
        """获取邀请统计"""
        invitees = PromotionRecord.objects.filter(
            ref=user_promotion,
            create_time__range=[start_date, end_date]
        )
        
        return {
            'new_invitees': invitees.count(),
            'active_invitees': invitees.filter(active=True).count(),
            'total_charge': invitees.aggregate(total=Sum('total_charge'))['total'] or 0,
            'avg_charge': invitees.aggregate(avg=Avg('total_charge'))['avg'] or 0
        }
    
    def get_profit_stats(self, user_promotion, start_date, end_date):
        """获取收益统计"""
        profit_logs = PromotionProfitLog.objects.filter(
            user_promotion=user_promotion,
            create_time__range=[start_date, end_date]
        )
        
        return {
            'total_profit': profit_logs.aggregate(total=Sum('amount'))['total'] or 0,
            'profit_by_type': profit_logs.values('profit_type').annotate(
                total=Sum('amount')
            ),
            'daily_profit': profit_logs.extra(
                select={'day': 'date(create_time)'}
            ).values('day').annotate(total=Sum('amount'))
        }
```

## 监控和运营

### 推广活动管理
```python
class PromotionCampaignManager:
    def __init__(self):
        self.notification_service = NotificationService()
    
    def create_time_limited_campaign(self, name, start_time, end_time, rewards):
        """创建限时推广活动"""
        campaign = PromotionCampaign.objects.create(
            name=name,
            start_time=start_time,
            end_time=end_time,
            milestone_rewards=rewards,
            is_active=True
        )
        
        # 通知所有推广用户
        self.notify_campaign_start(campaign)
        
        # 设置定时任务
        self.schedule_campaign_end(campaign)
        
        return campaign
    
    def process_milestone_rewards(self, user_promotion, milestone):
        """处理里程碑奖励"""
        active_campaigns = PromotionCampaign.objects.filter(
            is_active=True,
            start_time__lte=timezone.now(),
            end_time__gte=timezone.now()
        )
        
        for campaign in active_campaigns:
            rewards = campaign.milestone_rewards.get(str(milestone), {})
            if rewards:
                self.grant_milestone_reward(user_promotion, rewards, campaign)
```

## 总结

Promotion模块实现了基础的推荐返利功能，但在反作弊、数据统计、活动运营等方面存在不足。建议按照上述方案进行系统性改进，重点加强风险控制、数据分析和运营工具，提升推广系统的安全性和有效性。

## 接口清单

| 接口名称 | 方法 | 路径 | 状态 | 功能描述 |
|---------|------|------|------|----------|
| 获取推广信息 | GET | `/api/promotion/code/` | 正常 | 获取用户推广账户 |
| 获取推广记录 | GET | `/api/promotion/record/` | 正常 | 获取推广记录列表 |
| 设置推荐码 | POST | `/api/promotion/setcode/` | 正常 | 设置自定义推荐码 |
| 绑定推荐人 | POST | `/api/promotion/bind/` | 正常 | 绑定推荐关系 |
| 推荐预设置 | POST | `/api/promotion/refer/` | 正常 | 预设置推荐码 |
| 提取收益 | POST | `/api/promotion/pick/` | 正常 | 提取推广收益 |
| 推广配置 | GET | `/api/promotion/config/` | 正常 | 获取等级配置 |
| 推广箱子 | GET | `/api/promotion/box/` | 正常 | 获取特权箱子 |
| 绑定状态 | GET | `/api/promotion/bindstatus/` | 正常 | 获取绑定状态 |

**注：** 建议增加推广数据统计、活动管理、反作弊监控等管理接口。
