from django.db import models, transaction
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from sitecfg.models import SEO
from steambase.models import USER_MODEL
from decimal import Decimal

# 代理商
class Agent(models.Model):
    user = models.OneToOneField(USER_MODEL, on_delete=models.CASCADE, verbose_name=_('User'))
    agent_name = models.CharField(max_length=255, default='', verbose_name=_('Agent Name'))
    agent_phone = models.CharField(max_length=255, default='', null=True, blank=True, verbose_name=_('Agent Phone'))
    agent_email = models.EmailField(max_length=255, default='', null=True, blank=True, verbose_name=_('Agent Email'))
    agent_wechat = models.CharField(max_length=255, default='', null=True, blank=True, verbose_name=_('Agent Wechat'))
    agent_qq = models.Char<PERSON><PERSON>(max_length=255, default='', null=True, blank=True, verbose_name=_('Agent QQ'))
    agent_alipay = models.CharField(max_length=255, default='', null=True, blank=True, verbose_name=_('Agent Alipay'))
    agent_wechatpay = models.CharField(max_length=255, default='', null=True, blank=True, verbose_name=_('Agent Wechatpay'))
    agent_bankcard = models.CharField(max_length=255, default='', null=True, blank=True, verbose_name=_('Agent Bankcard'))
    agent_bank = models.CharField(max_length=255, default='', null=True, blank=True, verbose_name=_('Agent Bank'))
    agent_bankname = models.CharField(max_length=255, default='', null=True, blank=True, verbose_name=_('Agent Bankname'))
    balance = models.DecimalField(max_digits=10, decimal_places=2, default=0.0, verbose_name=_('Balance'))
    enable = models.BooleanField(default=True, verbose_name=_('Enable'))
    create_time = models.DateTimeField(auto_now_add=True, verbose_name=_('Create Time'))
    update_time = models.DateTimeField(auto_now=True, verbose_name=_('Update Time'))
    remark = models.TextField(verbose_name=_('Remark'), default='', null=True, blank=True)


    class Meta:
        db_table = 'agent'
        verbose_name = "代理"
        verbose_name_plural = _('Agents')

    def __str__(self):
        return self.agent_name
    
    def update_balance(self, amount, change_type, out_trade_no, remark=''):
        with transaction.atomic():
            amount = Decimal(amount)
            # 可以为负数
            # if self.balance + amount < 0:
            #     raise ValueError(_("Insufficient balance"))

            balance_before = self.balance
            self.balance += amount
            self.save()

            AgentBalanceRecord.objects.create(
                agent=self,
                balance_changed=amount,
                balance_before=balance_before,
                balance_after=self.balance,
                type=change_type,
                out_trade_no=out_trade_no,
                remark=remark                
            )




# 代理商结算申请订单
class AgentWithdrawalOrder(models.Model):
    agent = models.ForeignKey(Agent, on_delete=models.CASCADE, verbose_name = "代理")
    amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name=_('Amount'))
    status = models.IntegerField(choices=(
        (0, _('Init')),
        (1, _('Success')),
        (2, _('Failed')),
    ), default=0, verbose_name=_('Status'))
    pay_type = models.IntegerField(choices=(
        (0, _('Alipay')),
        (1, _('Wechatpay')),
        (2, _('Bankcard')),
    ), default=0, verbose_name=_('Pay Type'))
    remark = models.TextField(verbose_name=_('Remark'), default='', null=True, blank=True)
    create_time = models.DateTimeField(auto_now_add=True, verbose_name=_('Create Time'))
    update_time = models.DateTimeField(auto_now=True, verbose_name=_('Update Time'))

    class Meta:
        db_table = 'agent_withdrawal_order'
        verbose_name = "代理提现订单"
        verbose_name_plural = _('Agent Withdrawal Orders')

    def __str__(self):
        return self.agent.agent_name

# 余额变动记录
class AgentBalanceRecord(models.Model):
    agent = models.ForeignKey(Agent, on_delete=models.CASCADE, verbose_name = "代理")
    balance_changed = models.DecimalField(max_digits=10, decimal_places=2, default=0.0, verbose_name=_('Balance Changed'))
    balance_before = models.DecimalField(max_digits=10, decimal_places=2, default=0.0, verbose_name=_('Balance Before'))
    balance_after = models.DecimalField(max_digits=10, decimal_places=2, default=0.0, verbose_name=_('Balance After'))
    type = models.IntegerField(choices=(
        (0, _('User Recharge')),
        (1, _('User Withdrawal')),
        (2, _('Agent Withdrawal Order')),
        (3, _('Other')),
    ), verbose_name=_('Type'))
    out_trade_no = models.CharField(max_length=64, unique=True, null=True, blank=True, verbose_name=_('Out Trade No'))

    remark = models.TextField(verbose_name=_('Remark'), default='', null=True, blank=True)
    create_time = models.DateTimeField(auto_now_add=True, verbose_name=_('Create Time'))
    update_time = models.DateTimeField(auto_now=True, verbose_name=_('Update Time'))

    class Meta:
        db_table = 'agent_balance_record'
        verbose_name = "代理余额记录"
        verbose_name_plural = _('Agent Balance Records')

    def __str__(self):
        return self.agent.agent_name
    
class Article(models.Model):
    title = models.CharField(max_length=255, verbose_name=_('Title'))
    content = models.TextField(verbose_name = "内容")
    enable = models.BooleanField(default=True, verbose_name=_('Enable'))
    create_time = models.DateTimeField(auto_now_add=True, verbose_name=_('Create Time'))
    update_time = models.DateTimeField(auto_now=True, verbose_name=_('Update Time'))
    category = models.ForeignKey('ArticleCategory', on_delete=models.CASCADE, null=True, blank=True, verbose_name = "分类")
    user = models.ForeignKey(USER_MODEL, on_delete=models.CASCADE, verbose_name=_('User'), null=True, blank=True)

    class Meta:
        db_table = 'agent_article'
        verbose_name = "文章"
        verbose_name_plural = _('Articles')

    def __str__(self):
        return self.title
    
class ArticleCategory(models.Model):
    name = models.CharField(max_length=255, verbose_name=_('Name'))
    enable = models.BooleanField(default=True, verbose_name=_('Enable'))
    site = models.ForeignKey(SEO, on_delete=models.CASCADE, verbose_name=_('Site'), null=True, blank=True)
    user = models.ForeignKey(USER_MODEL, on_delete=models.CASCADE, verbose_name=_('User'), null=True, blank=True)

    class Meta:
        db_table = 'agent_article_category'
        verbose_name = "文章分类"
        verbose_name_plural = _('Article Categories')

    def __str__(self):
        return self.name

