/**
 * 战斗详情页案例物品管理
 * 管理箱子物品的获取、缓存和处理逻辑
 */
import { ref, watch } from 'vue'
import type { Ref } from 'vue'

export interface BattleDetailCaseItems {
  currentCaseItems: Ref<any[]>
  caseItemsCache: Ref<Map<string, any[]>>
  fetchCaseItems: (caseId: string) => Promise<any>
  originalFetchCaseItems: (caseId: string) => Promise<any>
  handleCurrentRoundChange: (newRound: number) => Promise<void>
}

export function useBattleDetailCaseItems(): BattleDetailCaseItems {
  const battleCore = useBattleCore()
  const roundManager = useBattleRoundManager()
  const memoryManager = useBattleMemoryManager()

  const currentCaseItems = ref<any[]>([])
  const caseItemsCache = ref<Map<string, any[]>>(new Map())

  // 🎯 原始的获取箱子物品函数（重命名用于调试）
  const originalFetchCaseItems = async (caseId: string) => {
    const cacheKey = `case_items_${caseId}`
    
    // 检查缓存
    if (caseItemsCache.value.has(cacheKey)) {
      console.log("[🎰BATTLE-PAGE] 使用缓存的箱子物品数据:", caseId)
      const cachedItems = caseItemsCache.value.get(cacheKey) || []
      currentCaseItems.value = cachedItems
      return { success: true, items: cachedItems, fromCache: true }
    }

    // 创建用于取消请求的标识（简化处理）
    const requestId = `case_items_${caseId}_${Date.now()}`
    
    try {
      console.log("[🎰BATTLE-PAGE] 开始获取箱子物品:", { caseId })
      
      const response: any = await $fetch(`/api/case/items/${caseId}`)

      if (response.code === 0 && response.body?.items) {
        const rawItems = response.body.items

        // 解析嵌套结构：将稀有度分组下的item_info提取为扁平数组
        const flattenedItems: any[] = []

        rawItems.forEach((rarityGroup: any) => {
          console.log("[🎰BATTLE-PAGE] 处理稀有度组:", {
            rarityName: rarityGroup.rarity_name,
            itemsCount: rarityGroup.items?.length || 0,
          })

          if (rarityGroup.items && Array.isArray(rarityGroup.items)) {
            rarityGroup.items.forEach((itemWrapper: any) => {
              if (itemWrapper.item_info) {
                // 将item_info与稀有度信息合并
                const flattenedItem = {
                  ...itemWrapper.item_info,
                  // 添加稀有度信息
                  rarity_id: rarityGroup.rarity_id,
                  rarity_name: rarityGroup.rarity_name,
                  rarity_name_en: rarityGroup.rarity_name_en,
                  rarity_name_zh_hans: rarityGroup.rarity_name_zh_hans,
                  rarity_color: rarityGroup.rarity_color,
                  // 添加概率信息
                  chance: itemWrapper.chance,
                  // 确保有正确的名称字段
                  name:
                    itemWrapper.item_info.name_zh_hans ||
                    itemWrapper.item_info.name_en ||
                    itemWrapper.item_info.name,
                  name_zh_hans:
                    itemWrapper.item_info.name_zh_hans ||
                    itemWrapper.item_info.name,
                  name_en:
                    itemWrapper.item_info.name_en || itemWrapper.item_info.name,
                  // 确保有正确的图片字段
                  image: itemWrapper.item_info.image,
                  // 确保有正确的价格字段
                  price:
                    itemWrapper.item_info.item_price?.price ||
                    itemWrapper.item_info.price,
                }
                flattenedItems.push(flattenedItem)
              }
            })
          }
        })

        console.log("[🎰BATTLE-PAGE] 解析后的物品数据:", {
          原始数据长度: rawItems.length,
          扁平化后长度: flattenedItems.length,
          前3个物品: flattenedItems.slice(0, 3).map((item) => ({
            name: item.name,
            rarity: item.rarity_name,
            price: item.price,
          })),
        })

        // 缓存结果
        caseItemsCache.value.set(cacheKey, flattenedItems)
        currentCaseItems.value = flattenedItems

        return { success: true, items: flattenedItems, fromCache: false }
      } else {
        console.error("[🎰BATTLE-PAGE] 获取箱子物品失败:", response)
        currentCaseItems.value = []
        return { success: false, error: response.message || '未知错误' }
      }
    } catch (error: any) {
      if (error.name === 'AbortError') {
        console.log("[🎰BATTLE-PAGE] 获取箱子物品请求被取消:", caseId)
        return { success: false, cancelled: true }
      }
      console.error("[🎰BATTLE-PAGE] 获取箱子物品异常:", error)
      currentCaseItems.value = []
      return { success: false, error: error.message || '网络错误' }
    }
  }

  // 🎯 调试包装器函数
  const fetchCaseItems = async (caseId: string) => {
    console.log("[🎯调试] fetchCaseItems 被调用:", {
      caseId,
      timestamp: new Date().toISOString(),
      cacheSize: caseItemsCache.value.size,
      currentItemsCount: currentCaseItems.value.length
    })

    try {
      const result = await originalFetchCaseItems(caseId)
      
      console.log("[🎯调试] fetchCaseItems 执行结果:", {
        caseId,
        success: result.success,
        itemsCount: result.items?.length || 0,
        fromCache: result.fromCache,
        error: result.error,
        timestamp: new Date().toISOString()
      })

      return result
    } catch (error) {
      console.error("[🎯调试] fetchCaseItems 执行异常:", {
        caseId,
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString()
      })
      throw error
    }
  }

  // 🎯 抽取处理当前轮次变化的逻辑
  const handleCurrentRoundChange = async (newRound: number) => {
    const displayCases = battleCore.battleState.displayCases.value || []
    
    const currentCase = displayCases.find(
      (caseItem: any) => caseItem.roundIndex === newRound - 1
    )

    if (!currentCase) {
      console.log("[🎰BATTLE-PAGE] 未找到当前轮次对应的箱子，清空物品数据")
      currentCaseItems.value = []
      return
    }

    console.log("[🎰BATTLE-PAGE] 获取轮次箱子物品:", {
      轮次: newRound,
      箱子名称: currentCase.name,
      箱子ID: currentCase.id,
    })

    try {
      await fetchCaseItems(currentCase.id)
    } catch (error) {
      console.error("[🎰BATTLE-PAGE] 获取轮次箱子物品失败:", error)
    }
  }

  // 🎯 监听当前轮次变化，自动获取箱子物品数据
  watch(
    () => roundManager.currentRound.value,
    async (newRound, oldRound) => {
      console.log("[🎰BATTLE-PAGE] currentRound变化:", { oldRound, newRound })

      if (!newRound) {
        console.log("[🎰BATTLE-PAGE] currentRound为空，清空物品数据")
        currentCaseItems.value = []
        return
      }

      const displayCases = battleCore.battleState.displayCases.value || []
      console.log(
        "[🎰BATTLE-PAGE] displayCases:",
        displayCases.map((c) => ({ name: c.name, roundIndex: c.roundIndex }))
      )

      // 🎯 关键修复：如果displayCases为空，等待数据加载完成
      if (displayCases.length === 0) {
        console.log("[🎰BATTLE-PAGE] displayCases为空，等待数据加载")
        // 等待100ms后重试，给battleData加载时间
        memoryManager.createTimer(() => {
          const retryDisplayCases = battleCore.battleState.displayCases.value || []
          if (retryDisplayCases.length > 0) {
            console.log("[🎰BATTLE-PAGE] 重试成功，displayCases已加载:", retryDisplayCases.length)
            // 递归调用自身处理数据
            const currentRound = roundManager.currentRound.value
            if (currentRound) {
              handleCurrentRoundChange(currentRound)
            }
          } else {
            console.warn("[🎰BATTLE-PAGE] 重试失败，displayCases仍为空")
          }
        }, 100)
        return
      }

      await handleCurrentRoundChange(newRound)
    },
    { immediate: true }
  )

  return {
    currentCaseItems,
    caseItemsCache,
    fetchCaseItems,
    originalFetchCaseItems,
    handleCurrentRoundChange,
  }
}
