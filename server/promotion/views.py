import logging

from django.utils.translation import gettext_lazy as _

from rest_framework.views import APIView
from rest_framework.permissions import AllowAny

from steambase.enums import RespCode
from steambase.utils import reformat_resp, current_user
from sitecfg.interfaces import get_maintenance
from promotion.models import PromotionRecord
from promotion.serializers import UserPromotionSerializer
from promotion.business import get_promotion_record, user_pick_profit, create_promotion, get_promotion_config
from promotion.business import set_promotion_code, get_promotion_box

_logger = logging.getLogger(__name__)


class UserPromotionView(APIView):

    def get(self, request, format=None):
        try:

            user = current_user(request)
            data = UserPromotionSerializer(user.promotion).data
            return reformat_resp(
                RespCode.Succeed.value,
                data,
                'Succeed'
            )
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetPromotionRecordView(APIView):

    def get(self, request):
        try:

            user = current_user(request)
            query = {
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            fields = ('user', 'profit')
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            code, resp = get_promotion_record(user, query, fields, page, page_size)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetPromotionConfigView(APIView):

    def get(self, request):
        try:

            query = {
                # 'type': 2
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            fields = ('level', 'profit_rate', 'refer_rate', 'min_amount', 'max_amount')
            code, resp = get_promotion_config(query, fields)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class UserReferView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        try:

            user = current_user(request)
            if user:
                ref_code = request.session.get('ref_code', None)
                if ref_code:
                    create_promotion(user, ref_code)
            else:
                ref_code = request.data.get('code', None)
                if ref_code:
                    request.session['ref_code'] = ref_code
            return reformat_resp(RespCode.Succeed.value, {}, 'Succeed')
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class UserBindPromotionView(APIView):

    def post(self, request):
        try:

            user = current_user(request)
            ref_code = request.data.get('ref_code', None)
            code, resp = create_promotion(user, ref_code)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class UserSetCodeView(APIView):

    def post(self, request):
        try:

            user = current_user(request)
            code = request.data.get('code', None)
            code, resp = set_promotion_code(user, code)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class UserPickProfitView(APIView):

    def post(self, request):
        try:

            user = current_user(request)
            code, resp = user_pick_profit(user)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class UserBindStatusView(APIView):

    def get(self, request):
        try:

            user = current_user(request)
            # resp = {}
            record = PromotionRecord.objects.filter(user=user).first()
            if record:
                ref_code = record.ref.code
                resp = {
                    "result": True,
                    "ref_code": ref_code
                }
            else:
                resp = {
                    "result": False,
                    "ref_code": None
                }
            return reformat_resp(RespCode.Succeed.value, resp, 'Succeed')
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetPromotionCaseList(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:

            user = current_user(request)
            code, resp = get_promotion_box(user)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')
