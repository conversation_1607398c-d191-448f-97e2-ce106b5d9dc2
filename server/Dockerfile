FROM python:3.8-slim

# 创建必要的目录
WORKDIR /app
RUN mkdir -p logs /etc/supervisord.d/ && \
    apt-get update && \
    apt-get install -y supervisor curl && \
    apt-get clean && rm -rf /var/lib/apt/lists/*

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive

# 复制项目文件到容器
COPY . .

# 安装Python依赖
RUN /usr/local/bin/python3 -m pip install --upgrade pip setuptools --default-timeout=300
RUN /usr/local/bin/pip install --no-cache-dir -r requirements.txt --default-timeout=300

# 拷贝 supervisord 配置文件
COPY supervisord.conf /etc/supervisord.conf

# 启动命令
CMD ["sh", "-c", "python3 manage.py migrate && exec supervisord -c /etc/supervisord.conf"]