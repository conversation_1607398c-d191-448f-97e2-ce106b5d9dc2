import logging
import re


from django.conf import settings
from django.utils.translation import gettext_lazy as _

from rest_framework.views import APIView
from rest_framework.permissions import AllowAny
from rest_framework.response import Response

from steambase.enums import RespCode
from steambase.utils import reformat_resp, current_user
from sitecfg.interfaces import get_maintenance, get_maintenance_deposit, get_maintenance_withdraw
from sitecfg.interfaces import get_maintenance_msg_deposit, get_maintenance_msg_withdraw
from sitecfg.interfaces import get_maintenance_exchange, get_maintenance_shop
from package.business import get_user_inventory, get_user_package, get_trade_record, get_shop_package, user_exchange_all_items
from package.business import user_deposit_items, user_withdraw_items, user_exchange_items, user_buy_items, get_item_detail, get_items_new, get_items_update, get_items_expensive, get_items_relate, search_items, get_random_items, get_type_items, get_tag_items, get_user_package_skin
from package.business import get_exchange_record, setup_sync_items_price_worker, sync_item_info_from_api, get_items_category, get_items_quality, get_items_rarity, get_items_exterior
from authentication.models import UserAsset
from package.tasks import delete_old_package_items


# 2025
from package.business import search_items


_logger = logging.getLogger(__name__)


class GetUserInventoryView(APIView):

    def get(self, request):
        try:

            user = current_user(request)
            steamid = user.steam.steamid
            appid = request.query_params.get('appid', '730')
            code, resp = get_user_inventory(steamid, appid)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')

class GetItemDetailView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            # 获取物品ID
            item_id = request.query_params.get('id')
            
            # 检查ID是否存在
            if not item_id:
                return reformat_resp(RespCode.InvalidParams.value, {}, _('物品ID不能为空'))
            
            # 构建查询参数
            query = {'id': item_id}
            
            # 定义需要返回的字段
            fields = (
                'id', 'name', 'name_en', 'name_zh_hans', 'image', 'item_price', 'item_category', 'item_quality', 'item_rarity', 'item_exterior', 'content', 'content_en', 'content_zh_hans', 'description', 'description_en', 'description_zh_hans', 'update_time'
            )
            
            # 调用业务逻辑函数
            code, resp = get_item_detail(query, fields)
            
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, _('成功'))
            else:
                return reformat_resp(code, {}, resp)
                
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, _('服务器异常'))

class GetNewItemsView(APIView):
    permission_classes = [AllowAny]
    
    def get(self, request):
        try:
            num = int(request.query_params.get('num', '24'))
            code, resp = get_items_new(num)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')
    
class GetUpdateItemsView(APIView):
    permission_classes = [AllowAny]
    
    def get(self, request):
        try:
            num = int(request.query_params.get('num', '36'))
            code, resp = get_items_update(num)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')
        
class GetExpensiveItemsView(APIView):
    permission_classes = [AllowAny]
    def get(self, request):
        try:
            num = int(request.query_params.get('num', '24'))
            code, resp = get_items_expensive(num)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')

class GetRelateItemsView(APIView):
    permission_classes = [AllowAny]
    
    def get(self, request):
        try:
            key = request.query_params.get('name', '').strip()
            if key == '':
                return reformat_resp(RespCode.Exception.value, {}, 'Invalid Param')
            
            # 使用正则表达式移除不需要的部分
            # condition_patterns = [
            #     r'\(战痕累累\)', r'\(破损不堪\)', r'\(久经沙场\)', 
            #     r'\(略有磨损\)', r'\(崭新出厂\)', r'（全息）', 
            #     r'（闪耀）', r'（闪亮）', r'（金色）', r'（透镜）'
            # ]
            # pattern = re.compile('|'.join(condition_patterns))
            # keywords = pattern.sub('', key)

            # 支持印花集不进行拆分，其他关键词按|拆分处理
            # if '印花集' not in key:
            #     keywords = key.split('|')
            
            # 调整 get_items_relate 函数以接受关键词列表
            code, resp = get_items_relate(key)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')

class GetUserPackageView(APIView):

    def get(self, request):
        try:
            user = current_user(request)

            query = {
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            fields = ('uid', 'market_name', 'market_hash_name', 'market_name_cn', 'icon_url', 'price', 'part', 'amount', 'state',
                      'source', 'case_name', 'case_cover', 'case_key', 'rarity_color', 'create_time')
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            order = request.query_params.get('order', '-update_time')
            _all = request.query_params.get('all', 0)
            name = request.query_params.get('q', None)
            code, resp = get_user_package(user, query, _all, fields, page, page_size, order, name)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')
    



class GetShopView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:

            query = {}
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            fields = ('uid', 'market_name', 'market_name_cn', 'icon_url', 'price')
            code, resp = get_shop_package(query, fields)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class UserDepositItemsView(APIView):

    def post(self, request):
        try:

            if get_maintenance_deposit():
                msg = get_maintenance_msg_deposit() or _('Deposit is under maintenance, please wait for a while.')
                return reformat_resp(RespCode.Maintenance.value, {}, msg)
            user = current_user(request)
            appid = request.data.get('appid', '570')
            assetids = request.data.get('assetids', [])
            code, resp = user_deposit_items(user, appid, assetids)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class UserWithdrawItemsView(APIView):

    def post(self, request):
        try:

            if get_maintenance_withdraw():
                msg = get_maintenance_msg_withdraw() or _('Withdraw is under maintenance, please wait for a while.')
                return reformat_resp(RespCode.Maintenance.value, {}, msg)
            user = current_user(request)
            uids = request.data.get('uids', [])
            code, resp = user_withdraw_items(user, uids)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetTradeOfferView(APIView):

    def get(self, request):
        try:

            user = current_user(request)
            query = {
                'uid': request.query_params.get('uid', None),
                'trade_type': request.query_params.get('trade_type', None),
                'state': request.query_params.get('state', None)
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            fields = ('uid', 'state', 'trade_no', 'security_code', 'amount', 'trade_time', 'trade_items')
            code, resp = get_trade_record(user, query, fields, page, page_size)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetExchangeRecordView(APIView):

    def get(self, request):
        try:

            user = current_user(request)
            assetid = request.query_params.get('assetid', None)
            query = {
                'uid': request.query_params.get('uid', None),
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            fields = ('uid', 'market_name', 'market_name_cn', 'price', 'create_time')
            code, resp = get_exchange_record(user, query, fields, page, page_size, assetid)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


# class UserExchangeItemsView(APIView):

#     def post(self, request):
#         try:

#             if get_maintenance_exchange():
#                 return reformat_resp(RespCode.Maintenance.value, {},
#                                      _('Exchange is under maintenance, please wait for a while.'))
#             user = current_user(request)
#             uids = request.data.get('uids', [])
#             code, resp = user_exchange_items(user, uids, settings.PACKAGE_ITEM_PRICE_RATE)

#             userblance = UserAsset.objects.filter(user=user).first()
#             if userblance:
#                 balance = userblance.balance  
#                 charge = userblance.total_charge_balance
#             if charge > 0 and balance > 10:
#                     user.update_box_chance_type('c')

#             if code == RespCode.Succeed.value:             
                                             
#                 return reformat_resp(code, resp, 'Succeed')
#             else:
#                 return reformat_resp(code, {}, resp)
                                    
#         except Exception as e:
#             _logger.exception(e)
#         return reformat_resp(RespCode.Exception.value, {}, 'Exception')
    
class UserExchangeItemsView(APIView):

    def post(self, request):
        try:
            maintenance_exchange = get_maintenance_exchange()
            if maintenance_exchange:
                return reformat_resp(RespCode.Maintenance.value, {},
                                     _('饰品售出子系统正在维护，请稍后再试。'))
            
            user = current_user(request)
            uids = request.data.get('uids', [])
            code, resp = user_exchange_items(user, uids, settings.PACKAGE_ITEM_PRICE_RATE)           
            
            
                
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
                                    
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, 'Exception')



class UserExchangeALLItemsView(APIView):

    def post(self, request):
        try:

            if get_maintenance_exchange():
                return reformat_resp(RespCode.Maintenance.value, {},
                                     _('Exchange is under maintenance, please wait for a while.'))
            user = current_user(request)
            code, resp = user_exchange_all_items(user, settings.PACKAGE_ITEM_PRICE_RATE)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class UserBuyItemsView(APIView):

    def post(self, request):
        try:

            if get_maintenance_shop():
                return reformat_resp(RespCode.Maintenance.value, {},
                                     _('Shop is under maintenance, please wait for a while.'))
            user = current_user(request)
            shopid = request.data.get('shopid', None)
            uids = request.data.get('uids', [])
            code, resp = user_buy_items(user, shopid, uids)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class SyncItemPriceView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            user = current_user(request)
            if user.is_superuser:
                setup_sync_items_price_worker()
                return reformat_resp(RespCode.Succeed.value, {}, 'Succeed')
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class SyncItemInfoView(APIView):

    def get(self, request):
        try:
            user = current_user(request)
            if user.is_superuser:
                appid = request.query_params.get('appid', None)
                contextid = request.query_params.get('contextid', None)
                classid = request.query_params.get('classid', None)
                sync_item_info_from_api(appid, contextid, classid)
                return reformat_resp(RespCode.Succeed.value, {}, 'Succeed')
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


    

class GetRandomItemsView(APIView):
    permission_classes = [AllowAny]
    
    def get(self, request):
        try:
            num = int(request.query_params.get('num', 24))
            domain = request.query_params.get('domain', None)
            # 排除的饰品类型，可能是多个，用逗号分隔
            exclude_types = request.query_params.get('excludeTypes', None)
            # exclude_types ='CSGO_Type_Spray,CSGO_Tool_Sticker'
            # 定义返回字段
            fields = ('id', 'name', 'name_en', 'name_zh_hans', 'market_name_cn', 
                      'market_hash_name', 'image', 'item_price', 'item_category', 
                      'item_quality', 'item_rarity', 'item_exterior')     
                        
            code, resp = get_random_items(num, domain, exclude_types, fields)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')   

class GetTypeItemsView(APIView):
    permission_classes = [AllowAny]
    
    def get(self, request):
        try:
            type = request.query_params.get('type', None)
            order = request.query_params.get('order', 'update_time')

            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            
            code, resp = get_type_items(type, page, page_size, order)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')   
    
 
class GetTagItemsView(APIView):
    permission_classes = [AllowAny]
    
    def get(self, request):
        try:
            keyword = request.query_params.get('keyword', None)
            type = request.query_params.get('type', None)

            order = request.query_params.get('order', 'update_time')

            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            
            code, resp = get_tag_items(keyword, type, page, page_size, order)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')   
    

class GetUserPackageSkinView(APIView):

    def get(self, request):
        try:
            user = current_user(request)

            fields = ('uid', 'state', 
                      'source', 'amount', 'create_time', 'update_time', 'item_info', 'case_info')
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            order = request.query_params.get('sort', '-update_time')
            _all = request.query_params.get('all', '0')
            name = request.query_params.get('q', None)

            code, resp = get_user_package_skin(user, _all, fields, page, page_size, order, name)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, 'Exception')
        
class DeleteUserPackageSkinView(APIView):

    permission_classes = [AllowAny]

    def get(self, request):
        """
        触发删除旧余额记录的任务并返回执行结果。
        """
        user = current_user(request)
            
        try:
            # 调用删除方法
            #if user.is_superuser:
            result = delete_old_package_items()
            
            if result:
                return Response({'code': 200, 'message': result}, status=200)
            else:
                return Response({'code': 200, 'message': 'No old records found'}, status=200)
            
        except Exception as e:
            _logger.exception(e)
            return Response({'code': 500, 'message': 'Exception occurred'}, status=500)

# 以下是2025新版

class GetItemsCategoryView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            fields = ('cate_id', 'cate_name', 'cate_name_en', 'cate_name_zh_hans', 'icon', 'parent', 'level')
            code, resp = get_items_category(fields)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')
    
class GetItemsQualityView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            fields = ('quality_id', 'quality_name', 'quality_name_en', 'quality_name_zh_hans', 'quality_color')
            code, resp = get_items_quality(fields)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')
    
class GetItemsRarityView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            fields = ('rarity_id', 'rarity_name', 'rarity_name_en', 'rarity_name_zh_hans', 'rarity_color')
            code, resp = get_items_rarity(fields)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')
    
class GetItemsExteriorView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            fields = ('exterior_id', 'exterior_name', 'exterior_name_en', 'exterior_name_zh_hans', 'exterior_color')
            code, resp = get_items_exterior(fields)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')
    

class SearchItemsView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            # 构建查询条件
            query = {}
            
            # 获取搜索关键词
            q = request.query_params.get('q', '')
            if q:
                query['q'] = q
            
            # 添加分类过滤条件
            if request.query_params.get('category'):
                query['item_category'] = request.query_params.get('category')
            
            # 添加品质过滤条件
            if request.query_params.get('quality'):
                query['item_quality'] = request.query_params.get('quality')
            
            # 添加稀有度过滤条件
            if request.query_params.get('rarity'):
                query['item_rarity'] = request.query_params.get('rarity')
            
            # 添加外观过滤条件
            if request.query_params.get('exterior'):
                query['item_exterior'] = request.query_params.get('exterior')
            
            # 处理分页和排序
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 24))
            # 默认排序 -item_price__price
            sort = request.query_params.get('sort', '-update_time')
            
            # 定义返回字段
            fields = ('id', 'name', 'name_en', 'name_zh_hans', 'market_name_cn', 
                      'market_hash_name', 'image', 'item_price', 'item_category', 
                      'item_quality', 'item_rarity', 'item_exterior')            
            # 调用业务逻辑函数
            code, resp = search_items(query, fields, page, page_size, sort)
            
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, _('成功'))
            else:
                return reformat_resp(code, {}, resp.get('message', _('搜索失败')))
                
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, _('服务器异常'))