#!/usr/bin/env python
import os
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')
django.setup()

from authentication.models import AuthUser
from box.models import <PERSON>Room, CaseRoomBet, CaseRoomRound
from steambase.enums import GameState
from django.utils import timezone
from datetime import timed<PERSON><PERSON>

def debug_user_rooms():
    print("=" * 60)
    print("调试用户房间状态")
    print("=" * 60)
    
    # 查找指定邮箱的用户
    user = AuthUser.objects.filter(email='<EMAIL>').first()
    if not user:
        print('❌ 未找到邮箱为 <EMAIL> 的用户')
        return
    
    print(f'✅ 找到用户: {user.username} (ID: {user.id})')
    
    # 查找该用户创建的满员房间
    full_rooms = CaseRoom.objects.filter(
        user=user, 
        state=GameState.Full.value
    ).order_by('-create_time')
    
    print(f'\n📊 该用户创建的满员房间数量: {full_rooms.count()}')
    
    if not full_rooms.exists():
        print('没有找到满员房间')
        return
    
    print('\n🔍 满员房间详情分析:')
    print('-' * 60)
    
    for i, room in enumerate(full_rooms, 1):
        print(f'\n{i}. 房间 {room.short_id} ({room.uid}):')
        print(f'   📅 创建时间: {room.create_time}')
        print(f'   🔄 更新时间: {room.update_time}')
        print(f'   ⏰ 房间年龄: {timezone.now() - room.create_time}')
        print(f'   👥 最大人数: {room.max_joiner}')
        print(f'   💰 价格: {room.price}')
        print(f'   🏷️  类型: {room.type}')
        print(f'   🔒 私有: {room.private}')
        
        # 检查参与者
        bets = CaseRoomBet.objects.filter(room=room)
        print(f'   👥 实际参与人数: {bets.count()}')
        
        if bets.exists():
            print('   📝 参与者列表:')
            for bet in bets:
                print(f'     • {bet.user.username} (下注: {bet.open_amount})')
        
        # 检查回合
        rounds = CaseRoomRound.objects.filter(room=room)
        print(f'   🎯 回合数量: {rounds.count()}')
        
        if rounds.exists():
            print('   📋 回合详情:')
            for round_obj in rounds:
                print(f'     • 回合 {round_obj.id}: 箱子={round_obj.case.name}, 已开启={round_obj.opened}')
        
        # 诊断问题
        print('   🔧 问题诊断:')
        
        # 检查是否人数匹配
        if bets.count() != room.max_joiner:
            print(f'     ⚠️  人数不匹配: 实际{bets.count()}人 vs 预期{room.max_joiner}人')
        else:
            print(f'     ✅ 人数匹配: {bets.count()}/{room.max_joiner}人')
        
        # 检查是否有未开启的回合
        unopened_rounds = rounds.filter(opened=False)
        if unopened_rounds.exists():
            print(f'     ✅ 有{unopened_rounds.count()}个未开启回合，可以进行游戏')
        else:
            print(f'     ⚠️  没有未开启的回合，无法开始游戏')
        
        # 检查房间年龄
        room_age = timezone.now() - room.create_time
        if room_age > timedelta(minutes=30):
            print(f'     ⚠️  房间过老: {room_age}，可能需要手动处理')
        else:
            print(f'     ✅ 房间年龄正常: {room_age}')
        
        print('-' * 60)
    
    # 总结
    print(f'\n📋 总结:')
    print(f'   • 总共 {full_rooms.count()} 个满员房间需要处理')
    
    # 检查是否所有房间都有问题
    problematic_rooms = []
    for room in full_rooms:
        bets = CaseRoomBet.objects.filter(room=room)
        rounds = CaseRoomRound.objects.filter(room=room, opened=False)
        
        issues = []
        if bets.count() != room.max_joiner:
            issues.append('人数不匹配')
        if not rounds.exists():
            issues.append('无未开启回合')
        
        room_age = timezone.now() - room.create_time
        if room_age > timedelta(minutes=30):
            issues.append('房间过老')
            
        if issues:
            problematic_rooms.append((room, issues))
    
    if problematic_rooms:
        print(f'   • {len(problematic_rooms)} 个房间有问题:')
        for room, issues in problematic_rooms:
            print(f'     - {room.short_id}: {", ".join(issues)}')
    else:
        print('   • 所有房间状态正常，可能是后台处理延迟')

if __name__ == '__main__':
    debug_user_rooms()
