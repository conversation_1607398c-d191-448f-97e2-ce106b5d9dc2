# API接口文档

## 接口概览

本项目提供RESTful API，基于Django REST Framework构建，支持JSON格式的请求和响应。

## 基础信息

### 接口地址
- **开发环境**: `http://localhost:8000/api/`
- **生产环境**: `https://your-domain.com/api/`

### 认证方式
- **Session认证**: 基于Django Session
- **CSRF保护**: 需要在请求头中包含CSRF Token

### 响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {
    // 响应数据
  }
}
```

### 状态码说明
- `200`: 成功
- `400`: 请求参数错误
- `401`: 未认证
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

## 用户认证模块 (auth)

### 用户注册/登录

#### Steam登录
```http
GET /api/auth/steam/
```

#### 手机号注册
```http
POST /api/auth/phone/register/
Content-Type: application/json

{
  "phone": "13800138000",
  "code": "123456",
  "password": "password123"
}
```

#### 手机号登录
```http
POST /api/auth/phone/login/
Content-Type: application/json

{
  "phone": "13800138000",
  "password": "password123"
}
```

#### 邮箱注册
```http
POST /api/auth/email/register/
Content-Type: application/json

{
  "email": "<EMAIL>",
  "code": "123456",
  "password": "password123"
}
```

#### 发送短信验证码
```http
POST /api/auth/phone/code/
Content-Type: application/json

{
  "phone": "13800138000",
  "type": "register"  // register, login, reset
}
```

#### 发送邮件验证码
```http
POST /api/auth/email/code/
Content-Type: application/json

{
  "email": "<EMAIL>",
  "type": "register"
}
```

### 用户信息管理

#### 获取用户信息
```http
GET /api/auth/userinfo/
```

响应示例:
```json
{
  "code": 200,
  "data": {
    "id": 1,
    "username": "player123",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "avatar": "https://example.com/avatar.jpg",
    "balance": "100.50",
    "level": 5,
    "steam_id": "76561198000000000"
  }
}
```

#### 设置用户名
```http
POST /api/auth/set/name/
Content-Type: application/json

{
  "name": "newusername"
}
```

#### 设置手机号
```http
POST /api/auth/set/phone/
Content-Type: application/json

{
  "phone": "13800138000",
  "code": "123456"
}
```

#### 设置Steam交易链接
```http
POST /api/auth/set/steamlink/
Content-Type: application/json

{
  "trade_url": "https://steamcommunity.com/tradeoffer/new/?partner=..."
}
```

## 开箱系统 (box)

### 箱子列表

#### 获取箱子列表
```http
GET /api/box/list/
```

查询参数:
- `page`: 页码 (默认1)
- `limit`: 每页数量 (默认20)
- `category`: 分类ID
- `tag`: 标签ID
- `sort`: 排序方式 (price_asc, price_desc, new, hot)

响应示例:
```json
{
  "code": 200,
  "data": {
    "count": 100,
    "results": [
      {
        "id": 1,
        "name": "AK-47箱子",
        "name_en": "AK-47 Case",
        "price": "10.00",
        "image": "https://example.com/case.jpg",
        "category": "步枪",
        "tags": ["热门", "新品"],
        "is_free": false,
        "discount": 0.9
      }
    ]
  }
}
```

#### 搜索箱子
```http
GET /api/box/search/
```

查询参数:
- `q`: 搜索关键词
- `category`: 分类
- `min_price`: 最低价格
- `max_price`: 最高价格

#### 获取箱子详情
```http
GET /api/box/detail/
```

查询参数:
- `id`: 箱子ID

响应示例:
```json
{
  "code": 200,
  "data": {
    "id": 1,
    "name": "AK-47箱子",
    "price": "10.00",
    "description": "包含多种AK-47皮肤",
    "items": [
      {
        "id": 1,
        "name": "AK-47 红线",
        "price": "50.00",
        "rarity": "保密",
        "probability": 0.26,
        "image": "https://example.com/ak47.jpg"
      }
    ]
  }
}
```

### 开箱操作

#### 开启箱子
```http
POST /api/box/open/
Content-Type: application/json

{
  "case_id": 1,
  "count": 1
}
```

响应示例:
```json
{
  "code": 200,
  "data": {
    "items": [
      {
        "id": 12345,
        "name": "AK-47 红线",
        "price": "50.00",
        "rarity": "保密",
        "image": "https://example.com/ak47.jpg"
      }
    ],
    "total_value": "50.00"
  }
}
```

### 对战模式

#### 创建对战房间
```http
POST /api/box/battle/create/
Content-Type: application/json

{
  "case_id": 1,
  "room_type": "1v1",  // 1v1, 1v2, 1v3
  "is_private": false
}
```

#### 加入对战房间
```http
POST /api/box/battle/join/
Content-Type: application/json

{
  "room_id": 123
}
```

#### 获取对战房间列表
```http
GET /api/box/battle/list/
```

#### 获取对战房间详情
```http
GET /api/box/battle/detail/
```

查询参数:
- `room_id`: 房间ID

## 背包系统 (package)

### 用户背包

#### 获取用户背包
```http
GET /api/package/package/
```

查询参数:
- `page`: 页码
- `limit`: 每页数量
- `quality`: 品质筛选
- `rarity`: 稀有度筛选
- `category`: 分类筛选

响应示例:
```json
{
  "code": 200,
  "data": {
    "count": 50,
    "total_value": "500.00",
    "results": [
      {
        "id": 12345,
        "name": "AK-47 红线",
        "price": "50.00",
        "quality": "久经沙场",
        "rarity": "保密",
        "category": "步枪",
        "image": "https://example.com/ak47.jpg",
        "can_trade": true,
        "acquired_at": "2023-12-01T10:00:00Z"
      }
    ]
  }
}
```

#### 出售物品
```http
POST /api/package/exchange/
Content-Type: application/json

{
  "item_ids": [12345, 12346]
}
```

### 物品信息

#### 搜索物品
```http
GET /api/package/skins/search/
```

查询参数:
- `q`: 搜索关键词
- `category`: 分类
- `quality`: 品质
- `rarity`: 稀有度
- `min_price`: 最低价格
- `max_price`: 最高价格

#### 获取物品详情
```http
GET /api/package/skin/detail/
```

查询参数:
- `id`: 物品ID

## 充值系统 (charge)

### 充值操作

#### 创建充值订单
```http
POST /api/charge/pay/
Content-Type: application/json

{
  "amount": "100.00",
  "payment_method": "alipay",  // alipay, wechat, etc.
  "return_url": "https://example.com/return"
}
```

响应示例:
```json
{
  "code": 200,
  "data": {
    "order_id": "ORDER_123456",
    "payment_url": "https://payment.example.com/pay",
    "qr_code": "data:image/png;base64,..."
  }
}
```

#### 查询充值状态
```http
GET /api/charge/status/
```

查询参数:
- `order_id`: 订单ID

#### 获取充值记录
```http
GET /api/charge/record/
```

查询参数:
- `page`: 页码
- `limit`: 每页数量
- `status`: 状态筛选

### 支付方式

支持的支付方式:
- `alipay`: 支付宝
- `wechat`: 微信支付
- `hppay`: 虎皮椒支付
- `jjpay`: 九嘉支付
- `cdkey`: CDKey充值

## 交易系统 (b2ctrade)

### 提取物品

#### 提取到Steam
```http
POST /api/b2c/withdraw/
Content-Type: application/json

{
  "item_ids": [12345, 12346],
  "trade_url": "https://steamcommunity.com/tradeoffer/new/?partner=..."
}
```

#### 批量提取
```http
POST /api/b2c/batch/
Content-Type: application/json

{
  "item_ids": [12345, 12346, 12347]
}
```

#### 取消提取
```http
POST /api/b2c/cancel/
Content-Type: application/json

{
  "trade_id": 789
}
```

#### 获取交易记录
```http
GET /api/b2c/record/
```

## 游戏模块

### Roll游戏 (roll)

#### 创建Roll房间
```http
POST /api/roll/create/
Content-Type: application/json

{
  "items": [12345, 12346],
  "min_value": "10.00",
  "max_players": 4
}
```

#### 加入Roll游戏
```http
POST /api/roll/bet/
Content-Type: application/json

{
  "room_id": 123,
  "items": [12347]
}
```

#### 获取Roll房间列表
```http
GET /api/roll/list/
```

### 崩盘游戏 (crash)

#### 获取当前游戏
```http
GET /api/crash/current/
```

#### 下注
```http
POST /api/crash/bet/
Content-Type: application/json

{
  "amount": "10.00",
  "auto_cashout": 2.0  // 自动退出倍数
}
```

#### 获取游戏历史
```http
GET /api/crash/history/
```

### 幸运盒子 (luckybox)

#### 获取幸运盒子
```http
GET /api/luckybox/
```

#### 尝试幸运盒子
```http
POST /api/luckybox/lucky/
Content-Type: application/json

{
  "item_ids": [12345, 12346]
}
```

## 聊天系统 (chat)

### 聊天消息

#### 获取聊天记录
```http
GET /api/chat/messages/
```

查询参数:
- `room`: 聊天室ID
- `limit`: 消息数量

#### 发送消息
```http
POST /api/chat/send/
Content-Type: application/json

{
  "room": "global",
  "message": "Hello world!",
  "type": "text"  // text, emoji, system
}
```

## WebSocket实时通信

### 连接地址
```
# 开发环境
ws://localhost:4000/socket.io/

# 生产环境
wss://socket.cs2.net.cn/socket.io/
```

### 事件类型

#### 客户端发送事件
- `join_room`: 加入房间
- `leave_room`: 离开房间
- `chat_message`: 发送聊天消息

#### 服务器推送事件
- `game_update`: 游戏状态更新
- `chat_message`: 新聊天消息
- `user_online`: 用户上线
- `box_opened`: 开箱结果

### 连接示例
```javascript
// 开发环境
const socket = io('http://localhost:4000');

// 生产环境
const socket = io('https://socket.cs2.net.cn');

// 加入房间
socket.emit('join_room', {
  room: 'game_room_123',
  user_id: 456
});

// 监听游戏更新
socket.on('game_update', (data) => {
  console.log('Game update:', data);
});

// 监听聊天消息
socket.on('chat_message', (data) => {
  console.log('New message:', data);
});
```

## 错误处理

### 错误响应格式
```json
{
  "code": 400,
  "message": "参数错误",
  "errors": {
    "field_name": ["错误详情"]
  }
}
```

### 常见错误码
- `400`: 请求参数错误
- `401`: 用户未认证
- `403`: 权限不足
- `404`: 资源不存在
- `429`: 请求频率限制
- `500`: 服务器内部错误

## 接口限流

### 限流规则
- **登录接口**: 每分钟5次
- **发送验证码**: 每分钟1次
- **开箱操作**: 每秒1次
- **聊天消息**: 每分钟30次

### 限流响应
```json
{
  "code": 429,
  "message": "请求过于频繁，请稍后再试",
  "retry_after": 60
}
```

## 开发调试

### 接口测试工具
推荐使用以下工具测试API:
- **Postman**: GUI工具，支持环境变量
- **curl**: 命令行工具
- **httpie**: 命令行工具，语法简洁

### 示例curl命令
```bash
# 获取用户信息
curl -X GET \
  http://localhost:8000/api/auth/userinfo/ \
  -H 'Cookie: sessionid=your_session_id'

# 开启箱子
curl -X POST \
  http://localhost:8000/api/box/open/ \
  -H 'Content-Type: application/json' \
  -H 'Cookie: sessionid=your_session_id' \
  -H 'X-CSRFToken: your_csrf_token' \
  -d '{"case_id": 1, "count": 1}'
```

## 更新日志

### v1.0.0 (2023-12-01)
- 初始版本发布
- 支持基础的开箱、交易、游戏功能

### v1.1.0 (2024-01-01)
- 新增自定义箱子功能
- 优化WebSocket通信
- 增加更多支付方式

### 待开发功能
- [ ] 移动端API优化
- [ ] GraphQL支持
- [ ] API版本控制
- [ ] 更详细的API文档
