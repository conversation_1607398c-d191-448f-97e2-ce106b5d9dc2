import { onMounted, onUnmounted } from 'vue'
import { useBattleAudioGlobal } from '~/composables/useBattleAudio'

export const useBattleAudioSync = () => {
  const { playBattleSequence } = useBattleAudioGlobal()

  const handleAnimationStart = () => {
    playBattleSequence.rollStart()
  }

  const handleDecelerate = (e: Event) => {
    const detail = (e as CustomEvent).detail || {}
    // detail could be betData array or object; find items
    let rare = false
    try {
      const results = detail.results || detail // flexible
      const items: any[] = Array.isArray(results) ? results.flatMap((r: any) => r.items || []) : []
      for (const item of items) {
        const rarityName = (item.item_rarity?.rarity_name || '').toLowerCase()
        if (['classified', 'covert', 'contraband', 'legendary', 'epic', 'rare'].includes(rarityName)) {
          rare = true
          break
        }
      }
    } catch {}

    // 播放掉落音效
    if (rare) {
      playBattleSequence.itemDrop('rare')
    } else {
      playBattleSequence.itemDrop('common')
    }
  }

  const handleBattleEnd = () => {
    playBattleSequence.battleComplete()
  }

  onMounted(() => {
    window.addEventListener('battle-animation-start', handleAnimationStart as any)
    window.addEventListener('battle:decelerate', handleDecelerate as any)
    window.addEventListener('battle:battle_end', handleBattleEnd as any)
  })

  onUnmounted(() => {
    window.removeEventListener('battle-animation-start', handleAnimationStart as any)
    window.removeEventListener('battle:decelerate', handleDecelerate as any)
    window.removeEventListener('battle:battle_end', handleBattleEnd as any)
  })
} 