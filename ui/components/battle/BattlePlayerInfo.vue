<template>
  <div class="player-header">
    <!-- 玩家头像 -->
    <div class="player-avatar-container">
      <img 
        :src="player.user?.profile?.avatar || '/demo/avatar1.png'" 
        :alt="player.user?.profile?.nickname"
        @error="handleImageError"
        class="player-avatar"
      />
      
      <!-- 状态徽章 -->
      <div class="player-badges">
        <div v-if="isHost" class="host-badge">
          <Icon name="heroicons:home" class="w-3 h-3" />
        </div>
        <div v-if="isWinner" class="winner-badge">
          <Icon name="heroicons:star" class="w-3 h-3" />
        </div>
        <div v-if="isOpening" class="opening-badge">
          <Icon name="heroicons:play" class="w-3 h-3" />
        </div>
      </div>
    </div>

    <!-- 玩家信息中心 -->
    <div class="player-info-center">
      <div class="player-name">{{ player.user?.profile?.nickname || `玩家${index + 1}` }}</div>

      <!-- 操作按钮 -->
      <BattlePlayerButtons 
        :is-battle-started="isBattleStarted"
        :is-battle-finished="isBattleFinished"
        :is-user-creator="isUserCreator"
        :is-user-joined="isUserJoined"
        :is-host="isHost"
        @start-battle="$emit('start-battle')"
        @view-result="$emit('view-result')"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import type { BattleBet } from "~/types/battle";

// 🎯 Props定义
interface Props {
  player: BattleBet;
  index: number;
  isHost: boolean;
  isOpening: boolean;
  isWinner?: boolean;
  isBattleStarted?: boolean;
  isBattleFinished?: boolean;
  isUserCreator?: boolean;
  isUserJoined?: boolean;
  hostUid?: string;
}

const props = withDefaults(defineProps<Props>(), {
  isWinner: false,
  isBattleStarted: false,
  isBattleFinished: false,
  isUserCreator: false,
  isUserJoined: false,
  hostUid: "",
});

// 🎯 Events
const emit = defineEmits<{
  "start-battle": [];
  "view-result": [];
}>();

// 🎯 事件处理
const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement;
  target.src = "/demo/avatar1.png";
};
</script>

<style lang="scss" scoped>
@use "~/assets/css/components/battle-player-info.scss";
</style> 