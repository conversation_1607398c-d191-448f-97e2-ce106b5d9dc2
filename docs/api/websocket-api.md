# WebSocket API 文档

## 概述

本文档描述了Socket.IO相关的API接口，用于实时通信功能，包括对战房间实时状态更新、开箱动画同步、聊天消息等。

**重要说明**：
- ✅ **Socket.IO连接**: 使用Socket.IO库进行实时通信，而非原生WebSocket
- ✅ **消息事件**: 通过 `message` 事件接收所有实时消息
- ✅ **完整动画同步支持**：后端已实现所有动画阶段的Socket.IO消息
- ✅ **时间戳同步机制**：支持基于服务器时间戳的精确动画同步
- ✅ **重连恢复机制**：支持Socket.IO断开重连后的动画状态恢复
- ✅ **国际化字段支持**：所有名称字段都提供多语言版本
- ✅ **Redis状态缓存**：动画状态缓存到Redis，支持毫秒级恢复
- ✅ **性能自适应同步**：自动根据设备性能调整动画质量
- ✅ **智能网络适应**：自动适应不同网络环境，支持高延迟网络优化
- ✅ **生产级容错**：完善的错误处理、降级策略、性能监控

## 基础信息

- **连接方式**: WebSocket（Django Channels）
- **WebSocket URL**: `ws://domain/ws/battle/{room_short_id}/`
- **认证方式**: Session Cookie 或 Token
- **消息格式**: JSON 数组 `[messageType, action, messageData, socketId?]`
- **事件监听**: 通过 `socket.on('message', ...)` 接收消息

## 连接管理

### Socket.IO连接流程

1. 使用Socket.IO库连接到 `http://domain/socket.io/`
2. 监听 `message` 事件接收实时消息
3. 接收实时消息（数组格式）
4. 根据消息类型和action处理相应业务逻辑

## 实际消息格式

### 消息结构

所有Socket.IO消息都采用JSON数组格式，通过 `message` 事件发送：

**消息格式**: `[messageType, action, messageData, socketId?]`

**参数说明**:
- `messageType`: 消息类型（如 `boxroom`、`boxroomdetail`、`box`、`monitor`）
- `action`: 操作类型（如 `new`、`update`、`start`、`cancel`、`round_start`、`opening_start`、`round_result`、`battle_end`）
- `messageData`: 具体的数据内容
- `socketId`: 可选的Socket ID（仅 `boxroomdetail` 消息包含）

---

## 1. 对战房间Socket.IO消息

### 1.1 房间级别状态变化 (boxroom)

所有房间级别的状态变化都通过 `boxroom` 消息类型发送：

**消息格式**: `["boxroom", action, roomData]`

#### 1.1.1 新房间创建

**消息类型**: `["boxroom", "new", roomData]`

**数据内容**:
- `uid`: 房间唯一标识符
- `short_id`: 房间短ID
- `state`: 房间状态（数字格式）
- `max_joiner`: 最大参与人数
- `type`: 房间类型（1=普通对战）
- `user`: 房主信息（包含uid、profile）
- `joiner_count`: 当前参与人数
- `round_count`: 总回合数
- `create_time`: 创建时间
- `rounds`: 箱子信息数组（包含case_key、name、name_en、name_zh_hans、price）

#### 1.1.2 房间状态更新

**消息类型**: `["boxroom", "update", roomData]`

**数据内容**:
- `uid`: 房间唯一标识符
- `short_id`: 房间短ID
- `state`: 房间状态（数字格式）
- `joiner_count`: 当前参与人数
- `max_joiner`: 最大参与人数
- `users`: 参与者信息数组（包含uid、profile）
- `countdown_start`: 是否开始倒计时（布尔值，仅在满员时出现）

**状态说明**:
- `state=2`: 可加入状态
- `state=3`: 加入中状态
- `state=4`: 满员状态（即将开始）
- `state=5`: 进行中状态
- `state=11`: 已结束状态
- `state=20`: 已取消状态

**示例：房间满员并开始倒计时**

**数据格式**:
```json
["boxroom", "update", {
  "uid": "room123",
  "short_id": "R123",
  "state": 4,
  "joiner_count": 4,
  "max_joiner": 4,
  "countdown_start": true
}]
```

> 当 `state=4` 且 `countdown_start=true` 时，前端应显示"满员倒计时"动画并准备对战开始。

**玩家加入/离开通知** ✅ **已实现**:
- 当玩家加入房间时，系统会发送 `update` 消息，包含更新后的参与者列表
- 当玩家离开房间时，系统会发送 `update` 消息，更新参与人数和参与者列表
- 当房主离开房间时，系统会发送 `cancel` 消息，房间状态变为已取消
- 前端可以通过监听 `update` 消息来实时显示玩家加入/离开的状态变化

**使用场景**:
- 房间列表页面：实时更新房间参与人数
- 房间详情页面：显示当前参与者列表
- 对战准备页面：显示等待加入的玩家
- 对战进行页面：显示参与对战的玩家

#### 1.1.3 对战开始

**消息类型**: `["boxroom", "start", roomData]`

**数据内容**:
- `uid`: 房间唯一标识符
- `short_id`: 房间短ID
- `state`: 房间状态（5=进行中）
- `current_round`: 当前回合数
- `round_count`: 总回合数
- `start_time`: 开始时间
- `animation_config`: 动画配置对象
  - `start_countdown`: 开始倒计时时长（毫秒）
  - `round_duration`: 回合总时长（毫秒）
  - `case_animation_duration`: 开箱动画时长（毫秒）
  - `result_reveal_delay`: 结果揭晓延迟（毫秒）

#### 1.1.4 房间取消

**消息类型**: `["boxroom", "cancel", roomData]`

**数据内容**:
- `uid`: 房间唯一标识符
- `short_id`: 房间短ID
- `state`: 房间状态（20=已取消）
- `cancel_reason`: 取消原因
- `cancel_time`: 取消时间

### 1.2 房间详情变化 (boxroomdetail) ✅ **完整动画同步已实现**

房间内部的详细变化通过 `boxroomdetail` 消息类型发送：

**消息格式**: `["boxroomdetail", action, betData, socketId]`

#### 1.2.1 回合开始 (round_start) ✅ **已实现 - 支持时间戳同步**

**消息类型**: `["boxroomdetail", "round_start", roundData, socketId]`

**数据内容**:
- `round`: 当前回合数
- `total_rounds`: 总回合数
- `round_start_timestamp`: 回合开始时间戳（毫秒）
- `server_timestamp`: 服务器当前时间戳（毫秒）
- `animation_config`: 动画配置对象
  - `case_animation_duration`: 开箱动画时长（毫秒）
  - `simultaneous_opening`: 是否同时开箱（布尔值）
  - `reveal_delay`: 结果揭晓延迟（毫秒）
- `sync_config`: 同步配置对象
  - `enable_timestamp_sync`: 启用时间戳同步（布尔值）
  - `tolerance_ms`: 同步容忍度（毫秒）
- `participants`: 参与者信息数组
  - `user`: 用户信息（username、nickname、avatar）
  - `case`: 箱子信息（case_key、name、name_en、name_zh_hans、cover）
  - `animation_duration`: 动画时长（毫秒）

#### 1.2.2 开箱动画触发 (opening_start) ✅ **已实现 - 支持时间戳同步**

**消息类型**: `["boxroomdetail", "opening_start", animationData, socketId]`

**数据内容**:
- `animation_id`: 动画唯一标识符（格式：anim_{timestamp}_{uuid}）
- `animation_start_timestamp`: 动画开始时间戳（毫秒）
- `round`: **✅ 使用实际轮次** - 通过BattleRoundManager动态获取，不再硬编码
- `server_timestamp`: 服务器当前时间戳（毫秒）
- `preparation_time`: 准备时间（毫秒）
- `sync_config`: 同步配置对象
  - `tolerance_ms`: 同步容忍度（毫秒）
  - `max_delay_compensation`: 最大延迟补偿（毫秒）
  - `enable_client_sync`: 启用客户端时钟同步（布尔值）
  - `adaptive_tolerance`: 启用自适应容忍度（布尔值）
  - `high_latency_tolerance_ms`: 高延迟网络容忍度（毫秒）
- `participants`: 参与者信息数组
  - `user`: 用户信息（username）
  - `animation_duration`: 动画时长（毫秒）

#### 1.2.3 动画进度同步 (animation_progress) ✅ **已实现 - 支持时间戳同步**

**消息类型**: `["boxroomdetail", "animation_progress", progressData, socketId]`

**数据内容**:
- `animation_id`: 动画唯一标识符
- `progress`: 动画进度（0-1之间的浮点数）
- `stage`: 当前动画阶段（如case_opening、case_shaking等）
- `server_timestamp`: 服务器当前时间戳（毫秒）
- `sync_config`: 同步配置对象
  - `enable_progress_sync`: 启用进度同步（布尔值）
  - `sync_interval_ms`: 同步间隔（毫秒）
- `participants`: 参与者进度信息数组
  - `user`: 用户信息（username）
  - `progress`: 当前进度（0-1之间的浮点数）
  - `current_stage`: 当前动画阶段

#### 1.2.4 回合结果 (round_result) ✅ **已实现**

**消息类型**: `["boxroomdetail", "round_result", resultData, socketId]`

**数据内容**:
- `animation_id`: 动画唯一标识符
- `results`: 结果数组
  - `user`: 用户信息（username）
  - `open_amount`: 开箱金额
  - `victory`: 是否胜利（null=平局，0=失败，1=胜利）
  - `items`: 开出的饰品数组
    - `uid`: 饰品唯一标识符
    - `item_id`: 饰品ID
    - `name`: 饰品名称
    - `name_en`: 饰品英文名称
    - `name_zh_hans`: 饰品简体中文名称
    - `image`: 饰品图片URL
    - `item_price`: 价格信息（price）
    - `item_rarity`: 稀有度信息（rarity_id、rarity_name、rarity_name_en、rarity_name_zh_hans、rarity_color）
    - `item_category`: 分类信息（cate_id、cate_name、cate_name_en、cate_name_zh_hans、icon）
    - `reveal_order`: 揭晓顺序
    - `animation_effects`: 动画效果配置（particles、glow_effect、sound_effect）

#### 1.2.5 对战结束 (battle_end) ✅ **已实现**

**消息类型**: `["boxroomdetail", "battle_end", endData, socketId]`

**数据内容**:
- `winner`: 获胜者信息
  - `user`: 用户信息（username）
  - `total_amount`: 总金额
  - `victory`: 胜利标识（1）
- `final_results`: 最终结果数组
  - `user`: 用户信息（username）
  - `open_amount`: 开箱金额
  - `win_amount`: 获胜金额
  - `victory`: 是否胜利（0=失败，1=胜利）
  - `total_items`: 总饰品数量
  - `rare_items`: 稀有饰品数量
- `animation_config`: 动画配置对象
  - `victory_celebration`: 胜利庆祝（布尔值）
  - `confetti_duration`: 彩带持续时间（毫秒）
  - `result_display_duration`: 结果展示时间（毫秒）

#### 1.2.6 时钟同步 (time_sync_request / time_sync_response)  **🆕 新增**

为进一步提升弱网环境的动画对齐精度，支持客户端主动发起时钟同步请求。

**客户端 → 服务器**
- 使用 `socket.emit('time_sync_request', data)` 发送同步请求
- 数据格式: `{sync_id, client_timestamp}`

**服务器 → 客户端**
- 通过 `message` 事件接收时钟同步响应
- 消息格式: `["boxroomdetail", "time_sync_response", responseData]`

**响应数据格式**:
```json
["boxroomdetail", "time_sync_response", {
  "sync_id": "sync_1735434567890_abc12345",
  "server_timestamp": 1735434568923,
  "client_timestamp_echo": 1735434567890
}, "socket_id_server"]
```

客户端收到后：
1. `rtt = (Date.now() - client_timestamp_echo) / 2` 估算往返延迟。
2. `clockOffset = server_timestamp - (Date.now() - rtt)` 修正本地时钟。

> 普通网络每 30 s 发一次；RTT > 250 ms 环境提升至 10 s。  
> 若 Socket.IO 长时间未连通，可调用 `GET /api/box/battle/time-sync/` 兜底校时。

**时钟同步实现要点**：
- 计算往返延迟和时钟偏差
- 使用移动平均值平滑时钟偏差
- 根据网络质量调整同步频率

### 1.3 个人开箱消息 (box)

个人开箱结果通过 `box` 消息类型发送：

**消息格式**: `["box", action, data]`

#### 1.3.1 开箱结果 (new)

**消息类型**: `["box", "new", openData]`

**数据内容**:
- `user`: 用户信息（uid、profile）
- `case`: 箱子信息（case_key、name、name_en、name_zh_hans、price）
- `items`: 开出的饰品数组（包含完整的饰品信息）
- `total_value`: 总价值
- `cost`: 开箱成本
- `profit`: 利润
- `open_count`: 箱子当前开箱次数

**说明**：
- 此消息在用户开箱后延迟15秒发送，用于实时显示开箱结果
- `open_count` 字段包含箱子当前的开箱次数
- 支持个人开箱和对战开箱两种场景

#### 1.3.2 箱子详情更新 (details) - 已注释

**消息类型**: `["box", "details", detailData]`

**数据内容**:
- `case_key`: 箱子key
- `open_count`: 开箱次数
- `data`: 箱子详细信息

**说明**：
- 此消息类型目前已被注释，暂未使用
- 用于推送箱子详情更新

### 1.4 房间数据消息 (boxroomdetail) - 兼容性消息

为了保持向后兼容性，系统还会发送传统的房间数据消息：

**消息格式**: `["boxroomdetail", action, betsData, socketId]`

**说明**：
- 这些兼容性消息与新的动画同步消息同时发送
- 主要用于保持与旧版本前端的兼容性
- 建议前端优先使用新的动画同步消息（`round_start`、`opening_start`、`round_result`、`battle_end`）

#### 1.4.1 回合数据 (round) - 兼容性消息

**消息类型**: `["boxroomdetail", "round", betsData, socketId]`

**数据内容**:
- 包含完整的投注和开箱结果信息
- 与新的动画同步消息同时发送
- 主要用于保持与旧版本前端的兼容性

#### 1.4.2 对战结束数据 (end) - 兼容性消息

**消息类型**: `["boxroomdetail", "end", betsData, socketId]`

**数据内容**:
- 包含完整的对战结束结果信息
- 与新的动画同步消息同时发送
- 主要用于保持与旧版本前端的兼容性

**说明**：
- 这些兼容性消息与新的动画同步消息同时发送
- 主要用于保持与旧版本前端的兼容性
- 建议前端优先使用新的动画同步消息（`round_start`、`opening_start`、`round_result`、`battle_end`）

### 1.5 监控系统消息 (monitor)

监控系统通过 `monitor` 消息类型发送实时统计数据：

**消息格式**: `["monitor", action, data]`

#### 1.5.1 在线人数更新 (online_number)

**消息类型**: `["online_number", "update", number]`

**数据内容**:
- 在线人数（整数）

**说明**：
- 每10秒更新一次在线人数
- 数据基于基础在线人数和时间段系数计算
- 用于实时显示平台活跃度

#### 1.5.2 监控统计数据更新 (monitor)

**消息类型**: `["monitor", "update", monitorData]`

**数据内容**:
- `user_number`: 平台注册用户总数
- `online_number`: 当前在线用户数（基于真实感算法计算）
- `case_number`: 平台总开箱次数
- `battle_number`: 平台总对战次数
- `recent_activity`: 最近活动数据
  - `opens_last_5min`: 最近5分钟开箱数量
  - `battles_last_5min`: 最近5分钟对战数量
  - `activity_level`: 活跃度等级（very_low/low/medium/high/very_high）
- `trend_info`: 趋势信息
  - `direction`: 趋势方向（-1下降/0稳定/1上升）
  - `strength`: 趋势强度（0-1之间）
  - `volatility`: 波动性（0-1之间）
- `timestamp`: 数据时间戳

**说明**：
- 每2.5-3.5秒更新一次监控统计数据（动态间隔）
- 包含用户总数、在线人数、开箱总数、对战总数
- **新增**: 实时活动数据和趋势信息，让数据更真实
- 用于管理员监控面板和系统状态展示

#### 1.5.3 开箱记录数据 (case_records) ✅ **已实现**

**消息类型**: `["case_records", "update", caseRecordsData]`

**数据内容**:
- 包含最近的开箱记录数组，每个记录包含：
  - `user_info`: 用户信息（nickname、avatar）
  - `item_info`: 饰品信息（market_name、market_name_cn、icon_url、price、rarity、rarity_color）
  - `case_info`: 箱子信息（name、cover）
  - `cost`: 开箱成本
  - `create_time`: 开箱时间

**完整响应格式示例**:
```json
["case_records", "update", [
  {
    "user_info": {
      "nickname": "玩家昵称",
      "avatar": "https://example.com/avatar.jpg"
    },
    "item_info": {
      "market_name": "AK-47 | 红线 (崭新出厂)",
      "market_name_cn": "AK-47 | 红线 (崭新出厂)",
                "icon_url": "https://example.com/item.jpg",
                "price": 125.50,
                "rarity": "pink",
                "rarity_color": "#eb4b4b"
    },
    "case_info": {
      "name": "AK-47 红线箱子",
      "cover": "https://example.com/cover.jpg"
    },
    "cost": 10.50,
    "create_time": "2023-12-01T10:30:00Z"
  }
]]
```

**说明**：
- 通过Socket.IO请求获取：`socket.emit('case_records')`
- 用于首页实时显示开箱记录
- 数据来源于缓存，更新频率为5分钟
- 支持国际化字段显示

**真实感算法特性**：
- **智能时间段**: 24小时精细时间段划分，模拟真实用户行为模式
- **自然趋势**: 在线人数会呈现自然的上升/下降趋势，避免机械式变化
- **随机波动**: 基于正态分布的随机波动，模拟真实用户行为
- **活动事件**: 随机触发特殊活动事件，增加在线人数波动
- **高峰低谷**: 识别高峰时段（10、14、19、21点）和低谷时段（2、4、6点）
- **动态更新**: 更新频率在合理范围内随机变化，避免过于规律

---

## 2. 时间戳同步机制 ✅ **完整实现**

### 2.1 同步问题分析

**网络延迟问题**：
- 不同用户的网络延迟不同，会导致动画不同步
- 即使start_delay相同，实际开始时间差异很大
- 动画不同步差异可达200ms以上

### 2.2 时间戳同步解决方案

**基于服务器时间戳 + 客户端时钟同步**：
- 使用服务器绝对时间戳：`animation_start_timestamp`
- 提供服务器当前时间：`server_timestamp`
- 客户端计算时钟偏差和网络延迟
- 智能延迟补偿和容错机制

### 2.3 同步配置参数

**sync_config字段说明**：
- `tolerance_ms`: 同步容忍度（默认100ms）
- `max_delay_compensation`: 最大延迟补偿（默认500ms）
- `enable_client_sync`: 启用客户端时钟同步
- `adaptive_tolerance`: 启用自适应容忍度
- `high_latency_tolerance_ms`: 高延迟网络容忍度（默认250ms）

### 2.4 客户端同步实现要点

**时钟偏差计算**：
- 客户端需要计算与服务器的时钟偏差
- 使用往返时间估算网络延迟
- 维护同步历史记录，使用移动平均值平滑时钟偏差

**动画同步控制**：
- 基于服务器时间戳计算本地开始时间
- 处理延迟补偿，确保动画同步
- 支持快进到正确进度，避免错过动画

**智能同步策略**：
- 自适应同步参数，根据网络质量调整
- 预测性同步，基于历史数据预测延迟
- 性能自适应同步，根据设备FPS调整动画质量

---

## 3. WebSocket重连恢复机制 ✅ **完整实现**

### 3.1 重连恢复问题分析

**核心问题**：
- 用户在动画进行中断网重连
- 重连后错过了关键的WebSocket消息
- 结果：重连后看到的是静态页面，没有动画效果

### 3.2 恢复机制解决方案

**动画状态API**: `GET /api/box/battle/animation-state/`
- 实时获取房间动画状态
- 基于服务器时间戳精确计算动画进度
- 支持快进到正确的动画位置

**Redis状态缓存**：
- 缓存动画状态到Redis，支持10分钟TTL
- 重连后快速恢复，毫秒级响应
- 动画结束后自动清理缓存

**智能恢复算法**：
- 支持等待、倒计时、动画中、结果揭晓、已结束等所有状态恢复
- 智能快进算法，确保重连后动画与其他用户同步
- 多种恢复策略，恢复失败时提供备选方案

### 3.3 恢复机制实现要点

**动画状态获取**：
- 通过HTTP API获取当前动画状态
- 包含动画进度、参与者信息、恢复配置等
- 支持过期检测和自动清理

**状态恢复逻辑**：
- 根据动画状态类型选择恢复策略
- 支持快进恢复和跳过已完成阶段
- 提供优雅降级和错误处理

**前端重连管理**：
- 自动检测WebSocket连接状态
- 实现指数退避重连策略
- 重连成功后自动恢复动画状态

---

## 4. 国际化字段支持 ✅ **完整实现**

### 4.1 支持的国际化字段

所有名称相关字段都提供以下版本：
- `name` - 默认名称（通常是中文）
- `name_en` - 英文名称
- `name_zh_hans` - 简体中文名称

### 4.2 涵盖范围

**箱子名称**：
- 对战箱子列表中的箱子名称
- 房间列表中rounds的箱子名称
- 房间详情中rounds的箱子名称

**饰品名称**：
- 房间详情中results的饰品名称
- 开箱结果中的饰品名称

**分类名称**：
- 饰品分类（Category）
- 饰品品质（Quality）  
- 饰品稀有度（Rarity）
- 饰品外观（Exterior）

### 4.3 使用建议

前端可以根据用户的语言偏好选择对应的字段：
- 根据用户语言偏好显示名称
- 提供语言切换功能
- 确保所有UI元素都支持多语言显示

---

## 5. 动画同步流程 ✅ **完整实现**

### 5.1 完整对战动画流程

对战动画分为以下几个阶段，每个阶段都有对应的WebSocket消息：

```
房间创建 → 玩家加入 → 房间满员 → 对战开始 → 回合开始 → 
