<template>
  <div class="minimal-card">
    <div class="flex items-center space-x-4 p-4 bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 hover:border-white/20 transition-all duration-300">
      <!-- 用户头像 -->
      <div class="flex-shrink-0">
        <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-medium text-sm">
          {{ record.user_name?.charAt(0).toUpperCase() || 'U' }}
        </div>
      </div>
      
      <!-- 开箱信息 -->
      <div class="flex-grow min-w-0">
        <div class="flex items-center space-x-2 mb-1">
          <span class="text-blue-400 font-medium text-sm">{{ record.user_name }}</span>
          <span class="text-white/40 text-xs">opened</span>
        </div>
        <div class="text-white text-sm truncate">{{ record.case_info?.name }}</div>
        <div class="text-white/60 text-xs truncate">
          Got: {{ getSkinName(record.item_info?.name) }}
        </div>
      </div>
      
      <!-- 物品图片 -->
      <div class="flex-shrink-0">
        <img
          :src="record.item_info?.image || '/images/placeholder-weapon.png'"
          :alt="record.item_info?.name"
          class="w-16 h-16 object-contain rounded-md bg-gray-700"
          @error="handleImageError"
        />
      </div>
      
      <!-- 时间 -->
      <div class="flex-shrink-0 text-white/40 text-xs">
        just now
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  record: any
}

const props = defineProps<Props>()

const getSkinName = (fullName?: string): string => {
  if (!fullName) return 'Unknown Item'
  const parts = fullName.split(' | ')
  if (parts.length < 2) return fullName
  const skinPart = parts[1].trim()
  return skinPart.split(' (')[0].trim()
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjMzc0MTUxIi8+CjxwYXRoIGQ9Ik0yMCAyMEg0NFY0NEgyMFYyMFoiIHN0cm9rZT0iIzZCNzI4MCIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIi8+CjxjaXJjbGUgY3g9IjI4IiBjeT0iMjgiIHI9IjQiIGZpbGw9IiM2QjcyODAiLz4KPHBhdGggZD0iTTI0IDM2TDI4IDMyTDM2IDQwSDI0VjM2WiIgZmlsbD0iIzZCNzI4MCIvPgo8L3N2Zz4K'
}
</script>

<style lang="scss" scoped>
.minimal-card {
  animation: slideInLeft 0.6s ease-out;
}

@keyframes slideInLeft {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
</style> 