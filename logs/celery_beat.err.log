[2025-07-18 14:44:37,834: INFO/MainProcess] Scheduler: Sending due task cleanup-duplicate-roll-room-bets (roll.tasks.cleanup_duplicate_roll_room_bets)
[2025-07-18 14:44:37,893: INFO/MainProcess] Scheduler: Sending due task update-case-records-cache (thworker.update_case_records_cache)
[2025-07-18 14:44:37,905: INFO/MainProcess] Scheduler: Sending due task check-b2c-trade-state (thworker.check_b2c_trade_state)
[2025-07-18 14:49:37,860: INFO/MainProcess] Scheduler: Sending due task cleanup-duplicate-roll-room-bets (roll.tasks.cleanup_duplicate_roll_room_bets)
[2025-07-18 14:49:37,895: INFO/MainProcess] Scheduler: Sending due task update-case-records-cache (thworker.update_case_records_cache)
[2025-07-18 14:49:37,911: INFO/MainProcess] Scheduler: Sending due task check-b2c-trade-state (thworker.check_b2c_trade_state)
[2025-07-18 14:54:38,029: INFO/MainProcess] Scheduler: Sending due task cleanup-duplicate-roll-room-bets (roll.tasks.cleanup_duplicate_roll_room_bets)
[2025-07-18 14:54:38,130: INFO/MainProcess] Scheduler: Sending due task update-case-records-cache (thworker.update_case_records_cache)
[2025-07-18 14:54:38,174: INFO/MainProcess] Scheduler: Sending due task check-b2c-trade-state (thworker.check_b2c_trade_state)
[2025-07-18 14:59:38,135: INFO/MainProcess] Scheduler: Sending due task cleanup-duplicate-roll-room-bets (roll.tasks.cleanup_duplicate_roll_room_bets)
[2025-07-18 14:59:38,172: INFO/MainProcess] Scheduler: Sending due task update-case-records-cache (thworker.update_case_records_cache)
[2025-07-18 14:59:38,227: INFO/MainProcess] Scheduler: Sending due task check-b2c-trade-state (thworker.check_b2c_trade_state)
[2025-07-18 15:00:00,005: INFO/MainProcess] Scheduler: Sending due task hourly-lottery (thworker.hourly_lottery)
[2025-07-18 15:04:38,193: INFO/MainProcess] Scheduler: Sending due task cleanup-duplicate-roll-room-bets (roll.tasks.cleanup_duplicate_roll_room_bets)
[2025-07-18 15:04:38,200: INFO/MainProcess] Scheduler: Sending due task update-case-records-cache (thworker.update_case_records_cache)
[2025-07-18 15:04:38,231: INFO/MainProcess] Scheduler: Sending due task check-b2c-trade-state (thworker.check_b2c_trade_state)
[2025-07-18 15:09:38,263: INFO/MainProcess] Scheduler: Sending due task cleanup-duplicate-roll-room-bets (roll.tasks.cleanup_duplicate_roll_room_bets)
[2025-07-18 15:09:38,273: INFO/MainProcess] Scheduler: Sending due task update-case-records-cache (thworker.update_case_records_cache)
[2025-07-18 15:09:38,279: INFO/MainProcess] Scheduler: Sending due task check-b2c-trade-state (thworker.check_b2c_trade_state)
[2025-07-18 15:14:38,330: INFO/MainProcess] Scheduler: Sending due task cleanup-duplicate-roll-room-bets (roll.tasks.cleanup_duplicate_roll_room_bets)
[2025-07-18 15:14:38,345: INFO/MainProcess] Scheduler: Sending due task update-case-records-cache (thworker.update_case_records_cache)
[2025-07-18 15:14:38,358: INFO/MainProcess] Scheduler: Sending due task check-b2c-trade-state (thworker.check_b2c_trade_state)
[2025-07-18 15:19:38,362: INFO/MainProcess] Scheduler: Sending due task check-b2c-trade-state (thworker.check_b2c_trade_state)
[2025-07-18 15:19:38,374: INFO/MainProcess] Scheduler: Sending due task cleanup-duplicate-roll-room-bets (roll.tasks.cleanup_duplicate_roll_room_bets)
[2025-07-18 15:19:38,385: INFO/MainProcess] Scheduler: Sending due task update-case-records-cache (thworker.update_case_records_cache)
[2025-07-18 15:24:38,429: INFO/MainProcess] Scheduler: Sending due task cleanup-duplicate-roll-room-bets (roll.tasks.cleanup_duplicate_roll_room_bets)
[2025-07-18 15:24:38,445: INFO/MainProcess] Scheduler: Sending due task update-case-records-cache (thworker.update_case_records_cache)
[2025-07-18 15:24:38,461: INFO/MainProcess] Scheduler: Sending due task check-b2c-trade-state (thworker.check_b2c_trade_state)
[2025-07-18 15:29:38,556: INFO/MainProcess] Scheduler: Sending due task cleanup-duplicate-roll-room-bets (roll.tasks.cleanup_duplicate_roll_room_bets)
[2025-07-18 15:29:38,566: INFO/MainProcess] Scheduler: Sending due task update-case-records-cache (thworker.update_case_records_cache)
[2025-07-18 15:29:38,576: INFO/MainProcess] Scheduler: Sending due task check-b2c-trade-state (thworker.check_b2c_trade_state)
[2025-07-18 15:34:38,603: INFO/MainProcess] Scheduler: Sending due task cleanup-duplicate-roll-room-bets (roll.tasks.cleanup_duplicate_roll_room_bets)
[2025-07-18 15:34:38,627: INFO/MainProcess] Scheduler: Sending due task update-case-records-cache (thworker.update_case_records_cache)
[2025-07-18 15:34:38,645: INFO/MainProcess] Scheduler: Sending due task check-b2c-trade-state (thworker.check_b2c_trade_state)
DEBUG:asyncio:Using selector: EpollSelector
DEBUG:celery.utils.functional:
def check_pendclose_trade_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_enable_pay_method(enable_list):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache_task(self, count=0):
    return 1

DEBUG:celery.utils.functional:
def accumulate(self, *args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache(count, batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def delete_old_log_entries(batch_size=0, days=1):
    return 1

DEBUG:celery.utils.functional:
def weekly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def unlock_chord(self, group_id, callback, interval=0, max_retries=1, result=2, Result=3, GroupResult=4, result_from_tuple=5, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_register_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def zbt_buy_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def xmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def update_monitor_data_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_today_amount():
    return 1

DEBUG:celery.utils.functional:
def check_b2c_trade_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def xstarmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def delete_old_case_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_staff_balance():
    return 1

DEBUG:celery.utils.functional:
def case_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def chunks(task, it, n):
    return 1

DEBUG:celery.utils.functional:
def update_recharge_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def case_pk_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def group(self, tasks, result, group_id, partial_args, add_to_parent=0):
    return 1

DEBUG:celery.utils.functional:
def init_system_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_package_items(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_month_amount():
    return 1

DEBUG:celery.utils.functional:
def sync_items_price_task(self):
    return 1

DEBUG:celery.utils.functional:
def chain(*args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def case_bot_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_user_active_point_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_new_user_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def chord(self, header, body, partial_args=0, interval=1, countdown=2, max_retries=3, eager=4, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_domain_verify_switch(value):
    return 1

DEBUG:celery.utils.functional:
def case_pk_join_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def hourly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def cleanup_duplicate_roll_room_bets():
    return 1

DEBUG:celery.utils.functional:
def roll_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_daily_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def delete_old_balance_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_enable_based_on_coins(min_coins, max_coins):
    return 1

DEBUG:celery.utils.functional:
def backend_cleanup():
    return 1

DEBUG:celery.utils.functional:
def check_envelop_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def daily_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def check_room_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_enable_rule_exam(value):
    return 1

[2025-07-18 15:35:00,643: INFO/MainProcess] beat: Starting...
[2025-07-18 15:35:03,149: INFO/MainProcess] DatabaseScheduler: Schedule changed.
[2025-07-18 15:39:38,606: INFO/MainProcess] Scheduler: Sending due task cleanup-duplicate-roll-room-bets (roll.tasks.cleanup_duplicate_roll_room_bets)
[2025-07-18 15:39:38,660: INFO/MainProcess] Scheduler: Sending due task update-case-records-cache (thworker.update_case_records_cache)
[2025-07-18 15:39:38,672: INFO/MainProcess] Scheduler: Sending due task check-b2c-trade-state (thworker.check_b2c_trade_state)
