#!/usr/bin/env node

/**
 * WebSocket客户端测试脚本
 * 测试连接到WebSocket服务器并接收消息
 */

const { io } = require('socket.io-client');

console.log('🔧 WebSocket客户端测试开始...');

// 测试配置
const configs = [
  {
    name: '本地4000端口',
    url: 'http://localhost:4000',
    options: {
      transports: ['polling', 'websocket'],
      forceNew: true,
      reconnection: true,
      reconnectionAttempts: 3,
      timeout: 10000,
      autoConnect: true,
      path: '/socket.io/',
      query: {
        page: 'ws',
        EIO: '4'
      }
    }
  },
  {
    name: '服务器4000端口',
    url: 'http://************:4000',
    options: {
      transports: ['polling', 'websocket'],
      forceNew: true,
      reconnection: true,
      reconnectionAttempts: 3,
      timeout: 10000,
      autoConnect: true,
      path: '/socket.io/',
      query: {
        page: 'ws',
        EIO: '4'
      }
    }
  }
];

async function testConnection(config) {
  return new Promise((resolve) => {
    console.log(`\n🔌 测试连接: ${config.name} (${config.url})`);
    
    const socket = io(config.url, config.options);
    
    const timeout = setTimeout(() => {
      console.log(`❌ ${config.name}: 连接超时`);
      socket.disconnect();
      resolve({ success: false, error: '连接超时' });
    }, 15000);
    
    socket.on('connect', () => {
      clearTimeout(timeout);
      console.log(`✅ ${config.name}: 连接成功`);
      console.log(`   Socket ID: ${socket.id}`);
      console.log(`   传输方式: ${socket.io.engine.transport.name}`);
      
      // 监听消息
      socket.on('message', (data) => {
        console.log(`📨 ${config.name}: 收到消息 -`, data);
      });
      
      // 监听新的消息格式
      socket.on('monitor', (data) => {
        console.log(`📊 ${config.name}: 收到监控数据 -`, data);
      });
      
      socket.on('case_records', (data) => {
        console.log(`📦 ${config.name}: 收到开箱记录 -`, data);
      });
      
      // 发送测试消息
      socket.emit('test_message', {
        message: '这是一条测试消息',
        testRedis: true,
        timestamp: Date.now()
      });
      console.log(`📤 ${config.name}: 发送测试消息`);
      
      // 监听测试响应
      socket.on('test_response', (data) => {
        console.log(`📨 ${config.name}: 收到测试响应 -`, data);
      });
      
      socket.on('test_error', (data) => {
        console.log(`❌ ${config.name}: 测试错误 -`, data);
      });
      
      // 保持连接30秒来接收消息
      setTimeout(() => {
        console.log(`🔌 ${config.name}: 测试完成，断开连接`);
        socket.disconnect();
        resolve({ success: true });
      }, 30000);
    });
    
    socket.on('connect_error', (error) => {
      clearTimeout(timeout);
      console.log(`❌ ${config.name}: 连接错误 - ${error.message}`);
      resolve({ success: false, error: error.message });
    });
    
    socket.on('disconnect', (reason) => {
      console.log(`🔌 ${config.name}: 连接断开 - ${reason}`);
    });
  });
}

async function runTests() {
  console.log('🚀 开始WebSocket客户端测试...\n');
  
  const results = [];
  
  // 只测试本地连接
  const config = configs[0];
  const result = await testConnection(config);
  results.push({ name: config.name, ...result });
  
  console.log('\n📊 测试结果总结:');
  console.log('='.repeat(50));
  
  let successCount = 0;
  for (const result of results) {
    if (result.success) {
      console.log(`✅ ${result.name}: 成功`);
      successCount++;
    } else {
      console.log(`❌ ${result.name}: 失败 - ${result.error}`);
    }
  }
  
  console.log(`\n🎯 成功率: ${successCount}/${results.length} (${(successCount/results.length*100).toFixed(1)}%)`);
  
  if (successCount > 0) {
    console.log('\n🎉 WebSocket连接测试成功！');
    console.log('💡 现在应该有Redis订阅者了，可以测试消息发布。');
  } else {
    console.log('\n❌ WebSocket连接测试失败');
    console.log('💡 建议检查:');
    console.log('   1. WebSocket服务器是否正在运行');
    console.log('   2. 端口4000是否开放');
    console.log('   3. 认证配置是否正确');
  }
  
  process.exit(successCount > 0 ? 0 : 1);
}

// 运行测试
runTests().catch(console.error);
