from django.utils import timezone

from rest_framework import serializers

_STEAM_IMG_BASE = 'https://steamcommunity-a.akamaihd.net/economy/image/{icon_url}/'


class CustomFieldsSerializer(serializers.ModelSerializer):
    def __init__(self, *args, **kwargs):
        fields = kwargs.pop('fields', None)
        exclude = kwargs.pop('exclude', None)
        super(CustomFieldsSerializer, self).__init__(*args, **kwargs)

        if fields is not None:
            allowed = set(fields)
            existing = set(self.fields.keys())
            for field_name in existing - allowed:
                self.fields.pop(field_name)

        if exclude is not None:
            banned = set(exclude)
            for field_name in banned:
                self.fields.pop(field_name)


class ItemInfoBaseSerializer(CustomFieldsSerializer):
    name = serializers.SerializerMethodField()    
    market_name = serializers.SerializerMethodField()
    market_hash_name = serializers.SerializerMethodField()
    name_cn = serializers.SerializerMethodField()
    market_name_cn = serializers.SerializerMethodField()
    name_color = serializers.SerializerMethodField()
    appid = serializers.SerializerMethodField()
    contextid = serializers.SerializerMethodField()
    classid = serializers.SerializerMethodField()
    icon_url = serializers.SerializerMethodField()
    icon_url_large = serializers.SerializerMethodField()
    rarity = serializers.SerializerMethodField()
    rarity_color = serializers.SerializerMethodField()
    quality = serializers.SerializerMethodField()
    quality_color = serializers.SerializerMethodField()
    type = serializers.SerializerMethodField()
    weapon = serializers.SerializerMethodField()
    exterior = serializers.SerializerMethodField()
    item_set = serializers.SerializerMethodField()
    slot = serializers.SerializerMethodField()
    hero = serializers.SerializerMethodField()

    
    def get_name(self, obj):
        return obj.item_info.name if obj.item_info else None
    

    def get_market_name(self, obj):
        return obj.item_info.market_name if obj.item_info else None

    def get_market_hash_name(self, obj):
        return obj.item_info.market_hash_name if obj.item_info else None

    def get_name_cn(self, obj):
        return obj.item_info.name_cn if obj.item_info else None

    def get_market_name_cn(self, obj):
        return obj.item_info.market_name_cn if obj.item_info else None

    def get_name_color(self, obj):
        return obj.item_info.name_color if obj.item_info else None

    def get_appid(self, obj):
        return obj.item_info.appid if obj.item_info else None

    def get_contextid(self, obj):
        return obj.item_info.contextid if obj.item_info else None

    def get_classid(self, obj):
        return obj.item_info.classid if obj.item_info else None

    def get_icon_url(self, obj):
        if obj.item_info:
            if obj.item_info.custom_icon:
                return obj.item_info.custom_icon.url
            else:
                icon_url = obj.item_info.icon_url
                if icon_url:
                    if "/" in icon_url:
                        icon_url = icon_url.split("/")[0]
                    return _STEAM_IMG_BASE.format(icon_url=icon_url)
        # 如果 item_info 为 None，则返回 None 或默认值
        return None

    def get_icon_url_large(self, obj):
        if obj.item_info:
            if obj.item_info.custom_icon:
                return obj.item_info.custom_icon.url
            else:
                icon_url = obj.item_info.icon_url
                if icon_url:
                    if "/" in icon_url:
                        icon_url = icon_url.split("/")[0]
                    return _STEAM_IMG_BASE.format(icon_url=icon_url)
        # 如果 item_info 为 None，则返回 None 或默认值
        return None

    def get_rarity(self, obj):
        return obj.item_info.rarity if obj.item_info else None

    def get_rarity_color(self, obj):
        if obj.item_info:
            if obj.item_info.custom_rarity:
                return obj.item_info.custom_rarity
            return obj.item_info.rarity_color
        else:
            return None

    def get_quality(self, obj):
        return obj.item_info.quality if obj.item_info else None

    def get_quality_color(self, obj):
        return obj.item_info.quality_color if obj.item_info else None

    def get_type(self, obj):
        return obj.item_info.type if obj.item_info else None

    def get_weapon(self, obj):
        return obj.item_info.weapon if obj.item_info else None

    def get_exterior(self, obj):
        return obj.item_info.exterior if obj.item_info else None

    def get_item_set(self, obj):
        return obj.item_info.item_set if obj.item_info else None

    def get_slot(self, obj):
        return obj.item_info.slot if obj.item_info else None

    def get_hero(self, obj):
        return obj.item_info.hero if obj.item_info else None
