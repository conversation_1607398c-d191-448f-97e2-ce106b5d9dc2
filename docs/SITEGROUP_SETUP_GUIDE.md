# 🌐 站群管理系统部署指南

## 📋 概述

本指南详细说明如何部署和配置站群管理系统，实现代理商多站点管理功能。

## 🎯 系统特性

### ✅ 核心功能
- **多站点管理**: 代理商可管理多个独立域名网站
- **用户域名关联**: 用户注册时自动关联访问域名
- **功能开关**: 每个站点独立控制功能模块
- **SEO配置**: 每个站点独立的SEO设置
- **主题定制**: 支持站点个性化主题配置
- **统计分析**: 独立的站点访问和业务统计

### 🔧 技术特性
- **缓存优化**: 站点信息和配置缓存
- **数据迁移**: 从sitecfg.SEO平滑迁移
- **API接口**: 完整的RESTful API
- **管理后台**: Django Admin集成

## 🚀 部署步骤

### 步骤1: 添加应用到Django设置

编辑 `server/steambase/settings.py`:

```python
INSTALLED_APPS = [
    # ... 现有应用 ...
    'sitegroup',  # 添加站群应用
]
```

### 步骤2: 创建数据库迁移

```bash
cd /www/wwwroot/csgoskins.com.cn/server
python manage.py makemigrations sitegroup
python manage.py migrate
```

### 步骤3: 添加URL配置

编辑 `server/steambase/urls.py`:

```python
urlpatterns = [
    # ... 现有URL配置 ...
    url(r'^api/sitegroup/', include('sitegroup.urls')),
]
```

### 步骤4: 运行数据迁移脚本

```bash
cd /www/wwwroot/csgoskins.com.cn
python3 scripts/migrations/migrate_seo_to_sitegroup.py
```

### 步骤5: 更新articles应用集成

编辑 `server/articles/models.py`，在Content模型中添加：

```python
class Content(ModelBase):
    # ... 现有字段 ...
    
    # 站点关联
    target_sites = models.ManyToManyField(
        'sitegroup.Site', 
        verbose_name=_('Target Sites'),
        blank=True, 
        related_name='contents',
        help_text=_('Leave empty to show on all sites')
    )
```

然后运行迁移：

```bash
python manage.py makemigrations articles
python manage.py migrate
```

### 步骤6: 更新用户注册流程

在用户注册相关代码中集成站点关联：

```python
from sitegroup.interfaces import register_user_to_site

def register_user(request, user_data):
    # 创建用户
    user = AuthUser.objects.create(**user_data)
    
    # 关联到当前站点
    register_user_to_site(user, request)
    
    return user
```

## 🔌 API接口使用

### 1. 获取站点信息
```bash
GET /api/sitegroup/site-info/
```

响应示例：
```json
{
    "code": 0,
    "message": "Success",
    "body": {
        "domain": "www.example.com",
        "name": "Example Site",
        "subtitle": "Best CSGO Site",
        "seo_title": "CSGO Skins Trading",
        "features": {
            "articles": true,
            "case_battle": true,
            "market": false
        }
    }
}
```

### 2. 检查功能开关
```bash
GET /api/sitegroup/check-feature/?feature=articles
```

### 3. 代理商站点管理
```bash
# 获取代理商站点列表
GET /api/sitegroup/agent/sites/

# 创建新站点
POST /api/sitegroup/agent/sites/
{
    "domain": "new-site.com",
    "name": "New Site",
    "seo_title": "New CSGO Site"
}
```

## 🎨 前端集成

### 1. 获取站点配置
```typescript
// 获取当前站点信息
async function getSiteInfo() {
    const response = await fetch('/api/sitegroup/site-info/');
    const data = await response.json();
    return data.body;
}

// 检查功能是否启用
async function checkFeature(feature: string) {
    const response = await fetch(`/api/sitegroup/check-feature/?feature=${feature}`);
    const data = await response.json();
    return data.body.enabled;
}
```

### 2. 条件渲染组件
```vue
<template>
    <div>
        <!-- 文章模块 -->
        <ArticleSection v-if="features.articles" />
        
        <!-- 对战模块 -->
        <BattleSection v-if="features.case_battle" />
        
        <!-- 市场模块 -->
        <MarketSection v-if="features.market" />
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const features = ref({})

onMounted(async () => {
    const siteInfo = await getSiteInfo()
    features.value = siteInfo.features
})
</script>
```

### 3. 动态主题应用
```typescript
// 应用站点主题
function applySiteTheme(themeConfig: any) {
    const root = document.documentElement;
    
    root.style.setProperty('--primary-color', themeConfig.primary_color);
    root.style.setProperty('--secondary-color', themeConfig.secondary_color);
    root.style.setProperty('--header-bg', themeConfig.header_background);
    root.style.setProperty('--font-family', themeConfig.font_family);
    
    // 应用自定义CSS
    if (themeConfig.custom_css) {
        const style = document.createElement('style');
        style.textContent = themeConfig.custom_css;
        document.head.appendChild(style);
    }
}
```

## 🔧 管理后台使用

### 1. 访问管理后台
访问 `/admin/sitegroup/` 进入站群管理：

- **站群管理**: 管理代理商的站点集合
- **站点管理**: 配置单个站点信息
- **站点配置**: 设置站点的详细配置项
- **站点主题**: 自定义站点外观
- **站点统计**: 查看访问和业务数据

### 2. 创建新站点
1. 进入"站点"管理页面
2. 点击"新增站点"
3. 填写基本信息：
   - 选择所属站群
   - 输入域名
   - 设置站点名称和副标题
   - 配置SEO信息
4. 设置功能开关
5. 保存并创建默认主题

### 3. 配置功能开关
在站点编辑页面的"功能开关"部分：
- ✅ 文章系统: 启用/禁用文章功能
- ✅ 公告系统: 启用/禁用公告功能  
- ✅ 开箱对战: 启用/禁用对战功能
- ✅ 市场交易: 启用/禁用市场功能
- ✅ 抽奖系统: 启用/禁用抽奖功能

## 📊 数据统计

### 1. 站点统计数据
系统自动收集以下统计数据：
- 页面浏览量
- 独立访客数
- 新用户注册数
- 开箱次数
- 对战创建数
- 市场交易数

### 2. 查看统计报告
```bash
GET /api/sitegroup/sites/{site_id}/analytics/?start_date=2025-01-01&end_date=2025-01-31
```

## 🔄 文章系统集成

### 1. 文章归属设置
在文章编辑页面：
1. 选择"目标站点"字段
2. 留空表示在所有站点显示
3. 选择特定站点表示仅在该站点显示

### 2. 获取站点文章
```python
from sitegroup.interfaces import get_site_articles

def get_articles_for_current_site(request):
    articles = get_site_articles(request)
    return articles.filter(is_featured=True)[:10]
```

## 🚨 注意事项

### 1. 缓存管理
- 站点信息会被缓存1小时
- 功能开关缓存30分钟
- 配置项缓存30分钟
- 修改后需要清除相关缓存

### 2. 性能优化
- 使用select_related预加载关联数据
- 合理设置缓存时间
- 定期清理过期统计数据

### 3. 安全考虑
- 敏感配置项标记为sensitive
- 代理商只能管理自己的站点
- 管理员权限检查

## 🔮 扩展功能

### 1. 自定义配置项
```python
from sitegroup.business import set_site_config

# 设置站点自定义配置
set_site_config('example.com', 'max_daily_openings', '1000', 'integer')
```

### 2. 主题模板
可以扩展SiteTheme模型添加更多主题选项：
- 背景图片
- 动画效果
- 布局样式
- 组件配色

### 3. 多语言支持
结合Django的国际化功能，为不同站点配置不同的默认语言。

---

**🎯 总结**: 
- 完整的站群管理解决方案
- 平滑的数据迁移过程
- 灵活的功能配置
- 强大的扩展能力

现在您可以为每个代理商创建独立的站点，实现真正的多站点管理！
