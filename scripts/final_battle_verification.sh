#!/bin/bash

# 对战系统最终验证脚本
# 确保所有修复都正常工作

echo "🔍 对战系统最终验证"
echo "===================="

# 1. 基础模块验证
echo ""
echo "📦 1. 基础模块验证"
python3 -c "
import sys
sys.path.insert(0, 'server')
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')

try:
    import django
    django.setup()
    
    # 验证核心模块
    from box.enhanced_battle_system import enhanced_battle_manager
    from box.compat_async import get_async_processor
    from box.message_utils import WebSocketMessageSender, sanitize_websocket_data
    from box.system_fixes import TimezoneFixer, get_system_health
    from box.battle_config import BattleSystemConfig
    
    print('✅ 所有核心模块导入成功')
    
except Exception as e:
    print(f'❌ 模块导入失败: {e}')
    exit(1)
"

if [ $? -ne 0 ]; then
    echo "❌ 基础模块验证失败"
    exit 1
fi

# 2. 异步兼容性验证
echo ""
echo "⚡ 2. 异步兼容性验证"
python3 -c "
import sys
sys.path.insert(0, 'server')
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')

try:
    import django
    django.setup()
    
    from box.compat_async import get_async_processor
    processor = get_async_processor()
    
    print(f'✅ 异步处理器类型: {type(processor).__name__}')
    
    # 测试异步任务创建
    import asyncio
    if hasattr(asyncio, 'create_task'):
        print('✅ 支持原生 create_task')
    else:
        print('✅ 使用兼容模式 ensure_future')
        
except Exception as e:
    print(f'❌ 异步兼容性验证失败: {e}')
    exit(1)
"

if [ $? -ne 0 ]; then
    echo "❌ 异步兼容性验证失败"
    exit 1
fi

# 3. 消息处理验证
echo ""
echo "📨 3. 消息处理验证"
python3 -c "
import sys
sys.path.insert(0, 'server')
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')

try:
    import django
    django.setup()
    
    from box.message_utils import sanitize_websocket_data, WebSocketMessageSender
    
    # 测试数据清理
    test_data = {
        'users': ['user1', 'user2'],
        'items': [{'name': 'item1'}, {'name': 'item2'}],
        'mixed': ['str', 123, {'key': 'value'}]
    }
    
    cleaned = sanitize_websocket_data(test_data)
    print('✅ 消息数据清理功能正常')
    
    # 测试消息发送器
    sender = WebSocketMessageSender()
    print('✅ 消息发送器初始化成功')
    
except Exception as e:
    print(f'❌ 消息处理验证失败: {e}')
    exit(1)
"

if [ $? -ne 0 ]; then
    echo "❌ 消息处理验证失败"
    exit 1
fi

# 4. 系统健康检查
echo ""
echo "🏥 4. 系统健康检查"
python3 -c "
import sys
sys.path.insert(0, 'server')
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')

try:
    import django
    django.setup()
    
    from box.enhanced_battle_system import enhanced_battle_manager
    
    health = enhanced_battle_manager.get_system_health()
    status = health.get('status', 'unknown')
    details = health.get('details', {})
    
    print(f'✅ 系统状态: {status}')
    
    if status in ['healthy', 'warning']:
        print('✅ 系统健康检查通过')
        
        if 'warnings' in details:
            for warning in details['warnings']:
                print(f'⚠️  警告: {warning}')
    else:
        print('❌ 系统健康检查失败')
        exit(1)
        
except Exception as e:
    print(f'❌ 系统健康检查失败: {e}')
    exit(1)
"

if [ $? -ne 0 ]; then
    echo "❌ 系统健康检查失败"
    exit 1
fi

# 5. 时区修复验证
echo ""
echo "🕒 5. 时区修复验证"
python3 -c "
import sys
sys.path.insert(0, 'server')
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')

try:
    import django
    django.setup()
    
    from box.system_fixes import TimezoneFixer
    from django.utils import timezone
    
    # 测试时区感知时间
    current_time = TimezoneFixer.get_current_time()
    print(f'✅ 当前时间 (时区感知): {current_time}')
    
    # 测试时区修复
    if timezone.is_aware(current_time):
        print('✅ 时区感知功能正常')
    else:
        print('⚠️  时区配置可能有问题')
        
except Exception as e:
    print(f'❌ 时区修复验证失败: {e}')
    exit(1)
"

if [ $? -ne 0 ]; then
    echo "❌ 时区修复验证失败"
    exit 1
fi

# 最终报告
echo ""
echo "🎉 最终验证报告"
echo "===================="
echo "✅ 基础模块验证通过"
echo "✅ 异步兼容性验证通过"
echo "✅ 消息处理验证通过"
echo "✅ 系统健康检查通过"
echo "✅ 时区修复验证通过"
echo ""
echo "🚀 对战系统优化完成，所有功能正常！"
echo ""
echo "📋 后续建议："
echo "1. 定期运行健康检查: python3 scripts/battle_system_monitor.py --mode diagnosis"
echo "2. 监控系统状态: python3 scripts/battle_system_monitor.py --mode monitor"
echo "3. 考虑升级Python到3.8+以获得更好性能"

exit 0
