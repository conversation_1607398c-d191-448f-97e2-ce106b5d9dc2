from typing import Any, Callable, Optional, Union

class HiredisError(Exception): ...
class ProtocolError(HiredisError): ...
class ReplyError(HiredisError): ...

class Reader:
    def __init__(
        self,
        protocolError: Callable[[str], Exception] = ...,
        replyError: Callable[[str], Exception] = ...,
        encoding: Optional[str] = ...,
        errors: Optional[str] = ...,
    ) -> None: ...
    def feed(
        self, __buf: Union[str, bytes], __off: int = ..., __len: int = ...
    ) -> None: ...
    def gets(self, __shouldDecode: bool = ...) -> Any: ...
    def setmaxbuf(self, __maxbuf: Optional[int]) -> None: ...
    def getmaxbuf(self) -> int: ...
    def len(self) -> int: ...
    def has_data(self) -> bool: ...
    def set_encoding(
        self, encoding: Optional[str] = ..., errors: Optional[str] = ...
    ) -> None: ...
