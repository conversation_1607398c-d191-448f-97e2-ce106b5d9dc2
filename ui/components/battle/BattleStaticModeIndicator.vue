<!--
对战静态模式指示器
用于显示已结束对战的状态信息，提醒用户这是一个已完成的对战
-->
<template>
  <div class="battle-static-mode-indicator bg-gradient-to-r from-green-500/10 to-blue-500/10 border border-green-500/20 rounded-lg p-4 mb-4">
    <div class="flex items-center gap-3">
      <!-- 状态图标 -->
      <div class="flex-shrink-0">
        <div class="w-8 h-8 bg-green-500/20 rounded-full flex items-center justify-center">
          <Icon name="heroicons:check-circle" class="w-5 h-5 text-green-400" />
        </div>
      </div>
      
      <!-- 状态信息 -->
      <div class="flex-1">
        <div class="flex items-center gap-2 mb-1">
          <h3 class="text-sm font-medium text-green-400">
            {{ $t('battle.staticMode.title') }}
          </h3>
          <div class="px-2 py-0.5 bg-green-500/20 rounded text-xs text-green-300">
            {{ getStateText(battleState) }}
          </div>
        </div>
        
        <p class="text-xs text-gray-400">
          {{ $t('battle.staticMode.description') }}
        </p>
        
        <!-- 完成时间 -->
        <div v-if="finishedTime" class="mt-2 text-xs text-gray-500">
          {{ $t('battle.staticMode.finishedAt') }}: {{ formatFinishedTime(finishedTime) }}
        </div>
      </div>
      
      <!-- 性能优化提示 -->
      <div class="flex-shrink-0">
        <div class="flex items-center gap-1 text-xs text-blue-400">
          <Icon name="heroicons:bolt" class="w-4 h-4" />
          <span>{{ $t('battle.staticMode.optimized') }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  battleState: number
  finishedTime?: string
}

const props = defineProps<Props>()
const { t } = useI18n()

/**
 * 获取对战状态文本
 */
const getStateText = (state: number): string => {
  switch (state) {
    case 11:
      return t('battle.state.calculated')
    case 20:
      return t('battle.state.finished')
    default:
      return t('battle.state.completed')
  }
}

/**
 * 格式化完成时间
 */
const formatFinishedTime = (timeStr: string): string => {
  try {
    const date = new Date(timeStr)
    return date.toLocaleString()
  } catch (error) {
    return timeStr
  }
}
</script>

<style scoped>
.battle-static-mode-indicator {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.05) 0%, rgba(59, 130, 246, 0.05) 100%);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(34, 197, 94, 0.2);
}
</style>
