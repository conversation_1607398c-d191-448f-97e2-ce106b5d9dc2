import type { SkinItem } from '~/services/skin-api'
import type { SkinItem as TransformedSkinItem } from '~/types/skin'

/**
 * 详细的皮肤API数据接口（基于old项目的数据结构）
 */
export interface SkinDetailItem {
  id: string | number
  name: string
  name_en?: string
  name_zh_hans?: string
  name_zh?: string
  weapon_type?: string
  skin_name?: string
  skin_name_en?: string
  skin_name_zh_hans?: string
  skin_name_zh?: string
  image: string
  icon_url?: string
  price?: number
  value?: number
  rarity?: string
  quality?: string
  type?: string
  category?: string
  exterior?: string
  is_stattrak?: boolean
  market_hash_name?: string
  // 嵌套对象字段
  item_price?: {
    price: number | string
  }
  item_category?: {
    cate_id: number
    cate_name: string
    cate_name_en?: string
    cate_name_zh_hans?: string
    cate_name_zh?: string
  }
  item_quality?: {
    quality_id: number
    quality_name: string
    quality_name_en?: string
    quality_name_zh_hans?: string
    quality_name_zh?: string
    quality_color?: string
  }
  item_rarity?: {
    rarity_id: number
    rarity_name: string
    rarity_name_en?: string
    rarity_name_zh_hans?: string
    rarity_name_zh?: string
    rarity_color?: string
  }
  item_exterior?: {
    exterior_id: number
    exterior_name: string
    exterior_name_en?: string
    exterior_name_zh_hans?: string
    exterior_name_zh?: string
    exterior_color?: string
  }
  [key: string]: any
}

/**
 * 获取本地化字段值的通用函数
 */
const getLocalizedField = (item: any, field: string, locale: string = 'zh_hans'): string | undefined => {
  if (!item || !field) return undefined
  
  // 标准化语言代码
  const normalizedLocale = locale === 'zh' ? 'zh_hans' : locale
  
  // 按优先级尝试获取本地化字段
  if (normalizedLocale === 'zh_hans') {
    // 中文环境：优先标准zh_hans，然后zh，最后基础字段
    const zhHansField = `${field}_zh_hans`
    if (item[zhHansField]) return item[zhHansField]
    
    const zhField = `${field}_zh`
    if (item[zhField]) return item[zhField]
    
    const cnField = `${field}_cn` // 兼容旧格式
    if (item[cnField]) return item[cnField]
  } else if (normalizedLocale === 'en') {
    // 英文环境：优先en字段，然后基础字段
    const enField = `${field}_en`
    if (item[enField]) return item[enField]
  }
  
  // 回退到基础字段
  if (item[field]) return item[field]
  
  // 最后尝试英文字段作为兜底
  const enField = `${field}_en`
  if (item[enField]) return item[enField]
  
  return undefined
}

/**
 * 将API返回的SkinDetailItem转换为组件使用的SkinItem
 */
export const transformSkinItem = (apiSkin: SkinDetailItem, locale: string = 'zh_hans'): TransformedSkinItem => {
  // 获取价格
  let price = 0
  if (apiSkin.price) {
    price = typeof apiSkin.price === 'number' ? apiSkin.price : parseFloat(String(apiSkin.price))
  } else if (apiSkin.value) {
    price = typeof apiSkin.value === 'number' ? apiSkin.value : parseFloat(String(apiSkin.value))
  } else if (apiSkin.item_price?.price) {
    const priceValue = typeof apiSkin.item_price.price === 'number' 
      ? apiSkin.item_price.price 
      : parseFloat(String(apiSkin.item_price.price))
    // 如果价格值大于1000，可能是以分为单位，需要转换为元
    price = priceValue > 1000 ? priceValue / 100 : priceValue
  }

  // 获取本地化名称
  const localizedName = getLocalizedField(apiSkin, 'name', locale) || 'Unknown Skin'
  
  // 分离武器名称和皮肤名称
  const getWeaponAndSkinName = (fullName: string) => {
    const parts = fullName.split(' | ')
    if (parts.length >= 2) {
      const weapon = parts[0].trim()
      const skinPart = parts[1].trim()
      const skin = skinPart.split(' (')[0].trim() // 移除外观部分，如 "(Field-Tested)"
      return { weapon, skin }
    }
    return { weapon: fullName, skin: '' }
  }

  const { weapon, skin } = getWeaponAndSkinName(localizedName)
  
  // 获取图片URL
  const image = apiSkin.image || apiSkin.icon_url || ''
  
  // 获取稀有度信息
  const rarity = apiSkin.item_rarity
    ? getLocalizedField(apiSkin.item_rarity, 'rarity_name', locale) || apiSkin.rarity
    : apiSkin.rarity || 'Common'
  
  // 获取品质信息
  const quality = apiSkin.item_quality
    ? getLocalizedField(apiSkin.item_quality, 'quality_name', locale) || apiSkin.quality
    : apiSkin.quality || 'Factory New'
  
  // 获取外观信息 - 处理多层嵌套结构
  let exterior = ''
  let exteriorColor = undefined
  
  if (apiSkin.item_exterior) {
    exterior = getLocalizedField(apiSkin.item_exterior, 'exterior_name', locale) || ''
    exteriorColor = apiSkin.item_exterior.exterior_color
  } else {
    exterior = getLocalizedField(apiSkin, 'exterior', locale) || apiSkin.exterior || ''
  }
  
  // 获取分类信息
  const category = apiSkin.item_category
    ? getLocalizedField(apiSkin.item_category, 'cate_name', locale) || apiSkin.category
    : apiSkin.category || apiSkin.type || 'Weapon'

  // 判断是否StatTrak
  const isStatTrak = apiSkin.is_stattrak || localizedName.includes('StatTrak™')

  return {
    id: apiSkin.id.toString(),
    name: localizedName,
    weapon,
    skin,
    image,
    price,
    rarity,
    quality,
    exterior,
    category,
    isStatTrak,
    market_hash_name: apiSkin.market_hash_name || localizedName,
    // 保留原始数据中的颜色信息
    rarityColor: apiSkin.item_rarity?.rarity_color,
    qualityColor: apiSkin.item_quality?.quality_color,
    exteriorColor: exteriorColor,
    // 其他可能需要的字段
    created_at: apiSkin.created_at,
    updated_at: apiSkin.updated_at
  }
}

/**
 * 批量转换皮肤数据
 */
export const transformSkinItems = (apiSkins: SkinDetailItem[], locale: string = 'zh_hans'): TransformedSkinItem[] => {
  if (!Array.isArray(apiSkins)) {
    console.warn('[SkinTransformer] 输入数据不是数组:', apiSkins)
    return []
  }
  
  return apiSkins.map(skin => transformSkinItem(skin, locale))
}

/**
 * 转换简单的皮肤数据（用于已经简化的API响应）
 */
export const transformSimpleSkinItem = (apiSkin: any, locale: string = 'zh_hans'): TransformedSkinItem => {
  // 获取本地化名称 - 考虑多语言字段
  const localizedName = getLocalizedField(apiSkin, 'name', locale) || apiSkin.name || 'Unknown Skin'
  
  // 分离武器名称和皮肤名称
  const getWeaponAndSkinName = (fullName: string) => {
    const parts = fullName.split(' | ')
    if (parts.length >= 2) {
      const weapon = parts[0].trim()
      const skinPart = parts[1].trim()
      const skin = skinPart.split(' (')[0].trim() // 移除外观部分
      return { weapon, skin }
    }
    return { weapon: fullName, skin: '' }
  }

  const { weapon, skin } = getWeaponAndSkinName(localizedName)
  
  // 获取本地化的其他字段
  const localizedRarity = getLocalizedField(apiSkin, 'rarity', locale) || apiSkin.rarity || 'Common'
  const localizedQuality = getLocalizedField(apiSkin, 'quality', locale) || apiSkin.quality || 'Factory New'
  const localizedCategory = getLocalizedField(apiSkin, 'category', locale) || 
                           getLocalizedField(apiSkin, 'type', locale) || 
                           apiSkin.type || apiSkin.category || 'Weapon'
  
  // 处理外观多语言
  let exterior = ''
  let exteriorColor = undefined
  
  if (apiSkin.item_exterior) {
    exterior = getLocalizedField(apiSkin.item_exterior, 'exterior_name', locale) || ''
    exteriorColor = apiSkin.item_exterior.exterior_color
  } else {
    exterior = getLocalizedField(apiSkin, 'exterior', locale) || apiSkin.exterior || ''
  }
  
  // 处理价格，考虑多种可能的价格字段
  let price = 0
  if (apiSkin.item_price?.price) {
    const priceValue = typeof apiSkin.item_price.price === 'number' 
      ? apiSkin.item_price.price 
      : parseFloat(String(apiSkin.item_price.price))
    // 如果价格值大于1000，可能是以分为单位，需要转换为元
    price = priceValue > 1000 ? priceValue / 100 : priceValue
  } else {
    price = apiSkin.value || apiSkin.price || 0
  }

  return {
    id: apiSkin.id.toString(),
    name: localizedName,
    weapon,
    skin,
    image: apiSkin.image || apiSkin.icon_url || '',
    price: price,
    rarity: localizedRarity,
    quality: localizedQuality,
    exterior: exterior, 
    category: localizedCategory,
    isStatTrak: localizedName.includes('StatTrak™'),
    market_hash_name: apiSkin.market_hash_name || localizedName,
    // 添加颜色信息
    rarityColor: apiSkin.item_rarity?.rarity_color,
    qualityColor: apiSkin.item_quality?.quality_color,
    exteriorColor: exteriorColor
  }
}

/**
 * 批量转换简单皮肤数据
 */
export const transformSimpleSkinItems = (apiSkins: any[], locale: string = 'zh_hans'): TransformedSkinItem[] => {
  if (!Array.isArray(apiSkins)) {
    console.warn('[SkinTransformer] 输入数据不是数组:', apiSkins)
    return []
  }
  
  return apiSkins.map(skin => transformSimpleSkinItem(skin, locale))
} 