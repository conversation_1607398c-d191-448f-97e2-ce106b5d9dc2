# Generated by Django 3.2.25 on 2025-07-19 03:46

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('chat', '0005_auto_20250718_1647'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='chatbot',
            options={'verbose_name': '聊天机器人配置', 'verbose_name_plural': 'Chat Bot Config'},
        ),
        migrations.AlterModelOptions(
            name='chatbotmessage',
            options={'verbose_name': '聊天机器人消息', 'verbose_name_plural': 'Chat Bot Message'},
        ),
        migrations.AlterModelOptions(
            name='message',
            options={'ordering': ['-sent_at'], 'verbose_name': '消息', 'verbose_name_plural': 'Messages'},
        ),
        migrations.AlterField(
            model_name='chatbot',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='chat_bot', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
    ]
