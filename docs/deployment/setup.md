# 环境搭建指南

## 系统要求

### 开发环境
- **操作系统**: Linux (推荐 Ubuntu 18.04+) / macOS / Windows WSL2
- **Python**: 3.6+
- **Node.js**: 12.0+
- **Docker**: 20.0+
- **Docker Compose**: 1.25+

### 依赖服务
- **MySQL**: 5.7+ 或 8.0+
- **Redis**: 5.0+

## 快速开始 (Docker方式)

### 1. 克隆项目
```bash
git clone <repository-url>
cd csgoskins.com.cn
```

### 2. 环境配置
```bash
# 复制环境变量文件
cp server/example.env server/.env

# 编辑环境变量
vim server/.env
```

### 3. 启动服务
```bash
# 构建并启动所有服务
docker-compose up -d

# 查看运行状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 4. 初始化数据库
```bash
# 进入Django容器
docker-compose exec web bash

# 运行数据库迁移
python manage.py migrate

# 创建超级用户
python manage.py createsuperuser

# 加载初始数据 (如果有)
python manage.py loaddata initial_data.json
```

## 本地开发环境搭建

### 1. Python环境配置

#### 安装Python依赖
```bash
cd server/

# 创建虚拟环境 (推荐)
python3 -m venv venv
source venv/bin/activate  # Linux/macOS
# 或者
venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt
```

#### 主要依赖说明
```txt
# 核心框架
Django>=1.11.29,<2.0.0    # Django主框架
djangorestframework>=3.9.1 # REST API框架
gunicorn==19.9.0           # WSGI服务器

# Django扩展
django-redis==4.9.0        # Redis集成
django-modeltranslation==0.14 # 国际化
django-ckeditor==5.6.1     # 富文本编辑器

# 数据库
PyMySQL==0.8.1             # MySQL驱动
redis==2.10.6              # Redis客户端

# 异步任务 (自定义thworker框架)
schedule                   # 定时任务调度
threading                  # 多线程处理 (内置)
apscheduler                # 额外的任务调度支持

# 第三方服务
oss2>=2.9.0                # 阿里云OSS
aliyun-python-sdk-*        # 阿里云SDK套件
python-alipay-sdk          # 支付宝SDK
boto3==1.9.161             # AWS SDK

# 安全相关
cryptography==3.4.8        # 加密库
PyJWT==1.6.4               # JWT令牌
social-auth-app-django==2.1.0 # OAuth认证
```

### 2. Node.js环境配置

#### 安装Node.js依赖
```bash
cd deployment/node/nodejs/

# 安装依赖
npm install

# 主要依赖
npm install socket.io redis request cookie
```

#### WebSocket服务配置
```javascript
// ws_server.js配置项
const CONFIG = {
  skipAPIVerification: false,  // 生产环境必须设为false
  debug: true,                 // 开发环境启用调试
  apiBaseUrl: 'http://localhost:8000',
  redisOptions: {
    host: 'localhost',
    port: 6379,
    password: '',              // 如果Redis有密码
  }
};
```

### 3. 数据库配置

#### MySQL配置
```sql
-- 创建数据库
CREATE DATABASE csgoskins CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'csgoskins'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON csgoskins.* TO 'csgoskins'@'localhost';
FLUSH PRIVILEGES;
```

#### 环境变量配置 (.env文件)
```env
# 基本配置
DEBUG=True
ALLOWED_HOSTS=*

# 数据库配置
DATABASE_URL=mysql://csgoskins:your_password@localhost:3306/csgoskins

# Redis配置
REDIS_URL=rediscache://@127.0.0.1:6379/0

# Steam API配置
STEAM_API_KEY=your_steam_api_key

# 阿里云配置
ALIYUN_OSS_BUCKET_NAME=your_bucket
ALIYUN_OSS_ACCESS_KEY_ID=your_access_key_id
ALIYUN_OSS_ACCESS_KEY_SECRET=your_access_key_secret
ALIYUN_OSS_ENDPOINT=your_endpoint

# 支付配置
ALIPAY_APP_ID=your_alipay_app_id
ALIPAY_PRIVATE_KEY=your_private_key

# 短信配置
ALIYUN_SMS_ACCESS_KEY_ID=your_sms_key_id
ALIYUN_SMS_ACCESS_KEY_SECRET=your_sms_key_secret

# 邮件配置
SMTP_SERVER=your_smtp_server
SENDER_EMAIL=your_email
SMTP_TOKEN=your_token
SMTP_PORT=587

# 游戏配置
JACKPOT_GAME_COUNTDOWN=5
JACKPOT_GAME_WAIT=10
ROLL_ROOM_CREATE_MAX=3
CASE_ROOM_CREATE_MAX=3

# 交易配置
DEPOSIT_EXCHANGE_AUTO=True
PACKAGE_EXCHANGE_AUTO=True
PACKAGE_EXCHANGE_DELAY=60
```

### 4. 启动开发服务

#### 启动Django开发服务器
```bash
cd server/

# 运行数据库迁移
python manage.py migrate

# 收集静态文件
python manage.py collectstatic

# 启动开发服务器
python manage.py runserver 0.0.0.0:8000
```

#### 启动thworker异步任务
```bash
# 新终端窗口
cd server/

# 启动thworker (包含定时任务和后台任务)
python manage.py setupworker

# 查看worker运行状态
ps aux | grep setupworker
```

#### 启动WebSocket服务
```bash
# 新终端窗口
cd deployment/node/nodejs/

# 启动WebSocket服务
node ws_server.js
```

### 5. 开发工具配置

#### 代码编辑器配置 (VS Code)
```json
// .vscode/settings.json
{
  "python.defaultInterpreterPath": "./server/venv/bin/python",
  "python.linting.enabled": true,
  "python.linting.pylintEnabled": true,
  "python.formatting.provider": "black",
  "files.exclude": {
    "**/__pycache__": true,
    "**/*.pyc": true
  }
}
```

#### Git配置
```bash
# .gitignore 重要配置
server/.env
server/logs/
server/media/
**/__pycache__/
**/*.pyc
*.log
node_modules/
```

## 常见问题解决

### 1. 数据库连接问题
```bash
# 检查MySQL服务状态
sudo systemctl status mysql

# 检查数据库连接
python manage.py dbshell
```

### 2. Redis连接问题
```bash
# 检查Redis服务状态
sudo systemctl status redis

# 测试Redis连接
redis-cli ping
```

### 3. Python依赖安装问题
```bash
# 升级pip
pip install --upgrade pip

# 清理缓存后重新安装
pip cache purge
pip install -r requirements.txt
```

### 4. Node.js依赖问题
```bash
# 清理npm缓存
npm cache clean --force

# 删除node_modules重新安装
rm -rf node_modules/
npm install
```

### 5. 端口占用问题
```bash
# 查看端口占用
lsof -i :8000
lsof -i :4000

# 杀死占用端口的进程
kill -9 <PID>
```

## 开发规范

### 1. 代码规范
- **Python**: 遵循PEP 8规范
- **JavaScript**: 使用ESLint
- **Django**: 遵循Django最佳实践

### 2. Git工作流
```bash
# 创建功能分支
git checkout -b feature/new-feature

# 提交代码
git add .
git commit -m "feat: add new feature"

# 推送分支
git push origin feature/new-feature

# 创建Pull Request
```

### 3. 测试规范
```bash
# 运行Django测试
python manage.py test

# 运行特定应用测试
python manage.py test authentication

# 生成测试覆盖率报告
coverage run --source='.' manage.py test
coverage report
```

### 4. 日志调试
```bash
# 查看Django日志
tail -f logs/steambase.log

# 查看特定模块日志
tail -f logs/steambase_service.log

# 查看WebSocket日志
tail -f logs/websocket.log
```

## 下一步

环境搭建完成后，建议：

1. 阅读 [API文档](./api.md) 了解接口设计
2. 查看 [数据库设计](./database.md) 理解数据模型
3. 参考 [部署指南](./deployment.md) 了解生产环境配置
4. 查阅 [常见问题](./faq.md) 解决开发中的问题
