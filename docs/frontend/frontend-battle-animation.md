# 前端对战动画规划方案

## 概述

本文档基于后端WebSocket消息流，为前端提供完整的对战动画实现方案。通过分析后端的消息时序和数据结构，设计了一套流畅、同步的动画系统。

## 动画阶段分析

### 1. 房间准备阶段
- **触发消息**: `["boxroom", "update", roomData]`
- **触发条件**: 用户加入房间
- **动画效果**: 用户头像飞入、位置占用动画
- **状态变化**: `state: 2` (可加入)
- **持续时间**: 800ms

### 2. 满员等待阶段  
- **触发消息**: `["boxroom", "update", roomData]`
- **触发条件**: `state: 4` 且 `joiner_count === max_joiner`
- **动画效果**: 满员特效、倒计时动画
- **状态变化**: `state: 4` (已满员)
- **持续时间**: 3000ms

### 3. 对战开始阶段
- **触发消息**: `["boxroom", "start", roomData]`
- **触发条件**: 后端自动推进房间状态
- **动画效果**: 开始倒计时、VS特效
- **状态变化**: `state: 5` (进行中)
- **持续时间**: 3000ms

### 4. 回合动画阶段
- **触发消息**: `["boxroomdetail", "round", betsData, socketId]`
- **触发条件**: 每个回合的开箱结果
- **动画效果**: 开箱动画、物品展示
- **数据来源**: `betsData` 包含所有玩家的开箱结果
- **持续时间**: 4500ms (晃动1s + 开箱2s + 展示1.5s)

### 5. 对战结束阶段
- **触发消息**: `["boxroomdetail", "end", finalData, socketId]`
- **触发条件**: 所有回合完成后
- **动画效果**: 胜负判定、最终结果展示
- **状态变化**: `state: 11` (已结束)
- **持续时间**: 4000ms

## 核心动画控制器

```javascript
class BattleAnimationController {
    constructor(container) {
        this.container = container;
        this.players = [];
        this.currentRound = 0;
        this.totalRounds = 3;
        this.animationQueue = [];
        this.isAnimating = false;
    }

    // 初始化房间UI
    initRoom(roomData) {
        this.totalRounds = roomData.round_count;
        this.setupPlayerSlots(roomData.max_joiner);
        this.renderRoomInfo(roomData);
    }

    // 处理用户加入动画
    handlePlayerJoined(roomData) {
        const newPlayerCount = roomData.joiner_count;
        const players = roomData.users || [];
        
        // 找到新加入的玩家
        const newPlayer = players[newPlayerCount - 1];
        if (newPlayer) {
            this.animatePlayerJoin(newPlayer, newPlayerCount - 1);
        }

        // 检查是否满员
        if (newPlayerCount === roomData.max_joiner && roomData.state === 4) {
            this.animateRoomFull();
        }
    }

    // 满员动画
    animateRoomFull() {
        this.queueAnimation(() => {
            // 1. 满员特效
            this.showFullRoomEffect();
            
            // 2. 倒计时动画（3秒）
            return this.showCountdown(3, '对战即将开始');
        });
    }

    // 对战开始动画
    handleBattleStart(data) {
        this.queueAnimation(() => {
            // 1. VS特效
            this.showVSEffect();
            
            // 2. 开始倒计时
            return this.showCountdown(3, '对战开始');
        });
    }

    // 回合动画
    handleRound(roundData) {
        this.currentRound = roundData.round;
        
        this.queueAnimation(async () => {
            // 1. 回合开始提示
            await this.showRoundStart(this.currentRound);
            
            // 2. 同步开箱动画
            await this.playOpeningAnimations(roundData.bets);
            
            // 3. 结果展示
            await this.showRoundResults(roundData.bets);
            
            // 4. 更新积分
            this.updateScores(roundData.bets);
        });
    }

    // 开箱动画
    async playOpeningAnimations(bets) {
        const animations = bets.map((bet, index) => {
            return this.createOpeningAnimation(bet, index);
        });

        // 所有玩家同时开箱
        await Promise.all(animations);
    }

    // 单个玩家开箱动画
    createOpeningAnimation(bet, playerIndex) {
        return new Promise((resolve) => {
            const playerSlot = this.getPlayerSlot(playerIndex);
            const caseBox = playerSlot.querySelector('.case-box');
            const resultArea = playerSlot.querySelector('.result-area');

            // 1. 箱子晃动动画
            caseBox.classList.add('shaking');
            
            setTimeout(() => {
                // 2. 箱子打开动画
                caseBox.classList.add('opening');
                
                setTimeout(() => {
                    // 3. 物品飞出动画
                    this.showItemDrop(bet.drop, resultArea);
                    caseBox.classList.remove('shaking', 'opening');
                    resolve();
                }, 2000); // 开箱动画2秒
            }, 1000); // 晃动1秒
        });
    }

    // 对战结束动画
    handleBattleEnd(endData) {
        this.queueAnimation(async () => {
            // 1. 最终结果计算动画
            await this.showFinalCalculation();
            
            // 2. 胜者揭晓动画
            await this.showWinnerReveal(endData.winner);
            
            // 3. 最终结果展示
            this.showFinalResults(endData.final_results);
        });
    }

    // 动画队列管理
    queueAnimation(animationFn) {
        this.animationQueue.push(animationFn);
        this.processQueue();
    }

    async processQueue() {
        if (this.isAnimating || this.animationQueue.length === 0) return;
        
        this.isAnimating = true;
        const animation = this.animationQueue.shift();
        
        try {
            await animation();
        } catch (error) {
            console.error('Animation error:', error);
        }
        
        this.isAnimating = false;
        this.processQueue();
    }

    // 工具方法
    showCountdown(seconds, message) {
        return new Promise((resolve) => {
            const overlay = this.createCountdownOverlay(message);
            let count = seconds;
            
            const timer = setInterval(() => {
                overlay.querySelector('.countdown-number').textContent = count;
                count--;
                
                if (count < 0) {
                    clearInterval(timer);
                    overlay.remove();
                    resolve();
                }
            }, 1000);
        });
    }

    showItemDrop(item, container) {
        const itemElement = this.createItemElement(item);
        itemElement.classList.add('item-drop-animation');
        container.appendChild(itemElement);

        // 根据稀有度添加特效
        this.addRarityEffect(itemElement, item.item_rarity.rarity_color);
    }

    addRarityEffect(element, rarityColor) {
        // 根据稀有度颜色添加不同的特效
        const effects = {
            '#eb4b4b': 'legendary-effect',  // 红色传说
            '#d32ce6': 'mythical-effect',   // 紫色神话  
            '#8847ff': 'rare-effect',       // 蓝色稀有
            '#4b69ff': 'uncommon-effect'    // 浅蓝色不常见
        };

        const effectClass = effects[rarityColor] || 'common-effect';
        element.classList.add(effectClass);
    }

    // 辅助方法实现
    setupPlayerSlots(maxJoiner) {
        const slotsContainer = this.container.querySelector('.players-container');
        slotsContainer.innerHTML = '';
        
        for (let i = 0; i < maxJoiner; i++) {
            const slot = document.createElement('div');
            slot.className = 'player-slot';
            slot.innerHTML = `
                <div class="player-info" style="display: none;">
                    <img class="avatar" src="" alt="">
                    <span class="nickname"></span>
                    <div class="score">0</div>
                </div>
                <div class="case-box"></div>
                <div class="result-area"></div>
            `;
            slotsContainer.appendChild(slot);
        }
    }

    animatePlayerJoin(player, slotIndex) {
        const slot = this.getPlayerSlot(slotIndex);
        const playerInfo = slot.querySelector('.player-info');
        
        // 更新玩家信息
        playerInfo.querySelector('.avatar').src = player.profile.avatar;
        playerInfo.querySelector('.nickname').textContent = player.profile.nick_name;
        
        // 显示并播放动画
        playerInfo.style.display = 'block';
        playerInfo.classList.add('player-join-animation');
        
        // 播放音效
        SoundManager.play('playerJoin');
    }

    showFullRoomEffect() {
        this.container.classList.add('room-full-effect');
        SoundManager.play('roomFull');
        
        setTimeout(() => {
            this.container.classList.remove('room-full-effect');
        }, 1000);
    }

    showVSEffect() {
        const vsOverlay = document.createElement('div');
        vsOverlay.className = 'vs-overlay';
        vsOverlay.innerHTML = '<div class="vs-text">VS</div>';
        this.container.appendChild(vsOverlay);
        
        SoundManager.play('battleStart');
        
        setTimeout(() => {
            vsOverlay.remove();
        }, 2000);
    }

    getPlayerSlot(index) {
        return this.container.querySelectorAll('.player-slot')[index];
    }

    createCountdownOverlay(message) {
        const overlay = document.createElement('div');
        overlay.className = 'animation-overlay';
        overlay.innerHTML = `
            <div class="countdown-container">
                <div class="countdown-message">${message}</div>
                <div class="countdown-number">3</div>
            </div>
        `;
        this.container.appendChild(overlay);
        return overlay;
    }

    createItemElement(item) {
        const element = document.createElement('div');
        element.className = 'item-result';
        element.innerHTML = `
            <img src="${item.icon_url}" alt="${item.market_name}">
            <div class="item-name">${item.market_name}</div>
            <div class="item-price">$${item.item_price.price}</div>
        `;
        return element;
    }
}
```

## CSS动画样式

```css
/* 玩家加入动画 */
@keyframes playerJoinAnimation {
    0% {
        transform: translateY(-100px) scale(0);
        opacity: 0;
    }
    60% {
        transform: translateY(10px) scale(1.1);
    }
    100% {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

.player-join-animation {
    animation: playerJoinAnimation 0.8s ease-out forwards;
}

/* 满员特效 */
@keyframes roomFullEffect {
    0% { 
        box-shadow: 0 0 0 0 rgba(103, 194, 58, 0.7); 
        background-color: rgba(103, 194, 58, 0.1);
    }
    70% { 
        box-shadow: 0 0 0 20px rgba(103, 194, 58, 0);
        background-color: rgba(103, 194, 58, 0.3);
    }
    100% { 
        box-shadow: 0 0 0 0 rgba(103, 194, 58, 0); 
        background-color: transparent;
    }
}

.room-full-effect {
    animation: roomFullEffect 1s ease-out;
}

/* VS特效 */
.vs-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.8);
    z-index: 1000;
}

.vs-text {
    font-size: 120px;
    font-weight: bold;
    color: #ff6b6b;
    text-shadow: 0 0 20px #ff6b6b;
    animation: vsAnimation 2s ease-out;
}

@keyframes vsAnimation {
    0% {
        transform: scale(0) rotate(-180deg);
        opacity: 0;
    }
    50% {
        transform: scale(1.2) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
}

/* 开箱动画 */
@keyframes caseShaking {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
    20%, 40%, 60%, 80% { transform: translateX(2px); }
}

@keyframes caseOpening {
    0% { 
        transform: scale(1) rotateY(0deg); 
        background: linear-gradient(45deg, #333, #666);
    }
    50% { 
        transform: scale(1.2) rotateY(90deg); 
        background: linear-gradient(45deg, #666, #999);
    }
    100% { 
        transform: scale(1) rotateY(180deg); 
        background: linear-gradient(45deg, #999, #ccc);
    }
}

.case-box {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, #333, #666);
    border-radius: 8px;
    margin: 10px auto;
    position: relative;
}

.case-box.shaking {
    animation: caseShaking 1s ease-in-out;
}

.case-box.opening {
    animation: caseOpening 2s ease-in-out;
}

/* 物品掉落动画 */
@keyframes itemDropAnimation {
    0% {
        transform: translateY(-50px) scale(0) rotate(-180deg);
        opacity: 0;
    }
    50% {
        transform: translateY(0) scale(1.2) rotate(-90deg);
        opacity: 1;
    }
    100% {
        transform: translateY(0) scale(1) rotate(0deg);
        opacity: 1;
    }
}

.item-drop-animation {
    animation: itemDropAnimation 1.5s ease-out forwards;
}

/* 稀有度特效 */
.legendary-effect {
    box-shadow: 0 0 20px #eb4b4b;
    animation: legendaryGlow 2s infinite alternate;
}

@keyframes legendaryGlow {
    0% { 
        box-shadow: 0 0 20px #eb4b4b;
        transform: scale(1);
    }
    100% { 
        box-shadow: 0 0 40px #eb4b4b, 0 0 60px #eb4b4b;
        transform: scale(1.05);
    }
}

.mythical-effect {
    box-shadow: 0 0 15px #d32ce6;
    animation: mythicalGlow 1.8s infinite alternate;
}

@keyframes mythicalGlow {
    0% { box-shadow: 0 0 15px #d32ce6; }
    100% { box-shadow: 0 0 30px #d32ce6, 0 0 45px #d32ce6; }
}

.rare-effect {
    box-shadow: 0 0 10px #8847ff;
    animation: rareGlow 1.5s infinite alternate;
}

@keyframes rareGlow {
    0% { box-shadow: 0 0 10px #8847ff; }
    100% { box-shadow: 0 0 20px #8847ff; }
}

.uncommon-effect {
    box-shadow: 0 0 8px #4b69ff;
}

/* 倒计时动画 */
.animation-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.7);
    z-index: 999;
}

.countdown-container {
    text-align: center;
    color: white;
}

.countdown-message {
    font-size: 24px;
    margin-bottom: 20px;
}

.countdown-number {
    font-size: 80px;
    font-weight: bold;
    animation: countdownPulse 1s ease-in-out infinite;
}

@keyframes countdownPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

/* 玩家槽位布局 */
.battle-arena {
    position: relative;
    width: 100%;
    height: 600px;
    background: linear-gradient(135deg, #1e3c72, #2a5298);
    border-radius: 12px;
    overflow: hidden;
}

.room-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.players-container {
    display: flex;
    justify-content: space-around;
    align-items: center;
    height: calc(100% - 80px);
    padding: 20px;
}

.player-slot {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 150px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    border: 2px dashed #ccc;
    transition: all 0.3s ease;
}

.player-slot.occupied {
    border: 2px solid #67c23a;
    background: rgba(103, 194, 58, 0.1);
}

.player-info {
    text-align: center;
    margin-bottom: 15px;
}

.player-info .avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin-bottom: 8px;
}

.player-info .nickname {
    display: block;
    color: white;
    font-weight: bold;
    margin-bottom: 5px;
}

.player-info .score {
    background: #409eff;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 14px;
}

.result-area {
    min-height: 120px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.item-result {
    text-align: center;
    padding: 10px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    margin-top: 10px;
}

.item-result img {
    width: 60px;
    height: 60px;
    margin-bottom: 5px;
}

.item-name {
    font-size: 12px;
    color: #333;
    margin-bottom: 3px;
}

.item-price {
    font-size: 14px;
    font-weight: bold;
    color: #67c23a;
}

/* 老虎机滚轮式对战动画样式 */
.battle-slot-container {
    width: 100%;
    max-width: 60rem;
    margin: 0 auto;
    padding: 1.5rem;
    background: linear-gradient(135deg, #0f1419, #1a1a2e);
    border-radius: 1rem;
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.6);
    border: 0.1rem solid rgba(255, 215, 0, 0.3);
    
    @media (max-width: 768px) {
        max-width: 95vw;
        padding: 1rem;
    }
}

// 对战头部
.battle-header {
    text-align: center;
    margin-bottom: 2rem;
    
    .battle-title {
        font-size: 1.8rem;
        font-weight: bold;
        color: #ffd700;
        margin-bottom: 1rem;
        text-shadow: 0 0 1rem rgba(255, 215, 0, 0.5);
    }
    
    .battle-timer {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
        
        .timer-text {
            color: white;
            font-size: 1.1rem;
            
            .round-number {
                color: #ff6b6b;
                font-weight: bold;
            }
        }
        
        .timer-bar {
            width: 20rem;
            height: 0.4rem;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 0.2rem;
            overflow: hidden;
            
            @media (max-width: 768px) {
                width: 15rem;
            }
            
            .timer-progress {
                height: 100%;
                background: linear-gradient(90deg, #ff6b6b, #feca57);
                border-radius: 0.2rem;
                transition: width 0.1s linear;
                width: 100%;
            }
        }
    }
}

// 玩家滚轮区域
.players-slots {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
    margin-bottom: 2rem;
    
    @media (max-width: 768px) {
        flex-direction: column;
        gap: 1rem;
    }
}

// 单个玩家滚轮
.player-slot {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    
    .player-info {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 0.5rem;
        border: 0.1rem solid rgba(255, 255, 255, 0.1);
        
        .player-avatar {
            width: 3rem;
            height: 3rem;
            border-radius: 50%;
            object-fit: cover;
            border: 0.2rem solid #ffd700;
        }
        
        .player-name {
            color: white;
            font-weight: bold;
            font-size: 1rem;
        }
        
        .player-score {
            color: #ffd700;
            font-size: 1.2rem;
            font-weight: bold;
        }
    }
    
    // 迷你滚轮机器
    .slot-machine.mini {
        transform: scale(0.8);
        
        .machine-frame {
            .frame-glow {
                position: absolute;
                top: -0.3rem;
                left: -0.3rem;
                right: -0.3rem;
                bottom: -0.3rem;
                border-radius: 1.2rem;
                opacity: 0;
                transition: opacity 0.3s ease;
                
                &.player-1 {
                    box-shadow: 0 0 2rem rgba(74, 144, 226, 0.6);
                    border: 0.2rem solid #4A90E2;
                }
                
                &.player-2 {
                    box-shadow: 0 0 2rem rgba(231, 76, 60, 0.6);
                    border: 0.2rem solid #E74C3C;
                }
                
                &.active {
                    opacity: 1;
                    animation: glowPulse 2s ease-in-out infinite;
                }
            }
        }
        
        .reel-window {
            .vs-indicator {
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                font-size: 1.2rem;
                font-weight: bold;
                color: #ffd700;
                text-shadow: 0 0 0.5rem rgba(255, 215, 0, 0.8);
                z-index: 10;
                
                &.left { left: -2rem; }
                &.right { right: -2rem; }
            }
        }
    }
}

@keyframes glowPulse {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 1; }
}

// VS分隔符
.vs-divider {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    
    @media (max-width: 768px) {
        order: -1;
        margin-bottom: 1rem;
    }
    
    .vs-text {
        font-size: 2rem;
        font-weight: bold;
        color: #ffd700;
        text-shadow: 0 0 1rem rgba(255, 215, 0, 0.8);
        z-index: 2;
        position: relative;
    }
    
    .lightning-effect {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 4rem;
        height: 4rem;
        background: radial-gradient(circle, rgba(255, 215, 0, 0.3), transparent);
        border-radius: 50%;
        opacity: 0;
        
        &.active {
            animation: lightningFlash 0.5s ease-in-out;
        }
    }
}

@keyframes lightningFlash {
    0%, 100% { opacity: 0; transform: translate(-50%, -50%) scale(1); }
    50% { opacity: 1; transform: translate(-50%, -50%) scale(1.5); }
}

// 回合结果
.round-result {
    text-align: center;
    padding: 1.5rem;
    background: linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 107, 107, 0.1));
    border-radius: 0.5rem;
    border: 0.1rem solid rgba(255, 215, 0, 0.3);
    opacity: 0;
    transform: translateY(2rem);
    transition: all 0.5s ease;
    
    &.show {
        opacity: 1;
        transform: translateY(0);
    }
    
    .winner-announcement {
        .winner-text {
            display: block;
            font-size: 1.4rem;
            font-weight: bold;
            margin-bottom: 1rem;
            
            &.player-1-wins {
                color: #4A90E2;
                text-shadow: 0 0 1rem rgba(74, 144, 226, 0.6);
            }
            
            &.player-2-wins {
                color: #E74C3C;
                text-shadow: 0 0 1rem rgba(231, 76, 60, 0.6);
            }
            
            &.tie {
                color: #ffd700;
                text-shadow: 0 0 1rem rgba(255, 215, 0, 0.6);
            }
        }
        
        .round-score {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
            font-size: 1.2rem;
            font-weight: bold;
            
            .score-player-1 { color: #4A90E2; }
            .score-player-2 { color: #E74C3C; }
            .score-vs { color: #ffd700; }
        }
    }
}
```

## Vue组件集成示例

```vue
<template>
  <div class="battle-arena" ref="battleContainer">
    <!-- 房间信息 -->
    <div class="room-header">
      <h3>{{ room?.case?.name }} - {{ room?.short_id }}</h3>
      <div class="round-indicator">{{ currentRound }}/{{ totalRounds }}</div>
    </div>

    <!-- 玩家槽位 -->
    <div class="players-container">
      <div 
        v-for="(slot, index) in playerSlots" 
        :key="index"
        class="player-slot"
        :class="{ 'occupied': slot.player }"
      >
        <div v-if="slot.player" class="player-info">
          <img :src="slot.player.profile.avatar" :alt="slot.player.profile.nick_name">
          <span>{{ slot.player.profile.nick_name }}</span>
          <div class="score">{{ slot.score }}</div>
        </div>
        
        <div class="case-box"></div>
        <div class="result-area"></div>
      </div>
    </div>

    <!-- 动画覆盖层 -->
    <div v-if="showOverlay" class="animation-overlay">
      <div class="countdown-container">
        <div class="countdown-message">{{ overlayMessage }}</div>
        <div class="countdown-number">{{ countdownNumber }}</div>
      </div>
    </div>

    <!-- 老虎机滚轮对战区域 -->
    <div class="battle-slot-container" v-if="showSlotAnimation">
      <!-- 对战信息头部 -->
      <div class="battle-header">
        <div class="battle-title">对战开箱</div>
        <div class="battle-timer">
          <span class="timer-text">第 <span class="round-number">1</span> 轮</span>
          <div class="timer-bar">
            <div class="timer-progress"></div>
          </div>
        </div>
      </div>
      
      <!-- 玩家滚轮区域 -->
      <div class="players-slots">
        <!-- 玩家1滚轮 -->
        <div class="player-slot" data-player-id="1">
          <div class="player-info">
            <img src="avatar1.jpg" class="player-avatar" alt="Player 1">
            <span class="player-name">玩家1</span>
            <span class="player-score">$0</span>
          </div>
          
          <!-- 滚轮机器 -->
          <div class="slot-machine mini">
            <div class="machine-frame">
              <div class="frame-glow player-1"></div>
            </div>
            
            <div class="reels-container">
              <div class="reel battle-reel">
                <div class="reel-strip">
                  <!-- 动态填充物品 -->
                </div>
                <div class="reel-window">
                  <div class="vs-indicator left">VS</div>
                </div>
              </div>
            </div>
            
            <div class="result-display">
              <div class="winning-item">
                <img src="" alt="" class="item-image">
                <div class="item-info">
                  <span class="item-name"></span>
                  <span class="item-value"></span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- VS分隔符 -->
        <div class="vs-divider">
          <div class="vs-text">VS</div>
          <div class="lightning-effect"></div>
        </div>
        
        <!-- 玩家2滚轮 -->
        <div class="player-slot" data-player-id="2">
          <div class="player-info">
            <img src="avatar2.jpg" class="player-avatar" alt="Player 2">
            <span class="player-name">玩家2</span>
            <span class="player-score">$0</span>
          </div>
          
          <div class="slot-machine mini">
            <div class="machine-frame">
              <div class="frame-glow player-2"></div>
            </div>
            
            <div class="reels-container">
              <div class="reel battle-reel">
                <div class="reel-strip">
                  <!-- 动态填充物品 -->
                </div>
                <div class="reel-window">
                  <div class="vs-indicator right">VS</div>
                </div>
              </div>
            </div>
            
            <div class="result-display">
              <div class="winning-item">
                <img src="" alt="" class="item-image">
                <div class="item-info">
                  <span class="item-name"></span>
                  <span class="item-value"></span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 回合结果展示 -->
      <div class="round-result">
        <div class="winner-announcement">
          <span class="winner-text"></span>
          <div class="round-score">
            <span class="score-player-1">$0</span>
            <span class="score-vs">-</span>
            <span class="score-player-2">$0</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BattleArena',
  props: {
    roomUid: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      room: null,
      animationController: null,
      playerSlots: Array(4).fill(null).map(() => ({ player: null, score: 0 })),
      currentRound: 0,
      totalRounds: 3,
      showOverlay: false,
      overlayMessage: '',
      countdownNumber: 0,
      showSlotAnimation: false
    }
  },
  mounted() {
    this.animationController = new BattleAnimationController(this.$refs.battleContainer);
    this.initWebSocket();
    this.loadRoomData();
  },
  beforeUnmount() {
    if (this.gameSocket) {
      this.gameSocket.disconnect();
    }
  },
  methods: {
    async loadRoomData() {
      try {
        const response = await this.$api.get(`/battle/detail/?uid=${this.roomUid}`);
        if (response.data.code === 0) {
          this.room = response.data.body;
          this.animationController.initRoom(this.room);
          this.updatePlayerSlots();
        }
      } catch (error) {
        console.error('Failed to load room data:', error);
      }
    },

    updatePlayerSlots() {
      if (!this.room) return;
      
      const users = this.room.users || [];
      this.playerSlots = Array(this.room.max_joiner).fill(null).map((_, index) => ({
        player: users[index] || null,
        score: 0
      }));
    },

    initWebSocket() {
      this.gameSocket = new GameWebSocket('ws://localhost:8000/ws/');
      
      // 设置消息处理回调
      this.gameSocket.onRoomUpdated = (data) => {
        if (this.room && this.room.uid === data.uid) {
          this.room = { ...this.room, ...data };
          this.animationController.handlePlayerJoined(data);
          this.updatePlayerSlots();
        }
      };
      
      this.gameSocket.onBattleStarted = (data) => {
        this.animationController.handleBattleStart(data);
      };
      
      this.gameSocket.onRoundStarted = (data) => {
        this.currentRound = data.round;
        this.animationController.handleRound(data);
      };
      
      this.gameSocket.onBattleEnded = (data) => {
        this.animationController.handleBattleEnd(data);
      };
      
      this.gameSocket.connect();
    },

    handleWebSocketMessage(messageType, action, data) {
      switch (messageType) {
        case 'boxroom':
          if (action === 'update') {
            this.gameSocket.onRoomUpdated(data);
          } else if (action === 'start') {
            this.gameSocket.onBattleStarted(data);
          }
          break;
          
        case 'boxroomdetail':
          if (action === 'round') {
            this.gameSocket.onRoundStarted(data);
          } else if (action === 'end') {
            this.gameSocket.onBattleEnded(data);
          }
          break;
      }
    }
  }
}
</script>

<style scoped>
/* 在这里引入上面定义的CSS样式 */
</style>
```

## 音效管理

```javascript
class SoundManager {
    static sounds = {
        playerJoin: '/sounds/player-join.mp3',
        roomFull: '/sounds/room-full.mp3',
        battleStart: '/sounds/battle-start.mp3',
        caseShaking: '/sounds/case-shaking.mp3',
        caseOpening: '/sounds/case-opening.mp3',
        itemDrop: '/sounds/item-drop.mp3',
        legendary: '/sounds/legendary-drop.mp3',
        victory: '/sounds/victory.mp3',
        defeat: '/sounds/defeat.mp3'
    };

    static audioCache = new Map();
    static volume = 0.6;
    static enabled = true;

    static preload() {
        Object.entries(this.sounds).forEach(([key, url]) => {
            const audio = new Audio(url);
            audio.preload = 'auto';
            audio.volume = this.volume;
            this.audioCache.set(key, audio);
        });
    }

    static play(soundName, volume = this.volume) {
        if (!this.enabled) return;

        let audio = this.audioCache.get(soundName);
        if (!audio) {
            audio = new Audio(this.sounds[soundName]);
            this.audioCache.set(soundName, audio);
        }

        audio.volume = volume;
        audio.currentTime = 0;
        audio.play().catch(error => {
            console.warn(`Failed to play sound ${soundName}:`, error);
        });
    }

    static setVolume(volume) {
        this.volume = Math.max(0, Math.min(1, volume));
        this.audioCache.forEach(audio => {
            audio.volume = this.volume;
        });
    }

    static toggle() {
        this.enabled = !this.enabled;
        return this.enabled;
    }
}

// 在应用启动时预加载音效
SoundManager.preload();
```

## 动画时间配置

```javascript
const ANIMATION_TIMINGS = {
    // 基础动画时长
    PLAYER_JOIN: 800,        // 玩家加入动画时长
    ROOM_FULL_EFFECT: 1000,  // 满员特效时长
    COUNTDOWN: 3000,         // 倒计时时长
    VS_EFFECT: 2000,         // VS特效时长
    
    // 开箱动画时长
    CASE_SHAKING: 1000,      // 箱子晃动时长
    CASE_OPENING: 2000,      // 开箱动画时长
    ITEM_DROP: 1500,         // 物品掉落时长
    RESULT_DISPLAY: 3000,    // 结果展示时长
    
    // 结束动画时长
    FINAL_CALCULATION: 2000, // 最终计算时长
    WINNER_REVEAL: 4000,     // 胜者揭晓时长
    FINAL_RESULTS: 5000,     // 最终结果展示时长
    
    // 过渡动画时长
    FADE_IN: 300,           // 淡入动画
    FADE_OUT: 300,          // 淡出动画
    SLIDE_IN: 500,          // 滑入动画
    SLIDE_OUT: 500,         // 滑出动画
};

// 动画缓动函数
const ANIMATION_EASINGS = {
    EASE_OUT: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
    EASE_IN: 'cubic-bezier(0.55, 0.055, 0.675, 0.19)',
    EASE_IN_OUT: 'cubic-bezier(0.645, 0.045, 0.355, 1)',
    BOUNCE: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
    ELASTIC: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)'
};
```

## 性能优化建议

### 1. 动画性能优化
- 使用 `transform` 和 `opacity` 进行动画，避免触发重排
- 对频繁动画的元素使用 `will-change` 属性
- 使用 `requestAnimationFrame` 进行复杂动画计算
- 及时清理动画监听器和定时器

### 2. 内存管理
- 动画结束后及时移除动画类名
- 清理不再使用的DOM元素
- 合理使用音效缓存，避免重复加载

### 3. 用户体验优化
- 提供动画开关选项
- 支持跳过动画功能
- 根据设备性能调整动画复杂度
- 提供无障碍访问支持

## 测试建议

### 1. 功能测试
- 测试各种网络延迟情况下的动画同步
- 测试快速连续消息的动画队列处理
- 测试异常情况下的动画回退

### 2. 性能测试
- 监控动画期间的FPS表现
- 测试内存使用情况
- 测试不同设备上的性能表现

### 3. 兼容性测试
- 测试不同浏览器的兼容性
- 测试移动端的触摸交互
- 测试屏幕阅读器的支持

---

最后更新时间: 2025-06-17

## 老虎机滚轮式对战动画

为了与开箱动画保持视觉一致性，对战动画也支持老虎机滚轮式风格，特别适用于对战开箱环节。

### 对战滚轮动画结构

```html
<!-- 对战滚轮容器 -->
<div class="battle-slot-container">
    <!-- 对战信息头部 -->
    <div class="battle-header">
        <div class="battle-title">对战开箱</div>
        <div class="battle-timer">
            <span class="timer-text">第 <span class="round-number">1</span> 轮</span>
            <div class="timer-bar">
                <div class="timer-progress"></div>
            </div>
        </div>
    </div>
    
    <!-- 玩家滚轮区域 -->
    <div class="players-slots">
        <!-- 玩家1滚轮 -->
        <div class="player-slot" data-player-id="1">
            <div class="player-info">
                <img src="avatar1.jpg" class="player-avatar" alt="Player 1">
                <span class="player-name">玩家1</span>
                <span class="player-score">$0</span>
            </div>
            
            <!-- 滚轮机器 -->
            <div class="slot-machine mini">
                <div class="machine-frame">
                    <div class="frame-glow player-1"></div>
                </div>
                
                <div class="reels-container">
                    <div class="reel battle-reel">
                        <div class="reel-strip">
                            <!-- 动态填充物品 -->
                        </div>
                        <div class="reel-window">
                            <div class="vs-indicator left">VS</div>
                        </div>
                    </div>
                </div>
                
                <div class="result-display">
                    <div class="winning-item">
                        <img src="" alt="" class="item-image">
                        <div class="item-info">
                            <span class="item-name"></span>
                            <span class="item-value"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- VS分隔符 -->
        <div class="vs-divider">
            <div class="vs-text">VS</div>
            <div class="lightning-effect"></div>
        </div>
        
        <!-- 玩家2滚轮 -->
        <div class="player-slot" data-player-id="2">
            <div class="player-info">
                <img src="avatar2.jpg" class="player-avatar" alt="Player 2">
                <span class="player-name">玩家2</span>
                <span class="player-score">$0</span>
            </div>
            
            <div class="slot-machine mini">
                <div class="machine-frame">
                    <div class="frame-glow player-2"></div>
                </div>
                
                <div class="reels-container">
                    <div class="reel battle-reel">
                        <div class="reel-strip">
                            <!-- 动态填充物品 -->
                        </div>
                        <div class="reel-window">
                            <div class="vs-indicator right">VS</div>
                        </div>
                    </div>
                </div>
                
                <div class="result-display">
                    <div class="winning-item">
                        <img src="" alt="" class="item-image">
                        <div class="item-info">
                            <span class="item-name"></span>
                            <span class="item-value"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 回合结果展示 -->
    <div class="round-result">
        <div class="winner-announcement">
            <span class="winner-text"></span>
            <div class="round-score">
                <span class="score-player-1">$0</span>
                <span class="score-vs">-</span>
                <span class="score-player-2">$0</span>
            </div>
        </div>
    </div>
</div>
```

### 对战滚轮样式

```scss
// 对战滚轮容器
.battle-slot-container {
    width: 100%;
    max-width: 60rem;
    margin: 0 auto;
    padding: 1.5rem;
    background: linear-gradient(135deg, #0f1419, #1a1a2e);
    border-radius: 1rem;
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.6);
    border: 0.1rem solid rgba(255, 215, 0, 0.3);
    
    @media (max-width: 768px) {
        max-width: 95vw;
        padding: 1rem;
    }
}

// 对战头部
.battle-header {
    text-align: center;
    margin-bottom: 2rem;
    
    .battle-title {
        font-size: 1.8rem;
        font-weight: bold;
        color: #ffd700;
        margin-bottom: 1rem;
        text-shadow: 0 0 1rem rgba(255, 215, 0, 0.5);
    }
    
    .battle-timer {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
        
        .timer-text {
            color: white;
            font-size: 1.1rem;
            
            .round-number {
                color: #ff6b6b;
                font-weight: bold;
            }
        }
        
        .timer-bar {
            width: 20rem;
            height: 0.4rem;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 0.2rem;
            overflow: hidden;
            
            @media (max-width: 768px) {
                width: 15rem;
            }
            
            .timer-progress {
                height: 100%;
                background: linear-gradient(90deg, #ff6b6b, #feca57);
                border-radius: 0.2rem;
                transition: width 0.1s linear;
                width: 100%;
            }
        }
    }
}

// 玩家滚轮区域
.players-slots {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
    margin-bottom: 2rem;
    
    @media (max-width: 768px) {
        flex-direction: column;
        gap: 1rem;
    }
}

// 单个玩家滚轮
.player-slot {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    
    .player-info {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 0.5rem;
        border: 0.1rem solid rgba(255, 255, 255, 0.1);
        
        .player-avatar {
            width: 3rem;
            height: 3rem;
            border-radius: 50%;
            object-fit: cover;
            border: 0.2rem solid #ffd700;
        }
        
        .player-name {
            color: white;
            font-weight: bold;
            font-size: 1rem;
        }
        
        .player-score {
            color: #ffd700;
            font-size: 1.2rem;
            font-weight: bold;
        }
    }
    
    // 迷你滚轮机器
    .slot-machine.mini {
        transform: scale(0.8);
        
        .machine-frame {
            .frame-glow {
                position: absolute;
                top: -0.3rem;
                left: -0.3rem;
                right: -0.3rem;
                bottom: -0.3rem;
                border-radius: 1.2rem;
                opacity: 0;
                transition: opacity 0.3s ease;
                
                &.player-1 {
                    box-shadow: 0 0 2rem rgba(74, 144, 226, 0.6);
                    border: 0.2rem solid #4A90E2;
                }
                
                &.player-2 {
                    box-shadow: 0 0 2rem rgba(231, 76, 60, 0.6);
                    border: 0.2rem solid #E74C3C;
                }
                
                &.active {
                    opacity: 1;
                    animation: glowPulse 2s ease-in-out infinite;
                }
            }
        }
        
        .reel-window {
            .vs-indicator {
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                font-size: 1.2rem;
                font-weight: bold;
                color: #ffd700;
                text-shadow: 0 0 0.5rem rgba(255, 215, 0, 0.8);
                z-index: 10;
                
                &.left { left: -2rem; }
                &.right { right: -2rem; }
            }
        }
    }
}

@keyframes glowPulse {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 1; }
}

// VS分隔符
.vs-divider {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    
    @media (max-width: 768px) {
        order: -1;
        margin-bottom: 1rem;
    }
    
    .vs-text {
        font-size: 2rem;
        font-weight: bold;
        color: #ffd700;
        text-shadow: 0 0 1rem rgba(255, 215, 0, 0.8);
        z-index: 2;
        position: relative;
    }
    
    .lightning-effect {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 4rem;
        height: 4rem;
        background: radial-gradient(circle, rgba(255, 215, 0, 0.3), transparent);
        border-radius: 50%;
        opacity: 0;
        
        &.active {
            animation: lightningFlash 0.5s ease-in-out;
        }
    }
}

@keyframes lightningFlash {
    0%, 100% { opacity: 0; transform: translate(-50%, -50%) scale(1); }
    50% { opacity: 1; transform: translate(-50%, -50%) scale(1.5); }
}

// 回合结果
.round-result {
    text-align: center;
    padding: 1.5rem;
    background: linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 107, 107, 0.1));
    border-radius: 0.5rem;
    border: 0.1rem solid rgba(255, 215, 0, 0.3);
    opacity: 0;
    transform: translateY(2rem);
    transition: all 0.5s ease;
    
    &.show {
        opacity: 1;
        transform: translateY(0);
    }
    
    .winner-announcement {
        .winner-text {
            display: block;
            font-size: 1.4rem;
            font-weight: bold;
            margin-bottom: 1rem;
            
            &.player-1-wins {
                color: #4A90E2;
                text-shadow: 0 0 1rem rgba(74, 144, 226, 0.6);
            }
            
            &.player-2-wins {
                color: #E74C3C;
                text-shadow: 0 0 1rem rgba(231, 76, 60, 0.6);
            }
            
            &.tie {
                color: #ffd700;
                text-shadow: 0 0 1rem rgba(255, 215, 0, 0.6);
            }
        }
        
        .round-score {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
            font-size: 1.2rem;
            font-weight: bold;
            
            .score-player-1 { color: #4A90E2; }
            .score-player-2 { color: #E74C3C; }
            .score-vs { color: #ffd700; }
        }
    }
}
```

### 对战滚轮动画控制器

```javascript
class BattleSlotAnimation {
    constructor(container, options = {}) {
        this.container = container;
        this.options = {
            roundDuration: 5000,
            revealDelay: 1000,
            spinDuration: 3000,
            ...options
        };
        
        this.players = [];
        this.currentRound = 1;
        this.roundTimer = null;
        this.audioManager = new AudioManager();
        
        this.init();
    }
    
    init() {
        this.setupPlayers();
        this.bindEvents();
        this.preloadSounds();
    }
    
    setupPlayers() {
        const playerSlots = this.container.querySelectorAll('.player-slot');
        playerSlots.forEach((slot, index) => {
            this.players.push({
                id: slot.dataset.playerId,
                element: slot,
                slotMachine: new SlotMachineAnimation(slot.querySelector('.slot-machine'), {
                    reelsCount: 1,
                    spinDuration: this.options.spinDuration,
                    compact: true
                }),
                score: 0,
                wins: 0
            });
        });
    }
    
    // 开始新回合
    async startRound(roundData) {
        this.currentRound = roundData.round;
        this.updateRoundDisplay();
        
        // 重置状态
        this.resetRoundState();
        
        // 激活机器光效
        this.activatePlayerGlow();
        
        // 开始计时器
        this.startRoundTimer();
        
        // 播放回合开始音效
        this.audioManager.play('battle_round_start');
        
        // 为每个玩家开始滚轮动画
        const spinPromises = this.players.map(async (player, index) => {
            const delay = index * 200; // 错开启动时间
            await this.delay(delay);
            
            return player.slotMachine.startBattleSpin(roundData.case_id);
        });
        
        // 等待所有滚轮开始
        await Promise.all(spinPromises);
    }
    
    // 停止滚轮并显示结果
    async revealResults(results) {
        // 停止计时器
        this.stopRoundTimer();
        
        // 为每个玩家显示结果
        const revealPromises = this.players.map(async (player, index) => {
            const playerResult = results.find(r => r.player_id === player.id);
            if (!playerResult) return;
            
            const delay = index * this.options.revealDelay;
            await this.delay(delay);
            
            // 停止滚轮在结果物品
            await player.slotMachine.stopAtResult(playerResult.item);
            
            // 更新玩家信息
            this.updatePlayerInfo(player, playerResult);
            
            return playerResult;
        });
        
        const finalResults = await Promise.all(revealPromises);
        
        // 计算回合胜者
        const winner = this.calculateRoundWinner(finalResults);
        
        // 显示回合结果
        await this.showRoundResult(winner, finalResults);
        
        // 去除机器光效
        this.deactivatePlayerGlow();
        
        return winner;
    }
    
    // 激活玩家机器光效
    activatePlayerGlow() {
        this.players.forEach(player => {
            const frameGlow = player.element.querySelector('.frame-glow');
            frameGlow.classList.add('active');
        });
    }
    
    // 去除玩家机器光效
    deactivatePlayerGlow() {
        this.players.forEach(player => {
            const frameGlow = player.element.querySelector('.frame-glow');
            frameGlow.classList.remove('active');
        });
    }
    
    // 开始回合计时器
    startRoundTimer() {
        const timerProgress = this.container.querySelector('.timer-progress');
        const duration = this.options.roundDuration;
        
        gsap.to(timerProgress, {
            width: '0%',
            duration: duration / 1000,
            ease: 'none',
            onComplete: () => {
                this.onRoundTimeout();
            }
        });
    }
    
    // 停止回合计时器
    stopRoundTimer() {
        const timerProgress = this.container.querySelector('.timer-progress');
        gsap.killTweensOf(timerProgress);
    }
    
    // 更新回合显示
    updateRoundDisplay() {
        const roundNumber = this.container.querySelector('.round-number');
        roundNumber.textContent = this.currentRound;
        
        // 回合数字动画
        gsap.fromTo(roundNumber, 
            { scale: 0.8, color: '#ffd700' },
            { scale: 1.2, color: '#ff6b6b', duration: 0.3, yoyo: true, repeat: 1 }
        );
    }
    
    // 更新玩家信息
    updatePlayerInfo(player, result) {
        const playerScore = player.element.querySelector('.player-score');
        const newScore = player.score + result.item.price;
        
        // 分数动画
        gsap.to({ value: player.score }, {
            value: newScore,
            duration: 1,
            ease: 'power2.out',
            onUpdate: function() {
                playerScore.textContent = `$${Math.round(this.targets()[0].value)}`;
            },
            onComplete: () => {
                player.score = newScore;
            }
        });
    }
    
    // 计算回合胜者
    calculateRoundWinner(results) {
        if (results.length < 2) return null;
        
        const player1Result = results[0];
        const player2Result = results[1];
        
        if (player1Result.item.price > player2Result.item.price) {
            this.players[0].wins++;
            return {
                type: 'player-1-wins',
                text: `${this.players[0].element.querySelector('.player-name').textContent} 获胜！`,
                player: this.players[0]
            };
        } else if (player2Result.item.price > player1Result.item.price) {
            this.players[1].wins++;
            return {
                type: 'player-2-wins',
                text: `${this.players[1].element.querySelector('.player-name').textContent} 获胜！`,
                player: this.players[1]
            };
        } else {
            return {
                type: 'tie',
                text: '平局！',
                player: null
            };
        }
    }
    
    // 显示回合结果
    async showRoundResult(winner, results) {
        const roundResult = this.container.querySelector('.round-result');
        const winnerText = roundResult.querySelector('.winner-text');
        const scorePlayer1 = roundResult.querySelector('.score-player-1');
        const scorePlayer2 = roundResult.querySelector('.score-player-2');
        
        // 更新结果文本
        winnerText.textContent = winner.text;
        winnerText.className = `winner-text ${winner.type}`;
        
        // 更新分数
        scorePlayer1.textContent = `$${this.players[0].score}`;
        scorePlayer2.textContent = `$${this.players[1].score}`;
        
        // 显示结果
        roundResult.classList.add('show');
        
        // 闪电效果
        const lightningEffect = this.container.querySelector('.lightning-effect');
        lightningEffect.classList.add('active');
        
        // 播放音效
        if (winner.type === 'tie') {
            this.audioManager.play('battle_tie');
        } else {
            this.audioManager.play('battle_win');
        }
        
        // 粒子效果
        this.createWinParticles(winner.player?.element);
        
        // 3秒后隐藏结果
        setTimeout(() => {
            roundResult.classList.remove('show');
            lightningEffect.classList.remove('active');
        }, 3000);
    }
    
    // 创建胜利粒子效果
    createWinParticles(playerElement) {
        if (!playerElement) return;
        
        const rect = playerElement.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        
        for (let i = 0; i < 15; i++) {
            const particle = document.createElement('div');
            particle.style.cssText = `
                position: fixed;
                width: 0.5rem;
                height: 0.5rem;
                background: #ffd700;
                border-radius: 50%;
                top: ${centerY}px;
                left: ${centerX}px;
                pointer-events: none;
                z-index: 1000;
            `;
            
            document.body.appendChild(particle);
            
            gsap.to(particle, {
                x: (Math.random() - 0.5) * 300,
                y: (Math.random() - 0.5) * 300,
                rotation: Math.random() * 360,
                scale: Math.random() * 1.5 + 0.5,
                opacity: 0,
                duration: 2,
                ease: 'power2.out',
                onComplete: () => particle.remove()
            });
        }
    }
    
    // 重置回合状态
    resetRoundState() {
        const roundResult = this.container.querySelector('.round-result');
        roundResult.classList.remove('show');
        
        const timerProgress = this.container.querySelector('.timer-progress');
        gsap.set(timerProgress, { width: '100%' });
    }
    
    // 回合超时处理
    onRoundTimeout() {
        console.warn('回合超时');
        // 这里可以添加超时处理逻辑
    }
    
    // 工具方法
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    preloadSounds() {
        const sounds = ['battle_round_start', 'battle_win', 'battle_tie'];
        sounds.forEach(sound => this.audioManager.preload(sound));
    }
    
    bindEvents() {
        // 可以添加一些交互事件
    }
}

// 扩展SlotMachineAnimation类支持对战模式
SlotMachineAnimation.prototype.startBattleSpin = function(caseId) {
    // 简化的对战滚轮动画
    return this.startSpin(caseId);
};
```

### 对战动画音效扩展

```javascript
// 在SoundManager中添加对战相关音效
const BATTLE_SOUNDS = {
    battle_round_start: '/sounds/battle-round-start.mp3',
    battle_win: '/sounds/battle-round-win.mp3',
    battle_tie: '/sounds/battle-round-tie.mp3',
    battle_final_win: '/sounds/battle-final-win.mp3',
    battle_countdown: '/sounds/battle-countdown.mp3'
};

// 合并到现有音效中
Object.assign(SoundManager.sounds, BATTLE_SOUNDS);
```

### 使用示例

```javascript
// 初始化对战滚轮动画
const battleContainer = document.querySelector('.battle-slot-container');
const battleAnimation = new BattleSlotAnimation(battleContainer, {
    roundDuration: 8000,
    revealDelay: 800,
    spinDuration: 4000
});

// WebSocket消息处理
websocket.addEventListener('message', (event) => {
    const data = JSON.parse(event.data);
    
    if (data.channel === 'boxroomdetail') {
        if (data.action === 'round') {
            // 开始新回合
            battleAnimation.startRound(data.data);
        } else if (data.action === 'result') {
            // 显示回合结果
            battleAnimation.revealResults(data.data.results);
        }
    }
});
```

这样，对战动画就与开箱动画保持了一致的老虎机滚轮式风格，提供了：

1. **统一的视觉风格**：使用相同的滚轮机器外观和动画效果
2. **对战特色元素**：VS指示器、玩家光效、闪电效果等
3. **回合制动画**：支持多回合对战的完整动画流程
4. **性能优化**：复用滚轮动画组件，减少代码重复
5. **响应式设计**：适配移动端和桌面端显示
