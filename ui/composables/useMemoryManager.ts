import { ref, onBeforeUnmount, nextTick } from 'vue'

export interface ManagedResource {
  id: string
  type: 'timer' | 'interval' | 'listener' | 'cache' | 'promise' | 'observer'
  resource: any
  created: number
  lastAccessed: number
  metadata?: any
}

export interface MemoryStats {
  timers: number
  intervals: number
  listeners: number
  caches: number
  promises: number
  observers: number
  totalResources: number
  memoryUsage: number
}

export interface CleanupOptions {
  maxAge?: number // 最大存活时间（毫秒）
  maxSize?: number // 最大资源数量
  force?: boolean // 强制清理
}

/**
 * 内存管理工具
 * 自动追踪和清理各种资源，防止内存泄漏
 */
export function useMemoryManager(componentName: string = 'Unknown') {
  // 🎯 资源注册表
  const managedResources = ref(new Map<string, ManagedResource>())
  
  // 🎯 自动清理定时器
  let autoCleanupInterval: NodeJS.Timeout | null = null
  
  // 🎯 配置参数
  const config = {
    autoCleanupInterval: 30000, // 30秒
    defaultMaxAge: 300000, // 5分钟
    maxResources: 1000,
    enableAutoCleanup: true
  }

  /**
   * 生成唯一资源ID
   */
  const generateResourceId = (type: string, prefix?: string): string => {
    const timestamp = Date.now()
    const random = Math.random().toString(36).substr(2, 9)
    return `${prefix || type}_${timestamp}_${random}`
  }

  /**
   * 注册资源
   */
  const registerResource = (
    type: ManagedResource['type'],
    resource: any,
    metadata?: any,
    customId?: string
  ): string => {
    const id = customId || generateResourceId(type)
    const now = Date.now()
    
    const managedResource: ManagedResource = {
      id,
      type,
      resource,
      created: now,
      lastAccessed: now,
      metadata
    }
    
    managedResources.value.set(id, managedResource)
    
    console.debug(`[MEMORY_MANAGER] ${componentName} 注册资源: ${type}#${id}`)
    
    return id
  }

  /**
   * 更新资源访问时间
   */
  const touchResource = (id: string): void => {
    const resource = managedResources.value.get(id)
    if (resource) {
      resource.lastAccessed = Date.now()
    }
  }

  /**
   * 创建并管理定时器
   */
  const createTimer = (
    callback: () => void,
    delay: number,
    metadata?: any
  ): string => {
    const timer = setTimeout(() => {
      callback()
      // 定时器执行后自动清理
      cleanupResource(id)
    }, delay)
    
    const id = registerResource('timer', timer, {
      delay,
      callback: callback.toString(),
      ...metadata
    })
    
    return id
  }

  /**
   * 创建并管理间隔定时器
   */
  const createInterval = (
    callback: () => void,
    interval: number,
    metadata?: any
  ): string => {
    const intervalId = setInterval(callback, interval)
    
    const id = registerResource('interval', intervalId, {
      interval,
      callback: callback.toString(),
      ...metadata
    })
    
    return id
  }

  /**
   * 创建并管理事件监听器
   */
  const createEventListener = (
    element: EventTarget,
    event: string,
    listener: EventListener,
    options?: AddEventListenerOptions,
    metadata?: any
  ): string => {
    element.addEventListener(event, listener, options)
    
    const id = registerResource('listener', {
      element,
      event,
      listener,
      options
    }, {
      eventType: event,
      elementType: (element as any).constructor?.name || 'Unknown',
      ...metadata
    })
    
    return id
  }

  /**
   * 创建并管理缓存
   */
  const createCache = <T = any>(
    initialData?: T,
    maxSize: number = 100,
    metadata?: any
  ): { id: string; cache: Map<string, T> } => {
    const cache = new Map<string, T>()
    
    if (initialData !== undefined) {
      cache.set('initial', initialData)
    }
    
    const id = registerResource('cache', cache, {
      maxSize,
      type: 'Map',
      ...metadata
    })
    
    return { id, cache }
  }

  /**
   * 创建并管理Promise
   */
  const createManagedPromise = <T>(
    promiseFactory: () => Promise<T>,
    metadata?: any
  ): { id: string; promise: Promise<T> } => {
    const promise = promiseFactory()
    
    const id = registerResource('promise', promise, {
      state: 'pending',
      ...metadata
    })
    
    // 自动清理已完成的Promise
    promise.finally(() => {
      setTimeout(() => cleanupResource(id), 1000) // 1秒后清理
    })
    
    return { id, promise }
  }

  /**
   * 创建并管理观察者
   */
  const createObserver = (
    observer: MutationObserver | IntersectionObserver | ResizeObserver,
    metadata?: any
  ): string => {
    const id = registerResource('observer', observer, {
      observerType: observer.constructor.name,
      ...metadata
    })
    
    return id
  }

  /**
   * 清理单个资源
   */
  const cleanupResource = (id: string): boolean => {
    const resource = managedResources.value.get(id)
    if (!resource) {
      return false
    }
    
    try {
      switch (resource.type) {
        case 'timer':
          clearTimeout(resource.resource)
          break
          
        case 'interval':
          clearInterval(resource.resource)
          break
          
        case 'listener':
          const { element, event, listener, options } = resource.resource
          element.removeEventListener(event, listener, options)
          break
          
        case 'cache':
          if (resource.resource instanceof Map) {
            resource.resource.clear()
          } else if (resource.resource instanceof Set) {
            resource.resource.clear()
          } else if (Array.isArray(resource.resource)) {
            resource.resource.length = 0
          }
          break
          
        case 'observer':
          if ('disconnect' in resource.resource) {
            resource.resource.disconnect()
          }
          break
          
        case 'promise':
          // Promise无法主动取消，只是移除引用
          break
      }
      
      managedResources.value.delete(id)
      console.debug(`[MEMORY_MANAGER] ${componentName} 已清理资源: ${resource.type}#${id}`)
      return true
      
    } catch (error) {
      console.error(`[MEMORY_MANAGER] ${componentName} 清理资源失败:`, error)
      managedResources.value.delete(id) // 即使清理失败也要移除引用
      return false
    }
  }

  /**
   * 批量清理资源
   */
  const cleanupResources = (
    filter?: (resource: ManagedResource) => boolean,
    options: CleanupOptions = {}
  ): number => {
    const { maxAge = config.defaultMaxAge, force = false } = options
    const now = Date.now()
    let cleanedCount = 0
    
    for (const [id, resource] of managedResources.value) {
      const shouldCleanup = force || 
        (maxAge && now - resource.lastAccessed > maxAge) ||
        (filter && filter(resource))
      
      if (shouldCleanup) {
        if (cleanupResource(id)) {
          cleanedCount++
        }
      }
    }
    
    console.log(`[MEMORY_MANAGER] ${componentName} 批量清理完成: ${cleanedCount}个资源`)
    return cleanedCount
  }

  /**
   * 清理特定类型的资源
   */
  const cleanupByType = (type: ManagedResource['type'], options: CleanupOptions = {}): number => {
    return cleanupResources(resource => resource.type === type, options)
  }

  /**
   * 清理过期资源
   */
  const cleanupExpired = (maxAge: number = config.defaultMaxAge): number => {
    const now = Date.now()
    return cleanupResources(resource => now - resource.lastAccessed > maxAge)
  }

  /**
   * 清理所有资源
   */
  const cleanupAll = (): number => {
    const totalResources = managedResources.value.size
    
    for (const id of managedResources.value.keys()) {
      cleanupResource(id)
    }
    
    console.log(`[MEMORY_MANAGER] ${componentName} 已清理所有资源: ${totalResources}个`)
    return totalResources
  }

  /**
   * 获取内存统计信息
   */
  const getMemoryStats = (): MemoryStats => {
    const stats: MemoryStats = {
      timers: 0,
      intervals: 0,
      listeners: 0,
      caches: 0,
      promises: 0,
      observers: 0,
      totalResources: managedResources.value.size,
      memoryUsage: 0
    }
    
    for (const resource of managedResources.value.values()) {
      switch (resource.type) {
        case 'timer':
          stats.timers++
          break
        case 'interval':
          stats.intervals++
          break
        case 'listener':
          stats.listeners++
          break
        case 'cache':
          stats.caches++
          break
        case 'promise':
          stats.promises++
          break
        case 'observer':
          stats.observers++
          break
      }
    }
    
    // 尝试获取内存使用情况
    if ('memory' in performance) {
      const memInfo = (performance as any).memory
      stats.memoryUsage = memInfo.usedJSHeapSize / 1024 / 1024 // MB
    }
    
    return stats
  }

  /**
   * 获取资源详情
   */
  const getResourceDetails = (id?: string) => {
    if (id) {
      return managedResources.value.get(id)
    }
    
    return Array.from(managedResources.value.values()).map(resource => ({
      ...resource,
      age: Date.now() - resource.created,
      lastAccessed: Date.now() - resource.lastAccessed
    }))
  }

  /**
   * 检查内存健康状态
   */
  const checkMemoryHealth = () => {
    const stats = getMemoryStats()
    const warnings: string[] = []
    
    if (stats.totalResources > config.maxResources) {
      warnings.push(`资源数量过多: ${stats.totalResources} > ${config.maxResources}`)
    }
    
    if (stats.timers > 50) {
      warnings.push(`定时器数量异常: ${stats.timers}`)
    }
    
    if (stats.listeners > 100) {
      warnings.push(`事件监听器数量异常: ${stats.listeners}`)
    }
    
    if (stats.memoryUsage > 100) {
      warnings.push(`内存使用过高: ${stats.memoryUsage.toFixed(2)}MB`)
    }
    
    return {
      isHealthy: warnings.length === 0,
      warnings,
      stats
    }
  }

  /**
   * 启动自动清理
   */
  const startAutoCleanup = () => {
    if (autoCleanupInterval || !config.enableAutoCleanup) {
      return
    }
    
    autoCleanupInterval = setInterval(() => {
      const cleaned = cleanupExpired()
      if (cleaned > 0) {
        console.log(`[MEMORY_MANAGER] ${componentName} 自动清理: ${cleaned}个过期资源`)
      }
      
      // 检查资源数量限制
      if (managedResources.value.size > config.maxResources) {
        const excess = managedResources.value.size - config.maxResources
        cleanupResources(undefined, { maxSize: excess })
      }
    }, config.autoCleanupInterval)
    
    console.log(`[MEMORY_MANAGER] ${componentName} 自动清理已启动`)
  }

  /**
   * 停止自动清理
   */
  const stopAutoCleanup = () => {
    if (autoCleanupInterval) {
      clearInterval(autoCleanupInterval)
      autoCleanupInterval = null
      console.log(`[MEMORY_MANAGER] ${componentName} 自动清理已停止`)
    }
  }

  /**
   * 强制垃圾回收（如果支持）
   */
  const forceGarbageCollection = () => {
    if ('gc' in window) {
      (window as any).gc()
      console.log(`[MEMORY_MANAGER] ${componentName} 已触发垃圾回收`)
    } else {
      console.warn(`[MEMORY_MANAGER] ${componentName} 浏览器不支持手动垃圾回收`)
    }
  }

  // 🎯 自动启动清理
  if (config.enableAutoCleanup) {
    startAutoCleanup()
  }

  // 🎯 组件卸载时自动清理
  onBeforeUnmount(() => {
    stopAutoCleanup()
    cleanupAll()
  })

  return {
    // 资源创建
    createTimer,
    createInterval,
    createEventListener,
    createCache,
    createManagedPromise,
    createObserver,
    
    // 资源管理
    registerResource,
    touchResource,
    cleanupResource,
    cleanupResources,
    cleanupByType,
    cleanupExpired,
    cleanupAll,
    
    // 状态查询
    getMemoryStats,
    getResourceDetails,
    checkMemoryHealth,
    
    // 自动管理
    startAutoCleanup,
    stopAutoCleanup,
    forceGarbageCollection,
    
    // 便捷访问
    resourceCount: computed(() => managedResources.value.size),
    isHealthy: computed(() => checkMemoryHealth().isHealthy)
  }
} 