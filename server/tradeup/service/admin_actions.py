import logging

from django.contrib import messages
from django.utils.translation import gettext_lazy as _

from tradeup.models import TradeupInventory
from package.interfaces import get_item_price_by_name

_logger = logging.getLogger(__name__)

def sync_tradeup_price(modeladmin, request, queryset):
    try:
        # qs = TradeupInventory.objects.all()
        for item in queryset:
            item_pirce = get_item_price_by_name(item.item_info.market_hash_name)
            item.price = item_pirce
            item.save()
    except Exception as e:
        _logger.exception(e)

    messages.success(request, _('Sync Tradeup Price complete'))

sync_tradeup_price.short_description = _('Sync Tradeup Price state')