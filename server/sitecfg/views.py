import logging
from pprint import pprint
from django.db.models import Q
from django.shortcuts import HttpResponse, render, redirect
from rest_framework.response import Response

from rest_framework.views import APIView
from rest_framework.permissions import AllowAny

from steambase.enums import RespCode
from steambase.utils import reformat_resp, current_user
from sitecfg.business import get_announce, get_announce_list, get_support, get_footer, get_faq, get_sitecfg, get_banner, get_article_detail, get_seo, get_article_list, get_article_weapon
from sitecfg.models import AccessControl
from authentication.business import get_client_ip, ip_in_blacklist

_logger = logging.getLogger(__name__)


class GetSiteAnnounceeView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            id = request.query_params.get('id', None)
            fields = ('id', 'title', 'title_en', 'title_zh_hans', 'content', 'content_en', 'content_zh_hans', 'create_time', 'update_time')
            code, resp = get_announce(id, fields)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')

class GetSiteAnnounceeListView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            order = request.query_params.get('order', '-id')
            fields = ('id', 'title', 'title_en', 'title_zh_hans', 'content', 'content_en', 'content_zh_hans', 'create_time', 'update_time')
            code, resp = get_announce_list(page, page_size, order, fields)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetSiteSupportView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            uid = request.query_params.get('uid', None)
            fields_list = ('uid', 'title', 'title_en', 'title_zh_hans')
            fields_detail = ('uid', 'title', 'title_en', 'title_zh_hans', 'content', 'content_en', 'content_zh_hans')
            code, resp = get_support(uid, fields_list, fields_detail)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetSiteFooterView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            code, resp = get_footer()
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


def page_not_found(request):
    return render(request, '500.html')


class GetSiteFAQView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            uid = request.query_params.get('uid', None)
            fields = ('uid', 'title', 'title_en', 'title_zh_hans', 'content', 'content_en', 'content_zh_hans')
            code, resp = get_faq(uid, fields)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetSiteCFGView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            query = {
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            fields = ()
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            code, resp = get_sitecfg()
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetSiteBannerView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:       
            code, resp = get_banner()
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')

class GetArticleListView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            query = {}
            # 删除值为 None 的键
            query = {k: v for k, v in query.items() if v is not None}
            fields =   ('title', 'tag', 'image')
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            condition = ['CSGO_Type_Machinegun', 'CSGO_Type_SMG', 'CSGO_Type_MusicKit', 'CSGO_Type_Pistol', 'CSGO_Type_Rifle', 'CSGO_Type_Shotgun', 'CSGO_Type_Hands', 'CSGO_Type_Knife', 'CSGO_Tool_Sticker', 'CSGO_Player', 'CSGO_Match', 'CSGO_Team']   
            default_image_url = '/media/article/default.png' # 默认图片路径
         
            
            code, resp = get_article_list(query, fields, condition, default_image_url, page, page_size)
            
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, 'Exception')

class GetArticleWeaponView(APIView):
    permission_classes = [AllowAny]
    def get(self, request):
        try:
            query = {}
            query = {k: v for k, v in query.items() if v is not None}
            fields = ()      

            cate_name_en_values = ['CSGO_Type_Machinegun', 'CSGO_Type_SMG', 'CSGO_Type_MusicKit', 'CSGO_Type_Pistol', 'CSGO_Type_Rifle', 'CSGO_Type_Shotgun', 'CSGO_Type_Hands', 'CSGO_Type_Knife', 'CSGO_Tool_Sticker', 'CSGO_Type_SniperRifle']
            condition = Q()
            for name_en_value in cate_name_en_values:
                # 使用循环构建或条件，将多个 name_en 值添加到条件中     
                condition |= Q(name_en=name_en_value)   
          
            
            code, resp = get_article_weapon(query, fields, condition)
            
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, 'Exception')

class GetArticleTeamView(APIView):
    permission_classes = [AllowAny]
    def get(self, request):
        try:
            query = {}
            # 删除值为 None 的键
            query = {k: v for k, v in query.items() if v is not None}
            fields =   ('title', 'tag', 'image')
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            condition = ['CSGO_Team']   
            default_image_url = '/media/article/team.png' # 默认图片路径
         
            
            code, resp = get_article_list(query, fields, condition, default_image_url, page, page_size)
            
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, 'Exception')
        
class GetArticlePlayerView(APIView):
    permission_classes = [AllowAny]
    def get(self, request):
        try:
            query = {}
            # 删除值为 None 的键
            query = {k: v for k, v in query.items() if v is not None}
            fields =   ('title', 'tag', 'image')
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            condition = ['CSGO_Player']   
            default_image_url = '/media/article/player.png' # 默认图片路径
         
            
            code, resp = get_article_list(query, fields, condition, default_image_url, page, page_size)
            
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, 'Exception')
        
class GetArticleMatchView(APIView):
    permission_classes = [AllowAny]
    def get(self, request):
        try:
            query = {}
            # 删除值为 None 的键
            query = {k: v for k, v in query.items() if v is not None}
            fields =   ('title', 'tag', 'image')
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            condition = ['CSGO_Match']   
            default_image_url = '/media/article/match.png' # 默认图片路径
         
            
            code, resp = get_article_list(query, fields, condition, default_image_url, page, page_size)
            
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, 'Exception')

    
class GetArticleDetailView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            query ={}
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            query['tag'] = request.query_params.get('tag', None)
            code, resp = get_article_detail(query)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')
    
class GetSEOView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            url =request.query_params.get('url', None)
            code, resp = get_seo(url)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')
    


class CheckIPView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            ip = get_client_ip(request)
            blacklisted_ips = AccessControl.objects.filter(enable=True).values_list('ip', flat=True)
            is_blacklisted = ip_in_blacklist(ip, blacklisted_ips)
            if is_blacklisted:
                code = RespCode.Succeed.value
                allow = False
            else:
                code = RespCode.Succeed.value
                allow = True
        
            return reformat_resp(code, allow, ip)

        except Exception as e:
            _logger.exception(f"Error processing request: {e}")
            return Response({"error": "Internal Server Error"}, status=500)



            


    
