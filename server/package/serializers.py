from rest_framework import serializers
import pytz

from box.models import Case, DropItem
from package.models import TradeRecord, TradeItem, PackageItem, ItemPrice, ItemInfo, ShopBot, ExchangeRecord, PackageStatisticsDay, ItemCategory, ItemQuality, ItemRarity, ItemExterior
from steambase.utils import id_generator
from steambase.serializers import CustomFieldsSerializer, ItemInfoBaseSerializer
from authentication.serializers import UserSteamSerializer, AuthUserSerializer

from package.service.item import get_item_price

from django.conf import settings



_STEAM_IMG_BASE = 'https://steamcommunity-a.akamaihd.net/economy/image/{icon_url}/'

class ItemCategorySerializer(CustomFieldsSerializer):
    class Meta:
        model = ItemCategory
        fields = '__all__'

class ItemQualitySerializer(CustomFieldsSerializer):
    class Meta:
        model = ItemQuality
        fields = '__all__'

class ItemRaritySerializer(CustomFieldsSerializer):
    class Meta:
        model = ItemRarity
        fields = '__all__'

class ItemExteriorSerializer(CustomFieldsSerializer):
    class Meta:
        model = ItemExterior
        fields = '__all__'


class PackageItemSerializer(CustomFieldsSerializer):
    create_time = serializers.SerializerMethodField()
    update_time = serializers.SerializerMethodField()
    item_info = serializers.SerializerMethodField(read_only=True)
    case_info = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = PackageItem
        fields = '__all__'    
    
    
    def get_create_time(self, obj):
        # 将时间转换为北京时间
        beijing_tz = pytz.timezone('Asia/Shanghai')
        create_time_beijing = obj.create_time.astimezone(beijing_tz)
        return create_time_beijing.strftime('%Y-%m-%d %H:%M:%S')
    

    def get_update_time(self, obj):
        # 将时间转换为北京时间
        beijing_tz = pytz.timezone('Asia/Shanghai')
        update_time_beijing = obj.update_time.astimezone(beijing_tz)
        return update_time_beijing.strftime('%Y-%m-%d %H:%M:%S')

    def get_item_info(self, obj):
        # 这里调用 ItemInfoSerializer（在文件后面定义），并限制返回字段
        fields = ('id', 'name', 'name_en', 'name_zh_hans', 'image', 'item_price',
                  'item_category', 'item_quality', 'item_rarity', 'item_exterior')
        return ItemInfoSerializer(obj.item_info, fields=fields).data
    
    def get_case_info(self, obj):
        """根据 PackageItem 的 case_key 查询 Case 实例并序列化"""
        if not obj.case_key:
            return None
        case = Case.objects.filter(case_key=obj.case_key).first()
        if not case:
            return None
        fields = ('id', 'name', 'name_en', 'name_zh_hans', 'cover', 'case_key', 'price')
        return CaseSerializer(case, fields=fields).data
    

class CaseSerializer(CustomFieldsSerializer):
    cover = serializers.SerializerMethodField()

    class Meta:
        model = Case
        fields = '__all__'
    
    def get_cover(self, obj):
        if obj.cover:
            if obj.cover.startswith('http'):
                return obj.cover
            else:
                # OSS拼接 settings.ALIYUN_OSS_ENDPOINT
                return f'https://{settings.ALIYUN_OSS_BUCKET_NAME}.{settings.ALIYUN_OSS_ENDPOINT}/{obj.cover}'
        return None
    
    
    

class TradeItemSerializer(ItemInfoBaseSerializer):

    class Meta:
        model = TradeItem
        fields = '__all__'


class TradeRecordSerializer(CustomFieldsSerializer):
    user = AuthUserSerializer(read_only=True, fields=('uid', 'steam', 'profile', 'nick_name'))
    trade_items = TradeItemSerializer(many=True, read_only=True)

    class Meta:
        model = TradeRecord
        fields = '__all__'

    def create(self, validated_data):
        user = validated_data.pop('user')
        items = validated_data.pop('items')
        security_code = id_generator(8)
        amount = sum([item['price'] for item in items])
        record = TradeRecord.objects.create(user=user, security_code=security_code, amount=amount, **validated_data)
        for item_data in items:
            item_info = item_data.get('item_info', ItemInfo.objects.filter(market_hash_name=item_data.get('market_hash_name', '')).first())
            package = item_data.get('package', None)
            assetid = item_data.get('assetid', '')
            instanceid = item_data.get('instanceid', '')
            TradeItem.objects.create(item_info=item_info, record=record, package=package,
                                     assetid=assetid, instanceid=instanceid,
                                     price=get_item_price(item_data.get('market_hash_name', None)))
        return record


class ExchangeRecordSerializer(ItemInfoBaseSerializer):
    user = AuthUserSerializer(read_only=True, fields=('uid', 'steam', 'profile', 'nick_name'))

    class Meta:
        model = ExchangeRecord
        fields = '__all__'


class PackageStatisticsDaySerializer(CustomFieldsSerializer):

    class Meta:
        model = PackageStatisticsDay
        fields = '__all__'

class ItemPriceListSerializer(CustomFieldsSerializer):
    item_info = serializers.SerializerMethodField(read_only=True)
    class Meta:
        model = ItemPrice
        fields = "__all__"
        
    
    def get_icon_url_large(self, obj):
        if obj.custom_icon:
            return obj.custom_icon.url
        else:
            icon_url = obj.icon_url_large
            if icon_url:
                if "/" in obj.icon_url:
                    icon_url = obj.icon_url.split("/")[0]
                return _STEAM_IMG_BASE.format(icon_url=icon_url)

    def get_rarity_color(self, obj):
        if obj.custom_rarity:
            return obj.custom_rarity
        return obj.rarity_color
    
    def get_item_info(self, obj):
        field = ('id', 'market_name_cn', 'icon_url_large', 'rarity_color')
        return ItemInfoSerializer(obj.item_info, fields = field).data
        
class ItemPriceSerializer(CustomFieldsSerializer):
    class Meta:
        model = ItemPrice        
        fields = "__all__"
    

class ItemInfoSerializer(CustomFieldsSerializer):
    item_price = ItemPriceSerializer(read_only=True, fields=('price', 'update_time'))
    item_category = ItemCategorySerializer(read_only=True, fields=('cate_id', 'cate_name', 'cate_name_en', 'cate_name_zh_hans', 'icon'))
    item_quality = ItemQualitySerializer(read_only=True, fields=('quality_id', 'quality_name', 'quality_name_en', 'quality_name_zh_hans', 'quality_color'))
    item_rarity = ItemRaritySerializer(read_only=True, fields=('rarity_id', 'rarity_name', 'rarity_name_en', 'rarity_name_zh_hans', 'rarity_color'))
    item_exterior = ItemExteriorSerializer(read_only=True, fields=('exterior_id', 'exterior_name', 'exterior_name_en', 'exterior_name_zh_hans', 'exterior_color'))
    image = serializers.SerializerMethodField()

    class Meta:
        model = ItemInfo
        fields = '__all__'

    def get_image(self, obj):
        if obj.custom_icon:
            return obj.custom_icon.url
        else:
            icon_url = obj.icon_url_large
            if icon_url:
                if "/" in icon_url:  # 建议修改这里的判断条件为 icon_url
                    icon_url = icon_url.split("/")[0]
                return _STEAM_IMG_BASE.format(icon_url=icon_url) if icon_url else None  # 添加对 icon_url 是否为空的检查

    

