# 开发者快速上手指南

## 🚀 5分钟快速启动

### 前置要求
- Docker & Docker Compose
- Git
- Node.js 16+ (如需本地开发前端)
- Python 3.6+ (如需本地开发后端)

### 一键启动
```bash
# 1. 克隆项目
git clone <your-repo-url>
cd csgoskins.com.cn

# 2. 配置环境
cp server/example.env server/.env
# 编辑 server/.env 文件，设置数据库密码等

# 3. 启动服务
docker-compose up -d

# 4. 初始化数据库
docker-compose exec web python manage.py migrate
docker-compose exec web python manage.py createsuperuser

# 5. 访问应用
# 后端API: http://localhost:8000
# 管理后台: http://localhost:8000/admin
```

## 📁 项目核心结构

```
├── 🐳 Docker相关
│   ├── docker-compose.yml          # 容器编排
│   └── deployment/                 # 部署配置
├── 🔧 后端服务 (Django)
│   └── server/
│       ├── manage.py              # Django管理命令
│       ├── steambase/             # 项目配置
│       ├── authentication/        # 用户认证模块
│       ├── box/                   # 开箱系统
│       ├── crash/                 # Crash游戏
│       ├── market/                # 市场交易
│       └── websocket/             # WebSocket处理
├── 🌐 WebSocket服务 (Node.js)
│   └── deployment/node/nodejs/    # 实时通信服务
├── 💻 前端建议结构
│   └── ui/ (或独立前端项目)
└── 📚 文档
    └── docs/                      # 完整文档
```

## 🛠️ 核心技术栈

| 组件 | 技术 | 版本 | 作用 |
|------|------|------|------|
| 后端框架 | Django + DRF | 1.11 | API服务 |
| 数据库 | MySQL | 5.7+ | 数据存储 |
| 缓存 | Redis | 6.0+ | 缓存/会话 |
| WebSocket | Node.js + Socket.IO | - | 实时通信 |
| 反向代理 | Nginx | - | 负载均衡 |
| 容器化 | Docker | - | 部署方案 |

## 🎯 快速开发指南

### 添加新API接口
```python
# 1. 在对应app下创建视图
# server/your_app/views.py
from rest_framework.viewsets import ModelViewSet
from .models import YourModel
from .serializers import YourSerializer

class YourViewSet(ModelViewSet):
    queryset = YourModel.objects.all()
    serializer_class = YourSerializer

# 2. 配置URL路由
# server/your_app/urls.py
from rest_framework.routers import DefaultRouter
from .views import YourViewSet

router = DefaultRouter()
router.register(r'items', YourViewSet)
urlpatterns = router.urls

# 3. 测试API
curl -X GET http://localhost:8000/api/your-app/items/
```

### 添加WebSocket事件
```javascript
// deployment/node/nodejs/ws_server.js
io.on('connection', (socket) => {
  // 新增事件处理
  socket.on('your_event', (data) => {
    // 处理逻辑
    console.log('Received:', data);
    
    // 广播给所有客户端
    io.emit('your_response', { message: 'Response data' });
    
    // 或只回复当前客户端
    socket.emit('your_response', { message: 'Response data' });
  });
});
```

### 添加数据库模型
```python
# server/your_app/models.py
from django.db import models
from django.contrib.auth.models import User

class YourModel(models.Model):
    name = models.CharField(max_length=100)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'your_table_name'
        
    def __str__(self):
        return self.name

# 创建和应用迁移
python manage.py makemigrations your_app
python manage.py migrate
```

## 🔍 开发调试技巧

### 1. 查看日志
```bash
# 实时查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f web
docker-compose logs -f websocket

# 查看Django应用日志
tail -f logs/steambase.log
```

### 2. 数据库操作
```bash
# 进入数据库控制台
docker-compose exec db mysql -u root -p steambase

# Django数据库Shell
docker-compose exec web python manage.py dbshell

# Django交互Shell
docker-compose exec web python manage.py shell
```

### 3. 代码热重载
```bash
# Django开发模式（自动重载）
docker-compose exec web python manage.py runserver 0.0.0.0:8000

# Node.js开发模式
npm install -g nodemon
nodemon deployment/node/nodejs/ws_server.js
```

## 🎮 核心功能模块

### 用户认证系统
```python
# 主要模型: authentication/models.py
class User(AbstractUser):
    steam_id = models.CharField(max_length=20, unique=True)
    avatar = models.URLField()
    balance = models.DecimalField(max_digits=10, decimal_places=2)

# 主要接口:
GET  /api/auth/profile/     # 获取用户信息
POST /api/auth/logout/      # 登出
GET  /api/auth/steam/       # Steam登录
```

### 开箱系统
```python
# 主要模型: box/models.py
class Box(models.Model):
    name = models.CharField(max_length=100)
    price = models.DecimalField(max_digits=8, decimal_places=2)
    image = models.URLField()

class BoxItem(models.Model):
    box = models.ForeignKey(Box, on_delete=models.CASCADE)
    item_name = models.CharField(max_length=100)
    probability = models.FloatField()  # 掉落概率

# 主要接口:
GET  /api/box/boxes/        # 获取开箱列表
POST /api/box/open/         # 开箱操作
```

### 游戏系统 (Crash)
```python
# 主要模型: crash/models.py
class CrashGame(models.Model):
    game_id = models.CharField(max_length=50, unique=True)
    multiplier = models.FloatField()
    status = models.CharField(max_length=20)

# WebSocket事件:
join_crash_room    # 加入游戏房间
place_bet          # 下注
cash_out           # 提现
```

## 🔗 API使用示例

### 认证流程
```javascript
// 1. Steam登录（重定向到Steam）
window.location.href = 'http://localhost:8000/api/auth/steam/';

// 2. 登录成功后获取token（从回调URL获取）
const token = localStorage.getItem('token');

// 3. 使用token调用API
fetch('http://localhost:8000/api/auth/profile/', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
})
.then(response => response.json())
.then(data => console.log(data));
```

### WebSocket连接
```javascript
import io from 'socket.io-client';

const socket = io('http://localhost:3001', {
  auth: {
    token: localStorage.getItem('token')
  }
});

// 监听连接事件
socket.on('connect', () => {
  console.log('Connected to WebSocket');
});

// 加入游戏房间
socket.emit('join_room', {
  game_type: 'crash',
  room_id: 'room_1'
});

// 监听游戏更新
socket.on('game_update', (data) => {
  console.log('Game update:', data);
});
```

## 🧪 测试指南

### 单元测试
```bash
# 运行所有测试
docker-compose exec web python manage.py test

# 运行特定应用测试
docker-compose exec web python manage.py test authentication

# 运行特定测试类
docker-compose exec web python manage.py test authentication.tests.UserTestCase
```

### API测试
```bash
# 使用curl测试
curl -X GET http://localhost:8000/api/auth/profile/ \
  -H "Authorization: Bearer YOUR_TOKEN"

# 使用httpie测试
pip install httpie
http GET localhost:8000/api/auth/profile/ Authorization:"Bearer YOUR_TOKEN"
```

## 📊 性能优化建议

### 数据库优化
```python
# 使用select_related减少查询
users = User.objects.select_related('profile').all()

# 使用prefetch_related优化反向查询
boxes = Box.objects.prefetch_related('boxitem_set').all()

# 添加数据库索引
class Meta:
    indexes = [
        models.Index(fields=['steam_id']),
        models.Index(fields=['created_at']),
    ]
```

### 缓存使用
```python
from django.core.cache import cache

# 缓存用户余额
def get_user_balance(user_id):
    cache_key = f'user_balance_{user_id}'
    balance = cache.get(cache_key)
    if balance is None:
        balance = User.objects.get(id=user_id).balance
        cache.set(cache_key, balance, 300)  # 5分钟
    return balance
```

## 🚀 部署检查清单

### 开发环境 → 测试环境
- [ ] 环境变量配置
- [ ] 数据库迁移
- [ ] 静态文件收集
- [ ] SSL证书配置
- [ ] 日志配置

### 测试环境 → 生产环境
- [ ] 性能测试
- [ ] 安全扫描
- [ ] 备份策略
- [ ] 监控配置
- [ ] 回滚方案

## 🔧 常用命令速查

### Docker相关
```bash
# 重建所有容器
docker-compose up --build

# 查看容器状态
docker-compose ps

# 进入容器
docker-compose exec web bash
docker-compose exec db mysql -u root -p

# 查看日志
docker-compose logs -f web
```

### Django相关
```bash
# 创建应用
python manage.py startapp myapp

# 数据库操作
python manage.py makemigrations
python manage.py migrate
python manage.py dbshell

# 创建超级用户
python manage.py createsuperuser

# 收集静态文件
python manage.py collectstatic
```

### 数据库相关
```sql
-- 查看表结构
DESCRIBE authentication_user;

-- 查看索引
SHOW INDEX FROM box_boxitem;

-- 查看慢查询
SHOW FULL PROCESSLIST;
```

## 📞 获取帮助

### 文档资源
- [完整API文档](./api.md) - 详细的API接口说明
- [架构文档](./architecture.md) - 系统架构设计
- [部署指南](./deployment.md) - 生产环境部署
- [故障排除](./faq.md) - 常见问题解答

### 开发工具推荐
- **API测试**: Postman, Insomnia
- **数据库管理**: MySQL Workbench, phpMyAdmin
- **代码编辑**: VS Code, PyCharm
- **调试工具**: Django Debug Toolbar
- **性能分析**: django-silk

### 代码规范
- **Python**: 遵循PEP8规范
- **JavaScript**: 使用ESLint + Prettier
- **Git**: 使用语义化提交消息
- **API**: RESTful设计原则

---

**🎉 恭喜！你已经掌握了项目的基础知识，可以开始贡献代码了！**

记住：
- 💡 遇到问题先查看 [FAQ文档](./faq.md)
- 🔍 使用搜索功能快速定位代码
- 🤝 不确定时询问团队成员
- 📝 记录你的解决方案帮助后来者
