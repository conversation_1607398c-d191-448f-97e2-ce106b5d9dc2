# ⏰ 时间处理和国际化规范

## 📋 概述

本文档定义了CS:GO皮肤交易平台的时间处理和国际化标准，确保全球用户获得一致的体验。

## 🕐 时间处理规范

### 基本原则
- **统一时区**: 所有时间以北京时间(Asia/Shanghai)为准
- **存储格式**: 数据库使用UTC时间存储，显示时转换为北京时间
- **API返回**: 提供时间戳和格式化时间两种格式

### 1. 后端时间配置

#### Django设置 (`server/steambase/settings.py`)
```python
# 时区配置
TIME_ZONE = 'Asia/Shanghai'
USE_TZ = True
USE_I18N = True
USE_L10N = True

# Celery时区配置
CELERY_TIMEZONE = 'Asia/Shanghai'
```

#### 时间工具函数 (`server/steambase/utils.py`)
```python
from django.utils import timezone
import pytz

def aware_datetime_to_timestamp(datetime_val):
    """将时区感知的datetime转换为时间戳"""
    if datetime_val:
        naive_time = timezone.make_naive(datetime_val, pytz.timezone('UTC'))
        return int(calendar.timegm(naive_time.timetuple()))
    return 0

def today_begin():
    """获取今天开始时间(北京时间)"""
    return timezone.localtime(timezone.now()).replace(
        hour=0, minute=0, second=0, microsecond=0
    )
```

### 2. API时间字段规范

#### 序列化器时间格式
```python
# 标准时间字段格式
class MessageSerializer(CustomFieldsSerializer):
    sent_at = serializers.DateTimeField(format="%Y-%m-%d %H:%M:%S", required=False)
    read_at = serializers.DateTimeField(format="%Y-%m-%d %H:%M:%S", required=False)
    
    # 同时提供时间戳字段
    sent_at_ts = serializers.SerializerMethodField()
    
    def get_sent_at_ts(self, obj):
        return aware_datetime_to_timestamp(obj.sent_at)
```

#### API响应时间格式
```json
{
    "code": 0,
    "message": "Succeed",
    "body": {
        "created_at": "2025-07-19 15:30:00",
        "created_at_ts": 1721374200,
        "updated_at": "2025-07-19 15:30:00", 
        "updated_at_ts": 1721374200
    }
}
```

### 3. 前端时间处理

#### 时间格式化可组合函数 (`ui/composables/useDateFormat.ts`)
```typescript
export const useDateFormat = () => {
  const formatDate = (
    date: Date | number | string | null | undefined,
    format: string = 'YYYY-MM-DD HH:mm:ss'
  ): string => {
    if (!date) return ''
    
    const dateObj = new Date(date)
    // 确保显示北京时间
    const beijingTime = new Date(dateObj.getTime() + (8 * 60 * 60 * 1000))
    
    // 格式化逻辑...
  }
  
  const getRelativeTime = (date: Date | number | string): string => {
    // 相对时间计算，基于北京时间
  }
}
```

#### 时间显示组件
```vue
<template>
  <div class="time-display">
    <!-- 显示北京时间 -->
    <span class="beijing-time">{{ formatDate(timestamp, 'YYYY-MM-DD HH:mm:ss') }}</span>
    <span class="relative-time">{{ getRelativeTime(timestamp) }}</span>
  </div>
</template>
```

## 🌍 国际化规范

### 基本原则
- **支持语言**: 简体中文(zh-hans)、英文(en)
- **默认语言**: 简体中文
- **切换方式**: 前端实时切换，无需刷新页面
- **API支持**: 后端提供多语言字段

### 1. 后端国际化配置

#### Django国际化设置
```python
# 语言配置
LANGUAGE_CODE = 'zh-hans'
LANGUAGES = [
    ('zh-hans', '简体中文'),
    ('en', 'English'),
]

# 模型翻译配置
MODELTRANSLATION_LANGUAGES = ('en', 'zh-hans')
MODELTRANSLATION_DEFAULT_LANGUAGE = 'zh-hans'
```

#### 模型多语言字段
```python
# 使用翻译函数
from django.utils.translation import gettext_lazy as _

class Article(models.Model):
    title = models.CharField(max_length=200, verbose_name=_('Title'))
    content = models.TextField(verbose_name=_('Content'))
    
    class Meta:
        verbose_name = _('Article')
        verbose_name_plural = _('Articles')
```

### 2. API多语言字段规范

#### 标准多语言字段结构
```json
{
    "name": "AK-47 红线箱子",           // 默认名称(中文)
    "name_en": "AK-47 Redline Case",   // 英文名称
    "name_zh_hans": "AK-47 红线箱子",  // 简体中文名称
    "description": "经典AK-47皮肤箱子",
    "description_en": "Classic AK-47 skin case",
    "description_zh_hans": "经典AK-47皮肤箱子"
}
```

#### 序列化器多语言支持
```python
class CaseSerializer(CustomFieldsSerializer):
    class Meta:
        model = Case
        fields = '__all__'
    
    # 自动包含多语言字段: name, name_en, name_zh_hans
```

### 3. 前端国际化实现

#### 语言管理可组合函数 (`ui/composables/useLanguage.ts`)
```typescript
export const useLanguage = () => {
  const currentLocale = ref('zh-hans')
  
  const availableLocales = [
    { code: 'zh-hans', name: '简体中文', flag: 'twemoji:flag-china' },
    { code: 'en', name: 'English', flag: 'twemoji:flag-united-states' }
  ]
  
  const switchLanguage = async (locale: string) => {
    currentLocale.value = locale
    // 保存到localStorage
    localStorage.setItem('language', locale)
    // 触发全局语言变更事件
    window.dispatchEvent(new Event('languageChanged'))
  }
  
  return { currentLocale, availableLocales, switchLanguage }
}
```

#### 多语言内容获取函数
```typescript
/**
 * 根据用户语言偏好获取本地化名称
 * @param item 包含国际化字段的对象
 * @param locale 语言代码
 * @returns 本地化的名称
 */
function getLocalizedName(item: any, locale: string = 'zh-hans'): string {
  switch (locale) {
    case 'en':
    case 'en-US':
      return item.name_en || item.name
    case 'zh-hans':
    case 'zh-CN':
      return item.name_zh_hans || item.name
    default:
      return item.name
  }
}
```

### 4. 语言切换组件

#### 语言切换器 (`ui/components/common/LanguageSwitcher.vue`)
```vue
<template>
  <div class="language-switcher">
    <button
      v-for="locale in availableLocales"
      :key="locale.code"
      @click="switchLanguage(locale.code)"
      :class="{ active: locale.code === currentLocale }"
    >
      <Icon :name="locale.flag" />
      {{ locale.name }}
    </button>
  </div>
</template>
```

## 📊 API响应标准格式

### 完整的API响应示例
```json
{
    "code": 0,
    "message": "Succeed",
    "body": {
        "id": "case_001",
        "name": "AK-47 红线箱子",
        "name_en": "AK-47 Redline Case",
        "name_zh_hans": "AK-47 红线箱子",
        "description": "经典AK-47皮肤箱子",
        "description_en": "Classic AK-47 skin case", 
        "description_zh_hans": "经典AK-47皮肤箱子",
        "price": 15.50,
        "created_at": "2025-07-19 15:30:00",
        "created_at_ts": 1721374200,
        "updated_at": "2025-07-19 15:30:00",
        "updated_at_ts": 1721374200
    }
}
```

### 列表API响应格式
```json
{
    "code": 0,
    "message": "Succeed", 
    "body": {
        "items": [
            {
                "id": "item_001",
                "name": "物品名称",
                "name_en": "Item Name",
                "name_zh_hans": "物品名称",
                "timestamp": 1721374200,
                "formatted_time": "2025-07-19 15:30:00"
            }
        ],
        "total": 100,
        "page": 1,
        "page_size": 20
    }
}
```

## 🔧 实施检查清单

### 后端开发检查
- [ ] 所有模型使用 `_()` 翻译函数
- [ ] 序列化器包含多语言字段
- [ ] 时间字段同时提供格式化时间和时间戳
- [ ] 定时任务使用北京时间

### 前端开发检查  
- [ ] 使用 `getLocalizedName()` 获取多语言内容
- [ ] 时间显示基于北京时间
- [ ] 语言切换无需刷新页面
- [ ] 语言偏好保存到localStorage

### API设计检查
- [ ] 响应包含 `name`、`name_en`、`name_zh_hans` 字段
- [ ] 时间字段包含格式化时间和时间戳
- [ ] 使用统一的响应格式 `{code, message, body}`

---

**重要提醒**: 
- 所有时间以北京时间为准，确保全球用户看到统一的时间
- API必须提供多语言字段，支持前端实时语言切换
- 时间戳和格式化时间并存，满足不同场景需求
