<template>
  <div class="min-h-screen bg-gradient-to-br from-gray-900 to-gray-800 p-6">
    <div class="max-w-6xl mx-auto">
      <!-- 页面标题 -->
      <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-white mb-4">⏰ 时间展示大全</h1>
        <p class="text-gray-300 text-lg">展示各种时间格式、动态效果和国际化时间显示方案</p>
        <div class="mt-4 flex justify-center gap-4">
          <NuxtLink to="/demo" class="nav-button">
            <i class="fas fa-arrow-left mr-2"></i>
            返回演示中心
          </NuxtLink>
        </div>
      </div>

      <!-- 控制面板 -->
      <div class="bg-gray-800/50 backdrop-blur-lg rounded-lg p-6 mb-8">
        <h2 class="text-xl font-semibold text-white mb-4">⚙️ 控制面板</h2>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <!-- 语言选择 -->
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">语言选择</label>
            <select 
              v-model="selectedLocale" 
              class="w-full bg-gray-700 text-white rounded-lg p-2 border border-gray-600"
            >
              <option value="zh-hans">简体中文</option>
              <option value="en">English</option>
            </select>
          </div>

          <!-- 时区选择 -->
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">时区选择</label>
            <select 
              v-model="selectedTimezone" 
              class="w-full bg-gray-700 text-white rounded-lg p-2 border border-gray-600"
            >
              <option value="Asia/Shanghai">中国标准时间</option>
              <option value="America/New_York">纽约时间</option>
              <option value="Europe/London">伦敦时间</option>
              <option value="Asia/Tokyo">东京时间</option>
            </select>
          </div>

          <!-- 动画效果 -->
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">动画效果</label>
            <div class="flex items-center space-x-3">
              <label class="flex items-center">
                <input 
                  v-model="enableAnimations" 
                  type="checkbox" 
                  class="mr-2 text-blue-500"
                >
                <span class="text-gray-300">启用动画</span>
              </label>
            </div>
          </div>

          <!-- 控制按钮 -->
          <div class="flex items-end gap-2">
            <button 
              @click="startCountdown(60)"
              class="px-3 py-2 bg-primary hover:bg-primary-dark text-white rounded-lg transition-all text-sm"
            >
              🚀 开始倒计时
            </button>
          </div>
        </div>
      </div>

      <!-- 时间展示区域 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        
        <!-- 实时时钟 -->
        <div class="time-card">
          <h3 class="card-title">🕐 实时时钟</h3>
          <div class="time-display-large">
            <div class="digital-clock" :class="{ 'animate-pulse': enableAnimations && currentTime.getSeconds() % 2 === 0 }">
              {{ formatTime(currentTime, 'HH:mm:ss', selectedLocale) }}
            </div>
            <div class="date-display">
              {{ formatTime(currentTime, 'YYYY年MM月DD日 dddd', selectedLocale) }}
            </div>
          </div>
        </div>

        <!-- 多时区时钟 -->
        <div class="time-card">
          <h3 class="card-title">🌍 多时区时钟</h3>
          <div class="timezone-grid">
            <div v-for="tz in timezones" :key="tz.zone" class="timezone-item">
              <div class="timezone-name">{{ tz.name }}</div>
              <div class="timezone-time">
                {{ formatTimeInTimezone(currentTime, tz.zone, 'HH:mm:ss') }}
              </div>
              <div class="timezone-date">
                {{ formatTimeInTimezone(currentTime, tz.zone, 'MM/DD') }}
              </div>
            </div>
          </div>
        </div>

        <!-- 倒计时器 -->
        <div class="time-card">
          <h3 class="card-title">⏳ 倒计时器</h3>
          <div class="countdown-container">
            <div class="countdown-display" :class="{ 'countdown-urgent': countdownSeconds <= 10 && countdownSeconds > 0 }">
              <div class="countdown-time">
                {{ formatCountdown(countdownSeconds) }}
              </div>
              <div class="countdown-label">
                {{ countdownSeconds > 0 ? '剩余时间' : '倒计时结束' }}
              </div>
            </div>
            <div class="countdown-controls">
              <button @click="startCountdown(300)" class="countdown-btn">5分钟</button>
              <button @click="startCountdown(60)" class="countdown-btn">1分钟</button>
              <button @click="startCountdown(10)" class="countdown-btn">10秒</button>
              <button @click="stopCountdown" class="countdown-btn bg-red-600">停止</button>
            </div>
          </div>
        </div>

        <!-- 模拟时钟 -->
        <div class="time-card">
          <h3 class="card-title">🎯 模拟时钟</h3>
          <div class="analog-clock-container">
            <div class="analog-clock">
              <!-- 时钟表盘 -->
              <div class="clock-face">
                <!-- 小时刻度 -->
                <div 
                  v-for="hour in 12" 
                  :key="hour"
                  class="hour-mark"
                  :style="{ transform: `rotate(${hour * 30}deg)` }"
                ></div>
                <!-- 分钟刻度 -->
                <div 
                  v-for="minute in 60" 
                  :key="`minute-${minute}`"
                  class="minute-mark"
                  :style="{ transform: `rotate(${minute * 6}deg)` }"
                ></div>
                <!-- 时针 -->
                <div 
                  class="hand hour-hand"
                  :style="{ transform: `rotate(${getHourAngle(currentTime)}deg)` }"
                ></div>
                <!-- 分针 -->
                <div 
                  class="hand minute-hand"
                  :style="{ transform: `rotate(${getMinuteAngle(currentTime)}deg)` }"
                ></div>
                <!-- 秒针 -->
                <div 
                  class="hand second-hand"
                  :style="{ transform: `rotate(${getSecondAngle(currentTime)}deg)` }"
                ></div>
                <!-- 中心点 -->
                <div class="clock-center"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 翻页效果时钟 -->
        <div class="time-card">
          <h3 class="card-title">📖 翻页效果时钟</h3>
          <UiFlipClock 
            :size="'medium'"
            @time-update="onFlipClockTimeUpdate"
            @animation-end="onFlipClockAnimationEnd"
          />
        </div>

        <!-- 翻页倒计时器 -->
        <div class="time-card">
          <h3 class="card-title">⌛ 翻页倒计时器</h3>
          <UiFlipCountdown 
            :seconds="demoCountdownSeconds"
            @end="onFlipCountdownEnd"
            @tick="onFlipCountdownTick"
          />
          <div class="flex gap-2 mt-4 flex-wrap justify-center">
            <button @click="startDemoCountdown(86400)" class="px-4 py-2 bg-primary text-white rounded-lg shadow hover:bg-primary-dark">1天</button>
            <button @click="startDemoCountdown(3600)" class="px-4 py-2 bg-primary text-white rounded-lg shadow hover:bg-primary-dark">1小时</button>
            <button @click="startDemoCountdown(300)" class="px-4 py-2 bg-primary text-white rounded-lg shadow hover:bg-primary-dark">5分钟</button>
            <button @click="startDemoCountdown(60)" class="px-4 py-2 bg-primary text-white rounded-lg shadow hover:bg-primary-dark">1分钟</button>
            <button @click="startDemoCountdown(30)" class="px-4 py-2 bg-primary text-white rounded-lg shadow hover:bg-primary-dark">30秒</button>
            <button @click="stopDemoCountdown()" class="px-4 py-2 bg-red-600 text-white rounded-lg shadow hover:bg-red-700">停止</button>
          </div>
        </div>

        <!-- 相对时间 -->
        <div class="time-card">
          <h3 class="card-title">📅 相对时间</h3>
          <div class="relative-time-container">
            <div v-for="event in sampleEvents" :key="event.id" class="relative-time-item">
              <div class="event-name">{{ event.name }}</div>
              <div class="event-time">{{ getRelativeTime(event.time, selectedLocale) }}</div>
              <div class="event-exact">{{ formatTime(event.time, 'YYYY-MM-DD HH:mm', selectedLocale) }}</div>
            </div>
          </div>
        </div>

        <!-- 特殊格式时间 -->
        <div class="time-card">
          <h3 class="card-title">🎨 特殊格式</h3>
          <div class="special-formats">
            <!-- Unix 时间戳 -->
            <div class="format-item">
              <label>Unix 时间戳</label>
              <div class="format-value">{{ Math.floor(currentTime.getTime() / 1000) }}</div>
            </div>
            <!-- ISO 格式 -->
            <div class="format-item">
              <label>ISO 8601</label>
              <div class="format-value">{{ currentTime.toISOString() }}</div>
            </div>
            <!-- 农历（模拟） -->
            <div class="format-item">
              <label>中文格式</label>
              <div class="format-value">{{ getChineseDate(currentTime) }}</div>
            </div>
            <!-- 英文格式 -->
            <div class="format-item">
              <label>英文格式</label>
              <div class="format-value">{{ formatTime(currentTime, 'dddd, MMMM Do YYYY', 'en') }}</div>
            </div>
          </div>
        </div>

      </div>

      <!-- 动态效果演示 -->
      <div class="mt-8">
        <h2 class="text-2xl font-bold text-white mb-6 text-center">✨ 动态效果演示</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          
          <!-- 霓虹灯时钟 -->
          <div class="time-card">
            <h3 class="card-title">💡 霓虹灯时钟</h3>
            <div class="neon-clock">
              <div class="neon-container">
                <div class="neon-time">
                  {{ formatTime(currentTime, 'HH:mm:ss', selectedLocale) }}
                </div>
                <div class="neon-date">
                  {{ formatTime(currentTime, 'MM/DD', selectedLocale) }}
                </div>
              </div>
            </div>
          </div>

          <!-- 进度环时钟 -->
          <div class="time-card">
            <h3 class="card-title">⭕ 进度环时钟</h3>
            <div class="progress-clock">
              <svg class="progress-svg" viewBox="0 0 200 200">
                <!-- 秒钟环 -->
                <circle 
                  class="progress-bg" 
                  cx="100" 
                  cy="100" 
                  r="90"
                ></circle>
                <circle 
                  class="progress-ring progress-seconds" 
                  cx="100" 
                  cy="100" 
                  r="90"
                  :stroke-dasharray="`${(currentTime.getSeconds() / 60) * 565} 565`"
                ></circle>
                <!-- 分钟环 -->
                <circle 
                  class="progress-bg" 
                  cx="100" 
                  cy="100" 
                  r="70"
                ></circle>
                <circle 
                  class="progress-ring progress-minutes" 
                  cx="100" 
                  cy="100" 
                  r="70"
                  :stroke-dasharray="`${(currentTime.getMinutes() / 60) * 440} 440`"
                ></circle>
                <!-- 小时环 -->
                <circle 
                  class="progress-bg" 
                  cx="100" 
                  cy="100" 
                  r="50"
                ></circle>
                <circle 
                  class="progress-ring progress-hours" 
                  cx="100" 
                  cy="100" 
                  r="50"
                  :stroke-dasharray="`${((currentTime.getHours() % 12) / 12) * 314} 314`"
                ></circle>
                <!-- 中心时间显示 -->
                <text x="100" y="105" class="progress-text">
                  {{ formatTime(currentTime, 'HH:mm', selectedLocale) }}
                </text>
              </svg>
            </div>
          </div>

        </div>
      </div>

    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'

// 页面元数据
useHead({
  title: '时间展示大全 - CSGO Skins',
  meta: [
    { name: 'description', content: '展示各种时间格式、动态效果和国际化时间显示方案' }
  ]
})

// 响应式变量
const currentTime = ref(new Date())
const selectedLocale = ref('zh-hans')
const selectedTimezone = ref('Asia/Shanghai')
const enableAnimations = ref(true)
const countdownSeconds = ref(0)
const demoCountdownSeconds = ref(0)
let demoCountdownTimer: ReturnType<typeof setInterval> | null = null

// 组件事件处理函数
const onFlipClockTimeUpdate = (time: Date) => {
  console.log('翻页时钟时间更新:', time)
}

const onFlipClockAnimationEnd = (index: number) => {
  console.log('翻页时钟动画结束:', index)
}

const onFlipCountdownEnd = () => {
  console.log('翻页倒计时结束')
}

const onFlipCountdownTick = (payload: { days: number; hours: number; minutes: number; seconds: number }) => {
  // 实时获取天、小时、分钟、秒
}

let timeInterval: ReturnType<typeof setInterval> | null = null
let countdownInterval: ReturnType<typeof setInterval> | null = null

// 时区配置
const timezones = [
  { zone: 'Asia/Shanghai', name: '北京' },
  { zone: 'America/New_York', name: '纽约' },
  { zone: 'Europe/London', name: '伦敦' },
  { zone: 'Asia/Tokyo', name: '东京' }
]

// 示例事件数据
const sampleEvents = ref([
  {
    id: 1,
    name: '系统启动',
    time: new Date(Date.now() - 2 * 60 * 60 * 1000) // 2小时前
  },
  {
    id: 2,
    name: '上次登录',
    time: new Date(Date.now() - 30 * 60 * 1000) // 30分钟前
  },
  {
    id: 3,
    name: '下次维护',
    time: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24小时后
  },
  {
    id: 4,
    name: '活动结束',
    time: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7天后
  }
])

// 时间格式化函数
const formatTime = (date: Date, format: string, locale: string = 'zh-hans') => {
  const options: Intl.DateTimeFormatOptions = {}
  
  if (format.includes('YYYY')) {
    options.year = 'numeric'
  }
  if (format.includes('MM')) {
    options.month = format.includes('MMMM') ? 'long' : '2-digit'
  }
  if (format.includes('DD') || format.includes('Do')) {
    options.day = '2-digit'
  }
  if (format.includes('dddd')) {
    options.weekday = 'long'
  }
  if (format.includes('HH')) {
    options.hour = '2-digit'
    options.hour12 = false
  }
  if (format.includes('mm')) {
    options.minute = '2-digit'
  }
  if (format.includes('ss')) {
    options.second = '2-digit'
  }

  // 简单的格式化处理
  if (format === 'HH:mm:ss') {
    return date.toLocaleTimeString(locale === 'zh-hans' ? 'zh-CN' : 'en-US', { 
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }
  
  if (format === 'HH:mm') {
    return date.toLocaleTimeString(locale === 'zh-hans' ? 'zh-CN' : 'en-US', { 
      hour12: false,
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (format === 'YYYY年MM月DD日 dddd') {
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long'
    })
  }

  if (format === 'YYYY-MM-DD HH:mm') {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hour = String(date.getHours()).padStart(2, '0')
    const minute = String(date.getMinutes()).padStart(2, '0')
    return `${year}-${month}-${day} ${hour}:${minute}`
  }

  if (format === 'MM/DD') {
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${month}/${day}`
  }

  if (format === 'dddd, MMMM Do YYYY') {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  return date.toLocaleDateString(locale === 'zh-hans' ? 'zh-CN' : 'en-US', options)
}

// 获取下一个值（用于翻页动画）
const getNextValue = (current: number, max: number) => {
  return (current + 1) % max
}

// 时区时间格式化
const formatTimeInTimezone = (date: Date, timezone: string, format: string) => {
  const options: Intl.DateTimeFormatOptions = {
    timeZone: timezone,
    hour12: false
  }
  
  if (format === 'HH:mm:ss') {
    options.hour = '2-digit'
    options.minute = '2-digit'
    options.second = '2-digit'
  } else if (format === 'MM/DD') {
    options.month = '2-digit'
    options.day = '2-digit'
  }
  
  return date.toLocaleString('en-US', options)
}

// 相对时间格式化
const getRelativeTime = (date: Date, locale: string) => {
  const now = currentTime.value
  const diff = now.getTime() - date.getTime()
  const absDiff = Math.abs(diff)
  
  const minute = 60 * 1000
  const hour = 60 * minute
  const day = 24 * hour
  const week = 7 * day
  const month = 30 * day
  const year = 365 * day

  if (locale === 'zh-hans') {
    if (absDiff < minute) {
      return diff > 0 ? '刚刚' : '即将'
    } else if (absDiff < hour) {
      const minutes = Math.floor(absDiff / minute)
      return diff > 0 ? `${minutes}分钟前` : `${minutes}分钟后`
    } else if (absDiff < day) {
      const hours = Math.floor(absDiff / hour)
      return diff > 0 ? `${hours}小时前` : `${hours}小时后`
    } else if (absDiff < week) {
      const days = Math.floor(absDiff / day)
      return diff > 0 ? `${days}天前` : `${days}天后`
    } else if (absDiff < month) {
      const weeks = Math.floor(absDiff / week)
      return diff > 0 ? `${weeks}周前` : `${weeks}周后`
    } else if (absDiff < year) {
      const months = Math.floor(absDiff / month)
      return diff > 0 ? `${months}个月前` : `${months}个月后`
    } else {
      const years = Math.floor(absDiff / year)
      return diff > 0 ? `${years}年前` : `${years}年后`
    }
  } else {
    if (absDiff < minute) {
      return diff > 0 ? 'just now' : 'soon'
    } else if (absDiff < hour) {
      const minutes = Math.floor(absDiff / minute)
      return diff > 0 ? `${minutes} minutes ago` : `in ${minutes} minutes`
    } else if (absDiff < day) {
      const hours = Math.floor(absDiff / hour)
      return diff > 0 ? `${hours} hours ago` : `in ${hours} hours`
    } else if (absDiff < week) {
      const days = Math.floor(absDiff / day)
      return diff > 0 ? `${days} days ago` : `in ${days} days`
    } else if (absDiff < month) {
      const weeks = Math.floor(absDiff / week)
      return diff > 0 ? `${weeks} weeks ago` : `in ${weeks} weeks`
    } else if (absDiff < year) {
      const months = Math.floor(absDiff / month)
      return diff > 0 ? `${months} months ago` : `in ${months} months`
    } else {
      const years = Math.floor(absDiff / year)
      return diff > 0 ? `${years} years ago` : `in ${years} years`
    }
  }
}

// 中文日期格式化
const getChineseDate = (date: Date) => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const weekdays = ['日', '一', '二', '三', '四', '五', '六']
  const weekday = weekdays[date.getDay()]
  
  return `${year}年${month}月${day}日 星期${weekday}`
}

// 倒计时格式化
const formatCountdown = (seconds: number) => {
  if (seconds <= 0) return '00:00:00'
  
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60
  
  return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(secs).padStart(2, '0')}`
}

// 模拟时钟角度计算
const getHourAngle = (date: Date) => {
  const hours = date.getHours() % 12
  const minutes = date.getMinutes()
  return (hours * 30) + (minutes * 0.5) - 90
}

const getMinuteAngle = (date: Date) => {
  return (date.getMinutes() * 6) - 90
}

const getSecondAngle = (date: Date) => {
  return (date.getSeconds() * 6) - 90
}

// 倒计时控制
const startCountdown = (seconds: number) => {
  countdownSeconds.value = seconds
  
  if (countdownInterval) {
    clearInterval(countdownInterval)
  }
  
  countdownInterval = setInterval(() => {
    if (countdownSeconds.value > 0) {
      countdownSeconds.value--
    } else {
      stopCountdown()
    }
  }, 1000)
}

const stopCountdown = () => {
  if (countdownInterval) {
    clearInterval(countdownInterval)
    countdownInterval = null
  }
  countdownSeconds.value = 0
}

function startDemoCountdown(sec: number) {
  demoCountdownSeconds.value = sec
  if (demoCountdownTimer) clearInterval(demoCountdownTimer)
  if (sec > 0) {
    demoCountdownTimer = setInterval(() => {
      if (demoCountdownSeconds.value > 0) {
        demoCountdownSeconds.value--
      } else {
        clearInterval(demoCountdownTimer!)
        demoCountdownTimer = null
      }
    }, 1000)
  }
}

function stopDemoCountdown() {
  if (demoCountdownTimer) clearInterval(demoCountdownTimer)
  demoCountdownTimer = null
  demoCountdownSeconds.value = 0
}

// 生命周期
onMounted(() => {
  // 更新当前时间
  timeInterval = setInterval(() => {
    const now = new Date()
    currentTime.value = now
  }, 1000)
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
  if (countdownInterval) {
    clearInterval(countdownInterval)
  }
  if (demoCountdownTimer) clearInterval(demoCountdownTimer)
})
</script>

<style lang="scss" scoped>
.time-card {
  @apply bg-gray-800/40 backdrop-blur-lg rounded-lg p-6 border border-gray-700/50 transition-all duration-300;
  
  &:hover {
    @apply border-primary/50 bg-gray-800/60 transform -translate-y-1;
    box-shadow: 0 8px 32px rgba(0, 168, 255, 0.15);
  }
}

.card-title {
  @apply text-lg font-semibold text-white mb-4 pb-2 border-b border-gray-600/50;
}

.nav-button {
  @apply inline-flex items-center px-4 py-2 bg-gray-700/50 hover:bg-gray-600/50 text-white rounded-lg transition-colors border border-gray-600/50 hover:border-gray-500/50;
}

// 大号时间显示
.time-display-large {
  @apply text-center;
  
  .digital-clock {
    @apply text-4xl font-mono font-bold text-primary mb-2;
    text-shadow: 0 0 20px rgba(0, 168, 255, 0.5);
  }
  
  .date-display {
    @apply text-lg text-gray-300;
  }
}

// 多时区显示
.timezone-grid {
  @apply grid grid-cols-2 gap-4;
  
  .timezone-item {
    @apply text-center p-3 bg-gray-700/30 rounded-lg;
    
    .timezone-name {
      @apply text-sm text-gray-400 mb-1;
    }
    
    .timezone-time {
      @apply text-xl font-mono font-bold text-white;
    }
    
    .timezone-date {
      @apply text-sm text-gray-300;
    }
  }
}

// 倒计时
.countdown-container {
  @apply text-center;
  
  .countdown-display {
    @apply mb-6;
    
    .countdown-time {
      @apply text-5xl font-mono font-bold text-white mb-2;
      transition: all 0.3s ease;
    }
    
    .countdown-label {
      @apply text-lg text-gray-300;
    }
    
    &.countdown-urgent {
      .countdown-time {
        @apply text-red-400;
        animation: pulse-urgent 1s infinite;
      }
    }
  }
  
  .countdown-controls {
    @apply flex gap-2 justify-center flex-wrap;
    
    .countdown-btn {
      @apply px-3 py-2 bg-primary hover:bg-primary-dark text-white rounded-lg transition-all text-sm;
    }
  }
}

// 模拟时钟
.analog-clock-container {
  @apply flex justify-center;
  
  .analog-clock {
    @apply relative w-48 h-48;
    
    .clock-face {
      @apply relative w-full h-full bg-gray-700/30 rounded-full border-4 border-gray-600;
      
      .hour-mark {
        @apply absolute w-1 h-6 bg-white;
        top: 0.5rem;
        left: 50%;
        margin-left: -0.125rem;
        transform-origin: 50% 5.5rem;
      }
      
      .minute-mark {
        @apply absolute w-0.5 h-3 bg-gray-400;
        top: 0.5rem;
        left: 50%;
        margin-left: -0.0625rem;
        transform-origin: 50% 5.5rem;
        
        &:nth-child(5n) {
          @apply hidden;
        }
      }
      
      .hand {
        @apply absolute bg-white rounded-full;
        left: 50%;
        transform-origin: 50% 100%;
        
        &.hour-hand {
          width: 0.375rem;
          height: 3.75rem;
          top: 2.25rem;
          margin-left: -0.1875rem;
          @apply bg-white;
        }
        
        &.minute-hand {
          width: 0.25rem;
          height: 5rem;
          top: 1rem;
          margin-left: -0.125rem;
          @apply bg-primary;
        }
        
        &.second-hand {
          width: 0.125rem;
          height: 5.625rem;
          top: 0.375rem;
          margin-left: -0.0625rem;
          @apply bg-red-500;
        }
      }
      
      .clock-center {
        @apply absolute w-4 h-4 bg-white rounded-full;
        top: 50%;
        left: 50%;
        margin: -0.5rem 0 0 -0.5rem;
      }
    }
  }
}

// 相对时间
.relative-time-container {
  @apply space-y-3;
  
  .relative-time-item {
    @apply flex justify-between items-center p-3 bg-gray-700/20 rounded-lg;
    
    .event-name {
      @apply text-white font-medium;
    }
    
    .event-time {
      @apply text-primary font-semibold;
    }
    
    .event-exact {
      @apply text-sm text-gray-400;
    }
  }
}

// 特殊格式
.special-formats {
  @apply space-y-4;
  
  .format-item {
    @apply p-3 bg-gray-700/20 rounded-lg;
    
    label {
      @apply block text-sm text-gray-400 mb-1;
    }
    
    .format-value {
      @apply text-white font-mono text-sm break-all;
    }
  }
}

// 现代化翻页时钟样式
.flip-clock-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 1rem 0;
  // 防止动画过程中的布局抖动
  overflow: hidden;
  
  // 倒计时器特殊样式
  .flip-clock {
    max-width: 100%;
    
    // 确保倒计时器始终在一行
    &.countdown-clock {
      @apply flex-nowrap;
      min-width: max-content;
      transition: transform 0.3s ease;
      // 防止动画过程中的抖动
      will-change: transform;
      backface-visibility: hidden;
      transform-style: preserve-3d;
    }
  }
}

.flip-clock {
  @apply flex items-center gap-1 flex-nowrap justify-center;
  font-family: 'SF Mono', 'Consolas', 'Menlo', monospace;
  
  .time-box {
    @apply relative box-border h-20 min-w-[3.5rem] text-center bg-gray-900 rounded-lg text-white;
    font-size: 1.75rem;
    perspective: 12.5rem;
    line-height: 5rem;
    box-shadow: 
      0 0.25rem 0.5rem rgba(0, 0, 0, 0.3),
      0 0.125rem 0.25rem rgba(0, 0, 0, 0.2),
      inset 0 0.0625rem 0 rgba(255, 255, 255, 0.1);
    border: 0.0625rem solid #333;
    // 防止动画抖动
    will-change: transform;
    backface-visibility: hidden;
    transform-style: preserve-3d;
    

    
    // 左右装饰条
    &:before {
      content: "";
      @apply absolute bg-gray-900 w-1 h-4 top-1/2 -left-1 -mt-2 -z-10 rounded-l-sm;
      box-shadow: inset 0 0.0625rem 0 rgba(255, 255, 255, 0.1);
    }
    
    &:after {
      content: "";
      @apply absolute bg-gray-900 w-1 h-4 top-1/2 -right-1 -mt-2 -z-10 rounded-r-sm;
      box-shadow: inset 0 0.0625rem 0 rgba(255, 255, 255, 0.1);
    }
    
    // 分割线背景
    .divider-bg {
      @apply absolute top-1/2 left-0 right-0 h-px bg-gray-700 z-10;
    }
    
    // 分割线
    .divider-line {
      @apply absolute top-1/2 left-0 right-0 h-px z-20;
      background: linear-gradient(90deg, transparent, #666, transparent);
      box-shadow: 0 0 0.25rem rgba(255, 255, 255, 0.1);
    }
    
    & > div {
      @apply overflow-hidden rounded-lg;
      animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
      animation-duration: 600ms;
      transform: rotateX(0deg);
      // 防止动画过程中的抖动
      will-change: transform;
      backface-visibility: hidden;
      
      &.base {
        @apply relative;
        background: linear-gradient(180deg, #2a2a2a 0%, #1a1a1a 50%, #0a0a0a 100%);
        
        .base-b {
          @apply absolute left-0 bottom-0 rounded-b-lg w-full h-full;
          background: linear-gradient(180deg, #0a0a0a 0%, #1a1a1a 50%, #2a2a2a 100%);
          clip-path: polygon(0 50%, 100% 50%, 100% 100%, 0 100%);
        }
      }
      
      &.face {
        @apply absolute left-0 top-0 w-full h-full z-20;
        background: linear-gradient(180deg, #2a2a2a 0%, #1a1a1a 50%, #0a0a0a 100%);
        backface-visibility: hidden;
        clip-path: polygon(0 0, 100% 0, 100% 50%, 0 50%);
        box-shadow: inset 0 0.0625rem 0 rgba(255, 255, 255, 0.1);
        // 防止动画抖动
        will-change: transform;
        // 确保动画过程中尺寸稳定
        transform-origin: 50% 0%;
        
        &.anime {
          animation-name: animate-flip-face;
        }
      }
      
      &.back {
        @apply absolute left-0 top-0 w-full h-full;
        background: linear-gradient(180deg, #0a0a0a 0%, #1a1a1a 50%, #2a2a2a 100%);
        transform: rotateX(-180deg);
        backface-visibility: hidden;
        clip-path: polygon(0 50%, 100% 50%, 100% 100%, 0 100%);
        box-shadow: inset 0 -0.0625rem 0 rgba(255, 255, 255, 0.1);
        // 防止动画抖动
        will-change: transform;
        // 确保动画过程中尺寸稳定
        transform-origin: 50% 100%;
        
        &.anime {
          animation-name: animate-flip-back;
        }
      }
    }
  }
  
  .time-unit {
    @apply px-2 text-primary font-bold whitespace-nowrap;
    font-size: 1.5rem;
    line-height: 5rem;
    text-shadow: 
      0 0 0.625rem rgba(0, 168, 255, 0.5),
      0 0 1.25rem rgba(0, 168, 255, 0.3);
    animation: separator-pulse 2s ease-in-out infinite;
  }
}

@keyframes animate-flip-face {
  0% {
    transform: rotateX(0deg);
    opacity: 1;
  }
  
  50% {
    transform: rotateX(-90deg);
    opacity: 0.5;
  }
  
  100% {
    transform: rotateX(-180deg);
    opacity: 0;
  }
}

@keyframes animate-flip-back {
  0% {
    transform: rotateX(180deg);
    opacity: 0;
  }
  
  50% {
    transform: rotateX(90deg);
    opacity: 0.5;
  }
  
  100% {
    transform: rotateX(0deg);
    opacity: 1;
  }
}





// 分隔符脉冲动画
@keyframes separator-pulse {
  0%, 100% {
    opacity: 0.9;
    transform: scale(1);
    text-shadow: 
      0 0 10px rgba(0, 168, 255, 0.5),
      0 0 20px rgba(0, 168, 255, 0.3);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.1);
    text-shadow: 
      0 0 15px rgba(0, 168, 255, 0.7),
      0 0 30px rgba(0, 168, 255, 0.5),
      0 0 45px rgba(0, 168, 255, 0.3);
  }
}

// 脉冲动画
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

// 霓虹灯时钟
.neon-clock {
  @apply text-center;
  
  .neon-container {
    @apply relative p-6 rounded-lg;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(20, 20, 20, 0.9));
    border: 0.0625rem solid rgba(0, 168, 255, 0.3);
    box-shadow: 
      0 0 1.25rem rgba(0, 168, 255, 0.2),
      inset 0 0 1.25rem rgba(0, 168, 255, 0.1);
    
    &::before {
      content: '';
      @apply absolute inset-0 rounded-lg;
      background: radial-gradient(circle at 50% 50%, rgba(0, 168, 255, 0.1) 0%, transparent 70%);
      animation: neon-glow 3s ease-in-out infinite alternate;
    }
  }
  
  .neon-time {
    @apply font-mono font-bold relative z-10;
    font-size: 2rem;
    color: #00a8ff;
    text-shadow: 
      0 0 0.3125rem #00a8ff,
      0 0 0.625rem #00a8ff,
      0 0 1.25rem #00a8ff,
      0 0 2.5rem #00a8ff;
    animation: neon-pulse 2s ease-in-out infinite;
    margin-bottom: 0.5rem;
  }
  
  .neon-date {
    @apply font-mono text-sm relative z-10;
    color: #4a9eff;
    text-shadow: 
      0 0 0.25rem #4a9eff,
      0 0 0.5rem #4a9eff;
    animation: neon-date-pulse 3s ease-in-out infinite;
  }
}

// 进度环时钟
.progress-clock {
  @apply flex justify-center;
  
  .progress-svg {
    @apply w-48 h-48;
    transform: rotate(-90deg);
    
    .progress-bg {
      fill: none;
      stroke: rgba(75, 85, 99, 0.3);
      stroke-width: 0.5rem;
    }
    
    .progress-ring {
      fill: none;
      stroke-width: 0.5rem;
      stroke-linecap: round;
      transition: stroke-dasharray 0.3s ease;
      
      &.progress-seconds {
        stroke: #ef4444;
      }
      
      &.progress-minutes {
        stroke: #3b82f6;
      }
      
      &.progress-hours {
        stroke: #10b981;
      }
    }
    
    .progress-text {
      fill: white;
      font-family: monospace;
      font-size: 1rem;
      font-weight: bold;
      text-anchor: middle;
      dominant-baseline: middle;
      transform: rotate(90deg);
      transform-origin: 6.25rem 6.25rem;
    }
  }
}

// 动画定义
@keyframes pulse-urgent {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes neon-pulse {
  0%, 100% {
    text-shadow: 
      0 0 0.3125rem #00a8ff,
      0 0 0.625rem #00a8ff,
      0 0 1.25rem #00a8ff,
      0 0 2.5rem #00a8ff;
    transform: scale(1);
  }
  50% {
    text-shadow: 
      0 0 0.5rem #00a8ff,
      0 0 1rem #00a8ff,
      0 0 2rem #00a8ff,
      0 0 4rem #00a8ff;
    transform: scale(1.02);
  }
}

@keyframes neon-date-pulse {
  0%, 100% {
    text-shadow: 
      0 0 0.25rem #4a9eff,
      0 0 0.5rem #4a9eff;
    opacity: 0.8;
  }
  50% {
    text-shadow: 
      0 0 0.375rem #4a9eff,
      0 0 0.75rem #4a9eff;
    opacity: 1;
  }
}

@keyframes neon-glow {
  0% {
    opacity: 0.3;
    transform: scale(1);
  }
  100% {
    opacity: 0.6;
    transform: scale(1.05);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .timezone-grid {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  
  .analog-clock {
    width: 8rem;
    height: 8rem;
  }
  
  .flip-clock {
    @apply gap-0.5;
    
    .time-box {
      @apply h-16 min-w-[2.5rem] px-1;
      font-size: 1.25rem;
      line-height: 4rem;
      
      &:before, &:after {
        @apply w-0.5 h-3 -left-0.5 -right-0.5 -mt-1.5;
      }
    }
    
    .time-unit {
      @apply px-1;
      font-size: 1rem;
      line-height: 4rem;
    }
  }
  

  
  .progress-svg {
    @apply w-32 h-32;
  }
  
  .neon-clock {
    .neon-container {
      @apply p-4;
    }
    
    .neon-time {
      font-size: 1.5rem;
    }
    
    .neon-date {
      font-size: 0.75rem;
    }
  }
}

@keyframes gentle-breathe {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.02);
  }
}

@keyframes urgent-shake {
  0%, 100% { 
    transform: translateX(0) scale(1); 
  }
  25% { 
    transform: translateX(-0.125rem) scale(1.02); 
  }
  75% { 
    transform: translateX(0.125rem) scale(1.02); 
  }
}


</style>


