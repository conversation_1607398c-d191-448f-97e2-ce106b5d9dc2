# B2CTrade 模块 API 分析文档

## 模块概述

B2CTrade（Business to Customer Trade）模块是平台的核心交易系统，主要负责用户与平台之间的饰品买卖业务。该模块集成了ZBT（至宝堂）第三方交易平台，为用户提供饰品提现、市场购买等功能。

## 目录结构

```
b2ctrade/
├── __init__.py
├── admin.py                # Django 管理后台配置
├── apps.py                 # 应用配置
├── business.py             # 核心业务逻辑
├── interfaces.py           # 接口定义
├── model_signals.py        # 模型信号处理
├── models.py               # 数据模型定义
├── serializers.py          # API 序列化器
├── tests.py                # 单元测试
├── urls.py                 # URL 路由配置
├── views.py                # API 视图
├── migrations/             # 数据库迁移文件
├── service/                # 服务层
└── zbtservice/             # ZBT 第三方服务集成
    └── zbtapi.py           # ZBT API 封装
```

## 数据模型分析

### 1. B2CMarketItem - B2C市场商品
```python
class B2CMarketItem(models.Model):
    item_info = models.OneToOneField(ItemInfo)  # 关联饰品信息
    price = models.FloatField(default=0)        # 金币价格
    zbt_price = models.FloatField(default=0.0)  # ZBT平台价格
    count = models.IntegerField(default=0)      # 库存数量
    unlimited = models.BooleanField(default=True)  # 是否无限库存
    enable = models.BooleanField(default=True)  # 是否启用
```

**设计问题：**
- 价格字段使用 FloatField，可能导致精度丢失
- 缺少价格更新时间戳，无法判断价格时效性
- 库存管理逻辑可能存在并发问题

### 2. ZBTradeRecord - ZBT交易记录
```python
class ZBTradeRecord(ModelBase):
    user = models.ForeignKey(USER_MODEL)        # 用户
    trade_url = models.CharField(max_length=256) # 交易链接
    amount = models.FloatField(default=0.0)     # 交易金额
    buy_price = models.FloatField(default=0.0)  # 购买价格
    state = models.SmallIntegerField()          # 平台交易状态
    zbt_state = models.SmallIntegerField()      # ZBT交易状态
    item_info = models.ForeignKey(ItemInfo)     # 饰品信息
    zbt_orderid = models.CharField(max_length=64) # ZBT订单ID
    package = models.ForeignKey(PackageItem)    # 关联包裹物品
    is_extract = models.BooleanField(default=False) # 是否计入提取总额
```

**设计亮点：**
- 双状态设计，分别跟踪平台和ZBT的交易状态
- 支持多种交易来源（市场、包裹）
- 包含错误信息字段便于问题排查

### 3. B2CTradeRecord - B2C交易记录
```python
class B2CTradeRecord(ModelBase):
    account = models.ForeignKey(USER_MODEL)     # 卖家账户
    buyer = models.ForeignKey(USER_MODEL)       # 买家账户
    seller_tradeurl = models.CharField()        # 卖家交易链接
    buyer_tradeurl = models.CharField()         # 买家交易链接
    amount = models.FloatField(default=0.0)     # 交易金额
    state = models.SmallIntegerField()          # 交易状态
    item_type = models.SmallIntegerField()      # 物品类型
    expire_time = models.DateTimeField()        # 过期时间
```

### 4. 统计相关模型
- `B2CTradeStatisticsDay` - 每日交易统计
- `B2CTradeStatisticsMonth` - 每月交易统计
- `B2COfficialAccount` - 官方账户管理
- `ZBTBlackList` - ZBT黑名单

## API 接口分析

### 1. 市场库存查询接口
**路径：** `GET /api/b2ctrade/market/inventory/`

**功能：** 获取B2C市场可购买的饰品列表

**参数：**
- `name`: 饰品名称搜索
- `appid`: 游戏ID（默认730-CSGO）
- `page`: 页码
- `pageSize`: 每页大小
- `order`: 排序方式

**响应格式：**
```json
{
    "code": 200,
    "data": {
        "items": [
            {
                "uid": "item_uuid",
                "price": 100.0,
                "item_info": {
                    "market_name": "AK-47 | Redline",
                    "market_name_cn": "AK-47 | 红线",
                    "icon_url": "https://...",
                    "rarity": "restricted"
                }
            }
        ],
        "total": 100,
        "page": 1,
        "limit": 10
    }
}
```

### 2. ZBT饰品提取接口
**路径：** `POST /api/b2ctrade/withdraw/zbt/item/`

**功能：** 用户提取包裹中的饰品到Steam账户

**参数：**
```json
{
    "itemId": "package_item_uuid"
}
```

**业务流程：**
1. 验证用户权限和交易链接
2. 检查系统维护状态
3. 验证提现限制（数量、金额、充值要求）
4. 检查物品状态和价格
5. 创建ZBT交易记录
6. 更新物品状态为"提现中"
7. 加入Redis队列等待处理
8. 发送SMS通知管理员

### 3. 批量提取饰品接口
**路径：** `POST /api/b2ctrade/withdraw/batch/`

**功能：** 批量提取多个饰品

**参数：**
```json
{
    "items": ["item_id1", "item_id2", ...]
}
```

### 4. 取消ZBT提取接口
**路径：** `POST /api/b2ctrade/cancel/zbt/item/`

**功能：** 取消正在进行的ZBT提取订单

### 5. ZBT交易记录查询
**路径：** `GET /api/b2ctrade/zbt/records/`

**功能：** 查询用户的ZBT交易记录

## ZBT服务集成分析

### ZbtApi 类功能
```python
class ZbtApi:
    # 核心接口
    def get_item_price_data(keyword: str)           # 查询物品价格
    def get_item_list_price_data(keyword_list)      # 批量查询价格
    def get_sell_list(game_appid, market_hash_name) # 获取在售列表
    def buy_item_v2(order_data)                     # 购买物品V2
    def get_order_detail(order_id)                  # 获取订单详情
    def buyer_cancel_order(order_id)                # 买家取消订单
    def create_steam_check(steam_id)                # 创建Steam验证
```

### 业务流程分析

#### 1. 饰品提取流程
```
用户发起提取请求
    ↓
验证用户权限和限制
    ↓
检查物品状态和价格
    ↓
创建交易记录并锁定物品
    ↓
加入Redis处理队列
    ↓
后台worker调用ZBT API
    ↓
更新交易状态
    ↓
通知用户结果
```

#### 2. 价格同步机制
- 使用定时任务更新市场价格
- 调用ZBT API获取实时价格
- 缓存价格数据避免频繁请求
- 价格变动超过阈值时更新数据库

## 存在的问题与风险

### 1. 数据一致性问题
**问题描述：**
- 物品状态更新可能存在竞态条件
- 价格同步延迟可能导致套利风险
- Redis队列和数据库状态可能不一致

**影响：**
- 用户可能重复提取同一物品
- 价格差异导致平台损失
- 交易记录状态混乱

### 2. 性能瓶颈
**问题描述：**
- 频繁的ZBT API调用可能触及限流
- 大量用户同时提取时系统压力过大
- 价格查询缺少有效缓存机制

### 3. 错误处理不足
**问题描述：**
- ZBT API失败时缺少重试机制
- 部分异常情况下物品状态无法恢复
- 错误信息对用户不够友好

### 4. 安全性问题
**问题描述：**
- 交易链接验证不够严格
- 缺少交易金额上限控制
- 未防范恶意刷单行为

## 改进建议

### 1. 数据模型优化
```python
# 建议的价格字段改进
class B2CMarketItem(models.Model):
    price = models.DecimalField(max_digits=10, decimal_places=2)  # 使用Decimal避免精度问题
    price_update_time = models.DateTimeField(auto_now=True)      # 价格更新时间
    version = models.IntegerField(default=1)                     # 乐观锁版本号
    
# 建议的库存管理改进
@transaction.atomic
def update_stock(item_id, quantity):
    item = B2CMarketItem.objects.select_for_update().get(id=item_id)
    if item.count >= quantity:
        item.count -= quantity
        item.save()
        return True
    return False
```

### 2. 缓存策略优化
```python
# 价格缓存策略
def get_cached_price(market_hash_name):
    cache_key = f"item_price:{market_hash_name}"
    price = cache.get(cache_key)
    if price is None:
        price = get_item_price_from_zbt(market_hash_name)
        cache.set(cache_key, price, timeout=300)  # 5分钟缓存
    return price

# 库存缓存
def get_cached_stock(item_id):
    cache_key = f"item_stock:{item_id}"
    return cache.get(cache_key, 0)
```

### 3. 错误处理机制
```python
# 重试机制
import tenacity

@tenacity.retry(
    stop=tenacity.stop_after_attempt(3),
    wait=tenacity.wait_exponential(multiplier=1, min=4, max=10)
)
def call_zbt_api(api_func, *args, **kwargs):
    try:
        return api_func(*args, **kwargs)
    except Exception as e:
        logger.error(f"ZBT API调用失败: {e}")
        raise

# 状态恢复机制
def recover_stuck_trades():
    stuck_trades = ZBTradeRecord.objects.filter(
        state=B2CTradeState.Trading.value,
        create_time__lt=timezone.now() - timedelta(hours=2)
    )
    for trade in stuck_trades:
        # 查询ZBT订单状态并更新
        pass
```

### 4. 监控和告警
```python
# 业务监控指标
def monitor_trade_metrics():
    # 交易成功率
    success_rate = calculate_success_rate()
    
    # 平均处理时间
    avg_processing_time = calculate_avg_processing_time()
    
    # 错误率
    error_rate = calculate_error_rate()
    
    # 发送告警
    if success_rate < 0.95:
        send_alert("交易成功率低于95%")
```

### 5. API响应优化
```python
# 统一响应格式
class B2CTradeResponse:
    @staticmethod
    def success(data=None, message="操作成功"):
        return {
            "code": 200,
            "message": message,
            "data": data,
            "timestamp": timezone.now().isoformat()
        }
    
    @staticmethod
    def error(code, message, details=None):
        return {
            "code": code,
            "message": message,
            "details": details,
            "timestamp": timezone.now().isoformat()
        }
```

## 性能优化建议

### 1. 数据库优化
- 为常用查询字段添加索引
- 使用数据库连接池
- 定期清理过期数据

### 2. 缓存优化
- 实现多级缓存策略
- 使用Redis集群提高可用性
- 优化缓存更新策略

### 3. 异步处理
- 将耗时操作移至后台队列
- 使用消息队列解耦业务逻辑
- 实现优雅的降级机制

## 总结

B2CTrade模块是平台的核心交易系统，具有较高的业务复杂度。当前实现在功能上基本满足需求，但在数据一致性、性能优化、错误处理等方面还有较大改进空间。建议按照上述优化方案逐步改进，特别是要重点关注数据一致性和用户体验的提升。

## 接口清单

| 接口名称 | 方法 | 路径 | 功能描述 |
|---------|------|------|----------|
| 市场库存查询 | GET | `/api/b2ctrade/market/inventory/` | 获取可购买饰品列表 |
| ZBT饰品提取 | POST | `/api/b2ctrade/withdraw/zbt/item/` | 提取单个饰品 |
| 批量提取饰品 | POST | `/api/b2ctrade/withdraw/batch/` | 批量提取饰品 |
| 取消ZBT提取 | POST | `/api/b2ctrade/cancel/zbt/item/` | 取消提取订单 |
| ZBT交易记录 | GET | `/api/b2ctrade/zbt/records/` | 查询交易记录 |
| 更新价格 | POST | `/api/b2ctrade/update/price/` | 更新市场价格 |
| 检查提现状态 | GET | `/api/b2ctrade/check/withdraw/` | 检查提现状态 |
