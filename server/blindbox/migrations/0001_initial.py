# -*- coding: utf-8 -*-
# Generated by Django 1.11.13 on 2021-04-14 02:09
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import steambase.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODELL),
        ('package', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='BlindBox',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128, verbose_name='name')),
                ('key', models.CharField(max_length=128, unique=True, verbose_name='key')),
                ('price', models.FloatField(default=0, verbose_name='price')),
                ('discount', models.SmallIntegerField(default=100, verbose_name='discount(%)')),
                ('cover', models.ImageField(blank=True, default=None, null=True, upload_to='cases', verbose_name='cover image')),
                ('item', models.ImageField(blank=True, default=None, null=True, upload_to='cases', verbose_name='item image')),
                ('order', models.IntegerField(default=0, verbose_name='order')),
                ('unlock', models.BooleanField(default=True, verbose_name='unlock')),
                ('enable', models.BooleanField(default=True, verbose_name='enable')),
                ('limited', models.IntegerField(default=0, verbose_name='limit timestamp')),
                ('limited_time', models.IntegerField(default=0, verbose_name='limited times')),
            ],
            options={
                'verbose_name': 'BlindBox',
                'verbose_name_plural': 'BlindBox',
                'ordering': ('case_type', 'order'),
            },
        ),
        migrations.CreateModel(
            name='BlindBoxDrop',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('show_chance', models.FloatField(default=0, verbose_name='show chance')),
                ('drop_chance_a', models.FloatField(default=0, verbose_name='drop chance a')),
                ('drop_chance_b', models.FloatField(default=0, verbose_name='drop chance b')),
                ('drop_chance_c', models.FloatField(default=0, verbose_name='drop chance c')),
                ('drop_chance_d', models.FloatField(default=0, verbose_name='drop chance d')),
                ('drop_chance_e', models.FloatField(default=0, verbose_name='drop chance e')),
                ('coins', models.FloatField(default=0, verbose_name='diamond')),
                ('custom_enable', models.BooleanField(default=False, verbose_name='custom enable')),
                ('custom_price', models.FloatField(blank=True, default=0, null=True, verbose_name='custom price')),
                ('box', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='blind_drops', to='blindbox.BlindBox', verbose_name='case')),
                ('item_info', models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='blind_box_drops', to='package.ItemInfo', verbose_name='item info')),
            ],
            options={
                'verbose_name': 'BlindBoxDrop',
                'verbose_name_plural': 'BlindBoxDrop',
            },
        ),
        migrations.CreateModel(
            name='BlindBoxGame',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('gid', models.CharField(max_length=56, verbose_name='game id')),
                ('seed', models.CharField(max_length=56, verbose_name='random seed')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='blind_game', to=settings.AUTH_USER_MODELL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'BlindBoxGame',
                'verbose_name_plural': 'BlindBoxGame',
            },
        ),
        migrations.CreateModel(
            name='BlindBoxRecord',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('price', models.FloatField(default=0, verbose_name='price')),
                ('source', models.SmallIntegerField(choices=[(1, 'Normal'), (2, 'Free')], default=1, verbose_name='type')),
                ('index', models.IntegerField(default=0, verbose_name='index')),
                ('box', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='records', to='blindbox.BlindBox', verbose_name='case')),
                ('game', models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='records_game', to='blindbox.BlindBoxGame', verbose_name='game')),
                ('item_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='blind_records', to='package.ItemInfo', verbose_name='item info')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='blind_records', to=settings.AUTH_USER_MODELL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'Blind Box Record',
                'verbose_name_plural': 'Blind Box Record',
            },
        ),
        migrations.CreateModel(
            name='BlindBoxType',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128, verbose_name='name')),
                ('category', models.SmallIntegerField(choices=[(1, 'Normal'), (2, 'Top'), (3, 'Free'), (4, 'Festival'), (5, 'FreeGive')], verbose_name='category')),
                ('order', models.IntegerField(default=0, verbose_name='order')),
            ],
            options={
                'verbose_name': 'Case Type',
                'verbose_name_plural': 'Case Type',
                'ordering': ('category', 'order'),
            },
        ),
        migrations.CreateModel(
            name='CaseBot',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('open_idle_min', models.IntegerField(default=0, verbose_name='open idle min(seconds)')),
                ('open_idle_max', models.IntegerField(default=0, verbose_name='open idle max(seconds)')),
                ('remark', models.CharField(max_length=128, verbose_name='remark')),
                ('enable', models.BooleanField(default=True, verbose_name='enable')),
                ('case', models.ManyToManyField(related_name='blindbox_bot', to='blindbox.BlindBox', verbose_name='case')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='blindbox_bot', to=settings.AUTH_USER_MODELL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'Case Bot Config',
                'verbose_name_plural': 'Case Bot Config',
            },
        ),
        migrations.CreateModel(
            name='FreeCaseConfig',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('level', models.IntegerField(default=0, verbose_name='level')),
                ('min_amount', models.FloatField(default=0, verbose_name='min point')),
                ('max_amount', models.FloatField(default=0, verbose_name='max point')),
                ('case', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='free_case', to='blindbox.BlindBox', verbose_name='case')),
            ],
            options={
                'verbose_name': 'Free Case Config',
                'verbose_name_plural': 'Free Case Config',
                'ordering': ('level',),
            },
        ),
        migrations.AddField(
            model_name='blindbox',
            name='case_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='blindbox', to='blindbox.BlindBoxType', verbose_name='blindbox type'),
        ),
    ]
