export interface SkinItem {
  id: string
  name: string
  image: string
  price: number
  rarity?: string
  wear?: number // 改为number类型，表示磨损值
  collection?: string
  category?: string
  quality?: string
  exterior?: string
  weapon?: string
  skin?: string
  isStatTrak?: boolean
  market_hash_name?: string
  rarityColor?: string
  qualityColor?: string
  exteriorColor?: string
  created_at?: string
  updated_at?: string
  // 详情页新增字段
  localizedName?: string
  description?: string
  content?: string
  updateTime?: string | number
}

export interface SkinResponse {
  skins: SkinItem[]
  total: number
}

export interface RandomSkinsResponse {
  random_skins: SkinItem[]
  total: number
} 