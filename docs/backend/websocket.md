# WebSocket实时通信文档

## 概述

本项目使用Node.js + Socket.IO实现WebSocket实时通信，支持游戏状态同步、聊天消息、开箱动画等实时功能。

## 架构设计

### 通信架构
```
前端客户端 ←→ Socket.IO服务器 ←→ Redis发布订阅 ←→ Django后端
```

### 组件说明
- **Socket.IO服务器**: Node.js实现，处理WebSocket连接
- **Redis发布订阅**: 消息中转，支持多实例扩展
- **Django后端**: 业务逻辑处理，发布消息到Redis

## Socket.IO服务器

### 服务器配置
```javascript
// ws_server.js 主要配置
const CONFIG = {
  skipAPIVerification: false,  // 生产环境必须为false
  debug: process.env.NODE_ENV !== 'production',
  apiBaseUrl: process.env.API_URL || 'http://localhost:8000',
  redisOptions: {
    host: process.env.REDIS_HOST || 'localhost',
    port: process.env.REDIS_PORT || 6379,
    password: process.env.REDIS_PASSWORD,
    retry_strategy: function(options) {
      // 指数退避重试策略
      return Math.min(options.attempt * 100, 3000);
    }
  }
};
```

### 连接管理
```javascript
// 维护Socket连接信息
const socketMap = {};

// 页面到频道的映射
const pageChannelMap = {
  'ws': 'ws_channel',
  'box': 'box_game_channel',
  'crash': 'crash_channel',
  'roll': 'roll_channel'
};

io.on('connection', (socket) => {
  console.log(`客户端连接: ${socket.id}`);
  
  // 存储连接信息
  socketMap[socket.id] = {
    userId: null,
    rooms: [],
    page: null,
    connectedAt: new Date()
  };
  
  // 断开连接处理
  socket.on('disconnect', () => {
    delete socketMap[socket.id];
    console.log(`客户端断开: ${socket.id}`);
  });
});
```

## 事件系统

### 客户端发送事件

#### 1. 加入房间
```javascript
socket.emit('join_room', {
  room: 'game_room_123',
  page: 'box',
  user_id: 456,
  session_id: 'session_abc123'
});
```

#### 2. 离开房间
```javascript
socket.emit('leave_room', {
  room: 'game_room_123'
});
```

#### 3. 发送聊天消息
```javascript
socket.emit('chat_message', {
  room: 'global',
  message: 'Hello everyone!',
  type: 'text'
});
```

#### 4. 游戏操作
```javascript
// 开箱操作
socket.emit('box_open', {
  case_id: 1,
  count: 1
});

// 下注操作
socket.emit('place_bet', {
  game_type: 'crash',
  amount: 10.0,
  room_id: 123
});
```

### 服务器推送事件

#### 1. 游戏状态更新
```javascript
// 开箱结果
socket.emit('box_result', {
  user_id: 456,
  case_id: 1,
  items: [
    {
      id: 12345,
      name: 'AK-47 红线',
      price: 50.00,
      rarity: 'Classified'
    }
  ],
  animation_data: {
    duration: 3000,
    items_shown: 20
  }
});

// 游戏状态变化
socket.emit('game_state_change', {
  game_type: 'crash',
  state: 'betting',  // waiting, betting, playing, ended
  countdown: 30,
  multiplier: 1.0
});
```

#### 2. 聊天消息
```javascript
socket.emit('chat_message', {
  id: 789,
  user: {
    id: 456,
    username: 'player123',
    avatar: 'https://example.com/avatar.jpg',
    level: 5
  },
  message: 'Hello everyone!',
  type: 'text',
  timestamp: '2023-12-01T10:00:00Z'
});
```

#### 3. 用户状态
```javascript
// 用户上线
socket.emit('user_online', {
  user_id: 456,
  username: 'player123',
  room: 'global'
});

// 用户离线
socket.emit('user_offline', {
  user_id: 456,
  room: 'global'
});
```

#### 4. 系统通知
```javascript
socket.emit('notification', {
  type: 'success',  // success, error, warning, info
  title: '开箱成功',
  message: '恭喜获得AK-47 红线！',
  duration: 5000
});
```

## Redis消息中转

### 频道设计
```python
# Django中定义的频道
REDIS_CHANNELS = {
    'ws_channel': '通用WebSocket频道',
    'box_game_channel': '开箱游戏频道',
    'crash_channel': 'Crash游戏频道',
    'roll_channel': 'Roll游戏频道',
    'chat_channel': '聊天频道'
}
```

### 消息发布 (Django端)
```python
from django_redis import get_redis_connection
import json

def send_websocket_message(channel, data):
    """发送WebSocket消息"""
    redis_client = get_redis_connection("default")
    message = {
        'type': 'websocket_message',
        'data': data,
        'timestamp': timezone.now().isoformat()
    }
    redis_client.publish(channel, json.dumps(message))

# 使用示例
def notify_box_opened(user_id, case_id, items):
    """通知开箱结果"""
    data = {
        'event': 'box_result',
        'user_id': user_id,
        'case_id': case_id,
        'items': items,
        'room': f'user_{user_id}'
    }
    send_websocket_message('box_game_channel', data)
```

### 消息订阅 (Node.js端)
```javascript
const redis = require('redis');
const client = redis.createClient(CONFIG.redisOptions);

// 订阅所有频道
Object.values(pageChannelMap).forEach(channel => {
  client.subscribe(channel);
});

// 处理接收到的消息
client.on('message', (channel, message) => {
  try {
    const data = JSON.parse(message);
    handleRedisMessage(channel, data);
  } catch (error) {
    logger.error('[Redis] 消息解析失败:', error);
  }
});

function handleRedisMessage(channel, data) {
  const { event, room, user_id, ...eventData } = data.data;
  
  if (room) {
    // 发送到特定房间
    io.to(room).emit(event, eventData);
  } else if (user_id) {
    // 发送给特定用户
    const userSockets = getUserSockets(user_id);
    userSockets.forEach(socket => {
      socket.emit(event, eventData);
    });
  } else {
    // 广播给所有连接
    io.emit(event, eventData);
  }
}
```

## 房间管理

### 房间类型
- **用户房间**: `user_{user_id}` - 用户私有消息
- **游戏房间**: `game_{game_type}_{room_id}` - 特定游戏房间
- **聊天房间**: `chat_{room_name}` - 聊天室
- **全局房间**: `global` - 全局广播

### 房间操作
```javascript
// 加入房间
socket.on('join_room', (data) => {
  const { room, user_id, page } = data;
  
  // 验证权限
  if (!validateRoomAccess(user_id, room)) {
    socket.emit('error', { message: '无权限访问该房间' });
    return;
  }
  
  // 加入Socket.IO房间
  socket.join(room);
  
  // 更新连接信息
  socketMap[socket.id].rooms.push(room);
  socketMap[socket.id].userId = user_id;
  socketMap[socket.id].page = page;
  
  // 通知其他用户
  socket.to(room).emit('user_joined', {
    user_id: user_id,
    room: room
  });
  
  logger.info(`用户 ${user_id} 加入房间 ${room}`);
});

// 离开房间
socket.on('leave_room', (data) => {
  const { room } = data;
  const socketInfo = socketMap[socket.id];
  
  socket.leave(room);
  
  // 更新连接信息
  const index = socketInfo.rooms.indexOf(room);
  if (index > -1) {
    socketInfo.rooms.splice(index, 1);
  }
  
  // 通知其他用户
  socket.to(room).emit('user_left', {
    user_id: socketInfo.userId,
    room: room
  });
});
```

## 安全机制

### 身份验证
```javascript
// Cookie验证
const cookie = require('cookie');

function validateUser(socket, data) {
  const cookies = cookie.parse(socket.handshake.headers.cookie || '');
  const sessionId = cookies.sessionid;
  
  if (!sessionId) {
    return false;
  }
  
  // 向Django API验证session
  return new Promise((resolve, reject) => {
    request({
      url: `${CONFIG.apiBaseUrl}/api/auth/checklogin/`,
      headers: {
        'Cookie': `sessionid=${sessionId}`
      }
    }, (error, response, body) => {
      if (error || response.statusCode !== 200) {
        resolve(false);
      } else {
        const result = JSON.parse(body);
        resolve(result.code === 200);
      }
    });
  });
}
```

### 权限控制
```javascript
function validateRoomAccess(userId, room) {
  // 用户只能访问自己的私有房间
  if (room.startsWith('user_')) {
    const roomUserId = parseInt(room.split('_')[1]);
    return roomUserId === userId;
  }
  
  // 游戏房间需要检查是否在房间内
  if (room.startsWith('game_')) {
    return checkGameRoomMembership(userId, room);
  }
  
  // 公开聊天室允许所有用户
  if (room.startsWith('chat_') || room === 'global') {
    return true;
  }
  
  return false;
}
```

### 频率限制
```javascript
const rateLimiter = new Map();

function checkRateLimit(userId, action) {
  const key = `${userId}_${action}`;
  const now = Date.now();
  const limit = rateLimits[action] || { count: 10, window: 60000 };
  
  if (!rateLimiter.has(key)) {
    rateLimiter.set(key, { count: 1, resetTime: now + limit.window });
    return true;
  }
  
  const data = rateLimiter.get(key);
  
  if (now > data.resetTime) {
    data.count = 1;
    data.resetTime = now + limit.window;
    return true;
  }
  
  if (data.count >= limit.count) {
    return false;
  }
  
  data.count++;
  return true;
}

// 频率限制配置
const rateLimits = {
  'chat_message': { count: 30, window: 60000 },    // 每分钟30条消息
  'box_open': { count: 10, window: 60000 },        // 每分钟10次开箱
  'place_bet': { count: 60, window: 60000 },       // 每分钟60次下注
};
```

## 错误处理

### 连接错误
```javascript
socket.on('connect_error', (error) => {
  logger.error('[Socket] 连接错误:', error);
  socket.emit('error', {
    type: 'connection_error',
    message: '连接失败，请刷新页面重试'
  });
});

socket.on('error', (error) => {
  logger.error('[Socket] 错误:', error);
});
```

### Redis错误
```javascript
client.on('error', (error) => {
  logger.error('[Redis] 连接错误:', error);
  
  // 尝试重连
  setTimeout(() => {
    client.connect();
  }, 5000);
});

client.on('reconnecting', () => {
  logger.info('[Redis] 正在重连...');
});
```

### 消息处理错误
```javascript
function safeEmit(socket, event, data) {
  try {
    socket.emit(event, data);
  } catch (error) {
    logger.error(`[Socket] 发送消息失败: ${event}`, error);
  }
}

function safeBroadcast(room, event, data) {
  try {
    io.to(room).emit(event, data);
  } catch (error) {
    logger.error(`[Socket] 广播消息失败: ${event} to ${room}`, error);
  }
}
```

## 性能优化

### 连接管理优化
```javascript
// 定期清理无效连接
setInterval(() => {
  const now = Date.now();
  Object.keys(socketMap).forEach(socketId => {
    const socket = io.sockets.sockets.get(socketId);
    if (!socket || !socket.connected) {
      delete socketMap[socketId];
    }
  });
}, 30000);

// 限制单用户连接数
function limitUserConnections(userId) {
  const userSockets = getUserSockets(userId);
  if (userSockets.length > 3) {
    // 断开最旧的连接
    const oldestSocket = userSockets[0];
    oldestSocket.emit('error', {
      type: 'multiple_connections',
      message: '检测到多个连接，已断开旧连接'
    });
    oldestSocket.disconnect();
  }
}
```

### 消息批处理
```javascript
const messageQueue = new Map();

function queueMessage(room, event, data) {
  if (!messageQueue.has(room)) {
    messageQueue.set(room, []);
    
    // 批量发送
    setTimeout(() => {
      const messages = messageQueue.get(room);
      if (messages && messages.length > 0) {
        io.to(room).emit('batch_messages', messages);
        messageQueue.delete(room);
      }
    }, 100);
  }
  
  messageQueue.get(room).push({ event, data, timestamp: Date.now() });
}
```

## 监控和日志

### 连接监控
```javascript
function getConnectionStats() {
  const totalConnections = Object.keys(socketMap).length;
  const userConnections = Object.values(socketMap)
    .filter(info => info.userId)
    .length;
  
  return {
    total: totalConnections,
    authenticated: userConnections,
    anonymous: totalConnections - userConnections,
    rooms: io.sockets.adapter.rooms.size
  };
}

// 每分钟记录连接统计
setInterval(() => {
  const stats = getConnectionStats();
  logger.info('[Stats] WebSocket连接统计:', stats);
}, 60000);
```

### 性能监控
```javascript
const performanceMetrics = {
  messagesPerSecond: 0,
  messageCount: 0,
  lastReset: Date.now()
};

function updateMessageMetrics() {
  performanceMetrics.messageCount++;
  
  const now = Date.now();
  if (now - performanceMetrics.lastReset >= 1000) {
    performanceMetrics.messagesPerSecond = performanceMetrics.messageCount;
    performanceMetrics.messageCount = 0;
    performanceMetrics.lastReset = now;
  }
}
```

## 前端集成

### 基础连接
```javascript
// 前端连接示例
// 开发环境
const socket = io('ws://localhost:4000', {
  // 配置选项...
});

// 生产环境
const socket = io('wss://socket.cs2.net.cn', {
  transports: ['websocket', 'polling'],
  timeout: 20000,
  reconnection: true,
  reconnectionAttempts: 5,
  reconnectionDelay: 1000
});

// 连接成功
socket.on('connect', () => {
  console.log('WebSocket连接成功');
  
  // 加入用户房间
  socket.emit('join_room', {
    room: `user_${userId}`,
    user_id: userId,
    page: 'box'
  });
});

// 连接失败
socket.on('connect_error', (error) => {
  console.error('WebSocket连接失败:', error);
});

// 断开连接
socket.on('disconnect', (reason) => {
  console.log('WebSocket断开连接:', reason);
});
```

### 事件监听
```javascript
// 监听开箱结果
socket.on('box_result', (data) => {
  showBoxResult(data.items);
  updateUserBalance(data.new_balance);
});

// 监听聊天消息
socket.on('chat_message', (data) => {
  addChatMessage(data);
});

// 监听游戏状态
socket.on('game_state_change', (data) => {
  updateGameUI(data);
});

// 监听系统通知
socket.on('notification', (data) => {
  showNotification(data.type, data.message);
});
```

## 故障排除

### 常见问题

1. **连接失败**
```javascript
// 检查网络连接
if (!navigator.onLine) {
  console.error('网络连接不可用');
  return;
}

// 检查服务器状态
fetch('/health')
  .then(response => response.ok)
  .catch(() => console.error('服务器不可用'));
```

2. **消息丢失**
```javascript
// 实现消息确认机制
socket.emit('important_message', data, (acknowledgment) => {
  if (!acknowledgment.success) {
    // 重发消息
    setTimeout(() => {
      socket.emit('important_message', data);
    }, 1000);
  }
});
```

3. **内存泄漏**
```javascript
// 正确清理事件监听器
function cleanup() {
  socket.removeAllListeners();
  socket.disconnect();
}

// 页面卸载时清理
window.addEventListener('beforeunload', cleanup);
```

这份WebSocket文档详细介绍了实时通信系统的设计和实现，包括安全机制、性能优化和故障处理等关键环节。
