from rest_framework.pagination import <PERSON>NumberPagination
from rest_framework.response import Response
from django.core.paginator import Pa<PERSON><PERSON>, EmptyPage, PageNotAnInteger
from django.core.cache import cache
import logging
import pytz
from django.db import transaction
from django.utils.translation import gettext_lazy as _
from decimal import Decimal, ROUND_HALF_UP, InvalidOperation
from decimal import Context, Overflow, getcontext

from django.conf import settings


from steambase.enums import RespCode

from sitecfg.models import SEO
from authentication.models import AuthUser
from charge.models import ChargeRecord, PayMethod
from b2ctrade.models import ZBTradeRecord
from package.models import PackageItem

from agent.serializers import AgentSiteSerializer, AgentSiteUserSerializer, AgentSiteUserChargeRecordSerializer, AgentSiteUserWithdrawSerializer, AgentSiteUserSkinRecordSerializer, ArticleCategorySerializer, AgentBalanceRecordSerializer, AgentSerializer, AgentWithdrawalOrderSerializer

from agent.models import Agent, Article, ArticleCategory, AgentBalanceRecord, AgentWithdrawalOrder

_logger = logging.getLogger(__name__)


def get_agent_site(user, fields):
    site = SEO.objects.filter(agent = user).order_by('-id')
    site_list = AgentSiteSerializer(site, many=True, fields=fields).data

    resp = {
        'items':site_list
    }
    return RespCode.Succeed.value, resp

def get_agent_site_user(agent, fields, page, page_size):
    # 缓存key
    cache_key = 'agent_site_user_%s_%s_%s' % (agent.id, page, page_size)
    # 从缓存中获取
    resp = cache.get(cache_key)
    if resp:
        return RespCode.Succeed.value, resp
    # 获取与 agent 关联的 SEO 记录
    sites = SEO.objects.filter(agent=agent).order_by('-id')
    
    items = []
    for site in sites:
        # 获取与 site 关联的所有 AuthUser 数据
        auth_users = AuthUser.objects.filter(domain=site.url)
        for auth_user in auth_users:
            serialized_data = AgentSiteUserSerializer(auth_user, fields=fields).data
            items.append((auth_user.date_joined, serialized_data))
    
    # 按照加入时间进行排序
    items.sort(key=lambda x: x[0], reverse=True)
    sorted_items = [item[1] for item in items]

    # 使用 Django Paginator 进行分页
    paginator = Paginator(sorted_items, page_size)
    paginated_items = paginator.page(page)
    
    resp = {
        'items': list(paginated_items),
        'total': paginator.count
    }

    # 缓存数据
    cache.set(cache_key, resp, timeout=60*30)
    
    return RespCode.Succeed.value, resp

def get_agent_site_user_charge_records(agent, fields, page, page_size):
    # 缓存key
    cache_key = 'agent_site_user_charge_records_%s_%s_%s' % (agent.id, page, page_size)
    # 从缓存中获取
    resp = cache.get(cache_key)
    if resp:
        return RespCode.Succeed.value, resp
    # 获取与 agent 关联的 SEO 记录
    sites = SEO.objects.filter(agent=agent).order_by('-id')
    
    charge_records = []
    for site in sites:
        # 获取与 site 关联的所有 AuthUser 数据
        auth_users = AuthUser.objects.filter(domain=site.url)
        for auth_user in auth_users:
            # 获取与 auth_user 关联的所有 ChargeRecord 数据
            records = ChargeRecord.objects.filter(user=auth_user)
            for record in records:
                serialized_data = AgentSiteUserChargeRecordSerializer(record, fields=fields).data
                charge_records.append((record.create_time, serialized_data))  
    
    # 按照充值时间进行排序
    charge_records.sort(key=lambda x: x[0], reverse=True)
    sorted_records = [record[1] for record in charge_records]

    # 使用 Django Paginator 进行分页
    paginator = Paginator(sorted_records, page_size)
    paginated_records = paginator.page(page)
    
    resp = {
        'items': list(paginated_records),
        'total': paginator.count
    }

    # 缓存数据
    cache.set(cache_key, resp, timeout=60*30)
    
    return RespCode.Succeed.value, resp

def get_agent_site_user_withdraw_records(agent, fields, page, page_size):
    # 缓存key
    cache_key = 'agent_site_user_withdraw_records_%s_%s_%s' % (agent.id, page, page_size)
    # 从缓存中获取
    resp = cache.get(cache_key)
    if resp:
        return RespCode.Succeed.value, resp
    # 获取与 agent 关联的 SEO 记录
    sites = SEO.objects.filter(agent=agent).order_by('-id')
    
    withdraw_records = []
    for site in sites:
        # 获取与 site 关联的所有 AuthUser 数据
        auth_users = AuthUser.objects.filter(domain=site.url)
        for auth_user in auth_users:
            # 获取与 auth_user 关联的所有 WithdrawRecord 数据
            records = ZBTradeRecord.objects.filter(user=auth_user)
            for record in records:
                serialized_data = AgentSiteUserWithdrawSerializer(record, fields=fields).data
                withdraw_records.append((record.create_time, serialized_data))  
    
    # 按照提现时间进行排序
    withdraw_records.sort(key=lambda x: x[0], reverse=True)
    sorted_records = [record[1] for record in withdraw_records]

    # 使用 Django Paginator 进行分页
    paginator = Paginator(sorted_records, page_size)
    paginated_records = paginator.page(page)
    
    resp = {
        'items': list(paginated_records),
        'total': paginator.count
    }

    # 缓存数据
    cache.set(cache_key, resp, timeout=60*30)
    
    return RespCode.Succeed.value, resp

def get_agent_site_user_skin_records(agent, fields, page, page_size):
    # 缓存key
    cache_key = 'agent_site_user_skin_records_%s_%s_%s' % (agent.id, page, page_size)
    # 从缓存中获取
    resp = cache.get(cache_key)
    if resp:
        return RespCode.Succeed.value, resp
    # 获取与 agent 关联的 SEO 记录
    sites = SEO.objects.filter(agent=agent).order_by('-id')
    
    skin_records = []
    for site in sites:
        # 获取与 site 关联的所有 AuthUser 数据
        auth_users = AuthUser.objects.filter(domain=site.url)
        for auth_user in auth_users:
            # 获取与 auth_user 关联的所有 SkinRecord 数据
            records = PackageItem.objects.filter(user=auth_user, state = 1)
            for record in records:
                serialized_data = AgentSiteUserSkinRecordSerializer(record, fields=fields).data
                skin_records.append((record.create_time, serialized_data))  
    
    # 按照提现时间进行排序
    skin_records.sort(key=lambda x: x[0], reverse=True)
    sorted_records = [record[1] for record in skin_records]

    # 使用 Django Paginator 进行分页
    paginator = Paginator(sorted_records, page_size)
    paginated_records = paginator.page(page)
    
    resp = {
        'items': list(paginated_records),
        'total': paginator.count
    }

    # 缓存数据
    cache.set(cache_key, resp, timeout=60*30)
    
    return RespCode.Succeed.value, resp

def manage_agent_article_category(user, site, name):
    site = SEO.objects.filter(agent=user, id=site).first()
    if site:
        agent_article_category, created = ArticleCategory.objects.update_or_create(
            site=site,
            defaults={'name': name}
        )
        if created:
            return RespCode.Succeed.value, 'Category created successfully'
        else:
            return RespCode.Succeed.value, 'Category updated successfully'
    else:
        return RespCode.NotFound.value, 'Site not found'

def manage_agent_article(user, title, content, category_id, article_id=None):
    try:
        if not title or not content or not category_id:
            return RespCode.InvalidParams.value, 'Invalid parameters'

        article_category = ArticleCategory.objects.filter(id=category_id).first()
        if not article_category:
            return RespCode.NotFound.value, 'Category not found'

        if article_id:
            # 编辑操作
            #article = Article.objects.filter(id=article_id, category__site__agent=user).first()
            article = Article.objects.filter(id=article_id, user=user).first()
            if not article:
                return RespCode.NotFound.value, 'Article not found'
            article.title = title
            article.content = content
            article.category = article_category
            article.save()
            return RespCode.Succeed.value, 'Article updated successfully'
        else:
            # 新增操作
            Article.objects.create(category=article_category, title=title, content=content, user=user)
            return RespCode.Succeed.value, 'Article created successfully'
    except Exception as e:
        _logger.exception(e)
        return RespCode.Exception.value, 'Exception'
    
    
    
    
def get_article_categories(user, page, page_size):
    # 缓存key
    #cache_key = f'article_categories_{user.id}_{page}_{page_size}'
    # 从缓存中获取
    #resp = cache.get(cache_key)
    # if resp:
    #     return RespCode.Succeed.value, resp

    # 获取与 agent 关联的 ArticleCategory 记录
    categories = ArticleCategory.objects.filter(user = user).select_related('site').order_by('-id')

    # 使用 Django Paginator 进行分页
    paginator = Paginator(categories, page_size)
    paginated_items = paginator.page(page)
    
    #serialized_data = ArticleCategorySerializer(paginated_items, many=True).data
     # 序列化数据并包含 site_url
    serialized_data = [
        {
            "id": category.id,
            "name": category.name,
            "enable": category.enable,
            "site_url": category.site.url if category.site else None
        }
        for category in paginated_items
    ]

    
    resp = {
        'items': serialized_data,
        'total': paginator.count
    }

    # 缓存数据
    #cache.set(cache_key, resp, timeout=60*5)
    
    return RespCode.Succeed.value, resp

def manage_article_category(user, site_id, category, category_id=None):
    try:
        if not site_id or not category:
            return RespCode.InvalidParams.value, 'Invalid parameters'

        site = SEO.objects.filter(agent=user, id=site_id).first()
        if not site:
            return RespCode.NotFound.value, 'Site not found'

        if category_id:
            # 编辑操作
            article_category = ArticleCategory.objects.filter(id=category_id, site=site).first()
            if not article_category:
                return RespCode.NotFound.value, 'Category not found'
            article_category.name = category
            article_category.save()
            return RespCode.Succeed.value, 'Category updated successfully'
        else:
            # 新增操作
            ArticleCategory.objects.create(site=site, name=category, user=user)
            return RespCode.Succeed.value, 'Category created successfully'

    except Exception as e:
        _logger.exception(e)
        return RespCode.Exception.value, 'Exception'

def get_article_category(id):
    try:
        category = ArticleCategory.objects.get(id=id)
        serializer = ArticleCategorySerializer(category)
        resp = {
            'items': serializer.data
        }
        return RespCode.Succeed.value, resp
    except ArticleCategory.DoesNotExist:
        return RespCode.NotFound.value, 'Category not found'
    except Exception as e:
        _logger.exception(e)
        return RespCode.Exception.value, 'Exception'
    
def get_article(id):
    try:
        article = Article.objects.get(id=id)
        beijing_tz = pytz.timezone('Asia/Shanghai')
        resp = {
            'items': {
                'title': article.title,
                'content': article.content,
                'category_id': article.category.id,
                'category_name': article.category.name,
                'create_time': article.create_time.astimezone(beijing_tz).strftime('%Y-%m-%d %H:%M:%S'),
                'update_time': article.update_time.astimezone(beijing_tz).strftime('%Y-%m-%d %H:%M:%S') 
            }
        }
        return RespCode.Succeed.value, resp
    except Article.DoesNotExist:
        return RespCode.NotFound.value, 'Article not found'
    except Exception as e:
        _logger.exception(e)
        return RespCode.Exception.value, 'Exception'
    
def get_article_list(user, page, page_size, category_id = None):
    try:
        if category_id:
            articles = Article.objects.filter(category__site__agent=user, category_id=category_id).order_by('-id')
        else:
            articles = Article.objects.filter(category__site__agent=user).order_by('-id')

        paginator = Paginator(articles, page_size)
        paginated_items = paginator.page(page)
        beijing_tz = pytz.timezone('Asia/Shanghai')
        
        serialized_data = [
            {
                "id": article.id,
                "title": article.title,
                #"content": article.content,
                "category": article.category.name,
                'create_time': article.create_time.astimezone(beijing_tz).strftime('%Y-%m-%d %H:%M:%S'),
                'update_time': article.update_time.astimezone(beijing_tz).strftime('%Y-%m-%d %H:%M:%S')
            }
            for article in paginated_items
        ]

        resp = {
            'items': serialized_data,
            'total': paginator.count
        }
        return RespCode.Succeed.value, resp
    except Exception as e:
        _logger.exception(e)
        return RespCode.Exception.value, 'Exception'
    
# 删除文章
def delete_article(user, article_id):
    try:
        article = Article.objects.get(id=article_id, user=user)
        article.delete()
        return RespCode.Succeed.value, 'Article deleted successfully'
    except Article.DoesNotExist:
        return RespCode.NotFound.value, 'Article not found'
    except Exception as e:
        _logger.exception(e)
        return RespCode.Exception.value, 'Exception'
    
# 编辑站点
def edit_site(user, site_id, subtitle, title, keywords, description):
    try:
        site = SEO.objects.get(id=site_id, agent=user)
        site.subtitle = subtitle
        site.title = title
        site.keywords = keywords
        site.description = description
        site.save()
        return RespCode.Succeed.value, 'Site updated successfully'
    except SEO.DoesNotExist:
        return RespCode.NotFound.value, 'Site not found'
    except Exception as e:
        _logger.exception(e)
        return RespCode.Exception.value, 'Exception'
    
# 站点信息
def get_site_info(user, id):
    try:
        site = SEO.objects.get(id=id, agent=user)
        resp = {
            'items': {
                'url': site.url,
                'name': site.name,
                'icp': site.icp,
                'subtitle': site.subtitle,
                'title': site.title,
                'keywords': site.keywords,
                'description': site.description
            }
        }
        return RespCode.Succeed.value, resp
    except SEO.DoesNotExist:
        return RespCode.NotFound.value, 'Site not found'
    except Exception as e:
        _logger.exception(e)
        return RespCode.Exception.value, 'Exception'
    
def get_site_article_list(site, page, page_size):
    try:
        articles = Article.objects.filter(enable=True).order_by('-id')

        paginator = Paginator(articles, page_size)
        paginated_items = paginator.page(page)
        py_beijing = pytz.timezone('Asia/Shanghai')
        
        serialized_data = [
            {
                "id": article.id,
                "title": article.title,
                #"content": article.content,
                "category": article.category.name,
                'create_time': article.create_time.astimezone(py_beijing).strftime('%Y-%m-%d %H:%M:%S'),
                'update_time': article.update_time.astimezone(py_beijing).strftime('%Y-%m-%d %H:%M:%S')
            }
            for article in paginated_items
        ]

        resp = {
            'items': serialized_data,
            'total': paginator.count
        }
        return RespCode.Succeed.value, resp
    except Exception as e:
        _logger.exception(e)
        return RespCode.Exception.value, 'Exception'
    


\
def user_withdrawal_to_agent(user, amount, zbt_orderid, rate=0.05):
    """
    用户提现并写入代理商余额记录

    参数:
    user -- 提现的用户 (AuthUser 实例)
    amount -- 提取金额 (Decimal)
    rate -- 提现手续费率 (默认为0.05)
    """
    try:
        # 确保 `amount` 是有效的 Decimal 类型
        try:
            amount = Decimal(amount).quantize(Decimal('0.00'), rounding=ROUND_HALF_UP)
        except (ValueError, InvalidOperation):
            _logger.error(f"Invalid `amount` for Decimal conversion: {amount}")
            return
        
        rate = Decimal(rate)
        fee = (amount * rate).quantize(Decimal('0.00'), rounding=ROUND_HALF_UP)
        net_amount = (amount + fee).quantize(Decimal('0.00'), rounding=ROUND_HALF_UP) 

        # 检查用户的 domain 和 SEO 记录
        user_domain = user.domain
        seo_record = SEO.objects.filter(url=user_domain).first()
        if not seo_record or not seo_record.agent:
            _logger.info(f"用户 {user.username} 不属于任何代理商")
            return
        
        agent = Agent.objects.filter(user=seo_record.agent).first()
        if not agent:
            _logger.info(f"代理商记录未找到：{seo_record.agent}")
            return
        
        with transaction.atomic():
            # 检查是否已有相同 out_trade_no 的代理商记录，避免重复
            if AgentBalanceRecord.objects.filter(out_trade_no=zbt_orderid).exists():
                _logger.info(f"订单号 {zbt_orderid} 已处理过，跳过重复写入")
                return

            agent.update_balance(-net_amount, 1, zbt_orderid, _(f"用户 {user.username} 提取饰品金额 {amount}，手续费 {fee}"))
            _logger.info(f"用户 {user.steam.personaname} 提取饰品确认成功, 价值 {amount}，已成功从代理商 {agent.agent_name} 的余额中扣除，含手续费 {fee}")

    except InvalidOperation as e:
        _logger.error(f"Decimal 转换无效，请检查 amount 或 fee 的值: amount={amount}, fee={fee}")
    except Exception as e:
        _logger.exception(f"用户提货更新代理商余额时发生错误：{e}")




def user_recharge_to_agent(user, amount, out_trade_no):
    try:
        amount = Decimal(amount).quantize(Decimal('0.00'), rounding=ROUND_HALF_UP)

        # 检查订单是否已处理
        existing_record = AgentBalanceRecord.objects.filter(out_trade_no=out_trade_no).first()
        if existing_record:
            _logger.info(f"订单号 {out_trade_no} 已存在，跳过记录")
            return

        # 获取代理商记录
        seo_record = SEO.objects.filter(url=user.domain).first()
        if not seo_record or not seo_record.agent:
            _logger.warning(f"用户 {user.steam.personaname} 未关联代理商")
            return

        agent = Agent.objects.filter(user=seo_record.agent).first()
        if not agent:
            _logger.warning(f"未找到代理商记录，用户 {user.steam.personaname}")
            return
        
        agent.update_balance(amount, 0, out_trade_no, remark=_('用户充值'))

        # 创建代理商记录
        # AgentBalanceRecord.objects.create(
        #     agent=agent,
        #     balance_changed=amount,
        #     balance_before=agent.balance,
        #     balance_after=agent.balance + amount,
        #     type=0,
        #     remark=f"用户 {user.username}:{user.steam.personaname} 充值 {amount}，订单号 {out_trade_no}",
        #     out_trade_no=out_trade_no
        # )
        _logger.info(f"用户 {user.steam.personaname} 的充值 {amount} 已成功计入代理商 {agent.agent_name} 的余额")
    except Exception as e:
        _logger.exception(f"用户充值更新代理商余额时发生错误：{e}")




def get_agent_balance_records(agent, page, page_size):
    # 缓存key
    cache_key = 'agent_balance_records_%s_%s_%s' % (agent.id, page, page_size)
    # 从缓存中获取
    resp = cache.get(cache_key)
    # if resp:
    #     return RespCode.Succeed.value, resp

    # 获取与 agent 关联的所有 AgentBalanceRecord 数据
    records = AgentBalanceRecord.objects.filter(agent=agent).order_by('-create_time')

    # 使用 Django Paginator 进行分页
    paginator = Paginator(records, page_size)
    paginated_records = paginator.page(page)

    # 序列化数据
    serialized_data = AgentBalanceRecordSerializer(paginated_records, many=True).data

    resp = {
        'items': serialized_data,
        'total': paginator.count
    }

    # 缓存数据
    cache.set(cache_key, resp, timeout=60*30)
    
    return RespCode.Succeed.value, resp

def get_agent_info(user):
    try:
        agent = Agent.objects.get(user=user)
        serializer = AgentSerializer(agent)
        resp = {
            'items': serializer.data
        }
        return RespCode.Succeed.value, resp
    except Agent.DoesNotExist:
        return RespCode.NotFound.value, 'Agent not found'
    except Exception as e:
        _logger.exception(e)
        return RespCode.Exception.value, 'Exception'

def edit_agent_info(user, agent_name, agent_phone, agent_email, agent_qq, agent_wechat, agent_alipay, agent_wechatpay, agent_bankname, agent_bankcard, agent_bank):
    try:
        agent = Agent.objects.get(user=user)
        agent.agent_name = agent_name
        agent.agent_phone = agent_phone
        agent.agent_email = agent_email
        agent.agent_qq = agent_qq
        agent.agent_wechat = agent_wechat
        agent.agent_alipay = agent_alipay
        agent.agent_wechatpay = agent_wechatpay
        agent.agent_bankname = agent_bankname
        agent.agent_bankcard = agent_bankcard
        agent.agent_bank = agent_bank
        agent.save()
        return RespCode.Succeed.value, 'Agent updated successfully'
    except Agent.DoesNotExist:
        return RespCode.NotFound.value, 'Agent not found'
    except Exception as e:
        _logger.exception(e)
        return RespCode.Exception.value, 'Exception'
    
# 代理商提现
def agent_withdrawal(user, amount, pay_type):
    try:
        agent = Agent.objects.get(user=user)
        amount = Decimal(amount).quantize(Decimal('0.00'), rounding=ROUND_HALF_UP)
        if agent.balance < amount or amount <= 0:
            return RespCode.NoBalance.value, 'Insufficient balance'
        with transaction.atomic():
            agent.update_balance(-amount, 2, _(f"代理商提现 {amount}"))
            AgentWithdrawalOrder.objects.create(agent=agent, amount=amount, pay_type=pay_type)
            return RespCode.Succeed.value, 'Withdrawal success'
    except Agent.DoesNotExist:
        return RespCode.NotFound.value, 'Agent not found'
    except Exception as e:
        _logger.exception(e)
        return RespCode.Exception.value, 'Exception'
    
# 获取代理商提现记录
def get_agent_withdrawal_records(agent, page, page_size):
    # 缓存key
    cache_key = 'agent_withdrawal_records_%s_%s_%s' % (agent.id, page, page_size)
    # 从缓存中获取
    resp = cache.get(cache_key)
    # if resp:
    #     return RespCode.Succeed.value, resp

    # 获取与 agent 关联的所有 AgentWithdrawalOrder 数据
    records = AgentWithdrawalOrder.objects.filter(agent=agent).order_by('-create_time')

    # 使用 Django Paginator 进行分页
    paginator = Paginator(records, page_size)
    paginated_records = paginator.page(page)

    # 序列化数据
    serialized_data = AgentWithdrawalOrderSerializer(paginated_records, many=True).data

    resp = {
        'items': serialized_data,
        'total': paginator.count
    }

    # 缓存数据
    cache.set(cache_key, resp, timeout=60*30)
    
    return RespCode.Succeed.value, resp