from django.conf.urls import url

from grab import views, model_signals



app_name = 'grab'
urlpatterns = [
    url(r'^list/', views.GetGrabRoomListView.as_view()),
    url(r'^userinfo', views.GetGrabUserView.as_view()),
    url(r'^room/', views.GetGrabRoomDetailView.as_view()),
    url(r'^positionList/', views.GetRoomPositionListView.as_view()),
    url(r'^dynamic/', views.GetGrabDynamicView.as_view()),
    url(r'^history/', views.GetGrabUserHistoryView.as_view()),
    url(r"^pick/", views.PickGrabCardView.as_view()),
    url(r'^join/', views.JoinGrabRoomView.as_view()),
]
