import logging

from django.core.paginator import Paginator
from django.utils.translation import gettext_lazy as _

from rest_framework.permissions import AllowAny
from rest_framework.views import APIView

from luckybox.models import LuckyBoxRecord, LuckyBoxCategory
from luckybox.serializers import LuckyBoxRecordSerializer
from package.models import ItemInfo
from sitecfg.interfaces import get_maintenance, get_maintenance_luckybox
from steambase.enums import RespCode
from steambase.utils import reformat_resp, current_user, Weapon, Category
from luckybox.business import get_lucky_inventory, get_lucky_inventory_item, get_lucky_box, search_lucky_item
from luckybox.business import random_lucky_item, get_lucky_percent, try_lucky_box_open

_logger = logging.getLogger(__name__)
_STEAM_IMG_BASE = 'https://steamcommunity-a.akamaihd.net/economy/image/{icon_url}/'


class GetLuckyBoxView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            code, resp = get_lucky_box()
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')
    # def get(self, request):
    #     categories = LuckyBoxCategory.objects.values("category", "image")
    #
    #     for cate in categories:
    #         category = cate['category']
    #         image = cate.get('image')
    #         if image:
    #             Weapon[category].update(icon_url="/media/"+image)
    #         else:
    #             weapon_first = ItemInfo.objects.filter(type=category).first()
    #             Weapon[category].update(icon_url=_STEAM_IMG_BASE.format(icon_url=weapon_first.icon_url))
    #     for key, value in Category.items():
    #         Weapon[key].update(**value)
    #     return reformat_resp(RespCode.Succeed, Weapon)


class GetLuckyInventoryView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            page = request.query_params.get("page", 1)
            pageSize = request.query_params.get("pageSize", 10)
            type = request.query_params.get('type', None)
            weapon = request.query_params.get('weapon', None)
            search = request.query_params.get('search', None)
            order = request.query_params.get('order', None)
            minPrice = request.query_params.get("minPrice", 0)
            maxPrice = request.query_params.get("maxPrice", None)
            code, resp = get_lucky_inventory(type, weapon, search, order, minPrice, maxPrice, page, pageSize)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetLuckyInventoryItemView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            lid = request.query_params.get('id', None)
            code, resp = get_lucky_inventory_item(lid)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class SearchLuckyInventoryItemView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            weapon = request.query_params.get('weapon', None)
            category = request.query_params.get('category', None)
            query = request.query_params.get('q', None)
            code, resp = search_lucky_item(category, weapon, query)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class RandomLuckyInventoryItemView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            category = request.query_params.get('type', None)
            weapon = request.query_params.get('weapon', None)
            code, resp = random_lucky_item(category, weapon)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetLuckyPercentView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            percent = request.query_params.get('percent', None)
            name = request.query_params.get('name', None)
            code, resp = get_lucky_percent(int(percent), name)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class TryLuckyBoxView(APIView):

    def post(self, request):
        try:
            user = current_user(request)
            if get_maintenance():
                return reformat_resp(RespCode.Maintenance.value, {},
                                     '全站维护中')
            if get_maintenance_luckybox():
                if user.is_superuser:
                    pass
                else:
                    return reformat_resp(RespCode.Maintenance.value, {},
                                         _('拉货维护中'))
            percent = request.data.get('percent', None)
            target = request.data.get('target', None)
            # amount = request.data.get('amount', 0)
            fields = (
                'uid', 'market_name', 'market_hash_name',
                'market_name_cn', 'icon_url', 'price', 'part', 'amount', 'state', 'source')
            code, resp = try_lucky_box_open(user, int(percent), target, fields)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetLuckyRecord(APIView):

    def get(self, request):
        page = request.query_params.get('page', 1)
        pageSize = request.query_params.get('pageSize', 10)
        #user = current_user(request)
        #records = LuckyBoxRecord.objects.filter(user=user).order_by("-create_time")
        records = LuckyBoxRecord.objects.order_by("-create_time")

        paginator = Paginator(records, pageSize)
        items = paginator.page(page)
        data = LuckyBoxRecordSerializer(items, many=True, exclude=(
            "percentage", "user", "origin_percent"
        )).data
        resp = {
            'data': data,
            "total": paginator.count
        }
        return reformat_resp(RespCode.Succeed, resp)



class GetLuckyHistory(APIView):
    permission_classes = [AllowAny]
    def get(self, request):
        page = request.query_params.get('page', 1)
        pageSize = request.query_params.get('pageSize', 10)
        user = current_user(request)
        records = LuckyBoxRecord.objects.filter(user=user).order_by("-create_time")
        paginator = Paginator(records, pageSize)
        items = paginator.page(page)
        data = LuckyBoxRecordSerializer(items, many=True, exclude=(
            "percentage", "user", "origin_percent"
        )).data
        resp = {
            'data': data,
            "total": paginator.count
        }
        return reformat_resp(RespCode.Succeed, resp)


class GetLuckyRule(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        message = "你会随机获得一件饰品，并有机会额外获得一件心仪的幸运饰品/每次幸运值为相互独立事件，例如，33%的幸运值开箱三次，这不代表有99%的机会。GoodLuck!"
        return reformat_resp(RespCode.Succeed, {}, message)

