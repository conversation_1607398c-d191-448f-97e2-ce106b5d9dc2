import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useAppStore = defineStore('app', () => {
  // 状态
  const isMobile = ref(false)
  const isDarkMode = ref(true) // 默认深色模式
  const isLoading = ref(false)
  const sidebarOpen = ref(false)

  // 计算属性
  const isDesktop = computed(() => !isMobile.value)

  // 方法
  const checkDevice = () => {
    if (process.client) {
      isMobile.value = window.innerWidth < 768
      // 监听窗口大小变化
      window.addEventListener('resize', () => {
        isMobile.value = window.innerWidth < 768
      })
    }
  }

  const toggleDarkMode = () => {
    isDarkMode.value = !isDarkMode.value
    if (process.client) {
      localStorage.setItem('darkMode', isDarkMode.value.toString())
    }
  }

  const setLoading = (loading: boolean) => {
    isLoading.value = loading
  }

  const toggleSidebar = () => {
    sidebarOpen.value = !sidebarOpen.value
  }

  const closeSidebar = () => {
    sidebarOpen.value = false
  }

  // 初始化
  const init = () => {
    if (process.client) {
      // 检查本地存储的主题设置
      const savedDarkMode = localStorage.getItem('darkMode')
      if (savedDarkMode !== null) {
        isDarkMode.value = savedDarkMode === 'true'
      }
      
      checkDevice()
    }
  }

  return {
    isMobile,
    isDarkMode,
    isLoading,
    sidebarOpen,
    isDesktop,
    checkDevice,
    toggleDarkMode,
    setLoading,
    toggleSidebar,
    closeSidebar,
    init
  }
})

// 向后兼容的别名
export const useStore = useAppStore 