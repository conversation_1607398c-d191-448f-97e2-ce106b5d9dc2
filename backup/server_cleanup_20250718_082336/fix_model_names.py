#!/usr/bin/env python3
"""
修复被破坏的模型名称
"""
import os
import re

def fix_model_names(file_path):
    """修复单个文件中的模型名称"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 修复常见的模型名称错误
        replacements = {
            'USER_MODEL': 'USER_MODEL',
            'models.CASCADE': 'models.CASCADE',
            'models.SET_NULL': 'models.SET_NULL',
            'SiteConfigCategory': 'SiteConfigCategoryy',
            'ArticleCategory': 'ArticleCategoryy',
            'Announce': 'Announcee',
        }
        
        for wrong, correct in replacements.items():
            content = content.replace(wrong, correct)
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 修复了 {file_path}")
            return True
        else:
            print(f"⏭️  跳过 {file_path} (无需修复)")
            return False
            
    except Exception as e:
        print(f"❌ 修复 {file_path} 时出错: {e}")
        return False

def main():
    """主函数"""
    print("🔧 开始修复模型名称错误...")
    
    # 查找所有Python文件
    model_files = []
    for root, dirs, files in os.walk('.'):
        # 跳过虚拟环境
        if 'venv' in root:
            continue
            
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                model_files.append(file_path)
    
    print(f"📁 找到 {len(model_files)} 个Python文件")
    
    fixed_count = 0
    for file_path in model_files:
        if fix_model_names(file_path):
            fixed_count += 1
    
    print(f"\n🎉 修复完成！共修复了 {fixed_count} 个文件")

if __name__ == '__main__':
    main()
