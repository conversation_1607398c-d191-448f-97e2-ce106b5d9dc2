import { ref, computed, onBeforeUnmount, nextTick } from 'vue'

export interface PerformanceMetrics {
  // 基础性能指标
  componentRenderTime: number
  watcherCount: number
  computedCount: number
  memoryUsage: number
  
  // 生命周期指标
  mountTime: number
  updateCount: number
  lastUpdateTime: number
  
  // 资源管理指标
  activeTimers: number
  eventListeners: number
  promisesInFlight: number
  
  // 业务指标
  apiCallCount: number
  websocketMessageCount: number
  stateUpdateCount: number
}

export interface PerformanceThresholds {
  maxWatcherCount: number
  maxRenderTime: number
  maxMemoryMB: number
  maxEventListeners: number
  maxApiCallsPerMinute: number
}

export interface PerformanceAlert {
  type: 'warning' | 'error' | 'info'
  metric: string
  current: number
  threshold: number
  message: string
  timestamp: number
}

/**
 * 性能监控和优化工具
 * 监控组件性能指标，检测内存泄漏，提供优化建议
 */
export function usePerformanceMonitor(componentName: string = 'Unknown') {
  // 🎯 性能指标
  const metrics = ref<PerformanceMetrics>({
    componentRenderTime: 0,
    watcherCount: 0,
    computedCount: 0,
    memoryUsage: 0,
    mountTime: 0,
    updateCount: 0,
    lastUpdateTime: 0,
    activeTimers: 0,
    eventListeners: 0,
    promisesInFlight: 0,
    apiCallCount: 0,
    websocketMessageCount: 0,
    stateUpdateCount: 0
  })

  // 🎯 性能阈值
  const thresholds: PerformanceThresholds = {
    maxWatcherCount: 20,
    maxRenderTime: 16, // 60fps = 16ms per frame
    maxMemoryMB: 50,
    maxEventListeners: 10,
    maxApiCallsPerMinute: 60
  }

  // 🎯 性能警告
  const alerts = ref<PerformanceAlert[]>([])

  // 🎯 监控状态
  const isMonitoring = ref(false)
  let monitoringInterval: NodeJS.Timeout | null = null
  let renderStartTime = 0

  // 🎯 资源跟踪
  const trackedTimers = ref(new Set<NodeJS.Timeout>())
  const trackedListeners = ref(new Map<string, EventListener[]>())
  const trackedPromises = ref(new Set<Promise<any>>())

  /**
   * 开始性能监控
   */
  const startMonitoring = () => {
    if (isMonitoring.value) return

    isMonitoring.value = true
    metrics.value.mountTime = performance.now()

    // 🎯 定期收集性能指标
    monitoringInterval = setInterval(() => {
      collectMetrics()
      checkThresholds()
      cleanupStaleData()
    }, 1000) // 每秒检查一次

    console.log(`[PERF_MONITOR] ${componentName} 性能监控已启动`)
  }

  /**
   * 停止性能监控
   */
  const stopMonitoring = () => {
    if (!isMonitoring.value) return

    isMonitoring.value = false
    
    if (monitoringInterval) {
      clearInterval(monitoringInterval)
      monitoringInterval = null
    }

    // 🎯 清理跟踪的资源
    trackedTimers.value.forEach(timer => clearTimeout(timer))
    trackedTimers.value.clear()
    trackedPromises.value.clear()

    console.log(`[PERF_MONITOR] ${componentName} 性能监控已停止`)
  }

  /**
   * 收集性能指标
   */
  const collectMetrics = () => {
    try {
      // 🎯 内存使用情况
      if ('memory' in performance) {
        const memInfo = (performance as any).memory
        metrics.value.memoryUsage = memInfo.usedJSHeapSize / 1024 / 1024 // MB
      }

      // 🎯 更新计数
      metrics.value.updateCount++
      metrics.value.lastUpdateTime = performance.now()

      // 🎯 资源计数
      metrics.value.activeTimers = trackedTimers.value.size
      metrics.value.eventListeners = Array.from(trackedListeners.value.values())
        .reduce((total, listeners) => total + listeners.length, 0)
      metrics.value.promisesInFlight = trackedPromises.value.size

    } catch (error) {
      console.warn('[PERF_MONITOR] 收集指标失败:', error)
    }
  }

  /**
   * 检查性能阈值
   */
  const checkThresholds = () => {
    const checks = [
      {
        metric: 'watcherCount',
        current: metrics.value.watcherCount,
        threshold: thresholds.maxWatcherCount,
        message: '监听器数量过多，可能影响性能'
      },
      {
        metric: 'renderTime',
        current: metrics.value.componentRenderTime,
        threshold: thresholds.maxRenderTime,
        message: '组件渲染时间过长，可能导致卡顿'
      },
      {
        metric: 'memoryUsage',
        current: metrics.value.memoryUsage,
        threshold: thresholds.maxMemoryMB,
        message: '内存使用量过高，可能存在内存泄漏'
      },
      {
        metric: 'eventListeners',
        current: metrics.value.eventListeners,
        threshold: thresholds.maxEventListeners,
        message: '事件监听器数量过多，可能存在内存泄漏'
      }
    ]

    checks.forEach(check => {
      if (check.current > check.threshold) {
        addAlert('warning', check.metric, check.current, check.threshold, check.message)
      }
    })
  }

  /**
   * 添加性能警告
   */
  const addAlert = (
    type: PerformanceAlert['type'],
    metric: string,
    current: number,
    threshold: number,
    message: string
  ) => {
    const alert: PerformanceAlert = {
      type,
      metric,
      current,
      threshold,
      message,
      timestamp: Date.now()
    }

    alerts.value.push(alert)

    // 🎯 限制警告数量
    if (alerts.value.length > 50) {
      alerts.value = alerts.value.slice(-30)
    }

    console.warn(`[PERF_MONITOR] ${componentName} 性能警告:`, alert)
  }

  /**
   * 清理过期数据
   */
  const cleanupStaleData = () => {
    const now = Date.now()
    const maxAge = 5 * 60 * 1000 // 5分钟

    // 🎯 清理过期警告
    alerts.value = alerts.value.filter(alert => now - alert.timestamp < maxAge)

    // 🎯 清理已完成的Promise
    for (const promise of trackedPromises.value) {
      Promise.resolve(promise).then(() => {
        trackedPromises.value.delete(promise)
      }).catch(() => {
        trackedPromises.value.delete(promise)
      })
    }
  }

  /**
   * 测量渲染时间
   */
  const measureRender = {
    start: () => {
      renderStartTime = performance.now()
    },
    end: () => {
      if (renderStartTime > 0) {
        const renderTime = performance.now() - renderStartTime
        metrics.value.componentRenderTime = renderTime
        renderStartTime = 0

        if (renderTime > thresholds.maxRenderTime) {
          addAlert('warning', 'renderTime', renderTime, thresholds.maxRenderTime, 
            `渲染时间 ${renderTime.toFixed(2)}ms 超过阈值`)
        }
      }
    }
  }

  /**
   * 跟踪定时器
   */
  const trackTimer = (timer: NodeJS.Timeout): NodeJS.Timeout => {
    trackedTimers.value.add(timer)
    return timer
  }

  /**
   * 清除跟踪的定时器
   */
  const clearTrackedTimer = (timer: NodeJS.Timeout): void => {
    clearTimeout(timer)
    trackedTimers.value.delete(timer)
  }

  /**
   * 跟踪事件监听器
   */
  const trackEventListener = (element: string, event: string, listener: EventListener): void => {
    const key = `${element}_${event}`
    if (!trackedListeners.value.has(key)) {
      trackedListeners.value.set(key, [])
    }
    trackedListeners.value.get(key)!.push(listener)
  }

  /**
   * 移除跟踪的事件监听器
   */
  const untrackEventListener = (element: string, event: string, listener: EventListener): void => {
    const key = `${element}_${event}`
    const listeners = trackedListeners.value.get(key)
    if (listeners) {
      const index = listeners.indexOf(listener)
      if (index > -1) {
        listeners.splice(index, 1)
        if (listeners.length === 0) {
          trackedListeners.value.delete(key)
        }
      }
    }
  }

  /**
   * 跟踪Promise
   */
  const trackPromise = <T>(promise: Promise<T>): Promise<T> => {
    trackedPromises.value.add(promise)
    
    const cleanup = () => {
      trackedPromises.value.delete(promise)
    }

    promise.then(cleanup, cleanup)
    return promise
  }

  /**
   * 记录API调用
   */
  const recordApiCall = (endpoint: string, duration?: number): void => {
    metrics.value.apiCallCount++
    
    if (duration && duration > 1000) { // 超过1秒的API调用
      addAlert('warning', 'apiResponse', duration, 1000, 
        `API调用 ${endpoint} 耗时 ${duration}ms`)
    }
  }

  /**
   * 记录WebSocket消息
   */
  const recordWebSocketMessage = (type: string, size?: number): void => {
    metrics.value.websocketMessageCount++
    
    if (size && size > 1024 * 10) { // 超过10KB的消息
      addAlert('info', 'websocketMessage', size, 1024 * 10, 
        `WebSocket消息 ${type} 大小 ${(size / 1024).toFixed(2)}KB`)
    }
  }

  /**
   * 记录状态更新
   */
  const recordStateUpdate = (component: string, updateCount: number): void => {
    metrics.value.stateUpdateCount++
    
    if (updateCount > 10) { // 单次更新超过10个状态
      addAlert('warning', 'stateUpdate', updateCount, 10, 
        `组件 ${component} 单次更新 ${updateCount} 个状态`)
    }
  }

  /**
   * 获取性能报告
   */
  const getPerformanceReport = () => {
    const uptime = performance.now() - metrics.value.mountTime
    
    return {
      componentName,
      uptime,
      metrics: { ...metrics.value },
      alerts: [...alerts.value],
      health: {
        overall: getOverallHealth(),
        memory: metrics.value.memoryUsage < thresholds.maxMemoryMB ? 'good' : 'warning',
        performance: metrics.value.componentRenderTime < thresholds.maxRenderTime ? 'good' : 'warning',
        resources: metrics.value.eventListeners < thresholds.maxEventListeners ? 'good' : 'warning'
      },
      recommendations: getRecommendations()
    }
  }

  /**
   * 获取整体健康状态
   */
  const getOverallHealth = (): 'good' | 'warning' | 'critical' => {
    const warningCount = alerts.value.filter(a => a.type === 'warning').length
    const errorCount = alerts.value.filter(a => a.type === 'error').length
    
    if (errorCount > 0) return 'critical'
    if (warningCount > 3) return 'warning'
    return 'good'
  }

  /**
   * 获取优化建议
   */
  const getRecommendations = (): string[] => {
    const recommendations: string[] = []
    
    if (metrics.value.watcherCount > thresholds.maxWatcherCount) {
      recommendations.push('减少watch监听器数量，考虑合并或使用computed属性')
    }
    
    if (metrics.value.eventListeners > thresholds.maxEventListeners) {
      recommendations.push('检查事件监听器是否正确清理，避免内存泄漏')
    }
    
    if (metrics.value.memoryUsage > thresholds.maxMemoryMB) {
      recommendations.push('检查是否存在内存泄漏，清理不必要的数据缓存')
    }
    
    if (metrics.value.componentRenderTime > thresholds.maxRenderTime) {
      recommendations.push('优化组件渲染性能，减少复杂计算和DOM操作')
    }
    
    return recommendations
  }

  // 🎯 计算属性
  const isHealthy = computed(() => getOverallHealth() === 'good')
  const hasWarnings = computed(() => alerts.value.some(a => a.type === 'warning'))
  const hasErrors = computed(() => alerts.value.some(a => a.type === 'error'))

  // 🎯 自动清理
  onBeforeUnmount(() => {
    stopMonitoring()
  })

  return {
    // 核心功能
    startMonitoring,
    stopMonitoring,
    
    // 测量工具
    measureRender,
    trackTimer,
    clearTrackedTimer,
    trackEventListener,
    untrackEventListener,
    trackPromise,
    
    // 记录工具
    recordApiCall,
    recordWebSocketMessage,
    recordStateUpdate,
    
    // 状态访问
    metrics: computed(() => metrics.value),
    alerts: computed(() => alerts.value),
    isMonitoring,
    
    // 健康状态
    isHealthy,
    hasWarnings,
    hasErrors,
    
    // 报告和建议
    getPerformanceReport,
    getRecommendations,
    
    // 手动控制
    addAlert,
    collectMetrics
  }
} 