# 🎨 CSGO皮肤交易平台 - 前端应用

> 基于 **Vue 3 + Nuxt 3 + TypeScript** 构建的现代化CSGO皮肤交易平台前端应用，集成实时WebSocket通信、炫酷动画效果和完整的用户交互体验。

[![Vue.js](https://img.shields.io/badge/Vue.js-3.5+-brightgreen.svg)](https://vuejs.org)
[![Nuxt.js](https://img.shields.io/badge/Nuxt.js-3.16+-00C58E.svg)](https://nuxt.com)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0+-blue.svg)](https://typescriptlang.org)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind%20CSS-3.3+-38B2AC.svg)](https://tailwindcss.com)

## 🌟 项目特色

- 🎯 **现代化架构** - Vue 3 Composition API + Nuxt 3 SSR/SSG
- ⚡ **极致性能** - Vite构建 + 代码分割 + 懒加载
- 🎨 **炫酷动画** - GSAP + Three.js + CSS动画
- 📱 **响应式设计** - 完美适配桌面端和移动端
- 🌐 **实时通信** - WebSocket集成，支持实时开箱和对战
- 🔄 **状态管理** - Pinia状态管理，组件化开发
- 🌍 **国际化** - 完整的多语言支持系统
- 🎮 **游戏体验** - 沉浸式的开箱和对战体验
- 🔐 **安全认证** - JWT + Steam OAuth集成
- 📊 **数据可视化** - ECharts图表展示

## 🚀 快速开始

### 📋 环境要求

- Node.js 16+
- npm/pnpm/yarn
- 现代浏览器 (Chrome 90+, Firefox 88+, Safari 14+)

### 🛠️ 安装依赖

```bash
# 使用 npm
npm install

# 使用 pnpm (推荐)
pnpm install

# 使用 yarn
yarn install

# 使用 bun
bun install
```

### 💻 开发服务器

启动开发服务器，访问 `http://localhost:3000`:

```bash
# npm
npm run dev

# pnpm
pnpm dev

# yarn
yarn dev

# bun
bun run dev
```

### 🏗️ 生产构建

构建生产版本:

```bash
# npm
npm run build

# pnpm
pnpm build

# yarn
yarn build

# bun
bun run build
```

### 👀 预览生产版本

本地预览生产构建:

```bash
# npm
npm run preview

# pnpm
pnpm preview

# yarn
yarn preview

# bun
bun run preview
```

## 📦 项目结构

```
ui/
├── 📂 components/                 # Vue组件
│   ├── 📂 auth/                   # 认证相关组件
│   ├── 📂 battle/                 # 对战系统组件
│   ├── 📂 case/                   # 开箱系统组件
│   ├── 📂 common/                 # 通用组件
│   ├── 📂 demo/                   # 演示组件
│   ├── 📂 home/                   # 首页组件
│   ├── 📂 skin/                   # 皮肤相关组件
│   └── 📂 ui/                     # UI基础组件
├── 📂 pages/                      # 页面路由
│   ├── 📂 auth/                   # 认证页面
│   ├── 📂 battle/                 # 对战页面
│   ├── 📂 cases/                  # 开箱页面
│   ├── 📂 profile/                # 用户中心
│   ├── 📂 skins/                  # 皮肤页面
│   └── 📄 index.vue               # 首页
├── 📂 stores/                     # Pinia状态管理
│   ├── 📄 app.ts                  # 应用状态
│   ├── 📄 battle.ts               # 对战状态
│   ├── 📄 case.ts                 # 开箱状态
│   ├── 📄 user.ts                 # 用户状态
│   └── 📄 socket.ts               # WebSocket状态
├── 📂 composables/                # 组合式函数
│   ├── 📄 useAuth.ts              # 认证逻辑
│   ├── 📄 useBattle*.ts           # 对战相关逻辑
│   ├── 📄 useCase*.ts             # 开箱相关逻辑
│   └── 📄 useWebSocket*.ts        # WebSocket逻辑
├── 📂 services/                   # API服务
│   ├── 📄 auth-api.ts             # 认证API
│   ├── 📄 battle-api.ts           # 对战API
│   ├── 📄 case-api.ts             # 开箱API
│   └── 📄 common-api.ts           # 通用API
├── 📂 types/                      # TypeScript类型定义
│   ├── 📄 battle.ts               # 对战类型
│   ├── 📄 case.ts                 # 开箱类型
│   └── 📄 skin.ts                 # 皮肤类型
├── 📂 utils/                      # 工具函数
│   ├── 📄 api-client.ts           # API客户端
│   ├── 📄 validation.ts           # 表单验证
│   └── 📄 date.ts                 # 日期处理
├── 📂 assets/                     # 静态资源
│   └── 📂 css/                    # 样式文件
├── 📂 public/                     # 公共文件
│   ├── 📂 images/                 # 图片资源
│   ├── 📂 audio/                  # 音频文件
│   └── 📂 fonts/                  # 字体文件
├── 📂 layouts/                    # 布局组件
│   ├── 📄 default.vue             # 默认布局
│   └── 📄 mobile.vue              # 移动端布局
├── 📂 middleware/                 # 中间件
│   ├── 📄 auth.ts                 # 认证中间件
│   └── 📄 guest.ts                # 游客中间件
├── 📂 plugins/                    # 插件
│   ├── 📄 app-init.client.ts      # 应用初始化
│   ├── 📄 socket.client.ts        # WebSocket插件
│   └── 📄 gsap.client.ts          # GSAP动画插件
├── 📂 locales/                    # 国际化文件
│   ├── 📄 en.json                 # 英文
│   └── 📄 zh-hans.json            # 简体中文
├── 📄 nuxt.config.ts              # Nuxt配置
├── 📄 tailwind.config.js          # Tailwind配置
├── 📄 tsconfig.json               # TypeScript配置
├── 📄 package.json                # 依赖配置
└── 📄 README.md                   # 项目说明
```

## 🔧 核心功能

### 🎮 开箱系统
- **实时开箱动画** - GSAP + CSS3动画
- **多种箱子类型** - 普通箱、盲盒、自定义箱
- **概率展示** - 透明的掉落概率
- **历史记录** - 开箱历史和统计

### ⚔️ 对战系统
- **实时对战** - WebSocket实时通信
- **动画同步** - 多人同步开箱动画
- **战绩统计** - 详细的对战数据
- **房间管理** - 创建和加入对战房间

### 🛒 交易市场
- **皮肤展示** - 3D预览和详细信息
- **价格趋势** - 历史价格图表
- **快速交易** - 一键买卖功能
- **筛选搜索** - 多维度筛选

### 👤 用户系统
- **Steam登录** - OAuth集成
- **个人中心** - 资产管理和设置
- **充值提现** - 多种支付方式
- **等级系统** - 用户等级和特权

## 🎨 技术特色

### 🚀 性能优化
- **代码分割** - 路由级别的懒加载
- **图片优化** - @nuxt/image自动优化
- **缓存策略** - 智能缓存管理
- **预加载** - 关键资源预加载

### 🎭 动画效果
- **GSAP动画** - 高性能动画库
- **Three.js 3D** - 3D皮肤预览
- **CSS动画** - 流畅的过渡效果
- **粒子效果** - 开箱特效

### 📱 响应式设计
- **移动优先** - Mobile-first设计理念
- **断点适配** - 多设备完美适配
- **触摸优化** - 移动端手势支持
- **PWA支持** - 渐进式Web应用

### 🌍 国际化
- **多语言** - 中文/英文切换
- **本地化** - 时间、货币格式化
- **RTL支持** - 右到左语言支持
- **动态加载** - 按需加载语言包

## 🔌 API集成

### 🌐 后端API
```typescript
// API服务示例
import { apiClient } from '~/utils/api-client'

// 获取用户信息
const user = await apiClient.get('/api/auth/user/')

// 开箱操作
const result = await apiClient.post('/api/cases/open/', {
  case_id: 123
})

// 对战加入
const battle = await apiClient.post('/api/battle/join/', {
  room_id: 456
})
```

### 🔄 WebSocket通信
```typescript
// WebSocket使用示例
const { $socket } = useNuxtApp()

// 监听开箱结果
$socket.on('case_opened', (data) => {
  // 处理开箱结果
})

// 发送对战消息
$socket.emit('battle_action', {
  action: 'open_case',
  case_id: 123
})
```

## 🛠️ 开发指南

### 📝 代码规范
- **ESLint** - 代码质量检查
- **Prettier** - 代码格式化
- **TypeScript** - 类型安全
- **Conventional Commits** - 提交信息规范

### 🧪 测试
```bash
# 运行测试
npm run test

# 测试覆盖率
npm run test:coverage

# E2E测试
npm run test:e2e
```

### 🔍 调试
```bash
# 开发模式调试
npm run dev

# 生产模式调试
npm run build && npm run preview

# 分析构建包
npm run analyze
```

## 📚 相关文档

- [🎯 快速开始指南](../docs/frontend/quickstart.md)
- [🏗️ 架构设计](../docs/frontend/architecture.md)
- [🎨 组件库](../docs/frontend/components.md)
- [🔌 API集成](../docs/frontend/api-integration.md)
- [🎭 动画系统](../docs/frontend/animations.md)
- [📱 响应式设计](../docs/frontend/responsive.md)
- [🌍 国际化](../docs/frontend/i18n.md)
- [🚀 部署指南](../docs/frontend/deployment.md)

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'feat: add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](../LICENSE) 文件了解详情。

---

⭐ 如果这个项目对你有帮助，请给我们一个星标！