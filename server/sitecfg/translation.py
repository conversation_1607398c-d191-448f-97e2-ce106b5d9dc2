from modeltranslation.translator import register, translator, TranslationOptions

from sitecfg.models import Announce<PERSON>, FAQ, Support, Banner, SEO, AccessControl, ArticleCategoryy


@register(Announcee)
class AnnounceeTranslationOptions(TranslationOptions):
    fields = ('content', 'title')

@register(FAQ)
class FAQTranslationOptions(TranslationOptions):
    fields = ('content', 'title',)

@register(Support)
class SupportTranslationOptions(TranslationOptions):
    fields = ('content', 'title', )

@register(Banner)
class BannerTranslationOptions(TranslationOptions):
    fields = ('title', 'description', 'primary_button_text', 'secondary_button_text',)

@register(SEO)
class SEOTranslationOptions(TranslationOptions):
    fields = ('name', 'title', 'subtitle', 'keywords', 'description',)

@register(AccessControl)
class AccessControlTranslationOptions(TranslationOptions):
    fields = ('message', )

@register(ArticleCategoryy)
class ArticleCategoryyTranslationOptions(TranslationOptions):
    fields = ('name', )