#!/usr/bin/env python3
"""
修复admin文件中的语法错误
"""
import os
import re

def fix_syntax_errors(file_path):
    """修复单个文件中的语法错误"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 修复各种语法错误
        # 1. 修复 "list_filter = ('field',)  # DateRangeFilter暂时禁用"
        content = re.sub(r"list_filter = ()  # DateRangeFilter暂时禁用, '([^']+)'\)", r"list_filter = ('\1',)  # DateRangeFilter暂时禁用", content)
        
        # 2. 修复 "list_filter = ()  # DateRangeFilter暂时禁用"
        content = re.sub(r"list_filter = \(暂时禁用\)", r"list_filter = ()  # DateRangeFilter暂时禁用", content)
        
        # 3. 修复其他可能的语法错误
        content = re.sub(r"list_filter = ()  # DateRangeFilter暂时禁用", r"list_filter = ()  # DateRangeFilter暂时禁用", content)
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 修复了 {file_path}")
            return True
        else:
            print(f"⏭️  跳过 {file_path} (无需修复)")
            return False
            
    except Exception as e:
        print(f"❌ 修复 {file_path} 时出错: {e}")
        return False

def main():
    """主函数"""
    print("🔧 开始修复语法错误...")
    
    # 查找所有包含"暂时禁用"的Python文件
    files_to_fix = []
    for root, dirs, files in os.walk('.'):
        # 跳过虚拟环境
        if 'venv' in root:
            continue
            
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if '暂时禁用' in content:
                            files_to_fix.append(file_path)
                except:
                    continue
    
    print(f"📁 找到 {len(files_to_fix)} 个需要修复的文件")
    
    fixed_count = 0
    for file_path in files_to_fix:
        if fix_syntax_errors(file_path):
            fixed_count += 1
    
    print(f"\n🎉 修复完成！共修复了 {fixed_count} 个文件")

if __name__ == '__main__':
    main()
