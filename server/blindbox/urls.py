from django.urls import re_path as url

from blindbox import views



app_name = 'blindbox'
urlpatterns = [
    url(r'^open/', views.BlindBoxOpenView.as_view()),
    url(r'^replace/', views.ReplaceBlindBoxView.as_view()),
    url(r'^list/', views.GetBlindBoxListView.as_view()),
    url(r'^free/', views.GetFreeBlindBoxListView.as_view()),
    url(r'^detail/', views.GetBlindBoxDetailView.as_view()),
    url(r'^current/', views.GetBlindBoxCurrentGameView.as_view()),
    # url(r'^record/', views.GetBlindBoxRecordView.as_view()),
    # url(r'^recent/', views.GetBlindBoxRecentRecordView.as_view()),

]
