#!/usr/bin/env node

/**
 * UI端WebSocket功能测试脚本
 * 测试新的WebSocket消息格式和房间管理功能
 */

const io = require('socket.io-client');

// 配置
const CONFIG = {
  serverUrl: 'http://localhost:8080',
  testDuration: 30000, // 30秒
  debug: true
};

class WebSocketTester {
  constructor() {
    this.socket = null;
    this.testResults = {
      connection: false,
      monitor: false,
      caseRecords: false,
      battle: false,
      messageCount: 0,
      errors: []
    };
  }

  async connect() {
    console.log('🔌 连接到WebSocket服务器:', CONFIG.serverUrl);
    
    this.socket = io(CONFIG.serverUrl, {
      transports: ['websocket'],
      timeout: 5000
    });

    return new Promise((resolve, reject) => {
      this.socket.on('connect', () => {
        console.log('✅ WebSocket连接成功, ID:', this.socket.id);
        this.testResults.connection = true;
        resolve();
      });

      this.socket.on('connect_error', (error) => {
        console.error('❌ WebSocket连接失败:', error.message);
        this.testResults.errors.push(`连接失败: ${error.message}`);
        reject(error);
      });

      this.socket.on('disconnect', (reason) => {
        console.log('🔴 WebSocket连接断开:', reason);
      });
    });
  }

  setupMessageListeners() {
    console.log('👂 设置消息监听器...');

    // 监听所有消息
    this.socket.onAny((event, ...args) => {
      this.testResults.messageCount++;
      if (CONFIG.debug) {
        console.log(`📨 收到事件: ${event}`, args);
      }
    });

    // 监听monitor消息
    this.socket.on('monitor', (data) => {
      console.log('📊 收到monitor消息:', data);
      this.testResults.monitor = true;
    });

    // 监听case_records消息
    this.socket.on('case_records', (data) => {
      console.log('📦 收到case_records消息:', data);
      this.testResults.caseRecords = true;
    });

    // 监听boxroom消息
    this.socket.on('boxroom', (data) => {
      console.log('🎰 收到boxroom消息:', data);
      this.testResults.battle = true;
    });

    // 监听boxroomdetail消息
    this.socket.on('boxroomdetail', (data) => {
      console.log('🎯 收到boxroomdetail消息:', data);
      this.testResults.battle = true;
    });

    // 监听通用message事件
    this.socket.on('message', (data) => {
      console.log('💬 收到message消息:', data);
      
      try {
        let parsedData = data;
        if (typeof data === 'string') {
          parsedData = JSON.parse(data);
        }
        
        if (Array.isArray(parsedData)) {
          const [messageType, action, payload] = parsedData;
          console.log(`📋 解析消息: type=${messageType}, action=${action}`);
          
          switch (messageType) {
            case 'monitor':
              this.testResults.monitor = true;
              break;
            case 'case_records':
              this.testResults.caseRecords = true;
              break;
            case 'boxroom':
            case 'boxroomdetail':
              this.testResults.battle = true;
              break;
          }
        }
      } catch (error) {
        console.error('❌ 解析消息失败:', error);
        this.testResults.errors.push(`消息解析失败: ${error.message}`);
      }
    });
  }

  async testMonitorRoom() {
    console.log('\n🧪 测试监控房间功能...');
    
    // 加入监控房间
    this.socket.emit('monitor', ['join', 'monitor']);
    console.log('📤 发送: monitor join');
    
    await this.sleep(1000);
    
    // 请求统计数据
    this.socket.emit('monitor', ['get_stats']);
    console.log('📤 发送: monitor get_stats');
    
    await this.sleep(2000);
  }

  async testCaseRecordsRoom() {
    console.log('\n🧪 测试开箱记录房间功能...');
    
    // 请求开箱记录
    this.socket.emit('monitor', ['case_records']);
    console.log('📤 发送: monitor case_records');
    
    await this.sleep(2000);
  }

  async testBattleRoom() {
    console.log('\n🧪 测试对战房间功能...');
    
    // 加入对战房间
    this.socket.emit('join', 'boxroom');
    console.log('📤 发送: join boxroom');
    
    await this.sleep(1000);
    
    // 加入特定对战房间
    this.socket.emit('join', 'test_battle_123');
    console.log('📤 发送: join test_battle_123');
    
    await this.sleep(2000);
  }

  async testLegacyFormat() {
    console.log('\n🧪 测试兼容性格式...');
    
    // 测试旧格式
    this.socket.emit('join', 'ws_channel');
    this.socket.emit('monitor', 'get_stats');
    this.socket.emit('case_records');
    
    console.log('📤 发送兼容性格式消息');
    
    await this.sleep(2000);
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async runTests() {
    try {
      await this.connect();
      this.setupMessageListeners();
      
      console.log('\n🚀 开始WebSocket功能测试...');
      
      await this.testMonitorRoom();
      await this.testCaseRecordsRoom();
      await this.testBattleRoom();
      await this.testLegacyFormat();
      
      console.log('\n⏳ 等待更多消息...');
      await this.sleep(5000);
      
      this.printResults();
      
    } catch (error) {
      console.error('❌ 测试失败:', error);
      this.testResults.errors.push(`测试失败: ${error.message}`);
    } finally {
      if (this.socket) {
        this.socket.disconnect();
      }
    }
  }

  printResults() {
    console.log('\n' + '='.repeat(50));
    console.log('📊 WebSocket测试结果');
    console.log('='.repeat(50));
    
    console.log(`✅ 连接状态: ${this.testResults.connection ? '成功' : '失败'}`);
    console.log(`📊 监控功能: ${this.testResults.monitor ? '正常' : '异常'}`);
    console.log(`📦 开箱记录: ${this.testResults.caseRecords ? '正常' : '异常'}`);
    console.log(`🎰 对战功能: ${this.testResults.battle ? '正常' : '异常'}`);
    console.log(`📨 消息总数: ${this.testResults.messageCount}`);
    
    if (this.testResults.errors.length > 0) {
      console.log('\n❌ 错误列表:');
      this.testResults.errors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error}`);
      });
    }
    
    const successCount = [
      this.testResults.connection,
      this.testResults.monitor,
      this.testResults.caseRecords,
      this.testResults.battle
    ].filter(Boolean).length;
    
    const successRate = (successCount / 4 * 100).toFixed(1);
    console.log(`\n🎯 成功率: ${successRate}% (${successCount}/4)`);
    
    if (successRate >= 75) {
      console.log('🎉 WebSocket功能测试通过！');
    } else {
      console.log('⚠️ WebSocket功能存在问题，需要检查');
    }
  }
}

// 运行测试
const tester = new WebSocketTester();
tester.runTests().then(() => {
  console.log('\n✅ 测试完成');
  process.exit(0);
}).catch((error) => {
  console.error('\n❌ 测试异常:', error);
  process.exit(1);
});
