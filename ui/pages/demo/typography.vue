<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-900 to-slate-800 relative">
    <!-- 简化的背景 -->
    <div class="fixed inset-0 -z-10">
      <div class="absolute inset-0 bg-grid opacity-5"></div>
    </div>

    <div class="container mx-auto px-4 py-8">
      <!-- 导航栏 -->
      <div class="flex justify-between items-center mb-8">
        <NuxtLink 
          to="/demo" 
          class="inline-flex items-center gap-2 px-4 py-2 rounded-lg bg-gray-700/50 hover:bg-gray-600/50 text-gray-300 hover:text-white transition-all duration-300 backdrop-blur-sm border border-gray-600/30"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
          返回演示中心
        </NuxtLink>
        
        <div class="flex items-center gap-4">
          <NuxtLink 
            to="/" 
            class="inline-flex items-center gap-2 px-4 py-2 rounded-lg bg-blue-600/20 hover:bg-blue-600/30 text-blue-300 hover:text-blue-200 transition-all duration-300 backdrop-blur-sm border border-blue-500/30"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
            回到首页
          </NuxtLink>
        </div>
      </div>

      <div class="text-center mb-12">
        <h1 class="text-display font-trivial text-gradient mb-4">
          CSGOSKINS 字体系统
        </h1>
        <p class="text-subtitle text-gray-300 font-i18n">完整的字体库展示与使用指南</p>
      </div>
      
      <!-- 国际化字体系统 -->
      <section class="mb-16">
        <h2 class="text-title text-gradient-primary mb-8 title-i18n">国际化字体系统</h2>
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <!-- 中文字体展示 -->
          <div class="glass-effect p-8 rounded-lg">
            <h3 class="text-lg mb-6 text-blue-400 title-i18n">简体中文 (Simplified Chinese)</h3>
            <div class="space-y-4">
              <div class="font-sample">
                <span class="label">Display:</span>
                <div class="text-display font-i18n">CSGOSKINS</div>
              </div>
              <div class="font-sample">
                <span class="label">Hero:</span>
                <div class="text-hero font-i18n">高级开箱平台</div>
              </div>
              <div class="font-sample">
                <span class="label">Title:</span>
                <div class="text-title font-i18n">探索稀有皮肤</div>
              </div>
              <div class="font-sample">
                <span class="label">Subtitle:</span>
                <div class="text-subtitle font-i18n">体验极致开箱乐趣</div>
              </div>
              <div class="font-sample">
                <span class="label">Body:</span>
                <div class="text-body font-i18n">阿里巴巴普惠体提供优雅的中文阅读体验，字形设计清晰易读，支持多种字重变化。</div>
              </div>
              <div class="font-sample">
                <span class="label">Caption:</span>
                <div class="text-caption font-i18n">支持多种字重和样式变化</div>
              </div>
            </div>
          </div>
          
          <!-- 英文字体展示 -->
          <div class="glass-effect p-8 rounded-lg" lang="en">
            <h3 class="text-lg mb-6 text-green-400 title-i18n">English (International)</h3>
            <div class="space-y-4">
              <div class="font-sample">
                <span class="label">Display:</span>
                <div class="text-display font-i18n">CSGOSKINS</div>
              </div>
              <div class="font-sample">
                <span class="label">Hero:</span>
                <div class="text-hero font-i18n">Premium Case Opening</div>
              </div>
              <div class="font-sample">
                <span class="label">Title:</span>
                <div class="text-title font-i18n">Discover Rare Skins</div>
              </div>
              <div class="font-sample">
                <span class="label">Subtitle:</span>
                <div class="text-subtitle font-i18n">Ultimate Gaming Experience</div>
              </div>
              <div class="font-sample">
                <span class="label">Body:</span>
                <div class="text-body font-i18n">Modern typography designed for gaming platform with excellent readability and clean aesthetic.</div>
              </div>
              <div class="font-sample">
                <span class="label">Caption:</span>
                <div class="text-caption font-i18n">Optimized for multilingual support</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 专用字体族展示 -->
      <section class="mb-16">
        <h2 class="text-title text-gradient-primary mb-8 title-i18n">专用英文字体族</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <!-- Arame 字体 -->
          <div class="glass-effect p-8 rounded-lg">
            <h3 class="text-lg mb-6 text-blue-400">Arame Font Family</h3>
            <div class="space-y-4">
              <div class="font-sample">
                <span class="label">Title Style:</span>
                <div class="title-arame text-2xl">CSGOSKINS</div>
              </div>
              <div class="font-sample">
                <span class="label">Regular:</span>
                <div class="font-arame text-lg">Premium Platform</div>
              </div>
              <div class="font-sample">
                <span class="label">Body Text:</span>
                <div class="font-arame text-base">Clean & Modern Design</div>
              </div>
              <div class="font-sample">
                <span class="label">Usage:</span>
                <div class="text-caption text-gray-400">适用于界面文字、按钮标签</div>
              </div>
            </div>
          </div>
          
          <!-- Paracaps 字体 -->
          <div class="glass-effect p-8 rounded-lg">
            <h3 class="text-lg mb-6 text-purple-400">Paracaps Font Family</h3>
            <div class="space-y-4">
              <div class="font-sample">
                <span class="label">Title Style:</span>
                <div class="title-paracaps text-2xl">CSGOSKINS</div>
              </div>
              <div class="font-sample">
                <span class="label">Regular:</span>
                <div class="font-paracaps text-lg">BOLD DESIGN</div>
              </div>
              <div class="font-sample">
                <span class="label">Body Text:</span>
                <div class="font-paracaps text-base">GAMING STYLE</div>
              </div>
              <div class="font-sample">
                <span class="label">Usage:</span>
                <div class="text-caption text-gray-400">适用于标题、强调文本</div>
              </div>
            </div>
          </div>
          
          <!-- Trivial 字体 -->
          <div class="glass-effect p-8 rounded-lg">
            <h3 class="text-lg mb-6 text-orange-400">Trivial Font Family</h3>
            <div class="space-y-4">
              <div class="font-sample">
                <span class="label">Title Style:</span>
                <div class="title-trivial text-2xl">CSGOSKINS</div>
              </div>
              <div class="font-sample">
                <span class="label">Regular:</span>
                <div class="font-trivial text-lg">ESPORTS</div>
              </div>
              <div class="font-sample">
                <span class="label">Body Text:</span>
                <div class="font-trivial text-base">PREMIUM</div>
              </div>
              <div class="font-sample">
                <span class="label">Usage:</span>
                <div class="text-caption text-gray-400">适用于Logo、主标题</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 阿里巴巴普惠体变体 -->
      <section class="mb-16">
        <h2 class="text-title text-gradient-primary mb-8 title-i18n">阿里巴巴普惠体 (Alibaba PuHuiTi)</h2>
        <div class="glass-effect p-8 rounded-lg">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <h4 class="text-lg mb-4 text-gray-300">Thin (细体)</h4>
              <div class="space-y-3">
                <div class="font-sample">
                  <div class="font-alibaba-thin text-2xl">CSGOSKINS</div>
                </div>
                <div class="font-sample">
                  <div class="font-alibaba-thin text-lg">高级开箱平台</div>
                </div>
                <div class="font-sample">
                  <div class="font-alibaba-thin text-base">优雅轻盈的设计风格</div>
                </div>
                <div class="text-caption text-gray-400">适用于副标题、说明文字</div>
              </div>
            </div>
            <div>
              <h4 class="text-lg mb-4 text-gray-300">Regular (常规)</h4>
              <div class="space-y-3">
                <div class="font-sample">
                  <div class="font-alibaba text-2xl">CSGOSKINS</div>
                </div>
                <div class="font-sample">
                  <div class="font-alibaba text-lg">高级开箱平台</div>
                </div>
                <div class="font-sample">
                  <div class="font-alibaba text-base">标准阅读体验，平衡美观与可读性</div>
                </div>
                <div class="text-caption text-gray-400">适用于正文、界面文字</div>
              </div>
            </div>
            <div>
              <h4 class="text-lg mb-4 text-gray-300">Bold (粗体)</h4>
              <div class="space-y-3">
                <div class="font-sample">
                  <div class="font-alibaba-bold text-2xl">CSGOSKINS</div>
                </div>
                <div class="font-sample">
                  <div class="font-alibaba-bold text-lg">高级开箱平台</div>
                </div>
                <div class="font-sample">
                  <div class="font-alibaba-bold text-base">突出重要信息，增强视觉层次</div>
                </div>
                <div class="text-caption text-gray-400">适用于标题、重点强调</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 响应式字体大小系统 -->
      <section class="mb-16">
        <h2 class="text-title text-gradient-primary mb-8 title-i18n">响应式字体大小系统</h2>
        <div class="glass-effect p-8 rounded-lg">
          <div class="space-y-6">
            <div class="font-size-demo">
              <div class="size-info">
                <span class="size-name">Display</span>
                <span class="size-values">60px / 40px (桌面 / 移动)</span>
              </div>
              <div class="text-display font-i18n">The quick brown fox jumps over the lazy dog</div>
            </div>
            
            <div class="font-size-demo">
              <div class="size-info">
                <span class="size-name">Hero</span>
                <span class="size-values">48px / 32px</span>
              </div>
              <div class="text-hero font-i18n">The quick brown fox jumps over the lazy dog</div>
            </div>
            
            <div class="font-size-demo">
              <div class="size-info">
                <span class="size-name">Title</span>
                <span class="size-values">36px / 28px</span>
              </div>
              <div class="text-title font-i18n">The quick brown fox jumps over the lazy dog</div>
            </div>
            
            <div class="font-size-demo">
              <div class="size-info">
                <span class="size-name">Subtitle</span>
                <span class="size-values">24px / 20px</span>
              </div>
              <div class="text-subtitle font-i18n">The quick brown fox jumps over the lazy dog</div>
            </div>
            
            <div class="font-size-demo">
              <div class="size-info">
                <span class="size-name">Body</span>
                <span class="size-values">16px / 15px</span>
              </div>
              <div class="text-body font-i18n">The quick brown fox jumps over the lazy dog. 敏捷的棕色狐狸跳过懒惰的狗。</div>
            </div>
            
            <div class="font-size-demo">
              <div class="size-info">
                <span class="size-name">Caption</span>
                <span class="size-values">14px / 13px</span>
              </div>
              <div class="text-caption font-i18n">The quick brown fox jumps over the lazy dog. 敏捷的棕色狐狸跳过懒惰的狗。</div>
            </div>
          </div>
        </div>
      </section>

      <!-- 字体使用指南 -->
      <section class="mb-16">
        <h2 class="text-title text-gradient-primary mb-8 title-i18n">CSS 类名使用指南</h2>
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <!-- 国际化字体工具类 -->
          <div class="glass-effect p-8 rounded-lg">
            <h3 class="text-lg mb-4 text-blue-400 font-i18n">国际化字体工具类</h3>
            <pre class="code-example"><code>&lt;!-- 国际化字体 - 根据语言自动切换 --&gt;
&lt;div class="font-i18n"&gt;自适应字体&lt;/div&gt;
&lt;h1 class="title-i18n"&gt;标题字体&lt;/h1&gt;
&lt;div class="accent-i18n"&gt;强调字体&lt;/div&gt;
&lt;div class="font-logo"&gt;Logo字体&lt;/div&gt;</code></pre>
          </div>

          <!-- 专用字体工具类 -->
          <div class="glass-effect p-8 rounded-lg">
            <h3 class="text-lg mb-4 text-purple-400 font-i18n">专用字体工具类</h3>
            <pre class="code-example"><code>&lt;!-- 英文字体 --&gt;
&lt;div class="font-arame"&gt;Arame 常规文本&lt;/div&gt;
&lt;div class="title-arame"&gt;ARAME 标题样式&lt;/div&gt;

&lt;div class="font-paracaps"&gt;PARACAPS 粗体文本&lt;/div&gt;
&lt;div class="title-paracaps"&gt;PARACAPS 标题样式&lt;/div&gt;

&lt;div class="font-trivial"&gt;TRIVIAL 粗体文本&lt;/div&gt;
&lt;div class="title-trivial"&gt;TRIVIAL 标题样式&lt;/div&gt;</code></pre>
          </div>

          <!-- 中文字体工具类 -->
          <div class="glass-effect p-8 rounded-lg">
            <h3 class="text-lg mb-4 text-orange-400 font-i18n">中文字体工具类</h3>
            <pre class="code-example"><code>&lt;!-- 阿里巴巴普惠体 --&gt;
&lt;div class="font-alibaba"&gt;阿里巴巴普惠体 常规&lt;/div&gt;
&lt;div class="font-alibaba-thin"&gt;阿里巴巴普惠体 细体&lt;/div&gt;
&lt;div class="font-alibaba-bold"&gt;阿里巴巴普惠体 粗体&lt;/div&gt;
&lt;div class="title-alibaba"&gt;阿里巴巴普惠体 标题样式&lt;/div&gt;</code></pre>
          </div>

          <!-- 响应式字体大小 -->
          <div class="glass-effect p-8 rounded-lg">
            <h3 class="text-lg mb-4 text-green-400 font-i18n">响应式字体大小</h3>
            <pre class="code-example"><code>&lt;!-- 响应式字体大小类 --&gt;
&lt;div class="text-display"&gt;Display 特大文字&lt;/div&gt;
&lt;div class="text-hero"&gt;Hero 大标题&lt;/div&gt;
&lt;div class="text-title"&gt;Title 标题&lt;/div&gt;
&lt;div class="text-subtitle"&gt;Subtitle 副标题&lt;/div&gt;
&lt;div class="text-body"&gt;Body 正文&lt;/div&gt;
&lt;div class="text-caption"&gt;Caption 说明文字&lt;/div&gt;</code></pre>
          </div>
        </div>
      </section>

      <!-- 字体特性测试 -->
      <section class="mb-16">
        <h2 class="text-title text-gradient-primary mb-8 title-i18n">字体特性测试</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <!-- 数字和符号 -->
          <div class="glass-effect p-8 rounded-lg">
            <h3 class="text-lg mb-6 text-blue-400">数字和符号测试</h3>
            <div class="space-y-4">
              <div class="font-sample">
                <span class="label">数字:</span>
                <div class="text-xl font-i18n">0123456789</div>
              </div>
              <div class="font-sample">
                <span class="label">价格:</span>
                <div class="text-xl font-i18n">¥199.99 $29.99 €24.99</div>
              </div>
              <div class="font-sample">
                <span class="label">符号:</span>
                <div class="text-xl font-i18n">!@#$%^&*()_+-={}[]|;':",./<>?</div>
              </div>
            </div>
          </div>

          <!-- 多语言混排 -->
          <div class="glass-effect p-8 rounded-lg">
            <h3 class="text-lg mb-6 text-green-400">多语言混排测试</h3>
            <div class="space-y-4">
              <div class="font-sample">
                <span class="label">中英混排:</span>
                <div class="text-lg font-i18n">CSGOSKINS 高级开箱平台</div>
              </div>
              <div class="font-sample">
                <span class="label">游戏术语:</span>
                <div class="text-lg font-i18n">AK-47 | 红线 (Redline)</div>
              </div>
              <div class="font-sample">
                <span class="label">品质等级:</span>
                <div class="text-lg font-i18n">StatTrak™ 崭新出厂 (Factory New)</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 语言切换演示 -->
      <section class="mb-16">
        <h2 class="text-title text-gradient-primary mb-8 title-i18n">语言切换演示</h2>
        <div class="glass-effect p-8 rounded-lg">
          <div class="flex justify-center mb-8">
            <div class="flex gap-4">
              <button 
                @click="switchLanguage('zh-hans')"
                :class="[
                  'px-6 py-3 rounded-lg transition-all duration-300 font-medium',
                  currentLang === 'zh-hans' 
                    ? 'bg-blue-500 text-white shadow-lg' 
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                ]"
              >
                简体中文
              </button>
              <button 
                @click="switchLanguage('en')"
                :class="[
                  'px-6 py-3 rounded-lg transition-all duration-300 font-medium',
                  currentLang === 'en' 
                    ? 'bg-blue-500 text-white shadow-lg' 
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                ]"
              >
                English
              </button>
            </div>
          </div>
          
          <div class="text-center space-y-6">
            <div class="text-hero font-i18n">
              {{ currentLang === 'zh-hans' ? 'CSGOSKINS 高级开箱平台' : 'CSGOSKINS Premium Platform' }}
            </div>
            <div class="text-subtitle font-i18n">
              {{ currentLang === 'zh-hans' ? '体验最佳的字体渲染效果' : 'Experience the best font rendering' }}
            </div>
            <div class="text-body font-i18n max-w-2xl mx-auto">
              {{ currentLang === 'zh-hans' 
                ? '我们的字体系统经过精心设计，确保在不同语言环境下都能提供最佳的阅读体验。支持中英文混排，具有良好的可读性和美观性。' 
                : 'Our typography system is carefully designed to provide optimal reading experience across different languages. It supports mixed Chinese-English text with excellent readability and aesthetics.' 
              }}
            </div>
          </div>
        </div>
      </section>

      <!-- 字体性能信息 -->
      <section class="mb-12">
        <h2 class="text-title text-gradient-primary mb-8 title-i18n">字体性能信息</h2>
        <div class="glass-effect p-8 rounded-lg">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div class="text-center">
              <div class="text-2xl font-bold text-blue-400 mb-2">5</div>
              <div class="text-sm text-gray-400">字体族数量</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-green-400 mb-2">8</div>
              <div class="text-sm text-gray-400">字重变体</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-purple-400 mb-2">6</div>
              <div class="text-sm text-gray-400">响应式尺寸</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-orange-400 mb-2">2</div>
              <div class="text-sm text-gray-400">语言支持</div>
            </div>
          </div>
        </div>
      </section>

      <!-- 状态指示器 -->
      <div class="text-center">
        <div class="inline-flex items-center gap-3 glass-effect px-6 py-3 rounded-full">
          <div class="w-2 h-2 bg-green-500 rounded-full"></div>
          <span class="text-white text-sm font-medium font-i18n">字体系统已就绪</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 页面配置
useHead({
  title: '字体系统展示 - CSGOSKINS',
  meta: [
    { name: 'description', content: 'CSGOSKINS 完整字体系统展示与使用指南' }
  ]
})

// 响应式数据
const currentLang = ref('zh-hans')

// 语言切换功能
const switchLanguage = (lang: string) => {
  currentLang.value = lang
  // 更新 HTML lang 属性
  if (process.client) {
    document.documentElement.setAttribute('lang', lang)
  }
}

// 初始化语言设置
onMounted(() => {
  if (process.client) {
    const htmlLang = document.documentElement.getAttribute('lang') || 'zh-hans'
    currentLang.value = htmlLang
  }
})
</script>

<style lang="scss" scoped>
// 背景网格
.bg-grid {
  background-image: 
    linear-gradient(to right, rgba(255, 255, 255, 0.03) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
  background-size: 20px 20px;
}

// 渐变文字效果
.text-gradient {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-primary {
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

// 玻璃效果
.glass-effect {
  backdrop-filter: blur(12px);
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.12);
  }
}

// 字体样本展示
.font-sample {
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  
  &:last-child {
    border-bottom: none;
  }
  
  .label {
    display: block;
    font-size: 0.75rem;
    color: #94a3b8;
    margin-bottom: 0.25rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }
}

// 字体大小演示
.font-size-demo {
  padding: 1.5rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  
  &:last-child {
    border-bottom: none;
  }
  
  .size-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
    
    .size-name {
      font-weight: 600;
      color: #e2e8f0;
    }
    
    .size-values {
      font-size: 0.875rem;
      color: #94a3b8;
      font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
    }
  }
}

// 代码示例样式
.code-example {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 1.5rem;
  overflow-x: auto;
  font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
  font-size: 0.875rem;
  line-height: 1.6;
  color: #e2e8f0;
  
  code {
    color: inherit;
    background: none;
    padding: 0;
    font-size: inherit;
  }
}

// 按钮样式优化
button {
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0);
  }
}

// 响应式优化
@media (max-width: 768px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .glass-effect {
    padding: 1.5rem;
  }
  
  .font-size-demo .size-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
  
  .code-example {
    padding: 1rem;
    font-size: 0.8rem;
  }
}

// 提高小屏幕上的可读性
@media (max-width: 640px) {
  .text-display {
    font-size: 2rem;
    line-height: 1.2;
  }
  
  .text-hero {
    font-size: 1.75rem;
    line-height: 1.3;
  }
  
  .grid {
    gap: 1rem;
  }
}
</style>
