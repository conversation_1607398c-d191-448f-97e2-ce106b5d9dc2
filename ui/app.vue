<template>
  <NuxtLayout>
    <NuxtPage />
  </NuxtLayout>
</template>

<script setup lang="ts">
// 简化的语言初始化处理
onMounted(() => {
  if (process.client) {
    // 监听全局语言变化事件，仅用于样式处理
    window.addEventListener('languageChanged', () => {
      // 轻量级的样式过渡效果
      document.body.classList.add('language-transition')
      setTimeout(() => {
        document.body.classList.remove('language-transition')
      }, 100)
    })
  }
})
</script>