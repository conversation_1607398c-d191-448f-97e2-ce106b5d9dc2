/**
 * 对战状态同步管理器
 * 统一管理箱子指示状态、玩家开箱记录、轮次进度等，确保各组件状态同步
 */

import { ref, reactive, computed, watch, nextTick } from 'vue'
import type { BattleBet, BattleItem } from '~/types/battle'

// 🎯 全局状态接口定义
export interface BattleStateSync {
  // 轮次状态
  currentRound: number
  totalRounds: number
  
  // 开箱状态
  openingCaseId: string | null
  openingPlayerIndex: number | null
  openingPlayerName: string
  
  // 案例状态
  completedRounds: Set<number>
  completedCases: Set<string>
  
  // 玩家记录状态
  playerRecords: Map<string, BattleItem[]>
  
  // 对战状态
  isBattleStarted: boolean
  isBattleFinished: boolean
  isRoundChanging: boolean
}

// 🎯 状态更新事件类型
export interface StateUpdateEvent {
  type: 'round_start' | 'round_complete' | 'opening_start' | 'opening_complete' | 'battle_end'
  round?: number
  caseId?: string
  playerIndex?: number
  playerName?: string
  item?: BattleItem
}

const DEBUG_PREFIX = '[🎰BATTLE-STATE-SYNC]'

// 🎯 全局状态实例
const battleStateSync = reactive<BattleStateSync>({
  currentRound: 1,
  totalRounds: 3,
  openingCaseId: null,
  openingPlayerIndex: null,
  openingPlayerName: '',
  completedRounds: new Set(),
  completedCases: new Set(),
  playerRecords: new Map(),
  isBattleStarted: false,
  isBattleFinished: false,
  isRoundChanging: false
})

// 🎯 状态更新回调队列
const stateUpdateCallbacks = new Set<(event: StateUpdateEvent) => void>()

export function useBattleStateSync() {
  
  // 🎯 计算属性 - 当前轮次箱子状态
  const getCurrentRoundCaseStatus = computed(() => {
    return (caseId: string, caseIndex: number) => {
      const state = battleStateSync
      
      // 正在开箱的箱子
      if (state.openingCaseId === caseId && state.isBattleStarted && !state.isBattleFinished) {
        return 'opening'
      }
      
      // 已完成的箱子
      if (state.completedCases.has(caseId) || state.isBattleFinished) {
        return 'completed'
      }
      
      // 当前轮次的箱子（但未开始开箱）
      if (caseIndex + 1 === state.currentRound && state.isBattleStarted) {
        return 'current'
      }
      
      // 待执行的箱子
      if (caseIndex + 1 > state.currentRound && state.isBattleStarted) {
        return 'pending'
      }
      
      return 'waiting'
    }
  })
  
  // 🎯 计算属性 - 玩家轮次记录
  const getPlayerRoundRecord = computed(() => {
    return (playerId: string, roundIndex: number) => {
      const records = battleStateSync.playerRecords.get(playerId) || []
      return records[roundIndex] || null
    }
  })
  
  // 🎯 状态更新方法
  const updateBattleState = (updates: Partial<BattleStateSync>) => {
    console.log(`${DEBUG_PREFIX} 更新对战状态:`, updates)
    
    // 原子性更新状态
    Object.assign(battleStateSync, updates)
    
    // 触发状态变化事件
    if (updates.currentRound !== undefined) {
      notifyStateUpdate({
        type: 'round_start',
        round: updates.currentRound
      })
    }
  }
  
  // 🎯 开箱状态管理
  const setOpeningState = (caseId: string | null, playerIndex: number | null, playerName: string = '') => {
    console.log(`${DEBUG_PREFIX} 设置开箱状态:`, { caseId, playerIndex, playerName })
    
    battleStateSync.openingCaseId = caseId
    battleStateSync.openingPlayerIndex = playerIndex
    battleStateSync.openingPlayerName = playerName
    
    if (caseId) {
      notifyStateUpdate({
        type: 'opening_start',
        caseId,
        playerIndex: playerIndex || undefined,
        playerName
      })
    }
  }
  
  // 🎯 玩家开箱记录管理
  const addPlayerRecord = (playerId: string, item: BattleItem, roundIndex?: number) => {
    console.log(`${DEBUG_PREFIX} 添加玩家记录:`, { playerId, item, roundIndex })
    
    const currentRoundIndex = roundIndex ?? (battleStateSync.currentRound - 1)
    
    // 获取或创建玩家记录数组
    if (!battleStateSync.playerRecords.has(playerId)) {
      battleStateSync.playerRecords.set(playerId, [])
    }
    
    const records = battleStateSync.playerRecords.get(playerId)!
    
    // 确保数组长度足够
    while (records.length <= currentRoundIndex) {
      records.push(null as any)
    }
    
    // 设置当前轮次记录 - 创建扩展的记录对象
    const extendedRecord = {
      ...item,
      // 添加额外的记录信息（不修改原始BattleItem类型）
      roundNumber: currentRoundIndex + 1,
      recordTimestamp: Date.now()
    } as BattleItem & { roundNumber: number; recordTimestamp: number }
    
    records[currentRoundIndex] = extendedRecord
    
    // 触发记录更新事件
    notifyStateUpdate({
      type: 'opening_complete',
      round: currentRoundIndex + 1,
      item
    })
  }
  
  // 🎯 轮次完成管理
  const completeRound = (roundNumber: number, caseId?: string) => {
    console.log(`${DEBUG_PREFIX} 完成轮次:`, { roundNumber, caseId })
    
    battleStateSync.completedRounds.add(roundNumber)
    
    if (caseId) {
      battleStateSync.completedCases.add(caseId)
    }
    
    // 清除开箱状态
    setOpeningState(null, null, '')
    
    notifyStateUpdate({
      type: 'round_complete',
      round: roundNumber,
      caseId
    })
  }
  
  // 🎯 轮次切换管理
  const switchToNextRound = async () => {
    console.log(`${DEBUG_PREFIX} 切换到下一轮次`)
    
    battleStateSync.isRoundChanging = true
    
    // 完成当前轮次
    completeRound(battleStateSync.currentRound, battleStateSync.openingCaseId || undefined)
    
    // 等待状态更新
    await nextTick()
    
    // 切换到下一轮
    if (battleStateSync.currentRound < battleStateSync.totalRounds) {
      battleStateSync.currentRound += 1
      console.log(`${DEBUG_PREFIX} 轮次已更新为: ${battleStateSync.currentRound}`)
    } else {
      // 对战结束
      finishBattle()
    }
    
    battleStateSync.isRoundChanging = false
  }
  
  // 🎯 对战结束管理
  const finishBattle = () => {
    console.log(`${DEBUG_PREFIX} 对战结束`)
    
    battleStateSync.isBattleFinished = true
    
    // 标记所有箱子为已完成
    for (let i = 1; i <= battleStateSync.totalRounds; i++) {
      battleStateSync.completedRounds.add(i)
    }
    
    // 清除开箱状态
    setOpeningState(null, null, '')
    
    notifyStateUpdate({
      type: 'battle_end'
    })
  }
  
  // 🎯 状态重置
  const resetBattleState = () => {
    console.log(`${DEBUG_PREFIX} 重置对战状态`)
    
    battleStateSync.currentRound = 1
    battleStateSync.totalRounds = 3
    battleStateSync.openingCaseId = null
    battleStateSync.openingPlayerIndex = null
    battleStateSync.openingPlayerName = ''
    battleStateSync.completedRounds.clear()
    battleStateSync.completedCases.clear()
    battleStateSync.playerRecords.clear()
    battleStateSync.isBattleStarted = false
    battleStateSync.isBattleFinished = false
    battleStateSync.isRoundChanging = false
  }
  
  // 🎯 事件通知系统
  const notifyStateUpdate = (event: StateUpdateEvent) => {
    console.log(`${DEBUG_PREFIX} 状态更新事件:`, event)
    
    stateUpdateCallbacks.forEach(callback => {
      try {
        callback(event)
      } catch (error) {
        console.error(`${DEBUG_PREFIX} 状态更新回调错误:`, error)
      }
    })
  }
  
  // 🎯 订阅状态更新
  const subscribeStateUpdate = (callback: (event: StateUpdateEvent) => void) => {
    stateUpdateCallbacks.add(callback)
    
    return () => {
      stateUpdateCallbacks.delete(callback)
    }
  }
  
  // 🎯 调试工具
  const getDebugInfo = () => {
    return {
      state: { ...battleStateSync },
      completedRounds: Array.from(battleStateSync.completedRounds),
      completedCases: Array.from(battleStateSync.completedCases),
      playerRecordsSize: battleStateSync.playerRecords.size,
      callbacksCount: stateUpdateCallbacks.size
    }
  }
  
  return {
    // 状态访问
    state: readonly(battleStateSync),
    
    // 计算属性
    getCurrentRoundCaseStatus,
    getPlayerRoundRecord,
    
    // 状态更新方法
    updateBattleState,
    setOpeningState,
    addPlayerRecord,
    completeRound,
    switchToNextRound,
    finishBattle,
    resetBattleState,
    
    // 事件系统
    subscribeStateUpdate,
    
    // 调试工具
    getDebugInfo
  }
} 