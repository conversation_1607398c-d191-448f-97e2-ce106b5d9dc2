from django.conf.urls import url

from withdraw import views#, model_signals



app_name = 'withdraw'
urlpatterns = [
    # url(r'^withdraw/', views.WithdrawItemsView.as_view()),
    url(r'^waxpeer_sell/', views.GetWaxpeerSellView.as_view()),
    url(r'^waxpeer_items/', views.GetWaxpeerItemsView.as_view()),
    url(r'^waxpeer_match/', views.GetWaxpeerMatchView.as_view()),
    # url(r'^buy/', views.BuyWaxpeerItemView.as_view()),
    url(r'^history/', views.GetTradeHistoryView.as_view()),
    url(r'^detail/', views.GetTradeDetailView.as_view()),
    url(r'^close/', views.CloseTradeView.as_view()),
    url(r'^checkoffer/',  views.CheckWaxpeerOfferView.as_view())
]