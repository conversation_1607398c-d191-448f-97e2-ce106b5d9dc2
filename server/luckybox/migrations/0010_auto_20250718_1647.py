# Generated by Django 3.2.25 on 2025-07-18 08:47

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('package', '0023_auto_20250718_1628'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('luckybox', '0009_luckyboxcategory_name'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='luckyboxrecord',
            name='item',
        ),
        migrations.AlterField(
            model_name='luckyboxcategory',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='luckyboxgroup',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='luckyboxitem',
            name='group',
            field=models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, to='luckybox.luckyboxgroup'),
        ),
        migrations.AlterField(
            model_name='luckyboxitem',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='luckyboxitem',
            name='item_info',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='package.iteminfo'),
        ),
        migrations.AlterField(
            model_name='luckyboxrecord',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='luckyboxrecord',
            name='target',
            field=models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='lucky_target', to='package.iteminfo', verbose_name='luckybox target item'),
        ),
        migrations.AlterField(
            model_name='luckyboxrecord',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lucky_record', to=settings.AUTH_USER_MODEL, verbose_name='luckybox item'),
        ),
        migrations.AlterField(
            model_name='luckyrecommendgroup',
            name='group',
            field=models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, to='luckybox.luckyboxgroup'),
        ),
        migrations.AlterField(
            model_name='luckyrecommendgroup',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
    ]
