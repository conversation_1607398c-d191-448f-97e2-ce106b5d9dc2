import json
import os

from django.contrib import admin, messages
from django.utils.translation import gettext_lazy as _

from steambase.enums import PackageState, RespCode
from sitecfg.interfaces import get_online_users_base_count
from steambase.redis_con import get_redis


def startup_set_online_base_count(modeladmin, request, queryset):
    key = queryset.first().key
    if key == 'online_users_base_count':
        count = get_online_users_base_count()
        r = get_redis()
        r.set('online:base:count', count)
        messages.success(request, _('set online users base count complete'))
    else:
        messages.warning(request, _('set online users base count fail'))

startup_set_online_base_count.short_description = _("set online base count")