from django.urls import re_path as url

from promotion import model_signals
from promotion import views


app_name = 'promotion'
urlpatterns = [
    url('^code/', views.UserPromotionView.as_view()),
    url('^box/', views.GetPromotionCaseList.as_view()),
    url('^record/', views.GetPromotionRecordView.as_view()),
    url('^refer/', views.UserReferView.as_view()),

    url('^pick/', views.UserPickProfitView.as_view()),


    url('^setcode/', views.UserSetCodeView.as_view()),


    url('^bind/', views.UserBindPromotionView.as_view()),
    url('^bindstatus/', views.UserBindStatusView.as_view()),

    url('^config/', views.GetPromotionConfigView.as_view())
]
