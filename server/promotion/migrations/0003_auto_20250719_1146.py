# Generated by Django 3.2.25 on 2025-07-19 03:46

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('promotion', '0002_auto_20250718_1647'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='promotioncaseconfig',
            options={'ordering': ('level',), 'verbose_name': '推广箱子配置', 'verbose_name_plural': 'PromotionCaseConfig'},
        ),
        migrations.AlterModelOptions(
            name='promotionlevelconfig',
            options={'ordering': ('level',), 'verbose_name': '推广等级配置', 'verbose_name_plural': 'Promotion Level Config'},
        ),
        migrations.AlterModelOptions(
            name='promotionrecord',
            options={'ordering': ('-create_time',), 'verbose_name': '推广记录', 'verbose_name_plural': 'Promotion Record'},
        ),
        migrations.AlterModelOptions(
            name='userpromotion',
            options={'verbose_name': '用户推广', 'verbose_name_plural': 'User Promotion'},
        ),
        migrations.AlterField(
            model_name='promotionrecord',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='promotion_records', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
        migrations.AlterField(
            model_name='userpromotion',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='promotion', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
    ]
