<template>
  <div class="min-h-screen relative">
    <!-- 背景装饰 -->
    <div class="fixed inset-0 -z-10">
      <div class="absolute top-0 -left-32 w-64 h-64 rounded-full blur-3xl bg-primary/10 animate-pulse-slow"></div>
      <div class="absolute bottom-0 -right-32 w-64 h-64 rounded-full blur-3xl bg-secondary/10 animate-pulse-slow"></div>
      <div class="absolute inset-0 bg-grid-pattern opacity-20"></div>
      <div class="absolute inset-0 bg-noise opacity-40 mix-blend-soft-light"></div>
    </div>

    <!-- PC端内容 -->
    <div class="container mx-auto px-4 py-8 relative z-10" v-if="!isMobile">
      <!-- 页面头部 -->
      <div class="page-header text-center mb-12">
        <h1 class="text-4xl font-bold mb-4">
          <span class="text-gradient">{{ $t('activities.title') }}</span>
        </h1>
        <p class="text-gray-300 max-w-2xl mx-auto">{{ $t('activities.description') }}</p>
        
        <!-- 活动状态筛选 -->
        <div class="status-filter mt-8 flex justify-center space-x-4">
          <button 
            v-for="status in statusOptions" 
            :key="status.value"
            @click="currentStatus = status.value"
            class="filter-btn"
            :class="{ 'active': currentStatus === status.value }"
          >
            {{ $t(status.label) }}
          </button>
        </div>
      </div>
      
      <!-- 活动内容 -->
      <div class="activities-content">
        <!-- 活动简介 -->
        <div class="activities-intro mb-8">
          <div class="text-lg font-medium mb-2 text-gradient-subtle">
            {{ $t(statusLabel) }}({{ filteredActivities.length }})
          </div>
          <div class="h-0.5 w-24 bg-gradient-primary rounded-full mb-6"></div>
        </div>
        
        <!-- 活动列表 -->
        <div class="activities-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <ActivityCard
            v-for="activity in filteredActivities"
            :key="activity.id"
            :activity="activity"
            @click="navigateToDetail(activity.id)"
            @reminder="setReminder"
            @participate="participateActivity"
            @view-results="viewResults"
          />
        </div>
        
        <!-- 无活动提示 -->
        <div v-if="filteredActivities.length === 0" class="empty-activities text-center py-16">
          <div class="empty-icon mb-6">
            <i class="i-heroicons-calendar-days text-6xl text-gray-500"></i>
          </div>
          <div class="empty-text text-gray-400 text-lg mb-6">
            {{ $t('activities.no_activities', { status: $t(statusLabel) }) }}
          </div>
          <button @click="currentStatus = 'all'" class="btn-primary">
            {{ $t('activities.view_all') }}
          </button>
        </div>
      </div>
    </div>

    <!-- 移动端内容 -->
    <div class="container mx-auto px-4 py-4 relative z-10" v-else>
      <!-- 页面头部 -->
      <div class="page-header mb-8">
        <h1 class="text-2xl font-bold mb-3">{{ $t('activities.title') }}</h1>
        <p class="text-gray-300 text-sm">{{ $t('activities.description') }}</p>
        
        <!-- 活动状态筛选 -->
        <div class="status-filter mt-6 flex space-x-2 overflow-x-auto pb-2">
          <button 
            v-for="status in statusOptions" 
            :key="status.value"
            @click="currentStatus = status.value"
            class="filter-btn-mobile whitespace-nowrap"
            :class="{ 'active': currentStatus === status.value }"
          >
            {{ $t(status.label) }}
          </button>
        </div>
      </div>
      
      <!-- 活动内容 -->
      <div class="activities-content">
        <!-- 活动简介 -->
        <div class="activities-intro mb-6">
          <div class="text-base font-medium mb-2 text-gradient-subtle">
            {{ $t(statusLabel) }}({{ filteredActivities.length }})
          </div>
          <div class="h-0.5 w-16 bg-gradient-primary rounded-full mb-4"></div>
        </div>
        
        <!-- 活动列表 -->
        <div class="activities-grid space-y-4">
          <ActivityCard
            v-for="activity in filteredActivities"
            :key="activity.id"
            :activity="activity"
            @click="navigateToDetail(activity.id)"
            @reminder="setReminder"
            @participate="participateActivity"
            @view-results="viewResults"
          />
        </div>
        
        <!-- 无活动提示 -->
        <div v-if="filteredActivities.length === 0" class="empty-activities text-center py-12">
          <div class="empty-icon mb-4">
            <i class="i-heroicons-calendar-days text-4xl text-gray-500"></i>
          </div>
          <div class="empty-text text-gray-400 text-base mb-4">
            {{ $t('activities.no_activities', { status: $t(statusLabel) }) }}
          </div>
          <button @click="currentStatus = 'all'" class="btn-primary">
            {{ $t('activities.view_all') }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 国际化
const { t } = useI18n()

// 设备检测 - 使用响应式状态
const isMobile = ref(false)

// 在客户端检测设备类型
onMounted(() => {
  const checkMobile = () => {
    isMobile.value = window.innerWidth < 768
  }
  
  checkMobile()
  window.addEventListener('resize', checkMobile)
  
  onUnmounted(() => {
    window.removeEventListener('resize', checkMobile)
  })
})

// SEO配置
useSeoMeta({
  title: '活动中心 - CSGO开箱网站',
  description: '参与活动赢取奖励，解锁稀有物品与限定皮肤',
  keywords: '活动中心,CSGO开箱,游戏活动,奖励活动',
  ogTitle: '活动中心 - CSGO开箱网站',
  ogDescription: '参与活动赢取奖励，解锁稀有物品与限定皮肤',
  twitterTitle: '活动中心 - CSGO开箱网站',
  twitterDescription: '参与活动赢取奖励，解锁稀有物品与限定皮肤'
})

// 状态选项
const statusOptions = [
  { label: 'activities.status.all', value: 'all' },
  { label: 'activities.status.upcoming', value: 'upcoming' },
  { label: 'activities.status.active', value: 'active' },
  { label: 'activities.status.ended', value: 'ended' }
]

// 当前选中的状态
const currentStatus = ref('all')

// 根据当前状态获取标签文本
const statusLabel = computed(() => {
  const option = statusOptions.find(option => option.value === currentStatus.value)
  return option ? option.label : 'activities.status.all'
})

// 模拟的活动数据
const activities = ref([
  {
    id: 1,
    title: '暑期开箱狂欢节',
    description: '参与暑期开箱活动，赢取稀有皮肤和实物奖励',
    image: '/demo/banner1.png',
    startTime: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
    endTime: new Date(Date.now() + 4 * 24 * 60 * 60 * 1000).toISOString(),
    status: 'active',
    participants: 1245,
    rewards: ['限定武器皮肤', '实物周边', '平台代币'],
    participantAvatars: [
      { url: '/demo/avatar1.png', name: '用户1' },
      { url: '/demo/avatar2.png', name: '用户2' },
      { url: '/demo/avatar3.png', name: '用户3' }
    ],
    tasks: [
      { name: '每日登录', completed: true },
      { name: '开启5个武器箱', completed: true },
      { name: '参与3场对战', completed: false },
      { name: '邀请好友', completed: false }
    ]
  },
  {
    id: 2,
    title: '新版本先锋体验',
    description: '新赛季内容尝鲜，提前体验新地图和新武器',
    image: '/demo/banner2.png',
    startTime: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
    endTime: new Date(Date.now() + 16 * 24 * 60 * 60 * 1000).toISOString(),
    status: 'upcoming',
    participants: 578,
    rewards: ['测试资格', '专属徽章', '角色皮肤'],
    participantAvatars: [
      { url: '/demo/avatar4.png', name: '用户4' },
      { url: '/demo/avatar5.png', name: '用户5' }
    ],
    tasks: [
      { name: '注册测试资格', completed: false, locked: false },
      { name: '完成问卷调查', completed: false, locked: true },
      { name: '体验新地图', completed: false, locked: true }
    ]
  },
  {
    id: 3,
    title: '电竞嘉年华',
    description: '观看职业比赛，为战队助威赢取奖励',
    image: '/demo/banner3.png',
    startTime: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
    endTime: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
    status: 'ended',
    participants: 3456,
    rewards: ['签名周边', '限定箱子', '战队贴纸'],
    participantAvatars: [
      { url: '/demo/avatar1.png', name: '用户1' },
      { url: '/demo/avatar2.png', name: '用户2' },
      { url: '/demo/avatar3.png', name: '用户3' },
      { url: '/demo/avatar4.png', name: '用户4' }
    ],
    tasks: [
      { name: '观看任意一场比赛', completed: true },
      { name: '为战队投票', completed: true },
      { name: '分享比赛预测', completed: true },
      { name: '收集所有战队徽章', completed: false }
    ]
  },
  {
    id: 4,
    title: '周末双倍掉落',
    description: '周末期间参与游戏，获得双倍开箱概率',
    image: '/demo/banner4.png',
    startTime: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
    endTime: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
    status: 'active',
    participants: 5621,
    rewards: ['开箱加成', '稀有武器皮肤'],
    participantAvatars: [
      { url: '/demo/avatar5.png', name: '用户5' },
      { url: '/demo/avatar3.png', name: '用户3' }
    ],
    tasks: [
      { name: '周末登录游戏', completed: true },
      { name: '进行3次开箱', completed: false },
      { name: '分享开箱结果', completed: false }
    ]
  },
  {
    id: 5,
    title: '年度纪念活动',
    description: '庆祝游戏上线周年，回顾精彩时刻',
    image: '/demo/banner5.png',
    startTime: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(),
    endTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
    status: 'upcoming',
    participants: 128,
    rewards: ['周年纪念皮肤', '限定徽章', '游戏内货币'],
    participantAvatars: [],
    tasks: [
      { name: '预约活动', completed: false, locked: false },
      { name: '分享活动', completed: false, locked: false },
      { name: '收集周年徽章', completed: false, locked: true },
      { name: '参加周年庆典', completed: false, locked: true }
    ]
  }
])

// 根据当前状态筛选活动
const filteredActivities = computed(() => {
  if (currentStatus.value === 'all') {
    return activities.value
  }
  return activities.value.filter(activity => activity.status === currentStatus.value)
})

// 方法
const navigateToDetail = (id: number) => {
  navigateTo(`/activities/${id}`)
}

const setReminder = (activity: any) => {
  console.log('设置提醒:', activity.title)
  // TODO: 实现提醒功能
}

const participateActivity = (activity: any) => {
  console.log('参与活动:', activity.title)
  // TODO: 实现参与功能
}

const viewResults = (activity: any) => {
  console.log('查看结果:', activity.title)
  // TODO: 实现查看结果功能
}
</script>

<style scoped>
/* 页面头部样式 */
.page-header {
  text-align: center;
  position: relative;
}

.page-header::after {
  content: '';
  position: absolute;
  bottom: -1rem;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
  border-radius: 2px;
}

/* 筛选按钮样式 */
.filter-btn {
  @apply px-6 py-2 rounded-xl text-sm font-medium transition-all duration-300;
  @apply bg-white/5 border border-white/10 text-white/70;
  @apply hover:bg-white/10 hover:border-white/20 hover:text-white;
}

.filter-btn.active {
  @apply bg-primary/20 border-primary text-primary;
  @apply shadow-lg shadow-primary/25;
}

.filter-btn-mobile {
  @apply px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300;
  @apply bg-white/5 border border-white/10 text-white/70;
  @apply hover:bg-white/10 hover:border-white/20 hover:text-white;
}

.filter-btn-mobile.active {
  @apply bg-primary/20 border-primary text-primary;
  @apply shadow-lg shadow-primary/25;
}

/* 空状态样式 */
.empty-activities {
  @apply text-center py-16;
}

.empty-icon {
  @apply mb-6;
}

.empty-text {
  @apply text-gray-400 text-lg mb-6;
}

/* 按钮样式 */
.btn-primary {
  @apply bg-primary hover:bg-primary-dark text-white font-semibold py-3 px-6 rounded-xl;
  @apply transition-all duration-300 shadow-lg shadow-primary/25;
}

/* 渐变文本 */
.text-gradient {
  @apply text-transparent bg-clip-text bg-gradient-to-r from-primary to-secondary;
}

.text-gradient-subtle {
  @apply text-transparent bg-clip-text bg-gradient-to-r from-white/80 to-white/60;
}

/* 网格背景 */
.bg-grid-pattern {
  background-image: linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
}

/* 噪声背景 */
.bg-noise {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
}

/* 动画 */
.animate-pulse-slow {
  animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style> 