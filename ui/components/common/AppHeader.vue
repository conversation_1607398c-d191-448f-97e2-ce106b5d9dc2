<!-- components/common/AppHeader.vue -->
<template>
  <div class="fixed top-0 left-0 right-0 z-[9999]">
    <!-- 动态背景装饰层 - 参考Footer设计 -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      <!-- 动态光球效果 -->
      <div class="header-orb header-orb-1"></div>
      <div class="header-orb header-orb-2"></div>
      <div class="header-orb header-orb-3"></div>
      
      <!-- 网格背景 -->
      <div class="header-grid"></div>
      
      <!-- 渐变叠加 -->
      <div class="header-gradient-overlay"></div>
      
      <!-- 毛玻璃效果 -->
      <div class="absolute inset-0 backdrop-blur-xl bg-slate-950/80"></div>
    </div>

    <!-- 顶部通知栏 - 重新设计 -->
    <div class="relative z-10 bg-black/40 border-b border-white/10 backdrop-blur-sm">
      <div class="container mx-auto px-4 lg:px-6">
        <div class="flex justify-between items-center h-12">
          <!-- 通知区域 - 增强设计 -->
          <div class="flex items-center gap-4 max-w-96 overflow-hidden">
            <!-- 通知图标装饰 -->
            <div class="relative flex items-center justify-center w-8 h-8 rounded-lg bg-blue-500/20 border border-blue-400/30">
              <Icon name="heroicons:megaphone" class="w-4 h-4 text-blue-400" />
              <div class="absolute inset-0 rounded-lg bg-blue-400/20 animate-pulse"></div>
            </div>
            
            <div class="flex-1 overflow-hidden relative h-6 flex items-center">
              <span 
                v-if="activeNotices[currentNoticeIndex]"
                class="whitespace-nowrap text-white/90 text-sm font-medium block leading-6 animate-slide-in"
                :title="getNotificationTitle(activeNotices[currentNoticeIndex])"
              >
                {{ getNotificationText(activeNotices[currentNoticeIndex]) }}
              </span>
              <span v-else class="whitespace-nowrap text-white/70 text-sm font-normal block leading-6">
                {{ $t('header.no_notifications') }}
              </span>
            </div>
          </div>

          <!-- 右侧功能区 - 现代化设计 -->
          <div class="flex items-center gap-3">
            <!-- Demo 链接 - 增强样式 -->
            <NuxtLink to="/demo" class="group relative flex items-center gap-2 px-4 py-2 text-white/80 text-sm rounded-xl transition-all duration-300 hover:text-blue-400 hover:bg-blue-400/10 hover:-translate-y-0.5 hover:scale-105 border border-transparent hover:border-blue-400/30 overflow-hidden">
              <Icon name="material-symbols:code" class="w-4 h-4 group-hover:rotate-12 transition-transform duration-300" />
              <span class="relative z-10">{{ $t('nav.demo') }}</span>
              <div class="absolute inset-0 bg-gradient-to-r from-blue-400/10 to-purple-400/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </NuxtLink>

            <!-- 帮助中心 - 增强下拉菜单 -->
            <div class="relative group">
              <button class="group relative flex items-center gap-2 px-4 py-2 text-white/80 text-sm bg-transparent border border-white/10 rounded-xl cursor-pointer transition-all duration-300 hover:text-blue-400 hover:bg-blue-400/10 hover:border-blue-400/30 hover:scale-105 overflow-hidden">
                <Icon name="material-symbols:help-outline" class="w-4 h-4 group-hover:scale-110 transition-transform duration-300" />
                <span class="relative z-10">{{ $t('nav.help') }}</span>
                <Icon name="material-symbols:keyboard-arrow-down" class="w-4 h-4 transition-transform group-hover:rotate-180 duration-300" />
                <div class="absolute inset-0 bg-gradient-to-r from-blue-400/5 to-purple-400/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </button>
              
              <!-- 增强的下拉菜单 -->
              <div class="absolute top-full right-0 mt-3 min-w-56 bg-slate-900/95 backdrop-blur-xl border border-white/20 rounded-2xl p-3 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-[9999]">
                <div class="absolute -top-2 right-6 w-4 h-4 bg-slate-900/95 border-l border-t border-white/20 rotate-45 backdrop-blur-xl"></div>
                
                <NuxtLink to="/faq" class="help-item-enhanced">
                  <Icon name="material-symbols:quiz-outline" class="w-5 h-5" />
                  <div class="flex flex-col">
                    <span class="font-medium">{{ $t('nav.faq') }}</span>
                    <span class="text-xs text-white/50">{{ $t('nav.faq_desc') }}</span>
                  </div>
                  <Icon name="heroicons:arrow-right" class="w-4 h-4 opacity-0 group-hover:opacity-100 -translate-x-2 group-hover:translate-x-0 transition-all duration-300" />
                </NuxtLink>
                
                <NuxtLink to="/support" class="help-item-enhanced">
                  <Icon name="material-symbols:support-agent" class="w-5 h-5" />
                  <div class="flex flex-col">
                    <span class="font-medium">{{ $t('nav.support') }}</span>
                    <span class="text-xs text-white/50">{{ $t('nav.support_desc') }}</span>
                  </div>
                  <Icon name="heroicons:arrow-right" class="w-4 h-4 opacity-0 group-hover:opacity-100 -translate-x-2 group-hover:translate-x-0 transition-all duration-300" />
                </NuxtLink>
                
                <NuxtLink to="/contact" class="help-item-enhanced">
                  <Icon name="material-symbols:contact-mail-outline" class="w-5 h-5" />
                  <div class="flex flex-col">
                    <span class="font-medium">{{ $t('nav.contact') }}</span>
                    <span class="text-xs text-white/50">{{ $t('nav.contact_desc') }}</span>
                  </div>
                  <Icon name="heroicons:arrow-right" class="w-4 h-4 opacity-0 group-hover:opacity-100 -translate-x-2 group-hover:translate-x-0 transition-all duration-300" />
                </NuxtLink>
              </div>
            </div>

            <!-- 语言切换器 - 保持原有 -->
            <LanguageSwitcher />
          </div>
        </div>
      </div>
    </div>

    <!-- 装饰分割线 - 增强效果 -->
    <div class="relative h-1 bg-gradient-to-r from-transparent via-blue-400/50 to-transparent">
      <div class="absolute inset-0 bg-gradient-to-r from-blue-400/20 via-purple-400/20 to-blue-400/20 animate-pulse"></div>
    </div>

    <!-- 主导航区域 - 重新设计 -->
    <header class="relative z-5 bg-slate-900/60 backdrop-blur-xl border-b border-white/10">
      <div class="container mx-auto px-4 lg:px-6">
        <div class="flex items-center justify-between h-24">
          <!-- Logo区域 - 参考Footer的精美设计 -->
          <NuxtLink to="/" class="group relative flex items-center gap-3 px-6 py-3 rounded-2xl transition-all duration-500 hover:scale-105 overflow-hidden" :title="$t('site.title')">
            <!-- Logo背景装饰 -->
            <div class="absolute inset-0 bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-blue-500/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            <div class="absolute inset-0 border border-white/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            
            <!-- Logo文字和图标 -->
            <div class="relative z-10 flex items-center gap-3">
              <span class="font-logo text-4xl font-bold bg-gradient-to-r from-blue-400 via-white to-blue-400 bg-clip-text text-transparent drop-shadow-lg">CSGO</span>
              
              <div class="relative">
                <img src="/images/logo.svg" alt="CSGOSKINS" class="w-12 h-12 relative z-10 filter drop-shadow-lg transition-all duration-500 group-hover:scale-110">
                <!-- Logo发光效果 -->
                <div class="absolute inset-0 w-12 h-12 bg-blue-400/30 rounded-full blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500 animate-pulse"></div>
                <div class="absolute inset-0 w-12 h-12 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full blur-xl opacity-20 group-hover:opacity-40 transition-opacity duration-500"></div>
              </div>
              
              <span class="font-logo text-4xl font-bold bg-gradient-to-r from-blue-400 via-white to-blue-400 bg-clip-text text-transparent drop-shadow-lg">SKINS</span>
            </div>
            
            <!-- Logo闪光效果 -->
            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out"></div>
          </NuxtLink>

          <!-- 主导航菜单 - 现代化重设计 -->
          <nav class="hidden md:block" role="navigation" :aria-label="$t('nav.main')">
            <ul class="flex items-center gap-1">
              <li v-for="item in mainNavItems" :key="item.path">
                <NuxtLink 
                  :to="item.path" 
                  class="group relative flex items-center gap-3 px-6 py-3 rounded-xl text-white/80 font-medium transition-all duration-300 hover:text-white hover:bg-white/10 hover:scale-105 border border-transparent hover:border-white/20 overflow-hidden"
                  :class="{ 'nav-link-active text-blue-400 bg-blue-400/10 border-blue-400/30': isActivePath(item.path) }"
                  :aria-current="isActivePath(item.path) ? 'page' : undefined"
                >
                  <!-- 导航项背景装饰 -->
                  <div class="absolute inset-0 bg-gradient-to-r from-blue-400/5 via-purple-400/5 to-blue-400/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  
                  <!-- 导航项内容 -->
                  <span class="relative z-10 transition-transform duration-300 group-hover:scale-105">{{ $t(item.label) }}</span>
                  
                  <!-- 徽章 -->
                  <span v-if="item.badge" class="relative z-10 px-2 py-0.5 text-xs font-bold text-white bg-gradient-to-r from-red-500 to-red-600 rounded-full shadow-lg animate-pulse">{{ item.badge }}</span>
                  
                  <!-- 活跃状态指示器 -->
                  <div v-if="isActivePath(item.path)" class="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full"></div>
                  
                  <!-- 悬停发光效果 -->
                  <div class="absolute inset-0 rounded-xl opacity-0 group-hover:opacity-20 transition-opacity duration-300" :style="{ boxShadow: '0 0 20px rgba(59, 130, 246, 0.3)' }"></div>
                </NuxtLink>
              </li>
            </ul>
          </nav>

          <!-- 用户区域 - 重新设计 -->
          <div class="flex items-center gap-4">
            <!-- 未登录状态 - 增强设计 -->
            <template v-if="!userStore?.isAuthenticated">
              <NuxtLink to="/auth/login" class="group relative flex items-center gap-3 px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-blue-500/30 border border-blue-400/30 overflow-hidden">
                <Icon name="material-symbols:login" class="w-5 h-5 group-hover:scale-110 transition-transform duration-300" />
                <span class="relative z-10">{{ $t('auth.login.title') }}</span>
                
                <!-- 按钮装饰效果 -->
                <div class="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-purple-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700 ease-out"></div>
              </NuxtLink>
            </template>
            
            <!-- 已登录状态 - 豪华用户卡片设计 -->
            <template v-else>
              <div class="relative group">
                <!-- 用户信息卡片 - 重新设计 -->
                <button class="group relative flex items-center gap-4 px-6 py-3 bg-gradient-to-r from-slate-800/80 to-slate-900/80 border border-white/20 rounded-2xl backdrop-blur-xl transition-all duration-300 hover:bg-gradient-to-r hover:from-slate-700/80 hover:to-slate-800/80 hover:border-white/30 hover:scale-105 hover:shadow-xl hover:shadow-black/30 overflow-hidden" :aria-label="$t('user.menu')">
                  <!-- 卡片背景装饰 -->
                  <div class="absolute inset-0 bg-gradient-to-r from-blue-500/5 via-purple-500/5 to-blue-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  
                  <!-- 用户头像 - 增强设计 -->
                  <div class="relative">
                    <img 
                      :src="userStore.userAvatar" 
                      :alt="userStore.userNickname" 
                      class="w-10 h-10 rounded-xl object-cover border-2 border-white/20 group-hover:border-blue-400/50 transition-all duration-300"
                    >
                    <!-- 头像发光环 -->
                    <div class="absolute inset-0 w-10 h-10 bg-gradient-to-r from-blue-400 to-purple-400 rounded-xl blur-md opacity-0 group-hover:opacity-30 transition-opacity duration-300 -z-10"></div>
                    <!-- 在线状态指示器 -->
                    <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 border-2 border-slate-900 rounded-full animate-pulse"></div>
                  </div>
                  
                  <!-- 用户信息 - 增强布局 -->
                  <div class="hidden sm:block text-left">
                    <div class="text-sm font-semibold text-white group-hover:text-blue-400 transition-colors duration-300">{{ userStore.userNickname || 'Guest' }}</div>
                    <div class="flex items-center text-xs">
                      <Icon name="material-symbols:attach-money" class="w-3 h-3 text-green-400" />
                      <span class="text-green-400 font-medium">{{ userStore.formattedBalance }}</span>
                    </div>
                  </div>
                  
                  <!-- 充值按钮 - 重新设计 -->
                  <NuxtLink to="/recharge" class="relative flex items-center justify-center w-10 h-10 bg-gradient-to-r from-green-500/20 to-green-600/20 border border-green-400/40 rounded-xl text-green-400 hover:bg-gradient-to-r hover:from-green-500/30 hover:to-green-600/30 hover:scale-110 hover:shadow-lg hover:shadow-green-400/20 transition-all duration-300 overflow-hidden" :title="$t('user.recharge')" @click.stop>
                    <Icon name="material-symbols:add-circle-outline" class="w-5 h-5 relative z-10" />
                    <div class="absolute inset-0 bg-gradient-to-r from-green-400/10 to-green-500/10 opacity-0 hover:opacity-100 transition-opacity duration-300"></div>
                  </NuxtLink>
                  
                  <!-- 下拉箭头 -->
                  <Icon name="material-symbols:keyboard-arrow-down" class="w-5 h-5 text-white/60 transition-transform group-hover:rotate-180 duration-300" />
                </button>
                
                <!-- 用户下拉菜单 - 豪华重设计 -->
                <div class="absolute top-full right-0 mt-4 w-80 bg-slate-900/95 backdrop-blur-xl border border-white/20 rounded-3xl p-6 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform translate-y-2 group-hover:translate-y-0 shadow-2xl shadow-black/50 z-[9999]">
                  <!-- 3D箭头指示器 -->
                  <div class="absolute -top-3 right-8 w-6 h-6 bg-slate-900/95 border-l border-t border-white/20 rotate-45 backdrop-blur-xl"></div>
                  
                  <!-- 用户信息头部 -->
                  <!-- <div class="flex items-center gap-4 p-4 bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-white/10 rounded-2xl mb-6">
                    <img :src="userStore.userAvatar" :alt="userStore.userNickname" class="w-14 h-14 rounded-xl object-cover border-2 border-white/20">
                    <div class="flex-1 min-w-0">
                      <div class="text-lg font-bold text-white truncate">{{ userStore.userNickname }}</div>
                      <div class="text-sm text-white/60 truncate">{{ userStore.userEmail }}</div>
                      <div class="flex items-center gap-2 mt-1">
                        <Icon name="material-symbols:attach-money" class="w-4 h-4 text-green-400" />
                        <span class="text-green-400 font-semibold">{{ userStore.formattedBalance }}</span>
                      </div>
                    </div>
                  </div> -->
                  
                  <!-- 分割线 -->
                  <div class="h-px bg-gradient-to-r from-transparent via-white/20 to-transparent mb-4"></div>
                  
                  <!-- 菜单项 -->
                  <div class="space-y-2">
                    <NuxtLink to="/profile" class="user-menu-item-enhanced">
                      <Icon name="material-symbols:person-outline" class="w-5 h-5" />
                      <div class="flex flex-col">
                        <span class="font-medium">{{ $t('user.profile') }}</span>
                        <span class="text-xs text-white/50">{{ $t('user.profile_desc') }}</span>
                      </div>
                      <Icon name="heroicons:arrow-right" class="w-4 h-4 opacity-0 group-hover:opacity-100 -translate-x-2 group-hover:translate-x-0 transition-all duration-300" />
                    </NuxtLink>
                    
                    <NuxtLink to="/profile/inventory" class="user-menu-item-enhanced">
                      <Icon name="material-symbols:inventory-2-outline" class="w-5 h-5" />
                      <div class="flex flex-col">
                        <span class="font-medium">{{ $t('user.inventory') }}</span>
                        <span class="text-xs text-white/50">{{ $t('user.inventory_desc') }}</span>
                      </div>
                      <Icon name="heroicons:arrow-right" class="w-4 h-4 opacity-0 group-hover:opacity-100 -translate-x-2 group-hover:translate-x-0 transition-all duration-300" />
                    </NuxtLink>
                    
                    <NuxtLink to="/profile/history" class="user-menu-item-enhanced">
                      <Icon name="material-symbols:history" class="w-5 h-5" />
                      <div class="flex flex-col">
                        <span class="font-medium">{{ $t('user.history') }}</span>
                        <span class="text-xs text-white/50">{{ $t('user.history_desc') }}</span>
                      </div>
                      <Icon name="heroicons:arrow-right" class="w-4 h-4 opacity-0 group-hover:opacity-100 -translate-x-2 group-hover:translate-x-0 transition-all duration-300" />
                    </NuxtLink>
                  </div>
                  
                  <!-- 分割线 -->
                  <div class="h-px bg-gradient-to-r from-transparent via-white/20 to-transparent my-4"></div>
                  
                  <!-- 退出登录 -->
                  <button @click="handleLogout" class="user-menu-item-enhanced w-full text-left text-red-400 hover:bg-red-500/10 hover:text-red-300 hover:border-red-400/30">
                    <Icon name="material-symbols:logout" class="w-5 h-5" />
                    <div class="flex flex-col">
                      <span class="font-medium">{{ $t('auth.logout') }}</span>
                      <span class="text-xs text-white/50">{{ $t('auth.logout_desc') }}</span>
                    </div>
                    <Icon name="heroicons:arrow-right" class="w-4 h-4 opacity-0 group-hover:opacity-100 -translate-x-2 group-hover:translate-x-0 transition-all duration-300" />
                  </button>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
    </header>
  </div>
</template>

<script setup lang="ts">
// 国际化设置 - 符合项目规范
const { t, locale } = useI18n()

// Nuxt 3 组合式 API
const route = useRoute()
const router = useRouter()

// 导入LanguageSwitcher组件
import LanguageSwitcher from '~/components/common/LanguageSwitcher.vue'

// 检查是否有user store
let userStore: any = null
try {
  userStore = useUserStore()
} catch (error) {
  console.warn('User store not available, user features disabled')
}

// 标准通知数据格式定义 - 支持多语言（API标准格式）
interface NotificationItem {
  id: number
  type: 'info' | 'success' | 'warning' | 'activity' | 'system' | 'promotion' | 'event'
  // 多语言文本字段
  text_zh_hans: string     // 简体中文文本
  text_en: string          // 英文文本
  title_zh_hans?: string   // 简体中文标题（可选）
  title_en?: string        // 英文标题（可选）
  // 显示属性
  icon?: string            // 图标名称
  priority: number         // 1-高优先级, 2-中等, 3-低优先级
  status: 'active' | 'inactive' | 'scheduled' // 状态
  // 时间相关
  created_at: string       // 创建时间 ISO 8601
  updated_at: string       // 更新时间 ISO 8601
  start_time?: string      // 开始显示时间（可选）
  end_time?: string        // 结束显示时间（可选）
  duration?: number        // 单次显示时长(ms)，0表示使用默认时长
  // 扩展属性
  link?: string            // 点击链接（可选）
  image?: string           // 图片链接（可选）
  meta?: Record<string, any> // 额外数据（可选）
}

// 通知数据 - API标准格式
const notices = ref<NotificationItem[]>([
  {
    id: 1,
    type: 'info',
    text_zh_hans: '欢迎来到CSGO开箱模拟器，体验最真实的开箱乐趣！',
    text_en: 'Welcome to CSGO Case Simulator, experience the most realistic unboxing fun!',
    title_zh_hans: '欢迎',
    title_en: 'Welcome',
    icon: 'material-symbols:waving-hand',
    priority: 1,
    status: 'active',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    duration: 5000
  },
  {
    id: 2,
    type: 'activity',
    text_zh_hans: '🎉 新活动：限时开箱大作战正在火热进行中，快来参与吧！',
    text_en: '🎉 New Event: Limited-time Case Opening Championship is in full swing, join now!',
    title_zh_hans: '活动通知',
    title_en: 'Event Notice',
    icon: 'material-symbols:celebration',
    priority: 2,
    status: 'active',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    duration: 6000,
    link: '/activities'
  },
  {
    id: 3,
    type: 'system',
    text_zh_hans: '⚙️ 系统维护：每日凌晨2:00-3:00进行系统维护，感谢理解',
    text_en: '⚙️ System Maintenance: Daily maintenance from 2:00-3:00 AM, thank you for understanding',
    title_zh_hans: '系统公告',
    title_en: 'System Notice',
    icon: 'material-symbols:settings',
    priority: 3,
    status: 'active',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    duration: 4000
  },
  {
    id: 4,
    type: 'success',
    text_zh_hans: '🏆 恭喜玩家小明开出传说级别AK47红线皮肤，价值$200+',
    text_en: '🏆 Congratulations to player XiaoMing for unboxing legendary AK47 Redline skin, worth $200+',
    title_zh_hans: '精彩开箱',
    title_en: 'Amazing Unbox',
    icon: 'material-symbols:trophy',
    priority: 1,
    status: 'active',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    duration: 7000
  },
  {
    id: 5,
    type: 'promotion',
    text_zh_hans: '💎 充值优惠：首次充值送50%奖励，限时3天！',
    text_en: '💎 Recharge Bonus: First deposit gets 50% bonus, limited time 3 days!',
    title_zh_hans: '充值优惠',
    title_en: 'Recharge Bonus',
    icon: 'material-symbols:diamond',
    priority: 1,
    status: 'active',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    duration: 8000,
    link: '/recharge'
  }
])

// 获取当前语言的通知文本
const getNotificationText = (notice: NotificationItem): string => {
  const currentLocale = locale.value as 'zh-hans' | 'en'
  
  if (currentLocale === 'zh-hans') {
    return notice.text_zh_hans || notice.text_en || '暂无通知'
  } else {
    return notice.text_en || notice.text_zh_hans || 'No notification'
  }
}

// 获取当前语言的通知标题
const getNotificationTitle = (notice: NotificationItem): string | undefined => {
  const currentLocale = locale.value as 'zh-hans' | 'en'
  
  if (currentLocale === 'zh-hans') {
    return notice.title_zh_hans || notice.title_en
  } else {
    return notice.title_en || notice.title_zh_hans
  }
}

const currentNoticeIndex = ref(0)
const activeNotices = computed(() => {
  return notices.value.filter(notice => 
    notice.status === 'active' && 
    notice.text_zh_hans && 
    notice.text_en
  )
})

// 主导航项 - 符合项目导航结构
const mainNavItems = [
  { path: '/', label: 'nav.home' },
  { path: '/cases', label: 'nav.cases' },
  { path: '/battle', label: 'nav.battle' },
  { path: '/activities', label: 'nav.activity', badge: 'HOT' },
  { path: '/profile', label: 'nav.profile' }
]

// 通知轮播 - 性能优化和智能轮播
let noticeInterval: NodeJS.Timeout | null = null

const startNoticeRotation = () => {
  if (activeNotices.value.length <= 1) return
  
  noticeInterval = setInterval(() => {
    const nextIndex = (currentNoticeIndex.value + 1) % activeNotices.value.length
    currentNoticeIndex.value = nextIndex
  }, 4000) // 每4秒切换一次
}

const stopNoticeRotation = () => {
  if (noticeInterval) {
    clearInterval(noticeInterval)
    noticeInterval = null
  }
}

// 检查路径是否激活
const isActivePath = (path: string) => {
  if (path === '/') {
    return route.path === '/'
  }
  return route.path.startsWith(path)
}

// 退出登录
const handleLogout = async () => {
  if (userStore) {
    await userStore.logout()
  }
  router.push('/')
}

// 生命周期
onMounted(() => {
  startNoticeRotation()
})

onUnmounted(() => {
  stopNoticeRotation()
})
</script>

<style lang="scss" scoped>
// 使用系统定义的CSS变量
// 背景层特效 - 保留必要的视觉效果
.header-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    180deg,
    rgba(10, 10, 10, 0.95) 0%,
    rgba(17, 24, 39, 0.9) 50%,
    rgba(31, 41, 55, 0.85) 100%
  );
}

.header-glass-effect {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(31, 41, 55, 0.4);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(107, 114, 128, 0.3);
}

.header-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    var(--color-primary-50) 25%,
    var(--color-secondary-50) 75%,
    transparent 100%
  );
  animation: glow-pulse 3s ease-in-out infinite;
}

// 分割线发光效果
.divider-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    var(--color-primary-30) 50%,
    transparent 100%
  );
  animation: glow-pulse 4s ease-in-out infinite;
}

// Logo特效
.logo-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    var(--color-primary-10) 0%,
    var(--color-secondary-10) 100%
  );
  border-radius: var(--radius-xl);
  opacity: 0;
  transition: opacity var(--transition-normal) ease;
  
  .logo-container:hover & {
    opacity: 1;
  }
}

.logo-text {
  color: var(--color-primary);
  font-weight: 700;
  position: relative;
  z-index: 2;
  text-shadow: 0 0 20px var(--color-primary-30);
}

.logo-icon {
  transition: all var(--transition-normal) ease;
  filter: drop-shadow(0 0 10px var(--color-primary-30));
}

.logo-icon-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 4rem;
  height: 4rem;
  background: radial-gradient(
    circle,
    var(--color-primary-30) 0%,
    var(--color-secondary-20) 50%,
    transparent 70%
  );
  border-radius: 50%;
  opacity: 0;
  transition: all var(--transition-normal) ease;
  
  .logo-container:hover & {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.2);
  }
}

.logo-shine {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.2) 50%,
    transparent 100%
  );
  transform: skewX(-20deg);
  transition: left 0.8s var(--ease-out);
  
  .logo-container:hover & {
    left: 100%;
  }
}

// 导航链接特效
.nav-indicator {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 2px;
  background: var(--gradient-primary);
  border-radius: 1px;
  transition: all var(--transition-normal) ease;
  opacity: 0;
  
  .nav-link-active & {
    width: 100%;
    opacity: 1;
  }
}

.nav-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    ellipse at center,
    var(--color-primary-20) 0%,
    transparent 70%
  );
  opacity: 0;
  transition: opacity var(--transition-normal) ease;
  
  .nav-link:hover &,
  .nav-link-active & {
    opacity: 1;
  }
}

// 用户头像环形特效
.user-avatar-ring {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 50%;
  background: var(--gradient-primary);
  opacity: 0;
  transition: opacity var(--transition-normal) ease;
  z-index: -1;
  
  .group:hover & {
    opacity: 1;
  }
}

// 帮助菜单项样式
.help-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  color: var(--color-text-secondary);
  text-decoration: none;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast) ease;
  font-size: 0.875rem;
  
  &:hover {
    color: var(--color-primary);
    background: var(--color-primary-10);
    transform: translateX(4px);
  }
}

// 用户菜单项样式
.user-menu-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  color: var(--color-text-secondary);
  text-decoration: none;
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast) ease;
  border: none;
  background: transparent;
  cursor: pointer;
  font-size: 0.875rem;
  
  &:hover {
    color: var(--color-primary);
    background: var(--color-primary-10);
    transform: translateX(4px);
  }
}

// 关键帧动画
@keyframes glow-pulse {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

// 动态背景装饰效果 - 参考Footer设计
.header-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(40px);
  opacity: 0.3;
  animation: float 6s ease-in-out infinite;
  
  &.header-orb-1 {
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.4) 0%, transparent 70%);
    top: -100px;
    left: 10%;
    animation-delay: 0s;
  }
  
  &.header-orb-2 {
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, rgba(147, 51, 234, 0.3) 0%, transparent 70%);
    top: -75px;
    right: 20%;
    animation-delay: 2s;
  }
  
  &.header-orb-3 {
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.2) 0%, transparent 70%);
    top: -50px;
    right: 40%;
    animation-delay: 4s;
  }
}

.header-grid {
  position: absolute;
  inset: 0;
  background-image: 
    linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
  background-size: 20px 20px;
  opacity: 0.5;
}

.header-gradient-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(
    135deg,
    rgba(15, 23, 42, 0.9) 0%,
    rgba(30, 41, 59, 0.8) 50%,
    rgba(15, 23, 42, 0.9) 100%
  );
}

// 增强的帮助菜单项样式
.help-item-enhanced {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-radius: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
  border: 1px solid transparent;
  transition: all 0.3s ease;
  overflow: hidden;
  
  &:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
    transform: translateX(0.5rem);
    box-shadow: 0 4px 20px rgba(59, 130, 246, 0.1);
  }
}

// 增强的用户菜单项样式
.user-menu-item-enhanced {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-radius: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
  border: 1px solid transparent;
  transition: all 0.3s ease;
  overflow: hidden;
  
  &:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
    transform: translateX(0.25rem);
    box-shadow: 0 4px 20px rgba(59, 130, 246, 0.1);
  }
}

// 动画效果
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-10px) rotate(1deg);
  }
  66% {
    transform: translateY(5px) rotate(-1deg);
  }
}

@keyframes slide-in {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-slide-in {
  animation: slide-in 0.5s ease-out;
}

// 响应式优化
@media (max-width: 768px) {
  .header-orb {
    display: none; // 移动端隐藏装饰球以提升性能
  }
}

// 硬件加速优化
.header-orb,
.help-item-enhanced,
.user-menu-item-enhanced {
  will-change: transform;
  transform: translateZ(0);
}
</style>