<template>
  <div class="classic-list-card bg-gray-800/90 backdrop-blur-md border border-gray-700/50 rounded-md overflow-hidden hover:border-primary/40 transition-all duration-300 group relative">
    <!-- 装饰性网格背景 -->
    <div class="absolute inset-0 bg-grid opacity-10"></div>
    
    <!-- 稀有度背景光效 -->
    <div 
      class="absolute inset-0 opacity-20 group-hover:opacity-30 transition-opacity duration-300"
      :style="{ background: `linear-gradient(135deg, ${rarityColor}10, transparent 70%)` }"
    ></div>

    <div class="relative z-10 p-4 flex items-center space-x-4">
      <!-- 用户信息区域 -->
      <div class="flex items-center space-x-3 min-w-0 flex-shrink-0 w-32">
        <img
          :src="record.user_avatar || 'https://placehold.co/32x32/374151/9ca3af?text=U'"
          :alt="record.user_name"
          class="w-8 h-8 rounded-full border border-gray-600"
          @error="(e) => (e.target as HTMLImageElement).src = 'https://placehold.co/32x32/374151/9ca3af?text=U'"
        />
        <div class="min-w-0 flex-1">
          <div class="text-primary font-medium text-sm truncate">{{ record.user_name }}</div>
          <div class="text-white/40 text-xs">{{ formatTime() }}</div>
        </div>
      </div>

      <!-- 箭头指示器 -->
      <div class="flex items-center space-x-2 text-white/60">
        <span class="text-xs">opened</span>
        <i class="i-ph-arrow-right text-primary"></i>
      </div>

      <!-- 箱子信息区域 -->
      <div class="flex items-center space-x-3 min-w-0 flex-shrink-0 w-40">
        <img
          :src="record.case_info?.cover"
          :alt="record.case_info?.name"
          class="w-12 h-12 object-contain rounded bg-gray-700"
          @error="(e) => (e.target as HTMLImageElement).src = 'https://placehold.co/48x48/374151/9ca3af?text=CASE'"
        />
        <div class="min-w-0 flex-1">
          <div class="text-white font-medium text-sm truncate">{{ record.case_info?.name }}</div>
          <div class="text-white/60 text-xs">Container</div>
        </div>
      </div>

      <!-- 获得指示器 -->
      <div class="flex items-center space-x-2 text-white/60">
        <span class="text-xs">got</span>
        <i class="i-ph-arrow-right text-success"></i>
      </div>

      <!-- 物品信息区域 -->
      <div class="flex items-center space-x-3 min-w-0 flex-grow">
        <div class="relative">
          <!-- 物品背景光效 -->
          <div 
            class="absolute inset-0 blur-lg opacity-30 rounded"
            :style="{ backgroundColor: rarityColor }"
          ></div>
          <img
            :src="record.item_info?.image"
            :alt="record.item_info?.name"
            class="relative w-16 h-16 object-contain drop-shadow-lg"
            @error="(e) => (e.target as HTMLImageElement).src = 'https://placehold.co/64x64/dc2626/fecaca?text=ITEM'"
          />
        </div>
        <div class="min-w-0 flex-1">
          <div class="text-white font-bold text-sm truncate">{{ getSkinName(record.item_info?.name) }}</div>
          <div class="text-white/80 text-xs truncate">{{ getWeaponName(record.item_info?.name) }}</div>
          <div 
            class="text-xs font-medium truncate"
            :style="{ color: rarityColor }"
          >
            {{ getExteriorName() }} | {{ getRarityName() }}
          </div>
        </div>
      </div>

      <!-- StatTrak 标签 -->
      <div 
        v-if="isStatTrak"
        class="flex-shrink-0 bg-orange-500/90 rounded px-2 py-1 text-xs font-bold text-white border border-orange-500/50"
      >
        <i class="i-ph-lightning mr-1"></i>ST
      </div>

      <!-- 稀有度标签 -->
      <div 
        class="flex-shrink-0 px-3 py-1 rounded text-xs font-medium text-white border"
        :style="{ 
          backgroundColor: rarityColor + '20',
          borderColor: rarityColor + '50',
          color: rarityColor
        }"
      >
        {{ getRarityName() }}
      </div>
    </div>

    <!-- 悬停时的详细信息遮罩 -->
    <div class="absolute inset-0 bg-black/80 opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center z-20">
      <div class="text-center text-white">
        <div class="text-lg font-bold mb-2">{{ getSkinName(record.item_info?.name) }}</div>
        <div class="text-sm text-white/80 mb-1">{{ getExteriorName() }}</div>
        <div class="text-xs text-primary">
          <i class="i-ph-cursor-click mr-1"></i>
          点击查看箱子详情
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  record: any
}

const props = defineProps<Props>()

const getSkinName = (fullName?: string): string => {
  if (!fullName) return 'Unknown Item'
  const parts = fullName.split(' | ')
  if (parts.length < 2) return fullName
  const skinPart = parts[1].trim()
  return skinPart.split(' (')[0].trim()
}

const getWeaponName = (fullName?: string): string => {
  if (!fullName) return 'Unknown Weapon'
  const parts = fullName.split(' | ')
  return parts[0].trim()
}

const getExteriorName = (): string => {
  return props.record.item_info?.item_exterior?.exterior_name || 'Factory New'
}

const getRarityName = (): string => {
  return 'Covert' // 可以根据实际数据调整
}

const isStatTrak = computed(() => {
  return props.record.item_info?.name?.includes('StatTrak™') || false
})

const rarityColor = computed(() => {
  // 根据稀有度返回颜色，这里使用固定值做演示
  const rarities: Record<string, string> = {
    'Covert': '#eb4b4b',
    'Classified': '#d32ce6', 
    'Restricted': '#8847ff',
    'Mil-Spec': '#4b69ff',
    'Industrial': '#5e98d9',
    'Consumer': '#b0c3d9'
  }
  return rarities[getRarityName()] || '#eb4b4b'
})

const formatTime = (): string => {
  return 'just now'
}
</script>

<style lang="scss" scoped>
.classic-list-card {
  animation: slideInFromLeft 0.6s ease-out;
}

.bg-grid {
  background-image: 
    linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
  background-size: 20px 20px;
}

@keyframes slideInFromLeft {
  from {
    transform: translateX(-30px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
</style> 