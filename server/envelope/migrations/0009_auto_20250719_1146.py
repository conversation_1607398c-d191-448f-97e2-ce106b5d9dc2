# Generated by Django 3.2.25 on 2025-07-19 03:46

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('envelope', '0008_auto_20250718_1647'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='enveloperecord',
            options={'verbose_name': '红包记录', 'verbose_name_plural': 'EnvelopeRecord'},
        ),
        migrations.AlterModelOptions(
            name='enveloperule',
            options={'verbose_name': '红包规则', 'verbose_name_plural': 'EnvelopeRule'},
        ),
        migrations.AlterField(
            model_name='enveloperecord',
            name='user',
            field=models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='envelop_record', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
    ]
