import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'
import type { CaseItem } from '~/types/case'
import { caseApi, type CaseDetailItem } from '~/services/case-api'
import { transformCaseItems } from '~/utils/case-transformer'

interface CaseDetailData {
  id: number
  name: string
  name_en?: string
  name_zh_hans?: string
  case_key: string
  cover: string
  price: number
  discount?: number
  tag?: string
  tag_en?: string
  tag_zh_hans?: string
  open_count: number
  type_name?: string
  type_name_en?: string
  type_name_zh_hans?: string
}

interface CaseItemData {
  item_info: {
    id: number
    name: string
    name_en?: string
    name_zh_hans?: string
    image: string
    item_price: {
      price: number
      update_time: string
    }
    item_category: {
      cate_name: string
      cate_name_en?: string
      cate_name_zh_hans?: string
    }
    item_quality: {
      quality_name: string
      quality_name_en?: string
      quality_name_zh_hans?: string
      quality_color: string
    }
    item_rarity: {
      rarity_name: string
      rarity_name_en?: string
      rarity_name_zh_hans?: string
      rarity_color: string
    }
    item_exterior?: {
      exterior_name: string
      exterior_name_en?: string
      exterior_name_zh_hans?: string
      exterior_color: string
    }
  }
  probability?: number
  rarity_color?: string
}

interface CaseRarityGroup {
  rarity_id: number
  rarity_name: string
  rarity_name_en?: string
  rarity_name_zh_hans?: string
  rarity_color: string
  items: CaseItemData[]
  count_percentage?: number
}

interface OpeningRecord {
  id: number
  uid: string
  create_time: string
  update_time: string
  price: number
  user_info: {
    profile: {
      avatar: string
      nickname: string
    }
    uid: string
  }
  case_info: {
    id: number
    name: string
    name_en?: string
    name_zh_hans?: string
    cover: string
    price: number
  }
  item_info: {
    id: number
    name: string
    name_en?: string
    name_zh_hans?: string
    image: string
    item_price: {
      price: number
      update_time: string
    }
    item_category: {
      cate_name: string
      cate_name_en?: string
      cate_name_zh_hans?: string
    }
    item_quality: {
      quality_name: string
      quality_name_en?: string
      quality_name_zh_hans?: string
      quality_color: string
    }
    item_rarity: {
      rarity_name: string
      rarity_name_en?: string
      rarity_name_zh_hans?: string
      rarity_color: string
    }
    item_exterior?: {
      exterior_name: string
      exterior_name_en?: string
      exterior_name_zh_hans?: string
      exterior_color: string
    }
  }
}

// 箱子分类数据
interface CaseCategoryData {
  cate_id: number
  cate_name: string
  cate_name_en?: string
  cate_name_zh_hans?: string
  cases: CaseDetailData[]
}

interface CaseState {
  // 箱子列表数据
  allCases: CaseCategoryData[]
  hotCases: CaseDetailData[]
  newCases: CaseDetailData[]
  discountCases: CaseDetailData[]
  
  // 箱子详情数据
  caseDetails: Record<string, CaseDetailData>
  // 箱子物品数据
  caseItems: Record<string, CaseRarityGroup[]>
  // 开箱记录数据
  openingRecords: Record<string, OpeningRecord[]>
  
  // 加载状态
  loading: {
    allCases: boolean
    hotCases: boolean
    newCases: boolean
    discountCases: boolean
    details: Record<string, boolean>
    items: Record<string, boolean>
    records: Record<string, boolean>
  }
  
  // 错误状态
  errors: {
    allCases: string | null
    hotCases: string | null
    newCases: string | null
    discountCases: string | null
    details: Record<string, string | null>
    items: Record<string, string | null>
    records: Record<string, string | null>
  }
  
  // 缓存时间戳
  cacheTimestamps: {
    allCases: number
    hotCases: number
    newCases: number
    discountCases: number
    details: Record<string, number>
    items: Record<string, number>
    records: Record<string, number>
  }
}

export const useCaseStore = defineStore('case', {
  state: (): CaseState => ({
    allCases: [],
    hotCases: [],
    newCases: [],
    discountCases: [],
    caseDetails: {},
    caseItems: {},
    openingRecords: {},
    loading: {
      allCases: false,
      hotCases: false,
      newCases: false,
      discountCases: false,
      details: {},
      items: {},
      records: {}
    },
    errors: {
      allCases: null,
      hotCases: null,
      newCases: null,
      discountCases: null,
      details: {},
      items: {},
      records: {}
    },
    cacheTimestamps: {
      allCases: 0,
      hotCases: 0,
      newCases: 0,
      discountCases: 0,
      details: {},
      items: {},
      records: {}
    }
  }),

  getters: {
    // 获取所有箱子分类
    getAllCases: (state) => state.allCases,
    
    // 获取热门箱子
    getHotCases: (state) => state.hotCases,
    
    // 获取新箱子
    getNewCases: (state) => state.newCases,
    
    // 获取折扣箱子
    getDiscountCases: (state) => state.discountCases,

    // 获取箱子详情
    getCaseDetail: (state) => (caseKey: string) => {
      return state.caseDetails[caseKey] || null
    },

    // 获取箱子物品
    getCaseItems: (state) => (caseKey: string) => {
      return state.caseItems[caseKey] || []
    },

    // 获取开箱记录
    getOpeningRecords: (state) => (caseKey: string) => {
      return state.openingRecords[caseKey] || []
    },

    // 检查是否正在加载
    isLoading: (state) => (caseKey: string, type: 'details' | 'items' | 'records') => {
      return state.loading[type][caseKey] || false
    },

    // 检查列表是否正在加载
    isListLoading: (state) => (type: 'allCases' | 'hotCases' | 'newCases' | 'discountCases') => {
      return state.loading[type]
    },

    // 获取错误信息
    getError: (state) => (caseKey: string, type: 'details' | 'items' | 'records') => {
      return state.errors[type][caseKey] || null
    },

    // 获取列表错误信息
    getListError: (state) => (type: 'allCases' | 'hotCases' | 'newCases' | 'discountCases') => {
      return state.errors[type]
    },

    // 检查数据是否已缓存且有效
    isCacheValid: (state) => (cacheKey: string, type: 'details' | 'items' | 'records' | 'allCases' | 'hotCases' | 'newCases' | 'discountCases') => {
      let timestamp: number
      
      if (type === 'details' || type === 'items' || type === 'records') {
        timestamp = (state.cacheTimestamps as any)[type][cacheKey]
      } else {
        timestamp = (state.cacheTimestamps as any)[type]
      }
      
      if (!timestamp) return false
      
      const now = Date.now()
      const cacheAge = now - timestamp
      
      // 不同类型数据的缓存时间
      const maxAge = (() => {
        if (type === 'records') return 2 * 60 * 1000 // 开箱记录缓存2分钟
        if (type === 'allCases' || type === 'hotCases' || type === 'newCases' || type === 'discountCases') return 5 * 60 * 1000 // 列表数据缓存5分钟
        return 10 * 60 * 1000 // 箱子详情和物品缓存10分钟
      })()
      
      return cacheAge < maxAge
    },

    // 检查箱子数据是否完整
    isCaseDataReady: (state) => (caseKey: string) => {
      const hasDetail = !!state.caseDetails[caseKey]
      const hasItems = !!state.caseItems[caseKey]?.length
      const notLoadingDetail = !state.loading.details[caseKey]
      const notLoadingItems = !state.loading.items[caseKey]
      
      return hasDetail && hasItems && notLoadingDetail && notLoadingItems
    }
  },

  actions: {
    // 获取所有箱子分类
    async fetchAllCases(force = false) {
      if (!force && this.isCacheValid('', 'allCases') && this.allCases.length > 0) {
        // console.log('[CaseStore] 使用缓存的箱子分类数据')
        return this.allCases
      }

      try {
        this.loading.allCases = true
        this.errors.allCases = null

        // console.log('[CaseStore] 获取箱子分类数据')
        const response = await $fetch('/api/box/search/') as any
        
        // 后端修正：search_case 现在直接返回分类数组，不再包装在 items 字段中
        if (response.code === 0 && response.body) {
          // 后端修正后，响应格式从 {code: 0, body: {items: []}} 改为 {code: 0, body: []}
          this.allCases = response.body
          this.cacheTimestamps.allCases = Date.now()
          // console.log(`[CaseStore] 箱子分类数据获取成功: ${this.allCases.length}个分类`)
          return this.allCases
        } else {
          throw new Error(response.message || '获取箱子分类失败')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '获取箱子分类失败'
        this.errors.allCases = errorMessage
        // console.error('[CaseStore] 获取箱子分类失败:', error)
        throw error
      } finally {
        this.loading.allCases = false
      }
    },

    // 获取热门箱子
    async fetchHotCases(limit = 5, force = false) {
      if (!force && this.isCacheValid('', 'hotCases') && this.hotCases.length > 0) {
        // console.log('[CaseStore] 使用缓存的热门箱子数据')
        return this.hotCases
      }

      try {
        this.loading.hotCases = true
        this.errors.hotCases = null

        // console.log('[CaseStore] 获取热门箱子数据')
        const response = await caseApi.getHotCases({ num: limit })
        
        if (response.success && response.data) {
          // 转换数据格式
          const convertedData = response.data.map(item => ({
            id: parseInt(item.id) || 0,
            name: item.name,
            name_en: item.name_en,
            name_zh_hans: item.name_zh_hans,
            case_key: item.case_key,
            cover: item.cover,
            price: item.price,
            discount: item.discount,
            tag: item.tag,
            tag_en: item.tag_en,
            tag_zh_hans: item.tag_zh_hans,
            open_count: item.open_count || 0,
            type_name: item.description,
            type_name_en: item.description_en,
            type_name_zh_hans: item.description_zh_hans
          }))
          
          this.hotCases = convertedData
          this.cacheTimestamps.hotCases = Date.now()
          // console.log(`[CaseStore] 热门箱子数据获取成功: ${this.hotCases.length}个`)
          return this.hotCases
        } else {
          throw new Error(response.message || '获取热门箱子失败')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '获取热门箱子失败'
        this.errors.hotCases = errorMessage
        // console.error('[CaseStore] 获取热门箱子失败:', error)
        return []
      } finally {
        this.loading.hotCases = false
      }
    },

    // 获取新箱子
    async fetchNewCases(limit = 5, force = false) {
      if (!force && this.isCacheValid('', 'newCases') && this.newCases.length > 0) {
        // console.log('[CaseStore] 使用缓存的新箱子数据')
        return this.newCases
      }

      try {
        this.loading.newCases = true
        this.errors.newCases = null

        // console.log('[CaseStore] 获取新箱子数据')
        const response = await caseApi.getNewCases({ num: limit })
        
        if (response.success && response.data) {
          // 转换数据格式
          const convertedData = response.data.map(item => ({
            id: parseInt(item.id) || 0,
            name: item.name,
            name_en: item.name_en,
            name_zh_hans: item.name_zh_hans,
            case_key: item.case_key,
            cover: item.cover,
            price: item.price,
            discount: item.discount,
            tag: item.tag,
            tag_en: item.tag_en,
            tag_zh_hans: item.tag_zh_hans,
            open_count: item.open_count || 0,
            type_name: item.description,
            type_name_en: item.description_en,
            type_name_zh_hans: item.description_zh_hans
          }))
          
          this.newCases = convertedData
          this.cacheTimestamps.newCases = Date.now()
          // console.log(`[CaseStore] 新箱子数据获取成功:
          return this.newCases
        } else {
          throw new Error(response.message || '获取新箱子失败')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '获取新箱子失败'
        this.errors.newCases = errorMessage
        // console.error('[CaseStore] 获取新箱子失败:', error)
        return []
      } finally {
        this.loading.newCases = false
      }
    },

    // 获取折扣箱子
    async fetchDiscountCases(limit = 5, force = false) {
      if (!force && this.isCacheValid('', 'discountCases') && this.discountCases.length > 0) {
        // console.log('[CaseStore] 使用缓存的折扣箱子数据')
        return this.discountCases
      }

      try {
        this.loading.discountCases = true
        this.errors.discountCases = null

        // console.log('[CaseStore] 获取折扣箱子数据')
        const response = await caseApi.getDiscountCases({ num: limit })
        
        if (response.success && response.data) {
          // 转换数据格式
          const convertedData = response.data.map(item => ({
            id: parseInt(item.id) || 0,
            name: item.name,
            name_en: item.name_en,
            name_zh_hans: item.name_zh_hans,
            case_key: item.case_key,
            cover: item.cover,
            price: item.price,
            discount: item.discount,
            tag: item.tag,
            tag_en: item.tag_en,
            tag_zh_hans: item.tag_zh_hans,
            open_count: item.open_count || 0,
            type_name: item.description,
            type_name_en: item.description_en,
            type_name_zh_hans: item.description_zh_hans
          }))
          
          this.discountCases = convertedData
          this.cacheTimestamps.discountCases = Date.now()
          // console.log(`[CaseStore] 折扣箱子数据获取成功: ${this.discountCases.length}个`)
          return this.discountCases
        } else {
          throw new Error(response.message || '获取折扣箱子失败')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '获取折扣箱子失败'
        this.errors.discountCases = errorMessage
        // console.error('[CaseStore] 获取折扣箱子失败:', error)
        return []
      } finally {
        this.loading.discountCases = false
      }
    },

    // 获取箱子详情
    async fetchCaseDetail(caseKey: string, force = false) {
      // 检查缓存
      if (!force && this.isCacheValid(caseKey, 'details') && this.caseDetails[caseKey]) {
        // console.log(`[CaseStore] 使用缓存的箱子详情: ${caseKey}`)
        return this.caseDetails[caseKey]
      }

      try {
        this.loading.details[caseKey] = true
        this.errors.details[caseKey] = null

        // console.log(`[CaseStore] 获取箱子详情: ${caseKey}`)
        const response = await caseApi.getCaseDetail(caseKey)
        
        if (response.success && response.data) {
          // 转换数据类型以匹配我们的接口
          const caseDetail: CaseDetailData = {
            id: parseInt(response.data.id as string) || 0,
            name: response.data.name,
            name_en: response.data.name_en,
            name_zh_hans: response.data.name_zh_hans,
            case_key: response.data.case_key,
            cover: response.data.cover,
            price: response.data.price,
            discount: response.data.discount,
            tag: response.data.tag,
            tag_en: response.data.tag_en,
            tag_zh_hans: response.data.tag_zh_hans,
            open_count: response.data.open_count || 0,
            type_name: response.data.description,
            type_name_en: response.data.description_en,
            type_name_zh_hans: response.data.description_zh_hans
          }
          
          this.caseDetails[caseKey] = caseDetail
          this.cacheTimestamps.details[caseKey] = Date.now()
          // console.log(`[CaseStore] 箱子详情获取成功: ${caseKey}`)
          return caseDetail
        } else {
          throw new Error(response.message || '获取箱子详情失败')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '获取箱子详情失败'
        this.errors.details[caseKey] = errorMessage
        // console.error(`[CaseStore] 获取箱子详情失败: ${caseKey}`, error)
        throw error
      } finally {
        this.loading.details[caseKey] = false
      }
    },

    // 获取箱子物品
    async fetchCaseItems(caseKey: string, force = false) {
      // 检查缓存
      if (!force && this.isCacheValid(caseKey, 'items') && this.caseItems[caseKey]?.length) {
        // console.log(`[CaseStore] 使用缓存的箱子物品: ${caseKey}`)
        return this.caseItems[caseKey]
      }

      try {
        this.loading.items[caseKey] = true
        this.errors.items[caseKey] = null

        // console.log(`[CaseStore] 获取箱子物品: ${caseKey}`)
        const response = await caseApi.getCaseSkins(caseKey)
        
        if (response.code === 0 && response.body) {
          // 后端修正：get_box_skin_list 现在直接返回物品数组，不再包装在 items 字段中
          const items = response.body.items || response.body
          this.caseItems[caseKey] = items
          this.cacheTimestamps.items[caseKey] = Date.now()
          // console.log(`[CaseStore] 箱子物品获取成功: ${caseKey}, 稀有度组数量: ${items.length}`)
          return items
        } else {
          throw new Error(response.message || '获取箱子物品失败')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '获取箱子物品失败'
        this.errors.items[caseKey] = errorMessage
        // console.error(`[CaseStore] 获取箱子物品失败: ${caseKey}`, error)
        throw error
      } finally {
        this.loading.items[caseKey] = false
      }
    },

    // 获取开箱记录
    async fetchOpeningRecords(caseKey: string, force = false) {
      // 检查缓存
      if (!force && this.isCacheValid(caseKey, 'records') && this.openingRecords[caseKey]?.length) {
        // console.log(`[CaseStore] 使用缓存的开箱记录: ${caseKey}`)
        return this.openingRecords[caseKey]
      }

      try {
        this.loading.records[caseKey] = true
        this.errors.records[caseKey] = null

        // console.log(`[CaseStore] 获取开箱记录: ${caseKey}`)
        const response = await caseApi.getCaseRecords(caseKey)
        
        if (response.code === 0 && response.body) {
          // 后端修正：get_case_activity_list 现在直接返回记录数组，不再包装在 items 字段中
          const records = response.body.items || response.body
          this.openingRecords[caseKey] = records
          this.cacheTimestamps.records[caseKey] = Date.now()
          // console.log(`[CaseStore] 开箱记录获取成功: ${caseKey}, 记录数量: ${records.length}`)
          return records
        } else {
          // 开箱记录失败不抛出错误，只是设置空数组
          this.openingRecords[caseKey] = []
          this.cacheTimestamps.records[caseKey] = Date.now()
          // console.warn(`[CaseStore] 开箱记录获取失败: ${caseKey}`, response.message)
          return []
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '获取开箱记录失败'
        this.errors.records[caseKey] = errorMessage
        this.openingRecords[caseKey] = []
        // console.error(`[CaseStore] 获取开箱记录失败: ${caseKey}`, error)
        return []
      } finally {
        this.loading.records[caseKey] = false
      }
    },

    // Setter方法 - 用于SSR等场景直接设置数据
    setHotCases(data: CaseDetailData[]) {
      this.hotCases = data
      this.cacheTimestamps.hotCases = Date.now()
      this.errors.hotCases = null
      // console.log(`[CaseStore] 设置热门箱子数据: ${data.length}个`)
    },

    setNewCases(data: CaseDetailData[]) {
      this.newCases = data
      this.cacheTimestamps.newCases = Date.now()
      this.errors.newCases = null
      // console.log(`[CaseStore] 设置新箱子数据: ${data.length}个`)
    },

    setDiscountCases(data: CaseDetailData[]) {
      this.discountCases = data
      this.cacheTimestamps.discountCases = Date.now()
      this.errors.discountCases = null
      // console.log(`[CaseStore] 设置折扣箱子数据: ${data.length}个`)
    },

    setAllCases(data: CaseCategoryData[]) {
      this.allCases = data
      this.cacheTimestamps.allCases = Date.now()
      this.errors.allCases = null
      // console.log(`[CaseStore] 设置箱子分类数据: ${data.length}个分类`)
    },

    setCaseDetail(caseKey: string, data: CaseDetailData) {
      this.caseDetails[caseKey] = data
      this.cacheTimestamps.details[caseKey] = Date.now()
      this.errors.details[caseKey] = null
      // console.log(`[CaseStore] 设置箱子详情: ${caseKey}`)
    },

    setCaseItems(caseKey: string, data: CaseRarityGroup[]) {
      this.caseItems[caseKey] = data
      this.cacheTimestamps.items[caseKey] = Date.now()
      this.errors.items[caseKey] = null
      // console.log(`[CaseStore] 设置箱子物品: ${caseKey}, 稀有度组数量: ${data.length}`)
    },

    setOpeningRecords(caseKey: string, data: OpeningRecord[]) {
      this.openingRecords[caseKey] = data
      this.cacheTimestamps.records[caseKey] = Date.now()
      this.errors.records[caseKey] = null
      // console.log(`[CaseStore] 设置开箱记录: ${caseKey}, 记录数量: ${data.length}`)
    },

    // Socket消息处理：更新开箱次数
    updateOpenCount(caseKey: string, newCount: number) {
      if (this.caseDetails[caseKey]) {
        this.caseDetails[caseKey].open_count = newCount
        // console.log(`[CaseStore] Socket更新开箱次数: ${caseKey} -> ${newCount}`)
      }
    },

    // Socket消息处理：添加新的开箱记录
    addOpeningRecord(caseKey: string, record: OpeningRecord) {
      if (!this.openingRecords[caseKey]) {
        this.openingRecords[caseKey] = []
      }
      
      // 检查记录是否已存在（防止重复）
      const existingRecord = this.openingRecords[caseKey].find(r => r.id === record.id)
      if (existingRecord) {
        // console.log(`[CaseStore] 开箱记录已存在，跳过: ${caseKey} - ${record.id}`)
        return
      }
      
      // 添加到开头，保持最新记录在前
      this.openingRecords[caseKey].unshift(record)
      
      // 限制记录数量，保留最新的50条
      if (this.openingRecords[caseKey].length > 50) {
        this.openingRecords[caseKey] = this.openingRecords[caseKey].slice(0, 50)
      }
      
      // console.log(`[CaseStore] Socket添加开箱记录: ${caseKey}, 当前记录数: ${this.openingRecords[caseKey].length}`)
    },

    // Socket消息处理：处理新开箱消息
    handleSocketCaseOpened(socketData: any) {
      try {
        // 从Socket消息中提取数据
        if (socketData.case_info && socketData.item_info) {
          const caseKey = socketData.case_info.case_key
          
          if (caseKey) {
            // 更新开箱次数
            if (socketData.case_info.open_count) {
              this.updateOpenCount(caseKey, socketData.case_info.open_count)
            }
            
            // 添加开箱记录
            const record: OpeningRecord = {
              id: socketData.id,
              uid: socketData.uid,
              create_time: socketData.create_time,
              update_time: socketData.update_time,
              price: socketData.price,
              user_info: socketData.user_info,
              case_info: socketData.case_info,
              item_info: socketData.item_info
            }
            
            this.addOpeningRecord(caseKey, record)
          }
        }
      } catch (error) {
        // console.error('[CaseStore] 处理Socket开箱消息失败:', error, socketData)
      }
    },

    // 清除指定箱子的缓存
    clearCaseCache(caseKey: string) {
      delete this.caseDetails[caseKey]
      delete this.caseItems[caseKey]
      delete this.openingRecords[caseKey]
      delete this.cacheTimestamps.details[caseKey]
      delete this.cacheTimestamps.items[caseKey]
      delete this.cacheTimestamps.records[caseKey]
      delete this.errors.details[caseKey]
      delete this.errors.items[caseKey]
      delete this.errors.records[caseKey]
      // console.log(`[CaseStore] 清除箱子缓存: ${caseKey}`)
    },

    // 清除所有缓存
    clearAllCache() {
      this.$reset()
      // console.log('[CaseStore] 清除所有缓存')
    },

    // 初始化箱子数据（SSR友好）
    async initializeCaseData(caseKey: string) {
      try {
        // console.log(`[CaseStore] 初始化箱子数据: ${caseKey}`)
        
        // 并行获取箱子详情和物品数据
        const [detail, items] = await Promise.all([
          this.fetchCaseDetail(caseKey),
          this.fetchCaseItems(caseKey)
        ])
        
        // 获取开箱记录（非关键数据，失败不影响主要功能）
        this.fetchOpeningRecords(caseKey).catch(error => {
          // console.warn(`[CaseStore] 获取开箱记录失败，但不影响主要功能: ${caseKey}`, error)
        })
        
        return { detail, items }
      } catch (error) {
        // console.error(`[CaseStore] 初始化箱子数据失败: ${caseKey}`, error)
        throw error
      }
    },

    // 设置Socket监听器
    setupSocketListeners() {
      if (typeof window === 'undefined') return

      // console.log('[CaseStore] 设置Socket监听器')

      // 监听新开箱记录事件
      window.addEventListener('socket-box', (event: Event) => {
        const data = (event as CustomEvent).detail?.data
        if (data) {
          this.handleSocketCaseOpened(data)
        }
      })

      // 监听开箱记录数组更新事件
      window.addEventListener('socket-case-records', (event: Event) => {
        const data = (event as CustomEvent).detail?.data
        if (Array.isArray(data) && data.length > 0) {
          // 如果是数组，说明是批量更新，暂时不处理，保持现有缓存
          // console.log('[CaseStore] 收到开箱记录批量更新，保持现有缓存')
        }
      })

      // 监听统计数据更新（包含开箱次数）
      window.addEventListener('socket-monitor', (event: Event) => {
        const data = (event as CustomEvent).detail
        if (data && data.case_number) {
          // 全局统计数据更新，这里可以做一些额外处理
          // console.log('[CaseStore] 全局开箱次数更新:', data.case_number)
        }
      })
    }
  }
})

// 导出兼容别名
export const useCaseDetailStore = useCaseStore 