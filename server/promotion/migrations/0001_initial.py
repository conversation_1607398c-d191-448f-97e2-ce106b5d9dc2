# -*- coding: utf-8 -*-
# Generated by Django 1.11.13 on 2021-04-14 02:09
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import promotion.models
import steambase.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('box', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODELL),
    ]

    operations = [
        migrations.CreateModel(
            name='PromotionCaseConfig',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('case', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='promotion_case', to='box.Case', verbose_name='case')),
            ],
            options={
                'verbose_name': 'PromotionCaseConfig',
                'verbose_name_plural': 'PromotionCaseConfig',
                'ordering': ('level',),
            },
        ),
        migrations.CreateModel(
            name='PromotionLevelConfig',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('level', models.IntegerField(default=0, verbose_name='level')),
                ('profit_rate', models.FloatField(default=0, verbose_name='profit rate(%)')),
                ('refer_rate', models.FloatField(default=0, verbose_name='refer rate(%)')),
                ('min_amount', models.FloatField(default=0, verbose_name='min amount')),
                ('max_amount', models.FloatField(default=0, verbose_name='max amount')),
            ],
            options={
                'verbose_name': 'Promotion Level Config',
                'verbose_name_plural': 'Promotion Level Config',
                'ordering': ('level',),
            },
        ),
        migrations.CreateModel(
            name='PromotionRecord',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('total_charge', models.FloatField(default=0.0, verbose_name='total charge')),
                ('profit', models.FloatField(default=0.0, verbose_name='refer profit')),
                ('active', models.BooleanField(default=False, verbose_name='active')),
            ],
            options={
                'verbose_name': 'Promotion Record',
                'verbose_name_plural': 'Promotion Record',
                'ordering': ('-create_time',),
            },
        ),
        migrations.CreateModel(
            name='UserPromotion',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(default=promotion.models.ref_generator, max_length=32, verbose_name='refer code')),
                ('count', models.IntegerField(default=0, verbose_name='refer count')),
                ('total_charge', models.FloatField(default=0.0, verbose_name='total charge')),
                ('total_profit', models.FloatField(default=0.0, verbose_name='total profit')),
                ('profit', models.FloatField(default=0.0, verbose_name='profit')),
                ('custom', models.BooleanField(default=False, verbose_name='enable custom')),
                ('level', models.IntegerField(default=0, verbose_name='refer level')),
                ('profit_rate', models.FloatField(default=0, verbose_name='profit rate(%)')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='promotion', to=settings.AUTH_USER_MODELL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'User Promotion',
                'verbose_name_plural': 'User Promotion',
            },
        ),
        migrations.AddField(
            model_name='promotionrecord',
            name='ref',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='records', to='promotion.UserPromotion', verbose_name='refer'),
        ),
        migrations.AddField(
            model_name='promotionrecord',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='promotion_records', to=settings.AUTH_USER_MODELL, verbose_name='user'),
        ),
        migrations.AddField(
            model_name='promotioncaseconfig',
            name='level',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='promotion_level', to='promotion.PromotionLevelConfig'),
        ),
    ]
