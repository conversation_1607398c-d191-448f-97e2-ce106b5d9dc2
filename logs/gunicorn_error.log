--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:14:45:30 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1465, 'M': 1, 'L': '0.001465', 'p': '<15>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd0179a8b0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd0179a970>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd01794a60 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '51982', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:14:46:01 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1505, 'M': 1, 'L': '0.001505', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd01a6c340>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd01ad4370>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd016b2460 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '38662', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:14:46:31 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1546, 'M': 1, 'L': '0.001546', 'p': '<14>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd02061bb0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd02061070>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd017684c0 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '41068', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:14:47:01 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1545, 'M': 1, 'L': '0.001545', 'p': '<14>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd02078ca0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd0206ab80>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd01768520 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '47446', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:14:47:31 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1479, 'M': 1, 'L': '0.001479', 'p': '<15>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd01ba0940>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd01ba0730>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd01794be0 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '34188', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:14:48:01 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1469, 'M': 1, 'L': '0.001469', 'p': '<15>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd017acbe0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd017ac3d0>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd01794b20 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '35704', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:14:48:31 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1434, 'M': 1, 'L': '0.001434', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd015d9a00>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd015d94f0>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd016b2c40 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '46526', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:14:49:01 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1439, 'M': 1, 'L': '0.001439', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd015d10a0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd015d1ee0>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd016b22e0 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '42294', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:14:49:31 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1492, 'M': 1, 'L': '0.001492', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd02060040>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd01633c10>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd016b2700 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '43622', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:14:50:01 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 13763, 'M': 13, 'L': '0.013763', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd0202bee0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd0202b100>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd015f94c0 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '57736', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:14:50:31 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1457, 'M': 1, 'L': '0.001457', 'p': '<17>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd01652790>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd01652ac0>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd016616a0 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '53890', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:14:51:01 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1514, 'M': 1, 'L': '0.001514', 'p': '<15>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd017ac490>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd017ac790>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd017bd760 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '55552', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:14:51:31 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1462, 'M': 1, 'L': '0.001462', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd016339d0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd016716d0>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd016b2be0 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '41660', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:14:52:02 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1852, 'M': 1, 'L': '0.001852', 'p': '<15>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd017a36a0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd017a3130>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd017bd7c0 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '49700', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:14:52:32 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1407, 'M': 1, 'L': '0.001407', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd015d13d0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd015d1be0>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd016b22e0 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '48096', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:14:53:02 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1958, 'M': 1, 'L': '0.001958', 'p': '<17>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd01662940>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd01662a30>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd016617c0 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '37636', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:14:53:32 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1449, 'M': 1, 'L': '0.001449', 'p': '<17>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd023ad6a0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd01abc580>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd0163e100 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '47718', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:14:54:02 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1522, 'M': 1, 'L': '0.001522', 'p': '<14>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd01c05a00>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd01c05190>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd01768280 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '55926', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:14:54:32 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1429, 'M': 1, 'L': '0.001429', 'p': '<15>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd017acdc0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd017ac5e0>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd01794b20 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '49528', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:14:55:02 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1424, 'M': 1, 'L': '0.001424', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd015bdac0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd015d9640>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd016b2c40 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '56098', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:14:55:32 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1304, 'M': 1, 'L': '0.001304', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd02064a90>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd020405b0>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd016b2460 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '54172', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:14:56:02 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1907, 'M': 1, 'L': '0.001907', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd015ecdf0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd01585e80>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd016b2820 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '48164', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:14:56:32 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1404, 'M': 1, 'L': '0.001404', 'p': '<15>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd01ba0640>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd01ba0ac0>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd01794a00 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '36308', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:14:57:02 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1460, 'M': 1, 'L': '0.001460', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd02031640>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd015f2250>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd016b28e0 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '34036', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:14:57:32 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1547, 'M': 1, 'L': '0.001547', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd015f0850>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd015f0100>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd01602a60 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '32920', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:14:58:03 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1794, 'M': 1, 'L': '0.001794', 'p': '<14>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd01ff5580>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd01ff5670>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd017689a0 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '43210', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:14:58:33 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1855, 'M': 1, 'L': '0.001855', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd0200ebb0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd0200e2b0>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd015f9040 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '36358', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:14:59:03 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1353, 'M': 1, 'L': '0.001353', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd015e72e0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd015e7730>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd01602e80 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '55060', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:14:59:33 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1570, 'M': 1, 'L': '0.001570', 'p': '<14>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd01f7aac0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd01c269a0>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd019921c0 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '40892', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:00:04 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1451, 'M': 1, 'L': '0.001451', 'p': '<15>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd0179a430>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd0179a580>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd01794940 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '43230', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:00:34 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1335, 'M': 1, 'L': '0.001335', 'p': '<17>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd01c9d730>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd0238aa90>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd0163efa0 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '59986', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:01:04 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1061, 'M': 1, 'L': '0.001061', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd02007340>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd02007d30>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd016b2ee0 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '59146', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:01:34 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1236, 'M': 1, 'L': '0.001236', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd01fc6af0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd01fc6ee0>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd016b2340 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '40980', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:02:04 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1061, 'M': 1, 'L': '0.001061', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd0206a100>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd0206af40>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd016b2100 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '48124', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:02:34 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1475, 'M': 1, 'L': '0.001475', 'p': '<15>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd0179af40>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd0179ac40>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd01794d60 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '56986', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:03:04 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1139, 'M': 1, 'L': '0.001139', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd015d9be0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd015d93a0>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd016b2d60 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '43194', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:03:34 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1050, 'M': 1, 'L': '0.001050', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd0206e880>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd015d1250>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd016b26a0 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '49550', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:04:04 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1356, 'M': 1, 'L': '0.001356', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd015d1eb0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd02060040>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd016b24c0 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '36146', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:04:34 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1599, 'M': 1, 'L': '0.001599', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd015f5580>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd015f5670>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd015f9c40 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '33732', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:05:04 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1446, 'M': 1, 'L': '0.001446', 'p': '<15>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd01be1d00>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd01c2fc70>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd01794e80 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '33878', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '************', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:05:04 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.61.1', 'T': 0, 'D': 1423, 'M': 1, 'L': '0.001423', 'p': '<14>', '{host}i': '************:8000', '{user-agent}i': 'curl/7.61.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd01fe0700>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd02078070>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd01905340 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': '************:8000', '{http_user_agent}e': 'curl/7.61.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '************', '{remote_port}e': '42764', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:05:34 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1516, 'M': 1, 'L': '0.001516', 'p': '<15>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd0179d850>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd0179d640>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd01794fa0 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '56556', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:06:04 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1099, 'M': 1, 'L': '0.001099', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd01fd0fa0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd02001f10>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd01602e80 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '53104', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:06:34 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 19843, 'M': 19, 'L': '0.019843', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd0200efd0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd0200e3a0>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd01602ca0 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '55600', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:07:04 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1029, 'M': 1, 'L': '0.001029', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd02027f70>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd02013a30>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd01602760 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '43448', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:07:34 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1043, 'M': 1, 'L': '0.001043', 'p': '<17>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd0164d280>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd0164d670>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd0163ef40 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '53792', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:08:04 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1510, 'M': 1, 'L': '0.001510', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd015f0250>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd015f0940>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd018a2100 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '40536', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:08:34 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1057, 'M': 1, 'L': '0.001057', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd0160aee0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd015e7a60>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd01602be0 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '44002', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:09:04 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1578, 'M': 1, 'L': '0.001578', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd015e7f40>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd015e76d0>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd015eda00 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '36920', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:09:35 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1041, 'M': 1, 'L': '0.001041', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd020648b0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd01ab6e80>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd015c3580 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '48578', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:10:05 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1649, 'M': 1, 'L': '0.001649', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd015f8bb0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd015f8880>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd015c39a0 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '36870', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:10:35 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1063, 'M': 1, 'L': '0.001063', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd01ab6c40>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd02064a90>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd018a2100 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '44412', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:11:05 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1059, 'M': 1, 'L': '0.001059', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd0160aee0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd015e72b0>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd015eda60 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '50874', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:11:35 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1055, 'M': 1, 'L': '0.001055', 'p': '<14>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd020651c0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd02065310>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd01768280 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '60818', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:12:05 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1041, 'M': 1, 'L': '0.001041', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd015e7490>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd015e7e80>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd016028e0 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '44916', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:12:35 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1100, 'M': 1, 'L': '0.001100', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd015f0070>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd015f0250>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd01602700 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '59902', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:13:05 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1088, 'M': 1, 'L': '0.001088', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd02013df0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd020270a0>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd01602ee0 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '37730', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:13:35 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1340, 'M': 1, 'L': '0.001340', 'p': '<14>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd02008dc0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd02008a90>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd017688e0 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '48358', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:14:05 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1053, 'M': 1, 'L': '0.001053', 'p': '<17>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd0164dfd0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd01652f70>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd0163ed60 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '50118', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:14:35 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1044, 'M': 1, 'L': '0.001044', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd0200e9d0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd0200e2b0>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd01602a60 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '58732', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 107, in handle_request
    respiter = self.wsgi(environ, resp.start_response)
  File "/usr/local/lib/python3.8/site-packages/django/core/handlers/wsgi.py", line 133, in __call__
    response = self.get_response(request)
  File "/usr/local/lib/python3.8/site-packages/django/core/handlers/base.py", line 133, in get_response
    log_response(
  File "/usr/local/lib/python3.8/site-packages/django/utils/log.py", line 224, in log_response
    getattr(logger, level)(
Message: '%s: %s'
Arguments: ('Not Found', '/api/box/case/list/')
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '************', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:14:54 +0800]', 'r': 'GET /api/box/case/list/?tag=&num=10 HTTP/1.1', 's': '404', 'm': 'GET', 'U': '/api/box/case/list/', 'q': 'tag=&num=10', 'H': 'HTTP/1.1', 'b': '1172', 'B': 1172, 'f': '-', 'a': 'curl/7.61.1', 'T': 0, 'D': 2435, 'M': 2, 'L': '0.002435', 'p': '<16>', '{host}i': '************:8000', '{user-agent}i': 'curl/7.61.1', '{accept}i': '*/*', '{content-type}o': 'text/html; charset=utf-8', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '1172', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd02001610>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd015f8e50>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd015c3580 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': 'tag=&num=10', '{raw_uri}e': '/api/box/case/list/?tag=&num=10', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': '************:8000', '{http_user_agent}e': 'curl/7.61.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '************', '{remote_port}e': '58102', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/box/case/list/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:15:05 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1454, 'M': 1, 'L': '0.001454', 'p': '<15>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd0179dc10>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd0179dd00>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd017bd760 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '52850', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 107, in handle_request
    respiter = self.wsgi(environ, resp.start_response)
  File "/usr/local/lib/python3.8/site-packages/django/core/handlers/wsgi.py", line 133, in __call__
    response = self.get_response(request)
  File "/usr/local/lib/python3.8/site-packages/django/core/handlers/base.py", line 133, in get_response
    log_response(
  File "/usr/local/lib/python3.8/site-packages/django/utils/log.py", line 224, in log_response
    getattr(logger, level)(
Message: '%s: %s'
Arguments: ('Not Found', '/api/box/case/list/')
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '************', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:15:15 +0800]', 'r': 'GET /api/box/case/list/?tag=&num=10 HTTP/1.1', 's': '404', 'm': 'GET', 'U': '/api/box/case/list/', 'q': 'tag=&num=10', 'H': 'HTTP/1.1', 'b': '1172', 'B': 1172, 'f': '-', 'a': 'curl/7.61.1', 'T': 0, 'D': 30670, 'M': 30, 'L': '0.030670', 'p': '<14>', '{host}i': '************:8000', '{user-agent}i': 'curl/7.61.1', '{accept}i': 'application/json', '{content-type}o': 'text/html; charset=utf-8', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '1172', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd0201e310>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd0201e4f0>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd017687c0 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': 'tag=&num=10', '{raw_uri}e': '/api/box/case/list/?tag=&num=10', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': '************:8000', '{http_user_agent}e': 'curl/7.61.1', '{http_accept}e': 'application/json', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '************', '{remote_port}e': '44308', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/box/case/list/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 107, in handle_request
    respiter = self.wsgi(environ, resp.start_response)
  File "/usr/local/lib/python3.8/site-packages/django/core/handlers/wsgi.py", line 133, in __call__
    response = self.get_response(request)
  File "/usr/local/lib/python3.8/site-packages/django/core/handlers/base.py", line 133, in get_response
    log_response(
  File "/usr/local/lib/python3.8/site-packages/django/utils/log.py", line 224, in log_response
    getattr(logger, level)(
Message: '%s: %s'
Arguments: ('Not Found', '/api/box/case/list/')
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '************', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:15:15 +0800]', 'r': 'GET /api/box/case/list/?tag=&num=10 HTTP/1.1', 's': '404', 'm': 'GET', 'U': '/api/box/case/list/', 'q': 'tag=&num=10', 'H': 'HTTP/1.1', 'b': '1172', 'B': 1172, 'f': '-', 'a': 'curl/7.61.1', 'T': 0, 'D': 2300, 'M': 2, 'L': '0.002300', 'p': '<15>', '{host}i': '************:8000', '{user-agent}i': 'curl/7.61.1', '{accept}i': '*/*', '{content-type}o': 'text/html; charset=utf-8', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '1172', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd020b7df0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd0205dcd0>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd017bdac0 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': 'tag=&num=10', '{raw_uri}e': '/api/box/case/list/?tag=&num=10', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': '************:8000', '{http_user_agent}e': 'curl/7.61.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '************', '{remote_port}e': '44316', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/box/case/list/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:15:35 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1045, 'M': 1, 'L': '0.001045', 'p': '<15>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd0179db50>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd0179db20>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd017bdd00 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '34520', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:16:05 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1100, 'M': 1, 'L': '0.001100', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd02007430>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd02007970>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd01602a60 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '44972', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:16:35 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1061, 'M': 1, 'L': '0.001061', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd01fc2580>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd0202bfd0>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd016027c0 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '33460', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:17:05 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1065, 'M': 1, 'L': '0.001065', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd01633a00>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd015f0f70>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd01602ca0 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '58824', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:17:35 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1524, 'M': 1, 'L': '0.001524', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd015f0820>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd015f04c0>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd018a2100 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '57282', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:18:05 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1472, 'M': 1, 'L': '0.001472', 'p': '<15>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd01f3ceb0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd022e1f70>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd017bd820 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '50634', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:18:35 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1051, 'M': 1, 'L': '0.001051', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd015e7eb0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd015e7130>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd01602820 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '34356', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:19:05 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1404, 'M': 1, 'L': '0.001404', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd016712b0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd01ffd5b0>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd015edac0 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '34172', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:19:35 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1088, 'M': 1, 'L': '0.001088', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd01ab6a30>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd01ab6d30>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd015c3460 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '40058', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:20:05 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1518, 'M': 1, 'L': '0.001518', 'p': '<15>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd01bd8670>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd01bd83d0>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd01794e80 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '45162', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:20:35 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1468, 'M': 1, 'L': '0.001468', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd015ec160>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd015ec880>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd015c34c0 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '54448', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:21:05 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1042, 'M': 1, 'L': '0.001042', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd01585e80>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd01ab6fa0>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd015c32e0 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '32826', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:21:36 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1057, 'M': 1, 'L': '0.001057', 'p': '<15>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd0179a970>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd0179a370>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd01794d60 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '53010', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:22:06 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1068, 'M': 1, 'L': '0.001068', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd01ffd8b0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd01ffd0a0>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd015eda60 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '37410', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:22:36 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1087, 'M': 1, 'L': '0.001087', 'p': '<14>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd01fa4fa0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd022b6df0>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd01768640 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '36300', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:23:06 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1033, 'M': 1, 'L': '0.001033', 'p': '<15>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd01ba08b0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd01ba0850>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd01794940 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '36340', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:23:36 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1074, 'M': 1, 'L': '0.001074', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd015e7cd0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd015e75b0>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd015c3460 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '40016', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:24:06 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1109, 'M': 1, 'L': '0.001109', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd015f00a0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd015f0a30>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd01602820 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '54868', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:24:36 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1136, 'M': 1, 'L': '0.001136', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd015f0c70>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd015f07c0>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd01602760 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '37046', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:25:06 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1060, 'M': 1, 'L': '0.001060', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd0202bdf0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd01fc2760>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd01602880 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '52726', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 107, in handle_request
    respiter = self.wsgi(environ, resp.start_response)
  File "/usr/local/lib/python3.8/site-packages/django/core/handlers/wsgi.py", line 133, in __call__
    response = self.get_response(request)
  File "/usr/local/lib/python3.8/site-packages/django/core/handlers/base.py", line 133, in get_response
    log_response(
  File "/usr/local/lib/python3.8/site-packages/django/utils/log.py", line 224, in log_response
    getattr(logger, level)(
Message: '%s: %s'
Arguments: ('Not Found', '/')
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '***************', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:25:22 +0800]', 'r': 'GET / HTTP/1.1', 's': '404', 'm': 'GET', 'U': '/', 'q': '', 'H': 'HTTP/1.1', 'b': '1172', 'B': 1172, 'f': '-', 'a': 'Mozilla/5.0 (Linux; Android 9; ASUS_X00TD; Flow) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/359.0.0.288 Mobile Safari/537.36', 'T': 0, 'D': 2027, 'M': 2, 'L': '0.002027', 'p': '<15>', '{user-agent}i': 'Mozilla/5.0 (Linux; Android 9; ASUS_X00TD; Flow) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/359.0.0.288 Mobile Safari/537.36', '{accept-encoding}i': 'gzip, x-gzip, deflate', '{host}i': 'owmfnfjpepwww.z.cs2.net.cn:8000', '{connection}i': 'keep-alive', '{content-type}o': 'text/html; charset=utf-8', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '1172', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd017a3400>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd017a39d0>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd01794a00 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/', '{server_protocol}e': 'HTTP/1.1', '{http_user_agent}e': 'Mozilla/5.0 (Linux; Android 9; ASUS_X00TD; Flow) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/359.0.0.288 Mobile Safari/537.36', '{http_accept_encoding}e': 'gzip, x-gzip, deflate', '{http_host}e': 'owmfnfjpepwww.z.cs2.net.cn:8000', '{http_connection}e': 'keep-alive', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '***************', '{remote_port}e': '22900', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:25:36 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1070, 'M': 1, 'L': '0.001070', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd02007160>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd02007430>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd01602580 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '40052', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 107, in handle_request
    respiter = self.wsgi(environ, resp.start_response)
  File "/usr/local/lib/python3.8/site-packages/django/core/handlers/wsgi.py", line 133, in __call__
    response = self.get_response(request)
  File "/usr/local/lib/python3.8/site-packages/django/core/handlers/base.py", line 133, in get_response
    log_response(
  File "/usr/local/lib/python3.8/site-packages/django/utils/log.py", line 224, in log_response
    getattr(logger, level)(
Message: '%s: %s'
Arguments: ('Not Found', '/')
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '***************', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:25:48 +0800]', 'r': 'GET / HTTP/1.1', 's': '404', 'm': 'GET', 'U': '/', 'q': '', 'H': 'HTTP/1.1', 'b': '1172', 'B': 1172, 'f': '-', 'a': 'Mozilla/5.0 (Linux; Android 9; ASUS_X00TD; Flow) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/359.0.0.288 Mobile Safari/537.36', 'T': 0, 'D': 2421, 'M': 2, 'L': '0.002421', 'p': '<15>', '{user-agent}i': 'Mozilla/5.0 (Linux; Android 9; ASUS_X00TD; Flow) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/359.0.0.288 Mobile Safari/537.36', '{accept-encoding}i': 'gzip, x-gzip, deflate', '{host}i': 'owmfnfjpepwww.z.cs2.net.cn:8000', '{connection}i': 'keep-alive', '{content-type}o': 'text/html; charset=utf-8', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '1172', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd017ac5e0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd017acf70>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd01794ac0 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/', '{server_protocol}e': 'HTTP/1.1', '{http_user_agent}e': 'Mozilla/5.0 (Linux; Android 9; ASUS_X00TD; Flow) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/359.0.0.288 Mobile Safari/537.36', '{http_accept_encoding}e': 'gzip, x-gzip, deflate', '{http_host}e': 'owmfnfjpepwww.z.cs2.net.cn:8000', '{http_connection}e': 'keep-alive', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '***************', '{remote_port}e': '15683', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 107, in handle_request
    respiter = self.wsgi(environ, resp.start_response)
  File "/usr/local/lib/python3.8/site-packages/django/core/handlers/wsgi.py", line 133, in __call__
    response = self.get_response(request)
  File "/usr/local/lib/python3.8/site-packages/django/core/handlers/base.py", line 133, in get_response
    log_response(
  File "/usr/local/lib/python3.8/site-packages/django/utils/log.py", line 224, in log_response
    getattr(logger, level)(
Message: '%s: %s'
Arguments: ('Not Found', '/')
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '***************', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:25:54 +0800]', 'r': 'GET / HTTP/1.1', 's': '404', 'm': 'GET', 'U': '/', 'q': '', 'H': 'HTTP/1.1', 'b': '1172', 'B': 1172, 'f': '-', 'a': 'Mozilla/5.0 (Linux; Android 9; ASUS_X00TD; Flow) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/359.0.0.288 Mobile Safari/537.36', 'T': 0, 'D': 1999, 'M': 1, 'L': '0.001999', 'p': '<15>', '{user-agent}i': 'Mozilla/5.0 (Linux; Android 9; ASUS_X00TD; Flow) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/359.0.0.288 Mobile Safari/537.36', '{accept-encoding}i': 'gzip, x-gzip, deflate', '{host}i': 'owmfnfjpepwww.z.cs2.net.cn:8000', '{connection}i': 'keep-alive', '{content-type}o': 'text/html; charset=utf-8', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '1172', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd01f891f0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd017ac220>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd01794f40 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/', '{server_protocol}e': 'HTTP/1.1', '{http_user_agent}e': 'Mozilla/5.0 (Linux; Android 9; ASUS_X00TD; Flow) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/359.0.0.288 Mobile Safari/537.36', '{http_accept_encoding}e': 'gzip, x-gzip, deflate', '{http_host}e': 'owmfnfjpepwww.z.cs2.net.cn:8000', '{http_connection}e': 'keep-alive', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '***************', '{remote_port}e': '45365', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:26:06 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1066, 'M': 1, 'L': '0.001066', 'p': '<15>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd01ba08e0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd01ba06d0>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd01794940 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '35350', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:26:36 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1303, 'M': 1, 'L': '0.001303', 'p': '<14>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd01c07070>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd01b20eb0>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd016c0e20 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '37046', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:27:06 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1943, 'M': 1, 'L': '0.001943', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd015ecd60>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd015ec550>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd016b26a0 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '50322', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:27:36 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1083, 'M': 1, 'L': '0.001083', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd02031a60>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd02007e80>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd01602ee0 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '42700', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:28:06 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1048, 'M': 1, 'L': '0.001048', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd01fc2d90>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd01fc2b80>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd01602580 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '53762', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:28:36 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1068, 'M': 1, 'L': '0.001068', 'p': '<15>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd0179a370>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd0179a8e0>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd01794be0 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '36246', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:29:06 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1047, 'M': 1, 'L': '0.001047', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd016330d0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd01633e50>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd01602a60 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '45114', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:29:36 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1241, 'M': 1, 'L': '0.001241', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd015f0a30>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd015f0940>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd018a2100 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '34104', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:30:06 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1032, 'M': 1, 'L': '0.001032', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd015e7ac0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd015e7c40>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd01602ca0 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '39140', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:30:36 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1069, 'M': 1, 'L': '0.001069', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd02060c70>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd02060b80>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd015eda60 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '47200', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:31:06 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1073, 'M': 1, 'L': '0.001073', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd01ab6fa0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd01ab6f40>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd015c3ca0 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '34726', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:31:36 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1359, 'M': 1, 'L': '0.001359', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd015f2070>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd0206ea60>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd016b2d60 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '50442', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:32:06 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1064, 'M': 1, 'L': '0.001064', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd020597f0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd01ab6df0>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd015c31c0 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '38690', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:32:36 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1216, 'M': 1, 'L': '0.001216', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd01ffd9a0>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd01671af0>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd015edac0 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '38426', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
--- Logging error ---
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/logging/handlers.py", line 940, in emit
    self.socket.sendto(msg, self.address)
OSError: [Errno 9] Bad file descriptor
Call stack:
  File "/usr/local/lib/python3.8/site-packages/gevent/baseserver.py", line 34, in _handle_and_close_when_done
    return handle(*args_tuple)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 123, in handle
    super().handle(listener, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 54, in handle
    self.handle_request(listener_name, req, client, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/ggevent.py", line 127, in handle_request
    super().handle_request(listener_name, req, sock, addr)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/workers/base_async.py", line 119, in handle_request
    self.log.access(resp, req, environ, request_time)
  File "/usr/local/lib/python3.8/site-packages/gunicorn/glogging.py", line 362, in access
    self.access_log.info(self.cfg.access_log_format, safe_atoms)
Message: '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
Arguments: {'h': '127.0.0.1', 'l': '-', 'u': '-', 't': '[18/Jul/2025:15:33:06 +0800]', 'r': 'GET /api/health/ HTTP/1.1', 's': '200', 'm': 'GET', 'U': '/api/health/', 'q': '', 'H': 'HTTP/1.1', 'b': '96', 'B': 96, 'f': '-', 'a': 'curl/7.88.1', 'T': 0, 'D': 1044, 'M': 1, 'L': '0.001044', 'p': '<16>', '{host}i': 'localhost:8000', '{user-agent}i': 'curl/7.88.1', '{accept}i': '*/*', '{content-type}o': 'application/json', '{vary}o': 'Accept-Language, Cookie', '{content-language}o': 'zh', '{x-frame-options}o': 'DENY', '{content-length}o': '96', '{x-content-type-options}o': 'nosniff', '{referrer-policy}o': 'same-origin', '{wsgi.errors}e': <gunicorn.http.wsgi.WSGIErrorsWrapper object at 0x7fdd015e7a60>, '{wsgi.version}e': (1, 0), '{wsgi.multithread}e': True, '{wsgi.multiprocess}e': True, '{wsgi.run_once}e': False, '{wsgi.file_wrapper}e': <class 'gunicorn.http.wsgi.FileWrapper'>, '{wsgi.input_terminated}e': True, '{server_software}e': 'gunicorn/23.0.0', '{wsgi.input}e': <gunicorn.http.body.Body object at 0x7fdd015e79a0>, '{gunicorn.socket}e': <gevent._socket3.socket at 0x7fdd015c3ca0 object, fd=16, family=2, type=1, proto=6>, '{request_method}e': 'GET', '{query_string}e': '', '{raw_uri}e': '/api/health/', '{server_protocol}e': 'HTTP/1.1', '{http_host}e': 'localhost:8000', '{http_user_agent}e': 'curl/7.88.1', '{http_accept}e': '*/*', '{wsgi.url_scheme}e': 'http', '{remote_addr}e': '127.0.0.1', '{remote_port}e': '44454', '{server_name}e': '0.0.0.0', '{server_port}e': '8000', '{path_info}e': '/api/health/', '{script_name}e': ''}
