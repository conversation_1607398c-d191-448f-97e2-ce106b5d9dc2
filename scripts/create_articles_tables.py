#!/usr/bin/env python
"""
直接创建articles app的数据库表
"""
import os
import sys
import django
from django.db import connection

# 添加项目路径
sys.path.append('/www/wwwroot/csgoskins.com.cn/server')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')

# 初始化Django
django.setup()

def create_articles_tables():
    """创建articles app的数据库表"""
    
    sql_commands = [
        # 创建ContentCategory表
        """
        CREATE TABLE IF NOT EXISTS `articles_contentcategory` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `created_at` datetime(6) NOT NULL,
            `updated_at` datetime(6) NOT NULL,
            `name` varchar(128) NOT NULL,
            `slug` varchar(128) NOT NULL UNIQUE,
            `description` longtext,
            `icon` varchar(255),
            `color` varchar(7) NOT NULL DEFAULT '#007bff',
            `sort_order` int(11) NOT NULL DEFAULT 0,
            `is_active` tinyint(1) NOT NULL DEFAULT 1,
            PRIMARY KEY (`id`),
            KEY `articles_contentcategory_sort_order` (`sort_order`),
            KEY `articles_contentcategory_name` (`name`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        """,
        
        # 创建ContentTag表
        """
        CREATE TABLE IF NOT EXISTS `articles_contenttag` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `created_at` datetime(6) NOT NULL,
            `updated_at` datetime(6) NOT NULL,
            `name` varchar(64) NOT NULL,
            `slug` varchar(64) NOT NULL UNIQUE,
            `color` varchar(7) NOT NULL DEFAULT '#6c757d',
            `is_active` tinyint(1) NOT NULL DEFAULT 1,
            PRIMARY KEY (`id`),
            KEY `articles_contenttag_name` (`name`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        """,
        
        # 创建Content表（不带外键约束）
        """
        CREATE TABLE IF NOT EXISTS `articles_content` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `created_at` datetime(6) NOT NULL,
            `updated_at` datetime(6) NOT NULL,
            `title` varchar(255) NOT NULL,
            `slug` varchar(255) NOT NULL UNIQUE,
            `subtitle` varchar(255),
            `excerpt` longtext,
            `content` longtext,
            `content_type` varchar(20) NOT NULL DEFAULT 'article',
            `status` varchar(20) NOT NULL DEFAULT 'draft',
            `priority` varchar(20) NOT NULL DEFAULT 'normal',
            `is_featured` tinyint(1) NOT NULL DEFAULT 0,
            `is_pinned` tinyint(1) NOT NULL DEFAULT 0,
            `publish_date` datetime(6) NOT NULL,
            `expire_date` datetime(6),
            `featured_image` varchar(500),
            `thumbnail` varchar(500),
            `seo_title` varchar(255),
            `seo_description` longtext,
            `seo_keywords` varchar(255),
            `sort_order` int(11) NOT NULL DEFAULT 0,
            `view_count` int(10) unsigned NOT NULL DEFAULT 0,
            `allow_comments` tinyint(1) NOT NULL DEFAULT 1,
            `custom_css_class` varchar(255),
            `background_color` varchar(7),
            `text_color` varchar(7),
            `author_id` int(11),
            `category_id` int(11),
            `editor_id` int(11),
            PRIMARY KEY (`id`),
            KEY `articles_content_content_type_status` (`content_type`, `status`),
            KEY `articles_content_publish_date` (`publish_date`),
            KEY `articles_content_is_featured` (`is_featured`),
            KEY `articles_content_is_pinned` (`is_pinned`),
            KEY `articles_content_author_id` (`author_id`),
            KEY `articles_content_category_id` (`category_id`),
            KEY `articles_content_editor_id` (`editor_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        """,
        
        # 创建Content和Tag的多对多关系表（不带外键约束）
        """
        CREATE TABLE IF NOT EXISTS `articles_content_tags` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `content_id` int(11) NOT NULL,
            `contenttag_id` int(11) NOT NULL,
            PRIMARY KEY (`id`),
            UNIQUE KEY `articles_content_tags_content_id_contenttag_id` (`content_id`, `contenttag_id`),
            KEY `articles_content_tags_content_id` (`content_id`),
            KEY `articles_content_tags_contenttag_id` (`contenttag_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        """,
        
        # 创建ContentAttachment表（不带外键约束）
        """
        CREATE TABLE IF NOT EXISTS `articles_contentattachment` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `created_at` datetime(6) NOT NULL,
            `updated_at` datetime(6) NOT NULL,
            `name` varchar(255) NOT NULL,
            `file_path` varchar(500) NOT NULL,
            `file_size` int(10) unsigned NOT NULL,
            `file_type` varchar(100) NOT NULL,
            `description` longtext,
            `download_count` int(10) unsigned NOT NULL DEFAULT 0,
            `content_id` int(11) NOT NULL,
            PRIMARY KEY (`id`),
            KEY `articles_contentattachment_content_id` (`content_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        """,
        
        # 创建ContentView表（不带外键约束）
        """
        CREATE TABLE IF NOT EXISTS `articles_contentview` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `ip_address` char(39) NOT NULL,
            `user_agent` longtext,
            `viewed_at` datetime(6) NOT NULL,
            `content_id` int(11) NOT NULL,
            `user_id` int(11),
            PRIMARY KEY (`id`),
            UNIQUE KEY `articles_contentview_content_user_ip` (`content_id`, `user_id`, `ip_address`),
            KEY `articles_contentview_content_id` (`content_id`),
            KEY `articles_contentview_user_id` (`user_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        """,
        
        # 插入django_migrations记录
        """
        INSERT IGNORE INTO `django_migrations` (`app`, `name`, `applied`) 
        VALUES ('articles', '0001_initial', NOW());
        """
    ]
    
    with connection.cursor() as cursor:
        for sql in sql_commands:
            try:
                cursor.execute(sql)
                print(f"✓ 执行成功: {sql[:50]}...")
            except Exception as e:
                print(f"✗ 执行失败: {sql[:50]}...")
                print(f"  错误: {e}")
    
    print("\n✅ Articles app数据库表创建完成！")

if __name__ == '__main__':
    create_articles_tables()
