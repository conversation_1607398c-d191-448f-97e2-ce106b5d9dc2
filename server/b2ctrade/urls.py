from django.conf.urls import url

from b2ctrade import views



app_name = 'b2ctrade'
urlpatterns = [
    # url(r'^market/', views.GetMarketInventoryView.as_view()),
    # url(r'^withdraw/', views.UserWithdrawItemsView.as_view()),
    url(r'^withdraw/', views.UserWithdrawZBTItemView.as_view()),
    url(r'^batch/', views.UserBatchWithdrawItemsView.as_view()),
    # url(r'^cancel/', views.UserCancelItemsView.as_view()),
    url(r'^cancel/', views.UserCancelZBTItemView.as_view()),
    # url(r'^cancelcheck/', views.UserCancelZBTItemView.as_view()),
    url(r'^checkwithdraw/', views.CheckWithdraw.as_view()),
    url(r'^checktrade/', views.CheckTrade.as_view()),
    # url(r'^checktrade/', views.CheckZBTrade.as_view()),
    url(r'^record/', views.GetTradeRecordView.as_view()),
    # url(r'^updateb2citem/', views.UpdateB2cItemsView.as_view())
    url(r'^receive/', views.ReceiveZBTradeState.as_view()),
    # New API for ZBT
    url(r'^zbtrecord/', views.GetZBTTradeRecordView.as_view()),
    url(r'^zbtwithdraw/', views.UserWithdrawZBTView.as_view()),
    # 获取用户steam信息
    url(r'^usersteam/$', views.GetSteamInfoView.as_view()),

]