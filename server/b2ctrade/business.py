import json
import logging
import random
import requests
import threading
import time
import hashlib
import redis

import logging.handlers
from uuid import uuid1
from decimal import Decimal


from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.core.paginator import Paginator
from django.db import transaction, connection
from django.db.models import Count
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from datetime import datetime as dt
from datetime import timedelta

from libs.steamapi import SteamApi
from steambase.enums import RespCode, B2CTradeState, B2CItemType, ZBTradeState, PackageState, PackageSourceType, \
    ZBTradeSource
from steambase.utils import ParamException, is_connection_usable, pid_generator, id_generator
from steambase.redis_con import get_redis
from b2ctrade.models import B2CTradeRecord, B2CItemlist, B2COfficialAccount, ZBTBlackList
from b2ctrade.models import B2CMarketItem, ZBTradeRecord, B2CTradeStatisticsDay, B2CTradeStatisticsMonth
from b2ctrade.serializers import B2CTradeRecordSerializer, B2CItemlistSerializer
from b2ctrade.serializers import B2CMarketItemSerializer, ZBTradeRecordSerializer
from package.business import get_user_inventory
from package.models import ItemInfo, PackageItem, TradeRecord
from package.service.item import get_item_price
from sitecfg.interfaces import get_withdraw_count_limit, get_withdraw_price_limit, get_withdraw_total_charge_limit, get_maintenance_withdraw, get_maintenance
from sitecfg.interfaces import get_zbt_buy_max_rate, get_withdraw_delivery
from authentication.models import AuthUser, UserExtra, UserAsset

from charge.business import send_sms
from agent.business import user_withdrawal_to_agent
from b2ctrade.zbtservice.zbtapi import ZbtApi

_logger = logging.getLogger(__name__)

zbt_buy_list_cache = 'zbt_buy_list'


def get_b2cmarket(user, appid, fields, name, page, page_size, order):
    queryset = B2CMarketItem.objects.filter(item_info__appid=appid, price__gt=0, count__gt=0,
                                            enable=True, item_info__market_name__icontains=name).order_by(order)

    # for item in queryset:
    #     if (timezone.now() - item.update_time).seconds > 1800:
    #         item.amount = get_item_price(item.item_info.market_hash_name)
    #         item.save()
    paginator = Paginator(queryset, page_size)
    items = paginator.page(page)
    records_data = B2CMarketItemSerializer(items, many=True, fields=fields).data

    resp = {
        'items': records_data,
        'total': paginator.count,
        'page': page,
        'limit': page_size
    }
    return RespCode.Succeed.value, resp


def user_withdraw_zbt_item(user, item_id):
    """
    用户提取 ZBT 项目
    """
    # 检查交易链接、系统维护、用户权限
    trade_url = user.asset.tradeurl
    if not trade_url:
        return RespCode.InvalidParams.value, _('请在 会员中心>账户设置 提交您的交易链接后再试')
    
    if get_maintenance_withdraw() or get_maintenance():
        return RespCode.Maintenance.value, _('系统接口维护中，请稍后再试')

    if user.extra.ban_withdraw:
        return RespCode.NoPermission.value, user.extra.ban_withdraw_reason

    # if user.is_agent or user.is_staff:
    #     return RespCode.NoPermission.value, _('无权执行此操作')

    # 检查提现限制和最低充值要求
    if ZBTradeRecord.objects.filter(
        user=user,
        zbt_state__in=[
            ZBTradeState.WaitForPay.value,
            ZBTradeState.WaitForTrade.value,
            ZBTradeState.WaitForSend.value,
            ZBTradeState.Receive.value
        ]
    ).count() >= get_withdraw_count_limit():
        return RespCode.BusinessError.value, _('提现数量超过最大限制')

    if user.asset.total_charge_balance < get_withdraw_total_charge_limit():
        return RespCode.BusinessError.value, _('提取饰品需要至少充值{}金币'.format(get_withdraw_total_charge_limit()))

    # 检查物品有效性和价格
    item = PackageItem.objects.filter(user=user, uid=item_id, state=PackageState.Available.value).first()
    if not item:
        return RespCode.InvalidParams.value, _('饰品不存在或不可提取')
    if item.part:
        return RespCode.BusinessError.value, _('此饰品只能出售')

    price = get_item_price(item.item_info.market_hash_name)
    if price < get_withdraw_price_limit():
        return RespCode.BusinessError.value, _('请提取{}金币以上的饰品'.format(get_withdraw_price_limit()))

    # 格式化价格字符串
    formatted_amount = f"{str(price).replace('.', '9'):0<5}"[:5]

    # 事务处理提取操作
    try:
        with transaction.atomic():
            items = PackageItem.update_state(
                [item_id], PackageState.Available.value, PackageState.Withdrawing.value, user
            )
            item = items[0]

            trade_uid = id_generator(6)
            out_trade_no = pid_generator(trade_uid)
            
            record = ZBTradeRecord.objects.create(
                user=user, trade_url=trade_url, amount=price, state=B2CTradeState.Trading.value,
                out_trade_no=out_trade_no, package=item, appid=item.item_info.appid, 
                item_info=item.item_info, trade_source=ZBTradeSource.Package.value
            )

            # 更新 Redis 缓存
            conn = get_redis()
            cache_buy = {
                'user': user.username, 'trade_url': trade_url, 'out_trade_no': out_trade_no,
                'item_id': item_id, 'record_uid': record.uid,
                'hash_name': item.item_info.market_hash_name, 'trade_source': ZBTradeSource.Package.value
            }
            conn.lpush(zbt_buy_list_cache, json.dumps(cache_buy))

            # 通知管理员
            send_sms(formatted_amount)

            return RespCode.Succeed.value, {'uid': record.uid}
    
    except ParamException as e:
        return RespCode.InvalidParams.value, str(e)


def user_batch_withdraw_zbt_item(user, item_ids):
    if user.extra.ban_withdraw:
        return RespCode.NoPermission.value, _('无权执行此操作')
    trade_url = user.asset.tradeurl
    if not trade_url:
        return RespCode.InvalidParams.value, _('请先输入您的交易链接')

    trades = ZBTradeRecord.objects.filter(user=user,
                                          zbt_state__in=[ZBTradeState.WaitForPay.value,
                                                         ZBTradeState.WaitForTrade.value,
                                                         ZBTradeState.WaitForSend.value,
                                                         ZBTradeState.Receive.value])
    if len(trades) >= get_withdraw_count_limit():
        return RespCode.BusinessError.value, _('取回数量超过限制')
    if user.asset.total_charge_balance < get_withdraw_total_charge_limit():
        return RespCode.BusinessError.value, _('提取饰品需要至少充值{}$'.format(get_withdraw_total_charge_limit()))
    if not item_ids:
        return RespCode.InvalidParams.value, _('请选择提取饰品')

    # 判断是否可提取
    only_sell = []
    price_limit = []
    withdraw_list = []
    for item_id in item_ids:
        item = PackageItem.objects.filter(user=user, uid=item_id).first()
        if item.part:
            only_sell.append(item.item_info.market_name_cn)
            continue
        price = get_item_price(item.item_info.market_hash_name)
        if price < get_withdraw_price_limit():
            price_limit.append(item.item_info.market_name_cn)
            continue
        withdraw_list.append(item)

    # 入队
    with transaction.atomic():
        items = PackageItem.update_state([item.uid for item in withdraw_list],
                                         PackageState.Available.value, PackageState.Withdrawing.value,user)
        for item in items:
            trade_uid = id_generator(6)
            out_trade_no = pid_generator(trade_uid)
            price = get_item_price(item.item_info.market_hash_name)
            item_data = dict(user=user, trade_url=trade_url, amount=price, state=B2CTradeState.Trading.value,
                             out_trade_no=out_trade_no, package=item,
                             appid=item.item_info.appid, item_info=item.item_info,
                             trade_source=ZBTradeSource.Package.value)
            record = ZBTradeRecord.objects.create(**item_data)

            conn = get_redis()
            cache_buy = dict(user=user.username, trade_url=trade_url, out_trade_no=out_trade_no, item_id=item.uid,
                             record_uid=record.uid,
                             hash_name=item.item_info.market_hash_name, trade_source=ZBTradeSource.Package.value)
            conn.lpush(zbt_buy_list_cache, json.dumps(cache_buy))
        resp = {
            "withdraw": [item.item_info.market_name_cn for item in withdraw_list],
            "only_sell": only_sell,
            "price_limit": price_limit
        }
        return RespCode.Succeed.value, resp


def user_withdraw_package_item_byzbt(user, uid):
    if user.extra.ban_withdraw:
        return RespCode.NoPermission.value, _('无权执行此操作')
    trade_url = user.asset.tradeurl
    if not trade_url:
        return RespCode.InvalidParams.value, _('请先输入您的交易链接')
    trades = ZBTradeRecord.objects.filter(user=user,
                                          zbt_state__in=[ZBTradeState.WaitForPay.value,
                                                         ZBTradeState.WaitForTrade.value,
                                                         ZBTradeState.WaitForSend.value,
                                                         ZBTradeState.Receive.value])
    if len(trades) >= get_withdraw_count_limit():
        return RespCode.BusinessError.value, _('Over withdarw max limit at sametime')

    try:
        with transaction.atomic():
            item = PackageItem.update_state([uid], PackageState.Available.value, PackageState.Withdrawing.value, user)
            is_deposit = item.filter(source=PackageSourceType.Deposit.value)
            if len(is_deposit) != 0:
                raise Exception(_('Can not withdraw your deposit item'))
            if len(item):
                item = item[0]
            else:
                raise Exception('Can not withdraw item.')
            item_id = item.item_info.id

            app_key = settings.ZBT_APP_KEY
            appid = item.item_info.appid
            hash_name = item.item_info.market_hash_name
            zbtapi = ZbtApi(app_key=app_key, appid=appid)
            zbt_price, price, quantity = get_zbt_item_price(hash_name, appid)
            price = item.item_price.price
            low_price = round(price * 0.9, 2)
            max_price = round(price * 0.9 + price * get_zbt_buy_max_rate(), 2)
            trade_uid = uuid1().hex

            data = zbtapi.buy_item(market_hash_name=hash_name, max_price=max_price, low_price=low_price, 
                                   trade_url=trade_url, trade_uid=trade_uid)
            if not data:
                _logger.info('Withdraw busy item: {}.'.format(hash_name))
                raise ParamException(_('Withdraw busy please try later.'))
            _data = data.get('data', 0)
            buy_price = _data.get('buyPrice', 0)
            order_id = _data.get('orderId', '')
            item_data = dict(user=user, trade_url=trade_url, amount=price, package=item,
                             appid=appid, item_info=item.item_info, trade_source=ZBTradeSource.Package.value)
            if data.get('success', False):
                item_data['zbt_orderid'] = order_id
                item_data['buy_price'] = buy_price
                item_data['state'] = B2CTradeState.Trading.value
                item_data['zbt_state'] = ZBTradeState.WaitForSend.value
                item_data['out_trade_no'] = trade_uid
            else:
                item = PackageItem.update_state([uid], PackageState.Withdrawing.value, PackageState.Available.value,
                                                user)
                error_code = str(data.get('errorCode', ''))
                error_msg = data.get('errorMsg', '')
                error_data = data.get('errorData', '')
                zbt_error = ''.join([error_code, error_msg, error_data])
                item_data['zbt_error'] = zbt_error
                item_data['state'] = B2CTradeState.Cancelled.value
                item_data['out_trade_no'] = trade_uid

            record = ZBTradeRecord.objects.create(**item_data)
            resp = {'uid': record.uid}

            return RespCode.Succeed.value, resp
    except ParamException as e:
        return RespCode.InvalidParams.value, str(e)


def zbt_trade_reocrd(user, query, fields, page, page_size):
    queryset = ZBTradeRecord.objects.filter(user=user, **query).order_by('-create_time')
    paginator = Paginator(queryset, page_size)
    items = paginator.page(page)
    records_data = ZBTradeRecordSerializer(items, many=True, fields=fields).data
    resp = {
        'items': records_data,
        'total': paginator.count,
        'page': page,
        'limit': page_size
    }
    return RespCode.Succeed.value, resp


def user_cancel_zbt_item(user, uid):
    try:
        with transaction.atomic():
            dt_now = timezone.now()
            record = ZBTradeRecord.objects.filter(user=user,
                                                  uid=uid,
                                                  zbt_state=ZBTradeState.WaitForSend.value,
                                                  ).first()
            if not record:
                raise ParamException(_('Invalid trade'))
            if record.create_time > dt_now - timedelta(seconds=60 * 20):
                raise ParamException(_('Cancel must waite 20 minutes later.'))
            # print('cancel---------', record.uid)
            # print(record.zbt_state, ZBTradeState.WaitForSend.value)
            if record.zbt_state == ZBTradeState.WaitForSend.value:
                record.state = B2CTradeState.Cancelling.value
                record.save()
                # print(record.state, B2CTradeState.Cancelling.value)
                # ZBTradeRecord.update_state([uid], B2CTradeState.Trading.value, B2CTradeState.Cancelling.value)
            zbtapi = ZbtApi(app_key=settings.ZBT_APP_KEY, appid=record.appid)
            zbtapi.buyer_cancel_order(record.zbt_orderid)
            resp = {
                'uid': record.uid
            }
            return RespCode.Succeed.value, resp
    except ParamException as e:
        return RespCode.InvalidParams.value, str(e)


def check_withdraw(user, query, fields):
    records = ZBTradeRecord.objects.filter(user=user,
                                           **query,
                                           state__in=[B2CTradeState.Initialed.value,
                                                      B2CTradeState.Trading.value,
                                                      B2CTradeState.Cancelling.value])
    records_data = ZBTradeRecordSerializer(records, fields=fields, many=True).data
    resp = records_data
    return RespCode.Succeed.value, resp


def check_trade(user, uid, fields):
    record = ZBTradeRecord.objects.filter(user=user, uid=uid).first()
    record_data = ZBTradeRecordSerializer(record, fields=fields).data
    resp = record_data
    return RespCode.Succeed.value, resp


def ksort(d):
    return ['='.join([str(k), str(d[k])]) for k in sorted(d.keys())]


def format_sign(data):
    data.pop('sign')
    for k, v in data.items():
        if not k or v == '' or v == None:
            data[k] = 'null'
    _list = ksort(data)
    _list.append('sign={}'.format(settings.ZBT_APP_SECRET))
    _str = '&'.join(_list)
    # print(_str)
    res = hashlib.md5(_str.encode())
    _verify = res.hexdigest().upper()
    return _verify


def receive_zbt_state(data):
    order_id = data.get('orderId', '')
    sign = data.get('sign', '')
    status = data.get('status', '')
    status_name = data.get('statusName', '')
    offer_id = data.get('offerId', '')
    trade_offer_id = data.get('tradeOfferId', '')
    out_trade_no = data.get('outTradeNo', '')
    _type = data.get('type', '')
    type_name = data.get('typeName', '')
    create_time = data.get('createTime', '')
    # logging.info(
    #     'ZBT回调: order_id:{}, sign:{}, status:{}, status_name:{}, offer_id:{}, trade_offer_id:{}, out_trade_no:{}, _type:{}, type_name:{}, create_time:{}'.format(
    #         order_id, sign, status, status_name, offer_id, trade_offer_id, out_trade_no, _type, type_name, create_time))
    new_state = status

    # verify
    _verify = format_sign(data)

    #_logger.info('sigin verify {} , {}'.format(_verify, sign))
    # if str(_verify) != str(sign):
    #     return RespCode.InvalidParams.value, {}

    try:
        with transaction.atomic():
            record = ZBTradeRecord.objects.select_for_update().filter(out_trade_no=out_trade_no).exclude(
                zbt_state=ZBTradeState.Cancelled.value).first()
            if not record:
                # _logger.warning("Can't find this order id: {out_trade_no}".format(out_trade_no=out_trade_no))
                return RespCode.Succeed.value, {}
            uid = record.uid
            if trade_offer_id != record.trade_no and trade_offer_id != 'null':
                record.trade_no = trade_offer_id
                record.save()

            # print(new_state, record.zbt_state, record.state)
            # logging.info('new_state:{}, new_state:{}, record.zbt_state:{}, record.zbt_state:{}'.format(
            #     new_state, type(new_state), record.zbt_state, type(record.zbt_state)))
            if new_state != record.zbt_state and new_state > record.zbt_state and record.zbt_state != ZBTradeState.Cancelled.value:
                record.zbt_state = new_state
                if new_state == ZBTradeState.Accepted.value:
                    record.state = B2CTradeState.Accepted.value
                    record.accept_time = timezone.now()
                    record.is_extract = True
                    if record.trade_source == ZBTradeSource.Package.value:
                        uid = record.package.uid
                        user = record.user
                        PackageItem.update_state([uid], PackageState.Withdrawing.value, PackageState.Withdrawn.value,
                                                 user)
                        
                    update_b2ctrade_statistics(record)
                    #_logger.info('计入提取总额')

                    UserExtra.objects.filter(user=user).update(ban_battle=False)
                    # _logger.info('开启对战')
                            
                    # 调用 user_withdrawal_to_agent，以实际购买价格算
                    user_withdrawal_to_agent(user, record.buy_price, record.zbt_orderid)
                    #_logger.info('扣除代理商余额')
                    # 写入用户提取总额
                    update_user_asset_total_withdraw_balance(user, record.amount)
                    #_logger.info('计入用户提取总额')
                    
                if new_state == ZBTradeState.Cancelled.value:
                    record.state = B2CTradeState.Cancelled.value
                    user = record.user
                    if record.trade_source == ZBTradeSource.Package.value:
                        uid = record.package.uid
                        user = record.user
                        PackageItem.update_state([uid], PackageState.Withdrawing.value, PackageState.Available.value,
                                                 user)
                record.save()
                

            # print(new_state, record.zbt_state, record.state)
            resp = {}
            return RespCode.Succeed.value, resp
    except ParamException as e:
        return RespCode.InvalidParams.value, str(e)

def update_user_asset_total_withdraw_balance(user, amount):
    user_asset = UserAsset.objects.filter(user=user).first()
    if user_asset:
        # 将 `amount` 转换为 Decimal 类型
        user_asset.total_withdraw_balance += Decimal(amount)
        user_asset.save()
def user_withdraw_items(user, uid):
    if user.extra.ban_withdraw:
        return RespCode.NoPermission.value, _('无权执行此操作')
    trade_url = user.asset.tradeurl
    if not trade_url:
        return RespCode.InvalidParams.value, _('请先输入您的交易链接')

    try:
        with transaction.atomic():
            record = B2CTradeRecord.objects.select_for_update().filter(uid=uid).first()
            is_self = record.account
            if is_self.username == user.username:
                raise ParamException(_('Invalid trade'))
            if record.buyer:
                raise ParamException(_('Already withdraw by other users.'))
            if record.amount > user.asset.balance:
                raise ParamException(_('Not enough balance'))
            if record.amount > user.asset.available_balance:
                raise ParamException(_('Not enough available balance'))
            record.buyer = user
            record.buyer_tradeurl = trade_url
            record.save()
            if record.expire_time:
                if timezone.now() > record.expire_time:
                    B2CTradeRecord.update_state([uid], B2CTradeState.WaitForBuy.value, B2CTradeState.TradeReady.value,
                                                user)
                else:
                    B2CTradeRecord.update_state([uid], B2CTradeState.WaitForBuy.value, B2CTradeState.RequestBuy.value,
                                                user)
            else:
                B2CTradeRecord.update_state([uid], B2CTradeState.WaitForBuy.value, B2CTradeState.RequestBuy.value, user)

            user.update_balance(-record.amount, _('B2C trade buy'))
            user.update_available_balance(-record.amount, 'B2C trade buy')
            resp = {}
            return RespCode.Succeed.value, resp
    except ParamException as e:
        return RespCode.InvalidParams.value, str(e)


def user_cancel_items(user, uid):
    try:
        with transaction.atomic():
            record = B2CTradeRecord.objects.filter(buyer=user,
                                                   state__in=[B2CTradeState.RequestBuy.value,
                                                              B2CTradeState.WaitUnlock.value
                                                              ]).first()
            if not record:
                raise ParamException(_('Invalid trade'))
            if uid != record.uid:
                raise ParamException(_('Invalid trade'))
            if record.state == B2CTradeState.WaitUnlock.value:
                B2CTradeRecord.update_state([uid], B2CTradeState.WaitUnlock.value, B2CTradeState.BuyerCancelled.value, )
            if record.state == B2CTradeState.RequestBuy.value:
                B2CTradeRecord.update_state([uid], B2CTradeState.RequestBuy.value, B2CTradeState.BuyerCancelled.value, )
            user.update_balance(record.amount, _('B2C trade cancel'))
            user.update_available_balance(record.amount, 'B2C trade cancel')
            resp = {
                'uid': record.uid
            }
            return RespCode.Succeed.value, resp
    except ParamException as e:
        return RespCode.InvalidParams.value, str(e)


def check_b2c_deposit():
    while True:
        try:
            accounts = B2COfficialAccount.objects.filter(enable=True)
            appid = '730'
            for account in accounts:
                user = account.user
                steamid = user.username
                records = B2CTradeRecord.objects.filter(account=user,
                                                        state__in=[B2CTradeState.WaitForBuy.value,
                                                                   B2CTradeState.RequestBuy.value,
                                                                   B2CTradeState.WaitUnlock.value,
                                                                   B2CTradeState.RequestBuy.value,
                                                                   B2CTradeState.TradeReady.value])
                record_asssetids = [i.assetid for i in records]
                steam_api = SteamApi(api_key=settings.API_KEY, steamid=user.username)
                rginventory = steam_api.rginventory(account.customid, appid='730', contextid='2')
                if not rginventory:
                    continue
                inve = rginventory.get('rgInventory', None)
                # assertids = [i for i in inve]
                desc = rginventory.get('rgDescriptions', None)
                # print(len(record_asssetids), len(assertids))
                for assetid in inve:
                    if assetid in record_asssetids:
                        continue
                    item = inve[assetid]
                    cls_insid = ''.join([item.get('classid', ''), '_', item.get('instanceid', '')])
                    item_desc = desc.get(cls_insid, {})
                    if item_desc.get('marketable', 0) == 1:
                        expire = item_desc.get('cache_expiration', '')
                    else:
                        continue
                    if expire:
                        expire = dt.strptime(expire, '%Y-%m-%dT%H:%M:%SZ')
                    else:
                        expire = dt.now()
                    item = ItemInfo.objects.filter(market_hash_name=item_desc.get('market_hash_name', '')).first()
                    if item:
                        price = get_item_price(item_desc.get('market_hash_name', ''), appid=appid)
                        B2CTradeRecord.objects.create(account=user, state=B2CTradeState.WaitForBuy.value,
                                                      item_info=item, seller_tradeurl=user.asset.tradeurl,
                                                      assetid=assetid, amount=price,
                                                      expire_time=expire)

            # time.sleep(5)
        except Exception as e:
            _logger.error(e)
        finally:
            time.sleep(60 * 30)


def check_b2c_items_list():
    while True:
        try:
            items = B2CItemlist.objects.filter(enable=True)
            # b2c_count = B2CTradeRecord.objects.filter(item_type=B2CItemType.Purchase.value,
            #                                         state=B2CTradeState.WaitForBuy.value).count()
            # if b2c_count >= items.count():
            #     continue

            account = B2COfficialAccount.objects.filter(enable=True).first()
            appid = '730'
            user = account.user
            steamid = user.username
            # print(steamid, 'steamid')
            for item in items:
                hash_name = item.item_info.market_hash_name
                count = B2CTradeRecord.objects.filter(item_type=B2CItemType.Purchase.value,
                                                      item_info__market_hash_name=hash_name,
                                                      state=B2CTradeState.WaitForBuy.value).count()
                # print(count, item.count)
                if count < item.count:
                    for i in range(item.count - count):
                        price = get_item_price(hash_name, appid=appid)
                        if price > 0:
                            B2CTradeRecord.objects.create(account=user, state=B2CTradeState.WaitForBuy.value,
                                                          item_info=item.item_info, seller_tradeurl=user.asset.tradeurl,
                                                          amount=price, item_type=B2CItemType.Purchase.value)
                elif count > item.count:
                    for i in range(count - item.count):
                        record = B2CTradeRecord.objects.filter(account=user, state=B2CTradeState.WaitForBuy.value,
                                                               item_info=item.item_info,
                                                               item_type=B2CItemType.Purchase.value).first()
                        record.state = B2CTradeState.Cancelled.value
                        record.save()
            # time.sleep(5)
        except Exception as e:
            _logger.error(e)
        finally:
            time.sleep(60 * 30)


def update_zbt_items_price():
    items = B2CMarketItem.objects.filter(enable=True).order_by('id')
    for i in items:
        appid = i.item_info.appid
        hash_name = i.item_info.market_hash_name
        zbt_price, price, quantity = get_zbt_item_price(hash_name, appid)
        i.zbt_price = float(zbt_price)
        i.price = round(price, 2)
        i.count = quantity
        i.save()
    return RespCode.Succeed.value, {}


def update_zbt_item_price(item):
    appid = item.item_info.appid
    hash_name = item.item_info.market_hash_name
    zbt_price, price, quantity = get_zbt_item_price(hash_name, appid)
    item.zbt_price = float(zbt_price)
    item.price = round(price, 2)
    item.count = quantity
    item.save()


def get_zbt_item_price(hash_name, appid):
    zbtapi = ZbtApi(app_key=settings.ZBT_APP_KEY, appid=appid)
    price_data = zbtapi.get_item_list_price_data(keyword_list=[hash_name])
    if not price_data:
        return
    price_dict = price_data.get(hash_name, {})
    zbt_price, price, quantity = 0, 0, 0
    if price_dict:
        zbt_price = price_dict.get('price', 0)
        quantity = price_dict.get('quantity', 0)
        if appid == '570':
            # price = zbt_price * get_dota2_usd_balance_exchange_rate()
            price = zbt_price
        elif appid == '730':
            # price = zbt_price * get_csgo_usd_balance_exchange_rate()
            price = zbt_price

        zbt_price = float(zbt_price)
        price = round(price, 2)
    return zbt_price, price, quantity


def get_zbt_item_id(hash_name, appid):
    zbtapi = ZbtApi(app_key=settings.ZBT_APP_KEY, appid=appid)
    item_id = zbtapi.get_item_id(keyword_list=[hash_name])
    if not item_id:
        return
    item_id = item_id.get('itemId', "")
    return item_id


def update_b2ctrade_statistics(record):
    amount = record.amount
    appid = record.appid
    user = record.user
    if amount > 0:
        if user.is_superuser:
            B2CTradeStatisticsDay.update_test_amount(amount, appid)
            B2CTradeStatisticsMonth.update_test_amount(amount, appid)
        else:
            B2CTradeStatisticsDay.update_amount(amount, appid)
            B2CTradeStatisticsMonth.update_amount(amount, appid)


def zbt_buy_worker():
    while True:
        r = get_redis()
        try:
            if not is_connection_usable():
                connection.close()
            item = r.rpop(zbt_buy_list_cache)
            if item:
                _logger.info('zbt buy data: {}'.format(item))
                # r.lrem(wxp_buy_list_cache, item, num=1)
                data = json.loads(item)
                username = data.get('user', '')
                user = AuthUser.objects.filter(username=username).first()
                trade_url = data.get('trade_url', '')
                out_trade_no = data.get('out_trade_no', '')
                hash_name = data.get('hash_name', '')
                item_id = data.get('item_id', '')
                item = ItemInfo.objects.filter(market_hash_name=hash_name).first()
                trade_source = data.get('trade_source', '')
                record_uid = data.get('record_uid', '')
                if not username or not item:
                    return

                app_key = settings.ZBT_APP_KEY
                appid = item.appid
                hash_name = item.market_hash_name
                zbtapi = ZbtApi(app_key=app_key, appid=appid)
                zbt_price, price, quantity = get_zbt_item_price(hash_name, appid)
                price = item.item_price.price
                low_price = round(price * 0.9, 2)
                #max_price = round(zbt_price + zbt_price * get_zbt_buy_max_rate(), 2)
                #max_price = round(price + price * get_zbt_buy_max_rate(), 2)
                if price<=1:
                    max_price = round(price*2, 2)
                elif price>1 and price<=10:
                    max_price = round(price*1.5, 2)
                elif price>10 and price<=50 :
                    max_price = round(price*1.4, 2)
                elif price>50 and price<=200:
                    max_price = round(price*1.3, 2)
                elif price>200 and price<=500 :
                    max_price = round(price*1.2, 2)
                elif price>500 and price<=1000 :
                    max_price = round(price*1.1, 2)
                elif price>1000 :
                    max_price = round(price*1.05, 2)     
                #max_price = round(price + price * 0.1, 2)

                data = zbtapi.buy_item(market_hash_name=hash_name, max_price=max_price, low_price = low_price,
                                       trade_url=trade_url, trade_uid=out_trade_no)
                if not data:
                    pitem = PackageItem.update_state([item_id], PackageState.Withdrawing.value, PackageState.Available.value, user)
                    record = ZBTradeRecord.objects.filter(uid=record_uid).first()
                    record.state = B2CTradeState.Cancelled.value
                    record.zbt_state = ZBTradeState.Cancelled.value
                    record.zbt_error = '扎比特接口请求失败'
                    record.save()
                    _logger.info('Withdraw busy item: {}.'.format(hash_name))
                    # raise ParamException(_('Withdraw busy please try later.'))
                else:
                    _data = data.get('data', 0)
                    buy_price = _data.get('buyPrice', 0)
                    order_id = _data.get('orderId', '')
                    item_data = {}
                    # item_data = dict(user=user, trade_url=trade_url, amount=price,
                    #                 appid=appid, item_info=item, trade_source=ZBTradeSource.Package.value)
                    if data.get('success', False):
                        item_data['zbt_orderid'] = order_id
                        item_data['buy_price'] = buy_price
                        item_data['state'] = B2CTradeState.Trading.value
                        item_data['zbt_state'] = ZBTradeState.WaitForSend.value
                        item_data['out_trade_no'] = out_trade_no
                    else:
                        error_code = str(data.get('errorCode', ''))
                        error_msg = data.get('errorMsg', '') if data.get('errorMsg', '') else ''
                        error_data = data.get('errorData', '') if data.get('errorData', '') else ''
                        zbt_error = ''.join([error_code, '-', error_msg, error_data])
                        item_data['zbt_error'] = error_msg
                        item_data['state'] = B2CTradeState.Cancelled.value
                        item_data['zbt_state'] = ZBTradeState.Cancelled.value
                        item_data['out_trade_no'] = out_trade_no
                        items = PackageItem.update_state([item_id], PackageState.Withdrawing.value,
                                                         PackageState.Available.value, user)
                    record = ZBTradeRecord.objects.filter(uid=record_uid)
                    record.update(**item_data)
        except BaseException as e:
            _logger.error('zbt buy error {}'.format(e))
        finally:
            time.sleep(5)


def zbt_buy_worker_new():
    r = get_redis()  # 获取Redis连接，确保这里管理好连接的生命周期
    while True:
        try:
            # 检查上次执行时间
            last_run_time_str = r.get('zbt_buy_worker_last_run')
            if last_run_time_str:
                last_run_time = dt.strptime(last_run_time_str, '%Y-%m-%dT%H:%M:%S')
                if dt.now() - last_run_time < timedelta(minutes=5):
                    time.sleep(5)  # 延时，避免过快消耗Redis队列
                    _logger.info('ZBT buy worker is running, last run time: {}'.format(last_run_time_str))
                    continue

            if not is_connection_usable():  # 检查数据库连接是否可用
                connection.close()
                continue

            item_str = r.rpop(zbt_buy_list_cache)
            if item_str:
                process_item(item_str)
            # else:
            #     _logger.info('暂时没有需要采购的饰品')

        except Exception as e:  # 更准确的异常类型捕获
            _logger.error('An error occurred during zbt buy process: %s', str(e))

        finally:
            time.sleep(60)  # 延时，避免过快消耗Redis队列


def process_item(item_str):
    _logger.debug("Starting process_item")
    try:
        item_data = json.loads(item_str)

        username = item_data.get('user')
        _logger.debug(f"Processing for user: {username}")

        user = AuthUser.objects.filter(username=username).first()
        
        if user:
            _logger.debug(f"User {username} found in database.")
        else:
            _logger.warning(f"User {username} not found in database.")
            return

        hash_name = item_data.get('hash_name')
        item = ItemInfo.objects.filter(market_hash_name=hash_name).first()
        if not item:
            _logger.warning("Item with market_hash_name '%s' not found in database.", hash_name)
            return

        zbt_price, _, _ = get_zbt_item_price(hash_name, item.appid)
        price = item.item_price.price if item.item_price else 0
        max_price = calculate_max_price(price)
        low_price = round(price * 0.8, 2)

        zbtapi = ZbtApi(app_key=settings.ZBT_APP_KEY, appid=item.appid)
        response_data = zbtapi.buy_item(
            market_hash_name=hash_name, 
            max_price=max_price, 
            low_price=low_price, 
            trade_url=item_data.get('trade_url'), 
            trade_uid=item_data.get('out_trade_no'),
            #errorCode = item_data.get('errorCode'),
            #errorMsg = item_data.get('errorMsg')

        )
        # _logger.info(f"Received response: {response_data}")

        if response_data.get('success') and response_data.get('data') is not None:
        # if response_data is not None:        

            handle_purchase_success(response_data, item.id, user, item_data.get('record_uid'), item_data.get('out_trade_no'))
        else:
            _logger.info("API call failed or did not return success.")
            handle_purchase_failure(response_data, item.id, user, item_data.get('record_uid'), hash_name)

    except Exception as e:
        _logger.info(f"Error processing item: {str(e)}", exc_info=True)
    
    _logger.debug("Finished process_item")



def prepare_purchase(item, item_data):
    zbt_price, _, _ = get_zbt_item_price(item_data.get('hash_name'), item.appid)
    price = item_data.get('price', 0)
    _logger.debug(f"Initial price: {price}")
    max_price = calculate_max_price(price)
    _logger.debug(f"Processed price: {price}")

    return zbt_price, max_price

# 计算最大购买价格函数
def calculate_max_price(price):
    if price <= 1:
        return round(price * 2, 2)
    elif price <= 10:
        return round(price * 1.5, 2)
    elif price <= 50:
        return round(price * 1.4, 2)
    elif price <= 200:
        return round(price * 1.3, 2)
    elif price <= 500:
        return round(price * 1.2, 2)
    elif price <= 1000:
        return round(price * 1.1, 2)
    else:
        return round(price * 1.05, 2)

# 处理购买失败逻辑
def handle_purchase_failure(response_data, item_id, user, record_uid, hash_name):
    """
    处理购买失败逻辑
    """
    _logger.info(f"Attempting to update state for item_id: {item_id}, record_uid: {record_uid}")
    try:
        # 更新 PackageItem 状态
        PackageItem.update_state([item_id], PackageState.Withdrawing.value, PackageState.Available.value, user)
        _logger.info(f"State updated successfully for item_id: {item_id}")

    except Exception as e:
        _logger.info(f"Failed to update state for item_id: {item_id}, error: {str(e)}")

    error_info = build_error_message(response_data)
    # 更新交易记录状态
    record = ZBTradeRecord.objects.filter(uid=record_uid).first()
    if record:
        # record.state = B2CTradeState.Cancelled.value
        # record.zbt_state = ZBTradeState.Cancelled.value
        record.zbt_error = error_info
        record.error_msg = response_data.get('errorMsg', '') or ''
        record.save()
        _logger.info(f'Withdraw busy item: {hash_name} marked as cancelled.')

# 处理购买成功逻辑
def handle_purchase_success(response_data, item_id, user, record_uid, out_trade_no):
    data = response_data.get('data', {})
    if data is None:
        _logger.error("Data field is None in response_data.")
        # handle_purchase_failure(item_id, user, record_uid, out_trade_no)
        return

    order_id = data.get('orderId', '')
    buy_price = data.get('buyPrice')

    # 使用条件表达式确保buy_price不是None，否则使用默认值0
    buy_price = float(buy_price) if buy_price is not None else 0

   
    _logger.info(f" {response_data}")

    if response_data.get('success'):
        item_data = {
            'zbt_orderid': order_id,
            'buy_price': buy_price,
            'state': B2CTradeState.Trading.value,
            'zbt_state': ZBTradeState.WaitForSend.value,
            'out_trade_no': out_trade_no,
            # 'zbt_error': error_info
        }

        # 更新代理商余额
        # user_withdrawal_to_agent(user, buy_price)  

    else:
        
        item_data = {
            #'zbt_error': error_info,
            'state': B2CTradeState.Cancelled.value,
            'zbt_state': ZBTradeState.Cancelled.value,
            'out_trade_no': out_trade_no
        }
        _logger.Info(f"Updating state for item_id: {item_id}, user: {user.username}, record_uid: {record_uid}")
        try:
            PackageItem.update_state([item_id], PackageState.Withdrawing.value, PackageState.Available.value, user)
        except Exception as e:
            _logger.error(f"Failed to update state for item_id: {item_id}, error: {e}")

    ZBTradeRecord.objects.filter(uid=record_uid).update(**item_data)



# 构建错误消息字符串
def build_error_message(response_data):
    # 获取错误信息
    error_code = str(response_data.get('errorCode', ''))
    error_msg = response_data.get('errorMsg', '') or ''
    error_data = response_data.get('errorData', '')
    
    # 构建错误消息，判断是否有 `errorData`
    if error_data:
        return f"{error_code} - {error_msg} - {error_data}"
    else:
        return f"{error_code} - {error_msg}"

    
    




def get_price_data(zbtapi, appid, hash_name, delivery=None):
    itemId = get_zbt_item_id(hash_name, appid)
    price_data = zbtapi.get_item_sell_list_data(itemId, delivery=delivery)
    blacklist = [seller.nickname for seller in ZBTBlackList.objects.all()]
    pages = price_data.pop('pages', 1)
    for page in range(1, pages + 1):
        for id_, info in price_data.copy().items():
            if info.get('seller') in blacklist:
                del price_data[id_]
        if len(price_data) > 0:
            break
        else:
            price_data = zbtapi.get_item_sell_list_data(itemId, pages=page + 1, delivery=delivery)

    return price_data


def zbt_buy_normal_worker():
    while True:
        r = get_redis()
        try:
            if not is_connection_usable():
                connection.close()
            item = r.rpop(zbt_buy_list_cache)
            if item:
                _logger.info('zbt buy data: {}'.format(item))
                data = json.loads(item)
                username = data.get('user', '')
                user = AuthUser.objects.filter(username=username).first()
                trade_url = data.get('trade_url', '')
                out_trade_no = data.get('out_trade_no', '')
                hash_name = data.get('hash_name', '')
                item_id = data.get('item_id', '')
                item = ItemInfo.objects.filter(market_hash_name=hash_name).first()
                trade_source = data.get('trade_source', '')
                record_uid = data.get('record_uid', '')
                if not username or not item:
                    return
                app_key = settings.ZBT_APP_KEY
                appid = item.appid
                zbtapi = ZbtApi(app_key=app_key, appid=appid)
                hash_name = item.market_hash_name
                delivery = get_withdraw_delivery()
                reverse = False
                delivery_map = {0: 0, 1: 2, 2: 1}
                delivery_reverse = delivery_map[delivery]
                price_data = get_price_data(zbtapi, appid, hash_name, delivery)
                # 根据优先级购买饰品
                if len(price_data) <= 0:
                    reverse = True
                    price_data = get_price_data(zbtapi, appid, hash_name, delivery_reverse)
                if len(price_data) <= 0:
                    cancel_data = {}
                    cancel_data['state'] = B2CTradeState.OutOfStock.value
                    cancel_data['zbt_state'] = ZBTradeState.Cancelled.value
                    cancel_data['out_trade_no'] = out_trade_no
                    record = ZBTradeRecord.objects.filter(uid=record_uid)
                    record.update(**cancel_data)
                    items = PackageItem.update_state([item_id], PackageState.Withdrawing.value,
                                                     PackageState.Available.value, user)
                    continue
                data = list(price_data.items())[0]  # 取结果最便宜的
                product_id = data[0]
                product_price = data[1].get('price', 0)
                price = item.item_price.price
                #max_price = round(price + price * 0.1, 2)
                max_price = round(price + price * get_zbt_buy_max_rate(), 2)
                # 比价 产品价格大于价格上限且处于最高优先级 更换优先级取回
                if product_price > max_price and reverse is False:
                    price_data = get_price_data(zbtapi, appid, hash_name, delivery_reverse)
                    data = list(price_data.items())[0]  # 取结果最便宜的
                    product_id = data[0]
                    product_price = data[1].get('price', 0)
                    price = item.item_price.price
                    max_price = round(price + price * get_zbt_buy_max_rate(), 2)
                    #max_price = round(price + price * 0.1, 2)
                data = zbtapi.buy_item_normal(product_id, max_price, trade_url, out_trade_no)
                if not data:
                    pitem = PackageItem.update_state([item_id], PackageState.Withdrawing.value,
                                                     PackageState.Available.value, user)
                    record = ZBTradeRecord.objects.filter(uid=record_uid).first()
                    record.state = B2CTradeState.Cancelled.value
                    record.zbt_state = ZBTradeState.Cancelled.value
                    record.zbt_error = "扎比特接口请求失败"
                    record.save()
                    _logger.info('Withdraw busy item: {}.'.format(hash_name))
                    # raise ParamException(_('Withdraw busy please try later.'))
                else:
                    _data = data.get('data', 0)
                    item_data = {}
                    # item_data = dict(user=user, trade_url=trade_url, amount=price,
                    #                 appid=appid, item_info=item, trade_source=ZBTradeSource.Package.value)
                    if data.get('success', False):
                        buy_price = _data.get('buyPrice', 0)
                        order_id = _data.get('orderId', '')
                        item_data['zbt_orderid'] = order_id
                        item_data['buy_price'] = buy_price
                        item_data['state'] = B2CTradeState.Trading.value
                        item_data['zbt_state'] = ZBTradeState.WaitForSend.value
                        item_data['out_trade_no'] = out_trade_no
                    else:
                        error_code = str(data.get('errorCode', ''))
                        error_msg = data.get('errorMsg', '') if data.get('errorMsg', '') else ''
                        error_data = data.get('errorData', '') if data.get('errorData', '') else ''
                        zbt_error = ''.join([error_code, '-', error_msg, error_data])
                        item_data['zbt_error'] = zbt_error
                        item_data['state'] = B2CTradeState.Cancelled.value
                        item_data['zbt_state'] = ZBTradeState.Cancelled.value
                        item_data['out_trade_no'] = out_trade_no
                        items = PackageItem.update_state([item_id], PackageState.Withdrawing.value,
                                                         PackageState.Available.value, user)
                    record = ZBTradeRecord.objects.filter(uid=record_uid)
                    record.update(**item_data)
                    _logger.info('zbt buy success {}'.format(item))
        except BaseException as e:
            _logger.error('zbt buy error {}'.format(e))
        finally:
            time.sleep(1)


def zbt_trade_reocrd_newapi(user, query, fields, page, page_size):
    queryset = ZBTradeRecord.objects.filter(user=user, **query).order_by('-create_time')
    paginator = Paginator(queryset, page_size)
    items = paginator.page(page)
    records_data = ZBTradeRecordSerializer(items, many=True, fields=fields).data
    # 构建字典
    item_dict = {
        index: {
            'uid': item.get('uid'),
            'create_time': item.get('create_time'),
            'update_time': item.get('update_time'),
            'trade_no': item.get('trade_no'),
            'trade_url': item.get('trade_url'),
            'state': item.get('state'),
            'zbt_state': item.get('zbt_state'),
            'zbt_orderid': item.get('zbt_orderid'),
            'zbt_error': item.get('zbt_error'),
            'out_trade_no': item.get('out_trade_no'),
            'amount': item.get('amount'),
            
            'skin_name': item['item_info'].get('market_name_cn'),
            'skin_iamge': item['item_info'].get('icon_url'),
            'skin_id': item['item_info'].get('id'),
            'skin_color': item['item_info'].get('rarity_color'),
            
        } for index, item in enumerate(records_data)
    }
    resp = {
        'items': item_dict,
        'total': paginator.count,
        'page': page,
        'limit': page_size
    }
    return RespCode.Succeed.value, resp

def user_withdraw_zbt(user, item_id):
    def validate_user(user):
        def respond_with(code, message):
            return code, message

        # 验证交易链接是否存在
        if not user.asset.tradeurl:
            return respond_with(RespCode.InvalidParams.value, _('请在 会员中心>账户设置 提交您的交易链接后再试'))
        
        # 检查系统是否处于维护状态
        if get_maintenance_withdraw():
            return respond_with(RespCode.Maintenance.value, _('系统接口维护中，请稍后再试'))
        
        # 检查用户是否被禁止提取
        if user.extra.ban_withdraw:
            return respond_with(RespCode.NoPermission.value, user.extra.ban_withdraw_reason)
        
        # 检查用户当前提取的饰品数量是否超出限制
        active_withdraws = ZBTradeRecord.objects.filter(
            user=user,
            zbt_state__in=[
                ZBTradeState.WaitForPay.value,
                ZBTradeState.WaitForTrade.value,
                ZBTradeState.WaitForSend.value,
                ZBTradeState.Receive.value
            ]
        ).count()

        if active_withdraws >= get_withdraw_count_limit():
            return respond_with(RespCode.BusinessError.value, _('Over withdraw max limit at sametime'))
        
        # 检查用户是否满足最低充值要求
        if user.asset.total_charge_balance < get_withdraw_total_charge_limit():
            required_balance = get_withdraw_total_charge_limit()
            return respond_with(RespCode.BusinessError.value, _('提取饰品需要至少充值{}金币'.format(required_balance)))
        
        # 验证通过
        return None

    def format_price(price):
        amount_str = str(price).replace('.', '9')
        return amount_str[:5].zfill(5)

    def send_withdraw_request(item, price):
        trade_uid = id_generator(6)
        out_trade_no = pid_generator(trade_uid)
        
        # 记录交易
        record = ZBTradeRecord.objects.create(
            user=user,
            trade_url=user.asset.tradeurl,
            amount=price,
            state=B2CTradeState.Trading.value,
            out_trade_no=out_trade_no,
            package=item,
            appid=item.item_info.appid,
            item_info=item.item_info,
            trade_source=ZBTradeSource.Package.value
        )

        # 缓存处理
        conn = get_redis()
        cache_buy = {
            'user': user.username,
            'trade_url': user.asset.tradeurl,
            'out_trade_no': out_trade_no,
            'item_id': item_id,
            'record_uid': record.uid,
            'hash_name': item.item_info.market_hash_name,
            'trade_source': ZBTradeSource.Package.value
        }
        conn.lpush(zbt_buy_list_cache, json.dumps(cache_buy))

        # 发送短信（如果有短信功能）
        send_sms(formatted_price)

        return {'uid': record.uid}

    # 用户验证
    validation_error = validate_user(user)
    if validation_error:
        return validation_error

    # 检查饰品是否存在并可提取
    item = PackageItem.objects.filter(user=user, uid=item_id, state=PackageState.Available.value).first()
    if not item:
        return RespCode.InvalidParams.value, _('饰品不存在或不可提取')
    
    if item.part:
        return RespCode.BusinessError.value, _('此饰品只能出售')

    # 获取价格并验证
    price = get_item_price(item.item_info.market_hash_name)
    if price < get_withdraw_price_limit():
        return RespCode.BusinessError.value, _('请提取{}金币以上的饰品'.format(get_withdraw_price_limit()))

    formatted_price = format_price(price)

    # 尝试进行提取操作
    try:
        with transaction.atomic():
            items = PackageItem.update_state(
                [item_id], 
                PackageState.Available.value, 
                PackageState.Withdrawing.value, 
                user
            )
            item = items[0]  # 更新后的item
            resp = send_withdraw_request(item, price)
            _logger.info('用户 {} 提取饰品  {} ，价格 {} '.format(user.steam.personaname, item.item_info.market_hash_name, price))   
            return RespCode.Succeed.value, resp
    except ParamException as e:
        return RespCode.InvalidParams.value, str(e)
    

# 获取用户 Steam 信息逻辑
def get_user_steam_info(trade_url, steam_id=None):
    """
    调用 ZBT API 获取用户 Steam 信息。
    
    :param trade_url: 用户的 Steam 交易链接
    :param steam_id: 用户的 Steam ID（可选）
    :return: 包含用户 Steam 信息的字典，或 None
    """
    try:
        # 初始化 ZbtApi 实例
        zbt_api = ZbtApi(app_key=settings.ZBT_APP_KEY, appid='730')

        # 创建 Steam 检查任务
        if not zbt_api.create_steam_check(trade_url=trade_url):
           # _logger.warning("创建 Steam 检查任务失败，trade_url=%s", trade_url)
            return None

        # 查询 Steam 用户信息
        steam_info = zbt_api.get_steam_user_info(trade_url=trade_url, steam_id=steam_id)
        if not steam_info:
            # _logger.warning("获取 Steam 用户信息失败，trade_url=%s, steam_id=%s", trade_url, steam_id)
            return None

        # _logger.info("成功获取 Steam 信息，trade_url=%s: %s", trade_url, steam_info)
        return steam_info

    except Exception as e:
        _logger.exception("获取 Steam 信息时发生错误: %s", e)
        return None