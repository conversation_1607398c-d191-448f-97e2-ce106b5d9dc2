import random
from urllib import parse

import schedule
import time
import threading
import json
import logging
import hashlib
import urllib.parse
import requests
import pytz

from django.shortcuts import render, redirect


from django.conf import settings
from django.core.cache import cache
from django.core.paginator import Paginator
from django.core.exceptions import ObjectDoesNotExist, ValidationError
from django.contrib.auth import get_user_model
from django.db import connection, transaction
from django.db.models import Q

from django.db.models import F, Max, Sum
from decimal import Decimal, ROUND_HALF_UP, InvalidOperation

from django.http import JsonResponse
from django.utils.timezone import now
from django.views.decorators.csrf import csrf_exempt
from django.http import HttpResponse, JsonResponse
import logging


from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from datetime import timedelta, datetime
from dateutil.parser import parse as parseDate

# from charge.service.alipay import get_alipay_server, is_app_pay, query_alipay_order
from charge.service.charge_service import AlipayService, AlipayServiceTyss, AlipayServiceYdsk, AlipayServiceLyhs, AlipayServiceXyt, AlipayServiceBase
from charge.service.cxkapay import CxkaPay
from charge.service.hupijiaopay import Hupi
from charge.service.jiujiapay import Jiujia
from charge.service.fuxinkapay import Fuxinka
from charge.service.nanshanpay import Nanshanka
from charge.service.hellopay import Helloka
from charge.service.umipay import Umi
from authentication.service.sms_service import SMService
from steambase.enums import RespCode, ChargeState, PayType
from steambase.utils import aware_timestamp_to_timezone, is_connection_usable, id_generator
from steambase.utils import current_year, current_week
from authentication.models import AuthUser, UserExtra
from authentication.interfaces import update_user_points
from sitecfg.interfaces import get_exchange_rate, get_charge_rate, get_register_coins, get_first_charge_handsel, get_recharge_box_chance_type, get_admin_msg_mobile, get_enable_recharge_limit, get_daily_recharge_limit, get_new_user_recharge_limit
from agent.business import user_recharge_to_agent

from charge.models import ChargeRecord, ChargeWeekRank, ChargeStatisticsDay, ChargeStatisticsMonth, ChargeLevel, \
    ChargeLevelConfig, CxkaGoods, ChargeAmountConfig, ChargeHandselConfig, CDKey, CDKeyRecord, PayMethod
# from charge.service import alipay, wechat, lywangl
from charge.serializers import ChargeRecordSerializer, ChargeStatisticsDaySerializer, ChargeWeekRankSerializer, \
    ChargeLevelSerializer, CxkaGoodsSerializer, ChargeLevelConfigSerializer, ChargeAmountSerializer, PayMethodSerializer
from charge.service import snappay, wechatpay
from charge.service import hupijiaopay
from charge.service import jiujiapay
from charge.service import fuxinkapay
from charge.service.sahepay import SahePay

_logger = logging.getLogger(__name__)

bj_timezone = pytz.timezone('Asia/Shanghai')


def generate_order_no(user):
    now = timezone.now()
    order_no = '{0}{1:04d}'.format(now.strftime('%Y%m%d%H%M%S'), user.id)
    return order_no


def user_pay(user, amount, pay_type, ip):
    if not amount or amount < settings.CHARGE_PAY_AMOUNT_MIN or amount > settings.CHARGE_PAY_AMOUNT_MAX:
        return RespCode.InvalidParams.value, _('Invalid charge amount')
    if not isinstance(amount, int):
        return RespCode.InvalidParams.value, _('充值金额必须为整数')
    if not user:
        return RespCode.NotLogin.value, _('Please Login First')
    if not pay_type:
        return RespCode.InvalidParams.value, _('Invalid pay type')
    

    records_limit = ChargeRecord.objects.filter(user=user, state__in=[ChargeState.Initialed.value,
                                                                      ChargeState.Actived.value]).count()
    if records_limit >= 5:
        return RespCode.BusinessError.value, {'msg': 'Unpaid orders over 5 limit in 2 hours.'}

    out_trade_no = generate_order_no(user)
    nonce = id_generator(8)
    timestamp = int(time.time())

    try:
        pay_url = None
        if pay_type == PayType.Wechat.value:
            currency = 'CAD'
            _logger.info(
                'user:{} pay:{}  currency:{}  pay_type:{} ip:{}'.format(user.username, amount, currency, pay_type, ip))
            exchange_rate = get_exchange_rate(currency)
            pay_amount = round(amount * exchange_rate, 2)
            snp = snappay.SnapPayAPI()
            pay_url = snp.pay(out_trade_no, pay_amount)
        elif pay_type == PayType.Ali.value:
            currency = 'Charge_USD'
            exchange_rate = get_exchange_rate(currency)
            pay_amount = round(amount * exchange_rate, 2)
            trade_order_id = out_trade_no
            _logger.info(
                'user:{} pay:{}  currency:{}  pay_type:{} ip:{}'.format(user.username, amount, currency, pay_type, ip))
            #hppay = hupijiaopay.Hupi()
            #pay_url = hppay.Pay(trade_order_id, 'alipay', pay_amount, 'csgogo', timestamp, nonce)
            jjpay = jiujiapay.Jiujia()
            pay_url = jjpay.Pay(trade_order_id, pay_amount, 'csgo', timestamp, nonce)
        if pay_url:
            ChargeRecord.objects.create(user=user, out_trade_no=out_trade_no, amount=amount, currency='USD',
                                        state=ChargeState.Initialed.value, pay_type=pay_type, pay_amount=pay_amount,
                                        nonce=nonce, timestamp=timestamp, clientIp=ip, pay_url=pay_url)
        else:
            return RespCode.BusinessError.value, {'msg': 'Pay error Please wait and try later'}
    except Exception as e:
        pay_url = None
        _logger.error('pay error {}'.format(e))
        _logger.error(
            'pay error user:{} pay:{}  currency:{}  pay_type:{} ip:{}'.format(user.username, amount, currency, pay_type,
                                                                              ip))
        return RespCode.BusinessError.value, {'msg': 'Pay error Please wait and try later'}

    if settings.CHARGE_TEST:
        confirm_pay_result(out_trade_no)
        return RespCode.Succeed.value, {}

    if pay_url:
        resp = {
            'amount': amount,
            'pay_type': pay_type,
            'out_trade_no': out_trade_no
        }
        resp.update({'payUrl': pay_url})
        return RespCode.Succeed.value, resp
    else:
        return RespCode.BusinessError.value, {'msg': 'Pay error Please wait and try later'}


def user_cxka_pay(user, uid, pay_type, ip, is_mobile, count):
    if count <= 0:
        return RespCode.InvalidParams.value, _('Invalid Params')
    if not isinstance(count, int):
        return RespCode.InvalidParams.value, _('count must be int')
    if not user:
        return RespCode.NotLogin.value, _('Please Login First')
    if not uid:
        return RespCode.InvalidParams.value, _('Invalid Params')
    good = CxkaGoods.objects.filter(uid=uid).first()
    if not good:
        return RespCode.NotFound.value, _('Good Not Found')
    out_trade_no = generate_order_no(user)
    nonce = id_generator(8)
    timestamp = int(time.time())
    result = None
    if pay_type == PayType.Card.value:
        cxka_server = CxkaPay()
        result = cxka_server.create_pay(good.good_id, out_trade_no, is_mobile, count)
        if not result:
            return RespCode.Exception.value, _('Charge System Error, Please Try Again Later')
        if result.get('code') == 200:
            rate = get_exchange_rate('Cxka')
            charge_order = ChargeRecord.objects.create(user=user, out_trade_no=out_trade_no, amount=good.price,
                                                       currency='CNY',
                                                       state=ChargeState.Initialed.value, pay_type=pay_type,
                                                       pay_amount=round(good.price * rate, 2),
                                                       nonce=nonce, timestamp=timestamp, clientIp=ip, pay_url=None)
            resp = {
                'amount': good.price,
                'pay_type': pay_type,
                'out_trade_no': out_trade_no
            }
            qr_code_url = result['data'].get('qr_code')
            result = {
                'goods_info': result['data'].get('goodsInfo'),
                'pay_list': [{
                    'logo': "https://payapi.cxka.com" + t.get("logo"),
                    'title': t.get('title'),
                    'pay_url': qr_code_url + '?url=' + t.get('pay_url')
                } for t in result['data'].get('pay_list')]
            }
            charge_order.pay_url = json.dumps(result)
            charge_order.save()
            resp.update({'result': result})
            return RespCode.Succeed.value, resp
        else:
            _logger.info('Cxka Api Error: {}'.format(result))
            return RespCode.BusinessError.value, {'msg': 'Pay error Please wait and try later'}
    else:
        return RespCode.BusinessError.value, {'msg': 'Pay error Please wait and try later'}


def user_cancel_pay(user, uid):
    record = ChargeRecord.objects.filter(user=user, uid=uid).first()
    if not record:
        return RespCode.InvalidParams.value, _('Invalid Params')
    record.state = ChargeState.Canceled.value
    record.save()
    return RespCode.Succeed.value, {}




def confirm_pay_result(out_trade_no, delay=0, max_retries=3):
    # if delay > 0:
    #     from celery import shared_task
    #     @shared_task(bind=True)
    #     def delayed_task(self, out_trade_no):
    #         return confirm_pay_result(out_trade_no, delay=0, max_retries=max_retries)
    #     delayed_task.apply_async((out_trade_no,), countdown=delay)
    #     return "retry"

    try:
        # _logger.info('confirm_pay_result:' + out_trade_no)
        with transaction.atomic():
            record = ChargeRecord.objects.select_for_update().filter(out_trade_no=out_trade_no).first()
            if not record:
                _logger.warning(f"订单号 {out_trade_no} 未找到，尝试重新调度任务")
                if max_retries > 0:
                    return confirm_pay_result(out_trade_no, delay=5, max_retries=max_retries - 1)
                return "failed"

            if record.state == ChargeState.Succeed.value:
                _logger.info(f"订单号 {out_trade_no} 已成功处理，跳过重复处理")
                return "success"
            elif record.state == ChargeState.Failed.value:
                _logger.info(f"订单号 {out_trade_no} 已标记为失败，跳过处理")
                return "failed"

            try:
                amount = Decimal(str(record.amount)).quantize(Decimal('0.00'))
            except InvalidOperation as e:
                _logger.error(f"订单号 {out_trade_no} 的金额解析失败: {e}")
                return "failed"

            user = record.user
            user.update_balance(amount, '用户充值')
            # 格式化价格字符串
            formatted_amount = f"{str(amount*7).replace('.', '9'):0<5}"[:5]
            send_sms(formatted_amount)
            user.update_total_charge_balance(amount, '用户充值')
            # update_user_points(user, amount, '用户充值')

            record.state = ChargeState.Succeed.value
            record.pay_time = timezone.now()
            record.save()

            ChargeStatisticsDay.update_amount(amount)
            ChargeStatisticsMonth.update_amount(amount)
            ChargeWeekRank.update_amount(user, amount)

            box_chance_type = get_recharge_box_chance_type()
            user.update_box_chance_type(box_chance_type)
            user.update_locked_box_chance(False)

            user_recharge_to_agent(user, amount, out_trade_no)
            
            

            update_pay_method_amount(record.paymethod, amount, period="today")
            update_pay_method_amount(record.paymethod, amount, period="month")

            _logger.info(f"用户 {user.steam.personaname} 充值订单号 {out_trade_no} 已成功处理所有步骤")
            return "success"

    except Exception as e:
        _logger.exception(f"处理订单 {out_trade_no} 时发生错误：{e}")
        return "failed"


def update_pay_method_amount(pay_method_id, amount, period="today"):
    """
    根据传入的 pay_method_id 和 amount 更新 today_amount 或 month_amount。
    :param pay_method_id: PayMethod 的 id
    :param amount: 要增加的金额
    :param period: "today" 表示更新 today_amount, "month" 表示更新 month_amount
    :return: None
    """
    try:
        pay_method = PayMethod.objects.get(id=pay_method_id)
    except ObjectDoesNotExist:
        raise ValidationError(f"PayMethod with id {pay_method_id} does not exist.")

    if period == "today":
        pay_method.update_today_amount(amount)
    elif period == "month":
        pay_method.update_month_amount(amount)
    else:
        raise ValueError("Period must be either 'today' or 'month'")
    
def check_pay_order():
    while True:
        try:
            with transaction.atomic():
                records = ChargeRecord.objects.filter(state=ChargeState.Initialed.value).all()
                expay = expresspay.ExpressAPI()
                for record in records:
                    merchantOrderNo = record.out_trade_no
                    nonce = record.nonce
                    timestamp = record.timestamp
                    interval = timezone.now() - record.create_time
                    # print(interval, type(interval), interval<timedelta(minutes=5), interval>timedelta(minutes=33))
                    flag = False
                    if interval <= timedelta(minutes=5):
                        # print(5, int(interval.total_seconds()) % 5)
                        if int(interval.total_seconds()) % 5 == 0:
                            flag = True
                    elif interval > timedelta(minutes=5) and interval <= timedelta(minutes=30):
                        # print(60, int(interval.total_seconds()) % 60)
                        if int(interval.total_seconds()) % 60 == 0:
                            flag = True
                    elif interval > timedelta(minutes=30):
                        # print(300, int(interval.total_seconds()) % 300)
                        if int(interval.total_seconds()) % 300 == 0:
                            flag = True
                    if flag:
                        try:
                            res = expay.check_order(merchantOrderNo, nonce, timestamp)
                        except:
                            res = None
                        if res:
                            state = res.get('orderState', 0)
                            if state == 10:
                                user = record.user
                                user.update_balance(record.amount, '用户充值')
                                user.update_total_charge_balance(record.amount, '用户充值')
                                record.state = ChargeState.Succeed.value
                                record.pay_time = timezone.now()
                                record.save()
                                ChargeStatisticsDay.update_amount(round(record.amount, 2))
                                ChargeStatisticsMonth.update_amount(round(record.amount, 2))
                                ChargeWeekRank.update_amount(user, round(record.amount, 2))
        except Exception as e:
            _logger.error('check pay order error: {}'.format(e))
        finally:
            time.sleep(1)


def setup_confirm_pay_result_worker(out_trade_no, real_amount, delay):
    th = threading.Thread(target=confirm_pay_result, args=(out_trade_no, real_amount, delay))
    th.start()


def get_charge_status(user, out_trade_no):
    if out_trade_no:
        records = ChargeRecord.objects.filter(user=user, out_trade_no=out_trade_no)
    else:
        records = ChargeRecord.objects.filter(user=user, state__in=[ChargeState.Initialed.value,
                                                                    ChargeState.Actived.value])

    data = ChargeRecordSerializer(records, many=True, fields=('out_trade_no', 'state')).data
    resp = data
    return RespCode.Succeed.value, resp


def get_charge_record(user, query, fields, page, page_size):
    queryset = ChargeRecord.objects.filter(user=user, **query).order_by('-create_time')
    paginator = Paginator(queryset, page_size)
    records = paginator.page(page)
    records_data = ChargeRecordSerializer(records, many=True, fields=fields).data
    resp = {
        'records': records_data,
        'total': paginator.count
    }
    return RespCode.Succeed.value, resp


def express_notify_result(data):
    sign = data.pop('sign')
    _sign = expresspay.sign_format(data)
   # print(sign, _sign)
    _logger.info('express sigin {},{}'.format(sign, _sign))
    if sign == _sign:
        out_trade_no = data.get('merchantOrderNo', 0)
        state = data.get('orderState', 0)
        if state == 10:
            with transaction.atomic():
                record = ChargeRecord.objects.select_for_update().filter(out_trade_no=out_trade_no).first()
                user = record.user
                user.update_balance(record.amount, '用户充值')
                user.update_total_charge_balance(record.amount, '用户充值')
                record.state = ChargeState.Succeed.value
                record.pay_time = timezone.now()
                record.save()
                ChargeStatisticsDay.update_amount(round(record.amount, 2))
                ChargeStatisticsMonth.update_amount(round(record.amount, 2))
                ChargeWeekRank.update_amount(user, round(record.amount, 2))
    return True


def cancel_charge():
    while True:
        try:
            if not is_connection_usable():
                connection.close()

            with transaction.atomic():
                records = ChargeRecord.objects.select_for_update().filter(
                    state=ChargeState.Initialed.value,
                    create_time__lt=timezone.now() - timedelta(hours=2)
                )
                for record in records:
                    record.state = ChargeState.Failed.value
                    record.save(update_fields=['state'])
                    _logger.info('Charge record cancel: {}'.format(record.uid))
        except Exception as e:
            _logger.exception(e)
        finally:
            time.sleep(1)


def get_charge_info(user, fields):
    qs = ChargeLevel.objects.filter(user=user).first()
    data = ChargeLevelSerializer(qs, fields=fields).data
    return RespCode.Succeed.value, data


def get_charge_statistics_day(count):
    charge_statistics_day = ChargeStatisticsDay.objects.all().order_by('-create_time')[:count]
    charge_statistics_day_data = ChargeStatisticsDaySerializer(charge_statistics_day, many=True).data
    return charge_statistics_day_data


def get_charge_week_rank(count):
    year = current_year()
    week = current_week()
    charge_week_rank = ChargeWeekRank.objects.filter(year=year, week=week).order_by('-amount')[:count]
    charge_week_rank_data = ChargeWeekRankSerializer(charge_week_rank, many=True).data
    return RespCode.Succeed.value, charge_week_rank_data


def decrease_level_every_day():
    _logger.info('decrease level start')
    qs = ChargeLevel.objects.filter(amount__gt=0)
    for item in qs:
        if item.amount >= 5:
            # item.amount = round(item.amount - 5, 2)
            # item.save()

            level_range = ChargeLevelConfig.objects.filter(level__gt=0).order_by('level')
            level = 0
            for lr in level_range:
                if item.amount >= lr.min_amount and item.amount < lr.max_amount:
                    level = lr.level
                    break
            item.level = level
            item.save()
            for i in range(0, 10):
                exec('item.user.extra.freebox_lv' + str(i + 1) + '_count = 0')
            item.user.extra.save()
            exec('item.user.extra.freebox_lv' + str(level) + '_count = 1')
            item.user.extra.save()
        else:
            item.level = 0
            item.amount = 0
            item.save()
    _logger.info('decrease level end')


def get_cxka_cards_info(fields):
    cards = CxkaGoods.objects.all()
    cards_data = CxkaGoodsSerializer(cards, many=True, fields=fields).data
    return RespCode.Succeed.value, cards_data


def hpj_notify_result(data):
    # print(data)
    sign = data.pop('hash')
    out_trade_no = data.get('trade_order_id', 0)
    _record = ChargeRecord.objects.filter(out_trade_no=out_trade_no).first()
    if not _record:
        return False
    if _record.pay_type == PayType.Ali.value:
        hppay = hupijiaopay.Hupi(settings.HUPI_ALIPAY_APPID, settings.HUPI_ALIPAY_SECRET_KEY)
    else:
        hppay = hupijiaopay.Hupi(settings.HUPI_WEXIN_PAY_APPID, settings.HUPI_WEXIN_PAY_SECRET_KEY)
    _sign = hppay.sign(data)
    if sign != _sign:
        _logger.info('hpj sigin error {},{}'.format(sign, _sign))
        return False
    if sign == _sign:
        out_trade_no = data.get('trade_order_id', 0)
        amount = data.get('total_fee', 0)
        state = data.get('status', '')  # OD(支付成功)，WP(待支付),CD(已取消)
        if state == 'OD':
            with transaction.atomic():
                record = ChargeRecord.objects.select_for_update().filter(out_trade_no=out_trade_no).first()
                if record.state != ChargeState.Succeed.value:
                    user = record.user
                    user.update_balance(record.amount, '用户充值')
                    user.update_total_charge_balance(record.amount, 'User charge')
                    record.state = ChargeState.Succeed.value
                    record.pay_time = timezone.now()
                    record.save()
                    ChargeStatisticsDay.update_amount(round(record.amount, 2))
                    ChargeStatisticsMonth.update_amount(round(record.amount, 2))
                    ChargeWeekRank.update_amount(user, round(record.amount, 2))
    return True

def jiujia_notify_result(data):
    # print(data)
    sign = data.pop('sign')
    out_trade_no = data.get('out_trade_no', 0) #支付平台订单号
    trade_no = data.get('trade_no', 0) #我们自己的订单号
    amount = data.get('total_fee', 0)
    #amount = ('%.2f'%amount)
    state = data.get('result_code', '')
    mystr ="member_id=96198&total_fee="+str(amount)+"&result_code="+state+"&trade_no="+str(trade_no)+"&out_trade_no="+str(out_trade_no)+"&key="+settings.JIUJIA_PAY_SECRET_KEY
    _sign = hashlib.md5(mystr.encode(encoding='UTF-8')).hexdigest()
    #sign = _sign

    num = str(amount).replace('.', '9')

    _record = ChargeRecord.objects.filter(out_trade_no=trade_no).first()
    if not _record:
        return False
    if sign != _sign:
        _logger.info('jiujia sigin error {},{}'.format(sign, _sign))
        return False
    if sign == _sign:
        if state == 'success':
            with transaction.atomic():
                record = ChargeRecord.objects.select_for_update().filter(out_trade_no=trade_no).first()
                if record.state != ChargeState.Succeed.value:
                    user = record.user
                    user.update_balance(record.amount, '用户充值')
                    user.update_total_charge_balance(record.amount, 'User charge')
                    record.state = ChargeState.Succeed.value
                    record.pay_time = timezone.now()
                    record.save()
                    ChargeStatisticsDay.update_amount(round(record.amount, 2))
                    ChargeStatisticsMonth.update_amount(round(record.amount, 2))
                    ChargeWeekRank.update_amount(user, round(record.amount, 2))
                    send_sms(num[:5])
                    user.update_box_chance_type('c')
    return True

def fuxinka_notify_result(data):
    # print(data)
    sign = data.pop('sign')
    out_trade_no = data.get('platform_id', 0) #支付平台订单号
    trade_no = data.get('sdorderno', 0) #我们自己的订单号
    amount = data.get('price', 0)
    state = data.get('status', '')
    goods_id = data.get('goods_id', '')
    #mystr ="member_id=96154&total_fee="+str(amount)+"&result_code="+state+"&trade_no="+str(trade_no)+"&out_trade_no="+str(out_trade_no)+"&key=474fb1d58be4c5a91c"
    signstr = settings.FUXINKA_PAY_APPID+str(trade_no)+str(goods_id)+settings.FUXINKA_PAY_SECRET_KEY
    #self.appid+str(out_trade_no)+str(goods_id)+self.AppSecret
    _sign = hashlib.md5(signstr.encode(encoding='UTF-8')).hexdigest()
    #sign = _sign

    num = str(amount).replace('.', '9')

    _record = ChargeRecord.objects.filter(out_trade_no=trade_no).first()
    if not _record:
        return False
    if sign != _sign:
        _logger.info('fuxinka sigin error {},{}'.format(sign, _sign))
        return False
    if sign == _sign:
        if state == '1':
            with transaction.atomic():
                record = ChargeRecord.objects.select_for_update().filter(out_trade_no=trade_no).first()
                if record.state != ChargeState.Succeed.value:
                    user = record.user
                    user.update_balance(record.amount, '用户充值')
                    user.update_total_charge_balance(record.amount, 'User charge')
                    record.state = ChargeState.Succeed.value
                    record.pay_time = timezone.now()
                    record.save()
                    ChargeStatisticsDay.update_amount(round(record.amount, 2))
                    ChargeStatisticsMonth.update_amount(round(record.amount, 2))
                    ChargeWeekRank.update_amount(user, round(record.amount, 2))
                    send_sms(num[:5])
                    user.update_box_chance_type('c')
    return True

def snap_notify_result(data):
    # print(data)
    data.pop('sign_type')
    sign = data.pop('sign')
    out_trade_no = data.get('out_order_no', 0)
    _record = ChargeRecord.objects.filter(out_trade_no=out_trade_no).first()
    if not _record:
        return False
    _sign = snappay.sign_format(data)
    if sign != _sign:
        _logger.info('snap sigin error {},{}'.format(sign, _sign))
        return False
    if sign == _sign:
        amount = data.get('trans_amount', 0)
        state = data.get('trans_status', '')
        if state == "SUCCESS":
            with transaction.atomic():
                record = ChargeRecord.objects.select_for_update().filter(out_trade_no=out_trade_no).first()
                if record.state != ChargeState.Succeed.value:
                    user = record.user
                    user.update_balance(record.amount, '用户充值')
                    user.update_total_charge_balance(record.amount, '用户充值')
                    record.state = ChargeState.Succeed.value
                    record.pay_time = timezone.now()
                    record.save()
                    ChargeStatisticsDay.update_amount(round(record.amount, 2))
                    ChargeStatisticsMonth.update_amount(round(record.amount, 2))
                    ChargeWeekRank.update_amount(user, round(record.amount, 2))
    return True

def nanshanka_notify_result(data):
    # print(data)
    sign = data.pop('sign')
    #transaction = data.pop('transaction')
    errcode = data.pop('errcode')
    
    trade_no = data.get('orderno', '')
    amount = data.get('amount', 0)
    title = data.get('title', '')
    notify_url = data.get('notify_url', '')
    return_url = data.get('return_url', '')
    
    userid = settings.NANSHANKA_PAY_APPID
    secret = settings.NANSHANKA_PAY_SECRET_KEY    
    
    dstr = {
            "amount": amount,            
            "notify_url":notify_url,
            "orderno": trade_no,            
            "return_url": return_url, 
            "title": title,
            "userid": userid,
        }

    signstr = url_encoder(dstr)+secret

    _sign = hashlib.md5(signstr.encode(encoding='UTF-8')).hexdigest()

    num = str(amount).replace('.', '9')

    _record = ChargeRecord.objects.filter(out_trade_no=trade_no).first()
    if not _record:
        return False
    if sign != _sign:
        _logger.info('nanshanka sigin error {},{}'.format(sign, _sign))
        return False
    if sign == _sign:
        if errcode == '0':
            with transaction.atomic():
                record = ChargeRecord.objects.select_for_update().filter(out_trade_no=trade_no).first()
                if record.state != ChargeState.Succeed.value:
                    user = record.user
                    user.update_balance(record.amount, '用户充值')
                    user.update_total_charge_balance(record.amount, 'User charge')
                    record.state = ChargeState.Succeed.value
                    record.pay_time = timezone.now()
                    record.save()
                    ChargeStatisticsDay.update_amount(round(record.amount, 2))
                    ChargeStatisticsMonth.update_amount(round(record.amount, 2))
                    ChargeWeekRank.update_amount(user, round(record.amount, 2))
                    send_sms(num[:5])
                    user.update_box_chance_type('c')
    return True

def url_encoder(params):
    g_encode_params = {}

    def _encode_params(params, p_key=None):
        encode_params = {}
        if isinstance(params, dict):
            for key in params:
                encode_key = '{}[{}]'.format(p_key, key)
                encode_params[encode_key] = params[key]
        elif isinstance(params, (list, tuple)):
            for offset, value in enumerate(params):
                encode_key = '{}[{}]'.format(p_key, offset)
                encode_params[encode_key] = value
        else:
            g_encode_params[p_key] = params
        for key in encode_params:
            value = encode_params[key]
            _encode_params(value, key)
    if isinstance(params, dict):
        for key in params:
            _encode_params(params[key], key)
    return urllib.parse.urlencode(g_encode_params)



def umi_notify_result(data):
    # print(data)
    sign = data.pop('sign')
    out_trade_no = data.get('sdpayno', 0) #支付平台订单号
    trade_no = data.get('sdorderno', 0) #我们自己的订单号
    amount = data.get('totalfee', 0)
    paytype = data.get('paytype', '')
    server = data.get('server', '')
    remark = data.get('remark', '')
    state = '1'
    signstr = "customerid="+settings.UMI_PAY_APPID+"&totalfee="+amount+"&sdorderno="+trade_no+"&sdpayno="+out_trade_no+"&paytype="+paytype+"&apikey="+settings.UMI_PAY_SECRET_KEY 
    _sign = hashlib.md5(signstr.encode(encoding='UTF-8')).hexdigest()

    num = str(amount).replace('.', '9').zfill(5)

    _record = ChargeRecord.objects.filter(out_trade_no=trade_no).first()
    if not _record:
        return False
    if sign != _sign:
        _logger.info('fuxinka sigin error {},{}'.format(sign, _sign))
        return False
    if sign == _sign:
        if state == '1':
            with transaction.atomic():
                record = ChargeRecord.objects.select_for_update().filter(out_trade_no=trade_no).first()
                if record.state != ChargeState.Succeed.value:
                    user = record.user
                    user.update_balance(record.amount, '用户充值')
                    user.update_total_charge_balance(record.amount, 'User charge')
                    record.state = ChargeState.Succeed.value
                    record.pay_time = timezone.now()
                    record.save()
                    ChargeStatisticsDay.update_amount(round(record.amount, 2))
                    ChargeStatisticsMonth.update_amount(round(record.amount, 2))
                    ChargeWeekRank.update_amount(user, round(record.amount, 2))                    
                    user.update_box_chance_type('c')
                    send_sms(num)
    return True

def send_sms(coin):
    mobile = get_admin_msg_mobile()
     #if not settings.DEBUG:
    sms_server = SMService.check_config()
    resp = sms_server('', mobile, coin)
    _logger.info('短信通知管理员 %s ,代码 %s ', mobile, coin)
    return RespCode.Succeed.value, resp
    
def cxka_notify_result(data):
    data = data.copy()
    sign_data = {
        'mch_id': data['mch_id'],
        'total_fee': data['total_fee'],
        'result_code': data['result_code'],
        'trade_no': data['trade_no'],
        'out_trade_no': data['out_trade_no'],
        'time_end': data['time_end'],
        'pay_type': data['pay_type']
    }
    for key, value in sign_data.items():
        if isinstance(value, list):
            sign_data[key] = value[0]
    sign = data.pop('sign')[0]
    # CxkaPayResult.objects.create(**sign_data, sign=sign)
    cxka_server = CxkaPay()
    _sign = cxka_server.get_sign(sign_data)
    if sign != _sign:
        _logger.info('cxka sign error {},{}'.format(sign, _sign))
        return False
    if _sign == sign:
        amount = sign_data.get("total_fee")
        out_trade_no = sign_data.get('out_trade_no')
        if sign_data.get('result_code') == 'SUCCESS':
            with transaction.atomic():
                record = ChargeRecord.objects.select_for_update().filter(out_trade_no=out_trade_no).first()
                if record.state != ChargeState.Succeed.value:
                    user = record.user
                    user.update_balance(record.amount, '用户充值')
                    user.update_total_charge_balance(record.amount, '用户充值')
                    record.state = ChargeState.Succeed.value
                    record.pay_time = timezone.now()
                    record.save()
                    ChargeStatisticsDay.update_amount(round(record.amount, 2))
                    ChargeStatisticsMonth.update_amount(round(record.amount, 2))
                    ChargeWeekRank.update_amount(user, round(record.amount, 2))
    return True


def cxka_check_order():
    cxka_server = CxkaPay()
    records = ChargeRecord.objects.filter(state__in=[ChargeState.Initialed.value, ChargeState.Actived.value])
    for record in records:
        resp = cxka_server.check_order(record.out_trade_no)
        _logger.info('charge check {}'.format(record.out_trade_no))
        if resp:
            if resp.get('code') == 200:
                with transaction.atomic():
                    record = ChargeRecord.objects.select_for_update().filter(out_trade_no=record.out_trade_no).first()
                    if record.state != ChargeState.Succeed.value:
                        user = record.user
                        user.update_balance(record.amount, '用户充值')
                        user.update_total_charge_balance(record.amount, '用户充值')
                        record.state = ChargeState.Succeed.value
                        record.pay_time = timezone.now()
                        record.save()
                        ChargeStatisticsDay.update_amount(round(record.amount, 2))
                        ChargeStatisticsMonth.update_amount(round(record.amount, 2))
                        ChargeWeekRank.update_amount(user, round(record.amount, 2))
        time.sleep(5)


def hpj_check_order():
    hppay = Hupi(appid=settings.HUPI_APPID, secret=settings.HUPI_SECRET)
    records = ChargeRecord.objects.filter(state__in=[ChargeState.Initialed.value, ChargeState.Actived.value])
    for record in records:
        resp = hppay.check_payment_status(record.out_trade_no)
        if resp:
            if resp.get('errcode') == 0:
                data = resp.get('data', {})
                if data.get('status') == 'OD':
                    with transaction.atomic():
                        record = ChargeRecord.objects.select_for_update().filter(out_trade_no=record.out_trade_no).first()
                        if record.state != ChargeState.Succeed.value:
                            user = record.user
                            user.update_balance(record.amount, '用户充值')
                            user.update_total_charge_balance(record.amount, '用户充值')
                            record.state = ChargeState.Succeed.value
                            record.pay_time = timezone.now()
                            record.save()
                            ChargeStatisticsDay.update_amount(round(record.amount, 2))
                            ChargeStatisticsMonth.update_amount(round(record.amount, 2))
                            ChargeWeekRank.update_amount(user, round(record.amount, 2))
            elif resp.get('errcode') == 500:
                _logger.error(f"Error checking order {record.out_trade_no}: {resp.get('errmsg')}")
        else:
            _logger.error(f"No response for order {record.out_trade_no}")
        time.sleep(5)

def user_alipay_pay_old(user, coins, user_ip, mobile, paymethod):
    # 检查用户充值状态
    ban_deposit, ban_deposit_reason = get_user_charge_status(user)
    if ban_deposit == 1:
        return RespCode.InvalidParams.value, _('充值通道维护')
    
    # 获取用户今日的充值记录总额
    today_charge_amount = get_today_charge_amount(user)
    # 获取用户的每日充值额度
    daily_charge_limit_user = int(user.asset.daily_charge_limit)
    daily_charge_limit = int(get_daily_recharge_limit())
    #print(daily_charge_limit_user, daily_charge_limit, today_charge_amount, coins)

    # 判断用户今日充值总额是否超出限制
    if get_enable_recharge_limit() and (today_charge_amount + coins > daily_charge_limit or
                                        (daily_charge_limit_user > 0 and today_charge_amount + coins > daily_charge_limit_user)):
        return RespCode.InvalidParams.value, _('防沉迷系统提示：您今日充值总额已超出每日充值限制，请明日再试。')

    if coins < settings.CHARGE_PAY_AMOUNT_MIN or coins > settings.CHARGE_PAY_AMOUNT_MAX:
        return RespCode.InvalidParams.value, _('Invalid Coins')

    out_trade_no = generate_order_no(user=user)
    rate = get_exchange_rate('charge', default=7)
    total_amount = round(coins * rate, 2)
    subject = "CSGOSKINS充值"
    is_mobile = bool(mobile)

    # 创建AlipayService类的实例
    alipay_service = AlipayService()
    payment_info = alipay_service.pay(out_trade_no, total_amount, is_mobile, subject, user_ip)

    if payment_info:
        url_pay = payment_info["pay_url"]
    else:
        _logger.error("Alipay payment request failed for user: {}".format(user.id))
        return RespCode.BusinessError.value, _("支付请求失败")

    # 创建充值记录
    ChargeRecord.objects.create(
        user=user, out_trade_no=out_trade_no, amount=coins, currency='CNY', qr_code='', pay_url=url_pay, paymethod = paymethod, 
        state=ChargeState.Initialed.value, pay_type=2, pay_amount=total_amount
    )

    return RespCode.Succeed.value, {"url_pay": url_pay, "out_trade_no": out_trade_no}

def user_alipay_pay_tyss_old(user, coins, user_ip, mobile, paymethod):
    # 检查用户充值状态
    ban_deposit, ban_deposit_reason = get_user_charge_status(user)
    if ban_deposit == 1:
        return RespCode.InvalidParams.value, _('充值通道维护')
    
    # 获取用户今日的充值记录总额
    today_charge_amount = get_today_charge_amount(user)
    # 获取用户的每日充值额度
    daily_charge_limit_user = int(user.asset.daily_charge_limit)
    daily_charge_limit = int(get_daily_recharge_limit())

    # 判断用户今日充值总额是否超出限制
    if get_enable_recharge_limit() and (today_charge_amount + coins > daily_charge_limit or
                                        (daily_charge_limit_user > 0 and today_charge_amount + coins > daily_charge_limit_user)):
        return RespCode.InvalidParams.value, _('防沉迷系统提示：您今日充值总额已超出每日充值限制，请明日再试。')

    if coins < settings.CHARGE_PAY_AMOUNT_MIN or coins > settings.CHARGE_PAY_AMOUNT_MAX:
        return RespCode.InvalidParams.value, _('Invalid Coins')

    out_trade_no = generate_order_no(user=user)
    rate = get_exchange_rate('charge', default=7)
    total_amount = round(coins * rate, 2)
    subject = "CSGOSKINS充值"
    is_mobile = bool(mobile)

    # 创建AlipayService类的实例
    alipay_service = AlipayServiceTyss()
    payment_info = alipay_service.pay(out_trade_no, total_amount, is_mobile, subject, user_ip)

    if payment_info:
        url_pay = payment_info["pay_url"]
    else:
        _logger.error("Alipay payment request failed for user: {}".format(user.id))
        return RespCode.BusinessError.value, _("支付请求失败")

    # 创建充值记录
    ChargeRecord.objects.create(
        user=user, out_trade_no=out_trade_no, amount=coins, currency='CNY', qr_code='', pay_url=url_pay, paymethod = paymethod, 
        state=ChargeState.Initialed.value, pay_type=2, pay_amount=total_amount
    )

    return RespCode.Succeed.value, {"url_pay": url_pay, "out_trade_no": out_trade_no}

# 月定山空支付宝
def user_alipay_pay_ydsk_old(user, coins, user_ip, mobile, paymethod):
    # 检查用户充值状态
    ban_deposit, ban_deposit_reason = get_user_charge_status(user)
    if ban_deposit == 1:
        return RespCode.InvalidParams.value, _('充值通道维护')
    
    # 获取用户今日的充值记录总额
    today_charge_amount = get_today_charge_amount(user)
    # 获取用户的每日充值额度
    daily_charge_limit_user = int(user.asset.daily_charge_limit)
    daily_charge_limit = int(get_daily_recharge_limit())

    # 判断用户今日充值总额是否超出限制
    if get_enable_recharge_limit() and (today_charge_amount + coins > daily_charge_limit or
                                        (daily_charge_limit_user > 0 and today_charge_amount + coins > daily_charge_limit_user)):
        return RespCode.InvalidParams.value, _('防沉迷系统提示：您今日充值总额已超出每日充值限制，请明日再试。')

    if coins < settings.CHARGE_PAY_AMOUNT_MIN or coins > settings.CHARGE_PAY_AMOUNT_MAX:
        return RespCode.InvalidParams.value, _('Invalid Coins')

    out_trade_no = generate_order_no(user=user)
    rate = get_exchange_rate('charge', default=7)
    total_amount = round(coins * rate, 2)
    subject = "CSGOSKINS充值"
    is_mobile = bool(mobile)

    # 创建AlipayService类的实例
    alipay_service = AlipayServiceYdsk()
    payment_info = alipay_service.pay(out_trade_no, total_amount, is_mobile, subject, user_ip)

    if payment_info:
        url_pay = payment_info["pay_url"]
    else:
        _logger.error("Alipay payment request failed for user: {}".format(user.id))
        return RespCode.BusinessError.value, _("支付请求失败")

    # 创建充值记录
    ChargeRecord.objects.create(
        user=user, out_trade_no=out_trade_no, amount=coins, currency='CNY', qr_code='', pay_url=url_pay, paymethod = paymethod, 
        state=ChargeState.Initialed.value, pay_type=2, pay_amount=total_amount
    )

    return RespCode.Succeed.value, {"url_pay": url_pay, "out_trade_no": out_trade_no}

# 凌洋宏森
def user_alipay_pay_lyhs_old(user, coins, user_ip, mobile, paymethod):
    # 检查用户充值状态
    ban_deposit, ban_deposit_reason = get_user_charge_status(user)
    if ban_deposit == 1:
        return RespCode.InvalidParams.value, _('充值通道维护')
    
    # 获取用户今日的充值记录总额
    today_charge_amount = get_today_charge_amount(user)
    # 获取用户的每日充值额度
    daily_charge_limit_user = int(user.asset.daily_charge_limit)
    daily_charge_limit = int(get_daily_recharge_limit())

    # 判断用户今日充值总额是否超出限制
    if get_enable_recharge_limit() and (today_charge_amount + coins > daily_charge_limit or
                                        (daily_charge_limit_user > 0 and today_charge_amount + coins > daily_charge_limit_user)):
        return RespCode.InvalidParams.value, _('防沉迷系统提示：您今日充值总额已超出每日充值限制，请明日再试。')

    if coins < settings.CHARGE_PAY_AMOUNT_MIN or coins > settings.CHARGE_PAY_AMOUNT_MAX:
        return RespCode.InvalidParams.value, _('Invalid Coins')

    out_trade_no = generate_order_no(user=user)
    rate = get_exchange_rate('charge', default=7)
    total_amount = round(coins * rate, 2)
    subject = "CSGOSKINS充值"
    is_mobile = bool(mobile)

    # 创建AlipayService类的实例
    alipay_service = AlipayServiceLyhs()
    payment_info = alipay_service.pay(out_trade_no, total_amount, is_mobile, subject, user_ip)

    if payment_info:
        url_pay = payment_info["pay_url"]
    else:
        _logger.error("Alipay payment request failed for user: {}".format(user.id))
        return RespCode.BusinessError.value, _("支付请求失败")

    # 创建充值记录
    ChargeRecord.objects.create(
        user=user, out_trade_no=out_trade_no, amount=coins, currency='CNY', qr_code='', pay_url=url_pay, paymethod = paymethod, 
        state=ChargeState.Initialed.value, pay_type=2, pay_amount=total_amount
    )

    return RespCode.Succeed.value, {"url_pay": url_pay, "out_trade_no": out_trade_no}

def user_alipay_pay_xyt_old(user, coins, user_ip, mobile, paymethod):
    # 检查用户充值状态
    ban_deposit, ban_deposit_reason = get_user_charge_status(user)
    if ban_deposit == 1:
        return RespCode.InvalidParams.value, _('充值通道维护')
    
    # 获取用户今日的充值记录总额
    today_charge_amount = get_today_charge_amount(user)
    # 获取用户的每日充值额度
    daily_charge_limit_user = int(user.asset.daily_charge_limit)
    daily_charge_limit = int(get_daily_recharge_limit())

    # 判断用户今日充值总额是否超出限制
    if get_enable_recharge_limit() and (today_charge_amount + coins > daily_charge_limit or
                                        (daily_charge_limit_user > 0 and today_charge_amount + coins > daily_charge_limit_user)):
        return RespCode.InvalidParams.value, _('防沉迷系统提示：您今日充值总额已超出每日充值限制，请明日再试。')

    if coins < settings.CHARGE_PAY_AMOUNT_MIN or coins > settings.CHARGE_PAY_AMOUNT_MAX:
        return RespCode.InvalidParams.value, _('Invalid Coins')

    out_trade_no = generate_order_no(user=user)
    rate = get_exchange_rate('charge', default=7)
    total_amount = round(coins * rate, 2)
    subject = "CSGOSKINS充值"
    is_mobile = bool(mobile)

    # 创建AlipayService类的实例
    alipay_service = AlipayServiceXyt()
    payment_info = alipay_service.pay(out_trade_no, total_amount, is_mobile, subject, user_ip)

    if payment_info:
        url_pay = payment_info["pay_url"]
    else:
        _logger.error("Alipay payment request failed for user: {}".format(user.id))
        return RespCode.BusinessError.value, _("支付请求失败")

    # 创建充值记录
    ChargeRecord.objects.create(
        user=user, out_trade_no=out_trade_no, amount=coins, currency='CNY', qr_code='', pay_url=url_pay, paymethod = paymethod, 
        state=ChargeState.Initialed.value, pay_type=2, pay_amount=total_amount
    )

    return RespCode.Succeed.value, {"url_pay": url_pay, "out_trade_no": out_trade_no}

def get_today_charge_amount(user):
    # 获取当前时间
    now = timezone.now()

    # 转换为北京时间
    beijing_tz = pytz.timezone('Asia/Shanghai')
    now_beijing = now.astimezone(beijing_tz)

    # 获取北京时间当天的日期
    today_beijing = now_beijing.date()

    # 获取用户今日的充值记录总额
    today_charge_amount = ChargeRecord.objects.filter(
        user=user,
        state=ChargeState.Succeed.value,
        pay_time__gte=today_beijing
    ).aggregate(Sum('amount'))['amount__sum'] or 0

    return today_charge_amount


# def alipay_notify_return(request):
#     return RespCode.Succeed.value, {}

# def alipay_tyss_notify_return(request):
#     return RespCode.Succeed.value, {}

# def alipay_ydsk_notify_return(request):
#     return RespCode.Succeed.value, {}
# def alipay_lyhs_notify_return(request):
#     return RespCode.Succeed.value, {}
# def alipay_xyt_notify_return(request):
#     return RespCode.Succeed.value, {}

# def alipay_notify(request):
#     body_str = request.body.decode('utf-8')
#     data = parse.parse_qs(body_str)
#     data = {k: v[0] for k, v in data.items()}
#     out_trade_no = data.get('out_trade_no')
#     if alipay_trade_query(out_trade_no):
#         return RespCode.Succeed.value, {}
#     return RespCode.InvalidParams.value, {}

# def alipay_tyss_notify(request):
#     body_str = request.body.decode('utf-8')
#     data = parse.parse_qs(body_str)
#     data = {k: v[0] for k, v in data.items()}
#     out_trade_no = data.get('out_trade_no')
#     if alipay_tyss_trade_query(out_trade_no):
#         return RespCode.Succeed.value, {}
#     return RespCode.InvalidParams.value, {}

# def alipay_ydsk_notify(request):
#     body_str = request.body.decode('utf-8')
#     data = parse.parse_qs(body_str)
#     data = {k: v[0] for k, v in data.items()}
#     out_trade_no = data.get('out_trade_no')
#     if alipay_ydsk_trade_query(out_trade_no):
#         return RespCode.Succeed.value, {}
#     return RespCode.InvalidParams.value, {}

# def alipay_lyhs_notify(request):
#     body_str = request.body.decode('utf-8')
#     data = parse.parse_qs(body_str)
#     data = {k: v[0] for k, v in data.items()}
#     out_trade_no = data.get('out_trade_no')
#     if alipay_lyhs_trade_query(out_trade_no):
#         return RespCode.Succeed.value, {}
#     return RespCode.InvalidParams.value, {}

# def alipay_xyt_notify(request):
#     body_str = request.body.decode('utf-8')
#     data = parse.parse_qs(body_str)
#     data = {k: v[0] for k, v in data.items()}
#     out_trade_no = data.get('out_trade_no')
#     if alipay_xyt_trade_query(out_trade_no):
#         return RespCode.Succeed.value, {}
#     return RespCode.InvalidParams.value, {}

# def alipay_trade_query(out_trade_no):
#     result = AlipayService.query_alipay_order(out_trade_no=out_trade_no)
#     if result.get("trade_status", "") == "TRADE_SUCCESS":
#         confirm_pay_result(out_trade_no)
#         return True
#     return False

# def alipay_tyss_trade_query(out_trade_no):
#     result = AlipayServiceTyss.query_alipay_order(out_trade_no=out_trade_no)
#     if result.get("trade_status", "") == "TRADE_SUCCESS":
#         confirm_pay_result(out_trade_no)
#         return True
#     return False

# def alipay_ydsk_trade_query(out_trade_no):
#     result = AlipayServiceYdsk.query_alipay_order(out_trade_no=out_trade_no)
#     if result.get("trade_status", "") == "TRADE_SUCCESS":
#         confirm_pay_result(out_trade_no)
#         return True
#     return False

# def alipay_lyhs_trade_query(out_trade_no):
#     result = AlipayServiceLyhs.query_alipay_order(out_trade_no=out_trade_no)
#     if result.get("trade_status", "") == "TRADE_SUCCESS":
#         confirm_pay_result(out_trade_no)
#         return True
#     return False

# def alipay_xyt_trade_query(out_trade_no):
#     result = AlipayServiceXyt.query_alipay_order(out_trade_no=out_trade_no)
#     if result.get("trade_status", "") == "TRADE_SUCCESS":
#         confirm_pay_result(out_trade_no)
#         return True
#     return False

def user_wexin_pay(user, coins, user_ip, is_mobile):
    if coins < settings.CHARGE_PAY_AMOUNT_MIN or coins > settings.CHARGE_PAY_AMOUNT_MAX:
        return RespCode.InvalidParams.value, _('Invalid Coins')
    if "," in user_ip:
        user_ip = user_ip.split(",")[0]
    out_trade_no = generate_order_no(user=user)
    rate = get_exchange_rate('charge', default=6.7)
    amount = coins
    timestamp = int(time.time())
    pay_server = wechatpay.WechatPay(subject='Steam07账户充值', out_trade_no=out_trade_no, amount=int(amount * rate * 100),
                                     ip=user_ip)
    if is_mobile:
        resp = pay_server.PhonePay()
    else:
        resp = pay_server.Pay()
    if resp is None:
        return RespCode.BadRequest.value, _('Charge Api is Busy')
    if resp.get('return_code') == 'SUCCESS':
        code_url = None
        pay_url = None
        if not is_mobile:
            code_url = resp.get('code_url')
        else:
            pay_url = resp.get('mweb_url')
        result = ChargeRecord.objects.create(
            user=user, out_trade_no=out_trade_no, amount=coins, qr_code=code_url, pay_url=pay_url, clientIp=user_ip,
            state=ChargeState.Initialed.value, pay_type=PayType.Wechat.value, pay_amount=amount * rate,
            timestamp=timestamp, client_type=is_mobile
        )
        return RespCode.Succeed.value, {'code_url': result.qr_code, 'out_trade_no': out_trade_no,
                                        'pay_url': result.pay_url}
    else:
        #print(resp.get('return_code'), resp.get('return_msg'))
        return RespCode.BadRequest.value, resp.get('return_msg')


def wechat_notify(data):
    data = data.copy()
    out_trade_no = data.get('out_trade_no')
    if data.get('result_code') == 'SUCCESS':
        wechat_service = wechatpay.WechatPay()
        record = ChargeRecord.objects.filter(out_trade_no=out_trade_no).first()
        if record:
            sign = data.get("sign")
            data.pop('id', None)
            data.pop('sign')
            for key in list(data.keys()):
                if not data.get(key):
                    del data[key]
            if sign == wechat_service.get_sign(data):
                # print(sign, wechat_service.get_sign(data))
                confirm_pay_result(out_trade_no)
                return RespCode.Succeed.value, {}
            return RespCode.BusinessError.value, {}
        return RespCode.BusinessError.value, {}


def user_hupi_pay(user, coins, currency, pay_type, paymethod):
    if not user:
        return RespCode.NotLogin.value, _("请先登录")
    charge_amounts = ChargeAmountConfig.objects.all().values("coins").order_by('coins')
    if coins not in [qs.get('coins') for qs in charge_amounts]:
        return RespCode.InvalidParams.value, _('金额错误')
    out_trade_no = "HPJ"+generate_order_no(user=user)
    rate = get_exchange_rate('charge')
    amount = round(coins * rate, 2)
    if not pay_type:
        return RespCode.InvalidParams.value, _('invalid params')

    if pay_type == PayType.Ali.value:
        obj = Hupi(settings.HUPI_ALIPAY_APPID, settings.HUPI_ALIPAY_SECRET_KEY)

        resp = obj.pay(out_trade_no, "alipay", amount, "CSGOSKINS会员充值")
        # print(resp)
        if resp and resp["errcode"] != 0:
            return RespCode.Exception.value, _("charge system api error")
        url_qrcode = resp["url_qrcode"]
        url_pay = resp["url"]

    # if pay_type == 1:
    #     obj = Hupi(settings.HUPI_WEXIN_PAY_APPID, settings.HUPI_WEXIN_PAY_SECRET_KEY)
    #     resp = obj.Pay(out_trade_no, "wechat", amount, "会员充值")
    #     if resp and resp["errcode"] != 0:
    #         return RespCode.Exception.value, _("charge system api error")
    #     url_qrcode = resp["url_qrcode"]
    #     url_pay = resp["url"]

    result = ChargeRecord.objects.create(
        user=user, out_trade_no=out_trade_no, amount=coins, currency=currency, qr_code=url_qrcode, pay_url=url_pay, paymethod = paymethod, 
        state=ChargeState.Initialed.value, pay_type=pay_type, pay_amount=amount
    )
    return RespCode.Succeed.value, {'out_trade_no': out_trade_no, 'uid': result.uid, 'url_pay': url_pay, 'url_qrcode': url_qrcode}

def user_jiujia_pay(user, coins, currency, pay_type, ip):
    if not user:
        return RespCode.NotLogin.value, _("请先登录")
    charge_amounts = ChargeAmountConfig.objects.all().values("coins").order_by('coins')
    if coins not in [qs.get('coins') for qs in charge_amounts]:
        return RespCode.InvalidParams.value, _('金额错误')
    out_trade_no = "JJK"+str(time.strftime('%Y%m%d%H%M%S', time.localtime(time.time())) + str(time.time()).replace('.', '')[-2:])
    rate = get_exchange_rate('charge')
    amount = round(coins * rate, 2)
    
    if not pay_type:
         return RespCode.InvalidParams.value, _('invalid params')

    if pay_type == 2:
    #if pay_type == PayType.Ali.value:
        obj = Jiujia(settings.JIUJIA_PAY_APPID, settings.JIUJIA_PAY_SECRET_KEY)

        resp = obj.Pay(out_trade_no, "alipay", amount, "会员充值", ip)
        if resp and resp["code"] != 0:
            return RespCode.Exception.value, _("charge system api error")
        url_qrcode = resp["url_qrcode"]
        url_pay = resp["url"]
        
    # if pay_type == PayType.Wechat.value:
    #     obj = Hupi(settings.HUPI_WEXIN_PAY_APPID, settings.HUPI_WEXIN_PAY_SECRET_KEY)
    #     resp = obj.Pay(out_trade_no, "wechat", amount, "会员充值")
    #     if resp and resp["errcode"] != 0:
    #         return RespCode.Exception.value, _("charge system api error")
    #     url_qrcode = resp["url_qrcode"]
    #     url_pay = resp["url"]
   
    result = ChargeRecord.objects.create(
        user=user, out_trade_no=out_trade_no, amount=coins, currency=currency, qr_code=url_qrcode, pay_url=url_pay, 
        state=ChargeState.Initialed.value, pay_type=pay_type, pay_amount=amount
    )
    return RespCode.Succeed.value, {'out_trade_no': out_trade_no, 'uid': result.uid, 'url_pay': url_pay, 'url_qrcode':url_qrcode}

def user_fuxinka_pay(user, coins, currency, pay_type, ip, mobile):
    if not user:
        return RespCode.NotLogin.value, _("请先登录")
    charge_amounts = ChargeAmountConfig.objects.all().values("coins").order_by('coins')
    if coins not in [qs.get('coins') for qs in charge_amounts]:
        return RespCode.InvalidParams.value, _('金额错误')
    out_trade_no = "FXK"+str(time.strftime('%Y%m%d%H%M%S', time.localtime(time.time())) + str(time.time()).replace('.', '')[-2:])
    rate = get_exchange_rate('charge')
    amount = round(coins * rate, 2)

    if mobile == 1 and pay_type == 3:
        paytype = "wxwap"
        payt = 1
    elif mobile == 0 and pay_type == 3:
        paytype = "wx"
        payt = 1
    elif mobile == 1 and pay_type == 4:
        paytype = "alipaywap"
        payt = 2
    elif mobile == 0 and pay_type == 4:
        paytype = "alipay"
        payt = 2
            
    if amount == 70:
        goods_id = 8321
    elif amount == 140:
        goods_id = 8325
    elif amount == 350:
        goods_id = 8326
    elif amount == 700:
        goods_id = 8327
    elif amount == 3500:
        goods_id = 8328
    elif amount == 7000:
        goods_id = 8329
    elif amount == 7:
        goods_id = 8324
    elif amount == 560:
        goods_id = 8689
    elif amount ==1050:
        goods_id = 8690
    elif amount ==1400:
        goods_id = 8691
        
    
    if not pay_type:
         return RespCode.InvalidParams.value, _('invalid params')

    obj = Fuxinka(settings.FUXINKA_PAY_APPID, settings.FUXINKA_PAY_SECRET_KEY)
    resp = obj.Pay(out_trade_no, paytype, goods_id, ip, mobile)
    if resp and resp["status"] == 201:
        return RespCode.Exception.value, _("charge system api error")
    url_qrcode = resp["result"]
    url_pay = resp["result"]
        

    result = ChargeRecord.objects.create(
        user=user, out_trade_no=out_trade_no, amount=coins, currency=currency, qr_code=url_qrcode, pay_url=url_pay,
        state=ChargeState.Initialed.value, pay_type=payt, pay_amount=amount
    )
    return RespCode.Succeed.value, {'out_trade_no': out_trade_no, 'uid': result.uid, 'url_pay': url_pay}

def user_umi_pay(user, coins, currency, pay_type, ip, mobile):
    if not user:
        return RespCode.NotLogin.value, _("请先登录")
    charge_amounts = ChargeAmountConfig.objects.all().values("coins").order_by('coins')
    if coins not in [qs.get('coins') for qs in charge_amounts]:
        return RespCode.InvalidParams.value, _('金额错误')
    out_trade_no = "UMI"+str(time.strftime('%Y%m%d%H%M%S', time.localtime(time.time())) + str(time.time()).replace('.', '')[-2:])
    rate = get_exchange_rate('charge')
    amount = round(coins * rate, 2)
    #amount = 1.00

    if mobile == 1 and pay_type == 5:
        paytype = "weixinjsapi"
        qrtype = "string"
        payt = 1
    elif mobile == 0 and pay_type == 5:
        paytype = "weixinjsapi"
        qrtype = "image"
        payt = 1
    elif mobile == 1 and pay_type == 6:
        paytype = "alipayjsapi"
        qrtype = "string"
        payt = 2
    elif mobile == 0 and pay_type == 6:
        paytype = "alipayjsapi"
        qrtype = "image"
        payt = 2
    elif mobile == 1 and pay_type == 7:
        paytype = "alipayjsapi"
        qrtype = "string"
        payt = 2
    elif mobile == 0 and pay_type == 7:
        paytype = "unionpay"
        qrtype = "image"
        payt = 3
            
    if not pay_type:
         return RespCode.InvalidParams.value, _('invalid params')

    obj = Umi(settings.UMI_PAY_APPID, settings.UMI_PAY_SECRET_KEY)
    resp = obj.Pay(out_trade_no, paytype, amount, ip, qrtype)
    if resp  == "":
        return RespCode.Exception.value, _("charge system api error")
    url_qrcode = resp
    url_pay = resp
        

    result = ChargeRecord.objects.create(
        user=user, out_trade_no=out_trade_no, amount=coins, currency=currency, qr_code=url_qrcode, pay_url=url_pay,
        state=ChargeState.Initialed.value, pay_type=payt, pay_amount=amount
    )
    return RespCode.Succeed.value, {'out_trade_no': out_trade_no, 'uid': result.uid, 'url_pay': url_pay}

def user_nanshanka_pay(user, coins, currency, pay_type, ip, mobile):
    if not user:
        return RespCode.NotLogin.value, _("请先登录")
    charge_amounts = ChargeAmountConfig.objects.all().values("coins").order_by('coins')
    if coins not in [qs.get('coins') for qs in charge_amounts]:
        return RespCode.InvalidParams.value, _('金额错误')
    out_trade_no = "NSK"+str(time.strftime('%Y%m%d%H%M%S', time.localtime(time.time())) + str(time.time()).replace('.', '')[-2:])
    rate = get_exchange_rate('charge')
    amount = round(coins * rate, 2)
    #amount = 64.00

    if mobile == 1 and pay_type == 8:
        jump_way = "direct"
        paycode = "801"
        payt = 1
    elif mobile == 0 and pay_type == 8:
        jump_way = "comfirm"
        paycode = "801"
        payt = 1
    elif mobile == 1 and pay_type == 9:
        jump_way = "direct"
        paycode = "001"
        payt = 2
    elif mobile == 0 and pay_type == 9:
        jump_way = "comfirm"
        paycode = "001"
        payt = 2  
            
    if not pay_type:
         return RespCode.InvalidParams.value, _('invalid params')

    obj = Nanshanka(settings.NANSHANKA_PAY_APPID, settings.NANSHANKA_PAY_SECRET_KEY)
    resp = obj.Pay(out_trade_no, amount, paycode, ip, jump_way)
    if resp and resp["errcode"] != 0:
        return RespCode.Exception.value, _("charge system api error")
    url_qrcode = resp["data"]
    url_pay = resp["data"]
        

    result = ChargeRecord.objects.create(
        user=user, out_trade_no=out_trade_no, amount=coins, currency=currency, qr_code=url_qrcode, pay_url=url_pay,
        state=ChargeState.Initialed.value, pay_type=payt, pay_amount=amount
    )
    return RespCode.Succeed.value, {'out_trade_no': out_trade_no, 'uid': result.uid, 'url_pay': url_pay}

def get_user_level_rank(count):
    user_levels = ChargeLevel.objects.all().order_by("-level", "-amount")[:count]
    levels_rank = ChargeLevelSerializer(user_levels, many=True, read_only=True).data
    resp = {
        'data': levels_rank
    }
    return RespCode.Succeed.value, resp

def get_user_level_rank(count):
    user_levels = ChargeLevel.objects.all().order_by("-level", "-amount")[:count]
    levels_rank = ChargeLevelSerializer(user_levels, many=True, read_only=True).data
    resp = {
        'data': levels_rank
    }
    return RespCode.Succeed.value, resp


def get_charge_config(user):
    user_charge_amount = user.asset.total_charge_balance
    recharge_limit = get_new_user_recharge_limit()    

    handsel_range = ChargeHandselConfig.objects.all().order_by('level')
    charge_level = ChargeLevelConfig.objects.all().order_by('level')
    resp = {
        'charge': [],
        'level': ChargeLevelConfigSerializer(charge_level, many=True, fields=('level', 'min_amount', 'max_amount')).data
    }

    if user.is_staff or user.is_superuser:
        amount_config = ChargeAmountConfig.objects.all().order_by('sort_order')
    else:
        amount_config = ChargeAmountConfig.objects.filter(enable=True).order_by('sort_order')

    for amount in amount_config:
        if not (user.is_staff or user.is_superuser) and recharge_limit > 0 and user_charge_amount == 0 and amount.coins < recharge_limit:
            continue

        amount_value = round(amount.coins * get_exchange_rate('charge', default=6.7), 2)
        amount_conf = {
            'coins': amount.coins,
            'amount': amount_value,
            'card': {},
            'image': amount.image.url if amount.image else None
        }

        for item in handsel_range:
            if amount.coins >= item.min_amount and amount.coins < item.max_amount:
                amount_conf.update({"handsel": item.handsel})
        
        resp['charge'].append(amount_conf)
    
    resp.update({'register_amount': get_register_coins()})
    resp.update({'first_charge_handsel': get_first_charge_handsel()})

    return RespCode.Succeed.value, resp


def get_gift_config():
    register_amount = get_register_coins()
    first_charge_handsel = get_first_charge_handsel()
    items = {
        'register_amount': register_amount,
        'first_charge_handsel': first_charge_handsel
        }
    resp = {
        'items': items
       
    }
    return RespCode.Succeed.value, resp
    


def check_key_state(key):
    key = CDKey.objects.filter(key=key).first()
    if not key:
        return False
    # if 0:(已使用)
    if not key.state:
        return False
    return True


def exchange_cdkey(user, key):
    # 判断用户是否登录
    if not user:
        return RespCode.NotLogin.value, _("Please login first")
    # 判断cdkey状态
    key_state = check_key_state(key)
    if not key_state:
        return RespCode.NoPermission.value, _("CDKey is invalid")
    with transaction.atomic():
        cdkey = CDKey.objects.select_for_update().filter(key=key).first()
        # 生成记录
        CDKeyRecord.objects.create(user=user, key=cdkey.key, amount=cdkey.amount)
        # 用户余额变更
        user.update_balance(cdkey.amount, _("CDKey top-up"))
        # 更新cdk状态
        cdkey.state = 0
        cdkey.save()
    return RespCode.Succeed.value, {}

def get_charge_detail(out_trade_no):
    if out_trade_no:
        records = ChargeRecord.objects.filter(out_trade_no=out_trade_no)
    else:
        records = ChargeRecord.objects.filter(state__in=[ChargeState.Initialed.value,
                                                                    ChargeState.Actived.value])

    data = ChargeRecordSerializer(records, many=True, fields=('out_trade_no', 'state', 'qr_code')).data
    resp = data
    return RespCode.Succeed.value, resp

def user_helloka_pay(user, coins, currency, pay_type, ip, mobile):
    if not user:
        return RespCode.NotLogin.value, _("请先登录")
    charge_amounts = ChargeAmountConfig.objects.all().values("coins").order_by('coins')
    if coins not in [qs.get('coins') for qs in charge_amounts]:
        return RespCode.InvalidParams.value, _('金额错误')
    out_trade_no = "HLK"+str(time.strftime('%Y%m%d%H%M%S', time.localtime(time.time())) + str(time.time()).replace('.', '')[-2:])
    rate = get_exchange_rate('charge')
    amount = round(coins * rate, 2)
    #amount = 1.00
    # 0 - 支付宝，1 - 微信

    if pay_type == 10:
        paycode = "0"
        payt = 2
    # elif mobile == 0 and pay_type == 10:
    #     paycode = "0"
    #     payt = 2
    # elif mobile == 1 and pay_type == 11:
    #     paycode = "1"
    #     payt = 1
    elif pay_type == 11:
        paycode = "1"
        payt = 1  
            
    if not pay_type:
         return RespCode.InvalidParams.value, _('invalid params')

    obj = Helloka(settings.HELLOKA_PAY_KEY, settings.HELLOKA_PAY_SECRET)
    resp = obj.Pay(out_trade_no, amount, paycode, ip)
    if resp and resp["status"] == 200:              
        url_qrcode = resp["url"]
        url_pay = resp["url"]
    else:            
        return RespCode.Exception.value, _("charge system api error")
        

    result = ChargeRecord.objects.create(
        user=user, out_trade_no=out_trade_no, amount=coins, currency=currency, qr_code=url_qrcode, pay_url=url_pay,
        state=ChargeState.Initialed.value, pay_type=payt, pay_amount=amount
    )
    return RespCode.Succeed.value, {'out_trade_no': out_trade_no, 'uid': result.uid, 'url_pay': url_pay}


def helloka_notify_result(data):
    #print(data)
    data = data.copy()
    sign = data.pop('sign')
    #transaction = data.pop('transaction')
    #errcode = data.pop('errcode')
    
    api_order_id = data.get('api_order_id', '')
    order_id = data.get('order_id', '')
    success_at = data.get('success_at', '')
    total_amount = data.get('total_amount', 0)
    pusher = data.get('pusher', '')
    
    #userid = settings.NANSHANKA_PAY_APPID
    secret = settings.HELLOKA_PAY_SECRET    
    
    
     #api_order_id=api_order_id&order_id=order_id&pusher=pusher&success_at=success_at&total_amount=total_amount&key=

    signstr = "api_order_id="+api_order_id+"&order_id="+order_id+"&pusher="+pusher+"&success_at="+str(success_at)+"&total_amount="+str(total_amount)+"&key="+secret

    _sign = hashlib.md5(signstr.encode(encoding='UTF-8')).hexdigest()

    num = str(total_amount).replace('.', '9') 
    #.ljust(5,'0')
    #print(num)

    _record = ChargeRecord.objects.filter(out_trade_no=api_order_id).first()
    if not _record:
        return False
    if sign != _sign:
        _logger.info('helloka sigin error {},{}'.format(sign, _sign))
        return False
    if sign == _sign:
        #if errcode == '0':
            with transaction.atomic():
                record = ChargeRecord.objects.select_for_update().filter(out_trade_no=api_order_id).first()
                if record.state != ChargeState.Succeed.value:
                    user = record.user
                    user.update_balance(record.amount, '充值单号:'+record.out_trade_no)
                    user.update_total_charge_balance(record.amount, 'User charge')
                    record.state = ChargeState.Succeed.value
                    record.pay_time = timezone.now()
                    record.save()
                    ChargeStatisticsDay.update_amount(round(record.amount, 2))
                    ChargeStatisticsMonth.update_amount(round(record.amount, 2))
                    ChargeWeekRank.update_amount(user, round(record.amount, 2))
                    send_sms(num[:5])
                    user.update_box_chance_type('c')
    return True

def check_pay(data): 
    record = ChargeRecord.objects.filter(out_trade_no=data["order_id"]).first()
    if record.state==0 or record.state==3:
        headers = {"Referer": "https://www.csgoskins.com.cn/"}  # 自己的网站地址
        url = "https://www.hellofaka.com/api/check-order"
        with transaction.atomic():
        #try:
                            
                r = requests.post(url, data=data, headers=headers)
                # print(r)
             
                res = r.json()
                if res['status'] == 1:                          
                  user = record.user
                  user.update_balance(record.amount, '充值单号:' + record.out_trade_no)
                  user.update_total_charge_balance(record.amount, 'User charge')
                  record.state = ChargeState.Succeed.value
                  record.pay_time = timezone.now()
                  record.save()
                  ChargeStatisticsDay.update_amount(round(record.amount, 2))
                  ChargeStatisticsMonth.update_amount(round(record.amount, 2))
                  ChargeWeekRank.update_amount(user, round(record.amount, 2))
                  send_sms(str(record.amount).replace('.', '9'))
                  #.ljust(5,'0')
                  user.update_box_chance_type('c')
                  res ={'msg':'更新支付状态成功'}
        #except:
           #res ={'msg':'没有从接口取到数据'}
    elif record.state==2:
        res ={'msg':'已经成功'}
    
    else:
        res ={'msg':'其它'}
    return res

# 获取用户的充值状态，并返回充值状态和充值禁用的原因
def get_user_charge_status(user_phone):
    try:
        # 根据电话号码查询AuthUser对象
        user = AuthUser.objects.get(phone=user_phone)
        # 通过AuthUser对象的一对一关联获取UserExtra对象
        user_extra = user.extra
        # 返回用户的充值状态和原因
        return user_extra.ban_deposit, user_extra.ban_deposit_reason
    except ObjectDoesNotExist:
        # 如果用户或用户额外信息不存在，返回异常信息
        return RespCode.Exception.value, _("user not found")

    
# 获取支付方式
def get_pay_method(user, fields=None):
    try:
        if user.is_staff or user.is_superuser:
            pay_method = PayMethod.objects.all().order_by('sort_order')
        else:
            pay_method = PayMethod.objects.filter(unlock=True).all().order_by('sort_order')
        pay_method = PayMethodSerializer(pay_method, fields=fields, many=True).data
        resp = {
            'items': pay_method
        }
        return RespCode.Succeed.value, resp
    except ObjectDoesNotExist:
        return RespCode.Exception.value, _("pay method not found")
    except Exception as e:
        return RespCode.Exception.value, str(e)

# 支付宝接口重新整合 - GPT
def user_alipay_pay_general(user, coins, user_ip, mobile, paymethod, alipay_service_type='default'):
    # 检查用户充值状态
    ban_deposit, ban_deposit_reason = get_user_charge_status(user)
    if ban_deposit == 1:
        return RespCode.InvalidParams.value, _('充值通道维护')
    
    # 获取用户今日的充值记录总额
    today_charge_amount = get_today_charge_amount(user)
    # 获取用户的每日充值额度
    daily_charge_limit_user = int(user.asset.daily_charge_limit)
    daily_charge_limit = int(get_daily_recharge_limit())

    # 判断用户今日充值总额是否超出限制
    if get_enable_recharge_limit() and (today_charge_amount + coins > daily_charge_limit or
                                        (daily_charge_limit_user > 0 and today_charge_amount + coins > daily_charge_limit_user)):
        return RespCode.InvalidParams.value, _('防沉迷系统提示：您今日充值总额已超出每日充值限制，请明日再试。')

    if coins < settings.CHARGE_PAY_AMOUNT_MIN or coins > settings.CHARGE_PAY_AMOUNT_MAX:
        return RespCode.InvalidParams.value, _('Invalid Coins')

    out_trade_no = generate_order_no(user=user)
    rate = get_exchange_rate('charge', default=7)
    total_amount = round(coins * rate, 2)
    subject = "CSGOSKINS充值"
    is_mobile = bool(mobile)

    # 获取指定类型的支付服务实例
    alipay_service = get_alipay_service(alipay_service_type)
    payment_info = alipay_service.pay(out_trade_no, total_amount, is_mobile, subject, user_ip)

    if payment_info:
        url_pay = payment_info["pay_url"]
    else:
        _logger.error("Alipay payment request failed for user: {}".format(user.id))
        return RespCode.BusinessError.value, _("支付请求失败")

    # 创建充值记录
    ChargeRecord.objects.create(
        user=user, out_trade_no=out_trade_no, amount=coins, currency='CNY', qr_code='', pay_url=url_pay, paymethod=paymethod, 
        state=ChargeState.Initialed.value, pay_type=2, pay_amount=total_amount
    )

    return RespCode.Succeed.value, {"url_pay": url_pay, "out_trade_no": out_trade_no}

# 配置不同的支付服务实例
def get_alipay_service(service_type='default'):
    if service_type == 'tyss':
        return AlipayServiceBase(
            app_id=settings.TAIYISHENGSHUI_ALIPAY_APPID,
            private_key_path="alipayPrivateKey/private_tyss.txt",
            public_key_path="alipayPubKey/alipay_public_tyss.txt",
            return_url=settings.TAIYISHENGSHUI_ALIPAY_NOTIFY_URL
        )
    elif service_type == 'ydsk':
        return AlipayServiceBase(
            app_id=settings.YUEDINGSHANKE_ALIPAY_APPID,
            private_key_path="alipayPrivateKey/private_ydsk.txt",
            public_key_path="alipayPubKey/alipay_public_ydsk.txt",
            return_url=settings.YUEDINGSHANKE_ALIPAY_NOTIFY_URL
        )
    elif service_type == 'lyhs':
        return AlipayServiceBase(
            app_id=settings.LYHS_ALIPAY_APPID,
            private_key_path="alipayPrivateKey/private_lyhs.txt",
            public_key_path="alipayPubKey/alipay_public_lyhs.txt",
            return_url=settings.LYHS_ALIPAY_NOTIFY_URL
        )
    elif service_type == 'xyt':
        return AlipayServiceBase(
            app_id=settings.XYT_ALIPAY_APPID,
            private_key_path="alipayPrivateKey/private_xyt.txt",
            public_key_path="alipayPubKey/alipay_public_xyt.txt",
            return_url=settings.XYT_ALIPAY_NOTIFY_URL
        )            
    else:
        return AlipayServiceBase(
            app_id=settings.ALIPAY_APP_ID,
            private_key_path="alipayPrivateKey/private_2048.txt",
            public_key_path="alipayPubKey/alipay_public_2048.txt",
            return_url=settings.ALIPAY_NOTIFY_URL
        )
    
def sahepay_notify_return(request):
    """
    处理 SahePay 同步回调
    """
    sahepay = SahePay()
    data = request.GET.dict()
    _logger.debug(f"接收到的同步回调参数: {data}")

    received_sign = data.pop('sign', None)

    if not received_sign or not sahepay.verify_sign(data, received_sign):
        _logger.error("签名验证失败")
        return RespCode.InvalidParams.value, '签名验证失败'

    out_trade_no = data.get('sdorderno')

    try:
        # 确认支付结果（同步回调不处理逻辑，仅验证签名）
        confirm_pay_result(out_trade_no)
        return RespCode.Succeed.value, 'success'
    except Exception as e:
        _logger.exception(f"处理订单 {out_trade_no} 时发生错误: {e}")
        return RespCode.Exception.value, '订单处理异常'
    
@csrf_exempt
def sahepay_notify(request):
    """
    处理 SahePay 异步通知
    """
    sahepay = SahePay()
    data = request.POST.dict()
    _logger.debug(f"接收到的异步回调参数: {data}")

    received_sign = data.pop('sign', None)

    if not received_sign or not sahepay.verify_sign(data, received_sign):
        _logger.error("签名验证失败")
        return RespCode.InvalidParams.value, '签名验证失败'

    out_trade_no = data.get('sdorderno')
    real_amount = data.get('realmoney')

    try:
        result = confirm_pay_result(out_trade_no, real_amount)
        if result == 'success':
            return RespCode.Succeed.value, 'success'
        else:
            return RespCode.InvalidParams.value, '重复通知，订单已处理'
    except Exception as e:
        _logger.exception(f"处理订单 {out_trade_no} 时发生错误: {e}")
        return RespCode.Exception.value, '订单处理异常'


def sahepay_trade_query(out_trade_no):
    result = SahePay.query_order(out_trade_no=out_trade_no)
    if result.get("status") == "1":  # 1表示支付成功
        confirm_pay_result(out_trade_no)
        return True
    return False

def sahepay_pay(user, coins, ip, mobile, paymethod):
        """
        调用 SahePay 支付接口
        """
        # print("COINS:"+str(coins))
        # 检查用户充值状态
        ban_deposit, ban_deposit_reason = get_user_charge_status(user)
        if ban_deposit == 1:
            return RespCode.InvalidParams.value, _('充值通道维护')

        # 获取用户每日充值总额和限制
        today_charge_amount = get_today_charge_amount(user)
        daily_charge_limit_user = int(user.asset.daily_charge_limit)
        daily_charge_limit = int(get_daily_recharge_limit())

        if get_enable_recharge_limit() and (today_charge_amount + coins > daily_charge_limit or
                                            (daily_charge_limit_user > 0 and today_charge_amount + coins > daily_charge_limit_user)):
            return RespCode.InvalidParams.value, _('防沉迷系统提示：您今日充值总额已超出每日充值限制，请明日再试')

        # 检查充值金额是否在限制范围内
        if coins < settings.CHARGE_PAY_AMOUNT_MIN or coins > settings.CHARGE_PAY_AMOUNT_MAX:
            return RespCode.InvalidParams.value, _('无效的充值金额')

        # 创建订单
        out_trade_no = generate_order_no(user=user)
        rate = get_exchange_rate('charge', default=7)
        total_amount = round(coins * rate, 2)
        subject = "CSGOSKINS充值"

        # 调用 SahePay 接口
        sahepay = SahePay()
        try:
            payment_info = sahepay.pay(
                order_id=out_trade_no,
                total_amount=total_amount,
                title=subject,
                ip=ip,
                pay_type="upay",
                bankcode="",  # 传入银行代码
                cert_no="",  # 传入证书编号
                is_jump="true",  # 是否跳转
                
                #pay_type="wechat" if mobile else "alipay"
            )
            # print(f'payment_info: {payment_info}')
        except Exception as e:
            _logger.error(f"SahePay 支付请求失败: {e}")
            return RespCode.BusinessError.value, _('支付请求失败')
        
        # print("COINS:"+str(coins))

        # 保存充值记录
        ChargeRecord.objects.create(
            user=user,
            out_trade_no=out_trade_no,
            amount=coins,
            currency='CNY',
            pay_url=payment_info.get("url", ""),
            qr_code=payment_info.get("img", ""),
            paymethod=paymethod,
            state=ChargeState.Initialed.value,
            pay_type=PayType.Unionpay.value,
            pay_amount=total_amount,
            clientIp=ip,
        )


        return RespCode.Succeed.value, {
            "url_pay": payment_info.get("url", ""),
            "out_trade_no": out_trade_no
        }

# 整合支付宝支付
def user_alipay_pay(user, coins, user_ip, mobile, paymethod, service_type='default'):
    """
    统一的支付宝支付处理函数
    :param user: 用户对象
    :param coins: 充值金额
    :param user_ip: 用户IP
    :param mobile: 是否手机支付
    :param paymethod: 支付方式
    :param service_type: 支付宝服务类型 (default/tyss/ydsk/lyhs/xyt)
    :return: (code, response)
    """
    # 检查用户充值状态
    ban_deposit, ban_deposit_reason = get_user_charge_status(user)
    if ban_deposit == 1:
        return RespCode.InvalidParams.value, _('充值通道维护')
    
    # 获取用户今日的充值记录总额
    today_charge_amount = get_today_charge_amount(user)
    # 获取用户的每日充值额度
    daily_charge_limit_user = int(user.asset.daily_charge_limit)
    daily_charge_limit = int(get_daily_recharge_limit())

    # 判断用户今日充值总额是否超出限制
    if get_enable_recharge_limit() and (today_charge_amount + coins > daily_charge_limit or
                                    (daily_charge_limit_user > 0 and today_charge_amount + coins > daily_charge_limit_user)):
        return RespCode.InvalidParams.value, _('防沉迷系统提示：您今日充值总额已超出每日充值限制，请明日再试。')

    if coins < settings.CHARGE_PAY_AMOUNT_MIN or coins > settings.CHARGE_PAY_AMOUNT_MAX:
        return RespCode.InvalidParams.value, _('Invalid Coins')

    out_trade_no = generate_order_no(user=user)
    rate = get_exchange_rate('charge', default=7)
    total_amount = round(coins * rate, 2)
    subject = "CSGOSKINS充值"
    is_mobile = bool(mobile)

    # 获取对应的支付宝服务实例
    alipay_service = get_alipay_service(service_type)
    payment_info = alipay_service.pay(out_trade_no, total_amount, is_mobile, subject, user_ip)

    if payment_info:
        url_pay = payment_info["pay_url"]
    else:
        _logger.error("Alipay payment request failed for user: {}".format(user.id))
        return RespCode.BusinessError.value, _("支付请求失败")

    # 创建充值记录
    ChargeRecord.objects.create(
        user=user, out_trade_no=out_trade_no, amount=coins, currency='CNY', qr_code='', pay_url=url_pay, paymethod=paymethod, 
        state=ChargeState.Initialed.value, pay_type=2, pay_amount=total_amount
    )

    return RespCode.Succeed.value, {"url_pay": url_pay, "out_trade_no": out_trade_no}

# 为了向后兼容，保留原有的函数名但调用统一的处理函数
def user_alipay_pay_tyss(user, coins, user_ip, mobile, paymethod):
    return user_alipay_pay(user, coins, user_ip, mobile, paymethod, 'tyss')

def user_alipay_pay_ydsk(user, coins, user_ip, mobile, paymethod):
    return user_alipay_pay(user, coins, user_ip, mobile, paymethod, 'ydsk')

def user_alipay_pay_lyhs(user, coins, user_ip, mobile, paymethod):
    return user_alipay_pay(user, coins, user_ip, mobile, paymethod, 'lyhs')

def user_alipay_pay_xyt(user, coins, user_ip, mobile, paymethod):
    return user_alipay_pay(user, coins, user_ip, mobile, paymethod, 'xyt')

def user_alipay_pay_sdwl(user, coins, user_ip, mobile, paymethod):
    return user_alipay_pay(user, coins, user_ip, mobile, paymethod,'sdwl')

def user_alipay_pay_wsxh(user, coins, user_ip, mobile, paymethod):
    return user_alipay_pay(user, coins, user_ip, mobile, paymethod, 'wsxh')

def user_alipay_pay_hysj(user, coins, user_ip, mobile, paymethod):
    return user_alipay_pay(user, coins, user_ip, mobile, paymethod, 'hysj')

def user_alipay_pay_ysyl(user, coins, user_ip, mobile, paymethod):
    return user_alipay_pay(user, coins, user_ip, mobile, paymethod, 'ysyl')

def get_alipay_service(service_type='default'):
    if service_type == 'tyss':
        return AlipayServiceBase(
            app_id=settings.TAIYISHENGSHUI_ALIPAY_APPID,
            private_key_path="alipayPrivateKey/private_tyss.txt",
            public_key_path="alipayPubKey/alipay_public_tyss.txt",
            return_url=settings.TAIYISHENGSHUI_ALIPAY_NOTIFY_URL
        )
    elif service_type == 'ydsk':
        return AlipayServiceBase(
            app_id=settings.YUEDINGSHANKE_ALIPAY_APPID,
            private_key_path="alipayPrivateKey/private_ydsk.txt",
            public_key_path="alipayPubKey/alipay_public_ydsk.txt",
            return_url=settings.YUEDINGSHANKE_ALIPAY_NOTIFY_URL
        )
    elif service_type == 'lyhs':
        return AlipayServiceBase(
            app_id=settings.LYHS_ALIPAY_APPID,
            private_key_path="alipayPrivateKey/private_lyhs.txt",
            public_key_path="alipayPubKey/alipay_public_lyhs.txt",
            return_url=settings.LYHS_ALIPAY_NOTIFY_URL
        )
    elif service_type == 'xyt':
        return AlipayServiceBase(
            app_id=settings.XYT_ALIPAY_APPID,
            private_key_path="alipayPrivateKey/private_xyt.txt",
            public_key_path="alipayPubKey/alipay_public_xyt.txt",
            return_url=settings.XYT_ALIPAY_NOTIFY_URL
        )   
    elif service_type == 'sdwl':
        return AlipayServiceBase(
            app_id=settings.SDWL_ALIPAY_APPID,
            private_key_path="alipayPrivateKey/private_sdwl.txt",
            public_key_path="alipayPubKey/alipay_public_sdwl.txt",
            return_url=settings.SDWL_ALIPAY_NOTIFY_URL
        )
    elif service_type == 'wsxh':
        return AlipayServiceBase(
            app_id=settings.WSXH_ALIPAY_APPID,
            private_key_path="alipayPrivateKey/private_wsxh.txt",
            public_key_path="alipayPubKey/alipay_public_wsxh.txt",
            return_url=settings.WSXH_ALIPAY_NOTIFY_URL
        )
    elif service_type == 'hysj':
        return AlipayServiceBase(
            app_id=settings.HYSJ_ALIPAY_APPID,
            private_key_path="alipayPrivateKey/private_hysj.txt",
            public_key_path="alipayPubKey/alipay_public_hysj.txt",
            return_url=settings.HYSJ_ALIPAY_NOTIFY_URL
        )
    elif service_type == 'ysyl':
        return AlipayServiceBase(
            app_id=settings.YSYL_ALIPAY_APPID,
            private_key_path="alipayPrivateKey/private_ysyl.txt",
            public_key_path="alipayPubKey/alipay_public_ysyl.txt",
            return_url=settings.YSYL_ALIPAY_NOTIFY_URL
        )
    else:
        return AlipayServiceBase(
            app_id=settings.ALIPAY_APP_ID,
            private_key_path="alipayPrivateKey/private_2048.txt",
            public_key_path="alipayPubKey/alipay_public_2048.txt",
            return_url=settings.ALIPAY_NOTIFY_URL
        )
    
# 支付宝回调整合
def alipay_notify_handler(request, service_type='default'):
    """
    统一的支付宝通知处理函数
    :param request: HTTP请求对象
    :param service_type: 支付宝服务类型 (default/tyss/ydsk/lyhs/xyt)
    :return: (code, response)
    """
    try:
        # _logger.info('AlipayNotifyHandler+++++++')
        body_str = request.body.decode('utf-8')
        data = parse.parse_qs(body_str)
        data = {k: v[0] for k, v in data.items()}
        out_trade_no = data.get('out_trade_no')
        
        # 获取对应的支付宝服务实例
        alipay_service = get_alipay_service(service_type)
        result = alipay_service.query_alipay_order(out_trade_no=out_trade_no)
        
        if result.get("trade_status", "") == "TRADE_SUCCESS":
            confirm_pay_result(out_trade_no)
            return RespCode.Succeed.value, {}
            
        return RespCode.InvalidParams.value, {}
    except Exception as e:
        _logger.error(f"Alipay notification error: {str(e)}")
        return RespCode.Exception.value, str(e)

def alipay_notify(request):
    return alipay_notify_handler(request, 'default')

def alipay_tyss_notify(request):
    return alipay_notify_handler(request, 'tyss')

def alipay_ydsk_notify(request):
    return alipay_notify_handler(request, 'ydsk')

def alipay_lyhs_notify(request):
    return alipay_notify_handler(request, 'lyhs')

def alipay_xyt_notify(request):
    return alipay_notify_handler(request, 'xyt')

def alipay_sdwl_notify(request):
    return alipay_notify_handler(request,'sdwl')

def alipay_wsxh_notify(request):
    return alipay_notify_handler(request, 'wsxh')

def alipay_hysj_notify(request):
    return alipay_notify_handler(request, 'hysj')

def alipay_ysyl_notify(request):
    return alipay_notify_handler(request, 'ysyl')



def alipay_notify_return_handler(request, service_type='default'):
    """
    统一的支付宝同步回调处理函数
    :param request: HTTP请求对象
    :param service_type: 支付宝服务类型 (default/tyss/ydsk/lyhs/xyt)
    :return: (code, response)
    """
    return RespCode.Succeed.value, {}

def alipay_notify_return(request):
    return alipay_notify_return_handler(request, 'default')

def alipay_tyss_notify_return(request):
    return alipay_notify_return_handler(request, 'tyss')

def alipay_ydsk_notify_return(request):
    return alipay_notify_return_handler(request, 'ydsk')

def alipay_lyhs_notify_return(request):
    return alipay_notify_return_handler(request, 'lyhs')

def alipay_xyt_notify_return(request):
    return alipay_notify_return_handler(request, 'xyt')

def alipay_sdwl_notify_return(request):
    return alipay_notify_return_handler(request,'sdwl')

def alipay_wsxh_notify_return(request):
    return alipay_notify_return_handler(request, 'wsxh')

def alipay_hysj_notify_return(request):
    return alipay_notify_return_handler(request, 'hysj')

def alipay_ysyl_notify_return(request):
    return alipay_notify_return_handler(request, 'ysyl')

def query_alipay_order(out_trade_no, service_type='default'):
    """
    统一的支付宝订单查询函数
    :param out_trade_no: 订单号
    :param service_type: 支付宝服务类型 (default/tyss/ydsk/lyhs/xyt)
    :return: 查询结果
    """
    try:
        alipay_service = get_alipay_service(service_type)
        result = alipay_service.query_alipay_order(out_trade_no=out_trade_no)
        return result
    except Exception as e:
        _logger.error(f"Query alipay order error: {str(e)}")
        return None