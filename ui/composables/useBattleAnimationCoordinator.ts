// 🎯 动画协调器 - 修复动画与数据更新的时序同步问题
// 统一管理动画状态和数据更新时机

import { ref, computed, watch, nextTick } from 'vue'
import { useBattleStateCoordinator } from './useBattleStateCoordinator'
import { useBattleRoundManager } from './useBattleRoundManager'

// 🎯 动画状态接口
interface AnimationState {
  isAnimating: boolean
  animationId: string | null
  animationType: 'case_opening' | 'round_transition' | 'battle_end' | null
  
  // 动画进度
  progress: number // 0-1
  startTime: number
  expectedDuration: number
  
  // 玩家动画状态
  playerAnimations: Map<number, PlayerAnimationState>
  
  // 数据更新队列
  pendingDataUpdates: DataUpdateEvent[]
  
  // 同步状态
  isDataUpdatePending: boolean
  lastSyncTime: number
}

// 🎯 玩家动画状态
interface PlayerAnimationState {
  playerIndex: number
  isAnimating: boolean
  animationId: string | null
  startTime: number
  expectedEndTime: number
  currentPhase: 'spinning' | 'slowing' | 'revealing' | 'completed'
  result?: any
}

// 🎯 数据更新事件
interface DataUpdateEvent {
  type: 'player_result' | 'round_complete' | 'battle_end'
  playerIndex?: number
  data: any
  timestamp: number
  scheduledTime?: number // 预定执行时间
  source: string
}

// 🎯 动画事件
interface AnimationEvent {
  type: 'animation_start' | 'animation_progress' | 'animation_end' | 'data_sync'
  animationId: string
  playerIndex?: number
  progress?: number
  data?: any
  timestamp: number
}

// 🎯 动画协调器类
class BattleAnimationCoordinator {
  private coordinator = useBattleStateCoordinator()
  private roundManager = useBattleRoundManager()
  
  private state = ref<AnimationState>({
    isAnimating: false,
    animationId: null,
    animationType: null,
    progress: 0,
    startTime: 0,
    expectedDuration: 0,
    playerAnimations: new Map(),
    pendingDataUpdates: [],
    isDataUpdatePending: false,
    lastSyncTime: 0
  })

  private animationCallbacks: Map<string, (event: AnimationEvent) => void> = new Map()
  private syncTimer: NodeJS.Timeout | null = null
  private progressTimer: NodeJS.Timeout | null = null

  constructor() {
    this.setupRoundManagerListener()
    this.setupProgressTracking()
  }

  // 🎯 设置轮次管理器监听
  private setupRoundManagerListener() {
    this.roundManager.subscribe(async (event) => {
      // 根据轮次事件触发相应的动画
      switch (event.type) {
        case 'round_start':
          await this.handleRoundStartAnimation(event)
          break
        case 'round_complete':
          await this.handleRoundCompleteAnimation(event)
          break
        case 'battle_complete':
          await this.handleBattleCompleteAnimation(event)
          break
      }
    })
  }

  // 🎯 设置进度追踪
  private setupProgressTracking() {
    // 每100ms更新一次进度
    this.progressTimer = setInterval(() => {
      if (this.state.value.isAnimating) {
        this.updateAnimationProgress()
      }
    }, 100)
  }

  // 🎯 开始动画
  async startAnimation(config: {
    animationId: string
    type: 'case_opening' | 'round_transition' | 'battle_end'
    duration: number
    players: number[]
    metadata?: any
  }) {
    const { animationId, type, duration, players, metadata } = config
    
    console.log(`[🎬ANIM-COORDINATOR] 开始动画: ${type}, ID: ${animationId}`)

    // 清理之前的动画
    if (this.state.value.isAnimating) {
      await this.stopAnimation()
    }

    // 🎯 设置动画状态
    this.state.value.isAnimating = true
    this.state.value.animationId = animationId
    this.state.value.animationType = type
    this.state.value.progress = 0
    this.state.value.startTime = Date.now()
    this.state.value.expectedDuration = duration
    this.state.value.isDataUpdatePending = false

    // 🎯 初始化玩家动画状态
    this.state.value.playerAnimations.clear()
    players.forEach(playerIndex => {
      this.state.value.playerAnimations.set(playerIndex, {
        playerIndex,
        isAnimating: true,
        animationId,
        startTime: Date.now(),
        expectedEndTime: Date.now() + duration,
        currentPhase: 'spinning'
      })
    })

    // 🎯 通知状态协调器
    await this.coordinator.updateState({
      type: 'animation_start',
      data: {
        animationId,
        animationType: type,
        duration,
        players,
        metadata
      },
      timestamp: Date.now(),
      source: 'animation_coordinator'
    })

    // 🎯 触发动画事件
    this.publishAnimationEvent({
      type: 'animation_start',
      animationId,
      timestamp: Date.now()
    })

    // 🎯 设置同步定时器
    this.setupSyncTimer(duration)
  }

  // 🎯 停止动画
  async stopAnimation() {
    if (!this.state.value.isAnimating) return

    const animationId = this.state.value.animationId!
    console.log(`[🎬ANIM-COORDINATOR] 停止动画: ${animationId}`)

    // 清理定时器
    if (this.syncTimer) {
      clearTimeout(this.syncTimer)
      this.syncTimer = null
    }

    // 🎯 标记所有玩家动画为完成
    this.state.value.playerAnimations.forEach(playerAnim => {
      playerAnim.isAnimating = false
      playerAnim.currentPhase = 'completed'
    })

    // 🎯 重置动画状态
    this.state.value.isAnimating = false
    this.state.value.progress = 1
    this.state.value.lastSyncTime = Date.now()

    // 🎯 处理待处理的数据更新
    await this.processPendingDataUpdates()

    // 🎯 触发动画结束事件
    this.publishAnimationEvent({
      type: 'animation_end',
      animationId,
      timestamp: Date.now()
    })

    // 🎯 通知状态协调器
    await this.coordinator.updateState({
      type: 'animation_complete',
      data: {
        animationId,
        completedPlayers: Array.from(this.state.value.playerAnimations.keys())
      },
      timestamp: Date.now(),
      source: 'animation_coordinator'
    })
  }

  // 🎯 更新玩家动画进度
  async updatePlayerAnimation(playerIndex: number, update: {
    phase?: 'spinning' | 'slowing' | 'revealing' | 'completed'
    result?: any
    progress?: number
  }) {
    const playerAnim = this.state.value.playerAnimations.get(playerIndex)
    if (!playerAnim) {
      console.warn(`[🎬ANIM-COORDINATOR] 玩家${playerIndex}动画状态不存在`)
      return
    }

    const { phase, result, progress } = update

    // 🎯 更新动画阶段
    if (phase) {
      playerAnim.currentPhase = phase
      
      // 如果进入revealing阶段，准备数据更新
      if (phase === 'revealing' && result) {
        this.scheduleDataUpdate({
          type: 'player_result',
          playerIndex,
          data: result,
          timestamp: Date.now(),
          scheduledTime: Date.now() + 500, // 500ms后更新数据
          source: 'player_animation_reveal'
        })
      }
      
      // 如果动画完成，标记为完成
      if (phase === 'completed') {
        playerAnim.isAnimating = false
        playerAnim.result = result
        
        // 检查是否所有玩家都完成了
        if (this.isAllPlayersAnimationComplete()) {
          await this.stopAnimation()
        }
      }
    }

    console.log(`[🎬ANIM-COORDINATOR] 更新玩家${playerIndex}动画: ${phase || 'progress'}`)
  }

  // 🎯 调度数据更新
  private scheduleDataUpdate(update: DataUpdateEvent) {
    this.state.value.pendingDataUpdates.push(update)
    this.state.value.isDataUpdatePending = true
    
    console.log(`[🎬ANIM-COORDINATOR] 调度数据更新: ${update.type}`)
  }

  // 🎯 处理待处理的数据更新
  private async processPendingDataUpdates() {
    if (this.state.value.pendingDataUpdates.length === 0) return

    console.log(`[🎬ANIM-COORDINATOR] 处理${this.state.value.pendingDataUpdates.length}个待处理数据更新`)

    // 按时间排序
    const sortedUpdates = this.state.value.pendingDataUpdates.sort((a, b) => 
      (a.scheduledTime || a.timestamp) - (b.scheduledTime || b.timestamp)
    )

    for (const update of sortedUpdates) {
      await this.processDataUpdate(update)
    }

    // 清空队列
    this.state.value.pendingDataUpdates.length = 0
    this.state.value.isDataUpdatePending = false
  }

  // 🎯 处理单个数据更新
  private async processDataUpdate(update: DataUpdateEvent) {
    const { type, playerIndex, data, source } = update

    switch (type) {
      case 'player_result':
        if (playerIndex !== undefined) {
          // 更新玩家轮次进度
          await this.roundManager.updatePlayerProgress(playerIndex, {
            status: 'completed',
            result: data,
            completionTime: Date.now()
          })
        }
        break
      
      case 'round_complete':
        // 完成当前轮次
        await this.roundManager.completeCurrentRound()
        break
      
             case 'battle_end':
         // 对战结束处理
         await this.coordinator.updateState({
           type: 'battle_status_change',
           data: data,
           timestamp: Date.now(),
           source: 'animation_coordinator'
         })
         break
    }

    console.log(`[🎬ANIM-COORDINATOR] 处理数据更新: ${type}`)
  }

  // 🎯 设置同步定时器
  private setupSyncTimer(duration: number) {
    // 在动画结束前100ms触发数据同步
    const syncDelay = Math.max(duration - 100, 0)
    
    this.syncTimer = setTimeout(async () => {
      console.log('[🎬ANIM-COORDINATOR] 触发数据同步')
      
      // 触发数据同步事件
      this.publishAnimationEvent({
        type: 'data_sync',
        animationId: this.state.value.animationId!,
        timestamp: Date.now()
      })
      
      // 处理待处理的数据更新
      await this.processPendingDataUpdates()
      
    }, syncDelay)
  }

  // 🎯 更新动画进度
  private updateAnimationProgress() {
    if (!this.state.value.isAnimating) return

    const elapsed = Date.now() - this.state.value.startTime
    const progress = Math.min(elapsed / this.state.value.expectedDuration, 1)
    
    this.state.value.progress = progress

    // 发布进度事件
    this.publishAnimationEvent({
      type: 'animation_progress',
      animationId: this.state.value.animationId!,
      progress,
      timestamp: Date.now()
    })
  }

  // 🎯 检查所有玩家动画是否完成
  private isAllPlayersAnimationComplete(): boolean {
    return Array.from(this.state.value.playerAnimations.values()).every(
      playerAnim => !playerAnim.isAnimating
    )
  }

  // 🎯 处理轮次开始动画
  private async handleRoundStartAnimation(event: any) {
    const { metadata } = event
    
    if (metadata?.animationId) {
      await this.startAnimation({
        animationId: metadata.animationId,
        type: 'case_opening',
        duration: metadata.duration || 3000,
        players: metadata.players || [],
        metadata
      })
    }
  }

  // 🎯 处理轮次完成动画
  private async handleRoundCompleteAnimation(event: any) {
    // 确保动画已完成
    if (this.state.value.isAnimating) {
      await this.stopAnimation()
    }
  }

  // 🎯 处理对战完成动画
  private async handleBattleCompleteAnimation(event: any) {
    // 启动对战结束动画
    await this.startAnimation({
      animationId: `battle_end_${Date.now()}`,
      type: 'battle_end',
      duration: 2000,
      players: [],
      metadata: event.metadata
    })
  }

  // 🎯 发布动画事件
  private publishAnimationEvent(event: AnimationEvent) {
    const callback = this.animationCallbacks.get(event.animationId)
    if (callback) {
      callback(event)
    }

    // 全局事件
    window.dispatchEvent(new CustomEvent('battle:animation', {
      detail: event
    }))
  }

  // 🎯 注册动画回调
  registerAnimationCallback(animationId: string, callback: (event: AnimationEvent) => void) {
    this.animationCallbacks.set(animationId, callback)
    
    return () => {
      this.animationCallbacks.delete(animationId)
    }
  }

  // 🎯 获取动画状态
  getAnimationState() {
    return this.state.value
  }

  // 🎯 获取玩家动画状态
  getPlayerAnimationState(playerIndex: number) {
    return this.state.value.playerAnimations.get(playerIndex)
  }

  // 🎯 检查是否正在动画
  isAnimating() {
    return this.state.value.isAnimating
  }

  // 🎯 检查玩家是否正在动画
  isPlayerAnimating(playerIndex: number) {
    const playerAnim = this.state.value.playerAnimations.get(playerIndex)
    return playerAnim?.isAnimating || false
  }

  // 🎯 获取动画进度
  getAnimationProgress() {
    return this.state.value.progress
  }

  // 🎯 获取动画统计信息（用于调试）
  getStats() {
    const coordinatorState = this.coordinator.getState()
    const totalPlayers = coordinatorState.battleData?.bets?.length || 0
    
    return {
      isAnimating: this.state.value.isAnimating,
      animationId: this.state.value.animationId,
      animationType: this.state.value.animationType,
      progress: this.state.value.progress,
      startTime: this.state.value.startTime,
      expectedDuration: this.state.value.expectedDuration,
      totalPlayers,
      animatingPlayers: Array.from(this.state.value.playerAnimations.values()).filter(p => p.isAnimating).length,
      pendingUpdates: this.state.value.pendingDataUpdates.length,
      isDataUpdatePending: this.state.value.isDataUpdatePending,
      lastSyncTime: this.state.value.lastSyncTime,
      playerAnimations: Array.from(this.state.value.playerAnimations.entries()).map(([index, anim]) => ({
        playerIndex: index,
        isAnimating: anim.isAnimating,
        currentPhase: anim.currentPhase,
        startTime: anim.startTime,
        expectedEndTime: anim.expectedEndTime
      }))
    }
  }

  // 🎯 等待动画完成
  async waitForAnimationComplete(timeout = 10000): Promise<boolean> {
    if (!this.state.value.isAnimating) return true

    return new Promise((resolve) => {
      const startTime = Date.now()
      
      const checkComplete = () => {
        if (!this.state.value.isAnimating) {
          resolve(true)
          return
        }
        
        if (Date.now() - startTime > timeout) {
          console.warn('[🎬ANIM-COORDINATOR] 等待动画完成超时')
          resolve(false)
          return
        }
        
        setTimeout(checkComplete, 100)
      }
      
      checkComplete()
    })
  }

  // 🎯 强制同步数据
  async forceSyncData() {
    console.log('[🎬ANIM-COORDINATOR] 强制同步数据')
    await this.processPendingDataUpdates()
  }

  // 🎯 清理方法
  cleanup() {
    console.log('[🎬ANIM-COORDINATOR] 清理动画协调器')
    
    if (this.syncTimer) {
      clearTimeout(this.syncTimer)
      this.syncTimer = null
    }
    
    if (this.progressTimer) {
      clearInterval(this.progressTimer)
      this.progressTimer = null
    }
    
    this.state.value.playerAnimations.clear()
    this.state.value.pendingDataUpdates.length = 0
    this.animationCallbacks.clear()
  }
}

// 🎯 全局动画协调器实例
let globalAnimationCoordinator: BattleAnimationCoordinator | null = null

export const useBattleAnimationCoordinator = () => {
  if (!globalAnimationCoordinator) {
    globalAnimationCoordinator = new BattleAnimationCoordinator()
  }
  
  return globalAnimationCoordinator
}

// 🎯 Composable接口 (协调器版本)
export const useBattleAnimationCoord = () => {
  const coordinator = useBattleAnimationCoordinator()
  
  return {
    // 状态访问
    isAnimating: computed(() => coordinator.isAnimating()),
    animationProgress: computed(() => coordinator.getAnimationProgress()),
    animationState: computed(() => coordinator.getAnimationState()),
    
    // 动画控制
    startAnimation: coordinator.startAnimation.bind(coordinator),
    stopAnimation: coordinator.stopAnimation.bind(coordinator),
    updatePlayerAnimation: coordinator.updatePlayerAnimation.bind(coordinator),
    
    // 状态查询
    getPlayerAnimationState: coordinator.getPlayerAnimationState.bind(coordinator),
    isPlayerAnimating: coordinator.isPlayerAnimating.bind(coordinator),
    
    // 工具方法
    waitForAnimationComplete: coordinator.waitForAnimationComplete.bind(coordinator),
    forceSyncData: coordinator.forceSyncData.bind(coordinator),
    registerAnimationCallback: coordinator.registerAnimationCallback.bind(coordinator),
    
    // 清理
    cleanup: coordinator.cleanup.bind(coordinator)
  }
} 