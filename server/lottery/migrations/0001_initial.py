# -*- coding: utf-8 -*-
# Generated by Django 1.11.13 on 2021-04-14 02:09
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import steambase.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODELL),
        ('package', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='LotteryInfoSetting',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(blank=True, default=None, max_length=50, null=True, verbose_name='title')),
                ('comment', models.CharField(blank=True, default=None, max_length=50, null=True, verbose_name='comment')),
                ('lottery_type', models.SmallIntegerField(choices=[(0, 'Hourly'), (1, 'Daily'), (2, 'Weekly')], default=0, verbose_name='lottery type')),
                ('no', models.IntegerField(default=0, verbose_name='order No.')),
                ('enable', models.BooleanField(default=True, verbose_name='enable')),
            ],
            options={
                'verbose_name': 'Lottery Info Setting',
                'verbose_name_plural': 'Lottery Info Setting',
            },
        ),
        migrations.CreateModel(
            name='LotteryJoiner',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('lottery_type', models.SmallIntegerField(choices=[(0, 'Hourly'), (1, 'Daily'), (2, 'Weekly')], default=0, verbose_name='lottery type')),
                ('charge', models.FloatField(default=0, verbose_name='charge amount')),
                ('state', models.SmallIntegerField(choices=[(0, 'Not enough charge'), (1, 'Joined'), (2, 'Full'), (3, 'End')], default=0, verbose_name='state')),
                ('win', models.BooleanField(default=False, verbose_name='win')),
                ('item_info', models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='lottery_joiner', to='package.ItemInfo', verbose_name='item info')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lottery_joiner', to=settings.AUTH_USER_MODELL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'Lottery Joiner',
                'verbose_name_plural': 'Lottery Joiner',
            },
        ),
        migrations.CreateModel(
            name='LotterySetting',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('lottery_type', models.SmallIntegerField(choices=[(0, 'Hourly'), (1, 'Daily'), (2, 'Weekly')], default=0, verbose_name='lottery type')),
                ('min_charge', models.FloatField(default=0, verbose_name='min charge')),
                ('max_joiner', models.IntegerField(default=0, verbose_name='max joiner')),
                ('enable', models.BooleanField(default=True, verbose_name='enable')),
                ('item_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lottery_award', to='package.ItemInfo', verbose_name='item info')),
            ],
            options={
                'verbose_name': 'Lottery Setting',
                'verbose_name_plural': 'Lottery Setting',
            },
        ),
    ]
