# Market 商城模块分析文档

## 模块概述

Market模块是平台的商城系统，提供饰品直接购买服务。用户可以浏览平台提供的饰品库存，通过多维度筛选找到心仪的物品，并直接用余额购买。该模块为用户提供了确定性的物品获取方式，是开箱、夺宝等概率性获取方式的重要补充。

## 目录结构

```
market/
├── __init__.py
├── admin.py                 # Django管理后台配置
├── apps.py                  # 应用配置
├── business.py              # 核心业务逻辑
├── models.py                # 数据模型定义
├── serializers.py           # API序列化器
├── tests.py                 # 单元测试
├── urls.py                  # URL路由配置
├── views.py                 # API视图
├── interfaces.py            # 接口定义（当前为空）
└── migrations/              # 数据库迁移文件
```

## 数据模型分析

### MarketItem - 商城物品
```python
class MarketItem(ModelBase):
    item_info = models.OneToOneField(ItemInfo)               # 关联物品信息
    rarity_cn = models.CharField(max_length=128)             # 稀有度中文名
    exterior = models.CharField(max_length=128)              # 磨损度中文名
    dark_gold = models.BooleanField(default=False)           # 是否暗金武器
```

**特点：**
- 与ItemInfo一对一关联，复用基础物品数据
- 增加中文本地化字段，提升用户体验
- 支持暗金武器特殊标识
- 继承ModelBase，具备基础字段（uid、创建时间等）

**设计问题：**
- 模型过于简单，缺少库存、上架状态等关键字段
- 价格依赖ItemInfo关联表，可能存在性能问题
- 缺少商品分类、推荐、热销等运营字段

## API 接口分析

### 1. 商品库存列表
**路径：** `GET /api/market/inventory/`

**功能：** 获取商城商品列表，支持多维度筛选

**参数：**
- `page`: 页码
- `pageSize`: 每页数量
- `category`: 物品分类 (步枪、手枪、匕首等)
- `rarity`: 稀有度 (工业级、军规级、保密级等)
- `ext`: 磨损度 (崭新出厂、略有磨损等)
- `dark`: 是否暗金武器
- `q`: 搜索关键词
- `min`/`max`: 价格区间
- `sort`: 排序方式 (up-升序, down-降序)

**业务逻辑：**
1. 根据筛选条件构建查询参数
2. 支持关键词模糊搜索（中英文名称）
3. 按价格排序（依赖关联表）
4. 分页返回结果并补充价格信息

### 2. 查询条件获取
**路径：** `GET /api/market/term/`

**功能：** 获取商城筛选条件的配置信息

**响应格式：**
```json
{
    "type": {
        "CSGO_Type_Rifle": {
            "name": "步枪",
            "data": ["AK-47", "M4A4", "M4A1-S", ...]
        }
    },
    "rarity": {
        "Rarity_Uncommon_Weapon": {
            "name": "工业级",
            "color": "5e98d9"
        }
    },
    "exterior": {
        "WearCategory0": "崭新出厂",
        "WearCategory1": "略有磨损"
    }
}
```

### 3. 购买商品
**路径：** `POST /api/market/buy/`

**功能：** 购买指定的商城商品

**参数：**
```json
{
    "uid": "market_item_uuid"
}
```

**业务流程：**
1. 验证用户登录状态
2. 查找指定商品
3. 获取当前市场价格
4. 扣除用户余额
5. 创建包裹物品
6. 返回包裹UID

## 核心业务逻辑分析

### 1. 商品查询逻辑
```python
def get_market_item(page, limit, category, rarity, exterior, dark, query, max_price, min_price, sort):
    """商品查询的核心逻辑"""
    # 构建基础查询条件
    query_params = {}
    if category:
        query_params['item_info__type'] = category
    if rarity:
        query_params['item_info__rarity'] = rarity
    if exterior:
        query_params['item_info__exterior'] = exterior
    if dark:
        query_params['dark_gold'] = dark
    
    # 价格区间筛选（跨表查询）
    if min_price:
        query_params['item_info__item_price__price__gt'] = float(min_price)
    if max_price:
        query_params['item_info__item_price__price__lt'] = float(max_price)
    
    # 基础查询
    market_items = MarketItem.objects.filter(**query_params)
    
    # 关键词搜索（支持中英文）
    if query:
        market_items = market_items.filter(
            Q(item_info__market_hash_name__contains=query) | 
            Q(item_info__market_name_cn__contains=query)
        ).distinct()
    
    # 价格排序
    if sort:
        order_map = {'up': '', 'down': '-'}
        market_items = market_items.order_by(
            order_map[sort] + 'item_info__item_price__price'
        )
    
    return market_items
```

### 2. 购买逻辑
```python
def buy_market_items(user, uid):
    """商品购买的核心逻辑"""
    with transaction.atomic():
        # 查找商品
        market_item = MarketItem.objects.filter(uid=uid).first()
        
        # 获取实时价格
        price = get_item_price(market_item.item_info.market_hash_name)
        
        # 扣除余额
        try:
            user.update_balance(-price, 
                remark=f"商城购买饰品:{market_item.item_info.market_hash_name}")
        except ParamException:
            return RespCode.NoBalance.value, "余额不足, 请充值"
        
        # 创建包裹物品
        package = PackageItem.objects.create(
            user=user,
            item_info=market_item.item_info,
            assetid='0',
            instanceid='0', 
            state=PackageState.Available.value,
            amount=price
        )
    
    return RespCode.Succeed.value, {"uid": package.uid}
```

### 3. 筛选条件生成
```python
def get_market_query_term():
    """生成筛选条件配置"""
    # 静态映射配置
    exterior_map = {
        'WearCategory0': '崭新出厂',
        'WearCategory1': '略有磨损',
        # ... 其他磨损度
    }
    
    rarity_map = {
        'Rarity_Uncommon_Weapon': {
            'name': '工业级', 
            'color': '5e98d9'
        },
        # ... 其他稀有度
    }
    
    category_map = {
        'CSGO_Type_Rifle': {'name': '步枪'},
        'CSGO_Type_Pistol': {'name': '手枪'},
        # ... 其他分类
    }
    
    # 动态生成武器名称列表
    for category in category_map.keys():
        item_qs = ItemInfo.objects.filter(type=category).only('market_name_cn')
        name_cn_list = []
        for item in item_qs:
            # 提取武器名称（去除皮肤名）
            name = item.market_name_cn.split("|")[0].strip()
            name = re.sub(r"(（)(.*?)(）)", '', name)  # 去除括号内容
            if name not in name_cn_list:
                name_cn_list.append(name)
        category_map[category]['data'] = name_cn_list
    
    return {'type': category_map, 'rarity': rarity_map, 'exterior': exterior_map}
```

## 存在的问题与风险

### 1. 数据模型设计缺陷

**问题描述：**
- MarketItem模型过于简单，缺少关键业务字段
- 缺少库存数量管理，可能出现超卖
- 缺少商品状态控制（上架/下架）
- 价格依赖外部表，查询性能差

**影响：**
- 无法精确控制商品销售
- 运营管理困难
- 查询性能低下
- 数据一致性风险

### 2. 业务逻辑不完整

**问题描述：**
- 缺少库存检查机制
- 缺少购买限制（单用户、单商品）
- 缺少购买历史记录
- 缺少退款和售后机制

**影响：**
- 可能出现业务异常
- 用户体验不完整
- 客服处理困难
- 财务风险

### 3. 性能问题

**问题描述：**
- 大量跨表查询（item_info、item_price等）
- 缺少必要的数据库索引
- 没有缓存机制
- 价格实时查询可能很慢

**影响：**
- 页面加载缓慢
- 数据库压力大
- 用户体验差
- 系统稳定性风险

### 4. 功能不完整

**问题描述：**
- 缺少商品推荐功能
- 缺少收藏和愿望单
- 缺少评价和评论系统
- 缺少促销和折扣机制

**影响：**
- 用户粘性不足
- 销售转化率低
- 运营手段有限
- 竞争力不足

## 改进建议

### 1. 数据模型重构

#### 完善的商城物品模型
```python
class MarketItem(ModelBase):
    # 基础信息
    item_info = models.OneToOneField(ItemInfo, on_delete=models.CASCADE)
    name_cn = models.CharField(max_length=256)               # 中文名称
    description = models.TextField(blank=True)               # 商品描述
    
    # 价格管理
    base_price = models.FloatField(default=0)                # 基础价格
    sale_price = models.FloatField(default=0)                # 销售价格
    discount_rate = models.FloatField(default=100)           # 折扣比例
    
    # 库存管理
    stock_quantity = models.IntegerField(default=0)          # 库存数量
    sold_quantity = models.IntegerField(default=0)           # 已售数量
    unlimited_stock = models.BooleanField(default=True)      # 无限库存
    
    # 状态管理
    is_active = models.BooleanField(default=True)            # 是否上架
    is_featured = models.BooleanField(default=False)         # 是否推荐
    is_hot = models.BooleanField(default=False)              # 是否热销
    
    # 购买限制
    max_per_user = models.IntegerField(default=0)            # 单用户购买限制
    purchase_limit_period = models.IntegerField(default=0)   # 限制周期（天）
    
    # 分类标签
    category_tags = models.ManyToManyField('MarketCategory', blank=True)
    rarity_cn = models.CharField(max_length=128)
    exterior_cn = models.CharField(max_length=128)
    special_tags = models.JSONField(default=list)            # 特殊标签
    
    # 统计信息
    view_count = models.IntegerField(default=0)              # 浏览次数
    purchase_count = models.IntegerField(default=0)          # 购买次数
    rating_avg = models.FloatField(default=0)                # 平均评分
    review_count = models.IntegerField(default=0)            # 评价数量
    
    # 时间控制
    sale_start_time = models.DateTimeField(null=True, blank=True)  # 开售时间
    sale_end_time = models.DateTimeField(null=True, blank=True)    # 停售时间
    
    class Meta:
        verbose_name = '商城物品'
        verbose_name_plural = '商城物品'
        indexes = [
            models.Index(fields=['is_active', 'sale_price']),
            models.Index(fields=['category_tags', 'is_active']),
            models.Index(fields=['is_featured', '-create_time']),
        ]

class MarketCategory(models.Model):
    """商品分类"""
    name = models.CharField(max_length=128)
    name_en = models.CharField(max_length=128)
    icon = models.ImageField(upload_to='market/category/')
    order = models.IntegerField(default=0)
    is_active = models.BooleanField(default=True)
    parent = models.ForeignKey('self', null=True, blank=True, on_delete=models.CASCADE)

class MarketPurchaseRecord(ModelBase):
    """购买记录"""
    user = models.ForeignKey(USER_MODEL, on_delete=models.CASCADE)
    market_item = models.ForeignKey(MarketItem, on_delete=models.CASCADE)
    quantity = models.IntegerField(default=1)
    unit_price = models.FloatField()
    total_price = models.FloatField()
    discount_amount = models.FloatField(default=0)
    payment_method = models.CharField(max_length=50)
    status = models.CharField(max_length=20, default='completed')
    package_item = models.ForeignKey(PackageItem, null=True, on_delete=models.SET_NULL)
```

### 2. 业务逻辑增强

#### 库存管理系统
```python
class MarketInventoryManager:
    def __init__(self):
        self.redis_client = redis.Redis()
    
    def check_stock_availability(self, market_item, quantity=1):
        """检查库存可用性"""
        if market_item.unlimited_stock:
            return True
        
        # 使用Redis分布式锁确保库存一致性
        lock_key = f"market_stock_lock:{market_item.uid}"
        with self.redis_client.lock(lock_key, timeout=10):
            current_stock = self.get_current_stock(market_item)
            return current_stock >= quantity
    
    def reserve_stock(self, market_item, quantity=1):
        """预留库存"""
        lock_key = f"market_stock_lock:{market_item.uid}"
        with self.redis_client.lock(lock_key, timeout=10):
            if not self.check_stock_availability(market_item, quantity):
                raise InsufficientStockError("库存不足")
            
            # 减少可用库存
            market_item.stock_quantity -= quantity
            market_item.save()
            
            return True
    
    def release_stock(self, market_item, quantity=1):
        """释放预留库存（取消订单时）"""
        lock_key = f"market_stock_lock:{market_item.uid}"
        with self.redis_client.lock(lock_key, timeout=10):
            market_item.stock_quantity += quantity
            market_item.save()
    
    def get_current_stock(self, market_item):
        """获取当前可用库存"""
        if market_item.unlimited_stock:
            return float('inf')
        
        return max(0, market_item.stock_quantity)

class MarketPurchaseLimiter:
    def __init__(self):
        self.redis_client = redis.Redis()
    
    def check_purchase_limit(self, user, market_item, quantity=1):
        """检查购买限制"""
        if market_item.max_per_user <= 0:
            return True
        
        # 计算时间范围
        if market_item.purchase_limit_period > 0:
            start_time = timezone.now() - timedelta(days=market_item.purchase_limit_period)
            purchased_count = MarketPurchaseRecord.objects.filter(
                user=user,
                market_item=market_item,
                create_time__gte=start_time,
                status='completed'
            ).aggregate(total=Sum('quantity'))['total'] or 0
        else:
            # 总购买限制
            purchased_count = MarketPurchaseRecord.objects.filter(
                user=user,
                market_item=market_item,
                status='completed'
            ).aggregate(total=Sum('quantity'))['total'] or 0
        
        return purchased_count + quantity <= market_item.max_per_user
```

#### 智能定价系统
```python
class MarketPricingEngine:
    def __init__(self):
        self.base_markup = 0.1  # 基础加价10%
        self.demand_factor = 0.05  # 需求影响因子
        self.inventory_factor = 0.03  # 库存影响因子
    
    def calculate_dynamic_price(self, market_item):
        """计算动态价格"""
        # 获取基础市场价格
        base_price = get_item_price(market_item.item_info.market_hash_name)
        
        # 需求调节
        demand_multiplier = self.calculate_demand_multiplier(market_item)
        
        # 库存调节
        inventory_multiplier = self.calculate_inventory_multiplier(market_item)
        
        # 计算最终价格
        final_price = base_price * (1 + self.base_markup) * demand_multiplier * inventory_multiplier
        
        # 应用折扣
        if market_item.discount_rate < 100:
            final_price = final_price * market_item.discount_rate / 100
        
        return round(final_price, 2)
    
    def calculate_demand_multiplier(self, market_item):
        """计算需求乘数"""
        # 基于最近7天的浏览和购买数据
        recent_views = self.get_recent_views(market_item, days=7)
        recent_purchases = self.get_recent_purchases(market_item, days=7)
        
        # 需求指数 = 购买次数 / 浏览次数
        demand_ratio = recent_purchases / max(recent_views, 1)
        
        # 转换为价格乘数
        return 1 + (demand_ratio * self.demand_factor)
    
    def calculate_inventory_multiplier(self, market_item):
        """计算库存乘数"""
        if market_item.unlimited_stock:
            return 1.0
        
        # 库存紧张度
        stock_ratio = market_item.stock_quantity / max(market_item.sold_quantity, 1)
        
        # 库存越少，价格越高
        if stock_ratio < 0.1:  # 库存不足10%
            return 1 + self.inventory_factor * 2
        elif stock_ratio < 0.3:  # 库存不足30%
            return 1 + self.inventory_factor
        else:
            return 1.0
```

### 3. 推荐系统

#### 个性化推荐引擎
```python
class MarketRecommendationEngine:
    def __init__(self):
        self.user_behavior_weight = 0.4
        self.item_similarity_weight = 0.3
        self.popularity_weight = 0.3
    
    def get_personalized_recommendations(self, user, limit=10):
        """获取个性化推荐"""
        # 用户行为分析
        user_profile = self.analyze_user_behavior(user)
        
        # 获取候选商品
        candidates = self.get_candidate_items(user_profile)
        
        # 计算推荐分数
        scored_items = []
        for item in candidates:
            score = self.calculate_recommendation_score(user, item, user_profile)
            scored_items.append((item, score))
        
        # 排序并返回
        scored_items.sort(key=lambda x: x[1], reverse=True)
        return [item for item, score in scored_items[:limit]]
    
    def analyze_user_behavior(self, user):
        """分析用户行为模式"""
        # 购买历史
        purchase_history = MarketPurchaseRecord.objects.filter(
            user=user,
            status='completed'
        ).select_related('market_item')
        
        # 浏览历史（需要添加浏览记录模型）
        # view_history = MarketViewRecord.objects.filter(user=user)
        
        profile = {
            'preferred_categories': {},
            'preferred_price_range': None,
            'preferred_rarity': {},
            'purchase_frequency': None
        }
        
        # 分析偏好分类
        for record in purchase_history:
            category = record.market_item.item_info.type
            profile['preferred_categories'][category] = profile['preferred_categories'].get(category, 0) + 1
        
        # 分析价格偏好
        prices = [record.total_price for record in purchase_history]
        if prices:
            profile['preferred_price_range'] = (min(prices), max(prices))
        
        return profile
    
    def calculate_item_similarity(self, item1, item2):
        """计算物品相似度"""
        similarity = 0
        
        # 分类相似度
        if item1.item_info.type == item2.item_info.type:
            similarity += 0.4
        
        # 稀有度相似度
        if item1.item_info.rarity == item2.item_info.rarity:
            similarity += 0.3
        
        # 价格相似度
        price1 = item1.sale_price
        price2 = item2.sale_price
        price_diff = abs(price1 - price2) / max(price1, price2)
        similarity += (1 - price_diff) * 0.3
        
        return similarity
```

### 4. 缓存优化

#### 多级缓存系统
```python
class MarketCacheManager:
    def __init__(self):
        self.redis_client = redis.Redis()
        self.cache_timeouts = {
            'item_list': 300,       # 商品列表5分钟
            'item_detail': 600,     # 商品详情10分钟
            'category_tree': 3600,  # 分类树1小时
            'hot_items': 1800,      # 热销商品30分钟
        }
    
    def get_cached_item_list(self, query_params):
        """获取缓存的商品列表"""
        cache_key = self.build_list_cache_key(query_params)
        cached_data = self.redis_client.get(cache_key)
        
        if cached_data:
            return json.loads(cached_data)
        
        # 查询数据库
        items = self.query_items_from_db(query_params)
        
        # 缓存结果
        self.redis_client.setex(
            cache_key,
            self.cache_timeouts['item_list'],
            json.dumps(items)
        )
        
        return items
    
    def invalidate_item_cache(self, market_item_id):
        """清除商品相关缓存"""
        patterns = [
            f"market:item_detail:{market_item_id}",
            "market:item_list:*",
            "market:hot_items",
            "market:featured_items"
        ]
        
        for pattern in patterns:
            keys = self.redis_client.keys(pattern)
            if keys:
                self.redis_client.delete(*keys)
    
    def build_list_cache_key(self, query_params):
        """构建列表缓存键"""
        # 将查询参数转换为有序字符串
        sorted_params = sorted(query_params.items())
        params_str = urllib.parse.urlencode(sorted_params)
        params_hash = hashlib.md5(params_str.encode()).hexdigest()
        return f"market:item_list:{params_hash}"
```

## 监控和运营

### 商城数据分析
```python
class MarketAnalytics:
    def __init__(self):
        self.redis_client = redis.Redis()
    
    def generate_sales_report(self, start_date, end_date):
        """生成销售报告"""
        records = MarketPurchaseRecord.objects.filter(
            create_time__range=[start_date, end_date],
            status='completed'
        )
        
        report = {
            'total_sales': records.aggregate(total=Sum('total_price'))['total'] or 0,
            'total_orders': records.count(),
            'avg_order_value': 0,
            'top_selling_items': [],
            'category_breakdown': {},
            'daily_sales': {}
        }
        
        if report['total_orders'] > 0:
            report['avg_order_value'] = report['total_sales'] / report['total_orders']
        
        # 热销商品
        top_items = records.values('market_item').annotate(
            total_qty=Sum('quantity'),
            total_revenue=Sum('total_price')
        ).order_by('-total_qty')[:10]
        
        for item in top_items:
            market_item = MarketItem.objects.get(id=item['market_item'])
            report['top_selling_items'].append({
                'name': market_item.name_cn,
                'quantity': item['total_qty'],
                'revenue': item['total_revenue']
            })
        
        return report
    
    def track_user_behavior(self, user, action, item_id=None, **kwargs):
        """追踪用户行为"""
        behavior_data = {
            'user_id': user.id,
            'action': action,  # view, add_to_cart, purchase, etc.
            'item_id': item_id,
            'timestamp': time.time(),
            **kwargs
        }
        
        # 存储到Redis用于实时分析
        self.redis_client.lpush('market:user_behavior', json.dumps(behavior_data))
        self.redis_client.ltrim('market:user_behavior', 0, 10000)  # 保留最近10000条
```

## 总结

Market模块当前实现了基础的商城功能，但在数据模型、业务逻辑、性能优化等方面存在显著不足。建议按照上述改进方案进行系统性重构，重点关注库存管理、购买限制、性能优化和用户体验提升。通过完善的商城系统，可以为用户提供更好的购物体验，为平台创造更多收益。

## 接口清单

| 接口名称 | 方法 | 路径 | 状态 | 功能描述 |
|---------|------|------|------|----------|
| 商品库存列表 | GET | `/api/market/inventory/` | 正常 | 获取商品列表 |
| 查询条件获取 | GET | `/api/market/term/` | 正常 | 获取筛选条件 |
| 购买商品 | POST | `/api/market/buy/` | 正常 | 购买指定商品 |

**注：** 当前接口数量较少，建议增加商品详情、收藏、评价、推荐等接口以完善功能。
