# Generated manually for articles app

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ContentCategory',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('name', models.CharField(max_length=128, verbose_name='Category Name')),
                ('slug', models.SlugField(max_length=128, unique=True, verbose_name='Slug')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('icon', models.CharField(blank=True, max_length=255, null=True, verbose_name='Icon')),
                ('color', models.CharField(default='#007bff', help_text='Hex color code', max_length=7, verbose_name='Color')),
                ('sort_order', models.IntegerField(default=0, verbose_name='Sort Order')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
            ],
            options={
                'verbose_name': 'Content Category',
                'verbose_name_plural': 'Content Categories',
                'ordering': ['sort_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='ContentTag',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('name', models.CharField(max_length=64, verbose_name='Tag Name')),
                ('slug', models.SlugField(max_length=64, unique=True, verbose_name='Slug')),
                ('color', models.CharField(default='#6c757d', help_text='Hex color code', max_length=7, verbose_name='Color')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
            ],
            options={
                'verbose_name': 'Content Tag',
                'verbose_name_plural': 'Content Tags',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Content',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('title', models.CharField(max_length=255, verbose_name='Title')),
                ('slug', models.SlugField(blank=True, max_length=255, unique=True, verbose_name='Slug')),
                ('subtitle', models.CharField(blank=True, max_length=255, null=True, verbose_name='Subtitle')),
                ('excerpt', models.TextField(blank=True, help_text='Brief description or summary', max_length=500, null=True, verbose_name='Excerpt')),
                ('content', models.TextField(blank=True, null=True, verbose_name='Content')),
                ('content_type', models.CharField(choices=[('article', 'Article'), ('announcement', 'Announcement'), ('news', 'News'), ('guide', 'Guide'), ('faq', 'FAQ'), ('policy', 'Policy'), ('notice', 'Notice')], default='article', max_length=20, verbose_name='Content Type')),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('published', 'Published'), ('archived', 'Archived'), ('scheduled', 'Scheduled')], default='draft', max_length=20, verbose_name='Status')),
                ('priority', models.CharField(choices=[('low', 'Low'), ('normal', 'Normal'), ('high', 'High'), ('urgent', 'Urgent')], default='normal', max_length=20, verbose_name='Priority')),
                ('is_featured', models.BooleanField(default=False, verbose_name='Is Featured')),
                ('is_pinned', models.BooleanField(default=False, verbose_name='Is Pinned')),
                ('publish_date', models.DateTimeField(default=django.utils.timezone.now, verbose_name='Publish Date')),
                ('expire_date', models.DateTimeField(blank=True, null=True, verbose_name='Expire Date')),
                ('featured_image', models.CharField(blank=True, max_length=500, null=True, verbose_name='Featured Image')),
                ('thumbnail', models.CharField(blank=True, max_length=500, null=True, verbose_name='Thumbnail')),
                ('seo_title', models.CharField(blank=True, max_length=255, null=True, verbose_name='SEO Title')),
                ('seo_description', models.TextField(blank=True, max_length=300, null=True, verbose_name='SEO Description')),
                ('seo_keywords', models.CharField(blank=True, max_length=255, null=True, verbose_name='SEO Keywords')),
                ('sort_order', models.IntegerField(default=0, verbose_name='Sort Order')),
                ('view_count', models.PositiveIntegerField(default=0, verbose_name='View Count')),
                ('allow_comments', models.BooleanField(default=True, verbose_name='Allow Comments')),
                ('custom_css_class', models.CharField(blank=True, max_length=255, null=True, verbose_name='Custom CSS Class')),
                ('background_color', models.CharField(blank=True, max_length=7, null=True, verbose_name='Background Color')),
                ('text_color', models.CharField(blank=True, max_length=7, null=True, verbose_name='Text Color')),
                ('author', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='authored_contents', to=settings.AUTH_USER_MODEL, verbose_name='Author')),
                ('category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='contents', to='articles.ContentCategory', verbose_name='Category')),
                ('editor', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='edited_contents', to=settings.AUTH_USER_MODEL, verbose_name='Last Editor')),
                ('tags', models.ManyToManyField(blank=True, related_name='contents', to='articles.ContentTag', verbose_name='Tags')),
            ],
            options={
                'verbose_name': 'Content',
                'verbose_name_plural': 'Contents',
                'ordering': ['-is_pinned', '-priority', '-publish_date'],
            },
        ),
        migrations.CreateModel(
            name='ContentView',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ip_address', models.GenericIPAddressField(verbose_name='IP Address')),
                ('user_agent', models.TextField(blank=True, null=True, verbose_name='User Agent')),
                ('viewed_at', models.DateTimeField(auto_now_add=True, verbose_name='Viewed At')),
                ('content', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='views', to='articles.Content')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Content View',
                'verbose_name_plural': 'Content Views',
            },
        ),
        migrations.CreateModel(
            name='ContentAttachment',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('name', models.CharField(max_length=255, verbose_name='File Name')),
                ('file_path', models.CharField(max_length=500, verbose_name='File Path')),
                ('file_size', models.PositiveIntegerField(help_text='Size in bytes', verbose_name='File Size')),
                ('file_type', models.CharField(max_length=100, verbose_name='File Type')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('download_count', models.PositiveIntegerField(default=0, verbose_name='Download Count')),
                ('content', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attachments', to='articles.Content', verbose_name='Content')),
            ],
            options={
                'verbose_name': 'Content Attachment',
                'verbose_name_plural': 'Content Attachments',
            },
        ),
        migrations.AddIndex(
            model_name='content',
            index=models.Index(fields=['content_type', 'status'], name='articles_co_content_b8b7b8_idx'),
        ),
        migrations.AddIndex(
            model_name='content',
            index=models.Index(fields=['publish_date'], name='articles_co_publish_b8b7b8_idx'),
        ),
        migrations.AddIndex(
            model_name='content',
            index=models.Index(fields=['is_featured'], name='articles_co_is_feat_b8b7b8_idx'),
        ),
        migrations.AddIndex(
            model_name='content',
            index=models.Index(fields=['is_pinned'], name='articles_co_is_pinn_b8b7b8_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='contentview',
            unique_together={('content', 'user', 'ip_address')},
        ),
    ]
