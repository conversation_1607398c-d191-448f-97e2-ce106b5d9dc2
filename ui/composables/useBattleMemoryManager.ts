/**
 * 对战内存管理器
 * 
 * 解决问题：
 * 1. 组件卸载后仍在执行的异步任务导致内存泄漏
 * 2. 定时器清理逻辑分散，可能遗漏
 * 3. 缓存没有清理机制，长时间运行占用过多内存
 * 4. 事件监听器清理不完整
 * 5. 提供统一的资源管理接口
 */

interface TimerRecord {
  id: string;
  timerId: NodeJS.Timeout;
  type: 'timeout' | 'interval';
  delay: number;
  callback: Function;
  createdAt: number;
  isActive: boolean;
}

interface CacheRecord {
  key: string;
  value: any;
  size: number;
  createdAt: number;
  lastAccessed: number;
  accessCount: number;
}

interface AsyncTaskRecord {
  id: string;
  promise: Promise<any>;
  abortController?: AbortController;
  createdAt: number;
  isCompleted: boolean;
  isCancelled: boolean;
}

interface EventListenerRecord {
  target: EventTarget;
  event: string;
  handler: Function;
  options?: any;
  createdAt: number;
}

interface MemoryStats {
  timers: {
    active: number;
    total: number;
    oldestAge: number;
  };
  cache: {
    entries: number;
    totalSize: number;
    hitRate: number;
  };
  asyncTasks: {
    active: number;
    completed: number;
    cancelled: number;
  };
  eventListeners: {
    active: number;
    total: number;
  };
}

export const useBattleMemoryManager = () => {
  // 组件是否已卸载
  const isUnmounted = ref(false);
  
  // 定时器记录
  const timers = ref<Map<string, TimerRecord>>(new Map());
  
  // 缓存记录
  const cache = ref<Map<string, CacheRecord>>(new Map());
  
  // 异步任务记录
  const asyncTasks = ref<Map<string, AsyncTaskRecord>>(new Map());
  
  // 事件监听器记录
  const eventListeners = ref<Set<EventListenerRecord>>(new Set());
  
  // 统计信息
  const stats = ref<MemoryStats>({
    timers: { active: 0, total: 0, oldestAge: 0 },
    cache: { entries: 0, totalSize: 0, hitRate: 0 },
    asyncTasks: { active: 0, completed: 0, cancelled: 0 },
    eventListeners: { active: 0, total: 0 }
  });
  
  // 缓存访问统计
  const cacheHits = ref(0);
  const cacheMisses = ref(0);

  /**
   * 创建安全的定时器
   */
  const createTimer = (
    callback: Function,
    delay: number,
    type: 'timeout' | 'interval' = 'timeout',
    id?: string
  ): string => {
    if (isUnmounted.value) {
      console.warn('[🧠MEMORY-MANAGER] 组件已卸载，拒绝创建定时器');
      return '';
    }
    
    const timerId = id || `timer_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const safeCallback = (...args: any[]) => {
      if (isUnmounted.value) {
        console.warn('[🧠MEMORY-MANAGER] 组件已卸载，取消定时器执行');
        clearTimer(timerId);
        return;
      }
      
      try {
        callback(...args);
      } catch (error: any) {
        console.error('[🧠MEMORY-MANAGER] 定时器执行失败:', error);
      }
    };
    
    const timer = type === 'timeout' 
      ? setTimeout(safeCallback, delay)
      : setInterval(safeCallback, delay);
    
    const record: TimerRecord = {
      id: timerId,
      timerId: timer,
      type,
      delay,
      callback,
      createdAt: Date.now(),
      isActive: true
    };
    
    timers.value.set(timerId, record);
    updateTimerStats();
    
    console.log(`[🧠MEMORY-MANAGER] 创建${type}定时器: ${timerId}`);
    
    return timerId;
  };

  /**
   * 清理定时器
   */
  const clearTimer = (timerId: string): boolean => {
    const record = timers.value.get(timerId);
    if (!record) {
      return false;
    }
    
    if (record.type === 'timeout') {
      clearTimeout(record.timerId);
    } else {
      clearInterval(record.timerId);
    }
    
    record.isActive = false;
    timers.value.delete(timerId);
    updateTimerStats();
    
    console.log(`[🧠MEMORY-MANAGER] 清理定时器: ${timerId}`);
    
    return true;
  };

  /**
   * 清理所有定时器
   */
  const clearAllTimers = (): number => {
    let clearedCount = 0;
    
    for (const [timerId, record] of timers.value.entries()) {
      if (record.isActive) {
        if (record.type === 'timeout') {
          clearTimeout(record.timerId);
        } else {
          clearInterval(record.timerId);
        }
        clearedCount++;
      }
    }
    
    timers.value.clear();
    updateTimerStats();
    
    console.log(`[🧠MEMORY-MANAGER] 清理所有定时器: ${clearedCount} 个`);
    
    return clearedCount;
  };

  /**
   * 设置缓存
   */
  const setCache = (key: string, value: any, maxAge?: number): void => {
    if (isUnmounted.value) {
      console.warn('[🧠MEMORY-MANAGER] 组件已卸载，拒绝设置缓存');
      return;
    }
    
    const size = calculateObjectSize(value);
    const now = Date.now();
    
    const record: CacheRecord = {
      key,
      value,
      size,
      createdAt: now,
      lastAccessed: now,
      accessCount: 0
    };
    
    cache.value.set(key, record);
    updateCacheStats();
    
    // 如果设置了过期时间，创建清理定时器
    if (maxAge) {
      createTimer(() => {
        deleteCache(key);
      }, maxAge, 'timeout', `cache_cleanup_${key}`);
    }
    
    console.log(`[🧠MEMORY-MANAGER] 设置缓存: ${key} (${size} bytes)`);
  };

  /**
   * 获取缓存
   */
  const getCache = <T = any>(key: string): T | null => {
    const record = cache.value.get(key);
    
    if (!record) {
      cacheMisses.value++;
      updateCacheStats();
      return null;
    }
    
    // 更新访问统计
    record.lastAccessed = Date.now();
    record.accessCount++;
    
    cacheHits.value++;
    updateCacheStats();
    
    return record.value as T;
  };

  /**
   * 删除缓存
   */
  const deleteCache = (key: string): boolean => {
    const deleted = cache.value.delete(key);
    if (deleted) {
      updateCacheStats();
      console.log(`[🧠MEMORY-MANAGER] 删除缓存: ${key}`);
    }
    return deleted;
  };

  /**
   * 清理过期缓存
   */
  const cleanupExpiredCache = (maxAge = 5 * 60 * 1000): number => {
    const now = Date.now();
    let cleanedCount = 0;
    
    for (const [key, record] of cache.value.entries()) {
      if (now - record.lastAccessed > maxAge) {
        cache.value.delete(key);
        cleanedCount++;
      }
    }
    
    if (cleanedCount > 0) {
      updateCacheStats();
      console.log(`[🧠MEMORY-MANAGER] 清理过期缓存: ${cleanedCount} 个`);
    }
    
    return cleanedCount;
  };

  /**
   * 清理所有缓存
   */
  const clearAllCache = (): number => {
    const count = cache.value.size;
    cache.value.clear();
    cacheHits.value = 0;
    cacheMisses.value = 0;
    updateCacheStats();
    
    console.log(`[🧠MEMORY-MANAGER] 清理所有缓存: ${count} 个`);
    
    return count;
  };

  /**
   * 创建可取消的异步任务
   */
  const createAsyncTask = <T = any>(
    taskFactory: (abortSignal?: AbortSignal) => Promise<T>,
    id?: string
  ): Promise<T> => {
    if (isUnmounted.value) {
      console.warn('[🧠MEMORY-MANAGER] 组件已卸载，拒绝创建异步任务');
      return Promise.reject(new Error('Component unmounted'));
    }
    
    const taskId = id || `async_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const abortController = new AbortController();
    
    const promise = taskFactory(abortController.signal)
      .then((result) => {
        const record = asyncTasks.value.get(taskId);
        if (record) {
          record.isCompleted = true;
          updateAsyncTaskStats();
        }
        return result;
      })
      .catch((error) => {
        const record = asyncTasks.value.get(taskId);
        if (record) {
          record.isCancelled = true;
          updateAsyncTaskStats();
        }
        throw error;
      });
    
    const record: AsyncTaskRecord = {
      id: taskId,
      promise,
      abortController,
      createdAt: Date.now(),
      isCompleted: false,
      isCancelled: false
    };
    
    asyncTasks.value.set(taskId, record);
    updateAsyncTaskStats();
    
    console.log(`[🧠MEMORY-MANAGER] 创建异步任务: ${taskId}`);
    
    return promise;
  };

  /**
   * 取消异步任务
   */
  const cancelAsyncTask = (taskId: string): boolean => {
    const record = asyncTasks.value.get(taskId);
    if (!record || record.isCompleted || record.isCancelled) {
      return false;
    }
    
    if (record.abortController) {
      record.abortController.abort();
    }
    
    record.isCancelled = true;
    updateAsyncTaskStats();
    
    console.log(`[🧠MEMORY-MANAGER] 取消异步任务: ${taskId}`);
    
    return true;
  };

  /**
   * 取消所有异步任务
   */
  const cancelAllAsyncTasks = (): number => {
    let cancelledCount = 0;
    
    for (const [taskId, record] of asyncTasks.value.entries()) {
      if (!record.isCompleted && !record.isCancelled) {
        if (record.abortController) {
          record.abortController.abort();
        }
        record.isCancelled = true;
        cancelledCount++;
      }
    }
    
    updateAsyncTaskStats();
    
    console.log(`[🧠MEMORY-MANAGER] 取消所有异步任务: ${cancelledCount} 个`);
    
    return cancelledCount;
  };

  /**
   * 添加事件监听器
   */
  const addEventListener = (
    target: EventTarget,
    event: string,
    handler: Function,
    options?: any
  ): void => {
    if (isUnmounted.value) {
      console.warn('[🧠MEMORY-MANAGER] 组件已卸载，拒绝添加事件监听器');
      return;
    }
    
    const safeHandler = (e: Event) => {
      if (isUnmounted.value) {
        console.warn('[🧠MEMORY-MANAGER] 组件已卸载，忽略事件处理');
        return;
      }
      
      try {
        handler(e);
      } catch (error: any) {
        console.error('[🧠MEMORY-MANAGER] 事件处理失败:', error);
      }
    };
    
    target.addEventListener(event, safeHandler as EventListener, options);
    
    const record: EventListenerRecord = {
      target,
      event,
      handler: safeHandler,
      options,
      createdAt: Date.now()
    };
    
    eventListeners.value.add(record);
    updateEventListenerStats();
    
    console.log(`[🧠MEMORY-MANAGER] 添加事件监听器: ${event}`);
  };

  /**
   * 移除所有事件监听器
   */
  const removeAllEventListeners = (): number => {
    let removedCount = 0;
    
    for (const record of eventListeners.value) {
      try {
        record.target.removeEventListener(
          record.event,
          record.handler as EventListener,
          record.options
        );
        removedCount++;
      } catch (error: any) {
        console.error('[🧠MEMORY-MANAGER] 移除事件监听器失败:', error);
      }
    }
    
    eventListeners.value.clear();
    updateEventListenerStats();
    
    console.log(`[🧠MEMORY-MANAGER] 移除所有事件监听器: ${removedCount} 个`);
    
    return removedCount;
  };

  /**
   * 完整的资源清理
   */
  const cleanup = (): void => {
    // console.log('[🧠MEMORY-MANAGER] 开始完整资源清理');
    
    isUnmounted.value = true;
    
    const timersCleared = clearAllTimers();
    const cacheCleared = clearAllCache();
    const tasksCleared = cancelAllAsyncTasks();
    const listenersCleared = removeAllEventListeners();
    
    console.log(`[🧠MEMORY-MANAGER] 资源清理完成: 定时器${timersCleared}个, 缓存${cacheCleared}个, 异步任务${tasksCleared}个, 事件监听器${listenersCleared}个`);
  };

  /**
   * 获取内存统计信息
   */
  const getMemoryStats = (): MemoryStats => {
    return { ...stats.value };
  };

  /**
   * 更新定时器统计
   */
  const updateTimerStats = (): void => {
    const activeTimers = Array.from(timers.value.values()).filter(t => t.isActive);
    const oldestTimer = activeTimers.reduce((oldest, current) => 
      current.createdAt < oldest.createdAt ? current : oldest, 
      activeTimers[0]
    );
    
    stats.value.timers = {
      active: activeTimers.length,
      total: timers.value.size,
      oldestAge: oldestTimer ? Date.now() - oldestTimer.createdAt : 0
    };
  };

  /**
   * 更新缓存统计
   */
  const updateCacheStats = (): void => {
    const totalSize = Array.from(cache.value.values()).reduce((sum, record) => sum + record.size, 0);
    const totalAccesses = cacheHits.value + cacheMisses.value;
    
    stats.value.cache = {
      entries: cache.value.size,
      totalSize,
      hitRate: totalAccesses > 0 ? (cacheHits.value / totalAccesses) * 100 : 0
    };
  };

  /**
   * 更新异步任务统计
   */
  const updateAsyncTaskStats = (): void => {
    const tasks = Array.from(asyncTasks.value.values());
    
    stats.value.asyncTasks = {
      active: tasks.filter(t => !t.isCompleted && !t.isCancelled).length,
      completed: tasks.filter(t => t.isCompleted).length,
      cancelled: tasks.filter(t => t.isCancelled).length
    };
  };

  /**
   * 更新事件监听器统计
   */
  const updateEventListenerStats = (): void => {
    stats.value.eventListeners = {
      active: eventListeners.value.size,
      total: eventListeners.value.size
    };
  };

  /**
   * 计算对象大小（字节）
   */
  const calculateObjectSize = (obj: any): number => {
    try {
      return JSON.stringify(obj).length * 2; // 近似值，UTF-16编码
    } catch {
      return 0;
    }
  };

  /**
   * 定期清理过期资源
   */
  const startPeriodicCleanup = (): void => {
    createTimer(() => {
      cleanupExpiredCache();
      
      // 清理已完成的异步任务记录
      const completedTasks = Array.from(asyncTasks.value.entries())
        .filter(([_, record]) => record.isCompleted || record.isCancelled)
        .filter(([_, record]) => Date.now() - record.createdAt > 5 * 60 * 1000); // 5分钟
      
      for (const [taskId] of completedTasks) {
        asyncTasks.value.delete(taskId);
      }
      
      if (completedTasks.length > 0) {
        updateAsyncTaskStats();
        console.log(`[🧠MEMORY-MANAGER] 清理已完成的异步任务记录: ${completedTasks.length} 个`);
      }
    }, 60 * 1000, 'interval', 'periodic_cleanup'); // 每分钟清理一次
  };

  // 生命周期管理
  onMounted(() => {
    startPeriodicCleanup();
    // console.log('[🧠MEMORY-MANAGER] 内存管理器已启动');
  });

  onUnmounted(() => {
    cleanup();
    // console.log('[🧠MEMORY-MANAGER] 内存管理器已关闭');
  });

  return {
    // 定时器管理
    createTimer,
    clearTimer,
    clearAllTimers,
    
    // 缓存管理
    setCache,
    getCache,
    deleteCache,
    cleanupExpiredCache,
    clearAllCache,
    
    // 异步任务管理
    createAsyncTask,
    cancelAsyncTask,
    cancelAllAsyncTasks,
    
    // 事件监听器管理
    addEventListener,
    removeAllEventListeners,
    
    // 资源管理
    cleanup,
    getMemoryStats,
    
    // 状态
    isUnmounted: readonly(isUnmounted),
    stats: readonly(stats)
  };
}; 