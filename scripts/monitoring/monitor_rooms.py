#!/usr/bin/env python
"""
房间状态监控脚本
用于定期检查是否有卡住的房间
"""
import os
import sys
import django
from datetime import timedelta

# 添加项目路径
sys.path.append('/www/wwwroot/csgoskins.com.cn/server')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')
django.setup()

from box.models import CaseRoom, CaseRoomRound, CaseRoomBet
from steambase.enums import GameState
from django.utils import timezone
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def monitor_rooms():
    """监控房间状态"""
    
    logger.info("开始监控房间状态...")
    
    # 检查各种状态的房间数量
    states = {
        'Initial': GameState.Initial.value,
        'Joinable': GameState.Joinable.value,
        'Joining': GameState.Joining.value,
        'Full': GameState.Full.value,
        'Running': GameState.Running.value,
        'End': GameState.End.value,
        'Cancelled': GameState.Cancelled.value
    }
    
    logger.info("=== 当前房间状态分布 ===")
    total_active = 0
    for state_name, state_value in states.items():
        count = CaseRoom.objects.filter(state=state_value).count()
        logger.info(f"{state_name}: {count}")
        if state_name in ['Initial', 'Joinable', 'Joining', 'Full', 'Running']:
            total_active += count
    
    logger.info(f"活跃房间总数: {total_active}")
    
    # 检查是否有长时间卡住的房间
    threshold_time = timezone.now() - timedelta(hours=2)
    
    # 检查长时间满员的房间
    stuck_full_rooms = CaseRoom.objects.filter(
        state=GameState.Full.value,
        update_time__lt=threshold_time
    )
    if stuck_full_rooms.exists():
        logger.warning(f"⚠️ 发现 {stuck_full_rooms.count()} 个长时间卡在满员状态的房间")
        for room in stuck_full_rooms:
            logger.warning(f"  房间: {room.short_id}, 创建时间: {room.create_time}")
    
    # 检查长时间进行中的房间
    stuck_running_rooms = CaseRoom.objects.filter(
        state=GameState.Running.value,
        update_time__lt=threshold_time
    )
    if stuck_running_rooms.exists():
        logger.warning(f"⚠️ 发现 {stuck_running_rooms.count()} 个长时间卡在进行中状态的房间")
        for room in stuck_running_rooms:
            logger.warning(f"  房间: {room.short_id}, 创建时间: {room.create_time}")
            
            # 检查回合情况
            total_rounds = CaseRoomRound.objects.filter(room=room).count()
            opened_rounds = CaseRoomRound.objects.filter(room=room, opened=True).count()
            participants = CaseRoomBet.objects.filter(room=room).count()
            logger.warning(f"    参与者: {participants}/{room.max_joiner}, 回合: {opened_rounds}/{total_rounds}")
    
    # 检查最近1小时的房间活动
    recent_time = timezone.now() - timedelta(hours=1)
    recent_rooms = CaseRoom.objects.filter(create_time__gte=recent_time)
    ended_recently = recent_rooms.filter(state=GameState.End.value).count()
    
    logger.info(f"最近1小时: 创建 {recent_rooms.count()} 个房间, 结束 {ended_recently} 个房间")
    
    # 健康状态总结
    if stuck_full_rooms.exists() or stuck_running_rooms.exists():
        logger.error("❌ 房间系统状态异常，发现卡住的房间")
        return False
    else:
        logger.info("✅ 房间系统状态正常")
        return True

def check_room_health():
    """检查房间系统健康状态"""
    
    # 检查是否有参与者不足的房间
    problematic_rooms = []
    
    for room in CaseRoom.objects.filter(state__in=[GameState.Full.value, GameState.Running.value]):
        participants = CaseRoomBet.objects.filter(room=room).count()
        if participants < room.max_joiner:
            problematic_rooms.append({
                'room': room,
                'participants': participants,
                'expected': room.max_joiner
            })
    
    if problematic_rooms:
        logger.warning(f"发现 {len(problematic_rooms)} 个参与者不足的房间:")
        for item in problematic_rooms:
            room = item['room']
            logger.warning(f"  {room.short_id}: {item['participants']}/{item['expected']} 参与者")
    
    return len(problematic_rooms) == 0

if __name__ == "__main__":
    try:
        room_status_ok = monitor_rooms()
        health_ok = check_room_health()
        
        if room_status_ok and health_ok:
            logger.info("🎉 所有检查通过，房间系统运行正常")
        else:
            logger.error("💥 发现问题，需要人工干预")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"监控脚本执行出错: {e}")
        sys.exit(1)
