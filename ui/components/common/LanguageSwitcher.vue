<!-- components/common/LanguageSwitcher.vue -->
<template>
  <div class="relative">
    <!-- 下拉选择器版本 -->
    <div v-if="variant === 'select'" class="relative">
      <select
        :value="currentLocale"
        @change="handleLanguageChange"
        class="bg-gray-800 text-white border border-gray-600 px-3 py-2 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent appearance-none pr-8 min-w-24"
      >
        <option v-for="item in availableLocales" :key="item.code" :value="item.code">
          {{ getLocaleName(item) }}
        </option>
      </select>
      <Icon name="heroicons:chevron-down" class="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
    </div>

    <!-- 按钮下拉菜单版本 - 改为hover触发 -->
    <div v-else class="language-dropdown group">
      <button
        class="language-trigger"
        @click="handleButtonClick"
      >
        <Icon :name="getCurrentFlag()" class="w-4 h-4" />
        <span>{{ getCurrentLocaleName() }}</span>
        <Icon 
          name="heroicons:chevron-down" 
          class="w-4 h-4 transition-transform duration-300 group-hover:rotate-180"
        />
      </button>

      <!-- 下拉菜单 - 改为hover触发 -->
      <div class="language-menu">
        <button
          v-for="locale in availableLocales"
          :key="locale.code"
          @click="handleLanguageSwitch(locale.code)"
          class="language-item"
          :class="{ 'language-item-active': locale.code === currentLocale }"
        >
          <Icon :name="getLocaleFlag(locale)" class="w-4 h-4" />
          <span>{{ getLocaleName(locale) }}</span>
          <Icon 
            v-if="locale.code === currentLocale"
            name="heroicons:check"
            class="w-4 h-4 ml-auto text-primary"
          />
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface Props {
  variant?: 'select' | 'dropdown'
  position?: 'left' | 'right'
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'dropdown',
  position: 'left'
})

// 使用语言管理可组合函数
const { 
  currentLocale, 
  availableLocales, 
  switchLanguage 
} = useLanguage()

// 模板引用
const dropdownRef = ref<HTMLElement>()
const triggerRef = ref<HTMLElement>()
const menuRef = ref<HTMLElement>()

// 菜单样式计算
const menuStyle = computed(() => {
  return {
    position: 'fixed',
    zIndex: 99999
  }
})

// 语言到国旗图标的映射
const flagMap: Record<string, string> = {
  'zh': 'twemoji:flag-china',
  'zh-CN': 'twemoji:flag-china',
  'zh-hans': 'twemoji:flag-china',
  'zh-TW': 'twemoji:flag-taiwan',
  'en': 'twemoji:flag-united-states',
  'en-US': 'twemoji:flag-united-states',
  'ja': 'twemoji:flag-japan',
  'ko': 'twemoji:flag-south-korea',
  'ru': 'twemoji:flag-russia',
  'de': 'twemoji:flag-germany',
  'fr': 'twemoji:flag-france',
  'es': 'twemoji:flag-spain',
  'pt': 'twemoji:flag-portugal',
  'it': 'twemoji:flag-italy'
}

// 语言名称映射
const nameMap: Record<string, string> = {
  'zh': '中文',
  'zh-CN': '简体中文',
  'zh-hans': '简体中文',
  'zh-TW': '繁體中文',
  'en': 'English',
  'en-US': 'English (US)',
  'ja': '日本語',
  'ko': '한국어',
  'ru': 'Русский',
  'de': 'Deutsch',
  'fr': 'Français',
  'es': 'Español',
  'pt': 'Português',
  'it': 'Italiano'
}

// 获取语言名称
const getLocaleName = (locale: any) => {
  return nameMap[locale.code] || locale.name || locale.code
}

// 获取当前语言名称
const getCurrentLocaleName = () => {
  return nameMap[currentLocale.value] || currentLocale.value
}

// 获取语言对应的国旗图标
const getLocaleFlag = (locale: any) => {
  return flagMap[locale.code] || 'twemoji:flag-white'
}

// 获取当前语言的国旗图标
const getCurrentFlag = () => {
  return flagMap[currentLocale.value] || 'twemoji:flag-white'
}

// 处理按钮点击（仅用于移动端fallback）
const handleButtonClick = () => {
  // 在移动设备上，点击按钮也能触发下拉菜单
  // 这是对hover的补充，确保移动端可用性
}

// 处理语言切换（select版本）
const handleLanguageChange = async (event: Event) => {
  const target = event.target as HTMLSelectElement
  const newLocale = target.value
  await handleLanguageSwitch(newLocale)
}

// 切换语言的统一处理函数
const handleLanguageSwitch = async (newLocale: string) => {
  if (newLocale === currentLocale.value) {
    return
  }
  
  try {
    // 使用useLanguage可组合函数的switchLanguage方法
    await switchLanguage(newLocale)
    
    // console.log('语言切换成功：', newLocale)
  } catch (error) {
    console.error('语言切换失败：', error)
  }
}

// 监听语言变化
watch(currentLocale, (newLocale, oldLocale) => {
  if (oldLocale && newLocale !== oldLocale) {
    console.log('LanguageSwitcher: 语言已更改', oldLocale, '=>', newLocale)
  }
}, { immediate: true })
</script>

<style scoped>
/* 选择器样式优化 */
select {
  background-image: none;
}

/* 语言下拉菜单 - 与help菜单保持一致的样式 */
.language-dropdown {
  position: relative;
}

.language-trigger {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  color: rgba(255, 255, 255, 0.8);
  background: transparent;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.875rem;
}

.language-trigger:hover {
  color: var(--color-primary, #1AC6FF);
  background: rgba(var(--color-primary-rgb, 26, 198, 255), 0.1);
}

.language-menu {
  position: absolute;
  top: calc(100% + 0.5rem);
  right: 0;
  min-width: 10rem;
  background: rgba(17, 24, 39, 0.95);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border: 1px solid rgba(107, 114, 128, 0.3);
  border-radius: 0.75rem;
  padding: 0.5rem;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
  z-index: 999999;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
}

.language-dropdown:hover .language-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.language-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
  background: transparent;
  border: none;
  text-align: left;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.15s ease;
  font-size: 0.875rem;
}

.language-item:hover {
  color: var(--color-primary, #1AC6FF);
  background: rgba(var(--color-primary-rgb, 26, 198, 255), 0.1);
  transform: translateX(4px);
}

.language-item-active {
  color: var(--color-primary, #1AC6FF) !important;
  background: rgba(var(--color-primary-rgb, 26, 198, 255), 0.15) !important;
}

.language-item-active:hover {
  background: rgba(var(--color-primary-rgb, 26, 198, 255), 0.2) !important;
}

/* 焦点样式 */
button:focus,
select:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(var(--color-primary-rgb, 26, 198, 255), 0.5);
}

/* 移动端优化 */
@media (max-width: 768px) {
  .language-menu {
    left: 0;
    right: auto;
    min-width: 8rem;
  }
  
  .language-item {
    padding: 0.625rem;
  }
  
  .language-trigger {
    padding: 0.375rem 0.625rem;
  }
}

/* 无障碍支持 - 减少动画 */
@media (prefers-reduced-motion: reduce) {
  .language-trigger,
  .language-menu,
  .language-item {
    transition: none;
  }
  
  .group:hover .transition-transform {
    transform: none;
  }
}
</style>