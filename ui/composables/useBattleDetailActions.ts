/**
 * 战斗详情页事件处理
 * 管理各种用户交互事件和业务逻辑处理
 */
import { ref } from 'vue'
import type { Ref } from 'vue'

export interface BattleDetailActions {
  isEventListenersSetup: Ref<boolean>
  setupAnimationEventListeners: () => void
  handleInitializationComplete: () => void
  handleInitializationCompleteWithCheck: () => void
  checkAnimationListeners: () => void
  handleFallbackInitialization: () => void
  handleJoinBattle: () => Promise<void>
  handleLeaveBattle: (userId?: string) => Promise<void>
  handleDismissBattle: () => Promise<void>
  handleCopyId: () => Promise<void>
  handleShare: () => Promise<void>
  handleStartBattle: () => Promise<void>
  handleCloseToast: () => void
}

export function useBattleDetailActions(): BattleDetailActions {
  const battleCore = useBattleCore()
  const roundManager = useBattleRoundManager()
  const memoryManager = useBattleMemoryManager()

  const isEventListenersSetup = ref(false)

  // 🎯 设置动画事件监听器（防重复）
  const setupAnimationEventListeners = () => {
    if (isEventListenersSetup.value) {
      console.log("[🎰BATTLE-PAGE] 动画事件监听器已设置，跳过重复设置")
      return
    }

    console.log("[🎰BATTLE-PAGE] 设置动画事件监听器")

    // 监听开箱完成事件
    const handleCaseOpeningComplete = (event: any) => {
      console.log("[🎰BATTLE-PAGE] 收到开箱完成事件:", event.detail)
    }

    // 监听轮次开始事件
    const handleRoundStart = (event: any) => {
      console.log("[🎰BATTLE-PAGE] 收到轮次开始事件:", event.detail)
    }

    // 监听轮次结束事件
    const handleRoundEnd = (event: any) => {
      console.log("[🎰BATTLE-PAGE] 收到轮次结束事件:", event.detail)
    }

    // 添加事件监听器
    window.addEventListener('battle:case_opening_complete', handleCaseOpeningComplete)
    window.addEventListener('battle:round_start', handleRoundStart)
    window.addEventListener('battle:round_end', handleRoundEnd)

    // 清理函数
    const cleanup = () => {
      window.removeEventListener('battle:case_opening_complete', handleCaseOpeningComplete)
      window.removeEventListener('battle:round_start', handleRoundStart)
      window.removeEventListener('battle:round_end', handleRoundEnd)
    }

    // 注册清理函数到内存管理器（简化处理）
    memoryManager.createTimer(() => {
      // 页面卸载时会自动清理
    }, 1000, 'timeout', 'animation_event_listeners_cleanup')

    isEventListenersSetup.value = true
  }

  // 🎯 初始化完成处理
  const handleInitializationComplete = () => {
    console.log("[🎯调试] handleInitializationComplete 开始执行")
    
    const currentBattleData = battleCore.battleState.battleData.value
    if (!currentBattleData) {
      console.warn("[🎯调试] battleData 为空，无法执行初始化")
      return
    }

    console.log("[🎯调试] 当前战斗数据:", {
      id: currentBattleData.id,
      state: currentBattleData.state,
      round_count: currentBattleData.round_count,
      round_count_total: currentBattleData.round_count_total,
      round_count_current: currentBattleData.round_count_current
    })

    // 🎯 执行轮次管理器同步
    console.log("[🎯调试] 执行轮次管理器同步")
    const syncResult = roundManager.syncFromApiData(currentBattleData)
    console.log("[🎯调试] 轮次同步结果:", syncResult)
    
    // 🎯 如果同步失败，手动设置
    if (roundManager.totalRounds.value <= 1 && (currentBattleData.round_count > 1 || currentBattleData.round_count_total > 1)) {
      console.warn("[🎯修复] 轮次管理器同步失败，手动设置")
      const totalRounds = currentBattleData.round_count || currentBattleData.round_count_total || 1
      const currentRound = currentBattleData.round_count_current || 1
      roundManager.setTotalRounds(totalRounds)
      roundManager.setCurrentRound(currentRound)
    }
    
    // 🎯 同步完成后，确保 battleCore 使用管理器的数据
    battleCore.battleState.currentRound.value = roundManager.currentRound.value
    battleCore.battleState.totalRounds.value = roundManager.totalRounds.value
    
    console.log("[🎯调试] 最终轮次状态:", {
      管理器当前轮次: roundManager.currentRound.value,
      管理器总轮次: roundManager.totalRounds.value,
      battleCore当前轮次: battleCore.battleState.currentRound.value,
      battleCore总轮次: battleCore.battleState.totalRounds.value
    })
  }

  // 🎯 添加动画监听器检查
  const checkAnimationListeners = () => {
    console.log("[🎯调试] 检查动画相关监听器:")
    
    // 检查页面中是否有useBattleAnimation相关的组件
    const animationElements = document.querySelectorAll('[data-animation], .battle-animation, .case-opening-animation')
    console.log("[🎯调试] 动画元素数量:", animationElements.length)
    
    // 检查是否有BattlePlayerDisplay组件
    const playerDisplays = document.querySelectorAll('.battle-player-display, [class*="player"]')
    console.log("[🎯调试] 玩家显示组件数量:", playerDisplays.length)
    
    // 手动派发测试事件
    console.log("[🎯调试] 派发测试动画事件")
    window.dispatchEvent(new CustomEvent('battle:test_event', { 
      detail: { test: true, timestamp: Date.now() }
    }))
  }

  // 🎯 修改初始化完成处理，添加延迟检查
  const handleInitializationCompleteWithCheck = () => {
    handleInitializationComplete()
    
    // 延迟执行检查，确保所有组件都已挂载
    setTimeout(() => {
      checkAnimationListeners()
    }, 500)
  }

  // 🎯 备选初始化处理
  const handleFallbackInitialization = () => {
    console.log("[🎰BATTLE-PAGE] 执行备选初始化")
    // 这里可以添加备选的初始化逻辑
  }

  // 🎯 用户交互事件处理
  const handleJoinBattle = async () => {
    try {
      console.log("[🎰BATTLE-PAGE] 用户点击加入对战")
      await battleCore.battleActions.handleJoinBattle()
    } catch (error) {
      console.error("[🎰BATTLE-PAGE] 加入对战失败:", error)
    }
  }

  const handleLeaveBattle = async (userId?: string) => {
    try {
      console.log("[🎰BATTLE-PAGE] 用户点击离开对战:", userId)
      await battleCore.battleActions.handleLeaveBattle()
    } catch (error) {
      console.error("[🎰BATTLE-PAGE] 离开对战失败:", error)
    }
  }

  const handleDismissBattle = async () => {
    try {
      console.log("[🎰BATTLE-PAGE] 房主点击解散对战")
      await battleCore.battleActions.handleDismissBattle()
    } catch (error) {
      console.error("[🎰BATTLE-PAGE] 解散对战失败:", error)
    }
  }

  const handleCopyId = async () => {
    try {
      await battleCore.battleActions.handleCopyId()
    } catch (error) {
      console.error("[🎰BATTLE-PAGE] 复制ID失败:", error)
    }
  }

  const handleShare = async () => {
    try {
      await battleCore.battleActions.handleShare()
    } catch (error) {
      console.error("[🎰BATTLE-PAGE] 分享失败:", error)
    }
  }

  const handleStartBattle = async () => {
    try {
      console.log("[🎰BATTLE-PAGE] 开始对战")
      await battleCore.battleActions.handleStartBattle()
    } catch (error) {
      console.error("[🎰BATTLE-PAGE] 开始对战失败:", error)
    }
  }

  const handleCloseToast = () => {
    try {
      battleCore.battleActions.hideToast()
    } catch (error) {
      console.error("[🎰BATTLE-PAGE] 关闭Toast失败:", error)
    }
  }

  return {
    isEventListenersSetup,
    setupAnimationEventListeners,
    handleInitializationComplete,
    handleInitializationCompleteWithCheck,
    checkAnimationListeners,
    handleFallbackInitialization,
    handleJoinBattle,
    handleLeaveBattle,
    handleDismissBattle,
    handleCopyId,
    handleShare,
    handleStartBattle,
    handleCloseToast,
  }
}
