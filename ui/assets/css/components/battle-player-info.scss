// 玩家头部信息
.player-header {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1rem;

  &.player-header-center {
    align-items: center;
  }
}

// 头像容器
.player-avatar-container {
  position: relative;
  flex-shrink: 0;
}

.player-avatar {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;

  &:hover {
    border-color: rgba(255, 255, 255, 0.4);
    transform: scale(1.05);
  }
}

// 空位头像
.empty-avatar {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.05);
  border: 2px dashed rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  &:hover {
    border-color: rgba(255, 255, 255, 0.4);
    background: rgba(255, 255, 255, 0.1);
  }
}

// 玩家徽章
.player-badges {
  position: absolute;
  top: -0.25rem;
  right: -0.25rem;
  display: flex;
  gap: 0.25rem;
}

.host-badge,
.winner-badge,
.opening-badge {
  width: 1.25rem;
  height: 1.25rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgba(0, 0, 0, 0.8);
  font-size: 0.625rem;
}

.host-badge {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #000;
}

.winner-badge {
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  color: white;
}

.opening-badge {
  background: linear-gradient(135deg, #00a8ff, #0097e6);
  color: white;
  animation: pulse 2s ease-in-out infinite;
}

// 玩家信息中心
.player-info-center {
  flex: 1;
  min-width: 0;
}

.player-name {
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-white, #ffffff);
  margin-bottom: 0.5rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.empty-player-name {
  font-size: 0.875rem;
  color: var(--color-gray-400, #9ca3af);
  font-style: italic;
}

// 空位消息
.empty-slot-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

// 吸引动画装饰
.attraction-decoration {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
}

.pulse-ring {
  position: absolute;
  width: 4rem;
  height: 4rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  animation: pulse-ring 2s ease-out infinite;

  &.delay-1 {
    animation-delay: 0.5s;
  }

  &.delay-2 {
    animation-delay: 1s;
  }
}

// 动画定义
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

@keyframes pulse-ring {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0;
  }
} 