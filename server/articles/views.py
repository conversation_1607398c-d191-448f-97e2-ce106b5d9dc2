from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.db.models import Q, F
from rest_framework import generics, status, filters
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from django_filters.rest_framework import DjangoFilterBackend

from .models import ContentCategory, ContentTag, Content, ContentView
from .serializers import (
    ContentCategorySerializer, ContentTagSerializer, ContentListSerializer,
    ContentDetailSerializer, ContentCreateUpdateSerializer, AnnouncementSerializer
)
from .filters import ContentFilter


class ContentCategoryListView(generics.ListAPIView):
    """内容分类列表"""
    queryset = ContentCategory.objects.filter(is_active=True)
    serializer_class = ContentCategorySerializer
    permission_classes = [AllowAny]


class ContentTagListView(generics.ListAPIView):
    """内容标签列表"""
    queryset = ContentTag.objects.filter(is_active=True)
    serializer_class = ContentTagSerializer
    permission_classes = [AllowAny]


class ContentListView(generics.ListAPIView):
    """内容列表"""
    serializer_class = ContentListSerializer
    permission_classes = [AllowAny]
    filter_backends = [DjangoFilterBackend, filters.SearchBackend, filters.OrderingFilter]
    filterset_class = ContentFilter
    search_fields = ['title', 'subtitle', 'excerpt', 'content']
    ordering_fields = ['publish_date', 'view_count', 'created_at', 'sort_order']
    ordering = ['-is_pinned', '-priority', '-publish_date']
    
    def get_queryset(self):
        queryset = Content.objects.filter(
            status='published',
            publish_date__lte=timezone.now()
        ).filter(
            Q(expire_date__isnull=True) | Q(expire_date__gt=timezone.now())
        ).select_related('category', 'author').prefetch_related('tags')
        
        return queryset


class ContentDetailView(generics.RetrieveAPIView):
    """内容详情"""
    serializer_class = ContentDetailSerializer
    permission_classes = [AllowAny]
    lookup_field = 'slug'
    
    def get_queryset(self):
        return Content.objects.filter(
            status='published',
            publish_date__lte=timezone.now()
        ).filter(
            Q(expire_date__isnull=True) | Q(expire_date__gt=timezone.now())
        ).select_related('category', 'author', 'editor').prefetch_related('tags', 'attachments')
    
    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        
        # 记录浏览量
        self.record_view(instance, request)
        
        serializer = self.get_serializer(instance)
        return Response(serializer.data)
    
    def record_view(self, content, request):
        """记录浏览量"""
        ip_address = self.get_client_ip(request)
        user = request.user if request.user.is_authenticated else None
        
        # 检查是否已经浏览过（同一用户或IP在24小时内只计算一次）
        view_exists = ContentView.objects.filter(
            content=content,
            ip_address=ip_address,
            viewed_at__gte=timezone.now() - timezone.timedelta(hours=24)
        )
        
        if user:
            view_exists = view_exists.filter(user=user)
        
        if not view_exists.exists():
            ContentView.objects.create(
                content=content,
                user=user,
                ip_address=ip_address,
                user_agent=request.META.get('HTTP_USER_AGENT', '')
            )
            # 更新浏览计数
            Content.objects.filter(id=content.id).update(view_count=F('view_count') + 1)
    
    def get_client_ip(self, request):
        """获取客户端IP"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class AnnouncementListView(generics.ListAPIView):
    """公告列表"""
    serializer_class = AnnouncementSerializer
    permission_classes = [AllowAny]
    filter_backends = [filters.OrderingFilter]
    ordering = ['-is_pinned', '-priority', '-publish_date']
    
    def get_queryset(self):
        return Content.objects.filter(
            content_type='announcement',
            status='published',
            publish_date__lte=timezone.now()
        ).filter(
            Q(expire_date__isnull=True) | Q(expire_date__gt=timezone.now())
        ).select_related('category')


class FeaturedContentView(generics.ListAPIView):
    """推荐内容"""
    serializer_class = ContentListSerializer
    permission_classes = [AllowAny]
    
    def get_queryset(self):
        return Content.objects.filter(
            status='published',
            is_featured=True,
            publish_date__lte=timezone.now()
        ).filter(
            Q(expire_date__isnull=True) | Q(expire_date__gt=timezone.now())
        ).select_related('category', 'author').prefetch_related('tags')[:10]


@api_view(['GET'])
@permission_classes([AllowAny])
def content_by_category(request, category_slug):
    """根据分类获取内容"""
    category = get_object_or_404(ContentCategory, slug=category_slug, is_active=True)
    
    contents = Content.objects.filter(
        category=category,
        status='published',
        publish_date__lte=timezone.now()
    ).filter(
        Q(expire_date__isnull=True) | Q(expire_date__gt=timezone.now())
    ).select_related('category', 'author').prefetch_related('tags')
    
    # 分页
    page = request.GET.get('page', 1)
    page_size = min(int(request.GET.get('page_size', 20)), 100)
    
    from django.core.paginator import Paginator
    paginator = Paginator(contents, page_size)
    page_obj = paginator.get_page(page)
    
    serializer = ContentListSerializer(page_obj, many=True)
    
    return Response({
        'category': ContentCategorySerializer(category).data,
        'results': serializer.data,
        'count': paginator.count,
        'num_pages': paginator.num_pages,
        'current_page': page_obj.number,
        'has_next': page_obj.has_next(),
        'has_previous': page_obj.has_previous(),
    })


@api_view(['GET'])
@permission_classes([AllowAny])
def content_by_tag(request, tag_slug):
    """根据标签获取内容"""
    tag = get_object_or_404(ContentTag, slug=tag_slug, is_active=True)
    
    contents = Content.objects.filter(
        tags=tag,
        status='published',
        publish_date__lte=timezone.now()
    ).filter(
        Q(expire_date__isnull=True) | Q(expire_date__gt=timezone.now())
    ).select_related('category', 'author').prefetch_related('tags')
    
    # 分页
    page = request.GET.get('page', 1)
    page_size = min(int(request.GET.get('page_size', 20)), 100)
    
    from django.core.paginator import Paginator
    paginator = Paginator(contents, page_size)
    page_obj = paginator.get_page(page)
    
    serializer = ContentListSerializer(page_obj, many=True)
    
    return Response({
        'tag': ContentTagSerializer(tag).data,
        'results': serializer.data,
        'count': paginator.count,
        'num_pages': paginator.num_pages,
        'current_page': page_obj.number,
        'has_next': page_obj.has_next(),
        'has_previous': page_obj.has_previous(),
    })


@api_view(['GET'])
@permission_classes([AllowAny])
def homepage_announcements(request):
    """首页公告"""
    announcements = Content.objects.filter(
        content_type='announcement',
        status='published',
        is_featured=True,
        publish_date__lte=timezone.now()
    ).filter(
        Q(expire_date__isnull=True) | Q(expire_date__gt=timezone.now())
    ).order_by('-is_pinned', '-priority', '-publish_date')[:5]
    
    serializer = AnnouncementSerializer(announcements, many=True)
    return Response(serializer.data)
