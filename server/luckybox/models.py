from django.contrib.auth import get_user_model
from django.db import models
from django.utils.translation import gettext_lazy as _

from package.models import ItemInfo
from steambase.models import ModelBase, ItemBase, USER_MODELL


UserModel = get_user_model()


class LuckyBoxGroup(models.Model):
    market_name = models.CharField(_('market name'), max_length=128, null=True, blank=True)
    market_hash_name = models.CharField(_('market hash name'), max_length=128, null=True, blank=True)
    type = models.CharField(_("type"), max_length=128, null=True, blank=True)
    weapon = models.CharField(_("weapon"), max_length=128, null=True, default=None, blank=True)
    price = models.FloatField(_('price'), default=0)
    icon_url = models.CharField(_("icon url"), max_length=256, default=None)
    rarity_color = models.CharField(_("rarity"), max_length=128, default=None, null=True, blank=True)

    class Meta:
        verbose_name = _('LuckyBox Group')
        verbose_name_plural = _('LuckyBox Group')

    def __str__(self):
        return self.market_name

    def set_price(self, price):
        if self.price == 0:
            self.price = price
            self.save()
            return
        else:
            if price < self.price:
                self.price = price
            self.save()

    def set_item_hash_name(self, hashName):
        # if self.market_hash_name == None:
        self.market_hash_name = hashName
        self.save()
        return


class LuckyRecommendGroup(models.Model):
    group = models.ForeignKey(LuckyBoxGroup, on_delete=models.SET_NULL, default=None,
                              null=True)

    class Meta:
        verbose_name = _('LuckyBox Recommend Group')
        verbose_name_plural = _('LuckyBox Recommend Group')

    def __str__(self):
        return self.group.market_name


class LuckyBoxItem(models.Model):
    item_info = models.OneToOneField(ItemInfo, on_delete=models.CASCADE)
    # market_name = models.CharField(_('market name'), max_length=128, null=True, blank=True)
    group = models.ForeignKey(LuckyBoxGroup, on_delete=models.SET_NULL, default=None,
                              null=True)
    price = models.FloatField(_('price'), default=0)

    class Meta:
        verbose_name = _('LuckyBox Item')
        verbose_name_plural = _('LuckyBox Item')

    def __str__(self):
        return str(self.item_info)


class LuckyBoxCategory(models.Model):
    category = models.CharField(_('category'), max_length=128, null=True, blank=True)
    image = models.ImageField(_("image"), max_length=256, default=None, null=True, blank=True,
                              upload_to='luckybox/category')
    ico = models.CharField(_('icon'), max_length=128, null=True, blank=True)
    name = models.CharField(_('name'), max_length=128, null=True, blank=True)

    class Meta:
        verbose_name = _('LuckyBoxCategory')
        verbose_name_plural = _('LuckyBoxCategory')

    def __str__(self):
        return self.category


class LuckyBoxRecord(ModelBase):
    user = models.ForeignKey(UserModel, on_delete=models.CASCADE, related_name="lucky_record", verbose_name=_("luckybox item"),
                             on_delete=models.CASCADE)
    origin_percent = models.FloatField(_("lucky origin percent"), default=0)
    percent = models.FloatField(_("lucky percent"), default=0)
    coins = models.FloatField(_("lucky coins"), default=0)
    percentage = models.FloatField(_("lucky percentage"), default=0)
    win = models.BooleanField(_('lucky result'), default=False)
    target = models.ForeignKey(ItemInfo, on_delete=models.SET_NULL, related_name="lucky_target", verbose_name=_("luckybox target item"),
                               on_delete=models.CASCADE, default=None, null=True)

    class Meta:
        verbose_name = _('LuckyBox Record')
        verbose_name_plural = _('LuckyBox Record')

    def __str__(self):
        return str(self.item)