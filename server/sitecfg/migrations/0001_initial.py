# -*- coding: utf-8 -*-
# Generated by Django 1.11.13 on 2021-04-14 02:09
from __future__ import unicode_literals

import ckeditor_uploader.fields
from django.db import migrations, models
import django.db.models.deletion
import steambase.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Announcee',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('type', models.SmallIntegerField(choices=[(1, 'Homepage'), (2, 'Normal')], verbose_name='type')),
                ('remark', models.CharField(max_length=128, verbose_name='remark')),
                ('content', ckeditor_uploader.fields.RichTextUploadingField(verbose_name='content')),
                ('enable', models.BooleanField(default=True, verbose_name='enable')),
            ],
            options={
                'verbose_name': 'Announcee',
                'verbose_name_plural': 'Announcee',
                'ordering': ('type',),
            },
        ),
        migrations.CreateModel(
            name='FAQ',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('title', models.CharField(max_length=255, verbose_name='title')),
                ('type', models.CharField(blank=True, default=None, max_length=128, null=True, verbose_name='type')),
                ('content', ckeditor_uploader.fields.RichTextUploadingField(verbose_name='content')),
                ('enable', models.BooleanField(default=True, verbose_name='enable')),
                ('no', models.IntegerField(default=0, verbose_name='order No.')),
            ],
            options={
                'verbose_name': 'FAQ',
                'verbose_name_plural': 'FAQ',
            },
        ),
        migrations.CreateModel(
            name='Footer',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('remark', models.CharField(max_length=128, verbose_name='remark')),
                ('type', models.SmallIntegerField(choices=[(1, 'Announcee'), (2, 'Link')], verbose_name='type')),
                ('title', models.CharField(max_length=255, verbose_name='title')),
                ('link', models.CharField(blank=True, default=None, max_length=255, null=True, verbose_name='link')),
                ('no', models.IntegerField(default=0, verbose_name='order No.')),
                ('enable', models.BooleanField(default=True, verbose_name='enable')),
                ('anno', models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='footer', to='sitecfg.Announcee', verbose_name='Announcee')),
            ],
            options={
                'verbose_name': 'Footer',
                'verbose_name_plural': 'Footer',
                'ordering': ('no',),
            },
        ),
        migrations.CreateModel(
            name='SiteConfig',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(max_length=64, unique=True)),
                ('value', models.CharField(blank=True, default=None, max_length=1280, verbose_name='value')),
                ('enable', models.BooleanField(default=True, verbose_name='enable')),
                ('remark', models.CharField(max_length=128, verbose_name='remark')),
            ],
            options={
                'verbose_name': 'Site Config',
                'verbose_name_plural': 'Site Config',
            },
        ),
        migrations.CreateModel(
            name='SiteConfigCategoryyy',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128, verbose_name='name')),
            ],
            options={
                'verbose_name': 'Site Config Category',
                'verbose_name_plural': 'Site Config Category',
            },
        ),
        migrations.CreateModel(
            name='Support',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('remark', models.CharField(max_length=128, verbose_name='remark')),
                ('title', models.CharField(max_length=255, verbose_name='title')),
                ('type', models.CharField(blank=True, default=None, max_length=128, null=True, verbose_name='type')),
                ('content', ckeditor_uploader.fields.RichTextUploadingField(verbose_name='content')),
                ('no', models.IntegerField(default=0, verbose_name='order No.')),
                ('enable', models.BooleanField(default=True, verbose_name='enable')),
                ('image', models.ImageField(blank=True, default=None, null=True, upload_to='support', verbose_name='image')),
            ],
            options={
                'verbose_name': 'Support',
                'verbose_name_plural': 'Support',
                'ordering': ('no',),
            },
        ),
        migrations.AddField(
            model_name='siteconfig',
            name='category',
            field=models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='site_config', to='sitecfg.SiteConfigCategoryyy', verbose_name='category'),
        ),
    ]
