export default {
  content: [
    './components/**/*.{vue,js,ts}',
    './layouts/**/*.vue',
    './pages/**/*.vue',
    './app.vue',
    './plugins/**/*.{js,ts}',
  ],
  theme: {
    extend: {
      colors: {
        // 主色系
        primary: {
          DEFAULT: '#00A8FF',
          dark: '#0076B3',
          light: '#33BBFF',
          50: '#E6F7FF',
          100: '#BAE7FF',
          200: '#91D5FF',
          300: '#69C0FF',
          400: '#40A9FF',
          500: '#00A8FF',
          600: '#0086CC',
          700: '#0066A3',
          800: '#004C7A',
          900: '#003252',
          950: '#001A29'
        },
        // 辅助色系
        secondary: {
          DEFAULT: '#FF4D00',
          dark: '#CC3D00',
          light: '#FF7033',
          50: '#FFF2E6',
          100: '#FFE0BA',
          200: '#FFCC91',
          300: '#FFB869',
          400: '#FFA440',
          500: '#FF4D00',
          600: '#CC3D00',
          700: '#A33100',
          800: '#7A2400',
          900: '#521800',
          950: '#290C00'
        },
        // 背景色
        background: '#0D0F12'
      }
    }
  },
  plugins: []
}