{"scripts": {"build:dev": "DISABLE_API_PROXY=true DISABLE_SOCKET=true USE_MOCK_DATA=true nuxt build", "build:staging": "DISABLE_API_PROXY=false DISABLE_SOCKET=false USE_MOCK_DATA=false nuxt build", "build:prod": "DISABLE_API_PROXY=false DISABLE_SOCKET=false USE_MOCK_DATA=false nuxt build", "dev:mock": "DISABLE_API_PROXY=true DISABLE_SOCKET=true USE_MOCK_DATA=true nuxt dev", "dev:real": "DISABLE_API_PROXY=false DISABLE_SOCKET=false USE_MOCK_DATA=false nuxt dev", "test:mock": "npm run dev:mock", "test:real": "npm run dev:real"}}