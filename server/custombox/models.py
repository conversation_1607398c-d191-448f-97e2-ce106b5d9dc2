from django.conf import settings
from django.db import models
from django.utils.translation import gettext_lazy as _

from package.models import ItemInfo
from steambase.models import ModelBase

USER_MODEL = settings.AUTH_USER_MODEL


class CustomBox(ModelBase):
    user = models.ForeignKey(USER_MODEL, on_delete=models.SET_NULL, verbose_name = "用户", default=None, null=True, blank=True,
                             related_name='custom_box')
    name = models.CharField(_('name'), max_length=128)
    key = models.CharField(_('key'), max_length=128, unique=True)
    price = models.FloatField(_('price'), default=0)
    cover = models.ImageField(_('cover image'), upload_to='cases', default=None, null=True, blank=True)
    item = models.ImageField(_('item image'), upload_to='cases', default=None, null=True, blank=True)

    class Meta:
        verbose_name = "自定义箱子"
        verbose_name_plural = _('CustomBox')

    def __str__(self):
        return str(self.name)


class CustomDropItem(ModelBase):
    item_info = models.ForeignKey(ItemInfo, on_delete=models.SET_NULL, verbose_name=_('item info'), related_name='custom_drops',
                                  default=None, null=True, blank=True)
    case = models.ForeignKey(CustomBox, on_delete=models.CASCADE, verbose_name=_('case'), related_name='custombox_drops')
    show_chance = models.FloatField(_('show chance'), default=0)
    drop_chance_a = models.FloatField(_('drop chance a'), default=0)
    drop_chance_b = models.FloatField(_('drop chance b'), default=0)
    drop_chance_c = models.FloatField(_('drop chance c'), default=0)
    drop_chance_d = models.FloatField(_('drop chance d'), default=0)
    drop_chance_e = models.FloatField(_('drop chance e'), default=0)

    class Meta:
        verbose_name = "自定义掉落物品"
        verbose_name_plural = _('CustomDropItem')

    def __str__(self):
        return str(self.item_info)


class CustomBoxCover(ModelBase):
    cover = models.ImageField(_('cover image'), upload_to='cases', default=None, null=True, blank=True)

    class Meta:
        verbose_name = "自定义箱子封面"
        verbose_name_plural = _('CustomBoxCover')

    def __str__(self):
        return str(self.uid)


class CustomBoxItem(ModelBase):
    item = models.ImageField(_('item image'), upload_to='cases', default=None, null=True, blank=True)

    class Meta:
        verbose_name = "自定义箱子物品"
        verbose_name_plural = _('CustomBoxItem')

    def __str__(self):
        return str(self.uid)


class CustomBoxItemInfo(ModelBase):
    item_info = models.OneToOneField(ItemInfo, on_delete=models.CASCADE)
    market_name = models.CharField(_('market name'), max_length=128, default=None, null=True, blank=True)

    class Meta:
        verbose_name = "自定义箱子物品信息"
        verbose_name_plural = _('CustomBoxItemInfo')

    def __str__(self):
        return str(self.market_name)


class CustomBoxRecord(ModelBase):
    user = models.ForeignKey(USER_MODEL, on_delete=models.SET_NULL, verbose_name = "用户", related_name='custombox_records',
                             default=None, null=True, blank=True)
    case = models.ForeignKey(CustomBox, on_delete=models.CASCADE, verbose_name=_('case'), related_name='custombox_records')
    item_info = models.ForeignKey(ItemInfo, on_delete=models.SET_NULL, verbose_name=_('item info'), related_name='custombox_records', default=None,
                                  null=True, blank=True)
    price = models.FloatField(_('price'), default=0)

    class Meta:
        verbose_name = "箱子记录"
        verbose_name_plural = _('Case Record')

    def __str__(self):
        return self.uid