import json
import logging

from django.conf import settings
from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.core.cache import cache

from box.serializers import CaseRoomDetailSerializer, CaseRoomRoundSerializer, CaseSerializer, DropItemSerializer, \
    CaseRoomCacheSerializer
from package.interfaces import get_item_price_by_name
from package.models import ItemPrice
from box.models import Drop<PERSON><PERSON>, Case<PERSON><PERSON>, CaseRoomBet, Case
from steambase.redis_con import get_redis
from steambase.utils import get_serializer_cache_key

from package.service.item import get_item_price_by_id

_logger = logging.getLogger(__name__)


def get_drop_price(drop):
    if drop.item_type == 1:
        return get_item_price_by_id(drop.item_info.id).price
    else:
        return drop.coins


@receiver(post_save, sender=DropItem, dispatch_uid="auto_set_custom_price")
def auto_set_custom_price(sender, instance, **kwargs):
    if instance.custom_enable == True:
        item = instance.item_info.item_price
        item.custom = True
        item.custom_price = instance.custom_price
        item.set_price()
    else:
        item = instance.item_info.item_price
        item.custom = False
        item.custom_price = instance.custom_price
        item.set_price()


@receiver(post_save, sender=Case, dispatch_uid="auto_set_case_cache")
def auto_set_case_cache(sender, instance, **kwargs):
    case_data = CaseSerializer(instance, read_only=True, fields=('case_key', 'name', 'cover', 'item', 'price', 'room_drops')).data
    cache.set('room_round_case:{}'.format(instance.case_key), case_data, None)


@receiver(post_save, sender=CaseRoom, dispatch_uid="cancel_case_room_cache")
def cancel_case_room_cache(sender, instance, **kwargs):
    # 清除当前状态的缓存
    params = {
        "app_label": instance._meta.app_label,
        "model_name": instance._meta.object_name,
        "serializer_name": CaseRoomCacheSerializer.__name__,
        "id": str(instance.pk),
        "state": str(instance.state),
        "update_time": str(int(instance.update_time.timestamp()))
    }
    current_key = ":".join(params.values())
    cache.delete(current_key)
    
    # 清除所有可能的状态缓存（因为我们不知道之前的状态是什么）
    from steambase.enums import GameState
    for state_value in [GameState.Initial.value, GameState.Joinable.value, GameState.Joining.value, 
                       GameState.Full.value, GameState.Running.value, GameState.End.value, GameState.Cancelled.value]:
        if state_value != instance.state:  # 当前状态已经清除了
            state_params = params.copy()
            state_params["state"] = str(state_value)
            state_key = ":".join(state_params.values())
            cache.delete(state_key)
    
    # 清除旧格式的缓存键（向后兼容）
    try:
        old_key = get_serializer_cache_key(instance, CaseRoomCacheSerializer.__name__)
        cache.delete(old_key)
    except:
        pass  # 忽略旧格式的错误

