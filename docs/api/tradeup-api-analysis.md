# Tradeup 换肤模块分析文档

## 模块概述

Tradeup模块是一个基于饰品价值的升级交换系统。用户可以用多个低价值的饰品通过算法匹配，有概率获得更高价值的饰品。该模块提供了类似CS:GO官方换肤合约的玩法，是平台重要的饰品消费和循环机制。

## 目录结构

```
tradeup/
├── __init__.py
├── admin.py                 # Django管理后台配置
├── apps.py                  # 应用配置
├── business.py              # 核心业务逻辑
├── models.py                # 数据模型定义
├── serializers.py           # API序列化器
├── views.py                 # API视图
├── urls.py                  # URL路由配置
├── interfaces.py            # 接口定义
├── model_signals.py         # 数据库信号处理
├── service/                 # 服务层
├── tests.py                 # 单元测试
└── migrations/              # 数据库迁移文件
```

## 数据模型分析

### TradeupGame - 换肤游戏记录
```python
class TradeupGame(ModelBase):
    user = models.ForeignKey(USER_MODEL)                    # 用户
    hash = models.CharField(max_length=255)                 # 游戏哈希
    secret = models.CharField(max_length=32)                # 随机种子
    percentage = models.FloatField(default=0)               # 随机百分比
    bet_percentage = models.FloatField(default=0)           # 投注百分比
    upper = models.BooleanField(default=False)              # 是否押高
    bet_amount = models.FloatField(default=0)               # 投注金额
    target_amount = models.FloatField(default=0)            # 目标金额
    win_amount = models.FloatField(default=0)               # 获胜金额
    win_result = models.SmallIntegerField()                 # 胜负结果
    appid = models.CharField(max_length=64)                 # 游戏应用ID
```

**特点：**
- 记录完整的换肤游戏过程
- 使用哈希和随机种子确保公平性
- 支持多种游戏应用（CS:GO、Dota2等）
- 包含投注方向（押高/押低）

### TradeupBetItem - 投注物品
```python
class TradeupBetItem(ModelBase):
    game = models.ForeignKey(TradeupGame)                   # 关联游戏
    package = models.ForeignKey(PackageItem)                # 包裹物品
    price = models.FloatField(default=0)                    # 物品价格
    type = models.SmallIntegerField()                       # 投注类型
```

**特点：**
- 记录用户投入的饰品
- 保存投注时的价格快照
- 支持物品和金额两种投注类型

### TradeupTargetItem - 目标物品
```python
class TradeupTargetItem(ModelBase):
    game = models.ForeignKey(TradeupGame)                   # 关联游戏
    item_info = models.ForeignKey(ItemInfo)                 # 物品信息
    price = models.FloatField(default=0)                    # 物品价格
```

**特点：**
- 记录可能获得的目标物品
- 保存目标物品的价格信息

### TradeupInventory - 换肤库存
```python
class TradeupInventory(ModelBase):
    item_info = models.OneToOneField(ItemInfo)              # 物品信息
    price = models.FloatField(default=0)                    # 价格
    count = models.IntegerField(default=0)                  # 数量
    unlimited = models.BooleanField(default=True)           # 无限库存
    enable = models.BooleanField(default=True)              # 启用状态
```

**特点：**
- 管理平台可用于换肤的物品库存
- 支持有限和无限库存模式
- 可动态启用/禁用物品

## API 接口分析

### 1. 获取换肤库存
**路径：** `GET /api/tradeup/inventory/`

**功能：** 获取可用于换肤的物品列表

**参数：**
- `page`: 页码
- `pageSize`: 每页数量
- `name`: 物品名称搜索
- `max_price`/`min_price`: 价格区间
- `order`: 排序方式
- `rarity`: 稀有度筛选

**响应格式：**
```json
{
    "items": [
        {
            "uid": "item_uuid",
            "market_name": "AK-47 | Redline",
            "market_hash_name": "AK-47 | Redline (Field-Tested)",
            "icon_url": "...",
            "price": 15.5,
            "rarity": "Rarity_Rare_Weapon",
            "rarity_color": "#4b69ff"
        }
    ],
    "total": 100
}
```

### 2. 匹配换肤库存
**路径：** `POST /api/tradeup/match/`

**功能：** 根据用户投入的物品匹配可能的输出物品

**参数：**
```json
{
    "bet_items": ["package_uid1", "package_uid2"],
    "bet_amount": 100.0,
    "times": 1
}
```

**业务逻辑：**
1. 验证用户拥有的投注物品
2. 计算投注总价值
3. 匹配可能的输出物品范围
4. 计算成功概率和预期收益

### 3. 执行换肤游戏
**路径：** `POST /api/tradeup/bet/`

**功能：** 执行实际的换肤操作

**参数：**
```json
{
    "bet_items": ["package_uid1", "package_uid2"],
    "bet_amount": 100.0,
    "target_items": ["target_item_id1", "target_item_id2"],
    "upper": true
}
```

**业务流程：**
1. 扣除用户投注物品
2. 生成随机数确定结果
3. 根据结果分配目标物品
4. 记录游戏历史

### 4. 获取投注记录
**路径：** `GET /api/tradeup/betrecord/`

**功能：** 获取用户的换肤游戏历史

**参数：**
- `page`: 页码
- `pageSize`: 每页数量

**响应格式：**
```json
{
    "records": [
        {
            "uid": "game_uuid",
            "hash": "game_hash",
            "percentage": 45.67,
            "bet_percentage": 50.0,
            "upper": true,
            "bet_amount": 100.0,
            "target_amount": 180.0,
            "win_result": 1,
            "bet_items": [...],
            "target_items": [...]
        }
    ],
    "total": 50
}
```

### 5. 获取换肤历史
**路径：** `GET /api/tradeup/history/`

**功能：** 获取全站换肤游戏历史（公开）

### 6. 获取成功率统计
**路径：** `GET /api/tradeup/percentage/`

**功能：** 获取用户的换肤成功率统计

### 7. 验证游戏结果
**路径：** `POST /api/tradeup/verify/`

**功能：** 验证游戏结果的公平性

**参数：**
```json
{
    "game_uid": "game_uuid"
}
```

**验证机制：**
- 使用游戏哈希和随机种子重现随机过程
- 确保结果的可验证性和公平性

### 8. 获取用户包裹
**路径：** `GET /api/tradeup/package/`

**功能：** 获取用户可用于换肤的包裹物品

## 核心业务逻辑分析

### 1. 物品匹配算法
```python
def match_tradeup_inventory(user, bet_items, bet_amount, times):
    """匹配换肤库存"""
    # 验证投注物品
    user_packages = validate_bet_items(user, bet_items)
    
    # 计算投注总价值
    total_bet_value = calculate_bet_value(user_packages)
    
    # 匹配目标物品范围
    target_range = calculate_target_range(total_bet_value)
    
    # 查找可用目标物品
    available_targets = TradeupInventory.objects.filter(
        price__range=target_range,
        enable=True
    ).filter(
        Q(unlimited=True) | Q(count__gt=0)
    )
    
    # 计算成功概率
    success_probability = calculate_success_probability(
        total_bet_value, 
        target_range
    )
    
    return {
        'target_items': serialize_target_items(available_targets),
        'success_rate': success_probability,
        'potential_profit': target_range[1] - total_bet_value
    }
```

### 2. 随机结果生成
```python
def generate_tradeup_result(game):
    """生成换肤结果"""
    # 基于游戏哈希生成随机种子
    random.seed(game.hash + game.secret)
    
    # 生成0-100的随机百分比
    percentage = random.uniform(0, 100)
    
    # 根据投注方向判断胜负
    if game.upper:
        win = percentage >= game.bet_percentage
    else:
        win = percentage <= game.bet_percentage
    
    # 更新游戏结果
    game.percentage = percentage
    game.win_result = WinResult.Win.value if win else WinResult.Lose.value
    game.save()
    
    return win, percentage
```

### 3. 概率计算系统
```python
class TradeupProbabilityCalculator:
    def __init__(self):
        self.house_edge = 0.05  # 5%平台抽成
        self.base_success_rate = 0.45  # 基础成功率45%
    
    def calculate_success_probability(self, bet_value, target_value):
        """计算成功概率"""
        # 基础概率计算
        value_ratio = bet_value / target_value
        
        # 概率调整
        if value_ratio >= 0.9:  # 接近等值交换
            probability = self.base_success_rate + 0.1
        elif value_ratio >= 0.7:  # 中等价值差
            probability = self.base_success_rate
        else:  # 大价值差
            probability = self.base_success_rate - 0.1
        
        # 应用平台抽成
        probability *= (1 - self.house_edge)
        
        return max(0.05, min(0.95, probability))  # 限制在5%-95%
    
    def calculate_expected_value(self, bet_items, target_items, success_rate):
        """计算期望价值"""
        bet_value = sum(item.price for item in bet_items)
        target_value = sum(item.price for item in target_items)
        
        expected_value = (success_rate * target_value) - bet_value
        return expected_value
```

### 4. 库存管理系统
```python
class TradeupInventoryManager:
    def __init__(self):
        self.redis_client = redis.Redis()
    
    def consume_inventory(self, target_item, quantity=1):
        """消耗库存"""
        inventory = TradeupInventory.objects.get(item_info=target_item)
        
        if inventory.unlimited:
            return True
        
        # 使用Redis分布式锁
        lock_key = f"tradeup_inventory_lock:{inventory.uid}"
        with self.redis_client.lock(lock_key, timeout=10):
            if inventory.count >= quantity:
                inventory.count -= quantity
                inventory.save()
                return True
            else:
                return False
    
    def replenish_inventory(self, item_info, quantity):
        """补充库存"""
        inventory, created = TradeupInventory.objects.get_or_create(
            item_info=item_info,
            defaults={'count': quantity, 'unlimited': False}
        )
        
        if not created and not inventory.unlimited:
            inventory.count += quantity
            inventory.save()
```

### 5. 公平性验证系统
```python
class TradeupFairnessValidator:
    def __init__(self):
        self.hash_algorithm = hashlib.sha256
    
    def generate_provably_fair_hash(self, user_id, timestamp):
        """生成可证明公平的哈希"""
        # 组合用户ID、时间戳和服务器密钥
        server_secret = settings.TRADEUP_SERVER_SECRET
        combined = f"{user_id}:{timestamp}:{server_secret}"
        
        return self.hash_algorithm(combined.encode()).hexdigest()
    
    def verify_game_result(self, game):
        """验证游戏结果"""
        # 重现随机过程
        random.seed(game.hash + game.secret)
        expected_percentage = random.uniform(0, 100)
        
        # 验证结果一致性
        if abs(expected_percentage - game.percentage) < 0.001:
            return True, "验证通过"
        else:
            return False, "验证失败：结果不一致"
    
    def generate_client_seed(self):
        """生成客户端种子"""
        return ''.join(random.choices(string.ascii_letters + string.digits, k=16))
```

## 存在的问题与风险

### 1. 概率机制不透明

**问题描述：**
- 成功率计算算法不明确
- 缺少概率公示机制
- 用户无法验证公平性
- 可能存在人工调节

**影响：**
- 用户信任度低
- 法律合规风险
- 可能被质疑作弊

### 2. 经济平衡问题

**问题描述：**
- 缺少经济模型分析
- 可能导致通胀或通缩
- 没有价值流向控制
- 缺少风险控制机制

**影响：**
- 平台经济失衡
- 用户流失
- 财务风险

### 3. 库存管理缺陷

**问题描述：**
- 库存更新可能不及时
- 缺少库存预警机制
- 没有动态定价
- 稀有物品管理不当

**影响：**
- 可能出现库存不足
- 用户体验差
- 运营效率低

### 4. 技术实现风险

**问题描述：**
- 随机数生成可能被预测
- 缺少并发控制
- 数据一致性风险
- 性能瓶颈

**影响：**
- 系统可靠性问题
- 可能被恶意利用
- 影响用户体验

## 改进建议

### 1. 透明化概率机制

#### 公开算法和参数
```python
class TransparentTradeupCalculator:
    def __init__(self):
        # 公开的算法参数
        self.algorithm_params = {
            'house_edge': 0.05,
            'base_success_rate': 0.45,
            'value_ratio_adjustments': {
                'high': {'threshold': 0.9, 'bonus': 0.1},
                'medium': {'threshold': 0.7, 'bonus': 0.0},
                'low': {'threshold': 0.0, 'bonus': -0.1}
            }
        }
    
    def get_public_algorithm_info(self):
        """获取公开算法信息"""
        return {
            'algorithm_version': '1.0',
            'last_updated': '2025-06-19',
            'parameters': self.algorithm_params,
            'formula': 'success_rate = base_rate + value_ratio_adjustment - house_edge'
        }
    
    def calculate_transparent_probability(self, bet_value, target_value):
        """透明的概率计算"""
        # 使用公开算法计算
        value_ratio = bet_value / target_value
        
        # 应用公开的调整规则
        adjustment = 0
        for level, config in self.algorithm_params['value_ratio_adjustments'].items():
            if value_ratio >= config['threshold']:
                adjustment = config['bonus']
                break
        
        # 计算最终概率
        probability = (
            self.algorithm_params['base_success_rate'] + 
            adjustment - 
            self.algorithm_params['house_edge']
        )
        
        return max(0.05, min(0.95, probability))
```

#### 可验证随机系统
```python
class ProvablyFairTradeup:
    def __init__(self):
        self.blockchain_integration = BlockchainRandomSource()
    
    def create_provably_fair_game(self, user, bet_items):
        """创建可证明公平的游戏"""
        # 用户提供客户端种子
        client_seed = self.generate_client_seed()
        
        # 服务器提供种子（提前承诺）
        server_seed = self.generate_server_seed()
        server_hash = hashlib.sha256(server_seed.encode()).hexdigest()
        
        # 创建游戏记录
        game = TradeupGame.objects.create(
            user=user,
            hash=server_hash,
            secret=client_seed,
            # ... 其他字段
        )
        
        # 向用户展示哈希值（在结果产生前）
        return {
            'game_uid': game.uid,
            'server_hash': server_hash,  # 用户可以记录
            'client_seed': client_seed,
            'blockchain_block': self.blockchain_integration.get_future_block()
        }
    
    def reveal_and_verify(self, game):
        """揭示并验证结果"""
        # 揭示服务器种子
        server_seed = self.get_server_seed(game.uid)
        
        # 验证哈希一致性
        if hashlib.sha256(server_seed.encode()).hexdigest() != game.hash:
            raise ValueError("服务器种子验证失败")
        
        # 使用区块链随机源
        blockchain_random = self.blockchain_integration.get_block_hash(game.target_block)
        
        # 组合随机源
        combined_seed = f"{server_seed}:{game.secret}:{blockchain_random}"
        
        # 生成最终随机数
        final_hash = hashlib.sha256(combined_seed.encode()).hexdigest()
        percentage = int(final_hash, 16) % 10000 / 100  # 0-99.99
        
        return percentage
```

### 2. 经济平衡优化

#### 动态经济模型
```python
class TradeupEconomicModel:
    def __init__(self):
        self.target_profit_margin = 0.05
        self.inflation_control_threshold = 0.1
        self.deflation_control_threshold = -0.05
    
    def analyze_economic_health(self):
        """分析经济健康度"""
        # 分析价值流向
        recent_games = TradeupGame.objects.filter(
            create_time__gte=timezone.now() - timedelta(days=7)
        )
        
        total_input = sum(game.bet_amount for game in recent_games)
        total_output = sum(game.win_amount for game in recent_games if game.win_result == WinResult.Win.value)
        
        net_flow = total_output - total_input
        flow_ratio = net_flow / total_input if total_input > 0 else 0
        
        # 评估健康度
        if flow_ratio > self.inflation_control_threshold:
            return 'inflation_risk', flow_ratio
        elif flow_ratio < self.deflation_control_threshold:
            return 'deflation_risk', flow_ratio
        else:
            return 'healthy', flow_ratio
    
    def adjust_success_rates(self, economic_status, flow_ratio):
        """根据经济状况调整成功率"""
        if economic_status == 'inflation_risk':
            # 降低成功率以减少产出
            adjustment = -min(0.1, abs(flow_ratio) * 0.5)
        elif economic_status == 'deflation_risk':
            # 提高成功率以增加产出
            adjustment = min(0.1, abs(flow_ratio) * 0.5)
        else:
            adjustment = 0
        
        # 应用调整
        self.update_global_success_rate_modifier(adjustment)
        
        return adjustment
```

### 3. 智能库存管理

#### 动态定价系统
```python
class DynamicPricingEngine:
    def __init__(self):
        self.demand_elasticity = 0.3
        self.supply_elasticity = 0.2
    
    def calculate_dynamic_price(self, item_info):
        """计算动态价格"""
        # 获取基础市场价格
        base_price = get_item_price(item_info.market_hash_name)
        
        # 分析需求
        demand_factor = self.analyze_demand(item_info)
        
        # 分析供给
        supply_factor = self.analyze_supply(item_info)
        
        # 计算调整后价格
        price_multiplier = (
            1 + 
            (demand_factor - 1) * self.demand_elasticity + 
            (supply_factor - 1) * self.supply_elasticity
        )
        
        return base_price * price_multiplier
    
    def analyze_demand(self, item_info):
        """分析需求情况"""
        # 最近7天的换肤次数
        recent_usage = TradeupTargetItem.objects.filter(
            item_info=item_info,
            game__create_time__gte=timezone.now() - timedelta(days=7)
        ).count()
        
        # 计算需求指数
        avg_usage = 10  # 假设平均使用次数
        demand_factor = recent_usage / avg_usage
        
        return max(0.5, min(2.0, demand_factor))
    
    def analyze_supply(self, item_info):
        """分析供给情况"""
        inventory = TradeupInventory.objects.filter(item_info=item_info).first()
        
        if not inventory or inventory.unlimited:
            return 1.0
        
        # 库存紧张度
        if inventory.count < 10:
            return 1.5  # 供给紧张，价格上调
        elif inventory.count > 100:
            return 0.8  # 供给充足，价格下调
        else:
            return 1.0  # 正常供给
```

## 监控和风控

### 异常检测系统
```python
class TradeupAnomalyDetector:
    def __init__(self):
        self.win_rate_threshold = 0.8  # 异常胜率阈值
        self.volume_threshold_multiplier = 3  # 异常交易量倍数
    
    def detect_user_anomalies(self, user):
        """检测用户异常行为"""
        anomalies = []
        
        # 检查胜率异常
        recent_games = TradeupGame.objects.filter(
            user=user,
            create_time__gte=timezone.now() - timedelta(days=30)
        )
        
        if recent_games.count() >= 10:
            win_rate = recent_games.filter(
                win_result=WinResult.Win.value
            ).count() / recent_games.count()
            
            if win_rate > self.win_rate_threshold:
                anomalies.append({
                    'type': 'high_win_rate',
                    'value': win_rate,
                    'risk_level': 'high'
                })
        
        # 检查交易量异常
        daily_volume = recent_games.filter(
            create_time__gte=timezone.now() - timedelta(days=1)
        ).aggregate(total=Sum('bet_amount'))['total'] or 0
        
        avg_daily_volume = self.get_average_daily_volume()
        if daily_volume > avg_daily_volume * self.volume_threshold_multiplier:
            anomalies.append({
                'type': 'high_volume',
                'value': daily_volume,
                'risk_level': 'medium'
            })
        
        return anomalies
    
    def monitor_system_health(self):
        """监控系统健康状态"""
        metrics = {
            'total_games_today': self.get_daily_game_count(),
            'average_bet_amount': self.get_average_bet_amount(),
            'win_rate_distribution': self.get_win_rate_distribution(),
            'inventory_levels': self.check_inventory_levels(),
            'economic_balance': self.check_economic_balance()
        }
        
        # 生成健康报告
        health_report = self.generate_health_report(metrics)
        
        # 如果发现问题，发送警报
        if health_report['status'] != 'healthy':
            self.send_admin_alert(health_report)
        
        return health_report
```

## 总结

Tradeup模块提供了创新的饰品升级玩法，但在公平性、经济平衡、风险控制等方面需要重大改进。建议重点关注透明化算法、动态经济模型、智能库存管理和全面的监控系统，确保系统的长期健康运行和用户信任。

## 接口清单

| 接口名称 | 方法 | 路径 | 状态 | 功能描述 |
|---------|------|------|------|----------|
| 获取换肤库存 | GET | `/api/tradeup/inventory/` | 正常 | 获取可换肤物品列表 |
| 匹配换肤库存 | POST | `/api/tradeup/match/` | 正常 | 匹配可能的输出物品 |
| 执行换肤游戏 | POST | `/api/tradeup/bet/` | 正常 | 执行换肤操作 |
| 获取投注记录 | GET | `/api/tradeup/betrecord/` | 正常 | 获取用户游戏历史 |
| 获取换肤历史 | GET | `/api/tradeup/history/` | 正常 | 获取全站游戏历史 |
| 获取成功率统计 | GET | `/api/tradeup/percentage/` | 正常 | 获取成功率数据 |
| 验证游戏结果 | POST | `/api/tradeup/verify/` | 正常 | 验证结果公平性 |
| 获取用户包裹 | GET | `/api/tradeup/package/` | 正常 | 获取可用包裹物品 |

**注：** 部分金币换肤接口已注释，建议根据实际需求决定是否启用。
