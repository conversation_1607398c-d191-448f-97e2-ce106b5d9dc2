/**
 * 简单的WebSocket服务器测试
 */

console.log('开始测试...');

try {
  console.log('1. 测试基础模块导入...');
  const http = require('http');
  const redis = require('redis');
  console.log('✅ 基础模块导入成功');

  console.log('2. 测试Redis配置...');
  const redisConfig = {
    socket: {
      host: process.env.REDIS_HOST || 'redis',
      port: process.env.REDIS_PORT || 6379,
      family: 4
    }
  };
  console.log('✅ Redis配置:', redisConfig);

  console.log('3. 测试Redis连接...');
  async function testRedis() {
    try {
      const client = redis.createClient(redisConfig);
      
      client.on('error', (err) => {
        console.error('❌ Redis错误:', err.message);
      });
      
      client.on('connect', () => {
        console.log('✅ Redis连接成功');
      });
      
      await client.connect();
      await client.ping();
      console.log('✅ Redis ping成功');
      
      await client.disconnect();
      console.log('✅ Redis断开连接成功');
      
      console.log('4. 测试HTTP服务器...');
      const server = http.createServer();
      
      server.on('request', (req, res) => {
        if (req.url === '/health') {
          res.writeHead(200, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({ status: 'ok' }));
        }
      });
      
      server.listen(4000, () => {
        console.log('✅ HTTP服务器启动成功，端口: 4000');
        
        // 测试健康检查
        setTimeout(() => {
          const http = require('http');
          const req = http.request('http://localhost:4000/health', (res) => {
            console.log('✅ 健康检查响应状态:', res.statusCode);
            server.close(() => {
              console.log('✅ 所有测试完成');
              process.exit(0);
            });
          });
          
          req.on('error', (err) => {
            console.error('❌ 健康检查失败:', err.message);
            server.close(() => {
              process.exit(1);
            });
          });
          
          req.end();
        }, 1000);
      });
      
    } catch (error) {
      console.error('❌ Redis测试失败:', error.message);
      console.error(error.stack);
      process.exit(1);
    }
  }
  
  testRedis();
  
} catch (error) {
  console.error('❌ 测试失败:', error.message);
  console.error(error.stack);
  process.exit(1);
}
