<template>
  <div class="relative w-full">
    <Swiper
      :modules="[Autoplay, Pagination, Navigation]"
      :slides-per-view="1"
      :space-between="0"
      :loop="true"
      :speed="500"
      :watch-slides-progress="true"
      :prevent-clicks="false"
      :prevent-clicks-propagation="false"
      :autoplay="{
        delay: 5000,
        disableOnInteraction: false,
        pauseOnMouseEnter: true
      }"
      :pagination="{
        clickable: true,
        bulletClass: 'swiper-pagination-bullet !bg-white/50',
        bulletActiveClass: 'swiper-pagination-bullet-active !bg-white'
      }"
      :navigation="{
        nextEl: '.swiper-button-next',
        prevEl: '.swiper-button-prev'
      }"
      class="w-full aspect-[21/9] md:aspect-[21/9]"
      @swiper="onSwiper"
      @slideChange="onSlideChange"
    >
      <SwiperSlide v-for="slide in slides" :key="slide.id">
        <div class="relative w-full h-full">
          <!-- 背景图片 -->
          <NuxtImg
            :src="slide.image"
            :alt="slide.title"
            class="w-full h-full object-cover"
            loading="lazy"
            placeholder
            :placeholder-blur="10"
            sizes="100vw sm:50vw md:400px"
            format="webp"
            quality="80"
          />
          
          <!-- 渐变遮罩 -->
          <div class="absolute inset-0 bg-gradient-to-r from-black/60 to-transparent" />
          
          <!-- 内容 -->
          <div class="absolute inset-0 flex items-center">
            <div class="container mx-auto px-4">
              <div class="max-w-xl">
                <h2 class="text-3xl md:text-4xl font-bold text-white mb-4">
                  {{ slide.title }}
                </h2>
                <NuxtLink
                  :to="slide.link"
                  class="inline-block px-6 py-3 bg-primary hover:bg-primary-dark text-white font-bold rounded-lg transition-colors"
                >
                  立即查看
                </NuxtLink>
              </div>
            </div>
          </div>
        </div>
      </SwiperSlide>

      <!-- 导航按钮 -->
      <div class="swiper-button-prev !text-white !w-12 !h-12 !bg-black/30 hover:!bg-black/50 rounded-full transition-colors" />
      <div class="swiper-button-next !text-white !w-12 !h-12 !bg-black/30 hover:!bg-black/50 rounded-full transition-colors" />
    </Swiper>
  </div>
</template>

<script setup lang="ts">
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Autoplay, Pagination, Navigation } from 'swiper/modules'
import type { Swiper as SwiperType } from 'swiper'
import { ref, onMounted, onBeforeUnmount } from 'vue'

// 按需导入 Swiper 样式
import 'swiper/css'
import 'swiper/css/pagination'
import 'swiper/css/navigation'

interface Slide {
  id: string | number
  image: string
  title: string
  link: string
}

interface Props {
  slides: Slide[]
}

const props = defineProps<Props>()
const swiperInstance = ref<SwiperType | null>(null)

// 预加载下一张图片
const preloadNextImage = (index: number) => {
  const nextIndex = (index + 1) % props.slides.length
  const nextSlide = props.slides[nextIndex]
  if (nextSlide) {
    const img = new Image()
    img.src = nextSlide.image
  }
}

// Swiper 实例化回调
const onSwiper = (swiper: SwiperType) => {
  swiperInstance.value = swiper
  // 预加载第一张图片
  preloadNextImage(0)
}

// 幻灯片切换回调
const onSlideChange = (swiper: SwiperType) => {
  preloadNextImage(swiper.activeIndex)
}

// 组件挂载时添加性能优化
onMounted(() => {
  // 使用 requestAnimationFrame 优化动画性能
  if (swiperInstance.value) {
    swiperInstance.value.params.speed = 500
    swiperInstance.value.params.watchSlidesProgress = true
  }
})

// 组件卸载前清理
onBeforeUnmount(() => {
  if (swiperInstance.value) {
    swiperInstance.value.destroy(true, true)
    swiperInstance.value = null
  }
})
</script>

<style>
.swiper-button-prev::after,
.swiper-button-next::after {
  font-size: 1.5rem !important;
}

.swiper-pagination-bullet {
  width: 0.75rem !important;
  height: 0.75rem !important;
  margin: 0 0.5rem !important;
  opacity: 1 !important;
}

.swiper-pagination-bullet-active {
  opacity: 1 !important;
}

/* 优化动画性能 */
.swiper-slide {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* 优化图片加载过渡 */
.swiper-slide img {
  transition: opacity 0.3s ease;
}

.swiper-slide img[loading] {
  opacity: 0;
}

.swiper-slide img:not([loading]) {
  opacity: 1;
}
</style> 