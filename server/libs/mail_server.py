import smtplib
import logging
import json
import random
import string

from django.core.cache import cache
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from email.mime.text import MIMEText
from email.header import Header
from steambase.enums import RespCode

smtp_server = settings.SMTP_SERVER
port = settings.SMTP_PORT
sender_email = settings.SENDER_EMAIL
auth_token = settings.SMTP_TOKEN
subject = "请查看Steam07验证码"

_logger = logging.getLogger(__name__)


def code_generator(size=6, chars=string.digits):
    return ''.join(random.choice(chars) for _ in range(size))


def send_mail(email):
    verify_code = code_generator(6)
    msg = MIMEText('你的邮箱验证码为：{} \n Please don`t report this email \n ---  STEAM07  ---'.format(verify_code), 'plain',
                   'utf-8')
    msg['From'] = sender_email
    msg['Subject'] = Header(subject, 'utf-8')
    msg['To'] = email
    try:
        smtp = smtplib.SMTP_SSL()
        smtp.connect(smtp_server, port)
        smtp.login(sender_email, auth_token)
        smtp.sendmail(sender_email, email, msg.as_string())
        _logger.info('Send verify code, result: {}'.format(verify_code))
        key = 'verify_code:{}'.format(email)
        cache.set(key, verify_code, 3 * 60)
        smtp.quit()
    except smtplib.SMTPException as E:
        _logger.exception(E)
        return RespCode.BadRequest.value, _('Send verify code fail')
    return RespCode.Succeed.value, {}


if __name__ == '__main__':
    email = "@qq.com"
    send_mail(email)
