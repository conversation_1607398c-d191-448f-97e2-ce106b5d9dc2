<!-- components/common/MobileBottomNav.vue -->
<template>
  <div>
    <!-- 占位元素，保持内容在底部菜单上方 -->
    <div class="footer-menu-placeholder" ref="placeholderElement"></div>

    <!-- 移动端底部导航菜单 -->
    <nav class="mobile-bottom-nav">
      <div class="nav-container">
        <div class="nav-wrapper">
          <NuxtLink 
            v-for="item in navItems" 
            :key="item.path"
            :to="item.path" 
            class="nav-item"
            :class="{ active: isActivePath(item.path) }"
          >
            <div class="icon-container">
              <i :class="item.icon"></i>
              <span v-if="item.badge" class="badge">{{ item.badge }}</span>
            </div>
            <span class="nav-label">{{ $t(item.label) }}</span>
          </NuxtLink>
        </div>
      </div>
    </nav>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'

const route = useRoute()
const userStore = useUserStore()
const placeholderElement = ref<HTMLElement | null>(null)

// 计算导航项（根据用户登录状态动态调整）
const navItems = computed(() => {
  const baseItems = [
    { path: '/', label: 'nav.home', icon: 'fas fa-home' },
    { path: '/cases', label: 'nav.cases', icon: 'fas fa-box' },
    { path: '/battle', label: 'nav.battle', icon: 'fas fa-sword' },
    { path: '/activities', label: 'nav.activity', icon: 'fas fa-star', badge: 'HOT' }
  ]

  // 根据用户状态添加最后一个导航项
  if (userStore.isAuthenticated) {
    baseItems.push({ path: '/profile', label: 'nav.profile', icon: 'fas fa-user' })
  } else {
    baseItems.push({ path: '/auth/login', label: 'nav.profile', icon: 'fas fa-user' })
  }

  return baseItems
})

// 检查路径是否激活
const isActivePath = (path: string) => {
  if (path === '/') {
    return route.path === '/'
  }
  return route.path.startsWith(path)
}

// 更新占位元素高度
const updateFooterPlaceholder = () => {
  if (process.client && placeholderElement.value) {
    const navElement = document.querySelector('.mobile-bottom-nav') as HTMLElement | null
    const footerHeight = navElement?.offsetHeight || 64 // 默认高度64px
    placeholderElement.value.style.height = `${footerHeight}px`
  }
}

onMounted(() => {
  nextTick(() => {
    updateFooterPlaceholder()
    window.addEventListener('resize', updateFooterPlaceholder)
  })
})

onUnmounted(() => {
  if (process.client) {
    window.removeEventListener('resize', updateFooterPlaceholder)
  }
})
</script>

<style lang="scss" scoped>
$nav-height: 4rem;
$nav-bg: linear-gradient(180deg, rgba(24, 24, 27, 0.95) 0%, rgba(0, 0, 0, 0.95) 100%);
$nav-border-color: rgba(113, 113, 122, 0.3);
$nav-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3);

.mobile-bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: $nav-height;
  background: $nav-bg;
  border-top: 1px solid $nav-border-color;
  box-shadow: $nav-shadow;
  z-index: 50;
  backdrop-filter: blur(10px);
}

.nav-container {
  height: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.nav-wrapper {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 100%;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 0.75rem;
  color: rgba(255, 255, 255, 0.6);
  text-decoration: none;
  min-width: 60px;
  position: relative;
  transition: all 0.2s ease;
  border-radius: 0.5rem;
  
  &:hover {
    color: rgba(255, 255, 255, 0.9);
    background-color: rgba(255, 255, 255, 0.02);
    transform: scale(1.02);
  }
  
  &.active {
    color: var(--color-primary);
    
    .icon-container {
      transform: scale(1.1);
      
      &::before {
        content: '';
        position: absolute;
        top: -8px;
        left: 50%;
        transform: translateX(-50%);
        width: 4px;
        height: 4px;
        background: var(--color-primary);
        border-radius: 50%;
      }
    }
  }

  &:active {
    transform: scale(0.95);
  }
}

.icon-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  margin-bottom: 0.25rem;
  transition: transform 0.2s ease;

  i {
    font-size: 1.125rem;
  }
}

.nav-label {
  font-size: 0.75rem;
  font-weight: 500;
  line-height: 1;
}

.badge {
  position: absolute;
  top: -4px;
  right: -6px;
  background: var(--color-secondary);
  color: white;
  font-size: 0.625rem;
  padding: 1px 4px;
  border-radius: 6px;
  line-height: 1;
  font-weight: 600;
  animation: pulse 2s infinite;
}

.footer-menu-placeholder {
  height: $nav-height;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

// 响应式调整
@media (max-width: 360px) {
  .nav-item {
    min-width: 50px;
    padding: 0.4rem 0.2rem;
  }
  
  .nav-label {
    font-size: 10px;
  }
  
  .icon-container i {
    font-size: 1rem;
  }
}
</style>
