import type { Ref } from 'vue'
import type { BattleStatus } from '~/services/battle-api'

// 🎯 Battle核心接口定义
export interface BattleCoreInterface {
  // 状态管理
  battleState: {
    isLoading: Ref<boolean>
    error: Ref<string | null>
    battleData: Ref<any>
    battleState: Ref<BattleStatus>
    currentRound: Ref<number>
    totalRounds: Ref<number>
    displayCases: Ref<any[]>
    allPlayers: Ref<any[]>
    openingPlayerName: Ref<string>
    openingCaseId: Ref<string | null>
    openingPlayerIndex: Ref<number | null>
    isUserJoined: Ref<boolean>
    isUserCreator: Ref<boolean>
    isBattleWaiting: Ref<boolean>
    isBattleInProgress: Ref<boolean>
    isBattleCompleted: Ref<boolean>
    isBattleCancelled: Ref<boolean>
    isBattleFinished: Ref<boolean>
    showCalculationAnimation: Ref<boolean>
    showResultModal: Ref<boolean>
    finalWinner: Ref<any>
    closeCalculationAnimation: () => void
    closeResultModal: () => void
  }
  
  // WebSocket 管理
  battleWebSocket: {
    connectionState: Ref<{
      isConnected: boolean
      isReconnecting: boolean
      reconnectAttempts: number
    }>
    animationState: Ref<any>
    timeSync: Ref<any>
    getMessageStats: () => any
    validateStateConsistency: () => boolean
  }
  
  // 操作方法
  battleActions: {
    showToast: Ref<boolean>
    toastMessage: Ref<string>
    toastType: Ref<string>
    handleJoinBattle: () => Promise<any>
    handleLeaveBattle: () => Promise<any>
    handleDismissBattle: () => Promise<boolean>
    handleStartBattle: () => Promise<any>
    handleCopyId: () => void
    handleShare: () => void
    getToastIcon: (type: string) => string
    hideToast: () => void
  }
  
  // 核心方法
  initialize: () => Promise<void>
  cleanup: () => void
  startTestAnimation: () => void
} 