# -*- coding: utf-8 -*-
# Generated by Django 1.11.13 on 2021-04-14 02:09
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import steambase.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODELL),
        ('package', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='TradeRecord',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('trade_url', models.CharField(max_length=256, verbose_name='trade url')),
                ('steam_id', models.CharField(max_length=128, verbose_name='steam id')),
                ('amount', models.FloatField(default=0.0, verbose_name='amount')),
                ('appid', models.CharField(blank=True, choices=[('570', 'Dota2'), ('730', 'CSGO'), ('578080', 'PUBG'), ('433850', 'H1Z1')], default='730', max_length=64, null=True, verbose_name='appid')),
                ('amount_used', models.FloatField(default=0.0, verbose_name='amount used')),
                ('drop_refund', models.FloatField(default=0.0, verbose_name='drop refund')),
                ('balance_refund', models.FloatField(default=0.0, verbose_name='balance refund')),
                ('state', models.SmallIntegerField(choices=[(0, 'Initialed'), (1, 'Active'), (2, 'Closed'), (3, 'PendClose')], default=0, verbose_name='trade state')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='wxp_user', to=settings.AUTH_USER_MODELL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'Trade Record',
                'verbose_name_plural': 'Trade Record',
                'ordering': ('-create_time',),
            },
        ),
        migrations.CreateModel(
            name='TradeStatisticsDay',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('date', models.DateField(default=django.utils.timezone.now, verbose_name='date')),
                ('amount', models.FloatField(default=0.0, verbose_name='amount')),
                ('test_amount', models.FloatField(default=0.0, verbose_name='test amount')),
                ('appid', models.CharField(blank=True, choices=[('570', 'Dota2'), ('730', 'CSGO'), ('578080', 'PUBG'), ('433850', 'H1Z1')], default='730', max_length=64, null=True, verbose_name='appid')),
            ],
            options={
                'verbose_name': 'Trade Statistics Day',
                'verbose_name_plural': 'Trade Statistics Day',
                'ordering': ('-create_time',),
            },
        ),
        migrations.CreateModel(
            name='TradeStatisticsMonth',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('date', models.DateField(default=django.utils.timezone.now, verbose_name='date')),
                ('amount', models.FloatField(default=0.0, verbose_name='amount')),
                ('test_amount', models.FloatField(default=0.0, verbose_name='test amount')),
                ('appid', models.CharField(blank=True, choices=[('570', 'Dota2'), ('730', 'CSGO'), ('578080', 'PUBG'), ('433850', 'H1Z1')], default='730', max_length=64, null=True, verbose_name='appid')),
            ],
            options={
                'verbose_name': 'Trade Statistics Month',
                'verbose_name_plural': 'Trade Statistics Month',
                'ordering': ('-create_time',),
            },
        ),
        migrations.CreateModel(
            name='WithdrawItem',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('returned', models.BooleanField(default=False, verbose_name='returned')),
                ('package', models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, to='package.PackageItem', verbose_name='package item')),
                ('trade', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='withdraw_items', to='withdraw.TradeRecord', verbose_name='trade id')),
            ],
            options={
                'verbose_name': 'Withdraw Item',
                'verbose_name_plural': 'Withdraw Item',
                'ordering': ('-create_time',),
            },
        ),
        migrations.CreateModel(
            name='WxpTradeOffer',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('project_id', models.CharField(blank=True, default=None, max_length=64, null=True, verbose_name='project id')),
                ('wxp_id', models.CharField(blank=True, default=None, max_length=64, null=True, verbose_name='waxpeer id')),
                ('wxp_item_id', models.CharField(blank=True, default=None, max_length=64, null=True, verbose_name='waxpeer item id')),
                ('status', models.SmallIntegerField(choices=[(0, 'waiting for user to buy more items'), (1, 'Proccessing the trade'), (2, 'Waiting for seller to confirm'), (4, 'Trade Sent'), (5, 'Completed'), (6, 'Declined and Refunded')], default=0, verbose_name='waxpeer withdraw status')),
                ('price', models.FloatField(default=0.0, verbose_name='price')),
                ('done', models.BooleanField(default=False, verbose_name='done')),
                ('reason', models.CharField(blank=True, default=None, max_length=256, null=True, verbose_name='reason')),
                ('market_name', models.CharField(blank=True, default=None, max_length=128, null=True, verbose_name='market name')),
                ('steam_trade_id', models.CharField(blank=True, default=None, max_length=64, null=True, verbose_name='steam trade id')),
                ('item_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='package.ItemInfo', verbose_name='item info')),
                ('trade', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='wxp_trades', to='withdraw.TradeRecord', verbose_name='trade id')),
            ],
            options={
                'verbose_name': 'Wxp Trade Offer',
                'verbose_name_plural': 'Wxp Trade Offer',
                'ordering': ('-create_time',),
            },
        ),
    ]
