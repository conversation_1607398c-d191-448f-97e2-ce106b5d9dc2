import { ref, onMounted, onUnmounted, watch } from 'vue';
import { useBattleState } from './useBattleState';
import { useBattleWebSocket } from './useBattleWebSocket';
import { useBattleAnimationCoordinator } from './useBattleAnimationCoordinator';
import { useTimeSync } from './useTimeSync';
import { useBattleReconnection } from './useBattleReconnection';
import { useNotification } from './useNotification';
import { battleApi } from '~/services/battle-api';
import type { Battle } from '~/types/battle';

export function useBattleMasterController(battleId: string) {
  const state = useBattleState();
  const ws = useBattleWebSocket(battleId);
  const animationCoordinator = useBattleAnimationCoordinator();
  const timeSync = useTimeSync();
  const reconnection = useBattleReconnection(battleId);
  const { showSuccess, showError } = useNotification();

  const initialize = async () => {
    state.isLoading.value = true;
    try {
      const initialBattleData = await battleApi.getRoomDetail(battleId);
      if (initialBattleData.success && initialBattleData.data) {
        state.updateBattleData(initialBattleData.data);
        ws.registerAdditionalId(initialBattleData.data.short_id);
      } else {
        throw new Error(initialBattleData.message || 'Failed to fetch battle details.');
      }

      ws.initialize();
      setupWebSocketHandlers();

      if (reconnection.requiresStateRecovery.value) {
        await handleReconnection();
      }
    } catch (error: any) {
      state.error.value = error.message;
      showError(error.message);
    } finally {
      state.isLoading.value = false;
    }
  };

  const setupWebSocketHandlers = () => {
    ws.onRoomUpdate(data => state.updateBattleData(data));
    ws.onBattleStart(data => state.updateBattleData(data));
    ws.onRoomCancel(() => state.updateBattleState('cancelled'));

    ws.onRoundStart(data => {
      // Prepare for animation
    });

    ws.onOpeningStart(data => {
      const localStartTime = timeSync.toLocalTime(data.animation_start_timestamp);
      const delay = localStartTime - Date.now();
      
      setTimeout(() => {
        animationCoordinator.startAnimation({
          animationId: data.animation_id,
          type: 'case_opening',
          duration: data.animation_duration,
          players: data.participants.map((p: any) => p.user.uid)
        });
      }, Math.max(0, delay));
    });

    ws.onRoundResult(data => {
      // Process results after animation
    });

    ws.onBattleEnd(data => {
      state.updateBattleData(data);
      state.updateBattleState('completed');
    });
  };

  const handleReconnection = async () => {
    showSuccess('Reconnected! Syncing battle state...');
    try {
      const animState = await reconnection.recoverAnimationState();
      if (animState) {
        // Logic to fast-forward animation based on animState
        const { animation_state } = animState;
        const timeDiff = Date.now() - animState.server_timestamp;
        const actualElapsedTime = animation_state.elapsed_time + timeDiff;
        
        animationCoordinator.fastForward(actualElapsedTime);
        showSuccess('Battle state synced!');
      }
    } catch (error: any) {
      showError('Failed to sync battle state. Please refresh.');
    }
  };

  watch(ws.connectionState, (newState) => {
    if (newState.isConnected && reconnection.requiresStateRecovery.value) {
      handleReconnection();
    }
  });

  const cleanup = () => {
    ws.cleanup();
  };

  return {
    ...state,
    initialize,
    cleanup,
  };
}