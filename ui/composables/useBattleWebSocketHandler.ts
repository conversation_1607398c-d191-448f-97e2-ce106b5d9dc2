import { useBattleStore } from '~/stores/battle';

// 消息统计信息
interface MessageStats {
  total: number;
  byType: Map<string, number>;
  byAction: Map<string, number>;
  lastMessageTimestamp: number;
  errorCount: number;
}

// 状态一致性检查结果
interface ConsistencyCheckResult {
  isValid: boolean;
  errors: string[];
}

class BattleWebSocketHandler {
  private battleStore = useBattleStore();
  private messageStats: MessageStats = {
    total: 0,
    byType: new Map(),
    byAction: new Map(),
    lastMessageTimestamp: 0,
    errorCount: 0,
  };

  // 处理WebSocket消息
  public handleWebSocketMessage(messageType: string, action: string, data: any, socketId?: string) {
    this.updateMessageStats(messageType, action);

    try {
      switch (messageType) {
        case 'boxroom':
          this.handleBoxRoomMessage(action, data);
          break;
        case 'boxroomdetail':
          this.handleBoxRoomDetailMessage(action, data, socketId);
          break;
        default:
          console.warn(`[WS-HANDLER] 未知的消息类型: ${messageType}`);
      }
    } catch (error) {
      this.messageStats.errorCount++;
      console.error(`[WS-HANDLER] 处理消息时出错: ${messageType}.${action}`, error);
    }
  }

  // 处理房间级别消息
  private handleBoxRoomMessage(action: string, data: any) {
    switch (action) {
      case 'new':
        this.battleStore.handleSocketNewRoom(data);
        break;
      case 'update':
        this.battleStore.handleSocketRoomUpdate(data);
        break;
      case 'start':
        this.battleStore.handleSocketRoomStart(data);
        break;
      case 'cancel':
        this.battleStore.handleSocketRoomCancel(data);
        break;
      case 'end':
        this.battleStore.handleSocketRoomEnd(data);
        break;
      default:
        console.warn(`[WS-HANDLER] 未知的boxroom操作: ${action}`);
    }
  }

  // 处理房间详情级别消息
  private handleBoxRoomDetailMessage(action: string, data: any, socketId?: string) {
    switch (action) {
      case 'round_start':
      case 'opening_start':
      case 'animation_progress':
      case 'round_result':
      case 'battle_end':
        // 这些事件由 useBattleWebSocket 直接处理
        break;
      case 'round': // 兼容旧版
        this.battleStore.handleSocketRoundStart(data, socketId);
        break;
      case 'end': // 兼容旧版
        this.battleStore.handleSocketBattleEnd(data, socketId);
        break;
      default:
        console.warn(`[WS-HANDLER] 未知的boxroomdetail操作: ${action}`);
    }
  }

  // 更新消息统计
  private updateMessageStats(messageType: string, action: string) {
    this.messageStats.total++;
    this.messageStats.lastMessageTimestamp = Date.now();

    const typeCount = this.messageStats.byType.get(messageType) || 0;
    this.messageStats.byType.set(messageType, typeCount + 1);

    const actionCount = this.messageStats.byAction.get(action) || 0;
    this.messageStats.byAction.set(action, actionCount + 1);
  }

  // 获取消息统计
  public getMessageStats(): MessageStats {
    return this.messageStats;
  }

  // 验证状态一致性
  public validateStateConsistency(): ConsistencyCheckResult {
    const errors: string[] = [];
    const { currentRoom } = this.battleStore;

    if (currentRoom) {
      if (currentRoom.joiner_count !== currentRoom.bets.length) {
        errors.push('参与人数与bets数组长度不匹配');
      }
      if (currentRoom.round_count !== currentRoom.rounds.length) {
        errors.push('总轮数与rounds数组长度不匹配');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}

let instance: BattleWebSocketHandler | null = null;

export const useBattleWebSocketHandler = () => {
  if (!instance) {
    instance = new BattleWebSocketHandler();
  }
  return instance;
};