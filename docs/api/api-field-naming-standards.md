# API字段命名规范

## 概述

本文档规定了CSGO饰品开箱项目中API字段的统一命名规范，确保前后端对接的一致性和前端开发的便利性。

**最后更新时间**: 2024-12-29  
**适用范围**: 所有API接口的字段命名

## 1. 国际化字段命名规范

### 1.1 统一格式

所有涉及名称的国际化字段必须使用以下统一格式：

```json
{
    "name": "默认名称（通常是中文）",
    "name_en": "英文名称", 
    "name_zh_hans": "简体中文名称"
}
```

### 1.2 适用范围

以下数据类型必须提供完整的国际化字段：

#### 箱子相关
- **箱子名称**：在所有API响应中统一使用 `name`、`name_en`、`name_zh_hans`
- **箱子分类**：使用相同的命名规范

#### 饰品相关
- **饰品名称**：使用 `name`、`name_en`、`name_zh_hans`
- **饰品分类**：`cate_name`、`cate_name_en`、`cate_name_zh_hans`
- **饰品品质**：`quality_name`、`quality_name_en`、`quality_name_zh_hans`
- **饰品稀有度**：`rarity_name`、`rarity_name_en`、`rarity_name_zh_hans`
- **饰品外观**：`exterior_name`、`exterior_name_en`、`exterior_name_zh_hans`

### 1.3 ❌ 错误示例

**禁止使用**以下不一致的命名：
```json
// ❌ 错误：使用了不一致的字段名
{
    "case_name": "AK-47 红线箱子",
    "case_name_en": "AK-47 Redline Case",
    "case_name_zh_hans": "AK-47 红线箱子"
}
```

### 1.4 ✅ 正确示例

**正确使用**统一的字段命名：
```json
// ✅ 正确：统一使用name字段
{
    "name": "AK-47 红线箱子",
    "name_en": "AK-47 Redline Case",
    "name_zh_hans": "AK-47 红线箱子"
}
```

## 2. 对战系统字段规范

### 2.1 房间列表接口字段

```json
{
    "rounds": [
        {
            "round_number": 1,
            "case_key": "ak47_redline_case",
            "name": "AK-47 红线箱子",
            "name_en": "AK-47 Redline Case",
            "name_zh_hans": "AK-47 红线箱子"
        }
    ]
}
```

### 2.2 房间详情接口字段

```json
{
    "rounds": [
        {
            "case": {
                "case_key": "ak47_redline_case",
                "name": "AK-47 红线箱子",
                "name_en": "AK-47 Redline Case", 
                "name_zh_hans": "AK-47 红线箱子"
            }
        }
    ]
}
```

### 2.3 开箱结果字段

```json
{
    "open_items": [
        {
            "name": "AK-47 | 红线 (久经沙场)",
            "name_en": "AK-47 | Redline (Field-Tested)",
            "name_zh_hans": "AK-47 | 红线 (久经沙场)"
        }
    ]
}
```

## 3. 后端实现规范

### 3.1 序列化器字段配置

在Django序列化器中，必须使用统一的字段名：

```python
class CaseSerializer(CustomFieldsSerializer):
    class Meta:
        model = Case
        fields = '__all__'
    
    # 国际化字段自动包含name、name_en、name_zh_hans

class CaseRoomRoundSerializer(CustomFieldsSerializer):
    case = CaseSerializer(
        read_only=True, 
        fields=('case_key', 'name', 'name_en', 'name_zh_hans', 'cover', 'price')
    )
```

### 3.2 模型字段要求

数据库模型必须包含完整的国际化字段：

```python
class Case(models.Model):
    name = models.CharField(max_length=255)           # 默认名称
    name_en = models.CharField(max_length=255)        # 英文名称
    name_zh_hans = models.CharField(max_length=255)   # 简体中文名称
```

## 4. 前端使用规范

### 4.1 推荐的前端处理函数

```javascript
/**
 * 根据用户语言偏好获取本地化名称
 * @param {Object} item - 包含国际化字段的对象
 * @param {string} locale - 语言代码 ('zh-CN', 'en-US')
 * @returns {string} 本地化的名称
 */
function getLocalizedName(item, locale = 'zh-CN') {
    switch (locale) {
        case 'en-US':
        case 'en':
            return item.name_en || item.name;
        case 'zh-CN':
        case 'zh-Hans':
            return item.name_zh_hans || item.name;
        default:
            return item.name;
    }
}

// 使用示例
const caseName = getLocalizedName(caseData, userLocale);
const itemName = getLocalizedName(itemData, userLocale);
```

### 4.2 TypeScript类型定义

```typescript
interface InternationalizedName {
    name: string;           // 默认名称
    name_en: string;        // 英文名称
    name_zh_hans: string;   // 简体中文名称
}

interface CaseInfo extends InternationalizedName {
    case_key: string;
    cover: string;
    price: number;
}

interface ItemInfo extends InternationalizedName {
    item_id: number;
    image: string;
    item_price: {
        price: number;
        update_time: string;
    };
}
```

## 5. 验证和测试

### 5.1 API响应验证

每个API接口的响应都必须通过以下验证：

1. **字段命名一致性检查**：确保所有国际化字段使用统一命名
2. **完整性检查**：确保所有必需的国际化字段都存在
3. **数据有效性检查**：确保国际化字段的数据不为空

### 5.2 自动化测试

```python
def test_api_field_naming_consistency():
    """测试API字段命名的一致性"""
    response = client.get('/api/box/battle/list/')
    data = response.json()
    
    for room in data['body']['rooms']:
        for round_item in room['rounds']:
            # 验证字段命名一致性
            assert 'name' in round_item
            assert 'name_en' in round_item
            assert 'name_zh_hans' in round_item
            
            # 禁止使用不一致的字段名
            assert 'case_name' not in round_item
            assert 'case_name_en' not in round_item
```

## 6. 修复历史

### 6.1 已修复的不一致问题

| 接口 | 原字段名 | 修复后字段名 | 修复日期 |
|------|----------|-------------|----------|
| 房间列表 | `case_name` | `name` | 2024-12-29 |
| 房间列表 | `case_name_en` | `name_en` | 2024-12-29 |
| 房间列表 | `case_name_zh_hans` | `name_zh_hans` | 2024-12-29 |

### 6.2 修复影响范围

- ✅ API文档已更新
- ✅ 后端序列化器字段已一致
- ✅ 相关测试用例已修复
- ⚠️ 前端需要相应调整（如果已经使用了错误的字段名）

## 7. 注意事项

### 7.1 向后兼容性

在字段命名修复过程中，需要考虑：

1. **渐进式修复**：优先修复文档，然后根据需要调整后端
2. **兼容性保证**：如果前端已经在使用错误的字段名，可能需要临时保持兼容
3. **版本控制**：重大字段变更需要通过API版本控制管理

### 7.2 团队协作

1. **代码审查**：所有API相关的PR都必须检查字段命名一致性
2. **文档同步**：API文档修改必须与后端实现同步
3. **前后端对齐**：字段变更需要前后端团队共同确认

## 8. 总结

通过统一的字段命名规范，我们可以：

1. **提高开发效率**：前端开发者不需要处理不一致的字段名
2. **减少错误**：避免因字段名混乱导致的数据显示问题
3. **提升维护性**：统一的规范便于代码维护和新人上手
4. **增强可靠性**：规范的字段命名提高了API的可靠性

**记住：所有名称相关的国际化字段都使用 `name`、`name_en`、`name_zh_hans` 格式！**

---

**维护责任人**: 后端开发团队  
**审核责任人**: 技术负责人  
**更新频率**: 根据API变更及时更新 