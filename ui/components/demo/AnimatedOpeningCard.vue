<template>
  <div class="animated-card relative overflow-hidden">
    <!-- 背景动画 -->
    <div class="absolute inset-0 bg-gradient-to-r from-cyan-500/10 via-blue-500/20 to-purple-500/10"></div>
    <div class="absolute inset-0 bg-tech-pattern opacity-10 animate-tech-flow"></div>
    
    <!-- 流光效果 -->
    <div class="absolute inset-0 bg-gradient-to-r from-transparent via-cyan-400/30 to-transparent animate-flow-light"></div>
    
    <!-- 边框光效 -->
    <div class="absolute inset-0 rounded-lg bg-gradient-to-r from-cyan-400/50 via-blue-500/50 to-purple-500/50 animate-border-glow"></div>
    
    <div class="relative bg-gray-900/90 backdrop-blur-lg rounded-lg border border-cyan-400/30 p-5 m-1">
      <!-- 顶部 HUD -->
      <div class="flex justify-between items-center mb-4">
        <div class="flex items-center space-x-2">
          <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
          <span class="text-green-400 font-mono text-xs">LIVE</span>
        </div>
        <div class="text-cyan-400 font-mono text-xs">
          ID: {{ String(record.id).padStart(6, '0') }}
        </div>
      </div>
      
      <!-- 主要内容 -->
      <div class="flex items-center space-x-4">
        <!-- 左侧用户区 -->
        <div class="flex-shrink-0 text-center">
          <div class="relative mb-2">
            <div class="absolute inset-0 bg-cyan-400/20 blur-md rounded-full animate-pulse"></div>
            <div class="relative w-12 h-12 bg-gradient-to-br from-cyan-400 to-blue-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
              {{ record.user_name?.charAt(0).toUpperCase() || 'U' }}
            </div>
          </div>
          <div class="text-cyan-400 font-mono text-xs">{{ record.user_name }}</div>
        </div>
        
        <!-- 中央箭头动画 -->
        <div class="flex items-center space-x-2">
          <img
            :src="record.case_info?.cover"
            :alt="record.case_info?.name"
            class="w-16 h-16 object-contain transform hover:scale-110 transition-transform duration-300 bg-gray-800"
            @error="(e) => (e.target as HTMLImageElement).src = 'https://placehold.co/64x64/1e293b/60a5fa?text=CASE'"
          />
          <div class="flex space-x-1">
            <div class="w-2 h-2 bg-cyan-400 rounded-full animate-bounce" style="animation-delay: 0s"></div>
            <div class="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
            <div class="w-2 h-2 bg-purple-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
          </div>
          <img
            :src="record.item_info?.image"
            :alt="record.item_info?.name"
            class="w-20 h-20 object-contain transform hover:scale-110 transition-transform duration-300 drop-shadow-2xl bg-gray-800"
            @error="(e) => (e.target as HTMLImageElement).src = 'https://placehold.co/80x80/dc2626/fecaca?text=WEAPON'"
          />
        </div>
        
        <!-- 右侧信息 -->
        <div class="flex-grow">
          <div class="text-white font-bold mb-1">{{ getSkinName(record.item_info?.name) }}</div>
          <div class="text-cyan-400 text-sm mb-1">{{ record.case_info?.name }}</div>
          <div class="text-purple-400 text-xs">{{ getExteriorName() }}</div>
        </div>
      </div>
      
      <!-- 底部进度条 -->
      <div class="mt-4">
        <div class="h-1 bg-gray-700 rounded-full overflow-hidden">
          <div class="h-full bg-gradient-to-r from-cyan-400 to-purple-500 rounded-full animate-progress"></div>
        </div>
      </div>
      
      <!-- 角落装饰 -->
      <div class="absolute top-2 right-2 text-cyan-400/50">
        <svg class="w-4 h-4 animate-spin" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 2v4m0 12v4m10-10h-4M6 12H2m15.364-7.364l-2.828 2.828M9.464 16.536l-2.828 2.828m12.728 0l-2.828-2.828M9.464 7.464L6.636 4.636"/>
        </svg>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  record: any
}

const props = defineProps<Props>()

const getSkinName = (fullName?: string): string => {
  if (!fullName) return 'Unknown Item'
  const parts = fullName.split(' | ')
  if (parts.length < 2) return fullName
  const skinPart = parts[1].trim()
  return skinPart.split(' (')[0].trim()
}

const getExteriorName = (): string => {
  return props.record.item_info?.item_exterior?.exterior_name || 'Factory New'
}
</script>

<style lang="scss" scoped>
.animated-card {
  animation: techSlideIn 0.8s ease-out;
}

.bg-tech-pattern {
  background-image: 
    linear-gradient(45deg, rgba(6, 182, 212, 0.1) 25%, transparent 25%),
    linear-gradient(-45deg, rgba(6, 182, 212, 0.1) 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, rgba(6, 182, 212, 0.1) 75%),
    linear-gradient(-45deg, transparent 75%, rgba(6, 182, 212, 0.1) 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
}

@keyframes techSlideIn {
  0% {
    transform: translateY(20px) scale(0.95);
    opacity: 0;
  }
  50% {
    transform: translateY(-5px) scale(1.02);
  }
  100% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

@keyframes tech-flow {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes flow-light {
  0% { transform: translateX(-200%); }
  100% { transform: translateX(200%); }
}

@keyframes border-glow {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.8; }
}

@keyframes progress {
  0% { width: 0%; }
  100% { width: 100%; }
}

.animate-tech-flow {
  animation: tech-flow 8s linear infinite;
}

.animate-flow-light {
  animation: flow-light 2s ease-in-out infinite;
}

.animate-border-glow {
  animation: border-glow 2s ease-in-out infinite;
}

.animate-progress {
  animation: progress 3s ease-out infinite;
}
</style> 