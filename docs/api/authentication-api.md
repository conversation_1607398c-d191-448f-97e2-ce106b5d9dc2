# 认证系统 API 文档

## 概述

本文档描述了认证系统的所有API接口，包括邮箱注册、登录、个人信息管理、验证码发送等功能。

## 基础信息

- **基础URL**: `/api/auth/`
- **Content-Type**: `application/json`
- **认证方式**: Session认证
- **响应格式**: JSON

## 响应状态码

| 状态码 | 含义 | 说明 |
|--------|------|------|
| 0 | Succeed | 请求成功 |
| 100 | NotLogin | 未登录 |
| 101 | BusinessError | 业务错误 |
| 102 | InvalidParams | 参数无效 |
| 103 | NoBalance | 余额不足 |
| 104 | MaxSending | 达到发送限制 |
| 400 | BadRequest | 错误请求 |
| 401 | Maintenance | 系统维护 |
| 404 | NotFound | 资源未找到 |
| 429 | TooManyRequests | 请求过于频繁 |
| 500 | Exception | 服务器内部错误 |

## 通用响应格式

```json
{
    "code": 0,
    "body": {},
    "message": "Succeed"
}
```

## 安全说明

### CSRF Token 使用说明

本系统对不同类型的接口采用不同的CSRF保护策略：

**无需CSRF Token的接口（已豁免）**：
- 注册登录：`/api/auth/email/register/`、`/api/auth/email/login/`
- 验证码相关：`/api/auth/email/code/`、`/api/auth/token/`
- 密码重置：`/api/auth/email/request-reset/`、`/api/auth/email/reset-password/`
- 获取资源：`/api/auth/csrf/`、`/api/auth/captcha/`、`/api/auth/userinfo/`

**需要CSRF Token的接口**：
- 用户设置相关：`/api/auth/set/*`（设置邮箱、昵称、头像等）
- 其他涉及用户数据修改的POST请求

### 获取和使用CSRF Token

对于需要CSRF Token的接口，请按以下步骤操作：

1. **获取CSRF Token**
```javascript
const csrfResponse = await fetch('/api/auth/csrf/', {
    method: 'GET',
    credentials: 'include'  // 重要：包含cookies
});
const csrfData = await csrfResponse.json();
const csrfToken = csrfData.body.csrf_token;
```

2. **在请求中使用CSRF Token**
```javascript
const response = await fetch('/api/auth/set/email/', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': csrfToken  // 在header中传递
    },
    credentials: 'include',  // 重要：包含cookies
    body: JSON.stringify({
        email: '<EMAIL>',
        verify_code: '123456'
    })
});
```

---

## 1. 安全相关接口

### 1.1 获取CSRF Token

**接口**: `GET /api/auth/csrf/`

**描述**: 获取CSRF token，用于需要CSRF保护的接口

**权限**: 无需认证

**请求参数**: 无

**响应示例**:
```json
{
    "code": 0,
    "body": {
        "csrf_token": "abc123xyz789..."
    },
    "message": "Succeed"
}
```

**使用说明**:
- 此接口已豁免CSRF检查，无需认证即可访问
- 获取的token需要在POST请求的`X-CSRFToken`头中传递
- 请求时必须设置`credentials: 'include'`以确保cookie正常工作

### 1.2 获取验证码

**接口**: `GET /api/auth/captcha/`

**描述**: 获取图形验证码

**请求参数**: 无

**响应示例**:
```json
{
    "code": 0,
    "body": {
        "uuid": "550e8400-e29b-41d4-a716-************",
        "captcha": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUg..."        
    },
    "message": "Succeed"
}
```

### 1.3 获取Token

**接口**: `POST /api/auth/token/`

**描述**: 获取用于验证码发送的token

**请求参数**:
```json
{
    "type": "email",  // 固定为 "email"
    "contact": "<EMAIL>",  // 邮箱地址
    "code": "1234",  // 图形验证码
    "uuid": "550e8400-e29b-41d4-a716-************"  // 验证码UUID
}
```

**响应示例**:
```json
{
    "code": 0,
    "body": {
        "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
    },
    "message": "Succeed"
}
```

---

## 2. 验证码相关接口

### 2.1 发送邮箱验证码

**接口**: `POST /api/auth/email/code/`

**描述**: 发送邮箱验证码

**请求参数**:
```json
{
    "email": "<EMAIL>",
    "type": 1,  // 1: 注册, 2: 找回密码
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

**响应示例**:
```json
{
    "code": 0,
    "body": {},
    "message": "Succeed"
}
```

**错误响应**:
```json
{
    "code": 102,
    "body": {},
    "message": "邮箱格式不正确"
}
```

---

## 3. 用户注册接口

### 3.1 邮箱注册

**接口**: `POST /api/auth/email/register/`

**描述**: 使用邮箱注册用户

**请求参数**:
```json
{
    "email": "<EMAIL>",
    "name": "用户昵称",
    "verify_code": "123456",
    "password": "password123",
    "ref_code": "ABC123"  // 可选，推荐码
}
```

**参数说明**:
- `email`: 邮箱地址
- `name`: 用户昵称，长度2-10字符
- `verify_code`: 邮箱验证码
- `password`: 密码，长度8-16字符
- `ref_code`: 推荐码（可选）

**响应示例**:
```json
{
    "code": 0,
    "body": {
        "user_id": 12345
    },
    "message": "Succeed"
}
```

---

## 4. 用户登录接口

### 4.1 邮箱登录

**接口**: `POST /api/auth/email/login/`

**描述**: 使用邮箱和密码登录

**请求参数**:
```json
{
    "email": "<EMAIL>",
    "password": "password123",
    "captcha": "1234",  // 图形验证码
    "uuid": "550e8400-e29b-41d4-a716-************"  // 验证码UUID
}
```

**响应示例**:
```json
{
    "code": 0,
    "body": {
        "user": {
            "uid": "abc123def456",
            "email": "<EMAIL>",
            "nickname": "用户昵称",
            "is_active": true
        }
    },
    "message": "Succeed"
}
```

### 4.2 检查登录状态

**接口**: `GET /api/auth/checklogin/`

**描述**: 检查用户是否已登录

**请求参数**: 无

**响应示例**:
```json
{
    "code": 0,
    "body": true,
    "message": "Succeed"
}
```

### 4.3 用户登出

**接口**: `GET /api/auth/logout/`

**描述**: 用户登出

**请求参数**: 无

**响应**: 重定向到首页

---

## 5. 密码重置接口

### 5.1 邮箱重置密码

**接口**: `POST /api/auth/email/reset-password/`

**描述**: 通过邮箱验证码重置密码

**请求参数**:
```json
{
    "email": "<EMAIL>",
    "verify_code": "123456",
    "newPassword": "newpassword123"
}
```

**响应示例**:
```json
{
    "code": 0,
    "body": {},
    "message": "Succeed"
}
```

### 5.2 请求重置邮件

**接口**: `POST /api/auth/email/request-reset/`

**描述**: 请求发送密码重置邮件

**请求参数**:
```json
{
    "email": "<EMAIL>",
    "code": "123456"  // 图形验证码
}
```

**响应示例**:
```json
{
    "code": 0,
    "body": {},
    "message": "Succeed"
}
```

---

## 6. 用户信息接口

### 6.1 获取用户信息

**接口**: `GET /api/auth/userinfo/`

**描述**: 获取当前登录用户的详细信息

**权限**: 需要登录

**请求参数**: 无

**响应示例**:
```json
{
    "code": 0,
    "body": {
        "uid": "abc123def456",
        "email": "<EMAIL>",
        "nickname": "用户昵称",
        "profile": {
            "avatar": "https://example.com/avatar.jpg"
        },
        "steam": {
            "steamid": "76561198000000000",
            "personaname": "Steam昵称",
            "level": 10,
            "avatar": "https://steamcdn-a.akamaihd.net/steamcommunity/public/images/avatars/...",
            "avatarmedium": "https://steamcdn-a.akamaihd.net/steamcommunity/public/images/avatars/...",
            "avatarfull": "https://steamcdn-a.akamaihd.net/steamcommunity/public/images/avatars/..."
        },
        "asset": {
            "balance": 100.50,
            "points": 200.0,
            "diamond": 50.0,
            "active_point": 80.0,
            "tradeurl": "https://steamcommunity.com/tradeoffer/new/?partner=123456&token=abcdefgh"
        },
        "extra": {
            "box_chance_type": "a",
            "box_free_count": 3,
            "ban_chat": false,
            "ban_deposit": false,
            "ban_withdraw": false
        },
        "level": 5,
        "is_agent": false,
        "is_active": true,
        "is_vip": false,
        "login_time": "2023-12-01T10:30:00Z",
        "login_ip": "***********",
        "date_joined": "2023-01-01T00:00:00Z"
    },
    "message": "Succeed"
}
```

### 6.2 获取余额记录

**接口**: `GET /api/auth/balancerecord/`

**描述**: 获取用户余额变动记录

**权限**: 需要登录

**请求参数**:
- `page`: 页码（默认1）
- `pageSize`: 每页数量（默认10）

**响应示例**:
```json
{
    "code": 0,
    "body": {
        "items": [
            {
                "uid": "record123",
                "create_time": "2023-12-01 10:30:00",
                "update_time": "2023-12-01 10:30:00",
                "balance_changed": 50.0,
                "balance_before": 100.0,
                "balance_after": 150.0,
                "reason": "充值"
            }
        ],
        "total": 100
    },
    "message": "Succeed"
}
```

---

## 7. 个人信息设置接口

### 7.1 设置邮箱

**接口**: `POST /api/auth/set/email/`

**描述**: 绑定或修改邮箱

**权限**: 需要登录

**请求参数**:
```json
{
    "email": "<EMAIL>",
    "verify_code": "123456"
}
```

**响应示例**:
```json
{
    "code": 0,
    "body": {},
    "message": "Succeed"
}
```

### 7.2 设置昵称

**接口**: `POST /api/auth/set/name/`

**描述**: 设置用户昵称

**权限**: 需要登录

**请求参数**:
```json
{
    "name": "新昵称"
}
```

**响应示例**:
```json
{
    "code": 0,
    "body": "新昵称",
    "message": "Succeed"
}
```

### 7.3 设置头像

**接口**: `POST /api/auth/set/avatar/`

**描述**: 上传设置用户头像

**权限**: 需要登录

**请求参数**:
```json
{
    "body": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD..."
}
```

**响应示例**:
```json
{
    "code": 0,
    "body": {
        "avatar_url": "https://example.com/avatar/123456.jpg"
    },
    "message": "Succeed"
}
```

### 7.4 设置Steam交易链接

**接口**: `POST /api/auth/set/steamlink/`

**描述**: 设置Steam交易链接

**权限**: 需要登录

**请求参数**:
```json
{
    "tradeUrl": "https://steamcommunity.com/tradeoffer/new/?partner=123456&token=abcdefgh"
}
```

**响应示例**:
```json
{
    "code": 0,
    "body": "https://steamcommunity.com/tradeoffer/new/?partner=123456&token=abcdefgh",
    "message": "Succeed"
}
```

**错误响应**:
```json
{
    "code": 102,
    "body": {},
    "message": "Invalid trade url"
}
```

---

## 8. 错误处理

### 常见错误码及处理

1. **未登录 (100)**
   ```json
   {
       "code": 100,
       "body": {},
       "message": "用户未登录"
   }
   ```

2. **参数错误 (102)**
   ```json
   {
       "code": 102,
       "body": {},
       "message": "邮箱格式不正确"
   }
   ```

3. **业务错误 (101)**
   ```json
   {
       "code": 101,
       "body": {},
       "message": "验证码错误"
   }
   ```

4. **请求过于频繁 (429)**
   ```json
   {
       "code": 429,
       "body": {},
       "message": "请求过于频繁，请稍后再试"
   }
   ```

---

## 9. 使用示例

### 完整注册流程

```javascript
// 1. 获取图形验证码
const captchaResponse = await fetch('/api/auth/captcha/');
const captchaData = await captchaResponse.json();

// 2. 获取token（已豁免CSRF）
const tokenResponse = await fetch('/api/auth/token/', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        type: 'email',
        contact: '<EMAIL>',
        code: '1234',
        uuid: captchaData.body.uuid
    })
});
const tokenData = await tokenResponse.json();

// 3. 发送邮箱验证码（已豁免CSRF）
const emailResponse = await fetch('/api/auth/email/code/', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        email: '<EMAIL>',
        type: 1,
        token: tokenData.body.token
    })
});

// 4. 注册用户（已豁免CSRF）
const registerResponse = await fetch('/api/auth/email/register/', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    credentials: 'include',  // 重要：包含cookies
    body: JSON.stringify({
        email: '<EMAIL>',
        name: '用户昵称',
        verify_code: '123456',
        password: 'password123'
    })
});
```

### 完整登录流程

```javascript
// 1. 获取图形验证码
const captchaResponse = await fetch('/api/auth/captcha/');
const captchaData = await captchaResponse.json();

// 2. 邮箱登录（已豁免CSRF）
const loginResponse = await fetch('/api/auth/email/login/', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    credentials: 'include',  // 重要：包含cookies
    body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123',
        captcha: '1234',
        uuid: captchaData.body.uuid
    })
});

// 3. 获取用户信息
const userInfoResponse = await fetch('/api/auth/userinfo/', {
    credentials: 'include'  // 重要：包含session cookies
});
const userInfo = await userInfoResponse.json();
```

### 需要CSRF Token的接口示例

```javascript
// 1. 获取CSRF Token
const csrfResponse = await fetch('/api/auth/csrf/', {
    credentials: 'include'
});
const csrfData = await csrfResponse.json();
const csrfToken = csrfData.body.csrf_token;

// 2. 设置用户邮箱（需要CSRF）
const setEmailResponse = await fetch('/api/auth/set/email/', {
    method: 'POST',
    headers: { 
        'Content-Type': 'application/json',
        'X-CSRFToken': csrfToken  // 重要：包含CSRF Token
    },
    credentials: 'include',
    body: JSON.stringify({
        email: '<EMAIL>',
        verify_code: '123456'
    })
});
```

---

## 10. 注意事项

1. **CSRF保护**: 登录、注册、密码重置等核心认证接口已豁免CSRF检查
2. **Session管理**: 登录后会建立session，后续请求需要包含cookies (`credentials: 'include'`)
3. **验证码有效期**: 图形验证码和邮箱验证码都有时效性
4. **频率限制**: 发送验证码有频率限制，避免频繁请求
5. **密码安全**: 密码传输建议使用HTTPS
6. **错误处理**: 请根据返回的code字段判断请求状态

最后更新时间: 2025-06-16


