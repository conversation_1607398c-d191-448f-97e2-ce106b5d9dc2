{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@formkit/auto-animate": "^0.8.2", "@nuxt/icon": "^1.13.0", "@nuxt/image": "^1.10.0", "@nuxtjs/i18n": "^9.3.4", "@pinia/nuxt": "^0.10.1", "@tweenjs/tween.js": "^25.0.0", "@types/socket.io": "^3.0.1", "@types/three": "^0.175.0", "@vueuse/core": "^13.0.0", "@vueuse/integrations": "^13.1.0", "@vueuse/nuxt": "^13.0.0", "animate.css": "^4.1.1", "axios": "^1.8.4", "cors": "^2.8.5", "echarts": "^5.6.0", "express": "^5.1.0", "gsap": "^3.12.7", "http-proxy-middleware": "^3.0.3", "ipx": "^3.0.3", "jquery": "^3.7.1", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "lucide-vue-next": "^0.486.0", "motion-v": "^1.0.0-alpha.4", "nuxt": "^3.16.1", "pinia": "^3.0.1", "qrcode": "^1.5.4", "sass": "^1.86.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "swiper": "^11.2.6", "three": "^0.175.0", "vitest": "^3.0.9", "vue": "^3.5.13", "vue-router": "^4.5.0", "ws": "^8.18.2"}, "devDependencies": {"@iconify-json/fluent-emoji-flat": "^1.2.3", "@iconify-json/ph": "^1.2.2", "@nuxtjs/proxy": "^2.1.0", "@nuxtjs/tailwindcss": "^6.13.2", "@types/js-cookie": "^3.0.6", "@types/node": "^22.13.11", "@types/socket.io-client": "^1.4.36", "@unocss/nuxt": "^66.1.2", "@unocss/preset-icons": "^66.1.2", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "tailwindcss": "^3.3.5", "unocss": "^66.1.2"}}