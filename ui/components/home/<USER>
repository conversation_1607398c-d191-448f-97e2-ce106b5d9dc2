<template>
  <div class="pc-home-content">
    <!-- Hero 轮播 -->
    <section class="relative">
      <HeroCarousel v-if="heroSlides.length > 0" :slides="heroSlides" />
      <div v-else class="hero-skeleton">
        <div class="skeleton-banner"></div>
      </div>
    </section>

    <!-- 实时开箱记录 -->
    <LiveOpenings
      :key="`live-openings-${localeKey}`"
      class="mt-6 hover:shadow-neon-primary"
    />

    <!-- 折扣武器箱 -->
    <section class="border border-gray-800/30 shadow-xl mt-6 px-6 py-5 relative overflow-hidden transition-all duration-500 hover:shadow-neon-primary rounded-xl">
      <!-- 装饰背景元素 -->
      <div class="absolute -right-10 -top-10 w-40 h-40 rounded-full blur-3xl" style="background-color: rgba(255, 77, 0, 0.05)" />
      <div class="absolute -left-10 -bottom-10 w-32 h-32 rounded-full blur-3xl" style="background-color: rgba(0, 168, 255, 0.05)" />

      <div class="flex justify-between items-center mb-4 border-b border-gray-800/50 pb-4 relative z-10">
        <div class="flex items-center group">
          <!-- 图标容器 -->
          <div class="relative mr-3">
            <div class="w-10 h-10 rounded-xl bg-red-500/20 border border-red-400/30 flex items-center justify-center group-hover:scale-110 transition-all duration-300 backdrop-blur-sm">
              <i class="iconfont icon-xianshizhekou text-red-400 text-lg group-hover:animate-pulse"></i>
            </div>
            <!-- 装饰光点 -->
            <div class="absolute -top-1 -right-1 w-3 h-3 bg-red-400/60 rounded-full animate-ping"></div>
          </div>
          <!-- 标题文字 -->
          <div>
            <h2 class="text-lg font-bold text-white group-hover:text-red-400 transition-colors duration-300">
              {{ $t('cases.discount_cases') }}
            </h2>
            <p class="text-xs text-gray-400 mt-0.5">{{ $t('cases.discount_subtitle') }}</p>
          </div>
        </div>
        <NuxtLink
          to="/cases"
          class="group flex items-center gap-2 px-3 py-2 rounded-xl bg-red-500/10 border border-red-400/20 text-red-400 hover:bg-red-500/20 hover:border-red-400/40 transition-all duration-300"
        >
          <i class="iconfont icon-more2 text-sm transition-transform duration-300 group-hover:translate-x-1" />
        </NuxtLink>
      </div>

      <CaseCard v-if="discountCases.length > 0" :cases="discountCases" :num="5" section-type="discount" @case-click="handleCaseClick" />
      <div v-else class="cases-skeleton">
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
          <div v-for="i in 5" :key="i" class="skeleton-case-card"></div>
        </div>
      </div>
    </section>

    <!-- Hot武器箱 -->
    <section class="border border-gray-800/30 shadow-xl mt-6 px-6 py-5 relative overflow-hidden transition-all duration-500 hover:shadow-neon-primary rounded-xl">
      <!-- 装饰背景元素 -->
      <div class="absolute -left-10 -bottom-10 w-40 h-40 rounded-full blur-3xl" style="background-color: rgba(0, 168, 255, 0.05)" />
      <div class="absolute -right-10 -top-10 w-32 h-32 rounded-full blur-3xl" style="background-color: rgba(255, 77, 0, 0.05)" />

      <div class="flex justify-between items-center mb-4 border-b border-gray-800/50 pb-4 relative z-10">
        <div class="flex items-center group">
          <!-- 图标容器 -->
          <div class="relative mr-3">
            <div class="w-10 h-10 rounded-xl bg-orange-500/20 border border-orange-400/30 flex items-center justify-center group-hover:scale-110 transition-all duration-300 backdrop-blur-sm">
              <i class="iconfont icon-remen2 text-orange-400 text-lg group-hover:animate-pulse"></i>
            </div>
            <!-- 装饰光点 -->
            <div class="absolute -top-1 -right-1 w-3 h-3 bg-orange-400/60 rounded-full animate-ping"></div>
          </div>
          <!-- 标题文字 -->
          <div>
            <h2 class="text-lg font-bold text-white group-hover:text-orange-400 transition-colors duration-300">
              {{ $t('cases.hot_cases') }}
            </h2>
            <p class="text-xs text-gray-400 mt-0.5">{{ $t('cases.hot_subtitle') }}</p>
          </div>
        </div>
        <NuxtLink
          to="/cases"
          class="group flex items-center gap-2 px-3 py-2 rounded-xl bg-orange-500/10 border border-orange-400/20 text-orange-400 hover:bg-orange-500/20 hover:border-orange-400/40 transition-all duration-300"
        >
          <i class="iconfont icon-more2 text-sm transition-transform duration-300 group-hover:translate-x-1" />
        </NuxtLink>
      </div>

      <CaseCard v-if="hotCases.length > 0" :cases="hotCases" :num="5" section-type="hot" @case-click="handleCaseClick" />
      <div v-else class="cases-skeleton">
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
          <div v-for="i in 5" :key="i" class="skeleton-case-card"></div>
        </div>
      </div>
    </section>

    <!-- New武器箱 -->
    <section class="border border-gray-800/30 shadow-xl mt-6 px-6 py-5 relative overflow-hidden transition-all duration-500 hover:shadow-neon-primary rounded-xl">
      <!-- 装饰背景元素 -->
      <div class="absolute -right-10 -top-10 w-40 h-40 rounded-full blur-3xl" style="background-color: rgba(255, 77, 0, 0.05)" />
      <div class="absolute -left-10 -bottom-10 w-32 h-32 rounded-full blur-3xl" style="background-color: rgba(0, 168, 255, 0.05)" />

      <div class="flex justify-between items-center mb-4 border-b border-gray-800/50 pb-4 relative z-10">
        <div class="flex items-center group">
          <!-- 图标容器 -->
          <div class="relative mr-3">
            <div class="w-10 h-10 rounded-xl bg-emerald-500/20 border border-emerald-400/30 flex items-center justify-center group-hover:scale-110 transition-all duration-300 backdrop-blur-sm">
              <i class="iconfont icon-new text-emerald-400 text-lg group-hover:animate-pulse"></i>
            </div>
            <!-- 装饰光点 -->
            <div class="absolute -top-1 -right-1 w-3 h-3 bg-emerald-400/60 rounded-full animate-ping"></div>
          </div>
          <!-- 标题文字 -->
          <div>
            <h2 class="text-lg font-bold text-white group-hover:text-emerald-400 transition-colors duration-300">
              {{ $t('cases.new_cases') }}
            </h2>
            <p class="text-xs text-gray-400 mt-0.5">{{ $t('cases.new_subtitle') }}</p>
          </div>
        </div>
        <NuxtLink
          to="/cases"
          class="group flex items-center gap-2 px-3 py-2 rounded-xl bg-emerald-500/10 border border-emerald-400/20 text-emerald-400 hover:bg-emerald-500/20 hover:border-emerald-400/40 transition-all duration-300"
        >
          <i class="iconfont icon-more2 text-sm transition-transform duration-300 group-hover:translate-x-1" />
        </NuxtLink>
      </div>

      <CaseCard v-if="newCases.length > 0" :cases="newCases" :num="5" section-type="new" @case-click="handleCaseClick" />
      <div v-else class="cases-skeleton">
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
          <div v-for="i in 5" :key="i" class="skeleton-case-card"></div>
        </div>
      </div>
    </section>

    <!-- 随机皮肤展示 -->
    <section class="border border-gray-800/30 shadow-xl mt-6 px-6 py-5 relative overflow-hidden transition-all duration-500 hover:shadow-neon-primary rounded-xl">
      <!-- 装饰背景元素 -->
      <div class="absolute -left-10 -top-10 w-40 h-40 rounded-full blur-3xl" style="background-color: rgba(0, 168, 255, 0.05)" />
      <div class="absolute -right-10 -bottom-10 w-32 h-32 rounded-full blur-3xl" style="background-color: rgba(255, 77, 0, 0.05)" />

      <div class="flex justify-between items-center mb-4 border-b border-gray-800/50 pb-4 relative z-10">
        <div class="flex items-center group">
          <!-- 图标容器 -->
          <div class="relative mr-3">
            <div class="w-10 h-10 rounded-xl bg-purple-500/20 border border-purple-400/30 flex items-center justify-center group-hover:scale-110 transition-all duration-300 backdrop-blur-sm">
              <i class="iconfont icon-24gl-list8 text-purple-400 text-lg group-hover:animate-pulse"></i>
            </div>
            <!-- 装饰光点 -->
            <div class="absolute -top-1 -right-1 w-3 h-3 bg-purple-400/60 rounded-full animate-ping"></div>
          </div>
          <!-- 标题文字 -->
          <div>
            <h2 class="text-lg font-bold text-white group-hover:text-purple-400 transition-colors duration-300">
              {{ $t('skins.csgoskins') }}
            </h2>
            <p class="text-xs text-gray-400 mt-0.5">{{ $t('skins.random_subtitle') }}</p>
          </div>
        </div>
        
        <NuxtLink
          to="/skins"
          class="group flex items-center gap-2 px-3 py-2 rounded-xl bg-purple-500/10 border border-purple-400/20 text-purple-400 hover:bg-purple-500/20 hover:border-purple-400/40 transition-all duration-300"
        >
         
          <i class="iconfont icon-more2 text-sm transition-transform duration-300 group-hover:translate-x-1" />
        </NuxtLink>

      </div>

      <div v-if="randomSkins.length > 0" class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
        <SkinCard
          v-for="skin in randomSkins.slice(0, 12)"
          :key="skin.id"
          :skin-item="skin"
          :show_rarityName="true"
          @click="handleSkinClick(skin)"
        />
      </div>
      <div v-else class="skins-skeleton">
        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
          <div v-for="i in 12" :key="i" class="skeleton-skin-card"></div>
        </div>
      </div>
    </section>

    <!-- 统计数据 -->
    <HomeStats class="mt-6" />
  </div>
</template>

<script setup lang="ts">
import type { BannerItem } from '~/services/site-api'
import type { CaseItem } from '~/types/case'
import type { SkinItem } from '~/types/skin'

// 导入组件
import LiveOpenings from '~/components/home/<USER>'
import HeroCarousel from '~/components/home/<USER>'
import HomeStats from '~/components/home/<USER>'

interface Props {
  heroSlides: BannerItem[]
  hotCases: CaseItem[]
  discountCases: CaseItem[]
  newCases: CaseItem[]
  randomSkins: SkinItem[]
}

const props = defineProps<Props>()

const emit = defineEmits<{
  refreshRandomSkins: []
}>()

// 处理箱子点击
const handleCaseClick = (caseData: CaseItem) => {
  navigateTo(`/cases/${caseData.case_key}`)
}

// 获取语言状态用于强制组件重新渲染
const { locale } = useI18n()
const localeKey = computed(() => locale.value)

// 处理皮肤点击
const handleSkinClick = (skin: SkinItem) => {
  // TODO: 实现皮肤详情页面
  console.log('皮肤点击:', skin)
}
</script>

<style lang="scss" scoped>
.pc-home-content {
  .loader {
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid #ff6b00;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .loader-inner {
    width: 12px;
    height: 12px;
    border: 2px solid transparent;
    border-top: 2px solid #6c5ce7;
    border-radius: 50%;
    animation: spin 0.8s linear infinite reverse;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .shadow-neon-primary {
    box-shadow: 0 0 20px rgba(255, 107, 0, 0.3);
  }

  .font-i18n {
    font-family: 'Inter', 'Noto Sans SC', sans-serif;
  }

  .text-text-secondary {
    color: rgba(255, 255, 255, 0.8);
  }

  .text-text-hint {
    color: rgba(255, 255, 255, 0.6);
  }

  .text-secondary-500 {
    color: #6c5ce7;
  }

  .text-secondary-400 {
    color: #a29bfe;
  }

  .text-primary-400 {
    color: #ff9500;
  }

  /* 骨架屏样式 */
  .hero-skeleton {
    .skeleton-banner {
      width: 100%;
      height: 500px;
      background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
      border-radius: 1rem;
      position: relative;
      overflow: hidden;
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.1),
          transparent
        );
        animation: skeleton-shimmer 2s infinite;
      }
    }
  }

  .cases-skeleton, .skins-skeleton {
    .skeleton-case-card {
      width: 100%;
      height: 200px;
      background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
      border-radius: 0.75rem;
      position: relative;
      overflow: hidden;
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.1),
          transparent
        );
        animation: skeleton-shimmer 2s infinite;
      }

      &::after {
        content: '📦';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 2rem;
        opacity: 0.3;
        animation: skeleton-pulse 2s ease-in-out infinite;
      }
    }

    .skeleton-skin-card {
      width: 100%;
      height: 120px;
      background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
      border-radius: 0.5rem;
      position: relative;
      overflow: hidden;
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.1),
          transparent
        );
        animation: skeleton-shimmer 2s infinite;
      }

      &::after {
        content: '🔫';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 1.5rem;
        opacity: 0.3;
        animation: skeleton-pulse 2s ease-in-out infinite;
      }
    }
  }

  /* 骨架屏动画 */
  @keyframes skeleton-shimmer {
    0% {
      left: -100%;
    }
    100% {
      left: 100%;
    }
  }

  @keyframes skeleton-pulse {
    0%, 100% {
      opacity: 0.3;
    }
    50% {
      opacity: 0.1;
    }
  }
}
</style> 