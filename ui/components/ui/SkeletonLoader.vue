<template>
  <div class="skeleton-loader" :class="[`skeleton-${type}`, { loading: isLoading }]">
    <template v-if="isLoading">
      <!-- 对战卡片骨架屏 -->
      <div v-if="type === 'battle-card'" class="battle-card-skeleton">
        <!-- 装饰性光效 -->
        <div class="absolute top-0 left-0 w-24 h-24 rounded-full blur-3xl bg-primary/5 animate-pulse-slow"></div>
        <div class="absolute bottom-0 right-0 w-24 h-24 rounded-full blur-3xl bg-secondary/5 animate-pulse-slow"></div>

        <!-- 骨架屏内容 -->
        <div class="p-6">
          <!-- 对战标题 -->
          <div class="skeleton-line h-5 w-3/4 mb-4"></div>

          <!-- 对战状态和玩家信息 -->
          <div class="flex justify-between items-center mb-6">
            <div class="skeleton-line h-4 w-20"></div>
            <div class="skeleton-line h-4 w-16"></div>
          </div>

          <!-- 玩家头像区域 -->
          <div class="flex gap-3 md:gap-4 mb-6 justify-center md:justify-start">
            <div class="skeleton-avatar w-10 h-10 md:w-12 md:h-12 rounded-full"></div>
            <div class="skeleton-avatar w-10 h-10 md:w-12 md:h-12 rounded-full"></div>
            <div class="skeleton-avatar w-10 h-10 md:w-12 md:h-12 rounded-full opacity-50"></div>
            <div class="skeleton-avatar w-10 h-10 md:w-12 md:h-12 rounded-full opacity-30 hidden sm:block"></div>
          </div>

          <!-- 箱子展示区域 -->
          <div class="grid grid-cols-2 gap-2 md:gap-3 mb-6">
            <div class="skeleton-case h-14 md:h-16 rounded-lg"></div>
            <div class="skeleton-case h-14 md:h-16 rounded-lg"></div>
            <div class="skeleton-case h-14 md:h-16 rounded-lg"></div>
            <div class="skeleton-case h-14 md:h-16 rounded-lg"></div>
          </div>

          <!-- 底部信息 -->
          <div class="flex justify-between items-center">
            <div class="skeleton-line h-4 w-20 md:w-24"></div>
            <div class="skeleton-button h-8 w-16 md:h-9 md:w-20 rounded-lg"></div>
          </div>
        </div>

        <!-- 加载动画覆盖层 -->
        <div class="absolute inset-0 flex items-center justify-center bg-gray-900/20">
          <CSGOLoader :text="loadingText" />
        </div>
      </div>

      <!-- Case卡片骨架屏 -->
      <div v-else-if="type === 'case-card'" class="skeleton-case-card">
        <div class="skeleton-image skeleton-pulse"></div>
        <div class="skeleton-content">
          <div class="skeleton-title skeleton-pulse"></div>
          <div class="skeleton-price skeleton-pulse"></div>
          <div class="skeleton-button skeleton-pulse"></div>
        </div>
      </div>

      <!-- Case列表骨架屏 -->
      <div v-else-if="type === 'case-list'" class="skeleton-case-list">
        <div v-for="i in count" :key="i" class="skeleton-case-item">
          <div class="skeleton-image skeleton-pulse"></div>
          <div class="skeleton-info">
            <div class="skeleton-name skeleton-pulse"></div>
            <div class="skeleton-price skeleton-pulse"></div>
          </div>
        </div>
      </div>

      <!-- 用户信息骨架屏 -->
      <div v-else-if="type === 'user-profile'" class="skeleton-profile">
        <div class="skeleton-avatar skeleton-pulse"></div>
        <div class="skeleton-user-info">
          <div class="skeleton-username skeleton-pulse"></div>
          <div class="skeleton-balance skeleton-pulse"></div>
          <div class="skeleton-level skeleton-pulse"></div>
        </div>
      </div>

      <!-- 列表项骨架屏 -->
      <div v-else-if="type === 'list-item'" class="skeleton-list">
        <div v-for="i in count" :key="i" class="skeleton-list-item">
          <div class="skeleton-line skeleton-pulse"></div>
        </div>
      </div>

      <!-- 自定义骨架屏 -->
      <div v-else-if="type === 'custom'" class="skeleton-custom">
        <slot name="skeleton" />
      </div>

      <!-- 默认通用骨架屏 -->
      <div v-else class="skeleton-default">
        <div class="skeleton-line h-6 w-3/4 mb-4 skeleton-pulse"></div>
        <div class="skeleton-line h-4 w-1/2 mb-2 skeleton-pulse"></div>
        <div class="skeleton-line h-4 w-2/3 skeleton-pulse"></div>
      </div>
    </template>

    <slot v-else />
  </div>
</template>

<script setup lang="ts">
interface Props {
  type: 'battle-card' | 'case-card' | 'case-list' | 'user-profile' | 'list-item' | 'custom' | 'default'
  isLoading: boolean
  count?: number
  loadingText?: string
}

const props = withDefaults(defineProps<Props>(), {
  count: 6,
  loadingText: ''
})
</script>

<style lang="scss" scoped>
// CSS 变量定义
:root {
  --skeleton-base: rgba(255, 255, 255, 0.1);
  --skeleton-highlight: rgba(255, 255, 255, 0.2);
}

// 基础骨架屏动画
.skeleton-pulse {
  background: linear-gradient(90deg, 
    var(--skeleton-base) 25%, 
    var(--skeleton-highlight) 50%, 
    var(--skeleton-base) 75%
  );
  background-size: 200% 100%;
  border-radius: 4px;
  animation: skeleton-pulse 1.5s ease-in-out infinite;
}

@keyframes skeleton-pulse {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

// 骨架屏样式
.skeleton-line {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.1) 100%
  );
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s ease-in-out infinite;
  border-radius: 0.25rem;
}

.skeleton-avatar {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(var(--color-primary-rgb), 0.2) 50%,
    rgba(255, 255, 255, 0.1) 100%
  );
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.skeleton-case {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.08) 0%,
    rgba(var(--color-secondary-rgb), 0.15) 50%,
    rgba(255, 255, 255, 0.08) 100%
  );
  background-size: 200% 100%;
  animation: skeleton-loading 1.8s ease-in-out infinite;
  border-radius: 0.5rem;
}

.skeleton-button {
  background: linear-gradient(
    90deg,
    rgba(var(--color-primary-rgb), 0.1) 0%,
    rgba(var(--color-primary-rgb), 0.3) 50%,
    rgba(var(--color-primary-rgb), 0.1) 100%
  );
  background-size: 200% 100%;
  animation: skeleton-loading 2s ease-in-out infinite;
}

@keyframes skeleton-loading {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

// 对战卡片骨架屏
.battle-card-skeleton {
  min-height: 320px;
  background: rgba(31, 41, 55, 0.8);
  border-radius: 0.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px);
  position: relative;
  overflow: hidden;

  @media (max-width: 768px) {
    min-height: 280px;
  }
}

// Case卡片骨架屏
.skeleton-case-card {
  .skeleton-image {
    width: 100%;
    height: 200px;
    border-radius: 8px;
    margin-bottom: 12px;
  }
  
  .skeleton-title {
    height: 20px;
    margin-bottom: 8px;
  }
  
  .skeleton-price {
    height: 24px;
    width: 80px;
    margin-bottom: 8px;
  }
  
  .skeleton-button {
    height: 36px;
    width: 100%;
    border-radius: 6px;
  }
}

// Case列表骨架屏
.skeleton-case-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  
  @media (max-width: 768px) {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 12px;
  }

  .skeleton-case-item {
    .skeleton-image {
      width: 100%;
      height: 160px;
      border-radius: 8px;
      margin-bottom: 8px;
    }
    
    .skeleton-info {
      .skeleton-name {
        height: 16px;
        margin-bottom: 4px;
      }
      
      .skeleton-price {
        height: 20px;
        width: 60%;
      }
    }
  }
}

// 用户信息骨架屏
.skeleton-profile {
  display: flex;
  align-items: center;
  gap: 16px;
  
  .skeleton-avatar {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    flex-shrink: 0;
  }
  
  .skeleton-user-info {
    flex: 1;
    
    .skeleton-username {
      height: 24px;
      width: 120px;
      margin-bottom: 8px;
    }
    
    .skeleton-balance {
      height: 20px;
      width: 80px;
      margin-bottom: 4px;
    }
    
    .skeleton-level {
      height: 16px;
      width: 60px;
    }
  }
}

// 列表项骨架屏
.skeleton-list {
  .skeleton-list-item {
    margin-bottom: 12px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

// 慢脉冲动画
.animate-pulse-slow {
  animation: pulse-slow 8s ease-in-out infinite;
}

@keyframes pulse-slow {
  0%, 100% { opacity: 0.4; }
  50% { opacity: 0.7; }
}
</style>
