/**
 * 语言管理可组合函数
 * 简化版本，确保语言切换立即生效
 */
export const useLanguage = () => {
  // 获取i18n实例
  const { locale, setLocale, locales } = useI18n()
  
  // 语言持久化键名
  const LANGUAGE_STORAGE_KEY = 'user-locale'
  
  // 直接返回i18n的locale引用，确保同步
  const currentLocale = locale
  
  // 可用语言列表
  const availableLocales = computed(() => locales.value)
  
  // 切换语言 - 最简化版本
  const switchLanguage = async (newLocale: string): Promise<void> => {
    if (newLocale === currentLocale.value) {
      return
    }
    
    if (newLocale !== 'en' && newLocale !== 'zh-hans') {
      console.warn('不支持的语言:', newLocale)
      return
    }
    
    try {
      console.log(`切换语言: ${currentLocale.value} -> ${newLocale}`)
      
      // 使用官方的setLocale方法
      await setLocale(newLocale)
      
      // 保存到localStorage
      if (process.client) {
        localStorage.setItem(LANGUAGE_STORAGE_KEY, newLocale)
        document.documentElement.setAttribute('lang', newLocale)
      }
      
      console.log(`语言切换完成: ${locale.value}`)
      
    } catch (error) {
      console.error('语言切换失败:', error)
      throw error
    }
  }
  
  // 恢复语言设置
  const restoreLanguage = (): void => {
    if (!process.client) return
    
    try {
      const savedLocale = localStorage.getItem(LANGUAGE_STORAGE_KEY)
      if (savedLocale && (savedLocale === 'en' || savedLocale === 'zh-hans')) {
        if (savedLocale !== locale.value) {
          console.log(`恢复语言设置: ${savedLocale}`)
          setLocale(savedLocale)
        }
      }
    } catch (error) {
      console.error('恢复语言设置失败:', error)
    }
  }
  
  return {
    // 状态
    currentLocale,
    availableLocales,
    
    // 方法
    switchLanguage,
    restoreLanguage
  }
} 