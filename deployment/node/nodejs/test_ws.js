/**
 * 简化的WebSocket测试服务器
 */

const http = require('http');
const { Server } = require('socket.io');

console.log('启动测试WebSocket服务器...');

const server = http.createServer();
const io = new Server(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

// 连接计数
let connectionCount = 0;

io.on('connection', (socket) => {
  connectionCount++;
  console.log(`[${new Date().toISOString()}] 新连接: ${socket.id}, 总连接数: ${connectionCount}`);

  // 监听所有事件
  socket.onAny((event, ...args) => {
    console.log(`[${socket.id}] 收到事件: ${event}`, args);
  });

  // 基本事件处理
  socket.on('join', (room) => {
    console.log(`[${socket.id}] 加入房间: ${room}`);
    socket.join(room);
    socket.emit('joined', room);
  });

  socket.on('monitor', (data) => {
    console.log(`[${socket.id}] 监控请求:`, data);
    
    // 模拟监控数据
    const mockData = {
      user_number: Math.floor(Math.random() * 1000) + 500,
      case_number: Math.floor(Math.random() * 5000) + 1000,
      online_number: Math.floor(Math.random() * 100) + 50,
      battle_number: Math.floor(Math.random() * 50) + 10
    };
    
    socket.emit('monitor', ['update', 'monitor', mockData]);
  });

  socket.on('case_records', () => {
    console.log(`[${socket.id}] 开箱记录请求`);
    
    // 模拟开箱记录
    const mockRecord = {
      id: Date.now(),
      user_info: { username: 'test_user' },
      item_info: { name: 'AK-47 | Redline', rarity: 'Classified' },
      case_info: { name: 'Chroma Case', key: 'chroma_case' },
      created_at: new Date().toISOString()
    };
    
    socket.emit('case_records', ['new', 'case_records', mockRecord]);
  });

  socket.on('disconnect', (reason) => {
    connectionCount--;
    console.log(`[${new Date().toISOString()}] 连接断开: ${socket.id}, 原因: ${reason}, 剩余连接: ${connectionCount}`);
  });
});

// 健康检查端点
server.on('request', (req, res) => {
  if (req.url === '/health') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      status: 'ok',
      connections: connectionCount,
      timestamp: new Date().toISOString()
    }));
  } else {
    res.writeHead(404);
    res.end('Not Found');
  }
});

// 启动服务器
const PORT = process.env.PORT || 4000;
server.listen(PORT, () => {
  console.log(`[${new Date().toISOString()}] WebSocket测试服务器启动成功`);
  console.log(`监听端口: ${PORT}`);
  console.log(`健康检查: http://localhost:${PORT}/health`);
});

// 定期发送模拟数据
setInterval(() => {
  if (connectionCount > 0) {
    const mockData = {
      user_number: Math.floor(Math.random() * 1000) + 500,
      case_number: Math.floor(Math.random() * 5000) + 1000,
      online_number: connectionCount,
      battle_number: Math.floor(Math.random() * 50) + 10
    };
    
    io.emit('monitor', ['update', 'monitor', mockData]);
    console.log(`[${new Date().toISOString()}] 广播监控数据到 ${connectionCount} 个连接`);
  }
}, 5000);

// 错误处理
process.on('uncaughtException', (err) => {
  console.error('未捕获的异常:', err);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason);
});

console.log('WebSocket测试服务器配置完成，等待连接...');
