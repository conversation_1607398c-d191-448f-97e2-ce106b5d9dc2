<template>
  <div class="flip-countdown-container" ref="countdownContainer">
    <div class="flip-clock countdown-clock" :style="countdownScaleStyle">
      <!-- 天数十位（静态显示） -->
      <div class="time-box">
        <div class="divider-bg"></div>
        <div class="divider-line"></div>
        <div class="base">
          {{ flipCountdownDays.toString().padStart(2, '0')[0] }}
          <div class="base-b">{{ flipCountdownDays.toString().padStart(2, '0')[0] }}</div>
        </div>
        <div class="face">
          {{ flipCountdownDays.toString().padStart(2, '0')[0] }}
        </div>
        <div class="back">
          {{ flipCountdownDays.toString().padStart(2, '0')[0] }}
        </div>
      </div>
      <!-- 天数个位（动态翻页） -->
      <div class="time-box">
        <div class="divider-bg"></div>
        <div class="divider-line"></div>
        <div class="base">
          {{ flipCountdownDays.toString().padStart(2, '0')[1] }}
          <div class="base-b">{{ flipCountdownDays.toString().padStart(2, '0')[1] }}</div>
        </div>
        <div 
          :class="['face', { anime: flipCountdownDaysTrigger }]" 
          @animationend="onFlipCountdownAnimateEnd(0)"
        >
          {{ flipCountdownDays.toString().padStart(2, '0')[1] }}
        </div>
        <div :class="['back', { anime: flipCountdownDaysTrigger }]">
          {{ flipCountdownDays.toString().padStart(2, '0')[1] }}
        </div>
      </div>
      <div class="time-unit">{{ t('time.days') }}</div>
      
      <!-- 小时十位（静态显示） -->
      <div class="time-box">
        <div class="divider-bg"></div>
        <div class="divider-line"></div>
        <div class="base">
          {{ flipCountdownHours.toString().padStart(2, '0')[0] }}
          <div class="base-b">{{ flipCountdownHours.toString().padStart(2, '0')[0] }}</div>
        </div>
        <div class="face">
          {{ flipCountdownHours.toString().padStart(2, '0')[0] }}
        </div>
        <div class="back">
          {{ flipCountdownHours.toString().padStart(2, '0')[0] }}
        </div>
      </div>
      <!-- 小时个位（动态翻页） -->
      <div class="time-box">
        <div class="divider-bg"></div>
        <div class="divider-line"></div>
        <div class="base">
          {{ flipCountdownHours.toString().padStart(2, '0')[1] }}
          <div class="base-b">{{ flipCountdownHours.toString().padStart(2, '0')[1] }}</div>
        </div>
        <div 
          :class="['face', { anime: flipCountdownHoursTrigger }]" 
          @animationend="onFlipCountdownAnimateEnd(1)"
        >
          {{ flipCountdownHours.toString().padStart(2, '0')[1] }}
        </div>
        <div :class="['back', { anime: flipCountdownHoursTrigger }]">
          {{ flipCountdownHours.toString().padStart(2, '0')[1] }}
        </div>
      </div>
      <div class="time-unit">{{ t('time.hours') }}</div>
      
      <!-- 分钟十位（静态显示） -->
      <div class="time-box">
        <div class="divider-bg"></div>
        <div class="divider-line"></div>
        <div class="base">
          {{ flipCountdownMinutes.toString().padStart(2, '0')[0] }}
          <div class="base-b">{{ flipCountdownMinutes.toString().padStart(2, '0')[0] }}</div>
        </div>
        <div class="face">
          {{ flipCountdownMinutes.toString().padStart(2, '0')[0] }}
        </div>
        <div class="back">
          {{ flipCountdownMinutes.toString().padStart(2, '0')[0] }}
        </div>
      </div>
      <!-- 分钟个位（动态翻页） -->
      <div class="time-box">
        <div class="divider-bg"></div>
        <div class="divider-line"></div>
        <div class="base">
          {{ flipCountdownMinutes.toString().padStart(2, '0')[1] }}
          <div class="base-b">{{ flipCountdownMinutes.toString().padStart(2, '0')[1] }}</div>
        </div>
        <div 
          :class="['face', { anime: flipCountdownMinutesTrigger }]" 
          @animationend="onFlipCountdownAnimateEnd(2)"
        >
          {{ flipCountdownMinutes.toString().padStart(2, '0')[1] }}
        </div>
        <div :class="['back', { anime: flipCountdownMinutesTrigger }]">
          {{ flipCountdownMinutes.toString().padStart(2, '0')[1] }}
        </div>
      </div>
      <div class="time-unit">{{ t('time.minutes') }}</div>
      
      <!-- 秒钟十位（静态显示） -->
      <div class="time-box">
        <div class="divider-bg"></div>
        <div class="divider-line"></div>
        <div class="base">
          {{ flipCountdownSeconds.toString().padStart(2, '0')[0] }}
          <div class="base-b">{{ flipCountdownSeconds.toString().padStart(2, '0')[0] }}</div>
        </div>
        <div class="face">
          {{ flipCountdownSeconds.toString().padStart(2, '0')[0] }}
        </div>
        <div class="back">
          {{ flipCountdownSeconds.toString().padStart(2, '0')[0] }}
        </div>
      </div>
      <!-- 秒钟个位（动态翻页） -->
      <div class="time-box">
        <div class="divider-bg"></div>
        <div class="divider-line"></div>
        <div class="base">
          {{ flipCountdownSeconds.toString().padStart(2, '0')[1] }}
          <div class="base-b">{{ flipCountdownSeconds.toString().padStart(2, '0')[1] }}</div>
        </div>
        <div 
          :class="['face', { anime: flipCountdownSecondsTrigger }]" 
          @animationend="onFlipCountdownAnimateEnd(3)"
        >
          {{ flipCountdownSeconds.toString().padStart(2, '0')[1] }}
        </div>
        <div :class="['back', { anime: flipCountdownSecondsTrigger }]">
          {{ flipCountdownSeconds.toString().padStart(2, '0')[1] }}
        </div>
      </div>
      <div class="time-unit">{{ t('time.seconds') }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, nextTick, watch } from 'vue'

// 国际化
const { t } = useI18n()

// Props
interface Props {
  seconds: number
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits(['end', 'tick'])

// 响应式变量
const flipCountdownDays = ref(0)
const flipCountdownHours = ref(0)
const flipCountdownMinutes = ref(0)
const flipCountdownSeconds = ref(0)

// 翻页倒计时触发器
const flipCountdownDaysTrigger = ref(false)
const flipCountdownHoursTrigger = ref(false)
const flipCountdownMinutesTrigger = ref(false)
const flipCountdownSecondsTrigger = ref(false)

// 倒计时容器引用
const countdownContainer = ref<HTMLElement>()

let flipCountdownInterval: ReturnType<typeof setInterval> | null = null

// 倒计时缩放样式计算属性
const countdownScaleStyle = computed(() => {
  if (!countdownContainer.value) return {}
  
  const containerWidth = countdownContainer.value.offsetWidth
  const contentWidth = 800 // 倒计时内容的理想宽度
  
  if (containerWidth >= contentWidth) {
    return { transform: 'scale(1)', transformOrigin: 'center' }
  }
  
  const scale = Math.max(0.4, containerWidth / contentWidth)
  return {
    transform: `scale(${scale})`,
    transformOrigin: 'center'
  }
})

// 翻页倒计时动画结束处理
const onFlipCountdownAnimateEnd = (index: number) => {
  // 动画结束后重置触发器
  if (index === 0) {
    flipCountdownDaysTrigger.value = false
  } else if (index === 1) {
    flipCountdownHoursTrigger.value = false
  } else if (index === 2) {
    flipCountdownMinutesTrigger.value = false
  } else if (index === 3) {
    flipCountdownSecondsTrigger.value = false
  }
  
  emit('animationEnd', index)
}

// 触发翻页动画
const triggerFlipAnimation = (type: 'days' | 'hours' | 'minutes' | 'seconds') => {
  switch (type) {
    case 'days':
      flipCountdownDaysTrigger.value = true
      setTimeout(() => flipCountdownDaysTrigger.value = false, 600)
      break
    case 'hours':
      flipCountdownHoursTrigger.value = true
      setTimeout(() => flipCountdownHoursTrigger.value = false, 600)
      break
    case 'minutes':
      flipCountdownMinutesTrigger.value = true
      setTimeout(() => flipCountdownMinutesTrigger.value = false, 600)
      break
    case 'seconds':
      flipCountdownSecondsTrigger.value = true
      setTimeout(() => flipCountdownSecondsTrigger.value = false, 600)
      break
  }
}

// 开始倒计时
const startCountdown = (totalSeconds: number) => {
  // 计算各个时间单位
  const days = Math.floor(totalSeconds / 86400)
  const hours = Math.floor((totalSeconds % 86400) / 3600)
  const minutes = Math.floor((totalSeconds % 3600) / 60)
  const seconds = totalSeconds % 60
  
  // 更新倒计时数据
  flipCountdownDays.value = days
  flipCountdownHours.value = hours
  flipCountdownMinutes.value = minutes
  flipCountdownSeconds.value = seconds
  
  if (flipCountdownInterval) {
    clearInterval(flipCountdownInterval)
  }
  
  flipCountdownInterval = setInterval(() => {
    // 检查是否结束
    if (flipCountdownDays.value === 0 && flipCountdownHours.value === 0 && flipCountdownMinutes.value === 0 && flipCountdownSeconds.value === 0) {
      stopCountdown()
      emit('end')
      return
    }
    
    // 处理秒钟
    if (flipCountdownSeconds.value > 0) {
      flipCountdownSeconds.value--
      triggerFlipAnimation('seconds')
    } else {
      flipCountdownSeconds.value = 59
      triggerFlipAnimation('seconds')
      
      // 处理分钟
      if (flipCountdownMinutes.value > 0) {
        flipCountdownMinutes.value--
        triggerFlipAnimation('minutes')
      } else {
        flipCountdownMinutes.value = 59
        triggerFlipAnimation('minutes')
        
        // 处理小时
        if (flipCountdownHours.value > 0) {
          flipCountdownHours.value--
          triggerFlipAnimation('hours')
        } else {
          flipCountdownHours.value = 23
          triggerFlipAnimation('hours')
          
          // 处理天数
          if (flipCountdownDays.value > 0) {
            flipCountdownDays.value--
            triggerFlipAnimation('days')
          }
        }
      }
    }
    
    // 发送更新事件
    emit('tick', { days: flipCountdownDays.value, hours: flipCountdownHours.value, minutes: flipCountdownMinutes.value, seconds: flipCountdownSeconds.value })
  }, 1000)
}

// 停止倒计时
const stopCountdown = () => {
  if (flipCountdownInterval) {
    clearInterval(flipCountdownInterval)
    flipCountdownInterval = null
  }
  flipCountdownDays.value = 0
  flipCountdownHours.value = 0
  flipCountdownMinutes.value = 0
  flipCountdownSeconds.value = 0
}

// 设置倒计时时间
const setCountdown = (days: number, hours: number, minutes: number, seconds: number) => {
  flipCountdownDays.value = days
  flipCountdownHours.value = hours
  flipCountdownMinutes.value = minutes
  flipCountdownSeconds.value = seconds
}

// 暴露方法给父组件
defineExpose({
  startCountdown,
  stopCountdown,
  setCountdown
})

// 生命周期
onMounted(() => {
  // 初始化倒计时器显示
  flipCountdownDays.value = 0
  flipCountdownHours.value = 0
  flipCountdownMinutes.value = 0
  flipCountdownSeconds.value = 0
  
  // 监听窗口大小变化，重新计算缩放
  const handleResize = () => {
    nextTick(() => {
      // 触发计算属性重新计算
    })
  }
  
  window.addEventListener('resize', handleResize)
  
  // 清理函数
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
  })
  
  // 如果设置了初始时间，自动开始倒计时
  if (props.seconds > 0) {
    startCountdown(props.seconds)
  }
})

onUnmounted(() => {
  if (flipCountdownInterval) {
    clearInterval(flipCountdownInterval)
  }
})

watch(() => props.seconds, (val) => {
  // 计算天、小时、分钟、秒
  let s = Math.max(0, val)
  flipCountdownDays.value = Math.floor(s / 86400)
  s %= 86400
  flipCountdownHours.value = Math.floor(s / 3600)
  s %= 3600
  flipCountdownMinutes.value = Math.floor(s / 60)
  flipCountdownSeconds.value = s % 60
  emit('tick', { days: flipCountdownDays.value, hours: flipCountdownHours.value, minutes: flipCountdownMinutes.value, seconds: flipCountdownSeconds.value })
  if (val === 0) emit('end')
}, { immediate: true })
</script>

<style lang="scss" scoped>
.flip-countdown-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 1rem 0;
  overflow: hidden;
  
  .flip-clock {
    max-width: 100%;
    
    // 确保倒计时器始终在一行
    &.countdown-clock {
      @apply flex-nowrap;
      min-width: max-content;
      transition: transform 0.3s ease;
      will-change: transform;
      backface-visibility: hidden;
      transform-style: preserve-3d;
    }
  }
}

.flip-clock {
  @apply flex items-center gap-1 flex-nowrap justify-center;
  font-family: 'SF Mono', 'Consolas', 'Menlo', monospace;
  
  .time-box {
    @apply relative box-border h-20 min-w-[3.5rem] text-center bg-gray-900 rounded-lg text-white;
    font-size: 1.75rem;
    perspective: 12.5rem;
    line-height: 5rem;
    box-shadow: 0 0.25rem 0.5rem rgba(0,0,0,0.3), 0 0.125rem 0.25rem rgba(0,0,0,0.2), inset 0 0.0625rem 0 rgba(255,255,255,0.1);
    border: 0.0625rem solid #333;
    will-change: transform;
    backface-visibility: hidden;
    transform-style: preserve-3d;
    
    &:before {
      content: "";
      @apply absolute bg-gray-900 w-1 h-4 top-1/2 -left-1 -mt-2 -z-10 rounded-l-sm;
      box-shadow: inset 0 0.0625rem 0 rgba(255,255,255,0.1);
    }
    &:after {
      content: "";
      @apply absolute bg-gray-900 w-1 h-4 top-1/2 -right-1 -mt-2 -z-10 rounded-r-sm;
      box-shadow: inset 0 0.0625rem 0 rgba(255,255,255,0.1);
    }
    .divider-bg {
      @apply absolute top-1/2 left-0 right-0 h-px bg-gray-700 z-10;
    }
    .divider-line {
      @apply absolute top-1/2 left-0 right-0 h-px z-20;
      background: linear-gradient(90deg, transparent, #666, transparent);
      box-shadow: 0 0 0.25rem rgba(255,255,255,0.1);
    }
    & > div {
      @apply overflow-hidden rounded-lg;
      animation-timing-function: cubic-bezier(0.4,0,0.2,1);
      animation-duration: 600ms;
      transform: rotateX(0deg);
      will-change: transform;
      backface-visibility: hidden;
      &.base {
        @apply relative;
        background: linear-gradient(180deg, #2a2a2a 0%, #1a1a1a 50%, #0a0a0a 100%);
        .base-b {
          @apply absolute left-0 bottom-0 rounded-b-lg w-full h-full;
          background: linear-gradient(180deg, #0a0a0a 0%, #1a1a1a 50%, #2a2a2a 100%);
          clip-path: polygon(0 50%, 100% 50%, 100% 100%, 0 100%);
        }
      }
      &.face {
        @apply absolute left-0 top-0 w-full h-full z-20;
        background: linear-gradient(180deg, #2a2a2a 0%, #1a1a1a 50%, #0a0a0a 100%);
        backface-visibility: hidden;
        clip-path: polygon(0 0, 100% 0, 100% 50%, 0 50%);
        box-shadow: inset 0 0.0625rem 0 rgba(255,255,255,0.1);
        will-change: transform;
        transform-origin: 50% 0%;
        &.anime {
          animation-name: animate-flip-face;
        }
      }
      &.back {
        @apply absolute left-0 top-0 w-full h-full;
        background: linear-gradient(180deg, #0a0a0a 0%, #1a1a1a 50%, #2a2a2a 100%);
        transform: rotateX(-180deg);
        backface-visibility: hidden;
        clip-path: polygon(0 50%, 100% 50%, 100% 100%, 0 100%);
        box-shadow: inset 0 -0.0625rem 0 rgba(255,255,255,0.1);
        will-change: transform;
        transform-origin: 50% 100%;
        &.anime {
          animation-name: animate-flip-back;
        }
      }
    }
  }
  .time-unit {
    @apply px-2 text-primary font-bold whitespace-nowrap;
    font-size: 1.5rem;
    line-height: 5rem;
    text-shadow: 0 0 0.625rem rgba(0,168,255,0.5), 0 0 1.25rem rgba(0,168,255,0.3);
    animation: separator-pulse 2s ease-in-out infinite;
  }
}

@keyframes animate-flip-face {
  0% { transform: rotateX(0deg); opacity: 1; }
  50% { transform: rotateX(-90deg); opacity: 0.5; }
  100% { transform: rotateX(-180deg); opacity: 0; }
}

@keyframes animate-flip-back {
  0% { transform: rotateX(180deg); opacity: 0; }
  50% { transform: rotateX(90deg); opacity: 0.5; }
  100% { transform: rotateX(0deg); opacity: 1; }
}

@keyframes separator-pulse {
  0%, 100% {
    opacity: 0.9;
    transform: scale(1);
    text-shadow: 0 0 10px rgba(0,168,255,0.5), 0 0 20px rgba(0,168,255,0.3);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.1);
    text-shadow: 0 0 15px rgba(0,168,255,0.7), 0 0 30px rgba(0,168,255,0.5), 0 0 45px rgba(0,168,255,0.3);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .flip-clock {
    @apply gap-0.5;
    
    .time-box {
      @apply h-16 min-w-[2.5rem] px-1;
      font-size: 1.25rem;
      line-height: 4rem;
      
      &:before, &:after {
        @apply w-0.5 h-3 -left-0.5 -right-0.5 -mt-1.5;
      }
    }
    
    .time-unit {
      @apply px-1;
      font-size: 1rem;
      line-height: 4rem;
    }
  }
}
</style> 