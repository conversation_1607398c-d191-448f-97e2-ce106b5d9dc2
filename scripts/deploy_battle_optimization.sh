#!/bin/bash

# 开箱对战系统优化部署和验证脚本
# 作者: GitHub Copilot
# 日期: 2025-07-15

echo "======================================================================"
echo "🚀 开箱对战系统优化部署和验证脚本"
echo "======================================================================"

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数：打印状态
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 检查当前目录
if [ ! -f "docker-compose.yml" ]; then
    print_error "请在项目根目录下运行此脚本"
    exit 1
fi

print_info "开始部署优化..."

# 1. 检查Python版本
echo ""
echo "📋 步骤 1: 检查Python环境"
PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
print_info "当前Python版本: $PYTHON_VERSION"

if python3 -c "import sys; exit(0 if sys.version_info >= (3, 7) else 1)"; then
    print_status "Python版本支持完整异步功能"
else
    print_warning "Python版本较低，将使用兼容模式"
fi

# 2. 验证文件存在性
echo ""
echo "📋 步骤 2: 验证优化文件"
files=(
    "server/box/compat_async.py"
    "server/box/message_utils.py" 
    "server/box/system_fixes.py"
    "server/box/enhanced_battle_system.py"
    "server/box/battle_config.py"
    "server/box/battle_system_integration.py"
)

for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        print_status "文件存在: $file"
    else
        print_error "文件缺失: $file"
        exit 1
    fi
done

# 3. 检查Django配置
echo ""
echo "📋 步骤 3: 验证Django环境"
cd /www/wwwroot/csgoskins.com.cn

if python3 -c "
import sys
sys.path.insert(0, 'server')
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')
import django
django.setup()
print('Django配置正常')
" 2>/dev/null; then
    print_status "Django环境配置正常"
else
    print_error "Django环境配置失败"
    exit 1
fi

# 4. 运行系统健康检查
echo ""
echo "📋 步骤 4: 系统健康检查"
python3 -c "
import sys
sys.path.insert(0, 'server')
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')

try:
    import django
    django.setup()
    
    from box.enhanced_battle_system import enhanced_battle_manager
    health = enhanced_battle_manager.get_system_health()
    
    print(f'系统状态: {health[\"status\"]}')
    
    if 'details' in health:
        details = health['details']
        print(f'Python版本: {details.get(\"python_version\", \"unknown\")}')
        print(f'异步支持: {details.get(\"async_support\", \"unknown\")}') 
        print(f'数据库: {details.get(\"database\", \"unknown\")}')
        print(f'Redis: {details.get(\"redis\", \"unknown\")}')
        
        if 'warnings' in details:
            print(f'警告: {details[\"warnings\"]}')
    
    if health['status'] in ['healthy', 'warning']:
        print('HEALTH_CHECK_PASSED')
    else:
        print('HEALTH_CHECK_FAILED')
        
except Exception as e:
    print(f'健康检查失败: {e}')
    print('HEALTH_CHECK_FAILED')
" > /tmp/health_check.log 2>&1

if grep -q "HEALTH_CHECK_PASSED" /tmp/health_check.log; then
    print_status "系统健康检查通过"
    cat /tmp/health_check.log | grep -v "HEALTH_CHECK_PASSED"
else
    print_error "系统健康检查失败"
    cat /tmp/health_check.log
    exit 1
fi

# 5. 功能模块测试
echo ""
echo "📋 步骤 5: 功能模块测试"

python3 -c "
import sys
sys.path.insert(0, 'server')
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')

try:
    import django
    django.setup()
    
    # 测试异步兼容性
    from box.compat_async import get_async_processor
    processor = get_async_processor()
    print('✅ 异步处理器正常')
    
    # 测试消息处理
    from box.message_utils import sanitize_websocket_data
    test_data = {'test': ['data'], 'number': 123}
    cleaned = sanitize_websocket_data(test_data)
    print('✅ 消息处理功能正常')
    
    # 测试配置管理
    from box.battle_config import BattleSystemConfig
    config = BattleSystemConfig()
    print('✅ 配置管理功能正常')
    
    print('MODULE_TEST_PASSED')
    
except Exception as e:
    print(f'模块测试失败: {e}')
    print('MODULE_TEST_FAILED')
" > /tmp/module_test.log 2>&1

if grep -q "MODULE_TEST_PASSED" /tmp/module_test.log; then
    print_status "功能模块测试通过"
    cat /tmp/module_test.log | grep -v "MODULE_TEST_PASSED"
else
    print_error "功能模块测试失败"
    cat /tmp/module_test.log
    exit 1
fi

# 6. 清理临时文件
rm -f /tmp/health_check.log /tmp/module_test.log

# 7. 部署完成
echo ""
echo "======================================================================"
echo "🎉 开箱对战系统优化部署完成！"
echo "======================================================================"
print_status "所有验证测试通过"
print_info "系统现在已经优化并可以稳定运行"
print_info "优化报告位置: docs/fixes/battle-system-optimization-report.md"

echo ""
echo "📋 后续建议："
print_warning "1. 定期运行健康检查监控系统状态"
print_warning "2. 考虑升级Python到3.8+以获得更好性能" 
print_warning "3. 监控系统日志，确保优化效果持续"

echo ""
echo "🔧 快速命令："
echo "  健康检查: python3 scripts/battle_system_monitor.py --mode diagnosis"
echo "  性能监控: python3 scripts/battle_system_monitor.py --mode monitor"

exit 0
