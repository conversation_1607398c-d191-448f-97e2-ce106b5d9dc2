<template>
  <div class="p-8">
    <h1 class="text-2xl font-bold mb-6">🔌 Socket.IO 连接测试</h1>
    
    <div class="space-y-4">
      <div class="bg-red-100 p-4 rounded">
        <h2 class="font-bold mb-2">⚠️ 重要提示:</h2>
        <p class="text-sm">
          如果您仍然看到 CORS 错误，请：<br>
          1. 硬刷新浏览器 (Ctrl+Shift+R 或 Cmd+Shift+R)<br>
          2. 清除浏览器缓存<br>
          3. 或者打开隐私/无痕浏览窗口
        </p>
      </div>
      
      <div class="bg-gray-100 p-4 rounded">
        <h2 class="font-bold mb-2">当前配置:</h2>
        <div class="text-sm font-mono">
          <div>Socket URL: {{ socketUrl }}</div>
          <div>开发环境: {{ isDev }}</div>
          <div>EIO 版本: 4</div>
          <div>传输方式: polling, websocket</div>
          <div>路径: /socket.io/</div>
        </div>
      </div>
      
      <div class="bg-blue-100 p-4 rounded">
        <h2 class="font-bold mb-2">连接状态:</h2>
        <div :class="connectionStatus.color" class="text-sm font-mono">
          {{ connectionStatus.text }}
        </div>
      </div>
      
      <div class="bg-green-100 p-4 rounded">
        <h2 class="font-bold mb-2">连接日志:</h2>
        <div class="text-sm font-mono max-h-40 overflow-y-auto">
          <div v-for="(log, index) in logs" :key="index" class="mb-1">
            {{ log }}
          </div>
        </div>
      </div>
      
      <div class="bg-yellow-100 p-4 rounded">
        <h2 class="font-bold mb-2">测试操作:</h2>
        <button @click="testConnection" class="bg-blue-500 text-white px-4 py-2 rounded mr-2">
          强制重新连接
        </button>
        <button @click="clearLogs" class="bg-gray-500 text-white px-4 py-2 rounded">
          清空日志
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const config = useRuntimeConfig()
const logs = ref<string[]>([])
const isConnected = ref(false)
const socketUrl = ref('')
const isDev = ref(false)

const connectionStatus = computed(() => {
  if (isConnected.value) {
    return { text: '✅ 已连接', color: 'text-green-600' }
  } else {
    return { text: '❌ 未连接', color: 'text-red-600' }
  }
})

function addLog(message: string) {
  const timestamp = new Date().toLocaleTimeString()
  logs.value.push(`[${timestamp}] ${message}`)
  if (logs.value.length > 20) {
    logs.value.shift()
  }
}

function clearLogs() {
  logs.value = []
}

async function testConnection() {
  try {
    addLog('🚀 开始强制 Socket.IO 连接测试...')
    
    // 强制重新导入 Socket.IO
    const io = (await import('socket.io-client')).default
    addLog('✅ Socket.IO 客户端重新导入成功')
    
    // 检查环境
    isDev.value = process.env.NODE_ENV === 'development'

    // 开发环境使用本地代理，生产环境使用服务器地址
    const url = isDev.value
      ? window.location.origin  // 开发环境使用本地代理
      : (config.public.socketUrl || 'https://socket.cs2.net.cn')
    socketUrl.value = url

    addLog(`🔗 连接地址: ${url}`)
    addLog(`🔧 开发环境: ${isDev.value}`)
    addLog(`🔧 EIO 版本: 4`)
    
    // 创建连接 - 强制使用正确的配置
    const socket = io(url, {
      transports: ['polling', 'websocket'],
      forceNew: true,  // 强制新连接
      reconnection: false,
      timeout: 10000,
      autoConnect: true,
      path: '/socket.io/',
      query: {
        page: 'ws',
        EIO: '4',  // 强制 EIO v4
        t: Date.now().toString()  // 添加时间戳避免缓存
      },
      upgrade: true,
      rememberUpgrade: false
    })
    
    addLog('🔌 Socket.IO 实例创建完成，强制使用新配置')
    
    socket.on('connect', () => {
      isConnected.value = true
      addLog(`🎉 连接成功! Socket ID: ${socket.id}`)
      const transportName = (socket as any).io?.engine?.transport?.name || 'unknown'
      addLog(`🚀 传输方式: ${transportName}`)
      const actualUri = (socket as any).io?.uri || 'unknown'
      addLog(`🌐 实际连接地址: ${actualUri}`)
    })
    
    socket.on('disconnect', (reason: any) => {
      isConnected.value = false
      addLog(`🔴 连接断开: ${reason}`)
    })
    
    socket.on('connect_error', (error: any) => {
      isConnected.value = false
      addLog(`❌ 连接错误: ${error.message}`)
      addLog(`❌ 错误详情: ${error.description || 'N/A'}`)
      addLog(`❌ 错误类型: ${error.type || 'N/A'}`)
      
      // 检查是否仍在使用旧配置
      if (error.message && error.message.includes('socket.cs2.net.cn')) {
        addLog('⚠️ 检测到仍在使用旧配置！请清除浏览器缓存！')
      }
    })
    
    // 10秒后断开连接
    setTimeout(() => {
      if (socket.connected) {
        socket.disconnect()
        addLog('🔌 主动断开连接')
      }
    }, 10000)
    
  } catch (error: any) {
    addLog(`💥 测试失败: ${error.message}`)
  }
}

// 页面加载时自动测试
onMounted(() => {
  addLog('📄 页面加载完成，开始自动测试...')
  setTimeout(() => {
    testConnection()
  }, 1000)
})
</script>
