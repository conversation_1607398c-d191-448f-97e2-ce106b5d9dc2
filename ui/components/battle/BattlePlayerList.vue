<template>
  <div class="bg-gradient-to-br from-slate-900/80 to-slate-800/60 backdrop-blur-xl border border-white/20 rounded-2xl p-6 shadow-xl relative overflow-hidden">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 bg-gradient-to-br from-cyan-400/5 to-blue-400/5"></div>
    <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-cyan-400 to-blue-400"></div>
    
    <!-- 标题 -->
    <div class="flex items-center justify-between mb-6 relative z-10">
      <div class="flex items-center gap-3">
        <div class="w-8 h-8 bg-gradient-to-r from-cyan-400 to-blue-400 rounded-lg flex items-center justify-center">
          <Icon name="material-symbols:group" class="w-5 h-5 text-white" />
        </div>
        <h3 class="title-i18n text-2xl font-bold text-white">
          {{ $t('battle.players') }}
        </h3>
      </div>
      <div class="flex items-center gap-2 bg-green-400/10 px-3 py-1 rounded-full border border-green-400/30">
        <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
        <span class="text-green-400 text-sm font-medium">{{ getJoinedPlayerCount() }}/{{ maxPlayers }}</span>
      </div>
    </div>

    <!-- 玩家列表 - 根据数量平分页面空间 -->
    <div 
      class="grid gap-4"
      :class="{
        'grid-cols-1': players.length === 1,
        'grid-cols-2': players.length === 2,
        'grid-cols-3': players.length === 3,
        'grid-cols-4': players.length === 4
      }"
    >
      <div 
        v-for="(player, idx) in players" 
        :key="player.id"
        class="relative group"
        :class="{
          'animate-pulse': player.isPlaceholder && showJoinButton
        }"
      >
        <!-- 占位符玩家卡片 - 特殊动效 -->
        <div 
          v-if="player.isPlaceholder"
          class="bg-gradient-to-br from-slate-800/80 to-slate-700/60 border-2 border-dashed border-cyan-400/50 rounded-xl p-6 hover:border-cyan-400 hover:bg-gradient-to-br hover:from-slate-700/80 hover:to-slate-600/60 transition-all duration-500 relative overflow-hidden min-h-[280px] flex flex-col justify-center"
        >
          <!-- 背景动效 -->
          <div class="absolute inset-0 bg-gradient-to-r from-transparent via-cyan-400/10 to-transparent animate-pulse" style="animation-duration: 2s"></div>
          <div class="absolute inset-0 bg-gradient-to-br from-cyan-400/5 to-transparent"></div>
          
          <!-- 玩家头像占位符 -->
          <div class="flex items-center justify-center mb-4">
            <div class="relative">
              <div class="w-16 h-16 rounded-full bg-gradient-to-br from-cyan-400/20 to-blue-500/20 border-2 border-dashed border-cyan-400/50 flex items-center justify-center animate-pulse">
                <Icon name="material-symbols:person" class="w-8 h-8 text-cyan-400" />
              </div>
              <!-- 脉冲光环 -->
              <div class="absolute inset-0 w-16 h-16 rounded-full border-2 border-cyan-400/30 animate-ping"></div>
              <div class="absolute inset-0 w-16 h-16 rounded-full border-2 border-cyan-400/20 animate-ping" style="animation-delay: 0.5s"></div>
            </div>
          </div>
          
          <!-- 等待文本 -->
          <div class="text-center mb-4">
            <div class="text-cyan-400 text-lg font-bold mb-2 animate-pulse">
              {{ $t('battle.waiting_player') }}
            </div>
          </div>
          
          <!-- 加入按钮 - 只在第一个空位且当前用户未加入时显示 -->
          <div v-if="showJoinButtonForCurrentUser(idx)" class="text-center">
            <button
              class="relative group/btn px-8 py-3 bg-gradient-to-r from-cyan-500 to-blue-500 text-white rounded-xl font-bold text-lg shadow-lg hover:shadow-cyan-500/25 transition-all duration-300 overflow-hidden"
              @click="$emit('join')"
            >
              <div class="absolute inset-0 bg-gradient-to-r from-cyan-400 to-blue-400 opacity-0 group-hover/btn:opacity-100 transition-opacity duration-300"></div>
              <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover/btn:translate-x-full transition-transform duration-700"></div>
              <div class="relative flex items-center justify-center gap-2">
                <Icon name="material-symbols:add-circle" class="w-5 h-5 animate-pulse" />
                <span>{{ $t('battle.join_battle') }}</span>
                <Icon name="material-symbols:arrow-forward" class="w-5 h-5 group-hover/btn:translate-x-1 transition-transform duration-300" />
              </div>
              <div class="absolute inset-0 rounded-xl border-2 border-cyan-400/50 group-hover/btn:border-cyan-400 transition-colors duration-300"></div>
            </button>
          </div>
          <!-- 房间已满提示 -->
          <div v-else class="text-center">
            <div class="text-gray-500 text-sm">
              {{ $t('battle.room_full') }}
            </div>
          </div>
        </div>
        
        <!-- 真实玩家卡片 -->
        <div 
          v-else
          class="bg-gradient-to-br from-slate-800/80 to-slate-700/60 border-2 border-white/10 rounded-xl p-6 hover:border-white/20 transition-all duration-300 relative overflow-hidden group min-h-[280px] flex flex-col"
          :class="{
            'border-yellow-400/50 bg-gradient-to-br from-yellow-400/10 to-orange-400/10': player.isWinner,
            'border-cyan-400/30 bg-gradient-to-br from-cyan-400/5 to-blue-400/5': !player.isWinner,
            'justify-center': battleState === 'waiting' || battleState === 'ready'
          }"
        >
          <!-- 获胜者光效 -->
          <div v-if="player.isWinner" class="absolute inset-0 bg-gradient-to-r from-yellow-400/10 to-orange-400/10 animate-pulse"></div>
          
          <!-- 普通玩家背景动效 -->
          <div v-else class="absolute inset-0 bg-gradient-to-br from-cyan-400/5 to-blue-400/5"></div>
          
          <!-- 悬停背景动效 -->
          <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
          
          <!-- 玩家状态指示器 -->
          <div class="absolute top-2 right-2 flex items-center gap-1">
            <!-- 在线状态 -->
            <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <!-- 房主标识 -->
            <div v-if="player.isHost" class="w-2 h-2 bg-cyan-400 rounded-full animate-pulse" style="animation-delay: 0.5s"></div>
            <!-- 获胜者标识 -->
            <div v-if="player.isWinner" class="w-2 h-2 bg-yellow-400 rounded-full animate-pulse" style="animation-delay: 1s"></div>
          </div>
          
          <!-- 等待状态下的简化布局 -->
          <div v-if="battleState === 'waiting' || battleState === 'ready'" class="flex flex-col items-center justify-center relative z-10">
            <!-- 头像 -->
            <div class="relative mb-3">
              <img 
                :src="player.avatar || `/demo/avatar${(player.id % 3) + 1}.png`" 
                :alt="player.nickname"
                class="w-16 h-16 rounded-full border-2 shadow-lg transition-all duration-300"
                :class="{
                  'border-yellow-400 shadow-yellow-400/50': player.isWinner,
                  'border-cyan-400/50 shadow-cyan-400/30': !player.isWinner
                }"
                @error="handleImageError"
              />
              <!-- 获胜者标识 -->
              <div v-if="player.isWinner" class="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full flex items-center justify-center text-xs font-bold text-white shadow-lg animate-pulse">
                ⭐
              </div>
              <!-- 房主标识 -->
              <div v-else-if="player.isHost" class="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-cyan-400 to-blue-400 rounded-full flex items-center justify-center text-xs font-bold text-white shadow-lg">
                👑
              </div>
              
              <!-- 操作按钮 - 仅在当前用户且等待状态时显示 -->
              <div v-if="isCurrentUser(player) && (battleState === 'waiting' || battleState === 'ready')" class="absolute -bottom-1 -right-1">
                <!-- 解散对战按钮（创建者） -->
                <button
                  v-if="isUserCreator"
                  @click="$emit('dismiss')"
                  class="w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center text-xs font-bold shadow-lg hover:shadow-red-500/50 transition-all duration-200 transform hover:scale-110"
                  :title="$t('battle.detail.dismiss_battle')"
                >
                  <Icon name="material-symbols:delete-forever" class="w-3 h-3" />
                </button>
                
                <!-- 退出对战按钮（参与者） -->
                <button
                  v-else
                  @click="$emit('leave')"
                  class="w-6 h-6 bg-gray-500 hover:bg-gray-600 text-white rounded-full flex items-center justify-center text-xs font-bold shadow-lg hover:shadow-gray-500/50 transition-all duration-200 transform hover:scale-110"
                  :title="$t('battle.detail.leave_battle')"
                >
                  <Icon name="material-symbols:logout" class="w-3 h-3" />
                </button>
              </div>
              
              <!-- 头像光环效果 -->
              <div 
                class="absolute inset-0 rounded-full border-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                :class="{
                  'border-yellow-400/50': player.isWinner,
                  'border-cyan-400/50': !player.isWinner
                }"
              ></div>
            </div>
            
            <!-- 玩家信息 -->
            <div class="text-center mb-3">
              <div class="text-white font-bold text-lg transition-colors duration-300 hover:text-cyan-400 transform origin-center">
                {{ player.nickname }}
              </div>
              <!-- 玩家状态标签：只在真实玩家显示 -->
              <div v-if="!player.isPlaceholder" class="flex items-center justify-center gap-2 mt-2">
                <span v-if="player.isHost" class="text-xs px-2 py-1 bg-gradient-to-r from-cyan-400/20 to-blue-400/20 text-cyan-400 rounded-full border border-cyan-400/30">
                  对战创建者
                </span>
                <span v-else class="text-xs px-2 py-1 bg-gradient-to-r from-green-400/20 to-emerald-400/20 text-green-400 rounded-full border border-green-400/30">
                  已加入
                </span>
              </div>
            </div>
          </div>

          <!-- 对战进行中的完整布局 -->
          <div v-else class="flex flex-col h-full">
            <!-- 玩家基本信息 - 居中对齐 -->
            <div class="flex flex-col items-center justify-center mb-4 relative z-10">
              <!-- 头像 -->
              <div class="relative mb-3">
                <img 
                  :src="player.avatar || `/demo/avatar${(player.id % 3) + 1}.png`" 
                  :alt="player.nickname"
                  class="w-16 h-16 rounded-full border-2 shadow-lg transition-all duration-300"
                  :class="{
                    'border-yellow-400 shadow-yellow-400/50': player.isWinner,
                    'border-cyan-400/50 shadow-cyan-400/30': !player.isWinner
                  }"
                  @error="handleImageError"
                />
                <!-- 获胜者标识 -->
                <div v-if="player.isWinner" class="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full flex items-center justify-center text-xs font-bold text-white shadow-lg animate-pulse">
                  ⭐
                </div>
                <!-- 房主标识 -->
                <div v-else-if="player.isHost" class="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-cyan-400 to-blue-400 rounded-full flex items-center justify-center text-xs font-bold text-white shadow-lg">
                  👑
                </div>
                <!-- 计算中指示器 -->
                <div v-if="isCalculating" class="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full flex items-center justify-center animate-pulse shadow-lg shadow-yellow-400/50">
                  <Icon name="material-symbols:calculate" class="w-3 h-3 text-white" />
                </div>
                
                <!-- 头像光环效果 -->
                <div 
                  class="absolute inset-0 rounded-full border-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  :class="{
                    'border-yellow-400/50': player.isWinner,
                    'border-cyan-400/50': !player.isWinner
                  }"
                ></div>
              </div>
              
              <!-- 玩家信息 -->
              <div class="text-center">
                <div class="text-white font-bold text-lg transition-colors duration-300 hover:text-cyan-400 transform origin-center">
                  {{ player.nickname }}
                </div>
                <!-- 计算阶段显示总价值 -->
                <div v-if="isCalculating" class="text-sm text-blue-400 font-bold animate-pulse mt-1">
                  总价值: ${{ displayCalculatingValue(player) }}
                </div>
                <!-- 玩家状态标签 -->
                <div class="flex items-center justify-center gap-2 mt-2">
                  <span v-if="player.isHost" class="text-xs px-2 py-1 bg-gradient-to-r from-cyan-400/20 to-blue-400/20 text-cyan-400 rounded-full border border-cyan-400/30">
                  对战创建者
                  </span>
                  <span v-if="player.isWinner" class="text-xs px-2 py-1 bg-gradient-to-r from-yellow-400/20 to-orange-400/20 text-yellow-400 rounded-full border border-yellow-400/30 animate-pulse">
                    获胜者
                  </span>
                  <span v-else class="text-xs px-2 py-1 bg-gradient-to-r from-green-400/20 to-emerald-400/20 text-green-400 rounded-full border border-green-400/30">
                    已加入
                  </span>
                </div>
              </div>
            </div>

            <!-- 内容区域 - 使用flex-1确保填充剩余空间 -->
            <div class="flex-1 flex flex-col items-center justify-center">
              <!-- 计算阶段的价值增长进度条 -->
              <div v-if="isCalculating" class="mb-3 relative z-10 w-full max-w-xs">
                <div class="h-2 bg-slate-700/50 rounded-full overflow-hidden relative">
                  <div
                    class="h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg transition-all duration-1000 ease-out relative"
                    :style="{ width: getCalculatingValuePercentage(player) + '%' }"
                  >
                    <!-- 进度条光效 -->
                    <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-pulse"></div>
                  </div>
                  <!-- 进度条光效 -->
                  <div
                    v-if="calculationProgress < 100"
                    class="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-pulse"
                    style="animation-duration: 1.5s"
                  ></div>
                </div>
                <!-- 计算状态提示 -->
                <div 
                  v-if="calculationProgress < 100"
                  class="text-xs text-yellow-400 animate-pulse flex items-center justify-center gap-1 mt-1"
                >
                  <Icon name="material-symbols:calculate" class="w-3 h-3" />
                  计算中...
                </div>
                <div 
                  v-else
                  class="text-xs text-green-400 flex items-center justify-center gap-1 mt-1"
                >
                  <Icon name="material-symbols:check-circle" class="w-3 h-3" />
                  计算完成
                </div>
              </div>

              <!-- 滚轮动画区域 - 在对战进行中显示 -->
              <div v-if="battleState === 'battle'" class="mb-3 relative z-10 w-full">
                <BattleCaseAnimation
                  :ref="el => { 
                    setAnimationRef(player.id, player.nickname, el)
                  }"
                  :selected-case="getPlayerSelectedCase(player)"
                  :case-items="getPlayerCaseItems(player)"
                  :player="player"
                  :player-index="getPlayerIndex(player)"
                  :round-index="currentRound - 1"
                  :is-animating="isCurrentPlayerAnimating(player)"
                  :animation-id="getCurrentAnimationId()"
                  :should-start-animation="shouldStartPlayerAnimation(player)"
                  @case-opened="handlePlayerCaseOpened"
                  @animation-complete="handlePlayerAnimationComplete"
                  @opening-start="() => handlePlayerAnimationStart(getPlayerIndex(player))"
                />
              </div>

              <!-- 开箱记录区域 - 对战进行中和结束后均显示 -->
              <div v-if="hasOpeningData(player)" class="mb-3">
                <div class="text-center mb-2">
                  <h3 class="text-lg font-semibold text-white">开箱记录</h3>
                </div>
                <div class="grid grid-cols-2 gap-2">
                  <div 
                    v-for="(item, index) in getFilteredOpeningHistory(player)" 
                    :key="index"
                    class="bg-gray-800 rounded-lg p-2 border"
                    :style="{ borderColor: item.rarity_color || '#6b7280' }"
                  >
                    <img :src="item.image || item.item_image" :alt="getLocalizedName(item)" class="w-full h-16 object-contain mb-1" />
                    <div class="text-xs text-gray-300 truncate">{{ getLocalizedName(item) }}</div>
                    <div class="text-xs text-yellow-400">${{ formatPrice(item.item_price?.price || item.price) }}</div>
                  </div>
                </div>
              </div>

              <!-- 对战结束时的奖励物品 -->
              <div v-if="isBattleFinished" class="relative z-10 w-full max-w-xs">
                <!-- 奖励物品标题 -->
                <div class="flex items-center justify-between mb-3 p-3 rounded-lg border transition-all duration-300"
                  :class="player.isWinner 
                    ? 'bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border-yellow-400/30 hover:from-yellow-500/30 hover:to-orange-500/30' 
                    : 'bg-gradient-to-r from-blue-500/20 to-purple-500/20 border-blue-400/30 hover:from-blue-500/30 hover:to-purple-500/30'"
                >
                  <div class="flex items-center gap-2">
                    <div class="w-6 h-6 rounded-full flex items-center justify-center transition-all duration-300"
                      :class="player.isWinner ? 'bg-gradient-to-r from-yellow-400 to-orange-400 animate-pulse' : 'bg-gradient-to-r from-blue-400 to-purple-400'"
                    >
                      <Icon 
                        :name="player.isWinner ? 'material-symbols:star' : 'material-symbols:card-giftcard'" 
                        class="w-4 h-4 text-white" 
                      />
                    </div>
                    <h4 class="text-sm font-bold transition-colors duration-300"
                      :class="player.isWinner ? 'text-yellow-400' : 'text-blue-400'"
                    >
                      {{ $t('battle.winning_items') }}
                    </h4>
                  </div>
                  <div class="text-xs text-green-400 font-bold bg-green-400/10 px-2 py-1 rounded border border-green-400/30">
                    ${{ formatPrice(getPlayerRewardValue(player)) }}
                  </div>
                </div>
                
                <!-- 奖励物品列表 -->
                <div class="grid grid-cols-2 gap-2">
                  <div 
                    v-for="(item, itemIndex) in getPlayerRewardItems(player)" 
                    :key="itemIndex"
                    class="bg-gradient-to-br from-slate-700/50 to-slate-600/50 border border-white/10 rounded-lg p-2 hover:border-white/30 hover:scale-105 transition-all duration-300 group/item relative overflow-hidden"
                  >
                    <!-- 物品背景光效 -->
                    <div class="absolute inset-0 bg-gradient-to-br from-transparent via-white/5 to-transparent opacity-0 group-hover/item:opacity-100 transition-opacity duration-300"></div>
                    
                    <div class="flex items-center gap-2 relative z-10">
                      <div class="relative">
                        <img 
                          :src="item.image || '/demo/item1.png'" 
                          :alt="item.name"
                          class="w-10 h-10 rounded border-2 object-cover transition-all duration-300 group-hover/item:scale-110"
                          :style="{ borderColor: item.rarity_color || '#6b7280' }"
                          @error="handleImageError"
                        />
                        <!-- StatTrak标识 -->
                        <div v-if="item.isStatTrak" class="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-orange-400 to-red-400 rounded-full flex items-center justify-center">
                          <span class="text-xs text-white font-bold">ST</span>
                        </div>
                        <!-- 稀有度光效 -->
                        <div 
                          v-if="item.rarity_color"
                          class="absolute inset-0 rounded border-2 opacity-0 group-hover/item:opacity-100 transition-opacity duration-300"
                          :style="{ borderColor: item.rarity_color }"
                        ></div>
                      </div>
                      <div class="flex-1 min-w-0">
                        <div class="text-white text-xs font-medium truncate">{{ getLocalizedName(item) }}</div>
                        <div class="text-cyan-400 text-xs font-bold">${{ formatPrice(item.item_price?.price || item.price || 0) }}</div>
                        <!-- 外观和稀有度信息 -->
                        <div class="flex items-center gap-2 mt-1">
                          <span v-if="item.exterior" class="text-xs text-white/60">{{ getExteriorName(item.exterior) }}</span>
                          <span v-if="item.rarity_name_zh_hans || item.rarity_name_en" 
                            class="text-xs px-1 rounded transition-all duration-300"
                            :style="{ 
                              color: item.rarity_color || '#6b7280',
                              backgroundColor: (item.rarity_color || '#6b7280') + '20'
                            }"
                          >
                            {{ getLocalizedRarityName(item) }}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 国际化
const { t } = useI18n()

// 调试前缀
const DEBUG_PREFIX = '[🎰BATTLE-PLAYER-LIST-DEBUG]'

// 导入Vue 3 Composition API
import { ref, computed, watch, nextTick, onMounted, onUnmounted, reactive } from 'vue'

// Props
const props = defineProps({
  players: {
    type: Array,
    required: true
  },
  maxPlayers: {
    type: Number,
    default: 4
  },
  currentRound: {
    type: Number,
    default: 1
  },
  isBattleFinished: {
    type: Boolean,
    default: false
  },
  currentRoundResults: {
    type: Array,
    default: () => []
  },
  isCalculating: {
    type: Boolean,
    default: false
  },
  calculationProgress: {
    type: Number,
    default: 0
  },
  currentPlayerId: {
    type: String,
    default: null
  },
  selectedCases: {
    type: Array,
    default: () => []
  },
  battleState: {
    type: String,
    default: 'battle'
  },
  isJoined: {
    type: Boolean,
    default: false
  },
  isUserCreator: {
    type: Boolean,
    default: false
  },
  animationState: {
    type: Object,
    default: () => ({
      currentAnimationId: null,
      animationProgress: 0,
      animationStage: 'idle',
      participantStates: new Map()
    })
  }
})

// Emits
const emit = defineEmits(['animation-start', 'animation-complete', 'playerAnimationStart', 'playerAnimationComplete', 'join', 'dismiss', 'leave'])

// 动画引用
const animationRefs = ref({})

// ⚡ 本地响应式记录Map，键为玩家uid/索引
const openingRecords = reactive(new Map())

// 安全的ref设置函数
const setAnimationRef = (playerId, playerName, el) => {
  try {
    console.log(`${DEBUG_PREFIX} 🔧 设置玩家${playerName}的ref:`, el)
    
    if (el && typeof el === 'object' && el.$el) {
      // 确保这是一个有效的Vue组件实例
      animationRefs.value[playerId] = el
      console.log(`${DEBUG_PREFIX} ✅ 玩家${playerName}动画组件ref设置成功`)
    } else if (el === null) {
      // 组件卸载时，el为null，这是正常的
      console.log(`${DEBUG_PREFIX} 🧹 玩家${playerName}动画组件卸载，清理ref`)
      delete animationRefs.value[playerId]
    } else {
      console.log(`${DEBUG_PREFIX} ❌ 玩家${playerName}动画组件ref设置失败，el类型:`, typeof el, 'el:', el)
    }
  } catch (error) {
    console.error(`${DEBUG_PREFIX} 🚨 设置玩家${playerName}的ref时出错:`, error)
  }
}

// 组件挂载时的调试信息
onMounted(() => {
  console.log(`${DEBUG_PREFIX} 🚀 组件挂载完成`)
  console.log(`${DEBUG_PREFIX} 📊 Props状态:`, {
    playersCount: props.players.length,
    battleState: props.battleState,
    isCalculating: props.isCalculating,
    isBattleFinished: props.isBattleFinished,
    currentRound: props.currentRound,
    selectedCasesCount: props.selectedCases.length
  })

  // 新增：监听全局开箱记录事件，实时更新openingHistory
  const handleAddRecord = (e) => {
    const detail = e.detail || {}
    const { playerId, playerName, playerIndex, item } = detail
    if (!item) return
    // 在props.players中定位玩家
    const target = props.players.find((p, idx) => {
      const matchIdx = playerIndex !== undefined && idx === playerIndex
      const matchId = playerId && (p.uid === playerId || p.id === playerId)
      const matchName = playerName && (p.nickname === playerName || p.username === playerName)
      return matchIdx || matchId || matchName
    })
    if (target) {
      const key = target.uid || target.id || target.nickname || playerIndex
      if (!openingRecords.has(key)) {
        openingRecords.set(key, [])
      }
      openingRecords.get(key).push(item)
    }
  }
  window.addEventListener('battle:add_record', handleAddRecord)
  onUnmounted(() => {
    window.removeEventListener('battle:add_record', handleAddRecord)
  })
})

// 监听props变化
watch(() => props.animationState, (newState, oldState) => {
  // console.log('[🎰BATTLE-DEBUG] 🎬 动画状态变化:', { 
  //   from: oldState?.animationStage, 
  //   to: newState?.animationStage,
  //   currentPlayerId: props.currentPlayerId 
  // })
  
  if (newState?.animationStage === 'opening' && props.currentPlayerId) {
    // console.log('[🎰BATTLE-DEBUG] 🎬 准备触发动画，玩家ID:', props.currentPlayerId)
    // 延迟一点时间确保组件完全渲染
    nextTick(() => {
      setTimeout(() => {
        console.log('[🎰BATTLE-DEBUG] 🚀 开始触发动画')
        triggerAnimations()
      }, 500)
    })
  }
}, { deep: true })

// 监听其他关键props
watch(() => [props.isCalculating, props.isBattleFinished], ([newIsCalculating, newIsBattleFinished], [oldIsCalculating, oldIsBattleFinished]) => {
  // 静默监听状态变化
})

// 计算属性
const hasWinner = computed(() => {
  return props.players.some(player => player.isWinner)
})

// 判断是否已经执行过动画（通过检查是否有开箱记录）
const hasExecutedAnimations = computed(() => {
  return props.players.some(player => {
    return player.openingHistory && player.openingHistory.length > 0
  })
})

// 调试：动画显示条件
const shouldShowAnimation = computed(() => {
  const show = props.battleState === 'battle' || props.battleState === 'running' || (props.battleState === 'finished' && !hasExecutedAnimations.value)
  return show
})

// 触发所有玩家的动画
const triggerAnimations = () => {
  console.log('[🎰BATTLE-DEBUG] 🎯 触发所有玩家动画，引用数量:', Object.keys(animationRefs.value).length)
  
  if (animationRefs.value && Object.keys(animationRefs.value).length > 0) {
    // 所有玩家同时开始动画
    Object.keys(animationRefs.value).forEach((playerId) => {
      const ref = animationRefs.value[playerId]
      
      if (ref && ref.triggerAutoStart) {
        try {
          console.log('[🎰BATTLE-DEBUG] ✅ 调用玩家动画方法:', playerId)
          ref.triggerAutoStart()
        } catch (error) {
          console.error('[🎰BATTLE-DEBUG] ❌ 玩家动画触发失败:', playerId, error)
        }
      } else if (ref && ref.startOpening) {
        try {
          console.log('[🎰BATTLE-DEBUG] ✅ 调用startOpening方法:', playerId)
          ref.startOpening()
        } catch (error) {
          console.error('[🎰BATTLE-DEBUG] ❌ startOpening调用失败:', playerId, error)
        }
      } else {
        console.warn('[🎰BATTLE-DEBUG] ⚠️ 玩家动画组件无效:', playerId)
      }
    })
  } else {
    console.warn('[🎰BATTLE-DEBUG] ⚠️ 没有找到动画组件引用')
  }
}

// 处理玩家动画开始
const handlePlayerAnimationStart = (playerIndex) => {
  console.log(`[🎰BATTLE-PLAYER-LIST] 玩家${playerIndex}动画开始`)
  emit('playerAnimationStart', playerIndex)
}

// 处理玩家动画完成
const handlePlayerAnimationComplete = (playerIndex, result) => {
  console.log(`[🎰BATTLE-PLAYER-LIST] ===== 收到玩家动画完成事件 =====`)
  console.log(`[🎰BATTLE-PLAYER-LIST] 参数:`, { playerIndex, result })
  console.log(`[🎰BATTLE-PLAYER-LIST] 触发父组件事件: playerAnimationComplete`)
  
  emit('playerAnimationComplete', playerIndex, result)
  
  console.log(`[🎰BATTLE-PLAYER-LIST] 事件已发送到父组件`)
}

// 方法
const formatPrice = (price) => {
  if (!price || isNaN(price)) return '0.00'
  return Number(price).toFixed(2)
}

const handleImageError = (event) => {
  event.target.src = '/demo/avatar1.png'
}

// 判断是否为当前玩家
const isCurrentPlayer = (player) => {
  return player.id === props.currentPlayerId
}

// 判断是否为当前用户
const isCurrentUser = (player) => {
  const userStore = useUserStore()
  if (!userStore.user) return false
  return player.uid === userStore.user.uid
}

// 获取玩家的箱子物品
const getPlayerCaseItems = (player) => {
  console.log(`${DEBUG_PREFIX} 🔍 getPlayerCaseItems调用:`, {
    playerId: player.id,
    playerName: player.nickname,
    currentRoundResults: props.currentRoundResults,
    currentRoundResultsLength: props.currentRoundResults?.length || 0,
    selectedCases: props.selectedCases
  })
  
  // 🔧 新增：处理多次使用箱子的情况
  const playerSelectedCases = props.selectedCases.filter(caseItem => caseItem.playerId === player.id)
  console.log(`${DEBUG_PREFIX} 🔍 玩家${player.nickname}选择的箱子:`, playerSelectedCases)
  
  // 如果玩家选择了多个箱子，合并所有箱子的物品
  if (playerSelectedCases.length > 0) {
    const allCaseItems = []
    
    playerSelectedCases.forEach((selectedCase, index) => {
      console.log(`${DEBUG_PREFIX} 🔍 处理箱子${index + 1}:`, selectedCase)
      
      // 从当前轮次结果中获取该箱子的物品
      const roundResult = props.currentRoundResults.find(result => 
        result.playerId === player.id && result.caseKey === selectedCase.key
      )
      
      if (roundResult && roundResult.caseItems) {
        console.log(`${DEBUG_PREFIX} ✅ 找到箱子${selectedCase.name}的物品:`, roundResult.caseItems.length)
        allCaseItems.push(...roundResult.caseItems)
      } else {
        // 如果没有找到该箱子的结果，使用默认物品
        console.log(`${DEBUG_PREFIX} ⚠️ 未找到箱子${selectedCase.name}的结果，使用默认物品`)
        const defaultItems = generateDefaultCaseItems(selectedCase, index)
        allCaseItems.push(...defaultItems)
      }
    })
    
    if (allCaseItems.length > 0) {
      console.log(`${DEBUG_PREFIX} ✅ 玩家${player.nickname}总共获得${allCaseItems.length}个物品`)
      return allCaseItems
    }
  }
  
  // 如果没有选择箱子或没有物品，使用默认演示物品
  console.log(`${DEBUG_PREFIX} ⚠️ 未找到玩家${player.nickname}的caseItems，使用默认演示物品`)
  return generateDefaultDemoItems()
}

// 🔧 新增：生成默认箱子物品
const generateDefaultCaseItems = (selectedCase, caseIndex) => {
  const items = []
  const itemCount = Math.floor(Math.random() * 10) + 15 // 15-25个物品
  
  for (let i = 0; i < itemCount; i++) {
    const rarity = Math.random()
    let rarityColor, rarityName, price
    
    if (rarity < 0.6) {
      rarityColor = '#b0c3d9'
      rarityName = '普通'
      price = Math.random() * 10 + 1
    } else if (rarity < 0.8) {
      rarityColor = '#5e98d9'
      rarityName = '罕见'
      price = Math.random() * 50 + 10
    } else if (rarity < 0.95) {
      rarityColor = '#4b69ff'
      rarityName = '稀有'
      price = Math.random() * 200 + 50
    } else {
      rarityColor = '#8847ff'
      rarityName = '史诗'
      price = Math.random() * 500 + 200
    }
    
    items.push({
      id: `${selectedCase.key}-${caseIndex}-${i}`,
      name: `${selectedCase.name} 物品 ${i + 1}`,
      name_zh_hans: `${selectedCase.name} 物品 ${i + 1}`,
      name_en: `${selectedCase.name} Item ${i + 1}`,
      image: `/demo/item${(i % 30) + 1}.png`,
      price: price,
      rarity_color: rarityColor,
      rarity_name: rarityName,
      rarity_name_zh_hans: rarityName,
      rarity_name_en: rarityName,
      exterior: ['FN', 'MW', 'FT', 'WW', 'BS'][Math.floor(Math.random() * 5)],
      quality: ['consumer', 'industrial', 'mil-spec', 'restricted', 'classified'][Math.floor(Math.random() * 5)],
      is_stattrak: Math.random() < 0.1
    })
  }
  
  return items
}

// 🔧 新增：生成默认演示物品
const generateDefaultDemoItems = () => {
  return [
    {
      id: 'demo-1',
      name: 'AK-47 | 血腥运动',
      name_zh_hans: 'AK-47 | 血腥运动',
      name_en: 'AK-47 | Bloodsport',
      image: '/demo/item1.png',
      price: 45.50,
      rarity_color: '#ff6b35',
      exterior: 'FN',
      quality: 'classified'
    },
    {
      id: 'demo-2', 
      name: 'M4A4 | 龙王',
      name_zh_hans: 'M4A4 | 龙王',
      name_en: 'M4A4 | Dragon King',
      image: '/demo/item2.png',
      price: 12.30,
      rarity_color: '#4b69ff',
      exterior: 'MW',
      quality: 'restricted'
    }
  ]
}

// 获取玩家选择的箱子
const getPlayerSelectedCase = (player) => {
  // 从selectedCases中查找该玩家的箱子
  const selectedCase = props.selectedCases.find(caseItem => caseItem.playerId === player.id)
  if (selectedCase) {
    return selectedCase
  }
  
  // 如果没有找到，返回默认箱子
  return {
    id: 'default-case',
    key: 'default-case',
    name: '默认箱子',
    name_zh_hans: '默认箱子',
    name_en: 'Default Case',
    image: '/demo/case1.png',
    price: 2.50,
    playerId: player.id
  }
}

// 获取玩家索引
const getPlayerIndex = (player) => {
  return props.players.findIndex(p => p.id === player.id)
}

// 获取所有获胜物品（获胜者获得所有物品）
const getAllWinnerItems = () => {
  const allItems = []
  props.players.forEach(player => {
    if (player.openingHistory) {
      allItems.push(...player.openingHistory)
    }
  })
  return allItems
}

// 获取获胜者奖励物品（所有玩家的开箱记录，但不包括自己的记录）
const getWinnerRewardItems = (winnerPlayer) => {
  const allItems = []
  props.players.forEach(player => {
    if (player.openingHistory && player.id !== winnerPlayer.id) {
      allItems.push(...player.openingHistory)
    }
  })
  return allItems
}

// 获取系统赠送物品（失败者获得）
const getSystemGiftItems = (player) => {
  // 如果玩家有rewardItems，使用它；否则生成默认的
  if (player.rewardItems && player.rewardItems.length > 0) {
    return player.rewardItems
  }
  
  // 默认系统赠送物品
  return [
    {
      id: 'gift-1',
      name: '系统赠送饰品',
      name_zh_hans: '系统赠送饰品',
      name_en: 'System Gift Item',
      image: '/demo/item1.png',
      price: Math.random() * 100 + 10,
      rarity_color: '#b0c3d9'
    }
  ]
}

// 获取玩家开箱记录总价值
const getPlayerOpeningValue = (player) => {
  if (!player.openingHistory) return 0
  // 直接计算开箱记录总价值
  return player.openingHistory.reduce((total, item) => {
    // 支持多种价格字段格式
    const price = item.item_price?.price || item.price || 0
    return total + Number(price)
  }, 0)
}

// 获取玩家奖励价值
const getPlayerRewardValue = (player) => {
  // 直接使用API返回的rewardItems数据计算价值
  if (!player.rewardItems || player.rewardItems.length === 0) {
    return 0;
  }
  
  return player.rewardItems.reduce((total, item) => {
    const price = item.item_price?.price || item.price || 0
    return total + Number(price)
  }, 0)
}

// 获取玩家奖励物品
const getPlayerRewardItems = (player) => {
  console.log(`[🎰BATTLE] getPlayerRewardItems调用:`, {
    playerId: player.id,
    playerName: player.nickname,
    isWinner: player.isWinner,
    hasRewardItems: !!player.rewardItems,
    rewardItemsLength: player.rewardItems?.length || 0,
    openingHistoryLength: player.openingHistory?.length || 0
  });
  
  // 直接使用API返回的rewardItems数据
  // 胜利者的rewardItems包含所有玩家的开箱记录
  // 失败者的rewardItems包含系统赠送的安慰奖
  if (player.rewardItems && player.rewardItems.length > 0) {
    console.log(`[🎰BATTLE] ${player.nickname} 获得 ${player.rewardItems.length} 件奖励物品:`, player.rewardItems);
    return player.rewardItems;
  }
  
  // 如果没有rewardItems，返回空数组
  console.log(`[🎰BATTLE] ${player.nickname} 没有奖励物品`);
  return [];
}

// 获取过滤后的开箱记录（排除系统赠送物品）
const getFilteredOpeningHistory = (player) => {
  const key = player.uid || player.id || player.nickname || getPlayerIndex(player)
  const localArr = openingRecords.get(key)
  if (localArr && localArr.length) return localArr
  return []
}

// 计算阶段显示总价值
const displayCalculatingValue = (player) => {
  if (!player.openingHistory) return '0.00'
  
  const totalValue = player.openingHistory.reduce((total, item) => {
    const price = item.item_price?.price || item.price || 0
    return total + Number(price)
  }, 0)
  
  // 如果计算进度为0，显示0
  if (props.calculationProgress === 0) {
    return '0.00'
  }
  
  // 如果计算完成，显示最终值
  if (props.calculationProgress >= 100) {
    return Number(totalValue).toFixed(2)
  }
  
  // 计算当前应该显示的值（基于进度）
  const progressRatio = props.calculationProgress / 100
  const displayValue = totalValue * progressRatio
  
  return Number(displayValue).toFixed(2)
}

// 获取价值百分比（用于进度条）
const getValuePercentage = (player) => {
  if (!player.openingHistory) return 0
  
  const playerValue = player.openingHistory.reduce((total, item) => {
    const price = item.item_price?.price || item.price || 0
    return total + Number(price)
  }, 0)
  const maxValue = Math.max(...props.players.map(p => {
    if (!p.openingHistory) return 0
    return p.openingHistory.reduce((total, item) => {
      const price = item.item_price?.price || item.price || 0
      return total + Number(price)
    }, 0)
  }))
  
  return maxValue > 0 ? (playerValue / maxValue) * 100 : 0
}

// 获取本地化名称
const getLocalizedName = (item) => {
  return item.name_zh_hans || item.name_zh || item.name_cn || item.name || item.name_en || '未知物品'
}

// 获取本地化稀有度名称
const getLocalizedRarityName = (item) => {
  return item.rarity_name_zh_hans || item.rarity_name_zh || item.rarity_name_cn || item.rarity_name || item.rarity_name_en || '普通'
}

// 获取外观名称
const getExteriorName = (exterior) => {
  const exteriorMap = {
    'FN': '崭新出厂',
    'MW': '略有磨损', 
    'FT': '久经沙场',
    'WW': '破损不堪',
    'BS': '战痕累累',
    'Factory New': '崭新出厂',
    'Minimal Wear': '略有磨损',
    'Field-Tested': '久经沙场', 
    'Well-Worn': '破损不堪',
    'Battle-Scarred': '战痕累累'
  }
  return exteriorMap[exterior] || exterior
}

// 获取品质名称
const getQualityName = (quality) => {
  const qualityMap = {
    'consumer': '消费级',
    'industrial': '工业级',
    'mil-spec': '军规级',
    'restricted': '受限级',
    'classified': '保密级',
    'covert': '隐秘级',
    'contraband': '违禁级'
  }
  return qualityMap[quality] || quality
}

// 获取计算价值百分比（用于进度条）
const getCalculatingValuePercentage = (player) => {
  if (!player.openingHistory) return 0
  
  const playerValue = player.openingHistory.reduce((total, item) => {
    const price = item.item_price?.price || item.price || 0
    return total + Number(price)
  }, 0)
  const maxValue = Math.max(...props.players.map(p => {
    if (!p.openingHistory) return 0
    return p.openingHistory.reduce((total, item) => {
      const price = item.item_price?.price || item.price || 0
      return total + Number(price)
    }, 0)
  }))
  
  // 基于计算进度动态显示
  const basePercentage = maxValue > 0 ? (playerValue / maxValue) * 100 : 0
  const progressRatio = props.calculationProgress / 100
  
  return basePercentage * progressRatio
}

// 暴露方法给父组件
defineExpose({
  triggerAnimations
})

// 计算属性：showJoinButton
const showJoinButton = computed(() => {
  console.log('[🎰BATTLE-PLAYER-LIST] showJoinButton计算:', {
    isJoined: props.isJoined,
    battleState: props.battleState,
    shouldShow: !props.isJoined && (props.battleState === 'waiting' || props.battleState === 'ready' || props.battleState === 'loading')
  });
  
  // 如果用户已加入，不显示加入按钮
  if (props.isJoined) {
    return false;
  }
  
  // 在等待、准备、加载状态下显示加入按钮
  return props.battleState === 'waiting' || 
         props.battleState === 'ready' || 
         props.battleState === 'loading';
})

// 计算已加入玩家的数量
const getJoinedPlayerCount = () => {
  return props.players.filter(player => !player.isPlaceholder).length
}

// 判断是否有开箱数据
const hasOpeningData = (player) => {
  const key = player.uid || player.id || player.nickname || getPlayerIndex(player)
  const localArr = openingRecords.get(key)
  if (localArr && localArr.length) return true
  return false
}

// 🚀 新增：动画控制方法
const isCurrentPlayerAnimating = (player) => {
  // 检查当前玩家是否正在动画中
  return props.battleState === 'battle' && 
         props.currentPlayerId === player.uid;
};

const getCurrentAnimationId = () => {
  // 返回当前动画ID，可以从父组件传入
  return `battle-${props.currentRound}`;
};

const shouldStartPlayerAnimation = (player) => {
  // 检查当前玩家是否应该开始动画
  // 使用uid匹配，因为currentPlayerId传递的是uid
  const shouldStart = props.battleState === 'battle' && props.currentPlayerId === player.uid;
  
  console.log('[🎰BATTLE-DEBUG] 🎯 shouldStartPlayerAnimation检查:', {
    playerNickname: player.nickname,
    playerUid: player.uid,
    currentPlayerId: props.currentPlayerId,
    battleState: props.battleState,
    shouldStart: shouldStart,
    hasCurrentRoundResult: !!player.currentRoundResult,
    currentRoundResult: player.currentRoundResult
  });
  
  if (shouldStart) {
    console.log('[🎰BATTLE-DEBUG] 🎯 触发动画:', player.nickname);
  }
  
  return shouldStart;
};

const handlePlayerCaseOpened = (result) => {
  console.log('[🎰BATTLE-PLAYER-LIST] 玩家开箱结果:', result);
  emit('playerAnimationComplete', result);
};

// 计算属性：当前用户是否已加入
const userStore = useUserStore()
const isCurrentUserJoined = computed(() => {
  if (!userStore.user) return false
  // 只统计真实玩家
  return props.players.some(p => !p.isPlaceholder && p.uid === userStore.user.uid)
})

// 方法：判断当前占位卡片是否应显示加入按钮（所有空位都显示）
const showJoinButtonForCurrentUser = (idx) => {
  // 所有空位都显示，且当前用户未加入，且状态为waiting/ready/loading
  return !isCurrentUserJoined.value &&
    (props.battleState === 'waiting' || props.battleState === 'ready' || props.battleState === 'loading')
}
</script> 