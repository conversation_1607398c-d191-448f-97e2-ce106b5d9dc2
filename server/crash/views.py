import logging
from django.utils.translation import gettext_lazy as _

from rest_framework.views import APIView
from rest_framework.permissions import AllowAny

from steambase.enums import RespCode
from steambase.utils import reformat_resp, current_user
from sitecfg.interfaces import get_maintenance, get_maintenance_crash
from crash.business import bet_crash_game, out_crash_game, get_current_crash_bet, get_crash_bet
from crash.business import get_crash_game_history, get_current_crash_game_data, get_crash_day_rank, get_crash_week_rank
from crash.business import verify_crash


_logger = logging.getLogger(__name__)


class GetCrashGameCurrentView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:

            fields = ('id', 'uid', 'hash', 'secret', 'state', 'joinable_time', 'run_time', 'end_time', 'countdown', 'past')
            code, resp = get_current_crash_game_data(fields)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class BetCrashGameView(APIView):
    
    def post(self, request):
        try:

            if get_maintenance_crash():
                return reformat_resp(RespCode.Maintenance.value, {},
                                     _('Current game is under maintenance, please wait for a while.'))
            user = current_user(request)
            amount = request.data.get('amount', 0)
            out_point = request.data.get('outPoint', 0)
            code, resp = bet_crash_game(user, amount, out_point)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class OutCrashGameView(APIView):

    def post(self, request):
        try:
            user = current_user(request)
            out_point = request.data.get('outPoint', 0)
            code, resp = out_crash_game(user, out_point)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetCrashBetCurrentView(APIView):

    def get(self, request):
        try:
            user = current_user(request)
            code, resp = get_current_crash_bet(user)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetCrashBetView(APIView):

    def get(self, request):
        try:

            user = current_user(request)
            query = {
                'uid': request.query_params.get('uid', None),
                'win_result': request.query_params.get('win_result', None)
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            fields = ('uid', 'game', 'amount', 'out_point', 'win_amount', 'win_result', 'create_time')
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            code, resp = get_crash_bet(user, query, fields, page, page_size)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetCrashHistoryView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:

            query = {
                'uid': request.query_params.get('uid', None)
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            fields = ('id', 'uid', 'hash', 'secret', 'percentage', 'total_amount', 'crash_point', 'end_time')
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            code, resp = get_crash_game_history(query, fields, page, page_size)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, _('Succeed'))
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, _('Exception'))


class GetCrashDayRankView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:

            query = {
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            fields = ('user', 'amount')
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            count = int(request.query_params.get('count', 10))
            code, resp = get_crash_day_rank(query, fields, page, page_size, count)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, _('Succeed'))
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, _('Exception'))


class GetCrashWeekRankView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:

            query = {
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            fields = ('user', 'amount')
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            count = int(request.query_params.get('count', 10))
            code, resp = get_crash_week_rank(query, fields, page, page_size, count)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, _('Succeed'))
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, _('Exception'))


class VerifyCrashView(APIView):

    def post(self, request):
        try:

            user = current_user(request)
            percentage = request.data.get('percentage', 0)
            secret = request.data.get('secret', '')
            code, resp = verify_crash(percentage, secret)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, _('Succeed'))
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, _('Exception'))
