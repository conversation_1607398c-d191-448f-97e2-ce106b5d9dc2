<!-- components/battle/BattleVerticalPlayerList.vue -->
<template>
  <div class="battle-vertical-player-list">
    <!-- 标题区域 -->
    <div class="list-header">
      <h3 class="title-i18n text-xl font-bold text-white">
        {{ $t('battle.players') }}
      </h3>
      <div class="player-count">
        <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
        <span class="text-green-400 text-sm font-medium">{{ players.length }}/{{ maxPlayers }}</span>
      </div>
    </div>

    <!-- 水平玩家列表 - 使用UnoCSS栅格系统 -->
    <div class="players-container grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 overflow-x-auto p-1">
      <div 
        v-for="(player, index) in players" 
        :key="player.id"
        class="player-card"
        :class="{
          'winner': player.isWinner,
          'active': isPlayerActive(player),
          'calculating': battleState === 'calculating',
          [`player-${index + 1}`]: true
        }"
      >
        <!-- 玩家头像和信息 -->
        <div class="player-info">
          <div class="avatar-container">
            <img 
              :src="player.avatar || `/demo/avatar${(player.id % 3) + 1}.png`" 
              :alt="player.username"
              class="player-avatar"
              @error="handleImageError"
            />
            <!-- 状态指示器 -->
            <div class="status-indicator">
              <!-- 获胜者标识 -->
              <div v-if="player.isWinner" class="winner-badge">
                <Icon name="material-symbols:star" class="w-4 h-4" />
              </div>
              <!-- 房主标识 -->
              <div v-else-if="player.is_creator" class="host-badge">
                <Icon name="material-symbols:crown" class="w-4 h-4" />
              </div>
              <!-- 计算中指示器 -->
              <div v-else-if="battleState === 'calculating'" class="calculating-badge">
                <Icon name="material-symbols:calculate" class="w-4 h-4" />
              </div>
            </div>
          </div>
          
          <div class="player-details">
            <div class="player-name">{{ player.username }}</div>
            <div class="player-score">${{ formatPrice(player.score) }}</div>
          </div>
        </div>

        <!-- 对战状态指示 -->
        <div class="battle-status">
          <!-- 等待中状态 -->
          <div v-if="battleState === 'waiting'" class="waiting-status">
            <div class="status-dot bg-gray-400"></div>
            <span class="status-text">等待中</span>
          </div>
          
          <!-- 对战中状态 -->
          <div v-else-if="battleState === 'battle'" class="battle-status">
            <div class="status-dot bg-blue-400 animate-pulse"></div>
            <span class="status-text">对战中</span>
          </div>
          
          <!-- 计算中状态 -->
          <div v-else-if="battleState === 'calculating'" class="calculating-status">
            <div class="status-dot bg-yellow-400 animate-pulse"></div>
            <span class="status-text">计算中</span>
            <!-- 计算进度条 -->
            <div class="calculation-progress">
              <div class="progress-bar">
                <div 
                  class="progress-fill"
                  :style="{ width: getCalculationProgress() + '%' }"
                ></div>
              </div>
            </div>
          </div>
          
          <!-- 已完成状态 -->
          <div v-else-if="battleState === 'finished'" class="finished-status">
            <div class="status-dot" :class="player.isWinner ? 'bg-yellow-400' : 'bg-gray-400'"></div>
            <span class="status-text" :class="player.isWinner ? 'text-yellow-400' : 'text-gray-400'">
              {{ player.isWinner ? '获胜' : '完成' }}
            </span>
          </div>
        </div>

        <!-- 开箱记录预览 -->
        <div v-if="player.items && player.items.length > 0 && battleState !== 'battle'" class="items-preview">
          <div class="items-header">
            <span class="items-title">开箱物品</span>
            <span class="items-count">{{ player.items.length }}件</span>
          </div>
          <div class="items-grid">
            <div 
              v-for="(item, itemIndex) in player.items.slice(0, 3)" 
              :key="itemIndex"
              class="item-card"
            >
              <img 
                :src="item.image || '/demo/item1.png'" 
                :alt="item.name"
                class="item-image"
                @error="handleImageError"
              />
              <div class="item-price">${{ formatPrice(item.price || 0) }}</div>
            </div>
            <!-- 更多物品指示器 -->
            <div v-if="player.items.length > 3" class="more-items">
              <span class="more-text">+{{ player.items.length - 3 }}</span>
            </div>
          </div>
        </div>

        <!-- 开箱动画区域 - 对战状态下显示 -->
        <div v-if="battleState === 'battle' && player.animationData" class="opening-animation">
          <div class="animation-header">
            <span class="animation-title">开箱中...</span>
            <div class="animation-status">
              <div class="status-dot bg-blue-400 animate-pulse"></div>
              <span class="status-text">滚动中</span>
            </div>
          </div>
          
          <!-- 使用SmoothCaseAnimation组件 -->
          <div class="animation-container">
            <SmoothCaseAnimation
              :ref="(el) => setAnimationRef(el, index)"
              :selected-case="player.animationData.selectedCase"
              :case-items="player.animationData.caseItems"
              :demo-mode="true"
              @case-opened="handleOpeningComplete(index, $event)"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, nextTick } from 'vue'

// Props
const props = defineProps({
  players: {
    type: Array,
    required: true
  },
  battleState: {
    type: String,
    required: true
  },
  winner: {
    type: Object,
    default: null
  },
  maxPlayers: {
    type: Number,
    default: 4
  },
  calculationProgress: {
    type: Number,
    default: 0
  }
})

// Emits
const emit = defineEmits([
  'opening-complete',
  'all-openings-complete'
])

// 国际化
const { t } = useI18n()

// 动画组件引用
const animationRefs = ref([])
const completedOpenings = ref(new Set())

// 计算属性
const isPlayerActive = (player) => {
  return props.battleState === 'battle' || 
         (props.battleState === 'calculating' && player.isWinner)
}

const getCalculationProgress = () => {
  return props.calculationProgress || 0
}

// 动画相关方法
const setAnimationRef = (el, playerIndex) => {
  if (el) {
    // 确保数组有足够的长度
    while (animationRefs.value.length <= playerIndex) {
      animationRefs.value.push(null)
    }
    animationRefs.value[playerIndex] = el
    console.log(`[🎰BATTLE-VERTICAL] 设置玩家${playerIndex + 1}动画引用:`, !!el)
  }
}

const handleOpeningComplete = (playerIndex, result) => {
  console.log(`[🎰BATTLE-VERTICAL] 玩家${playerIndex + 1}开箱完成:`, result)
  
  // 记录完成的玩家
  completedOpenings.value.add(playerIndex)
  
  // 更新玩家数据
  if (props.players[playerIndex]) {
    const player = props.players[playerIndex]
    if (!player.items) {
      player.items = []
    }
    player.items.push(result)
    player.score += result.price || 0
  }
  
  // 发送开箱完成事件
  emit('opening-complete', { playerIndex, result })
  
  // 检查是否所有玩家都完成了
  if (completedOpenings.value.size === props.players.length) {
    console.log('[🎰BATTLE-VERTICAL] 所有玩家开箱完成')
    emit('all-openings-complete')
  }
}

// 启动所有动画
const startAllAnimations = async () => {
  console.log('[🎰BATTLE-VERTICAL] 启动所有玩家开箱动画')
  completedOpenings.value.clear()
  
  await nextTick()
  
  // 延迟启动动画，确保组件完全渲染
  setTimeout(() => {
    animationRefs.value.forEach((ref, index) => {
      if (ref && typeof ref.startOpening === 'function') {
        console.log(`[🎰BATTLE-VERTICAL] 启动玩家${index + 1}动画`)
        ref.startOpening()
      } else {
        console.warn(`[🎰BATTLE-VERTICAL] 玩家${index + 1}组件引用未找到或方法不存在:`, !!ref, typeof ref?.startOpening)
      }
    })
  }, 500)
}

// 重置动画
const resetAnimations = () => {
  console.log('[🎰BATTLE-VERTICAL] 重置所有动画')
  completedOpenings.value.clear()
  animationRefs.value.forEach((ref) => {
    if (ref && typeof ref.resetAnimation === 'function') {
      ref.resetAnimation()
    }
  })
}

// 暴露方法供父组件调用
defineExpose({
  startAllAnimations,
  resetAnimations
})

// 工具函数
const formatPrice = (price) => {
  return price.toFixed(2)
}

const handleImageError = (event) => {
  const target = event.target
  target.src = '/demo/avatar1.png'
}
</script>

<style lang="scss" scoped>
.battle-vertical-player-list {
  background: rgba(31, 41, 55, 0.4);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(107, 114, 128, 0.3);
  border-radius: 12px;
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(107, 114, 128, 0.3);
  
  .player-count {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

.players-container {
  flex: 1;
  /* 栅格布局已通过UnoCSS原子类实现 */
}

.player-card {
  background: rgba(15, 23, 42, 0.6);
  border: 1px solid rgba(107, 114, 128, 0.2);
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s ease;
  position: relative;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  
  &:hover {
    border-color: rgba(0, 168, 255, 0.4);
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 168, 255, 0.15);
  }
  
  &.winner {
    border-color: rgba(255, 193, 7, 0.6);
    background: rgba(255, 193, 7, 0.1);
    box-shadow: 0 0 20px rgba(255, 193, 7, 0.3);
    
    .player-name {
      color: #FFC107;
    }
  }
  
  &.active {
    border-color: rgba(0, 168, 255, 0.6);
    background: rgba(0, 168, 255, 0.1);
  }
  
  &.calculating {
    .player-name {
      color: #FFC107;
    }
  }
}

.player-info {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
}

.avatar-container {
  position: relative;
  flex-shrink: 0;
  
  .player-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid rgba(0, 168, 255, 0.5);
    object-fit: cover;
  }
  
  .status-indicator {
    position: absolute;
    top: -3px;
    right: -3px;
    
    .winner-badge,
    .host-badge,
    .calculating-badge {
      width: 18px;
      height: 18px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 10px;
    }
    
    .winner-badge {
      background: linear-gradient(45deg, #FFC107, #FF9800);
      box-shadow: 0 2px 6px rgba(255, 193, 7, 0.4);
    }
    
    .host-badge {
      background: linear-gradient(45deg, #00A8FF, #0076B3);
      box-shadow: 0 2px 6px rgba(0, 168, 255, 0.4);
    }
    
    .calculating-badge {
      background: linear-gradient(45deg, #FFC107, #FF9800);
      box-shadow: 0 2px 6px rgba(255, 193, 7, 0.4);
      animation: pulse 1.5s infinite;
    }
  }
}

.player-details {
  flex: 1;
  min-width: 0;
  
  .player-name {
    font-size: 14px;
    font-weight: 600;
    color: white;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .player-score {
    font-size: 12px;
    font-weight: 700;
    color: #00A8FF;
  }
}

.battle-status {
  margin-bottom: 8px;
  
  .waiting-status,
  .battle-status,
  .calculating-status,
  .finished-status {
    display: flex;
    align-items: center;
    gap: 6px;
    
    .status-dot {
      width: 6px;
      height: 6px;
      border-radius: 50%;
    }
    
    .status-text {
      font-size: 11px;
      color: #9CA3AF;
      font-weight: 500;
    }
  }
  
  .calculation-progress {
    margin-top: 6px;
    
    .progress-bar {
      height: 3px;
      background: rgba(107, 114, 128, 0.3);
      border-radius: 2px;
      overflow: hidden;
      
      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #FFC107, #FF9800);
        border-radius: 2px;
        transition: width 0.3s ease;
      }
    }
  }
}

.items-preview {
  .items-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
    
    .items-title {
      font-size: 12px;
      color: #9CA3AF;
      font-weight: 500;
    }
    
    .items-count {
      font-size: 11px;
      color: #00A8FF;
      font-weight: 600;
    }
  }
  
  .items-grid {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
    
    .item-card {
      position: relative;
      width: 32px;
      height: 32px;
      
      .item-image {
        width: 100%;
        height: 100%;
        border-radius: 4px;
        border: 1px solid rgba(107, 114, 128, 0.3);
        object-fit: cover;
      }
      
      .item-price {
        position: absolute;
        bottom: -2px;
        right: -2px;
        background: rgba(0, 0, 0, 0.9);
        color: #00A8FF;
        font-size: 8px;
        font-weight: 700;
        padding: 2px 3px;
        border-radius: 2px;
        border: 1px solid rgba(0, 168, 255, 0.4);
      }
    }
    
    .more-items {
      width: 32px;
      height: 32px;
      background: rgba(107, 114, 128, 0.2);
      border: 1px dashed rgba(107, 114, 128, 0.4);
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      
      .more-text {
        font-size: 10px;
        color: #9CA3AF;
        font-weight: 600;
      }
    }
  }
}

.opening-animation {
  .animation-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
    
    .animation-title {
      font-size: 12px;
      color: #9CA3AF;
      font-weight: 500;
    }
    
    .animation-status {
      display: flex;
      align-items: center;
      gap: 4px;
      
      .status-dot {
        width: 6px;
        height: 6px;
        border-radius: 50%;
      }
      
      .status-text {
        font-size: 10px;
        color: #00A8FF;
        font-weight: 500;
      }
    }
  }
  
  .animation-container {
    height: 120px;
    overflow: hidden;
    border-radius: 6px;
    background: rgba(15, 23, 42, 0.8);
    border: 1px solid rgba(0, 168, 255, 0.3);
    
    // 确保SmoothCaseAnimation组件在容器内正确显示
    :deep(.smooth-case-animation) {
      height: 100%;
      
      .animation-container {
        height: 100%;
        min-height: auto;
      }
      
      .track-container {
        height: 80px;
        min-height: auto;
      }
      
      .step-indicator {
        display: none; // 隐藏步骤提示，节省空间
      }
      
      .control-buttons {
        display: none; // 隐藏控制按钮，节省空间
      }
    }
  }
}

// 动画
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .battle-vertical-player-list {
    padding: 16px;
  }
  
  .player-card {
    padding: 12px;
    min-height: 100px;
  }
  
  .player-info {
    gap: 10px;
  }
  
  .avatar-container .player-avatar {
    width: 36px;
    height: 36px;
  }
  
  .player-name {
    font-size: 13px;
  }
  
  .player-score {
    font-size: 11px;
  }
  
  .items-grid {
    gap: 4px;
    
    .item-card,
    .more-items {
      width: 28px;
      height: 28px;
    }
  }
}

@media (max-width: 480px) {
  .player-card {
    padding: 10px;
    min-height: 90px;
  }
  
  .avatar-container .player-avatar {
    width: 32px;
    height: 32px;
  }
  
  .player-name {
    font-size: 12px;
  }
  
  .player-score {
    font-size: 10px;
  }
}
</style> 