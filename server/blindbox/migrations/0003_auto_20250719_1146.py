# Generated by Django 3.2.25 on 2025-07-19 03:46

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('blindbox', '0002_auto_20250718_1647'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='blindbox',
            options={'ordering': ('case_type', 'order'), 'verbose_name': '盲盒', 'verbose_name_plural': 'BlindBox'},
        ),
        migrations.AlterModelOptions(
            name='blindboxdrop',
            options={'verbose_name': '盲盒掉落', 'verbose_name_plural': 'BlindBoxDrop'},
        ),
        migrations.AlterModelOptions(
            name='blindboxgame',
            options={'verbose_name': '盲盒游戏', 'verbose_name_plural': 'BlindBoxGame'},
        ),
        migrations.AlterModelOptions(
            name='blindboxrecord',
            options={'verbose_name': '盲盒记录', 'verbose_name_plural': 'Blind Box Record'},
        ),
        migrations.AlterModelOptions(
            name='blindboxtype',
            options={'ordering': ('category', 'order'), 'verbose_name': '箱子类型', 'verbose_name_plural': 'Case Type'},
        ),
        migrations.AlterModelOptions(
            name='casebot',
            options={'verbose_name': '箱子机器人配置', 'verbose_name_plural': 'Case Bot Config'},
        ),
        migrations.AlterModelOptions(
            name='freecaseconfig',
            options={'ordering': ('level',), 'verbose_name': '免费箱子配置', 'verbose_name_plural': 'Free Case Config'},
        ),
        migrations.AlterField(
            model_name='blindboxgame',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='blind_game', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
        migrations.AlterField(
            model_name='blindboxrecord',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='blind_records', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
        migrations.AlterField(
            model_name='casebot',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='blindbox_bot', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
    ]
