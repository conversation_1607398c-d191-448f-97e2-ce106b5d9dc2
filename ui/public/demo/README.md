# 开箱演示资源文件说明

## 需要的图片资源

### 箱子图片 (/demo/cases/)
- `ak47-case.jpg` - AK-47武器箱封面 (推荐尺寸: 300x200px)
- `awp-case.jpg` - AWP狙击箱封面 (推荐尺寸: 300x200px)
- `knife-case.jpg` - 刀具武器箱封面 (推荐尺寸: 300x200px)

### 物品图片 (/demo/items/)
- `item-1.jpg` 到 `item-10.jpg` - 武器/饰品图片 (推荐尺寸: 256x256px)
- 可以使用CSGO武器皮肤的截图或官方图片

### 用户头像 (/demo/avatars/)
- `demo-user.jpg` - 演示用户头像 (推荐尺寸: 64x64px)

## 临时解决方案

如果没有实际图片资源，可以使用以下占位服务：

### 箱子封面
```
https://via.placeholder.com/300x200/1a1a2e/16213e?text=AK47+Case
https://via.placeholder.com/300x200/1a1a2e/16213e?text=AWP+Case
https://via.placeholder.com/300x200/1a1a2e/16213e?text=Knife+Case
```

### 物品图片
```
https://via.placeholder.com/256x256/2c2c54/eee?text=Item+1
https://via.placeholder.com/256x256/2c2c54/eee?text=Item+2
...
```

### 用户头像
```
https://via.placeholder.com/64x64/4a4a4a/eee?text=User
```

## 实际使用建议

1. 从Steam Workshop或CSGO官方资源获取真实的武器皮肤图片
2. 使用统一的图片尺寸和格式(建议WebP格式以获得更好的性能)
3. 为不同稀有度的物品使用不同的边框颜色
4. 确保图片背景透明或与页面背景协调

## 开发注意事项

在开发环境中，如果图片加载失败，组件会显示占位符或错误处理提示。
生产环境中应确保所有图片资源都正确部署。
