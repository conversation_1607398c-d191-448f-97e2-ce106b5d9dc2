from django.conf import settings
from django.core.cache import cache
from django.db import models, transaction
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from steambase.enums import AppType, PackageState, TradeType, TradeState, PackageSourceType
from steambase.models import ModelBase, ItemBase, USER_MODEL
from steambase.utils import ParamException
from sitecfg.interfaces import get_exchange_rate, get_price_api_chosen
from ckeditor_uploader.fields import RichTextUploadingField


_item_price_key_base = 'item_price:{market_hash_name}'


class ItemInfo(ItemBase):
    RARITY_COLOR = (
        ('5e98d9', _("蓝色")),
        ('b0c3d9', _("灰色")),
        ('d32ce6', _("粉色")),
        ('eb4b4b', _("红色")),
        ('e4ae39', _("金色")),
        ('8847ff', _("紫色")),
        ('4b69ff', _("深蓝色"))
    )
    update_time = models.DateTimeField(_("update time"), editable=False, auto_now=True)
    description = RichTextUploadingField(_('description'), default=None, null=True, blank=True)
    content = RichTextUploadingField(_('content'), default=None, null=True, blank=True)    
    custom_icon = models.ImageField(_('custom icon'), max_length=256, default=None, null=True, blank=True,
                               upload_to='items/')
    custom_rarity = models.CharField(_("custom rarity"), max_length=128, default=None, null=True, blank=True,
                                     choices=RARITY_COLOR)
    #item_price = models.OneToOneField(ItemPrice, on_delete=models.CASCADE, verbose_name=_("item info"), related_name='item_price')
    # cate_id = models.ForeignKey('ItemCategory', on_delete=models.SET_NULL, verbose_name=_('Category'), null=True, blank=True, on_delete=models.SET_NULL, verbose_name=_('Quality'), null=True, blank=True, on_delete=models.SET_NULL, verbose_name=_('Rarity'), null=True, blank=True, on_delete=models.SET_NULL, verbose_name=_('Exterior'), null=True, blank=True, on_delete=models.SET_NULL, verbose_name=_('Category'), related_name='item_info', default=None, null=True, blank=True, on_delete=models.SET_NULL, verbose_name=_('Quality'), related_name='item_info', default=None, null=True, blank=True, on_delete=models.SET_NULL, verbose_name=_('Rarity'), related_name='item_info', default=None, null=True, blank=True, on_delete=models.SET_NULL, verbose_name=_('Exterior'), related_name='item_info', default=None, null=True, blank=True, on_delete=models.SET_NULL)

    def __str__(self):
        return self.market_name_cn or self.market_name

    class Meta:
        verbose_name = _('Item Info')
        verbose_name_plural = _('Item Info')

# 新增饰品分类，多级分类，代替type
class ItemCategory(models.Model):
    cate_id = models.AutoField(_('Cate Id'), primary_key=True)
    cate_name = models.CharField(_('Cate Name'), max_length=128)
    parent = models.ForeignKey('self', on_delete=models.SET_NULL, null=True, blank=True)
    level = models.IntegerField(_('Level'), default=0)
    is_show = models.BooleanField(_('Is Show'), default=True)
    sort = models.IntegerField(_('Sort'), default=0)
    icon = models.CharField(_('Icon'), max_length=256, default=None, null=True, blank=True)


    class Meta:
        verbose_name = _('Item Category')
        verbose_name_plural = _('Item Category')

# 新增品质分类，代替quality
class ItemQuality(models.Model):
    quality_id = models.AutoField(_('Quality Id'), primary_key=True)
    quality_name = models.CharField(_('Quality Name'), max_length=128)
    quality_color = models.CharField(_('Quality Color'), max_length=32, default=None, null=True, blank=True)
    is_show = models.BooleanField(_('Is Show'), default=True)
    sort = models.IntegerField(_('Sort'), default=0)

# 新增稀有度分类，代替rarity
class ItemRarity(models.Model):
    rarity_id = models.AutoField(_('Rarity Id'), primary_key=True)
    rarity_name = models.CharField(_('Rarity Name'), max_length=128)
    rarity_color = models.CharField(_('Rarity Color'), max_length=32, default=None, null=True, blank=True)
    is_show = models.BooleanField(_('Is Show'), default=True)
    sort = models.IntegerField(_('Sort'), default=0)

# 新增外观分类，代替exterior
class ItemExterior(models.Model):
    exterior_id = models.AutoField(_('Exterior Id'), primary_key=True)
    exterior_name = models.CharField(_('Exterior Name'), max_length=128)
    exterior_color = models.CharField(_('Exterior Color'), max_length=32, default=None, null=True, blank=True)
    is_show = models.BooleanField(_('Is Show'), default=True)
    sort = models.IntegerField(_('Sort'), default=0)



class ItemPrice(models.Model):
    update_time = models.DateTimeField(_("update time"), editable=False, auto_now=True)
    item_info = models.OneToOneField(ItemInfo, on_delete=models.CASCADE, verbose_name=_("item info"), related_name='item_price')
    price = models.FloatField(_('price'), default=0.0)
    custom = models.BooleanField(_('enable custom'), default=False)
    custom_price = models.FloatField(_('custom price(USD)'), default=0.0)
    custom_discount = models.FloatField(_('custom discount(%)'), default=100)
    wxp_price = models.FloatField(_('waxpeer market price(USD)-0'), default=0.0)
    zbt_price = models.FloatField(_('zbt market price(USD)-0'), default=0.0)
    steam_prices_net_price = models.FloatField(_('Steam market price(USD)-0'), default=0.0)
    steam_normal_price_dollar = models.FloatField(_('Steam normal price(USD)-10'), default=0.0)
    steam_sale_price_dollar = models.FloatField(_('Steam sale price(USD)-11'), default=0.0)
    lock_price = models.BooleanField(_('lock price'), default=False)
    lock_price_time = models.DateTimeField(_("lock price time"), null=True, blank=True)



    class Meta:
        verbose_name = _('Item Price')
        verbose_name_plural = _('Item Price')

    def __str__(self):
        return self.item_info.market_name_cn or self.item_info.market_name

    def set_price(self):
        if self.custom:
            price = self.custom_price
            currency = 'USD'
        else:
            price = 0
            currency = 'USD'
            price_list = [
                {'price': self.zbt_price, 'currency': 'USD'},
            ]
            price_chosen_index = get_price_api_chosen()
            if 0 <= price_chosen_index <= len(price_list) - 1 and price_list[price_chosen_index]['price']:
                price = price_list[price_chosen_index]['price']
                currency = price_list[price_chosen_index]['currency']
            else:
                for p in price_list:
                    if p['price']:
                        price = p['price']
                        currency = p['currency']
                        break
            zbt_rate = get_exchange_rate("zbt")
            price = price * zbt_rate

        exchange_rate_to_cny = get_exchange_rate(currency)
        price_cny = price * exchange_rate_to_cny
        item_price_currency = settings.ITEM_PRICE_CURRENCY
        exchange_rate_current = get_exchange_rate(item_price_currency)
        price = price_cny / exchange_rate_current
        self.price = round(price * self.custom_discount / 100, 2)
        item_price_key = _item_price_key_base.format(market_hash_name=self.item_info.market_hash_name)
        cache.set(item_price_key, self.price, settings.DAY_REDIS_TIMEOUT)
        self.save()


class PackageItem(ModelBase):
    PACKAGE_STATE = (
        (PackageState.Initialed.value, _('Initialed')),
        (PackageState.Available.value, _('Available')),
        (PackageState.Blocked.value, _('Blocked')),
        (PackageState.Gaming.value, _('Gaming')),
        (PackageState.Withdrawing.value, _('Withdrawing')),
        (PackageState.Withdrawn.value, _('Withdrawn')),
        (PackageState.Exchanged.value, _('Exchanged')),
        (PackageState.Invalid.value, _('Invalid')),
    )
    SOURCE_STATE = (
        (PackageSourceType.Case.value, _('Case')),
        # (PackageSourceType.LuckyBox.value, _('LuckyBox')),
        # (PackageSourceType.Shop.value, _('Shop')),
        # (PackageSourceType.Market.value, _('Market')),
        (PackageSourceType.Roll.value, _('Roll')),
        (PackageSourceType.BattleRoom.value, _('BattleRoom')),
        (PackageSourceType.Giveaway.value, _('Giveaway')),
    )
    user = models.ForeignKey(USER_MODEL, on_delete=models.SET_NULL, verbose_name=_("user"), default=None, null=True, blank=True,
                             related_name='package_items')
    item_info = models.ForeignKey(ItemInfo, on_delete=models.CASCADE, verbose_name=_("item info"))
    assetid = models.CharField(_('assetid'), max_length=64, default=0, null=True, blank=True)
    instanceid = models.CharField(_('instanceid'), max_length=64, default=0, null=True, blank=True)
    state = models.SmallIntegerField(_('status'), default=PackageState.Initialed.value, choices=PACKAGE_STATE)
    bot_steamid = models.CharField(_('bot Steamid'), max_length=64, default=None, null=True, blank=True)
    source = models.SmallIntegerField(_('status'), default=0, choices=SOURCE_STATE)
    part = models.BooleanField(_("part of item"), default=False)
    amount = models.FloatField(_('amount'), default=0.0)
    case_name = models.CharField(_('case name'), max_length=128, default=None, null=True, blank=True)
    case_cover = models.CharField(_('case cover'), max_length=128, default=None, null=True, blank=True)
    case_key = models.CharField(_('case key'), max_length=128, default=None, null=True, blank=True)

    @classmethod
    def update_state(cls, uids, origin_state, target_state, user=None):
        with transaction.atomic():
            query = {
                'uid__in': uids
            }
            if user:
                query['user'] = user
            items = cls.objects.select_for_update().filter(**query)
            items = items.filter(state=origin_state)
            if items.count() != len(uids):
                raise ParamException(_('Invalid items'))
            for item in items:
                item.state = target_state
                item.save()
            return items

    def __str__(self):
        return self.item_info.market_name_cn or self.item_info.market_name

    class Meta:
        verbose_name = _("Package Item")
        verbose_name_plural = _("Package Item")


class PackageItemPriceRate(models.Model):
    level = models.IntegerField(_('level'), default=0)
    price_rate = models.FloatField(_('price rate(%)'), default=0)
    min_price = models.FloatField(_('min price'), default=0)
    max_price = models.FloatField(_('max price'), default=0)

    class Meta:
        ordering = ('level',)
        verbose_name = _('Item Price Rate Config')
        verbose_name_plural = _('Item Price Rate Config')

    def __str__(self):
        return str(self.level)



class ExchangeRecord(ModelBase):
    CURRENCY_STATE = (
        (0, _('Coins')),
        (1, _('Diamond'))
    )
    user = models.ForeignKey(USER_MODEL, on_delete=models.CASCADE, verbose_name=_("user"), related_name='exchange_records')
    item_info = models.ForeignKey(ItemInfo, on_delete=models.CASCADE, verbose_name=_('item info'), related_name='exchange_records')
    package = models.ForeignKey(PackageItem, on_delete=models.SET_NULL, verbose_name=_('package item'), default=None, null=True, blank=True,
                                related_name='exchange_records')
    price = models.FloatField(_('price'), default=0)
    currency = models.SmallIntegerField(_('currency'), default=0, choices=CURRENCY_STATE)

    def __str__(self):
        return self.user.username

    class Meta:
        ordering = ('-create_time',)
        verbose_name = _("Exchange Record")
        verbose_name_plural = _("Exchange Record")


class ShopRecord(ModelBase):
    user = models.ForeignKey(USER_MODEL, on_delete=models.CASCADE, verbose_name=_("user"), related_name='shop_records')
    item_info = models.ForeignKey(ItemInfo, on_delete=models.CASCADE, verbose_name=_('item info'), related_name='shop_records')
    package = models.ForeignKey(PackageItem, on_delete=models.SET_NULL, verbose_name=_('package item'), default=None, null=True, blank=True,
                                related_name='shop_records')
    price = models.FloatField(_('price'), default=0)

    def __str__(self):
        return self.user.username

    class Meta:
        ordering = ('-create_time',)
        verbose_name = _("Shop record")
        verbose_name_plural = _("Shop record")


class TradeRecord(ModelBase):
    TRADE_TYPE = (
        (TradeType.Deposit.value, _('Deposit')),
        (TradeType.Withdraw.value, _('Withdraw'))
    )
    TRADE_STATE = (
        (TradeState.Initialed.value, _('Initialed')),
        (TradeState.Accepted.value, _('Accepted')),
        (TradeState.Cancelled.value, _('Cancelled')),
        (TradeState.Submitted.value, _('Submitted')),
        (TradeState.TradeNoUpdated.value, _('TradeNoUpdated')),
        (TradeState.Active.value, _('Active')),
    )
    user = models.ForeignKey(USER_MODEL, on_delete=models.CASCADE, verbose_name=_("user"), related_name='trade_records')
    tradeurl = models.CharField(_('trade url'), max_length=256)
    trade_type = models.SmallIntegerField(_('trade type'), choices=TRADE_TYPE)
    amount = models.FloatField(_('amount'), default=0.0)
    state = models.SmallIntegerField(_('trade status'), default=TradeState.Initialed.value, choices=TRADE_STATE)
    trade_no = models.CharField(_('trade No.'), max_length=32, default=None, null=True, blank=True)
    security_code = models.CharField(_('security code'), max_length=32, default=None, null=True, blank=True)
    bot_steamid = models.CharField(_('bot Steamid'), max_length=64, default=None, null=True, blank=True)
    bot_msg = models.CharField(_('bot message'), max_length=128, default=None, null=True, blank=True)
    trade_time = models.DateTimeField(_('trade time'), default=timezone.now)
    review = models.BooleanField(_('review'), default=False)

    def __str__(self):
        return self.uid

    class Meta:
        ordering = ('-create_time',)
        verbose_name = _('Trade Record')
        verbose_name_plural = _('Trade Record')


class TradeItem(ModelBase):
    item_info = models.ForeignKey(ItemInfo, on_delete=models.CASCADE, verbose_name=_("item info"))
    record = models.ForeignKey(TradeRecord, on_delete=models.CASCADE, verbose_name=_('trade record'), related_name='trade_items')
    package = models.ForeignKey(PackageItem, on_delete=models.SET_NULL, verbose_name=_('package item'), default=None, null=True, blank=True)
    assetid = models.CharField(_('assetid'), max_length=64)
    instanceid = models.CharField(_('instanceid'), max_length=64)
    price = models.FloatField(_('price'), default=0.0)

    def __str__(self):
        return self.item_info.market_name_cn or self.item_info.market_name

    class Meta:
        verbose_name = _('Trade Item')
        verbose_name_plural = _('Trade Item')


class TradeBot(models.Model):
    remark = models.CharField(_('remark'), max_length=128)
    steamid = models.CharField(_('Steamid'), max_length=128, unique=True)
    trade_url = models.CharField(_('trade url'), max_length=256)
    enable = models.BooleanField(_('enable'), default=True)
    enable_deposit = models.BooleanField(_('enable deposit'), default=True)
    enable_withdraw = models.BooleanField(_('enable withdraw'), default=True)
    account = models.CharField(_('account'), max_length=128)
    password = models.CharField(_('password'), max_length=128)
    shared_secret = models.CharField(_('shared secret'), max_length=128)
    identity_secret = models.CharField(_('identity secret'), max_length=128)

    def __str__(self):
        return self.remark

    class Meta:
        verbose_name = _('Trade Bot Config')
        verbose_name_plural = _('Trade Bot Config')


class TradeBotInventory(ModelBase):
    item_info = models.ForeignKey(ItemInfo, on_delete=models.CASCADE, verbose_name=_("item info"))
    assetid = models.CharField(_('assetid'), max_length=64)
    instanceid = models.CharField(_('instanceid'), max_length=64)
    bot_steamid = models.CharField(_('bot Steamid'), max_length=64, default=None, null=True, blank=True)
    matching = models.BooleanField(_('matching'), default=False)
    enable = models.BooleanField(_('enable'), default=True)

    def __str__(self):
        return self.item_info.market_name_cn or self.item_info.market_name

    class Meta:
        verbose_name = _('Trade Bot Inventory')
        verbose_name_plural = _('Trade Bot Inventory')


class ShopBot(models.Model):
    user = models.OneToOneField(USER_MODEL, on_delete=models.CASCADE, verbose_name=_("account"), related_name='shop_bot')
    remark = models.CharField(_('remark'), max_length=128)
    enable = models.BooleanField(_('enable'), default=True)

    def __str__(self):
        return self.remark

    class Meta:
        verbose_name = _('Shop Bot Config')
        verbose_name_plural = _('Shop Bot Config')


class BlackList(models.Model):
    user = models.OneToOneField(USER_MODEL, on_delete=models.CASCADE, verbose_name=_("user"), related_name='blacklist')
    enable = models.BooleanField(_('enable'), default=True)
    ban_deposit = models.BooleanField(_('ban deposit'), default=False)
    ban_withdraw = models.BooleanField(_('ban withdraw'), default=False)
    ban_exchange = models.BooleanField(_('ban exchange'), default=False)
    ban_shop = models.BooleanField(_('ban shop'), default=False)

    def __str__(self):
        return self.user.username

    class Meta:
        verbose_name = _('BlackList')
        verbose_name_plural = _('BlackList')


class PackageStatisticsDay(ModelBase):
    TRADE_TYPE = (
        (TradeType.Deposit.value, _('Deposit')),
        (TradeType.Withdraw.value, _('Withdraw'))
    )
    date = models.DateField(_("date"), default=timezone.now)
    amount = models.FloatField(_("amount"), default=0.0)
    trade_type = models.SmallIntegerField(_('trade type'), choices=TRADE_TYPE)

    @classmethod
    def update_amount(cls, amount, trade_type):
        with transaction.atomic():
            today = timezone.localdate()
            if not cls.objects.filter(date=today, trade_type=trade_type):
                cls.objects.create(date=today, trade_type=trade_type)
            record = cls.objects.select_for_update().get(date=today, trade_type=trade_type)
            amount = round(amount, 2)
            record.amount += amount
            record.save()

    def __str__(self):
        return str(self.date)

    class Meta:
        ordering = ('-create_time',)
        verbose_name = _('Item Statistics Day')
        verbose_name_plural = _('Item Statistics Day')


class PackageStatisticsMonth(ModelBase):
    TRADE_TYPE = (
        (TradeType.Deposit.value, _('Deposit')),
        (TradeType.Withdraw.value, _('Withdraw'))
    )
    date = models.DateField(_("date"), default=timezone.now)
    amount = models.FloatField(_("amount"), default=0.0)
    trade_type = models.SmallIntegerField(_('trade type'), choices=TRADE_TYPE)

    @classmethod
    def update_amount(cls, amount, trade_type):
        with transaction.atomic():
            today = timezone.localdate()
            month_day_first = today.replace(day=1)
            if not cls.objects.filter(date=month_day_first, trade_type=trade_type):
                cls.objects.create(date=month_day_first, trade_type=trade_type)
            record = cls.objects.select_for_update().get(date=month_day_first, trade_type=trade_type)
            amount = round(amount, 2)
            record.amount += amount
            record.save()

    def month(self):
        return self.date.strftime("%Y-%m")
    month.short_description = _('Month')

    def __str__(self):
        return self.month()

    class Meta:
        ordering = ('-create_time',)
        verbose_name = _('Item Statistics Month')
        verbose_name_plural = _('Item Statistics Month')


class UnlockTimeConfig(ModelBase):
    last_unlock_time = models.DateTimeField(_('last unlock time'))
    interval = models.IntegerField(_('interval(min)'))

    def __str__(self):
        return self.uid

    class Meta:
        ordering = ('-create_time',)
        verbose_name = _('Item Unlock Time Config')
        verbose_name_plural = _('Item Unlock Time Config')


class LockItemsStatistics(ModelBase):
    item_info = models.ForeignKey(ItemInfo, on_delete=models.CASCADE, verbose_name=_("item info"))
    count = models.IntegerField(_('Count'))

    def __str__(self):
        return self.item_info.market_name_cn or self.item_info.market_name

    class Meta:
        ordering = ('-create_time',)
        verbose_name = _('Lock Items Statistics')
        verbose_name_plural = _('Lock Items Statistics')


class ItemWhitelist(ModelBase):
    item_info = models.ForeignKey(ItemInfo, on_delete=models.SET_NULL, verbose_name=_('item info'), related_name='whitelist',
                                  default=None, null=True, blank=True)
    enable = models.BooleanField(_('enable'), default=True)

    class Meta:
        ordering = ('item_info__market_hash_name',)
        verbose_name = _('Item Whitelist')
        verbose_name_plural = _('Item Whitelist')

    def __str__(self):
        if self.item_info:
            return self.item_info.market_name_cn or self.item_info.market_name
        else:
            return '-'
