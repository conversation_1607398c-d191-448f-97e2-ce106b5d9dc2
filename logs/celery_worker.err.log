[2025-07-18 14:44:37,899: INFO/MainProcess] Task thworker.update_case_records_cache[2ba29d5e-fd3b-4969-a849-cdb76d4da993] received
[2025-07-18 14:44:37,901: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 14:44:37,911: INFO/MainProcess] Task thworker.check_b2c_trade_state[3df06f40-7269-4899-b88e-3e5eb52f33bd] received
[2025-07-18 14:44:37,926: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 14:44:37,926: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 14:44:37,942: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[3df06f40-7269-4899-b88e-3e5eb52f33bd] succeeded in 0.016960342996753752s: 'Success: B2C trade state checked'
[2025-07-18 14:44:37,949: INFO/MainProcess] Task thworker.update_case_records_cache[ba2bbecf-6008-4f29-a3ed-9cfe22945032] received
[2025-07-18 14:44:37,950: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 14:44:37,950: INFO/ForkPoolWorker-1] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 14:44:37,964: INFO/ForkPoolWorker-1] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 14:44:37,964: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 14:44:37,970: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[ba2bbecf-6008-4f29-a3ed-9cfe22945032] succeeded in 0.020668020995799452s: 'Success: Updated 100 case records cache'
[2025-07-18 14:44:47,344: ERROR/ForkPoolWorker-2] 更新 CaseRecord 缓存时发生错误: (1054, "Unknown column 'box_case.name_zh' in 'field list'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 173, in __get__
    rel_obj = self.field.get_cached_value(instance)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/mixins.py", line 15, in get_cached_value
    return instance._state.fields_cache[cache_name]
KeyError: 'case'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'box_case.name_zh' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/app/box/tasks.py", line 58, in update_case_records_cache
    batch_data = CaseRecordSerializer(batch_records, many=True).data
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 795, in data
    ret = super().data
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 249, in data
    self._data = self.to_representation(self.instance)
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 713, in to_representation
    return [
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 714, in <listcomp>
    self.child.to_representation(item) for item in iterable
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 538, in to_representation
    ret[field.field_name] = field.to_representation(attribute)
  File "/usr/local/lib/python3.8/site-packages/rest_framework/fields.py", line 1872, in to_representation
    return method(value)
  File "/app/box/serializers.py", line 117, in get_case_info
    return CaseSerializer(obj.case, fields=('id', 'case_key', 'name', 'name_en', 'name_zh_hans', 'cover', 'price', 'open_count')).data
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 187, in __get__
    rel_obj = self.get_object(instance)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 154, in get_object
    return qs.get(self.field.get_reverse_related_filter(instance))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 431, in get
    num = len(clone)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 262, in __len__
    self._fetch_all()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 1324, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 51, in __iter__
    results = compiler.execute_sql(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/compiler.py", line 1175, in execute_sql
    cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.OperationalError: (1054, "Unknown column 'box_case.name_zh' in 'field list'")
[2025-07-18 14:44:47,344: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 14:44:47,346: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[2ba29d5e-fd3b-4969-a849-cdb76d4da993] succeeded in 9.445320586004527s: 'Success: Updated 100 case records cache'
[2025-07-18 14:49:37,865: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[c57afc6a-b8cf-4012-b127-55883de8c032] received
[2025-07-18 14:49:37,866: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 14:49:37,870: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 14:49:37,872: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[c57afc6a-b8cf-4012-b127-55883de8c032] succeeded in 0.006330792006338015s: None
[2025-07-18 14:49:37,938: INFO/MainProcess] Task thworker.check_b2c_trade_state[10ff7151-2ecf-437d-a4b4-08ab532a741f] received
[2025-07-18 14:49:37,939: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 14:49:37,940: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 14:49:37,941: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 14:49:37,942: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[10ff7151-2ecf-437d-a4b4-08ab532a741f] succeeded in 0.0030760689987801015s: 'Success: B2C trade state checked'
[2025-07-18 14:49:38,085: INFO/MainProcess] Task thworker.update_case_records_cache[e6aa5fc5-fa60-495e-a5a5-a3cb2e8f52db] received
[2025-07-18 14:49:38,087: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 14:49:38,095: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 14:49:38,095: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 14:49:38,098: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[e6aa5fc5-fa60-495e-a5a5-a3cb2e8f52db] succeeded in 0.011972075008088723s: 'Success: Updated 100 case records cache'
[2025-07-18 14:54:38,141: INFO/MainProcess] Task thworker.update_case_records_cache[660ad45c-3098-4e32-91ce-64cc4e18a44e] received
[2025-07-18 14:54:38,142: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 14:54:38,192: INFO/MainProcess] Task thworker.check_b2c_trade_state[7bbe7343-272a-4d94-90df-769b330df454] received
[2025-07-18 14:54:38,193: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 14:54:38,194: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 14:54:38,210: INFO/ForkPoolWorker-1] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 14:54:38,239: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[7bbe7343-272a-4d94-90df-769b330df454] succeeded in 0.046412556985160336s: 'Success: B2C trade state checked'
[2025-07-18 14:54:47,798: ERROR/ForkPoolWorker-2] 更新 CaseRecord 缓存时发生错误: (1054, "Unknown column 'box_case.name_zh' in 'field list'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 173, in __get__
    rel_obj = self.field.get_cached_value(instance)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/mixins.py", line 15, in get_cached_value
    return instance._state.fields_cache[cache_name]
KeyError: 'case'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'box_case.name_zh' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/app/box/tasks.py", line 58, in update_case_records_cache
    batch_data = CaseRecordSerializer(batch_records, many=True).data
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 795, in data
    ret = super().data
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 249, in data
    self._data = self.to_representation(self.instance)
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 713, in to_representation
    return [
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 714, in <listcomp>
    self.child.to_representation(item) for item in iterable
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 538, in to_representation
    ret[field.field_name] = field.to_representation(attribute)
  File "/usr/local/lib/python3.8/site-packages/rest_framework/fields.py", line 1872, in to_representation
    return method(value)
  File "/app/box/serializers.py", line 117, in get_case_info
    return CaseSerializer(obj.case, fields=('id', 'case_key', 'name', 'name_en', 'name_zh_hans', 'cover', 'price', 'open_count')).data
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 187, in __get__
    rel_obj = self.get_object(instance)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 154, in get_object
    return qs.get(self.field.get_reverse_related_filter(instance))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 431, in get
    num = len(clone)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 262, in __len__
    self._fetch_all()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 1324, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 51, in __iter__
    results = compiler.execute_sql(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/compiler.py", line 1175, in execute_sql
    cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.OperationalError: (1054, "Unknown column 'box_case.name_zh' in 'field list'")
[2025-07-18 14:54:47,799: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 14:54:47,801: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[660ad45c-3098-4e32-91ce-64cc4e18a44e] succeeded in 9.65849095498561s: 'Success: Updated 100 case records cache'
[2025-07-18 14:59:38,187: INFO/MainProcess] Task thworker.update_case_records_cache[94359da9-7142-4578-b7cb-15948a7b5dd2] received
[2025-07-18 14:59:38,189: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 14:59:38,212: INFO/MainProcess] Task thworker.update_case_records_cache[57eca445-a826-4336-aaec-72f2385af33e] received
[2025-07-18 14:59:38,221: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 14:59:38,223: INFO/ForkPoolWorker-1] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 14:59:38,223: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 14:59:38,235: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[57eca445-a826-4336-aaec-72f2385af33e] succeeded in 0.014231871988158673s: 'Success: Updated 100 case records cache'
[2025-07-18 14:59:50,321: ERROR/ForkPoolWorker-2] 更新 CaseRecord 缓存时发生错误: (1054, "Unknown column 'box_case.name_zh' in 'field list'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 173, in __get__
    rel_obj = self.field.get_cached_value(instance)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/mixins.py", line 15, in get_cached_value
    return instance._state.fields_cache[cache_name]
KeyError: 'case'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'box_case.name_zh' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/app/box/tasks.py", line 58, in update_case_records_cache
    batch_data = CaseRecordSerializer(batch_records, many=True).data
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 795, in data
    ret = super().data
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 249, in data
    self._data = self.to_representation(self.instance)
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 713, in to_representation
    return [
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 714, in <listcomp>
    self.child.to_representation(item) for item in iterable
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 538, in to_representation
    ret[field.field_name] = field.to_representation(attribute)
  File "/usr/local/lib/python3.8/site-packages/rest_framework/fields.py", line 1872, in to_representation
    return method(value)
  File "/app/box/serializers.py", line 117, in get_case_info
    return CaseSerializer(obj.case, fields=('id', 'case_key', 'name', 'name_en', 'name_zh_hans', 'cover', 'price', 'open_count')).data
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 187, in __get__
    rel_obj = self.get_object(instance)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 154, in get_object
    return qs.get(self.field.get_reverse_related_filter(instance))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 431, in get
    num = len(clone)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 262, in __len__
    self._fetch_all()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 1324, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 51, in __iter__
    results = compiler.execute_sql(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/compiler.py", line 1175, in execute_sql
    cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.OperationalError: (1054, "Unknown column 'box_case.name_zh' in 'field list'")
[2025-07-18 14:59:50,321: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 14:59:50,333: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[94359da9-7142-4578-b7cb-15948a7b5dd2] succeeded in 12.143739115999779s: 'Success: Updated 100 case records cache'
[2025-07-18 15:00:00,013: INFO/MainProcess] Task thworker.hourly_lottery[1fce8b0f-0ba8-4d4d-a3a0-98bd55ab8218] received
[2025-07-18 15:00:00,014: INFO/ForkPoolWorker-2] ------------------------ 整点抽奖 ------------------------
[2025-07-18 15:00:00,015: INFO/ForkPoolWorker-2] [Celery] 每小时抽奖任务完成
[2025-07-18 15:00:00,017: INFO/ForkPoolWorker-2] Task thworker.hourly_lottery[1fce8b0f-0ba8-4d4d-a3a0-98bd55ab8218] succeeded in 0.0033297409827355295s: 'Success: Hourly lottery completed'
[2025-07-18 15:04:38,207: INFO/MainProcess] Task thworker.update_case_records_cache[10df4ad4-6243-424d-9347-6e1a571fe987] received
[2025-07-18 15:04:38,208: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 15:04:38,233: INFO/MainProcess] Task thworker.check_b2c_trade_state[b69b376a-9b04-4027-94b8-5faa7920adc5] received
[2025-07-18 15:04:38,234: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 15:04:38,235: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 15:04:38,237: INFO/ForkPoolWorker-1] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 15:04:38,238: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[b69b376a-9b04-4027-94b8-5faa7920adc5] succeeded in 0.004725054022856057s: 'Success: B2C trade state checked'
[2025-07-18 15:06:36,305: ERROR/ForkPoolWorker-2] 更新 CaseRecord 缓存时发生错误: (1054, "Unknown column 'box_case.name_zh' in 'field list'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 173, in __get__
    rel_obj = self.field.get_cached_value(instance)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/mixins.py", line 15, in get_cached_value
    return instance._state.fields_cache[cache_name]
KeyError: 'case'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'box_case.name_zh' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/app/box/tasks.py", line 58, in update_case_records_cache
    batch_data = CaseRecordSerializer(batch_records, many=True).data
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 795, in data
    ret = super().data
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 249, in data
    self._data = self.to_representation(self.instance)
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 713, in to_representation
    return [
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 714, in <listcomp>
    self.child.to_representation(item) for item in iterable
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 538, in to_representation
    ret[field.field_name] = field.to_representation(attribute)
  File "/usr/local/lib/python3.8/site-packages/rest_framework/fields.py", line 1872, in to_representation
    return method(value)
  File "/app/box/serializers.py", line 117, in get_case_info
    return CaseSerializer(obj.case, fields=('id', 'case_key', 'name', 'name_en', 'name_zh_hans', 'cover', 'price', 'open_count')).data
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 187, in __get__
    rel_obj = self.get_object(instance)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 154, in get_object
    return qs.get(self.field.get_reverse_related_filter(instance))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 431, in get
    num = len(clone)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 262, in __len__
    self._fetch_all()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 1324, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 51, in __iter__
    results = compiler.execute_sql(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/compiler.py", line 1175, in execute_sql
    cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.OperationalError: (1054, "Unknown column 'box_case.name_zh' in 'field list'")
[2025-07-18 15:06:36,306: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 15:06:36,307: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[10df4ad4-6243-424d-9347-6e1a571fe987] succeeded in 118.09932349898736s: 'Success: Updated 100 case records cache'
[2025-07-18 15:09:38,268: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[08dfe966-5681-47f9-ad73-af73654bbf3d] received
[2025-07-18 15:09:38,270: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 15:09:38,271: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 15:09:38,276: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[08dfe966-5681-47f9-ad73-af73654bbf3d] succeeded in 0.006222575000720099s: None
[2025-07-18 15:09:38,281: INFO/MainProcess] Task thworker.update_case_records_cache[32b48367-b3d4-48a4-9062-2c897324e9a1] received
[2025-07-18 15:09:38,284: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 15:09:38,287: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 15:09:38,287: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 15:09:38,293: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[32b48367-b3d4-48a4-9062-2c897324e9a1] succeeded in 0.00920878499164246s: 'Success: Updated 100 case records cache'
[2025-07-18 15:09:38,295: INFO/MainProcess] Task thworker.check_b2c_trade_state[0db1bfd9-5aca-4c54-99bd-b9cd06b182c3] received
[2025-07-18 15:09:38,296: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 15:09:38,296: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 15:09:38,297: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 15:09:38,298: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[0db1bfd9-5aca-4c54-99bd-b9cd06b182c3] succeeded in 0.002373140014242381s: 'Success: B2C trade state checked'
[2025-07-18 15:14:38,337: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[72dffd09-1a53-4b10-809d-2be791f1041d] received
[2025-07-18 15:14:38,338: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 15:14:38,350: ERROR/ForkPoolWorker-2] 清理 RollRoomBet 记录时发生错误: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
[2025-07-18 15:14:38,353: ERROR/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[72dffd09-1a53-4b10-809d-2be791f1041d] raised unexpected: OperationalError(1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
  File "/app/roll/tasks.py", line 37, in cleanup_duplicate_roll_room_bets
    latest_room = RollRoom.objects.filter(enable=True).order_by('-create_time').first()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 674, in first
    for obj in (self if self.ordered else self.order_by('pk'))[:1]:
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 280, in __iter__
    self._fetch_all()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 1324, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 51, in __iter__
    results = compiler.execute_sql(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/compiler.py", line 1175, in execute_sql
    cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.OperationalError: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
[2025-07-18 15:14:38,362: INFO/MainProcess] Task thworker.update_case_records_cache[b951fea9-4519-4875-b039-dcb27b793127] received
[2025-07-18 15:14:38,364: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 15:14:38,368: INFO/MainProcess] Task thworker.update_case_records_cache[757637cf-9585-4145-adc9-42ad6e4a6c13] received
[2025-07-18 15:14:38,368: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 15:14:38,369: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 15:14:38,370: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 15:14:38,371: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[757637cf-9585-4145-adc9-42ad6e4a6c13] succeeded in 0.003040261013666168s: 'Success: Updated 100 case records cache'
[2025-07-18 15:15:28,432: INFO/ForkPoolWorker-1] 没有新的 CaseRecord 更新。
[2025-07-18 15:15:28,432: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 15:15:28,436: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[b951fea9-4519-4875-b039-dcb27b793127] succeeded in 50.072432439978s: 'Success: Updated 100 case records cache'
