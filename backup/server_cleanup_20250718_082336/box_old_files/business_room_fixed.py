#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复后的开箱对战业务逻辑主文件
集成所有安全修复和优化措施
"""

import json
import logging
import threading
import time
import hashlib
from datetime import datetime, timedelta
from decimal import Decimal
import traceback

from django.core.cache import cache
from django.db import connection, transaction
from django.db.models import Q, F, Sum, Count
from django.utils import timezone as django_timezone

# 导入修复模块
from box.compat_async import (
    get_async_processor, 
    schedule_round_processing_safe,
    AsyncBattleProcessor
)
from box.message_utils import (
    build_opening_start_message_safe,
    build_round_result_message_safe,
    send_websocket_message_safe,
    sanitize_websocket_data
)
from box.system_fixes import (
    get_timezone_aware_now,
    make_datetime_aware,
    safe_database_query,
    BattleSystemHealthChecker
)
from box.battle_config import BattleSystemConfig, BattleSystemMetrics

from steambase.enums import GameState
from box.models import <PERSON><PERSON><PERSON>, CaseRoomBet, CaseRoomRound, CaseRoomItem

logger = logging.getLogger(__name__)


class FixedBattleRoomManager:
    """修复后的对战房间管理器"""
    
    def __init__(self):
        self.config = BattleSystemConfig()
        self.metrics = BattleSystemMetrics()
        self.async_processor = get_async_processor()
        self.health_checker = BattleSystemHealthChecker()
        
        # 锁管理
        self.locks = {}
        self.lock_timeout = 30
        
    def process_room_safe(self, room_uid: str):
        """安全处理房间"""
        start_time = time.time()
        
        try:
            # 获取分布式锁
            lock_key = f"room_processing:{room_uid}"
            
            with self._acquire_lock(lock_key):
                logger.debug(f"获取房间处理锁: {room_uid}")
                
                # 获取房间
                room = safe_database_query(
                    lambda: CaseRoom.objects.select_for_update().get(uid=room_uid)
                )
                
                if not room:
                    logger.warning(f"房间不存在: {room_uid}")
                    return False
                
                # 检查房间状态
                if room.state != GameState.Running.value:
                    logger.debug(f"房间状态不是运行中: {room_uid}, state={room.state}")
                    return False
                
                # 处理轮次
                return self._process_room_rounds(room)
                
        except Exception as e:
            logger.error(f"处理房间失败: room={room_uid}, error={e}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            self.metrics.increment_counter('room_processing_errors')
            return False
        finally:
            # 记录处理时间
            processing_time = time.time() - start_time
            self.metrics.record_timing('room_processing_time', processing_time)
    
    def _process_room_rounds(self, room):
        """处理房间轮次"""
        try:
            # 获取当前轮次
            current_round = self._get_current_round(room)
            
            if not current_round:
                logger.debug(f"房间没有可处理的轮次: {room.short_id}")
                return False
            
            # 检查轮次状态
            round_validator = RoundValidator(room, current_round)
            if not round_validator.validate():
                logger.warning(f"轮次验证失败: room={room.uid}, round={current_round.round}")
                return False
            
            # 处理轮次开始
            if not current_round.opened:
                return self._start_round_safe(room, current_round)
            else:
                return self._complete_round_safe(room, current_round)
                
        except Exception as e:
            logger.error(f"处理房间轮次失败: room={room.uid}, error={e}")
            return False
    
    def _start_round_safe(self, room, round_obj):
        """安全开始轮次"""
        try:
            # 检查消息是否已发送
            message_hash = self._generate_message_hash(
                'opening_start', room.uid, round_obj.round
            )
            
            if self._is_message_sent(message_hash):
                logger.warning(f"回合开始消息已发送过，跳过重复发送: "
                              f"room={room.uid}, round={round_obj.round}")
                return True
            
            # 生成动画ID
            animation_id = self._generate_animation_id()
            
            # 缓存动画状态
            self._cache_animation_status(room.uid, 'opening_animation')
            
            # 构建消息
            start_ts = int(time.time() * 1000) + 2000  # 2秒后开始
            sequence = int(time.time() * 1000)
            
            message = build_opening_start_message_safe(
                room_uid=room.uid,
                animation_id=animation_id,
                round_num=round_obj.round,
                start_ts=start_ts,
                sequence=sequence
            )
            
            # 发送WebSocket消息
            success = self._send_websocket_message(
                message, room.uid, 'opening_start'
            )
            
            if success:
                # 标记消息已发送
                self._mark_message_sent(message_hash)
                
                # 更新轮次状态
                with transaction.atomic():
                    round_obj.opened = True
                    round_obj.animation_id = animation_id
                    round_obj.start_time = make_datetime_aware(
                        datetime.fromtimestamp(start_ts / 1000)
                    )
                    round_obj.save()
                
                logger.info(f"Round {round_obj.round}/{room.case.round_count} "
                           f"started for room {room.short_id}")
                
                # 记录指标
                self.metrics.increment_counter('rounds_started_today')
                
                return True
            else:
                logger.error(f"发送开始消息失败: room={room.uid}")
                return False
                
        except Exception as e:
            logger.error(f"开始轮次失败: room={room.uid}, error={e}")
            return False
    
    def _complete_round_safe(self, room, round_obj):
        """安全完成轮次"""
        try:
            # 检查是否需要处理结果
            if not self._should_process_round_results(room, round_obj):
                return True
            
            # 处理开箱结果
            results = self._process_opening_results(room, round_obj)
            
            if not results:
                logger.error(f"处理开箱结果失败: room={room.uid}")
                return False
            
            # 发送结果消息
            return self._send_round_results(room, round_obj, results)
            
        except Exception as e:
            logger.error(f"完成轮次失败: room={room.uid}, error={e}")
            return False
    
    def _send_round_results(self, room, round_obj, results):
        """发送轮次结果"""
        try:
            # 检查消息去重
            message_hash = self._generate_message_hash(
                'round_result', room.uid, round_obj.round
            )
            
            if self._is_message_sent(message_hash):
                logger.debug(f"轮次结果消息已发送过: room={room.uid}")
                return True
            
            # 构建结果消息
            sequence = int(time.time() * 1000)
            
            message = build_round_result_message_safe(
                room_uid=room.uid,
                round_num=round_obj.round,
                animation_id=round_obj.animation_id or 'default',
                results=results,
                sequence=sequence
            )
            
            # 发送消息
            success = self._send_websocket_message(
                message, room.uid, 'round_result'
            )
            
            if success:
                # 标记消息已发送
                self._mark_message_sent(message_hash)
                
                logger.info(f"回合结果消息发送成功: room={room.uid}, "
                           f"round={round_obj.round}, "
                           f"animation_id={round_obj.animation_id}, "
                           f"sequence={sequence}")
                
                # 异步处理轮次完成
                self._handle_round_completion_async(room, round_obj)
                
                return True
            else:
                logger.error(f"发送结果消息失败: room={room.uid}")
                return False
                
        except Exception as e:
            logger.error(f"发送轮次结果失败: room={room.uid}, error={e}")
            return False
    
    def _handle_round_completion_async(self, room, round_obj):
        """异步处理轮次完成"""
        try:
            # 准备回调数据
            callback_data = {
                'room_uid': room.uid,
                'room_short_id': room.short_id,
                'round_number': round_obj.round,
                'total_rounds': room.case.round_count,
                'animation_id': round_obj.animation_id
            }
            
            # 调度异步处理
            future = schedule_round_processing_safe(
                room_uid=room.uid,
                round_num=round_obj.round,
                callback_data=callback_data,
                use_async=True
            )
            
            logger.info(f"异步轮次完成处理已调度: room={room.uid}")
            
        except Exception as e:
            logger.error(f"异步轮次推进失败，使用同步降级: room={room.short_id}, error={e}")
            self.metrics.increment_counter('async_fallbacks_today')
            
            # 同步降级处理
            try:
                self._handle_round_completion_sync(room, round_obj)
            except Exception as sync_e:
                logger.error(f"同步轮次完成处理失败: room={room.short_id}, error={sync_e}")
    
    def _handle_round_completion_sync(self, room, round_obj):
        """同步处理轮次完成"""
        try:
            with transaction.atomic():
                # 更新轮次状态
                round_obj.completed = True
                round_obj.completed_time = get_timezone_aware_now()
                round_obj.save()
                
                # 检查是否所有轮次完成
                completed_rounds = CaseRoomRound.objects.filter(
                    room=room, completed=True
                ).count()
                
                if completed_rounds >= room.case.round_count:
                    # 房间完成
                    self._complete_room(room)
                else:
                    # 准备下一轮次
                    self._prepare_next_round(room)
            
            logger.info(f"Round {round_obj.round}/{room.case.round_count} "
                       f"completed for room {room.short_id}")
            
        except Exception as e:
            logger.error(f"同步处理轮次完成失败: room={room.uid}, error={e}")
            raise
    
    def _send_websocket_message(self, message, room_uid, message_type):
        """发送WebSocket消息"""
        try:
            # 清理消息数据
            clean_message = sanitize_websocket_data(message)
            
            # 获取WebSocket连接管理器
            socket_manager = self._get_socket_manager()
            
            if socket_manager:
                success = send_websocket_message_safe(
                    socket_manager, clean_message, room_uid
                )
                
                if success:
                    self.metrics.increment_counter('messages_sent_today')
                    logger.info(f"Sent {message_type} to socket_id=None | "
                               f"room_uid={room_uid} | "
                               f"animation_id={clean_message.get('data', {}).get('animation_id')} | "
                               f"round={clean_message.get('data', {}).get('round')} | "
                               f"start_ts={clean_message.get('data', {}).get('start_ts')} | "
                               f"sequence={clean_message.get('data', {}).get('sequence')}")
                
                return success
            else:
                logger.warning(f"WebSocket管理器不可用，跳过消息发送: {message_type}")
                return False
                
        except Exception as e:
            logger.error(f"发送WebSocket消息失败: {e}")
            return False
    
    def _get_socket_manager(self):
        """获取WebSocket连接管理器"""
        try:
            # 这里应该返回实际的WebSocket管理器
            # 暂时返回None，需要根据实际的WebSocket实现来调整
            return None
        except Exception as e:
            logger.error(f"获取WebSocket管理器失败: {e}")
            return None
    
    @contextmanager
    def _acquire_lock(self, lock_key):
        """获取分布式锁"""
        redis_client = None
        acquired = False
        
        try:
            redis_client = cache._cache.get_client(write=True)
            
            # 尝试获取锁
            acquired = redis_client.set(
                lock_key, "locked", 
                nx=True, ex=self.lock_timeout
            )
            
            if not acquired:
                raise Exception(f"无法获取锁: {lock_key}")
            
            logger.debug(f"Acquired lock: {lock_key}")
            yield
            
        except Exception as e:
            logger.error(f"锁操作失败: {e}")
            raise
        finally:
            if acquired and redis_client:
                try:
                    redis_client.delete(lock_key)
                    logger.debug(f"Released lock: {lock_key}")
                except Exception as e:
                    logger.error(f"释放锁失败: {e}")
    
    def _generate_message_hash(self, message_type, room_uid, round_num):
        """生成消息哈希"""
        content = f"{message_type}:{room_uid}:{round_num}"
        return hashlib.md5(content.encode()).hexdigest()
    
    def _is_message_sent(self, message_hash):
        """检查消息是否已发送"""
        key = f"dedup:msg:{message_hash}"
        return cache.get(key) is not None
    
    def _mark_message_sent(self, message_hash):
        """标记消息已发送"""
        key = f"dedup:msg:{message_hash}"
        cache.set(key, "sent", timeout=300)  # 5分钟过期
        logger.info(f"标记消息已发送: hash={message_hash}")
    
    def _generate_animation_id(self):
        """生成动画ID"""
        timestamp = int(time.time() * 1000)
        random_part = hex(random.randint(0, 0xFFFFFFFF))[2:]
        return f"anim_{timestamp}_{random_part}"
    
    def _cache_animation_status(self, room_uid, status):
        """缓存动画状态"""
        cache_key = f"battle:room:{room_uid}:status"
        cache.set(cache_key, status, timeout=600)  # 10分钟过期
        logger.info(f"动画状态缓存成功: room_uid={room_uid}, status={status}")
    
    def _get_current_round(self, room):
        """获取当前轮次"""
        return safe_database_query(
            lambda: CaseRoomRound.objects.filter(
                room=room, completed=False
            ).order_by('round').first()
        )
    
    def _should_process_round_results(self, room, round_obj):
        """检查是否应该处理轮次结果"""
        # 检查开箱时间
        if round_obj.start_time:
            time_since_start = get_timezone_aware_now() - round_obj.start_time
            return time_since_start.total_seconds() >= 2.0  # 至少2秒后
        return True
    
    def _process_opening_results(self, room, round_obj):
        """处理开箱结果"""
        try:
            # 获取投注记录
            bets = safe_database_query(
                lambda: CaseRoomBet.objects.filter(room=room).select_related('user')
            )
            
            results = []
            for bet in bets:
                # 为每个投注生成开箱结果
                item_result = self._generate_item_for_bet(bet, round_obj)
                if item_result:
                    results.append(item_result)
                    
                    # 记录开箱信息
                    logger.info(f"User {bet.user.username} open item "
                               f"{item_result['item_name']} in bet {bet.uid}")
            
            return results
            
        except Exception as e:
            logger.error(f"处理开箱结果失败: room={room.uid}, error={e}")
            return []
    
    def _generate_item_for_bet(self, bet, round_obj):
        """为投注生成物品"""
        try:
            # 这里应该实现实际的开箱逻辑
            # 暂时返回模拟数据
            return {
                'bet_uid': bet.uid,
                'user_id': bet.user.id,
                'username': bet.user.username,
                'item_name': "示例物品",
                'item_price': 10.0,
                'round': round_obj.round
            }
        except Exception as e:
            logger.error(f"生成物品失败: bet={bet.uid}, error={e}")
            return None
    
    def _complete_room(self, room):
        """完成房间"""
        try:
            with transaction.atomic():
                room.state = GameState.End.value
                room.end_time = get_timezone_aware_now()
                room.save()
            
            logger.info(f"房间完成: {room.short_id}")
            self.metrics.increment_counter('rooms_completed_today')
            
        except Exception as e:
            logger.error(f"完成房间失败: room={room.uid}, error={e}")
    
    def _prepare_next_round(self, room):
        """准备下一轮次"""
        try:
            # 这里可以添加下一轮次的准备逻辑
            logger.debug(f"准备下一轮次: room={room.short_id}")
        except Exception as e:
            logger.error(f"准备下一轮次失败: room={room.uid}, error={e}")


class RoundValidator:
    """轮次验证器"""
    
    def __init__(self, room, round_obj):
        self.room = room
        self.round_obj = round_obj
    
    def validate(self):
        """验证轮次"""
        try:
            # 检查轮次编号
            if self.round_obj.round < 1 or self.round_obj.round > self.room.case.round_count:
                logger.error(f"轮次编号无效: {self.round_obj.round}")
                return False
            
            # 检查房间状态
            if self.room.state != GameState.Running.value:
                logger.error(f"房间状态无效: {self.room.state}")
                return False
            
            logger.debug(f"轮次验证通过: room={self.room.uid}, "
                        f"round={self.round_obj.round}/{self.room.case.round_count}")
            return True
            
        except Exception as e:
            logger.error(f"轮次验证失败: {e}")
            return False


# 全局实例
fixed_battle_manager = FixedBattleRoomManager()


def process_battle_room_safe(room_uid):
    """安全处理对战房间"""
    return fixed_battle_manager.process_room_safe(room_uid)


def get_battle_room_manager():
    """获取对战房间管理器"""
    return fixed_battle_manager
