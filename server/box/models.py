import string
from django.conf import settings
from django.db import models, transaction
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from steambase.models import ModelBase, USER_MODEL
from steambase.enums import GameState, RoomType, BetTeamType, CaseRecordType, CaseCategoryType
from steambase.utils import current_year, current_week, id_generator
from package.models import ItemInfo
from ckeditor_uploader.fields import RichTextUploadingField



class CaseType(models.Model):
    type_id = models.AutoField(_('Type Id'), primary_key=True)
    type_name = models.CharField(_('Type Name'), max_length=128)
    enable = models.BooleanField(_('enable'), default=True)
    sort = models.IntegerField(_('order'), default=0)

    def __str__(self):
        return self.type_name
    class Meta:
        ordering = ('sort',)
        verbose_name = _('Case Type')
        verbose_name_plural = _('Case Type')
    

class CaseCategory(models.Model):
    cate_id = models.AutoField(_('Cate Id'), primary_key=True)
    cate_name = models.CharField(_('Cate Name'), max_length=128)
    enable = models.BooleanField(_('enable'), default=True)
    sort = models.IntegerField(_('order'), default=0)
    icon = models.CharField(_('icon'), max_length=128, default=None, null=True, blank=True)

    def __str__(self):
        return self.cate_name
    class Meta:
        ordering = ('sort',)
        verbose_name = _('Case Category')
        verbose_name_plural = _('Case Category')

class Case(ModelBase):    
    name = models.CharField(_('name'), max_length=128)
    # key 是sql保留字，用case_key代替
    # key = models.CharField(_('key'), max_length=128, unique=True)
    case_key = models.CharField(_('key'), max_length=128, unique=True)
    price = models.FloatField(_('price'), default=0)
    discount = models.SmallIntegerField(_('discount(%)'), default=100)
    present = models.ForeignKey(ItemInfo, verbose_name=_('present'), related_name='case_present',
                                default=None, null=True, blank=True)
    cover = models.CharField(_('cover image'), max_length=255, default=None, null=True, blank=True)
    item = models.ImageField(_('item image'), upload_to='cases', default=None, null=True, blank=True)
    back = models.ImageField(_('back image'), upload_to='cases', default=None, null=True, blank=True)
    case_type = models.ForeignKey(CaseType, verbose_name=_('case type'), related_name='cases', null=True, blank=True, on_delete=models.SET_NULL)
    case_category = models.ForeignKey(CaseCategory, verbose_name=_('case category'), related_name='cases', null=True, blank=True, on_delete=models.SET_NULL)
    sort = models.IntegerField(_('order'), default=0)
    # unlock是sql保留字，用is_show代替
    is_show = models.BooleanField(_('is show'), default=True)
    # unlock = models.BooleanField(_('unlock'), default=True)
    enable = models.BooleanField(_('enable'), default=True)
    enable_room = models.BooleanField(_('enable room'), default=False)
    # 管理员可开
    enable_admin = models.BooleanField(_('superuser enable'), default=False)  
    # enable_case = models.BooleanField(_('superuser enable case'), default=True)

    tag = models.CharField(_('tag'), max_length=8, default=None, null=True, blank=True)
    tag_color = models.CharField(_('标签颜色'), max_length=8, default=None, null=True, blank=True)
    open_count = models.IntegerField(_('open count'), default=0)
    cost = models.FloatField(_("case cost"), default=0)
    up_sill = models.FloatField(_("up sill"), default=0)
    down_sill = models.FloatField(_("down sill"), default=0)
    dyntic = models.FloatField(_("dyntic"), default=0)
    algorithm_enable = models.BooleanField(_('algorithm_enable'), default=False)
    content = RichTextUploadingField(_('content'), default=None, null=True, blank=True)

    seo_title = models.CharField(_('SEO标题'), default=None, null=True, max_length=128)
    seo_keywords = models.CharField(_('SEO关键词'), default=None, null=True, max_length=128)
    seo_description = models.TextField(_('SEO描述'), default=None, null=True, max_length=600)   

    


    class Meta:
        ordering = ('case_type', 'sort')
        verbose_name = _('Case')
        verbose_name_plural = _('Case')

    def __str__(self):
        return self.name

    def update_open_count(self, count=1):
        with transaction.atomic():
            self.open_count += count
            self.save()

class DropItem(ModelBase):
    DROP_TYPE = (
        (1, _('Base by count and chance')),
        (2, _('Base by count')),
        (3, _('Base by chance')),
    )
    ITEM_TYPE = (
        (1, _('Assets')),
        # (2, _('Diamond'))
    )
    item_info = models.ForeignKey(ItemInfo, verbose_name=_('item info'), related_name='case_drops',
                                  default=None, null=True, blank=True)
    case = models.ForeignKey(Case, verbose_name=_('case'), related_name='drops')
    show_chance = models.FloatField(_('开箱概率'), default=0)
    drop_chance_a = models.FloatField(_('drop chance a'), default=0)
    drop_chance_b = models.FloatField(_('drop chance b'), default=0)
    drop_chance_c = models.FloatField(_('drop chance c'), default=0)
    drop_chance_d = models.FloatField(_('drop chance d'), default=0)
    drop_chance_e = models.FloatField(_('drop chance e'), default=0)
    item_type = models.SmallIntegerField(_('item type'), default=1, choices=ITEM_TYPE)
    coins = models.FloatField(_('diamond'), default=0)
    # drop_type = models.SmallIntegerField(_('drop type'), default=1, choices=DROP_TYPE)
    # 价格统一到item_price
    # custom_enable = models.BooleanField(_("custom enable"), default=False)
    # custom_price =  models.FloatField(_("custom price"), default=0, null=True, blank=True)
    up_sill_drop = models.BooleanField(_("up sill drop"), default=False)
    down_sill_drop = models.BooleanField(_("down sill drop"), default=False)
    class Meta:
        verbose_name = _('Drop Item')
        verbose_name_plural = _('Drop Item')

    def __str__(self):
        if self.item_type == 1:
            return self.item_info.market_name_cn or self.item_info.market_name
        else:
            return '{} diamond'.format(self.coins)

def short_id_gen():
    return id_generator(size=9, chars=string.ascii_letters + string.digits)


class CaseRoom(ModelBase):
    GAME_STATE = (
        (GameState.Initial.value, _('Initial')),
        (GameState.Joinable.value, _('Joinable')),
        (GameState.Joining.value, _('Joining')),
        (GameState.Full.value, _('Full')),
        (GameState.Running.value, _('Running')),
        (GameState.End.value, _('End')),
        (GameState.Cancelled.value, _('Cancelled'))
    )
    ROOM_TYPE = (
        (RoomType.Battle.value, _('Battle')),
        (RoomType.Equality.value, _('Equality')),
        (RoomType.TeamBattle.value, _('TeamBattle'))
    )
    short_id = models.CharField(_("short id"), max_length=64, unique=True, default=short_id_gen, editable=False)
    user = models.ForeignKey(USER_MODEL, verbose_name=_("user"), related_name='case_room')
    max_joiner = models.IntegerField(_('max joiner'), default=0)
    price = models.FloatField(_('price'), default=0)
    state = models.SmallIntegerField(_("status"), default=GameState.Initial.value, choices=GAME_STATE)
    type = models.SmallIntegerField(_("type"), default=RoomType.Battle.value, choices=ROOM_TYPE)
    private = models.SmallIntegerField(_("private"), default=0)

    class Meta:
        verbose_name = _('Case Room')
        verbose_name_plural = _('Case Room')

    def __str__(self):
        return self.uid


class CaseRoomRound(ModelBase):
    room = models.ForeignKey(CaseRoom, verbose_name=_('room'), related_name='rounds')
    case = models.ForeignKey(Case, verbose_name=_('case'), related_name='room_rounds')
    opened = models.BooleanField(_('opened'), default=False)

    class Meta:
        verbose_name = _('Case Room Round')
        verbose_name_plural = _('Case Room Round')

    def __str__(self):
        return self.uid


class CaseRoomBet(ModelBase):
    BET_TEAM_TYPE = (
        (BetTeamType.Null.value, ('null team')),
        (BetTeamType.CT.value, ('team T')),
        (BetTeamType.T.value, ('team CT')),
    )
    room = models.ForeignKey(CaseRoom, verbose_name=_('room'), related_name='bets')
    user = models.ForeignKey(USER_MODEL, verbose_name=_("user"), related_name='case_room_bets',
                             default=None, null=True, blank=True)
    open_amount = models.FloatField(_("open amount"), default=None, null=True, blank=True)
    win_amount = models.FloatField(_("win amount"), default=None, null=True, blank=True)
    win_items_count = models.IntegerField(_("win items count"), default=None, null=True, blank=True)
    victory = models.SmallIntegerField(_("victory"), default=None, null=True, blank=True)
    team = models.SmallIntegerField(_("bet team"), choices=BET_TEAM_TYPE, default=0)

    class Meta:
        verbose_name = _('Case Room Bet')
        verbose_name_plural = _('Case Room Bet')

    def __str__(self):
        return self.uid


class CaseRoomItem(ModelBase):
    ITEM_TYPE = (
        (1, _('Assets')),
        (2, _('Free'))
    )
    room = models.ForeignKey(CaseRoom, verbose_name=_("room"), related_name='items', on_delete=models.CASCADE)
    round = models.ForeignKey(CaseRoomRound, verbose_name=_("round"), related_name='items', on_delete=models.CASCADE,
                             null=True, default=None, blank=True)
    bet = models.ForeignKey(CaseRoomBet, verbose_name=_('bet'), related_name='open_items',
                            null=True, default=None, blank=True)
    winner = models.ForeignKey(CaseRoomBet, verbose_name=_('winner'), related_name='win_items',
                               null=True, default=None, blank=True)
    item_info = models.ForeignKey(ItemInfo, verbose_name=_('item info'), related_name='case_room_items',
                                  default=None, null=True, blank=True)
    item_type = models.SmallIntegerField(_('item type'), default=1, choices=ITEM_TYPE)
    price = models.FloatField(_('price'), default=0)
    part = models.BooleanField(_("part price of item"), default=False)
    split = models.BooleanField(_("item need to split"), default=False)

    class Meta:
        verbose_name = _('Case Room Item')
        verbose_name_plural = _('Case Room Item')

    def __str__(self):
        if self.item_type == 1:
            return self.item_info.market_name_cn or self.item_info.market_name
        else:
            return '{} diamond'.format(self.price)


class CaseRecord(ModelBase):
    CASE_RECORD_TYPE = (
        (CaseRecordType.Case.value, _('Case')), 
        (CaseRecordType.Battle.value, _('Battle')), 
        (CaseRecordType.Equality.value, _('Equality')), 
        (CaseRecordType.TeamBattle.value, _('TeamBattle')), 
    )
    user = models.ForeignKey(USER_MODEL, verbose_name=_("user"), related_name='case_records',
                             default=None, null=True, blank=True)
    case = models.ForeignKey(Case, verbose_name=_('case'), related_name='records')
    item_info = models.ForeignKey(ItemInfo, verbose_name=_('item info'), related_name='case_records', default=None,
                                  null=True, blank=True)
    price = models.FloatField(_('price'), default=0)
    source = models.SmallIntegerField(_("type"), default=CaseRecordType.Case.value, choices=CASE_RECORD_TYPE)

    class Meta:
        verbose_name = _('Case Record')
        verbose_name_plural = _('Case Record')

    def __str__(self):
        return self.uid


class CaseBot(models.Model):
    user = models.OneToOneField(USER_MODEL, verbose_name=_("user"), related_name='case_bot')
    open_idle_min = models.IntegerField(_('open idle min(seconds)'), default=0)
    open_idle_max = models.IntegerField(_('open idle max(seconds)'), default=0)
    case = models.ManyToManyField(Case, verbose_name=_('case'), related_name='case_bot')
    remark = models.CharField(_('remark'), max_length=128)
    enable = models.BooleanField(_('enable'), default=True)

    class Meta:
        verbose_name = _('Case Bot Config')
        verbose_name_plural = _('Case Bot Config')

    def __str__(self):
        return self.remark


class CaseStatisticsDay(ModelBase):
    date = models.DateField(_("date"), default=timezone.now)
    amount = models.FloatField(_("amount"), default=0.0)

    @classmethod
    def update_amount(cls, amount):
        with transaction.atomic():
            today = timezone.localdate()
            if not cls.objects.filter(date=today):
                cls.objects.create(date=today)
            record = cls.objects.select_for_update().get(date=today)
            amount = round(amount, 2)
            record.amount += amount
            record.save()

    def __str__(self):
        return str(self.date)

    class Meta:
        ordering = ('-create_time',)
        verbose_name = _('Case Statistics Day')
        verbose_name_plural = _('Case Statistics Day')


class CaseStatisticsMonth(ModelBase):
    date = models.DateField(_("date"), default=timezone.now)
    amount = models.FloatField(_("amount"), default=0.0)

    @classmethod
    def update_amount(cls, amount):
        with transaction.atomic():
            today = timezone.localdate()
            month_day_first = today.replace(day=1)
            if not cls.objects.filter(date=month_day_first):
                cls.objects.create(date=month_day_first)
            record = cls.objects.select_for_update().get(date=month_day_first)
            amount = round(amount, 2)
            record.amount += amount
            record.save()

    def month(self):
        if isinstance(self.date, str):
            return self.date
        return self.date.strftime("%Y-%m")
    month.short_description = _('Month')

    def __str__(self):
        return self.month()

    class Meta:
        ordering = ('-create_time',)
        verbose_name = _('Case Statistics Month')
        verbose_name_plural = _('Case Statistics Month')


class FreeCaseConfig(models.Model):
    level = models.IntegerField(_('level'), default=0)
    min_amount = models.FloatField(_('min point'), default=0)
    max_amount = models.FloatField(_('max point'), default=0)
    case = models.ForeignKey(Case, verbose_name=_('case'), related_name='free_case')

    class Meta:
        ordering = ('level',)
        verbose_name = _('Free Case Config')
        verbose_name_plural = _('Free Case Config')

    def __str__(self):
        return str(self.level)


class FestivalCaseConfig(models.Model):
    level = models.IntegerField(_('level'), default=0)
    min_amount = models.FloatField(_('min charge total amount'), default=0)
    max_amount = models.FloatField(_('max charge total amount'), default=0)
    case = models.ForeignKey(Case, verbose_name=_('case'), related_name='festival_case')

    class Meta:
        ordering = ('level',)
        verbose_name = _('Festival Case Config')
        verbose_name_plural = _('Festival Case Config')

    def __str__(self):
        return str(self.level)


class FestivalCaseDate(models.Model):
    month = models.IntegerField(_('month'), default=1)
    day = models.IntegerField(_('day'), default=1)
    enable = models.BooleanField(_('enable'), default=True)

    class Meta:
        ordering = ('month', 'day')
        verbose_name = _('Festival Case Date')
        verbose_name_plural = _('Festival Case Date')

    def __str__(self):
        return '{}月{}日'.format(self.month, self.day)


class FestivalCaseRecord(ModelBase):
    user = models.ForeignKey(USER_MODEL, verbose_name=_('user'), related_name='festival_record')
    case = models.ForeignKey(Case, verbose_name=_('case'), related_name='festival_record')
    opened = models.BooleanField(_('opened'), default=False)
    expired = models.DateTimeField(_('expired time'))

    class Meta:
        verbose_name = _('Festival Case Record')
        verbose_name_plural = _('Festival Case Record')

    def __str__(self):
        return self.uid


class DropDayRank(ModelBase):
    user = models.ForeignKey(USER_MODEL, verbose_name=_('user'), related_name='drop_day_rank')
    date = models.DateField(_('date'), default=timezone.now)
    amount = models.FloatField(_('amount'), default=0)
    item_info = models.ForeignKey(ItemInfo, verbose_name=_('item info'), related_name='drop_day_rank', default=None,
                                  null=True, blank=True)

    class Meta:
        verbose_name = _('Drop Day Rank')
        verbose_name_plural = _('Drop Day Rank')

    def __str__(self):
        return self.uid


class IncomeDayRank(ModelBase):
    user = models.ForeignKey(USER_MODEL, verbose_name=_('user'), related_name='income_day_rank')
    date = models.DateField(_('date'), default=timezone.now)
    amount = models.FloatField(_('amount'), default=0)
    item_info = models.ForeignKey(ItemInfo, verbose_name=_('item info'), related_name='income_day_rank', default=None,
                                  null=True, blank=True)
    case = models.ForeignKey(Case, verbose_name=_('case'), related_name='income_day_rank')

    class Meta:
        verbose_name = _('Income Day Rank')
        verbose_name_plural = _('Income Day Rank')

    def __str__(self):
        return self.uid


class RoomDayRank(ModelBase):
    user = models.ForeignKey(USER_MODEL, verbose_name=_('user'), related_name='room_day_rank')
    date = models.DateField(_('date'), default=timezone.now)
    amount = models.FloatField(_('amount'), default=0)
    room = models.ForeignKey(CaseRoom, verbose_name=_('room'), related_name='room_day_rank')

    class Meta:
        verbose_name = _('Room Day Rank')
        verbose_name_plural = _('Room Day Rank')

    def __str__(self):
        return self.uid


class LoseWeekRank(ModelBase):
    user = models.ForeignKey(USER_MODEL, verbose_name=_('user'), related_name='lose_week_rank')
    year = models.SmallIntegerField(_('year'), default=0)
    week = models.SmallIntegerField(_('week'), default=0)
    amount = models.FloatField(_('amount'), default=0)

    class Meta:
        verbose_name = _('Lose Week Rank')
        verbose_name_plural = _('Lose Week Rank')

    @classmethod
    def update_amount(cls, user, amount):
        with transaction.atomic():
            year = current_year()
            week = current_week()
            if not cls.objects.filter(user=user, year=year, week=week):
                cls.objects.create(user=user, year=year, week=week)
            record = cls.objects.select_for_update().get(user=user, year=year, week=week)
            amount = round(amount, 2)
            record.amount += amount
            record.save()

    def __str__(self):
        return self.uid


class CaseKeyConfig(ModelBase):
    case = models.ForeignKey(Case, verbose_name=_('case'), related_name='case', default=None, null=True, blank=True)
    key = models.ForeignKey(ItemInfo, verbose_name=_("case_key"), related_name='case_key', default=None, null=True, blank=True)

    class Meta:
        verbose_name = _("CaseKeyConfig")
        verbose_name_plural = _("CaseKeyConfig")

    def __str__(self):
        return str(self.case)
    

class GiveawayItems(ModelBase):
    item_info = models.OneToOneField(ItemInfo, verbose_name=_("item info"), related_name='giveaway_item')
    enable = models.BooleanField(_('enable'), default=True)
    remark = models.CharField(_('remark'), max_length=256, null=True, default=None, blank=True)

    class Meta:
        verbose_name = _("GiveawayItems")
        verbose_name_plural = _("GiveawayItems")
    def __str__(self):
        return str(self.uid)