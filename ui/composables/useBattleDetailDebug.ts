/**
 * 战斗详情页调试监控
 * 提供详细的状态监控和调试日志功能
 */
import { watch } from 'vue'

export interface BattleDetailDebug {
  setupDebugWatchers: () => void
}

export function useBattleDetailDebug(): BattleDetailDebug {
  const battleCore = useBattleCore()
  const roundManager = useBattleRoundManager()

  const setupDebugWatchers = () => {
    console.log("[🎯调试] 设置调试监控器")

    // 🎯 监控轮次管理器状态变化
    watch(
      () => roundManager.currentRound.value,
      (newRound, oldRound) => {
        console.log('[🎯轮次监控] currentRound变化:', {
          oldRound,
          newRound,
          timestamp: new Date().toISOString()
        })
        if (newRound && newRound !== oldRound) {
          console.log('[🎯轮次监控] 轮次详情:', {
            roundNumber: newRound,
            totalRounds: roundManager.totalRounds.value,
            battleState: battleCore.battleState.battleData.value?.state,
            playersCount: battleCore.battleState.battleData.value?.players?.length || 0
          })
        }
      },
      { immediate: true }
    )

    // 🎯 监控轮次管理器总轮次变化
    watch(
      () => roundManager.totalRounds.value,
      (newTotal, oldTotal) => {
        console.log('[🎯轮次监控] totalRounds变化:', {
          oldTotal,
          newTotal,
          currentRound: roundManager.currentRound.value,
          timestamp: new Date().toISOString()
        })
      },
      { immediate: true }
    )

    // 🎯 监控战斗数据加载状态
    watch(
      () => battleCore.battleState.battleData.value,
      (newData, oldData) => {
        console.log('[🎯数据监控] battleData变化:', {
          hadOldData: !!oldData,
          hasNewData: !!newData,
          battleId: newData?.id,
          status: newData?.status || newData?.state,
          roundsCount: newData?.rounds?.length || 0,
          playersCount: newData?.players?.length || 0,
          timestamp: new Date().toISOString()
        })
        
        // 如果有新数据，检查轮次信息
        if (newData) {
          console.log('[🎯数据监控] 战斗数据轮次信息:', {
            round_count: newData.round_count,
            round_count_total: newData.round_count_total,
            round_count_current: newData.round_count_current,
            cases: newData.cases?.length || 0
          })
        }
      },
      { immediate: true, deep: true }
    )

    // 🎯 监控WebSocket连接状态
    watch(
      () => battleCore.battleWebSocket.connectionState.value,
      (newState, oldState) => {
        console.log('[🎯连接监控] WebSocket状态变化:', {
          oldConnected: oldState?.isConnected,
          newConnected: newState?.isConnected,
          isReconnecting: newState?.isReconnecting,
          reconnectAttempts: newState?.reconnectAttempts,
          timestamp: new Date().toISOString()
        })
      },
      { immediate: true, deep: true }
    )

    // 🎯 监控战斗状态变化
    watch(
      () => [
        battleCore.battleState.isBattleWaiting.value,
        battleCore.battleState.isBattleInProgress.value,
        battleCore.battleState.isBattleFinished.value
      ],
      (newStates, oldStates) => {
        if (!newStates || !oldStates) return
        
        const [waiting, inProgress, finished] = newStates
        const [oldWaiting, oldInProgress, oldFinished] = oldStates
        
        console.log('[🎯状态监控] 战斗状态变化:', {
          old: { waiting: oldWaiting, inProgress: oldInProgress, finished: oldFinished },
          new: { waiting, inProgress, finished },
          timestamp: new Date().toISOString()
        })
      },
      { immediate: true }
    )

    // 🎯 监控显示箱子数据
    watch(
      () => battleCore.battleState.displayCases.value,
      (newCases, oldCases) => {
        console.log('[🎯箱子监控] displayCases变化:', {
          oldCount: oldCases?.length || 0,
          newCount: newCases?.length || 0,
          cases: newCases?.map(c => ({ name: c.name, id: c.id, roundIndex: c.roundIndex })) || [],
          timestamp: new Date().toISOString()
        })
      },
      { immediate: true, deep: true }
    )

    // 🎯 监控开箱状态
    watch(
      () => battleCore.battleState.openingCaseId.value,
      (newCaseId, oldCaseId) => {
        console.log('[🎯开箱监控] openingCaseId变化:', {
          oldCaseId,
          newCaseId,
          timestamp: new Date().toISOString()
        })
      },
      { immediate: true }
    )

    console.log("[🎯调试] 调试监控器设置完成")
  }

  return {
    setupDebugWatchers
  }
}
