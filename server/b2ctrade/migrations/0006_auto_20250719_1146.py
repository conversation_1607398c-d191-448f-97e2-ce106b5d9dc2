# Generated by Django 3.2.25 on 2025-07-19 03:46

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('b2ctrade', '0005_auto_20250718_1647'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='b2citemlist',
            options={'ordering': ('item_info__market_hash_name',), 'verbose_name': 'B2C物品列表', 'verbose_name_plural': 'B2C Item list'},
        ),
        migrations.AlterModelOptions(
            name='b2cmarketitem',
            options={'verbose_name': 'B2C市场物品', 'verbose_name_plural': 'B2C Market Item'},
        ),
        migrations.AlterModelOptions(
            name='b2cofficialaccount',
            options={'verbose_name': 'B2C官方账户', 'verbose_name_plural': 'B2COfficial Account'},
        ),
        migrations.AlterModelOptions(
            name='b2ctraderecord',
            options={'ordering': ('-create_time',), 'verbose_name': 'B2C官方交易记录', 'verbose_name_plural': 'B2COfficialTrade Record'},
        ),
        migrations.AlterModelOptions(
            name='b2ctradestatisticsday',
            options={'ordering': ('-create_time',), 'verbose_name': 'B2C交易日统计', 'verbose_name_plural': 'B2CTrade Statistics Day'},
        ),
        migrations.AlterModelOptions(
            name='b2ctradestatisticsmonth',
            options={'ordering': ('-create_time',), 'verbose_name': 'B2C交易月统计', 'verbose_name_plural': 'B2CTrade Statistics Month'},
        ),
        migrations.AlterModelOptions(
            name='zbtblacklist',
            options={'verbose_name': 'ZBT黑名单', 'verbose_name_plural': 'ZBTBlackList'},
        ),
        migrations.AlterModelOptions(
            name='zbtraderecord',
            options={'ordering': ('-create_time',), 'verbose_name': 'ZBT交易记录', 'verbose_name_plural': 'ZBT Trade Record'},
        ),
        migrations.AlterField(
            model_name='b2cofficialaccount',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='b2c_official_account', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
        migrations.AlterField(
            model_name='zbtraderecord',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='zbt_user', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
    ]
