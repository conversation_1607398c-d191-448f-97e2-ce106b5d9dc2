#!/usr/bin/env python
"""
测试模型的verbose_name是否正确显示
"""

import os
import sys
import django

# 设置Django环境
sys.path.append('/www/wwwroot/csgoskins.com.cn/server')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')
django.setup()

def test_model_verbose_names():
    print("🧪 测试模型verbose_name...")
    
    from charge.models import CDKey, ChargeRecord, CDKeyRecord, GenerateCDKey, PayMethod
    
    models_to_test = [
        (CDKey, "CDKey"),
        (ChargeRecord, "ChargeRecord"),
        (CDKeyRecord, "CDKeyRecord"), 
        (GenerateCDKey, "GenerateCDKey"),
        (PayMethod, "PayMethod")
    ]
    
    for model, name in models_to_test:
        verbose_name = model._meta.verbose_name
        verbose_name_plural = model._meta.verbose_name_plural
        
        print(f"✅ {name}:")
        print(f"   verbose_name: '{verbose_name}'")
        print(f"   verbose_name_plural: '{verbose_name_plural}'")
        print()
    
    # 测试字段的verbose_name
    print("🔧 测试字段verbose_name...")
    cdkey_fields = CDKey._meta.get_fields()
    for field in cdkey_fields:
        if hasattr(field, 'verbose_name'):
            print(f"   {field.name}: '{field.verbose_name}'")

if __name__ == "__main__":
    test_model_verbose_names()
