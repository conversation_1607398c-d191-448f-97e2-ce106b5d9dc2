import logging
import os, sys
import threading

import django
from django.db import transaction

# Django setup for standalone script
if not hasattr(django.conf.settings, 'configured') or not django.conf.settings.configured:
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "steambase.settings")
    django.setup()

from charge.models import ChargeLevel
from authentication.models import AuthUser
from apscheduler.schedulers.blocking import BlockingScheduler


_logger = logging.getLogger(__name__)

def init_charge_level():
    """初始化充值等级配置"""
    with transaction.atomic():
        users = AuthUser.objects.all()
        for user in users:
            # 重置所有用户的免费箱子限制
            user.extra.freebox_lv1_limit = 1
            user.extra.freebox_lv2_limit = 1
            user.extra.freebox_lv3_limit = 1
            user.extra.freebox_lv4_limit = 1
            user.extra.freebox_lv5_limit = 1
            user.extra.freebox_lv6_limit = 1
            user.extra.freebox_lv7_limit = 1
            user.extra.freebox_lv8_limit = 1
            user.extra.freebox_lv9_limit = 1
            user.extra.freebox_lv10_limit = 1

            user.extra.save()
            user.save()

        # 重置所有用户的充值等级
        user_levels = ChargeLevel.objects.all()
        user_levels.update(level=0, amount=0)
        _logger.info("charge level init success")

def job():
    """定时任务作业"""
    scheduler = BlockingScheduler()
    # 每月1号0点执行充值等级初始化
    scheduler.add_job(init_charge_level, trigger='cron', day=1, hour=0, minute=0, second=0)
    scheduler.start()

def setup_init_charge_level():
    """设置充值等级初始化定时任务"""
    th = threading.Thread(target=job, args=())
    th.daemon = True  # 设置为守护线程
    th.start()
    _logger.info("Init charge level scheduler started")

if __name__ == "__main__":
    # 直接运行时执行初始化
    init_charge_level()
