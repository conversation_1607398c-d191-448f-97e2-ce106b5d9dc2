# automatically generated by the FlatBuffers compiler, do not modify

# namespace: proto

import flatbuffers

class Authenticate(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAsAuthenticate(cls, buf, offset):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = Authenticate()
        x.Init(buf, n + offset)
        return x

    # Authenticate
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # Authenticate
    def Signature(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

    # Authenticate
    def Extra(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            x = self._tab.Indirect(o + self._tab.Pos)
            from .Map import Map
            obj = Map()
            obj.Init(self._tab.Bytes, x)
            return obj
        return None

def AuthenticateStart(builder): builder.StartObject(2)
def AuthenticateAddSignature(builder, signature): builder.PrependUOffsetTRelativeSlot(0, flatbuffers.number_types.UOffsetTFlags.py_type(signature), 0)
def AuthenticateAddExtra(builder, extra): builder.PrependUOffsetTRelativeSlot(1, flatbuffers.number_types.UOffsetTFlags.py_type(extra), 0)
def AuthenticateEnd(builder): return builder.EndObject()
