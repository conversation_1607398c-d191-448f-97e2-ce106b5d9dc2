# -*- coding: utf-8 -*-
# Generated by Django 1.11.13 on 2021-04-14 02:09
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import steambase.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODELL),
        ('package', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='CustomBox',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('name', models.CharField(max_length=128, verbose_name='name')),
                ('key', models.CharField(max_length=128, unique=True, verbose_name='key')),
                ('price', models.FloatField(default=0, verbose_name='price')),
                ('cover', models.ImageField(blank=True, default=None, null=True, upload_to='cases', verbose_name='cover image')),
                ('item', models.ImageField(blank=True, default=None, null=True, upload_to='cases', verbose_name='item image')),
                ('user', models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='custom_box', to=settings.AUTH_USER_MODELL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'CustomBox',
                'verbose_name_plural': 'CustomBox',
            },
        ),
        migrations.CreateModel(
            name='CustomBoxCover',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('cover', models.ImageField(blank=True, default=None, null=True, upload_to='cases', verbose_name='cover image')),
            ],
            options={
                'verbose_name': 'CustomBoxCover',
                'verbose_name_plural': 'CustomBoxCover',
            },
        ),
        migrations.CreateModel(
            name='CustomBoxItem',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('item', models.ImageField(blank=True, default=None, null=True, upload_to='cases', verbose_name='item image')),
            ],
            options={
                'verbose_name': 'CustomBoxItem',
                'verbose_name_plural': 'CustomBoxItem',
            },
        ),
        migrations.CreateModel(
            name='CustomBoxItemInfo',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('market_name', models.CharField(blank=True, default=None, max_length=128, null=True, verbose_name='market name')),
                ('item_info', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='custom_item', to='package.ItemInfo')),
            ],
            options={
                'verbose_name': 'CustomBoxItemInfo',
                'verbose_name_plural': 'CustomBoxItemInfo',
            },
        ),
        migrations.CreateModel(
            name='CustomBoxRecord',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('price', models.FloatField(default=0, verbose_name='price')),
                ('case', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='custombox_records', to='custombox.CustomBox', verbose_name='case')),
                ('item_info', models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='custombox_records', to='package.ItemInfo', verbose_name='item info')),
                ('user', models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='custombox_records', to=settings.AUTH_USER_MODELL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'Case Record',
                'verbose_name_plural': 'Case Record',
            },
        ),
        migrations.CreateModel(
            name='CustomDropItem',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('show_chance', models.FloatField(default=0, verbose_name='show chance')),
                ('drop_chance_a', models.FloatField(default=0, verbose_name='drop chance a')),
                ('drop_chance_b', models.FloatField(default=0, verbose_name='drop chance b')),
                ('drop_chance_c', models.FloatField(default=0, verbose_name='drop chance c')),
                ('drop_chance_d', models.FloatField(default=0, verbose_name='drop chance d')),
                ('drop_chance_e', models.FloatField(default=0, verbose_name='drop chance e')),
                ('case', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='custombox_drops', to='custombox.CustomBox', verbose_name='case')),
                ('item_info', models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='custom_drops', to='package.ItemInfo', verbose_name='item info')),
            ],
            options={
                'verbose_name': 'CustomDropItem',
                'verbose_name_plural': 'CustomDropItem',
            },
        ),
    ]
