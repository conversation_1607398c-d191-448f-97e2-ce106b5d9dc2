<template>
  <div class="audio-controller">
    <!-- 音效控制面板 -->
    <div 
      class="bg-slate-800/60 backdrop-blur-xl border border-white/20 rounded-2xl p-4 shadow-xl"
      :class="{
        'minimal-theme': theme === 'minimal',
        'battle-theme': theme === 'battle',
        'full-theme': theme === 'full'
      }"
    >
      <!-- 最小化主题 -->
      <div v-if="theme === 'minimal'" class="flex items-center gap-2">
        <!-- 静音开关 -->
        <button
          @click="handleToggleMute"
          class="group relative flex items-center justify-center w-8 h-8 text-white/80 font-medium rounded-lg transition-all duration-300 hover:scale-110 hover:text-white"
          :class="
            isMuted
              ? 'bg-red-500/20 hover:bg-red-500/30'
              : 'bg-green-500/20 hover:bg-green-500/30'
          "
          :title="isMuted ? '开启音效' : '关闭音效'"
        >
          <Icon
            :name="
              isMuted
                ? 'material-symbols:volume-off'
                : 'material-symbols:volume-up'
            "
            class="w-4 h-4 transition-transform duration-300"
          />
        </button>

        <!-- 音量控制 -->
        <div class="flex items-center gap-1">
          <!-- 音量滑块 -->
          <input
            type="range"
            min="0"
            max="1"
            step="0.1"
            :value="volumeValue"
            @input="handleVolumeChange"
            :disabled="isMuted"
            class="w-12 h-1 bg-white/10 rounded-full appearance-none cursor-pointer slider-minimal"
            :class="{ 'opacity-50': isMuted }"
            title="调整音量"
          />
          <!-- 音量百分比 -->
          <span class="text-xs text-white/40 min-w-[1.5rem]">{{ Math.round(volumeValue * 100) }}%</span>
        </div>
      </div>

      <!-- 默认主题 -->
      <div v-else>
        <div class="flex items-center justify-between mb-4">
          <h3 class="font-bold text-white flex items-center gap-2">
            <Icon name="material-symbols:volume-up" class="w-5 h-5 text-blue-400" />
            <span class="font-i18n">音效控制</span>
          </h3>
          <div class="flex items-center gap-2">
            <!-- 静音开关 -->
            <button
              @click="handleToggleMute"
              class="group relative flex items-center gap-2 px-3 py-2 text-white font-medium rounded-lg transition-all duration-300 hover:scale-105 border border-white/20 hover:border-white/40 overflow-hidden"
              :class="
                isMuted
                  ? 'bg-red-600 hover:bg-red-500'
                  : 'bg-green-600 hover:bg-green-500'
              "
            >
              <Icon
                :name="
                  isMuted
                    ? 'material-symbols:volume-off'
                    : 'material-symbols:volume-up'
                "
                class="w-5 h-5 transition-transform duration-300"
              />
              <span class="relative z-10 font-i18n">
                {{ isMuted ? "开启音效" : "关闭音效" }}
              </span>
              <div
                class="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                :class="isMuted ? 'bg-red-500/20' : 'bg-green-500/20'"
              ></div>
            </button>
          </div>
        </div>

        <!-- 音量控制 -->
        <div v-if="showVolumeSlider" class="mb-4">
          <div class="flex items-center gap-3 px-4 py-3 bg-slate-700/60 backdrop-blur-xl border border-white/20 rounded-xl">
            <Icon name="material-symbols:volume-up" class="w-5 h-5 text-white" />
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              :value="volumeValue"
              @input="handleVolumeChange"
              :disabled="isMuted"
              class="flex-1 h-2 bg-slate-600 rounded-lg appearance-none cursor-pointer slider"
              :class="{ 'opacity-50': isMuted }"
            />
            <span class="text-sm text-white/60 min-w-[3rem]">{{ Math.round(volumeValue * 100) }}%</span>
          </div>
        </div>

        <!-- 音效测试按钮 -->
        <div v-if="showTestButtons && testSounds && testSounds.length > 0" class="space-y-2">
          <h4 class="text-sm font-medium text-white/80 mb-2">🎵 音效测试</h4>
          <div class="flex flex-wrap gap-2">
            <button
              v-for="sound in testSounds"
              :key="sound"
              @click="handleTestSound(sound)"
              class="px-3 py-2 text-xs text-white bg-slate-700/60 hover:bg-slate-600/60 rounded-lg transition-colors border border-white/20"
            >
              {{ getSoundDisplayName(sound) }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// Props 定义
const props = defineProps({
  // 是否静音
  muted: {
    type: Boolean,
    default: false,
  },
  // 音量值 (0-1)
  volumeValue: {
    type: Number,
    default: 0.7,
  },
  // 测试音效类型
  testSoundType: {
    type: String,
    default: 'buttonClick',
  },
  // 是否显示测试按钮
  showTestButton: {
    type: Boolean,
    default: true,
  },
  // 是否显示状态指示
  showStatus: {
    type: Boolean,
    default: true,
  },
  // 自定义样式类
  customClass: {
    type: String,
    default: '',
  },
  // 主题
  theme: {
    type: String,
    default: 'full',
  },
  // 是否显示音量控制
  showVolumeSlider: {
    type: Boolean,
    default: true,
  },
  // 是否显示测试按钮
  showTestButtons: {
    type: Boolean,
    default: true,
  },
  // 测试音效列表
  testSounds: {
    type: Array,
    default: [],
  },
});

// Emits 定义
const emit = defineEmits([
  'update:muted',
  'update:volumeValue',
  'volume-change',
  'mute-toggle',
  'test-sound',
]);

// 响应式数据
const isMuted = computed({
  get: () => props.muted,
  set: (value) => emit('update:muted', value),
});

const volume = computed({
  get: () => props.volumeValue,
  set: (value) => emit('update:volumeValue', value),
});

// 音效状态
const audioState = ref('就绪');

// 处理音量变化
const handleVolumeChange = (event) => {
  const newVolume = Number(event.target.value);
  volume.value = newVolume;
  emit('volume-change', newVolume);
  audioState.value = '音量已调整';
  
  // 重置状态
  setTimeout(() => {
    audioState.value = '就绪';
  }, 1000);
};

// 处理静音切换
const handleToggleMute = () => {
  console.log('[🎰AUDIO-CONTROLLER] 静音按钮被点击', {
    currentMuted: isMuted.value,
    propsMuted: props.muted
  });
  
  // 直接使用props.muted来计算新状态，避免computed属性的异步更新问题
  const newMutedState = !props.muted;
  emit('update:muted', newMutedState);
  emit('mute-toggle', newMutedState);
  audioState.value = newMutedState ? '已静音' : '已开启';
  
  console.log('[🎰AUDIO-CONTROLLER] 静音状态已更新', {
    newMuted: newMutedState,
    emittedValue: newMutedState
  });
  
  // 重置状态
  setTimeout(() => {
    audioState.value = '就绪';
  }, 1000);
};

// 处理音效测试
const handleTestSound = (sound) => {
  if (isMuted.value) return;
  
  emit('test-sound', sound || props.testSoundType);
  audioState.value = '播放中...';
  
  // 重置状态
  setTimeout(() => {
    audioState.value = '就绪';
  }, 1000);
};

// 获取音效显示名称
const getSoundDisplayName = (soundType) => {
  const soundNames = {
    'buttonClick': '按钮点击',
    'roundStart': '轮次开始',
    'roundEnd': '轮次结束',
    'rollStart': '开箱开始',
    'rollEnd': '开箱结束',
    'winnerAnnounce': '获胜宣布',
    'battleStart': '对战开始',
    'calculate': '计算音效',
    'tick': '滴答声',
    'last-tick': '最后滴答',
    'boost-on': '加速开启',
    'boost-off': '加速关闭',
    'rare-special-item': '稀有物品',
    'win': '获胜音效',
    'chat_sound': '聊天音效'
  };
  
  return soundNames[soundType] || soundType;
};

// 组件挂载时的日志
onMounted(() => {
  console.log('[🎰AUDIO-CONTROLLER] 音效控制组件已挂载', {
    muted: isMuted.value,
    volume: volume.value,
    testSoundType: props.testSoundType,
  });
});
</script>

<style scoped>
/* 自定义滑块样式 */
.slider::-webkit-slider-thumb {
  @apply appearance-none w-4 h-4 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full cursor-pointer shadow-lg;
}

.slider::-moz-range-thumb {
  @apply appearance-none w-4 h-4 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full cursor-pointer border-0 shadow-lg;
}

.slider:disabled::-webkit-slider-thumb {
  @apply opacity-50 cursor-not-allowed;
}

.slider:disabled::-moz-range-thumb {
  @apply opacity-50 cursor-not-allowed;
}

/* 音量指示器动画 */
.volume-indicator {
  transition: width 0.3s ease-out;
}

/* 按钮悬停效果 */
.group:hover .group-hover\:scale-105 {
  transform: scale(1.05);
}

/* 响应式设计 */
@media (max-width: 640px) {
  .audio-controller {
    @apply text-sm;
  }
  
  .audio-controller button {
    @apply px-2 py-1.5;
  }
  
  .audio-controller button span {
    @apply text-xs;
  }
}

/* Minimal主题样式 - 完全透明融入页面 */
.minimal-theme {
  background: transparent !important;
  backdrop-filter: none !important;
  border: none !important;
  box-shadow: none !important;
  padding: 0 !important;
}

.minimal-theme:hover {
  background: transparent !important;
  border: none !important;
}

/* Minimal主题滑块样式 */
.slider-minimal::-webkit-slider-thumb {
  @apply appearance-none w-3 h-3 bg-white/60 rounded-full cursor-pointer shadow-sm hover:bg-white/80 transition-colors;
}

.slider-minimal::-moz-range-thumb {
  @apply appearance-none w-3 h-3 bg-white/60 rounded-full cursor-pointer border-0 shadow-sm hover:bg-white/80 transition-colors;
}

.slider-minimal:disabled::-webkit-slider-thumb {
  @apply opacity-30 cursor-not-allowed;
}

.slider-minimal:disabled::-moz-range-thumb {
  @apply opacity-30 cursor-not-allowed;
}
</style> 