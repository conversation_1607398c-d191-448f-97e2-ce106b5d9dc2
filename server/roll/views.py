import logging
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.core.cache import cache

from rest_framework.views import APIView
from rest_framework.permissions import AllowAny

from steambase.enums import RespCode, RollType
from steambase.utils import reformat_resp, current_user
from sitecfg.interfaces import get_maintenance, get_maintenance_roll
from roll.business import create_roll_room, edit_roll_room, deposit_roll_items, join_roll_room, cancel_roll_room, check_roll_room, roll_user_join, roll_check_join_status, deposit_roll_items_newapi
from roll.business import get_room_list, get_room_detail, get_room_items, get_room_bets, get_joinable_num, get_roll_list, get_roll_skin_list, get_roll_user_list, get_roll_winner_list, get_roll_detail, get_roll_user_num, get_new_roll_list
from roll.interfaces import init_roll_room_data

from django_redis import get_redis_connection

from roll.business import update_item_ids
from roll.tasks import cleanup_duplicate_roll_room_bets

_logger = logging.getLogger(__name__)


def clear_cache_by_prefix(prefix="new_api_roll:skin_list:"):
    redis_client = get_redis_connection("default")
    pattern = f"{prefix}*"
    keys = redis_client.keys(pattern)
    for key in keys:
        redis_client.delete(key)
        print(f"Cache key {key.decode('utf-8')} cleared.")
class CreateRollRoomView(APIView):

    def post(self, request):
        try:

            if get_maintenance_roll():
                return reformat_resp(RespCode.Maintenance.value, {},
                                     _('月度福利板块正在维护'))
            user = current_user(request)
            name = request.data.get('name', None)
            debug = request.data.get('debug', True)
            min_joiner = request.data.get('min_joiner', 1)
            max_joiner = request.data.get('max_joiner', 1000000)
            max_winner = request.data.get('max_winner', 0)
            begin_time = request.data.get('begin_time', None)
            due_time = request.data.get('due_time', None)
            type = request.data.get('type', RollType.Nocharge.value)
            desc = request.data.get('desc', None)
            password = request.data.get('password', None)
            fee = request.data.get('fee', 0)
            items = request.data.get('items', [])
            deposit_enable = request.data.get('deposit_enable', False)
            charge_limit = request.data.get('charge_limit', None)
            charge_begin_time = request.data.get('charge_begin_time', None)
            charge_end_time = request.data.get('charge_end_time', None)
            code, resp = create_roll_room(user, name, min_joiner, max_joiner, max_winner, begin_time, due_time,
                                          type, desc, password, fee, items, deposit_enable, charge_limit,
                                          charge_begin_time, charge_end_time, debug)
            if code == RespCode.Succeed.value:
                # 清除列表缓存
                cache.delete('new_api_roll:rolllist')
                
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class EditRollRoomView(APIView):

    def post(self, request):
        try:

            if get_maintenance_roll():
                return reformat_resp(RespCode.Maintenance.value, {},
                                     _('月度福利板块正在维护'))
            user = current_user(request)
            uid = request.data.get('uid', None)
            name = request.data.get('name', None)
            max_joiner = request.data.get('max_joiner', 0)
            max_winner = request.data.get('max_winner', 0)
            begin_time = request.data.get('begin_time', timezone.now())
            due_time = request.data.get('due_time', timezone.now())
            desc = request.data.get('desc', None)
            password = request.data.get('password', None)
            fee = request.data.get('fee', 0)
            code, resp = edit_roll_room(user, uid, name, desc, password, fee, max_joiner, max_winner,
                                        begin_time, due_time)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class DepositRollItemsView(APIView):

    def post(self, request):
        try:

            if get_maintenance_roll():
                return reformat_resp(RespCode.Maintenance.value, {},
                                     _('月度福利板块正在维护'))
            user = current_user(request)
            uid = request.data.get('uid', None)
            items = request.data.get('items', [])
            #print(items)
            coins = request.data.get('coins', [])
            code, resp = deposit_roll_items(user, uid, items, coins)
            if code == RespCode.Succeed.value:
                # 清除列表缓存
                cache.delete('new_api_roll:rolllist')
                # 清除饰品缓存
                clear_cache_by_prefix()
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')
    




class JoinRollRoomView(APIView):

    def post(self, request):
        try:

            if get_maintenance_roll():
                return reformat_resp(RespCode.Maintenance.value, {},
                                     _('月度福利板块正在维护'))
            user = current_user(request)
            uid = request.data.get('uid', None)
            password = request.data.get('password', "")
            code, resp = join_roll_room(user, uid, password)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class CancelRollRoomView(APIView):

    def post(self, request):
        try:

            if get_maintenance_roll():
                return reformat_resp(RespCode.Maintenance.value, {},
                                     _('月度福利板块正在维护'))
            user = current_user(request)
            uid = request.data.get('uid', None)
            code, resp = cancel_roll_room(user, uid)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetRollRoomListView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:

            user = current_user(request)
            query = {
                'id': request.query_params.get('id', None),
                'type': request.query_params.get('type', None),
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            fields = (
            'id', 'uid', 'user', 'type', 'name','desc','fee', 'begin_ts_val', 'due_ts_val', 'total_amount', 'official',
            'amount', 'items_count', 'joiners_count', 'state', 'items_preview', 'due_ts', 'begin_ts', 'has_password',
            'charge_limit', 'charge_begin_time', 'charge_end_time')
            items_preview_count = int(request.query_params.get('itemsPreviewCount', 4))
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            is_creator = True if request.query_params.get('isCreator', '0') == '1' else False
            is_joiner = True if request.query_params.get('isJoiner', '0') == '1' else False
            no_password = True if request.query_params.get('noPassword', '0') == '1' else False
            is_running = True if request.query_params.get('isRunning', '0') == '1' else False
            is_end = True if request.query_params.get('isEnd', '0') == '1' else False
            language = request.META.get("HTTP_ACCEPT_LANGUAGE")
            code, resp = get_room_list(user, query, fields, page, page_size, is_creator, is_joiner,
                                       items_preview_count, no_password, is_running, is_end, language)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetRollRoomDetailView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:

            user = current_user(request)
            query = {
                'type': request.query_params.get('type', None)
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            query['uid'] = request.query_params.get('uid', None)
            fields = ('id', 'uid', 'user', 'type', 'name', 'desc', 'fee', 'begin_ts_val', 'due_ts_val', 'official',
                      'amount', 'items_count', 'state', 'max_joiner', 'max_winner', 'has_password',
                      'begin_ts', 'due_ts', 'is_creator', 'is_joiner', 'min_joiner', 'charge_limit',
                      'charge_begin_time', 'charge_end_time')
            language = request.META.get("HTTP_ACCEPT_LANGUAGE")
            code, resp = get_room_detail(user, query, fields, language)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetRollRoomItemsView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:

            query = {
                'room__uid': request.query_params.get('rid', None)
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            fields = ('uid', 'package', 'balance', 'item_type')
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            code, resp = get_room_items(query, fields, page, page_size)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetRollRoomBetsView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:

            query = {
                'room__uid': request.query_params.get('rid', None),
                'victory': True if request.query_params.get('victory', None) == '1' else False
                if request.query_params.get('victory', None) == '0' else None
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            if query.get('victory', None) is not None:
                fields = ('uid', 'user', 'items_preview', 'win_items_count', 'victory')
            else:
                fields = ('uid', 'user', 'victory')
            items_preview_count = int(request.query_params.get('itemsPreviewCount', 1))
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            code, resp = get_room_bets(query, fields, page, page_size, items_preview_count)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetRollRoomJoinableNumView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:

            code, resp = get_joinable_num()
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')
    
class GetRollListView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            user = current_user(request)
            # 保留 RollRoom 合法字段
            allowed_query_params = {'id', 'type'}
            query = {k: v for k, v in request.query_params.items() if k in allowed_query_params and v}

            fields = (
                'id', 'uid', 'user', 'type', 'name', 'desc', 'fee', 'begin_ts_val', 'due_ts_val', 'total_amount', 'official',
                'amount', 'items_count', 'joiners_count', 'state', 'items_preview', 'due_ts', 'begin_ts', 'has_password',
                'charge_limit', 'charge_begin_time', 'charge_end_time'
            )

            # 获取分页和其他查询参数
            items_preview_count = int(request.query_params.get('itemsPreviewCount', 4))
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            is_creator = request.query_params.get('isCreator') == '1'
            is_joiner = request.query_params.get('isJoiner') == '1'
            no_password = request.query_params.get('noPassword') == '1'
            is_running = request.query_params.get('isRunning') == '1'
            is_end = request.query_params.get('isEnd') == '1'
            language = request.META.get("HTTP_ACCEPT_LANGUAGE", 'zh-hans')

            # 获取数据
            code, resp = get_roll_list(
                user, query, fields, page, page_size, is_creator, is_joiner,
                items_preview_count, no_password, is_running, is_end, language
            )

            # 返回响应
            return reformat_resp(code, resp, 'Succeed' if code == RespCode.Succeed.value else resp)

        except Exception as e:
            _logger.exception(f"Error in GetRollListView.get: {str(e)}")
            return reformat_resp(RespCode.Exception.value, {}, 'Exception')
    
class GetRollSkinView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:

            query = {
                'room__uid': request.query_params.get('rid', None)
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            fields = ('market_name_cn', 'market_name', 'icon_url', 'price', 'part', 'amount', 'rarity_color')
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            code, resp = get_roll_skin_list(query, fields, page, page_size)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')
    
class GetRollUserView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            query = {
                'room__uid': request.query_params.get('rid', None),
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            fields = ('id', 'uid', 'user')
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            code, resp = get_roll_user_list(query, fields, page, page_size)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')
    
class GetRollWinnerView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            query = {
                'room__uid': request.query_params.get('rid', None),

            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            fields = ('uid', 'package', 'item_type', 'bet_id')
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            code, resp = get_roll_winner_list(query, fields, page, page_size)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')
    
class GetRollDetailView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            query = {
                'uid': request.query_params.get('rid', None),
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            fields = ('id', 'uid', 'user', 'type', 'name', 'desc', 'fee', 'begin_ts_val', 'due_ts_val', 'official',
                      'amount', 'items_count', 'state', 'max_joiner', 'max_winner', 'has_password',
                      'begin_ts', 'due_ts', 'is_creator', 'is_joiner', 'min_joiner', 'charge_limit',
                      'charge_begin_time', 'charge_end_time')
            code, resp = get_roll_detail(query, fields)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')
    
class GetRollUserNumView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            uid = request.query_params.get('rid', None)
            #print(uid)        
            
            code, resp = get_roll_user_num(uid)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')
    

class RollUserJoinView(APIView):

    def post(self, request):
        try:
            if get_maintenance_roll():
                return reformat_resp(RespCode.Maintenance.value, {},
                                     _('福利房间维护中，请稍后再试'))
            
            
            user = current_user(request)

            trade_url = user.asset.tradeurl
            #print("trade_url:"+trade_url)
            if not trade_url:
                return reformat_resp(RespCode.InvalidParams.value, {},
                                     _('请在 会员中心>账户设置 提交您的交易链接后再试'))
            
            uid = request.query_params.get('rid', None)
            password = request.query_params.get('password', "")
            code, resp = roll_user_join(user, uid, password)
            if code == RespCode.Succeed.value:
                _logger.info(f"用户 {user.steam.personaname} 成功加入ROLL房: {uid}")
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')
    
class RollCheckStatusView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        try:
            if get_maintenance_roll():
                return reformat_resp(RespCode.Maintenance.value, {},
                                     _('月度福利板块正在维护'))
            user = current_user(request)
            uid = request.query_params.get('rid', None)
            code, resp = roll_check_join_status(user, uid)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')
    
    


class GetNewRollListView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
           
            
            code, resp = get_new_roll_list()
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')

class TestView(APIView):
    permission_classes = [AllowAny]
    def get(self, request):
        try:
            update_item_ids()
            return reformat_resp(RespCode.Succeed.value, {}, 'Succeed')
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, 'Exception')
        

class ClearupDuplicateRollUserView(APIView):
    permission_classes = [AllowAny]
    def get(self, request):
        try:
            cleanup_duplicate_roll_room_bets()
            return reformat_resp(RespCode.Succeed.value, {}, 'Succeed')
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, 'Exception')
        
    
    
# class OpenRollView(APIView):

#     permission_classes = [AllowAny]

#     def get(self, request):
#         try:
#             check_roll_room()
#             print("OpenRollView")        
#         except Exception as e:
#             _logger.exception(e)
#         return reformat_resp(RespCode.Exception.value, {}, 'Exception')
    

# class InitRollRoomDataView(APIView):

#     permission_classes = [AllowAny]

#     def get(self, request):
#         try:
#             init_roll_room_data()        
#             print("InitRollRoomDataView")        
#         except Exception as e:
#             _logger.exception(e)
#         return reformat_resp(RespCode.Exception.value, {}, 'Exception')