import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'
import { monitorApi, type StatsData, type CaseRecord } from '~/services/monitor-api'
import { skinApi, type SkinItem } from '~/services/skin-api'
import { siteApi } from '~/services/site-api'
import type { BannerItem } from '~/types/banner'
import { useCaseStore } from '~/stores/case'
import type { CaseItem } from '~/types/case'
import type { SkinItem as TransformedSkinItem } from '~/types/skin'
import { transformSimpleSkinItems } from '~/utils/skin-transformer'

export const useHomeStore = defineStore('home', () => {
  // 国际化
  const { locale } = useI18n()
  
  // Banner 数据
  const banner = ref<BannerItem[]>([])
  const bannerLoading = ref(false)
  const bannerError = ref<string | null>(null)
  const lastBannerRefresh = ref(0)

  // 统计数据 - 修正字段名称以匹配API
  const statsData = ref<StatsData>({
    user_number: 0,
    online_number: 0,
    case_number: 0,
    battle_number: 0
  })
  const statsLoading = ref(false)
  const statsError = ref<string | null>(null)
  const lastStatsRefresh = ref(0)

  // 开箱记录
  const caseRecords = ref<CaseRecord[]>([])
  const caseRecordsLoading = ref(false)
  const caseRecordsError = ref<string | null>(null)
  const lastCaseRecordsRefresh = ref(0)

  // 热门箱子数据 - 从 caseStore 获取
  const hotCasesLoading = ref(false)
  const hotCasesError = ref<string | null>(null)
  const lastHotCasesRefresh = ref(0)

  // 折扣箱子数据 - 从 caseStore 获取
  const discountCasesLoading = ref(false)
  const discountCasesError = ref<string | null>(null)
  const lastDiscountCasesRefresh = ref(0)

  // 新箱子数据 - 从 caseStore 获取
  const newCasesLoading = ref(false)
  const newCasesError = ref<string | null>(null)
  const lastNewCasesRefresh = ref(0)

  // 随机皮肤数据
  const randomSkins = ref<TransformedSkinItem[]>([])
  const randomSkinsLoading = ref(false)
  const randomSkinsError = ref<string | null>(null)
  const lastRandomSkinsRefresh = ref(0)
  
  // 原始皮肤数据缓存（用于语言切换时重新转换）
  const rawRandomSkinsData = ref<any[]>([])

  // 刷新间隔（50分钟）
  const REFRESH_INTERVAL = 50 * 60 * 1000

  // Getters - 从 caseStore 获取箱子数据
  const hotCases = computed(() => {
    const caseStore = useCaseStore()
    return caseStore.hotCases
  })

  const discountCases = computed(() => {
    const caseStore = useCaseStore()
    return caseStore.discountCases
  })

  const newCases = computed(() => {
    const caseStore = useCaseStore()
    return caseStore.newCases
  })

  // 检查是否需要刷新的计算属性
  const shouldRefreshBanner = computed(() => {
    const now = Date.now()
    return banner.value.length === 0 || now - lastBannerRefresh.value >= REFRESH_INTERVAL
  })

  const shouldRefreshStats = computed(() => {
    const now = Date.now()
    return statsData.value.user_number === 0 || now - lastStatsRefresh.value >= REFRESH_INTERVAL
  })

  const shouldRefreshCaseRecords = computed(() => {
    const now = Date.now()
    return caseRecords.value.length === 0 || now - lastCaseRecordsRefresh.value >= REFRESH_INTERVAL
  })

  const shouldRefreshHotCases = computed(() => {
    const now = Date.now()
    return hotCases.value.length === 0 || now - lastHotCasesRefresh.value >= REFRESH_INTERVAL
  })

  const shouldRefreshDiscountCases = computed(() => {
    const now = Date.now()
    return discountCases.value.length === 0 || now - lastDiscountCasesRefresh.value >= REFRESH_INTERVAL
  })

  const shouldRefreshNewCases = computed(() => {
    const now = Date.now()
    return newCases.value.length === 0 || now - lastNewCasesRefresh.value >= REFRESH_INTERVAL
  })

  const shouldRefreshRandomSkins = computed(() => {
    const now = Date.now()
    return randomSkins.value.length === 0 || now - lastRandomSkinsRefresh.value >= REFRESH_INTERVAL
  })

  // 获取统计数据和开箱记录
  const fetchStatsAndRecords = async () => {
    if (!shouldRefreshStats.value && !shouldRefreshCaseRecords.value) {
      return
    }

    statsLoading.value = true
    caseRecordsLoading.value = true
    statsError.value = null
    caseRecordsError.value = null

    try {
      const result = await monitorApi.getMonitorData()
      
      if (result.success && result.data) {
        if (result.data.stats) {
          statsData.value = result.data.stats
          lastStatsRefresh.value = Date.now()
        }
        
        if (result.data.case_records) {
          caseRecords.value = result.data.case_records
          lastCaseRecordsRefresh.value = Date.now()
        }
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : '获取数据失败'
      statsError.value = errorMsg
      caseRecordsError.value = errorMsg
    } finally {
      statsLoading.value = false
      caseRecordsLoading.value = false
    }
  }

  // 处理多语言 Banner 数据
  const processBannerData = (rawData: any[], currentLocale: string): BannerItem[] => {
    // console.log('[HomeStore] 处理Banner数据:', { rawDataLength: rawData.length, currentLocale })
    
    const filtered = rawData
      .filter(item => {
        const isActive = item.is_active !== false // 默认为true，除非明确设置为false
        
        return isActive
      })
      .sort((a, b) => (a.order || 0) - (b.order || 0)) // 按顺序排序
    
    // console.log('[HomeStore] 过滤后的数据数量:', filtered.length)
    
    return filtered.map((item, index) => ({
        id: `banner-${index}`,
        image: item.image,
        title: currentLocale === 'zh-hans' ? (item.title_zh_hans || item.title) : (item.title_en || item.title),
        description: currentLocale === 'zh-hans' ? (item.description_zh_hans || item.description) : (item.description_en || item.description),
        link: item.link,
        order: item.order,
        type: item.type,
        is_simple: item.is_simple,
        background_class: item.background_class,
        glow_class: item.glow_class,
        primary_button_text: currentLocale === 'zh-hans' ? (item.primary_button_text_zh_hans || item.primary_button_text) : (item.primary_button_text_en || item.primary_button_text),
        primary_button_link: item.primary_button_link,
        secondary_button_text: currentLocale === 'zh-hans' ? (item.secondary_button_text_zh_hans || item.secondary_button_text) : (item.secondary_button_text_en || item.secondary_button_text),
        secondary_button_link: item.secondary_button_link,
        ...item
      } as BannerItem))
  }

  // 存储原始 Banner 数据用于语言切换
  const rawBannerData = ref<any[]>([])

  // 获取 Banner 数据
  const fetchBanner = async (force = false) => {
    // console.log('[HomeStore] fetchBanner 调用', { force, shouldRefresh: shouldRefreshBanner.value, bannerLength: banner.value.length })
    
    if (!force && !shouldRefreshBanner.value) {
      // console.log('[HomeStore] 跳过 Banner 获取 - 不需要刷新')
      return
    }

    bannerLoading.value = true
    bannerError.value = null
    
    try {
      // console.log('[HomeStore] 开始调用 API 获取 Banner 数据')
      const result = await siteApi.getBanner()
      // console.log('[HomeStore] API 响应:', { success: result.success, dataLength: result.data?.length })
      
      if (result.success && result.data) {
        rawBannerData.value = result.data // 保存原始数据
        banner.value = processBannerData(result.data, locale.value)
        lastBannerRefresh.value = Date.now()
        // console.log('[HomeStore] Banner 数据获取成功，数量:', banner.value.length)
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      bannerError.value = error instanceof Error ? error.message : '获取 Banner 数据失败'
      // console.error('[HomeStore] Banner 数据获取失败:', error)
    } finally {
      bannerLoading.value = false
    }
  }

  // 监听语言变化，重新处理 Banner 数据
  watch(locale, (newLocale) => {
    if (rawBannerData.value.length > 0) {
      banner.value = processBannerData(rawBannerData.value, newLocale)
    }
  })

  // 获取热门箱子数据 - 通过 caseStore
  const fetchHotCases = async () => {
    if (!shouldRefreshHotCases.value) {
      // console.log('[HomeStore] 跳过热门箱子获取 - 不需要刷新')
      return
    }

    // console.log('[HomeStore] 开始获取热门箱子数据')
    hotCasesLoading.value = true
    hotCasesError.value = null
    
    try {
      const caseStore = useCaseStore()
      await caseStore.fetchHotCases(5)
      lastHotCasesRefresh.value = Date.now()
      // console.log('[HomeStore] 热门箱子数据获取完成')
    } catch (error) {
      hotCasesError.value = error instanceof Error ? error.message : '获取热门箱子数据失败'
      // console.error('[HomeStore] 获取热门箱子数据失败:', error)
    } finally {
      hotCasesLoading.value = false
    }
  }

  // 获取折扣箱子数据 - 通过 caseStore
  const fetchDiscountCases = async () => {
    if (!shouldRefreshDiscountCases.value) {
      return
    }

    discountCasesLoading.value = true
    discountCasesError.value = null
    
    try {
      const caseStore = useCaseStore()
      await caseStore.fetchDiscountCases(5)
      lastDiscountCasesRefresh.value = Date.now()
    } catch (error) {
      discountCasesError.value = error instanceof Error ? error.message : '获取折扣箱子数据失败'
    } finally {
      discountCasesLoading.value = false
    }
  }

  // 获取新箱子数据 - 通过 caseStore
  const fetchNewCases = async () => {
    if (!shouldRefreshNewCases.value) {
      return
    }

    newCasesLoading.value = true
    newCasesError.value = null
    
    try {
      const caseStore = useCaseStore()
      await caseStore.fetchNewCases(5)
      lastNewCasesRefresh.value = Date.now()
    } catch (error) {
      newCasesError.value = error instanceof Error ? error.message : '获取新箱子数据失败'
    } finally {
      newCasesLoading.value = false
    }
  }

  // 监听语言变化，重新转换皮肤数据
  watch(locale, (newLocale) => {
    // console.log('[HomeStore] 语言变化，重新转换皮肤数据:', newLocale)
    
    if (rawRandomSkinsData.value.length > 0) {
      randomSkins.value = transformSimpleSkinItems(rawRandomSkinsData.value, newLocale)
      // console.log('[HomeStore] 重新转换随机皮肤数据，数量:', randomSkins.value.length)
    }
    
    // Banner数据也需要重新处理
    if (rawBannerData.value.length > 0) {
      banner.value = processBannerData(rawBannerData.value, newLocale)
      // console.log('[HomeStore] 重新转换Banner数据，数量:', banner.value.length)
    }
  })

  // 获取随机皮肤数据
  const fetchRandomSkins = async () => {
    if (!shouldRefreshRandomSkins.value) {
      return
    }

    randomSkinsLoading.value = true
    randomSkinsError.value = null
    
    try {
      const result = await skinApi.getRandomSkins({
        num: 12,
        excludeTypes: 'CSGO_Type_Spray,CSGO_Tool_Sticker',
        domain: 'www.csgo.com'
      })
      
      if (result.success && result.data) {
        // 缓存原始数据
        rawRandomSkinsData.value = result.data
        // 转换并存储数据
        randomSkins.value = transformSimpleSkinItems(result.data, locale.value)
        lastRandomSkinsRefresh.value = Date.now()
        // console.log('[HomeStore] 随机皮肤获取成功，数量:', randomSkins.value.length)
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      randomSkinsError.value = error instanceof Error ? error.message : '获取随机皮肤数据失败'
      // console.error('[HomeStore] 获取随机皮肤失败:', error)
    } finally {
      randomSkinsLoading.value = false
    }
  }

  // 初始化首页所有数据
  const initializeHomeData = async () => {
    try {
      // console.log('[HomeStore] 开始初始化首页数据')
      // 强制获取所有数据，忽略刷新检查
      await Promise.all([
        fetchStatsAndRecords(),
        fetchBanner(true), // 强制获取
        fetchHotCasesForce(), // 强制获取
        fetchDiscountCasesForce(), // 强制获取  
        fetchNewCasesForce(), // 强制获取
        fetchRandomSkins()
      ])
      // console.log('[HomeStore] 首页数据初始化完成')
    } catch (error) {
      console.error('[Home Store] 初始化首页数据失败:', error)
    }
  }

  // 强制获取热门箱子 - 忽略刷新检查
  const fetchHotCasesForce = async () => {
    // console.log('[HomeStore] 强制获取热门箱子数据')
    hotCasesLoading.value = true
    hotCasesError.value = null
    
    try {
      const caseStore = useCaseStore()
      await caseStore.fetchHotCases(5, true) // force = true
      lastHotCasesRefresh.value = Date.now()
      // console.log('[HomeStore] 强制热门箱子数据获取完成')
    } catch (error) {
      hotCasesError.value = error instanceof Error ? error.message : '获取热门箱子数据失败'
      // console.error('[HomeStore] 强制获取热门箱子数据失败:', error)
    } finally {
      hotCasesLoading.value = false
    }
  }

  // 强制获取折扣箱子 - 忽略刷新检查
  const fetchDiscountCasesForce = async () => {
    // console.log('[HomeStore] 强制获取折扣箱子数据')
    discountCasesLoading.value = true
    discountCasesError.value = null
    
    try {
      const caseStore = useCaseStore()
      await caseStore.fetchDiscountCases(5, true) // force = true
      lastDiscountCasesRefresh.value = Date.now()
      // console.log('[HomeStore] 强制折扣箱子数据获取完成')
    } catch (error) {
      discountCasesError.value = error instanceof Error ? error.message : '获取折扣箱子数据失败'
      // console.error('[HomeStore] 强制获取折扣箱子数据失败:', error)
    } finally {
      discountCasesLoading.value = false
    }
  }

  // 强制获取新品箱子 - 忽略刷新检查
  const fetchNewCasesForce = async () => {
    // console.log('[HomeStore] 强制获取新品箱子数据')
    newCasesLoading.value = true
    newCasesError.value = null
    
    try {
      const caseStore = useCaseStore()
      await caseStore.fetchNewCases(5, true) // force = true
      lastNewCasesRefresh.value = Date.now()
      // console.log('[HomeStore] 强制新品箱子数据获取完成')
    } catch (error) {
      newCasesError.value = error instanceof Error ? error.message : '获取新品箱子数据失败'
      // console.error('[HomeStore] 强制获取新品箱子数据失败:', error)
    } finally {
      newCasesLoading.value = false
    }
  }

  // 更新统计数据
  const updateStatsData = (newStats: Partial<StatsData>) => {
    statsData.value = { ...statsData.value, ...newStats }
  }

  // 添加开箱记录
  const addCaseRecord = (record: CaseRecord) => {
    caseRecords.value.unshift(record)
    // 保持最多显示50条记录
    if (caseRecords.value.length > 50) {
      caseRecords.value = caseRecords.value.slice(0, 50)
    }
  }

  // 更新开箱记录
  const updateCaseRecords = (records: CaseRecord[]) => {
    caseRecords.value = records
  }

  // 设置Banner数据（用于SSR数据同步）
  const setBannerData = (data: BannerItem[]) => {
    banner.value = data
    lastBannerRefresh.value = Date.now()
  }

  // 更新随机皮肤数据（用于SSR数据同步）
  const updateRandomSkins = (data: any[]) => {
    rawRandomSkinsData.value = data
    randomSkins.value = transformSimpleSkinItems(data, locale.value)
    lastRandomSkinsRefresh.value = Date.now()
  }

  return {
    // State
    banner,
    bannerLoading,
    bannerError,
    statsData,
    statsLoading,
    statsError,
    caseRecords,
    caseRecordsLoading,
    caseRecordsError,
    hotCasesLoading,
    hotCasesError,
    discountCasesLoading,
    discountCasesError,
    newCasesLoading,
    newCasesError,
    randomSkins,
    randomSkinsLoading,
    randomSkinsError,
    
    // Getters
    hotCases,
    discountCases,
    newCases,
    shouldRefreshBanner,
    shouldRefreshStats,
    shouldRefreshCaseRecords,
    shouldRefreshHotCases,
    shouldRefreshDiscountCases,
    shouldRefreshNewCases,
    shouldRefreshRandomSkins,
    
    // Actions
    fetchStatsAndRecords,
    fetchBanner,
    fetchHotCases,
    fetchDiscountCases,
    fetchNewCases,
    fetchRandomSkins,
    initializeHomeData,
    fetchHotCasesForce,
    fetchDiscountCasesForce,
    fetchNewCasesForce,
    updateStatsData,
    addCaseRecord,
    updateCaseRecords,
    setBannerData,
    updateRandomSkins,
    processBannerData
  }
}) 