import json
import logging
# import logging.config
import pytz

from django.db.models import Sum
from django.utils.translation import gettext_lazy as _
from django.db import connection, transaction
from django.utils import timezone

from decimal import Decimal



from steambase.enums import RarityColor
from box.models import CaseRecord, CaseRoom
from steambase.redis_con import get_redis
from authentication.models import AuthUser, UserAsset, UserExtra
from charge.models import ChargeRecord
from b2ctrade.models import ZBTradeRecord
from sitecfg.interfaces import get_enable_dynamic_box_chance
from package.models import PackageItem, PackageState
from steambase.enums import RespCode, B2CTradeState, ChargeState, GameState

# 配置日志
logging.basicConfig(level=logging.INFO)
_logger = logging.getLogger(__name__)

bj_timezone = pytz.timezone('Asia/Shanghai')
# _logger = logging.getLogger('utils')
#_logger.info('This is an info message from box.utils')


def get_case_item_info_number():
    r = get_redis()

    RareCount = r.get('moniotr_RareCount')
    if RareCount:
        RareCount = json.loads(RareCount)
    else:
        RareCount = CaseRecord.objects.filter(item_info__rarity_color=RarityColor.Rare.value).count()
        r.set('moniot_RareCount', json.dumps(RareCount), 60 * 5)

    MythicalCount = r.get('moniotr_MythicalCount')
    if MythicalCount:
        MythicalCount = json.loads(MythicalCount)
    else:
        MythicalCount = CaseRecord.objects.filter(item_info__rarity_color=RarityColor.Mythical.value).count()
        r.set('moniotr_MythicalCount', json.dumps(MythicalCount), 60 * 5)

    LegendaryCount = r.get('moniotr_LegendaryCount')
    if LegendaryCount:
        LegendaryCount = json.loads(LegendaryCount)
    else:
        LegendaryCount = CaseRecord.objects.filter(item_info__rarity_color=RarityColor.Legendary.value).count()
        r.set('moniotr_LegendaryCount', json.dumps(LegendaryCount), 60 * 5)

    AncientCount = r.get('moniotr_AncientCount')
    if AncientCount:
        AncientCount = json.loads(AncientCount)
    else:
        AncientCount = CaseRecord.objects.filter(item_info__rarity_color=RarityColor.Ancient.value).count()
        r.set('moniot_AncientCount', json.dumps(AncientCount), 60 * 5)

    KnifeCount = r.get('KnifeCount')
    if KnifeCount:
        KnifeCount = json.loads(KnifeCount)
    else:
        KnifeCount = CaseRecord.objects.filter(item_info__market_hash_name__contains='Knife').count()
        r.set('moniot_KnifeCount', json.dumps(KnifeCount), 60 * 5)

    StatTrakCount = r.get('StatTrakCount')
    if StatTrakCount:
        StatTrakCount = json.loads(StatTrakCount)
    else:
        StatTrakCount = CaseRecord.objects.filter(item_info__market_hash_name__contains='StatTrak').count()
        r.set('moniot_StatTrakCount', json.dumps(StatTrakCount), 60 * 5)

    data = dict(RareCount=RareCount, MythicalCount=MythicalCount, LegendaryCount=LegendaryCount,
                AncientCount=AncientCount, KnifeCount=KnifeCount, StatTrakCount=StatTrakCount)
    return data




def update_user_box_chance_type(user):
    """
    动态更新用户的 box_chance_type
    :param user: 当前用户
    :return: 响应码
    """

    def get_total_values(user):
        """
        获取用户的余额，总充值，总提取、当日提取、当日新增物品总价值和总资产价值
        :param user: 当前用户
        :return: 余额，总充值，当日充值，总提取，当日提取，当日新增物品总价值，总资产价值和参与战斗总金额
        """
        # 用户余额信息
        user_balance = UserAsset.objects.select_for_update().filter(user=user).first()
        
        today_start = timezone.now().astimezone(bj_timezone).replace(hour=0, minute=0, second=0, microsecond=0)
        today_end = today_start + timezone.timedelta(days=1)
        # 当日充值金额
        today_charge_amount = ChargeRecord.objects.filter(
            user=user, state=ChargeState.Succeed.value, pay_time__gte=today_start, pay_time__lt=today_end
        ).aggregate(Sum('amount'))['amount__sum'] or 0

        # 提取中，排除提取成功 B2CTradeState.Accepted.value
        withdrawing = ZBTradeRecord.objects.filter(
            user=user, state__in=[B2CTradeState.Initialed.value, B2CTradeState.Trading.value]
        ).aggregate(Sum('amount'))['amount__sum'] or 0

        # 当日提取金额
        today_withdraw = ZBTradeRecord.objects.filter(
            user=user,
            state__in=[B2CTradeState.Initialed.value, B2CTradeState.Accepted.value, B2CTradeState.Trading.value],
            create_time__gte=today_start,
            create_time__lt=today_end
        ).aggregate(Sum('amount'))['amount__sum'] or 0

        # 用户总物品价值
        total_items_amount = PackageItem.objects.filter(
            user=user, state=PackageState.Available.value
        ).aggregate(Sum('amount'))['amount__sum'] or 0

        # 当日新增物品总价值
        today_items_amount = PackageItem.objects.filter(
            user=user,
            state=PackageState.Available.value,
            create_time__gte=today_start,
            create_time__lt=today_end
        ).aggregate(Sum('amount'))['amount__sum'] or 0

        # 用户参与战斗的总金额
        battle_amount = CaseRoom.objects.filter(
            user=user, state__in=[
                GameState.Initial.value,
                GameState.Joinable.value,
                GameState.Joining.value,
                GameState.Full.value,
                GameState.Running.value,
            ]
        ).aggregate(Sum('price'))['price__sum'] or 0

        return user_balance, today_charge_amount, withdrawing, today_withdraw, total_items_amount, today_items_amount, battle_amount

    def should_update_box_chance(user):
        """
        判断是否应该更新 box_chance_type
        :param user: 当前用户
        :return: 布尔值
        """
        user_extra = UserExtra.objects.select_for_update().filter(user=user).first()
        return not user.is_superuser and not user.is_staff and not user_extra.locked_box_chance

    def update_box_chance(user, balance, total_charge, total_user, total_withdraw, today_charge_amount, today_withdraw, today_items_amount, battle_amount):
        """
        更新用户的 box_chance_type
        :param user: 当前用户
        :param balance: 用户余额
        :param total_charge: 用户总充值
        :param total_user: 用户总资产
        :param total_withdraw: 用户总提取
        :param today_charge_amount: 用户今日充值金额
        :param today_withdraw: 当日提取物品的总金额
        :param today_items_amount: 当日新增可用物品总价值
        :param battle_amount: 用户未结束对战的总金额
        :return: None
        """
        BALANCE_THRESHOLD = 10  # 未充值用户的上限

        # 判断亏损情况
        loss_percentage = total_user / total_charge if total_charge > 0 else 0
        today_riches = balance + today_withdraw + today_items_amount + battle_amount 
        today_loss_percentage = today_riches / today_charge_amount if today_charge_amount > 0 else 0

        # 优先级: a > c > e
        # 判断是否为 'a'，当日止盈
        if today_loss_percentage > 1.5:
            user.update_box_chance_type('c')
            # 日志
            _logger.info(f"当日止盈C:{user.steam.personaname}，今日获取：{today_riches:.2f}，今日充值：{today_charge_amount:.2f}，余额：{balance:.2f}，今日取回：{today_withdraw:.2f}，今日获取可用：{today_items_amount:.2f}，正在对战：{battle_amount:.2f}，今日比例：{today_loss_percentage:.2f}")            
            return
        # elif today_loss_percentage > 1.5:
        #     user.update_box_chance_type('a')
        #     _logger.info(f"当日止盈A:{user.username}，今日获取总价值：{today_riches:.2f}，今日充值总额：{today_charge_amount:.2f}，账户余额：{balance:.2f}，今日取回总价值：{today_withdraw:.2f}，今日获取可用饰品价值：{today_items_amount:.2f}，正在对战总价值：{battle_amount:.2f}，今日盈利比例：{today_loss_percentage:.2f}")
        #     return

        # 获取用户的止盈和止损阈值
        profit_limit = user.extra.profit_limit
        loss_limit = user.extra.loss_limit
        

        # 使用止盈阈值进行判断
        if profit_limit > 0 and loss_limit > 0:
            _logger.info(f"独立止盈阈值进行判断:{user.username}，盈利比例：{loss_percentage:.2f}，止盈阈值：{profit_limit:.2f}, 止损阈值：{loss_limit:.2f}")
            if loss_percentage > profit_limit:
                user.update_box_chance_type('c')
                _logger.info(f"独立止盈阈值C:{user.steam.personaname}，今日获取：{today_riches:.2f}，今日充值：{today_charge_amount:.2f}，余额：{balance:.2f}，今日取回：{today_withdraw:.2f}，今日获取可用：{today_items_amount:.2f}，正在对战：{battle_amount:.2f}，今日比例：{today_loss_percentage:.2f}")
                return
            elif loss_percentage < loss_limit:
                user.update_box_chance_type('e')
                _logger.info(f"独立止损阈值E:{user.steam.personaname}，今日获取：{today_riches:.2f}，今日充值：{today_charge_amount:.2f}，余额：{balance:.2f}，今日取回：{today_withdraw:.2f}，今日获取可用：{today_items_amount:.2f}，正在对战：{battle_amount:.2f}，今日比例：{today_loss_percentage:.2f}")
                return
            else:
                user.update_box_chance_type('a')
                _logger.info(f"独立止盈阈值A:{user.steam.personaname}，今日获取：{today_riches:.2f}，今日充值：{today_charge_amount:.2f}，余额：{balance:.2f}，今日取回：{today_withdraw:.2f}，今日获取可用：{today_items_amount:.2f}，正在对战：{battle_amount:.2f}，今日比例：{today_loss_percentage:.2f}")
                return

        # 使用止损阈值进行判断
        # if loss_limit > 0:
        #     _logger.info(f"独立止盈阈值进行判断:{user.username}，盈利比例：{loss_percentage:.2f}，止盈阈值：{profit_limit:.2f}")
        #     if loss_percentage < loss_limit:
        #         user.update_box_chance_type('e')
        #         _logger.info(f"独立止损阈值E:{user.steam.personaname}，累计充值：{total_charge:.2f}，累计资产：{total_user:.2f}，累计取回：{total_withdraw:.2f}，账户余额：{balance:.2f}，今日充值：{today_charge_amount:.2f}，今日取回：{today_withdraw:.2f}，今日比例：{today_loss_percentage:.2f}，账户比例：{loss_percentage:.2f}")
        #         return
        #     else:
        #         user.update_box_chance_type('a')
        #         _logger.info(f"独立止损阈值A:{user.steam.personaname}，累计充值：{total_charge:.2f}，累计资产：{total_user:.2f}，累计取回：{total_withdraw:.2f}，账户余额：{balance:.2f}，今日充值：{today_charge_amount:.2f}，今日取回：{today_withdraw:.2f}，今日比例：{today_loss_percentage:.2f}，账户比例：{loss_percentage:.2f}")
        #         return

        # 使用字典来管理 'e' 的判断条件，止损
        e_conditions = {
            15000: 0.6,
            10000: 0.66,
            8000: 0.65,
            7000: 0.64,
            6000: 0.63,
            5000: 0.62,
            4500: 0.61,
            4000: 0.60,
            3500: 0.59,
            3000: 0.58,
            2500: 0.57,
            2000: 0.56,
            1500: 0.55,
            1000: 0.50,
            900: 0.45,
            800: 0.40,
            750: 0.35,
            700: 0.33,
            650: 0.32,
            600: 0.31,
            550: 0.30,
            500: 0.29,
            450: 0.28,
            400: 0.27,
            350: 0.26,
            300: 0.25,
            250: 0.24,
            200: 0.23,
            150: 0.22,
            100: 0.21,
            50: 0.20,
            10: 0.15,
        }

        # 遍历字典进行 'e' 的判断
        for charge_threshold, percentage_threshold in e_conditions.items():
            if total_charge > charge_threshold and loss_percentage < percentage_threshold:
                user.update_box_chance_type('e')
                _logger.info(f"账户止损E:{user.steam.personaname}，累计充值：{total_charge:.2f}，累计资产：{total_user:.2f}，累计取回：{total_withdraw:.2f}，账户余额：{balance:.2f}，今日充值：{today_charge_amount:.2f}，今日取回：{today_withdraw:.2f}，今日比例：{today_loss_percentage:.2f}，账户比例：{loss_percentage:.2f}，阈值：{percentage_threshold:.2f}")
                return

        # 判断是否为 'c'，账户止盈
        if total_charge == 0 and total_user > BALANCE_THRESHOLD:
            user.update_box_chance_type('c')
            _logger.info(f"无充值止盈C:{user.steam.personaname},累计资产：{total_user:.2f}")
            return
        elif total_charge > 0 and loss_percentage > 0.8:
            user.update_box_chance_type('c')
            _logger.info(f"账户止盈C:{user.steam.personaname}，累计充值：{total_charge:.2f}，累计资产：{total_user:.2f}，累计取回：{total_withdraw:.2f}，账户余额：{balance:.2f}，今日充值：{today_charge_amount:.2f}，今日取回：{today_withdraw:.2f}，今日比例：{today_loss_percentage:.2f}，账户比例：{loss_percentage:.2f}，阈值：{percentage_threshold:.2f}")
            return

        # 默认修改为 'a'
        user.update_box_chance_type('a')
        _logger.info(f"返回默认概率A:{user.steam.personaname}，累计充值：{total_charge:.2f}，累计资产：{total_user:.2f}，累计取回：{total_withdraw:.2f}，账户余额：{balance:.2f}，今日充值：{today_charge_amount:.2f}，今日取回：{today_withdraw:.2f}，今日比例：{today_loss_percentage:.2f}，账户比例：{loss_percentage:.2f}")
       

    

    if get_enable_dynamic_box_chance():
        # 检查是否需要更新箱子几率
        if should_update_box_chance(user):
            try:
                with transaction.atomic():
                    user_balance, today_charge_amount, withdrawing, today_withdraw, total_items_amount, today_items_amount, battle_amount = get_total_values(user)
                    
                    # 如果 user_balance 存在
                    if user_balance:
                        # 将所有数值转换为 Decimal 类型
                        balance = Decimal(user_balance.balance)
                        total_charge = Decimal(user_balance.total_charge_balance)
                        total_withdraw = Decimal(withdrawing) + Decimal(user_balance.total_withdraw_balance)
                        total_items_amount = Decimal(total_items_amount)
                        battle_amount = Decimal(battle_amount)

                        # 计算用户总金额
                        total_user = balance + total_withdraw + total_items_amount + battle_amount
                        
                        # 更新箱子几率
                        update_box_chance(
                            user, balance, total_charge, total_user, total_withdraw, 
                            Decimal(today_charge_amount), Decimal(today_withdraw), Decimal(today_items_amount), battle_amount
                        )
            except Exception as e:
                _logger.exception(e)
                return RespCode.Exception.value, str(e)
        else:
            _logger.info(f"用户 {user.steam.personaname} 锁定概率：累计充值：{total_charge:.2f}，累计资产：{total_user:.2f}，累计取回：{total_withdraw:.2f}，账户余额：{balance:.2f}，今日充值：{today_charge_amount:.2f}，今日取回：{today_withdraw:.2f}")

    return RespCode.Succeed.value
