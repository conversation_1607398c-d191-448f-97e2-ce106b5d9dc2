{% extends 'admin/base.html' %}
{% load static %}
{% load i18n %}

{% block extrahead %}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/vue/2.3.1/vue.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/axios/0.16.2/axios.min.js"></script>
{% endblock %}

{% block content %}
    <div class="row">
        <div class="col-md-6" id="table">
            <div class="box box-solid box-info">
                <!-- /.box-header -->
                <div class="box-header with-border">
                    <h2 class="box-title">每日新增用户</h2>
                </div>
                <!-- /.box-body -->
                <div class="box-body">
                    <table class="table no-margin">
                        <thead>
                            <tr>
                            <th>日期</th>
                            <th>人数</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="item in tableData.user_statistics_day">
                                <td>^{item.date}</td>
                                <td>^{item.count}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <!-- /.box-footer -->
                <div class="box-footer clearfix">
                    <a href="{% url 'admin:index' %}authentication/userstatisticsday/" target="_blank" class="btn btn-sm btn-default btn-flat pull-right">查看所有记录</a>
                </div>
            </div>

            <div class="box box-solid box-info">
                <!-- /.box-header -->
                <div class="box-header with-border">
                    <h2 class="box-title">每日充值金额</h2>
                </div>
                <!-- /.box-body -->
                <div class="box-body">
                    <table class="table no-margin">
                        <thead>
                            <tr>
                            <th>日期</th>
                            <th>金额</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="item in tableData.charge_statistics_day">
                                <td>^{item.date}</td>
                                <td>^{item.amount}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <!-- /.box-footer -->
                <div class="box-footer clearfix">
                    <a href="{% url 'admin:index' %}charge/chargestatisticsday/" target="_blank" class="btn btn-sm btn-default btn-flat pull-right">查看所有记录</a>
                </div>
            </div>

            <div class="box box-solid box-info">
                <!-- /.box-header -->
                <div class="box-header with-border">
                    <h2 class="box-title">每日开箱金额</h2>
                </div>
                <!-- /.box-body -->
                <div class="box-body">
                    <table class="table no-margin">
                        <thead>
                            <tr>
                            <th>日期</th>
                            <th>金额</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="item in tableData.case_statistics_day_data">
                                <td>^{item.date}</td>
                                <td>^{item.amount}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <!-- /.box-footer -->
                <div class="box-footer clearfix">
                    <a href="{% url 'admin:index' %}box/casestatisticsday/" target="_blank" class="btn btn-sm btn-default btn-flat pull-right">查看所有记录</a>
                </div>
            </div>

            <div class="box box-solid box-info">
                <!-- /.box-header -->
                <div class="box-header with-border">
                    <h2 class="box-title">每日取回物品金额</h2>
                </div>
                <!-- /.box-body -->
                <div class="box-body">
                    <table class="table no-margin">
                        <thead>
                            <tr>
                            <th>日期</th>
                            <th>金额</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="item in tableData.withdraw_statistics_day">
                                <td>^{item.date}</td>
                                <td>^{item.amount}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <!-- /.box-footer -->
                <div class="box-footer clearfix">
                    <a href="{% url 'admin:index' %}package/packagestatisticsday/?trade_type=2" target="_blank" class="btn btn-sm btn-default btn-flat pull-right">查看所有记录</a>
                </div>
            </div>
        </div>

        <div id="noSocket">{% trans 'Connecting' %}...</div>
        <style>
            #noSocket {
                position: absolute;
                top: 30%;
                left: 0;
                right: 0;
                z-index: 100;
                width: 400px;
                margin: 0 auto;
                background-color: rgba(240,240,240,0.95);
                border: 2px solid #ddd;
                font-size: 22px;
                text-align: center;
                padding: 10px 25px;
                display: none;
            }
            .table {
                width: 100%;
            }
        </style>
        <script>
            $(function () {
                var table = new Vue({
                    el: '#table',
                    delimiters: ['^{', '}'],
                    data: {
                      tableData: {}
                    },
                    created: function () {
                      let that = this;
                      that.loadData();
                    },
                    methods: {
                      loadData: function () {
                        let that = this;
                        let tUrl = '/super/monitor/data/';
                        axios.get(tUrl).then(function (resp) {
                          let result = resp.data;
                          if (result.code === 0) {
                            that.tableData = result.body;
                          } else {
                            that.$message({type: 'warning', message: result.message})
                          }
                        });
                      }
                    }
                });
            });
        </script>
    </div>
{% endblock %}


