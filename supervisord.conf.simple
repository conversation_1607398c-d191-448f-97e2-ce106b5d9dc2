[supervisord]
nodaemon=true
logfile=/app/logs/supervisord.log
logfile_maxbytes=50MB
logfile_backups=10
loglevel=info
pidfile=/app/logs/supervisord.pid
user=root

[program:steambase_server]
command=/usr/local/bin/python3 /usr/local/bin/gunicorn --config=/app/server.conf --log-config=/app/log_config.ini steambase.wsgi:application
stdout_logfile=/app/logs/gunicorn.log
stderr_logfile=/app/logs/gunicorn_error.log
autostart=true
autorestart=true
startsecs=5
priority=1
stopasgroup=true
killasgroup=true
