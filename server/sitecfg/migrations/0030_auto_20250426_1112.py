# -*- coding: utf-8 -*-
# Generated by Django 1.11.29 on 2025-04-26 03:12
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('sitecfg', '0029_siteconfig_order'),
    ]

    operations = [
        migrations.AddField(
            model_name='banner',
            name='background_class',
            field=models.CharField(blank=True, default=None, max_length=128, null=True, verbose_name='background class'),
        ),
        migrations.AddField(
            model_name='banner',
            name='description',
            field=models.TextField(blank=True, default=None, null=True, verbose_name='description'),
        ),
        migrations.AddField(
            model_name='banner',
            name='glow_class',
            field=models.CharField(blank=True, default=None, max_length=128, null=True, verbose_name='glow class'),
        ),
        migrations.AddField(
            model_name='banner',
            name='image',
            field=models.ImageField(blank=True, default=None, null=True, upload_to='banner', verbose_name='image'),
        ),
        migrations.AddField(
            model_name='banner',
            name='is_simple',
            field=models.BooleanField(default=True, verbose_name='is simple'),
        ),
        migrations.AddField(
            model_name='banner',
            name='link',
            field=models.CharField(blank=True, default=None, max_length=255, null=True, verbose_name='link'),
        ),
        migrations.AddField(
            model_name='banner',
            name='order',
            field=models.IntegerField(default=0, verbose_name='order No.'),
        ),
        migrations.AddField(
            model_name='banner',
            name='primary_button_link',
            field=models.CharField(blank=True, default=None, max_length=128, null=True, verbose_name='primary button link'),
        ),
        migrations.AddField(
            model_name='banner',
            name='primary_button_text',
            field=models.CharField(blank=True, default=None, max_length=128, null=True, verbose_name='primary button text'),
        ),
        migrations.AddField(
            model_name='banner',
            name='secondary_button_link',
            field=models.CharField(blank=True, default=None, max_length=128, null=True, verbose_name='secondary button link'),
        ),
        migrations.AddField(
            model_name='banner',
            name='secondary_button_text',
            field=models.CharField(blank=True, default=None, max_length=128, null=True, verbose_name='secondary button text'),
        ),
    ]
