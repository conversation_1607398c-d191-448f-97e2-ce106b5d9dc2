<template>
  <div 
    :class="['battle-connection-status', statusClass]"
    @click="toggleDetails"
  >
    <!-- 紧凑模式 -->
    <div v-if="!showDetails" class="status-compact">
      <div class="status-indicator">
        <Icon 
          :name="statusIcon" 
          class="w-4 h-4 transition-all duration-300"
          :class="statusIconClass"
        />
        <span class="status-text">{{ statusText }}</span>
      </div>
      
      <!-- 重连时显示进度 -->
      <div v-if="isReconnecting" class="reconnect-progress">
        <span class="reconnect-text">
          {{ t('battle.connection.reconnecting') }} ({{ reconnectAttempts }})
        </span>
        <div class="progress-dots">
          <span 
            v-for="i in 3" 
            :key="i" 
            class="dot"
            :style="{ animationDelay: `${(i - 1) * 0.2}s` }"
          ></span>
        </div>
      </div>
    </div>
    
    <!-- 详细模式（点击展开） -->
    <div v-if="showDetails" class="status-details">
      <div class="details-header">
        <h4 class="details-title">{{ t('battle.connection.status') }}</h4>
        <button 
          @click.stop="toggleDetails"
          class="close-btn"
          :aria-label="t('common.close')"
        >
          <Icon name="heroicons:x-mark" class="w-4 h-4" />
        </button>
      </div>
      
      <div class="details-content">
        <!-- 连接状态 -->
        <div class="detail-item">
          <span class="detail-label">{{ t('battle.connection.state') }}:</span>
          <span :class="['detail-value', statusClass]">
            {{ statusText }}
          </span>
        </div>
        
        <!-- 重连次数 -->
        <div v-if="reconnectAttempts > 0" class="detail-item">
          <span class="detail-label">{{ t('battle.connection.attempts') }}:</span>
          <span class="detail-value">{{ reconnectAttempts }}</span>
        </div>
        
        <!-- 连接时间 -->
        <div v-if="isConnected" class="detail-item">
          <span class="detail-label">{{ t('battle.connection.uptime') }}:</span>
          <span class="detail-value">{{ connectionUptime }}</span>
        </div>
        
        <!-- 网络质量 -->
        <div class="detail-item">
          <span class="detail-label">{{ t('battle.connection.quality') }}:</span>
          <span :class="['detail-value', qualityClass]">
            {{ networkQuality }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useI18n } from 'vue-i18n'

// Props 接口定义
interface BattleConnectionStatusProps {
  isConnected: boolean
  isReconnecting: boolean
  reconnectAttempts: number
}

// Props
const props = defineProps<BattleConnectionStatusProps>()

// 国际化
const { t } = useI18n()

// 响应式状态
const showDetails = ref(false)
const connectionStartTime = ref<number | null>(null)
const connectionUptime = ref('00:00')

// 计算属性
const statusClass = computed(() => {
  if (props.isReconnecting) return 'status-reconnecting'
  if (props.isConnected) return 'status-connected'
  return 'status-disconnected'
})

const statusIcon = computed(() => {
  if (props.isReconnecting) return 'heroicons:arrow-path'
  if (props.isConnected) return 'heroicons:wifi'
  return 'heroicons:exclamation-triangle'
})

const statusIconClass = computed(() => {
  if (props.isReconnecting) return 'text-orange-500 animate-spin'
  if (props.isConnected) return 'text-green-500'
  return 'text-red-500'
})

const statusText = computed(() => {
  if (props.isReconnecting) return t('battle.connection.reconnecting')
  if (props.isConnected) return t('battle.connection.connected')
  return t('battle.connection.disconnected')
})

const networkQuality = computed(() => {
  if (props.isReconnecting) return t('battle.connection.quality.reconnecting')
  if (props.isConnected) return t('battle.connection.quality.excellent')
  return t('battle.connection.quality.poor')
})

const qualityClass = computed(() => {
  if (props.isReconnecting) return 'text-orange-500'
  if (props.isConnected) return 'text-green-500'
  return 'text-red-500'
})

// 方法
const toggleDetails = () => {
  showDetails.value = !showDetails.value
}

const updateConnectionUptime = () => {
  if (props.isConnected && connectionStartTime.value) {
    const now = Date.now()
    const uptime = now - connectionStartTime.value
    const minutes = Math.floor(uptime / 60000)
    const seconds = Math.floor((uptime % 60000) / 1000)
    connectionUptime.value = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
  }
}

// 监听连接状态变化
const handleConnectionChange = () => {
  if (props.isConnected && !connectionStartTime.value) {
    connectionStartTime.value = Date.now()
  } else if (!props.isConnected) {
    connectionStartTime.value = null
    connectionUptime.value = '00:00'
  }
}

// 生命周期
onMounted(() => {
  handleConnectionChange()
  const interval = setInterval(updateConnectionUptime, 1000)
  
  onUnmounted(() => {
    clearInterval(interval)
  })
})

// 监听 props 变化
watch(() => props.isConnected, handleConnectionChange)
</script>

<style scoped lang="scss">
.battle-connection-status {
  @apply relative cursor-pointer select-none;
  
  // 紧凑模式
  .status-compact {
    @apply flex items-center gap-2 px-3 py-2 rounded-lg bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 transition-all duration-300;
    
    .status-indicator {
      @apply flex items-center gap-2;
      
      .status-text {
        @apply text-sm font-medium;
      }
    }
    
    .reconnect-progress {
      @apply flex items-center gap-2 ml-2;
      
      .reconnect-text {
        @apply text-xs text-orange-400;
      }
      
      .progress-dots {
        @apply flex gap-1;
        
        .dot {
          @apply w-1.5 h-1.5 bg-orange-400 rounded-full;
          animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
      }
    }
  }
  
  // 详细模式
  .status-details {
    @apply absolute top-full left-0 mt-2 w-64 bg-gray-900/95 backdrop-blur-md border border-gray-700/50 rounded-lg shadow-xl z-50;
    
    .details-header {
      @apply flex items-center justify-between p-3 border-b border-gray-700/50;
      
      .details-title {
        @apply text-sm font-semibold text-gray-200;
      }
      
      .close-btn {
        @apply p-1 rounded hover:bg-gray-700/50 transition-colors;
      }
    }
    
    .details-content {
      @apply p-3 space-y-2;
      
      .detail-item {
        @apply flex items-center justify-between;
        
        .detail-label {
          @apply text-xs text-gray-400;
        }
        
        .detail-value {
          @apply text-xs font-medium;
        }
      }
    }
  }
  
  // 状态样式
  &.status-connected {
    .status-compact {
      @apply border-green-500/30 bg-green-500/10;
    }
  }
  
  &.status-reconnecting {
    .status-compact {
      @apply border-orange-500/30 bg-orange-500/10;
    }
  }
  
  &.status-disconnected {
    .status-compact {
      @apply border-red-500/30 bg-red-500/10;
    }
  }
  
  // 悬停效果
  &:hover .status-compact {
    @apply bg-gray-800/70 border-gray-600/50;
  }
  
  // 动画
  .animate-pulse {
    animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
  
  .animate-spin {
    animation: spin 1s linear infinite;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .battle-connection-status {
    .status-compact {
      @apply px-2 py-1.5;
      
      .status-indicator {
        .status-text {
          @apply text-xs;
        }
      }
      
      .reconnect-progress {
        .reconnect-text {
          @apply text-[10px];
        }
      }
    }
    
    .status-details {
      @apply w-56;
    }
  }
}
</style> 