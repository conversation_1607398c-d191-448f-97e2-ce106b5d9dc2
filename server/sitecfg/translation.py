from modeltranslation.translator import register, translator, TranslationOptions

from sitecfg.models import Announcee, FAQ, Support, Banner, SEO, AccessControl, ArticleCategoryy


@register(Announcee)
class AnnounceeTranslationOptions(TranslationOptions):
    fields = ('title',)  # 暂时移除 content 字段，等待 CKEditor5 翻译支持

@register(FAQ)
class FAQTranslationOptions(TranslationOptions):
    fields = ('title',)  # 暂时移除 content 字段，等待 CKEditor5 翻译支持

@register(Support)
class SupportTranslationOptions(TranslationOptions):
    fields = ('title',)  # 暂时移除 content 字段，等待 CKEditor5 翻译支持

@register(Banner)
class BannerTranslationOptions(TranslationOptions):
    fields = ('title', 'description', 'primary_button_text', 'secondary_button_text',)

@register(SEO)
class SEOTranslationOptions(TranslationOptions):
    fields = ('name', 'title', 'subtitle', 'keywords', 'description',)

@register(AccessControl)
class AccessControlTranslationOptions(TranslationOptions):
    fields = ('message', )

@register(ArticleCategoryy)
class ArticleCategoryyTranslationOptions(TranslationOptions):
    fields = ('name', )