from django.conf.urls import url
from django.contrib.auth.decorators import login_required

from authentication import views

app_name = 'auth'

urlpatterns = [
    url(r'^steam/$', views.IndexView.as_view(), name='steam_index'),
    url(r'^logout/', login_required(views.LogoutView.as_view(), login_url='/'), name='logout'),
    url(r'^adminlogout/', login_required(views.AdminLogoutView.as_view(), login_url='/'), name='admin_logout'),

    

    url(r'^checklogin/', views.CheckLoginView.as_view()),
    url(r'^userinfo/', views.GetUserInfoView.as_view()),
    

    url(r'^balancerecord/', views.GetUserBalanceRecordView.as_view()),

    url(r'^phone/code/', views.SendSmsCodeView.as_view()),
    url(r'^phone/register/', views.RegisterByPhoneView.as_view()),
    url(r'^phone/login/', views.LoginByPhoneView.as_view()),
    url(r'^phone/reset/', views.ResetPasswordByPhoneView.as_view()),

   

    url(r'^set/phone/', views.SetUserPhoneView.as_view()),
    url(r'^set/name/', views.SetNameView.as_view()),
    url(r'^set/email/', views.SetUserEmailView.as_view()),
    url(r'^set/avatar/', views.SetAvatar.as_view()),
    url(r'^exam/check/', views.CheckUserExamView.as_view()),
    url(r'^exam/submit/', views.SetUserExamView.as_view()),

    

    url(r'^deleteoldblance/', views.DeleteOldBalanceRecordsView.as_view()),
    url(r'^deleteoldlog/', views.DeleteOldLogView.as_view()),
    url(r'^updatestaffbalance/', views.UpdateStaffBalanceView.as_view()),
    # 删除积分记录
    url(r'^deleteoldpoint/', views.DeleteOldUserActivePointRecordsView.as_view()),

    


    # 2025-05-20
    url(r'^csrf/', views.GetCsrftokenView.as_view()),
    url(r'^token/', views.GetTokenView.as_view()),
    url(r'^captcha/', views.GetCaptchaView.as_view()),
    url(r'^email/request-reset/', views.RequestResetEmailView.as_view()),
    url(r'^email/reset-password/', views.ResetPasswordByEmailView.as_view()),
    url(r'^email/code/', views.SendEmailCodeView.as_view()),
    url(r'^email/register/', views.RegisterByEmailView.as_view()),
    url(r'^email/login/', views.LoginByEmailView.as_view()),
    # 设置用户交易链接
    url(r'^set/steamlink/', views.SetUserTradeurlView.as_view()),



]
