import os,sys
import django
sys.path.append('./')
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "steambase.settings")
django.setup()

import csv
from uuid import uuid1
from package.models import ItemInfo

csvfile = './csgo_info0924.csv'

import csv
with open(csvfile,'r', encoding='UTF-8') as f:
    f_csv = csv.DictReader(f)
    count = 0
    for item in f_csv:
        appid = item['appid']
        contextid = item['contextid']
        classid = item['classid']
        market_hash_name = item['market_hash_name']
        market_name_cn = item['market_name_cn']
        icon_url = item['icon_url']
        icon_url_large = item['icon_url_large']
        exterior = item['exterior']
        rarity = item['rarity']
        rarity_color = item['rarity_color']
        _type = item['type']
        quality = item['quality']
        quality_color = item['quality_color']
        weapon = item['weapon']
        item = ItemInfo.objects.filter(market_hash_name=market_hash_name, market_name_cn='NULL').first()
        # print(market_hash_name, is_exists)
        if item:
            item.appid = appid
            item.contextid = contextid
            item.market_name_cn = market_name_cn
            item.name_cn = market_name_cn
            item.icon_url = icon_url
            item.icon_url_large = icon_url_large
            item.save()
        # ItemInfo.objects.create(appid=appid, contextid=contextid, classid=classid, market_hash_name=market_hash_name,
        #                            name=market_hash_name, market_name=market_hash_name, icon_url=icon_url, icon_url_large=icon_url_large,
        #                            exterior=exterior, rarity=rarity, rarity_color=rarity_color, type=_type, quality=quality, quality_color=quality_color,
        #                            weapon=weapon)
        if count > 2000:
            break
        #print(count)    
        count += 1

