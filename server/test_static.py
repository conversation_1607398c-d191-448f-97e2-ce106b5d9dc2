"""
测试静态文件访问
"""
from django.http import HttpResponse

def test_static_view(request):
    """测试静态文件的视图"""
    html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>静态文件测试</title>
        <meta charset="utf-8">
        <link rel="stylesheet" href="/static/admin/css/base.css">
        <style>
            body {{ padding: 20px; font-family: Arial, sans-serif; }}
            .test-box {{ 
                background: #f8f9fa; 
                border: 1px solid #dee2e6; 
                padding: 20px; 
                margin: 20px 0; 
                border-radius: 5px; 
            }}
            .success {{ color: #28a745; }}
            .error {{ color: #dc3545; }}
        </style>
    </head>
    <body>
        <h1>🔧 静态文件访问测试</h1>
        
        <div class="test-box">
            <h2>CSS文件测试</h2>
            <p>如果这个页面有Django管理后台的样式，说明静态文件配置正确。</p>
            <p class="success">✅ 基础样式已加载</p>
        </div>
        
        <div class="test-box">
            <h2>静态文件链接测试</h2>
            <ul>
                <li><a href="/static/admin/css/base.css" target="_blank">base.css</a></li>
                <li><a href="/static/admin/css/login.css" target="_blank">login.css</a></li>
                <li><a href="/static/admin/js/theme.js" target="_blank">theme.js</a></li>
            </ul>
        </div>
        
        <div class="test-box">
            <h2>管理后台链接</h2>
            <p><a href="/chenchen/login/" class="button">访问管理后台登录页面</a></p>
        </div>
        
        <script>
            console.log('静态文件测试页面已加载');
            // 测试CSS是否加载
            const styles = window.getComputedStyle(document.body);
            if (styles.fontFamily) {{
                console.log('✅ CSS样式已正确加载');
            }} else {{
                console.log('❌ CSS样式加载失败');
            }}
        </script>
    </body>
    </html>
    """
    
    return HttpResponse(html)
