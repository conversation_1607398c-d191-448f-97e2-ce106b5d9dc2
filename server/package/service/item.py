import json
import logging
import requests
import threading
from collections import namedtuple
from django.core.cache import cache
from django.conf import settings
from steambase.utils import ItemInfoMap, dict_field_filter, parse_wxp_price_to_usd
from package.models import ItemInfo, ItemPrice, PackageItemPriceRate

_logger = logging.getLogger(__name__)

waxpeer_price_api = "https://api.waxpeer.com/v1/get-steam-items"
_item_price_key_base = 'item_price:{market_hash_name}'


def get_item_price(market_hash_name, rate=1, update_cache=True):
    item_price_key = _item_price_key_base.format(market_hash_name=market_hash_name)
    price = cache.get(item_price_key)
    if price and not update_cache:
        price = float(price)
        #print("命中缓存")
    else:
        item_price = ItemPrice.objects.filter(item_info__market_hash_name=market_hash_name).first()
        if item_price:
            rate = 1
            if not item_price.custom:
                rate_cfg = PackageItemPriceRate.objects.filter(min_price__lte=item_price.price,
                                                               max_price__gte=item_price.price).order_by('level').first()
                if rate_cfg:
                    rate = rate_cfg.price_rate / 100
            price = item_price.price * rate
        else:
            price = 0
        cache.set(item_price_key, price, settings.HOUR_REDIS_TIMEOUT)
    return round(price * rate, 2)


    


def get_item_price_by_package(item):
    price = 0
    if item.part:
        price = item.amount
    else:
        price = get_item_price(item.item_info.market_hash_name)
    return price


def get_items_price_from_waxpeer():
    try:
        _logger.info('Update item price from waxpeer api')
        params = {
            'api': settings.WAXPEER_API_KEY
        }
        resp = requests.get(waxpeer_price_api, params=params, timeout=120)
        price_list = []
        if resp.status_code == 200:
            price_data = json.loads(resp.content, encoding='utf-8')
            items = price_data.get('items', [])
            for item in items:
                _price = {}
                wxp_price = item.get('average', 0)
                _price['price'] = parse_wxp_price_to_usd(wxp_price)
                _price['hash_name'] = item.get('name', '')
                price_list.append(_price)
            return price_list
        else:
            _logger.error(resp)
    except Exception as e:
        _logger.exception(e)


def parse_price_to_float(price):
    if price is None:
        return 0.0

    if isinstance(price, float):
        return price

    if isinstance(price, str):
        return float(price.replace(',', ''))
    else:
        return float(price)


def get_item_property(item):
    exterior_map = {
        'WearCategory0': u'崭新出厂', 'WearCategory1': u'略有磨损',
        'WearCategory2': u'久经沙场', 'WearCategory3': u'破损不堪',
        'WearCategory4': u'战痕累累', 'WearCategoryNA': u'无涂装', 'NULL': None
    }
    rarity_map = {
        'Rarity_Uncommon_Weapon': u'工业级', 'Rarity_Legendary_Weapon': u'保密级',
        'Rarity_Rare_Weapon': u'军规级', 'Rarity_Mythical_Weapon': u'受限',
        'Rarity_Ancient_Weapon': u'隐密', 'Rarity_Common_Weapon': u'消费级',
        'Rarity_Ancient': u'非凡', 'Rarity_Rare': u'高级',
        'Rarity_Mythical': u'卓越', 'Rarity_Common': u'普通级',
        'Rarity_Legendary': u'奇异',
        'Rarity_Ancient_Character': u'探员品质-大师', 'Rarity_Mythical_Character': u'探员品质-卓越',
        'Rarity_Rare_Character': u'探员品质-高级', 'Rarity_Legendary_Character': u'探员品质-非凡',
        'Rarity_Contraband': u'违禁',
        'NULL': None
    }
    properties = {}
    if item.rarity:
        properties.update({"rarity_cn": rarity_map[item.rarity]})
    if item.exterior:
        properties.update({'exterior': exterior_map[item.exterior]})
    if "StatTrak" in item.market_hash_name:
        properties.update({'dark_gold': True})
    return properties

# 2025
# 通过id获取用户的item所有价格字段
def get_item_price_by_id(item_info_id, rate=1, update_cache=True):
    """
    根据 ItemInfo.id 获取价格对象。

    1. 优先返回数据库中的 ItemPrice 记录（保持兼容，依然可 .price）。
    2. 若未找到记录，则使用现有 get_item_price 逻辑计算价格，并返回一个临时对象，
       该对象仅包含 price 字段，避免外部代码出现 NoneType 错误。
    """
    item_price = ItemPrice.objects.filter(item_info_id=item_info_id).first()
    if item_price:
        return item_price

    # 数据库无记录，尝试计算价格
    item_info = ItemInfo.objects.filter(id=item_info_id).first()
    if not item_info:
        # 若连 ItemInfo 都不存在，返回 price=0 占位对象
        TmpPrice = namedtuple('TmpPrice', ['price'])
        return TmpPrice(0)

    price_value = get_item_price(item_info.market_hash_name, rate=rate, update_cache=update_cache)
    TmpPrice = namedtuple('TmpPrice', ['price'])
    return TmpPrice(price_value)