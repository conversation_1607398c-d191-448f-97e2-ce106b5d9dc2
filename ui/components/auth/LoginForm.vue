<template>
  <div class="auth-form p-6 rounded-xl shadow-lg w-full max-w-md mx-auto bg-gray-800/40 backdrop-blur-md border border-gray-700/30">
    <h1 class="text-2xl font-bold mb-6 text-center text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-400">
      {{ $t('auth.login.title') }}
    </h1>
    
    <!-- 登录表单 -->
    <form @submit.prevent="handleSubmit" class="space-y-4">
      <!-- 邮箱输入 -->
      <div class="form-group">
        <label for="email" class="block mb-1.5 text-sm font-medium text-white/80">
          {{ $t('auth.email') }}
        </label>
        <div class="relative">
          <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50">📧</span>
          <input 
            type="email" 
            id="email" 
            v-model="formData.email" 
            :class="[
              'w-full pl-10 pr-4 py-2.5 rounded-lg bg-gray-700/50 border transition-all duration-300',
              'focus:outline-none focus:ring-1 focus:ring-blue-500/50 focus:border-blue-500/50',
              'placeholder:text-gray-400 text-white',
              errors.email ? 'border-red-500' : 'border-gray-700/50'
            ]"
            :placeholder="$t('auth.email_placeholder')"
            @blur="validateField('email')"
            autocomplete="email"
          />
        </div>
        <p v-if="errors.email" class="mt-1 text-sm text-red-400">{{ $t(errors.email) }}</p>
      </div>
      
      <!-- 密码输入 -->
      <div class="form-group">
        <label for="password" class="block mb-1.5 text-sm font-medium text-white/80">
          {{ $t('auth.password') }}
        </label>
        <div class="relative">
          <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50">🔒</span>
          <input 
            :type="showPassword ? 'text' : 'password'" 
            id="password" 
            v-model="formData.password" 
            :class="[
              'w-full pl-10 pr-12 py-2.5 rounded-lg bg-gray-700/50 border transition-all duration-300',
              'focus:outline-none focus:ring-1 focus:ring-blue-500/50 focus:border-blue-500/50',
              'placeholder:text-gray-400 text-white',
              errors.password ? 'border-red-500' : 'border-gray-700/50'
            ]"
            :placeholder="$t('auth.password_placeholder')"
            @blur="validateField('password')"
            autocomplete="current-password"
          />
          <button 
            type="button" 
            @click="showPassword = !showPassword"
            class="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/50 hover:text-white/70"
          >
            <span>{{ showPassword ? '🙈' : '👁️' }}</span>
          </button>
        </div>
        <p v-if="errors.password" class="mt-1 text-sm text-red-400">{{ $t(errors.password) }}</p>
      </div>

      <!-- 验证码输入 -->
      <div class="form-group" v-if="captcha">
        <label for="captcha" class="block mb-1.5 text-sm font-medium text-white/80">
          {{ $t('auth.captcha') }}
        </label>
        <div class="flex items-stretch gap-2">
          <div class="relative flex-1">
            <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50">🛡️</span>
            <input 
              type="text" 
              id="captcha" 
              v-model="formData.captcha" 
              :class="[
                'w-full pl-10 pr-4 py-2.5 rounded-lg bg-gray-700/50 border transition-all duration-300',
                'focus:outline-none focus:ring-1 focus:ring-blue-500/50 focus:border-blue-500/50',
                'placeholder:text-gray-400 text-white',
                errors.captcha ? 'border-red-500' : 'border-gray-700/50'
              ]"
              :placeholder="$t('auth.captcha_placeholder')"
              @blur="validateField('captcha')"
              autocomplete="off"
              maxlength="4"
            />
          </div>
          <div class="relative">
            <img 
              v-if="captcha.image" 
              :src="captcha.image" 
              alt="验证码"
              class="h-10 w-24 rounded border border-gray-600 cursor-pointer hover:opacity-80 transition-opacity"
              @click="refreshCaptcha"
            />
            <div 
              v-if="isLoadingCaptcha"
              class="absolute inset-0 flex items-center justify-center bg-gray-700/50 rounded"
            >
              <div class="animate-spin w-4 h-4 border-2 border-blue-400 border-t-transparent rounded-full"></div>
            </div>
          </div>
        </div>
        <p v-if="errors.captcha" class="mt-1 text-sm text-red-400">{{ $t(errors.captcha) }}</p>
      </div>
      
      <!-- 记住我和忘记密码 -->
      <div class="flex justify-between items-center">
        <div class="flex items-center">
          <div class="relative inline-flex items-center cursor-pointer" @click="formData.rememberMe = !formData.rememberMe">
            <input 
              type="checkbox" 
              id="remember" 
              v-model="formData.rememberMe" 
              class="sr-only"
            />
            <div class="toggle-bg transition-colors duration-300 border-2 h-6 w-11 rounded-full" 
                 :class="formData.rememberMe ? 'bg-blue-500/20 border-blue-500/50' : 'bg-gray-700 border-gray-600'">
            </div>
            <div class="dot absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition-transform duration-300"
                 :class="{ 'transform translate-x-5': formData.rememberMe }">
            </div>
          </div>
          <label for="remember" class="ml-3 text-sm text-white/70 cursor-pointer">
            {{ $t('auth.remember_me') }}
          </label>
        </div>
        <NuxtLink 
          to="/auth/forgot-password" 
          class="text-sm text-blue-400 hover:text-blue-300 transition-colors"
        >
          {{ $t('auth.forgot_password') }}
        </NuxtLink>
      </div>
      
      <!-- 提交按钮 -->
      <button 
        type="submit" 
        class="w-full bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-medium py-2.5 px-4 rounded-lg transition-all duration-300 mt-2 flex items-center justify-center transform hover:-translate-y-0.5 shadow-md disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
        :disabled="isLoading"
      >
        <div v-if="isLoading" class="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
        {{ isLoading ? $t('auth.logging_in') : $t('auth.login.title') }}
      </button>
      
      <!-- 错误信息 -->
      <div v-if="formError" class="p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
        <p class="text-sm text-red-400 text-center">{{ formError }}</p>
      </div>
      
      <!-- 注册链接 -->
      <div class="text-center text-sm text-white/70">
        {{ $t('auth.no_account') }}
        <NuxtLink 
          to="/auth/register" 
          class="text-blue-400 hover:text-blue-300 transition-colors font-medium ml-1"
        >
          {{ $t('auth.register_now') }}
        </NuxtLink>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useAuth } from '~/composables/useAuth'
import { validateEmail, validatePassword, validateCaptcha } from '~/utils/validation'

const { t } = useI18n()
const router = useRouter()

// 使用认证组合函数
const {
  captcha,
  isLoadingCaptcha,
  isLoading,
  authError,
  getCaptcha,
  refreshCaptcha,
  login
} = useAuth()

// 表单数据
const formData = reactive({
  email: '',
  password: '',
  captcha: '',
  rememberMe: false
})

// 错误状态
const errors = reactive({
  email: '',
  password: '',
  captcha: ''
})

// 组件状态
const showPassword = ref(false)
const formError = ref('')

// 验证字段
const validateField = (field: keyof typeof formData) => {
  switch (field) {
    case 'email':
      const emailResult = validateEmail(formData.email)
      errors.email = emailResult.valid ? '' : emailResult.message || ''
      break
    case 'password':
      const passwordResult = validatePassword(formData.password)
      errors.password = passwordResult.valid ? '' : passwordResult.message || ''
      break
    case 'captcha':
      if (captcha.value) {
        const captchaResult = validateCaptcha(formData.captcha)
        errors.captcha = captchaResult.valid ? '' : captchaResult.message || ''
      }
      break
  }
}

// 验证整个表单
const validateForm = (): boolean => {
  validateField('email')
  validateField('password')
  if (captcha.value) {
    validateField('captcha')
  }
  
  return !errors.email && !errors.password && (!captcha.value || !errors.captcha)
}

// 处理表单提交
const handleSubmit = async () => {
  formError.value = ''
  
  if (!validateForm()) {
    return
  }
  
  try {
    const credentials = {
      email: formData.email,
      password: formData.password,
      captcha: formData.captcha,
      uuid: captcha.value?.uuid
    }
    
    const result = await login(credentials)
    
    if (result.success) {
      // 登录成功，跳转到个人中心
      await router.push('/profile')
    } else {
      formError.value = result.error || t('auth.login_failed')
    }
  } catch (error: any) {
    formError.value = error.message || t('auth.login_error')
  }
}

// 组件挂载时初始化
onMounted(async () => {
  await getCaptcha()
})

// 暴露给父组件的方法
defineExpose({
  refreshCaptcha
})
</script>

<style scoped>
.auth-form {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style> 