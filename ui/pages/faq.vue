<template>
  <div class="min-h-screen relative">
    <!-- 背景层 -->
    <div class="absolute inset-0 overflow-hidden">
      <div class="absolute inset-0 bg-gradient-to-b from-background-darker via-background to-background-dark z-0" />
      <div class="particle-container absolute inset-0 z-0" />
      <div class="absolute top-0 left-0 w-full h-full overflow-hidden opacity-15 z-0">
        <div class="light-beam light-beam-1" />
        <div class="light-beam light-beam-3" />
        <div class="light-beam light-beam-2" />
      </div>
      <div class="absolute inset-0 bg-grid-pattern opacity-[0.04] z-0" />
    </div>

    <!-- PC版内容 -->
    <div class="container mx-auto px-4 py-6 relative z-10" v-if="!isMobile">
      <div class="max-w-6xl mx-auto">
        <!-- 页面头部 -->
        <div class="page-header mb-12">
          <h1 class="text-4xl font-bold text-white mb-4">{{ $t('faq.title') }}</h1>
          <p class="text-gray-300 text-lg">{{ $t('faq.description') }}</p>
        </div>

        <!-- 搜索栏 -->
        <div class="search-section mb-8">
          <div class="relative max-w-2xl mx-auto">
            <input
              v-model="searchQuery"
              type="text"
              :placeholder="$t('faq.search_placeholder')"
              class="w-full px-6 py-4 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-transparent"
            />
            <div class="absolute right-4 top-1/2 transform -translate-y-1/2">
              <i class="i-heroicons-magnifying-glass w-6 h-6 text-gray-400" />
            </div>
          </div>
        </div>

        <!-- 分类标签 -->
        <div class="category-tabs mb-8">
          <div class="flex flex-wrap justify-center gap-4">
            <button
              v-for="category in categories"
              :key="category.id"
              @click="selectedCategory = category.id"
              :class="[
                'px-6 py-3 rounded-xl font-medium transition-all duration-300',
                selectedCategory === category.id
                  ? 'bg-primary text-white shadow-lg shadow-primary/25'
                  : 'bg-white/10 text-gray-300 hover:bg-white/20'
              ]"
            >
              {{ $t(`faq.categories.${category.id}`) }}
            </button>
          </div>
        </div>

        <!-- FAQ列表 -->
        <div class="faq-list space-y-4">
          <div
            v-for="(item, index) in filteredFaqs"
            :key="index"
            class="faq-item bg-white/5 backdrop-blur-md border border-white/10 rounded-2xl overflow-hidden"
          >
            <button
              @click="toggleFaq(index)"
              class="w-full px-6 py-5 text-left flex items-center justify-between hover:bg-white/5 transition-colors duration-200"
            >
              <h3 class="text-white font-medium text-lg">{{ $t(`faq.items.${item.id}.question`) || `Question ${index + 1}` }}</h3>
              <i
                :class="[
                  'w-6 h-6 transition-transform duration-300',
                  expandedItems.includes(index) ? 'i-heroicons-chevron-up rotate-180' : 'i-heroicons-chevron-down'
                ]"
              />
            </button>
            <div
              v-show="expandedItems.includes(index)"
              class="px-6 pb-5 border-t border-white/10"
            >
              <div class="pt-4 text-gray-300 leading-relaxed">
                {{ $t(`faq.items.${item.id}.answer`) || `Answer for question ${index + 1}` }}
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="!filteredFaqs || filteredFaqs.length === 0" class="text-center py-12">
          <i class="i-heroicons-magnifying-glass w-16 h-16 text-gray-500 mx-auto mb-4" />
          <p class="text-gray-400 text-lg">{{ $t('faq.no_results') || '没有找到相关问题' }}</p>
        </div>
      </div>
    </div>

    <!-- 移动版内容 -->
    <div class="container mx-auto px-4 py-4 relative z-10" v-else>
      <div class="max-w-4xl mx-auto">
        <!-- 页面头部 -->
        <div class="page-header mb-8">
          <h1 class="text-2xl font-bold text-white mb-3">{{ $t('faq.title') }}</h1>
          <p class="text-gray-300 text-sm">{{ $t('faq.description') }}</p>
        </div>

        <!-- 搜索栏 -->
        <div class="search-section mb-6">
          <div class="relative">
            <input
              v-model="searchQuery"
              type="text"
              :placeholder="$t('faq.search_placeholder')"
              class="w-full px-4 py-3 bg-white/10 backdrop-blur-md border border-white/20 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-transparent text-sm"
            />
            <div class="absolute right-3 top-1/2 transform -translate-y-1/2">
              <i class="i-heroicons-magnifying-glass w-5 h-5 text-gray-400" />
            </div>
          </div>
        </div>

        <!-- 分类标签 -->
        <div class="category-tabs mb-6">
          <div class="flex gap-2 overflow-x-auto pb-2">
            <button
              v-for="category in categories"
              :key="category.id"
              @click="selectedCategory = category.id"
              :class="[
                'px-4 py-2 rounded-lg font-medium text-sm whitespace-nowrap transition-all duration-300',
                selectedCategory === category.id
                  ? 'bg-primary text-white shadow-lg shadow-primary/25'
                  : 'bg-white/10 text-gray-300 hover:bg-white/20'
              ]"
            >
              {{ $t(`faq.categories.${category.id}`) }}
            </button>
          </div>
        </div>

        <!-- FAQ列表 -->
        <div class="faq-list space-y-3">
          <div
            v-for="(item, index) in filteredFaqs"
            :key="index"
            class="faq-item bg-white/5 backdrop-blur-md border border-white/10 rounded-xl overflow-hidden"
          >
            <button
              @click="toggleFaq(index)"
              class="w-full px-4 py-4 text-left flex items-center justify-between hover:bg-white/5 transition-colors duration-200"
            >
              <h3 class="text-white font-medium text-base">{{ $t(`faq.items.${item.id}.question`) || `Question ${index + 1}` }}</h3>
              <i
                :class="[
                  'w-5 h-5 transition-transform duration-300',
                  expandedItems.includes(index) ? 'i-heroicons-chevron-up rotate-180' : 'i-heroicons-chevron-down'
                ]"
              />
            </button>
            <div
              v-show="expandedItems.includes(index)"
              class="px-4 pb-4 border-t border-white/10"
            >
              <div class="pt-3 text-gray-300 leading-relaxed text-sm">
                {{ $t(`faq.items.${item.id}.answer`) || `Answer for question ${index + 1}` }}
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="!filteredFaqs || filteredFaqs.length === 0" class="text-center py-8">
          <i class="i-heroicons-magnifying-glass w-12 h-12 text-gray-500 mx-auto mb-3" />
          <p class="text-gray-400 text-base">{{ $t('faq.no_results') || '没有找到相关问题' }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 国际化
const { t } = useI18n()

// 设备检测 - 使用响应式状态
const isMobile = ref(false)

// 在客户端检测设备类型
onMounted(() => {
  const checkMobile = () => {
    isMobile.value = window.innerWidth < 768
  }
  
  checkMobile()
  window.addEventListener('resize', checkMobile)
  
  onUnmounted(() => {
    window.removeEventListener('resize', checkMobile)
  })
})

// SEO配置
useSeoMeta({
  title: 'FAQ - 常见问题',
  description: 'CSGO开箱网站常见问题解答',
  keywords: 'FAQ,常见问题,CSGO开箱',
  ogTitle: 'FAQ - 常见问题',
  ogDescription: 'CSGO开箱网站常见问题解答',
  twitterTitle: 'FAQ - 常见问题',
  twitterDescription: 'CSGO开箱网站常见问题解答'
})

// 响应式数据
const searchQuery = ref('')
const selectedCategory = ref('general')
const expandedItems = ref<number[]>([])

// FAQ分类
const categories = [
  { id: 'general', name: 'faq.categories.general' },
  { id: 'account', name: 'faq.categories.account' },
  { id: 'payment', name: 'faq.categories.payment' },
  { id: 'gameplay', name: 'faq.categories.gameplay' },
  { id: 'technical', name: 'faq.categories.technical' }
]

// FAQ数据
const faqData = [
  { id: 'what_is_csgoskins', category: 'general' },
  { id: 'how_to_register', category: 'account' },
  { id: 'how_to_recharge', category: 'payment' },
  { id: 'how_to_open_case', category: 'gameplay' },
  { id: 'what_is_battle_mode', category: 'gameplay' },
  { id: 'how_to_withdraw', category: 'payment' },
  { id: 'account_security', category: 'account' },
  { id: 'payment_methods', category: 'payment' },
  { id: 'skin_rarity', category: 'gameplay' },
  { id: 'technical_support', category: 'technical' }
]

// 计算属性
const filteredFaqs = computed(() => {
  try {
    let filtered = faqData || []

    // 按分类筛选
    if (selectedCategory.value !== 'all') {
      filtered = filtered.filter(item => item.category === selectedCategory.value)
    }

    // 按搜索词筛选
    if (searchQuery.value.trim()) {
      const query = searchQuery.value.toLowerCase()
      filtered = filtered.filter(item => {
        try {
          const question = t(`faq.items.${item.id}.question`) || ''
          const answer = t(`faq.items.${item.id}.answer`) || ''
          return question.toLowerCase().includes(query) || answer.toLowerCase().includes(query)
        } catch (error) {
          console.warn(`FAQ国际化键不存在: faq.items.${item.id}`)
          return false
        }
      })
    }

    return filtered || []
  } catch (error) {
    console.error('FAQ过滤出错:', error)
    return []
  }
})

// 方法
const toggleFaq = (index: number) => {
  const itemIndex = expandedItems.value.indexOf(index)
  if (itemIndex > -1) {
    expandedItems.value.splice(itemIndex, 1)
  } else {
    expandedItems.value.push(index)
  }
}
</script>

<style scoped>
/* 粒子效果 */
.particle-container {
  background-image: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
}

/* 动态光线效果 */
.light-beam {
  position: absolute;
  width: 2px;
  height: 100%;
  background: linear-gradient(to bottom, transparent, var(--color-primary), transparent);
  animation: lightBeam 8s infinite linear;
}

.light-beam-1 {
  left: 10%;
  animation-delay: 0s;
}

.light-beam-2 {
  left: 50%;
  animation-delay: 2.5s;
}

.light-beam-3 {
  left: 90%;
  animation-delay: 5s;
}

@keyframes lightBeam {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(100%);
    opacity: 0;
  }
}

/* 网格背景 */
.bg-grid-pattern {
  background-image: linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
}

/* 页面头部样式 */
.page-header {
  text-align: center;
  position: relative;
}

.page-header::after {
  content: '';
  position: absolute;
  bottom: -1rem;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
  border-radius: 2px;
}

/* FAQ项目动画 */
.faq-item {
  transition: all 0.3s ease;
}

.faq-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* 搜索栏焦点效果 */
.search-section input:focus {
  box-shadow: 0 0 0 3px rgba(var(--color-primary-rgb), 0.1);
}

/* 分类标签悬停效果 */
.category-tabs button:hover {
  transform: translateY(-1px);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .page-header h1 {
    font-size: 1.75rem;
  }
  
  .faq-item {
    margin-bottom: 0.75rem;
  }
}
</style> 