<!-- layouts/mobile.vue -->
<template>
  <div class="layout-mobile min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white">
    <!-- 移动端顶部导航 -->
    <MobileHeader />
    
    <!-- 主要内容区域 -->
    <main class="main-content">
      <div class="container mx-auto px-4 py-4">
        <slot />
      </div>
    </main>
    
    <!-- 底部导航 -->
    <MobileBottomNav />
  </div>
</template>

<script setup lang="ts">
// Nuxt 3 组合式 API
const socketStore = useSocketStore()
const appStore = useAppStore()

// 初始化移动端特定逻辑
onMounted(() => {
  // 确保移动端状态正确
  appStore.init()
  
  // 移动端特定的初始化逻辑
  console.log('[MobileLayout] 移动端布局初始化完成')
})
</script>

<style lang="scss" scoped>
.layout-mobile {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.main-content {
  flex: 1;
  padding-top: 80px; // 为移动端header预留空间
  padding-bottom: 80px; // 为底部导航留出空间
  min-height: calc(100vh - 160px); // 总高度减去header和bottom nav
}



.container {
  max-width: 100%;
  
  @media (min-width: 640px) {
    max-width: 640px;
  }
}
</style>
