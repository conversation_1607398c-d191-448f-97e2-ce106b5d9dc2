# -*- coding: utf-8 -*-
# Generated by Django 1.11.13 on 2021-08-12 10:42
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import steambase.models


class Migration(migrations.Migration):

    dependencies = [
        ('package', '0004_iteminfo_custom_rarity'),
        migrations.swappable_dependency(settings.AUTH_USER_MODELL),
        ('luckybox', '0002_auto_20210809_1621'),
    ]

    operations = [
        migrations.CreateModel(
            name='LuckyBoxRecord',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('percent', models.FloatField(default=0, verbose_name='lucky percent')),
                ('coins', models.FloatField(default=0, verbose_name='lucky coins')),
                ('percentage', models.FloatField(default=0, verbose_name='lucky percentage')),
                ('win', models.BooleanField(default=False, verbose_name='lucky result')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lucky_record', to='package.ItemInfo', verbose_name='luckybox item')),
                ('target', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='lucky_target', to='package.ItemInfo', verbose_name='luckybox target item')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lucky_user', to=settings.AUTH_USER_MODELL)),
            ],
            options={
                'verbose_name': 'LuckyBox Record',
                'verbose_name_plural': 'LuckyBox Record',
            },
        ),
    ]
