[2025-07-18 14:44:37,899: INFO/MainProcess] Task thworker.update_case_records_cache[2ba29d5e-fd3b-4969-a849-cdb76d4da993] received
[2025-07-18 14:44:37,901: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 14:44:37,911: INFO/MainProcess] Task thworker.check_b2c_trade_state[3df06f40-7269-4899-b88e-3e5eb52f33bd] received
[2025-07-18 14:44:37,926: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 14:44:37,926: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 14:44:37,942: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[3df06f40-7269-4899-b88e-3e5eb52f33bd] succeeded in 0.016960342996753752s: 'Success: B2C trade state checked'
[2025-07-18 14:44:37,949: INFO/MainProcess] Task thworker.update_case_records_cache[ba2bbecf-6008-4f29-a3ed-9cfe22945032] received
[2025-07-18 14:44:37,950: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 14:44:37,950: INFO/ForkPoolWorker-1] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 14:44:37,964: INFO/ForkPoolWorker-1] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 14:44:37,964: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 14:44:37,970: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[ba2bbecf-6008-4f29-a3ed-9cfe22945032] succeeded in 0.020668020995799452s: 'Success: Updated 100 case records cache'
[2025-07-18 14:44:47,344: ERROR/ForkPoolWorker-2] 更新 CaseRecord 缓存时发生错误: (1054, "Unknown column 'box_case.name_zh' in 'field list'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 173, in __get__
    rel_obj = self.field.get_cached_value(instance)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/mixins.py", line 15, in get_cached_value
    return instance._state.fields_cache[cache_name]
KeyError: 'case'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'box_case.name_zh' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/app/box/tasks.py", line 58, in update_case_records_cache
    batch_data = CaseRecordSerializer(batch_records, many=True).data
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 795, in data
    ret = super().data
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 249, in data
    self._data = self.to_representation(self.instance)
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 713, in to_representation
    return [
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 714, in <listcomp>
    self.child.to_representation(item) for item in iterable
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 538, in to_representation
    ret[field.field_name] = field.to_representation(attribute)
  File "/usr/local/lib/python3.8/site-packages/rest_framework/fields.py", line 1872, in to_representation
    return method(value)
  File "/app/box/serializers.py", line 117, in get_case_info
    return CaseSerializer(obj.case, fields=('id', 'case_key', 'name', 'name_en', 'name_zh_hans', 'cover', 'price', 'open_count')).data
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 187, in __get__
    rel_obj = self.get_object(instance)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 154, in get_object
    return qs.get(self.field.get_reverse_related_filter(instance))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 431, in get
    num = len(clone)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 262, in __len__
    self._fetch_all()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 1324, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 51, in __iter__
    results = compiler.execute_sql(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/compiler.py", line 1175, in execute_sql
    cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.OperationalError: (1054, "Unknown column 'box_case.name_zh' in 'field list'")
[2025-07-18 14:44:47,344: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 14:44:47,346: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[2ba29d5e-fd3b-4969-a849-cdb76d4da993] succeeded in 9.445320586004527s: 'Success: Updated 100 case records cache'
[2025-07-18 14:49:37,865: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[c57afc6a-b8cf-4012-b127-55883de8c032] received
[2025-07-18 14:49:37,866: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 14:49:37,870: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 14:49:37,872: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[c57afc6a-b8cf-4012-b127-55883de8c032] succeeded in 0.006330792006338015s: None
[2025-07-18 14:49:37,938: INFO/MainProcess] Task thworker.check_b2c_trade_state[10ff7151-2ecf-437d-a4b4-08ab532a741f] received
[2025-07-18 14:49:37,939: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 14:49:37,940: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 14:49:37,941: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 14:49:37,942: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[10ff7151-2ecf-437d-a4b4-08ab532a741f] succeeded in 0.0030760689987801015s: 'Success: B2C trade state checked'
[2025-07-18 14:49:38,085: INFO/MainProcess] Task thworker.update_case_records_cache[e6aa5fc5-fa60-495e-a5a5-a3cb2e8f52db] received
[2025-07-18 14:49:38,087: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 14:49:38,095: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 14:49:38,095: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 14:49:38,098: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[e6aa5fc5-fa60-495e-a5a5-a3cb2e8f52db] succeeded in 0.011972075008088723s: 'Success: Updated 100 case records cache'
