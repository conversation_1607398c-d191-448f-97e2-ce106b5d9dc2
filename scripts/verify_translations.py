#!/usr/bin/env python
"""
验证Django模型翻译修复结果
"""

import os
import sys
import django
from pathlib import Path

# 设置Django环境
sys.path.append('/www/wwwroot/csgoskins.com.cn/server')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')
django.setup()

def get_all_models():
    """获取所有Django模型"""
    from django.apps import apps
    
    models = []
    for app_config in apps.get_app_configs():
        # 跳过第三方应用
        if 'django.' in app_config.name or 'rest_framework' in app_config.name:
            continue
            
        for model in app_config.get_models():
            models.append(model)
    
    return models

def verify_model_translations():
    """验证模型翻译"""
    print("🧪 验证模型翻译结果...")
    
    models = get_all_models()
    
    chinese_models = []
    english_models = []
    
    for model in models:
        verbose_name = model._meta.verbose_name
        verbose_name_plural = model._meta.verbose_name_plural
        
        # 检查是否包含中文字符
        has_chinese = any('\u4e00' <= char <= '\u9fff' for char in verbose_name)
        
        if has_chinese:
            chinese_models.append({
                'app': model._meta.app_label,
                'model': model.__name__,
                'verbose_name': verbose_name,
                'verbose_name_plural': verbose_name_plural
            })
        else:
            english_models.append({
                'app': model._meta.app_label,
                'model': model.__name__,
                'verbose_name': verbose_name,
                'verbose_name_plural': verbose_name_plural
            })
    
    print(f"✅ 已中文化的模型: {len(chinese_models)} 个")
    print(f"⚪ 仍为英文的模型: {len(english_models)} 个")
    
    # 显示已中文化的模型
    if chinese_models:
        print("\n📋 已中文化的模型:")
        for model in chinese_models:
            print(f"   {model['app']}.{model['model']}: {model['verbose_name']}")
    
    # 显示仍为英文的模型
    if english_models:
        print("\n📋 仍为英文的模型:")
        for model in english_models:
            print(f"   {model['app']}.{model['model']}: {model['verbose_name']}")
    
    return chinese_models, english_models

def verify_field_translations():
    """验证字段翻译"""
    print("\n🔧 验证字段翻译...")
    
    models = get_all_models()
    
    chinese_fields = []
    english_fields = []
    
    for model in models:
        # 跳过第三方应用
        if 'django.' in model._meta.app_label or 'rest_framework' in model._meta.app_label:
            continue
            
        for field in model._meta.get_fields():
            if hasattr(field, 'verbose_name') and field.verbose_name:
                # 检查是否包含中文字符
                has_chinese = any('\u4e00' <= char <= '\u9fff' for char in field.verbose_name)
                
                field_info = {
                    'app': model._meta.app_label,
                    'model': model.__name__,
                    'field': field.name,
                    'verbose_name': field.verbose_name
                }
                
                if has_chinese:
                    chinese_fields.append(field_info)
                else:
                    # 只显示常用的需要翻译的字段
                    common_fields = ['user', 'amount', 'name', 'title', 'content', 'description', 
                                   'status', 'state', 'type', 'category', 'tag', 'image', 'count', 'level']
                    if field.name in common_fields or field.verbose_name in common_fields:
                        english_fields.append(field_info)
    
    print(f"✅ 已中文化的字段: {len(chinese_fields)} 个")
    print(f"⚪ 仍为英文的常用字段: {len(english_fields)} 个")
    
    # 显示部分已中文化的字段
    if chinese_fields:
        print("\n📋 已中文化的字段 (前20个):")
        for field in chinese_fields[:20]:
            print(f"   {field['app']}.{field['model']}.{field['field']}: {field['verbose_name']}")
        if len(chinese_fields) > 20:
            print(f"   ... 还有 {len(chinese_fields) - 20} 个字段")
    
    # 显示仍为英文的常用字段
    if english_fields:
        print("\n📋 仍为英文的常用字段:")
        for field in english_fields:
            print(f"   {field['app']}.{field['model']}.{field['field']}: {field['verbose_name']}")
    
    return chinese_fields, english_fields

def main():
    print("🔍 验证翻译修复结果...")
    
    # 验证模型翻译
    chinese_models, english_models = verify_model_translations()
    
    # 验证字段翻译
    chinese_fields, english_fields = verify_field_translations()
    
    # 统计结果
    print(f"\n📊 翻译修复统计:")
    print(f"   模型翻译: {len(chinese_models)}/{len(chinese_models) + len(english_models)} ({len(chinese_models)/(len(chinese_models) + len(english_models))*100:.1f}%)")
    print(f"   字段翻译: {len(chinese_fields)} 个字段已中文化")
    
    if len(english_models) == 0:
        print("\n🎉 所有模型都已成功中文化！")
    else:
        print(f"\n⚠️  还有 {len(english_models)} 个模型需要中文化")
    
    return {
        'chinese_models': len(chinese_models),
        'english_models': len(english_models),
        'chinese_fields': len(chinese_fields),
        'english_fields': len(english_fields)
    }

if __name__ == "__main__":
    main()
