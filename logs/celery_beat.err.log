ERROR:celery.utils.dispatch.signal:Signal handler <bound method DjangoFixup.on_import_modules of <celery.fixups.django.DjangoFixup object at 0x7f9dda04fa00>> raised: ModuleNotFoundError("No module named 'django_ckeditor_5'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 91, in populate
    app_config = AppConfig.create(entry)
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 224, in create
    import_module(entry)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 973, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'django_ckeditor_5'
ERROR:celery.utils.dispatch.signal:Signal handler <promise@0x7f9dda06c900 --> <bound method Celery._autodiscover_tasks of <Celery steambase at 0x7f9dda30f2e0>>> raised: AppRegistryNotReady("Apps aren't loaded yet.")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 161, in __call__
    return self.throw()
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 158, in __call__
    retval = fun(*final_args, **final_kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 788, in _autodiscover_tasks
    return self._autodiscover_tasks_from_fixups(related_name)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 797, in _autodiscover_tasks_from_fixups
    return self._autodiscover_tasks_from_names([
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 800, in <listcomp>
    for pkg in fixup.autodiscover_tasks()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 111, in autodiscover_tasks
    return [config.name for config in apps.get_app_configs()]
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 145, in get_app_configs
    self.check_apps_ready()
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 136, in check_apps_ready
    raise AppRegistryNotReady("Apps aren't loaded yet.")
django.core.exceptions.AppRegistryNotReady: Apps aren't loaded yet.
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/runpy.py", line 194, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/local/lib/python3.8/runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 19, in <module>
    main()
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 15, in main
    sys.exit(_main())
  File "/usr/local/lib/python3.8/site-packages/celery/bin/celery.py", line 231, in main
    return celery(auto_envvar_prefix="CELERY")
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1082, in main
    rv = self.invoke(ctx)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 788, in invoke
    return __callback(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/decorators.py", line 33, in new_func
    return f(get_current_context(), *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/base.py", line 135, in caller
    return f(ctx, *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/beat.py", line 72, in beat
    return beat().run()
  File "/usr/local/lib/python3.8/site-packages/celery/apps/beat.py", line 82, in run
    self.init_loader()
  File "/usr/local/lib/python3.8/site-packages/celery/apps/beat.py", line 132, in init_loader
    self.app.loader.init_worker()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 110, in init_worker
    self.import_default_modules()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 104, in import_default_modules
    raise response
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 91, in populate
    app_config = AppConfig.create(entry)
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 224, in create
    import_module(entry)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 973, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'django_ckeditor_5'
ERROR:celery.utils.dispatch.signal:Signal handler <bound method DjangoFixup.on_import_modules of <celery.fixups.django.DjangoFixup object at 0x7f5d924289d0>> raised: ModuleNotFoundError("No module named 'django_ckeditor_5'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 91, in populate
    app_config = AppConfig.create(entry)
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 224, in create
    import_module(entry)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 973, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'django_ckeditor_5'
ERROR:celery.utils.dispatch.signal:Signal handler <promise@0x7f5d92444900 --> <bound method Celery._autodiscover_tasks of <Celery steambase at 0x7f5d925a9340>>> raised: AppRegistryNotReady("Apps aren't loaded yet.")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 161, in __call__
    return self.throw()
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 158, in __call__
    retval = fun(*final_args, **final_kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 788, in _autodiscover_tasks
    return self._autodiscover_tasks_from_fixups(related_name)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 797, in _autodiscover_tasks_from_fixups
    return self._autodiscover_tasks_from_names([
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 800, in <listcomp>
    for pkg in fixup.autodiscover_tasks()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 111, in autodiscover_tasks
    return [config.name for config in apps.get_app_configs()]
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 145, in get_app_configs
    self.check_apps_ready()
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 136, in check_apps_ready
    raise AppRegistryNotReady("Apps aren't loaded yet.")
django.core.exceptions.AppRegistryNotReady: Apps aren't loaded yet.
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/runpy.py", line 194, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/local/lib/python3.8/runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 19, in <module>
    main()
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 15, in main
    sys.exit(_main())
  File "/usr/local/lib/python3.8/site-packages/celery/bin/celery.py", line 231, in main
    return celery(auto_envvar_prefix="CELERY")
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1082, in main
    rv = self.invoke(ctx)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 788, in invoke
    return __callback(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/decorators.py", line 33, in new_func
    return f(get_current_context(), *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/base.py", line 135, in caller
    return f(ctx, *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/beat.py", line 72, in beat
    return beat().run()
  File "/usr/local/lib/python3.8/site-packages/celery/apps/beat.py", line 82, in run
    self.init_loader()
  File "/usr/local/lib/python3.8/site-packages/celery/apps/beat.py", line 132, in init_loader
    self.app.loader.init_worker()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 110, in init_worker
    self.import_default_modules()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 104, in import_default_modules
    raise response
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 91, in populate
    app_config = AppConfig.create(entry)
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 224, in create
    import_module(entry)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 973, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'django_ckeditor_5'
ERROR:celery.utils.dispatch.signal:Signal handler <bound method DjangoFixup.on_import_modules of <celery.fixups.django.DjangoFixup object at 0x7f25e27129d0>> raised: ModuleNotFoundError("No module named 'django_ckeditor_5'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 91, in populate
    app_config = AppConfig.create(entry)
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 224, in create
    import_module(entry)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 973, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'django_ckeditor_5'
ERROR:celery.utils.dispatch.signal:Signal handler <promise@0x7f25e272f900 --> <bound method Celery._autodiscover_tasks of <Celery steambase at 0x7f25e32ad340>>> raised: AppRegistryNotReady("Apps aren't loaded yet.")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 161, in __call__
    return self.throw()
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 158, in __call__
    retval = fun(*final_args, **final_kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 788, in _autodiscover_tasks
    return self._autodiscover_tasks_from_fixups(related_name)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 797, in _autodiscover_tasks_from_fixups
    return self._autodiscover_tasks_from_names([
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 800, in <listcomp>
    for pkg in fixup.autodiscover_tasks()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 111, in autodiscover_tasks
    return [config.name for config in apps.get_app_configs()]
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 145, in get_app_configs
    self.check_apps_ready()
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 136, in check_apps_ready
    raise AppRegistryNotReady("Apps aren't loaded yet.")
django.core.exceptions.AppRegistryNotReady: Apps aren't loaded yet.
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/runpy.py", line 194, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/local/lib/python3.8/runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 19, in <module>
    main()
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 15, in main
    sys.exit(_main())
  File "/usr/local/lib/python3.8/site-packages/celery/bin/celery.py", line 231, in main
    return celery(auto_envvar_prefix="CELERY")
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1082, in main
    rv = self.invoke(ctx)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 788, in invoke
    return __callback(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/decorators.py", line 33, in new_func
    return f(get_current_context(), *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/base.py", line 135, in caller
    return f(ctx, *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/beat.py", line 72, in beat
    return beat().run()
  File "/usr/local/lib/python3.8/site-packages/celery/apps/beat.py", line 82, in run
    self.init_loader()
  File "/usr/local/lib/python3.8/site-packages/celery/apps/beat.py", line 132, in init_loader
    self.app.loader.init_worker()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 110, in init_worker
    self.import_default_modules()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 104, in import_default_modules
    raise response
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 91, in populate
    app_config = AppConfig.create(entry)
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 224, in create
    import_module(entry)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 973, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'django_ckeditor_5'
ERROR:celery.utils.dispatch.signal:Signal handler <bound method DjangoFixup.on_import_modules of <celery.fixups.django.DjangoFixup object at 0x7f547a30a9d0>> raised: ModuleNotFoundError("No module named 'django_ckeditor_5'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 91, in populate
    app_config = AppConfig.create(entry)
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 224, in create
    import_module(entry)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 973, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'django_ckeditor_5'
ERROR:celery.utils.dispatch.signal:Signal handler <promise@0x7f547a326900 --> <bound method Celery._autodiscover_tasks of <Celery steambase at 0x7f547ae90340>>> raised: AppRegistryNotReady("Apps aren't loaded yet.")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 161, in __call__
    return self.throw()
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 158, in __call__
    retval = fun(*final_args, **final_kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 788, in _autodiscover_tasks
    return self._autodiscover_tasks_from_fixups(related_name)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 797, in _autodiscover_tasks_from_fixups
    return self._autodiscover_tasks_from_names([
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 800, in <listcomp>
    for pkg in fixup.autodiscover_tasks()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 111, in autodiscover_tasks
    return [config.name for config in apps.get_app_configs()]
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 145, in get_app_configs
    self.check_apps_ready()
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 136, in check_apps_ready
    raise AppRegistryNotReady("Apps aren't loaded yet.")
django.core.exceptions.AppRegistryNotReady: Apps aren't loaded yet.
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/runpy.py", line 194, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/local/lib/python3.8/runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 19, in <module>
    main()
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 15, in main
    sys.exit(_main())
  File "/usr/local/lib/python3.8/site-packages/celery/bin/celery.py", line 231, in main
    return celery(auto_envvar_prefix="CELERY")
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1082, in main
    rv = self.invoke(ctx)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 788, in invoke
    return __callback(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/decorators.py", line 33, in new_func
    return f(get_current_context(), *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/base.py", line 135, in caller
    return f(ctx, *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/beat.py", line 72, in beat
    return beat().run()
  File "/usr/local/lib/python3.8/site-packages/celery/apps/beat.py", line 82, in run
    self.init_loader()
  File "/usr/local/lib/python3.8/site-packages/celery/apps/beat.py", line 132, in init_loader
    self.app.loader.init_worker()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 110, in init_worker
    self.import_default_modules()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 104, in import_default_modules
    raise response
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 91, in populate
    app_config = AppConfig.create(entry)
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 224, in create
    import_module(entry)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 973, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'django_ckeditor_5'
DEBUG:asyncio:Using selector: EpollSelector
ERROR:celery.utils.dispatch.signal:Signal handler <bound method DjangoFixup.on_import_modules of <celery.fixups.django.DjangoFixup object at 0x7f2bfa194a00>> raised: ModuleNotFoundError("No module named 'aenum'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
ERROR:celery.utils.dispatch.signal:Signal handler <promise@0x7f2bfa1b1900 --> <bound method Celery._autodiscover_tasks of <Celery steambase at 0x7f2bfae762e0>>> raised: ModuleNotFoundError("No module named 'aenum'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 161, in __call__
    return self.throw()
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 158, in __call__
    retval = fun(*final_args, **final_kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 788, in _autodiscover_tasks
    return self._autodiscover_tasks_from_fixups(related_name)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 797, in _autodiscover_tasks_from_fixups
    return self._autodiscover_tasks_from_names([
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 792, in _autodiscover_tasks_from_names
    return self.loader.autodiscover_tasks(
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 220, in autodiscover_tasks
    mod.__name__ for mod in autodiscover_tasks(packages or (),
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 246, in autodiscover_tasks
    return [find_related_module(pkg, related_name) for pkg in packages]
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 246, in <listcomp>
    return [find_related_module(pkg, related_name) for pkg in packages]
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 278, in find_related_module
    raise e
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 270, in find_related_module
    return importlib.import_module(module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/tasks.py", line 7, in <module>
    from authentication.models import UserBalanceRecord, UserAsset, AuthUser, UserExtra, UserActivePointRecord
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/runpy.py", line 194, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/local/lib/python3.8/runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 19, in <module>
    main()
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 15, in main
    sys.exit(_main())
  File "/usr/local/lib/python3.8/site-packages/celery/bin/celery.py", line 231, in main
    return celery(auto_envvar_prefix="CELERY")
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1082, in main
    rv = self.invoke(ctx)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 788, in invoke
    return __callback(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/decorators.py", line 33, in new_func
    return f(get_current_context(), *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/base.py", line 135, in caller
    return f(ctx, *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/beat.py", line 72, in beat
    return beat().run()
  File "/usr/local/lib/python3.8/site-packages/celery/apps/beat.py", line 82, in run
    self.init_loader()
  File "/usr/local/lib/python3.8/site-packages/celery/apps/beat.py", line 132, in init_loader
    self.app.loader.init_worker()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 110, in init_worker
    self.import_default_modules()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 104, in import_default_modules
    raise response
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
DEBUG:asyncio:Using selector: EpollSelector
ERROR:celery.utils.dispatch.signal:Signal handler <bound method DjangoFixup.on_import_modules of <celery.fixups.django.DjangoFixup object at 0x7fcbbd06c9d0>> raised: ModuleNotFoundError("No module named 'aenum'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
ERROR:celery.utils.dispatch.signal:Signal handler <promise@0x7fcbbd088900 --> <bound method Celery._autodiscover_tasks of <Celery steambase at 0x7fcbbd1ed340>>> raised: ModuleNotFoundError("No module named 'aenum'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 161, in __call__
    return self.throw()
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 158, in __call__
    retval = fun(*final_args, **final_kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 788, in _autodiscover_tasks
    return self._autodiscover_tasks_from_fixups(related_name)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 797, in _autodiscover_tasks_from_fixups
    return self._autodiscover_tasks_from_names([
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 792, in _autodiscover_tasks_from_names
    return self.loader.autodiscover_tasks(
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 220, in autodiscover_tasks
    mod.__name__ for mod in autodiscover_tasks(packages or (),
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 246, in autodiscover_tasks
    return [find_related_module(pkg, related_name) for pkg in packages]
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 246, in <listcomp>
    return [find_related_module(pkg, related_name) for pkg in packages]
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 278, in find_related_module
    raise e
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 270, in find_related_module
    return importlib.import_module(module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/tasks.py", line 7, in <module>
    from authentication.models import UserBalanceRecord, UserAsset, AuthUser, UserExtra, UserActivePointRecord
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/runpy.py", line 194, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/local/lib/python3.8/runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 19, in <module>
    main()
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 15, in main
    sys.exit(_main())
  File "/usr/local/lib/python3.8/site-packages/celery/bin/celery.py", line 231, in main
    return celery(auto_envvar_prefix="CELERY")
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1082, in main
    rv = self.invoke(ctx)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 788, in invoke
    return __callback(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/decorators.py", line 33, in new_func
    return f(get_current_context(), *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/base.py", line 135, in caller
    return f(ctx, *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/beat.py", line 72, in beat
    return beat().run()
  File "/usr/local/lib/python3.8/site-packages/celery/apps/beat.py", line 82, in run
    self.init_loader()
  File "/usr/local/lib/python3.8/site-packages/celery/apps/beat.py", line 132, in init_loader
    self.app.loader.init_worker()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 110, in init_worker
    self.import_default_modules()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 104, in import_default_modules
    raise response
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
DEBUG:asyncio:Using selector: EpollSelector
ERROR:celery.utils.dispatch.signal:Signal handler <bound method DjangoFixup.on_import_modules of <celery.fixups.django.DjangoFixup object at 0x7fd65ee669d0>> raised: ModuleNotFoundError("No module named 'aenum'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
ERROR:celery.utils.dispatch.signal:Signal handler <promise@0x7fd65ee84900 --> <bound method Celery._autodiscover_tasks of <Celery steambase at 0x7fd65efe8340>>> raised: ModuleNotFoundError("No module named 'aenum'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 161, in __call__
    return self.throw()
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 158, in __call__
    retval = fun(*final_args, **final_kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 788, in _autodiscover_tasks
    return self._autodiscover_tasks_from_fixups(related_name)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 797, in _autodiscover_tasks_from_fixups
    return self._autodiscover_tasks_from_names([
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 792, in _autodiscover_tasks_from_names
    return self.loader.autodiscover_tasks(
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 220, in autodiscover_tasks
    mod.__name__ for mod in autodiscover_tasks(packages or (),
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 246, in autodiscover_tasks
    return [find_related_module(pkg, related_name) for pkg in packages]
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 246, in <listcomp>
    return [find_related_module(pkg, related_name) for pkg in packages]
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 278, in find_related_module
    raise e
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 270, in find_related_module
    return importlib.import_module(module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/tasks.py", line 7, in <module>
    from authentication.models import UserBalanceRecord, UserAsset, AuthUser, UserExtra, UserActivePointRecord
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/runpy.py", line 194, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/local/lib/python3.8/runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 19, in <module>
    main()
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 15, in main
    sys.exit(_main())
  File "/usr/local/lib/python3.8/site-packages/celery/bin/celery.py", line 231, in main
    return celery(auto_envvar_prefix="CELERY")
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1082, in main
    rv = self.invoke(ctx)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 788, in invoke
    return __callback(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/decorators.py", line 33, in new_func
    return f(get_current_context(), *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/base.py", line 135, in caller
    return f(ctx, *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/beat.py", line 72, in beat
    return beat().run()
  File "/usr/local/lib/python3.8/site-packages/celery/apps/beat.py", line 82, in run
    self.init_loader()
  File "/usr/local/lib/python3.8/site-packages/celery/apps/beat.py", line 132, in init_loader
    self.app.loader.init_worker()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 110, in init_worker
    self.import_default_modules()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 104, in import_default_modules
    raise response
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
DEBUG:asyncio:Using selector: EpollSelector
ERROR:celery.utils.dispatch.signal:Signal handler <bound method DjangoFixup.on_import_modules of <celery.fixups.django.DjangoFixup object at 0x7f7aa5fd19d0>> raised: ModuleNotFoundError("No module named 'aenum'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
ERROR:celery.utils.dispatch.signal:Signal handler <promise@0x7f7aa5fef900 --> <bound method Celery._autodiscover_tasks of <Celery steambase at 0x7f7aa6b90340>>> raised: ModuleNotFoundError("No module named 'aenum'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 161, in __call__
    return self.throw()
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 158, in __call__
    retval = fun(*final_args, **final_kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 788, in _autodiscover_tasks
    return self._autodiscover_tasks_from_fixups(related_name)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 797, in _autodiscover_tasks_from_fixups
    return self._autodiscover_tasks_from_names([
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 792, in _autodiscover_tasks_from_names
    return self.loader.autodiscover_tasks(
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 220, in autodiscover_tasks
    mod.__name__ for mod in autodiscover_tasks(packages or (),
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 246, in autodiscover_tasks
    return [find_related_module(pkg, related_name) for pkg in packages]
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 246, in <listcomp>
    return [find_related_module(pkg, related_name) for pkg in packages]
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 278, in find_related_module
    raise e
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 270, in find_related_module
    return importlib.import_module(module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/tasks.py", line 7, in <module>
    from authentication.models import UserBalanceRecord, UserAsset, AuthUser, UserExtra, UserActivePointRecord
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/runpy.py", line 194, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/local/lib/python3.8/runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 19, in <module>
    main()
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 15, in main
    sys.exit(_main())
  File "/usr/local/lib/python3.8/site-packages/celery/bin/celery.py", line 231, in main
    return celery(auto_envvar_prefix="CELERY")
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1082, in main
    rv = self.invoke(ctx)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 788, in invoke
    return __callback(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/decorators.py", line 33, in new_func
    return f(get_current_context(), *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/base.py", line 135, in caller
    return f(ctx, *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/beat.py", line 72, in beat
    return beat().run()
  File "/usr/local/lib/python3.8/site-packages/celery/apps/beat.py", line 82, in run
    self.init_loader()
  File "/usr/local/lib/python3.8/site-packages/celery/apps/beat.py", line 132, in init_loader
    self.app.loader.init_worker()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 110, in init_worker
    self.import_default_modules()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 104, in import_default_modules
    raise response
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
DEBUG:asyncio:Using selector: EpollSelector
ERROR:celery.utils.dispatch.signal:Signal handler <bound method DjangoFixup.on_import_modules of <celery.fixups.django.DjangoFixup object at 0x7f715a9519d0>> raised: ModuleNotFoundError("No module named 'aenum'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
ERROR:celery.utils.dispatch.signal:Signal handler <promise@0x7f715a96f900 --> <bound method Celery._autodiscover_tasks of <Celery steambase at 0x7f715b4ec340>>> raised: ModuleNotFoundError("No module named 'aenum'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 161, in __call__
    return self.throw()
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 158, in __call__
    retval = fun(*final_args, **final_kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 788, in _autodiscover_tasks
    return self._autodiscover_tasks_from_fixups(related_name)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 797, in _autodiscover_tasks_from_fixups
    return self._autodiscover_tasks_from_names([
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 792, in _autodiscover_tasks_from_names
    return self.loader.autodiscover_tasks(
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 220, in autodiscover_tasks
    mod.__name__ for mod in autodiscover_tasks(packages or (),
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 246, in autodiscover_tasks
    return [find_related_module(pkg, related_name) for pkg in packages]
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 246, in <listcomp>
    return [find_related_module(pkg, related_name) for pkg in packages]
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 278, in find_related_module
    raise e
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 270, in find_related_module
    return importlib.import_module(module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/tasks.py", line 7, in <module>
    from authentication.models import UserBalanceRecord, UserAsset, AuthUser, UserExtra, UserActivePointRecord
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/runpy.py", line 194, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/local/lib/python3.8/runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 19, in <module>
    main()
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 15, in main
    sys.exit(_main())
  File "/usr/local/lib/python3.8/site-packages/celery/bin/celery.py", line 231, in main
    return celery(auto_envvar_prefix="CELERY")
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1082, in main
    rv = self.invoke(ctx)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 788, in invoke
    return __callback(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/decorators.py", line 33, in new_func
    return f(get_current_context(), *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/base.py", line 135, in caller
    return f(ctx, *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/beat.py", line 72, in beat
    return beat().run()
  File "/usr/local/lib/python3.8/site-packages/celery/apps/beat.py", line 82, in run
    self.init_loader()
  File "/usr/local/lib/python3.8/site-packages/celery/apps/beat.py", line 132, in init_loader
    self.app.loader.init_worker()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 110, in init_worker
    self.import_default_modules()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 104, in import_default_modules
    raise response
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
DEBUG:asyncio:Using selector: EpollSelector
ERROR:celery.utils.dispatch.signal:Signal handler <bound method DjangoFixup.on_import_modules of <celery.fixups.django.DjangoFixup object at 0x7f0ba01d29d0>> raised: ModuleNotFoundError("No module named 'aenum'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
ERROR:celery.utils.dispatch.signal:Signal handler <promise@0x7f0ba01ef900 --> <bound method Celery._autodiscover_tasks of <Celery steambase at 0x7f0ba0d60340>>> raised: ModuleNotFoundError("No module named 'aenum'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 161, in __call__
    return self.throw()
  File "/usr/local/lib/python3.8/site-packages/vine/promises.py", line 158, in __call__
    retval = fun(*final_args, **final_kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 788, in _autodiscover_tasks
    return self._autodiscover_tasks_from_fixups(related_name)
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 797, in _autodiscover_tasks_from_fixups
    return self._autodiscover_tasks_from_names([
  File "/usr/local/lib/python3.8/site-packages/celery/app/base.py", line 792, in _autodiscover_tasks_from_names
    return self.loader.autodiscover_tasks(
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 220, in autodiscover_tasks
    mod.__name__ for mod in autodiscover_tasks(packages or (),
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 246, in autodiscover_tasks
    return [find_related_module(pkg, related_name) for pkg in packages]
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 246, in <listcomp>
    return [find_related_module(pkg, related_name) for pkg in packages]
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 278, in find_related_module
    raise e
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 270, in find_related_module
    return importlib.import_module(module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/tasks.py", line 7, in <module>
    from authentication.models import UserBalanceRecord, UserAsset, AuthUser, UserExtra, UserActivePointRecord
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/runpy.py", line 194, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/usr/local/lib/python3.8/runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 19, in <module>
    main()
  File "/usr/local/lib/python3.8/site-packages/celery/__main__.py", line 15, in main
    sys.exit(_main())
  File "/usr/local/lib/python3.8/site-packages/celery/bin/celery.py", line 231, in main
    return celery(auto_envvar_prefix="CELERY")
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1082, in main
    rv = self.invoke(ctx)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/usr/local/lib/python3.8/site-packages/click/core.py", line 788, in invoke
    return __callback(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/click/decorators.py", line 33, in new_func
    return f(get_current_context(), *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/base.py", line 135, in caller
    return f(ctx, *args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/bin/beat.py", line 72, in beat
    return beat().run()
  File "/usr/local/lib/python3.8/site-packages/celery/apps/beat.py", line 82, in run
    self.init_loader()
  File "/usr/local/lib/python3.8/site-packages/celery/apps/beat.py", line 132, in init_loader
    self.app.loader.init_worker()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 110, in init_worker
    self.import_default_modules()
  File "/usr/local/lib/python3.8/site-packages/celery/loaders/base.py", line 104, in import_default_modules
    raise response
  File "/usr/local/lib/python3.8/site-packages/celery/utils/dispatch/signal.py", line 280, in send
    response = receiver(signal=self, sender=sender, **named)
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 101, in on_import_modules
    self.worker_fixup.validate_models()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 139, in validate_models
    self.django_setup()
  File "/usr/local/lib/python3.8/site-packages/celery/fixups/django.py", line 135, in django_setup
    django.setup()
  File "/usr/local/lib/python3.8/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/usr/local/lib/python3.8/site-packages/django/apps/registry.py", line 114, in populate
    app_config.import_models()
  File "/usr/local/lib/python3.8/site-packages/django/apps/config.py", line 301, in import_models
    self.models_module = import_module(models_module_name)
  File "/usr/local/lib/python3.8/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1014, in _gcd_import
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "/app/authentication/models.py", line 15, in <module>
    from steambase.enums import PackageState
  File "/app/steambase/enums.py", line 1, in <module>
    from aenum import Enum, IntEnum
ModuleNotFoundError: No module named 'aenum'
DEBUG:asyncio:Using selector: EpollSelector
DEBUG:celery.utils.functional:
def delete_old_balance_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_today_amount():
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache(count, batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache_task(self, count=0):
    return 1

DEBUG:celery.utils.functional:
def check_room_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def check_pendclose_trade_task(self):
    return 1

DEBUG:celery.utils.functional:
def accumulate(self, *args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def weekly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def unlock_chord(self, group_id, callback, interval=0, max_retries=1, result=2, Result=3, GroupResult=4, result_from_tuple=5, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_recharge_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def zbt_buy_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def xmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def update_month_amount():
    return 1

DEBUG:celery.utils.functional:
def delete_old_case_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def check_b2c_trade_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_monitor_data_task(self):
    return 1

DEBUG:celery.utils.functional:
def xstarmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def delete_old_log_entries(batch_size=0, days=1):
    return 1

DEBUG:celery.utils.functional:
def update_new_user_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def case_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def chunks(task, it, n):
    return 1

DEBUG:celery.utils.functional:
def update_domain_verify_switch(value):
    return 1

DEBUG:celery.utils.functional:
def case_pk_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def group(self, tasks, result, group_id, partial_args, add_to_parent=0):
    return 1

DEBUG:celery.utils.functional:
def init_system_task(self):
    return 1

DEBUG:celery.utils.functional:
def sync_items_price_task(self):
    return 1

DEBUG:celery.utils.functional:
def chain(*args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_staff_balance():
    return 1

DEBUG:celery.utils.functional:
def update_daily_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def case_bot_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def chord(self, header, body, partial_args=0, interval=1, countdown=2, max_retries=3, eager=4, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_enable_based_on_coins(min_coins, max_coins):
    return 1

DEBUG:celery.utils.functional:
def case_pk_join_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_package_items(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def hourly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_user_active_point_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_enable_rule_exam(value):
    return 1

DEBUG:celery.utils.functional:
def roll_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def backend_cleanup():
    return 1

DEBUG:celery.utils.functional:
def cleanup_duplicate_roll_room_bets():
    return 1

DEBUG:celery.utils.functional:
def update_enable_pay_method(enable_list):
    return 1

DEBUG:celery.utils.functional:
def check_envelop_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def daily_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_register_box_chance_type(chance_type=0):
    return 1

[2025-07-18 10:20:38,164: INFO/MainProcess] beat: Starting...
[2025-07-18 10:20:38,687: INFO/MainProcess] DatabaseScheduler: Schedule changed.
DEBUG:asyncio:Using selector: EpollSelector
DEBUG:celery.utils.functional:
def case_pk_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def accumulate(self, *args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def sync_items_price_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_register_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def init_system_task(self):
    return 1

DEBUG:celery.utils.functional:
def unlock_chord(self, group_id, callback, interval=0, max_retries=1, result=2, Result=3, GroupResult=4, result_from_tuple=5, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def delete_old_balance_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache(count, batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_today_amount():
    return 1

DEBUG:celery.utils.functional:
def case_bot_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def xmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def case_pk_join_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def xstarmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def hourly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_recharge_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def update_enable_pay_method(enable_list):
    return 1

DEBUG:celery.utils.functional:
def chunks(task, it, n):
    return 1

DEBUG:celery.utils.functional:
def update_month_amount():
    return 1

DEBUG:celery.utils.functional:
def delete_old_case_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def roll_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_new_user_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def delete_old_log_entries(batch_size=0, days=1):
    return 1

DEBUG:celery.utils.functional:
def group(self, tasks, result, group_id, partial_args, add_to_parent=0):
    return 1

DEBUG:celery.utils.functional:
def check_envelop_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def chain(*args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def daily_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_domain_verify_switch(value):
    return 1

DEBUG:celery.utils.functional:
def chord(self, header, body, partial_args=0, interval=1, countdown=2, max_retries=3, eager=4, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def check_room_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_staff_balance():
    return 1

DEBUG:celery.utils.functional:
def update_daily_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache_task(self, count=0):
    return 1

DEBUG:celery.utils.functional:
def check_pendclose_trade_task(self):
    return 1

DEBUG:celery.utils.functional:
def weekly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_enable_based_on_coins(min_coins, max_coins):
    return 1

DEBUG:celery.utils.functional:
def delete_old_package_items(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def backend_cleanup():
    return 1

DEBUG:celery.utils.functional:
def zbt_buy_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_user_active_point_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_enable_rule_exam(value):
    return 1

DEBUG:celery.utils.functional:
def check_b2c_trade_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_monitor_data_task(self):
    return 1

DEBUG:celery.utils.functional:
def case_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def cleanup_duplicate_roll_room_bets():
    return 1

[2025-07-18 10:24:31,373: INFO/MainProcess] beat: Starting...
[2025-07-18 10:24:32,075: INFO/MainProcess] DatabaseScheduler: Schedule changed.
[2025-07-18 10:29:31,886: INFO/MainProcess] Scheduler: Sending due task cleanup-duplicate-roll-room-bets (roll.tasks.cleanup_duplicate_roll_room_bets)
[2025-07-18 10:29:32,061: INFO/MainProcess] Scheduler: Sending due task update-case-records-cache (thworker.update_case_records_cache)
[2025-07-18 10:29:32,150: INFO/MainProcess] Scheduler: Sending due task check-b2c-trade-state (thworker.check_b2c_trade_state)
DEBUG:asyncio:Using selector: EpollSelector
DEBUG:celery.utils.functional:
def sync_items_price_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_register_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def init_system_task(self):
    return 1

DEBUG:celery.utils.functional:
def accumulate(self, *args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def delete_old_balance_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache(count, batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_today_amount():
    return 1

DEBUG:celery.utils.functional:
def case_bot_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def unlock_chord(self, group_id, callback, interval=0, max_retries=1, result=2, Result=3, GroupResult=4, result_from_tuple=5, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def case_pk_join_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def xmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def hourly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_recharge_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def xstarmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def update_month_amount():
    return 1

DEBUG:celery.utils.functional:
def delete_old_case_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def roll_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def chunks(task, it, n):
    return 1

DEBUG:celery.utils.functional:
def delete_old_log_entries(batch_size=0, days=1):
    return 1

DEBUG:celery.utils.functional:
def update_new_user_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def check_envelop_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def group(self, tasks, result, group_id, partial_args, add_to_parent=0):
    return 1

DEBUG:celery.utils.functional:
def daily_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_domain_verify_switch(value):
    return 1

DEBUG:celery.utils.functional:
def chain(*args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def check_room_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def chord(self, header, body, partial_args=0, interval=1, countdown=2, max_retries=3, eager=4, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_staff_balance():
    return 1

DEBUG:celery.utils.functional:
def update_daily_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache_task(self, count=0):
    return 1

DEBUG:celery.utils.functional:
def check_pendclose_trade_task(self):
    return 1

DEBUG:celery.utils.functional:
def weekly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_enable_based_on_coins(min_coins, max_coins):
    return 1

DEBUG:celery.utils.functional:
def delete_old_package_items(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def zbt_buy_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_user_active_point_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_enable_rule_exam(value):
    return 1

DEBUG:celery.utils.functional:
def backend_cleanup():
    return 1

DEBUG:celery.utils.functional:
def check_b2c_trade_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_monitor_data_task(self):
    return 1

DEBUG:celery.utils.functional:
def case_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def cleanup_duplicate_roll_room_bets():
    return 1

DEBUG:celery.utils.functional:
def update_enable_pay_method(enable_list):
    return 1

DEBUG:celery.utils.functional:
def case_pk_worker_task(self):
    return 1

[2025-07-18 10:30:30,095: INFO/MainProcess] beat: Starting...
[2025-07-18 10:30:30,867: INFO/MainProcess] DatabaseScheduler: Schedule changed.
DEBUG:asyncio:Using selector: EpollSelector
DEBUG:celery.utils.functional:
def sync_items_price_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_register_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def init_system_task(self):
    return 1

DEBUG:celery.utils.functional:
def accumulate(self, *args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def delete_old_balance_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache(count, batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_today_amount():
    return 1

DEBUG:celery.utils.functional:
def case_bot_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def unlock_chord(self, group_id, callback, interval=0, max_retries=1, result=2, Result=3, GroupResult=4, result_from_tuple=5, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def case_pk_join_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def xmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def hourly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_recharge_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def xstarmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def update_month_amount():
    return 1

DEBUG:celery.utils.functional:
def delete_old_case_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def roll_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def chunks(task, it, n):
    return 1

DEBUG:celery.utils.functional:
def delete_old_log_entries(batch_size=0, days=1):
    return 1

DEBUG:celery.utils.functional:
def update_new_user_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def check_envelop_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def group(self, tasks, result, group_id, partial_args, add_to_parent=0):
    return 1

DEBUG:celery.utils.functional:
def daily_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_domain_verify_switch(value):
    return 1

DEBUG:celery.utils.functional:
def chain(*args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def check_room_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def chord(self, header, body, partial_args=0, interval=1, countdown=2, max_retries=3, eager=4, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_staff_balance():
    return 1

DEBUG:celery.utils.functional:
def update_daily_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache_task(self, count=0):
    return 1

DEBUG:celery.utils.functional:
def check_pendclose_trade_task(self):
    return 1

DEBUG:celery.utils.functional:
def weekly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_enable_based_on_coins(min_coins, max_coins):
    return 1

DEBUG:celery.utils.functional:
def delete_old_package_items(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def zbt_buy_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_user_active_point_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_enable_rule_exam(value):
    return 1

DEBUG:celery.utils.functional:
def backend_cleanup():
    return 1

DEBUG:celery.utils.functional:
def check_b2c_trade_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_monitor_data_task(self):
    return 1

DEBUG:celery.utils.functional:
def case_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def cleanup_duplicate_roll_room_bets():
    return 1

DEBUG:celery.utils.functional:
def update_enable_pay_method(enable_list):
    return 1

DEBUG:celery.utils.functional:
def case_pk_worker_task(self):
    return 1

[2025-07-18 10:33:20,182: INFO/MainProcess] beat: Starting...
[2025-07-18 10:33:20,904: INFO/MainProcess] DatabaseScheduler: Schedule changed.
[2025-07-18 10:34:31,888: INFO/MainProcess] Scheduler: Sending due task cleanup-duplicate-roll-room-bets (roll.tasks.cleanup_duplicate_roll_room_bets)
[2025-07-18 10:34:32,065: INFO/MainProcess] Scheduler: Sending due task update-case-records-cache (thworker.update_case_records_cache)
[2025-07-18 10:34:32,152: INFO/MainProcess] Scheduler: Sending due task check-b2c-trade-state (thworker.check_b2c_trade_state)
[2025-07-18 10:35:12,254: INFO/MainProcess] DatabaseScheduler: Schedule changed.
[2025-07-18 10:39:31,887: INFO/MainProcess] Scheduler: Sending due task cleanup-duplicate-roll-room-bets (roll.tasks.cleanup_duplicate_roll_room_bets)
[2025-07-18 10:39:32,098: INFO/MainProcess] Scheduler: Sending due task update-case-records-cache (thworker.update_case_records_cache)
[2025-07-18 10:39:32,163: INFO/MainProcess] Scheduler: Sending due task check-b2c-trade-state (thworker.check_b2c_trade_state)
DEBUG:asyncio:Using selector: EpollSelector
DEBUG:celery.utils.functional:
def sync_items_price_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_register_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def init_system_task(self):
    return 1

DEBUG:celery.utils.functional:
def accumulate(self, *args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def delete_old_balance_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache(count, batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_today_amount():
    return 1

DEBUG:celery.utils.functional:
def case_bot_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def unlock_chord(self, group_id, callback, interval=0, max_retries=1, result=2, Result=3, GroupResult=4, result_from_tuple=5, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def case_pk_join_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def xmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def hourly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_recharge_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def xstarmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def update_month_amount():
    return 1

DEBUG:celery.utils.functional:
def delete_old_case_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def roll_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def chunks(task, it, n):
    return 1

DEBUG:celery.utils.functional:
def delete_old_log_entries(batch_size=0, days=1):
    return 1

DEBUG:celery.utils.functional:
def update_new_user_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def check_envelop_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def group(self, tasks, result, group_id, partial_args, add_to_parent=0):
    return 1

DEBUG:celery.utils.functional:
def daily_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_domain_verify_switch(value):
    return 1

DEBUG:celery.utils.functional:
def chain(*args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def check_room_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def chord(self, header, body, partial_args=0, interval=1, countdown=2, max_retries=3, eager=4, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_staff_balance():
    return 1

DEBUG:celery.utils.functional:
def update_daily_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache_task(self, count=0):
    return 1

DEBUG:celery.utils.functional:
def check_pendclose_trade_task(self):
    return 1

DEBUG:celery.utils.functional:
def weekly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_enable_based_on_coins(min_coins, max_coins):
    return 1

DEBUG:celery.utils.functional:
def delete_old_package_items(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def zbt_buy_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_user_active_point_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_enable_rule_exam(value):
    return 1

DEBUG:celery.utils.functional:
def backend_cleanup():
    return 1

DEBUG:celery.utils.functional:
def check_b2c_trade_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_monitor_data_task(self):
    return 1

DEBUG:celery.utils.functional:
def case_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def cleanup_duplicate_roll_room_bets():
    return 1

DEBUG:celery.utils.functional:
def update_enable_pay_method(enable_list):
    return 1

DEBUG:celery.utils.functional:
def case_pk_worker_task(self):
    return 1

[2025-07-18 10:40:03,813: INFO/MainProcess] beat: Starting...
[2025-07-18 10:40:04,601: INFO/MainProcess] DatabaseScheduler: Schedule changed.
[2025-07-18 10:44:31,890: INFO/MainProcess] Scheduler: Sending due task cleanup-duplicate-roll-room-bets (roll.tasks.cleanup_duplicate_roll_room_bets)
[2025-07-18 10:44:32,105: INFO/MainProcess] Scheduler: Sending due task update-case-records-cache (thworker.update_case_records_cache)
[2025-07-18 10:44:32,166: INFO/MainProcess] Scheduler: Sending due task check-b2c-trade-state (thworker.check_b2c_trade_state)
[2025-07-18 10:49:31,891: INFO/MainProcess] Scheduler: Sending due task cleanup-duplicate-roll-room-bets (roll.tasks.cleanup_duplicate_roll_room_bets)
[2025-07-18 10:49:32,106: INFO/MainProcess] Scheduler: Sending due task update-case-records-cache (thworker.update_case_records_cache)
[2025-07-18 10:49:32,168: INFO/MainProcess] Scheduler: Sending due task check-b2c-trade-state (thworker.check_b2c_trade_state)
[2025-07-18 10:54:31,894: INFO/MainProcess] Scheduler: Sending due task cleanup-duplicate-roll-room-bets (roll.tasks.cleanup_duplicate_roll_room_bets)
[2025-07-18 10:54:32,108: INFO/MainProcess] Scheduler: Sending due task update-case-records-cache (thworker.update_case_records_cache)
[2025-07-18 10:54:32,171: INFO/MainProcess] Scheduler: Sending due task check-b2c-trade-state (thworker.check_b2c_trade_state)
[2025-07-18 10:59:31,896: INFO/MainProcess] Scheduler: Sending due task cleanup-duplicate-roll-room-bets (roll.tasks.cleanup_duplicate_roll_room_bets)
[2025-07-18 10:59:32,112: INFO/MainProcess] Scheduler: Sending due task update-case-records-cache (thworker.update_case_records_cache)
[2025-07-18 10:59:32,174: INFO/MainProcess] Scheduler: Sending due task check-b2c-trade-state (thworker.check_b2c_trade_state)
[2025-07-18 11:00:00,001: INFO/MainProcess] Scheduler: Sending due task hourly-lottery (thworker.hourly_lottery)
[2025-07-18 11:04:31,900: INFO/MainProcess] Scheduler: Sending due task cleanup-duplicate-roll-room-bets (roll.tasks.cleanup_duplicate_roll_room_bets)
[2025-07-18 11:04:32,120: INFO/MainProcess] Scheduler: Sending due task update-case-records-cache (thworker.update_case_records_cache)
[2025-07-18 11:04:32,175: INFO/MainProcess] Scheduler: Sending due task check-b2c-trade-state (thworker.check_b2c_trade_state)
[2025-07-18 11:09:31,902: INFO/MainProcess] Scheduler: Sending due task cleanup-duplicate-roll-room-bets (roll.tasks.cleanup_duplicate_roll_room_bets)
[2025-07-18 11:09:32,132: INFO/MainProcess] Scheduler: Sending due task update-case-records-cache (thworker.update_case_records_cache)
[2025-07-18 11:09:32,177: INFO/MainProcess] Scheduler: Sending due task check-b2c-trade-state (thworker.check_b2c_trade_state)
[2025-07-18 11:14:31,904: INFO/MainProcess] Scheduler: Sending due task cleanup-duplicate-roll-room-bets (roll.tasks.cleanup_duplicate_roll_room_bets)
[2025-07-18 11:14:32,132: INFO/MainProcess] Scheduler: Sending due task update-case-records-cache (thworker.update_case_records_cache)
[2025-07-18 11:14:32,179: INFO/MainProcess] Scheduler: Sending due task check-b2c-trade-state (thworker.check_b2c_trade_state)
[2025-07-18 11:19:31,907: INFO/MainProcess] Scheduler: Sending due task cleanup-duplicate-roll-room-bets (roll.tasks.cleanup_duplicate_roll_room_bets)
[2025-07-18 11:19:32,136: INFO/MainProcess] Scheduler: Sending due task update-case-records-cache (thworker.update_case_records_cache)
[2025-07-18 11:19:32,185: INFO/MainProcess] Scheduler: Sending due task check-b2c-trade-state (thworker.check_b2c_trade_state)
[2025-07-18 11:24:31,954: INFO/MainProcess] Scheduler: Sending due task cleanup-duplicate-roll-room-bets (roll.tasks.cleanup_duplicate_roll_room_bets)
[2025-07-18 11:24:32,131: INFO/MainProcess] Scheduler: Sending due task update-case-records-cache (thworker.update_case_records_cache)
[2025-07-18 11:24:32,186: INFO/MainProcess] Scheduler: Sending due task check-b2c-trade-state (thworker.check_b2c_trade_state)
DEBUG:asyncio:Using selector: EpollSelector
DEBUG:celery.utils.functional:
def sync_items_price_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_register_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def init_system_task(self):
    return 1

DEBUG:celery.utils.functional:
def accumulate(self, *args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def delete_old_balance_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache(count, batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_today_amount():
    return 1

DEBUG:celery.utils.functional:
def case_bot_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def unlock_chord(self, group_id, callback, interval=0, max_retries=1, result=2, Result=3, GroupResult=4, result_from_tuple=5, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def case_pk_join_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def xmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def hourly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_recharge_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def xstarmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def update_month_amount():
    return 1

DEBUG:celery.utils.functional:
def delete_old_case_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def roll_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def chunks(task, it, n):
    return 1

DEBUG:celery.utils.functional:
def delete_old_log_entries(batch_size=0, days=1):
    return 1

DEBUG:celery.utils.functional:
def update_new_user_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def check_envelop_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def group(self, tasks, result, group_id, partial_args, add_to_parent=0):
    return 1

DEBUG:celery.utils.functional:
def daily_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_domain_verify_switch(value):
    return 1

DEBUG:celery.utils.functional:
def chain(*args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def check_room_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def chord(self, header, body, partial_args=0, interval=1, countdown=2, max_retries=3, eager=4, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_staff_balance():
    return 1

DEBUG:celery.utils.functional:
def update_daily_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache_task(self, count=0):
    return 1

DEBUG:celery.utils.functional:
def check_pendclose_trade_task(self):
    return 1

DEBUG:celery.utils.functional:
def weekly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_enable_based_on_coins(min_coins, max_coins):
    return 1

DEBUG:celery.utils.functional:
def delete_old_package_items(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def zbt_buy_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_user_active_point_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_enable_rule_exam(value):
    return 1

DEBUG:celery.utils.functional:
def backend_cleanup():
    return 1

DEBUG:celery.utils.functional:
def check_b2c_trade_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_monitor_data_task(self):
    return 1

DEBUG:celery.utils.functional:
def case_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def cleanup_duplicate_roll_room_bets():
    return 1

DEBUG:celery.utils.functional:
def update_enable_pay_method(enable_list):
    return 1

DEBUG:celery.utils.functional:
def case_pk_worker_task(self):
    return 1

[2025-07-18 11:28:05,516: INFO/MainProcess] beat: Starting...
[2025-07-18 11:28:06,137: INFO/MainProcess] DatabaseScheduler: Schedule changed.
[2025-07-18 11:29:31,961: INFO/MainProcess] Scheduler: Sending due task cleanup-duplicate-roll-room-bets (roll.tasks.cleanup_duplicate_roll_room_bets)
[2025-07-18 11:29:32,134: INFO/MainProcess] Scheduler: Sending due task update-case-records-cache (thworker.update_case_records_cache)
[2025-07-18 11:29:32,186: INFO/MainProcess] Scheduler: Sending due task check-b2c-trade-state (thworker.check_b2c_trade_state)
DEBUG:asyncio:Using selector: EpollSelector
DEBUG:celery.utils.functional:
def case_pk_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def accumulate(self, *args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def sync_items_price_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_register_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def init_system_task(self):
    return 1

DEBUG:celery.utils.functional:
def unlock_chord(self, group_id, callback, interval=0, max_retries=1, result=2, Result=3, GroupResult=4, result_from_tuple=5, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def delete_old_balance_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache(count, batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_today_amount():
    return 1

DEBUG:celery.utils.functional:
def case_bot_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_enable_pay_method(enable_list):
    return 1

DEBUG:celery.utils.functional:
def xmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def case_pk_join_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def xstarmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def hourly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_recharge_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def chunks(task, it, n):
    return 1

DEBUG:celery.utils.functional:
def update_month_amount():
    return 1

DEBUG:celery.utils.functional:
def delete_old_case_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def roll_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_log_entries(batch_size=0, days=1):
    return 1

DEBUG:celery.utils.functional:
def update_new_user_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def group(self, tasks, result, group_id, partial_args, add_to_parent=0):
    return 1

DEBUG:celery.utils.functional:
def check_envelop_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def chain(*args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def daily_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_domain_verify_switch(value):
    return 1

DEBUG:celery.utils.functional:
def chord(self, header, body, partial_args=0, interval=1, countdown=2, max_retries=3, eager=4, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def check_room_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_staff_balance():
    return 1

DEBUG:celery.utils.functional:
def update_daily_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache_task(self, count=0):
    return 1

DEBUG:celery.utils.functional:
def check_pendclose_trade_task(self):
    return 1

DEBUG:celery.utils.functional:
def weekly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_enable_based_on_coins(min_coins, max_coins):
    return 1

DEBUG:celery.utils.functional:
def delete_old_package_items(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def backend_cleanup():
    return 1

DEBUG:celery.utils.functional:
def zbt_buy_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_user_active_point_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_enable_rule_exam(value):
    return 1

DEBUG:celery.utils.functional:
def check_b2c_trade_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_monitor_data_task(self):
    return 1

DEBUG:celery.utils.functional:
def case_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def cleanup_duplicate_roll_room_bets():
    return 1

[2025-07-18 11:32:49,542: INFO/MainProcess] beat: Starting...
[2025-07-18 11:32:50,165: INFO/MainProcess] DatabaseScheduler: Schedule changed.
[2025-07-18 11:34:31,961: INFO/MainProcess] Scheduler: Sending due task cleanup-duplicate-roll-room-bets (roll.tasks.cleanup_duplicate_roll_room_bets)
[2025-07-18 11:34:32,137: INFO/MainProcess] Scheduler: Sending due task update-case-records-cache (thworker.update_case_records_cache)
[2025-07-18 11:34:32,188: INFO/MainProcess] Scheduler: Sending due task check-b2c-trade-state (thworker.check_b2c_trade_state)
[2025-07-18 11:39:31,966: INFO/MainProcess] Scheduler: Sending due task cleanup-duplicate-roll-room-bets (roll.tasks.cleanup_duplicate_roll_room_bets)
[2025-07-18 11:39:32,138: INFO/MainProcess] Scheduler: Sending due task update-case-records-cache (thworker.update_case_records_cache)
[2025-07-18 11:39:32,243: INFO/MainProcess] Scheduler: Sending due task check-b2c-trade-state (thworker.check_b2c_trade_state)
[2025-07-18 11:44:31,968: INFO/MainProcess] Scheduler: Sending due task cleanup-duplicate-roll-room-bets (roll.tasks.cleanup_duplicate_roll_room_bets)
[2025-07-18 11:44:32,144: INFO/MainProcess] Scheduler: Sending due task update-case-records-cache (thworker.update_case_records_cache)
[2025-07-18 11:44:32,231: INFO/MainProcess] Scheduler: Sending due task check-b2c-trade-state (thworker.check_b2c_trade_state)
[2025-07-18 11:49:31,968: INFO/MainProcess] Scheduler: Sending due task cleanup-duplicate-roll-room-bets (roll.tasks.cleanup_duplicate_roll_room_bets)
[2025-07-18 11:49:32,149: INFO/MainProcess] Scheduler: Sending due task update-case-records-cache (thworker.update_case_records_cache)
[2025-07-18 11:49:32,230: INFO/MainProcess] Scheduler: Sending due task check-b2c-trade-state (thworker.check_b2c_trade_state)
