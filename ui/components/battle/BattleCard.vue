<template>
  <div
    class="battle-card bg-gray-900/80 rounded-xl border border-gray-800/30 shadow-xl relative overflow-hidden backdrop-blur-md hover:shadow-glow transition-all duration-300 group cursor-pointer w-full h-[25rem] flex flex-col"
    @click="$emit('view', battle)"
  >
    <!-- 装饰性光效 -->
    <div class="absolute top-0 left-0 w-32 h-32 rounded-full blur-3xl bg-primary/5 group-hover:bg-primary/10 transition-all duration-500"></div>
    <div class="absolute bottom-0 right-0 w-32 h-32 rounded-full blur-3xl bg-secondary/5 group-hover:bg-secondary/10 transition-all duration-500"></div>

    <div class="relative z-10 flex flex-col h-full">
      <!-- 头部信息 -->
      <div class="p-4 border-b border-gray-800/30 flex-shrink-0">
        <div class="flex justify-between items-start mb-2">
          <div class="flex-1 min-w-0">
            <h3 class="text-white font-bold text-base truncate group-hover:text-primary transition-colors duration-300">
              {{ `Battle #${battle.short_id}` }}
            </h3>
          </div>
          
          <!-- 状态标识 -->
          <div 
            class="px-2.5 py-1 rounded-full text-xs font-medium ml-2 flex items-center gap-1 shadow-lg flex-shrink-0"
            :class="getStatusClass(battle.state)"
          >
            <i :class="getStatusIcon(battle.state)" class="text-xs"></i>
            {{ getStatusText(battle.state) }}
          </div>
        </div>

        <!-- 基本信息 -->
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-4 text-sm text-white/70">
            <!-- 玩家数量 -->
            <div class="flex items-center gap-1.5">
              <div class="w-5 h-5 rounded-full bg-primary/20 flex items-center justify-center">
                <i class="fa fa-users text-primary text-xs"></i>
              </div>
              <span class="font-medium text-xs">{{ getCurrentPlayerCount() }}/{{ battle.max_joiner }}</span>
            </div>
            <!-- 总价值 -->
            <div class="flex items-center gap-1.5">
              <div class="w-5 h-5 rounded-full bg-emerald-500/20 flex items-center justify-center">
                <i class="fa fa-dollar-sign text-emerald-400 text-xs"></i>
              </div>
              <span class="text-emerald-400 font-bold text-xs">${{ battle.price.toFixed(2) }}</span>
            </div>
          </div>
          
          <!-- 房间类型 -->
          <div v-if="battle.type === 1" class="px-2 py-0.5 bg-purple-500/20 text-purple-400 rounded text-xs border border-purple-500/30 flex-shrink-0">
            <i class="fa fa-lock mr-1"></i>
            {{ t('battle.private') }}
          </div>
        </div>
      </div>

      <!-- 玩家头像区域 - 压缩高度 -->
      <div class="px-4 py-2.5 border-b border-gray-800/30 flex-shrink-0">
        <div class="flex items-center justify-between mb-1.5">
          <h4 class="text-white/80 text-xs font-semibold">
            {{ t('battle.players') }}
          </h4>
          <span class="text-xs text-white/50">
            {{ formatTime(battle.create_time) }}
          </span>
        </div>
        
        <div class="flex items-center justify-center">
          <!-- 动态网格布局：2-4人都用统一的flex布局 -->
          <div class="flex items-center justify-center gap-2 w-full">
            <!-- 房主（创建者） -->
            <div class="flex flex-col items-center group/player">
              <div class="w-8 h-8 relative">
                <img
                  :src="battle.user.profile.avatar || getDefaultAvatar(battle.user.profile.nickname)"
                  :alt="battle.user.profile.nickname"
                  class="w-full h-full rounded-full object-cover bg-gray-600/50 border border-yellow-500/70 transition-all duration-300 group-hover/player:border-yellow-400 group-hover/player:scale-105"
                  @error="handleAvatarError"
                >
                <!-- 房主标识 -->
                <div class="absolute -top-0.5 -right-0.5 w-3 h-3 bg-yellow-500 rounded-full flex items-center justify-center shadow-lg">
                  <i class="fa fa-crown text-black text-xs"></i>
                </div>
              </div>
              <div class="mt-0.5 text-center">
                <p class="text-xs text-white/80 font-medium truncate max-w-12 leading-tight">
                  {{ battle.user.profile.nickname }}
                </p>
              </div>
            </div>

            <!-- 参与者（排除创建者） -->
            <div
              v-for="(bet, index) in participants"
              :key="bet.user.uid"
              class="flex flex-col items-center group/player"
            >
              <div class="w-8 h-8 relative">
                <img
                  :src="bet.user.profile.avatar || getDefaultAvatar(bet.user.profile.nickname)"
                  :alt="bet.user.profile.nickname"
                  class="w-full h-full rounded-full object-cover bg-gray-600/50 border border-gray-600/30 transition-all duration-300 group-hover/player:border-primary/50 group-hover/player:scale-105"
                  @error="handleAvatarError"
                >
                <!-- 获胜标识 -->
                <div
                  v-if="bet.victory === true"
                  class="absolute -top-0.5 -right-0.5 w-3 h-3 bg-green-500 rounded-full flex items-center justify-center shadow-lg"
                >
                  <i class="fa fa-trophy text-white text-[8px]"></i>
                </div>
              </div>
              <div class="mt-0.5 text-center">
                <p class="text-xs text-white/80 font-medium truncate max-w-12 leading-tight">
                  {{ bet.user.profile.nickname }}
                </p>
              </div>
            </div>

            <!-- 空位：总数减去已有的用户数（房主1个+参与者数量） -->
            <div
              v-for="i in getEmptySlotCount()"
              :key="`empty-${i}`"
              class="flex flex-col items-center opacity-60"
            >
              <div class="w-8 h-8 bg-gray-700/50 border border-dashed border-gray-600 rounded-full flex items-center justify-center transition-all duration-300 hover:border-primary/50 hover:bg-gray-600/50">
                <i class="fa fa-plus text-gray-400 text-xs"></i>
              </div>
              <div class="mt-0.5 text-center">
                <p class="text-xs text-gray-500 leading-tight">{{ t('battle.waiting') }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 箱子展示区域 - 一行布局 -->
      <div class="p-4 flex-1 flex flex-col">
        <div class="flex-1">
          <div class="flex items-center justify-between mb-2">
            <h4 class="text-white/80 text-xs font-semibold flex items-center gap-1.5">
              <div class="w-4 h-4 rounded bg-primary/20 flex items-center justify-center">
                <i class="fa fa-box text-primary text-xs"></i>
              </div>
              {{ t('battle.battle_cases') }}
            </h4>
            <span class="text-xs text-white/50 bg-gray-800/50 px-1.5 py-0.5 rounded-full">
              {{ battle.round_count }} {{ t('battle.rounds') }}
            </span>
          </div>
          
          <!-- 箱子一行布局 - 根据183:125比例优化 -->
          <div class="flex gap-2 mb-3 overflow-x-auto scrollbar-hide">
            <!-- 显示的箱子 -->
            <div
              v-for="(round, index) in getDisplayRounds()"
              :key="`round-${index}`"
              class="relative bg-gradient-to-br from-gray-800/60 to-gray-900/80 rounded-lg border border-gray-700/30 group/case hover:border-primary/40 transition-all duration-300 overflow-hidden hover:shadow-lg hover:shadow-primary/10 flex-shrink-0"
              :style="{ width: '80px', height: '55px' }"
            >
              <!-- 箱子图片容器 - 按183:125比例 -->
              <div class="h-full relative p-1 flex flex-col">
                <!-- 背景光效 -->
                <div class="absolute inset-0 bg-gradient-to-br from-primary/5 to-secondary/5 opacity-0 group-hover/case:opacity-100 transition-opacity duration-300 rounded-lg"></div>
                
                <!-- 图片区域 -->
                <div class="flex-1 flex items-center justify-center relative">
                  <img
                    :src="round.case.cover"
                    :alt="getLocalizedCaseName(round.case)"
                    class="max-w-full max-h-full object-contain transition-all duration-300 group-hover/case:scale-110 relative z-10 drop-shadow-lg"
                    @error="handleImageError"
                  >
                  
                  <!-- 箱子使用次数 - 左上角 -->
                  <div v-if="round.case.count > 1" class="absolute top-0 left-0 bg-orange-500 text-white text-[8px] px-1 py-0.5 rounded-bl-lg font-bold z-20 shadow-lg flex items-center gap-0.5">
                    <i class="fa fa-fire text-[6px]"></i>
                    <span>{{ round.case.count }}</span>
                  </div>
                  
                  <!-- 折扣标识 - 右上角 -->
                  <div
                    v-if="round.case.discount > 0"
                    class="absolute top-0 right-0 bg-red-500 text-white text-[8px] px-1 py-0.5 rounded-br-lg font-bold z-20 shadow-lg"
                  >
                    -{{ Math.round(round.case.discount * 100) }}%
                  </div>
                </div>
                
                <!-- 箱子信息 -->
                <div class="mt-0.5">
                  <p class="text-white text-[9px] font-medium truncate mb-0.5 group-hover/case:text-primary transition-colors duration-300 leading-tight">
                    {{ getLocalizedCaseName(round.case) }}
                  </p>
                  <div class="flex items-center justify-between">
                    <p class="text-emerald-400 text-[9px] font-bold">
                      ${{ round.case.price.toFixed(2) }}
                    </p>
                  </div>
                </div>
                
                <!-- 稀有度指示器 -->
                <div class="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-primary/60 to-secondary/60 opacity-70"></div>
              </div>
            </div>
            
            <!-- 更多箱子指示器（仅当有超出显示的箱子时） -->
            <div
              v-if="getExtraRoundsCount() > 0"
              class="bg-gray-800/30 rounded-lg border border-gray-700/30 border-dashed flex flex-col items-center justify-center text-white/40 hover:text-white/60 hover:border-gray-600/50 transition-all duration-300 flex-shrink-0"
              :style="{ width: '80px', height: '55px' }"
            >
              <div class="text-center">
                <i class="fa fa-plus text-sm mb-0.5"></i>
                <div class="text-[10px] font-medium">+{{ getExtraRoundsCount() }}</div>
              </div>
            </div>
          </div>

          <!-- 进度条（仅限进行中状态） -->
          <div v-if="battle.state === BattleStatus.IN_PROGRESS" class="mb-3">
            <div class="flex items-center justify-between mb-1">
              <span class="text-xs text-white/60 font-medium">{{ t('battle.progress') }}</span>
              <span class="text-xs text-primary font-bold">{{ battle.round_count_current }}/{{ battle.round_count }}</span>
            </div>
            <div class="w-full h-2 bg-gray-800/50 rounded-full overflow-hidden">
              <div 
                class="h-full bg-gradient-to-r from-primary to-secondary transition-all duration-500 ease-out relative"
                :style="`width: ${Math.round((battle.round_count_current / battle.round_count) * 100)}%`"
              >
                <div class="absolute inset-0 bg-white/20 animate-pulse"></div>
                <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-pulse"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 底部操作按钮 - 固定在底部 -->
        <div class="flex gap-2 flex-shrink-0">
          <!-- 观战按钮 -->
          <button
            v-if="battle.state === BattleStatus.IN_PROGRESS"
            @click.stop="$emit('spectate', battle)"
            class="flex-1 px-3 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-all duration-200 text-xs font-medium flex items-center justify-center gap-1.5 shadow-md hover:shadow-lg"
          >
            <i class="fa fa-eye text-xs"></i>
            {{ t('battle.spectate') }}
          </button>
          
          <!-- 加入按钮 -->
          <button
            v-if="canJoin"
            @click.stop="$emit('join', battle)"
            :disabled="isJoining"
            class="flex-1 px-3 py-2 bg-gradient-to-r from-primary to-secondary hover:from-primary/90 hover:to-secondary/90 text-black rounded-lg transition-all duration-200 text-xs font-bold disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-1.5 shadow-md hover:shadow-lg hover:shadow-primary/20"
          >
            <i v-if="!isJoining" class="fa fa-plus text-xs"></i>
            <i v-else class="fa fa-spinner fa-spin text-xs"></i>
            {{ isJoining ? t('common.loading') : t('battle.join_battle') }}
          </button>
          
          <!-- 查看详情按钮 -->
          <button
            v-if="!canJoin && battle.state !== BattleStatus.IN_PROGRESS"
            @click.stop="$emit('view', battle)"
            class="flex-1 px-3 py-2 bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-500 hover:to-blue-400 text-white rounded-lg transition-all duration-200 text-xs font-medium flex items-center justify-center gap-1.5 shadow-md hover:shadow-lg hover:shadow-blue-500/20"
          >
            <i class="fa fa-info-circle text-xs"></i>
            {{ t('battle.view_details') }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Battle } from '~/services/battle-api'
import { BattleStatus } from '~/services/battle-api'

// Props
interface Props {
  battle: Battle
  isJoining?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isJoining: false
})

// Emits
const emit = defineEmits<{
  join: [battle: Battle]
  spectate: [battle: Battle]
  view: [battle: Battle]
}>()

// 国际化
const { t, locale } = useI18n()

// 计算属性
// 过滤后的参与者（排除创建者）
const participants = computed(() => {
  return props.battle.bets.filter(bet => bet.user.uid !== props.battle.user.uid)
})

const canJoin = computed(() => {
  return (
    (props.battle.state === BattleStatus.WAITING || props.battle.state === BattleStatus.INITIAL) &&
    getCurrentPlayerCount() < props.battle.max_joiner &&
    !props.isJoining
  )
})

// 显示的轮次（一行显示，最多6个以保持布局美观）
const getDisplayRounds = () => {
  return props.battle.rounds.slice(0, 6)
}

// 额外轮次数量
const getExtraRoundsCount = () => {
  return Math.max(0, props.battle.rounds.length - 6)
}

// 当前玩家数量：房主1个 + 参与者数量（排除创建者）
const getCurrentPlayerCount = () => {
  return 1 + participants.value.length // 房主 + 参与者（排除重复的创建者）
}

// 空位数量：总位置 - 实际用户数（房主1个 + 参与者数量）
const getEmptySlotCount = () => {
  const actualUserCount = getCurrentPlayerCount()
  return Math.max(0, props.battle.max_joiner - actualUserCount)
}

// 方法
const getLocalizedCaseName = (caseItem: any): string => {
  const isZhHans = locale.value === 'zh-hans'
  return isZhHans 
    ? (caseItem.name_zh_hans || caseItem.name || t('battle.unknown_case'))
    : (caseItem.name_en || caseItem.name || 'Unknown Case')
}

const getStatusClass = (status: BattleStatus): string => {
  const classes: Record<BattleStatus, string> = {
    [BattleStatus.WAITING]: 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30',
    [BattleStatus.IN_PROGRESS]: 'bg-green-500/20 text-green-400 border border-green-500/30',
    [BattleStatus.COMPLETED]: 'bg-blue-500/20 text-blue-400 border border-blue-500/30',
    [BattleStatus.CANCELLED]: 'bg-red-500/20 text-red-400 border border-red-500/30'
  }
  return classes[status] || 'bg-gray-500/20 text-gray-400 border border-gray-500/30'
}

const getStatusIcon = (status: BattleStatus): string => {
  const icons: Record<BattleStatus, string> = {
    [BattleStatus.WAITING]: 'fa fa-clock',
    [BattleStatus.IN_PROGRESS]: 'fa fa-play',
    [BattleStatus.COMPLETED]: 'fa fa-check-circle',
    [BattleStatus.CANCELLED]: 'fa fa-times-circle'
  }
  return icons[status] || 'fa fa-question-circle'
}

const getStatusText = (status: BattleStatus): string => {
  const texts: Record<BattleStatus, string> = {
    [BattleStatus.WAITING]: t('battle.status.waiting'),
    [BattleStatus.IN_PROGRESS]: t('battle.status.in_progress'),
    [BattleStatus.COMPLETED]: t('battle.status.completed'),
    [BattleStatus.CANCELLED]: t('battle.status.cancelled')
  }
  return texts[status] || t('battle.status.unknown')
}

const getDefaultAvatar = (nickname: string): string => {
  const firstChar = nickname?.charAt(0).toUpperCase() || '?'
  const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8']
  const colorIndex = nickname?.charCodeAt(0) % colors.length || 0
  const bgColor = colors[colorIndex]
  
  return `data:image/svg+xml;base64,${btoa(`
    <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
      <circle cx="16" cy="16" r="16" fill="${bgColor}"/>
      <text x="16" y="22" font-family="Arial, sans-serif" font-size="12" font-weight="bold" 
            text-anchor="middle" fill="white">${firstChar}</text>
    </svg>
  `)}`
}

const formatTime = (timeString: string): string => {
  try {
    const date = new Date(timeString)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMinutes = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
    
    if (diffMinutes < 1) {
      return t('battle.time.just_now')
    } else if (diffMinutes < 60) {
      return t('battle.time.minutes_ago', { count: diffMinutes })
    } else if (diffHours < 24) {
      return t('battle.time.hours_ago', { count: diffHours })
    } else if (diffDays < 7) {
      return t('battle.time.days_ago', { count: diffDays })
    } else {
      return date.toLocaleDateString(locale.value === 'zh-hans' ? 'zh-CN' : 'en-US')
    }
  } catch (error) {
    return t('battle.time.unknown')
  }
}

const handleAvatarError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = getDefaultAvatar('?')
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/favicon.ico'
}


</script>

<style lang="scss" scoped>
.battle-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.battle-card:hover {
  transform: translateY(-4px);
}

.shadow-glow {
  box-shadow: 
    0 10px 25px -5px rgba(26, 198, 255, 0.2),
    0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

// 进度条闪烁效果
@keyframes progress-shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

// 头像悬停效果
.group\/player:hover img {
  box-shadow: 0 2px 8px rgba(26, 198, 255, 0.3);
}

// 箱子卡片悬停效果
.group\/case:hover {
  transform: translateY(-1px);
}

// 滚动条隐藏
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
</style> 