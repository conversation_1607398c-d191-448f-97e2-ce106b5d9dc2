# BlindBox 盲盒模块分析文档

## 模块概述

BlindBox模块是平台的盲盒游戏系统，实现了多位置选择开启的盲盒玩法。用户可以从多个位置中选择开启，每个位置都隐藏着不同的奖励，类似于刮刮乐或翻牌游戏。该模块支持普通盲盒、免费盲盒、限时盲盒等多种类型。

## 目录结构

```
blindbox/
├── __init__.py
├── admin.py                 # Django管理后台配置
├── apps.py                  # 应用配置
├── business.py              # 核心业务逻辑
├── models.py                # 数据模型定义
├── serializers.py           # API序列化器
├── tests.py                 # 单元测试
├── urls.py                  # URL路由配置
├── views.py                 # API视图
└── migrations/              # 数据库迁移文件
```

## 数据模型分析

### 1. BlindBoxType - 盲盒类型
```python
class BlindBoxType(models.Model):
    name = models.CharField(max_length=128)              # 类型名称
    category = models.SmallIntegerField()                # 类型分类
    order = models.IntegerField(default=0)               # 排序
```

**类型分类：**
- Normal (1) - 普通盲盒
- Top (2) - 精品盲盒  
- Free (3) - 免费盲盒
- Festival (4) - 节日盲盒
- FreeGive (5) - 赠送盲盒

### 2. BlindBox - 盲盒主表
```python
class BlindBox(models.Model):
    name = models.CharField(max_length=128)              # 盲盒名称
    key = models.CharField(max_length=128, unique=True)  # 唯一标识符
    price = models.FloatField(default=0)                 # 价格
    discount = models.SmallIntegerField(default=100)     # 折扣百分比
    cover = models.ImageField()                          # 封面图片
    item = models.ImageField()                           # 物品图片
    case_type = models.ForeignKey(BlindBoxType)          # 盲盒类型
    order = models.IntegerField(default=0)               # 排序
    unlock = models.BooleanField(default=True)           # 是否解锁
    enable = models.BooleanField(default=True)           # 是否启用
    limited = models.IntegerField(default=0)             # 限制时间戳
    limited_time = models.IntegerField(default=0)        # 限制次数
```

### 3. BlindBoxDrop - 盲盒掉落配置
```python
class BlindBoxDrop(ModelBase):
    item_info = models.ForeignKey(ItemInfo)              # 物品信息
    box = models.ForeignKey(BlindBox)                    # 关联盲盒
    show_chance = models.FloatField(default=0)           # 显示概率
    drop_chance_a = models.FloatField(default=0)         # 掉落概率A
    drop_chance_b = models.FloatField(default=0)         # 掉落概率B
    drop_chance_c = models.FloatField(default=0)         # 掉落概率C
    drop_chance_d = models.FloatField(default=0)         # 掉落概率D
    drop_chance_e = models.FloatField(default=0)         # 掉落概率E
    coins = models.FloatField(default=0)                 # 金币奖励
    custom_enable = models.BooleanField(default=False)   # 自定义启用
    custom_price = models.FloatField()                   # 自定义价格
```

**多概率设计说明：**
- 支持A-E五个不同的概率档位
- 可能用于不同用户等级或活动的差异化概率
- 灵活的概率控制机制

### 4. BlindBoxGame - 游戏记录
```python
class BlindBoxGame(ModelBase):
    user = models.ForeignKey(USER_MODEL)                 # 用户
    gid = models.CharField(max_length=56)                # 游戏ID
    seed = models.CharField(max_length=56)               # 随机种子
```

### 5. BlindBoxRecord - 开启记录
```python
class BlindBoxRecord(ModelBase):
    user = models.ForeignKey(USER_MODEL)                 # 用户
    item_info = models.ForeignKey(ItemInfo)              # 获得物品
    box = models.ForeignKey(BlindBox)                    # 盲盒
    price = models.FloatField(default=0)                 # 价格
    source = models.SmallIntegerField()                  # 来源类型
    index = models.IntegerField(default=0)               # 位置索引
    game = models.ForeignKey(BlindBoxGame)               # 关联游戏
```

### 6. 配置相关模型

#### FreeCaseConfig - 免费盲盒配置
```python
class FreeCaseConfig(models.Model):
    level = models.IntegerField(default=0)               # 等级
    min_amount = models.FloatField(default=0)            # 最小积分
    max_amount = models.FloatField(default=0)            # 最大积分
    case = models.ForeignKey(BlindBox)                   # 关联盲盒
```

#### CaseBot - 盲盒机器人
```python
class CaseBot(models.Model):
    user = models.OneToOneField(USER_MODEL)              # 机器人用户
    open_idle_min = models.IntegerField(default=0)       # 最小间隔（秒）
    open_idle_max = models.IntegerField(default=0)       # 最大间隔（秒）
    case = models.ManyToManyField(BlindBox)              # 关联盲盒
    remark = models.CharField(max_length=128)            # 备注
    enable = models.BooleanField(default=True)           # 是否启用
```

## API 接口分析

### 1. 盲盒开启
**路径：** `POST /api/blindbox/open/`

**功能：** 用户开启指定位置的盲盒

**参数：**
```json
{
    "case_key": "box_001",
    "game": "game_id_123",
    "pos": 5
}
```

**业务流程：**
1. 验证盲盒状态和用户权限
2. 检查游戏缓存和位置有效性
3. 扣除用户余额或免费次数
4. 开启指定位置并获得奖励
5. 创建包裹物品和开启记录
6. 更新游戏缓存状态
7. WebSocket通知新的开启记录

### 2. 更换游戏
**路径：** `POST /api/blindbox/replace/`

**功能：** 重新生成当前盲盒游戏

**参数：**
```json
{
    "case_key": "box_001"
}
```

### 3. 盲盒列表
**路径：** `GET /api/blindbox/list/`

**功能：** 获取可用的盲盒列表

**响应格式：**
```json
{
    "code": 200,
    "data": {
        "case_list": {
            "top": [...],
            "normal": [
                {
                    "type": "经典系列",
                    "case": [...]
                }
            ],
            "freegive": [...]
        }
    }
}
```

### 4. 免费盲盒列表
**路径：** `GET /api/blindbox/free/`

**功能：** 获取免费盲盒配置列表

### 5. 盲盒详情
**路径：** `GET /api/blindbox/detail/`

**功能：** 获取指定盲盒的详细信息

**参数：**
- `case_key`: 盲盒标识符

### 6. 当前游戏状态
**路径：** `GET /api/blindbox/current/`

**功能：** 获取用户当前的盲盒游戏状态

**响应包含：**
- 游戏ID和位置信息
- 每个位置的开启状态
- 游戏剩余时间

### 7. 开启记录（已废弃）
**路径：** `GET /api/blindbox/record/`

**状态：** 已注释，可能功能转移到其他模块

## 核心算法分析

### 1. 游戏生成算法
```python
def generate_blindbox_game(case, user):
    # 获取所有掉落物品
    drops = case.blind_drops.all()
    
    # 随机打乱位置
    shuffle(drops)
    
    # 生成游戏标识
    seed = id_generator(8)
    gid = id_generator(12)
    
    # 为每个位置生成密钥
    game = {
        'game': gid,
        'pos': [],
        'drop': {},
        'seed': seed
    }
    
    md5 = hashlib.md5()
    for index, drop in enumerate(drops):
        # 基于物品名称、种子、游戏ID、位置生成密钥
        md5.update(drop['market_hash_name'].encode('utf-8'))
        base_secret = md5.hexdigest()
        secret_pre = f"{base_secret}:{seed}:{gid}:{index}"
        md5.update(secret_pre.encode('utf-8'))
        
        game['pos'].append({
            'secret': md5.hexdigest(),
            'is_opened': False
        })
        game['drop'][index] = drop
    
    return game
```

### 2. 位置开启逻辑
```python
def open_position(user, case, game, pos):
    # 验证位置有效性
    if pos not in range(len(case.blind_drops.all())):
        raise ValueError("Invalid position")
    
    # 检查位置是否已开启
    if game['pos'][pos]['is_opened']:
        raise ValueError("Position already opened")
    
    # 获取奖励物品
    reward_item = game['drop'][pos]
    
    # 扣除费用
    if case.case_type.category == CaseCategoryType.Free.value:
        deduct_free_count(user)
    else:
        cost = case.price * case.discount / 100
        user.update_balance(-cost, '开盲盒')
    
    # 创建奖励
    package = create_package_item(user, reward_item)
    
    # 更新游戏状态
    game['pos'][pos]['is_opened'] = True
    game['pos'][pos]['drop'] = reward_item
    
    # 检查是否全部开启
    if all(pos['is_opened'] for pos in game['pos']):
        delete_game_cache(user, case.key)
    else:
        update_game_cache(user, case.key, game)
    
    return package
```

### 3. 概率控制机制
```python
def select_drop_item(case, user_level='a'):
    """根据用户等级选择掉落物品"""
    drops = case.blind_drops.all()
    
    # 根据用户等级选择对应的概率字段
    chance_field = f'drop_chance_{user_level}'
    
    # 构建概率表
    total_weight = sum(getattr(drop, chance_field) for drop in drops)
    
    # 随机选择
    random_value = random.uniform(0, total_weight)
    current_weight = 0
    
    for drop in drops:
        current_weight += getattr(drop, chance_field)
        if random_value <= current_weight:
            return drop.item_info
    
    # 默认返回第一个物品
    return drops.first().item_info
```

## 缓存机制分析

### 1. 游戏状态缓存
```python
# 缓存键格式
game_cache_key = f"blindbox:{user_id}:{case_key}"

# 缓存内容
game_data = {
    'game': 'game_id',
    'pos': [
        {'secret': 'hash1', 'is_opened': False},
        {'secret': 'hash2', 'is_opened': True, 'drop': {...}}
    ],
    'drop': {0: item1, 1: item2, ...},
    'seed': 'random_seed'
}
```

### 2. 列表缓存
```python
# 盲盒列表缓存（5分钟）
cache.set('caselist', case_list_data, 5 * 60)

# 免费盲盒列表缓存（1天）
cache.set('freecaselist', free_case_data, settings.DAY_REDIS_TIMEOUT)
```

## WebSocket 实时通信

### 消息格式
```python
def ws_send_box_game(data, action):
    """发送盲盒游戏WebSocket消息"""
    rt_msg = ['box', action, data]
    redis_conn.publish('ws_channel', json.dumps(rt_msg))
```

**消息类型：**
- **new**: 新的开启记录

**消息内容：**
```json
{
    "uid": "record_uuid",
    "user": "username",
    "box": "case_name",
    "item_info": {...},
    "price": 100.0,
    "create_time": "2024-01-01T10:00:00Z"
}
```

## 存在的问题与风险

### 1. 游戏公平性问题

**问题描述：**
- 使用简单的shuffle打乱位置，可预测性较高
- 密钥生成基于MD5，安全性不足
- 缺少客户端验证机制

**影响：**
- 游戏结果可能被预测或操控
- 用户信任度降低
- 监管合规风险

### 2. 概率控制复杂性

**问题描述：**
- 支持A-E五个概率档位，但实际使用逻辑不明确
- 概率计算和选择算法没有在当前代码中体现
- 缺少概率验证和审计机制

**影响：**
- 概率设置可能不准确
- 难以保证概率公平性
- 运营配置复杂

### 3. 缓存一致性问题

**问题描述：**
- 游戏状态依赖Redis缓存，可能丢失
- 缓存过期处理不够完善
- 并发操作可能导致状态不一致

**影响：**
- 用户游戏进度可能丢失
- 重复开启同一位置
- 系统状态混乱

### 4. 用户体验问题

**问题描述：**
- 记录查询接口被废弃，用户无法查看历史
- 游戏更换机制可能被滥用
- 缺少游戏进度保护机制

**影响：**
- 用户体验不完整
- 可能存在刷奖励漏洞
- 客服处理困难

## 改进建议

### 1. 游戏公平性增强

#### 可证明公平算法
```python
import secrets
import hmac
import hashlib

class ProvablyFairBlindBox:
    def __init__(self):
        self.server_seed = secrets.token_hex(32)
        self.server_seed_hash = hashlib.sha256(self.server_seed.encode()).hexdigest()
    
    def generate_game(self, case, client_seed, nonce):
        """生成可证明公平的游戏"""
        # 组合所有熵源
        combined_seed = f"{client_seed}:{self.server_seed}:{nonce}"
        
        # 使用HMAC生成随机序列
        hmac_obj = hmac.new(
            self.server_seed.encode(),
            combined_seed.encode(),
            hashlib.sha256
        )
        
        # 生成随机数序列
        random_bytes = hmac_obj.digest()
        random_numbers = [b / 255.0 for b in random_bytes]
        
        # 基于随机数生成位置布局
        drops = list(case.blind_drops.all())
        positions = self.fisher_yates_shuffle(drops, random_numbers)
        
        return {
            'positions': positions,
            'server_seed_hash': self.server_seed_hash,
            'client_seed': client_seed,
            'nonce': nonce
        }
    
    def fisher_yates_shuffle(self, items, random_numbers):
        """Fisher-Yates洗牌算法"""
        items_copy = items.copy()
        random_index = 0
        
        for i in range(len(items_copy) - 1, 0, -1):
            if random_index >= len(random_numbers):
                break
            
            j = int(random_numbers[random_index] * (i + 1))
            random_index += 1
            
            items_copy[i], items_copy[j] = items_copy[j], items_copy[i]
        
        return items_copy
    
    def verify_game(self, client_seed, nonce, result):
        """验证游戏结果"""
        expected = self.generate_game(None, client_seed, nonce)
        return expected == result
```

### 2. 概率系统优化

#### 统一概率管理
```python
class BlindBoxProbabilityManager:
    def __init__(self):
        self.probability_cache = {}
    
    def get_user_probability_level(self, user):
        """获取用户概率等级"""
        if user.is_vip:
            return 'e'  # VIP用户使用最高概率
        elif user.total_charge >= 1000:
            return 'd'
        elif user.total_charge >= 500:
            return 'c'
        elif user.total_charge >= 100:
            return 'b'
        else:
            return 'a'  # 普通用户
    
    def select_reward_item(self, case, user):
        """基于概率选择奖励物品"""
        level = self.get_user_probability_level(user)
        drops = case.blind_drops.all()
        
        # 构建概率表
        probability_table = []
        total_weight = 0
        
        for drop in drops:
            weight = getattr(drop, f'drop_chance_{level}', 0)
            if weight > 0:
                probability_table.append({
                    'item': drop.item_info,
                    'weight': weight,
                    'cumulative': total_weight + weight
                })
                total_weight += weight
        
        # 随机选择
        random_value = random.uniform(0, total_weight)
        
        for entry in probability_table:
            if random_value <= entry['cumulative']:
                return entry['item']
        
        # 兜底返回第一个物品
        return probability_table[0]['item'] if probability_table else None
    
    def validate_probabilities(self, case):
        """验证概率配置的合理性"""
        drops = case.blind_drops.all()
        
        for level in ['a', 'b', 'c', 'd', 'e']:
            total_prob = sum(
                getattr(drop, f'drop_chance_{level}', 0) 
                for drop in drops
            )
            
            if total_prob <= 0:
                raise ValueError(f"Level {level} has zero total probability")
            
            # 检查是否存在无法获得的物品
            zero_prob_items = [
                drop for drop in drops 
                if getattr(drop, f'drop_chance_{level}', 0) == 0
            ]
            
            if zero_prob_items:
                _logger.warning(
                    f"Level {level} has {len(zero_prob_items)} items with zero probability"
                )
```

### 3. 状态管理优化

#### 持久化游戏状态
```python
class BlindBoxGameManager:
    def __init__(self):
        self.redis_client = redis.Redis()
    
    def create_game(self, user, case):
        """创建新游戏并持久化"""
        game_data = self.generate_game_data(case, user)
        
        # 保存到数据库
        game_record = BlindBoxGame.objects.create(
            user=user,
            gid=game_data['game'],
            seed=game_data['seed']
        )
        
        # 保存到Redis缓存
        cache_key = f"blindbox:{user.id}:{case.key}"
        self.redis_client.setex(
            cache_key,
            3600,  # 1小时过期
            json.dumps(game_data)
        )
        
        # 保存到数据库备份
        self.save_game_backup(game_record, game_data)
        
        return game_data
    
    def open_position(self, user, case, pos):
        """安全的位置开启"""
        cache_key = f"blindbox:{user.id}:{case.key}"
        
        # 使用分布式锁
        with self.redis_client.lock(f"{cache_key}:lock", timeout=10):
            # 从缓存获取游戏状态
            game_data = self.get_game_data(cache_key)
            if not game_data:
                raise ValueError("Game not found or expired")
            
            # 验证位置
            if pos < 0 or pos >= len(game_data['pos']):
                raise ValueError("Invalid position")
            
            if game_data['pos'][pos]['is_opened']:
                raise ValueError("Position already opened")
            
            # 执行开启逻辑
            reward = self.process_position_opening(user, case, game_data, pos)
            
            # 更新状态
            game_data['pos'][pos]['is_opened'] = True
            game_data['pos'][pos]['reward'] = reward
            
            # 保存更新后的状态
            self.save_game_data(cache_key, game_data)
            
            return reward
    
    def save_game_backup(self, game_record, game_data):
        """保存游戏数据备份到数据库"""
        # 可以使用JSON字段或者单独的表来存储
        pass
    
    def recover_game_from_backup(self, user, case_key):
        """从备份恢复游戏数据"""
        # 从数据库恢复游戏状态
        pass
```

### 4. 用户体验优化

#### 完善的记录系统
```python
class BlindBoxRecordManager:
    def get_user_records(self, user, filters=None, page=1, page_size=20):
        """获取用户开启记录"""
        queryset = BlindBoxRecord.objects.filter(user=user)
        
        if filters:
            if filters.get('case_key'):
                queryset = queryset.filter(box__key=filters['case_key'])
            if filters.get('date_from'):
                queryset = queryset.filter(create_time__gte=filters['date_from'])
            if filters.get('date_to'):
                queryset = queryset.filter(create_time__lte=filters['date_to'])
        
        paginator = Paginator(queryset.order_by('-create_time'), page_size)
        records = paginator.page(page)
        
        return {
            'records': BlindBoxRecordSerializer(records, many=True).data,
            'total': paginator.count,
            'page': page,
            'page_size': page_size
        }
    
    def get_record_statistics(self, user):
        """获取用户记录统计"""
        records = BlindBoxRecord.objects.filter(user=user)
        
        return {
            'total_opened': records.count(),
            'total_cost': records.aggregate(Sum('price'))['price__sum'] or 0,
            'total_value': sum(
                get_item_price_by_name(record.item_info.market_hash_name) 
                for record in records
            ),
            'favorite_case': records.values('box__name').annotate(
                count=Count('id')
            ).order_by('-count').first()
        }
```

### 5. 监控和风控

#### 异常检测系统
```python
class BlindBoxMonitor:
    def __init__(self):
        self.alert_thresholds = {
            'rapid_opening': 10,  # 10秒内开启次数
            'high_value_rate': 0.8,  # 高价值物品比例
            'duplicate_rewards': 5,  # 重复奖励次数
        }
    
    def check_user_behavior(self, user):
        """检查用户行为异常"""
        alerts = []
        
        # 检查快速开启
        recent_records = BlindBoxRecord.objects.filter(
            user=user,
            create_time__gte=timezone.now() - timedelta(seconds=10)
        )
        
        if recent_records.count() > self.alert_thresholds['rapid_opening']:
            alerts.append({
                'type': 'rapid_opening',
                'message': f"User {user.username} opened {recent_records.count()} boxes in 10 seconds"
            })
        
        # 检查高价值物品比例
        user_records = BlindBoxRecord.objects.filter(user=user)
        if user_records.count() > 10:
            high_value_count = sum(
                1 for record in user_records.order_by('-create_time')[:20]
                if get_item_price_by_name(record.item_info.market_hash_name) > 100
            )
            
            if high_value_count / 20 > self.alert_thresholds['high_value_rate']:
                alerts.append({
                    'type': 'high_value_rate',
                    'message': f"User {user.username} has unusually high valuable item rate"
                })
        
        return alerts
    
    def monitor_case_statistics(self, case):
        """监控盲盒统计数据"""
        records = BlindBoxRecord.objects.filter(box=case)
        
        # 计算实际概率分布
        item_counts = records.values('item_info').annotate(count=Count('id'))
        total_records = records.count()
        
        if total_records > 100:  # 样本量足够大时进行检查
            for item_count in item_counts:
                actual_rate = item_count['count'] / total_records
                expected_rate = self.get_expected_rate(case, item_count['item_info'])
                
                # 如果实际概率与期望概率差异过大
                if abs(actual_rate - expected_rate) > 0.1:
                    _logger.warning(
                        f"Case {case.key} item probability deviation: "
                        f"expected {expected_rate}, actual {actual_rate}"
                    )
```

## 性能优化建议

### 1. 缓存优化
```python
class BlindBoxCache:
    def __init__(self):
        self.redis_client = redis.Redis()
    
    def get_case_list_cached(self):
        """多级缓存的盲盒列表"""
        # L1: 内存缓存
        if hasattr(self, '_case_list_cache'):
            cache_time, data = self._case_list_cache
            if time.time() - cache_time < 60:  # 1分钟内存缓存
                return data
        
        # L2: Redis缓存
        cached = self.redis_client.get('blindbox:case_list')
        if cached:
            data = json.loads(cached)
            self._case_list_cache = (time.time(), data)
            return data
        
        # L3: 数据库查询
        data = self.build_case_list_from_db()
        
        # 保存到Redis
        self.redis_client.setex(
            'blindbox:case_list', 
            300,  # 5分钟
            json.dumps(data)
        )
        
        # 保存到内存
        self._case_list_cache = (time.time(), data)
        
        return data
```

### 2. 数据库优化
```sql
-- 添加必要索引
CREATE INDEX idx_blindbox_type_category ON blindbox_blindbox(case_type_id, category);
CREATE INDEX idx_blindboxrecord_user_time ON blindbox_blindboxrecord(user_id, create_time);
CREATE INDEX idx_blindboxdrop_box ON blindbox_blindboxdrop(box_id);
```

## 总结

BlindBox模块实现了一个相对完整的多位置选择盲盒系统。当前设计在基本功能上能够满足需求，但在游戏公平性、概率控制、状态管理等方面存在改进空间。建议按照上述优化方案逐步改进，重点关注游戏的公平性验证、概率系统的透明化和用户体验的完善。

## 接口清单

| 接口名称 | 方法 | 路径 | 状态 | 功能描述 |
|---------|------|------|------|----------|
| 盲盒开启 | POST | `/api/blindbox/open/` | 正常 | 开启指定位置 |
| 更换游戏 | POST | `/api/blindbox/replace/` | 正常 | 重新生成游戏 |
| 盲盒列表 | GET | `/api/blindbox/list/` | 正常 | 获取盲盒列表 |
| 免费盲盒列表 | GET | `/api/blindbox/free/` | 正常 | 获取免费盲盒 |
| 盲盒详情 | GET | `/api/blindbox/detail/` | 正常 | 获取盲盒详情 |
| 当前游戏状态 | GET | `/api/blindbox/current/` | 正常 | 获取游戏状态 |
| 开启记录 | GET | `/api/blindbox/record/` | 已废弃 | 查询开启记录 |
| 最近记录 | GET | `/api/blindbox/recent/` | 已废弃 | 获取最近记录 |

## 核心算法深度分析

### 1. 随机掉落生成算法

当前实现的随机掉落算法存在重要的设计缺陷：

```python
def get_blindbox_current(user, key):
    """当前算法实现分析"""
    drops = BlindBoxDropItemSerializer(case.blind_drops.all(), many=True, read_only=True, fields=fields).data
    # 问题1: 使用shuffle()进行简单随机，忽略了概率配置
    shuffle(drops)
    
    # 问题2: MD5哈希生成方式可能存在安全隐患
    md5 = hashlib.md5()
    for index, drop in enumerate(drops):
        md5.update(drop['market_hash_name'].encode('utf-8'))
        base_secret = md5.hexdigest()
        secret_pre = "{}:{}:{}:{}".format(base_secret, seed, gid, index)
        md5.update(secret_pre.encode('utf-8'))
```

**核心问题：**
1. **概率配置失效**: BlindBoxDrop模型定义了`drop_chance_a/b/c/d/e`字段，但实际算法完全忽略了这些概率配置
2. **随机性不够安全**: 使用Python标准的`shuffle()`函数，在生产环境中可能被预测
3. **位置固定化**: 一旦生成游戏，所有位置的掉落就固定了，缺乏真正的随机性

### 2. 免费盲盒等级机制

```python
def update_user_freebox_count(user):
    """免费盲盒次数更新机制"""
    user_level = get_user_level(user)
    if user_level < 1:
        return True  # 无等级用户无法开启免费盲盒
    
    # 根据等级更新相应的免费次数
    if user_level == 1:
        user.extra.update_freebox_lv1_count(user, 1)
    # ... 其他等级类似
```

**机制特点：**
- 与用户充值等级绑定
- 每个等级有独立的免费次数统计
- 通过用户扩展信息(`user.extra`)管理

### 3. 游戏状态缓存策略

```python
# 当前缓存实现的问题
game_key = "blindbox:{}:{}".format(user, key)
cache.set(detail_key, game, None)  # 永不过期 - 问题！

# 条件性缓存更新
if cache.ttl(game_key):
    cache.set(game_key, game, cache.ttl(game_key))
else:
    cache.set(game_key, game, case.limited)
```

**问题分析：**
- 初始设置缓存为永不过期(`None`)，可能导致内存泄漏
- 缓存更新逻辑复杂，容易出现数据不一致

## 深度业务逻辑问题

### 1. 概率系统完全失效

**当前状态：** BlindBoxDrop模型虽然定义了详细的概率字段，但在实际业务逻辑中完全被忽略。

```python
# 模型定义了概率，但未使用
drop_chance_a = models.FloatField(default=0)  # A级概率
drop_chance_b = models.FloatField(default=0)  # B级概率
drop_chance_c = models.FloatField(default=0)  # C级概率
drop_chance_d = models.FloatField(default=0)  # D级概率
drop_chance_e = models.FloatField(default=0)  # E级概率

# 实际使用简单随机
shuffle(drops)  # 忽略了所有概率配置
```

**影响：**
- 运营无法精确控制稀有物品掉落率
- 无法实现差异化的用户体验
- 违背了用户对概率公示的期望

### 2. 安全验证机制不足

**当前验证方式：**
```python
# 仅通过位置索引验证
if pos not in [index for index, d in enumerate(case.blind_drops.all())]:
    return RespCode.InvalidParams.value, _('位置错误，请重新选择')

# 通过缓存中的opened状态验证
if position[pos]['is_opened']:
    return RespCode.InvalidParams.value, _("该位置已开启")
```

**安全隐患：**
- 缺乏客户端伪造请求的防护
- 游戏状态完全依赖缓存，缓存丢失可能导致重复开启
- 没有服务器端的重复验证机制

### 3. WebSocket通知机制

```python
def ws_send_box_game(data, action):
    """实时通知机制"""
    if data:
        r = get_redis_connection('default')
        rt_msg = ['box', action, data]
        r.publish(_box_redis_channel_key, json.dumps(rt_msg))
```

**特点：**
- 使用Redis发布/订阅机制
- 统一的消息格式：`['box', action, data]`
- 实时广播开箱结果
