import logging

from django.contrib import admin

from jet.filters import Date<PERSON><PERSON><PERSON><PERSON><PERSON>er

from django.utils.translation import gettext_lazy as _
from steambase.admin import ReadOnlyAdmin
from steambase.enums import WinResult
from tradeup.models import TradeupGame, TradeupBetItem, TradeupTargetItem, TradeupInventory, TradeupBot
from tradeup.models import TradeupStatisticsDay, TradeupStatisticsMonth, TradeupPumpDay, TradeupPumpMonth
from tradeup.service.admin_actions import sync_tradeup_price


_logger = logging.getLogger(__name__)


@admin.register(TradeupGame)
class TradeupGameAdmin(ReadOnlyAdmin):
    list_display = ('uid', 'user', 'hash', 'bet_amount', 'target_amount', 'win_result', 'create_time')
    search_fields = ('user__username', 'uid')
    list_filter = ('appid',)
    list_per_page = 50


@admin.register(TradeupInventory)
class TradeupInventoryAdmin(admin.ModelAdmin):
    list_display = ('item_info', 'price', 'count', 'unlimited', 'enable')
    fields = ('item_info', 'price', 'count', 'unlimited', 'enable', 'create_time', 'update_time')
    readonly_fields = ('create_time', 'update_time')
    list_editable = ('count', 'unlimited', 'enable')
    raw_id_fields = ['item_info']
    list_per_page = 50
    actions = [sync_tradeup_price]


# @admin.register(TradeupPumpDay)
# class TradeupPumpDayAdmin(ReadOnlyAdmin):
#     fields = ('date', 'amount')
#     list_display = ('date', 'amount')
#     list_filter = (('date', DateRangeFilter),)
#     list_per_page = 50
# 
# 
# @admin.register(TradeupPumpMonth)
# class TradeupPumpMonthAdmin(ReadOnlyAdmin):
#     fields = ('month', 'amount')
#     extra_readonly_fields = ('month',)
#     list_display = ('month', 'amount')
#     list_filter = (('date', DateRangeFilter),)
#     list_per_page = 50


@admin.register(TradeupStatisticsDay)
class TradeupStatisticsDayAdmin(ReadOnlyAdmin):
    fields = ('date', 'win_amount', 'lose_amount', 'journal', 'amount', 'test_amount')
    list_display = ('date', 'win_amount', 'lose_amount', 'journal', 'amount', 'test_amount')
    list_filter = (('date', DateRangeFilter),)
    list_per_page = 50


@admin.register(TradeupStatisticsMonth)
class TradeupStatisticsMonthAdmin(ReadOnlyAdmin):
    fields = ('month', 'win_amount', 'lose_amount', 'journal', 'amount', 'test_amount')
    extra_readonly_fields = ('month',)
    list_display = ('month', 'win_amount', 'lose_amount', 'journal', 'amount', 'test_amount')
    list_filter = (('date', DateRangeFilter),)
    list_per_page = 50

# 
# @admin.register(TradeupBot)
# class TradeupBotAdmin(admin.ModelAdmin):
#     list_display = ('remark', 'user', 'chance_min', 'chance_max', 'price_min', 'price_max', 'bet_idle_min', 'bet_idle_max', 'enable')
#     list_editable = ('chance_min', 'chance_max', 'bet_idle_min', 'price_min', 'price_max', 'bet_idle_max', 'enable')
#     list_filter = ('enable',)
#     search_fields = ('remark', 'user__personaname')
#     list_per_page = 50


# @admin.register(TradeupBotInventory)
# class TradeupBotInventoryAdmin(admin.ModelAdmin):
#     list_display = ('package_item_info', 'price', 'update_time', 'enable')
#     list_editable = ('enable', )
#     raw_id_fields = ['package_item_info']
#     list_per_page = 50
# 
#     def price(self, obj):
#         return obj.package_item_info.item_info.item_price.price
#     price.short_description = _('price')
# 
#     def update_time(self, obj):
#         return obj.package_item_info.item_info.item_price.update_time
#     update_time.short_description = _('update time')
