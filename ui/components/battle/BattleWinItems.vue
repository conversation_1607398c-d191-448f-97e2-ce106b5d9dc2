<template>
  <div class="win-items-section">
    <div class="win-items-header">
      <Icon name="heroicons:gift" class="w-4 h-4 text-yellow-400" />
      <span class="win-items-title">{{ t("battle.detail.win_items") }}</span>
      <span v-if="player.victory" class="win-items-value">
        ￥{{ getWinTotalValue(player).toFixed(2) }}
      </span>
    </div>
    <BattleItemsGrid
      :items="getWinItems(player)"
      :max-players="maxPlayers"
      :convert-to-skin-item="convertToSkinItem"
      type="win-items"
    />
  </div>
</template>

<script setup lang="ts">
import type { BattleBet, BattleItem } from "~/types/battle";

// 🎯 国际化设置
const { t } = useI18n();

// 🎯 Props定义
interface Props {
  player: BattleBet;
  maxPlayers: number;
  convertToSkinItem: (item: BattleItem) => any;
}

const props = defineProps<Props>();

// 🎯 工具函数
const getWinItems = (player: BattleBet): BattleItem[] => {
  return player.win_items || [];
};

const getWinTotalValue = (player: BattleBet): number => {
  return (player.win_items || []).reduce(
    (sum, item) => sum + (item.item_price?.price || 0),
    0
  );
};

// 🎯 调试：检查maxPlayers值
if (import.meta.dev) {
  watch(
    () => props.maxPlayers,
    (newValue, oldValue) => {
      console.log(`[🎰WIN-ITEMS] maxPlayers变化: ${oldValue} -> ${newValue}`)
    },
    { immediate: true }
  )
}
</script>

<style lang="scss" scoped>
// 获胜物品区域
.win-items-section {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

// 获胜物品头部
.win-items-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  font-size: 0.875rem;
  color: var(--color-gray-300, #d1d5db);
}

.win-items-title {
  font-weight: 600;
  flex: 1;
}

.win-items-value {
  font-weight: 700;
  color: var(--color-yellow-400, #fbbf24);
}
</style> 