<template>
  <div class="p-6 bg-gray-800 rounded-lg border border-gray-600">
    <h2 class="text-xl font-semibold text-white mb-6 text-center">重置密码</h2>
    
    <div v-if="step === 1">
      <!-- 步骤1：输入邮箱 -->
      <form @submit.prevent="sendCode" class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">邮箱地址</label>
          <input 
            type="email" 
            v-model="email" 
            class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
            placeholder="请输入您的邮箱地址"
            required
          />
        </div>
        
        <button 
          type="submit" 
          :disabled="isLoading"
          class="w-full py-2 px-4 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white font-medium rounded transition-colors"
        >
          {{ isLoading ? '发送中...' : '发送验证码' }}
        </button>
      </form>
    </div>
    
    <div v-else-if="step === 2">
      <!-- 步骤2：输入验证码和新密码 -->
      <form @submit.prevent="resetPassword" class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">验证码</label>
          <input 
            type="text" 
            v-model="verifyCode" 
            class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
            placeholder="请输入邮箱验证码"
            maxlength="6"
            required
          />
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">新密码</label>
          <input 
            type="password" 
            v-model="newPassword" 
            class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
            placeholder="请输入新密码（至少8位）"
            required
          />
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">确认新密码</label>
          <input 
            type="password" 
            v-model="confirmPassword" 
            class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
            placeholder="请再次输入新密码"
            required
          />
        </div>
        
        <button 
          type="submit" 
          :disabled="isLoading"
          class="w-full py-2 px-4 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white font-medium rounded transition-colors"
        >
          {{ isLoading ? '重置中...' : '重置密码' }}
        </button>
        
        <button 
          type="button" 
          @click="step = 1"
          class="w-full py-2 px-4 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded transition-colors"
        >
          返回上一步
        </button>
      </form>
    </div>
    
    <div v-else-if="step === 3">
      <!-- 步骤3：完成 -->
      <div class="text-center">
        <div class="text-green-400 text-4xl mb-4">✅</div>
        <h3 class="text-lg font-semibold text-white mb-2">密码重置成功</h3>
        <p class="text-gray-300 mb-6">您的密码已成功重置，现在可以使用新密码登录。</p>
        <NuxtLink 
          to="/auth/login"
          class="inline-block py-2 px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded transition-colors"
        >
          前往登录
        </NuxtLink>
      </div>
    </div>
    
    <!-- 错误信息 -->
    <div v-if="error" class="mt-4 p-3 bg-red-500/20 border border-red-500 rounded">
      <p class="text-red-300 text-sm">{{ error }}</p>
    </div>
    
    <!-- 返回登录链接 -->
    <div v-if="step < 3" class="mt-6 text-center">
      <p class="text-gray-400 text-sm">
        想起密码了？
        <NuxtLink to="/auth/login" class="text-blue-400 hover:text-blue-300">返回登录</NuxtLink>
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { authApi } from '~/services/auth-api'

const step = ref(1)
const email = ref('')
const verifyCode = ref('')
const newPassword = ref('')
const confirmPassword = ref('')
const isLoading = ref(false)
const error = ref('')

const sendCode = async () => {
  if (!email.value) {
    error.value = '请输入邮箱地址'
    return
  }
  
  try {
    isLoading.value = true
    error.value = ''
    
    const response = await authApi.sendEmailVerification(email.value)
    if (response.code === 0) {
      step.value = 2
    } else {
      error.value = response.message || '发送验证码失败'
    }
  } catch (err: any) {
    error.value = err.message || '发送验证码失败，请重试'
  } finally {
    isLoading.value = false
  }
}

const resetPassword = async () => {
  // 基本验证
  if (!verifyCode.value || !newPassword.value || !confirmPassword.value) {
    error.value = '请填写所有字段'
    return
  }
  
  if (newPassword.value.length < 8) {
    error.value = '密码至少需要8位'
    return
  }
  
  if (newPassword.value !== confirmPassword.value) {
    error.value = '两次密码输入不一致'
    return
  }
  
  try {
    isLoading.value = true
    error.value = ''
    
    const response = await authApi.resetPassword({
      email: email.value,
      code: verifyCode.value,
      newPassword: newPassword.value
    })
    
    if (response.code === 0) {
      step.value = 3
    } else {
      error.value = response.message || '重置密码失败'
    }
  } catch (err: any) {
    error.value = err.message || '重置密码失败，请重试'
  } finally {
    isLoading.value = false
  }
}
</script> 