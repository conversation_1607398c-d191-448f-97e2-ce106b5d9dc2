<template>
  <div class="battle-opening-records">
    <!-- 记录展示头部 -->
    <div class="records-header">
      <div class="header-left">
        <Icon name="heroicons:trophy" class="w-5 h-5 text-yellow-400" />
        <h4 class="records-title">{{ t('battle.opening_records.title') }}</h4>
        <div class="records-count">{{ filteredRecords.length }}</div>
      </div>
      
      <!-- 轮次过滤器 -->
      <div class="round-filter">
        <button
          v-for="round in availableRounds"
          :key="round"
          @click="selectedRound = round"
          :class="['round-filter-btn', {
            'active': selectedRound === round,
            'completed': isRoundCompleted(round),
            'current': isCurrentRound(round)
          }]"
        >
          <span class="round-number">{{ round }}</span>
          <div v-if="isCurrentRound(round)" class="current-indicator"></div>
        </button>
        
        <button
          @click="selectedRound = null"
          :class="['round-filter-btn all-rounds', {
            'active': selectedRound === null
          }]"
        >
          {{ t('battle.opening_records.all_rounds') }}
        </button>
      </div>
    </div>

    <!-- 记录网格 -->
    <div class="records-grid" v-if="filteredRecords.length > 0">
      <TransitionGroup
        name="record-item"
        tag="div"
        class="records-container"
      >
        <div
          v-for="record in filteredRecords"
          :key="`${record.playerId}-${record.round}-${record.timestamp}`"
          :class="['record-item', {
            'new-record': isNewRecord(record),
            'winning-item': isWinningItem(record),
            'rare-item': isRareItem(record)
          }]"
        >
          <!-- 轮次标记 -->
          <div class="round-badge">
            <span class="round-text">R{{ record.round }}</span>
          </div>
          
          <!-- 玩家头像 -->
          <div class="player-avatar">
            <img 
              :src="getPlayerAvatar(record.playerId)" 
              :alt="getPlayerName(record.playerId)"
              class="avatar-image"
              @error="handleImageError"
            />
            <div class="player-indicator" :style="{ background: getPlayerColor(record.playerId) }"></div>
          </div>
          
          <!-- 物品展示 -->
          <div class="item-display">
            <div class="item-image-container">
              <img 
                :src="record.item.image || '/demo/item1.png'"
                :alt="getItemName(record.item)"
                class="item-image"
                @error="handleImageError"
              />
              
              <!-- 稀有度指示条 -->
              <div 
                class="rarity-indicator"
                :style="{ background: record.item.rarity_color || '#6b7280' }"
              ></div>
              
              <!-- StatTrak 标记 -->
              <div v-if="isStatTrakItem(record.item)" class="stattrak-badge">
                <Icon name="heroicons:bolt" class="w-3 h-3" />
                <span>ST</span>
              </div>
            </div>
            
            <!-- 物品信息 -->
            <div class="item-info">
              <div class="item-name">{{ getItemName(record.item) }}</div>
              <div class="item-details">
                <span class="item-condition">{{ getItemCondition(record.item) }}</span>
                <span class="item-price">${{ formatPrice(record.item.price || 0) }}</span>
              </div>
            </div>
          </div>
          
          <!-- 新记录动画效果 -->
          <div v-if="isNewRecord(record)" class="new-record-effect">
            <div class="shine-wave"></div>
            <div class="particle-burst">
              <div v-for="i in 6" :key="i" class="particle" :style="getParticleStyle(i)"></div>
            </div>
          </div>
          
          <!-- 时间戳 -->
          <div class="record-timestamp">
            {{ formatTimestamp(record.timestamp) }}
          </div>
        </div>
      </TransitionGroup>
    </div>
    
    <!-- 空状态 -->
    <div v-else class="empty-records">
      <div class="empty-icon">
        <Icon name="heroicons:cube-transparent" class="w-12 h-12 text-gray-400" />
      </div>
      <div class="empty-title">{{ t('battle.opening_records.no_records') }}</div>
      <div class="empty-description">{{ t('battle.opening_records.no_records_desc') }}</div>
    </div>
    
    <!-- 统计信息 -->
    <div v-if="filteredRecords.length > 0" class="records-stats">
      <div class="stat-item">
        <Icon name="heroicons:trophy" class="w-4 h-4 text-yellow-400" />
        <span class="stat-label">{{ t('battle.opening_records.total_value') }}</span>
        <span class="stat-value">${{ formatPrice(totalValue) }}</span>
      </div>
      
      <div class="stat-item">
        <Icon name="heroicons:star" class="w-4 h-4 text-purple-400" />
        <span class="stat-label">{{ t('battle.opening_records.rare_items') }}</span>
        <span class="stat-value">{{ rareItemsCount }}</span>
      </div>
      
      <div class="stat-item">
        <Icon name="heroicons:fire" class="w-4 h-4 text-red-400" />
        <span class="stat-label">{{ t('battle.opening_records.best_item') }}</span>
        <span class="stat-value">${{ formatPrice(bestItemValue) }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useBattleRoundAnimationManager } from '~/composables/useBattleRoundAnimationManager'

// 🎯 国际化
const { t } = useI18n()

// 🎯 Props 定义
interface OpeningRecord {
  playerId: string
  playerIndex: number
  round: number
  item: any
  timestamp: number
  recordTimestamp?: number
}

interface Props {
  records: OpeningRecord[]
  players: any[]
  currentRound: number
  totalRounds: number
  maxDisplayRecords?: number
}

const props = withDefaults(defineProps<Props>(), {
  maxDisplayRecords: 50
})

// 🎯 状态管理
const roundAnimationManager = useBattleRoundAnimationManager()
const selectedRound = ref<number | null>(null)
const newRecordTimestamps = ref<Set<number>>(new Set())

// 🎯 计算属性
const availableRounds = computed(() => {
  const rounds = new Set<number>()
  props.records.forEach(record => rounds.add(record.round))
  return Array.from(rounds).sort((a, b) => a - b)
})

const filteredRecords = computed(() => {
  let filtered = [...props.records]
  
  // 轮次过滤
  if (selectedRound.value !== null) {
    filtered = filtered.filter(record => record.round === selectedRound.value)
  }
  
  // 按时间排序（最新的在前）
  filtered.sort((a, b) => (b.timestamp || 0) - (a.timestamp || 0))
  
  // 限制显示数量
  return filtered.slice(0, props.maxDisplayRecords)
})

const totalValue = computed(() => {
  return filteredRecords.value.reduce((sum, record) => {
    return sum + (record.item.price || 0)
  }, 0)
})

const rareItemsCount = computed(() => {
  return filteredRecords.value.filter(record => isRareItem(record)).length
})

const bestItemValue = computed(() => {
  const values = filteredRecords.value.map(record => record.item.price || 0)
  return values.length > 0 ? Math.max(...values) : 0
})

// 🎯 状态判断函数
const isRoundCompleted = (round: number): boolean => {
  return round < props.currentRound
}

const isCurrentRound = (round: number): boolean => {
  return round === props.currentRound
}

const isNewRecord = (record: OpeningRecord): boolean => {
  const recordTime = record.recordTimestamp || record.timestamp
  return newRecordTimestamps.value.has(recordTime)
}

const isWinningItem = (record: OpeningRecord): boolean => {
  const itemValue = record.item.price || 0
  const averageValue = totalValue.value / filteredRecords.value.length
  return itemValue > averageValue * 1.5
}

const isRareItem = (record: OpeningRecord): boolean => {
  const rarity = record.item.rarity_name || ''
  const rareRarities = ['covert', 'classified', 'restricted', 'extraordinary', 'exceedingly rare']
  return rareRarities.some(rare => rarity.toLowerCase().includes(rare))
}

const isStatTrakItem = (item: any): boolean => {
  const name = item.name || ''
  return name.toLowerCase().includes('stattrak')
}

// 🎯 数据获取函数
const getPlayerAvatar = (playerId: string): string => {
  const player = props.players.find(p => p.user?.uid === playerId)
  return player?.user?.profile?.avatar || '/demo/avatar1.png'
}

const getPlayerName = (playerId: string): string => {
  const player = props.players.find(p => p.user?.uid === playerId)
  return player?.user?.profile?.nickname || `Player ${playerId}`
}

const getPlayerColor = (playerId: string): string => {
  const colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4']
  const index = parseInt(playerId.slice(-1)) || 0
  return colors[index % colors.length]
}

const getItemName = (item: any): string => {
  return item.name_zh_hans || item.name_en || item.name || 'Unknown Item'
}

const getItemCondition = (item: any): string => {
  return item.exterior || item.condition || 'Factory New'
}

// 🎯 格式化函数
const formatPrice = (price: number): string => {
  return price.toFixed(2)
}

const formatTimestamp = (timestamp: number): string => {
  const now = Date.now()
  const diff = now - timestamp
  
  if (diff < 60000) { // 小于1分钟
    return t('battle.opening_records.just_now')
  } else if (diff < 3600000) { // 小于1小时
    const minutes = Math.floor(diff / 60000)
    return t('battle.opening_records.minutes_ago', { count: minutes })
  } else {
    const hours = Math.floor(diff / 3600000)
    return t('battle.opening_records.hours_ago', { count: hours })
  }
}

// 🎯 动画效果函数
const getParticleStyle = (index: number) => {
  const angle = (index / 6) * 360
  const distance = 20 + Math.random() * 10
  return {
    '--angle': `${angle}deg`,
    '--distance': `${distance}px`,
    '--delay': `${index * 0.1}s`
  }
}

// 🎯 错误处理
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  if (img.src.includes('avatar')) {
    img.src = '/demo/avatar1.png'
  } else {
    img.src = '/demo/item1.png'
  }
}

// 🎯 新记录检测
watch(() => props.records.length, (newLength, oldLength) => {
  if (newLength > oldLength && oldLength > 0) {
    // 新增记录，标记为新记录
    const newRecords = props.records.slice(oldLength)
    newRecords.forEach(record => {
      const recordTime = record.recordTimestamp || record.timestamp
      newRecordTimestamps.value.add(recordTime)
      
      // 3秒后移除新记录标记
      setTimeout(() => {
        newRecordTimestamps.value.delete(recordTime)
      }, 3000)
    })
  }
})

// 🎯 自动切换到当前轮次
watch(() => props.currentRound, (newRound) => {
  if (selectedRound.value === null || selectedRound.value < newRound - 1) {
    selectedRound.value = newRound
  }
})

onMounted(() => {
  // 初始化时选择当前轮次
  if (props.currentRound > 0) {
    selectedRound.value = props.currentRound
  }
})
</script>

<style lang="scss" scoped>
.battle-opening-records {
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 1.5rem;
}

.records-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.records-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--color-white);
  margin: 0;
}

.records-count {
  background: var(--color-primary);
  color: var(--color-white);
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  min-width: 24px;
  text-align: center;
}

.round-filter {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.round-filter-btn {
  position: relative;
  padding: 0.5rem 0.75rem;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-md);
  background: rgba(255, 255, 255, 0.05);
  color: var(--color-gray-300);
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
  
  &:hover {
    border-color: var(--color-primary);
    background: rgba(var(--color-primary-rgb), 0.1);
  }
  
  &.active {
    border-color: var(--color-primary);
    background: var(--color-primary);
    color: var(--color-white);
  }
  
  &.completed {
    border-color: var(--color-green-500);
    
    &.active {
      background: var(--color-green-500);
    }
  }
  
  &.current {
    border-color: var(--color-yellow-400);
    animation: current-round-pulse 2s infinite ease-in-out;
    
    &.active {
      background: var(--color-yellow-400);
      color: var(--color-gray-900);
    }
  }
  
  &.all-rounds {
    font-size: 0.875rem;
  }
}

.current-indicator {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  background: var(--color-yellow-400);
  border-radius: 50%;
  animation: indicator-pulse 1.5s infinite ease-in-out;
}

.records-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.record-item {
  position: relative;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  padding: 1rem;
  transition: all 0.3s ease;
  overflow: hidden;
  
  &:hover {
    transform: translateY(-2px);
    border-color: var(--color-primary);
    box-shadow: 0 8px 25px rgba(var(--color-primary-rgb), 0.15);
  }
  
  &.new-record {
    border-color: var(--color-yellow-400);
    box-shadow: 0 0 20px rgba(251, 191, 36, 0.3);
    animation: new-record-glow 2s ease-in-out;
  }
  
  &.winning-item {
    border-color: var(--color-green-400);
    box-shadow: 0 0 15px rgba(34, 197, 94, 0.2);
  }
  
  &.rare-item {
    border-color: var(--color-purple-400);
    box-shadow: 0 0 15px rgba(147, 51, 234, 0.2);
  }
}

.round-badge {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: rgba(0, 0, 0, 0.8);
  color: var(--color-white);
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
}

.player-avatar {
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.avatar-image {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.player-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid var(--color-white);
}

.item-display {
  display: flex;
  gap: 0.75rem;
  align-items: flex-start;
}

.item-image-container {
  position: relative;
  flex-shrink: 0;
}

.item-image {
  width: 60px;
  height: 45px;
  object-fit: contain;
  border-radius: var(--radius-sm);
  background: rgba(255, 255, 255, 0.05);
  padding: 0.25rem;
}

.rarity-indicator {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  border-radius: 0 0 var(--radius-sm) var(--radius-sm);
}

.stattrak-badge {
  position: absolute;
  top: -2px;
  right: -2px;
  background: var(--color-secondary);
  color: var(--color-white);
  font-size: 0.625rem;
  font-weight: 600;
  padding: 0.125rem 0.25rem;
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  gap: 0.125rem;
}

.item-info {
  flex: 1;
  min-width: 0;
}

.item-name {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--color-white);
  margin-bottom: 0.25rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-details {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
}

.item-condition {
  color: var(--color-gray-400);
}

.item-price {
  color: var(--color-green-400);
  font-weight: 600;
}

.new-record-effect {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
  border-radius: var(--radius-lg);
}

.shine-wave {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(251, 191, 36, 0.3),
    transparent
  );
  animation: shine-wave 2s ease-in-out;
}

.particle-burst {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: var(--color-yellow-400);
  border-radius: 50%;
  animation: particle-burst 1s ease-out var(--delay, 0s);
}

.record-timestamp {
  position: absolute;
  bottom: 0.5rem;
  left: 0.5rem;
  font-size: 0.625rem;
  color: var(--color-gray-500);
}

.empty-records {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--color-gray-400);
}

.empty-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 1rem 0 0.5rem;
  color: var(--color-white);
}

.records-stats {
  display: flex;
  justify-content: space-around;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  
  .stat-label {
    color: var(--color-gray-300);
    font-weight: 500;
  }
  
  .stat-value {
    color: var(--color-white);
    font-weight: 600;
  }
}

// 🎯 动画定义
@keyframes current-round-pulse {
  0%, 100% { box-shadow: 0 0 0 0 rgba(251, 191, 36, 0.4); }
  50% { box-shadow: 0 0 0 8px rgba(251, 191, 36, 0); }
}

@keyframes indicator-pulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.7; }
}

@keyframes new-record-glow {
  0%, 100% { box-shadow: 0 0 20px rgba(251, 191, 36, 0.3); }
  50% { box-shadow: 0 0 30px rgba(251, 191, 36, 0.6); }
}

@keyframes shine-wave {
  0% { left: -100%; }
  100% { left: 100%; }
}

@keyframes particle-burst {
  0% {
    transform: translate(
      calc(var(--distance, 20px) * cos(var(--angle, 0deg))),
      calc(var(--distance, 20px) * sin(var(--angle, 0deg)))
    ) scale(0);
    opacity: 1;
  }
  100% {
    transform: translate(
      calc(var(--distance, 20px) * cos(var(--angle, 0deg)) * 2),
      calc(var(--distance, 20px) * sin(var(--angle, 0deg)) * 2)
    ) scale(1);
    opacity: 0;
  }
}

// 🎯 过渡动画
.record-item-enter-active {
  transition: all 0.6s ease;
}

.record-item-enter-from {
  opacity: 0;
  transform: translateY(20px) scale(0.9);
}

.record-item-leave-active {
  transition: all 0.3s ease;
}

.record-item-leave-to {
  opacity: 0;
  transform: translateY(-20px) scale(0.9);
}

// 🎯 响应式设计
@media (max-width: 768px) {
  .records-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .round-filter {
    width: 100%;
    justify-content: flex-start;
  }
  
  .records-container {
    grid-template-columns: 1fr;
  }
  
  .records-stats {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .stat-item {
    justify-content: space-between;
  }
}
</style>