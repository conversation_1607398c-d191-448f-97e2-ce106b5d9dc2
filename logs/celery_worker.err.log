[2025-07-18 14:44:37,899: INFO/MainProcess] Task thworker.update_case_records_cache[2ba29d5e-fd3b-4969-a849-cdb76d4da993] received
[2025-07-18 14:44:37,901: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 14:44:37,911: INFO/MainProcess] Task thworker.check_b2c_trade_state[3df06f40-7269-4899-b88e-3e5eb52f33bd] received
[2025-07-18 14:44:37,926: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 14:44:37,926: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 14:44:37,942: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[3df06f40-7269-4899-b88e-3e5eb52f33bd] succeeded in 0.016960342996753752s: 'Success: B2C trade state checked'
[2025-07-18 14:44:37,949: INFO/MainProcess] Task thworker.update_case_records_cache[ba2bbecf-6008-4f29-a3ed-9cfe22945032] received
[2025-07-18 14:44:37,950: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 14:44:37,950: INFO/ForkPoolWorker-1] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 14:44:37,964: INFO/ForkPoolWorker-1] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 14:44:37,964: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 14:44:37,970: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[ba2bbecf-6008-4f29-a3ed-9cfe22945032] succeeded in 0.020668020995799452s: 'Success: Updated 100 case records cache'
[2025-07-18 14:44:47,344: ERROR/ForkPoolWorker-2] 更新 CaseRecord 缓存时发生错误: (1054, "Unknown column 'box_case.name_zh' in 'field list'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 173, in __get__
    rel_obj = self.field.get_cached_value(instance)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/mixins.py", line 15, in get_cached_value
    return instance._state.fields_cache[cache_name]
KeyError: 'case'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'box_case.name_zh' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/app/box/tasks.py", line 58, in update_case_records_cache
    batch_data = CaseRecordSerializer(batch_records, many=True).data
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 795, in data
    ret = super().data
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 249, in data
    self._data = self.to_representation(self.instance)
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 713, in to_representation
    return [
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 714, in <listcomp>
    self.child.to_representation(item) for item in iterable
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 538, in to_representation
    ret[field.field_name] = field.to_representation(attribute)
  File "/usr/local/lib/python3.8/site-packages/rest_framework/fields.py", line 1872, in to_representation
    return method(value)
  File "/app/box/serializers.py", line 117, in get_case_info
    return CaseSerializer(obj.case, fields=('id', 'case_key', 'name', 'name_en', 'name_zh_hans', 'cover', 'price', 'open_count')).data
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 187, in __get__
    rel_obj = self.get_object(instance)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 154, in get_object
    return qs.get(self.field.get_reverse_related_filter(instance))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 431, in get
    num = len(clone)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 262, in __len__
    self._fetch_all()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 1324, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 51, in __iter__
    results = compiler.execute_sql(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/compiler.py", line 1175, in execute_sql
    cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.OperationalError: (1054, "Unknown column 'box_case.name_zh' in 'field list'")
[2025-07-18 14:44:47,344: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 14:44:47,346: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[2ba29d5e-fd3b-4969-a849-cdb76d4da993] succeeded in 9.445320586004527s: 'Success: Updated 100 case records cache'
[2025-07-18 14:49:37,865: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[c57afc6a-b8cf-4012-b127-55883de8c032] received
[2025-07-18 14:49:37,866: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 14:49:37,870: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 14:49:37,872: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[c57afc6a-b8cf-4012-b127-55883de8c032] succeeded in 0.006330792006338015s: None
[2025-07-18 14:49:37,938: INFO/MainProcess] Task thworker.check_b2c_trade_state[10ff7151-2ecf-437d-a4b4-08ab532a741f] received
[2025-07-18 14:49:37,939: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 14:49:37,940: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 14:49:37,941: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 14:49:37,942: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[10ff7151-2ecf-437d-a4b4-08ab532a741f] succeeded in 0.0030760689987801015s: 'Success: B2C trade state checked'
[2025-07-18 14:49:38,085: INFO/MainProcess] Task thworker.update_case_records_cache[e6aa5fc5-fa60-495e-a5a5-a3cb2e8f52db] received
[2025-07-18 14:49:38,087: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 14:49:38,095: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 14:49:38,095: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 14:49:38,098: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[e6aa5fc5-fa60-495e-a5a5-a3cb2e8f52db] succeeded in 0.011972075008088723s: 'Success: Updated 100 case records cache'
[2025-07-18 14:54:38,141: INFO/MainProcess] Task thworker.update_case_records_cache[660ad45c-3098-4e32-91ce-64cc4e18a44e] received
[2025-07-18 14:54:38,142: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 14:54:38,192: INFO/MainProcess] Task thworker.check_b2c_trade_state[7bbe7343-272a-4d94-90df-769b330df454] received
[2025-07-18 14:54:38,193: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 14:54:38,194: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 14:54:38,210: INFO/ForkPoolWorker-1] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 14:54:38,239: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[7bbe7343-272a-4d94-90df-769b330df454] succeeded in 0.046412556985160336s: 'Success: B2C trade state checked'
[2025-07-18 14:54:47,798: ERROR/ForkPoolWorker-2] 更新 CaseRecord 缓存时发生错误: (1054, "Unknown column 'box_case.name_zh' in 'field list'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 173, in __get__
    rel_obj = self.field.get_cached_value(instance)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/mixins.py", line 15, in get_cached_value
    return instance._state.fields_cache[cache_name]
KeyError: 'case'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'box_case.name_zh' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/app/box/tasks.py", line 58, in update_case_records_cache
    batch_data = CaseRecordSerializer(batch_records, many=True).data
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 795, in data
    ret = super().data
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 249, in data
    self._data = self.to_representation(self.instance)
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 713, in to_representation
    return [
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 714, in <listcomp>
    self.child.to_representation(item) for item in iterable
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 538, in to_representation
    ret[field.field_name] = field.to_representation(attribute)
  File "/usr/local/lib/python3.8/site-packages/rest_framework/fields.py", line 1872, in to_representation
    return method(value)
  File "/app/box/serializers.py", line 117, in get_case_info
    return CaseSerializer(obj.case, fields=('id', 'case_key', 'name', 'name_en', 'name_zh_hans', 'cover', 'price', 'open_count')).data
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 187, in __get__
    rel_obj = self.get_object(instance)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 154, in get_object
    return qs.get(self.field.get_reverse_related_filter(instance))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 431, in get
    num = len(clone)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 262, in __len__
    self._fetch_all()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 1324, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 51, in __iter__
    results = compiler.execute_sql(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/compiler.py", line 1175, in execute_sql
    cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.OperationalError: (1054, "Unknown column 'box_case.name_zh' in 'field list'")
[2025-07-18 14:54:47,799: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 14:54:47,801: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[660ad45c-3098-4e32-91ce-64cc4e18a44e] succeeded in 9.65849095498561s: 'Success: Updated 100 case records cache'
[2025-07-18 14:59:38,187: INFO/MainProcess] Task thworker.update_case_records_cache[94359da9-7142-4578-b7cb-15948a7b5dd2] received
[2025-07-18 14:59:38,189: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 14:59:38,212: INFO/MainProcess] Task thworker.update_case_records_cache[57eca445-a826-4336-aaec-72f2385af33e] received
[2025-07-18 14:59:38,221: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 14:59:38,223: INFO/ForkPoolWorker-1] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 14:59:38,223: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 14:59:38,235: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[57eca445-a826-4336-aaec-72f2385af33e] succeeded in 0.014231871988158673s: 'Success: Updated 100 case records cache'
[2025-07-18 14:59:50,321: ERROR/ForkPoolWorker-2] 更新 CaseRecord 缓存时发生错误: (1054, "Unknown column 'box_case.name_zh' in 'field list'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 173, in __get__
    rel_obj = self.field.get_cached_value(instance)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/mixins.py", line 15, in get_cached_value
    return instance._state.fields_cache[cache_name]
KeyError: 'case'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'box_case.name_zh' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/app/box/tasks.py", line 58, in update_case_records_cache
    batch_data = CaseRecordSerializer(batch_records, many=True).data
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 795, in data
    ret = super().data
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 249, in data
    self._data = self.to_representation(self.instance)
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 713, in to_representation
    return [
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 714, in <listcomp>
    self.child.to_representation(item) for item in iterable
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 538, in to_representation
    ret[field.field_name] = field.to_representation(attribute)
  File "/usr/local/lib/python3.8/site-packages/rest_framework/fields.py", line 1872, in to_representation
    return method(value)
  File "/app/box/serializers.py", line 117, in get_case_info
    return CaseSerializer(obj.case, fields=('id', 'case_key', 'name', 'name_en', 'name_zh_hans', 'cover', 'price', 'open_count')).data
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 187, in __get__
    rel_obj = self.get_object(instance)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 154, in get_object
    return qs.get(self.field.get_reverse_related_filter(instance))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 431, in get
    num = len(clone)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 262, in __len__
    self._fetch_all()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 1324, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 51, in __iter__
    results = compiler.execute_sql(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/compiler.py", line 1175, in execute_sql
    cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.OperationalError: (1054, "Unknown column 'box_case.name_zh' in 'field list'")
[2025-07-18 14:59:50,321: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 14:59:50,333: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[94359da9-7142-4578-b7cb-15948a7b5dd2] succeeded in 12.143739115999779s: 'Success: Updated 100 case records cache'
[2025-07-18 15:00:00,013: INFO/MainProcess] Task thworker.hourly_lottery[1fce8b0f-0ba8-4d4d-a3a0-98bd55ab8218] received
[2025-07-18 15:00:00,014: INFO/ForkPoolWorker-2] ------------------------ 整点抽奖 ------------------------
[2025-07-18 15:00:00,015: INFO/ForkPoolWorker-2] [Celery] 每小时抽奖任务完成
[2025-07-18 15:00:00,017: INFO/ForkPoolWorker-2] Task thworker.hourly_lottery[1fce8b0f-0ba8-4d4d-a3a0-98bd55ab8218] succeeded in 0.0033297409827355295s: 'Success: Hourly lottery completed'
[2025-07-18 15:04:38,207: INFO/MainProcess] Task thworker.update_case_records_cache[10df4ad4-6243-424d-9347-6e1a571fe987] received
[2025-07-18 15:04:38,208: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 15:04:38,233: INFO/MainProcess] Task thworker.check_b2c_trade_state[b69b376a-9b04-4027-94b8-5faa7920adc5] received
[2025-07-18 15:04:38,234: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 15:04:38,235: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 15:04:38,237: INFO/ForkPoolWorker-1] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 15:04:38,238: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[b69b376a-9b04-4027-94b8-5faa7920adc5] succeeded in 0.004725054022856057s: 'Success: B2C trade state checked'
[2025-07-18 15:06:36,305: ERROR/ForkPoolWorker-2] 更新 CaseRecord 缓存时发生错误: (1054, "Unknown column 'box_case.name_zh' in 'field list'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 173, in __get__
    rel_obj = self.field.get_cached_value(instance)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/mixins.py", line 15, in get_cached_value
    return instance._state.fields_cache[cache_name]
KeyError: 'case'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'box_case.name_zh' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/app/box/tasks.py", line 58, in update_case_records_cache
    batch_data = CaseRecordSerializer(batch_records, many=True).data
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 795, in data
    ret = super().data
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 249, in data
    self._data = self.to_representation(self.instance)
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 713, in to_representation
    return [
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 714, in <listcomp>
    self.child.to_representation(item) for item in iterable
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 538, in to_representation
    ret[field.field_name] = field.to_representation(attribute)
  File "/usr/local/lib/python3.8/site-packages/rest_framework/fields.py", line 1872, in to_representation
    return method(value)
  File "/app/box/serializers.py", line 117, in get_case_info
    return CaseSerializer(obj.case, fields=('id', 'case_key', 'name', 'name_en', 'name_zh_hans', 'cover', 'price', 'open_count')).data
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 187, in __get__
    rel_obj = self.get_object(instance)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 154, in get_object
    return qs.get(self.field.get_reverse_related_filter(instance))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 431, in get
    num = len(clone)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 262, in __len__
    self._fetch_all()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 1324, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 51, in __iter__
    results = compiler.execute_sql(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/compiler.py", line 1175, in execute_sql
    cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.OperationalError: (1054, "Unknown column 'box_case.name_zh' in 'field list'")
[2025-07-18 15:06:36,306: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 15:06:36,307: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[10df4ad4-6243-424d-9347-6e1a571fe987] succeeded in 118.09932349898736s: 'Success: Updated 100 case records cache'
[2025-07-18 15:09:38,268: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[08dfe966-5681-47f9-ad73-af73654bbf3d] received
[2025-07-18 15:09:38,270: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 15:09:38,271: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 15:09:38,276: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[08dfe966-5681-47f9-ad73-af73654bbf3d] succeeded in 0.006222575000720099s: None
[2025-07-18 15:09:38,281: INFO/MainProcess] Task thworker.update_case_records_cache[32b48367-b3d4-48a4-9062-2c897324e9a1] received
[2025-07-18 15:09:38,284: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 15:09:38,287: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 15:09:38,287: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 15:09:38,293: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[32b48367-b3d4-48a4-9062-2c897324e9a1] succeeded in 0.00920878499164246s: 'Success: Updated 100 case records cache'
[2025-07-18 15:09:38,295: INFO/MainProcess] Task thworker.check_b2c_trade_state[0db1bfd9-5aca-4c54-99bd-b9cd06b182c3] received
[2025-07-18 15:09:38,296: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 15:09:38,296: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 15:09:38,297: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 15:09:38,298: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[0db1bfd9-5aca-4c54-99bd-b9cd06b182c3] succeeded in 0.002373140014242381s: 'Success: B2C trade state checked'
[2025-07-18 15:14:38,337: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[72dffd09-1a53-4b10-809d-2be791f1041d] received
[2025-07-18 15:14:38,338: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 15:14:38,350: ERROR/ForkPoolWorker-2] 清理 RollRoomBet 记录时发生错误: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
[2025-07-18 15:14:38,353: ERROR/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[72dffd09-1a53-4b10-809d-2be791f1041d] raised unexpected: OperationalError(1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
  File "/app/roll/tasks.py", line 37, in cleanup_duplicate_roll_room_bets
    latest_room = RollRoom.objects.filter(enable=True).order_by('-create_time').first()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 674, in first
    for obj in (self if self.ordered else self.order_by('pk'))[:1]:
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 280, in __iter__
    self._fetch_all()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 1324, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 51, in __iter__
    results = compiler.execute_sql(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/compiler.py", line 1175, in execute_sql
    cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.OperationalError: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
[2025-07-18 15:14:38,362: INFO/MainProcess] Task thworker.update_case_records_cache[b951fea9-4519-4875-b039-dcb27b793127] received
[2025-07-18 15:14:38,364: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 15:14:38,368: INFO/MainProcess] Task thworker.update_case_records_cache[757637cf-9585-4145-adc9-42ad6e4a6c13] received
[2025-07-18 15:14:38,368: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 15:14:38,369: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 15:14:38,370: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 15:14:38,371: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[757637cf-9585-4145-adc9-42ad6e4a6c13] succeeded in 0.003040261013666168s: 'Success: Updated 100 case records cache'
[2025-07-18 15:15:28,432: INFO/ForkPoolWorker-1] 没有新的 CaseRecord 更新。
[2025-07-18 15:15:28,432: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 15:15:28,436: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[b951fea9-4519-4875-b039-dcb27b793127] succeeded in 50.072432439978s: 'Success: Updated 100 case records cache'
[2025-07-18 15:19:38,367: INFO/MainProcess] Task thworker.check_b2c_trade_state[47a87c3a-88de-4d57-93c7-16cd2c79a416] received
[2025-07-18 15:19:38,369: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 15:19:38,369: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 15:19:38,371: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 15:19:38,373: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[47a87c3a-88de-4d57-93c7-16cd2c79a416] succeeded in 0.004604760993970558s: 'Success: B2C trade state checked'
[2025-07-18 15:19:38,379: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[e93cb0f4-791f-4924-b083-cc053c49fe32] received
[2025-07-18 15:19:38,382: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 15:19:38,383: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 15:19:38,384: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[e93cb0f4-791f-4924-b083-cc053c49fe32] succeeded in 0.002392969006905332s: None
[2025-07-18 15:19:38,388: INFO/MainProcess] Task thworker.update_case_records_cache[815a74b3-19a8-44f1-8550-dfd1ee1037e6] received
[2025-07-18 15:19:38,388: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 15:21:56,270: ERROR/ForkPoolWorker-2] 更新 CaseRecord 缓存时发生错误: (1054, "Unknown column 'box_case.name_zh' in 'field list'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 173, in __get__
    rel_obj = self.field.get_cached_value(instance)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/mixins.py", line 15, in get_cached_value
    return instance._state.fields_cache[cache_name]
KeyError: 'case'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'box_case.name_zh' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/app/box/tasks.py", line 58, in update_case_records_cache
    batch_data = CaseRecordSerializer(batch_records, many=True).data
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 795, in data
    ret = super().data
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 249, in data
    self._data = self.to_representation(self.instance)
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 713, in to_representation
    return [
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 714, in <listcomp>
    self.child.to_representation(item) for item in iterable
  File "/usr/local/lib/python3.8/site-packages/rest_framework/serializers.py", line 538, in to_representation
    ret[field.field_name] = field.to_representation(attribute)
  File "/usr/local/lib/python3.8/site-packages/rest_framework/fields.py", line 1872, in to_representation
    return method(value)
  File "/app/box/serializers.py", line 117, in get_case_info
    return CaseSerializer(obj.case, fields=('id', 'case_key', 'name', 'name_en', 'name_zh', 'cover', 'price', 'open_count')).data
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 187, in __get__
    rel_obj = self.get_object(instance)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 154, in get_object
    return qs.get(self.field.get_reverse_related_filter(instance))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 431, in get
    num = len(clone)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 262, in __len__
    self._fetch_all()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 1324, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 51, in __iter__
    results = compiler.execute_sql(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/compiler.py", line 1175, in execute_sql
    cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.OperationalError: (1054, "Unknown column 'box_case.name_zh' in 'field list'")
[2025-07-18 15:21:56,271: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 15:21:56,273: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[815a74b3-19a8-44f1-8550-dfd1ee1037e6] succeeded in 137.88501366699347s: 'Success: Updated 100 case records cache'
[2025-07-18 15:24:38,439: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[bcfb9a65-56f8-4be1-8a86-c02956fde581] received
[2025-07-18 15:24:38,440: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 15:24:38,448: INFO/MainProcess] Task thworker.update_case_records_cache[de1fa96e-2872-408c-95c0-cdb53f0f2273] received
[2025-07-18 15:24:38,449: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 15:24:38,456: ERROR/ForkPoolWorker-2] 清理 RollRoomBet 记录时发生错误: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
[2025-07-18 15:24:38,460: ERROR/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[bcfb9a65-56f8-4be1-8a86-c02956fde581] raised unexpected: OperationalError(1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
  File "/app/roll/tasks.py", line 37, in cleanup_duplicate_roll_room_bets
    latest_room = RollRoom.objects.filter(enable=True).order_by('-create_time').first()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 674, in first
    for obj in (self if self.ordered else self.order_by('pk'))[:1]:
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 280, in __iter__
    self._fetch_all()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 1324, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 51, in __iter__
    results = compiler.execute_sql(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/compiler.py", line 1175, in execute_sql
    cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.OperationalError: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
[2025-07-18 15:25:34,534: INFO/ForkPoolWorker-1] 没有新的 CaseRecord 更新。
[2025-07-18 15:25:34,535: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 15:25:34,538: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[de1fa96e-2872-408c-95c0-cdb53f0f2273] succeeded in 56.088884762022644s: 'Success: Updated 100 case records cache'
[2025-07-18 15:29:38,561: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[1f0c000c-1106-44b5-8102-995471f5164f] received
[2025-07-18 15:29:38,562: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 15:29:38,570: ERROR/ForkPoolWorker-2] 清理 RollRoomBet 记录时发生错误: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
[2025-07-18 15:29:38,572: INFO/MainProcess] Task thworker.update_case_records_cache[1b06d323-3d09-498c-9d3e-8555b94ff5f2] received
[2025-07-18 15:29:38,573: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 15:29:38,575: ERROR/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[1f0c000c-1106-44b5-8102-995471f5164f] raised unexpected: OperationalError(1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/app/trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
  File "/usr/local/lib/python3.8/site-packages/celery/app/trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
  File "/app/roll/tasks.py", line 37, in cleanup_duplicate_roll_room_bets
    latest_room = RollRoom.objects.filter(enable=True).order_by('-create_time').first()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 674, in first
    for obj in (self if self.ordered else self.order_by('pk'))[:1]:
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 280, in __iter__
    self._fetch_all()
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 1324, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "/usr/local/lib/python3.8/site-packages/django/db/models/query.py", line 51, in __iter__
    results = compiler.execute_sql(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)
  File "/usr/local/lib/python3.8/site-packages/django/db/models/sql/compiler.py", line 1175, in execute_sql
    cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/usr/local/lib/python3.8/site-packages/django/db/backends/mysql/base.py", line 73, in execute
    return self.cursor.execute(query, args)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/usr/local/lib/python3.8/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/usr/local/lib/python3.8/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/usr/local/lib/python3.8/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/usr/local/lib/python3.8/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.OperationalError: (1054, "Unknown column 'roll_rollroom.name_zh' in 'field list'")
[2025-07-18 15:29:38,582: INFO/MainProcess] Task thworker.check_b2c_trade_state[90843624-a1bf-47bd-9b90-d3fe0cb234b1] received
[2025-07-18 15:29:38,584: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 15:29:38,584: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 15:29:38,586: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 15:29:38,587: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[90843624-a1bf-47bd-9b90-d3fe0cb234b1] succeeded in 0.004374547017505392s: 'Success: B2C trade state checked'
[2025-07-18 15:34:38,656: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[1a1e39e8-0c12-4516-b250-f1a9d500a535] received
[2025-07-18 15:34:38,656: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 15:34:38,657: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 15:34:38,659: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[1a1e39e8-0c12-4516-b250-f1a9d500a535] succeeded in 0.003149193013086915s: None
[2025-07-18 15:34:38,664: INFO/MainProcess] Task thworker.check_b2c_trade_state[f005e9b0-6c5a-46b3-87f5-43adfcc8595b] received
[2025-07-18 15:34:38,666: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 15:34:38,667: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 15:34:38,667: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 15:34:38,668: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[f005e9b0-6c5a-46b3-87f5-43adfcc8595b] succeeded in 0.0022265159932430834s: 'Success: B2C trade state checked'
[2025-07-18 15:34:41,191: ERROR/MainProcess] Process 'ForkPoolWorker-2' pid:36 exited with 'signal 15 (SIGTERM)'
[2025-07-18 15:34:41,712: ERROR/MainProcess] Process 'ForkPoolWorker-1' pid:35 exited with 'signal 15 (SIGTERM)'
[2025-07-18 15:34:41,734: ERROR/MainProcess] Task handler raised error: WorkerLostError('Worker exited prematurely: signal 15 (SIGTERM) Job: 106.')
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/billiard/pool.py", line 1265, in mark_as_worker_lost
    raise WorkerLostError(
billiard.einfo.ExceptionWithTraceback: 
"""
Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/celery/worker/worker.py", line 203, in start
    self.blueprint.start(self)
  File "/usr/local/lib/python3.8/site-packages/celery/bootsteps.py", line 116, in start
    step.start(parent)
  File "/usr/local/lib/python3.8/site-packages/celery/bootsteps.py", line 365, in start
    return self.obj.start()
  File "/usr/local/lib/python3.8/site-packages/celery/worker/consumer/consumer.py", line 341, in start
    blueprint.start(self)
  File "/usr/local/lib/python3.8/site-packages/celery/bootsteps.py", line 116, in start
    step.start(parent)
  File "/usr/local/lib/python3.8/site-packages/celery/worker/consumer/consumer.py", line 772, in start
    c.loop(*c.loop_args())
  File "/usr/local/lib/python3.8/site-packages/celery/worker/loops.py", line 86, in asynloop
    state.maybe_shutdown()
  File "/usr/local/lib/python3.8/site-packages/celery/worker/state.py", line 93, in maybe_shutdown
    raise WorkerShutdown(should_stop)
celery.exceptions.WorkerShutdown: 0

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.8/site-packages/billiard/pool.py", line 1265, in mark_as_worker_lost
    raise WorkerLostError(
billiard.exceptions.WorkerLostError: Worker exited prematurely: signal 15 (SIGTERM) Job: 106.
"""
DEBUG:asyncio:Using selector: EpollSelector
DEBUG:celery.utils.functional:
def update_monitor_data_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_user_active_point_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def check_b2c_trade_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def accumulate(self, *args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_recharge_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def case_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def unlock_chord(self, group_id, callback, interval=0, max_retries=1, result=2, Result=3, GroupResult=4, result_from_tuple=5, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_month_amount():
    return 1

DEBUG:celery.utils.functional:
def case_pk_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def xmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def init_system_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_new_user_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def sync_items_price_task(self):
    return 1

DEBUG:celery.utils.functional:
def xstarmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def delete_old_balance_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_domain_verify_switch(value):
    return 1

DEBUG:celery.utils.functional:
def case_bot_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def chunks(task, it, n):
    return 1

DEBUG:celery.utils.functional:
def case_pk_join_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def cleanup_duplicate_roll_room_bets():
    return 1

DEBUG:celery.utils.functional:
def group(self, tasks, result, group_id, partial_args, add_to_parent=0):
    return 1

DEBUG:celery.utils.functional:
def update_daily_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def hourly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def chain(*args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_enable_based_on_coins(min_coins, max_coins):
    return 1

DEBUG:celery.utils.functional:
def roll_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def chord(self, header, body, partial_args=0, interval=1, countdown=2, max_retries=3, eager=4, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def delete_old_log_entries(batch_size=0, days=1):
    return 1

DEBUG:celery.utils.functional:
def update_today_amount():
    return 1

DEBUG:celery.utils.functional:
def check_envelop_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def zbt_buy_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache(count, batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_enable_rule_exam(value):
    return 1

DEBUG:celery.utils.functional:
def daily_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_enable_pay_method(enable_list):
    return 1

DEBUG:celery.utils.functional:
def check_room_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_staff_balance():
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache_task(self, count=0):
    return 1

DEBUG:celery.utils.functional:
def backend_cleanup():
    return 1

DEBUG:celery.utils.functional:
def check_pendclose_trade_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_case_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_register_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def weekly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_package_items(batch_size=0):
    return 1

/usr/local/lib/python3.8/site-packages/celery/platforms.py:841: SecurityWarning: You're running the worker with superuser privileges: this is
absolutely not recommended!

Please specify a different user using the --uid option.

User information: uid=0 euid=0 gid=0 egid=0

  warnings.warn(SecurityWarning(ROOT_DISCOURAGED.format(
[2025-07-18 15:35:01,108: INFO/MainProcess] Connected to redis://localhost:6379/0
[2025-07-18 15:35:01,111: INFO/MainProcess] mingle: searching for neighbors
[2025-07-18 15:35:02,123: INFO/MainProcess] mingle: all alone
[2025-07-18 15:35:02,137: INFO/MainProcess] celery@iZj6c3ddfpik1c7yalqdt4Z ready.
[2025-07-18 15:39:38,632: INFO/MainProcess] Task thworker.update_case_records_cache[c97fa917-dd34-4dde-ab5a-d62958be1afa] received
[2025-07-18 15:39:38,635: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 15:39:38,643: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 15:39:38,643: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 15:39:38,646: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[c97fa917-dd34-4dde-ab5a-d62958be1afa] succeeded in 0.011294550989987329s: 'Success: Updated 100 case records cache'
[2025-07-18 15:39:38,652: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[0c8402f0-8967-4e82-87f3-7b0d9c3e39b7] received
[2025-07-18 15:39:38,653: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 15:39:38,677: INFO/MainProcess] Task thworker.check_b2c_trade_state[2e69d9a4-2a5d-43c2-8c87-378d527d8f97] received
[2025-07-18 15:39:38,678: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 15:39:38,680: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 15:39:38,682: INFO/ForkPoolWorker-1] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 15:39:38,683: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[2e69d9a4-2a5d-43c2-8c87-378d527d8f97] succeeded in 0.0055506289936602116s: 'Success: B2C trade state checked'
[2025-07-18 15:39:44,749: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 15:39:44,751: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[0c8402f0-8967-4e82-87f3-7b0d9c3e39b7] succeeded in 6.098657606984489s: None
DEBUG:asyncio:Using selector: EpollSelector
DEBUG:celery.utils.functional:
def update_monitor_data_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_user_active_point_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def check_b2c_trade_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def accumulate(self, *args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_recharge_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def case_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def unlock_chord(self, group_id, callback, interval=0, max_retries=1, result=2, Result=3, GroupResult=4, result_from_tuple=5, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_month_amount():
    return 1

DEBUG:celery.utils.functional:
def case_pk_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def xmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def init_system_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_new_user_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def sync_items_price_task(self):
    return 1

DEBUG:celery.utils.functional:
def xstarmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def delete_old_balance_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_domain_verify_switch(value):
    return 1

DEBUG:celery.utils.functional:
def case_bot_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def chunks(task, it, n):
    return 1

DEBUG:celery.utils.functional:
def case_pk_join_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def cleanup_duplicate_roll_room_bets():
    return 1

DEBUG:celery.utils.functional:
def group(self, tasks, result, group_id, partial_args, add_to_parent=0):
    return 1

DEBUG:celery.utils.functional:
def update_daily_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def hourly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def chain(*args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_enable_based_on_coins(min_coins, max_coins):
    return 1

DEBUG:celery.utils.functional:
def roll_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def chord(self, header, body, partial_args=0, interval=1, countdown=2, max_retries=3, eager=4, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def delete_old_log_entries(batch_size=0, days=1):
    return 1

DEBUG:celery.utils.functional:
def check_envelop_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_today_amount():
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache(count, batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_enable_rule_exam(value):
    return 1

DEBUG:celery.utils.functional:
def daily_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_enable_pay_method(enable_list):
    return 1

DEBUG:celery.utils.functional:
def check_room_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def zbt_buy_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_staff_balance():
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache_task(self, count=0):
    return 1

DEBUG:celery.utils.functional:
def backend_cleanup():
    return 1

DEBUG:celery.utils.functional:
def check_pendclose_trade_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_case_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_register_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def weekly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_package_items(batch_size=0):
    return 1

/usr/local/lib/python3.8/site-packages/celery/platforms.py:841: SecurityWarning: You're running the worker with superuser privileges: this is
absolutely not recommended!

Please specify a different user using the --uid option.

User information: uid=0 euid=0 gid=0 egid=0

  warnings.warn(SecurityWarning(ROOT_DISCOURAGED.format(
[2025-07-18 15:44:23,427: INFO/MainProcess] Connected to redis://localhost:6379/0
[2025-07-18 15:44:23,431: INFO/MainProcess] mingle: searching for neighbors
[2025-07-18 15:44:24,444: INFO/MainProcess] mingle: all alone
[2025-07-18 15:44:24,460: INFO/MainProcess] celery@iZj6c3ddfpik1c7yalqdt4Z ready.
[2025-07-18 15:44:38,666: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[359657cb-21de-4799-9849-d0344941cdcd] received
[2025-07-18 15:44:38,668: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 15:44:38,676: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 15:44:38,701: INFO/MainProcess] Task thworker.update_case_records_cache[6bc99d41-0217-4284-a430-91c6b03514c1] received
[2025-07-18 15:44:38,702: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 15:44:38,720: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[359657cb-21de-4799-9849-d0344941cdcd] succeeded in 0.05223727598786354s: None
[2025-07-18 15:44:38,723: INFO/MainProcess] Task thworker.update_case_records_cache[505a8066-8a69-4c8c-8037-0a0ddc7cb90e] received
[2025-07-18 15:44:38,724: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 15:44:38,725: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 15:44:38,725: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 15:44:38,726: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[505a8066-8a69-4c8c-8037-0a0ddc7cb90e] succeeded in 0.002374751988099888s: 'Success: Updated 100 case records cache'
[2025-07-18 15:44:38,729: INFO/MainProcess] Task thworker.check_b2c_trade_state[7692c4d2-27f4-45c6-b5b2-b85563eb50bf] received
[2025-07-18 15:44:38,730: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 15:44:38,731: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 15:44:38,732: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 15:44:38,732: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[7692c4d2-27f4-45c6-b5b2-b85563eb50bf] succeeded in 0.003103864990407601s: 'Success: B2C trade state checked'
[2025-07-18 15:44:38,734: INFO/MainProcess] Task thworker.check_b2c_trade_state[bed528b0-6de8-42b4-81a8-2b798ca96083] received
[2025-07-18 15:44:38,735: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 15:44:38,736: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 15:44:38,737: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 15:44:38,737: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[bed528b0-6de8-42b4-81a8-2b798ca96083] succeeded in 0.0022769549977965653s: 'Success: B2C trade state checked'
[2025-07-18 15:49:38,615: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[1031e831-21b0-4465-bdce-b8b6ee4fe6db] received
[2025-07-18 15:49:38,616: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 15:49:38,616: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 15:49:38,619: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[1031e831-21b0-4465-bdce-b8b6ee4fe6db] succeeded in 0.003832774003967643s: None
[2025-07-18 15:49:38,680: INFO/MainProcess] Task thworker.update_case_records_cache[d52b70e4-927a-402e-b752-6f428db76270] received
[2025-07-18 15:49:38,681: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 15:49:38,683: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 15:49:38,683: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 15:49:38,686: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[d52b70e4-927a-402e-b752-6f428db76270] succeeded in 0.00515390100190416s: 'Success: Updated 100 case records cache'
[2025-07-18 15:49:38,705: INFO/MainProcess] Task thworker.check_b2c_trade_state[afccb349-9d6d-4407-a33a-1a1989fd4d7c] received
[2025-07-18 15:49:38,705: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 15:49:38,705: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 15:49:38,708: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 15:49:38,709: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[afccb349-9d6d-4407-a33a-1a1989fd4d7c] succeeded in 0.004079445003299043s: 'Success: B2C trade state checked'
[2025-07-18 15:52:06,594: INFO/ForkPoolWorker-1] 更新 CaseRecord 缓存成功。
[2025-07-18 15:52:06,594: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 15:52:06,681: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[6bc99d41-0217-4284-a430-91c6b03514c1] succeeded in 447.9797034120129s: 'Success: Updated 100 case records cache'
DEBUG:asyncio:Using selector: EpollSelector
DEBUG:celery.utils.functional:
def update_monitor_data_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_user_active_point_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def check_b2c_trade_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def accumulate(self, *args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_recharge_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def case_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def unlock_chord(self, group_id, callback, interval=0, max_retries=1, result=2, Result=3, GroupResult=4, result_from_tuple=5, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_month_amount():
    return 1

DEBUG:celery.utils.functional:
def case_pk_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def xmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def init_system_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_new_user_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def sync_items_price_task(self):
    return 1

DEBUG:celery.utils.functional:
def xstarmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def delete_old_balance_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_domain_verify_switch(value):
    return 1

DEBUG:celery.utils.functional:
def case_bot_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def chunks(task, it, n):
    return 1

DEBUG:celery.utils.functional:
def case_pk_join_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def group(self, tasks, result, group_id, partial_args, add_to_parent=0):
    return 1

DEBUG:celery.utils.functional:
def cleanup_duplicate_roll_room_bets():
    return 1

DEBUG:celery.utils.functional:
def update_daily_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def hourly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def chain(*args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_enable_based_on_coins(min_coins, max_coins):
    return 1

DEBUG:celery.utils.functional:
def roll_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def chord(self, header, body, partial_args=0, interval=1, countdown=2, max_retries=3, eager=4, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def delete_old_log_entries(batch_size=0, days=1):
    return 1

DEBUG:celery.utils.functional:
def check_envelop_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_today_amount():
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache(count, batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_enable_rule_exam(value):
    return 1

DEBUG:celery.utils.functional:
def daily_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_enable_pay_method(enable_list):
    return 1

DEBUG:celery.utils.functional:
def check_room_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def zbt_buy_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_staff_balance():
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache_task(self, count=0):
    return 1

DEBUG:celery.utils.functional:
def backend_cleanup():
    return 1

DEBUG:celery.utils.functional:
def check_pendclose_trade_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_case_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_register_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def weekly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_package_items(batch_size=0):
    return 1

/usr/local/lib/python3.8/site-packages/celery/platforms.py:841: SecurityWarning: You're running the worker with superuser privileges: this is
absolutely not recommended!

Please specify a different user using the --uid option.

User information: uid=0 euid=0 gid=0 egid=0

  warnings.warn(SecurityWarning(ROOT_DISCOURAGED.format(
[2025-07-18 15:53:13,264: INFO/MainProcess] Connected to redis://localhost:6379/0
[2025-07-18 15:53:13,269: INFO/MainProcess] mingle: searching for neighbors
[2025-07-18 15:53:14,357: INFO/MainProcess] mingle: all alone
[2025-07-18 15:53:14,558: INFO/MainProcess] celery@iZj6c3ddfpik1c7yalqdt4Z ready.
[2025-07-18 15:54:38,776: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[d192c555-b0ea-4c93-b4d9-cac6f36a31b6] received
[2025-07-18 15:54:38,777: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 15:54:38,944: INFO/MainProcess] Task thworker.check_b2c_trade_state[e44697e5-f22e-4f51-96b7-f868d497be70] received
[2025-07-18 15:54:38,955: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 15:54:38,957: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 15:54:38,978: INFO/ForkPoolWorker-1] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 15:54:39,039: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[e44697e5-f22e-4f51-96b7-f868d497be70] succeeded in 0.0933689889498055s: 'Success: B2C trade state checked'
[2025-07-18 15:54:39,108: INFO/MainProcess] Task thworker.check_b2c_trade_state[0c2b8c22-90e8-483a-97b6-1d3ec84314b0] received
[2025-07-18 15:54:39,110: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 15:54:39,111: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 15:54:39,118: INFO/ForkPoolWorker-1] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 15:54:39,135: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[0c2b8c22-90e8-483a-97b6-1d3ec84314b0] succeeded in 0.026062312012072653s: 'Success: B2C trade state checked'
[2025-07-18 15:54:39,770: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 15:54:39,909: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[d192c555-b0ea-4c93-b4d9-cac6f36a31b6] succeeded in 1.1315105649991892s: None
[2025-07-18 15:59:38,807: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[6500740d-d263-4139-b273-54baa7871983] received
[2025-07-18 15:59:38,808: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 15:59:39,183: INFO/MainProcess] Task thworker.update_case_records_cache[c908cebe-c493-42c3-98f9-2162f6e4ae78] received
[2025-07-18 15:59:39,189: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 15:59:39,224: INFO/ForkPoolWorker-1] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 15:59:39,225: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 15:59:39,270: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 15:59:39,302: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[c908cebe-c493-42c3-98f9-2162f6e4ae78] succeeded in 0.11288545798743144s: 'Success: Updated 100 case records cache'
[2025-07-18 15:59:39,360: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[6500740d-d263-4139-b273-54baa7871983] succeeded in 0.5526341120130382s: None
[2025-07-18 15:59:39,429: INFO/MainProcess] Task thworker.check_b2c_trade_state[56702ba4-8b4b-4372-91a0-2410f76c7fbf] received
[2025-07-18 15:59:39,443: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 15:59:39,444: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 15:59:39,454: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 15:59:39,487: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[56702ba4-8b4b-4372-91a0-2410f76c7fbf] succeeded in 0.05659023398766294s: 'Success: B2C trade state checked'
[2025-07-18 16:00:00,066: INFO/MainProcess] Task thworker.hourly_lottery[b1a11f4a-0cbc-4f7e-9c41-24dcc1075ad9] received
[2025-07-18 16:00:00,081: INFO/ForkPoolWorker-2] ------------------------ 整点抽奖 ------------------------
[2025-07-18 16:00:00,081: INFO/ForkPoolWorker-2] [Celery] 每小时抽奖任务完成
[2025-07-18 16:00:00,085: INFO/ForkPoolWorker-2] Task thworker.hourly_lottery[b1a11f4a-0cbc-4f7e-9c41-24dcc1075ad9] succeeded in 0.01576621801359579s: 'Success: Hourly lottery completed'
[2025-07-18 16:04:38,810: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[7868dce9-63ba-446e-8470-0c2e11645666] received
[2025-07-18 16:04:38,819: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 16:04:39,273: INFO/MainProcess] Task thworker.update_case_records_cache[e7ecd091-d250-4cd3-a85c-a9ec5c37bd0c] received
[2025-07-18 16:04:39,293: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 16:04:39,320: INFO/ForkPoolWorker-1] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 16:04:39,321: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 16:04:39,371: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[e7ecd091-d250-4cd3-a85c-a9ec5c37bd0c] succeeded in 0.07777267997153103s: 'Success: Updated 100 case records cache'
[2025-07-18 16:04:39,424: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 16:04:39,449: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[7868dce9-63ba-446e-8470-0c2e11645666] succeeded in 0.6305943879997358s: None
[2025-07-18 16:09:38,907: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[b1a8f3f2-72b3-4a14-870c-f13dfd935098] received
[2025-07-18 16:09:38,908: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 16:09:39,372: INFO/MainProcess] Task thworker.update_case_records_cache[116e870b-2768-40a6-9e7f-69bcdbcc4b80] received
[2025-07-18 16:09:39,374: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 16:09:39,375: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 16:09:39,394: INFO/ForkPoolWorker-1] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 16:09:39,394: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 16:09:39,434: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[b1a8f3f2-72b3-4a14-870c-f13dfd935098] succeeded in 0.5261547359987162s: None
[2025-07-18 16:09:39,455: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[116e870b-2768-40a6-9e7f-69bcdbcc4b80] succeeded in 0.08160135499201715s: 'Success: Updated 100 case records cache'
[2025-07-18 16:09:39,500: INFO/MainProcess] Task thworker.check_b2c_trade_state[c801e79a-80ca-44ce-87b1-1f92ab9fff67] received
[2025-07-18 16:09:39,524: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 16:09:39,535: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 16:09:39,549: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 16:09:39,590: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[c801e79a-80ca-44ce-87b1-1f92ab9fff67] succeeded in 0.08907967299455777s: 'Success: B2C trade state checked'
DEBUG:asyncio:Using selector: EpollSelector
DEBUG:asyncio:Using selector: EpollSelector
DEBUG:celery.utils.functional:
def update_monitor_data_task(self):
    return 1

DEBUG:celery.utils.functional:
def check_b2c_trade_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def accumulate(self, *args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_register_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def delete_old_package_items(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def case_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def unlock_chord(self, group_id, callback, interval=0, max_retries=1, result=2, Result=3, GroupResult=4, result_from_tuple=5, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def delete_old_user_active_point_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_today_amount():
    return 1

DEBUG:celery.utils.functional:
def case_pk_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def xmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def init_system_task(self):
    return 1

DEBUG:celery.utils.functional:
def sync_items_price_task(self):
    return 1

DEBUG:celery.utils.functional:
def xstarmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def update_recharge_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def case_bot_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def chunks(task, it, n):
    return 1

DEBUG:celery.utils.functional:
def update_month_amount():
    return 1

DEBUG:celery.utils.functional:
def case_pk_join_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_balance_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def group(self, tasks, result, group_id, partial_args, add_to_parent=0):
    return 1

DEBUG:celery.utils.functional:
def update_new_user_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def hourly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def chain(*args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def cleanup_duplicate_roll_room_bets():
    return 1

DEBUG:celery.utils.functional:
def update_domain_verify_switch(value):
    return 1

DEBUG:celery.utils.functional:
def roll_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def chord(self, header, body, partial_args=0, interval=1, countdown=2, max_retries=3, eager=4, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def check_envelop_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_enable_pay_method(enable_list):
    return 1

DEBUG:celery.utils.functional:
def zbt_buy_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_daily_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def daily_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_log_entries(batch_size=0, days=1):
    return 1

DEBUG:celery.utils.functional:
def update_enable_based_on_coins(min_coins, max_coins):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache(count, batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def check_room_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache_task(self, count=0):
    return 1

DEBUG:celery.utils.functional:
def backend_cleanup():
    return 1

DEBUG:celery.utils.functional:
def check_pendclose_trade_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_enable_rule_exam(value):
    return 1

DEBUG:celery.utils.functional:
def weekly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_staff_balance():
    return 1

DEBUG:celery.utils.functional:
def delete_old_case_records(batch_size=0):
    return 1

/usr/local/lib/python3.8/site-packages/celery/platforms.py:841: SecurityWarning: You're running the worker with superuser privileges: this is
absolutely not recommended!

Please specify a different user using the --uid option.

User information: uid=0 euid=0 gid=0 egid=0

  warnings.warn(SecurityWarning(ROOT_DISCOURAGED.format(
[2025-07-18 16:14:28,383: INFO/MainProcess] Connected to redis://localhost:6379/0
[2025-07-18 16:14:28,387: INFO/MainProcess] mingle: searching for neighbors
[2025-07-18 16:14:29,536: INFO/MainProcess] mingle: all alone
[2025-07-18 16:14:29,865: INFO/MainProcess] celery@iZj6c3ddfpik1c7yalqdt4Z ready.
[2025-07-18 16:14:38,887: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[9c680bcf-a201-4c8e-a16b-608d96892acb] received
[2025-07-18 16:14:38,908: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 16:14:39,432: INFO/MainProcess] Task thworker.update_case_records_cache[e440e970-4464-4d34-ad9f-ac476269c12b] received
[2025-07-18 16:14:39,440: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 16:14:39,443: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 16:14:39,453: INFO/ForkPoolWorker-1] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 16:14:39,453: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 16:14:39,462: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[e440e970-4464-4d34-ad9f-ac476269c12b] succeeded in 0.022901167976669967s: 'Success: Updated 100 case records cache'
[2025-07-18 16:14:39,470: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[9c680bcf-a201-4c8e-a16b-608d96892acb] succeeded in 0.5619278179947287s: None
[2025-07-18 16:19:38,908: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[e0d32d04-db36-40ba-89bb-2fcbe3313ae4] received
[2025-07-18 16:19:38,914: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 16:19:39,422: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 16:19:39,526: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[e0d32d04-db36-40ba-89bb-2fcbe3313ae4] succeeded in 0.6115897319978103s: None
[2025-07-18 16:19:39,534: INFO/MainProcess] Task thworker.update_case_records_cache[495b0d17-42c8-46ea-9991-89fdc570d7dd] received
[2025-07-18 16:19:39,548: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 16:19:39,556: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 16:19:39,556: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 16:19:39,561: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[495b0d17-42c8-46ea-9991-89fdc570d7dd] succeeded in 0.012623954971786588s: 'Success: Updated 100 case records cache'
[2025-07-18 16:24:39,006: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[e379c18e-5e50-4b28-8abc-3e05d830dd49] received
[2025-07-18 16:24:39,022: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 16:24:39,552: INFO/MainProcess] Task thworker.update_case_records_cache[4a44ddf3-5b32-4acc-8421-703f69bc3d70] received
[2025-07-18 16:24:39,553: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 16:24:39,578: INFO/ForkPoolWorker-1] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 16:24:39,578: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 16:24:39,628: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[4a44ddf3-5b32-4acc-8421-703f69bc3d70] succeeded in 0.07463205500971526s: 'Success: Updated 100 case records cache'
[2025-07-18 16:24:39,658: INFO/MainProcess] Task thworker.check_b2c_trade_state[3e2fc624-5579-4b83-9f19-8c1f0beb0019] received
[2025-07-18 16:24:39,688: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 16:24:39,699: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 16:24:39,725: INFO/ForkPoolWorker-1] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 16:24:39,759: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[3e2fc624-5579-4b83-9f19-8c1f0beb0019] succeeded in 0.09976355399703607s: 'Success: B2C trade state checked'
[2025-07-18 16:24:39,865: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 16:24:39,945: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[e379c18e-5e50-4b28-8abc-3e05d830dd49] succeeded in 0.922398880997207s: None
[2025-07-18 16:29:39,036: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[b665a730-da31-47ca-ab71-e8fd23d80c98] received
[2025-07-18 16:29:39,037: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 16:29:39,039: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 16:29:39,045: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[b665a730-da31-47ca-ab71-e8fd23d80c98] succeeded in 0.007801842002663761s: None
[2025-07-18 16:29:39,585: INFO/MainProcess] Task thworker.update_case_records_cache[cd49d694-e074-4b4a-8315-972d9b1e20bb] received
[2025-07-18 16:29:39,586: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 16:29:39,598: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 16:29:39,598: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 16:29:39,682: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[cd49d694-e074-4b4a-8315-972d9b1e20bb] succeeded in 0.09544194396585226s: 'Success: Updated 100 case records cache'
[2025-07-18 16:29:39,694: INFO/MainProcess] Task thworker.check_b2c_trade_state[88777597-3e62-4e40-8c0f-8757cd6e1265] received
[2025-07-18 16:29:39,696: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 16:29:39,697: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 16:29:39,725: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 16:29:39,746: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[88777597-3e62-4e40-8c0f-8757cd6e1265] succeeded in 0.050652133009862155s: 'Success: B2C trade state checked'
[2025-07-18 16:34:39,388: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[705aabaf-2868-4efa-bfb0-f05e6b0a1cd8] received
[2025-07-18 16:34:39,389: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 16:34:39,532: INFO/MainProcess] Task thworker.update_case_records_cache[c0858033-9747-4ca5-99a6-cf8af7fc68c6] received
[2025-07-18 16:34:39,534: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 16:34:40,243: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 16:34:40,393: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[705aabaf-2868-4efa-bfb0-f05e6b0a1cd8] succeeded in 1.0039744089590386s: None
[2025-07-18 16:35:03,683: INFO/ForkPoolWorker-1] 更新 CaseRecord 缓存成功。
[2025-07-18 16:35:03,683: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 16:35:03,739: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[c0858033-9747-4ca5-99a6-cf8af7fc68c6] succeeded in 24.206001430982724s: 'Success: Updated 100 case records cache'
[2025-07-18 16:39:39,752: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[dc522162-3718-48e7-b6cb-d141d9d60293] received
[2025-07-18 16:39:39,757: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 16:39:39,958: INFO/MainProcess] Task thworker.check_b2c_trade_state[622f8351-6265-4e0b-a031-4f93de39e8dc] received
[2025-07-18 16:39:39,977: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 16:39:39,977: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 16:39:39,980: INFO/ForkPoolWorker-1] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 16:39:39,984: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[622f8351-6265-4e0b-a031-4f93de39e8dc] succeeded in 0.024036568007431924s: 'Success: B2C trade state checked'
[2025-07-18 16:39:39,990: INFO/MainProcess] Task thworker.update_case_records_cache[1946b47d-e816-4381-9b83-e7af9384e5ad] received
[2025-07-18 16:39:39,990: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 16:39:39,991: INFO/ForkPoolWorker-1] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 16:39:39,991: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 16:39:40,007: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[1946b47d-e816-4381-9b83-e7af9384e5ad] succeeded in 0.016708704992197454s: 'Success: Updated 100 case records cache'
[2025-07-18 16:39:40,326: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 16:39:40,490: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[dc522162-3718-48e7-b6cb-d141d9d60293] succeeded in 0.7325150609831326s: None
[2025-07-18 16:44:40,074: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[df507114-7d71-43b7-976c-e212fff9ceb9] received
[2025-07-18 16:44:40,084: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 16:44:40,231: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[3cae1008-aef5-4241-ab7b-375b98cd9706] received
[2025-07-18 16:44:40,260: INFO/ForkPoolWorker-1] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 16:44:40,261: INFO/ForkPoolWorker-1] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 16:44:40,262: INFO/ForkPoolWorker-1] Task roll.tasks.cleanup_duplicate_roll_room_bets[3cae1008-aef5-4241-ab7b-375b98cd9706] succeeded in 0.00261176802450791s: None
[2025-07-18 16:44:40,265: INFO/MainProcess] Task thworker.update_case_records_cache[06f40652-f8b0-4515-be88-0cafccbf4b9e] received
[2025-07-18 16:44:40,266: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 16:44:40,266: INFO/ForkPoolWorker-1] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 16:44:40,267: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 16:44:40,268: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[06f40652-f8b0-4515-be88-0cafccbf4b9e] succeeded in 0.0020496590295806527s: 'Success: Updated 100 case records cache'
[2025-07-18 16:44:40,355: INFO/MainProcess] Task thworker.check_b2c_trade_state[be0ad620-3e1e-4901-a692-704ca20b2cf9] received
[2025-07-18 16:44:40,357: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 16:44:40,358: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 16:44:40,379: INFO/ForkPoolWorker-1] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 16:44:40,384: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[be0ad620-3e1e-4901-a692-704ca20b2cf9] succeeded in 0.028188996016979218s: 'Success: B2C trade state checked'
[2025-07-18 16:44:40,608: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 16:44:40,655: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[df507114-7d71-43b7-976c-e212fff9ceb9] succeeded in 0.5711810780339874s: None
[2025-07-18 16:49:40,113: INFO/MainProcess] Task thworker.update_case_records_cache[647c2bf6-ef1c-4e67-a04e-797fcde36667] received
[2025-07-18 16:49:40,114: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 16:49:40,114: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 16:49:40,114: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 16:49:40,115: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[647c2bf6-ef1c-4e67-a04e-797fcde36667] succeeded in 0.0018982160254381597s: 'Success: Updated 100 case records cache'
[2025-07-18 16:49:40,186: INFO/MainProcess] Task thworker.update_case_records_cache[c32813ee-8470-44a8-be8d-260715b1fffc] received
[2025-07-18 16:49:40,187: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 16:49:40,192: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 16:49:40,192: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 16:49:40,194: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[c32813ee-8470-44a8-be8d-260715b1fffc] succeeded in 0.00762012496124953s: 'Success: Updated 100 case records cache'
DEBUG:asyncio:Using selector: EpollSelector
DEBUG:celery.utils.functional:
def update_monitor_data_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_user_active_point_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_today_amount():
    return 1

DEBUG:celery.utils.functional:
def check_b2c_trade_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def accumulate(self, *args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def case_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def unlock_chord(self, group_id, callback, interval=0, max_retries=1, result=2, Result=3, GroupResult=4, result_from_tuple=5, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_recharge_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def case_pk_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def xmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def init_system_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_month_amount():
    return 1

DEBUG:celery.utils.functional:
def sync_items_price_task(self):
    return 1

DEBUG:celery.utils.functional:
def xstarmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def delete_old_balance_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_new_user_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def case_bot_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def chunks(task, it, n):
    return 1

DEBUG:celery.utils.functional:
def cleanup_duplicate_roll_room_bets():
    return 1

DEBUG:celery.utils.functional:
def update_domain_verify_switch(value):
    return 1

DEBUG:celery.utils.functional:
def case_pk_join_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def group(self, tasks, result, group_id, partial_args, add_to_parent=0):
    return 1

DEBUG:celery.utils.functional:
def hourly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def chain(*args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def roll_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_daily_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def chord(self, header, body, partial_args=0, interval=1, countdown=2, max_retries=3, eager=4, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def delete_old_log_entries(batch_size=0, days=1):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache(count, batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_enable_based_on_coins(min_coins, max_coins):
    return 1

DEBUG:celery.utils.functional:
def check_envelop_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def daily_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def check_room_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_enable_rule_exam(value):
    return 1

DEBUG:celery.utils.functional:
def update_staff_balance():
    return 1

DEBUG:celery.utils.functional:
def update_enable_pay_method(enable_list):
    return 1

DEBUG:celery.utils.functional:
def backend_cleanup():
    return 1

DEBUG:celery.utils.functional:
def delete_old_case_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache_task(self, count=0):
    return 1

DEBUG:celery.utils.functional:
def check_pendclose_trade_task(self):
    return 1

DEBUG:celery.utils.functional:
def weekly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_package_items(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_register_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def zbt_buy_worker_task(self):
    return 1

/usr/local/lib/python3.8/site-packages/celery/platforms.py:841: SecurityWarning: You're running the worker with superuser privileges: this is
absolutely not recommended!

Please specify a different user using the --uid option.

User information: uid=0 euid=0 gid=0 egid=0

  warnings.warn(SecurityWarning(ROOT_DISCOURAGED.format(
[2025-07-18 16:51:35,730: INFO/MainProcess] Connected to redis://localhost:6379/0
[2025-07-18 16:51:35,734: INFO/MainProcess] mingle: searching for neighbors
[2025-07-18 16:51:36,751: INFO/MainProcess] mingle: all alone
[2025-07-18 16:51:36,767: INFO/MainProcess] celery@iZj6c3ddfpik1c7yalqdt4Z ready.
[2025-07-18 16:54:40,180: INFO/MainProcess] Task thworker.update_case_records_cache[eb002733-b988-47a3-8057-779052d0202f] received
[2025-07-18 16:54:40,185: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 16:54:40,188: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 16:54:40,188: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 16:54:40,191: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[eb002733-b988-47a3-8057-779052d0202f] succeeded in 0.007283348997589201s: 'Success: Updated 100 case records cache'
[2025-07-18 16:54:40,199: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[6c953efc-e38a-422a-a5bd-e628fe39291f] received
[2025-07-18 16:54:40,200: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 16:54:40,200: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 16:54:40,206: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[6c953efc-e38a-422a-a5bd-e628fe39291f] succeeded in 0.005965134012512863s: None
[2025-07-18 16:54:40,213: INFO/MainProcess] Task thworker.check_b2c_trade_state[285d703d-6d68-43ff-8799-26f3de215228] received
[2025-07-18 16:54:40,214: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 16:54:40,214: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 16:54:40,216: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 16:54:40,216: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[285d703d-6d68-43ff-8799-26f3de215228] succeeded in 0.0021321640233509243s: 'Success: B2C trade state checked'
[2025-07-18 16:59:40,164: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[c9768fb8-c7a2-44ac-ba7e-ff9cb5ea7ccd] received
[2025-07-18 16:59:40,165: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 16:59:40,168: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 16:59:40,169: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[c9768fb8-c7a2-44ac-ba7e-ff9cb5ea7ccd] succeeded in 0.0037114989827387035s: None
[2025-07-18 16:59:40,177: INFO/MainProcess] Task thworker.update_case_records_cache[d1ba5273-fb17-43a4-9efe-abfffbf07c90] received
[2025-07-18 16:59:40,178: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 16:59:40,179: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 16:59:40,179: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 16:59:40,180: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[d1ba5273-fb17-43a4-9efe-abfffbf07c90] succeeded in 0.002584076952189207s: 'Success: Updated 100 case records cache'
[2025-07-18 16:59:40,200: INFO/MainProcess] Task thworker.check_b2c_trade_state[cba52417-a1dd-4f48-aab5-64c18df6b7e1] received
[2025-07-18 16:59:40,201: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 16:59:40,202: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 16:59:40,203: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 16:59:40,205: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[cba52417-a1dd-4f48-aab5-64c18df6b7e1] succeeded in 0.004429463006090373s: 'Success: B2C trade state checked'
[2025-07-18 17:00:00,010: INFO/MainProcess] Task thworker.hourly_lottery[ed9f3f13-3a03-4def-abf3-97d92ee934bb] received
[2025-07-18 17:00:00,012: INFO/ForkPoolWorker-2] ------------------------ 整点抽奖 ------------------------
[2025-07-18 17:00:00,016: INFO/ForkPoolWorker-2] [Celery] 每小时抽奖任务完成
[2025-07-18 17:00:00,021: INFO/ForkPoolWorker-2] Task thworker.hourly_lottery[ed9f3f13-3a03-4def-abf3-97d92ee934bb] succeeded in 0.010044976021163166s: 'Success: Hourly lottery completed'
[2025-07-18 17:04:40,161: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[25550a82-4995-4ec0-ac1e-bbaa80af1220] received
[2025-07-18 17:04:40,162: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 17:04:40,164: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 17:04:40,167: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[25550a82-4995-4ec0-ac1e-bbaa80af1220] succeeded in 0.005651401006616652s: None
[2025-07-18 17:04:40,178: INFO/MainProcess] Task thworker.update_case_records_cache[26c5ab23-404d-4be2-946b-f6ebeb0b83de] received
[2025-07-18 17:04:40,179: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 17:04:40,180: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 17:04:40,180: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 17:04:40,183: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[26c5ab23-404d-4be2-946b-f6ebeb0b83de] succeeded in 0.003897607035469264s: 'Success: Updated 100 case records cache'
[2025-07-18 17:04:40,202: INFO/MainProcess] Task thworker.check_b2c_trade_state[c3fff7b3-4c18-413d-ae31-ffa9fd6984f8] received
[2025-07-18 17:04:40,203: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 17:04:40,204: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 17:04:40,205: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 17:04:40,207: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[c3fff7b3-4c18-413d-ae31-ffa9fd6984f8] succeeded in 0.004337118996772915s: 'Success: B2C trade state checked'
DEBUG:asyncio:Using selector: EpollSelector
DEBUG:celery.utils.functional:
def update_monitor_data_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_user_active_point_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_today_amount():
    return 1

DEBUG:celery.utils.functional:
def check_b2c_trade_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def accumulate(self, *args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def case_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def unlock_chord(self, group_id, callback, interval=0, max_retries=1, result=2, Result=3, GroupResult=4, result_from_tuple=5, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_recharge_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def case_pk_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def xmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def init_system_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_month_amount():
    return 1

DEBUG:celery.utils.functional:
def sync_items_price_task(self):
    return 1

DEBUG:celery.utils.functional:
def xstarmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def delete_old_balance_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_new_user_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def case_bot_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def chunks(task, it, n):
    return 1

DEBUG:celery.utils.functional:
def cleanup_duplicate_roll_room_bets():
    return 1

DEBUG:celery.utils.functional:
def update_domain_verify_switch(value):
    return 1

DEBUG:celery.utils.functional:
def case_pk_join_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def group(self, tasks, result, group_id, partial_args, add_to_parent=0):
    return 1

DEBUG:celery.utils.functional:
def hourly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def zbt_buy_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def chain(*args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def roll_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_daily_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def chord(self, header, body, partial_args=0, interval=1, countdown=2, max_retries=3, eager=4, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def delete_old_log_entries(batch_size=0, days=1):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache(count, batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_enable_based_on_coins(min_coins, max_coins):
    return 1

DEBUG:celery.utils.functional:
def check_envelop_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def daily_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def check_room_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_enable_rule_exam(value):
    return 1

DEBUG:celery.utils.functional:
def update_staff_balance():
    return 1

DEBUG:celery.utils.functional:
def update_enable_pay_method(enable_list):
    return 1

DEBUG:celery.utils.functional:
def backend_cleanup():
    return 1

DEBUG:celery.utils.functional:
def delete_old_case_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache_task(self, count=0):
    return 1

DEBUG:celery.utils.functional:
def check_pendclose_trade_task(self):
    return 1

DEBUG:celery.utils.functional:
def weekly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_package_items(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_register_box_chance_type(chance_type=0):
    return 1

/usr/local/lib/python3.8/site-packages/celery/platforms.py:841: SecurityWarning: You're running the worker with superuser privileges: this is
absolutely not recommended!

Please specify a different user using the --uid option.

User information: uid=0 euid=0 gid=0 egid=0

  warnings.warn(SecurityWarning(ROOT_DISCOURAGED.format(
[2025-07-18 17:08:58,600: INFO/MainProcess] Connected to redis://localhost:6379/0
[2025-07-18 17:08:58,604: INFO/MainProcess] mingle: searching for neighbors
[2025-07-18 17:08:59,625: INFO/MainProcess] mingle: all alone
[2025-07-18 17:08:59,646: INFO/MainProcess] celery@iZj6c3ddfpik1c7yalqdt4Z ready.
[2025-07-18 17:09:40,191: INFO/MainProcess] Task thworker.update_case_records_cache[bf6eb814-4ba7-4060-940a-e75cc5d344e2] received
[2025-07-18 17:09:40,194: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 17:09:40,197: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 17:09:40,197: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 17:09:40,205: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[bf6eb814-4ba7-4060-940a-e75cc5d344e2] succeeded in 0.011902811005711555s: 'Success: Updated 100 case records cache'
[2025-07-18 17:09:40,210: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[2a0af12e-6408-41d5-8749-c9ccec32e5c5] received
[2025-07-18 17:09:40,211: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 17:09:40,230: INFO/MainProcess] Task thworker.check_b2c_trade_state[5e96c6a3-aa6f-4b74-81ba-f9deb11a7b67] received
[2025-07-18 17:09:40,231: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 17:09:40,233: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 17:09:40,234: INFO/ForkPoolWorker-1] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 17:09:40,235: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[5e96c6a3-aa6f-4b74-81ba-f9deb11a7b67] succeeded in 0.004770534986164421s: 'Success: B2C trade state checked'
[2025-07-18 17:09:45,154: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 17:09:45,185: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[2a0af12e-6408-41d5-8749-c9ccec32e5c5] succeeded in 4.974285359960049s: None
[2025-07-18 17:14:40,171: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[45541a40-373b-40c9-b99c-32c5d4734acc] received
[2025-07-18 17:14:40,174: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 17:14:40,175: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 17:14:40,177: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[45541a40-373b-40c9-b99c-32c5d4734acc] succeeded in 0.0026420719805173576s: None
[2025-07-18 17:14:40,225: INFO/MainProcess] Task thworker.update_case_records_cache[e37d8652-f80f-495f-84f6-4c41aa85a29c] received
[2025-07-18 17:14:40,226: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 17:14:40,227: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 17:14:40,227: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 17:14:40,232: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[e37d8652-f80f-495f-84f6-4c41aa85a29c] succeeded in 0.00565036095213145s: 'Success: Updated 100 case records cache'
[2025-07-18 17:14:40,233: INFO/MainProcess] Task thworker.check_b2c_trade_state[e52f1671-8497-4b7e-a695-f9f6abbb344f] received
[2025-07-18 17:14:40,235: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 17:14:40,236: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 17:14:40,238: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 17:14:40,241: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[e52f1671-8497-4b7e-a695-f9f6abbb344f] succeeded in 0.005669737991411239s: 'Success: B2C trade state checked'
DEBUG:asyncio:Using selector: EpollSelector
DEBUG:celery.utils.functional:
def update_monitor_data_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_user_active_point_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_today_amount():
    return 1

DEBUG:celery.utils.functional:
def check_b2c_trade_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def accumulate(self, *args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def case_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def unlock_chord(self, group_id, callback, interval=0, max_retries=1, result=2, Result=3, GroupResult=4, result_from_tuple=5, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_recharge_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def case_pk_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def xmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def init_system_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_month_amount():
    return 1

DEBUG:celery.utils.functional:
def sync_items_price_task(self):
    return 1

DEBUG:celery.utils.functional:
def xstarmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def delete_old_balance_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_new_user_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def case_bot_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def chunks(task, it, n):
    return 1

DEBUG:celery.utils.functional:
def cleanup_duplicate_roll_room_bets():
    return 1

DEBUG:celery.utils.functional:
def update_domain_verify_switch(value):
    return 1

DEBUG:celery.utils.functional:
def case_pk_join_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def group(self, tasks, result, group_id, partial_args, add_to_parent=0):
    return 1

DEBUG:celery.utils.functional:
def hourly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def zbt_buy_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def chain(*args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def roll_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_daily_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def chord(self, header, body, partial_args=0, interval=1, countdown=2, max_retries=3, eager=4, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def delete_old_log_entries(batch_size=0, days=1):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache(count, batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_enable_based_on_coins(min_coins, max_coins):
    return 1

DEBUG:celery.utils.functional:
def check_envelop_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def daily_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def check_room_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_enable_rule_exam(value):
    return 1

DEBUG:celery.utils.functional:
def update_staff_balance():
    return 1

DEBUG:celery.utils.functional:
def update_enable_pay_method(enable_list):
    return 1

DEBUG:celery.utils.functional:
def backend_cleanup():
    return 1

DEBUG:celery.utils.functional:
def delete_old_case_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache_task(self, count=0):
    return 1

DEBUG:celery.utils.functional:
def check_pendclose_trade_task(self):
    return 1

DEBUG:celery.utils.functional:
def weekly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_package_items(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_register_box_chance_type(chance_type=0):
    return 1

/usr/local/lib/python3.8/site-packages/celery/platforms.py:841: SecurityWarning: You're running the worker with superuser privileges: this is
absolutely not recommended!

Please specify a different user using the --uid option.

User information: uid=0 euid=0 gid=0 egid=0

  warnings.warn(SecurityWarning(ROOT_DISCOURAGED.format(
[2025-07-18 17:17:11,017: INFO/MainProcess] Connected to redis://localhost:6379/0
[2025-07-18 17:17:11,057: INFO/MainProcess] mingle: searching for neighbors
[2025-07-18 17:17:12,135: INFO/MainProcess] mingle: all alone
[2025-07-18 17:17:12,320: INFO/MainProcess] celery@iZj6c3ddfpik1c7yalqdt4Z ready.
[2025-07-18 17:19:40,200: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[798a871b-6c34-4d7e-a9fb-baa55cb9c7f4] received
[2025-07-18 17:19:40,204: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 17:19:40,206: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 17:19:40,209: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[798a871b-6c34-4d7e-a9fb-baa55cb9c7f4] succeeded in 0.004722295969258994s: None
[2025-07-18 17:19:40,227: INFO/MainProcess] Task thworker.update_case_records_cache[0bbe6ecd-9704-48c4-a2e2-21caa35edc6e] received
[2025-07-18 17:19:40,228: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 17:19:40,233: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 17:19:40,233: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 17:19:40,235: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[0bbe6ecd-9704-48c4-a2e2-21caa35edc6e] succeeded in 0.007634001027327031s: 'Success: Updated 100 case records cache'
[2025-07-18 17:19:40,246: INFO/MainProcess] Task thworker.check_b2c_trade_state[d72f3a62-c477-4940-a9f7-e37ca1464dc4] received
[2025-07-18 17:19:40,248: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 17:19:40,249: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 17:19:40,250: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 17:19:40,250: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[d72f3a62-c477-4940-a9f7-e37ca1464dc4] succeeded in 0.0026501529500819743s: 'Success: B2C trade state checked'
DEBUG:asyncio:Using selector: EpollSelector
DEBUG:celery.utils.functional:
def update_monitor_data_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_user_active_point_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_today_amount():
    return 1

DEBUG:celery.utils.functional:
def check_b2c_trade_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def accumulate(self, *args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def case_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def unlock_chord(self, group_id, callback, interval=0, max_retries=1, result=2, Result=3, GroupResult=4, result_from_tuple=5, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_recharge_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def case_pk_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def xmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def init_system_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_month_amount():
    return 1

DEBUG:celery.utils.functional:
def sync_items_price_task(self):
    return 1

DEBUG:celery.utils.functional:
def xstarmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def delete_old_balance_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_new_user_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def case_bot_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def zbt_buy_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def chunks(task, it, n):
    return 1

DEBUG:celery.utils.functional:
def cleanup_duplicate_roll_room_bets():
    return 1

DEBUG:celery.utils.functional:
def update_domain_verify_switch(value):
    return 1

DEBUG:celery.utils.functional:
def case_pk_join_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def group(self, tasks, result, group_id, partial_args, add_to_parent=0):
    return 1

DEBUG:celery.utils.functional:
def hourly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def chain(*args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def roll_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_daily_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def chord(self, header, body, partial_args=0, interval=1, countdown=2, max_retries=3, eager=4, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def delete_old_log_entries(batch_size=0, days=1):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache(count, batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_enable_based_on_coins(min_coins, max_coins):
    return 1

DEBUG:celery.utils.functional:
def check_envelop_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def daily_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def check_room_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_enable_rule_exam(value):
    return 1

DEBUG:celery.utils.functional:
def update_staff_balance():
    return 1

DEBUG:celery.utils.functional:
def update_enable_pay_method(enable_list):
    return 1

DEBUG:celery.utils.functional:
def backend_cleanup():
    return 1

DEBUG:celery.utils.functional:
def delete_old_case_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache_task(self, count=0):
    return 1

DEBUG:celery.utils.functional:
def check_pendclose_trade_task(self):
    return 1

DEBUG:celery.utils.functional:
def weekly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_package_items(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_register_box_chance_type(chance_type=0):
    return 1

/usr/local/lib/python3.8/site-packages/celery/platforms.py:841: SecurityWarning: You're running the worker with superuser privileges: this is
absolutely not recommended!

Please specify a different user using the --uid option.

User information: uid=0 euid=0 gid=0 egid=0

  warnings.warn(SecurityWarning(ROOT_DISCOURAGED.format(
[2025-07-18 17:23:49,149: INFO/MainProcess] Connected to redis://localhost:6379/0
[2025-07-18 17:23:49,157: INFO/MainProcess] mingle: searching for neighbors
[2025-07-18 17:23:50,181: INFO/MainProcess] mingle: all alone
[2025-07-18 17:23:50,193: INFO/MainProcess] celery@iZj6c3ddfpik1c7yalqdt4Z ready.
[2025-07-18 17:24:40,173: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[6d1d49fe-4c0d-4afe-9d14-e64963b5ed4a] received
[2025-07-18 17:24:40,174: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 17:24:40,226: INFO/MainProcess] Task thworker.update_case_records_cache[cb67a35c-cec4-4d6c-a11b-ce9380869d04] received
[2025-07-18 17:24:40,228: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 17:24:50,909: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 17:24:50,912: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[6d1d49fe-4c0d-4afe-9d14-e64963b5ed4a] succeeded in 10.738292567024473s: None
[2025-07-18 17:26:47,351: INFO/ForkPoolWorker-1] 更新 CaseRecord 缓存成功。
[2025-07-18 17:26:47,351: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 17:26:47,355: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[cb67a35c-cec4-4d6c-a11b-ce9380869d04] succeeded in 127.1275785510079s: 'Success: Updated 100 case records cache'
DEBUG:asyncio:Using selector: EpollSelector
DEBUG:celery.utils.functional:
def update_monitor_data_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_user_active_point_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_today_amount():
    return 1

DEBUG:celery.utils.functional:
def check_b2c_trade_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def accumulate(self, *args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def case_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def unlock_chord(self, group_id, callback, interval=0, max_retries=1, result=2, Result=3, GroupResult=4, result_from_tuple=5, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_recharge_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def case_pk_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def xmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def init_system_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_month_amount():
    return 1

DEBUG:celery.utils.functional:
def sync_items_price_task(self):
    return 1

DEBUG:celery.utils.functional:
def xstarmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def delete_old_balance_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_new_user_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def case_bot_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def zbt_buy_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def chunks(task, it, n):
    return 1

DEBUG:celery.utils.functional:
def cleanup_duplicate_roll_room_bets():
    return 1

DEBUG:celery.utils.functional:
def update_domain_verify_switch(value):
    return 1

DEBUG:celery.utils.functional:
def case_pk_join_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def group(self, tasks, result, group_id, partial_args, add_to_parent=0):
    return 1

DEBUG:celery.utils.functional:
def hourly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def chain(*args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def roll_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_daily_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def chord(self, header, body, partial_args=0, interval=1, countdown=2, max_retries=3, eager=4, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def delete_old_log_entries(batch_size=0, days=1):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache(count, batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_enable_based_on_coins(min_coins, max_coins):
    return 1

DEBUG:celery.utils.functional:
def check_envelop_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def daily_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def check_room_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_enable_rule_exam(value):
    return 1

DEBUG:celery.utils.functional:
def update_staff_balance():
    return 1

DEBUG:celery.utils.functional:
def update_enable_pay_method(enable_list):
    return 1

DEBUG:celery.utils.functional:
def backend_cleanup():
    return 1

DEBUG:celery.utils.functional:
def delete_old_case_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache_task(self, count=0):
    return 1

DEBUG:celery.utils.functional:
def check_pendclose_trade_task(self):
    return 1

DEBUG:celery.utils.functional:
def weekly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_package_items(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_register_box_chance_type(chance_type=0):
    return 1

/usr/local/lib/python3.8/site-packages/celery/platforms.py:841: SecurityWarning: You're running the worker with superuser privileges: this is
absolutely not recommended!

Please specify a different user using the --uid option.

User information: uid=0 euid=0 gid=0 egid=0

  warnings.warn(SecurityWarning(ROOT_DISCOURAGED.format(
[2025-07-18 17:27:06,361: INFO/MainProcess] Connected to redis://localhost:6379/0
[2025-07-18 17:27:06,367: INFO/MainProcess] mingle: searching for neighbors
[2025-07-18 17:27:07,380: INFO/MainProcess] mingle: all alone
[2025-07-18 17:27:07,394: INFO/MainProcess] celery@iZj6c3ddfpik1c7yalqdt4Z ready.
[2025-07-18 17:29:40,229: INFO/MainProcess] Task thworker.update_case_records_cache[321461ef-d809-4fa8-ae83-ac4db8c291d8] received
[2025-07-18 17:29:40,232: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 17:29:40,250: INFO/MainProcess] Task thworker.check_b2c_trade_state[5afa5e13-c8b0-4799-945a-633b48122bf1] received
[2025-07-18 17:29:40,252: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 17:29:40,254: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 17:29:40,257: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[5afa5e13-c8b0-4799-945a-633b48122bf1] succeeded in 0.005615313013549894s: 'Success: B2C trade state checked'
[2025-07-18 17:29:40,260: INFO/ForkPoolWorker-1] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 17:30:14,111: INFO/ForkPoolWorker-2] 没有新的 CaseRecord 更新。
[2025-07-18 17:30:14,111: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 17:30:14,114: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[321461ef-d809-4fa8-ae83-ac4db8c291d8] succeeded in 33.882760640990455s: 'Success: Updated 100 case records cache'
[2025-07-18 17:34:40,178: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[89e55291-b036-4731-8831-7dddf78144e7] received
[2025-07-18 17:34:40,179: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 17:34:40,181: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 17:34:40,182: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[89e55291-b036-4731-8831-7dddf78144e7] succeeded in 0.003534103976562619s: None
[2025-07-18 17:34:40,262: INFO/MainProcess] Task thworker.update_case_records_cache[f47445c6-66cd-4006-b7db-127246a90d65] received
[2025-07-18 17:34:40,263: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 17:36:44,746: INFO/ForkPoolWorker-2] 更新 CaseRecord 缓存成功。
[2025-07-18 17:36:44,746: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 17:36:44,749: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[f47445c6-66cd-4006-b7db-127246a90d65] succeeded in 124.48548151401337s: 'Success: Updated 100 case records cache'
DEBUG:asyncio:Using selector: EpollSelector
DEBUG:celery.utils.functional:
def update_monitor_data_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_user_active_point_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_today_amount():
    return 1

DEBUG:celery.utils.functional:
def check_b2c_trade_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def accumulate(self, *args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def case_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def unlock_chord(self, group_id, callback, interval=0, max_retries=1, result=2, Result=3, GroupResult=4, result_from_tuple=5, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_recharge_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def case_pk_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def xmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def init_system_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_month_amount():
    return 1

DEBUG:celery.utils.functional:
def sync_items_price_task(self):
    return 1

DEBUG:celery.utils.functional:
def xstarmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def delete_old_balance_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_new_user_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def case_bot_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def zbt_buy_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def chunks(task, it, n):
    return 1

DEBUG:celery.utils.functional:
def cleanup_duplicate_roll_room_bets():
    return 1

DEBUG:celery.utils.functional:
def update_domain_verify_switch(value):
    return 1

DEBUG:celery.utils.functional:
def case_pk_join_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def group(self, tasks, result, group_id, partial_args, add_to_parent=0):
    return 1

DEBUG:celery.utils.functional:
def hourly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def chain(*args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def roll_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_daily_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def chord(self, header, body, partial_args=0, interval=1, countdown=2, max_retries=3, eager=4, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def delete_old_log_entries(batch_size=0, days=1):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache(count, batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_enable_based_on_coins(min_coins, max_coins):
    return 1

DEBUG:celery.utils.functional:
def check_envelop_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def daily_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def check_room_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_enable_rule_exam(value):
    return 1

DEBUG:celery.utils.functional:
def update_staff_balance():
    return 1

DEBUG:celery.utils.functional:
def update_enable_pay_method(enable_list):
    return 1

DEBUG:celery.utils.functional:
def backend_cleanup():
    return 1

DEBUG:celery.utils.functional:
def delete_old_case_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache_task(self, count=0):
    return 1

DEBUG:celery.utils.functional:
def check_pendclose_trade_task(self):
    return 1

DEBUG:celery.utils.functional:
def weekly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_package_items(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_register_box_chance_type(chance_type=0):
    return 1

/usr/local/lib/python3.8/site-packages/celery/platforms.py:841: SecurityWarning: You're running the worker with superuser privileges: this is
absolutely not recommended!

Please specify a different user using the --uid option.

User information: uid=0 euid=0 gid=0 egid=0

  warnings.warn(SecurityWarning(ROOT_DISCOURAGED.format(
[2025-07-18 17:38:07,184: INFO/MainProcess] Connected to redis://localhost:6379/0
[2025-07-18 17:38:07,188: INFO/MainProcess] mingle: searching for neighbors
[2025-07-18 17:38:08,209: INFO/MainProcess] mingle: all alone
[2025-07-18 17:38:08,237: INFO/MainProcess] celery@iZj6c3ddfpik1c7yalqdt4Z ready.
[2025-07-18 17:39:40,232: INFO/MainProcess] Task thworker.update_case_records_cache[4d93ab21-2f1e-4bd8-b182-564f53b00512] received
[2025-07-18 17:39:40,234: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 17:39:40,237: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 17:39:40,237: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 17:39:40,240: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[4d93ab21-2f1e-4bd8-b182-564f53b00512] succeeded in 0.006826509954407811s: 'Success: Updated 100 case records cache'
[2025-07-18 17:39:40,331: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[f42f64f9-5d66-4331-95a1-e127a4629a6b] received
[2025-07-18 17:39:40,332: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 17:39:40,333: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 17:39:40,334: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[f42f64f9-5d66-4331-95a1-e127a4629a6b] succeeded in 0.002078490040730685s: None
[2025-07-18 17:39:40,343: INFO/MainProcess] Task thworker.check_b2c_trade_state[a288e642-c7fc-49b0-8cd0-bcec966fd351] received
[2025-07-18 17:39:40,344: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 17:39:40,345: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 17:39:40,346: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 17:39:40,346: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[a288e642-c7fc-49b0-8cd0-bcec966fd351] succeeded in 0.002460025018081069s: 'Success: B2C trade state checked'
DEBUG:asyncio:Using selector: EpollSelector
DEBUG:celery.utils.functional:
def update_monitor_data_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_user_active_point_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_today_amount():
    return 1

DEBUG:celery.utils.functional:
def check_b2c_trade_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def accumulate(self, *args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def case_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def unlock_chord(self, group_id, callback, interval=0, max_retries=1, result=2, Result=3, GroupResult=4, result_from_tuple=5, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_recharge_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def case_pk_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def xmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def init_system_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_month_amount():
    return 1

DEBUG:celery.utils.functional:
def sync_items_price_task(self):
    return 1

DEBUG:celery.utils.functional:
def xstarmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def delete_old_balance_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_new_user_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def case_bot_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def chunks(task, it, n):
    return 1

DEBUG:celery.utils.functional:
def cleanup_duplicate_roll_room_bets():
    return 1

DEBUG:celery.utils.functional:
def update_domain_verify_switch(value):
    return 1

DEBUG:celery.utils.functional:
def case_pk_join_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def zbt_buy_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def group(self, tasks, result, group_id, partial_args, add_to_parent=0):
    return 1

DEBUG:celery.utils.functional:
def hourly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def chain(*args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def roll_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_daily_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def chord(self, header, body, partial_args=0, interval=1, countdown=2, max_retries=3, eager=4, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def delete_old_log_entries(batch_size=0, days=1):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache(count, batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_enable_based_on_coins(min_coins, max_coins):
    return 1

DEBUG:celery.utils.functional:
def check_envelop_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def daily_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def check_room_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_enable_rule_exam(value):
    return 1

DEBUG:celery.utils.functional:
def update_staff_balance():
    return 1

DEBUG:celery.utils.functional:
def update_enable_pay_method(enable_list):
    return 1

DEBUG:celery.utils.functional:
def backend_cleanup():
    return 1

DEBUG:celery.utils.functional:
def delete_old_case_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache_task(self, count=0):
    return 1

DEBUG:celery.utils.functional:
def check_pendclose_trade_task(self):
    return 1

DEBUG:celery.utils.functional:
def weekly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_package_items(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_register_box_chance_type(chance_type=0):
    return 1

/usr/local/lib/python3.8/site-packages/celery/platforms.py:841: SecurityWarning: You're running the worker with superuser privileges: this is
absolutely not recommended!

Please specify a different user using the --uid option.

User information: uid=0 euid=0 gid=0 egid=0

  warnings.warn(SecurityWarning(ROOT_DISCOURAGED.format(
[2025-07-18 17:42:53,346: INFO/MainProcess] Connected to redis://localhost:6379/0
[2025-07-18 17:42:53,366: INFO/MainProcess] mingle: searching for neighbors
[2025-07-18 17:42:54,471: INFO/MainProcess] mingle: all alone
[2025-07-18 17:42:54,556: INFO/MainProcess] celery@iZj6c3ddfpik1c7yalqdt4Z ready.
[2025-07-18 17:44:40,235: INFO/MainProcess] Task thworker.update_case_records_cache[7f1c4b3a-0d2c-483f-9391-d9c36ffd5513] received
[2025-07-18 17:44:40,237: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 17:44:40,239: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 17:44:40,239: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 17:44:40,242: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[7f1c4b3a-0d2c-483f-9391-d9c36ffd5513] succeeded in 0.005591424007434398s: 'Success: Updated 100 case records cache'
[2025-07-18 17:44:40,317: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[73294732-55bd-4ef3-b86b-f553a6b4cfed] received
[2025-07-18 17:44:40,318: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 17:44:40,319: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 17:44:40,322: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[73294732-55bd-4ef3-b86b-f553a6b4cfed] succeeded in 0.004191456013359129s: None
[2025-07-18 17:44:40,332: INFO/MainProcess] Task thworker.check_b2c_trade_state[8530c582-0c57-46c3-9413-012b45c49dd3] received
[2025-07-18 17:44:40,333: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 17:44:40,334: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 17:44:40,335: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 17:44:40,337: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[8530c582-0c57-46c3-9413-012b45c49dd3] succeeded in 0.004551685007754713s: 'Success: B2C trade state checked'
[2025-07-18 17:49:40,194: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[f53eabbf-ef7c-4901-a6eb-8efebe01a872] received
[2025-07-18 17:49:40,195: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 17:49:40,329: INFO/MainProcess] Task thworker.update_case_records_cache[eedc44ef-b262-4ad4-a67a-47cf5ec04852] received
[2025-07-18 17:49:40,331: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 17:49:40,335: INFO/ForkPoolWorker-1] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 17:49:40,335: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 17:49:40,339: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[eedc44ef-b262-4ad4-a67a-47cf5ec04852] succeeded in 0.008207817969378084s: 'Success: Updated 100 case records cache'
[2025-07-18 17:49:40,345: INFO/MainProcess] Task thworker.check_b2c_trade_state[fc714ad3-cb06-4986-890f-419bf9cb17f8] received
[2025-07-18 17:49:40,353: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 17:49:40,354: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 17:49:40,356: INFO/ForkPoolWorker-1] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 17:49:40,357: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[fc714ad3-cb06-4986-890f-419bf9cb17f8] succeeded in 0.00535715400474146s: 'Success: B2C trade state checked'
[2025-07-18 17:49:44,199: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 17:49:44,207: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[f53eabbf-ef7c-4901-a6eb-8efebe01a872] succeeded in 4.0123576750047505s: None
DEBUG:asyncio:Using selector: EpollSelector
DEBUG:celery.utils.functional:
def update_monitor_data_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_user_active_point_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_today_amount():
    return 1

DEBUG:celery.utils.functional:
def check_b2c_trade_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def accumulate(self, *args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def case_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def unlock_chord(self, group_id, callback, interval=0, max_retries=1, result=2, Result=3, GroupResult=4, result_from_tuple=5, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_recharge_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def case_pk_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def xmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def init_system_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_month_amount():
    return 1

DEBUG:celery.utils.functional:
def sync_items_price_task(self):
    return 1

DEBUG:celery.utils.functional:
def xstarmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def delete_old_balance_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_new_user_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def case_bot_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def chunks(task, it, n):
    return 1

DEBUG:celery.utils.functional:
def cleanup_duplicate_roll_room_bets():
    return 1

DEBUG:celery.utils.functional:
def update_domain_verify_switch(value):
    return 1

DEBUG:celery.utils.functional:
def case_pk_join_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def zbt_buy_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def group(self, tasks, result, group_id, partial_args, add_to_parent=0):
    return 1

DEBUG:celery.utils.functional:
def hourly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def chain(*args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def roll_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_daily_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def chord(self, header, body, partial_args=0, interval=1, countdown=2, max_retries=3, eager=4, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def delete_old_log_entries(batch_size=0, days=1):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache(count, batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_enable_based_on_coins(min_coins, max_coins):
    return 1

DEBUG:celery.utils.functional:
def check_envelop_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def daily_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def check_room_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_enable_rule_exam(value):
    return 1

DEBUG:celery.utils.functional:
def update_staff_balance():
    return 1

DEBUG:celery.utils.functional:
def update_enable_pay_method(enable_list):
    return 1

DEBUG:celery.utils.functional:
def backend_cleanup():
    return 1

DEBUG:celery.utils.functional:
def delete_old_case_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache_task(self, count=0):
    return 1

DEBUG:celery.utils.functional:
def check_pendclose_trade_task(self):
    return 1

DEBUG:celery.utils.functional:
def weekly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_package_items(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_register_box_chance_type(chance_type=0):
    return 1

/usr/local/lib/python3.8/site-packages/celery/platforms.py:841: SecurityWarning: You're running the worker with superuser privileges: this is
absolutely not recommended!

Please specify a different user using the --uid option.

User information: uid=0 euid=0 gid=0 egid=0

  warnings.warn(SecurityWarning(ROOT_DISCOURAGED.format(
[2025-07-18 17:50:21,719: INFO/MainProcess] Connected to redis://localhost:6379/0
[2025-07-18 17:50:21,735: INFO/MainProcess] mingle: searching for neighbors
[2025-07-18 17:50:22,821: INFO/MainProcess] mingle: all alone
[2025-07-18 17:50:22,846: INFO/MainProcess] celery@iZj6c3ddfpik1c7yalqdt4Z ready.
DEBUG:asyncio:Using selector: EpollSelector
DEBUG:celery.utils.functional:
def update_monitor_data_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_user_active_point_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_today_amount():
    return 1

DEBUG:celery.utils.functional:
def check_b2c_trade_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def accumulate(self, *args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def case_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def unlock_chord(self, group_id, callback, interval=0, max_retries=1, result=2, Result=3, GroupResult=4, result_from_tuple=5, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def update_recharge_box_chance_type(chance_type=0):
    return 1

DEBUG:celery.utils.functional:
def case_pk_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def xmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def init_system_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_month_amount():
    return 1

DEBUG:celery.utils.functional:
def sync_items_price_task(self):
    return 1

DEBUG:celery.utils.functional:
def xstarmap(task, it):
    return 1

DEBUG:celery.utils.functional:
def delete_old_balance_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_new_user_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def case_bot_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def chunks(task, it, n):
    return 1

DEBUG:celery.utils.functional:
def cleanup_duplicate_roll_room_bets():
    return 1

DEBUG:celery.utils.functional:
def update_domain_verify_switch(value):
    return 1

DEBUG:celery.utils.functional:
def case_pk_join_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def group(self, tasks, result, group_id, partial_args, add_to_parent=0):
    return 1

DEBUG:celery.utils.functional:
def hourly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def chain(*args, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def roll_room_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_daily_recharge_limit(value):
    return 1

DEBUG:celery.utils.functional:
def chord(self, header, body, partial_args=0, interval=1, countdown=2, max_retries=3, eager=4, **kwargs):
    return 1

DEBUG:celery.utils.functional:
def delete_old_log_entries(batch_size=0, days=1):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache(count, batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_enable_based_on_coins(min_coins, max_coins):
    return 1

DEBUG:celery.utils.functional:
def check_envelop_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def zbt_buy_worker_task(self):
    return 1

DEBUG:celery.utils.functional:
def daily_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def check_room_state_task(self):
    return 1

DEBUG:celery.utils.functional:
def update_enable_rule_exam(value):
    return 1

DEBUG:celery.utils.functional:
def update_staff_balance():
    return 1

DEBUG:celery.utils.functional:
def update_enable_pay_method(enable_list):
    return 1

DEBUG:celery.utils.functional:
def backend_cleanup():
    return 1

DEBUG:celery.utils.functional:
def delete_old_case_records(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_case_records_cache_task(self, count=0):
    return 1

DEBUG:celery.utils.functional:
def check_pendclose_trade_task(self):
    return 1

DEBUG:celery.utils.functional:
def weekly_lottery_task(self):
    return 1

DEBUG:celery.utils.functional:
def delete_old_package_items(batch_size=0):
    return 1

DEBUG:celery.utils.functional:
def update_register_box_chance_type(chance_type=0):
    return 1

/usr/local/lib/python3.8/site-packages/celery/platforms.py:841: SecurityWarning: You're running the worker with superuser privileges: this is
absolutely not recommended!

Please specify a different user using the --uid option.

User information: uid=0 euid=0 gid=0 egid=0

  warnings.warn(SecurityWarning(ROOT_DISCOURAGED.format(
[2025-07-18 17:52:50,376: INFO/MainProcess] Connected to redis://localhost:6379/0
[2025-07-18 17:52:50,383: INFO/MainProcess] mingle: searching for neighbors
[2025-07-18 17:52:51,404: INFO/MainProcess] mingle: all alone
[2025-07-18 17:52:51,439: INFO/MainProcess] celery@iZj6c3ddfpik1c7yalqdt4Z ready.
[2025-07-18 17:54:40,245: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[3a0699f7-afd2-4507-bbc6-2c50bf416df3] received
[2025-07-18 17:54:40,246: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 17:54:40,331: INFO/MainProcess] Task thworker.update_case_records_cache[87c86e2a-060b-4999-8960-98eb049d9aa1] received
[2025-07-18 17:54:40,333: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 17:54:40,335: INFO/ForkPoolWorker-1] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 17:54:40,335: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 17:54:40,340: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[87c86e2a-060b-4999-8960-98eb049d9aa1] succeeded in 0.007078244001604617s: 'Success: Updated 100 case records cache'
[2025-07-18 17:54:40,348: INFO/MainProcess] Task thworker.check_b2c_trade_state[9791d3c0-abb5-4fff-a84f-6698808ca2e7] received
[2025-07-18 17:54:40,350: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 17:54:40,351: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 17:54:40,353: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[9791d3c0-abb5-4fff-a84f-6698808ca2e7] succeeded in 0.004241480026394129s: 'Success: B2C trade state checked'
[2025-07-18 17:54:40,354: INFO/ForkPoolWorker-1] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 17:54:43,483: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 17:54:43,486: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[3a0699f7-afd2-4507-bbc6-2c50bf416df3] succeeded in 3.2395988709758967s: None
[2025-07-18 17:59:40,225: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[ad9b662b-883d-4351-b794-0fa792ffea1e] received
[2025-07-18 17:59:40,230: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 17:59:40,251: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 17:59:40,252: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[ad9b662b-883d-4351-b794-0fa792ffea1e] succeeded in 0.02192592405481264s: None
[2025-07-18 17:59:40,364: INFO/MainProcess] Task thworker.check_b2c_trade_state[cdbefca7-e568-4985-b99c-d78b52fdc436] received
[2025-07-18 17:59:40,366: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 17:59:40,367: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 17:59:40,386: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 17:59:40,391: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[cdbefca7-e568-4985-b99c-d78b52fdc436] succeeded in 0.025305377959739417s: 'Success: B2C trade state checked'
[2025-07-18 17:59:40,397: INFO/MainProcess] Task thworker.check_b2c_trade_state[8f14764b-a1c4-4cd7-ae4e-ea4eb1228bb5] received
[2025-07-18 17:59:40,398: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 17:59:40,399: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 17:59:40,419: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 17:59:40,426: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[8f14764b-a1c4-4cd7-ae4e-ea4eb1228bb5] succeeded in 0.028208636038471013s: 'Success: B2C trade state checked'
[2025-07-18 18:00:00,018: INFO/MainProcess] Task thworker.hourly_lottery[39c4e80a-122d-4f40-853d-54b0835b0041] received
[2025-07-18 18:00:00,020: INFO/ForkPoolWorker-2] ------------------------ 整点抽奖 ------------------------
[2025-07-18 18:00:00,021: INFO/ForkPoolWorker-2] [Celery] 每小时抽奖任务完成
[2025-07-18 18:00:00,043: INFO/ForkPoolWorker-2] Task thworker.hourly_lottery[39c4e80a-122d-4f40-853d-54b0835b0041] succeeded in 0.023580129956826568s: 'Success: Hourly lottery completed'
[2025-07-18 18:04:40,207: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[89b48f87-a9d1-4693-8996-eddb4e103ccf] received
[2025-07-18 18:04:40,208: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 18:04:40,344: INFO/MainProcess] Task thworker.update_case_records_cache[7c78dc88-e1b9-4023-8617-baa83c8ce3a6] received
[2025-07-18 18:04:40,345: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 18:04:48,838: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 18:04:48,840: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[89b48f87-a9d1-4693-8996-eddb4e103ccf] succeeded in 8.631543378985953s: None
[2025-07-18 18:06:31,776: INFO/ForkPoolWorker-1] 更新 CaseRecord 缓存成功。
[2025-07-18 18:06:31,776: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 18:06:31,778: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[7c78dc88-e1b9-4023-8617-baa83c8ce3a6] succeeded in 111.43272241204977s: 'Success: Updated 100 case records cache'
[2025-07-18 18:09:40,210: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[7fbe75b0-ae40-4b8a-8979-44c3bd09dc7e] received
[2025-07-18 18:09:40,212: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 18:09:40,348: INFO/MainProcess] Task thworker.update_case_records_cache[8719146e-ef24-4f6f-b808-b6c3ac6c955e] received
[2025-07-18 18:09:40,349: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 18:09:48,825: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 18:09:48,828: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[7fbe75b0-ae40-4b8a-8979-44c3bd09dc7e] succeeded in 8.61587509396486s: None
[2025-07-18 18:10:08,005: INFO/ForkPoolWorker-1] 没有新的 CaseRecord 更新。
[2025-07-18 18:10:08,005: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 18:10:08,006: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[8719146e-ef24-4f6f-b808-b6c3ac6c955e] succeeded in 27.657570807030424s: 'Success: Updated 100 case records cache'
[2025-07-18 18:14:40,213: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[66ef2120-5ed1-4141-b374-f974841a6a08] received
[2025-07-18 18:14:40,214: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 18:14:40,369: INFO/MainProcess] Task thworker.update_case_records_cache[6d68fd87-83a1-448d-aaea-7c9c970e8d11] received
[2025-07-18 18:14:40,370: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 18:14:53,998: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 18:14:54,000: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[66ef2120-5ed1-4141-b374-f974841a6a08] succeeded in 13.786461786017753s: None
[2025-07-18 18:17:11,279: INFO/ForkPoolWorker-1] 更新 CaseRecord 缓存成功。
[2025-07-18 18:17:11,279: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 18:17:11,280: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[6d68fd87-83a1-448d-aaea-7c9c970e8d11] succeeded in 150.91061379702296s: 'Success: Updated 100 case records cache'
[2025-07-18 18:19:40,215: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[293bfda2-ba26-4a10-93ab-18860f6a8df2] received
[2025-07-18 18:19:40,216: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 18:19:40,372: INFO/MainProcess] Task thworker.update_case_records_cache[3cc279cc-0c43-420f-b966-661f833ba23c] received
[2025-07-18 18:19:40,373: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 18:19:45,017: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 18:19:45,020: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[293bfda2-ba26-4a10-93ab-18860f6a8df2] succeeded in 4.803845526999794s: None
[2025-07-18 18:20:11,096: INFO/ForkPoolWorker-1] 没有新的 CaseRecord 更新。
[2025-07-18 18:20:11,096: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 18:20:11,097: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[3cc279cc-0c43-420f-b966-661f833ba23c] succeeded in 30.724762036988977s: 'Success: Updated 100 case records cache'
[2025-07-18 18:24:40,218: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[29177d52-1840-4df7-bfa8-b95479efbe12] received
[2025-07-18 18:24:40,219: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 18:24:40,374: INFO/MainProcess] Task thworker.update_case_records_cache[e87823ef-a3a0-488f-96a6-b09dfac84d07] received
[2025-07-18 18:24:40,375: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 18:24:49,482: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 18:24:49,484: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[29177d52-1840-4df7-bfa8-b95479efbe12] succeeded in 9.265205161995254s: None
[2025-07-18 18:26:32,003: INFO/ForkPoolWorker-1] 更新 CaseRecord 缓存成功。
[2025-07-18 18:26:32,003: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 18:26:32,006: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[e87823ef-a3a0-488f-96a6-b09dfac84d07] succeeded in 111.63092277798569s: 'Success: Updated 100 case records cache'
[2025-07-18 18:29:40,283: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[01d22031-590f-449f-8d68-c8923ebca83e] received
[2025-07-18 18:29:40,284: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 18:29:40,385: INFO/MainProcess] Task thworker.update_case_records_cache[8c1fc5bb-d9f0-4aaf-ad2b-762f7b5537fc] received
[2025-07-18 18:29:40,387: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 18:29:40,392: INFO/ForkPoolWorker-1] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 18:29:40,392: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 18:29:40,414: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[8c1fc5bb-d9f0-4aaf-ad2b-762f7b5537fc] succeeded in 0.02796502801356837s: 'Success: Updated 100 case records cache'
[2025-07-18 18:29:40,458: INFO/MainProcess] Task thworker.check_b2c_trade_state[ac71ffcb-99cd-4306-8d7e-5f805d7b54a1] received
[2025-07-18 18:29:40,488: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 18:29:40,499: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 18:29:40,514: INFO/ForkPoolWorker-1] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 18:29:40,563: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[ac71ffcb-99cd-4306-8d7e-5f805d7b54a1] succeeded in 0.07965681003406644s: 'Success: B2C trade state checked'
[2025-07-18 18:29:41,148: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 18:29:41,167: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[01d22031-590f-449f-8d68-c8923ebca83e] succeeded in 0.8828635329846293s: None
[2025-07-18 18:34:40,534: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[4b346365-8171-4ed9-bf90-a656539a1850] received
[2025-07-18 18:34:40,536: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 18:34:40,638: INFO/MainProcess] Task thworker.update_case_records_cache[d7866919-b40f-4dd2-963e-d6d0645ddeca] received
[2025-07-18 18:34:40,639: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 18:34:41,795: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 18:34:41,869: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[4b346365-8171-4ed9-bf90-a656539a1850] succeeded in 1.3333351670298725s: None
[2025-07-18 18:35:03,033: INFO/ForkPoolWorker-1] 更新 CaseRecord 缓存成功。
[2025-07-18 18:35:03,033: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 18:35:03,094: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[d7866919-b40f-4dd2-963e-d6d0645ddeca] succeeded in 22.454889499989804s: 'Success: Updated 100 case records cache'
[2025-07-18 18:39:40,733: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[4cc5b7cd-f865-4098-be12-1e6f622d2188] received
[2025-07-18 18:39:40,734: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 18:39:40,860: INFO/MainProcess] Task thworker.update_case_records_cache[4f35bb5f-ee30-4d70-9570-c6cf5a1f5789] received
[2025-07-18 18:39:40,861: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 18:39:41,258: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 18:39:41,310: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[4cc5b7cd-f865-4098-be12-1e6f622d2188] succeeded in 0.5760278929956257s: None
[2025-07-18 18:40:08,457: INFO/ForkPoolWorker-1] 更新 CaseRecord 缓存成功。
[2025-07-18 18:40:08,457: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 18:40:08,518: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[4f35bb5f-ee30-4d70-9570-c6cf5a1f5789] succeeded in 27.656949252996128s: 'Success: Updated 100 case records cache'
[2025-07-18 18:44:40,743: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[c9a1604a-1942-458e-9834-a92309b4407c] received
[2025-07-18 18:44:40,796: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 18:44:40,828: INFO/MainProcess] Task thworker.update_case_records_cache[0a3b0eb4-9369-4c8f-b3e6-824f14e1390a] received
[2025-07-18 18:44:40,832: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 18:44:40,853: INFO/ForkPoolWorker-1] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 18:44:40,853: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 18:44:40,898: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[0a3b0eb4-9369-4c8f-b3e6-824f14e1390a] succeeded in 0.06597200996475294s: 'Success: Updated 100 case records cache'
[2025-07-18 18:44:40,990: INFO/MainProcess] Task thworker.check_b2c_trade_state[e1f00efb-7204-4223-a07a-cbdab2cbb29b] received
[2025-07-18 18:44:41,006: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 18:44:41,007: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 18:44:41,026: INFO/ForkPoolWorker-1] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 18:44:41,045: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[e1f00efb-7204-4223-a07a-cbdab2cbb29b] succeeded in 0.038989487977232784s: 'Success: B2C trade state checked'
[2025-07-18 18:44:41,447: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 18:44:41,570: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[c9a1604a-1942-458e-9834-a92309b4407c] succeeded in 0.7739916599821299s: None
[2025-07-18 18:49:40,753: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[0637a3a7-00af-464a-9ce8-0145cda07bc0] received
[2025-07-18 18:49:40,754: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 18:49:40,773: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 18:49:40,812: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[0637a3a7-00af-464a-9ce8-0145cda07bc0] succeeded in 0.05778017902048305s: None
[2025-07-18 18:49:40,862: INFO/MainProcess] Task thworker.update_case_records_cache[1c3168c5-6ffc-4a14-9d2c-9a5027f2a9f2] received
[2025-07-18 18:49:40,864: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 18:49:40,896: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 18:49:40,896: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 18:49:40,945: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[1c3168c5-6ffc-4a14-9d2c-9a5027f2a9f2] succeeded in 0.08183066197670996s: 'Success: Updated 100 case records cache'
[2025-07-18 18:49:40,957: INFO/MainProcess] Task thworker.check_b2c_trade_state[60ef18e8-cf66-441a-9024-da5e786ea1d0] received
[2025-07-18 18:49:40,983: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 18:49:40,990: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 18:49:41,003: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 18:49:41,020: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[60ef18e8-cf66-441a-9024-da5e786ea1d0] succeeded in 0.05224364099558443s: 'Success: B2C trade state checked'
[2025-07-18 18:54:40,832: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[01fc3364-3e0d-4bba-9905-34cb4676a9dd] received
[2025-07-18 18:54:40,836: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 18:54:40,891: INFO/MainProcess] Task thworker.update_case_records_cache[796aadac-6b79-4b5a-a24f-8a558b826d4b] received
[2025-07-18 18:54:40,896: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 18:54:40,902: INFO/ForkPoolWorker-1] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 18:54:40,902: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 18:54:40,911: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[796aadac-6b79-4b5a-a24f-8a558b826d4b] succeeded in 0.015818338026292622s: 'Success: Updated 100 case records cache'
[2025-07-18 18:54:40,987: INFO/MainProcess] Task thworker.check_b2c_trade_state[8cbfa527-d993-455b-8754-1daebf949dfb] received
[2025-07-18 18:54:40,991: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 18:54:40,992: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 18:54:41,006: INFO/ForkPoolWorker-1] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 18:54:41,033: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[8cbfa527-d993-455b-8754-1daebf949dfb] succeeded in 0.04516489600064233s: 'Success: B2C trade state checked'
[2025-07-18 18:54:41,321: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 18:54:41,341: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[01fc3364-3e0d-4bba-9905-34cb4676a9dd] succeeded in 0.5047133100451902s: None
[2025-07-18 18:59:40,935: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[43131649-8111-493b-a7c6-2246a4cf4012] received
[2025-07-18 18:59:40,961: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 18:59:40,989: INFO/MainProcess] Task thworker.update_case_records_cache[54b31c5a-a86b-4259-b32b-90ae43942067] received
[2025-07-18 18:59:40,990: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 18:59:41,043: INFO/ForkPoolWorker-1] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 18:59:41,043: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 18:59:41,115: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[54b31c5a-a86b-4259-b32b-90ae43942067] succeeded in 0.12529702100437135s: 'Success: Updated 100 case records cache'
[2025-07-18 18:59:41,193: INFO/MainProcess] Task thworker.check_b2c_trade_state[153528d2-81a8-4faf-a2f3-44b56fc3b0c2] received
[2025-07-18 18:59:41,198: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 18:59:41,198: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 18:59:41,222: INFO/ForkPoolWorker-1] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 18:59:41,241: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[153528d2-81a8-4faf-a2f3-44b56fc3b0c2] succeeded in 0.04410770599497482s: 'Success: B2C trade state checked'
[2025-07-18 18:59:41,612: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 18:59:41,685: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[43131649-8111-493b-a7c6-2246a4cf4012] succeeded in 0.7235474660410546s: None
[2025-07-18 19:00:00,113: INFO/MainProcess] Task thworker.hourly_lottery[cad3c953-3196-435f-b9b9-76e61df6d4f5] received
[2025-07-18 19:00:00,122: INFO/ForkPoolWorker-2] ------------------------ 整点抽奖 ------------------------
[2025-07-18 19:00:00,123: INFO/ForkPoolWorker-2] [Celery] 每小时抽奖任务完成
[2025-07-18 19:00:00,161: INFO/ForkPoolWorker-2] Task thworker.hourly_lottery[cad3c953-3196-435f-b9b9-76e61df6d4f5] succeeded in 0.04744516999926418s: 'Success: Hourly lottery completed'
[2025-07-18 19:04:40,976: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[ae02a102-4635-4df3-bef7-3be5b47c0d86] received
[2025-07-18 19:04:40,978: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 19:04:41,001: INFO/MainProcess] Task thworker.update_case_records_cache[e39d2d23-8490-431b-b8f1-cabd9367471b] received
[2025-07-18 19:04:41,024: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 19:04:41,034: INFO/ForkPoolWorker-1] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 19:04:41,034: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 19:04:41,048: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[e39d2d23-8490-431b-b8f1-cabd9367471b] succeeded in 0.025086276000365615s: 'Success: Updated 100 case records cache'
[2025-07-18 19:04:41,095: INFO/MainProcess] Task thworker.check_b2c_trade_state[3ac813e1-1515-4462-8044-8fe2c445adec] received
[2025-07-18 19:04:41,106: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 19:04:41,107: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 19:04:41,110: INFO/ForkPoolWorker-1] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 19:04:41,111: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[3ac813e1-1515-4462-8044-8fe2c445adec] succeeded in 0.014863980002701283s: 'Success: B2C trade state checked'
[2025-07-18 19:04:41,459: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 19:04:41,530: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[ae02a102-4635-4df3-bef7-3be5b47c0d86] succeeded in 0.5530047469655983s: None
[2025-07-18 19:09:41,126: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[6a5a4f54-940f-4028-8e68-495bbfb27805] received
[2025-07-18 19:09:41,128: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 19:09:41,343: INFO/MainProcess] Task thworker.update_case_records_cache[237acd7c-3e01-4387-b8bc-8724a4e4658d] received
[2025-07-18 19:09:41,379: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 19:09:41,398: INFO/ForkPoolWorker-1] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 19:09:41,398: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 19:09:41,420: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[237acd7c-3e01-4387-b8bc-8724a4e4658d] succeeded in 0.04095000895904377s: 'Success: Updated 100 case records cache'
[2025-07-18 19:09:41,700: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 19:09:41,782: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[6a5a4f54-940f-4028-8e68-495bbfb27805] succeeded in 0.6547423719894141s: None
[2025-07-18 19:14:41,110: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[93755415-20b0-4579-89ad-64ef6ac56833] received
[2025-07-18 19:14:41,112: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 19:14:41,131: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 19:14:41,185: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[93755415-20b0-4579-89ad-64ef6ac56833] succeeded in 0.07339653302915394s: None
[2025-07-18 19:14:41,214: INFO/MainProcess] Task thworker.update_case_records_cache[80eb9e35-9e7d-49a7-96ac-4d9898ce4294] received
[2025-07-18 19:14:41,215: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 19:14:41,238: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 19:14:41,238: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 19:14:41,289: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[80eb9e35-9e7d-49a7-96ac-4d9898ce4294] succeeded in 0.07393054099520668s: 'Success: Updated 100 case records cache'
[2025-07-18 19:14:41,295: INFO/MainProcess] Task thworker.check_b2c_trade_state[f3f53d04-f25a-4e03-839c-71c947ab1ac1] received
[2025-07-18 19:14:41,298: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 19:14:41,299: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 19:14:41,300: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 19:14:41,301: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[f3f53d04-f25a-4e03-839c-71c947ab1ac1] succeeded in 0.0033654129947535694s: 'Success: B2C trade state checked'
[2025-07-18 19:19:41,253: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[7f39f3ca-9413-4cdd-9681-721dfca5adc7] received
[2025-07-18 19:19:41,260: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 19:19:41,361: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[dfc2628c-6782-4f73-a7e4-2d5e531febdc] received
[2025-07-18 19:19:41,362: INFO/ForkPoolWorker-1] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 19:19:41,396: INFO/ForkPoolWorker-1] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 19:19:41,446: INFO/ForkPoolWorker-1] Task roll.tasks.cleanup_duplicate_roll_room_bets[dfc2628c-6782-4f73-a7e4-2d5e531febdc] succeeded in 0.08415050798794255s: None
[2025-07-18 19:19:41,561: INFO/MainProcess] Task thworker.update_case_records_cache[db5a39ae-fdfb-4be4-a348-4a0358d13357] received
[2025-07-18 19:19:41,562: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 19:19:41,580: INFO/ForkPoolWorker-1] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 19:19:41,580: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 19:19:41,600: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[db5a39ae-fdfb-4be4-a348-4a0358d13357] succeeded in 0.03871400497155264s: 'Success: Updated 100 case records cache'
[2025-07-18 19:19:41,945: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 19:19:42,051: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[7f39f3ca-9413-4cdd-9681-721dfca5adc7] succeeded in 0.7914118939661421s: None
[2025-07-18 19:24:41,414: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[9e1f1e67-5ffd-4b61-9a22-8263df0747e8] received
[2025-07-18 19:24:41,415: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 19:24:41,546: INFO/MainProcess] Task thworker.update_case_records_cache[6e5e702a-d497-427e-b4a2-246e7084799d] received
[2025-07-18 19:24:41,557: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 19:24:41,572: INFO/ForkPoolWorker-1] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 19:24:41,572: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 19:24:41,577: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[6e5e702a-d497-427e-b4a2-246e7084799d] succeeded in 0.020715571008622646s: 'Success: Updated 100 case records cache'
[2025-07-18 19:24:41,875: INFO/MainProcess] Task thworker.check_b2c_trade_state[0df9499e-c23d-4f87-9e2e-773f103ab8b8] received
[2025-07-18 19:24:41,876: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 19:24:41,878: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 19:24:41,897: INFO/ForkPoolWorker-1] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 19:24:41,950: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[0df9499e-c23d-4f87-9e2e-773f103ab8b8] succeeded in 0.07419457100331783s: 'Success: B2C trade state checked'
[2025-07-18 19:24:42,071: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 19:24:42,210: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[9e1f1e67-5ffd-4b61-9a22-8263df0747e8] succeeded in 0.7945814920240082s: None
[2025-07-18 19:29:41,495: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[0440a802-54e9-4641-b8a6-2ce223f4772c] received
[2025-07-18 19:29:41,496: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 19:29:41,592: INFO/MainProcess] Task thworker.update_case_records_cache[266cbeda-a428-4bee-8172-f934bd68c424] received
[2025-07-18 19:29:41,593: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 19:29:41,642: INFO/ForkPoolWorker-1] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 19:29:41,642: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 19:29:41,690: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[266cbeda-a428-4bee-8172-f934bd68c424] succeeded in 0.09781719400780275s: 'Success: Updated 100 case records cache'
[2025-07-18 19:29:41,849: INFO/MainProcess] Task thworker.check_b2c_trade_state[1f80c7f9-f2ce-4ac1-bcf9-205373bcf0f4] received
[2025-07-18 19:29:41,871: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 19:29:41,892: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 19:29:41,901: INFO/ForkPoolWorker-1] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 19:29:41,940: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 19:29:41,969: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[1f80c7f9-f2ce-4ac1-bcf9-205373bcf0f4] succeeded in 0.11860124801751226s: 'Success: B2C trade state checked'
[2025-07-18 19:29:42,022: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[0440a802-54e9-4641-b8a6-2ce223f4772c] succeeded in 0.5260160629986785s: None
[2025-07-18 19:34:41,611: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[91d632eb-fbf2-4a4c-ba9e-4da559cc75d9] received
[2025-07-18 19:34:41,612: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 19:34:41,629: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 19:34:41,650: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[91d632eb-fbf2-4a4c-ba9e-4da559cc75d9] succeeded in 0.037948212993796915s: None
[2025-07-18 19:34:41,677: INFO/MainProcess] Task thworker.update_case_records_cache[c5205b79-00a4-4922-923b-583a66288894] received
[2025-07-18 19:34:41,719: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 19:34:41,740: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 19:34:41,740: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 19:34:41,761: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[c5205b79-00a4-4922-923b-583a66288894] succeeded in 0.042170166969299316s: 'Success: Updated 100 case records cache'
[2025-07-18 19:34:41,886: INFO/MainProcess] Task thworker.check_b2c_trade_state[07e97e5a-9d46-4f96-9e4b-8dfb0b5e5f08] received
[2025-07-18 19:34:41,888: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 19:34:41,889: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 19:34:41,891: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 19:34:41,893: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[07e97e5a-9d46-4f96-9e4b-8dfb0b5e5f08] succeeded in 0.005884685961063951s: 'Success: B2C trade state checked'
[2025-07-18 19:39:41,671: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[3331f60b-69fa-4150-a33d-d3c9b39449b2] received
[2025-07-18 19:39:41,672: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 19:39:41,689: INFO/MainProcess] Task thworker.update_case_records_cache[1f11a746-7bb8-45c5-9ffa-e8cc37aec36e] received
[2025-07-18 19:39:41,690: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 19:39:41,717: INFO/ForkPoolWorker-1] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 19:39:41,717: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 19:39:41,739: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[1f11a746-7bb8-45c5-9ffa-e8cc37aec36e] succeeded in 0.049634385970421135s: 'Success: Updated 100 case records cache'
[2025-07-18 19:39:41,930: INFO/MainProcess] Task thworker.check_b2c_trade_state[8681ebe1-01bd-4b74-93ee-d010b4107796] received
[2025-07-18 19:39:41,951: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 19:39:41,954: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 19:39:41,971: INFO/ForkPoolWorker-1] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 19:39:41,972: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[8681ebe1-01bd-4b74-93ee-d010b4107796] succeeded in 0.02381339098792523s: 'Success: B2C trade state checked'
[2025-07-18 19:39:42,123: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 19:39:42,187: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[3331f60b-69fa-4150-a33d-d3c9b39449b2] succeeded in 0.5156952270190232s: None
[2025-07-18 19:44:41,751: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[cf45eddf-42c2-486d-8777-28b0dedd3a5b] received
[2025-07-18 19:44:41,752: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 19:44:41,815: INFO/MainProcess] Task thworker.update_case_records_cache[fd566e07-4f58-4923-9e4a-7306efb3a315] received
[2025-07-18 19:44:41,817: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 19:44:42,272: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 19:44:42,354: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[cf45eddf-42c2-486d-8777-28b0dedd3a5b] succeeded in 0.6016542470315471s: None
[2025-07-18 19:45:04,568: INFO/ForkPoolWorker-1] 更新 CaseRecord 缓存成功。
[2025-07-18 19:45:04,568: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 19:45:04,599: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[fd566e07-4f58-4923-9e4a-7306efb3a315] succeeded in 22.78273642802378s: 'Success: Updated 100 case records cache'
[2025-07-18 19:49:41,678: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[9bb835b5-9f3a-4bc4-a20f-a89ad1ebb142] received
[2025-07-18 19:49:41,680: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 19:49:41,683: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 19:49:41,688: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[9bb835b5-9f3a-4bc4-a20f-a89ad1ebb142] succeeded in 0.008494184003211558s: None
[2025-07-18 19:49:41,810: INFO/MainProcess] Task thworker.update_case_records_cache[d88244d2-87fd-4c12-a8a2-a5c8b0d43848] received
[2025-07-18 19:49:41,811: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 19:49:41,813: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 19:49:41,813: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 19:49:41,823: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[d88244d2-87fd-4c12-a8a2-a5c8b0d43848] succeeded in 0.012378044018987566s: 'Success: Updated 100 case records cache'
[2025-07-18 19:49:41,951: INFO/MainProcess] Task thworker.check_b2c_trade_state[cc7201c4-bd04-4b01-aa27-fd7bb9200915] received
[2025-07-18 19:49:41,952: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 19:49:41,953: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 19:49:41,962: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 19:49:41,982: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[cc7201c4-bd04-4b01-aa27-fd7bb9200915] succeeded in 0.03027581999776885s: 'Success: B2C trade state checked'
[2025-07-18 19:54:41,848: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[f4309d87-caee-499a-8fb5-2427196bcbcf] received
[2025-07-18 19:54:41,854: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 19:54:41,894: INFO/MainProcess] Task thworker.update_case_records_cache[29929005-ab16-45ea-bddb-a6e386f7bf77] received
[2025-07-18 19:54:41,895: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 19:54:41,916: INFO/ForkPoolWorker-1] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 19:54:41,916: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 19:54:41,957: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[29929005-ab16-45ea-bddb-a6e386f7bf77] succeeded in 0.06256297399522737s: 'Success: Updated 100 case records cache'
[2025-07-18 19:54:42,313: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 19:54:42,401: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[f4309d87-caee-499a-8fb5-2427196bcbcf] succeeded in 0.5471110729849897s: None
[2025-07-18 19:59:41,972: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[2b40e1cf-9e73-4f0c-b8ab-7489526c5b6b] received
[2025-07-18 19:59:41,978: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 19:59:42,024: INFO/MainProcess] Task thworker.update_case_records_cache[654a71ad-cfb6-4f8a-889c-32850dabfd86] received
[2025-07-18 19:59:42,040: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 19:59:42,059: INFO/ForkPoolWorker-1] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 19:59:42,059: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 19:59:42,089: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[654a71ad-cfb6-4f8a-889c-32850dabfd86] succeeded in 0.05001669505145401s: 'Success: Updated 100 case records cache'
[2025-07-18 19:59:42,589: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 19:59:42,595: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[2b40e1cf-9e73-4f0c-b8ab-7489526c5b6b] succeeded in 0.6175202089943923s: None
[2025-07-18 20:00:00,125: INFO/MainProcess] Task thworker.hourly_lottery[d9e11e9a-4555-4d1b-b287-1ec3af531848] received
[2025-07-18 20:00:00,127: INFO/ForkPoolWorker-2] ------------------------ 整点抽奖 ------------------------
[2025-07-18 20:00:00,128: INFO/ForkPoolWorker-2] [Celery] 每小时抽奖任务完成
[2025-07-18 20:00:00,132: INFO/ForkPoolWorker-2] Task thworker.hourly_lottery[d9e11e9a-4555-4d1b-b287-1ec3af531848] succeeded in 0.0052201690268702805s: 'Success: Hourly lottery completed'
[2025-07-18 20:04:41,960: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[44e574f3-da09-4e7f-a75c-52ced7703aec] received
[2025-07-18 20:04:41,962: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 20:04:41,963: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 20:04:41,972: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[44e574f3-da09-4e7f-a75c-52ced7703aec] succeeded in 0.010621948051266372s: None
[2025-07-18 20:04:42,025: INFO/MainProcess] Task thworker.update_case_records_cache[5e668c69-c791-47c4-be94-dc154c73f8fe] received
[2025-07-18 20:04:42,026: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 20:04:42,030: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 20:04:42,030: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 20:04:42,033: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[5e668c69-c791-47c4-be94-dc154c73f8fe] succeeded in 0.006432837981265038s: 'Success: Updated 100 case records cache'
[2025-07-18 20:04:42,101: INFO/MainProcess] Task thworker.check_b2c_trade_state[3645a8cc-75a4-498f-8ee0-af9e066d98b7] received
[2025-07-18 20:04:42,119: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 20:04:42,121: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 20:04:42,131: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 20:04:42,168: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[3645a8cc-75a4-498f-8ee0-af9e066d98b7] succeeded in 0.06650393997551873s: 'Success: B2C trade state checked'
[2025-07-18 20:09:42,097: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[69452d6d-60af-41a7-a829-34c711c7b7f4] received
[2025-07-18 20:09:42,135: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 20:09:42,185: INFO/MainProcess] Task thworker.update_case_records_cache[55c6fcbd-e0ac-401f-ab98-3f130f4e4c8d] received
[2025-07-18 20:09:42,189: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 20:09:42,214: INFO/ForkPoolWorker-1] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 20:09:42,214: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 20:09:42,284: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[55c6fcbd-e0ac-401f-ab98-3f130f4e4c8d] succeeded in 0.09499131696065888s: 'Success: Updated 100 case records cache'
[2025-07-18 20:09:42,687: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 20:09:42,754: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[69452d6d-60af-41a7-a829-34c711c7b7f4] succeeded in 0.6193366289953701s: None
[2025-07-18 20:14:42,050: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[198fbdbe-5e86-4204-9c9b-49163a634a3a] received
[2025-07-18 20:14:42,051: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 20:14:42,058: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 20:14:42,060: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[198fbdbe-5e86-4204-9c9b-49163a634a3a] succeeded in 0.009500261978246272s: None
[2025-07-18 20:14:42,278: INFO/MainProcess] Task thworker.update_case_records_cache[843d98b1-100e-426e-8f55-104053e5c6f5] received
[2025-07-18 20:14:42,280: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 20:14:42,306: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 20:14:42,306: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 20:14:42,354: INFO/MainProcess] Task thworker.check_b2c_trade_state[222e53f6-ed70-4c5c-b062-98383126c779] received
[2025-07-18 20:14:42,359: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 20:14:42,360: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 20:14:42,387: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[843d98b1-100e-426e-8f55-104053e5c6f5] succeeded in 0.10704163298942149s: 'Success: Updated 100 case records cache'
[2025-07-18 20:14:42,422: INFO/ForkPoolWorker-1] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 20:14:42,471: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[222e53f6-ed70-4c5c-b062-98383126c779] succeeded in 0.11295626999344677s: 'Success: B2C trade state checked'
[2025-07-18 20:19:42,175: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[f84f4e01-90bc-4bc3-800e-aeee771b3a95] received
[2025-07-18 20:19:42,177: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 20:19:42,275: INFO/MainProcess] Task thworker.update_case_records_cache[008d8136-cd54-422c-9165-5868ab3130c5] received
[2025-07-18 20:19:42,276: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 20:19:42,278: INFO/ForkPoolWorker-1] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 20:19:42,278: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 20:19:42,280: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[008d8136-cd54-422c-9165-5868ab3130c5] succeeded in 0.0037891590036451817s: 'Success: Updated 100 case records cache'
[2025-07-18 20:19:42,386: INFO/MainProcess] Task thworker.check_b2c_trade_state[52978924-adc4-4459-9246-86740610b9bc] received
[2025-07-18 20:19:42,387: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 20:19:42,389: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 20:19:42,408: INFO/ForkPoolWorker-1] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 20:19:42,455: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[52978924-adc4-4459-9246-86740610b9bc] succeeded in 0.06810236698947847s: 'Success: B2C trade state checked'
[2025-07-18 20:19:42,630: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 20:19:42,692: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[f84f4e01-90bc-4bc3-800e-aeee771b3a95] succeeded in 0.5155706419609487s: None
[2025-07-18 20:24:42,146: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[af8aad9b-cffd-49bc-99f3-341b664358b4] received
[2025-07-18 20:24:42,147: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 20:24:42,161: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 20:24:42,166: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[af8aad9b-cffd-49bc-99f3-341b664358b4] succeeded in 0.01916686602635309s: None
[2025-07-18 20:24:42,264: INFO/MainProcess] Task thworker.update_case_records_cache[0ab77183-9a1f-4101-90e4-9648777836ad] received
[2025-07-18 20:24:42,281: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 20:24:42,298: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 20:24:42,298: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 20:24:42,305: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[0ab77183-9a1f-4101-90e4-9648777836ad] succeeded in 0.024134206003509462s: 'Success: Updated 100 case records cache'
[2025-07-18 20:24:42,422: INFO/MainProcess] Task thworker.check_b2c_trade_state[e3ef621e-d2f6-4f6c-974f-0e767a7259d1] received
[2025-07-18 20:24:42,424: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 20:24:42,425: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 20:24:42,438: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 20:24:42,462: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[e3ef621e-d2f6-4f6c-974f-0e767a7259d1] succeeded in 0.03891694702906534s: 'Success: B2C trade state checked'
[2025-07-18 20:29:42,301: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[4e15bdac-f754-4a68-a475-e37e5f8f93ae] received
[2025-07-18 20:29:42,314: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 20:29:42,344: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 20:29:42,368: INFO/MainProcess] Task thworker.update_case_records_cache[9546cd9e-9ac8-40fb-88a6-3adde377ed32] received
[2025-07-18 20:29:42,369: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 20:29:42,390: INFO/ForkPoolWorker-1] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 20:29:42,390: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 20:29:42,453: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[4e15bdac-f754-4a68-a475-e37e5f8f93ae] succeeded in 0.13826649403199553s: None
[2025-07-18 20:29:42,489: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[9546cd9e-9ac8-40fb-88a6-3adde377ed32] succeeded in 0.1204657509806566s: 'Success: Updated 100 case records cache'
[2025-07-18 20:34:42,180: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[9a899301-d4c3-465e-a7be-7def7a53e0ab] received
[2025-07-18 20:34:42,182: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 20:34:42,183: INFO/ForkPoolWorker-2] 在5分钟内已执行过清理，跳过此次执行。
[2025-07-18 20:34:42,188: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[9a899301-d4c3-465e-a7be-7def7a53e0ab] succeeded in 0.006334091944154352s: None
[2025-07-18 20:34:42,382: INFO/MainProcess] Task thworker.update_case_records_cache[3fda182f-9887-4a54-b2e3-c83a75ddd35e] received
[2025-07-18 20:34:42,383: INFO/ForkPoolWorker-2] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 20:34:42,403: INFO/ForkPoolWorker-2] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 20:34:42,403: INFO/ForkPoolWorker-2] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 20:34:42,467: INFO/ForkPoolWorker-2] Task thworker.update_case_records_cache[3fda182f-9887-4a54-b2e3-c83a75ddd35e] succeeded in 0.08384923200355843s: 'Success: Updated 100 case records cache'
[2025-07-18 20:34:42,477: INFO/MainProcess] Task thworker.check_b2c_trade_state[95052713-37bc-4470-9991-d5fafce63134] received
[2025-07-18 20:34:42,483: INFO/ForkPoolWorker-2] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 20:34:42,483: INFO/ForkPoolWorker-2] [Celery] B2C交易状态检查完成
[2025-07-18 20:34:42,488: INFO/ForkPoolWorker-2] Task thworker.check_b2c_trade_state[95052713-37bc-4470-9991-d5fafce63134] succeeded in 0.006461989018134773s: 'Success: B2C trade state checked'
[2025-07-18 20:34:42,507: INFO/ForkPoolWorker-2] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 20:39:42,263: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[4cd0c141-57af-4eff-92fa-e18f1d9d9fdc] received
[2025-07-18 20:39:42,264: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 20:39:42,411: INFO/MainProcess] Task thworker.update_case_records_cache[2db506c4-ffb7-4c9c-bed8-194893d487ab] received
[2025-07-18 20:39:42,421: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 20:39:42,438: INFO/ForkPoolWorker-1] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 20:39:42,438: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 20:39:42,471: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[2db506c4-ffb7-4c9c-bed8-194893d487ab] succeeded in 0.050530171021819115s: 'Success: Updated 100 case records cache'
[2025-07-18 20:39:42,546: INFO/MainProcess] Task thworker.check_b2c_trade_state[e6394dad-b2b6-4d92-8262-3c6e260a93ce] received
[2025-07-18 20:39:42,548: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 20:39:42,548: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 20:39:42,580: INFO/ForkPoolWorker-1] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 20:39:42,602: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[e6394dad-b2b6-4d92-8262-3c6e260a93ce] succeeded in 0.05491646198788658s: 'Success: B2C trade state checked'
[2025-07-18 20:39:42,786: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 20:39:42,817: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[4cd0c141-57af-4eff-92fa-e18f1d9d9fdc] succeeded in 0.553697141003795s: None
[2025-07-18 20:44:42,333: INFO/MainProcess] Task roll.tasks.cleanup_duplicate_roll_room_bets[06cfd8c8-7685-4cc9-a85e-fb439efbd864] received
[2025-07-18 20:44:42,336: INFO/ForkPoolWorker-2] --------------------- 开始清理 ROLL 重复用户 ---------------------
[2025-07-18 20:44:42,403: INFO/MainProcess] Task thworker.update_case_records_cache[59575615-e8af-4fc7-8f7c-d57eeb9aa6e1] received
[2025-07-18 20:44:42,407: INFO/ForkPoolWorker-1] ------------------------ 更新 CaseRecord 缓存（间隔5分钟） ------------------------
[2025-07-18 20:44:42,438: INFO/ForkPoolWorker-1] 在5分钟内已执行过更新，跳过此次执行。
[2025-07-18 20:44:42,439: INFO/ForkPoolWorker-1] [Celery] 更新CaseRecords缓存完成，处理数量: 100
[2025-07-18 20:44:42,472: INFO/ForkPoolWorker-1] Task thworker.update_case_records_cache[59575615-e8af-4fc7-8f7c-d57eeb9aa6e1] succeeded in 0.06591188302263618s: 'Success: Updated 100 case records cache'
[2025-07-18 20:44:42,594: INFO/MainProcess] Task thworker.check_b2c_trade_state[9b837be4-59bf-4028-99e9-8dfbcdb0c28e] received
[2025-07-18 20:44:42,596: INFO/ForkPoolWorker-1] ------------------------ 检测ZBT取回订单状态（间隔5分钟） ------------------------
[2025-07-18 20:44:42,597: INFO/ForkPoolWorker-1] [Celery] B2C交易状态检查完成
[2025-07-18 20:44:42,650: INFO/ForkPoolWorker-1] 在5分钟内已执行过状态检测，跳过此次执行。
[2025-07-18 20:44:42,665: INFO/ForkPoolWorker-1] Task thworker.check_b2c_trade_state[9b837be4-59bf-4028-99e9-8dfbcdb0c28e] succeeded in 0.06962993199704215s: 'Success: B2C trade state checked'
[2025-07-18 20:44:42,810: INFO/ForkPoolWorker-2] 成功删除 0 条重复记录。
[2025-07-18 20:44:42,890: INFO/ForkPoolWorker-2] Task roll.tasks.cleanup_duplicate_roll_room_bets[06cfd8c8-7685-4cc9-a85e-fb439efbd864] succeeded in 0.5548082599998452s: None
