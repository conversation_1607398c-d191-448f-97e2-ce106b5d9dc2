celery beat v5.5.3 (immunity) is starting.
celery beat v5.5.3 (immunity) is starting.
celery beat v5.5.3 (immunity) is starting.
celery beat v5.5.3 (immunity) is starting.
celery beat v5.5.3 (immunity) is starting.
celery beat v5.5.3 (immunity) is starting.
celery beat v5.5.3 (immunity) is starting.
celery beat v5.5.3 (immunity) is starting.
celery beat v5.5.3 (immunity) is starting.
celery beat v5.5.3 (immunity) is starting.
celery beat v5.5.3 (immunity) is starting.
__    -    ... __   -        _
LocalTime -> 2025-07-18 10:20:38
Configuration ->
    . broker -> redis://localhost:6379/0
    . loader -> celery.loaders.app.AppLoader
    . scheduler -> django_celery_beat.schedulers.DatabaseScheduler

    . logfile -> [stderr]@%INFO
    . maxinterval -> 5.00 seconds (5s)
celery beat v5.5.3 (immunity) is starting.
__    -    ... __   -        _
LocalTime -> 2025-07-18 10:24:31
Configuration ->
    . broker -> redis://localhost:6379/0
    . loader -> celery.loaders.app.AppLoader
    . scheduler -> django_celery_beat.schedulers.DatabaseScheduler

    . logfile -> [stderr]@%INFO
    . maxinterval -> 5.00 seconds (5s)
celery beat v5.5.3 (immunity) is starting.
__    -    ... __   -        _
LocalTime -> 2025-07-18 10:30:30
Configuration ->
    . broker -> redis://localhost:6379/0
    . loader -> celery.loaders.app.AppLoader
    . scheduler -> django_celery_beat.schedulers.DatabaseScheduler

    . logfile -> [stderr]@%INFO
    . maxinterval -> 5.00 seconds (5s)
celery beat v5.5.3 (immunity) is starting.
__    -    ... __   -        _
LocalTime -> 2025-07-18 10:33:20
Configuration ->
    . broker -> redis://localhost:6379/0
    . loader -> celery.loaders.app.AppLoader
    . scheduler -> django_celery_beat.schedulers.DatabaseScheduler

    . logfile -> [stderr]@%INFO
    . maxinterval -> 5.00 seconds (5s)
celery beat v5.5.3 (immunity) is starting.
__    -    ... __   -        _
LocalTime -> 2025-07-18 10:40:03
Configuration ->
    . broker -> redis://localhost:6379/0
    . loader -> celery.loaders.app.AppLoader
    . scheduler -> django_celery_beat.schedulers.DatabaseScheduler

    . logfile -> [stderr]@%INFO
    . maxinterval -> 5.00 seconds (5s)
celery beat v5.5.3 (immunity) is starting.
__    -    ... __   -        _
LocalTime -> 2025-07-18 11:28:05
Configuration ->
    . broker -> redis://localhost:6379/0
    . loader -> celery.loaders.app.AppLoader
    . scheduler -> django_celery_beat.schedulers.DatabaseScheduler

    . logfile -> [stderr]@%INFO
    . maxinterval -> 5.00 seconds (5s)
celery beat v5.5.3 (immunity) is starting.
__    -    ... __   -        _
LocalTime -> 2025-07-18 11:32:49
Configuration ->
    . broker -> redis://localhost:6379/0
    . loader -> celery.loaders.app.AppLoader
    . scheduler -> django_celery_beat.schedulers.DatabaseScheduler

    . logfile -> [stderr]@%INFO
    . maxinterval -> 5.00 seconds (5s)
