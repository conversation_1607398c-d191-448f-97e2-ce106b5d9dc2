<template>
  <div class="battle-player-slot" :class="slotClasses">
    <!-- 玩家信息区域 -->
    <div class="player-info-section">
      <!-- 玩家头像和信息 -->
      <div class="player-avatar-container">
        <div class="avatar-frame" :class="playerFrameClass">
          <img 
            :src="player?.avatar || '/demo/avatar1.png'" 
            :alt="player?.nickname"
            class="player-avatar"
            @error="handleImageError"
          >
          <!-- 状态指示器 -->
          <div class="player-status" :class="statusClass">
            <div class="status-dot"></div>
          </div>
          <!-- 胜利王冠 -->
          <div v-if="isWinner" class="victory-crown">
            <Icon name="heroicons:crown" class="w-6 h-6 text-yellow-400" />
          </div>
        </div>
        
        <!-- 玩家名称和分数 -->
        <div class="player-details">
          <div class="player-name">{{ player?.nickname || t('battle.detail.empty_slot') }}</div>
          <div class="player-score">${{ currentScore.toFixed(2) }}</div>
          <div v-if="roundWins > 0" class="player-wins">
            {{ t('battle.detail.wins', { count: roundWins }) }}
          </div>
        </div>
      </div>
    </div>

    <!-- 竖向老虎机区域 -->
    <div class="slot-machine-section">
      <div class="slot-machine-container" :class="{ 'spinning': isSpinning }">
        <!-- 中奖指示线（水平） -->
        <div class="winner-indicator-line">
          <div class="line-left"></div>
          <div class="line-center">
            <div class="indicator-arrow">→</div>
          </div>
          <div class="line-right"></div>
        </div>

        <!-- 竖向滚轮 -->
        <div class="vertical-reel" ref="reelContainer">
          <div 
            class="reel-strip" 
            ref="reelStrip"
            :style="reelTransform"
          >
            <div 
              v-for="(item, index) in animationItems" 
              :key="index"
              class="reel-item"
              :class="{ 'winner-item': index === winnerIndex && !isSpinning }"
            >
              <div class="item-frame" :style="{ borderColor: item.rarity_color || '#666' }">
                <img 
                  :src="item.image || '/demo/item1.png'" 
                  :alt="item.name"
                  class="item-image"
                  @error="handleImageError"
                >
                <!-- 稀有度光效 -->
                <div 
                  class="item-glow" 
                  :style="{ backgroundColor: item.rarity_color + '20' }"
                ></div>
                <!-- StatTrak标识 -->
                <div v-if="item.stattrak" class="stattrak-badge">ST</div>
              </div>
              
              <div class="item-info">
                <div class="item-name">{{ getItemDisplayName(item) }}</div>
                <div class="item-price">${{ (item.price || 0).toFixed(2) }}</div>
                <div class="item-rarity">{{ item.rarity_name || 'Common' }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 获胜物品特效展示 -->
        <div v-if="winnerItem && !isSpinning" class="winner-showcase">
          <div class="winner-particles"></div>
          <div class="winner-glow" :style="{ borderColor: winnerItem.rarity_color }"></div>
        </div>
      </div>
    </div>

    <!-- 回合结果显示 -->
    <div v-if="roundResult" class="round-result">
      <div class="result-badge" :class="roundResult.win ? 'winner' : 'loser'">
        <Icon :name="roundResult.win ? 'heroicons:trophy' : 'heroicons:x-mark'" class="w-4 h-4" />
        <span>{{ roundResult.win ? t('battle.detail.round_win') : t('battle.detail.round_lose') }}</span>
      </div>
      <div class="result-value">+${{ (roundResult.item_value || 0).toFixed(2) }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { gsap } from 'gsap'
import type { BattleUser, BattleResult } from '~/types/battle'

interface SlotItem {
  id: string
  name: string
  image: string
  price: number
  rarity_color: string
  rarity_name: string
  stattrak?: boolean
}

interface Props {
  player: BattleUser | null
  slotIndex: number
  isSpinning: boolean
  currentScore: number
  roundWins: number
  isWinner: boolean
  roundResult: BattleResult | null
  animationItems: SlotItem[]
  winnerItem: SlotItem | null
}

interface Emits {
  (e: 'animation-complete'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const { t } = useI18n()

// 模板引用
const reelContainer = ref<HTMLElement>()
const reelStrip = ref<HTMLElement>()

// 响应式状态
const reelTransform = ref('translateY(0px)')
const winnerIndex = ref(-1)

// 计算属性
const slotClasses = computed(() => ({
  'has-player': !!props.player,
  'is-spinning': props.isSpinning,
  'is-winner': props.isWinner,
  [`player-slot-${props.slotIndex + 1}`]: true
}))

const playerFrameClass = computed(() => ({
  'player-1': props.slotIndex === 0,
  'player-2': props.slotIndex === 1,
  'player-3': props.slotIndex === 2,
  'player-4': props.slotIndex === 3,
  'winner-frame': props.isWinner
}))

const statusClass = computed(() => ({
  'status-empty': !props.player,
  'status-ready': props.player && !props.isSpinning,
  'status-spinning': props.isSpinning,
  'status-winner': props.isWinner
}))

// 方法
const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement
  target.src = target.src.includes('avatar') ? '/demo/avatar1.png' : '/demo/item1.png'
}

const getItemDisplayName = (item: SlotItem) => {
  if (!item.name) return 'Unknown'
  
  // 简化显示名称，最多显示15个字符
  let displayName = item.name
  if (displayName.length > 15) {
    displayName = displayName.substring(0, 15) + '...'
  }
  
  return displayName
}

// 动画方法
const startSpinAnimation = async () => {
  if (!reelStrip.value || props.animationItems.length === 0) return

  const itemHeight = 120 // 每个物品的高度
  const totalItems = props.animationItems.length
  
  // 重置位置
  reelTransform.value = 'translateY(0px)'
  winnerIndex.value = -1

  // 开始滚动动画
  const timeline = gsap.timeline()
  
  // 第一阶段：快速滚动
  timeline.to(reelStrip.value, {
    y: -itemHeight * totalItems * 2, // 滚动两轮
    duration: 2,
    ease: "power2.in"
  })
  
  // 第二阶段：减速并定位到获胜物品
  if (props.winnerItem) {
    // 找到获胜物品在列表中的位置
    const winnerItemIndex = props.animationItems.findIndex(item => 
      item.id === props.winnerItem?.id || item.name === props.winnerItem?.name
    )
    
    if (winnerItemIndex !== -1) {
      winnerIndex.value = winnerItemIndex
      const finalPosition = -itemHeight * winnerItemIndex + itemHeight * 2 // 居中显示
      
      timeline.to(reelStrip.value, {
        y: finalPosition,
        duration: 1.5,
        ease: "power4.out",
        onComplete: () => {
          // 更新 reelTransform 以保持最终位置
          reelTransform.value = `translateY(${finalPosition}px)`
          emit('animation-complete')
        }
      })
    }
  }

  return timeline
}

const resetAnimation = () => {
  reelTransform.value = 'translateY(0px)'
  winnerIndex.value = -1
}

// 监听器
watch(() => props.isSpinning, (newValue, oldValue) => {
  if (newValue && !oldValue) {
    // 开始旋转
    startSpinAnimation()
  } else if (!newValue && oldValue) {
    // 停止旋转
    // 动画完成在 startSpinAnimation 中处理
  }
})

// 暴露方法
defineExpose({
  startSpinAnimation,
  resetAnimation
})
</script>

<style lang="scss" scoped>
.battle-player-slot {
  @apply relative bg-gray-800/30 border border-gray-700/30 rounded-xl p-4;
  @apply transition-all duration-300;
  min-height: 500px;
  
  &.has-player {
    @apply border-gray-600/50;
  }
  
  &.is-spinning {
    @apply border-primary/50;
    box-shadow: 0 0 20px rgba(var(--color-primary-rgb), 0.3);
  }
  
  &.is-winner {
    @apply border-yellow-400/50;
    box-shadow: 0 0 30px rgba(255, 215, 0, 0.4);
    animation: winner-pulse 2s ease-in-out infinite;
  }
}

.player-info-section {
  @apply mb-4;
  
  .player-avatar-container {
    @apply flex flex-col items-center gap-2;
    
    .avatar-frame {
      @apply relative w-16 h-16 rounded-full border-2 border-gray-600;
      @apply transition-all duration-300;
      
      &.player-1 { @apply border-blue-400; }
      &.player-2 { @apply border-red-400; }
      &.player-3 { @apply border-green-400; }
      &.player-4 { @apply border-purple-400; }
      &.winner-frame { @apply border-yellow-400; }
      
      .player-avatar {
        @apply w-full h-full rounded-full object-cover;
      }
      
      .player-status {
        @apply absolute -top-1 -right-1 w-4 h-4 rounded-full border-2 border-gray-800;
        
        .status-dot {
          @apply w-full h-full rounded-full;
        }
        
        &.status-empty .status-dot { @apply bg-gray-500; }
        &.status-ready .status-dot { @apply bg-green-500; }
        &.status-spinning .status-dot { 
          @apply bg-blue-500;
          animation: pulse 1s ease-in-out infinite;
        }
        &.status-winner .status-dot { @apply bg-yellow-500; }
      }
      
      .victory-crown {
        @apply absolute -top-3 left-1/2 transform -translate-x-1/2;
        animation: crown-bounce 1s ease-in-out infinite;
      }
    }
    
    .player-details {
      @apply text-center;
      
      .player-name {
        @apply text-white font-semibold text-sm mb-1;
      }
      
      .player-score {
        @apply text-primary font-bold text-lg;
      }
      
      .player-wins {
        @apply text-yellow-400 text-xs font-medium;
      }
    }
  }
}

.slot-machine-section {
  @apply flex-1;
  
  .slot-machine-container {
    @apply relative bg-gray-900/50 rounded-lg border border-gray-700/30 overflow-hidden;
    height: 360px;
    
    &.spinning {
      @apply border-primary/50;
    }
    
    .winner-indicator-line {
      @apply absolute left-0 right-0 z-20 flex items-center;
      top: 50%;
      transform: translateY(-50%);
      
      .line-left, .line-right {
        @apply flex-1 h-0.5 bg-gradient-to-r from-transparent;
      }
      
      .line-left {
        @apply to-yellow-400;
      }
      
      .line-right {
        @apply from-yellow-400 to-transparent;
      }
      
      .line-center {
        @apply px-2;
        
        .indicator-arrow {
          @apply text-yellow-400 font-bold text-lg;
          animation: arrow-pulse 1.5s ease-in-out infinite;
        }
      }
    }
    
    .vertical-reel {
      @apply relative w-full h-full overflow-hidden;
      
      .reel-strip {
        @apply relative;
        will-change: transform;
        
        .reel-item {
          @apply flex items-center gap-3 p-3 border-b border-gray-700/30;
          height: 120px;
          transition: all 0.3s ease;
          
          &.winner-item {
            @apply bg-yellow-400/10 border-yellow-400/30;
            box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
            animation: winner-item-glow 2s ease-in-out infinite;
          }
          
          .item-frame {
            @apply relative w-16 h-16 border-2 rounded-lg flex items-center justify-center overflow-hidden;
            border-color: #666;
            
            .item-image {
              @apply w-full h-full object-cover;
            }
            
            .item-glow {
              @apply absolute inset-0 opacity-30 pointer-events-none rounded-lg;
            }
            
            .stattrak-badge {
              @apply absolute top-0 right-0 bg-orange-500 text-white text-xs px-1 rounded-bl;
              font-size: 8px;
              line-height: 1;
            }
          }
          
          .item-info {
            @apply flex-1 min-w-0;
            
            .item-name {
              @apply text-white font-medium text-sm truncate mb-1;
            }
            
            .item-price {
              @apply text-primary font-bold text-sm mb-1;
            }
            
            .item-rarity {
              @apply text-gray-400 text-xs;
            }
          }
        }
      }
    }
    
    .winner-showcase {
      @apply absolute inset-0 pointer-events-none;
      
      .winner-particles {
        @apply absolute inset-0;
        background: radial-gradient(circle at 50% 50%, rgba(255, 215, 0, 0.1) 0%, transparent 70%);
        animation: particles-float 3s ease-in-out infinite;
      }
      
      .winner-glow {
        @apply absolute inset-2 border-2 rounded-lg;
        border-color: #ffd700;
        box-shadow: 
          inset 0 0 20px rgba(255, 215, 0, 0.2),
          0 0 30px rgba(255, 215, 0, 0.3);
        animation: winner-glow-pulse 2s ease-in-out infinite;
      }
    }
  }
}

.round-result {
  @apply mt-4 text-center;
  
  .result-badge {
    @apply inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium mb-2;
    
    &.winner {
      @apply bg-green-500/20 text-green-400 border border-green-500/30;
    }
    
    &.loser {
      @apply bg-red-500/20 text-red-400 border border-red-500/30;
    }
  }
  
  .result-value {
    @apply text-primary font-bold text-lg;
  }
}

// 动画定义
@keyframes winner-pulse {
  0%, 100% {
    box-shadow: 0 0 30px rgba(255, 215, 0, 0.4);
  }
  50% {
    box-shadow: 0 0 50px rgba(255, 215, 0, 0.6);
  }
}

@keyframes crown-bounce {
  0%, 100% {
    transform: translateX(-50%) translateY(0);
  }
  50% {
    transform: translateX(-50%) translateY(-4px);
  }
}

@keyframes arrow-pulse {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

@keyframes winner-item-glow {
  0%, 100% {
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
  }
  50% {
    box-shadow: 0 0 25px rgba(255, 215, 0, 0.5);
  }
}

@keyframes particles-float {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.05);
  }
}

@keyframes winner-glow-pulse {
  0%, 100% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style> 