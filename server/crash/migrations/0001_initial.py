# -*- coding: utf-8 -*-
# Generated by Django 1.11.13 on 2021-04-14 02:09
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import steambase.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODELL),
    ]

    operations = [
        migrations.CreateModel(
            name='CrashBet',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('amount', models.FloatField(default=0, verbose_name='amount')),
                ('out_point', models.FloatField(default=0, verbose_name='out point')),
                ('win_amount', models.FloatField(default=0, verbose_name='win amount')),
                ('win_result', models.SmallIntegerField(choices=[(4, 'Not End'), (2, 'Win'), (3, 'Lose')], default=4, verbose_name='win result')),
            ],
            options={
                'verbose_name': 'Crash Bet',
                'verbose_name_plural': 'Crash Bet',
                'ordering': ('-create_time',),
            },
        ),
        migrations.CreateModel(
            name='CrashGame',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('hash', models.CharField(max_length=255, verbose_name='hash')),
                ('secret', models.CharField(max_length=32, verbose_name='secret')),
                ('percentage', models.FloatField(default=0, verbose_name='percentage')),
                ('crash_point', models.FloatField(default=0, verbose_name='crash point')),
                ('total_amount', models.FloatField(default=0, verbose_name='total amount')),
                ('pump_amount', models.FloatField(default=0, verbose_name='pump amount')),
                ('win_amount', models.FloatField(default=0, verbose_name='win amount')),
                ('joinable_time', models.DateTimeField(blank=True, default=None, null=True, verbose_name='joinable time')),
                ('run_time', models.DateTimeField(blank=True, default=None, null=True, verbose_name='run time')),
                ('end_time', models.DateTimeField(blank=True, default=None, null=True, verbose_name='end time')),
                ('state', models.SmallIntegerField(choices=[(1, 'Initial'), (2, 'Joinable'), (3, 'Joining'), (4, 'Full'), (5, 'Running'), (11, 'End'), (20, 'Cancelled')], default=1, verbose_name='status')),
            ],
            options={
                'verbose_name': 'Crash Game',
                'verbose_name_plural': 'Crash Game',
                'ordering': ('-create_time',),
            },
        ),
        migrations.CreateModel(
            name='CrashPumpDay',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('date', models.DateField(default=django.utils.timezone.now, verbose_name='date')),
                ('amount', models.FloatField(default=0, verbose_name='amount')),
            ],
            options={
                'verbose_name': 'Crash Pump Day',
                'verbose_name_plural': 'Crash Pump Day',
                'ordering': ('-create_time',),
            },
        ),
        migrations.CreateModel(
            name='CrashPumpMonth',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('date', models.DateField(default=django.utils.timezone.now, verbose_name='date')),
                ('amount', models.FloatField(default=0, verbose_name='amount')),
            ],
            options={
                'verbose_name': 'Crash Pump Month',
                'verbose_name_plural': 'Crash Pump Month',
                'ordering': ('-create_time',),
            },
        ),
        migrations.CreateModel(
            name='CrashStatisticsDay',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('date', models.DateField(default=django.utils.timezone.now, verbose_name='date')),
                ('amount', models.FloatField(default=0, verbose_name='game amount')),
                ('journal', models.FloatField(default=0, verbose_name='game journal')),
                ('win_amount', models.FloatField(default=0, verbose_name='win amount journal')),
                ('lose_amount', models.FloatField(default=0, verbose_name='lose amount journal')),
                ('test_amount', models.FloatField(default=0, verbose_name='test game amount')),
                ('admin_amount', models.FloatField(default=0, verbose_name='admin game amount')),
            ],
            options={
                'verbose_name': 'Crash Statistics Day',
                'verbose_name_plural': 'Crash Statistics Day',
                'ordering': ('-create_time',),
            },
        ),
        migrations.CreateModel(
            name='CrashStatisticsMonth',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('date', models.DateField(default=django.utils.timezone.now, verbose_name='date')),
                ('amount', models.FloatField(default=0, verbose_name='game amount')),
                ('journal', models.FloatField(default=0, verbose_name='game journal')),
                ('win_amount', models.FloatField(default=0, verbose_name='win amount journal')),
                ('lose_amount', models.FloatField(default=0, verbose_name='lose amount journal')),
                ('test_amount', models.FloatField(default=0, verbose_name='test game amount')),
                ('admin_amount', models.FloatField(default=0, verbose_name='admin game amount')),
            ],
            options={
                'verbose_name': 'Crash Statistics Month',
                'verbose_name_plural': 'Crash Statistics Month',
                'ordering': ('-create_time',),
            },
        ),
        migrations.CreateModel(
            name='CrashWinDayRank',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('date', models.DateField(default=django.utils.timezone.now, verbose_name='date')),
                ('amount', models.FloatField(default=0, verbose_name='amount')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='crash_win_day_rank', to=settings.AUTH_USER_MODELL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'Crash Win Day Rank',
                'verbose_name_plural': 'Crash Win Day Rank',
            },
        ),
        migrations.CreateModel(
            name='CrashWinWeekRank',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('year', models.SmallIntegerField(default=0, verbose_name='year')),
                ('week', models.SmallIntegerField(default=0, verbose_name='week')),
                ('amount', models.FloatField(default=0, verbose_name='amount')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='crash_win_week_rank', to=settings.AUTH_USER_MODELL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'Crash Win Week Rank',
                'verbose_name_plural': 'Crash Win Week Rank',
            },
        ),
        migrations.AddField(
            model_name='crashbet',
            name='game',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bets', to='crash.CrashGame', verbose_name='game'),
        ),
        migrations.AddField(
            model_name='crashbet',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='crash_bets', to=settings.AUTH_USER_MODELL, verbose_name='user'),
        ),
    ]
