# Crash 游戏模块分析文档

## 模块概述

Crash模块是平台的核心娱乐游戏系统，实现了经典的"崩盘游戏"玩法。用户在游戏开始前下注，游戏运行时倍数不断上升，用户需要在合适的时机提现，如果倍数崩盘前成功提现则获得相应倍数的奖励，否则损失全部下注金额。

## 目录结构

```
crash/
├── __init__.py
├── admin.py                 # Django管理后台配置
├── apps.py                  # 应用配置
├── business.py              # 核心游戏逻辑
├── interfaces.py            # ThWorker集成接口
├── models.py                # 数据模型定义
├── model_signals.py         # 模型信号处理
├── serializers.py           # API序列化器
├── tests.py                 # 单元测试
├── urls.py                  # URL路由配置
├── views.py                 # API视图
└── migrations/              # 数据库迁移文件
```

## 数据模型分析

### 1. CrashGame - 游戏主表
```python
class CrashGame(ModelBase):
    hash = models.CharField(max_length=255)             # 游戏哈希值
    secret = models.CharField(max_length=32)            # 密钥
    percentage = models.FloatField(default=0)           # 随机百分比
    crash_point = models.FloatField(default=0)          # 崩盘倍数
    total_amount = models.FloatField(default=0)         # 总下注金额
    pump_amount = models.FloatField(default=0)          # 抽水金额
    win_amount = models.FloatField(default=0)           # 中奖金额
    joinable_time = models.DateTimeField()             # 可下注时间
    run_time = models.DateTimeField()                  # 开始运行时间
    end_time = models.DateTimeField()                  # 结束时间
    state = models.SmallIntegerField()                 # 游戏状态
```

**游戏状态流转：**
- Initial → Joinable → Running → End
- 支持Cancelled状态处理异常情况

### 2. CrashBet - 下注记录
```python
class CrashBet(ModelBase):
    user = models.ForeignKey(USER_MODEL)               # 下注用户
    game = models.ForeignKey(CrashGame)                # 关联游戏
    amount = models.FloatField(default=0)              # 下注金额
    out_point = models.FloatField(default=0)           # 提现倍数
    win_amount = models.FloatField(default=0)          # 中奖金额
    win_result = models.SmallIntegerField()            # 输赢结果
```

**输赢判定逻辑：**
- NotEnd：游戏进行中
- Win：提现倍数 ≤ 崩盘倍数
- Lose：提现倍数 > 崩盘倍数或未提现

### 3. 统计相关模型

#### CrashStatisticsDay/Month - 游戏统计
```python
amount = models.FloatField()              # 游戏收入
journal = models.FloatField()            # 流水金额
win_amount = models.FloatField()         # 中奖金额
lose_amount = models.FloatField()        # 输掉金额
test_amount = models.FloatField()        # 测试账户金额
admin_amount = models.FloatField()       # 管理员账户金额
```

#### CrashWinDayRank/WeekRank - 排行榜
```python
user = models.ForeignKey(USER_MODEL)     # 用户
amount = models.FloatField()             # 净盈利金额
date/year/week = models.Field()          # 时间标识
```

#### CrashPumpDay/Month - 抽水统计
```python
date = models.DateField()                # 日期
amount = models.FloatField()             # 抽水金额
```

## API 接口分析

### 1. 获取当前游戏状态
**路径：** `GET /api/crash/current/`

**功能：** 获取当前游戏的基本信息和状态

**响应格式：**
```json
{
    "code": 200,
    "data": {
        "id": 12345,
        "uid": "game_uuid",
        "hash": "abc123...",
        "secret": "secret123",
        "state": 2,
        "joinable_time": "2024-01-01T10:00:00Z",
        "run_time": "2024-01-01T10:01:00Z",
        "end_time": null,
        "countdown": 30,
        "past": false
    }
}
```

### 2. 游戏下注
**路径：** `POST /api/crash/bet/`

**功能：** 用户对当前游戏下注

**参数：**
```json
{
    "amount": 100.0,
    "outPoint": 2.5
}
```

**业务逻辑：**
- 验证游戏状态（必须是Joinable状态）
- 检查用户余额和下注限额
- 创建下注记录并扣除用户余额
- 更新游戏总金额

### 3. 游戏提现（已废弃）
**路径：** `POST /api/crash/out/`

**状态：** 已注释，可能由于游戏机制调整

### 4. 获取当前下注
**路径：** `GET /api/crash/betcurrent/`

**功能：** 获取用户在当前游戏的下注信息

### 5. 下注记录查询
**路径：** `GET /api/crash/betrecord/`

**功能：** 查询用户的历史下注记录

**参数：**
- `uid`: 记录ID
- `win_result`: 输赢结果筛选
- `page`: 页码
- `pageSize`: 每页大小

### 6. 游戏历史
**路径：** `GET /api/crash/history/`

**功能：** 查询历史游戏记录

**响应包含：**
- 游戏ID、哈希值、密钥
- 随机百分比、崩盘倍数
- 总投注金额、结束时间

### 7. 排行榜查询
**路径：** 
- `GET /api/crash/dayrank/` - 日排行榜
- `GET /api/crash/weekrank/` - 周排行榜

**功能：** 获取用户盈利排行榜

### 8. 游戏验证
**路径：** `POST /api/crash/verify/`

**功能：** 验证游戏结果的公平性

**参数：**
```json
{
    "percentage": 85.5,
    "secret": "secret123"
}
```

## 核心算法分析

### 1. 随机数生成算法
```python
def create_new_game_hash():
    # 生成0-100的随机百分比
    percentage = numpy.random.uniform(0, 100)
    
    # 生成10位随机密钥
    secret = secret_generator(size=10)
    
    # 组合数据并生成MD5哈希
    data = '{0:.13f}:{1}'.format(percentage, secret)
    hash_key = hashlib.md5(data.encode('utf-8')).hexdigest()
    
    return {
        'percentage': percentage,
        'secret': secret,
        'hash': hash_key
    }
```

### 2. 崩盘倍数计算
```python
def calculate_crash_point(percentage):
    # 基于随机百分比计算崩盘倍数
    crash_point = round(1 / (1 - percentage / 100) * discount_rate / 100, 2)
    
    # 限制倍数范围 [1, 999.99]
    crash_point = max(1, min(999.99, crash_point))
    
    return crash_point
```

**算法特点：**
- 使用反比例函数确保低倍数概率高
- 应用平台抽水系数调整期望收益
- 设置合理的倍数上下限

### 3. 输赢判定逻辑
```python
def calc_crash_game(game):
    for bet in game.bets.filter(win_result=WinResult.NotEnd.value):
        if 1 < bet.out_point <= game.crash_point:
            # 成功提现：获得倍数奖励
            win_amount = round(bet.amount * bet.out_point, 2)
            bet.win_result = WinResult.Win.value
            bet.win_amount = win_amount
            bet.user.update_balance(win_amount)
        else:
            # 失败：损失全部下注金额
            bet.win_result = WinResult.Lose.value
```

## 游戏流程分析

### 1. 游戏生命周期
```
创建游戏 (Initial)
    ↓ (等待end_countdown秒)
开放下注 (Joinable)
    ↓ (等待run_countdown秒)
开始运行 (Running)
    ↓ (立即结束)
游戏结束 (End)
    ↓
结算奖励和统计
    ↓
创建下一局游戏
```

### 2. 实时监控机制
```python
def check_current_crash_game():
    """持续监控游戏状态变化"""
    while True:
        try:
            game = get_current_crash_game(create=True)
            dt_now = timezone.now()
            
            # 检查是否可以开放下注
            if (game.state == GameState.Initial.value and 
                game.create_time <= dt_now - timedelta(seconds=end_countdown)):
                # 状态转换：Initial → Joinable
                game.state = GameState.Joinable.value
                game.joinable_time = dt_now
                # WebSocket通知所有用户
                ws_send_crash_game(game_data, 'new')
            
            # 检查是否可以开始运行
            if (game.state == GameState.Joinable.value and 
                game.joinable_time <= dt_now - timedelta(seconds=run_countdown)):
                # 状态转换：Joinable → Running → End
                game.state = GameState.Running.value
                game.run_time = dt_now
                game.state = GameState.End.value
                game.end_time = dt_now
                # WebSocket通知游戏结束
                ws_send_crash_game(game_data, 'end')
            
            # 处理游戏结算
            if game.state == GameState.End.value:
                calc_crash_game(game)
                update_crash_statistics(game)
                # 创建下一局游戏
                next_game = get_current_crash_game(create=True)
                
        except Exception as e:
            _logger.exception(e)
        finally:
            time.sleep(1)  # 每秒检查一次
```

## WebSocket 实时通信

### 1. 消息类型
- **new**: 新游戏开始，开放下注
- **end**: 游戏结束，公布结果
- **bet_res**: 下注结果统计

### 2. 消息格式
```python
def ws_send_crash_game(data, action):
    """发送WebSocket消息"""
    msg = ['crash', action, data]
    redis_conn.publish('ws_channel', json.dumps(msg))
```

## ThWorker 集成

### 后台任务
```python
def setup_crash_game_worker():
    """启动游戏监控线程"""
    th = threading.Thread(target=check_current_crash_game, args=())
    th.start()
```

**任务职责：**
- 持续监控游戏状态变化
- 自动进行状态转换
- 处理游戏结算和统计
- 发送实时WebSocket通知

## 存在的问题与风险

### 1. 游戏公平性问题

**问题描述：**
- 随机数生成使用简单的numpy.random，可预测性较高
- 哈希验证机制相对简单，可能存在漏洞
- 缺少服务端种子和客户端种子混合机制

**影响：**
- 游戏结果可能被预测
- 用户信任度降低
- 监管合规风险

### 2. 并发安全问题

**问题描述：**
- 游戏状态转换缺少原子性保护
- 用户余额更新可能存在竞态条件
- 下注时机检查不够严格

**影响：**
- 可能出现重复下注
- 余额计算错误
- 游戏状态混乱

### 3. 性能瓶颈

**问题描述：**
- 游戏监控线程每秒执行数据库查询
- 缺少有效的缓存机制
- WebSocket消息可能造成广播风暴

**影响：**
- 数据库负载过高
- 系统响应延迟
- 用户体验下降

### 4. 业务逻辑问题

**问题描述：**
- 自动提现功能被注释，用户体验不完整
- 游戏时间配置硬编码，缺少灵活性
- 错误处理不够完善

**影响：**
- 功能不完整
- 系统可配置性差
- 异常恢复困难

## 改进建议

### 1. 游戏公平性增强

#### 可证明公平算法
```python
import hmac
import hashlib
from datetime import datetime

class ProvablyFairCrash:
    def __init__(self):
        self.server_seed = self.generate_server_seed()
        self.server_seed_hash = self.hash_seed(self.server_seed)
    
    def generate_server_seed(self):
        """生成服务端种子"""
        return secrets.token_hex(32)
    
    def hash_seed(self, seed):
        """对种子进行哈希"""
        return hashlib.sha256(seed.encode()).hexdigest()
    
    def generate_crash_point(self, client_seed, nonce):
        """基于种子生成崩盘倍数"""
        # 组合所有熵源
        message = f"{client_seed}:{self.server_seed}:{nonce}"
        
        # 使用HMAC-SHA256生成随机数
        signature = hmac.new(
            self.server_seed.encode(),
            message.encode(),
            hashlib.sha256
        ).hexdigest()
        
        # 转换为0-1之间的浮点数
        hex_value = int(signature[:8], 16)
        random_value = hex_value / 0xFFFFFFFF
        
        # 计算崩盘倍数
        if random_value == 0:
            return 999.99
        
        crash_point = 1 / random_value
        return min(999.99, max(1.0, round(crash_point, 2)))
    
    def verify_result(self, client_seed, nonce, crash_point):
        """验证游戏结果"""
        expected = self.generate_crash_point(client_seed, nonce)
        return expected == crash_point
```

### 2. 并发安全优化

#### 原子性状态管理
```python
import redis
from django.db import transaction

class CrashGameManager:
    def __init__(self):
        self.redis_client = redis.Redis()
        self.game_lock_prefix = "crash_game_lock:"
    
    @transaction.atomic
    def safe_state_transition(self, game_id, from_state, to_state):
        """安全的状态转换"""
        lock_key = f"{self.game_lock_prefix}{game_id}"
        
        with self.redis_client.lock(lock_key, timeout=10):
            game = CrashGame.objects.select_for_update().get(id=game_id)
            
            if game.state != from_state:
                raise ValueError(f"Invalid state transition: {game.state} -> {to_state}")
            
            game.state = to_state
            game.save()
            
            return game
    
    @transaction.atomic
    def safe_place_bet(self, user, game_id, amount, out_point):
        """安全的下注处理"""
        with transaction.atomic():
            # 锁定用户和游戏
            user = AuthUser.objects.select_for_update().get(id=user.id)
            game = CrashGame.objects.select_for_update().get(id=game_id)
            
            # 验证游戏状态
            if game.state != GameState.Joinable.value:
                raise ValueError("Game is not accepting bets")
            
            # 验证用户余额
            if user.balance < amount:
                raise ValueError("Insufficient balance")
            
            # 检查重复下注
            existing_bet = CrashBet.objects.filter(user=user, game=game).exists()
            if existing_bet:
                raise ValueError("Already placed bet in this game")
            
            # 执行下注
            user.balance -= amount
            user.save()
            
            bet = CrashBet.objects.create(
                user=user,
                game=game,
                amount=amount,
                out_point=out_point
            )
            
            game.total_amount += amount
            game.save()
            
            return bet
```

### 3. 性能优化

#### 缓存策略
```python
class CrashGameCache:
    def __init__(self):
        self.redis_client = redis.Redis()
        self.current_game_key = "crash:current_game"
        self.game_cache_prefix = "crash:game:"
        self.bet_cache_prefix = "crash:bets:"
    
    def get_current_game(self):
        """获取当前游戏（带缓存）"""
        cached_data = self.redis_client.get(self.current_game_key)
        if cached_data:
            return json.loads(cached_data)
        
        game = CrashGame.objects.filter(
            state__in=[GameState.Initial.value, GameState.Joinable.value]
        ).first()
        
        if game:
            game_data = CrashGameSerializer(game).data
            self.redis_client.setex(
                self.current_game_key, 
                60,  # 缓存60秒
                json.dumps(game_data)
            )
            return game_data
        
        return None
    
    def cache_game_bets(self, game_id, bets_data):
        """缓存游戏下注数据"""
        cache_key = f"{self.bet_cache_prefix}{game_id}"
        self.redis_client.setex(
            cache_key,
            300,  # 缓存5分钟
            json.dumps(bets_data)
        )
    
    def invalidate_game_cache(self, game_id):
        """清除游戏相关缓存"""
        keys_to_delete = [
            self.current_game_key,
            f"{self.game_cache_prefix}{game_id}",
            f"{self.bet_cache_prefix}{game_id}"
        ]
        self.redis_client.delete(*keys_to_delete)
```

#### 批量处理优化
```python
def optimized_check_crash_game():
    """优化的游戏检查逻辑"""
    while True:
        try:
            # 批量获取需要处理的游戏
            games_to_process = CrashGame.objects.filter(
                state__in=[
                    GameState.Initial.value,
                    GameState.Joinable.value
                ]
            ).select_related().prefetch_related('bets')
            
            current_time = timezone.now()
            
            for game in games_to_process:
                try:
                    process_single_game(game, current_time)
                except Exception as e:
                    _logger.error(f"Error processing game {game.id}: {e}")
            
            # 减少检查频率，降低数据库负载
            time.sleep(2)
            
        except Exception as e:
            _logger.exception(e)
            time.sleep(5)  # 出错时等待更长时间
```

### 4. 配置管理优化

```python
# crash_config.py
CRASH_CONFIG = {
    'run_countdown': 30,           # 下注时间（秒）
    'end_countdown': 5,            # 等待时间（秒）
    'min_bet_amount': 1.0,         # 最小下注金额
    'max_bet_amount': 1000.0,      # 最大下注金额
    'max_crash_point': 999.99,     # 最大崩盘倍数
    'house_edge': 5.0,             # 平台抽水百分比
    'max_concurrent_bets': 100,    # 单局最大下注数
}

class CrashConfigManager:
    @classmethod
    def get_config(cls, key, default=None):
        return CRASH_CONFIG.get(key, default)
    
    @classmethod
    def update_config(cls, key, value):
        CRASH_CONFIG[key] = value
        # 通知所有worker更新配置
        cls.broadcast_config_update(key, value)
```

### 5. 监控和告警

```python
class CrashGameMonitor:
    def __init__(self):
        self.metrics = {}
    
    def collect_metrics(self):
        """收集游戏指标"""
        return {
            'active_games': CrashGame.objects.filter(
                state__in=[GameState.Joinable.value, GameState.Running.value]
            ).count(),
            'total_bets_24h': CrashBet.objects.filter(
                create_time__gte=timezone.now() - timedelta(hours=24)
            ).count(),
            'average_game_duration': self.calculate_avg_game_duration(),
            'house_edge_actual': self.calculate_actual_house_edge(),
            'error_rate': self.calculate_error_rate()
        }
    
    def check_anomalies(self, metrics):
        """检查异常情况"""
        alerts = []
        
        if metrics['active_games'] == 0:
            alerts.append("No active games detected")
        
        if metrics['house_edge_actual'] < 0:
            alerts.append("Negative house edge detected")
        
        if metrics['error_rate'] > 0.05:
            alerts.append("High error rate detected")
        
        return alerts
```

## 性能优化建议

### 1. 数据库优化
```sql
-- 添加必要索引
CREATE INDEX idx_crashgame_state_create ON crash_crashgame(state, create_time);
CREATE INDEX idx_crashbet_game_user ON crash_crashbet(game_id, user_id);
CREATE INDEX idx_crashbet_win_result ON crash_crashbet(win_result);
```

### 2. Redis优化
```python
# 使用Redis Stream处理实时消息
def optimized_ws_broadcast():
    redis_client.xadd(
        'crash_events',
        {
            'action': 'game_update',
            'data': json.dumps(game_data),
            'timestamp': time.time()
        }
    )
```

## 总结

Crash模块是一个功能相对完整的游戏系统，具有清晰的业务逻辑和状态管理。当前实现在基本功能上能够满足需求，但在游戏公平性、并发安全、性能优化等方面还有较大改进空间。建议按照上述优化方案逐步改进，重点关注游戏的公平性验证、系统的稳定性和用户体验的提升。

## 接口清单

| 接口名称 | 方法 | 路径 | 状态 | 功能描述 |
|---------|------|------|------|----------|
| 当前游戏状态 | GET | `/api/crash/current/` | 正常 | 获取当前游戏信息 |
| 游戏下注 | POST | `/api/crash/bet/` | 正常 | 用户下注 |
| 游戏提现 | POST | `/api/crash/out/` | 已废弃 | 手动提现 |
| 当前下注 | GET | `/api/crash/betcurrent/` | 正常 | 获取当前下注 |
| 下注记录 | GET | `/api/crash/betrecord/` | 正常 | 查询下注历史 |
| 游戏历史 | GET | `/api/crash/history/` | 正常 | 查询游戏历史 |
| 日排行榜 | GET | `/api/crash/dayrank/` | 正常 | 获取日盈利排行 |
| 周排行榜 | GET | `/api/crash/weekrank/` | 正常 | 获取周盈利排行 |
| 游戏验证 | POST | `/api/crash/verify/` | 正常 | 验证游戏公平性 |
