from django.urls import re_path as url

from crash import views, model_signals



app_name = 'crash'
urlpatterns = [
    url(r'^current/', views.GetCrashGameCurrentView.as_view()),
    url(r'^bet/', views.BetCrashGameView.as_view()),
    # url(r'^out/', views.OutCrashGameView.as_view()),
    # url(r'^autobet/', views.AutoBetCrashGameView.as_view()),
    url(r'^betcurrent/', views.GetCrashBetCurrentView.as_view()),
    url(r'^betrecord/', views.GetCrashBetView.as_view()),
    url(r'^history/', views.GetCrashHistoryView.as_view()),
    url(r'^dayrank/', views.GetCrashDayRankView.as_view()),
    url(r'^weekrank/', views.GetCrashWeekRankView.as_view()),
    url(r'^verify', views.VerifyCrashView.as_view()),
]
