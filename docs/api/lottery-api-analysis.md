# Lottery模块API分析文档

## 概述

Lottery模块是一个定时抽奖系统，支持整点抽奖、每日抽奖和每周抽奖三种类型。用户通过充值获得参与资格，系统定时开奖并发放奖品。

## 现有API接口

### 1. GET /api/lottery/setting/ - 获取抽奖配置

**功能**: 获取三种抽奖类型的配置信息和用户参与状态。

**请求方式**: GET

**权限**: AllowAny（允许未登录用户访问）

**响应格式**:
```json
{
    "status": 200,
    "data": {
        "hourly": {
            "id": 1,
            "lottery_type": 0,
            "item_info": {
                "id": 1,
                "market_name": "AK-47 | Redline",
                "market_hash_name": "AK-47 | Redline (Field-Tested)",
                "icon_url": "xxx",
                "item_price": {...}
            },
            "min_charge": 50.0,
            "max_joiner": 100,
            "enable": true,
            "joiner_count": 25,
            "price": 150.00,
            "message": "已参与",
            "last_second": 1800,
            "status": true
        },
        "daily": {...},
        "weekly": {...}
    },
    "msg": "Succeed"
}
```

**字段说明**:
- `lottery_type`: 抽奖类型 (0-整点, 1-每日, 2-每周)
- `joiner_count`: 当前参与人数
- `price`: 奖品价值
- `message`: 参与状态提示
- `last_second`: 距离开奖剩余秒数
- `status`: 用户是否已参与

### 2. GET /api/lottery/winner/ - 获取中奖记录

**功能**: 获取指定抽奖类型的中奖者列表，支持分页。

**请求方式**: GET

**权限**: AllowAny

**请求参数**:
- `lotteryType`: 抽奖类型 (0/1/2)
- `page`: 页码，默认为1
- `pageSize`: 每页大小，默认为10
- `order`: 排序方式，默认为'-create_time'

**响应格式**:
```json
{
    "status": 200,
    "data": {
        "winners": [
            {
                "id": 1,
                "uid": "xxx",
                "user": {
                    "uid": "xxx",
                    "nick_name": "用户昵称",
                    "steam": {
                        "personaname": "Steam昵称",
                        "avatar": "头像URL"
                    },
                    "profile": {...}
                },
                "lottery_type": 0,
                "state": 1,
                "win": true,
                "item_info": {
                    "market_name": "AK-47 | Redline",
                    "icon_url": "xxx"
                }
            }
        ],
        "total": 50,
        "page": 1,
        "limit": 10
    },
    "msg": "Succeed"
}
```

### 3. GET /api/lottery/info/ - 获取抽奖说明

**功能**: 获取三种抽奖类型的说明信息和规则。

**请求方式**: GET

**权限**: AllowAny

**响应格式**:
```json
{
    "status": 200,
    "data": {
        "hour": {
            "title": "整点抽奖说明",
            "content": "<p>整点抽奖规则详情...</p>"
        },
        "day": {
            "title": "每日抽奖说明", 
            "content": "<p>每日抽奖规则详情...</p>"
        },
        "week": {
            "title": "每周抽奖说明",
            "content": "<p>每周抽奖规则详情...</p>"
        }
    },
    "msg": "Succeed"
}
```

## 业务逻辑分析

### 抽奖参与机制

1. **自动参与**: 用户充值时通过信号机制自动参与抽奖
2. **阶梯参与**: 根据充值金额决定可参与的抽奖类型
3. **状态管理**: 参与者有多种状态（充值不足、已参与、人满、结束）

### 开奖流程

1. **定时开奖**: 通过定时任务触发开奖
2. **优先中奖**: 支持设置指定用户中奖（`set_winner`字段）
3. **随机抽取**: 从符合条件的参与者中随机选择中奖者
4. **奖品发放**: 自动创建PackageItem记录，将奖品发放到用户背包

### 抽奖类型

- **整点抽奖**: 每小时开奖一次
- **每日抽奖**: 每天开奖一次
- **每周抽奖**: 每周开奖一次

## 存在的问题和缺陷

### 1. API设计问题

**问题**:
- 所有接口都使用`AllowAny`权限，存在安全风险
- 缺少用户参与抽奖的主动接口
- 响应格式中部分字段命名不一致（如`pageSize`和`limit`）

**建议**:
```python
# 建议的权限设置
class GetLotterySettingView(APIView):
    # 移除AllowAny，使用默认认证

# 添加用户主动参与接口
url(r'^join/$', views.JoinLotteryView.as_view()),  # POST参与抽奖
url(r'^my_status/$', views.MyLotteryStatusView.as_view()),  # GET个人状态
```

### 2. 数据一致性问题

**问题**:
- 充值信号处理中可能出现并发问题
- 开奖过程中缺少完整的事务保护
- 参与人数统计可能不准确

**建议**:
```python
@receiver(post_save, sender=ChargeRecord, dispatch_uid="lottery_join_for_charge")
def lottery_join_for_charge(sender, instance, **kwargs):
    if instance.state == ChargeState.Succeed.value:
        # 添加幂等性检查
        cache_key = f'lottery_signal_{instance.id}'
        if cache.get(cache_key):
            return
        cache.set(cache_key, True, 300)
        
        try:
            with transaction.atomic():
                # 处理逻辑...
                pass
        finally:
            cache.delete(cache_key)
```

### 3. 性能问题

**问题**:
- 获取抽奖配置时重复查询参与人数
- 缺少必要的数据库索引
- 序列化器中的实时计算较多

**建议**:
```python
# 优化数据库查询
def get_lottery_setting(user):
    # 使用批量查询减少数据库访问
    settings = LotterySetting.objects.filter(enable=True).select_related('item_info')
    
    # 批量获取参与人数
    joiner_counts = LotteryJoiner.objects.filter(
        lottery_type__in=[0, 1, 2], 
        state=1
    ).values('lottery_type').annotate(count=Count('id'))
    
    # 使用缓存存储计算结果
    cache_key = f'lottery_joiner_count_{lottery_type}'
    # ...
```

### 4. 业务逻辑缺陷

**问题**:
- 开奖时间计算复杂且可能不准确
- 机器人参与逻辑硬编码在业务代码中
- 缺少抽奖历史记录和统计功能

**建议**:
```python
# 使用专门的定时任务框架
from celery import Celery

@app.task
def run_hourly_lottery_task():
    """整点抽奖定时任务"""
    run_hourly_lottery()

# 分离机器人逻辑
class LotteryBot:
    def add_fake_joiners(self, lottery_type, count):
        """添加机器人参与者"""
        pass
```

### 5. 安全和公平性问题

**问题**:
- `set_winner`功能可能被滥用
- 缺少抽奖过程的审计日志
- 随机性可能不够强

**建议**:
```python
import secrets

def select_winner(joiners):
    """使用加密安全的随机数选择中奖者"""
    if not joiners:
        return None
    
    # 记录抽奖过程
    seed = secrets.randbits(32)
    _logger.info(f'Lottery seed: {seed}, participants: {len(joiners)}')
    
    # 使用密码学安全的随机选择
    winner = secrets.choice(joiners)
    
    # 记录中奖结果
    _logger.info(f'Winner selected: {winner.user.id}')
    return winner
```

### 6. 错误处理和用户体验

**问题**:
- 异常处理过于简单，信息不够详细
- 缺少用户友好的错误提示
- 没有参与失败的重试机制

**建议**:
```python
def process_lottery_join(user, charge_amount, lottery_type):
    """处理用户参与抽奖"""
    try:
        lottery_setting = LotterySetting.objects.get(
            lottery_type=lottery_type, 
            enable=True
        )
    except LotterySetting.DoesNotExist:
        return RespCode.InvalidParams.value, "该抽奖活动暂未开启"
    
    if charge_amount < lottery_setting.min_charge:
        return RespCode.InvalidParams.value, f"最少需要充值{lottery_setting.min_charge}才能参与"
    
    # 其他业务逻辑...
```

### 7. 代码质量问题

**问题**:
- business.py中函数职责不清晰
- 序列化器中有重复的缓存逻辑
- 缺少类型注解和完整的文档

**建议**:
```python
from typing import Optional, Dict, Any, List
from dataclasses import dataclass

@dataclass
class LotteryResult:
    """抽奖结果数据类"""
    winner_id: Optional[int]
    item_info_id: int
    participants_count: int
    lottery_type: int

def run_lottery(lottery_type: int) -> LotteryResult:
    """执行抽奖逻辑"""
    pass
```

## 改进建议

### 1. API接口扩展

**建议添加的接口**:

```python
# lottery/urls.py
urlpatterns = [
    # 现有接口
    url(r'^setting/$', views.GetLotterySettingView.as_view()),
    url(r'^winner/$', views.GetLotteryWinnerView.as_view()),
    url(r'^info/$', views.GetLotteryInfoView.as_view()),
    
    # 新增接口
    url(r'^join/$', views.JoinLotteryView.as_view()),  # POST主动参与
    url(r'^my_status/$', views.MyLotteryStatusView.as_view()),  # GET个人状态
    url(r'^statistics/$', views.LotteryStatisticsView.as_view()),  # GET抽奖统计
    url(r'^history/$', views.LotteryHistoryView.as_view()),  # GET抽奖历史
]
```

### 2. 数据模型优化

**建议新增模型**:
```python
class LotteryRound(models.Model):
    """抽奖轮次记录"""
    lottery_type = models.SmallIntegerField()
    start_time = models.DateTimeField()
    end_time = models.DateTimeField()
    winner = models.ForeignKey(USER_MODEL, null=True, blank=True)
    total_participants = models.IntegerField()
    item_info = models.ForeignKey(ItemInfo)
    random_seed = models.CharField(max_length=32)  # 随机种子
    
class LotteryAuditLog(models.Model):
    """抽奖审计日志"""
    action = models.CharField(max_length=50)
    user = models.ForeignKey(USER_MODEL, null=True, blank=True)
    lottery_type = models.SmallIntegerField()
    details = models.TextField()
    timestamp = models.DateTimeField(auto_now_add=True)
```

### 3. 缓存策略优化

**建议的缓存方案**:
```python
class LotteryCacheManager:
    """抽奖缓存管理器"""
    
    @staticmethod
    def get_joiner_count(lottery_type: int) -> int:
        cache_key = f'lottery_joiner_count_{lottery_type}'
        count = cache.get(cache_key)
        if count is None:
            count = LotteryJoiner.objects.filter(
                lottery_type=lottery_type, 
                state=1
            ).count()
            cache.set(cache_key, count, 60)  # 1分钟缓存
        return count
    
    @staticmethod
    def invalidate_joiner_count(lottery_type: int):
        cache_key = f'lottery_joiner_count_{lottery_type}'
        cache.delete(cache_key)
```

### 4. 定时任务优化

**建议使用专业的任务调度**:
```python
# 使用thworker或其他任务队列
from thworker import task

@task(schedule='0 * * * *')  # 每小时执行
def hourly_lottery_task():
    """整点抽奖任务"""
    try:
        result = run_hourly_lottery()
        # 发送WebSocket通知
        send_lottery_notification(result)
    except Exception as e:
        _logger.exception(e)
        # 发送错误通知给管理员
```

### 5. WebSocket通知集成

**建议添加实时通知**:
```python
def send_lottery_notification(lottery_type: int, event: str, data: dict):
    """发送抽奖相关的WebSocket通知"""
    notification = {
        'type': 'lottery_notification',
        'action': event,  # 'draw_start', 'draw_end', 'winner_announced'
        'lottery_type': lottery_type,
        'data': data,
        'timestamp': timezone.now().isoformat()
    }
    
    # 全局广播
    channel = f'lottery_{lottery_type}'
    ws_send_msg(channel, notification)
```

### 6. 统计分析功能

**建议添加数据分析**:
```python
def get_lottery_statistics(lottery_type: int, days: int = 30):
    """获取抽奖统计数据"""
    end_date = timezone.now()
    start_date = end_date - timedelta(days=days)
    
    stats = {
        'total_rounds': LotteryRound.objects.filter(
            lottery_type=lottery_type,
            start_time__range=[start_date, end_date]
        ).count(),
        'total_participants': LotteryJoiner.objects.filter(
            lottery_type=lottery_type,
            create_time__range=[start_date, end_date]
        ).count(),
        'avg_participants_per_round': 0,
        'total_prize_value': 0
    }
    
    return stats
```

## 总结

Lottery模块是一个相对完整的抽奖系统，但在API设计、性能优化、安全性和用户体验方面还有较大改进空间。建议：

1. **短期改进**: 修复权限问题、优化数据库查询、完善错误处理
2. **中期重构**: 添加缺失的API接口、实现缓存策略、集成WebSocket通知
3. **长期优化**: 实现完整的审计系统、添加数据分析功能、提升系统公平性

通过这些改进，可以将Lottery模块打造成一个更加安全、高效、用户友好的抽奖系统。
