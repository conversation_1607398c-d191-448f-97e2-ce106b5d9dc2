<template>
  <div class="min-h-screen bg-slate-900 p-8">
    <div class="max-w-4xl mx-auto">
      <!-- 页面标题 -->
      <div class="text-center mb-8">
        <h1 class="text-4xl font-bold text-white mb-4">
          🏆 胜利者庆祝弹窗测试
        </h1>
        <p class="text-lg text-gray-300">
          测试 BattleWinnerModal 组件的庆祝特效、粒子动画和数据展示功能
        </p>
      </div>
      
      <!-- 测试控制区域 -->
      <div class="bg-slate-800 rounded-xl p-6 mb-8">
        <h2 class="text-2xl font-bold text-white mb-4">测试控制</h2>
        
        <!-- 测试按钮 -->
        <div class="flex flex-wrap gap-4 justify-center mb-6">
          <button
            @click="showModal = true"
            class="px-8 py-4 bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-black font-bold rounded-lg transition-all duration-300 hover:scale-105 shadow-lg"
          >
            🎉 显示胜利者弹窗
          </button>
          
          <button
            @click="showModal = false"
            class="px-8 py-4 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-bold rounded-lg transition-all duration-300 hover:scale-105 shadow-lg"
          >
            ❌ 隐藏弹窗
          </button>
          
          <button
            @click="switchTestData"
            class="px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-bold rounded-lg transition-all duration-300 hover:scale-105 shadow-lg"
          >
            🔄 切换测试数据
          </button>
        </div>
        
        <!-- 当前数据类型指示 -->
        <div class="text-center">
          <span class="inline-flex items-center gap-2 px-4 py-2 bg-gray-700 rounded-lg text-gray-300">
            <span class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
            当前数据: {{ currentDataType }}
          </span>
        </div>
      </div>
      
      <!-- 特效说明 -->
      <div class="bg-slate-800 rounded-xl p-6 mb-8">
        <h2 class="text-2xl font-bold text-white mb-4">✨ 新增特效</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div class="bg-slate-700 rounded-lg p-4">
            <h3 class="text-lg font-semibold text-yellow-400 mb-2">🎊 彩纸下落</h3>
            <p class="text-gray-300 text-sm">50个彩色彩纸从顶部飘落，带有旋转和淡出效果</p>
          </div>
          <div class="bg-slate-700 rounded-lg p-4">
            <h3 class="text-lg font-semibold text-blue-400 mb-2">💫 庆祝粒子</h3>
            <p class="text-gray-300 text-sm">30个彩色粒子随机跳跃动画，营造庆祝氛围</p>
          </div>
          <div class="bg-slate-700 rounded-lg p-4">
            <h3 class="text-lg font-semibold text-purple-400 mb-2">✨ 星星闪烁</h3>
            <p class="text-gray-300 text-sm">15个星星图标闪烁和旋转，增强视觉效果</p>
          </div>
          <div class="bg-slate-700 rounded-lg p-4">
            <h3 class="text-lg font-semibold text-orange-400 mb-2">🌟 光球效果</h3>
            <p class="text-gray-300 text-sm">8个发光球体脉冲动画，提供背景装饰</p>
          </div>
          <div class="bg-slate-700 rounded-lg p-4">
            <h3 class="text-lg font-semibold text-green-400 mb-2">⚡ 光束扫描</h3>
            <p class="text-gray-300 text-sm">3条水平光束渐变闪烁，模拟科技感</p>
          </div>
          <div class="bg-slate-700 rounded-lg p-4">
            <h3 class="text-lg font-semibold text-red-400 mb-2">📱 响应式优化</h3>
            <p class="text-gray-300 text-sm">移动端自动减少特效数量，保证性能</p>
          </div>
        </div>
      </div>
      
      <!-- 弹窗组件 -->
      <BattleWinnerModal
        :visible="showModal"
        :winner="testWinner"
        @close="showModal = false"
      />
      
      <!-- 测试数据展示 -->
      <div class="bg-slate-800 rounded-xl p-6">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-2xl font-bold text-white">当前测试数据</h2>
          <div class="flex items-center gap-2">
            <span class="text-sm text-gray-400">数据集:</span>
            <span class="px-3 py-1 bg-blue-600 text-white rounded-full text-sm font-medium">
              {{ currentDataIndex + 1 }} / {{ testDataSets.length }}
            </span>
          </div>
        </div>
        
        <!-- 数据预览卡片 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          <!-- 胜利者信息 -->
          <div class="bg-slate-700 rounded-lg p-4">
            <h3 class="text-lg font-semibold text-yellow-400 mb-3">🏆 胜利者信息</h3>
            <div class="space-y-2">
              <div class="flex justify-between">
                <span class="text-gray-400">昵称:</span>
                <span class="text-white font-medium">{{ testWinner.nickname }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-400">总价值:</span>
                <span class="text-green-400 font-bold">${{ testWinner.totalValue }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-400">奖励数量:</span>
                <span class="text-blue-400 font-medium">{{ testWinner.rewardItems?.length || 0 }} 件</span>
              </div>
            </div>
          </div>
          
          <!-- 奖励物品 -->
          <div class="bg-slate-700 rounded-lg p-4">
            <h3 class="text-lg font-semibold text-purple-400 mb-3">🎁 奖励物品</h3>
            <div class="space-y-2 max-h-32 overflow-y-auto">
              <div v-for="item in testWinner.rewardItems" :key="item.id" class="flex justify-between text-sm">
                <span class="text-gray-300 truncate flex-1 mr-2">{{ item.name_zh_hans || item.name }}</span>
                <span class="text-yellow-400 font-medium">${{ item.price }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 完整JSON数据 -->
        <details class="bg-slate-700 rounded-lg">
          <summary class="p-4 cursor-pointer text-white font-medium hover:bg-slate-600 rounded-lg transition-colors">
            📋 查看完整JSON数据 (点击展开)
          </summary>
          <div class="p-4 border-t border-slate-600">
            <pre class="text-sm text-slate-300 overflow-auto bg-slate-800 rounded p-3 max-h-96">{{ JSON.stringify(testWinner, null, 2) }}</pre>
          </div>
        </details>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 国际化
const { t } = useI18n()

// SEO
useSeoMeta({
  title: '胜利者庆祝弹窗测试',
  description: '测试胜利者庆祝弹窗组件的功能和样式'
})

// 响应式状态
const showModal = ref(false)
const currentDataIndex = ref(0)

// 多种测试数据
const testDataSets = [
  {
    type: "富数据集 - 大量奖励",
    data: {
      id: 1,
      nickname: "CSGO大神",
      avatar: "/demo/avatar1.png",
      totalValue: 1250.75,
      rewardItems: [
        {
          id: "1",
          name: "AK-47 | Redline",
          name_zh_hans: "AK-47 | 红线",
          image: "/demo/item1.png",
          price: 45.50,
          isStatTrak: true,
          exterior: "Field-Tested",
          rarity_name_zh_hans: "受限",
          rarity_color: "#8847ff"
        },
        {
          id: "2", 
          name: "AWP | Dragon Lore",
          name_zh_hans: "AWP | 龙狙",
          image: "/demo/item2.png",
          price: 1200.00,
          isStatTrak: false,
          exterior: "Factory New",
          rarity_name_zh_hans: "隐秘",
          rarity_color: "#ffd700"
        },
        {
          id: "3",
          name: "M4A4 | Howl",
          name_zh_hans: "M4A4 | 咆哮",
          image: "/demo/item3.png", 
          price: 2500.25,
          isStatTrak: true,
          exterior: "Minimal Wear",
          rarity_name_zh_hans: "违禁",
          rarity_color: "#eb4b4b"
        },
        {
          id: "4",
          name: "Desert Eagle | Golden Koi",
          name_zh_hans: "沙漠之鹰 | 金鲤",
          image: "/demo/item4.png",
          price: 85.00,
          isStatTrak: false,
          exterior: "Well-Worn",
          rarity_name_zh_hans: "军规级",
          rarity_color: "#4b69ff"
        },
        {
          id: "5",
          name: "Karambit | Fade",
          name_zh_hans: "卡兰比特 | 渐变",
          image: "/demo/item5.png",
          price: 850.00,
          isStatTrak: true,
          exterior: "Factory New",
          rarity_name_zh_hans: "隐秘",
          rarity_color: "#ffd700"
        },
        {
          id: "6",
          name: "Glock-18 | Fade",
          name_zh_hans: "格洛克18 | 渐变",
          image: "/demo/item6.png",
          price: 120.50,
          isStatTrak: false,
          exterior: "Minimal Wear",
          rarity_name_zh_hans: "受限",
          rarity_color: "#8847ff"
        }
      ],
      openingHistory: [
        {
          round: 1,
          item: {
            name: "AK-47 | Redline",
            price: 45.50
          }
        },
        {
          round: 2,
          item: {
            name: "AWP | Dragon Lore", 
            price: 1200.00
          }
        }
      ]
    }
  },
  {
    type: "简单数据集 - 少量奖励",
    data: {
      id: 2,
      nickname: "幸运玩家",
      avatar: "/demo/avatar2.png",
      totalValue: 89.99,
      rewardItems: [
        {
          id: "7",
          name: "P90 | Asiimov",
          name_zh_hans: "P90 | 龙王",
          image: "/demo/item7.png",
          price: 25.50,
          isStatTrak: false,
          exterior: "Field-Tested",
          rarity_name_zh_hans: "受限",
          rarity_color: "#8847ff"
        },
        {
          id: "8",
          name: "USP-S | Orion",
          name_zh_hans: "USP-S | 猎户座",
          image: "/demo/item8.png",
          price: 64.49,
          isStatTrak: true,
          exterior: "Minimal Wear",
          rarity_name_zh_hans: "受限",
          rarity_color: "#8847ff"
        }
      ],
      openingHistory: [
        {
          round: 1,
          item: {
            name: "P90 | Asiimov",
            price: 25.50
          }
        }
      ]
    }
  },
  {
    type: "极简数据集 - 单一奖励",
    data: {
      id: 3,
      nickname: "新手玩家",
      avatar: "/demo/avatar3.png",
      totalValue: 15.99,
      rewardItems: [
        {
          id: "9",
          name: "Five-SeveN | Case Hardened",
          name_zh_hans: "57式 | 蓝宝石",
          image: "/demo/item9.png",
          price: 15.99,
          isStatTrak: false,
          exterior: "Battle-Scarred",
          rarity_name_zh_hans: "军规级",
          rarity_color: "#4b69ff"
        }
      ],
      openingHistory: [
        {
          round: 1,
          item: {
            name: "Five-SeveN | Case Hardened",
            price: 15.99
          }
        }
      ]
    }
  }
]

// 计算属性
const testWinner = computed(() => testDataSets[currentDataIndex.value].data)
const currentDataType = computed(() => testDataSets[currentDataIndex.value].type)

// 方法
const switchTestData = () => {
  currentDataIndex.value = (currentDataIndex.value + 1) % testDataSets.length
  console.log("[🎰TEST] 切换到测试数据:", currentDataType.value)
}

// 监听弹窗状态
watch(showModal, (newVal) => {
  console.log("[🎰TEST] 弹窗状态:", newVal ? "显示" : "隐藏")
})
</script>

<style scoped>
/* 自定义样式 */
</style> 