#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
命名一致性检查脚本
检查项目中box/case和skin/item命名的使用情况
"""

import os
import re
import sys
from pathlib import Path
from collections import defaultdict

def scan_python_files(directory, pattern, description):
    """扫描Python文件中的命名模式"""
    results = defaultdict(list)
    
    for py_file in Path(directory).rglob('*.py'):
        # 跳过虚拟环境和缓存文件
        if any(exclude in str(py_file) for exclude in ['venv', '__pycache__', '.git', 'migrations']):
            continue
            
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 查找匹配的模式
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                line_num = content[:match.start()].count('\n') + 1
                line_content = content.split('\n')[line_num - 1].strip()
                
                results[str(py_file)].append({
                    'line': line_num,
                    'content': line_content,
                    'match': match.group()
                })
                
        except Exception as e:
            print(f"❌ 读取文件失败 {py_file}: {e}")
    
    return results

def check_box_case_naming():
    """检查box/case命名一致性"""
    print("📦 检查箱子命名一致性 (box vs case)")
    print("=" * 60)
    
    server_dir = Path(__file__).parent.parent.parent / "server"
    
    # 检查函数名中的box命名
    box_pattern = r'\b(get_\w*box\w*|box_\w+|.*_box_.*)\b'
    box_results = scan_python_files(server_dir, box_pattern, "box命名")
    
    if box_results:
        print("⚠️ 发现使用'box'命名的地方:")
        count = 0
        for file_path, matches in box_results.items():
            if matches:
                print(f"\n📄 {file_path}:")
                for match in matches[:3]:  # 只显示前3个
                    print(f"   行 {match['line']}: {match['content']}")
                    count += 1
                if len(matches) > 3:
                    print(f"   ... 还有 {len(matches) - 3} 个匹配")
        
        print(f"\n📊 总计发现 {count} 处使用'box'命名")
    else:
        print("✅ 未发现'box'命名使用")
    
    # 检查已统一的case命名
    case_pattern = r'\b(get_\w*case\w*|case_\w+|.*_case_.*|Case[A-Z]\w*)\b'
    case_results = scan_python_files(server_dir, case_pattern, "case命名")
    
    case_count = sum(len(matches) for matches in case_results.values())
    print(f"✅ 发现 {case_count} 处正确使用'case'命名")
    
    return len(box_results) == 0

def check_skin_item_naming():
    """检查skin/item命名一致性"""
    print("\n🎨 检查饰品命名一致性 (skin vs item)")
    print("=" * 60)
    
    server_dir = Path(__file__).parent.parent.parent / "server"
    
    # 检查item相关命名 (排除ItemInfo等核心模型)
    item_pattern = r'\b(?!ItemInfo|item_info|item_price|item_category|item_quality|item_rarity|item_exterior)(get_\w*item\w*|item_\w+|.*_item_.*)\b'
    item_results = scan_python_files(server_dir, item_pattern, "item命名")
    
    if item_results:
        print("⚠️ 发现可能需要统一为'skin'的'item'命名:")
        count = 0
        for file_path, matches in item_results.items():
            if matches:
                print(f"\n📄 {file_path}:")
                for match in matches[:3]:
                    print(f"   行 {match['line']}: {match['content']}")
                    count += 1
                if len(matches) > 3:
                    print(f"   ... 还有 {len(matches) - 3} 个匹配")
        
        print(f"\n📊 总计发现 {count} 处可能需要统一的'item'命名")
    else:
        print("✅ 未发现需要统一的'item'命名")
    
    # 检查skin命名使用情况
    skin_pattern = r'\b(get_\w*skin\w*|skin_\w+|.*_skin_.*|Skin[A-Z]\w*)\b'
    skin_results = scan_python_files(server_dir, skin_pattern, "skin命名")
    
    skin_count = sum(len(matches) for matches in skin_results.values())
    print(f"✅ 发现 {skin_count} 处正确使用'skin'命名")
    
    return len(item_results) == 0

def check_api_endpoints():
    """检查API端点命名"""
    print("\n🔌 检查API端点命名")
    print("=" * 60)
    
    server_dir = Path(__file__).parent.parent.parent / "server"
    
    # 查找URL配置文件
    url_files = []
    for url_file in server_dir.rglob('urls.py'):
        if 'venv' not in str(url_file):
            url_files.append(url_file)
    
    box_endpoints = []
    case_endpoints = []
    item_endpoints = []
    skin_endpoints = []
    
    for url_file in url_files:
        try:
            with open(url_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找box相关端点
            box_matches = re.findall(r'url\([^)]*[\'"]([^\'"]*/box/[^\'"]*)[\'"]', content)
            box_endpoints.extend([(str(url_file), endpoint) for endpoint in box_matches])
            
            # 查找case相关端点
            case_matches = re.findall(r'url\([^)]*[\'"]([^\'"]*/case/[^\'"]*)[\'"]', content)
            case_endpoints.extend([(str(url_file), endpoint) for endpoint in case_matches])
            
            # 查找item相关端点
            item_matches = re.findall(r'url\([^)]*[\'"]([^\'"]*/item/[^\'"]*)[\'"]', content)
            item_endpoints.extend([(str(url_file), endpoint) for endpoint in item_matches])
            
            # 查找skin相关端点
            skin_matches = re.findall(r'url\([^)]*[\'"]([^\'"]*/skin/[^\'"]*)[\'"]', content)
            skin_endpoints.extend([(str(url_file), endpoint) for endpoint in skin_matches])
            
        except Exception as e:
            print(f"❌ 读取URL文件失败 {url_file}: {e}")
    
    print(f"📦 发现 {len(box_endpoints)} 个'box'相关API端点")
    if box_endpoints:
        for file_path, endpoint in box_endpoints[:5]:
            print(f"   {endpoint}")
        if len(box_endpoints) > 5:
            print(f"   ... 还有 {len(box_endpoints) - 5} 个")
    
    print(f"📦 发现 {len(case_endpoints)} 个'case'相关API端点")
    print(f"🎨 发现 {len(item_endpoints)} 个'item'相关API端点")
    print(f"🎨 发现 {len(skin_endpoints)} 个'skin'相关API端点")
    
    return len(box_endpoints) == 0 and len(item_endpoints) == 0

def check_frontend_consistency():
    """检查前端命名一致性"""
    print("\n🎨 检查前端命名一致性")
    print("=" * 60)
    
    ui_dir = Path(__file__).parent.parent.parent / "ui"
    
    if not ui_dir.exists():
        print("❌ 前端目录不存在")
        return False
    
    # 检查组件命名
    case_components = list(ui_dir.rglob('*case*.vue')) + list(ui_dir.rglob('*Case*.vue'))
    skin_components = list(ui_dir.rglob('*skin*.vue')) + list(ui_dir.rglob('*Skin*.vue'))
    box_components = list(ui_dir.rglob('*box*.vue')) + list(ui_dir.rglob('*Box*.vue'))
    item_components = list(ui_dir.rglob('*item*.vue')) + list(ui_dir.rglob('*Item*.vue'))
    
    print(f"📦 Case相关组件: {len(case_components)} 个")
    print(f"🎨 Skin相关组件: {len(skin_components)} 个")
    print(f"📦 Box相关组件: {len(box_components)} 个 (应该为0)")
    print(f"🎨 Item相关组件: {len(item_components)} 个 (应该为0)")
    
    if box_components:
        print("⚠️ 发现Box相关组件:")
        for comp in box_components:
            print(f"   {comp}")
    
    if item_components:
        print("⚠️ 发现Item相关组件:")
        for comp in item_components:
            print(f"   {comp}")
    
    return len(box_components) == 0 and len(item_components) == 0

def generate_refactor_suggestions():
    """生成重构建议"""
    print("\n💡 重构建议")
    print("=" * 60)
    
    suggestions = [
        "1. 优先重命名业务函数中的'box'命名为'case'",
        "2. 更新缓存键名，将'box_'前缀改为'case_'",
        "3. 新API端点使用'/api/case/'而非'/api/box/'",
        "4. 逐步添加'skin'相关的业务函数",
        "5. 在代码审查中检查命名一致性",
        "6. 更新文档和注释中的命名",
        "7. 考虑添加函数别名以保持兼容性"
    ]
    
    for suggestion in suggestions:
        print(f"   {suggestion}")

def main():
    """主函数"""
    print("🏷️ 命名一致性检查工具")
    print("=" * 80)
    
    # 执行各项检查
    checks = [
        ("箱子命名一致性", check_box_case_naming),
        ("饰品命名一致性", check_skin_item_naming),
        ("API端点命名", check_api_endpoints),
        ("前端命名一致性", check_frontend_consistency)
    ]
    
    all_passed = True
    results = []
    
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
            if not result:
                all_passed = False
        except Exception as e:
            print(f"❌ {check_name}检查失败: {e}")
            results.append((check_name, False))
            all_passed = False
    
    # 输出总结
    print("\n" + "=" * 80)
    print("📊 检查结果总结:")
    
    for check_name, result in results:
        status = "✅ 已统一" if result else "⚠️ 需要统一"
        print(f"  {check_name}: {status}")
    
    # 生成建议
    generate_refactor_suggestions()
    
    if all_passed:
        print("\n🎉 所有命名都已统一！")
        return 0
    else:
        print("\n📝 发现命名不一致，建议按照重构计划逐步统一。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
