"""
最小URL配置 - 仅用于测试articles app
"""
from django.conf.urls import url, include
from django.contrib import admin
from django.conf.urls.static import static
from django.contrib.staticfiles.urls import staticfiles_urlpatterns
from django.conf import settings
from django.http import HttpResponse, JsonResponse

def health_check(request):
    return JsonResponse({'status': 'ok', 'message': 'Articles app is running'})

urlpatterns = [
    url(r'^admin/', admin.site.urls),
    url(r'^health/$', health_check, name='health-check'),
    url(r'^api/articles/', include('articles.urls', namespace='articles')),
]

# 静态文件配置
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += staticfiles_urlpatterns()
