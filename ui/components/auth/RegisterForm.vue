<template>
  <div class="auth-form p-6 rounded-xl shadow-lg w-full max-w-md mx-auto bg-gray-800/40 backdrop-blur-md border border-gray-700/30">
    <h1 class="text-2xl font-bold mb-6 text-center text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-blue-400">
      {{ $t('auth.register.title') }}
    </h1>

    <!-- 注册表单 -->
    <form @submit.prevent="handleSubmit" class="space-y-4">
      <!-- 邮箱输入 -->
      <div class="form-group">
        <label for="email" class="block mb-1.5 text-sm font-medium text-white/80">
          {{ $t('auth.email') }}
        </label>
        <div class="flex items-stretch">
          <div class="relative flex-1">
            <div class="i-ph-envelope-simple absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 w-5 h-5"></div>
            <input 
              type="email" 
              id="email" 
              v-model="formData.email" 
              :class="[
                'w-full pl-10 pr-4 py-2.5 rounded-l-lg bg-gray-700/50 border transition-all duration-300',
                'focus:outline-none focus:ring-1 focus:ring-green-500/50 focus:border-green-500/50',
                'placeholder:text-gray-400',
                errors.email ? 'border-red-500' : 'border-gray-700/50'
              ]"
              :placeholder="$t('auth.email_placeholder')"
              @blur="validateField('email')"
              autocomplete="email"
            />
          </div>
          <button 
            type="button"
            class="px-4 py-2.5 rounded-r-lg bg-gradient-to-r from-green-500 to-blue-500 text-white font-medium transition-all duration-300 hover:opacity-90 disabled:opacity-50 disabled:cursor-not-allowed whitespace-nowrap flex items-center justify-center min-w-[6rem]"
            :disabled="codeSent || isCodeLoading || !isEmailValid || codeCountdown > 0"
            @click="sendVerificationCode"
          >
            <div v-if="isCodeLoading" class="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full"></div>
            <span v-else-if="codeCountdown > 0">{{ $t('auth.code_countdown', { seconds: codeCountdown }) }}</span>
            <span v-else>{{ $t('auth.send_code') }}</span>
          </button>
        </div>
        <p v-if="errors.email" class="mt-1 text-sm text-red-400">{{ $t(errors.email) }}</p>
      </div>

      <!-- 验证码输入 -->
      <div class="form-group">
        <label for="verifyCode" class="block mb-1.5 text-sm font-medium text-white/80">
          {{ $t('auth.email_verification_code') }}
        </label>
        <div class="relative">
          <div class="i-ph-shield-check absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 w-5 h-5"></div>
          <input 
            type="text" 
            id="verifyCode" 
            v-model="formData.verifyCode" 
            :class="[
              'w-full pl-10 pr-4 py-2.5 rounded-lg bg-gray-700/50 border transition-all duration-300',
              'focus:outline-none focus:ring-1 focus:ring-green-500/50 focus:border-green-500/50',
              'placeholder:text-gray-400',
              errors.verifyCode ? 'border-red-500' : 'border-gray-700/50'
            ]"
            :placeholder="$t('auth.email_verification_code_placeholder')"
            @blur="validateField('verifyCode')"
            autocomplete="off"
            maxlength="6"
          />
        </div>
        <p v-if="errors.verifyCode" class="mt-1 text-sm text-red-400">{{ $t(errors.verifyCode) }}</p>
      </div>

      <!-- 昵称输入（可选） -->
      <div class="form-group">
        <label for="nickname" class="block mb-1.5 text-sm font-medium text-white/80">
          {{ $t('auth.nickname') }}
        </label>
        <div class="relative">
          <div class="i-ph-user absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 w-5 h-5"></div>
          <input 
            type="text" 
            id="nickname" 
            v-model="formData.nickname" 
            :class="[
              'w-full pl-10 pr-4 py-2.5 rounded-lg bg-gray-700/50 border transition-all duration-300',
              'focus:outline-none focus:ring-1 focus:ring-green-500/50 focus:border-green-500/50',
              'placeholder:text-gray-400',
              errors.nickname ? 'border-red-500' : 'border-gray-700/50'
            ]"
            :placeholder="$t('auth.nickname_placeholder')"
            @blur="validateField('nickname')"
            autocomplete="username"
            maxlength="20"
          />
        </div>
        <p v-if="errors.nickname" class="mt-1 text-sm text-red-400">{{ errors.nickname }}</p>
      </div>

      <!-- 密码输入 -->
      <div class="form-group">
        <label for="password" class="block mb-1.5 text-sm font-medium text-white/80">
          {{ $t('auth.password') }}
        </label>
        <div class="relative">
          <div class="i-ph-lock-simple absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 w-5 h-5"></div>
          <input 
            :type="showPassword ? 'text' : 'password'" 
            id="password" 
            v-model="formData.password" 
            :class="[
              'w-full pl-10 pr-12 py-2.5 rounded-lg bg-gray-700/50 border transition-all duration-300',
              'focus:outline-none focus:ring-1 focus:ring-green-500/50 focus:border-green-500/50',
              'placeholder:text-gray-400',
              errors.password ? 'border-red-500' : 'border-gray-700/50'
            ]"
            :placeholder="$t('auth.password_placeholder')"
            @blur="validateField('password')"
            autocomplete="new-password"
          />
          <button 
            type="button" 
            @click="showPassword = !showPassword"
            class="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/50 hover:text-white/70"
          >
            <div :class="showPassword ? 'i-ph-eye-slash' : 'i-ph-eye'" class="w-5 h-5"></div>
          </button>
        </div>
        <!-- 密码强度指示器 -->
        <div v-if="formData.password" class="mt-2">
          <div class="flex items-center gap-2 text-xs">
            <span class="text-white/60">{{ $t('auth.password_weak') }}</span>
            <div class="flex-1 bg-gray-700 rounded-full h-1.5">
              <div 
                class="h-1.5 rounded-full transition-all duration-300"
                :class="{
                  'w-1/3 bg-red-500': passwordStrength.strength === 'weak',
                  'w-2/3 bg-yellow-500': passwordStrength.strength === 'medium',
                  'w-full bg-green-500': passwordStrength.strength === 'strong'
                }"
              ></div>
            </div>
            <span 
              class="text-xs"
              :class="{
                'text-red-400': passwordStrength.strength === 'weak',
                'text-yellow-400': passwordStrength.strength === 'medium',
                'text-green-400': passwordStrength.strength === 'strong'
              }"
            >
              {{ $t(`auth.password_${passwordStrength.strength}`) }}
            </span>
          </div>
        </div>
        <p v-if="errors.password" class="mt-1 text-sm text-red-400">{{ $t(errors.password) }}</p>
      </div>

      <!-- 确认密码输入 -->
      <div class="form-group">
        <label for="confirmPassword" class="block mb-1.5 text-sm font-medium text-white/80">
          {{ $t('auth.confirm_password') }}
        </label>
        <div class="relative">
          <div class="i-ph-lock-simple absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 w-5 h-5"></div>
          <input 
            :type="showConfirmPassword ? 'text' : 'password'" 
            id="confirmPassword" 
            v-model="formData.confirmPassword" 
            :class="[
              'w-full pl-10 pr-12 py-2.5 rounded-lg bg-gray-700/50 border transition-all duration-300',
              'focus:outline-none focus:ring-1 focus:ring-green-500/50 focus:border-green-500/50',
              'placeholder:text-gray-400',
              errors.confirmPassword ? 'border-red-500' : 'border-gray-700/50'
            ]"
            :placeholder="$t('auth.confirm_password_placeholder')"
            @blur="validateField('confirmPassword')"
            autocomplete="new-password"
          />
          <button 
            type="button" 
            @click="showConfirmPassword = !showConfirmPassword"
            class="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/50 hover:text-white/70"
          >
            <div :class="showConfirmPassword ? 'i-ph-eye-slash' : 'i-ph-eye'" class="w-5 h-5"></div>
          </button>
        </div>
        <p v-if="errors.confirmPassword" class="mt-1 text-sm text-red-400">{{ $t(errors.confirmPassword) }}</p>
      </div>

      <!-- 服务条款和隐私政策 -->
      <div class="flex items-start gap-3">
        <div class="relative inline-flex items-center cursor-pointer mt-0.5" @click="formData.agreeToTerms = !formData.agreeToTerms">
          <input 
            type="checkbox" 
            id="terms" 
            v-model="formData.agreeToTerms" 
            class="sr-only"
          />
          <div 
            class="w-4 h-4 rounded border-2 transition-all duration-300 flex items-center justify-center"
            :class="formData.agreeToTerms ? 'bg-green-500 border-green-500' : 'border-gray-500'"
          >
            <div v-if="formData.agreeToTerms" class="i-ph-check text-white w-3 h-3"></div>
          </div>
        </div>
        <label for="terms" class="text-sm text-white/70 cursor-pointer">
          {{ $t('auth.agree_to') }}
          <NuxtLink to="/terms" class="text-green-400 hover:text-green-300 transition-colors">
            {{ $t('auth.terms') }}
          </NuxtLink>
          {{ $t('auth.and') }}
          <NuxtLink to="/privacy" class="text-green-400 hover:text-green-300 transition-colors">
            {{ $t('auth.privacy') }}
          </NuxtLink>
        </label>
      </div>
      <p v-if="errors.terms" class="text-sm text-red-400">{{ $t(errors.terms) }}</p>

      <!-- 提交按钮 -->
      <button 
        type="submit" 
        class="w-full bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-medium py-2.5 px-4 rounded-lg transition-all duration-300 mt-2 flex items-center justify-center transform hover:-translate-y-0.5 shadow-md disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
        :disabled="isLoading"
      >
        <div v-if="isLoading" class="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
        {{ isLoading ? $t('auth.registering') : $t('auth.register.title') }}
      </button>

      <!-- 错误信息 -->
      <div v-if="formError" class="p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
        <p class="text-sm text-red-400 text-center">{{ formError }}</p>
      </div>

      <!-- 登录链接 -->
      <div class="text-center text-sm text-white/70">
        {{ $t('auth.already_have_account') }}
        <NuxtLink 
          to="/auth/login" 
          class="text-green-400 hover:text-green-300 transition-colors font-medium ml-1"
        >
          {{ $t('auth.login_here') }}
        </NuxtLink>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useAuth } from '~/composables/useAuth'
import { 
  validateEmail, 
  validatePassword, 
  validateConfirmPassword, 
  validateVerificationCode,
  validateNickname
} from '~/utils/validation'

const { t } = useI18n()
const router = useRouter()

// 使用认证组合函数
const {
  codeCountdown,
  isCodeLoading,
  codeSent,
  isLoading,
  sendEmailCode,
  register
} = useAuth()

// 表单数据
const formData = reactive({
  email: '',
  verifyCode: '',
  nickname: '',
  password: '',
  confirmPassword: '',
  agreeToTerms: false
})

// 错误状态
const errors = reactive({
  email: '',
  verifyCode: '',
  nickname: '',
  password: '',
  confirmPassword: '',
  terms: ''
})

// 组件状态
const showPassword = ref(false)
const showConfirmPassword = ref(false)
const formError = ref('')

// 计算属性
const isEmailValid = computed(() => {
  const result = validateEmail(formData.email)
  return result.valid
})

const passwordStrength = computed(() => {
  return validatePassword(formData.password)
})

// 验证字段
const validateField = (field: keyof typeof formData) => {
  switch (field) {
    case 'email':
      const emailResult = validateEmail(formData.email)
      errors.email = emailResult.valid ? '' : emailResult.message || ''
      break
    case 'verifyCode':
      const codeResult = validateVerificationCode(formData.verifyCode)
      errors.verifyCode = codeResult.valid ? '' : codeResult.message || ''
      break
    case 'nickname':
      const nicknameResult = validateNickname(formData.nickname)
      errors.nickname = nicknameResult.valid ? '' : nicknameResult.message || ''
      break
    case 'password':
      const passwordResult = validatePassword(formData.password)
      errors.password = passwordResult.valid ? '' : passwordResult.message || ''
      // 如果确认密码已输入，同时验证确认密码
      if (formData.confirmPassword) {
        validateField('confirmPassword')
      }
      break
    case 'confirmPassword':
      const confirmResult = validateConfirmPassword(formData.password, formData.confirmPassword)
      errors.confirmPassword = confirmResult.valid ? '' : confirmResult.message || ''
      break
  }
}

// 验证服务条款
const validateTerms = () => {
  if (!formData.agreeToTerms) {
    errors.terms = 'auth.terms_required'
    return false
  }
  errors.terms = ''
  return true
}

// 验证整个表单
const validateForm = (): boolean => {
  validateField('email')
  validateField('verifyCode')
  validateField('nickname')
  validateField('password')
  validateField('confirmPassword')
  const termsValid = validateTerms()
  
  return !errors.email && 
         !errors.verifyCode && 
         !errors.nickname && 
         !errors.password && 
         !errors.confirmPassword && 
         termsValid
}

// 发送验证码
const sendVerificationCode = async () => {
  validateField('email')
  if (!isEmailValid.value) {
    return
  }
  
  const success = await sendEmailCode(formData.email)
  if (success) {
    // 可以显示成功消息
    console.log('验证码发送成功')
  } else {
    formError.value = t('auth.code_sent')
  }
}

// 处理表单提交
const handleSubmit = async () => {
  formError.value = ''
  
  if (!validateForm()) {
    return
  }
  
  try {
    const userData = {
      email: formData.email,
      password: formData.password,
      confirmPassword: formData.confirmPassword,
      verifyCode: formData.verifyCode,
      nickname: formData.nickname || undefined
    }
    
    const result = await register(userData)
    
    if (result.success) {
      // 注册成功，跳转到个人中心
      await router.push('/profile')
    } else {
      formError.value = result.error || t('auth.register_failed')
    }
  } catch (error: any) {
    formError.value = error.message || t('auth.register_error')
  }
}

// 暴露给父组件的方法
defineExpose({
  sendVerificationCode
})
</script>

<style scoped>
.auth-form {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}
</style> 