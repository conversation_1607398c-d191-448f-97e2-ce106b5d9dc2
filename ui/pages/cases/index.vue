// pages/cases/index.vue
<template>
  <div class="cases-list-page relative min-h-screen">
    <!-- 动态背景装饰系统 -->
    <div class="fixed inset-0 overflow-hidden pointer-events-none z-0">
      <!-- 浮动光球装饰 -->
      <div class="floating-orb orb-1"></div>
      <div class="floating-orb orb-2"></div>
      <div class="floating-orb orb-3"></div>
      
      <!-- 网格背景 -->
      <div class="absolute inset-0 bg-grid opacity-[0.02]"></div>
      
      <!-- 渐变叠加 -->
      <div class="absolute inset-0 bg-gradient-to-br from-slate-900/50 via-gray-900/30 to-slate-800/50"></div>
    </div>

    <!-- 主要内容区域 -->
    <div class="container mx-auto px-4 pt-8 relative z-10">
      <!-- 页面标题和搜索筛选整合区域 -->
      <div class="hero-filter-section mb-12">
        <div class="hero-filter-container relative overflow-hidden rounded-2xl">
          <!-- 多层背景装饰系统 -->
          <div class="absolute inset-0 bg-slate-900/70 backdrop-blur-2xl rounded-2xl"></div>
          <div class="absolute inset-0 bg-gradient-to-br from-blue-900/15 via-purple-900/10 to-orange-900/15 rounded-2xl"></div>
          <div class="absolute inset-0 border border-white/20 rounded-2xl"></div>
          
          <!-- 动态装饰光球 -->
          <div class="absolute -top-20 -left-20 w-40 h-40 rounded-full bg-blue-500/10 blur-3xl animate-pulse"></div>
          <div class="absolute -bottom-20 -right-20 w-32 h-32 rounded-full bg-orange-500/10 blur-3xl animate-pulse" style="animation-delay: 1s;"></div>
          <div class="absolute top-1/4 left-1/2 transform -translate-x-1/2 w-60 h-60 rounded-full bg-purple-500/5 blur-3xl animate-float"></div>
          
          <!-- 顶部发光线条 -->
          <div class="absolute top-0 left-1/4 right-1/4 h-px bg-gradient-to-r from-transparent via-blue-400/60 to-transparent"></div>
          <div class="absolute top-1 left-1/3 right-1/3 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
          
          <!-- 侧边装饰线 -->
          <div class="absolute left-0 top-1/4 bottom-1/4 w-px bg-gradient-to-b from-transparent via-blue-400/30 to-transparent"></div>
          <div class="absolute right-0 top-1/4 bottom-1/4 w-px bg-gradient-to-b from-transparent via-orange-400/30 to-transparent"></div>
          
          <div class="relative z-10 p-8">
            <!-- 页面主标题区域 -->
            <div class="text-center mb-12">
              <div class="space-y-6">
                <!-- 主标题 -->
                <div class="space-y-2">
                  <h1 class="text-5xl lg:text-6xl font-bold tracking-tight">
                    <span class="hero-title-gradient">{{ $t('cases.title') }}</span>
                  </h1>
                  
                   <!-- 分隔装饰线 -->
            <div class="flex items-center justify-center pt-4">
              <div class="w-24 h-px bg-gradient-to-r from-transparent to-blue-400/50"></div>
              <div class="mx-4 w-3 h-3 rounded-full bg-gradient-to-r from-blue-400 to-orange-400"></div>
              <div class="w-24 h-px bg-gradient-to-l from-transparent to-orange-400/50"></div>
            </div>
                </div>
                
                <!-- 副标题 -->
                <p class="text-xl text-white/70 max-w-3xl mx-auto leading-relaxed">
                  {{ $t('cases.subtitle') }}
                </p>
              </div>
            </div>

           

            <!-- 搜索和排序行 - 增强设计 -->
            <div class="flex flex-col lg:flex-row gap-6 items-stretch mb-8">
              <!-- 搜索框 - 重新设计 -->
              <div class="flex-1 relative group">
                                 <div class="search-input-wrapper relative overflow-hidden rounded-2xl">
                   <!-- 多层背景效果 -->
                   <div class="absolute inset-0 bg-slate-800/60 backdrop-blur-sm rounded-2xl border border-slate-600/50 group-focus-within:border-blue-400/60 transition-all duration-500"></div>
                   <div class="absolute inset-0 bg-gradient-to-r from-blue-500/8 via-purple-500/5 to-orange-500/8 rounded-2xl opacity-0 group-focus-within:opacity-100 transition-opacity duration-500"></div>
                   
                   <!-- 发光效果 -->
                   <div class="absolute inset-0 rounded-2xl opacity-0 group-focus-within:opacity-100 transition-opacity duration-500" style="box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);"></div>
                   
                   <!-- 顶部装饰线 -->
                   <div class="absolute top-0 left-1/4 right-1/4 h-px bg-gradient-to-r from-transparent via-blue-400/40 to-transparent opacity-0 group-focus-within:opacity-100 transition-opacity duration-500"></div>
                   
                   <input 
                     type="text" 
                     v-model="searchQuery" 
                     :placeholder="$t('cases.search_placeholder')"
                     class="search-input relative z-10 w-full bg-transparent py-3 pl-12 pr-10 text-white placeholder-white/60 focus:outline-none text-base font-medium rounded-2xl"
                   >
                  
                  <!-- 搜索图标 - 增强设计 -->
                  <div class="absolute left-4 top-1/2 transform -translate-y-1/2 z-10">
                    <div class="relative">
                      <i class="fas fa-search text-white/60 group-focus-within:text-blue-400 transition-all duration-300 text-base"></i>
                      <div class="absolute inset-0 rounded-full bg-blue-400/20 scale-0 group-focus-within:scale-150 transition-transform duration-300 blur-sm"></div>
                    </div>
                  </div>
                  
                  <!-- 清除按钮 - 重新设计 -->
                  <button 
                    v-if="searchQuery"
                    @click="searchQuery = ''"
                    class="absolute right-4 top-1/2 transform -translate-y-1/2 z-10 w-8 h-8 rounded-full bg-white/10 hover:bg-red-500/20 border border-white/20 hover:border-red-400/50 flex items-center justify-center transition-all duration-300 group/clear"
                  >
                    <i class="fas fa-times text-sm text-white/70 group-hover/clear:text-red-400 transition-colors duration-300"></i>
                  </button>
                </div>
              </div>

              <!-- 排序下拉菜单 - 重新设计 -->
              <div class="lg:w-72 relative group">
                                 <div class="select-wrapper relative overflow-hidden rounded-2xl">
                   <!-- 多层背景效果 -->
                   <div class="absolute inset-0 bg-slate-800/60 backdrop-blur-sm rounded-2xl border border-slate-600/50 group-focus-within:border-orange-400/60 transition-all duration-500"></div>
                   <div class="absolute inset-0 bg-gradient-to-r from-orange-500/8 via-purple-500/5 to-blue-500/8 rounded-2xl opacity-0 group-focus-within:opacity-100 transition-opacity duration-500"></div>
                   
                   <!-- 发光效果 -->
                   <div class="absolute inset-0 rounded-2xl opacity-0 group-focus-within:opacity-100 transition-opacity duration-500" style="box-shadow: 0 0 20px rgba(251, 146, 60, 0.3);"></div>
                   
                   <!-- 顶部装饰线 -->
                   <div class="absolute top-0 left-1/4 right-1/4 h-px bg-gradient-to-r from-transparent via-orange-400/40 to-transparent opacity-0 group-focus-within:opacity-100 transition-opacity duration-500"></div>
                   
                   <select 
                     v-model="sortBy"
                     class="sort-select relative z-10 w-full bg-transparent py-3 px-4 pr-10 text-white appearance-none focus:outline-none cursor-pointer text-base font-medium rounded-2xl"
                   >
                    <option value="popular">{{ $t('cases.sort_by_popular') }}</option>
                    <option value="newest">{{ $t('cases.sort_by_newest') }}</option>
                    <option value="price-low">{{ $t('cases.sort_by_price_low') }}</option>
                    <option value="price-high">{{ $t('cases.sort_by_price_high') }}</option>
                  </select>
                  
                  <!-- 下拉箭头 - 增强设计 -->
                  <div class="absolute right-4 top-1/2 transform -translate-y-1/2 z-10 pointer-events-none">
                    <div class="relative">
                      <i class="fas fa-chevron-down text-white/60 group-focus-within:text-orange-400 transition-all duration-300 text-base group-focus-within:rotate-180"></i>
                      <div class="absolute inset-0 rounded-full bg-orange-400/20 scale-0 group-focus-within:scale-150 transition-transform duration-300 blur-sm"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

                         <!-- 标签筛选 - 重新设计 -->
             <div class="tags-section">
               <!-- 标签区域 - 标题和标签合并为一行 -->
               <div class="flex items-center flex-wrap gap-4">
                 <!-- 标签标题 -->
                 <div class="flex items-center gap-3 mr-2">
                   <div class="w-6 h-6 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center">
                     <i class="fas fa-tags text-white text-xs"></i>
                   </div>
                   <h3 class="text-base font-bold text-white whitespace-nowrap">{{ $t('cases.filter_by_tags') }}:</h3>
                 </div>
                 
                 <!-- 标签按钮 -->
                 <div class="flex flex-wrap gap-3 flex-1">
                   <button 
                     v-for="tag in filterTags" 
                     :key="tag.id" 
                     @click="toggleTag(tag.id)"
                     class="filter-tag group/tag relative overflow-hidden"
                     :class="{ 'active': activeTagIds.includes(tag.id) }"
                   >
                     <!-- 背景装饰 -->
                     <div class="absolute inset-0 bg-slate-700/40 rounded-full border border-white/20 group-hover/tag:border-blue-400/50 transition-all duration-300"></div>
                     <div class="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 rounded-full opacity-0 group-hover/tag:opacity-100 transition-opacity duration-300"></div>
                     
                     <!-- 激活状态背景 -->
                     <div class="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-full opacity-0 transition-opacity duration-300" :class="{ 'opacity-100': activeTagIds.includes(tag.id) }"></div>
                     <div class="absolute inset-0 border-2 border-blue-400/60 rounded-full opacity-0 transition-opacity duration-300" :class="{ 'opacity-100': activeTagIds.includes(tag.id) }"></div>
                     
                     <!-- 发光效果 -->
                     <div class="absolute inset-0 rounded-full opacity-0 group-hover/tag:opacity-100 transition-opacity duration-300" style="box-shadow: 0 0 15px rgba(59, 130, 246, 0.4);" :class="{ 'opacity-100': activeTagIds.includes(tag.id) }"></div>
                     
                     <span class="tag-content relative z-10 px-4 py-2 text-white/80 group-hover/tag:text-white font-medium text-sm transition-colors duration-300" :class="{ 'text-blue-300 font-semibold': activeTagIds.includes(tag.id) }">
                       {{ tag.name }}
                     </span>
                   </button>
                 </div>
                 
                 <!-- 清除筛选按钮 - 移到右侧 -->
                 <button 
                   v-if="activeTagIds.length > 0"
                   @click="activeTagIds = []"
                   class="clear-filters-btn group/clear relative overflow-hidden ml-auto"
                 >
                   <div class="absolute inset-0 bg-red-500/10 rounded-lg border border-red-400/30 group-hover/clear:bg-red-500/20 group-hover/clear:border-red-400/50 transition-all duration-300"></div>
                   <div class="absolute inset-0 bg-gradient-to-r from-red-500/5 to-pink-500/5 rounded-lg opacity-0 group-hover/clear:opacity-100 transition-opacity duration-300"></div>
                   <div class="relative z-10 flex items-center gap-2 px-3 py-2">
                     <i class="fas fa-times text-red-400 group-hover/clear:text-red-300 transition-colors duration-300 text-xs"></i>
                     <span class="text-red-400 group-hover/clear:text-red-300 font-medium text-sm transition-colors duration-300">{{ $t('cases.clear_filters') }}</span>
                   </div>
                 </button>
               </div>
             </div>
           </div>
           
           <!-- 底部装饰线 -->
           <div class="absolute bottom-0 left-1/4 right-1/4 h-px bg-gradient-to-r from-transparent via-white/30 to-transparent"></div>
         </div>
       </div>
     </div>

     <!-- 箱子展示区域 -->
     <div class="container mx-auto px-4 py-8 relative z-10">

      <!-- 加载状态 -->
      <div v-if="shouldShowSkeleton" class="space-y-8">
        <!-- 分类骨架 -->
        <div v-for="i in 3" :key="i" class="category-skeleton">
          <div class="absolute inset-0 bg-slate-900/60 backdrop-blur-xl rounded-2xl"></div>
          <div class="absolute inset-0 border border-white/10 rounded-2xl"></div>
          <div class="relative z-10 p-8">
            <UiCSGOSkeleton type="category" />
            <!-- 箱子网格骨架 -->
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6 mt-6">
              <UiCSGOSkeleton v-for="j in 10" :key="`${i}-${j}`" type="case" />
            </div>
          </div>
        </div>
        
        <!-- 错误状态浮层 -->
        <div v-if="hasError" class="error-overlay">
          <div class="error-card">
            <div class="error-icon">
              <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="error-content">
              <h4 class="error-title">{{ $t('cases.error_text') }}</h4>
              <p class="error-message">{{ errorMessage }}</p>
              <button @click="fetchCasesAll" class="retry-btn">
                <i class="fas fa-redo mr-2"></i>
                {{ $t('cases.retry_text') }}
              </button>
            </div>
          </div>
        </div>
        
        <!-- 无数据状态浮层 -->
        <div v-else-if="!isLoading && !hasError && casesData.length === 0" class="no-data-overlay">
          <div class="no-data-card">
            <div class="no-data-icon">
              <i class="fas fa-box-open"></i>
            </div>
            <div class="no-data-content">
              <h4 class="no-data-title">{{ $t('cases.no_data_text') }}</h4>
              <p class="no-data-message">{{ $t('cases.contact_text') }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 分类展示区域 -->
      <div v-else class="categories-section space-y-12">
        <!-- 搜索结果为空 -->
        <div v-if="filteredCasesData.length === 0" class="no-results-card">
          <div class="absolute inset-0 bg-slate-900/60 backdrop-blur-xl rounded-2xl"></div>
          <div class="absolute inset-0 border border-white/10 rounded-2xl"></div>
          <div class="relative z-10 p-12 text-center">
            <div class="no-results-icon mb-6">
              <i class="fas fa-search"></i>
            </div>
            <h3 class="text-2xl font-bold text-white mb-4">{{ $t('cases.no_search_results') || '没有找到匹配的箱子' }}</h3>
            <p class="text-white/70 mb-8">{{ $t('cases.try_different_search') || '请尝试不同的搜索条件或清除筛选' }}</p>
            <button @click="searchQuery = ''; activeTagIds = []" class="clear-all-btn">
              <i class="fas fa-times mr-2"></i>
              {{ $t('cases.clear_filters') || '清除筛选' }}
            </button>
          </div>
        </div>
        
        <!-- 显示箱子分类 -->
        <div v-else v-for="category in filteredCasesData" :key="category.cate_id" class="category-section">
          <div class="category-container">
            <!-- 背景装饰 -->
            <div class="absolute inset-0 bg-slate-900/60 backdrop-blur-xl rounded-2xl"></div>
            <div class="absolute inset-0 bg-gradient-to-br from-blue-900/5 via-transparent to-orange-900/5 rounded-2xl"></div>
            <div class="absolute inset-0 border border-white/10 rounded-2xl"></div>
            
            <!-- 顶部发光线 -->
            <div class="absolute top-0 left-1/4 right-1/4 h-px bg-gradient-to-r from-transparent via-blue-400/30 to-transparent"></div>
            
            <div class="relative z-10 p-8">
              <!-- 分类标题 -->
              <div class="category-header">
                <div class="category-title-wrapper">
                  <div class="category-icon">
                    <i :class="getCategoryIcon(category)"></i>
                  </div>
                  <h2 class="category-title">{{ category.localizedCateName }}</h2>
                  <div class="category-count">{{ getCategoryBoxCount(category) }}</div>
                </div>
                
                <!-- 展开/折叠按钮 -->
                <button 
                  v-if="getCategoryBoxCount(category) > 10" 
                  @click="toggleCategory(category.cate_id)"
                  class="expand-btn"
                  :class="{ 'expanded': expandedCategories[category.cate_id] }"
                >
                  <span class="expand-text">
                    <span v-if="expandedCategories[category.cate_id]">{{ $t('cases.collapse') }}</span>
                    <span v-else>{{ $t('cases.show_all') }} ({{ getCategoryBoxCount(category) }})</span>
                  </span>
                  <i class="expand-icon" :class="expandedCategories[category.cate_id] ? 'fas fa-chevron-up' : 'fas fa-chevron-down'"></i>
                  <div class="expand-glow"></div>
                </button>
              </div>

              <!-- 无箱子提示 -->
              <div v-if="!category.cases || !category.cases.length" class="no-cases-message">
                <div class="no-cases-icon">
                  <i class="fas fa-box"></i>
                </div>
                <p class="no-cases-text">{{ $t('cases.no_cases_text') }}</p>
              </div>

              <!-- 箱子网格 -->
              <div v-else class="cases-grid-section">
                <div class="cases-grid">
                  <div 
                    v-for="caseItem in getCasesForDisplay(category.cate_id, false)" 
                    :key="caseItem.case_key"
                    class="case-card-enhanced"
                  >
                                            <!-- 卡片发光效果 -->
                        <div class="card-glow"></div>

                    <NuxtLink :to="`/cases/${caseItem.case_key}`" class="card-link">
                      <!-- 箱子图片区域 -->
                      <div class="case-image-section">
                        <div class="image-container">
                          <img 
                            :src="caseItem.cover" 
                            :alt="caseItem.localizedName"
                            class="case-image"
                            @error="handleImageError"
                          >
                          <!-- 图片装饰光效 -->
                          <div class="image-glow"></div>
                        </div>

                        <!-- 稀有度指示条 -->
                        <div class="rarity-indicator">
                          <div class="rarity-bar" :class="getTagClass(caseItem.tag)"></div>
                        </div>

                        <!-- 标签 -->
                        <div class="case-tags">
                          <div v-if="caseItem.tag" class="case-tag" :class="getTagClass(caseItem.tag)">
                            {{ caseItem.localizedTag }}
                          </div>
                        </div>
                      </div>

                      <!-- 箱子信息 -->
                      <div class="case-info-section">
                        <h3 class="case-name overflow-hidden text-ellipsis whitespace-nowrap">{{ caseItem.localizedName }}</h3>
                        
                        <div class="case-stats">
                          <!-- 价格信息 -->
                          <div class="price-section">
                            <div class="price-wrapper">
                              <span class="currency-symbol">$</span>
                              <span class="price-value">{{ caseItem.price?.toFixed(2) || '0.00' }}</span>
                              <span v-if="caseItem.discount && caseItem.discount < 100" class="original-price">
                                {{ ((caseItem.price || 0) * (100 / (caseItem.discount || 100))).toFixed(2) }}
                              </span>
                            </div>
                          </div>
                          
                          <!-- 开箱统计 -->
                          <div class="open-stats">
                            <i class="fas fa-box-open"></i>
                            <span>{{ caseItem.open_count || 0 }}</span>
                          </div>
                        </div>
                      </div>
                    </NuxtLink>

                    <!-- 快速开箱按钮 -->
                    <div class="quick-open-overlay">
                      <button class="quick-open-btn" @click="navigateToCase(caseItem.case_key)">
                        <i class="fas fa-bolt mr-2"></i>
                        {{ $t('cases.quick_open') }}
                        <div class="btn-glow"></div>
                      </button>
                    </div>
                  </div>
                </div>

                <!-- 展开的附加箱子 -->
                <transition name="expanded-cases">
                  <div v-if="expandedCategories[category.cate_id]" class="expanded-cases-section">
                    <div class="cases-grid">
                      <div 
                        v-for="caseItem in getCasesForDisplay(category.cate_id, true)" 
                        :key="caseItem.case_key"
                        class="case-card-enhanced"
                      >
                        <!-- 卡片发光效果 -->
                        <div class="card-glow"></div>

                        <NuxtLink :to="`/cases/${caseItem.case_key}`" class="card-link">
                          <!-- 箱子图片区域 -->
                          <div class="case-image-section">
                            <div class="image-container">
                              <img 
                                :src="caseItem.cover" 
                                :alt="caseItem.localizedName"
                                class="case-image"
                                @error="handleImageError"
                              >
                              <!-- 图片装饰光效 -->
                              <div class="image-glow"></div>
                            </div>

                            <!-- 稀有度指示条 -->
                            <div class="rarity-indicator">
                              <div class="rarity-bar" :class="getTagClass(caseItem.tag)"></div>
                            </div>

                            <!-- 标签 -->
                            <div class="case-tags">
                              <div v-if="caseItem.tag" class="case-tag" :class="getTagClass(caseItem.tag)">
                                {{ caseItem.localizedTag }}
                              </div>
                            </div>
                          </div>

                          <!-- 箱子信息 -->
                          <div class="case-info-section">
                            <h3 class="case-name">{{ caseItem.localizedName }}</h3>
                            
                            <div class="case-stats">
                              <!-- 价格信息 -->
                              <div class="price-section">
                                <div class="price-wrapper">
                                  <span class="currency-symbol">$</span>
                                  <span class="price-value">{{ caseItem.price?.toFixed(2) || '0.00' }}</span>
                                  <span v-if="caseItem.discount && caseItem.discount < 100" class="original-price">
                                    {{ ((caseItem.price || 0) * (100 / (caseItem.discount || 100))).toFixed(2) }}
                                  </span>
                                </div>
                              </div>
                              
                              <!-- 开箱统计 -->
                              <div class="open-stats">
                                <i class="fas fa-box-open"></i>
                                <span>{{ caseItem.open_count || 0 }}</span>
                              </div>
                            </div>
                          </div>
                        </NuxtLink>

                        <!-- 快速开箱按钮 -->
                        <div class="quick-open-overlay">
                          <button class="quick-open-btn" @click="navigateToCase(caseItem.case_key)">
                            <i class="fas fa-bolt mr-2"></i>
                            {{ $t('cases.quick_open') }}
                            <div class="btn-glow"></div>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </transition>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 手动导入骨架图组件


// 页面配置和SEO优化
definePageMeta({
  title: 'Cases',
  key: 'cases'
})

// SEO和国际化头部配置
const { $i18n } = useNuxtApp()
const localePath = useLocalePath()

useHead({
  title: () => $i18n.t('cases.title'),
  meta: [
    {
      name: 'description',
      content: () => $i18n.t('cases.subtitle')
    },
    {
      name: 'keywords',
      content: 'CSGO, CS:GO, cases, weapon cases, skins, gaming'
    },
    {
      property: 'og:title',
      content: () => $i18n.t('cases.title')
    },
    {
      property: 'og:description',
      content: () => $i18n.t('cases.subtitle')
    }
  ],
  link: [
    {
      rel: 'canonical',
      href: () => `${useRuntimeConfig().public.siteUrl}${localePath('/cases')}`
    }
  ]
})

// 响应式状态
const casesData = ref<any[]>([])
const caseCategories = ref<any[]>([])
const isLoading = ref(true)
const error = ref<string | null>(null)
const searchQuery = ref('')
const sortBy = ref('popular')
const activeTagIds = ref<string[]>([])
const expandedCategories = ref<Record<string, boolean>>({})

// 计算属性
const hasError = computed(() => !!error.value)
const errorMessage = computed(() => error.value || '')

// 是否显示骨架图 - 加载中、出错或无数据时都显示骨架图
const shouldShowSkeleton = computed(() => {
  const loading = isLoading.value
  const hasErr = hasError.value
  const dataLength = casesData.value.length
  
  // 如果正在加载，显示骨架图
  if (loading) {
    return true
  }
  
  // 如果有错误，显示骨架图
  if (hasErr) {
    return true
  }
  
  // 如果没有数据，显示骨架图
  if (dataLength === 0) {
    return true
  }
  
  return false
})

// 使用增强的store
const caseStore = useCaseStore()

// 获取所有箱子 - 使用store
const fetchCasesAll = async () => {
  try {
    isLoading.value = true
    error.value = null

    //console.log('[Cases页面] 开始获取箱子分类数据，显示骨架图')
    
    // 使用store获取数据
    const data = await caseStore.fetchAllCases()
    casesData.value = data
    
    // console.log('[Cases页面] 分类数据获取成功:', {
    //   totalCategories: casesData.value.length,
    //   categories: casesData.value.map((cat: any) => ({
    //     id: cat.cate_id,
    //     name: cat.cate_name,
    //     count: cat.cases?.length || 0
    //   }))
    // })

  } catch (err) {
    console.error('获取案例数据失败:', err)
    error.value = err instanceof Error ? err.message : '获取数据失败，请稍后再试'
  } finally {
    isLoading.value = false
    // console.log('[Cases页面] 数据加载完成，隐藏骨架图')
  }
}

// 标签样式
const getTagClass = (tag: string) => {
  if (!tag) return 'bg-gray-500/10 text-white'

  if (tag === 'NEW') return 'bg-emerald-500/90 text-white'
  if (tag === 'HOT') return 'bg-orange-500/90 text-white'  
  if (tag === 'SELL') return 'bg-red-500/90 text-white'
  if (tag === 'SPECIAL') return 'bg-purple-500/90 text-white'
  if (tag === 'FREE') return 'bg-red-600 text-white'

  return 'bg-gray-500/90 text-white'
}

// 标签数据（根据当前语言本地化）
const filterTags = computed(() => {
  const tags = new Set<string>()
  casesData.value.forEach(category => {
    category.cases?.forEach((caseItem: any) => {
      if (caseItem.tag) {
        tags.add(caseItem.tag)
      }
    })
  })
  
  return Array.from(tags).map(tag => ({
    id: tag,
    name: tag
  }))
})

// 导航到箱子详情
const navigateToCase = (caseKey: string) => {
  if (caseKey) {
    navigateTo(`/cases/${caseKey}`)
  }
}

// 切换标签筛选
const toggleTag = (tagId: string) => {
  const index = activeTagIds.value.indexOf(tagId)
  if (index > -1) {
    activeTagIds.value.splice(index, 1)
  } else {
    activeTagIds.value.push(tagId)
  }
}

// 工具函数
const { t, locale } = useI18n()

// 获取本地化名称
const getLocalizedName = (item: any): string => {
  if (!item) return ''
  
  const currentLocale = locale.value as string
  
  // 优先级：当前语言 > 中文 > 英文 > name字段 > 默认值
  if (currentLocale.startsWith('zh')) {
    return item.name_zh_hans || item.name_zh || item.name_cn || item.name || item.cate_name || '未知'
  } else {
    return item.name_en || item.name || item.cate_name || 'Unknown'
  }
}

// 为模板中的所有本地化名称创建响应式计算属性
const localizedCasesData = computed(() => {
  return casesData.value.map(category => ({
    ...category,
    localizedCateName: getLocalizedName(category),
    cases: category.cases?.map((caseItem: any) => ({
      ...caseItem,
      localizedName: getLocalizedName(caseItem),
      localizedTag: caseItem.tag ? getLocalizedName({ name: caseItem.tag }) : '',
      localizedTypeName: caseItem.type_name ? getLocalizedName({ name: caseItem.type_name }) : ''
    })) || []
  }))
})

// 获取过滤后的分类数据
const filteredCasesData = computed(() => {
  let filtered = localizedCasesData.value

  // 搜索过滤
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase().trim()
    filtered = filtered.map(category => ({
      ...category,
      cases: category.cases.filter((caseItem: any) => 
        caseItem.localizedName.toLowerCase().includes(query) ||
        caseItem.localizedTag.toLowerCase().includes(query) ||
        caseItem.localizedTypeName.toLowerCase().includes(query)
      )
    })).filter(category => category.cases.length > 0)
  }

  // 标签过滤
  if (activeTagIds.value.length > 0) {
    filtered = filtered.map(category => ({
      ...category,
      cases: category.cases.filter((caseItem: any) => 
        activeTagIds.value.includes(caseItem.tag)
      )
    })).filter(category => category.cases.length > 0)
  }

  return filtered
})

// 获取显示的箱子列表
const getCasesForDisplay = (categoryId: string, isExpanded: boolean) => {
  const category = filteredCasesData.value.find(cat => cat.cate_id === categoryId)
  if (!category || !category.cases) return []
  
  if (isExpanded) {
    // 展开状态显示除前5个之外的所有箱子
    return category.cases.slice(5)
  } else {
    // 否则，返回前5个箱子
    return category.cases.slice(0, 5)
  }
}

// 切换分类的展开状态
const toggleCategory = (categoryId: string) => {
  expandedCategories.value[categoryId] = !expandedCategories.value[categoryId]
}

// 获取分类箱子数量
const getCategoryBoxCount = (category: any): number => {
  return category.cases?.length || 0
}

// 根据分类获取适当的图标
const getCategoryIcon = (category: any): string => {
  if (!category) return 'fas fa-box'

  const cateName = (category.localizedCateName || getLocalizedName(category)).toLowerCase()

  // 检查分类名称中的关键词
  if (cateName.includes('武器') || cateName.includes('weapon')) {
    return 'fas fa-crosshairs'
  } else if (cateName.includes('刀具') || cateName.includes('knife')) {
    return 'fas fa-cut'
  } else if (cateName.includes('手套') || cateName.includes('glove')) {
    return 'fas fa-hand-paper'
  } else if (cateName.includes('热门') || cateName.includes('hot')) {
    return 'fas fa-fire'
  } else if (cateName.includes('新品') || cateName.includes('new')) {
    return 'fas fa-star'
  } else if (cateName.includes('折扣') || cateName.includes('discount')) {
    return 'fas fa-tag'
  }

  // 默认图标
  return 'fas fa-box'
}

// 处理图片加载错误
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/favicon.ico'
}

// 监听语言变化，强制更新数据
watch(locale, (newLocale, oldLocale) => {
  // console.log('语言切换:', oldLocale, '->', newLocale)
  // 语言变化时不需要重新获取数据，计算属性会自动更新
}, { immediate: false })

// 监听排序变化，重新获取数据
watch(sortBy, () => {
  fetchCasesAll()
})

// 页面挂载时获取数据
onMounted(async () => {
  // console.log('[Cases页面] 页面挂载，开始获取数据')
  await fetchCasesAll()
  // console.log('[Cases页面] 数据获取完成，casesData.value:', casesData.value.length, '个分类')
})
</script>

<style lang="scss" scoped>
// CSS变量定义
:root {
  --color-primary: #00A8FF;
  --color-secondary: #FF4D00;
  --color-primary-rgb: 0, 168, 255;
  --color-secondary-rgb: 255, 77, 0;
}

// 动态背景装饰系统
.floating-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(60px);
  opacity: 0.6;
  animation-duration: 20s;
  animation-iteration-count: infinite;
  animation-timing-function: ease-in-out;
  
  &.orb-1 {
    width: 400px;
    height: 400px;
    background: radial-gradient(circle, rgba(0, 168, 255, 0.15) 0%, transparent 70%);
    top: 10%;
    left: 10%;
    animation-name: float-1;
  }
  
  &.orb-2 {
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(255, 77, 0, 0.12) 0%, transparent 70%);
    top: 60%;
    right: 15%;
    animation-name: float-2;
    animation-delay: -7s;
  }
  
  &.orb-3 {
    width: 250px;
    height: 250px;
    background: radial-gradient(circle, rgba(147, 51, 234, 0.1) 0%, transparent 70%);
    bottom: 20%;
    left: 60%;
    animation-name: float-3;
    animation-delay: -14s;
  }
}

@keyframes float-1 {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  33% { transform: translate(30px, -30px) rotate(120deg); }
  66% { transform: translate(-20px, 20px) rotate(240deg); }
}

@keyframes float-2 {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  33% { transform: translate(-25px, 35px) rotate(-120deg); }
  66% { transform: translate(40px, -15px) rotate(-240deg); }
}

@keyframes float-3 {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  33% { transform: translate(20px, 25px) rotate(90deg); }
  66% { transform: translate(-30px, -20px) rotate(180deg); }
}

// 网格背景
.bg-grid {
  background-image:
    linear-gradient(to right, rgba(255, 255, 255, 0.03) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
  background-size: 20px 20px;
}

// Hero区域
.hero-section {
  min-height: 40vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-title-gradient {
  background: linear-gradient(135deg, #00A8FF 0%, #ffffff 50%, #FF4D00 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  animation: title-shimmer 3s ease-in-out infinite;
}

@keyframes title-shimmer {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

// 统计卡片
.stat-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem 2rem;
  background: rgba(30, 41, 59, 0.6);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 1rem;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(0, 168, 255, 0.15);
    border-color: rgba(0, 168, 255, 0.3);
  }
}

.stat-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
}

.stat-content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: white;
  line-height: 1;
}

.stat-label {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

// 浮动动画
@keyframes animate-float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes animate-float-delayed {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-15px); }
}

.animate-float {
  animation: animate-float 6s ease-in-out infinite;
}

.animate-float-delayed {
  animation: animate-float-delayed 8s ease-in-out infinite;
  animation-delay: -2s;
}

// 筛选区域
.filter-section {
  position: relative;
}

.filter-container {
  position: relative;
  overflow: hidden;
}

// 搜索输入框
.search-input-wrapper {
  position: relative;
}

.search-input {
  font-size: 1rem;
  font-weight: 500;
  
  &::placeholder {
    transition: color 0.3s ease;
  }
  
  &:focus::placeholder {
    color: rgba(255, 255, 255, 0.3);
  }
}

// 下拉选择框
.select-wrapper {
  position: relative;
}

.sort-select {
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  
  option {
    background: #1e293b;
    color: white;
    padding: 0.5rem;
  }
}

// 标签筛选 - 重新设计的样式
.tags-section {
  // 新的清除按钮样式已在模板中使用内联类
}

.filter-tag {
  // 新的标签样式已在模板中使用内联类
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  &:hover {
    transform: translateY(-2px) scale(1.02);
  }
  
  &:active {
    transform: translateY(0) scale(0.98);
  }
}

// 骨架图
.category-skeleton {
  position: relative;
  overflow: hidden;
}

// 错误和无数据状态
.error-overlay,
.no-data-overlay {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 50;
}

.error-card,
.no-data-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(30, 41, 59, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 1rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  max-width: 24rem;
}

.error-card {
  border-color: rgba(239, 68, 68, 0.3);
}

.error-icon,
.no-data-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.error-icon {
  background: rgba(239, 68, 68, 0.2);
  color: #f87171;
}

.no-data-icon {
  background: rgba(107, 114, 128, 0.2);
  color: #9ca3af;
}

.error-content,
.no-data-content {
  flex: 1;
}

.error-title,
.no-data-title {
  font-size: 1rem;
  font-weight: 600;
  color: white;
  margin-bottom: 0.5rem;
}

.error-message,
.no-data-message {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 1rem;
}

.retry-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
  border-radius: 0.5rem;
  color: white;
  font-size: 0.875rem;
  font-weight: 600;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 10px 20px rgba(0, 168, 255, 0.3);
  }
}

// 无搜索结果
.no-results-card {
  position: relative;
  overflow: hidden;
}

.no-results-icon {
  width: 4rem;
  height: 4rem;
  border-radius: 50%;
  background: rgba(107, 114, 128, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: #9ca3af;
  margin: 0 auto;
}

.clear-all-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 2rem;
  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
  border-radius: 0.5rem;
  color: white;
  font-weight: 600;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(0, 168, 255, 0.3);
  }
}

// 分类区域
.category-section {
  position: relative;
}

.category-container {
  position: relative;
  overflow: hidden;
}

.category-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.category-title-wrapper {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.category-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
}

.category-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: white;
  margin: 0;
}

.category-count {
  padding: 0.25rem 0.75rem;
  background: rgba(0, 168, 255, 0.2);
  border: 1px solid rgba(0, 168, 255, 0.3);
  border-radius: 1rem;
  color: #00A8FF;
  font-size: 0.875rem;
  font-weight: 600;
}

// 展开按钮
.expand-btn {
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1.5rem;
  background: rgba(30, 41, 59, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 2rem;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.875rem;
  font-weight: 600;
  transition: all 0.3s ease;
  overflow: hidden;
  
  &:hover {
    transform: translateY(-2px);
    color: white;
    border-color: rgba(0, 168, 255, 0.3);
  }
  
  &.expanded {
    background: rgba(0, 168, 255, 0.2);
    border-color: rgba(0, 168, 255, 0.5);
    color: #00A8FF;
    
    .expand-glow {
      opacity: 1;
    }
  }
}

.expand-text {
  position: relative;
  z-index: 10;
}

.expand-icon {
  position: relative;
  z-index: 10;
  transition: transform 0.3s ease;
}

.expand-btn.expanded .expand-icon {
  transform: rotate(180deg);
}

.expand-glow {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(0, 168, 255, 0.1), rgba(255, 77, 0, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
}

// 无箱子消息
.no-cases-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 3rem;
  text-align: center;
}

.no-cases-icon {
  width: 4rem;
  height: 4rem;
  border-radius: 50%;
  background: rgba(107, 114, 128, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: #9ca3af;
}

.no-cases-text {
  color: rgba(255, 255, 255, 0.7);
  font-size: 1rem;
}

// 箱子网格
.cases-grid-section {
  margin-top: 2rem;
}

.cases-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 1.5rem;
  
  @media (max-width: 1280px) {
    grid-template-columns: repeat(4, 1fr);
  }
  
  @media (max-width: 1024px) {
    grid-template-columns: repeat(3, 1fr);
  }
  
  @media (max-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
  
  @media (max-width: 480px) {
    grid-template-columns: 1fr;
  }
}

// 箱子卡片增强版
.case-card-enhanced {
  position: relative;
  border-radius: 1rem;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  
  &:hover {
    transform: translateY(-4px) scale(1.01);
    border-color: rgba(0, 168, 255, 0.3);
    
    .card-glow {
      opacity: 1;
    }
    
    .case-image {
      transform: scale(1.05);
    }
    
    .quick-open-overlay {
      opacity: 1;
      transform: translateY(0);
    }
    
    .image-glow {
      opacity: 1;
    }
  }
}

// 卡片发光效果
.card-glow {
  position: absolute;
  inset: -2px;
  background: linear-gradient(135deg, rgba(0, 168, 255, 0.3), rgba(255, 77, 0, 0.3));
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.4s ease;
  z-index: -1;
  filter: blur(8px);
}

// 卡片链接
.card-link {
  position: relative;
  z-index: 10;
  display: block;
  height: 100%;
}

// 箱子图片区域
.case-image-section {
  position: relative;
  padding: 2rem;
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.case-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.3));
}

.image-glow {
  position: absolute;
  inset: 10%;
  background: radial-gradient(circle, rgba(0, 168, 255, 0.2) 0%, transparent 70%);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.4s ease;
  filter: blur(20px);
}

// 稀有度指示条
.rarity-indicator {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4px;
  z-index: 20;
}

.rarity-bar {
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, currentColor, transparent);
}

// 箱子标签
.case-tags {
  position: absolute;
  top: 1rem;
  left: 1rem;
  z-index: 20;
}

.case-tag {
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border: 1px solid currentColor;
}

// 箱子信息区域
.case-info-section {
  padding: 1.5rem;
  position: relative;
  z-index: 10;
}

.case-name {
  font-size: 1.125rem;
  font-weight: 700;
  color: white;
  margin-bottom: 1rem;
  text-align: center;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.case-stats {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

// 价格区域
.price-section {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.price-wrapper {
  display: flex;
  align-items: baseline;
  gap: 0.25rem;
}

.currency-symbol {
  color: #10b981;
  font-size: 1.25rem;
  font-weight: 700;
}

.price-value {
  color: #10b981;
  font-size: 1.25rem;
  font-weight: 700;
}

.original-price {
  color: rgba(255, 255, 255, 0.4);
  font-size: 0.875rem;
  text-decoration: line-through;
}

// 开箱统计
.open-stats {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.875rem;
  
  i {
    font-size: 1rem;
  }
}

// 快速开箱覆盖层
.quick-open-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 1.5rem;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.9), transparent);
  opacity: 0;
  transform: translateY(100%);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 20;
}

.quick-open-btn {
  position: relative;
  width: 100%;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
  border-radius: 0.75rem;
  color: white;
  font-size: 0.875rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  transition: all 0.3s ease;
  overflow: hidden;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(0, 168, 255, 0.4);
    
    .btn-glow {
      opacity: 1;
    }
  }
  
  &:active {
    transform: translateY(0);
  }
}

.btn-glow {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

// 展开的箱子区域
.expanded-cases-section {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

// 展开动画
.expanded-cases-enter-active,
.expanded-cases-leave-active {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: top;
}

.expanded-cases-enter-from {
  opacity: 0;
  transform: translateY(-20px) scale(0.95);
}

.expanded-cases-leave-to {
  opacity: 0;
  transform: translateY(-10px) scale(0.98);
}

// 响应式设计
@media (max-width: 1024px) {
  .hero-section {
    min-height: 50vh;
    
    .container {
      padding-top: 3rem;
      padding-bottom: 3rem;
    }
  }
  
  .stat-card {
    padding: 1rem 1.5rem;
  }
  
  .stat-number {
    font-size: 1.5rem;
  }
  
  .category-title {
    font-size: 1.5rem;
  }
}

@media (max-width: 768px) {
  .floating-orb {
    display: none;
  }
  
  .hero-section {
    min-height: 40vh;
  }
  
  .hero-title-gradient {
    font-size: 2.5rem;
  }
  
  .filter-container {
    padding: 1.5rem;
  }
  
  .category-container {
    padding: 1.5rem;
  }
  
  .cases-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
  }
  
  .case-card-enhanced:hover {
    transform: translateY(-4px) scale(1.01);
  }
}

@media (max-width: 640px) {
  .hero-title-gradient {
    font-size: 2rem;
  }
  
  .stat-card {
    flex-direction: column;
    text-align: center;
    padding: 1rem;
  }
  
  .category-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .category-title-wrapper {
    justify-content: center;
  }
  
  .expand-btn {
    justify-content: center;
  }
}
</style>