import { ref, computed } from 'vue'

export interface WebSocketMessage {
  message_sequence?: number
  message_type?: string
  round?: number
  server_timestamp?: number
  [key: string]: any
}

export interface MessageValidationResult {
  isValid: boolean
  reason?: string
  shouldProcess: boolean
  isDuplicate: boolean
  isStale: boolean
}

export interface DeduplicationStats {
  totalReceived: number
  duplicatesBlocked: number
  staleMessagesBlocked: number
  validProcessed: number
  messagesInCache: number
  lastCleanupTime: number
}

/**
 * WebSocket消息去重管理器
 * 基于message_sequence字段防止重复处理消息
 * 解决前端反馈的轮次消息重复和缺失问题
 */
export function useWebSocketMessageDeduplicator() {
  // 🎯 消息缓存 - 存储已处理的消息ID
  const processedMessages = ref(new Set<string>())
  
  // 🎯 序列号跟踪 - 记录最后处理的序列号
  const lastSequenceByType = ref(new Map<string, number>())
  
  // 🎯 消息缓冲区 - 临时存储乱序消息
  const messageBuffer = ref(new Map<string, WebSocketMessage>())
  
  // 🎯 统计信息
  const stats = ref<DeduplicationStats>({
    totalReceived: 0,
    duplicatesBlocked: 0,
    staleMessagesBlocked: 0,
    validProcessed: 0,
    messagesInCache: 0,
    lastCleanupTime: Date.now()
  })

  // 🎯 配置参数
  const config = {
    cacheExpirationTime: 5 * 60 * 1000, // 5分钟
    maxCacheSize: 1000, // 最大缓存消息数
    staleMessageThreshold: 10 * 1000, // 10秒内的消息被认为是过期的
    bufferTimeout: 2000, // 缓冲区超时时间
    enableSequenceValidation: true,
    enableBuffering: true
  }

  /**
   * 验证消息是否应该被处理
   * @param message WebSocket消息
   * @returns 验证结果
   */
  const validateMessage = (message: WebSocketMessage): MessageValidationResult => {
    stats.value.totalReceived++

    // 🎯 1. 基础字段检查
    if (!message.message_sequence || !message.message_type) {
      return {
        isValid: false,
        reason: '缺少必要的message_sequence或message_type字段',
        shouldProcess: false,
        isDuplicate: false,
        isStale: false
      }
    }

    const { message_sequence, message_type, round, server_timestamp } = message
    
    // 🎯 2. 生成唯一消息ID
    const messageId = generateMessageId(message)
    
    // 🎯 3. 检查重复消息
    if (processedMessages.value.has(messageId)) {
      stats.value.duplicatesBlocked++
      console.warn(`[MESSAGE_DEDUP] 重复消息已忽略: ${messageId}`)
      return {
        isValid: true,
        reason: '重复消息',
        shouldProcess: false,
        isDuplicate: true,
        isStale: false
      }
    }

    // 🎯 4. 检查消息时效性
    if (server_timestamp) {
      const messageAge = Date.now() - server_timestamp
      if (messageAge > config.staleMessageThreshold) {
        stats.value.staleMessagesBlocked++
        console.warn(`[MESSAGE_DEDUP] 过期消息已忽略: ${messageId}, 延迟: ${messageAge}ms`)
        return {
          isValid: true,
          reason: `消息过期 (延迟${messageAge}ms)`,
          shouldProcess: false,
          isDuplicate: false,
          isStale: true
        }
      }
    }

    // 🎯 5. 序列号验证（可选）
    if (config.enableSequenceValidation) {
      const lastSequence = lastSequenceByType.value.get(message_type) || 0
      if (message_sequence < lastSequence) {
        console.warn(`[MESSAGE_DEDUP] 收到过期序列号: ${messageId}, 当前序列: ${lastSequence}`)
        // 注意：不阻止处理，只是警告
      }
    }

    return {
      isValid: true,
      reason: '消息有效',
      shouldProcess: true,
      isDuplicate: false,
      isStale: false
    }
  }

  /**
   * 标记消息为已处理
   * @param message WebSocket消息
   */
  const markAsProcessed = (message: WebSocketMessage): void => {
    const messageId = generateMessageId(message)
    
    // 🎯 添加到已处理集合
    processedMessages.value.add(messageId)
    
    // 🎯 更新序列号跟踪
    if (message.message_sequence && message.message_type) {
      const currentSequence = lastSequenceByType.value.get(message.message_type) || 0
      lastSequenceByType.value.set(
        message.message_type, 
        Math.max(currentSequence, message.message_sequence)
      )
    }
    
    // 🎯 更新统计
    stats.value.validProcessed++
    stats.value.messagesInCache = processedMessages.value.size
    
    // 🎯 从缓冲区移除（如果存在）
    messageBuffer.value.delete(messageId)
    
    console.debug(`[MESSAGE_DEDUP] 消息已标记为处理: ${messageId}`)
  }

  /**
   * 处理消息（包含去重逻辑）
   * @param message WebSocket消息
   * @param processor 消息处理函数
   * @returns 是否成功处理
   */
  const processMessage = async (
    message: WebSocketMessage, 
    processor: (msg: WebSocketMessage) => Promise<void> | void
  ): Promise<boolean> => {
    try {
      // 🎯 验证消息
      const validation = validateMessage(message)
      
      if (!validation.shouldProcess) {
        return false
      }

      // 🎯 处理消息
      await processor(message)
      
      // 🎯 标记为已处理
      markAsProcessed(message)
      
      return true
    } catch (error) {
      console.error('[MESSAGE_DEDUP] 消息处理失败:', error)
      return false
    }
  }

  /**
   * 批量处理消息
   * @param messages 消息数组
   * @param processor 处理函数
   * @returns 成功处理的消息数量
   */
  const processBatch = async (
    messages: WebSocketMessage[], 
    processor: (msg: WebSocketMessage) => Promise<void> | void
  ): Promise<number> => {
    let processedCount = 0
    
    // 🎯 按序列号排序，确保按正确顺序处理
    const sortedMessages = [...messages].sort((a, b) => {
      return (a.message_sequence || 0) - (b.message_sequence || 0)
    })
    
    for (const message of sortedMessages) {
      const success = await processMessage(message, processor)
      if (success) {
        processedCount++
      }
    }
    
    return processedCount
  }

  /**
   * 清理过期消息记录
   */
  const cleanup = (): void => {
    const now = Date.now()
    const expiredBefore = now - config.cacheExpirationTime
    
    // 🎯 清理过期的已处理消息记录
    let cleanedCount = 0
    for (const messageId of processedMessages.value) {
      const timestamp = extractTimestampFromMessageId(messageId)
      if (timestamp && timestamp < expiredBefore) {
        processedMessages.value.delete(messageId)
        cleanedCount++
      }
    }
    
    // 🎯 清理缓冲区中的过期消息
    for (const [messageId, message] of messageBuffer.value) {
      if (message.server_timestamp && message.server_timestamp < expiredBefore) {
        messageBuffer.value.delete(messageId)
      }
    }
    
    // 🎯 限制缓存大小
    if (processedMessages.value.size > config.maxCacheSize) {
      const sortedIds = Array.from(processedMessages.value).sort()
      const toRemove = sortedIds.slice(0, sortedIds.length - config.maxCacheSize)
      toRemove.forEach(id => processedMessages.value.delete(id))
    }
    
    stats.value.lastCleanupTime = now
    stats.value.messagesInCache = processedMessages.value.size
    
    console.debug(`[MESSAGE_DEDUP] 清理完成: 删除${cleanedCount}条过期记录`)
  }

  /**
   * 生成唯一消息ID
   * @param message WebSocket消息
   * @returns 唯一消息ID
   */
  const generateMessageId = (message: WebSocketMessage): string => {
    const { message_sequence, message_type, round } = message
    return `${message_type}_${round || 0}_${message_sequence}`
  }

  /**
   * 从消息ID中提取时间戳
   * @param messageId 消息ID
   * @returns 时间戳或null
   */
  const extractTimestampFromMessageId = (messageId: string): number | null => {
    const parts = messageId.split('_')
    const sequencePart = parts[parts.length - 1]
    const timestamp = parseInt(sequencePart)
    return isNaN(timestamp) ? null : timestamp
  }

  /**
   * 重置去重器状态
   */
  const reset = (): void => {
    processedMessages.value.clear()
    lastSequenceByType.value.clear()
    messageBuffer.value.clear()
    
    stats.value = {
      totalReceived: 0,
      duplicatesBlocked: 0,
      staleMessagesBlocked: 0,
      validProcessed: 0,
      messagesInCache: 0,
      lastCleanupTime: Date.now()
    }
    
    console.log('[MESSAGE_DEDUP] 去重器已重置')
  }

  /**
   * 获取调试信息
   */
  const getDebugInfo = () => {
    return {
      stats: stats.value,
      config,
      cacheSize: processedMessages.value.size,
      bufferSize: messageBuffer.value.size,
      lastSequences: Object.fromEntries(lastSequenceByType.value),
      recentMessages: Array.from(processedMessages.value).slice(-10)
    }
  }

  // 🎯 自动清理定时器
  const cleanupInterval = setInterval(() => {
    cleanup()
  }, config.cacheExpirationTime)

  // 🎯 计算属性
  const deduplicationRate = computed(() => {
    const total = stats.value.totalReceived
    if (total === 0) return 0
    return ((stats.value.duplicatesBlocked + stats.value.staleMessagesBlocked) / total * 100).toFixed(2)
  })

  const isHealthy = computed(() => {
    return stats.value.messagesInCache < config.maxCacheSize && 
           Date.now() - stats.value.lastCleanupTime < config.cacheExpirationTime * 2
  })

  return {
    // 核心功能
    validateMessage,
    processMessage,
    processBatch,
    markAsProcessed,
    
    // 管理功能
    cleanup,
    reset,
    
    // 状态和统计
    stats: computed(() => stats.value),
    deduplicationRate,
    isHealthy,
    
    // 调试功能
    getDebugInfo,
    
    // 生命周期
    destroy: () => {
      clearInterval(cleanupInterval)
      reset()
    }
  }
} 