from django.contrib import admin
from modeltranslation.admin import TranslationAdmin

from steambase.admin import ReadOnlyAdmin, ChangeOnlyAdmin
from lottery.models import LotterySetting, LotteryJoiner, LotteryInfoSetting


@admin.register(LotterySetting)
class LotterySettingAdmin(admin.ModelAdmin):
    list_display = ('lottery_type', 'item_info', 'min_charge', 'max_joiner', 'enable')
    list_editable = ('min_charge', 'max_joiner', 'enable')
    raw_id_fields = ['item_info']
    list_per_page = 50


@admin.register(LotteryJoiner)
class LotteryJoinerAdmin(admin.ModelAdmin):
    list_display = ('user', 'lottery_type', 'charge', 'state')
    #list_display = ('user', 'lottery_type', 'charge', 'state', 'win', 'set_winner',)
    #list_editable = ('set_winner',)
    readonly_fields = ['win', "item_info"]
    list_filter = ('lottery_type', 'state', 'win')
    search_fields = ('user__username',)
    list_per_page = 50
    raw_id_fields = ("user", )
    fields = ('user', 'lottery_type', 'charge', 'state')

    actions = []



@admin.register(LotteryInfoSetting)
class LotteryInfoSettingAdmin(admin.ModelAdmin):
    fields = ('title', 'comment', 'lottery_type', 'enable')
    list_display = ('id', 'title', 'comment', 'lottery_type', 'enable')
    list_editable = ('enable', )
    list_per_page = 50