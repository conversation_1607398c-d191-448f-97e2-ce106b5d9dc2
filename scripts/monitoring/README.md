# Monitoring 监控脚本

本目录包含用于系统监控和状态检查的脚本。

## 脚本列表

### 房间监控

#### monitor_rooms.py
- **功能**：监控房间状态和运行情况
- **监控内容**：
  - 房间创建/销毁频率
  - 房间状态分布
  - 房间运行时长统计
  - 异常房间检测
- **运行方式**：
  ```bash
  cd /www/wwwroot/csgoskins.com.cn/server
  python ../scripts/monitoring/monitor_rooms.py
  ```
- **输出**：生成监控报告和告警信息

## 监控分类

### 1. 实时监控
- 系统状态实时检查
- 关键指标实时跟踪
- 异常情况即时告警

### 2. 定期监控
- 定时状态检查
- 趋势分析报告
- 性能统计汇总

### 3. 事件监控
- 特定事件跟踪
- 异常事件记录
- 事件影响分析

### 4. 业务监控
- 核心业务指标
- 用户行为分析
- 收入相关监控

## 监控指标

### 系统指标

#### 基础资源
- CPU使用率
- 内存使用率
- 磁盘空间使用
- 网络IO状态

#### 服务状态
- 数据库连接状态
- Redis连接状态
- Web服务响应状态
- 后台任务状态

### 业务指标

#### 用户相关
- 在线用户数量
- 用户活跃度
- 新用户注册量
- 用户留存率

#### 房间相关
- 活跃房间数量
- 房间平均时长
- 房间成功率
- 房间等待时间

#### 游戏相关
- 游戏完成率
- 平均游戏时长
- 游戏异常率
- 奖励发放状态

### 性能指标

#### 响应时间
- API平均响应时间
- 数据库查询时间
- 页面加载时间
- 静态资源加载时间

#### 错误率
- API错误率
- 系统异常率
- 用户操作失败率
- 数据同步失败率

## 监控配置

### 监控频率设置

```python
# monitoring/config.py
MONITORING_CONFIG = {
    'real_time': {
        'interval': 10,  # 秒
        'metrics': ['cpu', 'memory', 'active_users']
    },
    'periodic': {
        'interval': 300,  # 5分钟
        'metrics': ['room_stats', 'performance_stats']
    },
    'daily': {
        'interval': 86400,  # 24小时
        'metrics': ['user_stats', 'revenue_stats']
    }
}
```

### 告警阈值配置

```python
# monitoring/thresholds.py
ALERT_THRESHOLDS = {
    'cpu_usage': 80,        # CPU使用率超过80%
    'memory_usage': 85,     # 内存使用率超过85%
    'error_rate': 5,        # 错误率超过5%
    'response_time': 2000,  # 响应时间超过2秒
    'room_wait_time': 300,  # 房间等待时间超过5分钟
}
```

### 通知配置

```python
# monitoring/notifications.py
NOTIFICATION_CONFIG = {
    'email': {
        'enabled': True,
        'recipients': ['<EMAIL>', '<EMAIL>'],
        'severity_levels': ['critical', 'high']
    },
    'webhook': {
        'enabled': True,
        'url': 'https://hooks.slack.com/services/...',
        'severity_levels': ['critical', 'high', 'medium']
    },
    'sms': {
        'enabled': False,
        'recipients': ['+1234567890'],
        'severity_levels': ['critical']
    }
}
```

## 监控脚本运行

### 实时监控模式

```bash
# 启动实时监控
python ../scripts/monitoring/monitor_rooms.py --real-time

# 监控特定指标
python ../scripts/monitoring/monitor_rooms.py --metrics cpu,memory,rooms

# 输出到文件
python ../scripts/monitoring/monitor_rooms.py --output /tmp/monitor.log
```

### 定时任务模式

```bash
# 定时执行监控 (crontab)
*/5 * * * * cd /www/wwwroot/csgoskins.com.cn/server && python ../scripts/monitoring/monitor_rooms.py --check >> /var/log/steambase/monitor.log 2>&1

# 每小时性能报告
0 * * * * cd /www/wwwroot/csgoskins.com.cn/server && python ../scripts/monitoring/monitor_rooms.py --performance-report

# 每日统计报告
0 0 * * * cd /www/wwwroot/csgoskins.com.cn/server && python ../scripts/monitoring/monitor_rooms.py --daily-report
```

### 告警模式

```bash
# 启动告警监控
python ../scripts/monitoring/monitor_rooms.py --alert-mode

# 自定义告警阈值
python ../scripts/monitoring/monitor_rooms.py --alert-mode --cpu-threshold 90 --memory-threshold 90
```

## 监控报告

### 实时监控输出

```
========================================
系统实时监控 - 2025-06-28 14:30:00
========================================

系统资源:
- CPU使用率: 45.2% ✓
- 内存使用率: 62.8% ✓
- 磁盘使用率: 38.5% ✓
- 网络IO: 1.2MB/s ✓

服务状态:
- 数据库: 正常 ✓
- Redis: 正常 ✓
- Web服务: 正常 ✓
- 队列处理: 正常 ✓

业务指标:
- 在线用户: 1,245
- 活跃房间: 86
- 平均响应时间: 156ms ✓
- 错误率: 0.2% ✓

告警状态:
- 无当前告警 ✓

========================================
```

### 异常告警输出

```
🚨 CRITICAL ALERT 🚨
时间: 2025-06-28 14:35:22
类型: 性能异常

问题描述:
- API响应时间异常: 3.2秒 (阈值: 2.0秒)
- 数据库连接池耗尽: 95% (阈值: 90%)
- 错误率飙升: 8.5% (阈值: 5%)

影响范围:
- 用户登录受影响
- 房间创建延迟
- 部分API不可用

建议操作:
1. 立即检查数据库性能
2. 重启应用服务器
3. 扩容数据库连接池
4. 检查近期代码变更

联系人: <EMAIL>, <EMAIL>
```

### 统计报告

```
========================================
每日监控统计报告 - 2025-06-28
========================================

用户活动:
- 总访问用户: 5,432
- 新注册用户: 234
- 活跃用户率: 78.5%
- 平均在线时长: 2小时15分钟

房间统计:
- 创建房间总数: 1,876
- 完成游戏数: 1,652
- 游戏完成率: 88.1%
- 平均房间时长: 8分钟30秒

性能表现:
- 平均响应时间: 178ms
- 99%响应时间: 850ms
- 系统可用性: 99.8%
- 错误率: 0.3%

资源使用:
- 平均CPU使用: 52.3%
- 平均内存使用: 68.7%
- 峰值并发: 1,580
- 数据传输量: 45.2GB

异常统计:
- 总告警次数: 3
- 严重告警: 0
- 中级告警: 2
- 低级告警: 1

趋势分析:
📈 用户活跃度上升 (+5.2%)
📈 游戏完成率提升 (+2.1%)
📉 平均响应时间改善 (-12ms)
➡️ 系统稳定性保持良好

========================================
```

## 监控脚本开发

### 监控脚本模板

```python
#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
监控脚本：[监控功能描述]

监控指标：[具体监控指标]
告警策略：[告警触发条件]

作者：[作者名]
创建时间：[创建日期]
"""

import os
import sys
import django
import time
import json
import logging
from datetime import datetime, timedelta

# Django环境设置
sys.path.append('./')
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "steambase.settings")
django.setup()

class SystemMonitor:
    """系统监控基类"""
    
    def __init__(self):
        self.setup_logging()
        self.load_config()
        self.load_thresholds()
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def load_config(self):
        """加载配置"""
        # 加载监控配置
        pass
    
    def load_thresholds(self):
        """加载阈值配置"""
        # 加载告警阈值
        pass
    
    def collect_metrics(self):
        """收集监控指标"""
        metrics = {}
        
        # 收集系统指标
        metrics['system'] = self.collect_system_metrics()
        
        # 收集业务指标
        metrics['business'] = self.collect_business_metrics()
        
        # 收集性能指标
        metrics['performance'] = self.collect_performance_metrics()
        
        return metrics
    
    def collect_system_metrics(self):
        """收集系统指标"""
        import psutil
        
        return {
            'cpu_percent': psutil.cpu_percent(),
            'memory_percent': psutil.virtual_memory().percent,
            'disk_percent': psutil.disk_usage('/').percent,
            'network_io': psutil.net_io_counters()._asdict()
        }
    
    def collect_business_metrics(self):
        """收集业务指标"""
        # 实现业务指标收集
        pass
    
    def collect_performance_metrics(self):
        """收集性能指标"""
        # 实现性能指标收集
        pass
    
    def check_thresholds(self, metrics):
        """检查阈值并生成告警"""
        alerts = []
        
        for metric_name, value in metrics.items():
            if self.is_threshold_exceeded(metric_name, value):
                alert = self.create_alert(metric_name, value)
                alerts.append(alert)
        
        return alerts
    
    def is_threshold_exceeded(self, metric_name, value):
        """检查是否超过阈值"""
        # 实现阈值检查逻辑
        pass
    
    def create_alert(self, metric_name, value):
        """创建告警"""
        return {
            'timestamp': datetime.now(),
            'metric': metric_name,
            'value': value,
            'severity': self.get_severity(metric_name, value),
            'message': f"{metric_name} exceeded threshold: {value}"
        }
    
    def send_notifications(self, alerts):
        """发送通知"""
        for alert in alerts:
            if alert['severity'] in ['critical', 'high']:
                self.send_email_notification(alert)
            
            if alert['severity'] == 'critical':
                self.send_webhook_notification(alert)
    
    def generate_report(self, metrics, alerts):
        """生成监控报告"""
        report = {
            'timestamp': datetime.now(),
            'metrics': metrics,
            'alerts': alerts,
            'summary': self.generate_summary(metrics, alerts)
        }
        
        return report
    
    def run_monitoring(self):
        """运行监控"""
        try:
            # 收集指标
            metrics = self.collect_metrics()
            
            # 检查告警
            alerts = self.check_thresholds(metrics)
            
            # 发送通知
            if alerts:
                self.send_notifications(alerts)
            
            # 生成报告
            report = self.generate_report(metrics, alerts)
            
            # 输出报告
            self.output_report(report)
            
        except Exception as e:
            self.logger.error(f"监控执行失败: {e}")

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='系统监控')
    parser.add_argument('--real-time', action='store_true', help='实时监控模式')
    parser.add_argument('--interval', type=int, default=60, help='监控间隔（秒）')
    parser.add_argument('--output', help='输出文件')
    
    args = parser.parse_args()
    
    monitor = SystemMonitor()
    
    if args.real_time:
        # 实时监控模式
        while True:
            monitor.run_monitoring()
            time.sleep(args.interval)
    else:
        # 单次监控
        monitor.run_monitoring()

if __name__ == '__main__':
    main()
```

## 高级监控功能

### 1. 预测性监控
```python
def predict_issues(historical_data):
    """基于历史数据预测潜在问题"""
    # 实现趋势分析
    # 预测资源需求
    # 识别异常模式
```

### 2. 自动化响应
```python
def auto_response(alert):
    """自动响应告警"""
    if alert['type'] == 'high_memory':
        # 自动清理缓存
        clear_cache()
    elif alert['type'] == 'slow_response':
        # 自动重启服务
        restart_service()
```

### 3. 智能告警
```python
def intelligent_alerting(metrics, context):
    """智能告警，减少误报"""
    # 考虑历史趋势
    # 分析上下文信息
    # 动态调整阈值
```

## 监控最佳实践

### 1. 监控策略
- 分层监控：系统→应用→业务
- 关键路径监控
- 用户体验监控
- 安全监控

### 2. 告警管理
- 合理设置阈值
- 分级告警机制
- 避免告警疲劳
- 及时响应处理

### 3. 数据管理
- 数据保留策略
- 数据压缩存储
- 数据隐私保护
- 数据备份恢复

### 4. 持续改进
- 定期评估监控效果
- 优化监控指标
- 更新告警策略
- 培训监控技能

最后更新：2025-06-28
