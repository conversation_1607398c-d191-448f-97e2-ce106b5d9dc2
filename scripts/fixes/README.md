# Fixes 修复脚本

本目录包含用于修复数据库数据和系统状态的脚本。

## 脚本列表

### 房间状态修复

#### fix_stuck_rooms.py
- **功能**：修复卡住的房间状态
- **问题描述**：某些房间可能因为网络异常、系统重启等原因导致状态异常，无法正常进行
- **修复方案**：重置房间状态，重新分配资源
- **运行方式**：
  ```bash
  cd /www/wwwroot/csgoskins.com.cn/server
  python ../scripts/fixes/fix_stuck_rooms.py
  ```
- **风险等级**：中等 - 会修改房间状态数据

#### fix_running_rooms.py
- **功能**：修复运行中的房间状态异常
- **问题描述**：房间显示运行中但实际已结束，或者房间数据不一致
- **修复方案**：检查房间实际状态，更新数据库记录
- **运行方式**：
  ```bash
  cd /www/wwwroot/csgoskins.com.cn/server
  python ../scripts/fixes/fix_running_rooms.py
  ```
- **风险等级**：中等 - 涉及活跃房间数据

#### fix_full_rooms.py
- **功能**：修复满员房间状态问题
- **问题描述**：房间显示满员但实际人数不足，或者无法正常开始游戏
- **修复方案**：重新计算房间人数，更新房间状态
- **运行方式**：
  ```bash
  cd /www/wwwroot/csgoskins.com.cn/server
  python ../scripts/fixes/fix_full_rooms.py
  ```
- **风险等级**：低 - 主要是状态同步

#### fix_rooms_with_no_rounds.py
- **功能**：修复没有回合记录的房间
- **问题描述**：房间已创建但缺少回合数据，导致无法正常进行游戏
- **修复方案**：为房间创建初始回合记录
- **运行方式**：
  ```bash
  cd /www/wwwroot/csgoskins.com.cn/server
  python ../scripts/fixes/fix_rooms_with_no_rounds.py
  ```
- **风险等级**：中等 - 会创建新的游戏数据

#### fix_team_battle_rooms.py
- **功能**：修复团队对战房间的特殊问题
- **问题描述**：团队对战房间的队伍分配、积分计算等异常
- **修复方案**：重新计算队伍状态和积分
- **运行方式**：
  ```bash
  cd /www/wwwroot/csgoskins.com.cn/server
  python ../scripts/fixes/fix_team_battle_rooms.py
  ```
- **风险等级**：高 - 涉及用户积分和游戏结果

## 执行规范

### 执行前必做检查

1. **数据备份**
   ```bash
   # 备份相关数据表
   mysqldump -u user -p database_name table_name > backup_$(date +%Y%m%d_%H%M%S).sql
   ```

2. **系统状态检查**
   - 确认当前没有大量活跃用户
   - 检查服务器负载情况
   - 确认数据库连接稳定

3. **影响评估**
   - 评估脚本执行时间
   - 确认受影响的用户范围
   - 准备回滚方案

### 执行顺序建议

对于多个房间修复脚本，建议按以下顺序执行：

1. `fix_rooms_with_no_rounds.py` - 修复基础数据
2. `fix_stuck_rooms.py` - 修复卡住状态
3. `fix_running_rooms.py` - 修复运行状态
4. `fix_full_rooms.py` - 修复满员状态
5. `fix_team_battle_rooms.py` - 修复团队对战（如果需要）

### 安全执行模式

所有修复脚本都支持以下安全模式：

#### 1. 预览模式（Dry Run）
```bash
python ../scripts/fixes/fix_stuck_rooms.py --dry-run
```
- 只检查和显示问题，不实际修改数据
- 用于评估修复范围和影响

#### 2. 限制模式
```bash
python ../scripts/fixes/fix_stuck_rooms.py --limit 10
```
- 限制一次处理的记录数量
- 用于分批处理，降低风险

#### 3. 详细日志模式
```bash
python ../scripts/fixes/fix_stuck_rooms.py --verbose > fix_log_$(date +%Y%m%d_%H%M%S).log 2>&1
```
- 输出详细执行日志
- 便于问题排查和审计

## 监控和验证

### 执行监控

1. **实时监控**
   ```bash
   # 监控脚本执行
   tail -f fix_log_*.log
   
   # 监控数据库连接
   mysqladmin processlist
   
   # 监控系统资源
   htop
   ```

2. **进度跟踪**
   - 记录开始时间和预期结束时间
   - 监控处理记录数量
   - 观察系统性能指标

### 结果验证

1. **数据一致性检查**
   ```sql
   -- 检查房间状态分布
   SELECT status, COUNT(*) FROM box_caseroom GROUP BY status;
   
   -- 检查回合数据完整性
   SELECT COUNT(*) FROM box_caseroomround WHERE room_id NOT IN (SELECT id FROM box_caseroom);
   ```

2. **功能验证**
   - 测试房间创建功能
   - 验证游戏流程正常
   - 检查用户界面显示

3. **性能验证**
   - 检查API响应时间
   - 验证数据库查询性能
   - 监控内存使用情况

## 故障回滚

### 数据回滚

```bash
# 恢复数据备份
mysql -u user -p database_name < backup_20250628_143000.sql
```

### 状态回滚

```bash
# 重启相关服务
sudo systemctl restart steambase
sudo systemctl restart redis
sudo systemctl restart nginx
```

### 缓存清理

```bash
# 清理Redis缓存
redis-cli FLUSHDB

# 重启应用服务
sudo supervisorctl restart all
```

## 报告和文档

### 执行报告

每次修复执行后，应创建执行报告，包含：

1. **执行概要**
   - 执行时间
   - 处理记录数量
   - 修复成功/失败统计

2. **问题分析**
   - 问题根本原因
   - 影响范围评估
   - 预防措施建议

3. **验证结果**
   - 功能验证状态
   - 性能影响评估
   - 用户反馈情况

### 文档更新

根据修复经验更新：
- 问题处理手册
- 监控告警规则
- 预防措施文档

## 联系信息

如遇到修复脚本相关问题：
1. 立即停止脚本执行
2. 保留执行日志
3. 联系技术负责人
4. 准备数据回滚

最后更新：2025-06-28
