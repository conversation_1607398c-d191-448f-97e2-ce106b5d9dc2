# steambase/settings.py
import json

import environ
import logging
import os
from celery.schedules import crontab
 


from django.core.serializers.json import DjangoJSONEncoder

from .base_settings import *

# BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# def project_root(*paths):
#     return os.path.join(BASE_DIR, *paths)

current = environ.Path(__file__)
project_root = current - 2  # three folder back (/a/b/c/ - 3 = /)

env = environ.Env(
    DEBUG=(bool, False),
    SECRET_KEY=(str, SECRET_KEY),
    ALLOWED_HOSTS=(list, ['localhost', '127.0.0.1']),
    ENABLE_LOG_CONFIG_FILE=(bool, False),
    USER_POINT_MAX=(float, 2000),
    JACKPOT_GAME_COUNTDOWN=(int, 20),
    JACKPOT_GAME_WAIT=(int, 10),
    JACKPOT_BET_AMOUNT_MAX=(int, 1000),
    DOUBLE_GAME_COUNTDOWN=(int, 20),
    DOUBLE_GAME_WAIT=(int, 10),
    DOUBLE_BET_AMOUNT_MAX=(int, 1000),
    ROLL_ROOM_CREATE_MAX=(int, 3),
    CASE_ROOM_CREATE_MAX=(int, 3),
    DEPOSIT_ORIGIN_PRICE_MIN=(float, 1),
    DEPOSIT_EXCHANGE_AUTO=(bool, False),
    PACKAGE_EXCHANGE_AUTO=(bool, False),
    PACKAGE_EXCHANGE_DELAY=(int, 60),
    ITEM_PRICE_CURRENCY=(str, 'CNY'),
    PACKAGE_ITEM_PRICE_MIN=(float, 1),
    PACKAGE_ITEM_PRICE_RATE=(float, 1),
    SHOP_ITEM_PRICE_RATE=(float, 1),
    BOX_ITEM_PRICE_RATE=(float, 1),
    CHARGE_PAY_AMOUNT_MIN=(float, 1),
    CHARGE_PAY_AMOUNT_MAX=(float, 200000),
    PROMOTION_PROFIT_PICK_UNIT=(float, 1),
    PROMOTION_PROFIT_PICK_IMMEDIATELY=(bool, False),
    PROMOTION_NEW_USER_REWARD=(float, 0),
    TRADE_TEST=(bool, True),
    CHARGE_TEST=(bool, True),
    CHAT_MESSAGE_LAST_COUNT=(int, 10),
    WAXPEER_API_KEY=(str, ''),
    EXPRESS_API_KEY=(str, ''),
    EXPRESS_MERCHANT_NO=(str, ''),

    ZBT_APP_KEY=(str, ''),
    ZBT_APP_SECRET=(str, ''),

    AWS_ACCESS_KEY_ID=(str, ''),
    AWS_SECRET_ACCESS_KEY=(str, ''),
    AWS_SMS_REGION_NAME=(str, ''),

    ALISMS_ACCESS_KEY_ID=(str, ''),
    ALISMS_ACCESS_KEY_SECRET=(str, ''),
    ALISMS_SIGN_NAME=(str, ''),
    ALISMS_TEMPLATE_CODE=(str, ''),

    QYB_USERNAME=(str, ''),
    QYB_PASSWORD=(str, ''),
    LOK_USERNAME=(str, ''),
    LOK_PASSWORD=(str, ''),
    LOK_TOKEN=(str, ''),
    LOK_TEMPLATE=(str, ''),
    WEIXIN_SMS_APPKEY=(str, ''),
    WEIXIN_SMS_SECRET=(str, ''),

    CXKA_API_KEY=(str, ''),
    CXKA_API_SECRET=(str, ''),
    CXKA_DOMAIN=(str, ''),
    CXKA_CALLBACK_URL=(str, ''),

    HUPI_WEXIN_PAY_APPID=(str, ''),
    HUPI_WEXIN_PAY_SECRET_KEY=(str, ''),
    HUPI_ALIPAY_APPID=(str, ''),
    HUPI_ALIPAY_SECRET_KEY=(str, ''),
    HUPIJIAO_NOTIFY_URL=(str, ''),
    HUPIJIAO_RETURN_URL=(str, ''),
    HUPIJIAO_CALLBACK_URL=(str, ''),

    JIUJIA_PAY_ID=(str, ''),
    JIUJIA_PAY_APPID=(str, ''),
    JIUJIA_PAY_SECRET_KEY=(str, ''),   
    JIUJIA_PAY_NOTIFY_URL=(str, ''),
    JIUJIA_PAY_RETURN_URL=(str, ''),
    JIUJIA_PAY_CALLBACK_URL=(str, ''), 

    FUXINKA_PAY_APPID=(str, ''),
    FUXINKA_PAY_SECRET_KEY=(str, ''),   
    FUXINKA_PAY_NOTIFY_URL=(str, ''),
    FUXINKA_PAY_RETURN_URL=(str, ''),
    FUXINKA_PAY_CALLBACK_URL=(str, ''), 

    UMI_PAY_APPID=(str, ''),
    UMI_PAY_SECRET_KEY=(str, ''),   
    UMI_PAY_NOTIFY_URL=(str, ''),
    UMI_PAY_RETURN_URL=(str, ''),
    UMI_PAY_CALLBACK_URL=(str, ''), 

    NANSHANKA_PAY_APPID=(str, ''),
    NANSHANKA_PAY_SECRET_KEY=(str, ''),   
    NANSHANKA_PAY_NOTIFY_URL=(str, ''),
    NANSHANKA_PAY_RETURN_URL=(str, ''),
    NANSHANKA_PAY_CALLBACK_URL=(str, ''), 

    HELLOKA_PAY_ID=(str, ''),
    HELLOKA_PAY_KEY=(str, ''),
    HELLOKA_PAY_SECRET=(str, ''),   
    HELLOKA_PAY_NOTIFY_URL=(str, ''),
    HELLOKA_PAY_RETURN_URL=(str, ''),
    HELLOKA_PAY_CALLBACK_URL=(str, ''), 

    ALIPAY_APP_ID=(str, ''),
    ALIPAY_NOTIFY_URL=(str, ''),
    ALI_PAY_URL=(str, ''),

    # 太一生水支付宝支付配置
    TAIYISHENGSHUI_ALIPAY_APPID=(str, ''),
    TAIYISHENGSHUI_ALIPAY_NOTIFY_URL=(str, ''),
    TAIYISHENGSHUI_ALIPAY_URL=(str, ''),

    # 月定山空支付宝支付配置
    YUEDINGSHANKE_ALIPAY_APPID=(str, ''),
    YUEDINGSHANKE_ALIPAY_NOTIFY_URL=(str, ''),
    YUEDINGSHANKE_ALIPAY_URL=(str, ''),

    # 凌洋宏森支付宝支付配置
    LYHS_ALIPAY_APPID=(str, ''),
    LYHS_ALIPAY_NOTIFY_URL=(str, ''),
    LYHS_ALIPAY_URL=(str, ''),

    # 昕运泰支付宝支付配置
    XYT_ALIPAY_APPID=(str, ''),
    XYT_ALIPAY_NOTIFY_URL=(str, ''),
    XYT_ALIPAY_URL=(str, ''),

    # 饰动未来支付宝支付配置
    SDWL_ALIPAY_APPID=(str, ''),
    SDWL_ALIPAY_NOTIFY_URL=(str, ''),
    SDWL_ALIPAY_URL=(str, ''),

    # 网饰星河支付宝支付配置
    WSXH_ALIPAY_APPID=(str, ''),
    WSXH_ALIPAY_NOTIFY_URL=(str, ''),
    WSXH_ALIPAY_URL=(str, ''),

    # 幻游饰界支付宝支付配置
    HYSJ_ALIPAY_APPID=(str, ''),
    HYSJ_ALIPAY_NOTIFY_URL=(str, ''),
    HYSJ_ALIPAY_URL=(str, ''),

    # 游饰云联支付宝支付配置
    YSYL_ALIPAY_APPID=(str, ''),
    YSYL_ALIPAY_NOTIFY_URL=(str, ''),
    YSYL_ALIPAY_URL=(str, ''),

    # SahePay
    SAHEPAY_CUSTOMER_ID=(str, ''),
    SAHEPAY_KEY=(str, ''),
    SAHEPAY_NOTIFY_URL=(str, ''),
    SAHEPAY_RETURN_URL=(str, ''),




    WEXIN_PAY_APPID=(str, ''),
    WEXIN_PAY_MCH_ID=(str, ''),
    WEXIN_PAY_API_KEY=(str, ''),
    WEXIN_PAY_NOTIFY_URL=(str, ''),

    CUSTOMBOX_MIN_ITEMS=(int, 2),

    SMTP_SERVER=(str, ''),
    SENDER_EMAIL=(str, ''),
    SMTP_TOKEN=(str, ''),
    SMTP_PORT=(str, ''),

    ALIYUN_OSS_BUCKET_NAME=(str, ''),
    ALIYUN_OSS_ACCESS_KEY_ID=(str, ''),
    ALIYUN_OSS_ACCESS_KEY_SECRET=(str, ''),
    ALIYUN_OSS_ENDPOINT=(str, ''),



)  # set default values and casting

env_file = project_root('.env')
env.read_env(env_file)

SECRET_KEY = env('SECRET_KEY')

DEBUG = env('DEBUG')  # False if not in os.environ

ALLOWED_HOSTS = env('ALLOWED_HOSTS')

DATABASES = {
    'default': env.db(default='mysql://root:@127.0.0.1:3306/csgogo')
}

# 设置媒体文件和静态文件目录

MEDIA_ROOT = project_root('media')
STATIC_ROOT = project_root('static')




LOCALE_PATHS = (
    project_root('locale'),
)

CACHES = {
    'default': env.cache('REDIS_URL', default='rediscache://127.0.0.1:6379/0')
}
USER_POINT_MAX = env('USER_POINT_MAX')

JACKPOT_GAME_COUNTDOWN = env('JACKPOT_GAME_COUNTDOWN')
JACKPOT_GAME_WAIT = env('JACKPOT_GAME_WAIT')
JACKPOT_BET_AMOUNT_MAX = env('JACKPOT_BET_AMOUNT_MAX')

DOUBLE_GAME_COUNTDOWN = env('DOUBLE_GAME_COUNTDOWN')
DOUBLE_GAME_WAIT = env('DOUBLE_GAME_WAIT')
DOUBLE_BET_AMOUNT_MAX = env('DOUBLE_BET_AMOUNT_MAX')

ROLL_ROOM_CREATE_MAX = env('ROLL_ROOM_CREATE_MAX')
CASE_ROOM_CREATE_MAX = env('CASE_ROOM_CREATE_MAX')

DEPOSIT_ORIGIN_PRICE_MIN = env('DEPOSIT_ORIGIN_PRICE_MIN')
DEPOSIT_EXCHANGE_AUTO = env('DEPOSIT_EXCHANGE_AUTO')
PACKAGE_EXCHANGE_AUTO = env('PACKAGE_EXCHANGE_AUTO')
PACKAGE_EXCHANGE_DELAY = env('PACKAGE_EXCHANGE_DELAY')

ITEM_PRICE_CURRENCY = env('ITEM_PRICE_CURRENCY')
PACKAGE_ITEM_PRICE_MIN = env('PACKAGE_ITEM_PRICE_MIN')
PACKAGE_ITEM_PRICE_RATE = env('PACKAGE_ITEM_PRICE_RATE')
SHOP_ITEM_PRICE_RATE = env('SHOP_ITEM_PRICE_RATE')
BOX_ITEM_PRICE_RATE = env('BOX_ITEM_PRICE_RATE')

CHARGE_PAY_AMOUNT_MIN = env('CHARGE_PAY_AMOUNT_MIN')
CHARGE_PAY_AMOUNT_MAX = env('CHARGE_PAY_AMOUNT_MAX')

PROMOTION_PROFIT_PICK_UNIT = env('PROMOTION_PROFIT_PICK_UNIT')
PROMOTION_PROFIT_PICK_IMMEDIATELY = env('PROMOTION_PROFIT_PICK_IMMEDIATELY')
PROMOTION_NEW_USER_REWARD = env('PROMOTION_NEW_USER_REWARD')

TRADE_TEST = env('TRADE_TEST')
CHARGE_TEST = env('CHARGE_TEST')

CHAT_MESSAGE_LAST_COUNT = env('CHAT_MESSAGE_LAST_COUNT')

WAXPEER_API_KEY = env('WAXPEER_API_KEY')

EXPRESS_API_KEY = env('EXPRESS_API_KEY')
EXPRESS_MERCHANT_NO = env('EXPRESS_MERCHANT_NO')

# 扎比特
ZBT_APP_KEY = env('ZBT_APP_KEY')
ZBT_APP_SECRET = env('ZBT_APP_SECRET')
# 乐讯通短信
LOK_USERNAME = env('LOK_USERNAME')
LOK_PASSWORD = env('LOK_PASSWORD')
LOK_TOKEN = env('LOK_TOKEN')
LOK_TEMPLATE = env('LOK_TEMPLATE')
# 亚马逊短信
AWS_ACCESS_KEY_ID = env('AWS_ACCESS_KEY_ID')
AWS_SECRET_ACCESS_KEY = env('AWS_SECRET_ACCESS_KEY')
AWS_SMS_REGION_NAME = env('AWS_SMS_REGION_NAME')

# 企业宝短信
QYB_USERNAME = env('QYB_USERNAME')
QYB_PASSWORD = env('QYB_PASSWORD')

# 唯鑫短信
WEIXIN_SMS_APPKEY = env('WEIXIN_SMS_APPKEY')
WEIXIN_SMS_SECRET = env('WEIXIN_SMS_SECRET')

# 阿里云短信
ALISMS_ACCESS_KEY_ID = env('ALISMS_ACCESS_KEY_ID')
ALISMS_ACCESS_KEY_SECRET = env('ALISMS_ACCESS_KEY_SECRET')
ALISMS_SIGN_NAME = env('ALISMS_SIGN_NAME')
ALISMS_TEMPLATE_CODE = env('ALISMS_TEMPLATE_CODE')

# 虎皮椒支付
HUPI_WEXIN_PAY_APPID = env('HUPI_WEXIN_PAY_APPID')
HUPI_WEXIN_PAY_SECRET_KEY = env('HUPI_WEXIN_PAY_SECRET_KEY')
HUPI_ALIPAY_APPID = env('HUPI_ALIPAY_APPID')
HUPI_ALIPAY_SECRET_KEY = env('HUPI_ALIPAY_SECRET_KEY')
HUPIJIAO_NOTIFY_URL = env('HUPIJIAO_NOTIFY_URL')
HUPIJIAO_RETURN_URL = env('HUPIJIAO_RETURN_URL')
HUPIJIAO_CALLBACK_URL = env('HUPIJIAO_CALLBACK_URL')

# 九嘉支付
JIUJIA_PAY_ID = env('JIUJIA_PAY_ID')
JIUJIA_PAY_APPID = env('JIUJIA_PAY_APPID')
JIUJIA_PAY_SECRET_KEY = env('JIUJIA_PAY_SECRET_KEY')
JIUJIA_PAY_NOTIFY_URL = env('JIUJIA_PAY_NOTIFY_URL')
JIUJIA_PAY_RETURN_URL = env('JIUJIA_PAY_RETURN_URL')
JIUJIA_PAY_CALLBACK_URL = env('JIUJIA_PAY_CALLBACK_URL')

# 富信
FUXINKA_PAY_APPID = env('FUXINKA_PAY_APPID')
FUXINKA_PAY_SECRET_KEY = env('FUXINKA_PAY_SECRET_KEY')
FUXINKA_PAY_NOTIFY_URL = env('FUXINKA_PAY_NOTIFY_URL')
FUXINKA_PAY_RETURN_URL = env('FUXINKA_PAY_RETURN_URL')
FUXINKA_PAY_CALLBACK_URL = env('FUXINKA_PAY_CALLBACK_URL')

# 优米
UMI_PAY_APPID = env('UMI_PAY_APPID')
UMI_PAY_SECRET_KEY = env('UMI_PAY_SECRET_KEY')
UMI_PAY_NOTIFY_URL = env('UMI_PAY_NOTIFY_URL')
UMI_PAY_RETURN_URL = env('UMI_PAY_RETURN_URL')
UMI_PAY_CALLBACK_URL = env('UMI_PAY_CALLBACK_URL')

# 南山卡
NANSHANKA_PAY_APPID = env('NANSHANKA_PAY_APPID')
NANSHANKA_PAY_SECRET_KEY = env('NANSHANKA_PAY_SECRET_KEY')
NANSHANKA_PAY_NOTIFY_URL = env('NANSHANKA_PAY_NOTIFY_URL')
NANSHANKA_PAY_RETURN_URL = env('NANSHANKA_PAY_RETURN_URL')
NANSHANKA_PAY_CALLBACK_URL = env('NANSHANKA_PAY_CALLBACK_URL')

# helloka
HELLOKA_PAY_ID = env('HELLOKA_PAY_ID')
HELLOKA_PAY_KEY = env('HELLOKA_PAY_KEY')
HELLOKA_PAY_SECRET = env('HELLOKA_PAY_SECRET')
HELLOKA_PAY_NOTIFY_URL = env('HELLOKA_PAY_NOTIFY_URL')
HELLOKA_PAY_RETURN_URL = env('HELLOKA_PAY_RETURN_URL')
HELLOKA_PAY_CALLBACK_URL = env('HELLOKA_PAY_CALLBACK_URL')

# 畅想发卡支付
CXKA_API_KEY = env('CXKA_API_KEY')
CXKA_API_SECRET = env('CXKA_API_SECRET')
CXKA_DOMAIN = env('CXKA_DOMAIN')
CXKA_CALLBACK_URL = env('CXKA_CALLBACK_URL')

# 支付宝支付
ALIPAY_APP_ID = env('ALIPAY_APP_ID')
ALIPAY_NOTIFY_URL = env('ALIPAY_NOTIFY_URL')
ALI_PAY_URL = env('ALI_PAY_URL')

# 太一生水支付宝支付配置
TAIYISHENGSHUI_ALIPAY_APPID = env('TAIYISHENGSHUI_ALIPAY_APPID')
TAIYISHENGSHUI_ALIPAY_NOTIFY_URL = env('TAIYISHENGSHUI_ALIPAY_NOTIFY_URL')
TAIYISHENGSHUI_ALIPAY_URL = env('TAIYISHENGSHUI_ALIPAY_URL')

# 月定山空支付宝支付配置
YUEDINGSHANKE_ALIPAY_APPID = env('YUEDINGSHANKE_ALIPAY_APPID')
YUEDINGSHANKE_ALIPAY_NOTIFY_URL = env('YUEDINGSHANKE_ALIPAY_NOTIFY_URL')
YUEDINGSHANKE_ALIPAY_URL = env('YUEDINGSHANKE_ALIPAY_URL')

# 凌洋宏森支付宝支付配置
LYHS_ALIPAY_APPID = env('LYHS_ALIPAY_APPID')
LYHS_ALIPAY_NOTIFY_URL = env('LYHS_ALIPAY_NOTIFY_URL')
LYHS_ALIPAY_URL = env('LYHS_ALIPAY_URL')

# 昕运泰支付宝支付配置
XYT_ALIPAY_APPID = env('XYT_ALIPAY_APPID')
XYT_ALIPAY_NOTIFY_URL = env('XYT_ALIPAY_NOTIFY_URL')
XYT_ALIPAY_URL = env('XYT_ALIPAY_URL')

# 网饰星河支付宝支付配置
WSXH_ALIPAY_APPID = env('WSXH_ALIPAY_APPID')
WSXH_ALIPAY_NOTIFY_URL = env('WSXH_ALIPAY_NOTIFY_URL')
WSXH_ALIPAY_URL = env('WSXH_ALIPAY_URL')

# 饰动未来支付宝支付配置
SDWL_ALIPAY_APPID = env('SDWL_ALIPAY_APPID')
SDWL_ALIPAY_NOTIFY_URL = env('SDWL_ALIPAY_NOTIFY_URL')
SDWL_ALIPAY_URL = env('SDWL_ALIPAY_URL')

# 幻游饰界支付宝支付配置
HYSJ_ALIPAY_APPID = env('HYSJ_ALIPAY_APPID')
HYSJ_ALIPAY_NOTIFY_URL = env('HYSJ_ALIPAY_NOTIFY_URL')
HYSJ_ALIPAY_URL = env('HYSJ_ALIPAY_URL')

# 游饰云联支付宝支付配置
YSYL_ALIPAY_APPID = env('YSYL_ALIPAY_APPID')
YSYL_ALIPAY_NOTIFY_URL = env('YSYL_ALIPAY_NOTIFY_URL')
YSYL_ALIPAY_URL = env('YSYL_ALIPAY_URL')

# SahePay
SAHEPAY_CUSTOMER_ID = env('SAHEPAY_CUSTOMER_ID')
SAHEPAY_KEY = env('SAHEPAY_KEY')
SAHEPAY_NOTIFY_URL = env('SAHEPAY_NOTIFY_URL')
SAHEPAY_RETURN_URL = env('SAHEPAY_RETURN_URL')

# 微信支付
WEXIN_PAY_APPID = env('WEXIN_PAY_APPID')
WEXIN_PAY_MCH_ID = env('WEXIN_PAY_MCH_ID')
WEXIN_PAY_API_KEY = env('WEXIN_PAY_API_KEY')
WEXIN_PAY_NOTIFY_URL = env('WEXIN_PAY_NOTIFY_URL')

# 自定义箱子最小值配置
CUSTOMBOX_MIN_ITEMS = env("CUSTOMBOX_MIN_ITEMS")

# 邮件服务
SMTP_SERVER = env("SMTP_SERVER")
SENDER_EMAIL = env("SENDER_EMAIL")
SMTP_TOKEN = env("SMTP_TOKEN")
SMTP_PORT = env("SMTP_PORT")

#ALIYUN OSS
ALIYUN_OSS_BUCKET_NAME = env('ALIYUN_OSS_BUCKET_NAME')
ALIYUN_OSS_ACCESS_KEY_ID = env('ALIYUN_OSS_ACCESS_KEY_ID')
ALIYUN_OSS_ACCESS_KEY_SECRET = env('ALIYUN_OSS_ACCESS_KEY_SECRET')
ALIYUN_OSS_ENDPOINT = env('ALIYUN_OSS_ENDPOINT')

# 注册app限制访问  'withdraw', 'promotion', 
INSTALLED_APPS_STINT = ['custombox', 'market', 'grab', 'tradeup', 'crash', 'blindbox', 'admin', 'luckybox']

if env('ENABLE_LOG_CONFIG_FILE'):
    LOGGING_CONFIG = 'logging.config.fileConfig'
    LOGGING = project_root(env('LOG_CONFIG_FILE_PATH'))
else:
    LOGGING_CONFIG = 'logging.config.dictConfig'
    logging.basicConfig(level=logging.DEBUG)

# Celery 配置
CELERY_BROKER_URL = 'redis://localhost:6379/0'
CELERY_RESULT_BACKEND = 'redis://localhost:6379/0'

# 使用 DatabaseScheduler 替代文件存储
CELERY_BEAT_SCHEDULER = 'django_celery_beat.schedulers:DatabaseScheduler'

# Celery Beat 配置 - 迁移自thworker
CELERY_BEAT_SCHEDULE = {
    # 原有任务
    'cleanup-duplicate-roll-room-bets': {
        'task': 'roll.tasks.cleanup_duplicate_roll_room_bets',
        'schedule': 300.0,  # 每5分钟执行一次
    },

    # 从thworker迁移的任务
    'update-case-records-cache': {
        'task': 'thworker.update_case_records_cache',
        'schedule': 300.0,  # 每5分钟执行一次
        'kwargs': {'count': 100}
    },
    'check-b2c-trade-state': {
        'task': 'thworker.check_b2c_trade_state',
        'schedule': 300.0,  # 每5分钟执行一次
    },
    'sync-items-price': {
        'task': 'thworker.sync_items_price',
        'schedule': crontab(hour=3, minute=10),  # 每天凌晨3:10
    },
    'hourly-lottery': {
        'task': 'thworker.hourly_lottery',
        'schedule': crontab(minute=0),  # 每小时整点
    },
    'daily-lottery': {
        'task': 'thworker.daily_lottery',
        'schedule': crontab(hour=0, minute=0),  # 每天0点
    },
    'weekly-lottery': {
        'task': 'thworker.weekly_lottery',
        'schedule': crontab(hour=0, minute=0, day_of_week=1),  # 每周一0点
    },

    # 系统维护任务
    'update-new-user-recharge-limit-100': {
        'task': 'sitecfg.tasks.update_new_user_recharge_limit',
        'schedule': crontab(hour=0, minute=0),  # 每天0点
        'kwargs': {'limit': 100}
    },
    'update-new-user-recharge-limit-800': {
        'task': 'sitecfg.tasks.update_new_user_recharge_limit',
        'schedule': crontab(hour=9, minute=0),  # 每天9点
        'kwargs': {'limit': 800}
    },
    'update-daily-recharge-limit-200': {
        'task': 'sitecfg.tasks.update_daily_recharge_limit',
        'schedule': crontab(hour=0, minute=0),  # 每天0点
        'kwargs': {'limit': 200}
    },
    'update-daily-recharge-limit-800': {
        'task': 'sitecfg.tasks.update_daily_recharge_limit',
        'schedule': crontab(hour=9, minute=0),  # 每天9点
        'kwargs': {'limit': 800}
    },
}

CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = 'Asia/Shanghai'

# 时间
USE_TZ = True
TIME_ZONE = 'Asia/Shanghai'

# Channels 配置
INSTALLED_APPS += ['channels']
ASGI_APPLICATION = 'steambase.routing.application'
CHANNEL_LAYERS = {
    'default': {
        'BACKEND': 'channels_redis.core.RedisChannelLayer',
        'CONFIG': { 'hosts': [('127.0.0.1', 6379)] },
    },
}