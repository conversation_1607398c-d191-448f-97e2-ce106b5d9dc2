# Articles App 文档

## 📝 概述

Articles App是CSGO皮肤交易平台的内容管理系统，提供完整的文章、公告、新闻等内容的管理功能。

## 🎯 主要功能

### ✅ 已实现功能

1. **内容管理**
   - 支持多种内容类型：文章、公告、新闻、指南、FAQ、政策、通知
   - 富文本内容编辑
   - 内容状态管理（草稿、已发布、已归档）
   - 定时发布功能
   - 内容过期管理

2. **分类系统**
   - 分层分类管理
   - 自定义分类图标和颜色
   - 分类排序功能
   - 分类内容统计

3. **标签系统**
   - 多标签支持
   - 标签颜色自定义
   - 标签内容统计
   - 标签云功能

4. **SEO优化**
   - 自定义URL slug
   - SEO标题和描述
   - 关键词管理
   - 搜索引擎友好

5. **API接口**
   - RESTful API设计
   - 完整的CRUD操作
   - 高级过滤和搜索
   - 分页支持

6. **管理后台**
   - Django Admin集成
   - 批量操作支持
   - 内容预览功能
   - 用户权限管理

## 📊 数据统计

- **总内容数量**: 324条
- **文章数量**: 254篇
- **公告数量**: 66条
- **分类数量**: 4个
- **标签数量**: 8个

## 🔗 API接口

### 基础接口

| 接口 | 方法 | 描述 |
|------|------|------|
| `/api/articles/categories/` | GET | 获取分类列表 |
| `/api/articles/tags/` | GET | 获取标签列表 |
| `/api/articles/contents/` | GET | 获取内容列表 |
| `/api/articles/contents/{slug}/` | GET | 获取内容详情 |

### 专门接口

| 接口 | 方法 | 描述 |
|------|------|------|
| `/api/articles/announcements/` | GET | 获取公告列表 |
| `/api/articles/announcements/homepage/` | GET | 获取首页公告 |
| `/api/articles/featured/` | GET | 获取推荐内容 |
| `/api/articles/category/{slug}/` | GET | 按分类获取内容 |
| `/api/articles/tag/{slug}/` | GET | 按标签获取内容 |

### 过滤参数

- `content_type`: 内容类型过滤
- `category`: 分类过滤
- `is_featured`: 推荐内容过滤
- `is_pinned`: 置顶内容过滤
- `search`: 全文搜索
- `ordering`: 排序方式
- `page`: 页码
- `page_size`: 每页数量

## 🚀 使用示例

### 获取首页公告
```javascript
fetch('/api/articles/announcements/homepage/')
  .then(response => response.json())
  .then(data => {
    console.log('首页公告:', data);
  });
```

### 搜索CSGO相关文章
```javascript
fetch('/api/articles/contents/?search=CSGO&content_type=article')
  .then(response => response.json())
  .then(data => {
    console.log('搜索结果:', data.results);
  });
```

### 获取推荐内容
```javascript
fetch('/api/articles/featured/')
  .then(response => response.json())
  .then(data => {
    console.log('推荐内容:', data.results);
  });
```

## 🗄️ 数据模型

### ContentCategory (分类)
- `name`: 分类名称
- `slug`: URL别名
- `description`: 分类描述
- `icon`: 图标类名
- `color`: 颜色代码
- `sort_order`: 排序
- `is_active`: 是否激活

### ContentTag (标签)
- `name`: 标签名称
- `slug`: URL别名
- `color`: 颜色代码
- `is_active`: 是否激活

### Content (内容)
- `title`: 标题
- `slug`: URL别名
- `subtitle`: 副标题
- `excerpt`: 摘要
- `content`: 正文内容
- `content_type`: 内容类型
- `category`: 所属分类
- `tags`: 关联标签
- `status`: 发布状态
- `priority`: 优先级
- `is_featured`: 是否推荐
- `is_pinned`: 是否置顶
- `publish_date`: 发布时间
- `expire_date`: 过期时间
- `featured_image`: 特色图片
- `thumbnail`: 缩略图
- `seo_title`: SEO标题
- `seo_description`: SEO描述
- `seo_keywords`: SEO关键词
- `view_count`: 浏览次数

## 🔧 管理后台

访问 `/admin/articles/` 进入管理后台：

1. **内容管理**
   - 创建、编辑、删除内容
   - 批量发布/下架
   - 内容预览
   - 状态管理

2. **分类管理**
   - 分类创建和编辑
   - 图标和颜色设置
   - 排序管理

3. **标签管理**
   - 标签创建和编辑
   - 颜色设置
   - 使用统计

## 📈 性能优化

1. **数据库优化**
   - 适当的索引设置
   - 查询优化
   - 关联查询优化

2. **API优化**
   - 分页支持
   - 字段选择
   - 缓存策略

3. **搜索优化**
   - 全文搜索支持
   - 搜索结果排序
   - 搜索性能优化

## 🔄 数据迁移

Articles App成功从原有的sitecfg系统迁移了所有数据：

- ✅ 迁移了252篇文章
- ✅ 迁移了63条公告
- ✅ 创建了4个默认分类
- ✅ 创建了8个标签
- ✅ 保持了原有的发布时间和内容

## 🛠️ 开发指南

### 添加新的内容类型

1. 在 `models.py` 中的 `CONTENT_TYPE_CHOICES` 添加新类型
2. 运行数据库迁移
3. 在管理后台中配置新类型

### 自定义API接口

1. 在 `views.py` 中创建新的视图类
2. 在 `urls.py` 中添加URL配置
3. 在 `serializers.py` 中定义序列化器

### 扩展搜索功能

1. 修改 `views.py` 中的搜索字段
2. 添加新的过滤参数
3. 优化搜索性能

## 📝 注意事项

1. **权限管理**: 确保适当的用户权限设置
2. **内容审核**: 建议添加内容审核流程
3. **SEO优化**: 定期检查和优化SEO设置
4. **性能监控**: 监控API响应时间和数据库查询
5. **备份策略**: 定期备份内容数据

## 🔮 未来计划

1. **内容版本控制**: 支持内容历史版本管理
2. **评论系统**: 添加用户评论功能
3. **内容推荐**: 基于用户行为的智能推荐
4. **多语言支持**: 国际化和本地化支持
5. **内容统计**: 详细的内容访问统计分析
