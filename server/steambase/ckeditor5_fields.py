"""
CKEditor 5字段定义
提供与CKEditor 4兼容的字段接口，便于逐步迁移
"""
from django.db import models
from django_ckeditor_5.fields import CKEditor5Field


class RichTextField5(CKEditor5Field):
    """
    CKEditor 5版本的富文本字段
    与原有的RichTextField接口兼容
    """
    def __init__(self, config_name='default', *args, **kwargs):
        # 将config_name映射到CKEditor 5的配置
        super().__init__(config_name=config_name, *args, **kwargs)


class RichTextUploadingField5(CKEditor5Field):
    """
    CKEditor 5版本的富文本上传字段
    与原有的RichTextUploadingField接口兼容
    """
    def __init__(self, config_name='extends', *args, **kwargs):
        # 使用extends配置，包含图片上传功能
        super().__init__(config_name=config_name, *args, **kwargs)
