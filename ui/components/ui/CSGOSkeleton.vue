<template>
  <div class="csgo-skeleton-wrapper">
    <!-- 箱子卡片骨架 -->
    <div v-if="type === 'case'" class="case-skeleton bg-gray-900/90 rounded-xl overflow-hidden border border-gray-700/50 shadow-xl relative backdrop-blur-md">
      <!-- 装饰性光效 -->
      <div class="absolute top-0 left-0 w-20 h-20 rounded-full blur-3xl bg-blue-500/10 animate-pulse"></div>
      <div class="absolute bottom-0 right-0 w-20 h-20 rounded-full blur-3xl bg-purple-500/10 animate-pulse" style="animation-delay: 0.5s;"></div>

      <!-- 图片区域 -->
      <div class="relative pt-[80%] overflow-hidden bg-gray-800/30">
        <div class="skeleton-image absolute inset-4 w-16 h-16 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 rounded-lg"></div>
        
        <!-- 模拟武器图标 -->
        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-4xl opacity-20">
          🔫
        </div>
        
        <!-- 底部颜色条 -->
        <div class="absolute bottom-0 left-0 right-0 h-2 flex z-20">
          <div class="skeleton-line h-full w-full bg-gradient-to-r from-blue-500/30 to-purple-500/30"></div>
        </div>
        
        <!-- 稀有度标签位置 -->
        <div class="absolute top-3 left-3 z-20">
          <div class="skeleton-line h-6 w-14 rounded bg-yellow-500/20"></div>
        </div>
        
        <!-- 右上角特效标记 -->
        <div class="absolute top-3 right-3 z-20">
          <div class="w-3 h-3 rounded-full bg-cyan-400/30 animate-pulse"></div>
        </div>
      </div>

      <!-- 信息区域 -->
      <div class="p-4 bg-gray-800/20">
        <div class="skeleton-line h-5 w-3/4 mb-3 mx-auto rounded"></div>
        <div class="flex items-center justify-between">
          <div class="skeleton-line h-4 w-20 rounded bg-green-500/20"></div>
          <div class="skeleton-line h-6 w-16 rounded bg-orange-500/20"></div>
        </div>
      </div>
    </div>

    <!-- 分类标题骨架 -->
    <div v-else-if="type === 'category'" class="flex justify-between items-center mb-4">
      <div class="flex items-center gap-3">
        <div class="skeleton-line h-8 w-8 rounded-lg bg-blue-500/20"></div>
        <div class="skeleton-line h-7 w-40 rounded bg-gradient-to-r from-gray-600/50 to-gray-500/50"></div>
      </div>
      <div class="skeleton-line h-9 w-28 rounded-full bg-green-500/20"></div>
    </div>

    <!-- 通用行骨架 -->
    <div v-else-if="type === 'line'" class="skeleton-line rounded" :style="{ height: height, width: width }"></div>

    <!-- 头像骨架 -->
    <div v-else-if="type === 'avatar'" class="skeleton-avatar rounded-full border-2 border-gray-600/30" :style="{ width: size, height: size }"></div>

    <!-- 按钮骨架 -->
    <div v-else-if="type === 'button'" class="skeleton-button rounded-lg border border-blue-500/20" :style="{ height: height, width: width }"></div>
    
    <!-- 默认骨架 -->
    <div v-else class="skeleton-line h-4 w-full rounded"></div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  type?: 'case' | 'category' | 'line' | 'avatar' | 'button'
  height?: string
  width?: string
  size?: string
}

const props = withDefaults(defineProps<Props>(), {
  type: 'line',
  height: '1rem',
  width: '100%',
  size: '3rem'
})
</script>

<style lang="scss" scoped>
.case-skeleton {
  min-height: 12rem;
}

.skeleton-line,
.skeleton-avatar,
.skeleton-image,
.skeleton-button {
  background: linear-gradient(
    90deg,
    rgba(55, 65, 81, 0.6) 0%,
    rgba(75, 85, 99, 0.8) 50%,
    rgba(55, 65, 81, 0.6) 100%
  );
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s ease-in-out infinite;
  position: relative;
  overflow: hidden;
}

.skeleton-image {
  background: linear-gradient(
    90deg,
    rgba(75, 85, 99, 0.7) 0%,
    rgba(107, 114, 128, 0.9) 50%,
    rgba(75, 85, 99, 0.7) 100%
  );
  background-size: 200% 100%;
  animation: skeleton-loading 1.8s ease-in-out infinite;
}

.skeleton-button {
  background: linear-gradient(
    90deg,
    rgba(59, 130, 246, 0.4) 0%,
    rgba(99, 102, 241, 0.6) 50%,
    rgba(59, 130, 246, 0.4) 100%
  );
  background-size: 200% 100%;
  animation: skeleton-loading 2s ease-in-out infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 增强骨架效果 */
.csgo-skeleton-wrapper {
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(255, 255, 255, 0.08) 50%,
      transparent 100%
    );
    animation: shimmer 2.5s ease-in-out infinite;
    pointer-events: none;
    z-index: 1;
    border-radius: inherit;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}
</style> 