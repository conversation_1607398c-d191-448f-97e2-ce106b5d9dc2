# CSGOSkins.com.cn Docker部署版本

## 🎯 项目概述

这是CSGOSkins.com.cn的完整Docker化部署版本，包含了Web服务、任务调度、WebSocket实时通信等完整功能。

## 🏗️ 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                  CSGOSkins.com.cn                          │
│                   完整Docker架构                             │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────┬─────────────────┬─────────────────┬─────────────────┐
│   Redis服务     │   Django Web    │   任务调度       │  WebSocket服务  │
│                 │                 │                 │                 │
│ • 缓存存储      │ • Web API       │ • Celery Worker │ • 实时通信      │
│ • 消息队列      │ • 管理后台      │ • Celery Beat   │ • 事件推送      │
│ • 会话存储      │ • 静态文件      │ • ThWorker      │ • Redis订阅     │
│ • 任务队列      │ • 健康检查      │ • 定时任务      │ • 健康检查      │
└─────────────────┴─────────────────┴─────────────────┴─────────────────┘
```

## 🚀 快速开始

### 1. 环境要求

- Docker 20.10+
- Docker Compose 2.0+
- 8GB+ RAM
- 20GB+ 磁盘空间

### 2. 一键部署

```bash
# 克隆项目
git clone <repository-url>
cd csgoskins.com.cn

# 配置环境
cp .env.docker .env
# 编辑 .env 文件，配置数据库连接等

# 一键部署
./docker-deploy.sh start
```

### 3. 验证部署

```bash
# 检查服务状态
./docker-deploy.sh status

# 检查任务调度
./monitor-tasks.sh status

# 访问服务
curl http://localhost:8000/api/health/
curl http://localhost:4000/health
```

## 📦 服务组件

| 服务 | 容器名称 | 端口 | 功能 |
|------|----------|------|------|
| Redis | csgoskins-redis | 6379 | 缓存、消息队列 |
| Django Web | csgoskins-web | 8000 | API、管理后台 |
| Celery Worker | csgoskins-celery-worker | - | 异步任务处理 |
| Celery Beat | csgoskins-celery-beat | - | 定时任务调度 |
| ThWorker | csgoskins-thworker | - | 自定义任务调度 |
| WebSocket | csgoskins-websocket | 4000 | 实时通信 |

## 🔧 管理命令

### 部署管理

```bash
# 构建镜像
./docker-deploy.sh build

# 启动服务
./docker-deploy.sh start

# 停止服务
./docker-deploy.sh stop

# 重启服务
./docker-deploy.sh restart

# 查看日志
./docker-deploy.sh logs

# 检查状态
./docker-deploy.sh status
```

### 任务监控

```bash
# 检查所有任务状态
./monitor-tasks.sh status

# 检查Celery Worker
./monitor-tasks.sh worker

# 检查定时任务
./monitor-tasks.sh beat

# 检查自定义任务
./monitor-tasks.sh thworker

# 重启任务服务
./monitor-tasks.sh restart

# 查看任务日志
./monitor-tasks.sh logs
```

### 数据库管理

```bash
# 进入Web容器
docker exec -it csgoskins-web bash

# 数据库迁移
python manage.py migrate

# 创建超级用户
python manage.py createsuperuser

# 收集静态文件
python manage.py collectstatic --noinput
```

## 📊 功能特性

### ✅ 已实现功能

1. **完整的Docker化部署**
   - 多容器架构
   - 服务依赖管理
   - 健康检查
   - 自动重启

2. **任务调度系统**
   - Celery异步任务
   - Celery Beat定时任务
   - ThWorker自定义调度
   - Redis消息队列

3. **WebSocket实时通信**
   - Socket.IO服务
   - Redis订阅发布
   - 实时事件推送
   - 连接状态监控

4. **监控和管理**
   - 健康检查端点
   - 服务状态监控
   - 任务执行监控
   - 日志聚合

5. **开发和运维工具**
   - 一键部署脚本
   - 任务监控脚本
   - 详细文档
   - 故障排除指南

### 🎯 核心优势

1. **高可用性**
   - 容器自动重启
   - 服务健康检查
   - 依赖关系管理
   - 故障自动恢复

2. **可扩展性**
   - 微服务架构
   - 水平扩展支持
   - 负载均衡就绪
   - 资源隔离

3. **易于维护**
   - 标准化部署
   - 统一日志管理
   - 配置集中管理
   - 版本控制

4. **开发友好**
   - 本地开发环境
   - 热重载支持
   - 调试工具
   - 测试环境

## 📚 文档目录

- [Docker部署指南](docs/DOCKER_DEPLOYMENT.md)
- [API文档](docs/api/)
- [任务调度文档](docs/backend/thworker.md)
- [WebSocket文档](docs/websocket/)

## 🔍 监控和调试

### 健康检查

```bash
# Django API健康检查
curl http://localhost:8000/api/health/

# WebSocket健康检查
curl http://localhost:4000/health

# Redis连接检查
docker exec csgoskins-redis redis-cli ping
```

### 日志查看

```bash
# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs web
docker-compose logs celery-worker
docker-compose logs websocket

# 实时日志跟踪
docker-compose logs -f
```

### 性能监控

```bash
# 容器资源使用
docker stats

# Celery任务统计
docker exec csgoskins-celery-worker celery -A steambase inspect stats

# Redis性能信息
docker exec csgoskins-redis redis-cli info
```

## 🛠️ 故障排除

### 常见问题

1. **容器启动失败**
   - 检查日志: `docker-compose logs <service>`
   - 重新构建: `docker-compose build --no-cache`

2. **数据库连接失败**
   - 检查配置: `cat .env | grep DATABASE`
   - 测试连接: `docker exec csgoskins-web python manage.py dbshell`

3. **任务不执行**
   - 检查Worker: `./monitor-tasks.sh worker`
   - 检查队列: `docker exec csgoskins-redis redis-cli llen celery`

4. **WebSocket连接失败**
   - 检查端口: `netstat -tlnp | grep 4000`
   - 检查日志: `docker-compose logs websocket`

## 🔄 更新和维护

### 代码更新

```bash
# 拉取最新代码
git pull origin main

# 重新部署
./docker-deploy.sh restart
```

### 数据备份

```bash
# 数据库备份
docker exec csgoskins-web python manage.py dumpdata > backup.json

# Redis数据备份
docker exec csgoskins-redis redis-cli save
docker cp csgoskins-redis:/data/dump.rdb ./redis-backup.rdb
```

## 📞 技术支持

如有问题，请查看：
1. [故障排除指南](docs/DOCKER_DEPLOYMENT.md#故障排除)
2. [日志分析](docs/DOCKER_DEPLOYMENT.md#监控和调试)
3. [性能优化](docs/DOCKER_DEPLOYMENT.md#性能监控)

---

**🎉 现在您拥有了一个完整的、生产就绪的CSGOSkins.com.cn Docker部署环境！**
