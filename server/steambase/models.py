import string
import random

from uuid import uuid1

from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from steambase.enums import AppType, ExteriorType
from django.contrib.auth import get_user_model

USER_MODELL = settings.AUTH_USER_MODELL


def uid_gen():
    return uuid1().hex


class ModelBase(models.Model):
    uid = models.CharField(_("uid"), max_length=64, unique=True, default=uid_gen, editable=False)
    create_time = models.DateTimeField(_("create time"), editable=False, auto_now_add=True)
    update_time = models.DateTimeField(_("update time"), editable=False, auto_now=True)

    class Meta:
        abstract = True


class ItemBase(models.Model):
    APP_TYPE = (
        (AppType.Dota2.value, _('Dota2')),
        (AppType.CSGO.value, _('CSGO')),
        (AppType.PUBG.value, _('PUBG')),
        (AppType.H1Z1.value, _('H1Z1')),
    )
    EXTERIOR_TYPE = (
        (ExteriorType.WearCategory0.value, _('WearCategory0')),
        (ExteriorType.WearCategory1.value, _('WearCategory1')),
        (ExteriorType.WearCategory2.value, _('WearCategory2')),
        (ExteriorType.WearCategory3.value, _('WearCategory3')),
        (ExteriorType.WearCategory4.value, _('WearCategory4')),
    )
    name = models.CharField(_('name'), max_length=128)
    market_name = models.CharField(_('market name'), max_length=128)
    market_hash_name = models.CharField(_('market hash name'), max_length=128, unique=True)
    name_cn = models.CharField(_('name(CN)'), max_length=128, default=None, null=True, blank=True)
    market_name_cn = models.CharField(_('market name(CN)'), max_length=128, default=None, null=True, blank=True)
    name_color = models.CharField(_('name color'), max_length=32, default=None, null=True, blank=True)
    appid = models.CharField(_('appid'), max_length=64, choices=APP_TYPE)
    contextid = models.CharField(_('contextid'), max_length=64)
    classid = models.CharField(_('classid'), max_length=64)
    icon_url = models.CharField(_('icon url'), max_length=512, default=None, null=True, blank=True)
    icon_url_large = models.CharField(_('icon url large'), max_length=512, default=None, null=True, blank=True)
    rarity = models.CharField(_('rarity'), max_length=32, default=None, null=True, blank=True)
    rarity_color = models.CharField(_('rarity color'), max_length=32, default=None, null=True, blank=True)
    quality = models.CharField(_('quality'), max_length=32, default=None, null=True, blank=True)
    quality_color = models.CharField(_('quality color'), max_length=32, default=None, null=True, blank=True)
    type = models.CharField(_('type'), max_length=64, default=None, null=True, blank=True)
    weapon = models.CharField(_('weapon'), max_length=32, default=None, null=True, blank=True)
    exterior = models.CharField(_('exterior'), max_length=32, default=None, null=True, blank=True, choices=EXTERIOR_TYPE)
    item_set = models.CharField(_('item set'), max_length=64, default=None, null=True, blank=True)
    slot = models.CharField(_('slot'), max_length=64, default=None, null=True, blank=True)
    hero = models.CharField(_('hero'), max_length=64, default=None, null=True, blank=True)

    class Meta:
        abstract = True


