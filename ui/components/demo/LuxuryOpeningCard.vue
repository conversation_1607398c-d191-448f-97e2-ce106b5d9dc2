<template>
  <div class="luxury-card relative overflow-hidden">
    <!-- 背景光效 -->
    <div class="absolute inset-0 bg-gradient-to-r from-yellow-500/10 via-orange-500/20 to-red-500/10 animate-pulse"></div>
    <div class="absolute inset-0 bg-grid opacity-5"></div>
    
    <!-- 流光边框 -->
    <div class="absolute inset-0 rounded-xl bg-gradient-to-r from-transparent via-yellow-400/30 to-transparent animate-border-flow"></div>
    
    <div class="relative bg-gray-900/80 backdrop-blur-md rounded-xl border border-yellow-400/30 p-6">
      <!-- 顶部装饰 -->
      <div class="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
        <div class="bg-yellow-400 text-gray-900 px-3 py-1 rounded-full text-xs font-bold">
          🔥 LEGENDARY DROP
        </div>
      </div>
      
      <div class="flex items-center space-x-6 mt-4">
        <!-- 箱子展示 -->
        <div class="flex-shrink-0 relative">
          <div class="absolute inset-0 bg-yellow-400/20 blur-xl rounded-full animate-pulse"></div>
          <img
            :src="record.case_info?.cover"
            :alt="record.case_info?.name"
            class="relative w-20 h-20 object-contain drop-shadow-2xl transform hover:scale-110 transition-transform duration-300 bg-gray-800"
            @error="handleImageError"
          />
        </div>
        
        <!-- 中央信息 -->
        <div class="flex-grow text-center">
          <div class="text-yellow-400 font-bold text-lg mb-2">{{ record.user_name }}</div>
          <div class="text-white/80 text-sm mb-1">{{ record.case_info?.name }}</div>
          <div class="flex items-center justify-center space-x-2">
            <span class="text-orange-400 text-sm">🎯</span>
            <span class="text-white font-medium">{{ getSkinName(record.item_info?.name) }}</span>
          </div>
          <div class="text-yellow-400 text-xs mt-1">{{ getExteriorName() }}</div>
        </div>
        
        <!-- 物品展示 -->
        <div class="flex-shrink-0 relative">
          <div class="absolute inset-0 bg-orange-500/20 blur-xl rounded-full animate-pulse"></div>
          <img
            :src="record.item_info?.image"
            :alt="record.item_info?.name"
            class="relative w-24 h-24 object-contain drop-shadow-2xl transform hover:scale-110 transition-transform duration-300 bg-gray-800"
            @error="handleImageError"
          />
        </div>
      </div>
      
      <!-- 底部装饰 -->
      <div class="flex justify-center mt-4 space-x-4">
        <div class="flex items-center space-x-1 text-yellow-400 text-xs">
          <span>⭐</span>
          <span>Covert</span>
        </div>
        <div class="flex items-center space-x-1 text-orange-400 text-xs">
          <span>💎</span>
          <span>Rare Drop</span>
        </div>
        <div class="flex items-center space-x-1 text-red-400 text-xs">
          <span>🔥</span>
          <span>Hot Item</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  record: any
}

const props = defineProps<Props>()

const getSkinName = (fullName?: string): string => {
  if (!fullName) return 'Unknown Item'
  const parts = fullName.split(' | ')
  if (parts.length < 2) return fullName
  const skinPart = parts[1].trim()
  return skinPart.split(' (')[0].trim()
}

const getExteriorName = (): string => {
  return props.record.item_info?.item_exterior?.exterior_name || 'Factory New'
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjMzc0MTUxIi8+CjxwYXRoIGQ9Ik0yMCAyMEg0NFY0NEgyMFYyMFoiIHN0cm9rZT0iIzZCNzI4MCIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIi8+CjxjaXJjbGUgY3g9IjI4IiBjeT0iMjgiIHI9IjQiIGZpbGw9IiM2QjcyODAiLz4KPHBhdGggZD0iTTI0IDM2TDI4IDMyTDM2IDQwSDI0VjM2WiIgZmlsbD0iIzZCNzI4MCIvPgo8L3N2Zz4K'
}
</script>

<style lang="scss" scoped>
.luxury-card {
  animation: luxuryEntrance 1s ease-out;
}

.bg-grid {
  background-image: 
    linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
  background-size: 20px 20px;
}

@keyframes luxuryEntrance {
  0% {
    transform: scale(0.9) rotateY(-10deg);
    opacity: 0;
  }
  50% {
    transform: scale(1.05) rotateY(5deg);
  }
  100% {
    transform: scale(1) rotateY(0deg);
    opacity: 1;
  }
}

@keyframes border-flow {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.animate-border-flow {
  animation: border-flow 3s linear infinite;
}
</style> 