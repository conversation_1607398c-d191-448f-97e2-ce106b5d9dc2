import { ref } from 'vue';
import { battleApi } from '~/services/battle-api';

export function useBattleReconnection(battleId: string) {
  const requiresStateRecovery = ref(false);
  const lastDisconnectTime = ref<number | null>(null);

  const onDisconnected = () => {
    lastDisconnectTime.value = Date.now();
    requiresStateRecovery.value = true;
  };

  const onReconnected = () => {
    if (lastDisconnectTime.value && Date.now() - lastDisconnectTime.value > 1000) {
      requiresStateRecovery.value = true;
    }
  };

  const recoverAnimationState = async () => {
    if (!requiresStateRecovery.value) return null;

    try {
      const response = await battleApi.getAnimationState(battleId);
      if (response.success && response.data) {
        requiresStateRecovery.value = false;
        lastDisconnectTime.value = null;
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to recover animation state.');
      }
    } catch (error) {
      console.error('Animation state recovery failed:', error);
      throw error;
    }
  };

  return {
    requiresStateRecovery,
    onDisconnected,
    onReconnected,
    recoverAnimationState,
  };
}