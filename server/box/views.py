import logging
import time

from django.shortcuts import render
from django.utils.translation import gettext_lazy as _

from rest_framework.views import APIView
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response

from steambase.enums import RespCode
from steambase.utils import reformat_resp, current_user, ParamException, current_sock
from sitecfg.interfaces import get_maintenance, get_maintenance_box, get_maintenance_box_room
from box.business import user_open_case, get_case_list, get_case_list_hot, get_case_list_new, get_tag_box_list, get_case_detail, get_case_record, socket_connect, \
    socket_disconnect, get_battle_info, get_online_number, get_drop_day_price_rank, get_drop_day_count_rank, get_discount_box_list
from box.business import get_free_case_list, setup_sync_box_show_chance_worker, get_case_recent_records, get_case_list_random, get_case_category, get_case_type
# from box.business import create_case_room, join_case_room, get_room_list, get_room_detail, quit_case_room
from box.business import get_drop_day_rank, get_income_day_rank, get_room_day_rank, get_lose_week_rank, get_battle_day_rank, get_box_skin_list, get_box_record
from box.business_room import create_case_room, join_case_room, get_room_list, get_room_detail, quit_case_room, dismiss_case_room
from box.business_room import join_battle_case_room, get_room_self, get_room_participated, leave_room_detail, \
    quick_sell_room_item, get_battle_list
from box.business_room import get_room_record, get_room_history, get_room_statistics, get_room_record_all
from box.tasks import delete_old_case_records, update_dropitem_and_itemprice_custom_price

# 2025
from box.business import search_case, get_battle_case_list
# 新增：轮次管理器导入
from box.round_manager import BattleRoundManager, BattleRoundValidator

_logger = logging.getLogger(__name__)


class OpenCaseView(APIView):

    def post(self, request):
        try:
            user = current_user(request)            
            case_key = request.data.get('case_key', None)
            count = request.data.get('count', 1)
            simulate = request.data.get('simulate', False) # 模拟开箱
            # print('case_key:'+str(case_key), 'count:'+str(count), 'simulate:'+str(simulate))
            
            code, resp = user_open_case(user, case_key, count, simulate)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, _('Succeed'))
            else:
                return reformat_resp(code, {}, resp)
        except ParamException as pe:
            return reformat_resp(RespCode.InvalidParams.value, {}, str(pe))
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, _('Exception'))


class GetCaseListView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            user = current_user(request)

            query = {
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            if request.query_params.get('enableRoom', None) == '1':
                query['enable_room'] = True
            if request.query_params.get('enableRoom', None) == '0':
                query['enable_room'] = False
            if request.query_params.get('is_show', None) == '1':
                query['is_show'] = True
            if request.query_params.get('is_show', None) == '0':
                query['is_show'] = False
            fields = ('key', 'name', 'name_en', 'name_zh_hans', 'price','open_count', 'discount', 'cover', 'item', 'case_type_category', 'tag', 'tag_color',
                      'enable_room')
            code, resp = get_case_list(user, query, fields)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')

class GetHotBoxListView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            num = request.query_params.get('num', 20)
            num = int(num)
            
            fields = ('key', 'name', 'name_en', 'name_zh_hans', 'price', 'open_count', 'discount', 'cover', 'tag', 'tag_en', 'tag_zh_hans', 'tag_color', 'cate_id', 'cate_name', 'cate_name_en', 'cate_name_zh_hans', 'type_name', 'type_name_en', 'type_name_zh_hans')
            code, resp = get_case_list_hot(num, fields)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')

class GetRandomBoxListView(APIView):
    permission_classes = [AllowAny]
    def get(self, request):
        try:
            num = request.query_params.get('num', 30)
            domain = request.query_params.get('domain', 'www.csgo114.com')
            num = int(num)
            fields = ('key', 'name', 'name_zh_hans', 'price','open_count', 'discount', 'cover', 'item', 'tag', 'tag_color')
            code, resp = get_case_list_random(num, fields, domain, randomize=True)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')



class GetNewBoxListView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:           
            num = request.query_params.get('num', 5)
            num = int(num)
            #print(num)
            # user = current_user(request)

            # query = {
            # }
            # for key in list(query.keys()):
            #     if not query.get(key):
            #         del query[key]
            # if request.query_params.get('enableRoom', None) == '1':
            #     query['enable_room'] = True
            # if request.query_params.get('enableRoom', None) == '0':
            #     query['enable_room'] = False
            # if request.query_params.get('unlock', None) == '1':
            #     query['unlock'] = True
            # if request.query_params.get('unlock', None) == '0':
            #     query['unlock'] = False
            fields = ('case_key', 'name', 'create_time', 'name_zh_hans', 'price','open_count', 'discount', 'cover', 'item')
            code, resp = get_case_list_new(num, fields)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')

class GetFreeCaseListView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            user = current_user(request)
            query = {
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            fields = ('level', 'min_amount', 'max_amount', 'case')
            code, resp = get_free_case_list(user, query, fields)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetCaseDetailView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            user = current_user(request)
            
            case_key= request.query_params.get('key', None)           
            
            fields = ('id', 'uid', 'case_key', 'name', 'name_en', 'name_zh_hans', 'price','open_count', 'discount', 'cover', 'cate_id', 'cate_name', 'cate_name_en', 'cate_name_zh_hans', 'tag', 'tag_en', 'tag_zh_hans', 'tag_color', 'type_name', 'type_name_en', 'type_name_zh_hans')            
            code, resp = get_case_detail(user, case_key, fields)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')
    




class GetCaseRecordView(APIView):

    def get(self, request):
        try:
            user = current_user(request)

            query = {
                'case__case_type': request.query_params.get('case_type', None)
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            fields = (
            'uid', 'case', 'case_name', 'market_name', 'market_name_cn', 'icon_url', 'price', 'cost', 'create_time')
            code, resp = get_case_record(user, query, fields, page, page_size)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetCaseRecentRecordView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:

            # user = current_user(request)
            query = {
                'key': request.query_params.get('key', None)
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            # page = int(request.query_params.get('page', 1))
            # page_size = int(request.query_params.get('pageSize', 10))
            fields = ('case', 'user', 'item', 'price', 'source', 'create_time')
            code, resp = get_case_recent_records(query, fields)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class CreateCaseRoomView(APIView):

    def post(self, request):
        try:
            if get_maintenance() or get_maintenance_box_room():
                return reformat_resp(RespCode.Maintenance.value, {},
                                     'Current game is under maintenance, please wait for a while.')

            user = current_user(request)
            cases = request.data.get('cases', [])
            total_joiner = request.data.get('totalJoiner', 2)
            room_type = request.data.get('roomType', 1)
            private = request.data.get('private', 0)
            code, resp = create_case_room(user, cases, total_joiner, room_type, private)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except ParamException as pe:
            return reformat_resp(RespCode.InvalidParams.value, {}, str(pe))
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class JoinCaseRoomView(APIView):

    def post(self, request):
        try:
            if get_maintenance() or get_maintenance_box_room():
                return reformat_resp(RespCode.Maintenance.value, {},
                                     'Current game is under maintenance, please wait for a while.')

            user = current_user(request)
            uid = request.data.get('uid', None)
            
            code, resp = join_case_room(user, uid)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except ParamException as pe:
            return reformat_resp(RespCode.InvalidParams.value, {}, str(pe))
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class CreateBattleCaseRoomView(APIView):

    def post(self, request):
        try:

            user = current_user(request)
            cases = request.data.get('cases_key', [])
            max_joiner = request.data.get('max_joiner', 4)  # 默认4人
            private = request.data.get('private', 0)
            
            # 使用create_case_room，room_type=1 表示普通对战
            code, resp = create_case_room(user, cases, max_joiner, 1, private)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except ParamException as pe:
            return reformat_resp(RespCode.InvalidParams.value, {}, str(pe))
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class JoinBattleCaseRoomView(APIView):

    def post(self, request):
        try:

            user = current_user(request)
            uid = request.data.get('uid', None)
            team = request.data.get('team', 1)
            code, resp = join_battle_case_room(user, uid, team)
            if code == RespCode.Succeed.value:
                # 返回文档要求的简化结果
                body = {'uid': resp.get('room_uid'), 'success': True}
                return reformat_resp(code, body, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except ParamException as pe:
            return reformat_resp(RespCode.InvalidParams.value, {}, str(pe))
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class QuitCaseRoomListView(APIView):

    def post(self, request):
        try:

            user = current_user(request)
            uid = request.data.get('uid', None)
            code, resp = quit_case_room(user, uid)
            if code == RespCode.Succeed.value:
                # 返回文档要求的退款信息
                body = {'success': True, 'refund_amount': resp.get('refund_amount')}
                return reformat_resp(code, body, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except ParamException as pe:
            return reformat_resp(RespCode.InvalidParams.value, {}, str(pe))
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class DismissCaseRoomView(APIView):
    """
    房主解散对战房间接口
    只有房主可以解散房间，解散后所有参与者退款
    """

    def post(self, request):
        try:
            user = current_user(request)
            uid = request.data.get('uid', None)
            
            if not uid:
                return reformat_resp(RespCode.InvalidParams.value, {}, '房间UID不能为空')
            
            code, resp = dismiss_case_room(user, uid)
            if code == RespCode.Succeed.value:
                # 返回文档要求的解散退款信息
                body = {
                    'success': True,
                    'refund_total': resp.get('refund_amount'),
                    'participants_refunded': resp.get('participant_count')
                }
                return reformat_resp(code, body, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except ParamException as pe:
            return reformat_resp(RespCode.InvalidParams.value, {}, str(pe))
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, '解散房间时发生异常')


class SellCaseRoomItemView(APIView):

    def post(self, request):
        try:

            user = current_user(request)
            uid = request.data.get('uid', None)
            code, resp = quick_sell_room_item(user, uid)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except ParamException as pe:
            return reformat_resp(RespCode.InvalidParams.value, {}, str(pe))
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetCaseRoomListView(APIView):
    # 权限类
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            # 获取当前用户
            user = current_user(request)
            
            # 检查是否有状态参数，如果有则使用battle list逻辑
            state = request.query_params.get('state', None)
            if state:
                # 使用battle list逻辑来处理状态过滤
                assigner = request.query_params.get('assigner', None)
                state_list = state.split(',')
                
                fields = (
                    'uid', 'short_id', 'user', 'max_joiner', 'price', 'state', 'type', 
                    'rounds', 'bets', 'joiner_count', 'create_time', 'update_time'
                )
                page = int(request.query_params.get('page', 1))
                page_size = int(request.query_params.get('pageSize', 10))
                
                code, resp = get_battle_list(user, state_list, fields, page, page_size, assigner)
            else:
                # 使用原有的room list逻辑
                query = {
                    'uid': request.query_params.get('uid', None)
                }
                for key in list(query.keys()):
                    if not query.get(key):
                        del query[key]
                        
                fields = (
                    'uid', 'short_id', 'create_time', 'update_time', 'max_joiner', 'price', 'state', 'type', 'joiner_count', 
                    'round_count', 'round_count_current', 'joiner_count', 'rounds', 'bets', 'user'            
                )
                page = int(request.query_params.get('page', 1))
                page_size = int(request.query_params.get('pageSize', 10))
                
                code, resp = get_room_list(user, query, fields, page, page_size)
            
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')

class GetCaseRoomSelfView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:

            user = current_user(request)
            query = {
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            fields = (
            'uid', 'short_id', 'user', 'max_joiner', 'price', 'state', 'type', 'rounds', 'bets', 'joiner_count',
            'create_time')
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            code, resp = get_room_self(user, query, fields, page, page_size)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetCaseRoomDetailView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:

            sid = current_sock(request)
            uid = request.query_params.get('uid', None)          
            
            fields = (
                'uid', 'short_id', 'create_time', 'update_time', 'max_joiner', 'price', 'state', 'type', 'joiner_count', 'private', 
                'round_count', 'round_count_current', 'joiner_count', 'rounds', 'bets', 'user'            
            )
            
            code, resp = get_room_detail(uid, sid, fields)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetCaseRoomLeaveView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:

            user = current_user(request)
            sid = current_sock(request)
            query = {
                'uid': request.query_params.get('uid', None),
                # 'short_id': request.query_params.get('name', None),
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            fields = ()
            code, resp = leave_room_detail(user, query, sid)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class SocketConnectView(APIView):
    # 接收用户发送的参数
    permission_classes = [AllowAny]

    def socket_connect(self, request, params):
        try:

            # 调用socket_connect函数，返回响应
            code, resp = socket_connect(request, params)
            # 如果响应码为成功，返回响应
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'succeed')
                return reformat_resp(code, resp,'succeed')
            # 否则返回响应
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            # 捕获异常
            _logger.exception(e)
            # 返回异常响应
            return reformat_resp(RespCode.Exception.value, {}, _('Exception'))

    def post(self, request, format=None):
        # 调用socket_connect函数，返回响应
        return self.socket_connect(request, request.data)


class SocketDisconnectView(APIView):
    permission_classes = [AllowAny]

    def socket_disconnect(self, request, params):
        try:

            code, resp = socket_disconnect(request, params)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, 'Exception')

    def post(self, request, format=None):
        return self.socket_disconnect(request, request.data)


class SyncBoxShowChanceView(APIView):

    def get(self, request):
        try:
            user = current_user(request)
            if user.is_superuser:
                setup_sync_box_show_chance_worker()
                return reformat_resp(RespCode.Succeed.value, {}, 'Succeed')
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetDropDayRankView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            num = int(request.query_params.get('num', 10))
            code, resp = get_drop_day_rank(num)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetIncomeDayRankView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:

            query = {
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            code, resp = get_income_day_rank(10)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetRoomDayRankView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:

            query = {
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            code, resp = get_room_day_rank(10)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetLoseWeekRankView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:

            query = {
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            code, resp = get_lose_week_rank(10)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetRoomRecordView(APIView):

    def get(self, request):
        try:

            user = current_user(request)
            query = {
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            fields = ('create_time','short_id', 'uid', 'max_joiner', 'price', 'state', 'type', 'private',
                      'rounds', 'bets', 'round_count', 'win_amount')
            code, resp = get_room_record(user, fields, page, page_size)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


# 我发起的对战
class GetRoomHistoryView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:

            user = current_user(request)
            query = {
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            fields = ('create_time','short_id', 'uid', 'max_joiner', 'price', 'state', 'type',
                      'private', 'rounds', 'bets', 'round_count', 'win_amount')
            code, resp = get_room_history(fields, page, page_size)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetRoomStatistics(APIView):

    def get(self, request):
        try:

            user = current_user(request)
            query = {
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            code, resp = get_room_statistics(user)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetBattleInfoView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:

            user = current_user(request)
            query = {
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            code, resp = get_battle_info(user, 18)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')


class GetJoinableRoomView(APIView):
    permission_classes = [AllowAny]
    def get(self, request):
        try:
            user = current_user(request)
            code, resp = get_online_number()
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')
    
#我参与的全部对战
class GetRoomRecordAllView(APIView): 

    def get(self, request):
        try:

            user = current_user(request)
            query = {
            }
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            fields = ('create_time', 'update_time', 'short_id', 'open_amount', 'win_amount', 'win_items_count', 'victory',  'max_joiner', 'state')
            code, resp = get_room_record_all(user, fields, page, page_size)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')  
    

class GetDropDayPriceRankView(APIView):
    permission_classes = [AllowAny]
    def get(self, request):
        try:
            num = int(request.query_params.get('num', 12))
            code, resp = get_drop_day_price_rank(num)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')  
    

class GetDropDayMostRankView(APIView):
    permission_classes = [AllowAny]
    def get(self, request):
        try:
            num = int(request.query_params.get('num', 12))
            code, resp = get_drop_day_count_rank(num)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception') 
    
class GetBattleDayRankView(APIView):
    permission_classes = [AllowAny]
    def get(self, request):
        try:
            num = int(request.query_params.get('num', 10))
            code, resp = get_battle_day_rank(num)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception') 
    
class GetTagBoxListView(APIView):
    permission_classes = [AllowAny]
    def get(self, request):
        try:
            tag = request.query_params.get('q', None)    
            num = int(request.query_params.get('num', 5))        
            if not tag:
                return reformat_resp(RespCode.InvalidParams.value, {}, 'InvalidParams')
            # tag转换为大写
            tag = tag.upper()
            fields = ('id', 'uid', 'case_key', 'name', 'name_en', 'name_zh_hans', 'price', 'open_count', 'discount', 'cover', 'tag', 'tag_en', 'tag_zh_hans', 'tag_color', 'cate_id', 'cate_name', 'cate_name_en', 'cate_name_zh_hans', 'type_name', 'type_name_en', 'type_name_zh_hans', 'create_time')

            code, resp = get_tag_box_list(num, tag, fields)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception') 
    
class GetBattleListView(APIView):

    permission_classes = [AllowAny]
    def get(self, request):
        try:
            # 获取当前用户
            user = current_user(request)
            # 状态分类
            state = request.query_params.get('state', None)
            #private = request.query_params.get('private', 0)
            assigner = request.query_params.get('assigner', None)
            if not state:
                return reformat_resp(RespCode.InvalidParams.value, {}, 'InvalidParams')     
            # 将状态参数解析为列表
            state_list = state.split(',')             
        
            # 获取查询参数
            fields = (
                'uid', 'short_id', 'user', 'max_joiner', 'price', 'state', 'type', 'rounds', 'bets', 'joiner_count', 
                'create_time'
            )
            # 获取页码
            page = int(request.query_params.get('page', 1))
            # 获取每页数量
            page_size = int(request.query_params.get('pageSize', 10))
            code, resp = get_battle_list(user, state_list, fields, page, page_size, assigner)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception') 
    
class GetDiscountBoxListView(APIView):
    permission_classes = [AllowAny]
    def get(self, request):
        try:
            num = int(request.query_params.get('num', 6))
            fields = ('key', 'name', 'name_en', 'create_time', 'name_zh_hans', 'price','open_count', 'discount', 'cover')            
            code, resp = get_discount_box_list(fields, num)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception') 
    

    
class SearchCaseView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            # 获取搜索关键词，允许为空
            q = request.query_params.get('q', '')
            
            # 构建查询条件
            query = {}
            
            # 添加可选过滤条件
            if request.query_params.get('type'):
                query['case_type'] = request.query_params.get('type')
                
            if request.query_params.get('category'):
                query['case_category'] = request.query_params.get('category')
                
            if request.query_params.get('enable_room') in ['0', '1']:
                query['enable_room'] = request.query_params.get('enable_room') == '1'
            
            # 定义返回字段
            fields = (
                'case_key', 'name', 'name_en', 'name_zh_hans', 'price', 'open_count', 
                'discount', 'cover', 'tag', 'tag_zh_hans', 'tag_en', 'tag_color', 'enable_room'
            )
            
            # 调用搜索函数
            code, resp = search_case(q, query, fields)
            
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, _('成功'))
            else:
                return reformat_resp(code, {}, resp.get('message', _('搜索失败')))
                
        except Exception as e:
            _logger.exception(e)
            return reformat_resp(RespCode.Exception.value, {}, _('服务器异常'))
    
class GetBoxSkinView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            case_key = request.query_params.get('key', None)
            if not case_key:
                return reformat_resp(RespCode.InvalidParams.value, {}, 'InvalidParams')
            fields = ('item_info', 'chance')
            code, resp = get_box_skin_list(case_key, fields)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')   
    
class GetBoxRecordView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            case_key = request.query_params.get('key', None)
            num = int(request.query_params.get('num', 30))
            if not case_key:
                return reformat_resp(RespCode.InvalidParams.value, {}, 'InvalidParams')
            fields = ('user_info', 'item_info', 'case_info', 'cost', 'create_time')
            code, resp = get_box_record(case_key, num, fields)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')      
    
class DeleteOldCaseRecordsView(APIView):
    permission_classes = [AllowAny]
    

    def get(self, request):
        """
        触发删除旧 CaseRecord 记录的任务并返回执行结果。
        """
        user = current_user(request)
        try:
            # 调用删除方法
            #if user.is_superuser:
            result = delete_old_case_records()
            
            if result:
                return Response({'code': 200, 'message': result}, status=200)
            else:
                return Response({'code': 200, 'message': 'No old CaseRecord records found'}, status=200)
            
        except Exception as e:
            _logger.exception(e)
            return Response({'code': 500, 'message': 'Exception occurred'}, status=500)
        
class UpdateBoxItemsCustomPriceView(APIView):
    permission_classes = [AllowAny]
    def get(self, request):
        user = current_user(request)
        try:
            # if user.is_superuser:
            result = update_dropitem_and_itemprice_custom_price(batch_size=100)
            if result:
                return Response({'code': 200, 'message': 'Update box items custom price succeed'}, status=200)
            else:
                return Response({'code': 200, 'message': 'No box items found'}, status=200)
        except Exception as e:
            _logger.exception(e)
            return Response({'code': 500, 'message': 'Exception occurred'}, status=500)
    


class GetCaseCategoryView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            fields = ('cate_id', 'cate_name', 'cate_name_en', 'cate_name_zh_hans', 'icon')
            code, resp = get_case_category(fields)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')
    
class GetCaseTypeView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            fields = ('type_id', 'type_name', 'type_name_en', 'type_name_zh_hans')
            code, resp = get_case_type(fields)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')
    
class GetBattleCaseListView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            fields = ('case_key', 'name', 'name_en', 'name_zh_hans', 'price', 'open_count', 'discount', 'cover')
            code, resp = get_battle_case_list(fields)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')

class GetCaseRoomParticipatedView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            user = current_user(request)
            query = {}
            
            # 支持状态过滤
            state = request.query_params.get('state')
            if state:
                query['state'] = state
                
            for key in list(query.keys()):
                if not query.get(key):
                    del query[key]
                    
            # 支持房间类型过滤
            room_type = request.query_params.get('type', 'all')  # all, created, joined
            
            fields = (
                'uid', 'short_id', 'user', 'max_joiner', 'price', 'state', 'type', 'rounds', 'bets', 'joiner_count',
                'create_time', 'update_time'
            )
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('pageSize', 10))
            
            code, resp = get_room_participated(user, query, fields, page, page_size, room_type)
            if code == RespCode.Succeed.value:
                return reformat_resp(code, resp, 'Succeed')
            else:
                return reformat_resp(code, {}, resp)
        except Exception as e:
            _logger.exception(e)
        return reformat_resp(RespCode.Exception.value, {}, 'Exception')

class BattleAnimationStateView(APIView):
    """对战动画状态恢复API - 修复轮次硬编码问题"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """获取房间当前的动画状态，用于WebSocket重连后的动画恢复"""
        try:
            room_uid = request.GET.get('uid')
            if not room_uid:
                return reformat_resp(RespCode.InvalidParams.value, {}, _('room_uid required'))

            user = current_user(request)
            
            # 获取房间信息
            from box.models import CaseRoom, CaseRoomBet
            room = CaseRoom.objects.filter(uid=room_uid).first()
            if not room:
                return reformat_resp(RespCode.InvalidParams.value, {}, _('Room not found'))

            # 权限检查：公开房间任何登录用户可观看，私有房间仅参与者可访问
            if room.private:
                participant = CaseRoomBet.objects.filter(room=room, user=user).exists()
                if not participant:
                    return reformat_resp(RespCode.InvalidParams.value, {}, _('Access denied'))

            # 使用轮次管理器获取准确的轮次信息
            round_manager = BattleRoundManager(room_uid)
            current_round = round_manager.get_current_round()
            total_rounds = round_manager.get_total_rounds()
            round_status = round_manager.get_round_status()

            # 验证轮次合理性
            try:
                round_manager.validate_round(current_round)
            except ValueError as e:
                _logger.warning(f"轮次验证失败，使用安全值: room={room_uid}, error={e}")
                current_round = BattleRoundValidator.get_safe_round(room_uid, 1)

            # 获取动画状态（从Redis缓存）
            from django.core.cache import cache
            animation_cache_key = f"battle_animation:{room_uid}"
            animation_state = cache.get(animation_cache_key)
            
            if not animation_state:
                # 如果没有缓存，根据房间状态推断动画状态
                if room.state == 2:  # Joinable
                    animation_state = {'status': 'waiting'}
                elif room.state == 4:  # Full
                    animation_state = {'status': 'countdown'}
                elif room.state == 5:  # Running
                    animation_state = {'status': 'round_start'}
                elif room.state == 11:  # End
                    animation_state = {'status': 'battle_end'}
                else:
                    animation_state = {'status': 'waiting'}

            # 准备参与者信息
            from box.serializers import CaseRoomBetSerializer
            from box.business_room import safe_image_url
            
            bets = CaseRoomBet.objects.filter(room=room).select_related('user', 'user__profile')
            participants = []
            
            for bet in bets:
                participant = {
                    'user': {
                        'uid': bet.user.uid,
                        'profile': {
                            'nickname': bet.user.profile.nickname if hasattr(bet.user, 'profile') and bet.user.profile else bet.user.username,
                            'avatar': safe_image_url(bet.user.profile.avatar) if hasattr(bet.user, 'profile') and bet.user.profile else ''
                        }
                    },
                    'status': 'waiting'  # 默认状态
                }
                participants.append(participant)

            # 构建响应数据
            current_timestamp = int(time.time() * 1000)
            
            # 构建符合文档示例的响应格式
            response_data = {
                'animation_state': animation_state.get('status'),
                'current_round': current_round,
                'total_rounds': total_rounds,
                'animation_start_timestamp': animation_state.get('animation_start_timestamp'),
                'progress_percent': animation_state.get('progress_percent'),
                'server_timestamp': current_timestamp
            }

            _logger.info(f"动画状态恢复: room={room_uid}, user={user.username}, round={current_round}/{total_rounds}, state={animation_state.get('status', 'unknown')}")
            
            return reformat_resp(RespCode.Succeed.value, response_data, _('Succeed'))

        except Exception as e:
            _logger.error(f"获取动画状态失败: room_uid={request.GET.get('uid')}, error={e}")
            return reformat_resp(RespCode.Exception.value, {}, _('Internal error'))


class BattleTimeSyncView(APIView):
    """服务器时间同步API"""
    permission_classes = [AllowAny]  # 无需登录即可调用

    def get(self, request):
        """返回服务器当前时间戳（毫秒），用于客户端快速校准时钟"""
        try:
            server_timestamp = int(time.time() * 1000)
            
            response_data = {
                'server_timestamp': server_timestamp,
                'timezone': 'UTC'
            }
            
            return reformat_resp(RespCode.Succeed.value, response_data, _('Succeed'))

        except Exception as e:
            _logger.error(f"时间同步失败: error={e}")
            return reformat_resp(RespCode.Exception.value, {}, _('Internal error'))


class BattleAnimationConfigView(APIView):
    """对战动画配置API"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """获取对战动画的配置参数，用于前端同步动画效果"""
        try:
            # 按文档示例返回 animation_config
            import time
            server_ts = int(time.time() * 1000)
            animation_config = {
                'duration_per_round': 8000,
                'reveal_delay': 1500,
                'sync_tolerance': 150,
                'server_timestamp': server_ts
            }
            return reformat_resp(RespCode.Succeed.value, {'animation_config': animation_config}, _('Succeed'))
        except Exception as e:
            _logger.error(f"获取动画配置失败: error={e}")
            return reformat_resp(RespCode.Exception.value, {}, _('Internal error'))

