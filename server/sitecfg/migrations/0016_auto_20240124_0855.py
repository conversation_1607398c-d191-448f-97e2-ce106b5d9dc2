# -*- coding: utf-8 -*-
# Generated by Django 1.11.13 on 2024-01-24 00:55
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion
import steambase.models


class Migration(migrations.Migration):

    dependencies = [
        ('sitecfg', '0015_auto_20240120_1622'),
    ]

    operations = [
        migrations.CreateModel(
            name='ArticleCategoryy',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(default=steambase.models.uid_gen, editable=False, max_length=64, unique=True, verbose_name='uid')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='create time')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='update time')),
                ('name', models.Char<PERSON><PERSON>(max_length=128, verbose_name='name')),
                ('enable', models.<PERSON><PERSON>an<PERSON>ield(default=True, verbose_name='enable')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='article',
            name='category',
            field=models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='articles', to='sitecfg.ArticleCategoryy', verbose_name='category'),
        ),
    ]
