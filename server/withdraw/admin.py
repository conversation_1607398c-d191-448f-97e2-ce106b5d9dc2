import json
import logging
import os

from datetime import timedelta

from django.contrib import admin, messages
from django.contrib.admin import TabularInline, StackedInline
from django.contrib.admin.views.main import ChangeList
from django.db.models import Sum
from django.utils.translation import gettext_lazy as _
from jet.filters import DateRangeFilter

from steambase.admin import ReadOnlyAdmin, ChangeOnlyAdmin
from steambase.enums import PackageState, RespCode
from steambase.utils import ParamException
from withdraw.models import TradeRecord, WithdrawItem, WxpTradeOffer, TradeStatisticsDay, TradeStatisticsMonth
from authentication.models import AuthUser



class WithdrawItemInline(StackedInline):
    model = WithdrawItem
    can_delete = False
    fields = ['trade', 'package', 'returned']
    readonly_fields = ['trade', 'package', 'returned']


class WxpTradeOfferInline(StackedInline):
    model = WxpTradeOffer
    can_delete = False
    fields = ['trade', 'project_id', 'wxp_item_id', 'status', 'price', 'done', 'market_name', 'update_time']
    readonly_fields = ['trade', 'project_id', 'wxp_item_id', 'status', 'price', 'done', 'market_name', 'update_time']


@admin.register(TradeRecord)
class TradeRecordAdmin(ReadOnlyAdmin):
    fields = ('user', 'amount', 'amount_used', 'drop_refund', 'balance_refund', 'state', 'trade_url', 'create_time')
    list_display = ('uid', 'user', 'amount', 'amount_used', 'drop_refund', 'balance_refund', 'state', 'trade_url', 'create_time')
    list_filter = ('state', ('create_time', DateRangeFilter))
    search_fields = ('user__username', 'uid')
    ordering = ('-create_time',)
    list_per_page = 50
    inlines = [WxpTradeOfferInline]

    def get_changelist(self, request, **kwargs):
        return AddTotalTradeRecordChangeList


class AddTotalTradeRecordChangeList(ChangeList):

    def get_results(self, request):
        super().get_results(request)
        total = TradeRecord()
        setattr(total, 'uid', '-')
        setattr(total, 'user', AuthUser(username='总和'))
        setattr(total, 'drop_refund', '-')
        setattr(total, 'balance_refund', '-')
        setattr(total, 'state', '-')
        setattr(total, 'trade_url', '-')
        setattr(total, 'create_time', '-')
        fields_to_total = ['amount']
        for field in fields_to_total:
            setattr(total, field, round(list(self.queryset.aggregate(Sum(field)).items())[0][1] or 0, 2))
        fields_to_total = ['amount_used']
        for field in fields_to_total:
            setattr(total, field, round(list(self.queryset.aggregate(Sum(field)).items())[0][1] or 0, 2))
        
        len(self.result_list)
        self.result_list._result_cache.insert(0, total)


@admin.register(WxpTradeOffer)
class WxpTradeOfferAdmin(ReadOnlyAdmin):
    fields = ('trade', 'project_id', 'wxp_id', 'wxp_item_id', 'status', 'price', 'done', 'reason', 'steam_trade_id', 'market_name', 'item_info')
    list_display = ('trade', 'project_id', 'wxp_item_id', 'status', 'price', 'done', 'market_name', 'update_time')
    list_filter = ('status', ('update_time', DateRangeFilter), 'done')
    search_fields = ('trade__uid', 'project_id', 'wxp_id', 'reason')
    ordering = ('-create_time',)
    list_per_page = 50

    def get_changelist(self, request, **kwargs):
        return AddTotalWxpTradeOfferChangeList


class AddTotalWxpTradeOfferChangeList(ChangeList):

    def get_results(self, request):
        super().get_results(request)
        total = WxpTradeOffer()
        setattr(total, 'project_id', '-')
        setattr(total, 'wxp_item_id', WxpTradeOffer(uid='总和'))
        setattr(total, 'status', '-')
        setattr(total, 'done', None)
        setattr(total, 'market_name', '-')
        setattr(total, 'update_time', '-')
        fields_to_total = ['price']
        for field in fields_to_total:
            setattr(total, field, round(list(self.queryset.aggregate(Sum(field)).items())[0][1] or 0, 2))
        
        len(self.result_list)
        self.result_list._result_cache.insert(0, total)


@admin.register(TradeStatisticsDay)
class TradeStatisticsDayAdmin(ReadOnlyAdmin):
    fields = ('date', 'amount')
    list_display = ('date', 'amount')
    list_filter = (('date', DateRangeFilter),)
    list_per_page = 50

    def get_changelist(self, request, **kwargs):
        return TradeStatisticsDayAddTotalChangeList


class TradeStatisticsDayAddTotalChangeList(ChangeList):

    def get_results(self, request):
        super().get_results(request)
        total = TradeStatisticsDay()
        setattr(total, 'date', 'total')
        fields_to_total = ['amount']
        for field in fields_to_total:
            setattr(total, field, round(list(self.queryset.aggregate(Sum(field)).items())[0][1] or 0, 2))
        len(self.result_list)
        self.result_list._result_cache.insert(0, total)


@admin.register(TradeStatisticsMonth)
class TradeStatisticsMonthAdmin(ReadOnlyAdmin):
    fields = ('month', 'amount')
    extra_readonly_fields = ('month',)
    list_display = ('month', 'amount')
    list_filter = (('date', DateRangeFilter),)
    list_per_page = 50

    def get_changelist(self, request, **kwargs):
        return TradeStatisticsMonthAddTotalChangeList


class TradeStatisticsMonthAddTotalChangeList(ChangeList):

    def get_results(self, request):
        super().get_results(request)
        total = TradeStatisticsMonth()
        setattr(total, 'date', 'total')
        fields_to_total = ['amount']
        for field in fields_to_total:
            setattr(total, field, round(list(self.queryset.aggregate(Sum(field)).items())[0][1] or 0, 2))
        len(self.result_list)
        self.result_list._result_cache.insert(0, total)