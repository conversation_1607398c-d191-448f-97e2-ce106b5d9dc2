import json
import logging
import os
import threading
from datetime import datetime

from django.contrib import admin, messages
from django.http import HttpResponse
from django.utils.translation import gettext_lazy as _
from openpyxl import Workbook

from authentication.models import AuthUser, UserExtra
# from libs.aws_sms import send_sms as aws_send_sms
from steambase.enums import PackageState, RespCode
from sitecfg.interfaces import get_admin_sms_msg

_logger = logging.getLogger(__name__)


def send_admin_message(queryset, message):
    users = queryset.filter(active=True)
    for user in users:
        phone = user.phone
        if not phone:
            continue
        send_sms_phone = '+86{}'.format(phone)
        resp = aws_send_sms(send_sms_phone, message)
        _logger.info('Send admin message to {}, result: {}'.format(phone, resp))


def setup_send_admin_message_worker(queryset, message):
    th = threading.Thread(target=send_admin_message, args=(queryset, message))
    th.start()


def admin_send_sms_msg(modeladmin, request, queryset):
    message = get_admin_sms_msg()
    if not message:
        messages.warning(request, _('Send fail, no admin message settings'))
        return
    setup_send_admin_message_worker(queryset, message)
    messages.success(request, _('Send message request complete'))

admin_send_sms_msg.short_description = _("Admin send sms message")


def change_user_chance_a_b(modeladmin, request, queryset):
    type_ = request.POST.get('box_chance_type')
    for user in queryset:
        user.extra.box_chance_type = type_
        user.extra.save()
        user.save()
    if type_ == 'a':
        messages.success(request, _('User Chance Type Change To A'))
        return
    messages.success(request, _('User Chance Type Change To B'))
    return

change_user_chance_a_b.short_description = _("Change User Chance A B")


def authuser_export_to_excel(modeladmin, request, queryset):
    name = "AuthUser-{}".format(datetime.now().strftime("%Y%m%d-%H%M%S"))  # 用于定义文件名, 格式为: app名.模型类名
    field_names = modeladmin.list_display
    field_names_trans = {
        "username": "用户名",
        "balance": "余额",
        "box_chance_type": '开箱概率类型',
        "is_active": '活跃',
        "is_staff": '用户状态',
        "is_superuser": '超级用户状态',
        "date_joined": '加入日期',
    }
    response = HttpResponse(content_type='application/msexcel')  # 定义响应内容类型
    response['Content-Disposition'] = f'attachment; filename={name}.xlsx'  # 定义响应数据格式
    wb = Workbook()  # 新建Workbook
    ws = wb.active  # 使用当前活动的Sheet表
    ws.append([field_names_trans.get(field, "") for field in field_names])  # 将模型字段名作为标题写入第一行

    # 遍历选中queryset
    for obj in queryset:
        set_attr = {
            'balance': obj.asset.balance,
            'box_chance_type': obj.extra.box_chance_type,
        }
        # # 设置自定义字段
        [setattr(obj, key, value) for key, value in set_attr.items()]

        data = [f'{getattr(obj, field)}' for field in field_names]
        row = ws.append(data)
    wb.save(response)  # 将数据存入响应内容
    return response

authuser_export_to_excel.short_description = _("用户导出EXCEL")


def userbalance_export_to_excel(modeladmin, request, queryset):
    name = "UserBalanceRecord-{}".format(datetime.now().strftime("%Y%m%d-%H%M%S"))  # 用于定义文件名, 格式为: app名.模型类名
    field_names = modeladmin.list_display
    field_names_trans = {
        "user": "用户名",
        "balance_changed": "已变更",
        "balance_before": '变更以前',
        "balance_after": '变更以后',
        "reason": '原因',
        "create_time": '创建时间',
    }
    response = HttpResponse(content_type='application/msexcel')  # 定义响应内容类型
    response['Content-Disposition'] = f'attachment; filename={name}.xlsx'  # 定义响应数据格式
    wb = Workbook()  # 新建Workbook
    ws = wb.active  # 使用当前活动的Sheet表
    ws.append([field_names_trans.get(field, "") for field in field_names])  # 将模型字段名作为标题写入第一行

    # 遍历选中queryset
    for obj in queryset:
        data = [f'{getattr(obj, field)}' for field in field_names]
        row = ws.append(data)
    wb.save(response)  # 将数据存入响应内容
    return response

userbalance_export_to_excel.short_description = _("余额记录导出EXCEL")