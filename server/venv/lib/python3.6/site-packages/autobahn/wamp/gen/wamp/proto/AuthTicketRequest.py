# automatically generated by the FlatBuffers compiler, do not modify

# namespace: proto

import flatbuffers

class AuthTicketRequest(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAsAuthTicketRequest(cls, buf, offset):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = AuthTicketRequest()
        x.Init(buf, n + offset)
        return x

    # AuthTicketRequest
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

def AuthTicketRequestStart(builder): builder.StartObject(0)
def AuthTicketRequestEnd(builder): return builder.EndObject()
