#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化后的开箱对战业务逻辑模块
修复关键缺陷和安全漏洞
"""

import json
import logging
import random
import threading
import time
import math
import uuid
import hashlib
import traceback
from datetime import datetime
from decimal import Decimal, ROUND_HALF_UP
from contextlib import contextmanager
import sys

from django.core.cache import cache
from django.contrib.auth import get_user_model
from django.db import connection, transaction
from django.db.models import Q, F, Sum, Count
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django_redis import get_redis_connection

from steambase import settings
from steambase.redis_con import get_redis
from steambase.enums import RespCode, GameState, PackageState, PackageSourceType
from steambase.utils import ParamException
from package.models import PackageItem, ItemInfo
from box.models import (
    Case, CaseRecord, CaseRoom, CaseRoomRound, 
    CaseRoomBet, CaseRoomItem, GiveawayItems
)

from .enhanced_battle_system import enhanced_battle_manager
from .message_utils import sanitize_websocket_data, WebSocketMessageSender
from .compat_async import get_async_processor

_logger = logging.getLogger(__name__)

# 消息去重配置
MESSAGE_DEDUP_TIMEOUT = 300  # 5分钟去重窗口
MESSAGE_DEDUP_KEY_PREFIX = 'ws_msg_dedup:'
BATTLE_LOCK_TIMEOUT = 30  # 对战锁超时时间
MAX_RETRY_ATTEMPTS = 3  # 最大重试次数

class BattleSecurityManager:
    """对战安全管理器"""
    
    @staticmethod
    def validate_room_integrity(room):
        """验证房间完整性"""
        errors = []
        
        # 1. 参与者数量检查
        bets_count = CaseRoomBet.objects.filter(room=room).count()
        if bets_count == 0:
            errors.append("房间没有参与者")
        elif bets_count > room.max_joiner:
            errors.append(f"参与者数量超限: {bets_count} > {room.max_joiner}")
        
        # 2. 余额一致性检查
        total_bet_amount = CaseRoomBet.objects.filter(room=room).aggregate(
            total=Sum('open_amount')
        )['total'] or 0
        
        expected_total = room.price * bets_count
        if abs(total_bet_amount - expected_total) > 0.01:
            errors.append(f"投注金额不一致: {total_bet_amount} != {expected_total}")
        
        # 3. 轮次状态检查
        total_rounds = CaseRoomRound.objects.filter(room=room).count()
        opened_rounds = CaseRoomRound.objects.filter(room=room, opened=True).count()
        
        if opened_rounds > total_rounds:
            errors.append(f"已开启轮次超出总数: {opened_rounds} > {total_rounds}")
        
        return errors
    
    @staticmethod
    def validate_user_permissions(user):
        """验证用户权限"""
        if user.extra.ban_battle == 1:
            return False, _('对战功能已被禁用')
        
        # 检查用户活跃房间数量
        active_rooms = CaseRoom.objects.filter(
            bets__user=user,
            state__in=[
                GameState.Initial.value,
                GameState.Joinable.value,
                GameState.Joining.value,
                GameState.Full.value,
                GameState.Running.value,
            ]
        ).count()
        
        if active_rooms >= settings.CASE_ROOM_CREATE_MAX:
            return False, _('超出最大房间数量限制')
        
        return True, None

class MessageHandler:
    """消息处理器 - 修复数据类型问题"""
    
    @staticmethod
    def generate_message_hash(room_uid, message_type, round_number=None, animation_id=None):
        """生成消息唯一标识符"""
        components = [str(room_uid), str(message_type)]
        if round_number is not None:
            components.append(str(round_number))
        if animation_id:
            components.append(str(animation_id))
        
        message_key = ':'.join(components)
        return hashlib.md5(message_key.encode()).hexdigest()
    
    @staticmethod
    def is_message_already_sent(room_uid, message_type, round_number=None, animation_id=None):
        """检查消息是否已经发送过"""
        message_hash = MessageHandler.generate_message_hash(
            room_uid, message_type, round_number, animation_id
        )
        cache_key = f"{MESSAGE_DEDUP_KEY_PREFIX}{message_hash}"
        return cache.get(cache_key) is not None
    
    @staticmethod
    def mark_message_as_sent(room_uid, message_type, round_number=None, animation_id=None):
        """标记消息已发送"""
        message_hash = MessageHandler.generate_message_hash(
            room_uid, message_type, round_number, animation_id
        )
        cache_key = f"{MESSAGE_DEDUP_KEY_PREFIX}{message_hash}"
        
        cache.set(cache_key, True, MESSAGE_DEDUP_TIMEOUT)
        _logger.info(f"标记消息已发送: {message_type}, room={room_uid}, round={round_number}, hash={message_hash}")
    
    @staticmethod
    def prepare_websocket_message(action, data):
        """准备WebSocket消息 - 确保数据类型正确"""
        try:
            # 确保所有数据都是可序列化的
            sanitized_data = MessageHandler._sanitize_data(data)
            
            # 构建消息格式：[模块, 动作, 数据]
            message = ['boxroom', str(action), sanitized_data]
            
            return json.dumps(message, ensure_ascii=False, default=str)
        except Exception as e:
            _logger.error(f"消息序列化失败: action={action}, error={e}")
            return None
    
    @staticmethod
    def _sanitize_data(data):
        """数据清理 - 确保类型安全"""
        if isinstance(data, dict):
            return {k: MessageHandler._sanitize_data(v) for k, v in data.items()}
        elif isinstance(data, list):
            return [MessageHandler._sanitize_data(item) for item in data]
        elif isinstance(data, (int, float, str, bool)) or data is None:
            return data
        elif hasattr(data, 'isoformat'):  # datetime对象
            return data.isoformat()
        else:
            return str(data)

class AsyncCompatibilityManager:
    """异步兼容性管理器 - 修复Python版本兼容问题"""
    
    @staticmethod
    def create_task_safe(coro):
        """安全创建异步任务 - 兼容Python 3.6"""
        try:
            # Python 3.7+
            if hasattr(asyncio, 'create_task'):
                return asyncio.create_task(coro)
            else:
                # Python 3.6 fallback
                return asyncio.ensure_future(coro)
        except Exception as e:
            _logger.error(f"异步任务创建失败: {e}")
            return None
    
    @staticmethod
    def run_async_safely(coro):
        """安全运行异步协程"""
        try:
            import asyncio
            async_processor = get_async_processor()
            
            # 检查是否已有事件循环
            try:
                loop = asyncio.get_running_loop()
                # 如果已在事件循环中，使用ensure_future
                task = async_processor.create_task_safe(coro)
                return task
            except RuntimeError:
                # 没有运行的事件循环，创建新的
                if sys.version_info >= (3, 7):
                    return asyncio.run(coro)
                else:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        return loop.run_until_complete(coro)
                    finally:
                        loop.close()
        except Exception as e:
            _logger.error(f"异步执行失败: {e}")
            return None

@contextmanager
def distributed_lock(lock_key, timeout=BATTLE_LOCK_TIMEOUT):
    """分布式锁上下文管理器"""
    redis_client = get_redis()
    acquired = False
    
    try:
        # 尝试获取锁
        acquired = redis_client.set(
            lock_key, 
            "locked", 
            nx=True, 
            ex=timeout
        )
        
        if not acquired:
            _logger.warning(f"获取锁失败: {lock_key}")
            raise RuntimeError(f"Cannot acquire lock: {lock_key}")
        
        _logger.debug(f"获取锁成功: {lock_key}")
        yield acquired
        
    finally:
        if acquired:
            try:
                redis_client.delete(lock_key)
                _logger.debug(f"释放锁: {lock_key}")
            except Exception as e:
                _logger.error(f"释放锁失败: {lock_key}, error={e}")

class OptimizedBattleRoomManager:
    """优化的对战房间管理器"""
    
    def __init__(self):
        self.security_manager = BattleSecurityManager()
        self.message_handler = MessageHandler()
    
    def create_room_safely(self, user, cases, max_joiner, room_type, private):
        """安全创建房间"""
        try:
            # 1. 权限验证
            is_valid, error_msg = self.security_manager.validate_user_permissions(user)
            if not is_valid:
                return RespCode.BadRequest.value, error_msg
            
            # 2. 参数验证
            validation_result = self._validate_room_parameters(cases, max_joiner, user)
            if validation_result[0] != RespCode.Succeed.value:
                return validation_result
            
            room_cases, room_price = validation_result[1], validation_result[2]
            
            # 3. 使用分布式锁确保并发安全
            lock_key = f"create_room:{user.uid}"
            with distributed_lock(lock_key):
                return self._create_room_transaction(
                    user, room_cases, room_price, max_joiner, room_type, private
                )
                
        except Exception as e:
            _logger.error(f"创建房间失败: user={user.uid}, error={e}")
            return RespCode.InternalError.value, _('创建房间失败')
    
    def _validate_room_parameters(self, cases, max_joiner, user):
        """验证房间参数"""
        # 箱子数量验证
        if not cases or len(cases) < 1 or len(cases) > 10:
            return (RespCode.InvalidParams.value, _('箱子数量无效'), None)
        
        # 参与者数量验证
        if not max_joiner or max_joiner < 2 or max_joiner > 4:
            return (RespCode.InvalidParams.value, _('参与者数量无效'), None)
        
        # 获取有效箱子
        room_cases = []
        room_price = Decimal('0')
        
        for case_key in cases:
            case = Case.objects.filter(
                case_key=case_key, 
                is_show=True, 
                enable=True, 
                enable_room=True
            ).first()
            
            if not case:
                return (RespCode.InvalidParams.value, _('箱子无效'), None)
            
            if not user.is_superuser and case.enable_admin:
                return (RespCode.InvalidGame.value, _('箱子已被管理员禁用'), None)
            
            room_cases.append(case)
            cost = Decimal(str(case.price)) * Decimal(str(case.discount)) / Decimal('100')
            room_price += cost.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
        
        # 余额验证
        user_balance = Decimal(str(user.asset.balance))
        if user_balance < room_price:
            return (RespCode.InvalidParams.value, _('余额不足'), None)
        
        if user_balance * Decimal('0.5') < room_price:
            return (RespCode.InvalidParams.value, _('投注金额不能超过余额的50%'), None)
        
        return (RespCode.Succeed.value, room_cases, float(room_price))
    
    def _create_room_transaction(self, user, room_cases, room_price, max_joiner, room_type, private):
        """房间创建事务"""
        with transaction.atomic():
            # 扣除用户余额
            user.update_balance(-room_price, _('创建对战'))
            
            # 创建房间
            room = CaseRoom.objects.create(
                user=user,
                max_joiner=max_joiner,
                price=room_price,
                type=room_type,
                private=private
            )
            
            # 创建参与记录
            CaseRoomBet.objects.create(room=room, user=user)
            
            # 创建轮次（循环使用箱子）
            rounds_to_create = []
            for i in range(max_joiner):
                case_index = i % len(room_cases)
                rounds_to_create.append(
                    CaseRoomRound(room=room, case=room_cases[case_index])
                )
            CaseRoomRound.objects.bulk_create(rounds_to_create)
            
            # 更新房间状态
            room.state = GameState.Joinable.value
            room.save()
            
            # 发送WebSocket消息
            self._send_room_created_message(room)
            
            return RespCode.Succeed.value, {
                'room': room.uid,
                'rid': room.short_id
            }
    
    def _send_room_created_message(self, room):
        """发送房间创建消息"""
        try:
            from box.serializers import CaseRoomSerializer
            room_data = CaseRoomSerializer(room).data
            
            message = self.message_handler.prepare_websocket_message('new', room_data)
            if message:
                redis_client = get_redis_connection('default')
                redis_client.publish('ws_channel', message)
                _logger.info(f"房间创建消息发送成功: {room.uid}")
        except Exception as e:
            _logger.error(f"房间创建消息发送失败: {room.uid}, error={e}")

class OptimizedRoundManager:
    """优化的轮次管理器"""
    
    def __init__(self, room_uid):
        self.room_uid = room_uid
        self.cache_key = f"battle_round:{room_uid}"
    
    def get_current_round(self):
        """获取当前轮次"""
        try:
            # 先从缓存获取
            cached_round = cache.get(self.cache_key)
            if cached_round:
                return int(cached_round)
            
            # 从数据库计算
            room = CaseRoom.objects.filter(uid=self.room_uid).first()
            if not room:
                return 1
            
            opened_count = CaseRoomRound.objects.filter(
                room=room, 
                opened=True
            ).count()
            
            current_round = opened_count + 1
            
            # 缓存结果
            cache.set(self.cache_key, current_round, 300)
            
            return current_round
            
        except Exception as e:
            _logger.error(f"获取当前轮次失败: room={self.room_uid}, error={e}")
            return 1
    
    def get_total_rounds(self):
        """获取总轮次数"""
        try:
            room = CaseRoom.objects.filter(uid=self.room_uid).first()
            if not room:
                return 1
            
            return CaseRoomRound.objects.filter(room=room).count()
            
        except Exception as e:
            _logger.error(f"获取总轮次失败: room={self.room_uid}, error={e}")
            return 1
    
    def advance_round(self):
        """推进轮次"""
        try:
            current = self.get_current_round()
            next_round = current + 1
            
            # 更新缓存
            cache.set(self.cache_key, next_round, 300)
            
            _logger.info(f"轮次推进: room={self.room_uid}, {current} -> {next_round}")
            return next_round
            
        except Exception as e:
            _logger.error(f"轮次推进失败: room={self.room_uid}, error={e}")
            raise

def optimized_open_room_case(room, room_round):
    """优化的开箱函数"""
    try:
        # 1. 初始化管理器
        round_manager = OptimizedRoundManager(room.uid)
        message_handler = MessageHandler()
        
        # 2. 获取轮次信息
        current_round = round_manager.get_current_round()
        total_rounds = round_manager.get_total_rounds()
        
        _logger.info(f"开始处理房间 {room.short_id}, 轮次 {current_round}/{total_rounds}")
        
        # 3. 检查消息去重
        if message_handler.is_message_already_sent(room.uid, 'round_start', current_round):
            _logger.warning(f"回合开始消息已发送，跳过: room={room.uid}, round={current_round}")
            return
        
        # 4. 获取参与者和箱子信息
        case = room_round.case
        bets = CaseRoomBet.objects.filter(room=room)
        available_drops = case.drops.all()
        
        if not available_drops.exists():
            raise ParamException(_('箱子掉落物品无效'))
        
        # 5. 处理开箱逻辑
        _process_case_opening(room, room_round, case, bets, available_drops)
        
        # 6. 发送结果消息
        _send_round_messages(room, current_round, total_rounds, bets, message_handler)
        
        # 7. 标记轮次完成
        room_round.opened = True
        room_round.save()
        
        # 8. 推进轮次
        round_manager.advance_round()
        
        _logger.info(f"轮次 {current_round} 处理完成: room={room.short_id}")
        
    except Exception as e:
        _logger.error(f"开箱处理失败: room={room.short_id}, error={e}")
        _logger.error(traceback.format_exc())
        raise

def _process_case_opening(room, room_round, case, bets, available_drops):
    """处理开箱逻辑"""
    for bet in bets:
        user = bet.user
        
        # 随机选择掉落物品
        drop = random.choices(
            available_drops,
            weights=[d.weight for d in available_drops]
        )[0]
        
        # 获取物品价格
        from package.service.item import get_item_price_by_id
        price = get_item_price_by_id(drop.item_info.id).price if drop.item_type == 1 else drop.coin
        
        # 更新用户统计
        bet.open_amount = (bet.open_amount or 0) + price
        bet.save()
        
        # 创建房间物品记录
        CaseRoomItem.objects.create(
            room=room,
            round=room_round,
            bet=bet,
            item_info=drop.item_info,
            item_type=drop.item_type,
            price=price
        )
        
        # 创建开箱记录
        CaseRecord.objects.create(
            user=user,
            case=case,
            item_info=drop.item_info,
            price=price,
            source=room.type
        )
        
        _logger.info(f"用户 {user.steam.personaname} 开出物品 {drop.item_info.market_hash_name}, 价格: {price}")

def _send_round_messages(room, current_round, total_rounds, bets, message_handler):
    """发送轮次消息 - 使用增强版消息处理器"""
    try:
        # 使用增强版消息发送器
        message_sender = WebSocketMessageSender()
        
        # 准备参与者数据并清理数据类型
        participants = []
        for bet in bets:
            participant_data = {
                'user_uid': str(bet.user.uid),  # 确保字符串类型
                'nickname': str(bet.user.profile.nickname if hasattr(bet.user, 'profile') else bet.user.username),
                'open_amount': float(bet.open_amount or 0)
            }
            participants.append(participant_data)
        
        # 发送回合开始消息（带去重检查）
        round_start_data = {
            'room_uid': str(room.uid),
            'round': int(current_round),
            'total_rounds': int(total_rounds),
            'participants': participants,
            'timestamp': int(time.time() * 1000)
        }
        
        # 使用增强版消息发送器发送消息（自动去重和数据清理）
        message_sender.send_unique_message(
            room_id=room.uid,
            message_type='round_start',
            data=round_start_data,
            dedup_key=f"{room.uid}:round_start:{current_round}"
        )
        
        _logger.info(f"回合开始消息发送成功: room={room.uid}, round={current_round}")
        
        # 发送开箱结果消息（也使用增强版处理）
        results_data = {
            'room_uid': str(room.uid),
            'round': int(current_round),
            'results': participants,
            'timestamp': int(time.time() * 1000)
        }
        
        message_sender.send_unique_message(
            room_id=room.uid,
            message_type='round_results',
            data=results_data,
            dedup_key=f"{room.uid}:round_results:{current_round}"
        )
        
        message = message_handler.prepare_websocket_message('round_result', results_data)
        if message:
            redis_client = get_redis_connection('default')
            redis_client.publish('ws_channel', message)
            _logger.info(f"回合结果消息发送成功: room={room.uid}, round={current_round}")
            
    except Exception as e:
        _logger.error(f"发送轮次消息失败: room={room.uid}, error={e}")

# 导出优化后的函数
__all__ = [
    'OptimizedBattleRoomManager',
    'OptimizedRoundManager',
    'BattleSecurityManager',
    'MessageHandler',
    'AsyncCompatibilityManager',
    'optimized_open_room_case',
    'distributed_lock'
]
