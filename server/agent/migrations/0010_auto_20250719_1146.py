# Generated by Django 3.2.25 on 2025-07-19 03:46

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('agent', '0009_auto_20250718_1647'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='agent',
            options={'verbose_name': '代理', 'verbose_name_plural': 'Agents'},
        ),
        migrations.AlterModelOptions(
            name='agentbalancerecord',
            options={'verbose_name': '代理余额记录', 'verbose_name_plural': 'Agent Balance Records'},
        ),
        migrations.AlterModelOptions(
            name='agentwithdrawalorder',
            options={'verbose_name': '代理提现订单', 'verbose_name_plural': 'Agent Withdrawal Orders'},
        ),
        migrations.AlterModelOptions(
            name='article',
            options={'verbose_name': '文章', 'verbose_name_plural': 'Articles'},
        ),
        migrations.AlterModelOptions(
            name='articlecategory',
            options={'verbose_name': '文章分类', 'verbose_name_plural': 'Article Categories'},
        ),
        migrations.AlterField(
            model_name='agentbalancerecord',
            name='agent',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='agent.agent', verbose_name='代理'),
        ),
        migrations.AlterField(
            model_name='agentwithdrawalorder',
            name='agent',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='agent.agent', verbose_name='代理'),
        ),
        migrations.AlterField(
            model_name='article',
            name='category',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='agent.articlecategory', verbose_name='分类'),
        ),
        migrations.AlterField(
            model_name='article',
            name='content',
            field=models.TextField(verbose_name='内容'),
        ),
    ]
