// 🎯 统一对战状态协调器 - 解决多状态管理系统冲突
// 作为唯一状态源，协调所有其他状态管理器

import { ref, computed, reactive, watch } from 'vue'
import type { BattleDetail } from '~/types/battle'
import { BattleStatus } from '~/services/battle-api'

// 🎯 统一状态接口
interface UnifiedBattleState {
  // 基础数据
  battleData: BattleDetail | null
  isLoading: boolean
  error: string | null
  
  // 轮次状态
  currentRound: number
  totalRounds: number
  completedRounds: Set<number>
  
  // 动画状态
  isAnimating: boolean
  animationId: string | null
  openingCaseId: string | null
  openingPlayerIndex: number | null
  
  // 对战状态
  battleStatus: BattleStatus
  isBattleStarted: boolean
  isBattleFinished: boolean
  
  // 玩家记录
  playerRecords: Map<number, any[]> // playerIndex -> 开箱记录数组
  
  // WebSocket状态
  isConnected: boolean
  lastMessageTime: number
}

// 🎯 状态更新事件
interface StateUpdateEvent {
  type: 'data_update' | 'round_change' | 'animation_start' | 'animation_complete' | 'battle_status_change' | 'websocket_message'
  data: any
  timestamp: number
  source: string
}

// 🎯 状态协调器类
class BattleStateCoordinator {
  private state: UnifiedBattleState
  private listeners: Set<(event: StateUpdateEvent) => void> = new Set()
  private updateQueue: StateUpdateEvent[] = []
  private isProcessing = false
  
  constructor() {
    this.state = reactive({
      // 基础数据
      battleData: null,
      isLoading: false,
      error: null,
      
      // 轮次状态
      currentRound: 1,
      totalRounds: 1,
      completedRounds: new Set<number>(),
      
      // 动画状态
      isAnimating: false,
      animationId: null,
      openingCaseId: null,
      openingPlayerIndex: null,
      
      // 对战状态
      battleStatus: BattleStatus.WAITING,
      isBattleStarted: false,
      isBattleFinished: false,
      
      // 玩家记录
      playerRecords: new Map<number, any[]>(),
      
      // WebSocket状态
      isConnected: false,
      lastMessageTime: 0
    })
  }

  // 🎯 状态访问器（只读）
  getState() {
    return this.state
  }

  // 🎯 监听状态更新
  subscribe(listener: (event: StateUpdateEvent) => void) {
    this.listeners.add(listener)
    return () => this.listeners.delete(listener)
  }

  // 🎯 发布状态更新事件
  private publish(event: StateUpdateEvent) {
    console.log('[🎯STATE-COORDINATOR] 发布状态更新:', event.type, event.source)
    
    this.listeners.forEach(listener => {
      try {
        listener(event)
      } catch (error) {
        console.error('[🎯STATE-COORDINATOR] 监听器错误:', error)
      }
    })
  }

  // 🎯 统一状态更新入口
  async updateState(event: StateUpdateEvent) {
    this.updateQueue.push(event)
    
    if (!this.isProcessing) {
      await this.processUpdateQueue()
    }
  }

  // 🎯 处理状态更新队列（确保顺序执行）
  private async processUpdateQueue() {
    this.isProcessing = true
    
    while (this.updateQueue.length > 0) {
      const event = this.updateQueue.shift()!
      await this.processStateUpdate(event)
    }
    
    this.isProcessing = false
  }

  // 🎯 处理单个状态更新
  private async processStateUpdate(event: StateUpdateEvent) {
    console.log(`[🎯STATE-COORDINATOR] 处理状态更新: ${event.type} from ${event.source}`)
    
    try {
      switch (event.type) {
        case 'data_update':
          await this.handleDataUpdate(event)
          break
        case 'round_change':
          await this.handleRoundChange(event)
          break
        case 'animation_start':
          await this.handleAnimationStart(event)
          break
        case 'animation_complete':
          await this.handleAnimationComplete(event)
          break
        case 'battle_status_change':
          await this.handleBattleStatusChange(event)
          break
        case 'websocket_message':
          await this.handleWebSocketMessage(event)
          break
        default:
          console.warn('[🎯STATE-COORDINATOR] 未知的状态更新类型:', event.type)
      }
      
      // 发布给监听器
      this.publish(event)
      
         } catch (error) {
       console.error('[🎯STATE-COORDINATOR] 状态更新处理失败:', error)
       this.state.error = `状态更新失败: ${error instanceof Error ? error.message : String(error)}`
     }
  }

  // 🎯 处理数据更新
  private async handleDataUpdate(event: StateUpdateEvent) {
    const { data } = event
    
    // 🔄 合并对战数据，保留现有记录
    if (data.battleData) {
      const oldRecords = this.extractPlayerRecords(this.state.battleData)
      this.state.battleData = data.battleData
      this.mergePlayerRecords(oldRecords)
    }
    
    // 更新基础状态
    if (data.isLoading !== undefined) this.state.isLoading = data.isLoading
    if (data.error !== undefined) this.state.error = data.error
    
    // 同步轮次信息
    if (data.currentRound) this.state.currentRound = data.currentRound
    if (data.totalRounds) this.state.totalRounds = data.totalRounds
    
    // 更新对战状态
    this.updateBattleStatus()
  }

  // 🎯 处理轮次变化
  private async handleRoundChange(event: StateUpdateEvent) {
    const { round, totalRounds } = event.data
    
    console.log(`[🎯STATE-COORDINATOR] 轮次变化: ${this.state.currentRound} → ${round}`)
    
    // 🚩 关键：确保轮次更新的原子性
    if (round !== this.state.currentRound) {
      // 标记前一轮次为完成
      if (this.state.currentRound > 0) {
        this.state.completedRounds.add(this.state.currentRound)
      }
      
      this.state.currentRound = round
      console.log(`[🎯STATE-COORDINATOR] ✅ 轮次已更新: ${round}`)
    }
    
    if (totalRounds) {
      this.state.totalRounds = totalRounds
    }
    
    // 重置动画状态（新轮次开始）
    this.resetAnimationState()
  }

  // 🎯 处理动画开始
  private async handleAnimationStart(event: StateUpdateEvent) {
    const { animationId, caseId, playerIndex } = event.data
    
    console.log('[🎯STATE-COORDINATOR] 动画开始:', animationId)
    
    this.state.isAnimating = true
    this.state.animationId = animationId
    this.state.openingCaseId = caseId
    this.state.openingPlayerIndex = playerIndex
  }

  // 🎯 处理动画完成
  private async handleAnimationComplete(event: StateUpdateEvent) {
    const { playerIndex, item, round } = event.data
    
    console.log(`[🎯STATE-COORDINATOR] 动画完成: 玩家${playerIndex}, 轮次${round}`)
    
    // 🎯 添加玩家记录
    this.addPlayerRecord(playerIndex, item, round)
    
    // 检查是否所有玩家都完成了当前轮次
    if (this.isRoundComplete(round || this.state.currentRound)) {
      console.log(`[🎯STATE-COORDINATOR] 轮次${round || this.state.currentRound}完成`)
      this.state.completedRounds.add(round || this.state.currentRound)
      
      // 准备下一轮或结束对战
      await this.prepareNextRoundOrFinish()
    }
  }

  // 🎯 处理对战状态变化
  private async handleBattleStatusChange(event: StateUpdateEvent) {
    const { status } = event.data
    
    if (status !== this.state.battleStatus) {
      console.log(`[🎯STATE-COORDINATOR] 对战状态变化: ${this.state.battleStatus} → ${status}`)
      this.state.battleStatus = status
      this.updateBattleFlags()
    }
  }

  // 🎯 处理WebSocket消息
  private async handleWebSocketMessage(event: StateUpdateEvent) {
    const { messageType, action, data } = event.data
    
    this.state.isConnected = true
    this.state.lastMessageTime = event.timestamp
    
    // 根据消息类型更新相应状态
    if (messageType === 'boxroomdetail') {
      await this.handleBattleDetailMessage(action, data)
    } else if (messageType === 'boxroom') {
      await this.handleBattleRoomMessage(action, data)
    }
  }

  // 🎯 处理对战详情消息
  private async handleBattleDetailMessage(action: string, data: any) {
    switch (action) {
      case 'round_start':
        await this.updateState({
          type: 'round_change',
          data: { round: data.round, totalRounds: data.total_rounds },
          timestamp: Date.now(),
          source: 'websocket_round_start'
        })
        break
        
      case 'opening_start':
        await this.updateState({
          type: 'animation_start',
          data: { 
            animationId: data.animation_id,
            caseId: data.animation_id,
            playerIndex: 0
          },
          timestamp: Date.now(),
          source: 'websocket_opening_start'
        })
        break
        
      case 'round_result':
        // 处理轮次结果，标记动画完成
        if (data.results && Array.isArray(data.results)) {
          for (let i = 0; i < data.results.length; i++) {
            const result = data.results[i]
            const item = result.items?.[0] || result.open_items?.[0] || result.item
            
            if (item) {
              await this.updateState({
                type: 'animation_complete',
                data: { playerIndex: i, item, round: data.round },
                timestamp: Date.now(),
                source: 'websocket_round_result'
              })
            }
          }
        }
        break
    }
  }

  // 🎯 处理对战房间消息
  private async handleBattleRoomMessage(action: string, data: any) {
    switch (action) {
      case 'start':
        await this.updateState({
          type: 'battle_status_change',
          data: { status: BattleStatus.IN_PROGRESS },
          timestamp: Date.now(),
          source: 'websocket_battle_start'
        })
        break
        
      case 'end':
        await this.updateState({
          type: 'battle_status_change',
          data: { status: BattleStatus.COMPLETED },
          timestamp: Date.now(),
          source: 'websocket_battle_end'
        })
        break
    }
  }

  // 🎯 辅助方法
  private extractPlayerRecords(battleData: BattleDetail | null): Map<number, any[]> {
    const records = new Map<number, any[]>()
    
    if (battleData?.bets) {
      battleData.bets.forEach((bet, index) => {
        if (bet.open_items && bet.open_items.length > 0) {
          records.set(index, [...bet.open_items])
        }
      })
    }
    
    return records
  }

  private mergePlayerRecords(oldRecords: Map<number, any[]>) {
    oldRecords.forEach((records, playerIndex) => {
      if (!this.state.playerRecords.has(playerIndex)) {
        this.state.playerRecords.set(playerIndex, [])
      }
      
      const currentRecords = this.state.playerRecords.get(playerIndex)!
      records.forEach(record => {
        if (!currentRecords.find(r => r.uid === record.uid)) {
          currentRecords.push(record)
        }
      })
    })
  }

  private addPlayerRecord(playerIndex: number, item: any, round: number) {
    if (!this.state.playerRecords.has(playerIndex)) {
      this.state.playerRecords.set(playerIndex, [])
    }
    
    const records = this.state.playerRecords.get(playerIndex)!
    
    // 确保记录数组长度足够
    while (records.length < round) {
      records.push(null)
    }
    
    // 添加到指定轮次
    records[round - 1] = {
      ...item,
      round,
      timestamp: Date.now()
    }
    
    // 同步到battleData
    if (this.state.battleData?.bets?.[playerIndex]) {
      if (!this.state.battleData.bets[playerIndex].open_items) {
        this.state.battleData.bets[playerIndex].open_items = []
      }
      this.state.battleData.bets[playerIndex].open_items = [...records.filter(r => r !== null)]
    }
  }

  private isRoundComplete(round: number): boolean {
    if (!this.state.battleData?.bets) return false
    
    const totalPlayers = this.state.battleData.bets.length
    let completedPlayers = 0
    
    for (let i = 0; i < totalPlayers; i++) {
      const records = this.state.playerRecords.get(i) || []
      if (records[round - 1]) {
        completedPlayers++
      }
    }
    
    return completedPlayers === totalPlayers
  }

  private async prepareNextRoundOrFinish() {
    if (this.state.currentRound >= this.state.totalRounds) {
      // 对战结束
      await this.updateState({
        type: 'battle_status_change',
        data: { status: BattleStatus.COMPLETED },
        timestamp: Date.now(),
        source: 'round_completion'
      })
    } else {
      // 准备下一轮
      this.resetAnimationState()
    }
  }

  private resetAnimationState() {
    this.state.isAnimating = false
    this.state.animationId = null
    this.state.openingCaseId = null
    this.state.openingPlayerIndex = null
  }

  private updateBattleStatus() {
    if (!this.state.battleData) return
    
    const apiState = this.state.battleData.state
    
    if (apiState === 5) {
      this.state.battleStatus = BattleStatus.IN_PROGRESS
    } else if (apiState === 11) {
      this.state.battleStatus = BattleStatus.COMPLETED
    } else if (apiState === 20) {
      this.state.battleStatus = BattleStatus.CANCELLED
    } else {
      this.state.battleStatus = BattleStatus.WAITING
    }
    
    this.updateBattleFlags()
  }

  private updateBattleFlags() {
    this.state.isBattleStarted = this.state.battleStatus === BattleStatus.IN_PROGRESS || this.state.battleStatus === BattleStatus.COMPLETED
    this.state.isBattleFinished = this.state.battleStatus === BattleStatus.COMPLETED || this.state.battleStatus === BattleStatus.CANCELLED
  }

  // 🎯 清理方法
  cleanup() {
    this.listeners.clear()
    this.updateQueue.length = 0
    this.isProcessing = false
    this.state.playerRecords.clear()
    this.state.completedRounds.clear()
  }
}

// 🎯 全局状态协调器实例
let globalCoordinator: BattleStateCoordinator | null = null

export const useBattleStateCoordinator = () => {
  if (!globalCoordinator) {
    globalCoordinator = new BattleStateCoordinator()
  }
  
  return globalCoordinator
}

// 🎯 Composable接口
export const useBattleCoordinatedState = () => {
  const coordinator = useBattleStateCoordinator()
  const state = coordinator.getState()
  
  // 计算属性
  const displayCases = computed(() => {
    if (!state.battleData?.rounds) return []
    
    const expanded: any[] = []
    let seq = 0
    
    state.battleData.rounds.forEach((round: any) => {
      const caseObj = round?.case || round
      if (!caseObj) return
      
      const repeat = Math.max(1, caseObj?.count || round?.count || 1)
      const caseKey = caseObj.case_key || caseObj.key || caseObj.id
      
      for (let i = 0; i < repeat; i++) {
        expanded.push({
          id: caseKey,
          key: caseKey,
          name: caseObj.name,
          name_en: caseObj.name_en,
          name_zh_hans: caseObj.name_zh_hans,
          price: caseObj.price,
          cover: caseObj.cover,
          roundIndex: seq,
          instanceId: `${caseKey || 'unknown'}-round-${seq}`,
          isOpening: state.openingCaseId !== null && seq === state.currentRound - 1
        })
        seq += 1
      }
    })
    
    return expanded
  })
  
  const allPlayers = computed(() => {
    if (!state.battleData?.bets) return []
    
    return state.battleData.bets.map((bet, index) => {
      const playerRecords = state.playerRecords.get(index) || []
      
      return {
        id: bet.user.uid,
        nickname: bet.user.profile.nickname,
        avatar: bet.user.profile.avatar,
        uid: bet.user.uid,
        isHost: bet.user.uid === state.battleData?.user?.uid,
        isWinner: bet.victory || false,
        openingHistory: playerRecords.filter(r => r !== null),
        open_items: playerRecords.filter(r => r !== null),
        rewardItems: bet.win_items || [],
        openAmount: bet.open_amount || 0,
        winAmount: bet.win_amount || 0,
        totalValue: playerRecords.reduce((sum, item) => {
          return sum + (item?.item_price?.price || item?.price || 0)
        }, 0),
        isPlaceholder: false
      }
    })
  })
  
  return {
    // 状态
    state: readonly(state),
    
    // 计算属性
    displayCases,
    allPlayers,
    
    // 方法
    updateState: coordinator.updateState.bind(coordinator),
    subscribe: coordinator.subscribe.bind(coordinator),
    cleanup: coordinator.cleanup.bind(coordinator)
  }
} 