#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
UI端WebSocket功能测试脚本 (Python版本)
测试新的WebSocket消息格式和房间管理功能
"""

import asyncio
import json
import time
import sys
from typing import Dict, List, Any

try:
    import socketio
    import aiohttp
except ImportError:
    print("❌ 缺少依赖包，请安装: pip install python-socketio aiohttp")
    sys.exit(1)

class WebSocketTester:
    def __init__(self, server_url: str = 'http://localhost:8080'):
        self.server_url = server_url
        self.sio = socketio.AsyncClient()
        self.test_results = {
            'connection': False,
            'monitor': False,
            'case_records': False,
            'battle': False,
            'message_count': 0,
            'errors': []
        }
        self.setup_event_handlers()

    def setup_event_handlers(self):
        """设置事件处理器"""
        
        @self.sio.event
        async def connect():
            print(f'✅ WebSocket连接成功, ID: {self.sio.sid}')
            self.test_results['connection'] = True

        @self.sio.event
        async def connect_error(data):
            print(f'❌ WebSocket连接失败: {data}')
            self.test_results['errors'].append(f'连接失败: {data}')

        @self.sio.event
        async def disconnect():
            print('🔴 WebSocket连接断开')

        @self.sio.on('monitor')
        async def on_monitor(data):
            print(f'📊 收到monitor消息: {data}')
            self.test_results['monitor'] = True
            self.test_results['message_count'] += 1

        @self.sio.on('case_records')
        async def on_case_records(data):
            print(f'📦 收到case_records消息: {data}')
            self.test_results['case_records'] = True
            self.test_results['message_count'] += 1

        @self.sio.on('boxroom')
        async def on_boxroom(data):
            print(f'🎰 收到boxroom消息: {data}')
            self.test_results['battle'] = True
            self.test_results['message_count'] += 1

        @self.sio.on('boxroomdetail')
        async def on_boxroomdetail(data):
            print(f'🎯 收到boxroomdetail消息: {data}')
            self.test_results['battle'] = True
            self.test_results['message_count'] += 1

        @self.sio.on('message')
        async def on_message(data):
            print(f'💬 收到message消息: {data}')
            self.test_results['message_count'] += 1
            
            try:
                # 尝试解析消息格式
                if isinstance(data, str):
                    parsed_data = json.loads(data)
                else:
                    parsed_data = data
                
                if isinstance(parsed_data, list) and len(parsed_data) >= 2:
                    message_type, action = parsed_data[0], parsed_data[1]
                    print(f'📋 解析消息: type={message_type}, action={action}')
                    
                    if message_type == 'monitor':
                        self.test_results['monitor'] = True
                    elif message_type == 'case_records':
                        self.test_results['case_records'] = True
                    elif message_type in ['boxroom', 'boxroomdetail']:
                        self.test_results['battle'] = True
                        
            except Exception as e:
                print(f'❌ 解析消息失败: {e}')
                self.test_results['errors'].append(f'消息解析失败: {e}')

        # 捕获所有其他事件
        @self.sio.on('*')
        async def catch_all(event, data):
            if event not in ['connect', 'disconnect', 'connect_error', 'monitor', 'case_records', 'boxroom', 'boxroomdetail', 'message']:
                print(f'📨 收到其他事件: {event} -> {data}')
                self.test_results['message_count'] += 1

    async def connect_to_server(self):
        """连接到WebSocket服务器"""
        print(f'🔌 连接到WebSocket服务器: {self.server_url}')
        
        try:
            await self.sio.connect(self.server_url, transports=['websocket'])
            await asyncio.sleep(1)  # 等待连接稳定
            return True
        except Exception as e:
            print(f'❌ 连接失败: {e}')
            self.test_results['errors'].append(f'连接失败: {e}')
            return False

    async def test_monitor_room(self):
        """测试监控房间功能"""
        print('\n🧪 测试监控房间功能...')
        
        # 加入监控房间
        await self.sio.emit('monitor', ['join', 'monitor'])
        print('📤 发送: monitor join')
        await asyncio.sleep(1)
        
        # 请求统计数据
        await self.sio.emit('monitor', ['get_stats'])
        print('📤 发送: monitor get_stats')
        await asyncio.sleep(2)

    async def test_case_records_room(self):
        """测试开箱记录房间功能"""
        print('\n🧪 测试开箱记录房间功能...')
        
        # 请求开箱记录
        await self.sio.emit('monitor', ['case_records'])
        print('📤 发送: monitor case_records')
        await asyncio.sleep(2)

    async def test_battle_room(self):
        """测试对战房间功能"""
        print('\n🧪 测试对战房间功能...')
        
        # 加入对战房间
        await self.sio.emit('join', 'boxroom')
        print('📤 发送: join boxroom')
        await asyncio.sleep(1)
        
        # 加入特定对战房间
        await self.sio.emit('join', 'test_battle_123')
        print('📤 发送: join test_battle_123')
        await asyncio.sleep(2)

    async def test_legacy_format(self):
        """测试兼容性格式"""
        print('\n🧪 测试兼容性格式...')
        
        # 测试旧格式
        await self.sio.emit('join', 'ws_channel')
        await self.sio.emit('monitor', 'get_stats')
        await self.sio.emit('case_records')
        
        print('📤 发送兼容性格式消息')
        await asyncio.sleep(2)

    async def run_tests(self):
        """运行所有测试"""
        try:
            # 连接到服务器
            if not await self.connect_to_server():
                return
            
            print('\n🚀 开始WebSocket功能测试...')
            
            # 运行各项测试
            await self.test_monitor_room()
            await self.test_case_records_room()
            await self.test_battle_room()
            await self.test_legacy_format()
            
            print('\n⏳ 等待更多消息...')
            await asyncio.sleep(5)
            
        except Exception as e:
            print(f'❌ 测试失败: {e}')
            self.test_results['errors'].append(f'测试失败: {e}')
        finally:
            await self.sio.disconnect()
            self.print_results()

    def print_results(self):
        """打印测试结果"""
        print('\n' + '=' * 50)
        print('📊 WebSocket测试结果')
        print('=' * 50)
        
        print(f"✅ 连接状态: {'成功' if self.test_results['connection'] else '失败'}")
        print(f"📊 监控功能: {'正常' if self.test_results['monitor'] else '异常'}")
        print(f"📦 开箱记录: {'正常' if self.test_results['case_records'] else '异常'}")
        print(f"🎰 对战功能: {'正常' if self.test_results['battle'] else '异常'}")
        print(f"📨 消息总数: {self.test_results['message_count']}")
        
        if self.test_results['errors']:
            print('\n❌ 错误列表:')
            for i, error in enumerate(self.test_results['errors'], 1):
                print(f"  {i}. {error}")
        
        success_count = sum([
            self.test_results['connection'],
            self.test_results['monitor'],
            self.test_results['case_records'],
            self.test_results['battle']
        ])
        
        success_rate = (success_count / 4 * 100)
        print(f"\n🎯 成功率: {success_rate:.1f}% ({success_count}/4)")
        
        if success_rate >= 75:
            print('🎉 WebSocket功能测试通过！')
        else:
            print('⚠️ WebSocket功能存在问题，需要检查')

async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='WebSocket UI功能测试')
    parser.add_argument('--server', default='http://localhost:8080', 
                       help='WebSocket服务器地址 (默认: http://localhost:8080)')
    parser.add_argument('--debug', action='store_true', 
                       help='启用调试模式')
    
    args = parser.parse_args()
    
    print('🌐 WebSocket UI功能测试工具')
    print('=' * 50)
    print(f'服务器地址: {args.server}')
    print(f'调试模式: {"开启" if args.debug else "关闭"}')
    print('=' * 50)
    
    tester = WebSocketTester(args.server)
    await tester.run_tests()
    
    print('\n✅ 测试完成')

if __name__ == '__main__':
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print('\n⚠️ 测试被用户中断')
    except Exception as e:
        print(f'\n❌ 测试异常: {e}')
        sys.exit(1)
